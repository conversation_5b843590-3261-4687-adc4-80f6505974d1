﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TdAntParaCommonForm : MinCloseForm
    {
        public TdAntParaCommonForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initAngleArgs();
            this.mapForm = MainModel.MainForm.GetMapForm();
            rtbDesc.Text = TDAntMRAnaForm.WriteMRDesc();
            cbPart.Items.Clear();
            cbPart.Text = "全部";
            cbPart.Items.Add("全部");
            cbPart.Items.Add("(0%,50%]");
            cbPart.Items.Add("(0%,70%]");
            cbPart.Items.Add("(0%,80%]");
        }
        MapForm mapForm;
        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        Dictionary<LaiKey, TdAntParaItem> cellAntParaInfoDic = null;
        TdAntParaItem tdAntParaItem = null;

        public void FillData(Dictionary<LaiKey, TdAntParaItem> cellAntParaInfoDic)
        {
            try
            {
                this.cellAntParaInfoDic = cellAntParaInfoDic;
                labNum.Text = cellAntParaInfoDic.Count.ToString();
                int iPage = cellAntParaInfoDic.Count % 200 > 0 ? cellAntParaInfoDic.Count / 200 + 1 : cellAntParaInfoDic.Count / 200;
                labPage.Text = iPage.ToString();

                dataGridViewCell.Columns.Clear();
                dataGridViewCell.Rows.Clear();//小区级

                int rowCellAt = 0;
                foreach (NPOIRow data in nrDatasList[0])
                {
                    if (rowCellAt == 0)
                    {
                        intDataViewColumn(dataGridViewCell, data.cellValues);
                        rowCellAt++;
                        continue;
                    }
                    if (rowCellAt > 200)
                        break;
                    initDataRow(dataGridViewCell, data);
                    rowCellAt++;
                }

            }
            catch
            {
                //continue
            }
        }

        public void initAngleArgs()
        {
            for (int i = -179; i < 181; i++)
            {
                DataGridViewTextBoxColumn angelCol = new DataGridViewTextBoxColumn();
                angelCol.HeaderText = i + "°";
                angelCol.Width = 63;
                dataGridViewAngle.Columns.Add(angelCol);
            }

            cbbxSeries1.Items.Add("PCCPCH RSCP_Scan");
            cbbxSeries1.Items.Add("平滑PCCPCH RSCP_Scan");
            cbbxSeries1.Items.Add("PCCPCH C/I_Scan");
            cbbxSeries1.Items.Add("PCCPCH Ec/Io_Scan");
            cbbxSeries1.Items.Add("通信距离_Scan");
            cbbxSeries1.SelectedIndex = 0;

            cbbxSeries2.Items.Add("PCCPCH RSCP_Dt");
            cbbxSeries2.Items.Add("DPCH RSCP_Dt");
            cbbxSeries2.Items.Add("TD BLER_Dt");
            cbbxSeries2.Items.Add("过覆盖指数_Dt");
            cbbxSeries2.Items.Add("DPCH C/I_Dt");
            cbbxSeries2.Items.Add("通信距离_Dt");
            cbbxSeries2.SelectedIndex = 0;

            Series series = chartControl1.Series[0];
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical; //设置x轴有固定间距
            SeriesPoint pt;
            pt = new SeriesPoint(-150, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(0, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(150, 50);
            series.Points.Add(pt);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
        }

        private void intDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        private void initDataRow(DataGridView datatGridView, NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[4] + "_" + nop.cellValues[5];//小区LAC_小区CI
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && data.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, data);
                rowCellAt++;
            }
        }

        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, data);
                rowCellAt++;
            }
        }

        private void miShwoChart_Click(object sender, EventArgs e)
        {
            if (dataGridViewCell.SelectedRows.Count == 0)
            {
                return;
            }
            string cellLac_Ci = dataGridViewCell.SelectedRows[0].Tag as string;
            LaiKey cellKey = new LaiKey(Convert.ToInt32(cellLac_Ci.Split('_')[0]), Convert.ToInt32(cellLac_Ci.Split('_')[1]));
            if (!this.cellAntParaInfoDic.TryGetValue(cellKey, out tdAntParaItem))
            {
                return;
            }
            if (tdAntParaItem.iFunc == 2)
            {
                getCellInfoByLacCi(tdAntParaItem);
            }
            setAngelTable(tdAntParaItem);
            cbbxSeries1_SelectedIndexChanged(null, null);
            cbbxSeries2_SelectedIndexChanged(null, null);
            drawAntRadarSeries(tdAntParaItem);
            drawCellCoverRadarSeries(null, 0);
            drawCellMRRadarSeries(tdAntParaItem);
            cbPart.SelectedIndex = 0;
            xTabGSMAnt.SelectedTabPage = pageAngle;
        }

        public void getCellInfoByLacCi(TdAntParaItem tdAntParaItem)
        {
            LaiKey cellKey = new LaiKey(tdAntParaItem.小区LAC, tdAntParaItem.小区CI);
            DIYQueryTDAntAngle tdAntQuery = new DIYQueryTDAntAngle(MainModel);
            tdAntQuery.SetCondition(tdAntParaItem.timeCfg.ISitme, tdAntParaItem.timeCfg.IEitme, cellKey);
            tdAntQuery.Query();
            tdAntParaItem.dtCellInfoItem = new ZTTdAntenna.CellInfoItem();
            tdAntParaItem.scanCellInfoItem =  new ZTTdAntenna.CellInfoItem();
            if (tdAntQuery.dtCellInfoDic.ContainsKey(cellKey))
            {
                tdAntParaItem.dtCellInfoItem = tdAntQuery.dtCellInfoDic[cellKey];
            }
            if (tdAntQuery.scanCellInfoDic.ContainsKey(cellKey))
            {
                tdAntParaItem.scanCellInfoItem = tdAntQuery.scanCellInfoDic[cellKey];
            }          
        }

        public void setAngelTable(TdAntParaItem tdAntParaItem)
        {
            dataGridViewAngle.Rows.Clear();
            int rowAt = 0;
            dataGridViewAngle.Rows.Add(1);
            int colAt = 0;

            ZTTdAntenna.CellInfoItem scanCellInfoItem = tdAntParaItem.scanCellInfoItem;
            if (scanCellInfoItem == null)
            {
                scanCellInfoItem = new ZTTdAntenna.CellInfoItem();
                tdAntParaItem.scanCellInfoItem = new ZTTdAntenna.CellInfoItem();
            }
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH RSCP_Scan";
            for (int i = 181; i < 360; i++) //181~359度（按正负算-179~-1）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -140 : scanCellInfoItem.rscpArray[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -140 : scanCellInfoItem.rscpArray[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑PCCPCH RSCP_Scan";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(scanCellInfoItem.rscpNewArray[i], 2);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(scanCellInfoItem.rscpNewArray[i], 2);
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH C/I_Scan";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -25 : scanCellInfoItem.pcchc2iArrayAll[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -25 : scanCellInfoItem.pcchc2iArrayAll[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH Ec/Io_Scan";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -25 : scanCellInfoItem.pcchc2iArrayAll_EcIo[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -25 : scanCellInfoItem.pcchc2iArrayAll_EcIo[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "通信距离_Scan";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (Math.Round(scanCellInfoItem.samplNumArray[i] == 0 ? 0 : scanCellInfoItem.samplArray[i] / scanCellInfoItem.samplNumArray[i], 2)).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (Math.Round(scanCellInfoItem.samplNumArray[i] == 0 ? 0 : scanCellInfoItem.samplArray[i] / scanCellInfoItem.samplNumArray[i], 2)).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            //路测角度信息
            rowAt++;
            colAt = 0;
            ZTTdAntenna.CellInfoItem dtCellInfoItem = tdAntParaItem.dtCellInfoItem;
            if (dtCellInfoItem == null)
            {
                dtCellInfoItem = new ZTTdAntenna.CellInfoItem();
                tdAntParaItem.dtCellInfoItem = new ZTTdAntenna.CellInfoItem();
            }
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "PCCPCH RSCP_Dt";
            for (int i = 181; i < 360; i++) //181~359度（按正负算-179~-1）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (dtCellInfoItem.samplNumArray[i] == 0 ? -140 : dtCellInfoItem.rscpArray[i] / dtCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (dtCellInfoItem.samplNumArray[i] == 0 ? -140 : dtCellInfoItem.rscpArray[i] / dtCellInfoItem.samplNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "DPCH RSCP_Dt";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = dtCellInfoItem.samplNumArray[i] == 0 ? -140 : dtCellInfoItem.dpchRscpArray[i] / dtCellInfoItem.samplNumArray[i];
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = dtCellInfoItem.samplNumArray[i] == 0 ? -140 : dtCellInfoItem.dpchRscpArray[i] / dtCellInfoItem.samplNumArray[i];
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "TD BLER_Dt";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = dtCellInfoItem.samplNumArray[i] == 0 ? 0 : dtCellInfoItem.blerArray[i] / dtCellInfoItem.samplNumArray[i];
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = dtCellInfoItem.samplNumArray[i] == 0 ? 0 : dtCellInfoItem.blerArray[i] / dtCellInfoItem.samplNumArray[i];
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "过覆盖指数_Dt";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = dtCellInfoItem.samplNumArray[i] == 0 ? 0 : dtCellInfoItem.coverArray[i] / dtCellInfoItem.samplNumArray[i];
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = dtCellInfoItem.samplNumArray[i] == 0 ? 0 : dtCellInfoItem.coverArray[i] / dtCellInfoItem.samplNumArray[i];
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "DPCH C/I_Dt";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (dtCellInfoItem.samplNumArray[i] == 0 ? -25 : dtCellInfoItem.dpchc2iArray[i] / dtCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (dtCellInfoItem.samplNumArray[i] == 0 ? -25 : dtCellInfoItem.dpchc2iArray[i] / dtCellInfoItem.samplNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = tdAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "通信距离_Dt";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (Math.Round(dtCellInfoItem.samplNumArray[i] == 0 ? 0 : dtCellInfoItem.samplArray[i] / dtCellInfoItem.samplNumArray[i], 2)).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (Math.Round(dtCellInfoItem.samplNumArray[i] == 0 ? 0 : dtCellInfoItem.samplArray[i] / dtCellInfoItem.samplNumArray[i], 2)).ToString();
            }
        }

        private void cbbxSeries1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tdAntParaItem != null)
            {
                UpdateTableSeries();
            }
        }

        private void cbbxSeries2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tdAntParaItem != null)
            {
                UpdateTableSeries();
            }
        }

        private void UpdateTableSeries()
        {
            chartControl1.Series.Clear();
            Cursor.Current = Cursors.WaitCursor;
            #region 图例1内容
            switch (this.cbbxSeries1.Text)
            {
                case "PCCPCH RSCP_Scan":
                    {
                        DrawTable1Series(tdAntParaItem.scanCellInfoItem.rscpArray);
                    }
                    break;
                case "平滑PCCPCH RSCP_Scan":
                    {
                        DrawTable1Series(tdAntParaItem.scanCellInfoItem.rscpNewArray);
                    }
                    break;
                case "PCCPCH C/I_Scan":
                    {
                        DrawTable1Series(tdAntParaItem.scanCellInfoItem.pcchc2iArrayAll);
                    }
                    break;
                case "PCCPCH Ec/Io_Scan":
                    {
                        DrawTable1Series(tdAntParaItem.scanCellInfoItem.pcchc2iArrayAll_EcIo);
                    }
                    break;
                case "通信距离_Scan":
                    {
                        DrawTable1Series(tdAntParaItem.scanCellInfoItem.samplArray);
                    }
                    break;
                default:
                    break;
            }
            #endregion

            #region 图例2内容
            switch (this.cbbxSeries2.Text)
            {
                case "PCCPCH RSCP_Dt":
                    {
                        drawTable2Series(tdAntParaItem.dtCellInfoItem.rscpArray);
                    }
                    break;
                case "DPCH RSCP_Dt":
                    {
                        drawTable2Series(tdAntParaItem.dtCellInfoItem.dpchRscpArray);
                    }
                    break;
                case "TD BLER_Dt":
                    {
                        drawTable2Series(tdAntParaItem.dtCellInfoItem.blerArray);
                    }
                    break;
                case "过覆盖指数_Dt":
                    {
                        drawTable2Series(tdAntParaItem.dtCellInfoItem.coverArray);
                    }
                    break;
                case "DPCH C/I_Dt":
                    {
                        drawTable2Series(tdAntParaItem.dtCellInfoItem.dpchc2iArray);
                    }
                    break;
                case "通信距离_Dt":
                    {
                        drawTable2Series(tdAntParaItem.dtCellInfoItem.samplArray);
                    }
                    break;            
                default:
                    break;
            }
            #endregion
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        private void checkReverse()
        {
            if ((double)(((XYDiagram)chartControl1.Diagram).AxisY.Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = true;
            }

            if ((double)(((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = true;
            }
        }

        private void DrawTable1Series(int[] seriesValues)
        {
            int count = 0;
            SeriesPoint pt;
            chartControl1.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 181; i < 360; i++)
            {
                string arg = (-179 + count).ToString();
                double value = Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] * 1.0 / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
                count++;
            }
            for (int i = 1; i < 181; i++)
            {
                string arg = i.ToString();
                double value = Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] * 1.0 / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        private void DrawTable1Series(double[] seriesValues)
        {
            chartControl1.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        private void drawTable2Series(int[] seriesValues)
        {
            int count = 0;
            if (chartControl1.Series.Count > 1)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] * 1.0 / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] * 1.0 / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        private void drawTable2Series(double[] seriesValues)
        {
            int count = 0;
            if (chartControl1.Series.Count > 1)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / tdAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        private void drawAntRadarSeries(TdAntParaItem tdAntParaItem)
        {
            #region 扫频测试数据
            chartControlScan.Series.Clear();
            if (tdAntParaItem.scanCellInfoItem != null)
            {
                Series seriesScan = new Series();
                seriesScan.ShowInLegend = true;
                seriesScan.LegendText = "扫频PCCPCHRSCP";
                seriesScan.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesScanView = new RadarLineSeriesView();
                lineSeriesScanView.Color = Color.Blue;
                lineSeriesScanView.LineMarkerOptions.Size = 2;

                seriesScan.View = lineSeriesScanView;
                seriesScan.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesScan.Label.Visible = false;

                for (int i = 359; i >= 0; i--)
                {
                    seriesScan.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tdAntParaItem.scanCellInfoItem.rscpNewArray[i], 2)));
                }
                chartControlScan.Series.Insert(0, seriesScan);
            }
            int iMaxValue = -140;
            int iMinValue = 25;
            ZTAntFuncHelper.getMaxAndMinValue(tdAntParaItem.scanCellInfoItem.rscpNewArray, ref iMaxValue, ref iMinValue);
            ((RadarDiagram)chartControlScan.Diagram).AxisY.Range.MinValue = iMinValue;
            ((RadarDiagram)chartControlScan.Diagram).AxisY.Range.MaxValue = iMaxValue;
            ((RadarDiagram)chartControlScan.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControlScan.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControlScan.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            chartControlScan.Focus();
            #endregion

            #region 路测测试数据
            chartControlDt.Series.Clear();
            if (tdAntParaItem.dtCellInfoItem != null)
            {
                Series seriesDT = new Series();
                seriesDT.ShowInLegend = true;
                seriesDT.LegendText = "路测PCCPCHRSCP";
                seriesDT.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesDTView = new RadarLineSeriesView();
                lineSeriesDTView.Color = Color.DarkViolet;
                lineSeriesDTView.LineMarkerOptions.Size = 2;

                seriesDT.View = lineSeriesDTView;
                seriesDT.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesDT.Label.Visible = false;

                for (int i = 359; i >= 0; i--)
                {
                    seriesDT.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tdAntParaItem.dtCellInfoItem.rscpNewArray[i], 2)));
                }
                chartControlDt.Series.Insert(0, seriesDT);
            }
            iMaxValue = -140;
            iMinValue = 25;
            ZTAntFuncHelper.getMaxAndMinValue(tdAntParaItem.dtCellInfoItem.rscpNewArray, ref iMaxValue, ref iMinValue);
            ((RadarDiagram)chartControlDt.Diagram).AxisY.Range.MinValue = iMinValue;
            ((RadarDiagram)chartControlDt.Diagram).AxisY.Range.MaxValue = iMaxValue;
            ((RadarDiagram)chartControlDt.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControlDt.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControlDt.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            chartControlDt.Focus();
            #endregion
        }

        private void drawCellCoverRadarSeries(double[] seriesValues,int iType)
        {
            chartCellLine.Series.Clear();
            ZTTDAntMRAna.AnaRttdAoa anaRttdAoa = new ZTTDAntMRAna.AnaRttdAoa();
            anaRttdAoa.AnaRttdAoa90 = tdAntParaItem.mrData.AnaRttdAoa;
            double[] maxTaArray = iType == 0 ? ZTTDAntMRAna.GetDirMaxTa(anaRttdAoa) : seriesValues;

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int iDir = tdAntParaItem.方位角 < 0 ? 0 : tdAntParaItem.方位角;

            for (int i = 0; i < 72; i++)
            {
                int j = (i * 5 + iDir) % 360;
                series.Points.Add(new SeriesPoint(j, maxTaArray[i]));
            }
            chartCellLine.Series.Insert(0, series);

            ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MinValue = -1;
            ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(maxTaArray) + 300;
            ((RadarDiagram)chartCellLine.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartCellLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartCellLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartCellLine.Focus();
        }

        private void drawCellMRRadarSeries(TdAntParaItem tdAntParaItem)
        {
            if (tdAntParaItem == null)
            {
                return;
            }
            chartCellPoint.Series.Clear();
            int idx = 0;
            int iMaxValue = 0;
            string strCellPart = cbPart.Text;
            int iDir = tdAntParaItem.方位角 < 0 ? 0 : tdAntParaItem.方位角;

            foreach (int iColor in tdAntParaItem.tdMrData.aoaTaDic.Keys)
            {
                if (ZTAntFuncHelper.checkLegendIsVaild(iColor, strCellPart))
                {
                    AntLegend antLegend = ZTAntFuncHelper.GetMRDataAnaLegend(iColor);
                    Series series = new Series();
                    series.ShowInLegend = true;
                    series.LegendText = antLegend.strLegend;
                    series.PointOptions.PointView = PointView.Values;

                    RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                    pointSeriesView.Color = antLegend.colorType;
                    pointSeriesView.PointMarkerOptions.Size = 3;

                    series.View = pointSeriesView;
                    series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                    series.Label.Visible = false;

                    foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in tdAntParaItem.tdMrData.aoaTaDic[iColor])
                    {
                        int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);
                        int n = (aoaTa.iAoaId * 5 + iDir) % 360;

                        if (iDist > iMaxValue)
                            iMaxValue = iDist;
                        series.Points.Add(new SeriesPoint(n.ToString(), iDist.ToString()));
                    }

                    chartCellPoint.Series.Insert(idx, series);
                    idx++;
                }
            }


            //增加天线方位角连线
            Series seriesCell = ZTAntFuncHelper.DrawRadarLine(Color.Red, "工参", iDir, iMaxValue);
            chartCellPoint.Series.Insert(idx, seriesCell);
            idx++;

            int iMainDir = (tdAntParaItem.tdMrData.iMainDir + iDir) % 360;
            Series seriesMR = ZTAntFuncHelper.DrawRadarLine(Color.DarkBlue, "MR", iMainDir, iMaxValue);
            chartCellPoint.Series.Insert(idx, seriesMR);

            if (tdAntParaItem.tdMrData.aoaTaDic.Count > 0)
            {
                ((RadarDiagram)chartCellPoint.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartCellPoint.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;

                ((RadarDiagram)chartCellPoint.Diagram).AxisX.GridSpacing = 20;

                ((RadarDiagram)chartCellPoint.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartCellPoint.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartCellPoint.Series.Insert(0, series);
            }
            chartCellPoint.Focus();
        }


        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = this.cellAntParaInfoDic.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = this.cellAntParaInfoDic.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void outPutCsv_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        protected int iPointIndex = 0;
        private void miShowChart_Click(object sender, EventArgs e)
        {
            if (dataGridViewCell.SelectedRows.Count == 0)
            {
                return;
            }
            string cellLac_Ci = dataGridViewCell.SelectedRows[0].Tag as string;
            LaiKey cellKey = new LaiKey(Convert.ToInt32(cellLac_Ci.Split('_')[0]), Convert.ToInt32(cellLac_Ci.Split('_')[1]));
            if (!this.cellAntParaInfoDic.TryGetValue(cellKey, out tdAntParaItem))
            {
                return;
            }
            groupControl4.Text = string.Format("MR测量项数据图表({0})", tdAntParaItem.小区名称);
            this.xTabGSMAnt.SelectedTabPageIndex = 4;

            addTableSeries();
        }

        private void addTableSeries()
        {
            #region 图表数据整理
            Dictionary<string, object> txPowerDic = new Dictionary<string, object>();
            for (int i = 0; i < 28; i++)
            {
                int j = -50 + i * 3;
                string iKey = j.ToString();
                int iValue = tdAntParaItem.mrData.tdMRTxPowerItem.dataValue[i];
                if (!txPowerDic.ContainsKey(iKey))
                    txPowerDic.Add(iKey, iValue);
            }
            DrawTableSeries(txPowerDic, chartControlPower);

            Dictionary<string, object> rscpDic = new Dictionary<string, object>();
            for (int i = 0; i < 55; i++)
            {
                int j = i - 101;
                string iKey = j.ToString();
                int iValue = tdAntParaItem.mrData.tdMRRscpItem.dataValue[i];
                if (!rscpDic.ContainsKey(iKey))
                    rscpDic.Add(iKey, iValue);
            }
            DrawTableSeries(rscpDic, chartControlRsrp);

            Dictionary<string, object> sirDic = new Dictionary<string, object>();
            for (int i = 0; i < 64; i++)
            {
                string iKey = Math.Round((i - 23.0) / 2, 1).ToString();
                int iValue = tdAntParaItem.mrData.tdMRSirUlItem.dataValue[i];
                if (!sirDic.ContainsKey(iKey))
                    sirDic.Add(iKey, iValue);
            }
            DrawTableSeries(sirDic, chartControlSinr);

            Dictionary<string, object> aoaDic = new Dictionary<string, object>();
            for (int i = 0; i < 72; i++)
            {
                int j = (i + 1) * 5;
                string iKey = j.ToString();
                int iValue = tdAntParaItem.mrData.tdMRAoaItem.dataValue[i];
                if (!aoaDic.ContainsKey(iKey))
                    aoaDic.Add(iKey, iValue);
            }
            DrawTableSeries(aoaDic, chartControlAoa);

            Dictionary<string, object> taDic = new Dictionary<string, object>();
            for (int i = 0; i < 37; i++)
            {
                string iKey = ZTAntFuncHelper.calcDistByTdMrTa(i).ToString();
                int iValue = tdAntParaItem.mrData.tdMRTaItem.dataValue[i];
                if (!taDic.ContainsKey(iKey))
                    taDic.Add(iKey, iValue);
            }
            DrawTableSeries(taDic, chartControlTA);
            #endregion
        }

        /// <summary>
        /// 绘制柱状率图表
        /// </summary>
        private void DrawTableSeries(Dictionary<string, object> dataDic, ChartControl chartCtrl)
        {
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            foreach (string strKey in dataDic.Keys)
            {
                series.Points.Add(new SeriesPoint(strKey, dataDic[strKey].ToString()));
            }
            iPointIndex = 0;

            chartCtrl.Series.Clear();
            chartCtrl.Series.Insert(0, series);
            ((XYDiagram)chartCtrl.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartCtrl.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(dataDic);

            chartCtrl.Focus();
        }

        private void cbPart_SelectedIndexChanged(object sender, EventArgs e)
        {
            drawCellMRRadarSeries(tdAntParaItem);
            string cellPart = cbPart.Text;
            double[] maxTaArray = getMaxTaArray(tdAntParaItem, cellPart);
            drawCellCoverRadarSeries(maxTaArray, 1);
        }
        private double[] getMaxTaArray(TdAntParaItem tdAntParaItem, string cellPart)
        {
            List<ZTLteAntMRAna.AntMRAoaTa> mrList = new List<ZTLteAntMRAna.AntMRAoaTa>();
            foreach (int iColor in tdAntParaItem.tdMrData.aoaTaDic.Keys)
            {
                if (iColor == 2 && cellPart == "(0%,50%]")
                    continue;

                if (iColor == 3 && (cellPart == "(0%,50%]" || cellPart == "(0%,70%]"))
                    continue;

                if (iColor == 4 && (cellPart == "(0%,50%]" || cellPart == "(0%,70%]" || cellPart == "(0%,80%]"))
                    continue;

                mrList.AddRange(tdAntParaItem.tdMrData.aoaTaDic[iColor]);
            }

            int[,] AnaRttdAoaTmp = new int[44, 72];
            int iNum = mrList.Count;
            for (int i = 0; i < iNum; i++)
            {
                ZTLteAntMRAna.AntMRAoaTa at = mrList[i];
                AnaRttdAoaTmp[at.iTaId, at.iAoaId] = at.iCount;
            }
            ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
            ary.AnaRttdAoa90 = AnaRttdAoaTmp;
            double[] maxTaArray = ZTLteAntMRAna.getDirMaxTa(ary);

            return maxTaArray;
        }

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            if (dataGridViewCell.SelectedRows.Count == 0)
            {
                return;
            }
            string cellLac_Ci = dataGridViewCell.SelectedRows[0].Tag as string;
            LaiKey cellKey = new LaiKey(Convert.ToInt32(cellLac_Ci.Split('_')[0]), Convert.ToInt32(cellLac_Ci.Split('_')[1]));
            if (!this.cellAntParaInfoDic.TryGetValue(cellKey, out tdAntParaItem))
            {
                return;
            }
            if (tdAntParaItem.iFunc == 2)
            {
                getCellInfoByLacCi(tdAntParaItem);
            }
            if (tdAntParaItem.scanCellInfoItem == null || tdAntParaItem.dtCellInfoItem == null)
                return;
            CalcAntDetailValue();
            MainModel.SelectedTDCell = CellManager.GetInstance().GetTDCellByName(tdAntParaItem.小区名称);
            MainModel.MainForm.GetMapForm().GoToView(tdAntParaItem.天线经度, tdAntParaItem.天线纬度);
            AntLineLayer antLayer = mapForm.GetLayerBase(typeof(AntLineLayer)) as AntLineLayer;
            if (antLayer != null)
            {
                antLayer.Invalidate();
                antLayer.dtLongLatList = tdAntParaItem.dtCellAngleData.longLatDTList;
                antLayer.scanLongLatList = tdAntParaItem.scanCellAngleData.longLatDTList;
                antLayer.modelLongLatList = tdAntParaItem.dtCellAngleData.longLatModelList;
                antLayer.Invalidate();
            }
        }

        /// <summary>
        /// 计算权值
        /// </summary>
        private void CalcAntDetailValue()
        {
            AntParaItem antItem = new AntParaItem("D", 1, 1, 1, 1, "");
            antItem.Init(0, 90, 90, 0);

            double[] modelArray = antItem.getPowerArray();
            LongLat ll = new LongLat();
            ll.fLongitude = (float)(tdAntParaItem.天线经度);
            ll.fLatitude = (float)(tdAntParaItem.天线纬度);

            tdAntParaItem.dtCellAngleData.longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, modelArray, 10, -20, tdAntParaItem.方位角);
            int iMaxValue = -19;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(tdAntParaItem.scanCellInfoItem.rscpNewArray, ref iMaxValue, ref iMinValue);
            tdAntParaItem.scanCellAngleData.longLatDTList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, tdAntParaItem.scanCellInfoItem.rscpNewArray, iMaxValue, iMinValue, tdAntParaItem.方位角);

            iMaxValue = -19;
            iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(tdAntParaItem.dtCellInfoItem.rscpNewArray, ref iMaxValue, ref iMinValue);
            tdAntParaItem.dtCellAngleData.longLatDTList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, tdAntParaItem.dtCellInfoItem.rscpNewArray, iMaxValue, iMinValue, tdAntParaItem.方位角);


        }
    }
}
