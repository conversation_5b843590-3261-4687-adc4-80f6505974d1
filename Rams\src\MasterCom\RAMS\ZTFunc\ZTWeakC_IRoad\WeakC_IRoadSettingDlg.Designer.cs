﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakC_IRoadSettingDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.spinEditC_I = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDistanceLast = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.spinEditDistanceTP = new DevExpress.XtraEditors.SpinEdit();
            this.label6 = new System.Windows.Forms.Label();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label7 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditC_I.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceLast.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceTP.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(242, 71);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 21;
            this.label4.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(242, 43);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 20;
            this.label3.Text = "dBm";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(58, 71);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(71, 12);
            this.label2.TabIndex = 23;
            this.label2.Text = "持续距离 ≥";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(82, 43);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(47, 12);
            this.label8.TabIndex = 22;
            this.label8.Text = "场强 ≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(90, 15);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 22;
            this.label1.Text = "C/I ≤";
            // 
            // spinEditC_I
            // 
            this.spinEditC_I.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            this.spinEditC_I.Location = new System.Drawing.Point(136, 12);
            this.spinEditC_I.Name = "spinEditC_I";
            this.spinEditC_I.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditC_I.Properties.IsFloatValue = false;
            this.spinEditC_I.Properties.Mask.EditMask = "N00";
            this.spinEditC_I.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditC_I.Properties.MinValue = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.spinEditC_I.Size = new System.Drawing.Size(100, 21);
            this.spinEditC_I.TabIndex = 26;
            // 
            // spinEditRxLev
            // 
            this.spinEditRxLev.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.spinEditRxLev.Location = new System.Drawing.Point(136, 40);
            this.spinEditRxLev.Name = "spinEditRxLev";
            this.spinEditRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxLev.Properties.IsFloatValue = false;
            this.spinEditRxLev.Properties.Mask.EditMask = "N00";
            this.spinEditRxLev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRxLev.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.spinEditRxLev.Size = new System.Drawing.Size(100, 21);
            this.spinEditRxLev.TabIndex = 26;
            // 
            // spinEditDistanceLast
            // 
            this.spinEditDistanceLast.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditDistanceLast.Location = new System.Drawing.Point(136, 68);
            this.spinEditDistanceLast.Name = "spinEditDistanceLast";
            this.spinEditDistanceLast.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceLast.Properties.IsFloatValue = false;
            this.spinEditDistanceLast.Properties.Mask.EditMask = "N00";
            this.spinEditDistanceLast.Size = new System.Drawing.Size(100, 21);
            this.spinEditDistanceLast.TabIndex = 26;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(22, 100);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(107, 12);
            this.label5.TabIndex = 23;
            this.label5.Text = "相邻采样点距离 ≤";
            // 
            // spinEditDistanceTP
            // 
            this.spinEditDistanceTP.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditDistanceTP.Location = new System.Drawing.Point(136, 97);
            this.spinEditDistanceTP.Name = "spinEditDistanceTP";
            this.spinEditDistanceTP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceTP.Properties.IsFloatValue = false;
            this.spinEditDistanceTP.Properties.Mask.EditMask = "N00";
            this.spinEditDistanceTP.Size = new System.Drawing.Size(100, 21);
            this.spinEditDistanceTP.TabIndex = 26;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(242, 100);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 21;
            this.label6.Text = "米";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(92, 145);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 27;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(184, 145);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 27;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(242, 15);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 28;
            this.label7.Text = "dB";
            // 
            // WeakC_IRoadSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(295, 195);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.spinEditDistanceTP);
            this.Controls.Add(this.spinEditDistanceLast);
            this.Controls.Add(this.spinEditRxLev);
            this.Controls.Add(this.spinEditC_I);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label8);
            this.Name = "WeakC_IRoadSettingDlg";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditC_I.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceLast.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceTP.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit spinEditC_I;
        private DevExpress.XtraEditors.SpinEdit spinEditRxLev;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceLast;
        private System.Windows.Forms.Label label5;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceTP;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Label label7;
    }
}