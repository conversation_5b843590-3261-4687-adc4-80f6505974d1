﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class UnstabitilyCover : CauseBase
    {
        public override string Name
        {
            get { return "覆盖不稳定"; }
        }
        //[NonSerialized]
        //private ICell serverCell = null;
        //[NonSerialized]
        //private ICell nbCell = null;
        public float RSRPDiff { get; set; } = 10;
        public int Second { get; set; } = 5;
        public override string Desc
        {
            get
            {
                return string.Format("在{0}秒内，信号强度变化幅度超过{1}dB", Second, RSRPDiff);
            }
        }

        public override string Suggestion
        {
            get
            {
                return "检查周围是否有遮挡";
            }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            for (int i = 0; i < segItem.TestPoints.Count; i++)
            {
                TestPoint tp = segItem.TestPoints[i];
                if (!segItem.IsNeedJudge(tp))
                {
                    continue;
                }
                float? rsrp = (float?)GetRSRP(tp);
                if (rsrp != null)
                {
                    for (int j = i + 1; j < segItem.TestPoints.Count; j++)
                    {
                        TestPoint tpNext = segItem.TestPoints[j];
                        if (tpNext.Time - tp.Time > Second)
                        {//时间范围外
                            break;
                        }
                        else
                        {
                            setLowSpeedSegReason(segItem, i, tp, rsrp, j, tpNext);
                        }
                    }
                }
            }
            //foreach (TestPoint pnt in segItem.TestPoints)
            //{
            //    if (lastTime==0)
            //    {
            //        lastTime = pnt.Time;
            //    }
            //    if (!segItem.IsNeedJudge(pnt))
            //    {
            //        lastTime = pnt.Time;
            //        continue;
            //    }
            //    pnts.Add(pnt);
            //    float? rsrp = (float?)pnt["lte_RSRP"];
            //    if (rsrp!=null)
            //    {
            //        minRsrp = Math.Min((float)rsrp, minRsrp);
            //        maxRsrp = Math.Max((float)rsrp, maxRsrp);
            //    }
            //    if (lastTime > Second)
            //    {
            //        if (maxRsrp - minRsrp > RSRPDiff)
            //        {
            //            foreach (TestPoint pt in pnts)
            //            {
            //                UnstabitilyCover cln = this.Clone() as UnstabitilyCover;
            //                segItem.SetReason(new LowSpeedPointDetail(pt, cln));
            //            }
            //            pnts.Clear();
            //            minRsrp = float.MaxValue;
            //            maxRsrp = float.MinValue;
            //        }
            //    }
            //    lastTime = pnt.Time;
            //}
            //if (maxRsrp - minRsrp > RSRPDiff)
            //{
            //    foreach (TestPoint pt in pnts)
            //    {
            //        UnstabitilyCover cln = this.Clone() as UnstabitilyCover;
            //        segItem.SetReason(new LowSpeedPointDetail(pt, cln));
            //    }
            //}
        }

        private void setLowSpeedSegReason(LowSpeedSeg segItem, int i, TestPoint tp, float? rsrp, int j, TestPoint tpNext)
        {
            float? rsrpNext = (float?)GetRSRP(tpNext);
            if (rsrpNext != null && (Math.Abs((float)rsrpNext - (float)rsrp) > RSRPDiff))
            {
                UnstabitilyCover cln = this.Clone() as UnstabitilyCover;
                segItem.SetReason(new LowSpeedPointDetail(tp, cln));
                for (int k = j; k > i; k--)
                {
                    TestPoint p = segItem.TestPoints[k];
                    if (segItem.IsNeedJudge(p))
                    {
                        segItem.SetReason(new LowSpeedPointDetail(p
                            , (UnstabitilyCover)this.Clone()));
                    }
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpDiff"] = this.RSRPDiff;
                paramDic["second"] = this.Second;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPDiff = (float)value["rsrpDiff"];
                this.Second = (int)value["second"];
            }
        }
    }

}
