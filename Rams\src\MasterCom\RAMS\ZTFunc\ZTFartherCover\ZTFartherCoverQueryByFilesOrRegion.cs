﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTFartherCoverQueryByFilesOrRegion : DIYQueryFileOrRegion
    {
        protected FartherCoverCondition cond;
        protected Dictionary<string, FartherCoverInfo> nameFartherMap;
        protected List<FartherCoverInfo> fartherVec;

        public string themeName { get; set; }//默认选中指标

        public ZTFartherCoverQueryByFilesOrRegion(MainModel mainModel, EAnaType eType)
            : base(mainModel, eType)
        {
            themeName = "TD_LTE_RSRP";
            cond = new FartherCoverCondition();
            nameFartherMap = new Dictionary<string, FartherCoverInfo>();
            fartherVec = new List<FartherCoverInfo>();
        }

        public override string Name
        {
            get { return "超远覆盖"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22021, this.Name);
        }

        protected override bool isValidCondition()
        {
            FartherCoverSettingDlg dlg = new FartherCoverSettingDlg(cond);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            cond = dlg.GetCondition();
            return true;
        }

        protected override void DoSomethingBeforeQuery()
        {
            nameFartherMap.Clear();
            fartherVec.Clear();
        }

        protected override void DoWithDTData(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            ushort? tac = (ushort?)tp["lte_TAC"];
            int? eci = (int?)tp["lte_ECI"];
            int? earfcn = (int?)tp["lte_EARFCN"];
            int? pci = (int?)(short?)tp["lte_PCI"];
            if (rsrp == null) return;
            LTECell lteCell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (int?)tac, eci, earfcn, pci, tp.Longitude, tp.Latitude);
            if (lteCell == null) return;
            double distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lteCell.Longitude, lteCell.Latitude);
            addCoverInfo(lteCell, tp, (float)rsrp, distance);
            if (!cond.NBCellChecked) return;
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
                int? nEarfcn = (int?)tp["lte_NCell_EARFCN", i];
                short? nPci = (short?)tp["lte_NCell_PCI", i];

                if (nRsrp == null || nEarfcn == null || nPci == null || nRsrp < cond.RsrpThreshold)
                {
                    continue;
                }
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, nEarfcn, (int?)nPci, tp.Longitude, tp.Latitude);
                if (nbCell == null)
                {
                    continue;
                }
                distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nbCell.Longitude, nbCell.Latitude);
                addCoverInfo(nbCell, tp, (float)nRsrp, distance);
            }
        }

        protected void addCoverInfo(LTECell lteCell, TestPoint tp, float rsrp, double distance)
        {
            if (distance >= cond.DistanceMin && distance <= cond.DistanceMax && rsrp >= cond.RsrpThreshold)
            {
                FartherCoverInfo info;
                if (!nameFartherMap.TryGetValue(lteCell.Name, out info))
                {
                    info = new FartherCoverInfo(lteCell);
                    nameFartherMap.Add(lteCell.Name, info);
                }
                info.DealTestPoint(tp, rsrp, distance);
            }
        }

        protected override void DealResultAfterQuery()
        {
            foreach (KeyValuePair<string, FartherCoverInfo> pair in nameFartherMap)
            {
                if (pair.Value.SampleNum >= cond.SampleNum)
                {
                    fartherVec.Add(pair.Value);
                    pair.Value.SN = fartherVec.Count;
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override void FireShowForm()
        {
            object obj = MainModel.GetObjectFromBlackboard(typeof(FartherCoverInfoForm).FullName);
            FartherCoverInfoForm form = obj == null ? new FartherCoverInfoForm(MainModel) : obj as FartherCoverInfoForm;
            if (form.IsDisposed)
            {
                form = new FartherCoverInfoForm(MainModel);
            }
            form.FillData(fartherVec);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
            else
            {
                form.BringToFront();
            }
        }
    }

    //超远覆盖*********************************************************
    public class ZTFartherCoverQueryByFilesOrRegion_LteFdd : ZTFartherCoverQueryByFilesOrRegion
    {
        public ZTFartherCoverQueryByFilesOrRegion_LteFdd(MainModel mainModel, EAnaType eType)
            : base(mainModel, eType)
        {
            themeName = "LTE_FDD:RSRP";
        }

        public override string Name
        {
            get { return "超远覆盖_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26008, this.Name);
        }

        protected virtual bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }
        protected override void DoWithDTData(TestPoint tp)
        {
            if (!isValidTestPoint(tp)) return;
            float? rsrp = (float?)tp["lte_fdd_RSRP"];
            ushort? tac = (ushort?)tp["lte_fdd_TAC"];
            int? eci = (int?)tp["lte_fdd_ECI"];
            int? earfcn = (int?)tp["lte_fdd_EARFCN"];
            int? pci = (int?)(short?)tp["lte_fdd_PCI"];
            if (rsrp == null) return;
            LTECell lteCell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (int?)tac, eci, earfcn, pci, tp.Longitude, tp.Latitude);
            if (lteCell == null) return;
            double distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lteCell.Longitude, lteCell.Latitude);
            addCoverInfo(lteCell, tp, (float)rsrp, distance);
            if (!cond.NBCellChecked) return;
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)tp["lte_fdd_NCell_RSRP", i];
                int? nEarfcn = (int?)tp["lte_fdd_NCell_EARFCN", i];
                short? nPci = (short?)tp["lte_fdd_NCell_PCI", i];

                if (nRsrp == null || nEarfcn == null || nPci == null || nRsrp < cond.RsrpThreshold)
                {
                    continue;
                }
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, nEarfcn, (int?)nPci, tp.Longitude, tp.Latitude);
                if (nbCell == null)
                {
                    continue;
                }
                distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nbCell.Longitude, nbCell.Latitude);
                addCoverInfo(nbCell, tp, (float)nRsrp, distance);
            }
        }

    }

    public class ZTFartherCoverQueryByFilesOrRegion_LteFdd_VOLTE : ZTFartherCoverQueryByFilesOrRegion_LteFdd
    {
        readonly List<string> serviceTypes = null;
        public ZTFartherCoverQueryByFilesOrRegion_LteFdd_VOLTE(MainModel mainModel, EAnaType eType)
            : base(mainModel, eType)
        {
            serviceTypes = new List<string>();
            serviceTypes.Add(ServiceType.LTE_FDD_VOLTE.ToString());
            serviceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE.ToString());
        }
        public override string Name
        {
            get { return "VOLTE_FDD超远覆盖"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30010, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (serviceTypes.Count > 0 && !serviceTypes.Contains(tp.ServiceType.ToString())) return false;
            return true;
        }
    }
}
