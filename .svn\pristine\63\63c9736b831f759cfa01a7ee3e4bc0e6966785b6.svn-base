﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.ES.Data;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using System.Drawing.Drawing2D;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    class TDCellWeightRegionManager
    {
        private static TDCellWeightRegionManager instance = new TDCellWeightRegionManager();
        public static TDCellWeightRegionManager GetInstance()
        {
            return instance;
        }

        public MainModel MainModel
        {
            get { return MainModel.GetInstance(); }
        }

        // 构建CellWeightRegionInfo数组，主要是计算理想覆盖半径
        public void CreateTDCellWeightRegion(Dictionary<TDCell, double> tdCellWeightDic, 
            double minWeight, double maxWeight)
        {
            if (MainModel.cellWeightRegionInfoList.Count > 0) return;
            TDCellWeightRegionInfo.MaxWeight = maxWeight;
            TDCellWeightRegionInfo.MinWeight = minWeight;
            WaitBox.Show("正在计算小区权重区域...", CreateTDCellWeightRegion, tdCellWeightDic);
        }

        private void CreateTDCellWeightRegion(object o)
        {
            Dictionary<TDCell, double> tdCellWeightDic = (Dictionary<TDCell, double>)o;
            int counter = 0;
            double radius;
            foreach (TDCell tdCell in tdCellWeightDic.Keys)
            {
                WaitBox.ProgressPercent = (100 * ++counter / tdCellWeightDic.Count);
                if (MainModel.tdCellRadiusDic.ContainsKey(tdCell))
                {
                    radius = MainModel.tdCellRadiusDic[tdCell];
                }
                else
                {
                    radius = CfgDataProvider.CalculateRadius(tdCell, 3);
                    MainModel.tdCellRadiusDic[tdCell] = radius;
                }
                if (radius < 1) continue;
                MainModel.tdCellWeightRegionInfoList.Add(new TDCellWeightRegionInfo(
                    tdCell, tdCellWeightDic[tdCell], radius));
            }
            System.Threading.Thread.Sleep(1000);
            WaitBox.Close();
        }

    } // end class

    public class TDCellWeightRegionInfo
    {
        public TDCell cell { get; set; }
        public double weight { get; set; }
        public double radius { get; set; }

        public TDCellWeightRegionInfo(TDCell cell, double weight, double radius)
        {
            this.cell = cell;
            this.weight = weight;
            this.radius = radius;
        }

        public double Ratio
        {
            get
            {
                return (weight - MinWeight) / (MaxWeight - MinWeight);
            }
        }

        public static double MaxWeight { get; set; }

        public static double MinWeight { get; set; }
    }

} // end namespace
