﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRtpPacketsLostMessageSetConditionForm : BaseDialog
    {
        public ZTRtpPacketsLostMessageSetConditionForm()
        {
            InitializeComponent();
        }

        public void GetCondition(out ZTRtpPacketsLostMessageConditon condition)
        {
            condition = new ZTRtpPacketsLostMessageConditon();
            condition.LossTime = (float)numSiteLossTime.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
