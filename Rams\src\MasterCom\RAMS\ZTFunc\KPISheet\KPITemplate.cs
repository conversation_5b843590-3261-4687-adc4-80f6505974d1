﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class KPITemplate
    {
        public KPITemplate()
        { }
        public KPITemplate(string NetType)
        {
            this.NetType = NetType;
        }

        public string NetType
        {
            get;
            set;
        }

        public override string ToString()
        {
            return this.NetType;
        }

        /// <summary>
        /// 列名信息
        /// </summary>
        private List<KPISubTemplate> templateList = new List<KPISubTemplate>();

        public List<KPISubTemplate> TemplateList
        {
            get { return templateList; }
        }

        //网络类型、模版名与列名List
        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["NetType"] = this.NetType;
                List<object> displayParams = new List<object>();
                foreach (KPISubTemplate col in this.templateList)
                {
                    displayParams.Add(col.CfgParam);
                }
                paramDic["NetTypeDic"] = displayParams;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.NetType = value["NetType"].ToString();
                templateList = new List<KPISubTemplate>();
                List<object> list = value["NetTypeDic"] as List<object>;
                foreach (object objParam in list)
                {
                    KPISubTemplate col = new KPISubTemplate();
                    col.CfgParam = objParam as Dictionary<string, object>;
                    templateList.Add(col);
                }
            }
        }

        internal bool AddTemplate(KPISubTemplate subTemp)
        {
            KPISubTemplate existCol = templateList.Find(
            delegate(KPISubTemplate c) { return c.ParamKey == subTemp.ParamKey; });
            if (existCol != null)
            {
                return false;
            }
            else
            {
                templateList.Add(subTemp);
                return true;
            }
        }

    }
}
