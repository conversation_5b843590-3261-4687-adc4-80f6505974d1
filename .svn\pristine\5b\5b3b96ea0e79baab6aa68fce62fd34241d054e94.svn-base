﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ModRoadItemBase
    {
        public List<TestPoint> TestPoints
        {
            get;
            private set;
        }

        public double Length
        {
            get;
            private set;
        }

        public string RoadDesc
        {
            get;
            private set;
        }

        public string FileName
        {
            get;
            private set;
        }

        public int SampleCount
        {
            get;
            private set;
        }

        public ModRoadItemBase(TestPoint firstPoint, string fileName, ModRoadConditionBase cond)
        {
            this.cond = cond;
            this.FileName = fileName;
            this.TestPoints = new List<TestPoint>();
            TestPoints.Add(firstPoint);
            ++sampleCnt;
        }

        public bool AddTestPoint(TestPoint tp)
        {
            TestPoint lastTp = TestPoints[TestPoints.Count - 1];
            double interval = MathFuncs.GetDistance(lastTp.Longitude, lastTp.Latitude, tp.Longitude, tp.Latitude);
            if (interval > cond.SampleInterval)
            {
                return false;
            }
            length += interval;

            TestPoints.Add(tp);
            ++sampleCnt;
            return true;
        }

        public void CloseRoad()
        {
            TestPoint midTp = TestPoints[TestPoints.Count / 2];
            RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(midTp.Longitude, midTp.Latitude);
            Length = length;
            SampleCount = sampleCnt;

            ProcessInfo();
        }

        public virtual void Clear()
        {
            TestPoints.Clear();
        }

        protected virtual void ProcessInfo()
        {
        }

        protected ModRoadConditionBase cond;
        private double length;
        private int sampleCnt;
    }

    public class ModRoadConditionBase
    {
        public double RoadLength { get; set; } = 50;
        public double SampleInterval { get; set; } = 20;
        public double MaxRxlev { get; set; } = -95;
    }
}
