﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYNoCoverRoadByRegion_NRScan : DIYNoCoverRoadByRegion_GScan
    {
        private static DIYNoCoverRoadByRegion_NRScan intance = null;
        public new static DIYNoCoverRoadByRegion_NRScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYNoCoverRoadByRegion_NRScan();
                    }
                }
            }
            return intance;
        }

        protected DIYNoCoverRoadByRegion_NRScan()
            : base()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖路段分析_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36001, this.Name);//////
        }

        protected override void getReadyBeforeQuery()
        {
            Columns = new List<string>();
            Columns = NRTpHelper.InitNrScanParamBackground();
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);
        }

        protected override bool filterFile(FileInfo fileInfo)
        {
            return false;
        }

        private LTEScanNoCoverRoadSetDlg setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new LTEScanNoCoverRoadSetDlg();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                setForm.GetFilterCondition(out var rsrp, out var distance);
                rxLevThreshold = rsrp;
                distanceLast = distance;
                return true;
            }
            return false;
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, 0);
            if (rsrp != null && rsrp <= rxLevThreshold)
            {
                return true;
            }
            return false;
        }

        protected override NoCoverRoad init(TestPoint testPoint)
        {
            return new NRScanNoCoverRoad(testPoint);
        }

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }
    }

    public class NRScanNoCoverRoad : NoCoverRoad
    {
        public NRScanNoCoverRoad(TestPoint tp)
        {
            AddTestPoint(tp, GSMFreqBandType.All);
        }

        public override void AddTestPoint(TestPoint tp, GSMFreqBandType bandtype)
        {
            float? rsrpTmp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, 0);
            if (rsrpTmp == null)
            {
                return;
            }
            float rsrp = (float)rsrpTmp;

            addValidData(tp, rsrp);
        }
    }
}
