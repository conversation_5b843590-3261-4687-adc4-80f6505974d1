﻿namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    partial class ReasonPnlHandoverProblem
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label3 = new System.Windows.Forms.Label();
            this.timePersistMax = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.timeLimitMax = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.timePersistMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeLimitMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.timeLimitMax);
            this.grp.Controls.Add(this.timePersistMax);
            this.grp.Controls.Add(this.label4);
            this.grp.Controls.Add(this.label5);
            this.grp.Controls.Add(this.label2);
            this.grp.Controls.Add(this.label1);
            this.grp.Controls.Add(this.label3);
            this.grp.Size = new System.Drawing.Size(654, 127);
            this.grp.Text = "切换不合理";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(36, 40);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "在质差点的前";
            // 
            // timePersistMax
            // 
            this.timePersistMax.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.timePersistMax.Location = new System.Drawing.Point(299, 92);
            this.timePersistMax.Name = "timePersistMax";
            this.timePersistMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timePersistMax.Properties.MaxValue = new decimal(new int[] {
            166,
            0,
            0,
            0});
            this.timePersistMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.timePersistMax.Size = new System.Drawing.Size(75, 21);
            this.timePersistMax.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(200, 40);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(269, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "秒内，存在切换不合理的情况，不合理定义如下：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(38, 68);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(227, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "(1)切换后2秒的电平均值比前2秒均值差的";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(38, 97);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(257, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "(2)切换后主服小区电平小于邻小区电平3dB持续";
            // 
            // timeLimitMax
            // 
            this.timeLimitMax.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.timeLimitMax.Location = new System.Drawing.Point(119, 35);
            this.timeLimitMax.Name = "timeLimitMax";
            this.timeLimitMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeLimitMax.Properties.MaxValue = new decimal(new int[] {
            166,
            0,
            0,
            0});
            this.timeLimitMax.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.timeLimitMax.Size = new System.Drawing.Size(75, 21);
            this.timeLimitMax.TabIndex = 1;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(381, 97);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "秒";
            // 
            // ReasonPnlHandoverProblem
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlHandoverProblem";
            this.Size = new System.Drawing.Size(654, 127);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.timePersistMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeLimitMax.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit timePersistMax;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit timeLimitMax;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label2;
    }
}
