﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class NbIotStationAcceptManagerXJ : NbIotStationAcceptManager
    {
        protected new virtual List<NbIotCellAcceptKpiAnaXJ> getAcceptAnaList(LTEBTSType btsType)
        {
            List<NbIotCellAcceptKpiAnaXJ> acceptAnaList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptAnaList = new List<NbIotCellAcceptKpiAnaXJ>()
                {
                   new NbIotAcpAutoAttachRateXJ(),
                   new NbIotAcpAutoInStationReSelectXJ(),
                   new NbIotAcpAutoBetweenStationReSelectXJ(),
                   new NbIotAcpAutoPingRateXJ(),
                   new NbIotAcpAutoPingDelayXJ(),              
                   new NbIotAcpAutoULSpeedXJ(),
                   new NbIotAcpAutoDLSpeedXJ(),
                   new NbIotAcpAutoCoverPictureXJ(),
                };
            }
            else
            {
                //移动白皮书暂未写NB室分站
            }
            return acceptAnaList;
        }

        /// <summary>
        /// 分析文件
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="fileManager"></param>
        protected override void anaWithOtherFile(FileInfo fileInfo, DTFileDataManager fileManager)
        {
            LTECell targetCell = GetFileTestCell(fileManager);
            if (targetCell == null)
            {
                reportInfo(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                return;
            }

            List<NbIotCellAcceptKpiAnaXJ> acceptAnaList = getAcceptAnaList(targetCell.Type);//根据小区类型获取业务分析类
            foreach (NbIotCellAcceptKpiAnaXJ acp in acceptAnaList)
            {
                if (acp.IsValidFile(fileInfo))
                {
                    doStatWithData(acp, fileInfo, fileManager, targetCell);
                    //目前没有一个文件对应多个业务的情况,执行完一个业务就返回
                    break;
                }
            }
        }

        protected void doStatWithData(NbIotCellAcceptKpiAnaXJ acp, FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            if (NBIotAcceptFileInfo == null)
            {
                NBIotAcceptFileInfo = new NBIotCellAcceptFileInfo(fileInfo, targetCell);
            }
            Dictionary<NbIotKpiKeyXJ, object> kpiInfoDic = acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
            if (kpiInfoDic.Count > 0)
            {
                foreach (NbIotKpiKeyXJ key in kpiInfoDic.Keys)
                {
                    object valueObj = kpiInfoDic[key];
                    if (valueObj is double)
                    {
                        double valueDouble = (double)valueObj;
                        if (valueDouble == double.MinValue)
                        {
                            valueObj = double.NaN;
                        }
                        else
                        {
                            valueObj = Math.Round(valueDouble, 4);
                        }
                    }

                    NBIotAcceptFileInfo.AcceptKpiDic.Add((uint)key, valueObj);
                }
            }
        }
    }

    public static class NbIotStaionAcceptResultHelperXJ
    {
        public static NbIotOutDoorBtsAcceptInfoXJ GetOutDoorBtsResultByBgData(List<BackgroundResult> bgResultList
            , int enodeBid)
        {
            NbIotOutDoorBtsAcceptInfoXJ btsAcceptInfo = null;
            foreach (BackgroundResult bgResult in bgResultList)
            {
                if (bgResult.StrDesc == "室外")
                {
                    btsAcceptInfo = addOutdoorCellAcceptInfo(enodeBid, btsAcceptInfo, bgResult);
                }
            }
            //新疆报告暂未包含是否通过结论,但工参表中需记录是否通过验收,还是需要判断各项是否合格
            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static NbIotOutDoorBtsAcceptInfoXJ addOutdoorCellAcceptInfo(int enodeBid, NbIotOutDoorBtsAcceptInfoXJ btsAcceptInfo, BackgroundResult bgResult)
        {
            DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
            LTECell nbiotCell = CellManager.GetInstance().GetLTECell(bgResultTime, bgResult.LAC, bgResult.CI);
            if (nbiotCell == null || nbiotCell.BelongBTS.BTSID != enodeBid)
            {
                reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
            }
            else
            {
                if (btsAcceptInfo == null)
                {
                    //工参中的基站名,共站时不一定是NB的基站名
                    btsAcceptInfo = new NbIotOutDoorBtsAcceptInfoXJ(nbiotCell.BelongBTS, nbiotCell.BelongBTS.Name.Trim());
                }

                try
                {
                    //解析出image中保存的结果数据
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    NbIotOutDoorCellAcceptInfoXJ cellAcceptInfo;
                    if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(nbiotCell.CellID, out cellAcceptInfo))
                    {
                        cellAcceptInfo = new NbIotOutDoorCellAcceptInfoXJ(nbiotCell);
                        btsAcceptInfo.CellsAcceptDic.Add(nbiotCell.CellID, cellAcceptInfo);
                    }
                    cellAcceptInfo.AddAcceptKpiInfo(kpiDic);
                }
                catch (Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }
            return btsAcceptInfo;
        }

        public static NBIotInDoorBtsAcceptInfo GetInDoorBtsResultByBgData(List<BackgroundResult> bgResultList
            , int enodeBid)
        {
            NBIotInDoorBtsAcceptInfo btsAcceptInfo = null;
            foreach (BackgroundResult bgResult in bgResultList)
            {
                if (bgResult.StrDesc != "室外")
                {
                    btsAcceptInfo = GetInDoorBtsResultByBgData(enodeBid, btsAcceptInfo, bgResult);
                }
            }
            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static NBIotInDoorBtsAcceptInfo GetInDoorBtsResultByBgData(int enodeBid, NBIotInDoorBtsAcceptInfo btsAcceptInfo, BackgroundResult bgResult)
        {
            DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
            LTECell nbiotCell = CellManager.GetInstance().GetLTECell(bgResultTime, bgResult.LAC, bgResult.CI);
            if (nbiotCell == null || nbiotCell.BelongBTS.BTSID != enodeBid)
            {
                reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
            }
            else
            {
                if (btsAcceptInfo == null)
                {
                    btsAcceptInfo = new NBIotInDoorBtsAcceptInfo(nbiotCell.BelongBTS);
                    btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgResultTime);
                }
                btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgResultTime);

                try
                {
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    NBIotInDoorCellAcceptInfo cellAcceptInfo;
                    if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(nbiotCell.CellID, out cellAcceptInfo))
                    {
                        cellAcceptInfo = new NBIotInDoorCellAcceptInfo(nbiotCell);
                        btsAcceptInfo.CellsAcceptDic.Add(nbiotCell.CellID, cellAcceptInfo);
                    }
                    cellAcceptInfo.AddAcceptKpiInfo(bgResult.FileName, kpiDic);
                }
                catch (Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }
            return btsAcceptInfo;
        }

        public static void reportBackgroundInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }

        public static void reportBackgroundError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
}
