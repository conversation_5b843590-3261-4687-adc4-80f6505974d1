﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Diagnostics;
using System.Drawing.Drawing2D;


namespace DataGridView_MergeDemo
{
    public class HMergedCell : DataGridViewTextBoxCell
    {
        /// <summary>
        /// Column Index of the left-most cell to be merged.
        /// This cell controls the merged text.
        /// </summary>
        public int LeftColumn { get; set; }

        /// <summary>
        /// Column Index of the right-most cell to be merged
        /// </summary>
        public int RightColumn { get; set; }

        protected override void Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, int rowIndex, DataGridViewElementStates cellState, object value, object formattedValue, string errorText, DataGridViewCellStyle cellStyle, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts paintParts)
        {
            try
            {
                int i;
                int nWidth;
                int nWidthLeft;
                string strText;

                // Draw the background
                graphics.FillRectangle(new SolidBrush(SystemColors.Control), cellBounds);

                // Draw the separator for rows
                graphics.DrawLine(new Pen(new SolidBrush(SystemColors.ControlDark)), cellBounds.Left, cellBounds.Bottom - 1, cellBounds.Right, cellBounds.Bottom - 1);

                // Draw the right vertical line for the cell
                if (ColumnIndex == RightColumn)
                    graphics.DrawLine(new Pen(new SolidBrush(SystemColors.ControlDark)), cellBounds.Right - 1, cellBounds.Top, cellBounds.Right - 1, cellBounds.Bottom);

                // Draw the text
                RectangleF rectDest;
                StringFormat sf = new StringFormat();
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;
                sf.Trimming = StringTrimming.EllipsisCharacter;

                // Determine the total width of the merged cell
                nWidth = 0;
                for (i = LeftColumn; i <= RightColumn; i++)
                    nWidth += this.OwningRow.Cells[i].Size.Width;

                // Determine the width before the current cell.
                nWidthLeft = 0;
                for (i = LeftColumn; i < ColumnIndex; i++)
                    nWidthLeft += this.OwningRow.Cells[i].Size.Width;

                // Retrieve the text to be displayed
                strText = this.OwningRow.Cells[LeftColumn].Value.ToString();

                rectDest = new RectangleF(cellBounds.Left - nWidthLeft, cellBounds.Top, nWidth, cellBounds.Height);
                graphics.DrawString(strText, new Font("Arial", 10, FontStyle.Regular), Brushes.Black, rectDest, sf);
            }
            catch (Exception ex)
            {
                Trace.WriteLine(ex.ToString());
            }
        }

    }// class
}