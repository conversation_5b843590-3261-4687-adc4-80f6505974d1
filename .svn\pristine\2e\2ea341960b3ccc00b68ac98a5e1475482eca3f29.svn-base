﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using System.IO;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_MCCFILE = 0xac;
    }

    public class DownLoadMccFile : DownLoadFileByPath
    {
        public static readonly string UserDataPath = Path.Combine(Application.StartupPath, "userData");

        private bool bSuccess = false;
        public bool BSuccess
        {
            get { return bSuccess; }
            private set { bSuccess = value; }
        }

        private string cityName;

        public DownLoadMccFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }

            cityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);

            WaitBox.Show(string.Format("开始下载 {0} 工参文件...", cityName), queryInThread, clientProxy);
        }

        private void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = o as ClientProxy;

                prepareSearchPackage(clientProxy.Package);
                clientProxy.Send();

                foreach (string file in Directory.GetFiles(UserDataPath, "CellData *.*.mcc"))
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch
                    {
                    	//continue
                    }
                }
                string fileName = Path.Combine(UserDataPath, string.Format("CellData {0:yyyyMMdd}.{1}.mcc.zip", DateTime.Now, cityName));
                receiveFile(clientProxy, fileName);

                afterReceive(fileName);
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        protected void prepareSearchPackage(Package package)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_MCCFILE;
            package.Content.PrepareAddParam();
            package.Content.AddParam(cityName);
        }

        protected override void receiveFile(ClientProxy clientProxy, string fileName)
        {
            FileStream fileStream = new FileStream(fileName, FileMode.Create);
            try
            {
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.FileBegin || package.Content.Type == ResponseType.FileContinue || package.Content.Type == ResponseType.FileEnd)
                    {
                        package.Content.GetParamString();
                        package.Content.GetParamInt();
                        package.Content.GetParamInt(); //length
                        fileStream.Write(package.Content.Buff, package.Content.CurOffset, package.Content.Buff.Length - package.Content.CurOffset);
                        if (package.Content.Type == ResponseType.FileEnd)
                        {
                            break;
                        }
                    }
                    else if (package.Content.Type == ResponseType.FileNotFound || package.Content.Type == ResponseType.Error)
                    {
                        fileStream.Close();
                        File.Delete(fileName);
                        break;
                    }
                }
            }
            finally
            {
                fileStream.Close();
            }
        }

        private void afterReceive(string fileName)
        {
            UnZipClass zip = new UnZipClass();
            if (File.Exists(fileName) && zip.UnZip(fileName, Path.GetDirectoryName(fileName)))
            {
                try
                {
                    BSuccess = true;

                    File.Delete(fileName);
                }
                catch
                {
                    //continue
                }
            }
        }
    }
}
