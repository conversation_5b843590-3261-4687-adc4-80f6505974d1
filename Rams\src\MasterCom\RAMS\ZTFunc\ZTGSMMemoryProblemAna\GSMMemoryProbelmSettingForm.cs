﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMMemoryProbelmSettingForm : MasterCom.RAMS.Func.BaseDialog
    {
        private int TimeSpan = 10;
        private double DisMin = 0;
        private double DisMax = 1200;
        private int SixMin = 30;
        private int SixMax = 100;

        public GSMMemoryProbelmSettingForm()
            : base()
        {
            InitializeComponent();
            nUP_TimeSpan.Value = 10;
        }

        public void getTimeSpan(out int timeSpan, out double disMin, out double disMax, out int sixMin, out int sixMax)
        {
            timeSpan = this.TimeSpan;
            disMin = this.DisMin;
            disMax = this.DisMax;
            sixMin = this.SixMin;
            sixMax = this.SixMax;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {

            int timeSpan = Convert.ToInt32(nUP_TimeSpan.Value);
            if (timeSpan == 0)
            {
                MessageBox.Show("设置的时间间隔不符合规则");
                return;
            }

            int disMin = Convert.ToInt32(nUP_DisMin.Value);
            int disMax = Convert.ToInt32(nUP_DisMax.Value);

            if (disMin >= disMax)
            {
                MessageBox.Show("设置的小区间距范围格式不正确，最小值大于或等于了最大值");
                return;
            }

            int sixMin = Convert.ToInt32(nUP_SixMin.Value);
            int sixMax = Convert.ToInt32(nUP_SixMax.Value);

            if (sixMin >= sixMax)
            {
                MessageBox.Show("设置的六强比例范围格式不正确，最小值大于或等于了最大值");
                return;
            }

            this.TimeSpan = timeSpan;
            this.DisMin = Convert.ToDouble(disMin);
            this.DisMax = Convert.ToDouble(disMax);
            this.SixMin = sixMin;
            this.SixMax = sixMax;
            this.DialogResult = DialogResult.OK;

        }

        private void btnCancle_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
