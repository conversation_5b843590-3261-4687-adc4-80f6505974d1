<!DOCTYPE html>
<!--
Put this file into "%startup directory%\config\".
This file must be named "SosoStreetView.html".
-->
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title>街景</title>
		<script charset="utf-8" src="http://map.qq.com/api/js?v=2.exp&libraries=convertor&key=9f8adb23ad00d400f82b07fa8a54c852"></script>
		<style type="text/css">
			html, body {
				margin:0px;
				padding:0px;
				height: 100%;
			}
			body, button, input, select, textarea {
				font: 12px/16px Verdana, Helvetica, Arial, sans-serif;
			}
			.startup {
				top: 30%;
				left: 110px;
				font-size: 20px;
				line-height: 25px;
				font-weight: bold;
			}
			.notfound {
				right: 10px;
				bottom: 1%;
				font-size: 20px;
				line-height: 25px;
				font-weight: bold;
			}
			.road {
				bottom: 6%;
				left: 10px;
			}
			.pano {
				width: 100%;
				height: 100%;
				position: fixed;
			}
			.tips {
				padding: 0 3px;
				position: absolute;
				float: left;
				z-index: 10;
				background-color: white;
				filter: Alpha(Opacity=30);
			}
		</style>
		<script>
			var pano;
			var geoCoder;
			var firstCall;

			function init() {
				service = new qq.maps.PanoramaService();
				pano = new qq.maps.Panorama(document.getElementById('pano'), {
					visible: true,
					scrollwheel: true,
					disableCompass: false,
					disableMove: false,
					disableFullScreen: false,
					pov:{
						heading:20,
						pitch:15
					},
					zoom:1
				});
				geoCoder = new qq.maps.Geocoder();
				geoCoder.setComplete(function(result) {
					document.getElementById("road").innerText = result.detail.addressComponents.street;
				});
				qq.maps.event.addListener(pano, "position_changed", function() {
					geoCoder.getAddress(pano.getPosition());
					document.getElementById("notfound").style.display = "none";
				});
				document.getElementById("road").style.display = "none";
				document.getElementById("notfound").style.display = "none";
				firstCall = true;
			}

			function setPanoCallBack(pt, radius)
			{
				var lng = pt.lng;
				var lat = pt.lat;
				var service = new qq.maps.PanoramaService();
				service.getPano(pt, radius, function(pos) {
					if (firstCall && pos == null)
					{
						document.getElementById("startup").style.display = "none";
						document.getElementById("notfound").style.display = "inline";
					}
					else if (firstCall && pos != null)
					{
						document.getElementById("startup").style.display = "none";
						document.getElementById("road").style.display = "inline";
						document.getElementById("road").innerText = pos.description;
						pano.setPano(pos.svid);
						setPov(pos.latlng.lng, pos.latlng.lat, lng, lat);
						firstCall = false;
					}
					else if (!firstCall && pos == null)
					{
						document.getElementById("notfound").style.display = "inline";
					}
					else if (!firstCall && pos != null)
					{
						pano.setPano(pos.svid);
						setPov(pos.latlng.lng, pos.latlng.lat, lng, lat);
						document.getElementById("notfound").style.display = "none";
					}
				});
			}
			
			function setPano(lng, lat, radius)
			{
				qq.maps.convertor.translate(new qq.maps.LatLng(lat, lng), 1, function(res) {
					setPanoCallBack(res[0], 30);
				});
			}

			function setPov(x1, y1, x2, y2)
			{
				var alpha = Math.acos((y2 - y1) / Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2)));
				if (x2 - x1 < 0) {
					alpha = Math.PI * 2 - alpha;
				}
				pano.setPov({heading : alpha / Math.PI * 180, pitch : 0});
			}
		</script>
	</head>
	<body onLoad="init()">
		<div id="pano" class="pano"></div>
		<label id="startup" class="startup tips">点击地图任意位置</label>
		<label id="notfound" class="notfound tips">未找到街景</label>
		<label id="road" class="road tips"></label>
	</body>
</html>
