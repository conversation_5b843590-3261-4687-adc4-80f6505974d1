﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using DBDataViewer;

namespace MasterCom.RAMS.Net
{
    public class ZTMultiGridCoverAna : DIYGridQuery
    {
        public ZTMultiGridCoverAna(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "综合覆盖率_GSM/TD(按栅格)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12010, 12091, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return isContainDbRect(grid.Bounds);
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add("Mx_640101");
            formulaSet.Add("Mx_640102");
            formulaSet.Add("Tx_5C04030A");
            formulaSet.Add("Tx_5C04030D");
            formulaSet.Add("Tx_5C040374");
            formulaSet.Add("Tx_5C040316");
            formulaSet.Add("Tx_5C040375");
            formulaSet.Add("Wx_710A3F");
            formulaSet.Add("Wx_6A0A0A");
            formulaSet.Add("Wx_710A67");
            formulaSet.Add("Cx_5B060526");
            formulaSet.Add("Cx_6C062C");
            formulaSet.Add("Cx_5B06012C");
            formulaSet.Add("Ex_5E09010B");
            formulaSet.Add("Ex_5E09050F");
            formulaSet.Add("Ex_5E09040F");
            formulaSet.Add("Ex_5E09013F");
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        #region 全局变量
        string strCityName = "";
        string strStartTime = "";
        List<int> iCarrList = null;
        string strCarrName = "";
        DTDataHeaderManager headerManager { get; set; }
        public List<MultiGridCoverAnaInfo> multiGridCoverAnaInfoList { get; set; }
        Dictionary<string, MultiGridCoverAnaInfo> cityComCoverRateInfoDic = null;
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        Dictionary<CenterLongLat, SizeGrid> gridInexDic = null;
        public List<GridSampleInfo> gridSampleList { get; set; }
        #endregion

        ZTMultiGridCoverAnaSetForm gridSetForm = null;
        protected override void query()
        {
            gridSetForm = new ZTMultiGridCoverAnaSetForm();
            if (gridSetForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            
            iCarrList = new List<int>();
            iCarrList.AddRange(condition.CarrierTypes);
            strStartTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            gridSampleList = new List<GridSampleInfo>();
            cityComCoverRateInfoDic = new Dictionary<string, MultiGridCoverAnaInfo>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            InitRegionMop2();
            multiGridCoverAnaInfoList = new List<MultiGridCoverAnaInfo>();
            foreach (int districtID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                queryDistrictData(districtID);
                multiGridCoverAnaInfoList.Add(cityComCoverRateInfoDic[strCityName]);
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                headerManager = new DTDataHeaderManager();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                gridInexDic = new Dictionary<CenterLongLat, SizeGrid>();

                string statImgIDSet = getStatImgNeededTriadID();
                foreach (int iCarr in iCarrList)
                {
                    if (iCarr == 1)
                    {
                        strCarrName = " 移动 ";
                    }
                    else if (iCarr == 2)
                    {
                        strCarrName = " 联通 ";
                    } 
                    else
                    {
                        strCarrName = " 电信 ";
                    }
                    condition.CarrierTypes.Clear();
                    condition.CarrierTypes.Add(iCarr);
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }

                    changGridDataFormula();

                    gridInexDic.Clear();
                    MainModel.DTDataManager.Clear();
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedEvents.Clear();
                    MainModel.SelectedMessage = null;
                    MainModel.CurGridCoverData = null;
                    MainModel.CurGridColorUnitMatrix = null;
                    MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                    
                }
               
                WaitBox.Text = strCityName + " 数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }
        /// <summary>
        /// 重写接收方法(优化)
        /// </summary>
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            GridMatrix<ColorUnit> colorMatrix = reservedParams[0] as GridMatrix<ColorUnit>;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = strCityName + " 正在从服务器接收" + strCarrName + "数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(colorMatrix, package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                int tmp = (int)(Math.Log(counter++) * 10);
                if (tmp < 95 && tmp > 0 && curPercent != tmp)
                {
                    WaitBox.ProgressPercent = tmp;
                }
                else if (tmp > 95)
                {
                    curPercent = 5;
                    counter = 0;
                }
            }
        }

        private void fillData(GridMatrix<ColorUnit> colorMatrix, Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            ColorUnit cu = new ColorUnit();
            cu.LTLng = lng;
            cu.LTLat = lat;
            int rAt, cAt;

            CenterLongLat cll = new CenterLongLat(cu.CenterLng, cu.CenterLat);
            if (!gridInexDic.ContainsKey(cll))
            {
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                SizeGrid sGrid = new SizeGrid(rAt, cAt);
                gridInexDic.Add(cll, sGrid);
            }
            else
            {
                rAt = gridInexDic[cll].rAt;
                cAt = gridInexDic[cll].cAt;
            }
            cu = colorMatrix[rAt, cAt];
            if (cu == null)
            {
                if (isValidStatImg(lng, lat))
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    colorMatrix[rAt, cAt] = cu;
                }
            }
            else
            {
                cu.Status = 1;
                cu.DataHub.AddStatData(singleStatData, false);
            }
        }

        /// <summary>
        /// 统计计算栅格内的指标
        /// </summary>
        private void changGridDataFormula()
        {
            if (!cityComCoverRateInfoDic.ContainsKey(strCityName))
            {
                MultiGridCoverAnaInfo comCoverRateInfo = new MultiGridCoverAnaInfo();
                comCoverRateInfo.StrCityName = strCityName;
                cityComCoverRateInfoDic.Add(strCityName,comCoverRateInfo);
            }

            WaitBox.Text = strCityName + " 正在统计" + strCarrName + "栅格数据......";
            WaitBox.ProgressPercent = 35;

            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                StatDataGSM dataStatGSM = cu.DataHub.GetStatData(typeof(StatDataGSM)) as StatDataGSM;
                StatDataTD dataStatTD = cu.DataHub.GetStatData(typeof(StatDataTD)) as StatDataTD;
                StatDataWCDMA dataStatWCDMA = cu.DataHub.GetStatData(typeof(StatDataWCDMA)) as StatDataWCDMA;
                StatDataCDMA_Voice dataStatCDMA = cu.DataHub.GetStatData(typeof(StatDataCDMA_Voice)) as StatDataCDMA_Voice;
                StatDataCDMA_EVDO dataStatEVDO = cu.DataHub.GetStatData(typeof(StatDataCDMA_EVDO)) as StatDataCDMA_EVDO;
                if (dataStatGSM == null && dataStatTD == null && dataStatWCDMA == null 
                    && dataStatCDMA == null && dataStatEVDO == null)
                {
                    continue;
                }
                if (!gridSetForm.MultGridThresholdSet.IsShowGridSample)
                {
                    CenterLongLat cll = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    bool isCover = false;
                    doDataStatGsm(dataStatGSM, cu, cll);
                    doDataStatTdscdma(dataStatTD, cu, cll, out isCover);
                    doDataStatWcdma(dataStatWCDMA, cu, cll, out isCover);
                    doDataStatCdma(dataStatCDMA, cu, cll, out isCover);
                    doDataStatEvdo(dataStatEVDO, cu, cll, out isCover);
                } 
                else
                {
                    addGridSampleList(cu, dataStatGSM, dataStatTD);
                }

                cu.DataHub = new StatDataHubBase();
            }
            fillDataAndClearCache();
        }

        private void addGridSampleList(ColorUnit cu, StatDataGSM dataStatGSM, StatDataTD dataStatTD)
        {
            GridSampleInfo gridSample = new GridSampleInfo();
            gridSample.StrCity = strCityName;
            gridSample.DLongitude = cu.CenterLng;
            gridSample.DLatitude = cu.CenterLat;
            setISampleNum(cu, dataStatGSM, dataStatTD, gridSample);
            if (gridSample.ISampleNumGsm >= 0 || gridSample.ISampleNumtTd >= 0)
            {
                gridSampleList.Add(gridSample);
            }
            else
            {
                float fRscp = (float)cu.DataHub.CalcValueByFormula("Tx_5C04030D");
                float fDMO_DISTANCE = (float)cu.DataHub.CalcValueByFormula("Tx_5C040374");
                if (fDMO_DISTANCE >= 0 || (fRscp >= -140 && fRscp <= -10))
                {
                    gridSampleList.Add(gridSample);
                }
            }
        }

        private void setISampleNum(ColorUnit cu, StatDataGSM dataStatGSM, StatDataTD dataStatTD, GridSampleInfo gridSample)
        {
            if (dataStatGSM != null)
            {
                float sample = (float)cu.DataHub.CalcValueByFormula("Mx_640101");
                if (sample >= 0)
                {
                    gridSample.ISampleNumGsm = (int)sample;
                    float fRxlevSub = (float)cu.DataHub.CalcValueByFormula("Mx_640102");
                    if (fRxlevSub < -140 || fRxlevSub > -10)
                    {
                        gridSample.ISampleNumGsm = 0;
                    }
                }
            }
            if (dataStatTD != null)
            {
                float sample = (float)cu.DataHub.CalcValueByFormula("Tx_5C04030A");
                if (sample >= 0)
                {
                    gridSample.ISampleNumtTd = (int)sample;
                }
            }
        }

        #region 数据填值

        /// <summary>
        /// GSM语音统计
        /// </summary>
        private void doDataStatGsm(StatDataGSM dataStatGSM, ColorUnit cu, CenterLongLat cll)
        {
            if (dataStatGSM != null)
            {
                float fRxlevSub = (float)cu.DataHub.CalcValueByFormula("Mx_640102");
                if (fRxlevSub >= -140 && fRxlevSub <= -10)
                {
                    if (condition.CarrierTypes.Contains(1))
                    {
                        setYDCoveragegGridList(cll, fRxlevSub);
                    }
                    else if (condition.CarrierTypes.Contains(2))
                    {
                        setLTCoveragegGridList(cll, fRxlevSub);
                    }
                }
            }
        }

        private void setYDCoveragegGridList(CenterLongLat cll, float fRxlevSub)
        {
            cityComCoverRateInfoDic[strCityName].IYDGSMGridCount++;
            if (!cityComCoverRateInfoDic[strCityName].StrYDAllGridList.Contains(cll))
            {
                cityComCoverRateInfoDic[strCityName].StrYDAllGridList.Add(cll);
            }
            if (fRxlevSub >= gridSetForm.MultGridThresholdSet.IGSMStatThreshold)
            {
                cityComCoverRateInfoDic[strCityName].IYDGSMCoveragegGridCount++;
                if (!cityComCoverRateInfoDic[strCityName].StrYDAllCoveragegGridList.Contains(cll))
                {
                    cityComCoverRateInfoDic[strCityName].StrYDAllCoveragegGridList.Add(cll);
                }
            }
        }

        private void setLTCoveragegGridList(CenterLongLat cll, float fRxlevSub)
        {
            cityComCoverRateInfoDic[strCityName].ILTGSMGridCount++;
            if (!cityComCoverRateInfoDic[strCityName].StrLTAllGridList.Contains(cll))
            {
                cityComCoverRateInfoDic[strCityName].StrLTAllGridList.Add(cll);
            }
            if (fRxlevSub >= gridSetForm.MultGridThresholdSet.IGSMStatThreshold)
            {
                cityComCoverRateInfoDic[strCityName].ILTGSMCoveragegGridCount++;
                if (!cityComCoverRateInfoDic[strCityName].StrLTAllCoveragegGridList.Contains(cll))
                {
                    cityComCoverRateInfoDic[strCityName].StrLTAllCoveragegGridList.Add(cll);
                }
            }
        }

        /// <summary>
        /// 移动TDSCDMA空闲统计
        /// </summary>
        private void doDataStatTdscdma(StatDataTD dataStatTD, ColorUnit cu, CenterLongLat cll, out bool isCover)
        {
            isCover = false;
            bool isOnlyTD = false;
            if (dataStatTD != null)
            {
                float fRscp = (float)cu.DataHub.CalcValueByFormula("Tx_5C04030D");
                float fC_I = (float)cu.DataHub.CalcValueByFormula("Tx_5C040316");
                float fDMO_DISTANCE = (float)cu.DataHub.CalcValueByFormula("Tx_5C040375");// Tx_5C040374 换脱网时长，大于0算脱网
                if (fDMO_DISTANCE > 0 || (fRscp >= -140 && fRscp <= -10))
                {
                    cityComCoverRateInfoDic[strCityName].IYDTDGridCount++;
                    if (!cityComCoverRateInfoDic[strCityName].StrYDAllGridList.Contains(cll)
                        && !cityComCoverRateInfoDic[strCityName].StrYDOnlyTDGridList.Contains(cll))
                    {
                        cityComCoverRateInfoDic[strCityName].StrYDOnlyTDGridList.Add(cll);
                        isOnlyTD = true;
                    }
                }
                //移动统计
                bool isValid = judgeVlaid(fRscp, fC_I);
                if (isValid && fRscp >= gridSetForm.MultGridThresholdSet.ITDStatThreshold 
                    && fC_I >= gridSetForm.MultGridThresholdSet.ITDEcIoThreshold)
                {
                    cityComCoverRateInfoDic[strCityName].IYDTDCoveragegGridCount++;
                    isCover = true;
                }
            }

            if (isCover && !isOnlyTD && !cityComCoverRateInfoDic[strCityName].StrYDAllCoveragegGridList.Contains(cll))
            {
                cityComCoverRateInfoDic[strCityName].StrYDAllCoveragegGridList.Add(cll);
            }
        }

        /// <summary>
        /// 联通WCDMA空闲统计
        /// </summary>
        private void doDataStatWcdma(StatDataWCDMA dataStatWCDMA, ColorUnit cu, CenterLongLat cll, out bool isCover)
        {
            isCover = false;
            bool isOnlyTD = false;
            if (dataStatWCDMA != null)
            {
                float fRscp = (float)cu.DataHub.CalcValueByFormula("Wx_710A3F");
                float fC_I = (float)cu.DataHub.CalcValueByFormula("Wx_6A0A0A");
                float fDMO_DISTANCE = (float)cu.DataHub.CalcValueByFormula("Wx_710A67"); //  Wx_710A66  换脱网时长，大于0算脱网
                if (fDMO_DISTANCE > 0 || (fRscp >= -140 && fRscp <= -10))
                {
                    cityComCoverRateInfoDic[strCityName].ILTTDGridCount++;
                    if (!cityComCoverRateInfoDic[strCityName].StrLTAllGridList.Contains(cll)
                        && !cityComCoverRateInfoDic[strCityName].StrLTOnlyTDGridList.Contains(cll))
                    {
                        cityComCoverRateInfoDic[strCityName].StrLTOnlyTDGridList.Add(cll);
                        isOnlyTD = true;
                    }
                }
                bool isValid = judgeVlaid(fRscp, fC_I);
                if (isValid && fRscp >= gridSetForm.MultGridThresholdSet.IWCDMAStatThreshold
                    && fC_I >= gridSetForm.MultGridThresholdSet.IWCDMAEcIoThreshold)
                {
                    cityComCoverRateInfoDic[strCityName].ILTTDCoveragegGridCount++;
                    isCover = true;
                }
            }

            if (isCover && !isOnlyTD && !cityComCoverRateInfoDic[strCityName].StrLTAllCoveragegGridList.Contains(cll))
            {
                cityComCoverRateInfoDic[strCityName].StrLTAllCoveragegGridList.Add(cll);
            }
        }

        private bool judgeVlaid(float fRscp, float fC_I)
        {
            if (fRscp >= -140 && fRscp <= -10 && fC_I > -40 && fC_I < 40)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 电信CDMA语音统计
        /// </summary>
        private void doDataStatCdma(StatDataCDMA_Voice dataStatCDMA, ColorUnit cu, CenterLongLat cll, out bool isCover)
        {
            isCover = false;
            if (dataStatCDMA != null)
            {
                float fRxAGC = (float)cu.DataHub.CalcValueByFormula("Cx_5B060526");
                float fTxPower = (float)cu.DataHub.CalcValueByFormula("Cx_6C062C");
                float fC_I = (float)cu.DataHub.CalcValueByFormula("Cx_5B06012C");
                bool isValid = judgeVlaidCdma(fRxAGC, fC_I, fTxPower);
                if (isValid)
                {
                    cityComCoverRateInfoDic[strCityName].IDXGSMGridCount++;
                    if (!cityComCoverRateInfoDic[strCityName].StrDXAllGridCount.Contains(cll))
                    {
                        cityComCoverRateInfoDic[strCityName].StrDXAllGridCount.Add(cll);
                    }
                    if (fRxAGC >= gridSetForm.MultGridThresholdSet.ICDMAStatThreshold && fC_I >= gridSetForm.MultGridThresholdSet.ICDMAEcIoThreshold
                        && fTxPower <= gridSetForm.MultGridThresholdSet.ICDMAPowerThreshold)
                    {
                        cityComCoverRateInfoDic[strCityName].IDXGSMCoveragegGridCount++;
                        if (!cityComCoverRateInfoDic[strCityName].StrDXAllCoveragegGridList.Contains(cll))
                        {
                            cityComCoverRateInfoDic[strCityName].StrDXAllCoveragegGridList.Add(cll);
                        }
                    }
                }
            }
        }

        private bool judgeVlaidCdma(float fRxAGC, float fC_I, float fTxPower)
        {
            if (fRxAGC >= -140 && fRxAGC <= -10 && fC_I >= -40 && fC_I <= 40 && fTxPower >= -40 && fTxPower <= 40)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 电信EVDO空闲统计
        /// </summary>
        private void doDataStatEvdo(StatDataCDMA_EVDO dataStatEVDO, ColorUnit cu, CenterLongLat cll, out bool isCover)
        {
            isCover = false;
            bool isOnlyTD = false;
            if (dataStatEVDO != null)
            {
                float fRxAGC = (float)cu.DataHub.CalcValueByFormula("Ex_5E09010B");//5E09030B
                float fTxAgc = (float)cu.DataHub.CalcValueByFormula("Ex_5E09050F");
                float fSinr = (float)cu.DataHub.CalcValueByFormula("Ex_5E09040F");
                float fDMO_DISTANCE = (float)cu.DataHub.CalcValueByFormula("Ex_5E09013F"); // Ex_5E09013E 换脱网时长，大于0算脱网
                if (fDMO_DISTANCE > 0 || (fRxAGC >= -140 && fRxAGC <= -10))
                {
                    cityComCoverRateInfoDic[strCityName].IDXTDGridCount++;
                    if (!cityComCoverRateInfoDic[strCityName].StrDXAllGridCount.Contains(cll)
                        && !cityComCoverRateInfoDic[strCityName].StrDXOnlyTDGridList.Contains(cll))
                    {
                        cityComCoverRateInfoDic[strCityName].StrDXOnlyTDGridList.Add(cll);
                        isOnlyTD = true;
                    }
                }
                bool isValid = judgeVlaidEvdo(fRxAGC, fTxAgc, fSinr);
                if (isValid && fRxAGC >= gridSetForm.MultGridThresholdSet.IEVDOStatThreshold 
                    && fTxAgc <= gridSetForm.MultGridThresholdSet.IEVDOPowerThreshold
                    && fSinr >= gridSetForm.MultGridThresholdSet.IEVDOSinrThreshold)
                {
                    cityComCoverRateInfoDic[strCityName].IDXTDCoveragegGridCount++;
                    isCover = true;
                }
            }
            if (isCover && !isOnlyTD && !cityComCoverRateInfoDic[strCityName].StrDXAllCoveragegGridList.Contains(cll))
            {
                cityComCoverRateInfoDic[strCityName].StrDXAllCoveragegGridList.Add(cll);
            }
        }

        private bool judgeVlaidEvdo(float fRxAGC, float fTxAgc, float fSinr)
        {
            if (fRxAGC >= -140 && fRxAGC <= -10 && fTxAgc > -40 && fTxAgc < 40 && fSinr > -40 && fSinr < 40)
            {
                return true;
            }
            return false;
        }

        #endregion
        /// <summary>
        /// 数据填值及缓存清空
        /// </summary>
        private void fillDataAndClearCache()
        {
            if (condition.CarrierTypes.Contains(1))
            {
                cityComCoverRateInfoDic[strCityName].IYDAllGridCount = cityComCoverRateInfoDic[strCityName].StrYDAllGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrYDAllGridList.Clear();
                cityComCoverRateInfoDic[strCityName].IYDAllCoveragegGridCount = cityComCoverRateInfoDic[strCityName].StrYDAllCoveragegGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrYDAllCoveragegGridList.Clear();
                cityComCoverRateInfoDic[strCityName].IYDTDOnlyCoveragegGridCount = cityComCoverRateInfoDic[strCityName].StrYDOnlyTDGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrYDOnlyTDGridList.Clear();
            }
            else if (condition.CarrierTypes.Contains(2))
            {
                cityComCoverRateInfoDic[strCityName].ILTAllGridCount = cityComCoverRateInfoDic[strCityName].StrLTAllGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrLTAllGridList.Clear();
                cityComCoverRateInfoDic[strCityName].ILTAllCoveragegGridCount = cityComCoverRateInfoDic[strCityName].StrLTAllCoveragegGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrLTAllCoveragegGridList.Clear();
                cityComCoverRateInfoDic[strCityName].ILTTDOnlyCoveragegGridCount = cityComCoverRateInfoDic[strCityName].StrLTOnlyTDGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrLTOnlyTDGridList.Clear();
            }
            else if (condition.CarrierTypes.Contains(3))
            {
                cityComCoverRateInfoDic[strCityName].IDXAllGridCount = cityComCoverRateInfoDic[strCityName].StrDXAllGridCount.Count;
                cityComCoverRateInfoDic[strCityName].StrDXAllGridCount.Clear();
                cityComCoverRateInfoDic[strCityName].IDXAllCoveragegGridCount = cityComCoverRateInfoDic[strCityName].StrDXAllCoveragegGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrDXAllCoveragegGridList.Clear();
                cityComCoverRateInfoDic[strCityName].IDXTDOnlyCoveragegGridCount = cityComCoverRateInfoDic[strCityName].StrDXOnlyTDGridList.Count;
                cityComCoverRateInfoDic[strCityName].StrDXOnlyTDGridList.Clear();
            }
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格
        /// </summary>
        private bool isContainDbRect(DbRect dRect)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (!(gridType + grid).Contains(strCityName) && !(gridType + grid).Contains("无网格"))
                    {
                        continue;
                    }
                    if (mutRegionMopDic[gridType][grid].CheckRectIntersectWithRegion(dRect))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //
        }

        protected override void fireShowResult()
        {
            ZTMultiGridCoverAnaForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTMultiGridCoverAnaForm).FullName);
            showForm = obj == null ? null : obj as ZTMultiGridCoverAnaForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTMultiGridCoverAnaForm(MainModel, strStartTime, gridSetForm.MultGridThresholdSet.IsShowGridSample);
            }
            showForm.FillData(multiGridCoverAnaInfoList, gridSampleList);
            showForm.Show(MainModel.MainForm);
        }
    }

    public class MultiGridCoverAnaInfo
    {
        public string StrCityName { get; set; }
        public List<CenterLongLat> StrYDGSMGridList { get; set; } = new List<CenterLongLat>();
        public int IYDGSMGridCount { get; set; }
        public int IYDGSMCoveragegGridCount { get; set; }

        public string StrGSMRate
        {
            get
            {
                string strRate = "";
                if (IYDGSMGridCount > 0)
                {
                    strRate = (100.0 * IYDGSMCoveragegGridCount / IYDGSMGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public int IYDTDGridCount { get; set; }
        public int IYDTDTdGridCount { get; set; }
        public int IYDTDCoveragegGridCount { get; set; }
        public List<CenterLongLat> StrYDOnlyTDGridList { get; set; } = new List<CenterLongLat>();
        public int IYDTDOnlyCoveragegGridCount { get; set; }


        public string StrTDRate
        {
            get
            {
                string strRate = "";
                if (IYDTDGridCount > 0)
                {
                    strRate = (100.0 * IYDTDCoveragegGridCount / IYDTDGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public List<CenterLongLat> StrYDAllGridList { get; set; } = new List<CenterLongLat>();
        public int IYDAllGridCount { get; set; }
        public List<CenterLongLat> StrYDAllCoveragegGridList { get; set; } = new List<CenterLongLat>();
        public int IYDAllCoveragegGridCount { get; set; }

        public string StrCoveragegRate
        {
            get
            {
                string strRate = "";
                if (IYDAllGridCount > 0)
                {
                    strRate = (100.0 * IYDAllCoveragegGridCount / IYDAllGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public List<CenterLongLat> StrLTGSMGridList { get; set; } = new List<CenterLongLat>();
        public int ILTGSMGridCount { get; set; }
        public int ILTGSMCoveragegGridCount { get; set; }

        public string StrLTGSMRate
        {
            get
            {
                string strRate = "";
                if (ILTGSMGridCount > 0)
                {
                    strRate = (100.0 * ILTGSMCoveragegGridCount / ILTGSMGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public int ILTTDGridCount { get; set; }
        public int ILTTDTdGridCount { get; set; }
        public int ILTTDCoveragegGridCount { get; set; }
        public List<CenterLongLat> StrLTOnlyTDGridList { get; set; } = new List<CenterLongLat>();
        public int ILTTDOnlyCoveragegGridCount { get; set; }

        public string StrLTTDRate
        {
            get
            {
                string strRate = "";
                if (ILTTDGridCount > 0)
                {
                    strRate = (100.0 * ILTTDCoveragegGridCount / ILTTDGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public List<CenterLongLat> StrLTAllGridList { get; set; } = new List<CenterLongLat>();
        public int ILTAllGridCount { get; set; }
        public List<CenterLongLat> StrLTAllCoveragegGridList { get; set; } = new List<CenterLongLat>();
        public int ILTAllCoveragegGridCount { get; set; }

        public string StrLTCoverRate
        {
            get
            {
                string strRate = "";
                if (ILTAllGridCount > 0)
                {
                    strRate = (100.0 * ILTAllCoveragegGridCount / ILTAllGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public List<CenterLongLat> StrDXGSMGridList { get; set; } = new List<CenterLongLat>();
        public int IDXGSMGridCount { get; set; }
        public int IDXGSMCoveragegGridCount { get; set; }

        public string StrDXGSMRate
        {
            get
            {
                string strRate = "";
                if (IDXGSMGridCount > 0)
                {
                    strRate = (100.0 * IDXGSMCoveragegGridCount / IDXGSMGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public int IDXTDGridCount { get; set; }
        public int IDXTDTdGridCount { get; set; }
        public int IDXTDCoveragegGridCount { get; set; }
        public List<CenterLongLat> StrDXOnlyTDGridList { get; set; } = new List<CenterLongLat>();
        public int IDXTDOnlyCoveragegGridCount { get; set; }

        public string StrDXTDRate
        {
            get
            {
                string strRate = "";
                if (IDXTDGridCount > 0)
                {
                    strRate = (100.0 * IDXTDCoveragegGridCount / IDXTDGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        
        public List<CenterLongLat> StrDXAllGridCount { get; set; } = new List<CenterLongLat>();
        public int IDXAllGridCount { get; set; }
        public List<CenterLongLat> StrDXAllCoveragegGridList { get; set; } = new List<CenterLongLat>();
        public int IDXAllCoveragegGridCount { get; set; }

        public string StrDXCoverRate
        {
            get
            {
                string strRate = "";
                if (IDXAllGridCount > 0)
                {
                    strRate = (100.0 * IDXAllCoveragegGridCount / IDXAllGridCount).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
    }

    public class MultGridCoverAnaThresholdSet
    {
        public int IGSMStatThreshold { get; set; }
        public int ICDMAStatThreshold { get; set; }
        public int ICDMAPowerThreshold { get; set; }
        public int ICDMAEcIoThreshold { get; set; }
        public int ITDStatThreshold { get; set; }
        public int ITDEcIoThreshold { get; set; }
        public int IWCDMAStatThreshold { get; set; }
        public int IWCDMAEcIoThreshold { get; set; }
        public int IEVDOStatThreshold { get; set; }
        public int IEVDOPowerThreshold { get; set; }
        public int IEVDOSinrThreshold { get; set; }
        public bool IsShowGridSample { get; set; }
    }

    public class CenterLongLat
    {
        public double dLongitude { get; set; }
        public double dLatitude { get; set; }

        public CenterLongLat()
        {
            dLongitude = 0;
            dLatitude = 0;
        }

        public CenterLongLat(double dLong, double dLat)
        {
            this.dLongitude = dLong;
            this.dLatitude = dLat;
        }

        public override bool Equals(object obj)
        {
            CenterLongLat other = obj as CenterLongLat;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.dLongitude.Equals(other.dLongitude) &&
                    this.dLatitude.Equals(other.dLatitude));
        }

        public override int GetHashCode()
        {
            int iKey = (int)(dLongitude * 10000000 + dLatitude * 10000000);
            return iKey.GetHashCode();
        }
    }

    public class SizeGrid
    {
        public int rAt { get; set; }
        public int cAt { get; set; }

        public SizeGrid()
        {
            rAt = 0;
            cAt = 0;
        }

        public SizeGrid(int rAt, int cAt)
        {
            this.rAt = rAt;
            this.cAt = cAt;
        }

        public override bool Equals(object obj)
        {
            SizeGrid other = obj as SizeGrid;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.rAt.Equals(other.rAt) &&
                    this.cAt.Equals(other.cAt));
        }

        public override int GetHashCode()
        {
            int iKey = (rAt * 10000000 + cAt); 
            return iKey.GetHashCode();
        }
    }

    public class GridSampleInfo
    {
        public string StrCity { get; set; }
        public double DLongitude { get; set; }
        public double DLatitude { get; set; }
        public int ISampleNumGsm { get; set; }
        public int ISampleNumtTd { get; set; }

        public GridSampleInfo()
        {
            this.StrCity = "";
            this.DLongitude = 0;
            this.DLatitude = 0;
            this.ISampleNumGsm = -1;
            this.ISampleNumtTd = -1;
        }
    }
}
