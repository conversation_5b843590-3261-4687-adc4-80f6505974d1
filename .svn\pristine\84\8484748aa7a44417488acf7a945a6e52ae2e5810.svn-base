﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNCellLevelHigherByRegion : DIYSampleByRegion
    {
        private int numRSRP = 0;//查询条件

        //主服电平
        private float fRSRP = -1;
        //邻服电平
        private float fNRSRP = -1;
        //电平差
        private float fRSRPDiff = -1;

        #region instance
        private static volatile ZTLteNCellLevelHigherByRegion instance = null;

        protected ZTLteNCellLevelHigherByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public static ZTLteNCellLevelHigherByRegion GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTLteNCellLevelHigherByRegion(MainModel.GetInstance());
            }
            return instance;
        }
        #endregion

        /// <summary>
        /// 添加功能点
        /// </summary>
        /// <returns></returns>
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22116, this.Name);
        }

        /// <summary>
        /// 界面显示按钮的注释
        /// </summary>
        public override string Name
        {
            get { return "邻区电平强于主服务小区电平小区集(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        /// <summary>
        /// 采样点是否有效 父类saveTestPoint
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (!base.isValidTestPoint(tp))
            {
                return false;
            }
            float? fRsrp = getRSRP(tp);
            if (fRsrp == null)
            {
                fRSRP = -1; 
                fRSRPDiff = -1;
                fNRSRP = -1;
                return false;
            }

            for (int i = 0; i < 10; i++)
            {
                float? fNRsrp = getNRSRP(tp, i);
                if (fNRsrp == null)
                {
                    break;
                }
                float diff = (float)fNRsrp - (float)fRsrp;
                if (diff >= numRSRP)
                {
                    fRSRP = (float)fRsrp;
                    fNRSRP = (float)fNRsrp;
                    fRSRPDiff = diff;
                    return true;
                }
            }
            fRSRP = -1;
            fRSRPDiff = -1;
            fNRSRP = -1;
            return false;
        }

        /// <summary>
        /// 查询前准备 父类queryInThread
        /// </summary>
        protected override void doSomethingBeforeQueryInThread()
        {
            CelllteInfos = new List<LteRsrpInfo>();
        }  

        /// <summary>
        /// 弹出邻区电平比主服务小区电平强多少的条件窗口
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            ZTLTENCellLevelHigherDlg dlg = new ZTLTENCellLevelHigherDlg();
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                dlg.GetCondition(out numRSRP);
                return true;
            }
            return false;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRQ");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_SINR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            return cellSetGroup;
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                WaitBox.CanCancel = true;
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

                FireShowFormAfterQuery();
            }
            finally
            {
                clientProxy.Close();
            }
        }  

        /// <summary>
        /// 查询完数据后调用展示窗口
        /// </summary>
        protected override void FireShowFormAfterQuery()
        {
            ZTLTENCellLevelHigherForm lteCellSetForm = MainModel.CreateResultForm(typeof(ZTLTENCellLevelHigherForm)) as ZTLTENCellLevelHigherForm;
            lteCellSetForm.FillData(CelllteInfos);
            lteCellSetForm.Visible = true;
            lteCellSetForm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
            releaseData();
        }

        /// <summary>
        /// 释放部分变量
        /// </summary>
        private void releaseData()
        {
            CelllteInfos = null;
        }

        List<LteRsrpInfo> CelllteInfos = new List<LteRsrpInfo>();

        protected override void doWithDTData(TestPoint tp)
        {
            LteRsrpInfo lteInfo = new LteRsrpInfo();
            LTECell cell = tp.GetMainCell_LTE();
            if (cell == null)
            {
                int? iTAC = tp.GetLAC();
                if (iTAC == null)
                {
                    iTAC = 0;
                }
                int? iEci = tp.GetCI();
                if (iEci == null)
                {
                    iEci = 0;
                }

                lteInfo.TAC = (int)iTAC;
                lteInfo.ECI = (int)iEci;
                lteInfo.CellName = "" + iTAC + "_" + iEci;
            }
            else
            {
                lteInfo.TAC = cell.TAC;
                lteInfo.ECI = cell.ECI;
                lteInfo.CellName = cell.Name;
            }
            lteInfo.Longitude = tp.Longitude;
            lteInfo.Latitude = tp.Latitude;
            lteInfo.RSRP = fRSRP;
            lteInfo.NRSRP = fNRSRP;
            lteInfo.RSRPDiff = fRSRPDiff;
            lteInfo.FileName = tp.FileName;
            lteInfo.TestPoint = tp;
            CelllteInfos.Add(lteInfo);
        }

        /// <summary>
        /// 获取主服RSRP
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        private float? getRSRP(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        /// <summary>
        /// 获取邻服RSRP
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        private float? getNRSRP(TestPoint tp, int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }

    }

    public class LteRsrpInfo
    {     
        public int SN { get; set; } = 1;
        public string FileName { get; set; } = "";
        public string CellName { get; set; } = "";
        /// <summary>
        /// 小区类型
        /// </summary>
        public string CellType { get; set; } = "";
        public int TAC { get; set; }
        public int ECI { get; set; }
        public float RSRP { get; set; }
        public float NRSRP { get; set; }
        public float RSRPDiff { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public TestPoint TestPoint { get; set; } = new TestPoint();

    }

}
