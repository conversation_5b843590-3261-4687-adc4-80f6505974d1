﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="toolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>110, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="cascadeToolStripButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEsSURBVDhP7ZI/S0JRGIf9VBEZoRFJaWoSenONBpdoaDFw
        CaJFS6HBP0P7VYI+QkNb1KKBRChKFuaFtCgvnnueblTmohcutPXjcHiH83t4eTiO45MLkgWVePqMaPyc
        wPYpng2VOSWHU8kyG83j9O/iGJejnMp+acBmRiO81yCQqLG4c4trq8p0rMxUrIrbGxkPOCwUkVKCeX7y
        Oerm9awLuoacDEjmisOy+Za+bhZfBC1NcPdoUNOYDEjl1a+2GVs+0tnScHtbPlL50nfdwJaPxEGGYDDE
        6to6/z7+0Ic+gN6bpNMVNDqCm6ZO5cHih476eO1LtB40nwTVluCqLrhuWG7w6+PdAK0P9a6k0ja4vDcB
        bQvAqI+VkMJSUMHjV1jwRXAth5n3Kcy4vXwAE6M73CYb39EAAAAASUVORK5CYII=
</value>
  </data>
  <data name="tileVerticalToolStripButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAG2SURBVDhPrdLJSwJhGMdx/7Dy1HaNdvNa0GYFIR2iCKJT
        hAgFlZkQpe1mRU6EeWnBTCpsr8lWBUVfKYyaxl+PoyAz2aV64LnMl/czw8yo/mUMNoZBK0P3RBS64QC0
        /QGo9Tzy2vxQtuq+API6rlHV60HmeBp4eQPMW8DgmoiuCQaNMSIB4464rHWaGcoHQt+BPz2BxXkI5TyF
        wlBX6jDnPslcyc7+0RmqGvQ/A8In4PHnBt7egVW3ErD7MpkmCXx8iNg74KGuaMEc588EGmqJhADHhlcB
        LLmlmKQVRbrLq4hNLwHlzQTsylo8LsDGKQCzzS1Fgd42ow2/iNjY5pFPwIxjV9aCMQHTKwrAYHVJMZYA
        ojEgFBaxuEVfoawJpuUdWbsPCjAtKgGLHTGKEdpANImrRwHzrhTQCNMsJ2tnd+8YXcgBRCjeBkXwzwLO
        bxgszjQwNsPJ2vElo38jB3CTOvjA4L1m2LsgYD0NjNg4Wds+JcCaA3B6eOmuqYOWZR7GSZ8EDE2tfWt9
        wy45YJpdR4m2HcXatuzWtqK0rhMG8zyKNDppCzUtKKxpRgFtvb4nC/x+VKov+RhzhE9Y6Q0AAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="tileHorizontalToolStripButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAE1SURBVDhP7ZHPK4MBGMf3Tzm4yElxcJQzuUiKw1a0JGtk
        JTU/WhImFCkHDhwdHEgRGoa17d2Pd8bYu/d99/7YPmzvuyK7kIvyrc/lqe/3+/Q8jl+RbzNDDe96hpEV
        EedCmr6ZFF1TSTrHE7SPCrQMCzQ54zQOxrCtlirGMhA8hLl9k+k9A9+OjmdLw71RxBVUGFiU6Q3I9MxK
        9QMqeNZEhpZE+uet5g5vgjZ3nGa79SO21ZJ/+aC6gWGCqpUoKCY5ySST0xEeNSIplZuYysWDzOltgYbW
        7s8Bk4FdzFIZTS8hqyavBZPsi0EyqxFNFwkLClcRmbOwzHFI+how5t9GN8oo7+152eQpb5B+1omJGncJ
        lVBU4fxe5uRa4uiyToBrYrU6/A621VLtiBX+3/hn3/hzORxv5HYtf0SibUoAAAAASUVORK5CYII=
</value>
  </data>
  <data name="tsBtnAutoWidth.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGHSURBVEhL7VJNSwJRFD1boT/htpXQb2g1W0NTo5IKpC/S
        tNR07MdEGEVggS0iqIWLgogoGJIKKqIookAj3bzemW7JoK6mpQcuvHM499w3dx566OF/8GXC/5rGoFDX
        YBYzhQLNVaiXNM6FugazmCm084C7BXiaBSQvZtEvUhvOZuDVnuyziT6RbLQN+Co4B9RM+OomLOqn0wiJ
        3IaTGAx6dFXrOQyIbA+gLhT4NFsD9Dn+kUGDmlTXAbUcjF+f3ZNHkjqzqNkmopaHelqGdZ1AmWc3xQxm
        8SzxwPsK1H0KlzsRrOmVqLcsFDXWcQxqd6RzVaZaPvawlxmPS7CoSbx+Vhmo28WfFZXCiO+Po/GQgqJe
        mey+osMoDHroPYiioXvtFTGLum0i9Cepm0TrJ2+H4NsKwuLt98a6DyiPwqCnFERV9/z9ZGYxUyjAG1zN
        OZ9p0Q/P5jCyxXD3Z1qMwEvPxpDzmTKLmUKBowkovRbHADdgFjOFAusBzOsKCHUNZjFTaA89uALwDXYN
        EZR88hxDAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>37</value>
  </metadata>
</root>