﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class DIYReplayOptionByMultiServiceDlg : BaseDialog
    {
        /// <summary>
        /// 一种业务对应多个可选的回放模板
        /// </summary>
        private Dictionary<int, List<DIYReplayContentOption>> optionsDic;
        private List<DIYReplayContentOption> allOptionList;
        private QueryCondition condition;

        public bool IsAutoLoadCQTPicture
        {
            get { return chkCQT.Checked; }
        }

        /// <summary>
        /// 当前选定的业务回放模板
        /// </summary>
        public Dictionary<int, DIYReplayContentOption> ReplayOptionDic
        {
            get
            {
                Dictionary<int, DIYReplayContentOption> retDic = new Dictionary<int, DIYReplayContentOption>();
                foreach (DataGridViewRow row in dataGridView.Rows)
                {
                    DIYReplayContentOption opt = GetCurRowOption(row);
                    if (opt != null)
                    {
                        retDic.Add((int)row.Tag, opt);
                    }
                }
                return retDic;
            }
        }

        public DIYReplayOptionByMultiServiceDlg(QueryCondition cond)
        {
            // 配置数据
            InitializeComponent();
            condition = cond;
            allOptionList = loadOptionSettings();
            InitOptionsDic();

            // 控件
            InitDataGridView();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnSetting.Click += BtnSetting_Click;

#if NotShowLoadCQTPicture
            chkCQT.Checked = false;
            chkCQT.Visible = false;
#else
            chkCQT.Checked = true;
            chkCQT.Visible = true;
#endif
            // 窗体
            Size size = new Size(this.ClientSize.Width, panel1.Location.Y + panel1.Height);
            this.ClientSize = size;
        }

        /// <summary>
        /// 载入模版配置文件
        /// </summary>
        /// <returns></returns>
        private List<DIYReplayContentOption> loadOptionSettings()
        {
            List<DIYReplayContentOption> optionList = new List<DIYReplayContentOption>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/diyreplayoption.xml"));
                List<Object> list = configFile.GetItemValue("DIYReplayOptions", "options") as List<Object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        DIYReplayContentOption option = new DIYReplayContentOption();
                        option.Param = value as Dictionary<string, object>;
                        optionList.Add(option);
                    }
                }
            }
            catch
            {
                return new List<DIYReplayContentOption>();
            }
            return optionList;
        }

        /// <summary>
        /// 初始化业务可选的回放模板
        /// </summary>
        private void InitOptionsDic()
        {
            optionsDic = new Dictionary<int, List<DIYReplayContentOption>>();
            foreach (FileInfo fInfo in condition.FileInfos)
            {
                if (!optionsDic.ContainsKey(fInfo.ServiceType))
                {
                    optionsDic.Add(fInfo.ServiceType, FillCurrentServiceType(fInfo.ServiceType));
                }
            }
        }

        /// <summary>
        /// 根据业务类型，返回可用的采样点模板
        /// </summary>
        /// <param name="svtype"></param>
        private List<DIYReplayContentOption> FillCurrentServiceType(int svtype)
        {
            int refSvType = DIYReplayOptionDlg.GetCurServiceType(svtype);

            List<DIYReplayContentOption> retList = new List<DIYReplayContentOption>();
            foreach (DIYReplayContentOption option in allOptionList)
            {
                if (option.SampleServiceType == refSvType)
                {
                    retList.Add(option);
                }
            }
            return retList;
        }

        private void InitDataGridView()
        {
            dataGridView.EditingControlShowing += DataGridView_EditingControlShowing;
            dataGridView.SelectionChanged += DataGridView_SelectionChanged;
            ColReplayContent.Width = dataGridView.Width - ColServiceType.Width - 3;
            ColServiceType.ReadOnly = true;

            dataGridView.Rows.Clear();
            foreach (int serviceType in optionsDic.Keys)
            {
                List<DIYReplayContentOption> options = optionsDic[serviceType];
                if (options.Count == 0)
                {
                    continue;
                }

                DataGridViewTextBoxCell txtCell = new DataGridViewTextBoxCell();
                txtCell.Value = options[0].Name.IndexOf("_") == -1 ? options[0].Name : options[0].Name.Substring(0, options[0].Name.IndexOf("_"));

                DataGridViewComboBoxCell cbxCell = new DataGridViewComboBoxCell();
                foreach (DIYReplayContentOption opt in options)
                {
                    cbxCell.Items.Add(opt.Name);
                }
                cbxCell.Value = cbxCell.Items[0];

                DataGridViewRow row = new DataGridViewRow();
                row.Cells.Add(txtCell);
                row.Cells.Add(cbxCell);
                row.Tag = serviceType;

                dataGridView.Rows.Add(row);
            }
            dataGridView.Height = dataGridView.Rows[0].Height * (dataGridView.Rows.Count + 1);
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnSetting_Click(object sender, EventArgs e)
        {
            DIYReplayContentSettingDlg dlg = new DIYReplayContentSettingDlg();
            dlg.FillAllReplayOptions(allOptionList, "");
            dlg.ShowDialog();
            allOptionList = dlg.AllOptionList;
            InitOptionsDic();
        }

        /// <summary>
        /// 当datagridview的控件进入编辑状态时发生
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DataGridView_EditingControlShowing(object sender, DataGridViewEditingControlShowingEventArgs e)
        {
            ComboBox cbx = e.Control as ComboBox;
            if (cbx != null)
            {
                cbx.SelectedIndexChanged -= ComboBox_SelectedChanged;
                cbx.SelectedIndexChanged += ComboBox_SelectedChanged;
            }
        }

        /// <summary>
        /// 选中行改变
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            SetLabelDesc(GetCurRowOption(dataGridView.SelectedRows[0]));
        }

        /// <summary>
        /// 下拉框值改变
        /// 该事件发生时，DataGridViewComboBoxEditingControl所在行的Value值是尚未改变的
        /// 需要使用Text属性
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ComboBox_SelectedChanged(object sender, EventArgs e)
        {
            DataGridViewComboBoxEditingControl cbxCtrl = sender as DataGridViewComboBoxEditingControl;
            DIYReplayContentOption opt = GetCurRowOption((int)dataGridView.Rows[cbxCtrl.EditingControlRowIndex].Tag, cbxCtrl.Text);
            SetLabelDesc(opt);
        }

        private void SetLabelDesc(DIYReplayContentOption opt)
        {
            if (opt != null)
            {
                labelDesc.Text = opt.Desc;
            }
        }

        /// <summary>
        /// 更加业务ID和模版名获取
        /// </summary>
        /// <param name="service"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        private DIYReplayContentOption GetCurRowOption(int service, string name)
        {
            foreach (DIYReplayContentOption opt in optionsDic[service])
            {
                if (opt.Name == name)
                {
                    return opt;
                }
            }
            return null;
        }

        /// <summary>
        /// 根据指定行获取
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        private DIYReplayContentOption GetCurRowOption(DataGridViewRow row)
        {
            int serviceType = (int)row.Tag;
            string name = (row.Cells[1] as DataGridViewComboBoxCell).Value as string;
            return GetCurRowOption(serviceType, name);
        }
    }
}
