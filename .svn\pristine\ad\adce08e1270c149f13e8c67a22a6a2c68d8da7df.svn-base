﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MultiCompareInfo
    {

    }
    public class MultiCompareRoadInfo
    {
        public MultiCompareRoadInfo(TimePeriod period)
        {
            this.Period = period;
            this.Period.showDayFormat = true;
            ProblemUnFinishCount = 0;
            ProblemHasFinishCount = 0;
            ProblemUnFinishDic = new Dictionary<int, RoadProblemInfo>();
            ProblemHasFinishDic = new Dictionary<int, RoadProblemInfo>();

            StatInfo = new StatInfoBase();
            UnfitParamCount = 0;
            UnfitCellMsgInfoDic = new Dictionary<string, CelllMessageInfo>();
        }

        public int SN { get; set; }

        public TimePeriod Period { get; set; }
        public string PeriodIndex
        {
            get
            {
                return "X" + this.SN;
            }
        }

        #region 路测问题

        public Dictionary<int, RoadProblemInfo> ProblemUnFinishDic { get; set; }
        public int ProblemUnFinishCount { get; set; }

        public Dictionary<int, RoadProblemInfo> ProblemHasFinishDic { get; set; }
        public int ProblemHasFinishCount { get; set; }
        public int ProblemTotalCount
        {
            get
            {
                return ProblemUnFinishCount + ProblemHasFinishCount;
            }
        }
        public void SetUnFinishProblemPercent()
        {
            if (ProblemUnFinishCount > 0)
            {
                foreach (var info in ProblemUnFinishDic)
                {
                    info.Value.Percent = Math.Round((double)100 * info.Value.ProblemTimes / ProblemUnFinishCount, 2);
                }
            }
        }

        public string GetUnFinishPrbReasonDescSum()
        {
            StringBuilder strbReason = new StringBuilder();
            foreach (var pairUnfinishPrb in this.ProblemUnFinishDic)
            {
                foreach (var pairReason in pairUnfinishPrb.Value.ReasonCountDic)
                {
                    string strReasonNameCount = string.Format("\"{0}\"问题{1}个，", pairReason.Key, pairReason.Value);
                    strbReason.Append(strReasonNameCount);
                }
            }
            if (strbReason.Length > 0)
            {
                strbReason.Remove(strbReason.Length - 1, 1);
            }
            return strbReason.ToString();
        }
        #endregion

        /// <summary>
        /// 自定义指标
        /// </summary>
        public StatInfoBase StatInfo { get; set; }

        public int UnfitParamCount { get; set; }

        public Dictionary<string, CelllMessageInfo> UnfitCellMsgInfoDic { get; set; }
    }
    public class RoadProblemInfo
    {
        public RoadProblemInfo(Event evt)
        {
            Percent = 0;
            ReasonCountDic = new Dictionary<string, int>();
            ProblemEvtList = new List<Event>();

            this.ProblemEvtId = evt.ID;
            this.ProblemName = evt.Name;
            this.ProblemEvtList.Add(evt);
        }
        public RoadProblemInfo(RoadProblemInfo info)
        {
            this.ReasonCountDic = info.ReasonCountDic;
            this.ProblemEvtList = info.ProblemEvtList;
            this.ProblemName = info.ProblemName;
            this.ProblemEvtId = info.ProblemEvtId;
        }
        /// <summary>
        /// 该类问题的 不同原因_次数
        /// </summary>
        public Dictionary<string, int> ReasonCountDic { get; set; }

        /// <summary>
        /// 该类问题的事件集合
        /// </summary>
        public List<Event> ProblemEvtList { get; set; }

        public string ProblemName { get; set; }
        public int ProblemEvtId { get; set; }
        public int ProblemTimes
        {
            get
            {
                return ProblemEvtList.Count;
            }
        }

        /// <summary>
        /// 该类问题的次数在全部问题次数中的占比
        /// </summary>
        public double Percent { get; set; }
    }

    public class ProblemDetailInfo
    {
        public int SN { get; set; }
        public string PeriodIndex { get; set; }
        public string TestTime { get; set; }
        public string Area { get; set; }
        public string DistrictName { get; set; }

        /// <summary>
        /// 厂家
        /// </summary>
        public string Manufacturers { get; set; }
        public string ProblemType { get; set; }
        public string PreReasonDesc { get; set; }
        public string ReasonDesc { get; set; }
        public bool IsSolve { get; set; }
        public string SolveDate { get; set; }
        public string FileName { get; set; }
        public string RoadName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string CellName { get; set; }
        public string Note { get; set; }
    }
}
