﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 像素邻域均值模糊
    /// </summary>
    public sealed class AverageBlur
    {
        private readonly Bitmap bitmap;
        private int radius;
        private int channel;
        private readonly float[] src;
        private readonly float[] dst;
        private readonly int width;
        private readonly int height;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bitmap">像素格式需要是Canonical（Bitmap的默认格式）</param>
        public AverageBlur(Bitmap bitmap)
        {
            this.bitmap = bitmap;
            width = bitmap.Width;
            height = bitmap.Height;
            src = new float[width * height];
            dst = new float[width * height];
        }

        /// <summary>
        /// 单通道模糊处理
        /// </summary>
        /// <param name="channel">值为0到2，对应B,G,R通道</param>
        /// <param name="radius">半径应远小于Bitmap的长宽</param>
        public void BlurSingleChannel(int channel, int radius)
        {
            if (channel >= 3 || channel < 0) return;
            if (radius * 2 + 1 >= Math.Min(width, height) || radius > 10) return;

            this.channel = channel;
            this.radius = radius;

            BitmapToArray();
            ProcessTopBorder();
            ProcessButtomBorder();
            ProcessLeftBorder();
            ProcessRightBorder();
            ProcessInner();
            ArrayToBitmap();
        }

        /// <summary>
        /// 对RGB三个通道进行模糊
        /// </summary>
        /// <param name="radius">半径应远小于Bitmap的长宽</param>
        public void BlurRGBChannel(int radius)
        {
            for (int i = 0; i < 3; ++i)
                BlurSingleChannel(i, radius);
        }

        // 将颜色通道值复制到数组以便计算
        private void BitmapToArray()
        {
            BitmapData bmData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                ImageLockMode.ReadOnly, bitmap.PixelFormat);
            int len = width * height;

            unsafe
            {
                byte* bmpPtr = (byte*)bmData.Scan0;
                fixed (float* srcPtr = src)
                {
                    for (int i = 0; i < len; ++i)
                        srcPtr[i] = bmpPtr[i * 4 + channel];
                }
            }

            bitmap.UnlockBits(bmData);
        }

        // 将数组结果复制回Bitmap
        private void ArrayToBitmap()
        {
            BitmapData bmData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                ImageLockMode.WriteOnly, bitmap.PixelFormat);
            int len = width * height;

            unsafe
            {
                byte* bmpPtr = (byte*)bmData.Scan0;
                fixed (float* dstPtr = dst)
                {
                    for (int i = 0; i < len; ++i)
                        bmpPtr[i * 4 + channel] = (byte)Math.Max(Math.Min(255, dstPtr[i]), 0);
                }
            }

            bitmap.UnlockBits(bmData);
        }

        // 上边界处理，从0到radius共radius + 1行作为上边界
        // 四个边界处理的函数的效率都是很低的，边界上的每个元素需要计算(radius * 2 + 1)^2次
        // 四个边界示意图：T上，L左，R右，B下，I内部
        // TTTTTTTTTT
        // TTTTTTTTTT
        // TTTTTTTTTT
        // LLIIIIIIRR
        // LLIIIIIIRR
        // BBBBBBBBBB
        // BBBBBBBBBB
        private void ProcessTopBorder()
        {
            unsafe
            {
                fixed (float* srcPtr = src, dstPtr = dst)
                {
                    // dst[row, col]为需要计算的边界点
                    for (int row = 0; row <= radius; ++row)
                        for (int col = 0; col < width; ++col)
                        {
                            setTopBorderPtr(srcPtr, dstPtr, row, col);
                        }
                }
            }
        }

        private unsafe void setTopBorderPtr(float* srcPtr, float* dstPtr, int row, int col)
        {
            float sum = 0;
            int counter = 0;
            for (int rr = 0; rr <= row + radius; ++rr) // 跟第row行相邻
                for (int cc = col - radius; cc <= col + radius; ++cc) // 跟col列相邻
                {
                    if (cc < 0 || cc >= width) continue;
                    sum += srcPtr[rr * width + cc];
                    ++counter;
                }
            // counter肯定不会为0
            dstPtr[row * width + col] = sum / counter;
        }

        // 下边界处理，从height - radius到height - 1共radius行作为下边界
        private void ProcessButtomBorder()
        {
            unsafe
            {
                fixed (float* srcPtr = src, dstPtr = dst)
                {
                    // dst[row, col]为需要计算的边界点
                    for (int row = height - radius; row < height; ++row)
                        for (int col = 0; col < width; ++col)
                        {
                            setButtomBorderPtr(srcPtr, dstPtr, row, col);
                        }
                }
            }
        }

        private unsafe void setButtomBorderPtr(float* srcPtr, float* dstPtr, int row, int col)
        {
            float sum = 0;
            int counter = 0;
            for (int rr = row - radius; rr < height; ++rr) // 跟第row行相邻
                for (int cc = col - radius; cc <= col + radius; ++cc) // 跟col列相邻
                {
                    if (cc < 0 || cc >= width) continue;
                    sum += srcPtr[rr * width + cc];
                    ++counter;
                }
            dstPtr[row * width + col] = sum / counter;
        }

        // 左边界，从0到radius共radius列作为左边界，不与上下边界重复行
        private void ProcessLeftBorder()
        {
            unsafe
            {
                fixed (float* srcPtr = src, dstPtr = dst)
                {
                    // dst[row, col]为需要计算的边界点
                    for (int row = radius + 1; row < height - radius; ++row)
                        for (int col = 0; col < radius; ++col)
                        {
                            setLeftBorderPtr(srcPtr, dstPtr, row, col);
                        }
                }
            }
        }

        private unsafe void setLeftBorderPtr(float* srcPtr, float* dstPtr, int row, int col)
        {
            float sum = 0;
            int counter = 0;
            for (int rr = row - radius; rr <= row + radius; ++rr) // 跟第row行相邻
                for (int cc = col - radius; cc <= col + radius; ++cc) // 跟col列相邻
                {
                    if (cc < 0) continue;
                    sum += srcPtr[rr * width + cc];
                    ++counter;
                }
            dstPtr[row * width + col] = sum / counter;
        }

        // 右边界，从width - radius到width - 1作为右边界，不与上下边界重复行
        private void ProcessRightBorder()
        {
            unsafe
            {
                fixed (float* srcPtr = src, dstPtr = dst)
                {
                    // dst[row, col]为需要计算的边界点
                    for (int row = radius + 1; row < height - radius; ++row)
                        for (int col = width - radius; col < width; ++col)
                        {
                            setRightBorderPtr(srcPtr, dstPtr, row, col);
                        }
                }
            }
        }

        private unsafe void setRightBorderPtr(float* srcPtr, float* dstPtr, int row, int col)
        {
            float sum = 0;
            int counter = 0;
            for (int rr = row - radius; rr <= row + radius; ++rr) // 跟第row行相邻
                for (int cc = col - radius; cc <= col + radius; ++cc) // 跟col列相邻
                {
                    if (cc >= width) continue;
                    sum += srcPtr[rr * width + cc];
                    ++counter;
                }
            dstPtr[row * width + col] = sum / counter;
        }

        // 内部处理算法：http://www.docin.com/p-61224746.html
        // u(x + 1, y) = u(x, y) + sum / (2 * radius + 1)^2，其中sum的计算可进一步优化
        private void ProcessInner()
        {
            // startX和stopX限定需要计算的像素点的内部范围
            int startRow = radius + 1;
            int stopRow = height - radius;
            int startCol = radius;
            int stopCol = width - radius;
            int counter = (2 * radius + 1) * (2 * radius + 1);

            // xRow和xCol限定像素点的邻域范围
            int buttomRow;
            int topRow;
            int leftCol;
            int rightCol;

            unsafe
            {
                fixed (float* srcPtr = src, dstPtr = dst)
                {
                    for (int row = startRow; row < stopRow; ++row)
                    {
                        float[] fsum = new float[width];
                        // 从左到右扫描邻域求得第一个sum值
                        buttomRow = row + radius;
                        topRow = row - radius - 1;
                        rightCol = startCol + radius;
                        leftCol = startCol - radius;
                        for (int cc = leftCol; cc <= rightCol; ++cc)
                            fsum[startCol] += srcPtr[buttomRow * width + cc] 
                                - srcPtr[topRow * width + cc];

                        // 其后的每个sum值可直接计算得出
                        for (int col = startCol + 1; col < stopCol; ++col)
                        {
                            rightCol = col + radius;
                            leftCol = col - radius - 1; // 左边像素的最左邻域
                            fsum[col] = fsum[col - 1]
                                + (srcPtr[buttomRow * width + rightCol]
                                - srcPtr[buttomRow * width + leftCol])
                                - (srcPtr[topRow * width + rightCol]
                                - srcPtr[topRow * width + leftCol]);
                        }

                        // 均值计算
                        for (int col = startCol; col < stopCol; ++col)
                        {
                            dstPtr[row * width + col] = dstPtr[(row - 1) * width + col]
                                + fsum[col] / counter;
                        }
                    } // end for (row
                } // end fixed
            } // end unsafe
        } // end function

    } // end class
}
