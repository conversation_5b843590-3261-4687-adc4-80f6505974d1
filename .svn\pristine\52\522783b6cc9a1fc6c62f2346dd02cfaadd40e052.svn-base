﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using AxMapWinGIS;
using MapWinGIS;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 编辑点标记对话框
    /// </summary>
    public partial class PointMarkEditDlg : Form
    {
        private List<SfLayerInfo> sfLayerList;
        private AxMap map;
        private MapForm mapForm;
        private PointMarkManager pmManager;
        private List<ExPointMark> exPointList;

        public PointMarkEditDlg(AxMap map, MapForm mapForm, int pixelX, int pixelY)
        {
            InitializeComponent();
            this.map = map;
            this.mapForm = mapForm;
            this.sfLayerList = this.mapForm.AllLayerList;

            pmManager = new PointMarkManager(this.sfLayerList);
            exPointList = pmManager.GetSelectedPointDic(this.map, pixelX, pixelY, pmManager.GetShownPointDic());
            if (exPointList.Count == 0)
            {
                this.DialogResult = DialogResult.None;
            }
            else
            {
                this.DialogResult = DialogResult.OK;
                Init();
            }
        }

        private void Init()
        {
            foreach (ExPointMark point in exPointList)
            {
                int idx = this.gvPoints.Rows.Add();
                this.gvPoints.Rows[idx].Cells[0].Value = this.pmManager.ToShowName(point.FileName);
                this.gvPoints.Rows[idx].Cells[1].Value = point.FieldValue;
                this.gvPoints.Rows[idx].Cells[2].Value = point.Longitude;
                this.gvPoints.Rows[idx].Cells[3].Value = point.Latitude;
            }
            this.gvPoints.SelectionChanged += Select_Changed;
            this.gvPoints.CellEndEdit += CellEdit_End;
        }

        private void CellEdit_End(object sender, DataGridViewCellEventArgs e)
        {
            int r = e.RowIndex, c = e.ColumnIndex;
            ExPointMark p = this.exPointList[r];
            Object[] o = new Object[] { "", p.FieldValue, p.Longitude, p.Latitude };
            string err = string.Empty;
            string str = p.FieldValue;
            double x = p.Longitude;
            double y = p.Latitude;

            judgeValid(r, c, p, ref err, ref str, ref x, ref y);

            if (!string.IsNullOrEmpty(err))
            {
                MessageBox.Show(this, err, "错误");
                this.gvPoints.Rows[r].Cells[c].Value = o[c];
                return;
            }

            if (str == p.FieldValue && x == p.Longitude && y == p.Latitude)
            {
                return;
            }

            UpdateModification(str, x, y, e.RowIndex);
        }

        private void judgeValid(int r, int c, ExPointMark p, ref string err, ref string str, ref double x, ref double y)
        {
            object obj = gvPoints.Rows[r].Cells[c].Value;
            if (obj == null)
            {
                err = "输入不能为空";
            }

            if (c == 1 && string.IsNullOrEmpty(err))
            {
                str = judgeStringInput(p, ref err, obj);
            }
            else if (c == 2 && string.IsNullOrEmpty(err))
            {
                judgeDoubleInput(ref err, ref x, obj);
            }
            else if (c == 3 && string.IsNullOrEmpty(err))
            {
                judgeDoubleInput(ref err, ref y, obj);
            }
        }

        private string judgeStringInput(ExPointMark p, ref string err, object obj)
        {
            string str = (obj as string);
            if (string.IsNullOrEmpty(str) || string.IsNullOrEmpty(str.Trim()))
            {
                err = "输入不能为空";
            }
            else
            {
                str = str.Trim();
                if (!str.Equals(p.FieldValue) && this.pmManager.IsFieldExisted(p.FileName, str))
                {
                    err = "字段值已经存在";
                }
            }

            return str;
        }

        private void judgeDoubleInput(ref string err, ref double data, object obj)
        {
            try
            {
                data = Convert.ToDouble(obj as string);
            }
            catch
            {
                err = "输入格式错误";
            }
        }

        private void Select_Changed(object sender, EventArgs e)
        {
            if (this.gvPoints.SelectedCells.Count == 0)
            {
                this.btnDelete.Enabled = false;
                return;
            }

            DataGridViewCell selCell = this.gvPoints.SelectedCells[0];
            if (selCell.ColumnIndex == 0)
            {
                this.gvPoints.Rows[selCell.RowIndex].Selected = true;
                this.btnDelete.Enabled = true;
            }
            else
            {
                this.btnDelete.Enabled = false;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void PointMarkEditDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult == DialogResult.Retry)
            {
                e.Cancel = true;
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            ExPointMark point = exPointList[this.gvPoints.CurrentRow.Index];
            Shapefile shpFile = this.pmManager.GetOpenedShapefile(point.FileName);
            if (!point.Delete(shpFile))
            {
                MessageBox.Show(this, "删除失败", "错误", MessageBoxButtons.OK);
                this.DialogResult = DialogResult.Retry;
                return;
            }

            this.exPointList.Remove(point);
            this.gvPoints.Rows.RemoveAt(this.gvPoints.CurrentRow.Index);
            this.pmManager.RefreshLayer(shpFile, this.map, this.mapForm);
        }

        private void UpdateModification(string str, double x, double y, int row)
        {
            ExPointMark point = exPointList[row];
            ExPointMark newPoint = new ExPointMark(x, y, str);
            Shapefile shpFile = this.pmManager.GetOpenedShapefile(point.FileName);
            newPoint.FileName = point.FileName;
            point.Update(shpFile, newPoint);
            exPointList[row] = newPoint;
            this.pmManager.RefreshLayer(shpFile, this.map, this.mapForm);
        }
    }
}
