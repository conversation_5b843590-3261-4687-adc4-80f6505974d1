﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteMosWithRtpEvtAnaByFile : VolteMosWithRtpEvtAnaBase
    {
        private VolteMosWithRtpEvtAnaByFile()
            : base()
        {
        }

        private static VolteMosWithRtpEvtAnaByFile instance = null;
        public static VolteMosWithRtpEvtAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteMosWithRtpEvtAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "弱MOS关联RTP问题分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class VolteMosWithRtpEvtAnaByFile_FDD : VolteMosWithRtpEvtAnaBase_FDD
    {
        private static VolteMosWithRtpEvtAnaByFile_FDD instance = null;
        public static VolteMosWithRtpEvtAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteMosWithRtpEvtAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected VolteMosWithRtpEvtAnaByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "弱MOS关联RTP问题分析(VOLTE_FDD按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
