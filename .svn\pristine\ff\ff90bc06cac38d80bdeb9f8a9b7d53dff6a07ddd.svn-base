﻿namespace MasterCom.RAMS.Func
{
    partial class MainCellCoFrequencyForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainCellCoFrequencyForm));
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.grpSetting = new DevExpress.XtraEditors.GroupControl();
            this.chkShowLine = new DevExpress.XtraEditors.CheckEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numCellCnt = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpSetting)).BeginInit();
            this.grpSetting.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkShowLine.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCnt.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(903, 269);
            this.gridControl.TabIndex = 2;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridView.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView.ColumnPanelRowHeight = 35;
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.colBCCH,
            this.colBSIC,
            this.colTCH,
            this.gridColumn7,
            this.gridColumn8});
            this.gridView.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView.GridControl = this.gridControl;
            this.gridView.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsDetail.EnableMasterViewMode = false;
            this.gridView.OptionsDetail.ShowDetailTabs = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView_CustomDrawCell);
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "类型";
            this.gridColumn9.FieldName = "CellType";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            this.gridColumn9.Width = 56;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区名";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            this.gridColumn1.Width = 136;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "LAC";
            this.gridColumn2.FieldName = "LAC";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 49;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "CI";
            this.gridColumn3.FieldName = "CI";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 48;
            // 
            // colBCCH
            // 
            this.colBCCH.Caption = "BCCH/CPI";
            this.colBCCH.FieldName = "BCCH";
            this.colBCCH.Name = "colBCCH";
            this.colBCCH.Visible = true;
            this.colBCCH.VisibleIndex = 4;
            this.colBCCH.Width = 74;
            // 
            // colBSIC
            // 
            this.colBSIC.Caption = "BSIC";
            this.colBSIC.FieldName = "BSIC";
            this.colBSIC.Name = "colBSIC";
            this.colBSIC.Visible = true;
            this.colBSIC.VisibleIndex = 5;
            this.colBSIC.Width = 61;
            // 
            // colTCH
            // 
            this.colTCH.Caption = "TCH/FREQ";
            this.colTCH.FieldName = "TCH";
            this.colTCH.Name = "colTCH";
            this.colTCH.Visible = true;
            this.colTCH.VisibleIndex = 6;
            this.colTCH.Width = 280;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "与采样点距离(米)";
            this.gridColumn7.FieldName = "Distance";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 7;
            this.gridColumn7.Width = 61;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "方向角与到采样点的连线之间的夹角";
            this.gridColumn8.FieldName = "Angle";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 8;
            this.gridColumn8.Width = 107;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.grpSetting);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControl);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(903, 338);
            this.splitContainerControl1.SplitterPosition = 63;
            this.splitContainerControl1.TabIndex = 3;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // grpSetting
            // 
            this.grpSetting.Controls.Add(this.chkShowLine);
            this.grpSetting.Controls.Add(this.label2);
            this.grpSetting.Controls.Add(this.label1);
            this.grpSetting.Controls.Add(this.numCellCnt);
            this.grpSetting.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpSetting.Location = new System.Drawing.Point(0, 0);
            this.grpSetting.Name = "grpSetting";
            this.grpSetting.Size = new System.Drawing.Size(903, 63);
            this.grpSetting.TabIndex = 0;
            this.grpSetting.Text = "设置";
            // 
            // chkShowLine
            // 
            this.chkShowLine.EditValue = true;
            this.chkShowLine.Location = new System.Drawing.Point(313, 32);
            this.chkShowLine.Name = "chkShowLine";
            this.chkShowLine.Properties.Caption = "显示与采样点的拉线";
            this.chkShowLine.Size = new System.Drawing.Size(136, 19);
            this.chkShowLine.TabIndex = 2;
            this.chkShowLine.CheckedChanged += new System.EventHandler(this.chkShowLine_CheckedChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(198, 35);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "个同频小区";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(21, 35);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "距离最近的";
            // 
            // numCellCnt
            // 
            this.numCellCnt.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numCellCnt.Location = new System.Drawing.Point(92, 30);
            this.numCellCnt.Name = "numCellCnt";
            this.numCellCnt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numCellCnt.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.numCellCnt.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.numCellCnt.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCellCnt.Size = new System.Drawing.Size(100, 21);
            this.numCellCnt.TabIndex = 0;
            this.numCellCnt.EditValueChanged += new System.EventHandler(this.numCellCnt_EditValueChanged);
            // 
            // MainCellCoFrequencyForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("MainCellCoFrequencyForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(903, 338);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "MainCellCoFrequencyForm";
            this.Text = "主服/主强小区同频列表";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainCellCoFrequencyForm_FormClosing);
            this.VisibleChanged += new System.EventHandler(this.MainCellCoFrequencyForm_VisibleChanged);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpSetting)).EndInit();
            this.grpSetting.ResumeLayout(false);
            this.grpSetting.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkShowLine.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCnt.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn colBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn colBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn colTCH;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl grpSetting;
        private DevExpress.XtraEditors.SpinEdit numCellCnt;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.CheckEdit chkShowLine;
    }
}