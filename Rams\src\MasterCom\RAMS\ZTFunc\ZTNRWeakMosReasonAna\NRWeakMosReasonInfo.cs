﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakMosReasonInfo : WeakMosReasonInfoBase
    {
        public NRWeakMosReasonInfo(string fileName, Event callBeginEvt)
            : base(fileName, callBeginEvt)
        {
        }

        public float? LteRsrpAvg_MosPeriod { get; set; }
        public float? LteSinrAvg_MosPeriod { get; set; }

        public DataInfo LteRsrpInfo { get; set; } = new DataInfo();
        public DataInfo LteSinrInfo { get; set; } = new DataInfo();

        public DataInfo OppositeRsrpInfo { get; private set; } = new DataInfo();
        public DataInfo OppositeSinrInfo { get; private set; } = new DataInfo();
        public DataInfo OppositeLteRsrpInfo { get; private set; } = new DataInfo();
        public DataInfo OppositeLteSinrInfo { get; private set; } = new DataInfo();

        protected override float? getRsrp(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellRsrp(tp);
        }

        protected override float? getSinr(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellSinr(tp);
        }

        public override void GetTPInfo()
        {
            base.GetTPInfo();

            foreach (TestPoint tp in MosTestPoints)
            {
                float? rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
                LteRsrpInfo.Add(rsrp);

                float? sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
                LteSinrInfo.Add(sinr);
            }
            LteRsrpInfo.GetAvg();
            LteSinrInfo.GetAvg();
        }

        public void SetOppositeInfo(NRWeakMosReasonInfo info)
        {
            OppositeRsrpInfo = info.RsrpInfo;
            OppositeSinrInfo = info.SinrInfo;
            OppositeLteRsrpInfo = info.LteRsrpInfo;
            OppositeLteSinrInfo = info.LteSinrInfo;
        }
    }
}
