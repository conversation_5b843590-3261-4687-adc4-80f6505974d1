﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage.HighReverseFlowCoverageImport
{
    public partial class HighReverseFlowCoverageImportDlg : BaseDialog
    {
        ImportCondition curCondition = new ImportCondition();
        public HighReverseFlowCoverageImportDlg(ImportExcelDataHelper import)
        {
            InitializeComponent();

            importDataPanel.Init(import);
            importDataPanel.SetLoadData(false);
            dataBaseConnection.Init("主库连接设置");
        }

        public void SetCondition(ImportCondition condition)
        {
            importDataPanel.SetCondtion(condition.Path);
            dataBaseConnection.SetCondition(condition.Condition.DBCond);
        }

        public ImportCondition GetCondition()
        {
            return curCondition;  
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            curCondition.Path = importDataPanel.GetCondtion();
            if (string.IsNullOrEmpty(curCondition.Path))
            {
                MessageBox.Show("文件路径不能为空");
                return;
            }

            var cond = dataBaseConnection.GetCondition();
            if (cond == null)
            {
                MessageBox.Show("主库连接设置不能为空");
                return;
            }

            curCondition.Condition.DBCond = cond;
            DialogResult = DialogResult.OK;
        }
    }

    public class ImportCondition
    {
        public string Path { get; set; } = "";
        public CoverageCondition Condition { get; set; } = new CoverageCondition();
    }
}
