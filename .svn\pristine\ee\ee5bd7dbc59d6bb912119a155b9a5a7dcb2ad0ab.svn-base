﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckAnaByRegion_GSM : ZTLteNBCellCheckAnaBase_GSM
    {
        public ZTLteNBCellCheckAnaByRegion_GSM(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLteNBCellCheckAnaByRegion_GSM intance = null;
        public static ZTLteNBCellCheckAnaByRegion_GSM GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLteNBCellCheckAnaByRegion_GSM(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "LTE邻区核查(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22026, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}