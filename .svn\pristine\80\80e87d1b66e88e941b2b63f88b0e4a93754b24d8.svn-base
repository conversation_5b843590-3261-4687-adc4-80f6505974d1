﻿using System;
using System.Collections.Generic;
using System.Text;
using DBDataViewer;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    [Serializable]
    public class StatDataEvent : KPIStatDataBase
    {
        public const string StaticToken = "evt";
        public override string Token
        {
            get { return StaticToken; }
        }
        public override bool GatherStatData(KPIStatDataBase data)
        {
            StatDataEvent other = data as StatDataEvent;
            if (other != null)
            {
                foreach (string eventID in other.fieldValueDic.Keys)
                {
                    if (this.fieldValueDic.ContainsKey(eventID))
                    {
                        fieldValueDic[eventID] = fieldValueDic[eventID] + other.fieldValueDic[eventID];
                    }
                    else
                    {
                        fieldValueDic[eventID] = other.fieldValueDic[eventID];
                    }
                }
                return true;
            }
            return false;
        }

        private double ltLat;
        public override double LTLat
        {
            get
            {
                return ltLat;
            }
        }

        private double ltLng;
        public override double LTLng
        {
            get
            {
                return ltLng;
            }
        }

        private int fileID;
        public override int FileID
        {
            get
            {
                return fileID;
            }
        }

        public StatDataEvent(Event evt, bool separateByServiceID, string fileNameKey)
        {
            this.fileID = evt.FileID;
            ltLat = MasterCom.RAMS.Grid.GridHelper.RoundAsTop(evt.Latitude);
            ltLng = MasterCom.RAMS.Grid.GridHelper.RoundAsLeft(evt.Longitude);
            //area stat
            string countKey = string.Format("evtIdCount[{0}]", evt.ID - 1);
            fieldValueDic[countKey] = evt.EventNum;
            for (int i = 1; i < 11; i++)
            {
                string valueXKey = string.Format("value{0}[{1}]", i, evt.ID - 1);
                string valueXStr = string.Format("Value{0}", i);
                object objV = evt[valueXStr];
                double dV;
                if (objV != null && double.TryParse(objV.ToString(), out dV))
                {
                    fieldValueDic[valueXKey] = dV;
                }
            }
            if (separateByServiceID || !string.IsNullOrEmpty(fileNameKey))
            {
                int evtServiceId = -1;
                if (separateByServiceID)
                {
                    evtServiceId = evt.ServiceType;
                }
                List<string> keyList = CreateKeysWithServIdAndFileName(countKey, evtServiceId, fileNameKey);
                setDataByServIdAndFileName(evt, fileNameKey, keyList);
            }
        }

        private void setDataByServIdAndFileName(Event evt, string fileNameKey, List<string> keyList)
        {
            foreach (string strKey in keyList)
            {
                fieldValueDic[strKey] = evt.EventNum;
            }
            for (int i = 1; i < 11; i++)
            {
                string valueXKey = string.Format("value{0}[{1}]", i, evt.ID - 1);
                keyList = CreateKeysWithServIdAndFileName(valueXKey, evt.ServiceType, fileNameKey);
                string valueXStr = string.Format("Value{0}", i);
                object objV = evt[valueXStr];
                double dV;
                if (objV != null && double.TryParse(objV.ToString(), out dV))
                {
                    foreach (string strKey in keyList)
                    {
                        fieldValueDic[strKey] = dV;
                    }
                }
            }
        }

        //internal double GetValue(string keyWithoutToken, List<int> serviceIDSet)
        //{
        //    if (serviceIDSet == null || serviceIDSet.Count == 0)
        //    {
        //        return this[keyWithoutToken, -1];
        //    }
        //    double ret = 0;
        //    foreach (int id in serviceIDSet)
        //    {
        //        double temp = this[keyWithoutToken, id];
        //        if (!double.IsNaN(temp))
        //        {
        //            ret += temp;
        //        }
        //    }
        //    return ret;
        //}
    }

    #region voice & data
    [Serializable]
    public class StatDataGSM : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Mx_"; }
        }
    }

    [Serializable]
    public class StatDataGSM_MTR : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Ux_"; }
        }
    }

    [Serializable]
    public class StatDataTD : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Tx_"; }
        }
    }

    [Serializable]
    public class StatDataLTE : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Lte_"; }
        }
    }

    [Serializable]
    public class StatDataLTE_FDD : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Lf_"; }
        }
    }

    [Serializable]
    public class StatDataLTE_Signal : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Sn_"; }
        }
    }

    [Serializable]
    public class StatDataWLAN : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Wl_"; }
        }
    }

    [Serializable]
    public class StatDataWCDMA : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Wx_"; }
        }
    }

    [Serializable]
    public class StatDataCDMA_Voice : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Cx_"; }
        }
    }

    [Serializable]
    public class StatDataCDMA_EVDO : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Ex_"; }
        }
    }

    [Serializable]
    public class StatDataNR : KPIStatDataBase
    {
        public const string RSRPSampleNumKey = "BA040001";
        public const string RSRPMeanValueKey = "BA040002";

        public override string Token
        {
            get { return "Nr_"; }
        }
    }
    #endregion

    #region scan

    [Serializable]
    public abstract class ScanKPIData : KPIStatDataBase
    {
        public Dictionary<int, Dictionary<string, double>> CellInicatorDic
        {
            get { return cellIndicatorDic; }
        }
        protected Dictionary<int, Dictionary<string, double>> cellIndicatorDic = new Dictionary<int, Dictionary<string, double>>();
        public void AddCellStatData(int cellID, Dictionary<string, double> dataDic)
        {
            Dictionary<string, double> indicatorDic;
            if (this.cellIndicatorDic.TryGetValue(cellID, out indicatorDic))
            {
                StatDataConverter.gatherStatImgInfo(dataDic, indicatorDic);
            }
            else
            {
                this.cellIndicatorDic[cellID] = dataDic;
            }
        }
        public override bool GatherStatData(KPIStatDataBase data)
        {
            if (base.GatherStatData(data))
            {
                ScanKPIData other = data as ScanKPIData;
                foreach (int cellID in other.cellIndicatorDic.Keys)
                {
                    AddCellStatData(cellID, other.cellIndicatorDic[cellID]);
                }
            }
            return false;
        }

        public double this[int cellID, string keyWithoutToken]
        {
            get
            {
                Dictionary<string, double> indicatorDic = null;
                double val = double.NaN;
                if (cellIndicatorDic.TryGetValue(cellID, out indicatorDic)
                    && !indicatorDic.TryGetValue(keyWithoutToken, out val))
                {
                    val = double.NaN;
                }
                return val;
            }
        }

        public override string Token
        {
            get { return "Scan"; }
        }
    }

    [Serializable]
    public class StatDataSCAN_CDMA : ScanKPIData
    {
        public override string Token
        {
            get { return "Cc_"; }
        }
    }

    [Serializable]
    public class StatDataSCAN_GSM : ScanKPIData
    {
        public const string CellNameKey = "0848";
        public const string BCCHKey = "0846";
        public const string BSICKey = "0847";
        public const string RxlevMeanValueKey = "5F0C0104";
        public const string RxlevSampleCountKey = "5F0C0101";
        public const string RxlevMaxKey = "5F0C0102";
        public const string RxlevMinKey = "5F0C0103";
        public const string RxqualMeanValueKey = "5F0C0204";
        public const string RxqualMaxValueKey = "5F0C0202";
        public const string RxqualSampleNumKey = "5F0C0201";
        public const string C_IMeanValueKey = "5F0C0304";
        public const string BERMeanValueKey = "5F0C0404";

        public override string Token
        {
            get { return "Gc_"; }
        }
    }

    [Serializable]
    public class StatDataSCAN_TD : ScanKPIData
    {
        public const string CellNameKey = "0848";
        public const string CellIDKey = "0849";
        public const string ChannelKey = "084A";
        public const string CPIKey = "084B";
        public const string PCCPCHRSCPMeanValueKey = "5F13030D";
        public const string PCCPCHRSCPSampleNumKey = "5F13030A";
        public const string PCCPCHRSCPMaxKey = "5F13030B";
        public const string PCCPCHRSCPMinKey = "5F13030C";

        public override string Token
        {
            get { return "Tc_"; }
        }
    }

    [Serializable]
    public class StatDataSCAN_LTE : ScanKPIData
    {
        public const string CellFreqKey = "087A";
        public const string CellPCIKey = "087B";
        public const string PSSRPSampleNumKey = "5F230101";
        public const string PSSRPMeanValueKey = "5F230102";
        public const string PSSRPMaxValueKey = "5F230103";
        public const string PSSRPMinValueKey = "5F230104";

        public override string Token
        {
            get { return "Lc_"; }
        }
    }

    [Serializable]
    public class StatDataSCAN_NBIOT : StatDataSCAN_LTE
    {
    }

    [Serializable]
    public class StatDataSCAN_WCDMA : ScanKPIData
    {
        public const string CellNameKey = "0848";
        public const string CellIDKey = "0849";
        public const string ChannelKey = "084A";
        public const string CPIKey = "084B";
        public const string RSCPMeanValueKey = "5F153F";
        public const string RSCPSampleNumKey = "5F153C";
        public const string RSCPMaxKey = "5F153D";
        public const string RSCPMinKey = "5F153E";
        public override string Token
        {
            get { return "Wc_"; }
        }
    }

    [Serializable]
    public class StatDataSCAN_NR : KPIStatDataBase
    {
        public override string Token
        {
            get { return "Nc_"; }
        }
    }
    #endregion

}
