﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    public partial class HighReverseFlowCoverageDlg : BaseDialog
    {
        readonly CoverageCondition curCondition = new CoverageCondition();

        public HighReverseFlowCoverageDlg()
        {
            InitializeComponent();
            dataBaseConnection.Init("主库连接设置");
        }

        public void SetCondition(CoverageCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numDistance.Value = condition.Distance;
            dataBaseConnection.SetCondition(condition.DBCond);
        }

        public CoverageCondition GetCondition()
        {
            return curCondition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            curCondition.Distance = (int)numDistance.Value;
            var cond = dataBaseConnection.GetCondition();
            if (cond != null)
            {
                curCondition.DBCond = cond;
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }
    }
}
