﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakCoverGrid : TDWeakCovRoadInfo
    {
        public ZTWeakCoverGrid(double gridSpan, DbRect regionGrdiBounds)
        {
            this.gridSpan = gridSpan;
            this.regionGrdiBounds = regionGrdiBounds;
        }

        private List<DbRect> grids = null;
        public List<DbRect> Grids
        {
            get { return grids; }
        }

        private readonly DbRect regionGrdiBounds= null;
        private readonly double gridSpan = 0;
        public override void GetResult(bool saveTestPoints)
        {
            base.GetResult(saveTestPoints);
            makeGrid();
        }

        List<string> gridIdxList = null;
        private void makeGrid()
        {
            gridIdxList = new List<string>(SampleList.Count);
            grids = new List<DbRect>();
            foreach (TestPoint tp in SampleList)
            {
                int colIdx = 0;
                int rowIdx = 0;
                getGridIdx(tp, out colIdx, out rowIdx);
                string idxStr = rowIdx.ToString() + "_" + colIdx.ToString();
                if (!gridIdxList.Contains(idxStr))
                {
                    gridIdxList.Add(idxStr);
                    double ltX = regionGrdiBounds.x1 + colIdx * gridSpan;
                    double ltY = regionGrdiBounds.y2 - rowIdx * gridSpan;
                    double brX = ltX + gridSpan;
                    double brY = ltY - gridSpan;
                    grids.Add(new DbRect(ltX, brY, brX, ltY));
                }
            }
        }

        protected void getGridIdx(TestPoint tp, out int colIdx, out int rowIdx)
        {
            double xDis = tp.Longitude - regionGrdiBounds.x1;
            colIdx = (int)(xDis / gridSpan);
            double yDis = regionGrdiBounds.y2 - tp.Latitude;
            rowIdx = (int)(yDis / gridSpan);
        }

        public bool CanMerge(ZTWeakCoverGrid otherGrid)
        {
            foreach (TestPoint tp in otherGrid.SampleList)
            {
                if (isInGrid(tp))
                {
                    return true;
                }
            }
            return false;
        }

        private bool isInGrid(TestPoint tp)
        {
            bool ret = false;
            if (grids != null)
            {
                int colIdx = 0;
                int rowIdx = 0;
                getGridIdx(tp, out colIdx, out rowIdx);
                string idxStr = rowIdx.ToString() + "_" + colIdx.ToString();
                ret = gridIdxList.Contains(idxStr);
            }
            return ret;
        }

    }
}
