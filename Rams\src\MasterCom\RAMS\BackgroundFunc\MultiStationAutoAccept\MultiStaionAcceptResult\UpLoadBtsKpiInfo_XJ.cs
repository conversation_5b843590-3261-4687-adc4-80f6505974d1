﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class UpLoadOutdoorBtsKpiInfo_XJ : UpLoadBtsKpiInfoBase_XJ
    {
        readonly List<OutDoorBtsAcceptInfo> btsAcceptInfoList;
        public UpLoadOutdoorBtsKpiInfo_XJ(DateTime beginTime, DateTime endTime, List<OutDoorBtsAcceptInfo> btsAcceptInfoList)
            : base()
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.btsAcceptInfoList = btsAcceptInfoList;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (OutDoorBtsAcceptInfo btsInfo in btsAcceptInfoList)
            {
                foreach (OutDoorCellAcceptInfo cellInfo in btsInfo.CellsAcceptDic.Values)
                {
                    strb.AppendLine(string.Format("delete from tb_xinjiang_cellCheck_kpiInfo where eci = {0};"
                        , cellInfo.LteCell.ECI));

                    strb.AppendLine(string.Format(@"insert into tb_xinjiang_cellCheck_kpiInfo([eci],[cellName]
,[beginTime],[endTime],[ulSpeed],[dlSpeed],[dlRsrpAvg],[dlSinrAvg],[csfbCallSuccessRate],[attachSuccessRate]
,[rrcSetupSuccessRate],[erabSetupSuccessRate]) values ({0}, '{1}','{2}' ,'{3}',{4}, {5}, {6}, {7}, {8}, {9}, {10}, {11});"
                        , cellInfo.LteCell.ECI, cellInfo.CellName, beginTime.ToString(), endTime.ToString()
                        , getValueDes(cellInfo.FtpUlInfo.SpeedAvg), getValueDes(cellInfo.FtpDlInfo.SpeedAvg)
                        , getValueDes(cellInfo.FtpDlInfo.RsrpAvg), getValueDes(cellInfo.FtpDlInfo.SinrAvg)
                        , getValueDes(cellInfo.CsfbInfo.Rate), getValueDes(cellInfo.AccessInfo.Rate)
                        , getValueDes(cellInfo.RrcInfo.Rate), getValueDes(cellInfo.ErabInfo.Rate)));
                }
            }
            return strb.ToString();
        }
    }
    public class UpLoadIndoorBtsKpiInfo_XJ : UpLoadBtsKpiInfoBase_XJ
    {
        readonly List<InDoorBtsAcceptInfo> btsAcceptInfoList;
        public UpLoadIndoorBtsKpiInfo_XJ(DateTime beginTime, DateTime endTime, List<InDoorBtsAcceptInfo> btsAcceptInfoList)
            : base()
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.btsAcceptInfoList = btsAcceptInfoList;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (InDoorBtsAcceptInfo btsInfo in btsAcceptInfoList)
            {
                foreach (InDoorCellAcceptInfo cellInfo in btsInfo.CellsAcceptDic.Values)
                {
                    strb.AppendLine(string.Format("delete from tb_xinjiang_cellCheck_kpiInfo where eci = {0};"
                        , cellInfo.LteCell.ECI));

                    strb.AppendLine(string.Format(@"insert into tb_xinjiang_cellCheck_kpiInfo([eci],[cellName]
,[beginTime],[endTime],[ulSpeed],[dlSpeed],[dlRsrpAvg],[dlSinrAvg],[csfbCallSuccessRate],[attachSuccessRate]
,[rrcSetupSuccessRate],[erabSetupSuccessRate]) values ({0}, '{1}','{2}' ,'{3}',{4}, {5}, {6}, {7}, {8}, {9}, {10}, {11});"
                        , cellInfo.LteCell.ECI, cellInfo.CellName, beginTime.ToString(), endTime.ToString()
                        , getValueDes(cellInfo.FtpUlSpeedInfo.KpiAvgValue), getValueDes(cellInfo.FtpDlSpeedInfo.KpiAvgValue)
                        , getValueDes(cellInfo.FtpRsrpInfo.KpiAvgValue), getValueDes(cellInfo.FtpSinrInfo.KpiAvgValue)
                        , getValueDes(cellInfo.CsfbInfo.Rate), getValueDes(cellInfo.AccessInfo.Rate)
                        , getValueDes(cellInfo.RrcInfo.Rate), getValueDes(cellInfo.ErabInfo.Rate)));
                }
            }
            return strb.ToString();
        }
    }

    public abstract class UpLoadBtsKpiInfoBase_XJ : DiySqlMultiNonQuery
    {
        protected DateTime beginTime;
        protected DateTime endTime;

        protected string getValueDes(object obj)
        {
            return obj == null ? "null" : obj.ToString();
        }
    }
}
