﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTerminalSatisfactionSQLQuery : DIYSQLBase
    {
        public ZTTerminalSatisfactionSQLQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "用户终端满意度统计"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        private string city;
        public void SetCity(string city)
        {
            this.city = city;
        }

        private string month;
        public void SetMonth(string month)
        {
            this.month = month;
        }

        private string requestioType;
        public void SetRequestionType(string requestioType)
        {
            this.requestioType = requestioType;
        }

        protected override string getSqlTextString()
        {
            //string sql = "execute sp_stat_wlmq_user_satisfaction select city,ymonth,terminalType,cast(SUM(networkCover) as float)*100/SUM(totalcount),cast(SUM(networkQuality) as float)*100/SUM(totalcount) ,cast(SUM(mobileInternet) as float)*100/SUM(totalcount) ,cast(SUM(voiceCall) as float)*100/SUM(totalcount) from wlmq_stat_user_satisfaction where (" + (this.city == null ? "null" : ("'" + this.city + "'")) + " is null or city='" + this.city + "') and (" + (this.month == null ? "null" : ("'" + this.month + "'")) + " is null or ymonth='" + this.month + "') and (" + (this.requestioType == null ? "null" : ("'" + this.requestioType + "'")) + " is null or requestionType='" + this.requestioType + "') group by city,ymonth,terminalType order by city,ymonth,terminalType";
            string sql = "execute Complain_Sys..sp_stat_wlmq_user_satisfaction select city,ymonth,terminalType,cast(SUM(networkCover) as float)*100/SUM(totalcount),cast(SUM(networkQuality) as float)*100/SUM(totalcount) ,cast(SUM(mobileInternet) as float)*100/SUM(totalcount) ,cast(SUM(voiceCall) as float)*100/SUM(totalcount) from Complain_Sys..wlmq_stat_user_satisfaction where (" + (this.city == null ? "null" : ("'" + this.city + "'")) + " is null or city='" + this.city + "') and (ymonth=" + (this.month == null ? "(select MAX(ymonth) from Complain_Sys..wlmq_stat_user_satisfaction where Provider='中国移动')" : ("'" + this.month + "'")) + " or '" + this.month + "'='全部') and (" + (this.requestioType == null ? "null" : ("'" + this.requestioType + "'")) + " is null or requestionType='" + this.requestioType + "') group by city,ymonth,terminalType order by city,ymonth desc,terminalType";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[7];
            vType[0] = E_VType.E_String;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_String;
            vType[3] = E_VType.E_Float;
            vType[4] = E_VType.E_Float;
            vType[5] = E_VType.E_Float;
            vType[6] = E_VType.E_Float;
            return vType;
        }

        private List<TerminalStatData> terminalStatDatas = new List<TerminalStatData>();
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
            fireResultForm();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            terminalStatDatas = new List<TerminalStatData>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TerminalStatData terminaldata = new TerminalStatData();
                    terminaldata.City = package.Content.GetParamString();
                    terminaldata.Month = package.Content.GetParamString();
                    terminaldata.TerminalType = package.Content.GetParamString();
                    terminaldata.networkCover = package.Content.GetParamFloat();
                    terminaldata.networkQuality = package.Content.GetParamFloat();
                    terminaldata.mobileInternet = package.Content.GetParamFloat();
                    terminaldata.voiceCall = package.Content.GetParamFloat();

                    terminalStatDatas.Add(terminaldata);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            WaitBox.Close();
        }

        protected void fireResultForm()
        {
            TerminalSatisfactionReportForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(TerminalSatisfactionReportForm).FullName) as TerminalSatisfactionReportForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new TerminalSatisfactionReportForm(MainModel);
            }
            frm.FillData(terminalStatDatas);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            else
            {
                frm.BringToFront();
            }
        }
    }
}
