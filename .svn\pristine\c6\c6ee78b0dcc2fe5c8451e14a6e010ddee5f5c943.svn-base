﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.LteSignalImsi;

namespace MasterCom.RAMS.ZTFunc
{
    class LteSignalReplayEventByImsi : DIYReplayFileWithinPeriodQuery
    {
        public static LteSignalReplayEventByImsi Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LteSignalReplayEventByImsi();
                }
                return instance;
            }
        }

        private LteSignalReplayEventByImsi()
            : base(MainModel.GetInstance())
        {
            NeedAskDlg = false;
            IsAddSampleToDTDataManager = false;
            layer = new ReplayEventByImsiLayer();
            MainModel.DTDataChanged += MainModel_DTDataChanged;
        }

        public long CurrentIMSI
        {
            get;
            set;
        }

        public bool SetPeriodCondition(DTData dtData)
        {
            ReplayEventByImsiPeriodForm periodForm = new ReplayEventByImsiPeriodForm();
            if (periodForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            periodCond = periodForm.GetCondition(dtData.DateTime);
            return true;
        }

        protected override bool isValidCondition()
        {
            condition.Periods.Clear();
            condition.Periods.Add(periodCond.Period);
            layer.IsGisIntervalLimit = periodCond.IsGisIntervalLimit;
            layer.GisIntervalMinutes = periodCond.GisIntervalMinutes;
            return true;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            long? imsi = (long?)tp["signal_IMSI"];
            if (imsi != null && imsi == CurrentIMSI)
            {
                MainModel.DTDataManager.Add(tp);
            }
        }

        protected override bool isValidEvent(Event e)
        {
            object imsi = e["Value3"];
            return imsi != null && (long)imsi == CurrentIMSI;
        }

        private void MainModel_DTDataChanged(object sender, EventArgs e)
        {
            layer.DrawArrow(sender == this);
        }

        private readonly ReplayEventByImsiLayer layer;

        private ReplayEventByImsiPeriodCond periodCond;

        private static LteSignalReplayEventByImsi instance;
    }
}
