﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Stat
{
    /// <summary>
    /// 配置参数的节点
    /// </summary>
    public class ParamCfgItem
    {
        public string fileName { get; set; } = "";
        public string nodeName { get; set; }
        public string nodeDesc { get; set; } = "";
        public FieldTag tag { get; set; }
        public List<ParamCfgItem> children { get; set; } = new List<ParamCfgItem>();
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static List<ParamCfgItem> loadParamCfgFromFile(string filename)
        {
            List<ParamCfgItem> paramNodesItems = new List<ParamCfgItem>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(filename);
                List<Object> list = configFile.GetItemValue("StatParamCfg", "configs") as List<Object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        ParamCfgItem item = new ParamCfgItem();
                        item.fileName = filename;
                        item.Param = value as Dictionary<string, object>;
                        paramNodesItems.Add(item);
                    }
                }
            }
            catch (Exception e)
            {
                log.Warn("读入基础数据指标文件" + filename + "发生错误:" + e.Message);
                XtraMessageBox.Show("读入基础数据指标文件" + filename + "发生错误:" + e.Message);
            }
            return paramNodesItems;
        }


        public static void doExportTreeToFile(string filename, TreeView treeView)
        {
            List<ParamCfgItem> configItems = new List<ParamCfgItem>();
            foreach (TreeNode node in treeView.Nodes)
            {
                configItems.Add(ParamCfgItem.parseFromTreeNode(node));
            }
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("StatParamCfg");
                List<object> configs = new List<object>();
                foreach (ParamCfgItem itm in configItems)
                {
                    configs.Add(itm.Param);
                }
                configFile.AddItem(cfg, "configs", configs);
                configFile.Save(filename);
            }
            catch (Exception e)
            {
                log.Warn("保存失败!" + e.Message);
                XtraMessageBox.Show("保存失败!" + e.Message);
            }
        }

        public static void saveChangeToFile(string filename, ParamCfgItem rootParams)
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("StatParamCfg");
                List<object> configs = new List<object>();
                configs.Add(rootParams.Param);
                configFile.AddItem(cfg, "configs", configs);
                configFile.Save(filename);
            }
            catch (Exception e)
            {
                log.Warn("保存失败!" + e.Message);
                XtraMessageBox.Show("保存失败!" + e.Message);
            }
        }

        internal static void prepareDescriptionDic(Dictionary<string, string> paraDiscriptionDic, ParamCfgItem cfgItem)
        {
            FieldTag fieldTag = cfgItem.tag;
            if(cfgItem.tag!=null)
            {
                string field = fieldTag.field;
                if(fieldTag.arg!=-1)
                {
                    field += "[" + fieldTag.arg + "]";
                }
                paraDiscriptionDic[field] = cfgItem.nodeName;
            }
            foreach (ParamCfgItem c in cfgItem.children)
            {
                prepareDescriptionDic(paraDiscriptionDic, c);
            }
        }
        public static TreeNode makeTreeNodeFromCfg(ParamCfgItem item)
        {
            TreeNode tn = new TreeNode();
            tn.Text = item.nodeName;
            tn.ToolTipText = item.nodeDesc;
            tn.Tag = item.tag;
            foreach (ParamCfgItem c in item.children)
            {
                tn.Nodes.Add(makeTreeNodeFromCfg(c));
            }
            return tn;
        }
        public static ParamCfgItem parseFromTreeNode(TreeNode tn)
        {
            ParamCfgItem ret = new ParamCfgItem();
            ret.nodeName = tn.Text;
            if (tn.Tag is FieldTag)
            {
                ret.tag = tn.Tag as FieldTag;
            }
            foreach (TreeNode childnd in tn.Nodes)
            {
                ret.children.Add(parseFromTreeNode(childnd));
            }
            return ret;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Name"] = nodeName;
                param["FDesc"] = nodeDesc;
                if(tag!=null)
                {
                    param["FName"] = tag.field;
                    param["FTag"] = tag.arg;
                }
                else
                {
                    param["FName"] = "";
                }
                List<object> childParams = new List<object>();
                param["children"] = childParams;
                foreach (ParamCfgItem item in children)
                {
                    childParams.Add(item.Param);
                }
                return param;
            }
            set
            {
                nodeName = (String)value["Name"];
                nodeDesc = value.ContainsKey("FDesc") ? (string)value["FDesc"] : "";
                string fieldName = (String)value["FName"];
                if (!string.IsNullOrEmpty(fieldName))
                {
                    int fieldTag = (int)value["FTag"];
                    tag = new FieldTag(fieldName, fieldTag);
                }
                else
                {
                    tag = null;
                }
                children.Clear();
                List<object> childParams = (List<object>)value["children"];
                foreach (object o in childParams)
                {
                    Dictionary<string, object> cfgParam = (Dictionary<string, object>)o;
                    ParamCfgItem item = new ParamCfgItem();
                    item.Param = cfgParam;
                    children.Add(item);
                }
            }
        }

        
    }
}
