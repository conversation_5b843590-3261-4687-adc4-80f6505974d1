﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class FartherCoverQueryByRegion : FartherCoverQueryBase
    {
        private FartherCoverQueryByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        public FartherCoverQueryByRegion(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        private static FartherCoverQueryByRegion instance = null;
        public static FartherCoverQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FartherCoverQueryByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "超远覆盖_LTE(按区域)"; }
        }

    }
    public class FartherCoverQueryByFile : FartherCoverQueryBase
    {
        private FartherCoverQueryByFile()
            : base()
        {
        }

        public FartherCoverQueryByFile(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        private static FartherCoverQueryByFile instance = null;
        public static FartherCoverQueryByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FartherCoverQueryByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "超远覆盖_LTE(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
    public class FartherCoverQueryBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        public FartherCoverCondition FartherCoverCond { get; set; }
        protected Dictionary<string, FartherCoverInfo> nameFartherMap;
        protected List<FartherCoverInfo> fartherVec;
        public string themeName { get; set; } = "";//默认选中指标

        protected static readonly object lockObj = new object();
        protected FartherCoverQueryBase()
            : base(MainModel.GetInstance())
        {
            IncludeEvent = false;
            FartherCoverCond = new FartherCoverCondition();
            nameFartherMap = new Dictionary<string, FartherCoverInfo>();
            fartherVec = new List<FartherCoverInfo>();
            init();
        }
        protected void init()
        {
            Columns = new List<string>();
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_NCell_RSRP");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");

            themeName = "TD_LTE_RSRP";
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public override string Name
        {
            get { return "超远覆盖_LTE"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22021, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            FartherCoverSettingDlg dlg = new FartherCoverSettingDlg(FartherCoverCond);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                FartherCoverCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            nameFartherMap.Clear();
            fartherVec.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected virtual void doWithTestPoint(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            ushort? tac = (ushort?)tp["lte_TAC"];
            int? eci = (int?)tp["lte_ECI"];
            int? earfcn = (int?)tp["lte_EARFCN"];
            int? pci = (int?)(short?)tp["lte_PCI"];
            if (rsrp == null) return;
            LTECell lteCell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, (int?)tac, eci, earfcn, pci, tp.Longitude, tp.Latitude);
            if (lteCell == null) return;
            double distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lteCell.Longitude, lteCell.Latitude);
            addCoverInfo(lteCell, tp, (float)rsrp, distance);
            if (!FartherCoverCond.NBCellChecked) return;
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
                int? nEarfcn = (int?)tp["lte_NCell_EARFCN", i];
                short? nPci = (short?)tp["lte_NCell_PCI", i];

                if (nRsrp == null || nEarfcn == null || nPci == null || nRsrp < FartherCoverCond.RsrpThreshold)
                {
                    continue;
                }
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, nEarfcn, (int?)nPci, tp.Longitude, tp.Latitude);
                if (nbCell == null)
                {
                    continue;
                }
                distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nbCell.Longitude, nbCell.Latitude);
                addCoverInfo(nbCell, tp, (float)nRsrp, distance);
            }
        }
        protected void addCoverInfo(LTECell lteCell, TestPoint tp, float rsrp, double distance)
        {
            if (distance >= FartherCoverCond.DistanceMin && distance <= FartherCoverCond.DistanceMax && rsrp >= FartherCoverCond.RsrpThreshold)
            {
                FartherCoverInfo info;
                if (!nameFartherMap.TryGetValue(lteCell.Name, out info))
                {
                    info = new FartherCoverInfo(lteCell);
                    nameFartherMap.Add(lteCell.Name, info);
                }
                info.DealTestPoint(tp, rsrp, distance);
            }
        }
        protected override void getResultsAfterQuery()
        {
            foreach (KeyValuePair<string, FartherCoverInfo> pair in nameFartherMap)
            {
                if (pair.Value.SampleNum >= FartherCoverCond.SampleNum)
                {
                    fartherVec.Add(pair.Value);
                    pair.Value.SN = fartherVec.Count;
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }
        protected override void fireShowForm()
        {
            if (fartherVec.Count <= 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            FartherCoverInfoForm frm = MainModel.CreateResultForm(typeof(FartherCoverInfoForm)) as FartherCoverInfoForm;
            frm.FillData(fartherVec);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }


        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Simple; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RsrpThreshold"] = FartherCoverCond.RsrpThreshold;
                param["SampleNum"] = FartherCoverCond.SampleNum;
                param["DistanceMax"] = FartherCoverCond.DistanceMax;
                param["DistanceMin"] = FartherCoverCond.DistanceMin;
                param["NBCellChecked"] = FartherCoverCond.NBCellChecked;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RsrpThreshold"))
                {
                    FartherCoverCond.RsrpThreshold = int.Parse(param["RsrpThreshold"].ToString());
                }
                if (param.ContainsKey("SampleNum"))
                {
                    FartherCoverCond.SampleNum = int.Parse(param["SampleNum"].ToString());
                }
                if (param.ContainsKey("DistanceMax"))
                {
                    FartherCoverCond.DistanceMax = int.Parse(param["DistanceMax"].ToString());
                }
                if (param.ContainsKey("DistanceMin"))
                {
                    FartherCoverCond.DistanceMin = int.Parse(param["DistanceMin"].ToString());
                }
                if (param.ContainsKey("NBCellChecked"))
                {
                    FartherCoverCond.NBCellChecked = (bool)param["NBCellChecked"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new FartherCoverProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (FartherCoverInfo item in nameFartherMap.Values)
            {
                BackgroundResult result = item.ConvertToBackgroundResult(curAnaFileInfo);
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            nameFartherMap.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            Dictionary<int, BackgroundResult> bgResultDic = new Dictionary<int, BackgroundResult>();
            foreach (BackgroundResult curBgResult in BackgroundResultList)
            {
                curBgResult.GetImageValueString(); //strCellName
                float distanceMax = curBgResult.GetImageValueFloat();
                float distanceMin = curBgResult.GetImageValueFloat();
                float distanceMean = curBgResult.GetImageValueFloat();

                BackgroundResult cellBgResult;
                Dictionary<string, float> imageDic;
                if (bgResultDic.TryGetValue(curBgResult.CI, out cellBgResult))
                {
                    imageDic = cellBgResult.ImageResultObj as Dictionary<string, float>;

                    cellBgResult.SampleCount += curBgResult.SampleCount;

                    cellBgResult.RxLevMax = Math.Max(cellBgResult.RxLevMax, curBgResult.RxLevMax);
                    cellBgResult.RxLevMin = Math.Min(cellBgResult.RxLevMin, curBgResult.RxLevMin);
                    imageDic["总场强"] += curBgResult.SampleCount * curBgResult.RxLevMean;

                    imageDic["最远距离"] = Math.Max(imageDic["最远距离"], distanceMax);
                    imageDic["最近距离"] = Math.Max(imageDic["最近距离"], distanceMin);
                    imageDic["总距离"] += curBgResult.SampleCount * distanceMean;
                }
                else
                {
                    imageDic = new Dictionary<string, float>();
                    imageDic["总场强"] = curBgResult.SampleCount * curBgResult.RxLevMean;
                    imageDic["最远距离"] = distanceMax;
                    imageDic["最近距离"] = distanceMin;
                    imageDic["总距离"] = curBgResult.SampleCount * distanceMean;
                    curBgResult.ImageResultObj = imageDic;

                    cellBgResult = curBgResult;
                    bgResultDic[curBgResult.CI] = cellBgResult;
                }
            }

            BackgroundResultList.Clear();
            foreach (BackgroundResult bgResult in bgResultDic.Values)
            {
                if (bgResult.SampleCount >= FartherCoverCond.SampleNum)
                {
                    Dictionary<string, float> imageDic = bgResult.ImageResultObj as Dictionary<string, float>;
                    bgResult.RxLevMean = (float)Math.Round(imageDic["总场强"] * 1.0 / bgResult.SampleCount, 2);
                    imageDic["平均距离"] = (float)Math.Round(imageDic["总距离"] * 1.0 / bgResult.SampleCount, 2);

                    StringBuilder sb = new StringBuilder();
                    sb.Append("最大场强：" + bgResult.RxLevMax + "\r\n");
                    sb.Append("最小场强：" + bgResult.RxLevMin + "\r\n");
                    sb.Append("平均场强：" + bgResult.RxLevMean + "\r\n");

                    sb.Append("最远距离：" + imageDic["最远距离"] + "\r\n");
                    sb.Append("最近距离：" + imageDic["最近距离"] + "\r\n");
                    sb.Append("平均距离：" + imageDic["平均距离"]);
                    bgResult.ImageDesc = sb.ToString();
                    BackgroundResultList.Add(bgResult);
                }
            }
        }
        #endregion
    }
}
