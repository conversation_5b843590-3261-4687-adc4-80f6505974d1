﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetQueryForm : BaseDialog
    {
        private ItemSelectionPanel projPanelDT;
        private ItemSelectionPanel projPanelScan;
        private ItemSelectionPanel servPanelDT;
        private ItemSelectionPanel servPanelScan;
        private QueryCondition CurCondition;
        private MapFormItemSelection ItemSelection;
        public bool isFirstTimeFireShowForm { get; set; } = true;
        Dictionary<TestPoint, tpGridposition> tpGridDicDT = null;
        Dictionary<TestPoint, tpGridposition> tpGridDicScan = null;
        List<markGrid> markGirds = null; 
        List<TestPoint> tpInconnectionDTList = null;
        List<TestPoint> tpInconnectionScanList = null;

        /// <summary>
        /// 路测数据最强小区的采样点数字典
        /// </summary>
        Dictionary<Cell, int> strongestCellDTDic = null;
        /// <summary>
        /// 扫频数据最强小区的采样点字典
        /// </summary>
        Dictionary<Cell, int> strongestCellScanDic = null;
        /// <summary>
        /// 路测数据的最强小区的电平字典
        /// </summary>
        Dictionary<Cell, int> strongestCellDTRxlevDic = null;
        /// <summary>
        /// 扫频数据的最强小区的电平字典
        /// </summary>
        Dictionary<Cell, int> strongestCellScanRxlevDic = null;
        /// <summary>
        /// 路测数据最强未知小区的采样点数字典
        /// </summary>
        Dictionary<string, int> strongestUnknownCellDTDic = null;
        /// <summary>
        /// 扫频数据最强未知小区的采样点数字典
        /// </summary>
        Dictionary<string, int> strongestUnknownCellScanDic = null;
        /// <summary>
        /// 路测数据的最强未知小区的电平字典
        /// </summary>
        Dictionary<string, int> strongestUnknownCellDTRxlevDic = null;
        /// <summary>
        /// 扫频数据的最强未知小区的电平字典
        /// </summary>
        Dictionary<string, int> strongestUnknownCellScanRxlevDic = null;
        /// <summary>
        /// 小区下的路测采样点字典(包含已知名字和未知名字的小区)
        /// </summary>
        Dictionary<string, List<TestPoint>> strongestCellTestpointDTDic = null;
        /// <summary>
        /// 小区下的扫频采样点字典(包含已知名字和未知名字的小区)
        /// </summary>
        Dictionary<string, List<TestPoint>> strongestCellTestpointScanDic = null;

        public SetQueryForm(MainModel mModel, MapFormItemSelection itemSelection, QueryCondition condition)
        {
            InitializeComponent();

            mainModel = mModel;
            CurCondition = condition;
            ItemSelection = itemSelection;

            dateTimePickerBeginTimeDT.Value = DateTime.Now.AddDays(-1);
            dateTimePickerBeginTimeScan.Value = DateTime.Now.AddDays(-1);
            dateTimePickerEndTimeDT.Value = DateTime.Now;
            dateTimePickerEndTimeScan.Value = DateTime.Now;

            listViewProjectDT.Items.Clear();
            listViewProjectScan.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanelDT = new ItemSelectionPanel(toolStripDropDownProjectDT, listViewProjectDT, lbProjCountDT, itemSelection, "Project", true);
                projPanelScan = new ItemSelectionPanel(toolStripDropDownProjectScan, listViewProjectScan, lbProjCountScan, itemSelection, "Project", true);
                toolStripDropDownProjectDT.Items.Clear();
                toolStripDropDownProjectScan.Items.Clear();
                projPanelDT.FreshItems();
                projPanelScan.FreshItems();
                toolStripDropDownProjectDT.Items.Add(new ToolStripControlHost(projPanelDT));
                toolStripDropDownProjectScan.Items.Add(new ToolStripControlHost(projPanelScan));
            }

            listViewServiceDT.Items.Clear();
            listViewServiceScan.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelDT = new ItemSelectionPanel(toolStripDropDownServiceDT, listViewServiceDT, lbSvCountDT, itemSelection, "ServiceType", true);
                servPanelScan = new ItemSelectionPanel(toolStripDropDownServiceScan, listViewServiceScan, lbSvCountScan, itemSelection, "ServiceType", true);
                toolStripDropDownServiceDT.Items.Clear();
                toolStripDropDownServiceScan.Items.Clear();
                servPanelDT.FreshItems();
                servPanelScan.FreshItems();
                toolStripDropDownServiceDT.Items.Add(new ToolStripControlHost(servPanelDT));
                toolStripDropDownServiceScan.Items.Add(new ToolStripControlHost(servPanelScan));
            }

            setDefaultService();

            if (mainModel.ConditionRecorderCellComp != null && mainModel.ConditionRecorderCellComp.kind == conditionRecorder.Kind.CellComp)
            {
                conditionRecorder recorder = mainModel.ConditionRecorderCellComp;
                this.dateTimePickerBeginTimeDT.Value = recorder.dateTimePickerBeginTimeDTValue;
                this.dateTimePickerBeginTimeScan.Value = recorder.dateTimePickerBeginTimeScanValue;
                this.dateTimePickerEndTimeDT.Value = recorder.dateTimePickerEndTimeDTValue;
                this.dateTimePickerEndTimeScan.Value = recorder.dateTimePickerEndTimeScanValue;
                this.listViewProjectDT.Items.Clear();
                this.listViewProjectScan.Items.Clear();
                this.listViewServiceDT.Items.Clear();
                this.listViewServiceScan.Items.Clear();
                foreach (saveItem item in recorder.listViewProjectDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewProjectScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectScan.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceScan.Items.Add(lvi);
                }
                this.trackBarChannel.Value = recorder.trackBarChannelValue;
                this.trackBarChannel_Scroll(null, null);
            }
            mainModel.SetQueryForm = this;
        }

        private void setDefaultService()
        {
            ListViewItem lviDt = new ListViewItem();
            lviDt.Text = "GSM语音业务";
            lviDt.Tag = 1;
            this.listViewServiceDT.Items.Add(lviDt);

            ListViewItem lviScan = new ListViewItem();
            lviScan.Text = "扫频业务";
            lviScan.Tag = 12;
            this.listViewServiceScan.Items.Add(lviScan);
        }

        private void trackBarChannel_Scroll(object sender, EventArgs e)
        {
            if (trackBarChannel.Value==1)
            {
                label900.ForeColor = Color.Red;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value==2)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Red;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value==3)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Red;
            }
        }

        private void buttonProjDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjDT.Width, buttonProjDT.Height);
            toolStripDropDownProjectDT.Show(buttonProjDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjScan.Width, buttonProjScan.Height);
            toolStripDropDownProjectScan.Show(buttonProjScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServDT.Width, buttonServDT.Height);
            toolStripDropDownServiceDT.Show(buttonServDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServScan.Width, buttonServScan.Height);
            toolStripDropDownServiceScan.Show(buttonServScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private bool isValidCondition()
        {
            if (dateTimePickerBeginTimeDT.Value > dateTimePickerEndTimeDT.Value)
            {
                MessageBox.Show("查询路测数据，结束时间必须大于开始时间。");
            }
            if (dateTimePickerBeginTimeScan.Value > dateTimePickerEndTimeScan.Value)
            {
                MessageBox.Show("查询扫频数据，结束时间必须大于开始时间。");
            }
            else if (listViewProjectDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个项目。");
            }
            else if (listViewServiceDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个业务。");
            }
            else if (listViewProjectScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个项目。");
            }
            else if (listViewServiceScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个业务。");
            }
            else
                return true;
            return false;
        }

        private void buttonQuery_Click(object sender, EventArgs e)
        {
            conditionRecorder recorder = new conditionRecorder();
            recorder.fill(conditionRecorder.Kind.CellComp, this.dateTimePickerBeginTimeDT.Value, this.dateTimePickerBeginTimeScan.Value, this.dateTimePickerEndTimeDT.Value
                , this.dateTimePickerEndTimeScan.Value, this.trackBarChannel.Value);
            recorder.fillCollection(this.listViewProjectDT.Items, this.listViewProjectScan.Items, this.listViewServiceDT.Items, this.listViewServiceScan.Items);
            mainModel.ConditionRecorderCellComp = recorder;

            tpGridDicDT = queryDTData();
            if (tpGridDicDT==null)
                return;
            if (tpGridDicDT.Count == 0)
                return;
            tpGridDicScan = queryScanData();
            if (tpGridDicScan==null)
                return;
            if (tpGridDicScan.Count == 0)
                return;
           
            mainModel.SetQueryForm = this;
            mainModel.SetQueryForm.Visible = false;
            WaitBox.Show(compareTpGridDicDTToTpGridDicScan);//保留DT和Scan都在同一栅格内的采样点集合

            WaitBox.Show(queryStrongestCell);//通过有效采样点在当前小区和邻区小区中比较，查询出采样点的最强信号小区集合
            
            fireShowForm();

            clearTempObjects();
        }

        /// <summary>
        /// 清除之前用于计算的对象，减少其对内存的占用
        /// </summary>
        private void clearTempObjects()
        {
            this.tpInconnectionDTList.Clear();
            this.tpInconnectionScanList.Clear();
            this.markGirds.Clear();
            this.Dispose();
            //GC.Collect();
        }

        public void compareTpGridDicDTToTpGridDicScan()
        {
            try
            {
                WaitBox.Text = "正在调整频段……";
                WaitBox.ProgressPercent = 30;

                markGirds = new List<markGrid>();
                tpInconnectionDTList = new List<TestPoint>();
                tpInconnectionScanList = new List<TestPoint>();

                List<TestPoint> curBandTestpointDT = new List<TestPoint>();
                List<TestPoint> curBandTestpointScan = new List<TestPoint>();
                addCurBandTestpointDT(curBandTestpointDT);
                addCurBandTestpointScan(curBandTestpointScan);

                WaitBox.ProgressPercent = 50;

                markGrid markGrid = null;
                bool needAdd = false;
                addTP(curBandTestpointDT, ref markGrid, ref needAdd);

                addScanTP(curBandTestpointScan, ref markGrid, ref needAdd);

                foreach (markGrid mg in markGirds)
                {
                    tpInconnectionDTList.AddRange(mg.testPointDtList);
                    tpInconnectionScanList.AddRange(mg.testPointScanList);
                }

                WaitBox.ProgressPercent = 90;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void addCurBandTestpointDT(List<TestPoint> curBandTestpointDT)
        {
            foreach (TestPoint tpDT in tpGridDicDT.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointDT.Add(tpDT);
                }
            }
        }

        private void addCurBandTestpointScan(List<TestPoint> curBandTestpointScan)
        {
            foreach (TestPoint tpScan in tpGridDicScan.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointScan.Add(tpScan);
                }
            }
        }

        private void addTP(List<TestPoint> curBandTestpointDT, ref markGrid markGrid, ref bool needAdd)
        {
            foreach (TestPoint tpDT in curBandTestpointDT)
            {
                tpGridposition posDT = tpGridDicDT[tpDT];
                if (markGirds.Count == 0)
                {
                    markGrid = new markGrid(posDT.row, posDT.col);
                    markGrid.hasDtSample = true;
                    markGrid.testPointDtList.Add(tpDT);
                    markGirds.Add(markGrid);
                }
                else
                {
                    dealTPMarkGirds(ref markGrid, ref needAdd, tpDT, posDT);
                }
            }
        }

        private void dealTPMarkGirds(ref markGrid markGrid, ref bool needAdd, TestPoint tpDT, tpGridposition posDT)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posDT.row && mg.col == posDT.col)
                {
                    mg.testPointDtList.Add(tpDT);  //往栅格加入该采样点
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        needAdd = true;
                        markGrid = new markGrid(posDT.row, posDT.col);
                        markGrid.testPointDtList.Add(tpDT);
                        markGrid.hasDtSample = true;
                    }
                }
            }
            if (needAdd)    //将含有路测采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private void addScanTP(List<TestPoint> curBandTestpointScan, ref markGrid markGrid, ref bool needAdd)
        {
            foreach (TestPoint tpScan in curBandTestpointScan)
            {
                tpGridposition posScan = tpGridDicScan[tpScan];
                dealScanTPMarkGirds(ref markGrid, ref needAdd, tpScan, posScan);
            }
        }

        private void dealScanTPMarkGirds(ref markGrid markGrid, ref bool needAdd, TestPoint tpScan, tpGridposition posScan)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posScan.row && mg.col == posScan.col)
                {
                    mg.testPointScanList.Add(tpScan);  //往栅格加入该采样点
                    mg.hasScanSample = true;
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        markGrid = new markGrid(posScan.row, posScan.col);
                        markGrid.hasScanSample = true;
                        markGrid.testPointScanList.Add(tpScan);
                        needAdd = true;
                    }
                }
            }
            if (needAdd)    //将含有扫频采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private Dictionary<TestPoint,tpGridposition> queryDTData()
        {
            if (isValidCondition())
            {
                DIYSampleToGridQueryByRegion sampleToGridQueryForDT = new DIYSampleToGridQueryByRegion(mainModel);
                CurCondition.Periods.Clear();
                CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeDT.Value.Date, dateTimePickerEndTimeDT.Value.Date.AddDays(1).AddMilliseconds(-1)));
                CurCondition.Projects.Clear();
                CurCondition.ServiceTypes.Clear();
                foreach (ListViewItem item in listViewProjectDT.Items)
                {
                    CurCondition.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in listViewServiceDT.Items)
                {
                    CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
                }
                CurCondition.DistrictIDs.Clear();
                CurCondition.DistrictIDs.Add(mainModel.DistrictID);
                sampleToGridQueryForDT.CurCondition = CurCondition;
                sampleToGridQueryForDT.Query();

                return sampleToGridQueryForDT.tpGridDic;
            }
            return new Dictionary<TestPoint, tpGridposition>();
        }

        private Dictionary<TestPoint, tpGridposition> queryScanData()
        {
            DIYSampleToGridQueryByRegion sampleToGridQueryForScan = new DIYSampleToGridQueryByRegion(mainModel);
            CurCondition.Periods.Clear();
            CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeScan.Value.Date, dateTimePickerEndTimeScan.Value.Date.AddDays(1).AddMilliseconds(-1)));
            CurCondition.Projects.Clear();
            CurCondition.ServiceTypes.Clear();
            foreach (ListViewItem item in listViewProjectScan.Items)
            {
                CurCondition.Projects.Add((byte)(int)item.Tag);
            }
            foreach (ListViewItem item in listViewServiceScan.Items)
            {
                CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
            }
            CurCondition.DistrictIDs.Clear();
            CurCondition.DistrictIDs.Add(mainModel.DistrictID);
            sampleToGridQueryForScan.CurCondition = CurCondition;
            sampleToGridQueryForScan.Query();

            return sampleToGridQueryForScan.tpGridDic;
        }

        public void queryStrongestCell()
        {
            try
            {
                WaitBox.Text = "正在分析路测数据与扫频数据关联的最强小区……";
                WaitBox.ProgressPercent = 30;
                Init();

                getStrongestCell();

                WaitBox.ProgressPercent = 70;

                getScanStrongestCell();

                WaitBox.ProgressPercent = 90;
                WaitBox.Text = "显示小区占用对比结果……";
                System.Threading.Thread.Sleep(1000);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void Init()
        {
            strongestCellDTDic = new Dictionary<Cell, int>();
            strongestCellScanDic = new Dictionary<Cell, int>();
            strongestUnknownCellDTDic = new Dictionary<string, int>();
            strongestUnknownCellScanDic = new Dictionary<string, int>();

            strongestCellDTRxlevDic = new Dictionary<Cell, int>();
            strongestCellScanRxlevDic = new Dictionary<Cell, int>();
            strongestUnknownCellDTRxlevDic = new Dictionary<string, int>();
            strongestUnknownCellScanRxlevDic = new Dictionary<string, int>();
            strongestCellTestpointDTDic = new Dictionary<string, List<TestPoint>>();
            strongestCellTestpointScanDic = new Dictionary<string, List<TestPoint>>();
        }

        private void getStrongestCell()
        {
            foreach (TestPoint testpoint in tpInconnectionDTList)  //求路测的最强小区集合
            {
                Cell curCellDT = null;
                string curUnknownCellLacCi = null;

                float? rxLevSub = (float?)(short?)testpoint["RxLevSub"];
                getCurCell(testpoint, ref curCellDT, ref curUnknownCellLacCi);

                Cell strongestNbCell = null;
                int strongestNbCellRxlev = -120;
                getStrongestRxlev(testpoint, ref strongestNbCell, ref strongestNbCellRxlev);

                if (rxLevSub != null)
                {
                    addStrongestCell(testpoint, curCellDT, curUnknownCellLacCi, rxLevSub, strongestNbCell, strongestNbCellRxlev);
                }

            }
        }

        private void addStrongestCell(TestPoint testpoint, Cell curCellDT, string curUnknownCellLacCi, float? rxLevSub, Cell strongestNbCell, int strongestNbCellRxlev)
        {
            addCurCellAsStrongestCell(testpoint, curCellDT, curUnknownCellLacCi, rxLevSub, strongestNbCell, strongestNbCellRxlev);
            addNbCellAsStrongestCell(testpoint, curCellDT, rxLevSub, strongestNbCell);
        }

        private void addNbCellAsStrongestCell(TestPoint testpoint, Cell curCellDT, float? rxLevSub, Cell strongestNbCell)
        {
            if (strongestNbCell != null)  //若采样点的邻区的最强信号大于当前小区信号，则判断这邻区为最强信号小区
            {
                if (!strongestCellDTDic.ContainsKey(curCellDT))
                {
                    strongestCellDTDic.Add(curCellDT, 1);
                    strongestCellDTRxlevDic.Add(curCellDT, (int)rxLevSub);

                    List<TestPoint> tpList = new List<TestPoint>();
                    tpList.Add(testpoint);
                    strongestCellTestpointDTDic.Add(curCellDT.LAC + "_" + curCellDT.CI, tpList);
                }
                else
                {
                    strongestCellDTDic[curCellDT] += 1;
                    strongestCellDTRxlevDic[curCellDT] += (int)rxLevSub;

                    strongestCellTestpointDTDic[curCellDT.LAC + "_" + curCellDT.CI].Add(testpoint);
                }
            }
        }

        private void addCurCellAsStrongestCell(TestPoint testpoint, Cell curCellDT, string curUnknownCellLacCi, float? rxLevSub, Cell strongestNbCell, int strongestNbCellRxlev)
        {
            if (rxLevSub >= strongestNbCellRxlev || strongestNbCell == null)    //若采样点当前的小区信号大于邻区的最强信号，或不存在邻区，则判断当前小区为最强信号小区
            {
                if (curCellDT != null)
                {
                    if (!strongestCellDTDic.ContainsKey(curCellDT))
                    {
                        strongestCellDTDic.Add(curCellDT, 1);
                        strongestCellDTRxlevDic.Add(curCellDT, (int)rxLevSub);

                        List<TestPoint> tpList = new List<TestPoint>();
                        tpList.Add(testpoint);
                        strongestCellTestpointDTDic.Add(curCellDT.LAC + "_" + curCellDT.CI, tpList);
                    }
                    else
                    {
                        strongestCellDTDic[curCellDT] += 1;
                        strongestCellDTRxlevDic[curCellDT] += (int)rxLevSub;

                        strongestCellTestpointDTDic[curCellDT.LAC + "_" + curCellDT.CI].Add(testpoint);
                    }
                }
                else if (curUnknownCellLacCi != null)
                {
                    if (!strongestUnknownCellDTDic.ContainsKey(curUnknownCellLacCi))
                    {
                        strongestUnknownCellDTDic.Add(curUnknownCellLacCi, 1);
                        strongestUnknownCellDTRxlevDic.Add(curUnknownCellLacCi, (int)rxLevSub);

                        List<TestPoint> tpList = new List<TestPoint>();
                        tpList.Add(testpoint);
                        strongestCellTestpointDTDic.Add(curUnknownCellLacCi, tpList);
                    }
                    else
                    {
                        strongestUnknownCellDTDic[curUnknownCellLacCi] += 1;
                        strongestUnknownCellDTRxlevDic[curUnknownCellLacCi] += (int)rxLevSub;

                        strongestCellTestpointDTDic[curUnknownCellLacCi].Add(testpoint);
                    }
                }
            }
        }

        private void getStrongestRxlev(TestPoint testpoint, ref Cell strongestNbCell, ref int strongestNbCellRxlev)
        {
            for (int i = 0; i < 6; i++)    //求最强信号的邻区
            {
                short? nbcch = (short?)testpoint["N_BCCH", i];
                byte? nbsic = (byte?)testpoint["N_BSIC", i];
                int? nRxLev = (int?)(short?)testpoint["N_RxLev", i];

                if (nbcch != null && nbsic != null && nRxLev != null)
                {
                    strongestNbCell = CellManager.GetInstance().GetNearestCell(testpoint.DateTime, (short)nbcch, (byte)nbsic, testpoint.Longitude, testpoint.Latitude);
                    bool isGet = getStrongestNbCellRxlev(testpoint, strongestNbCell, ref strongestNbCellRxlev, nRxLev);
                    if (isGet)
                    {
                        break;
                    }
                }
            }
        }

        private bool getStrongestNbCellRxlev(TestPoint testpoint, Cell strongestNbCell, ref int strongestNbCellRxlev, int? nRxLev)
        {
            if (strongestNbCell != null)
            {
                if (MainModel.GetInstance().SystemConfigInfo.distLimit)//距离限制设置
                {
                    if (strongestNbCell.GetDistance(testpoint.Longitude, testpoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                    {
                        strongestNbCellRxlev = (int)nRxLev;
                        return true;
                    }
                }
                else
                {
                    strongestNbCellRxlev = (int)nRxLev;
                    return true;
                }
            }

            return false;
        }

        private static void getCurCell(TestPoint testpoint, ref Cell curCellDT, ref string curUnknownCellLacCi)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(testpoint.DateTime, (ushort?)(int?)testpoint["LAC"], (ushort?)(int?)testpoint["CI"], (short?)testpoint["BCCH"], (byte?)testpoint["BSIC"], testpoint.Longitude, testpoint.Latitude);
            if (cell != null)
            {
                if (MainModel.GetInstance().SystemConfigInfo.distLimit)//距离限制设置
                {
                    if (cell.GetDistance(testpoint.Longitude, testpoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                    {
                        curCellDT = cell;
                    }
                }
                else
                {
                    curCellDT = cell;
                }
            }
            else  //未知名字的小区
            {
                curUnknownCellLacCi = ((ushort?)(int?)testpoint["LAC"]).ToString() + "_" + ((ushort?)(int?)testpoint["CI"]).ToString() + "/" + ((short?)testpoint["BCCH"]).ToString() + "_" + ((byte?)testpoint["BSIC"]).ToString();
            }
        }

        private void getScanStrongestCell()
        {
            foreach (TestPoint testpoint in tpInconnectionScanList)  //求路测的最强小区集合
            {
                Cell curCellScan = null;
                string curUnknownCellScan = null;

                float? rxLevSub = (float?)testpoint["GSCAN_RxLev", 0];
                if (rxLevSub != null)
                {
                    short? bcch = (short?)(int?)testpoint["GSCAN_BCCH", 0];
                    byte? bsic = (byte?)(int?)testpoint["GSCAN_BSIC", 0];
                    if (bcch != null && bsic != null)
                    {
                        addScanStrongestCell(testpoint, ref curCellScan, ref curUnknownCellScan, rxLevSub, bcch, bsic);
                    }
                }
            }
        }

        private void addScanStrongestCell(TestPoint testpoint, ref Cell curCellScan, ref string curUnknownCellScan, float? rxLevSub, short? bcch, byte? bsic)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(testpoint.DateTime, (short)bcch, (byte)bsic, testpoint.Longitude, testpoint.Latitude);
            if (cell != null)
            {
                curCellScan = cell;

                if (!strongestCellScanDic.ContainsKey(curCellScan))
                {
                    strongestCellScanDic.Add(curCellScan, 1);
                    strongestCellScanRxlevDic.Add(curCellScan, (int)rxLevSub);

                    List<TestPoint> tpList = new List<TestPoint>();
                    tpList.Add(testpoint);
                    strongestCellTestpointScanDic.Add(curCellScan.LAC + "_" + curCellScan.CI, tpList);
                }
                else
                {
                    strongestCellScanDic[curCellScan] += 1;
                    strongestCellScanRxlevDic[curCellScan] += (int)rxLevSub;

                    strongestCellTestpointScanDic[curCellScan.LAC + "_" + curCellScan.CI].Add(testpoint);
                }
            }
            else  //未知名字的小区
            {
                curUnknownCellScan = bcch.ToString() + "_" + bsic.ToString();
                if (!strongestUnknownCellScanDic.ContainsKey(curUnknownCellScan))
                {
                    strongestUnknownCellScanDic.Add(curUnknownCellScan, 1);
                    strongestUnknownCellScanRxlevDic.Add(curUnknownCellScan, (int)rxLevSub);

                    List<TestPoint> tpList = new List<TestPoint>();
                    tpList.Add(testpoint);
                    strongestCellTestpointScanDic.Add(curUnknownCellScan, tpList);
                }
                else
                {
                    strongestUnknownCellScanDic[curUnknownCellScan] += 1;
                    strongestUnknownCellScanRxlevDic[curUnknownCellScan] += (int)rxLevSub;

                    strongestCellTestpointScanDic[curUnknownCellScan].Add(testpoint);
                }
            }
        }

        public void fireShowForm()
        {
            List<cellComparisonInfo> cellComparisonInfoList = new List<cellComparisonInfo>();
            List<Cell> cellOnlyInDT = new List<Cell>();
            List<Cell> cellOnlyInScan = new List<Cell>();
            List<string> unknownCellOnlyInDt = new List<string>();
            List<string> unknownCellOnlyInScan = new List<string>();

            int sumDt = 0; //统计路测的小区次数
            int sumScan = 0; //统计扫频的小区次数
            getSum(ref sumDt, ref sumScan);

            addCellInfo(cellComparisonInfoList, sumDt, sumScan);

            addUnknownCellInfo(cellComparisonInfoList, sumDt, sumScan);

            addStrongestCellDT(cellOnlyInDT);
            addStrongestCellScan(cellOnlyInScan);

            addUnknownCellDt(unknownCellOnlyInDt);
            addUnknownCellScan(unknownCellOnlyInScan);

            addCellDtInfo(cellComparisonInfoList, cellOnlyInDT, sumDt);
            addUnknownCellDtInfo(cellComparisonInfoList, unknownCellOnlyInDt, sumDt);
            addCellScanInfo(cellComparisonInfoList, cellOnlyInScan, sumScan);
            addUnknownCellScanInfo(cellComparisonInfoList, unknownCellOnlyInScan, sumScan);

            mainModel.CellComparisonInfoList = cellComparisonInfoList;
            if (isFirstTimeFireShowForm) //在结果窗口改变频段，重算小区则不需要再新建结果窗口
            {
                fireShowFormResult();
            }
        }

        private void getSum(ref int sumDt, ref int sumScan)
        {
            foreach (Cell cellDt in strongestCellDTDic.Keys)
            {
                sumDt += strongestCellDTDic[cellDt];
            }
            foreach (string unknownCellDt in strongestUnknownCellDTDic.Keys)
            {
                sumDt += strongestUnknownCellDTDic[unknownCellDt];
            }
            foreach (Cell cellScan in strongestCellScanDic.Keys)
            {
                sumScan += strongestCellScanDic[cellScan];
            }
            foreach (string unknownCellScan in strongestUnknownCellScanDic.Keys)
            {
                sumScan += strongestUnknownCellScanDic[unknownCellScan];
            }
        }

        private void addCellInfo(List<cellComparisonInfo> cellComparisonInfoList, int sumDt, int sumScan)
        {
            foreach (Cell cellDt in strongestCellDTDic.Keys)
            {
                foreach (Cell cellScan in strongestCellScanDic.Keys)
                {
                    if (cellDt == cellScan)
                    {
                        cellComparisonInfo info = new cellComparisonInfo();
                        info.cellName = cellDt.Name;
                        info.lacCi = cellDt.LAC.ToString() + "_" + cellDt.CI.ToString();
                        info.bcchBsic = cellDt.BCCH.ToString() + "_" + cellDt.BSIC.ToString();
                        info.sampleDtCount = strongestCellDTDic[cellDt];
                        info.sampleScanCount = strongestCellScanDic[cellScan];
                        info.avgRxlevDt = strongestCellDTRxlevDic[cellDt] / strongestCellDTDic[cellDt];
                        info.avgRxlevScan = strongestCellScanRxlevDic[cellScan] / strongestCellScanDic[cellScan];
                        info.dtPct = Math.Round(100 * ((double)strongestCellDTDic[cellDt] / (double)sumDt), 2);
                        info.scanPct = Math.Round(100 * ((double)strongestCellScanDic[cellScan] / (double)sumScan), 2);
                        info.differPct = Math.Round(100 * ((double)strongestCellScanDic[cellScan] / (double)sumScan) - 100 * ((double)strongestCellDTDic[cellDt] / (double)sumDt), 2);

                        info.dtTestpointList.AddRange(strongestCellTestpointDTDic[cellDt.LAC + "_" + cellDt.CI]);
                        info.scanTestpointList.AddRange(strongestCellTestpointScanDic[cellScan.LAC + "_" + cellScan.CI]);

                        cellComparisonInfoList.Add(info);
                    }
                }
            }
        }

        private void addUnknownCellInfo(List<cellComparisonInfo> cellComparisonInfoList, int sumDt, int sumScan)
        {
            foreach (string unknownCellDt in strongestUnknownCellDTDic.Keys)
            {
                int index = unknownCellDt.IndexOf("/");
                string unknownCellDtBcchBsic = unknownCellDt.Substring(index + 1);
                foreach (string unknownCellScan in strongestUnknownCellScanDic.Keys)
                {
                    if (unknownCellDtBcchBsic == unknownCellScan)
                    {
                        cellComparisonInfo info = new cellComparisonInfo();
                        int i = unknownCellDt.IndexOf("/");
                        string lacCi = unknownCellDt.Substring(0, i);
                        info.lacCi = lacCi;
                        info.bcchBsic = unknownCellDt.Substring(i + 1);
                        info.sampleDtCount = strongestUnknownCellDTDic[unknownCellDt];
                        info.sampleScanCount = strongestUnknownCellScanDic[unknownCellScan];
                        info.avgRxlevDt = strongestUnknownCellDTRxlevDic[unknownCellDt] / strongestUnknownCellDTDic[unknownCellDt];
                        info.avgRxlevScan = strongestUnknownCellScanRxlevDic[unknownCellScan] / strongestUnknownCellScanDic[unknownCellScan];
                        info.dtPct = Math.Round(100 * (double)strongestUnknownCellDTDic[unknownCellDt] / (double)sumDt, 2);
                        info.scanPct = Math.Round(100 * (double)strongestUnknownCellScanDic[unknownCellScan] / (double)sumScan, 2);
                        info.differPct = Math.Round(100 * ((double)strongestUnknownCellScanDic[unknownCellScan] / (double)sumScan) - 100 * ((double)strongestUnknownCellDTDic[unknownCellDt] / (double)sumDt), 2);

                        info.dtTestpointList.AddRange(strongestCellTestpointDTDic[unknownCellDt]);
                        info.scanTestpointList.AddRange(strongestCellTestpointScanDic[unknownCellScan]);

                        cellComparisonInfoList.Add(info);
                    }
                }
            }
        }

        private void addStrongestCellDT(List<Cell> cellOnlyInDT)
        {
            foreach (Cell cellDt in strongestCellDTDic.Keys)
            {
                int numScan = 1;
                foreach (Cell cellScan in strongestCellScanDic.Keys)
                {
                    if (cellDt == cellScan)
                    {
                        break;
                    }
                    if (numScan++ == strongestCellScanDic.Keys.Count)
                    {
                        cellOnlyInDT.Add(cellDt);
                    }
                }
            }
        }

        private void addStrongestCellScan(List<Cell> cellOnlyInScan)
        {
            foreach (Cell cellScan in strongestCellScanDic.Keys)
            {
                int numDT = 1;
                foreach (Cell cellDT in strongestCellDTDic.Keys)
                {
                    if (cellDT == cellScan)
                    {
                        break;
                    }
                    if (numDT++ == strongestCellDTDic.Keys.Count)
                    {
                        cellOnlyInScan.Add(cellScan);
                    }
                }
            }
        }

        private void addUnknownCellDt(List<string> unknownCellOnlyInDt)
        {
            foreach (string unknownCellDt in strongestUnknownCellDTDic.Keys)
            {
                int numScan = 1;
                int index = unknownCellDt.IndexOf("/");
                string unknownCellDtBcchBsic = unknownCellDt.Substring(index + 1);
                foreach (string unknownCellScan in strongestUnknownCellScanDic.Keys)
                {
                    if (unknownCellDtBcchBsic == unknownCellScan)
                    {
                        break;
                    }
                    if (numScan++ == strongestUnknownCellScanDic.Keys.Count)
                    {
                        unknownCellOnlyInDt.Add(unknownCellDt);
                    }
                }
            }
        }

        private void addUnknownCellScan(List<string> unknownCellOnlyInScan)
        {
            foreach (string unknownCellScan in strongestUnknownCellScanDic.Keys)
            {
                int numDt = 1;
                foreach (string unknownCellDt in strongestUnknownCellDTDic.Keys)
                {
                    int index = unknownCellDt.IndexOf("/");
                    string unknownCellDtBcchBsic = unknownCellDt.Substring(index + 1);
                    if (unknownCellDtBcchBsic == unknownCellScan)
                    {
                        break;
                    }
                    if (numDt++ == strongestUnknownCellDTDic.Keys.Count)
                    {
                        unknownCellOnlyInScan.Add(unknownCellScan);
                    }
                }
            }
        }

        private void addCellDtInfo(List<cellComparisonInfo> cellComparisonInfoList, List<Cell> cellOnlyInDT, int sumDt)
        {
            foreach (Cell cellDt in cellOnlyInDT)
            {
                cellComparisonInfo info = new cellComparisonInfo();
                info.cellName = cellDt.Name;
                info.lacCi = cellDt.LAC.ToString() + "_" + cellDt.CI.ToString();
                info.bcchBsic = cellDt.BCCH.ToString() + "_" + cellDt.BSIC.ToString();
                info.sampleDtCount = strongestCellDTDic[cellDt];
                info.sampleScanCount = 0;
                info.avgRxlevDt = strongestCellDTRxlevDic[cellDt] / strongestCellDTDic[cellDt];
                info.dtPct = Math.Round(100 * (double)strongestCellDTDic[cellDt] / (double)sumDt, 2);
                info.scanPct = 0;
                info.differPct = -info.dtPct;

                info.dtTestpointList.AddRange(strongestCellTestpointDTDic[cellDt.LAC + "_" + cellDt.CI]);

                cellComparisonInfoList.Add(info);
            }
        }

        private void addUnknownCellDtInfo(List<cellComparisonInfo> cellComparisonInfoList, List<string> unknownCellOnlyInDt, int sumDt)
        {
            foreach (string unknownCellDt in unknownCellOnlyInDt)
            {
                cellComparisonInfo info = new cellComparisonInfo();
                int i = unknownCellDt.IndexOf("/");
                info.lacCi = unknownCellDt.Substring(0, i);
                string bcchBsic = unknownCellDt.Substring(i + 1);
                info.bcchBsic = bcchBsic;
                info.sampleDtCount = strongestUnknownCellDTDic[unknownCellDt];
                info.sampleScanCount = 0;
                info.avgRxlevDt = strongestUnknownCellDTRxlevDic[unknownCellDt] / strongestUnknownCellDTDic[unknownCellDt];
                info.dtPct = Math.Round(100 * (double)strongestUnknownCellDTDic[unknownCellDt] / (double)sumDt, 2);
                info.scanPct = 0;
                info.differPct = -info.dtPct;

                info.dtTestpointList.AddRange(strongestCellTestpointDTDic[unknownCellDt]);

                cellComparisonInfoList.Add(info);
            }
        }

        private void addCellScanInfo(List<cellComparisonInfo> cellComparisonInfoList, List<Cell> cellOnlyInScan, int sumScan)
        {
            foreach (Cell cellScan in cellOnlyInScan)
            {
                cellComparisonInfo info = new cellComparisonInfo();
                info.cellName = cellScan.Name;
                info.lacCi = cellScan.LAC.ToString() + "_" + cellScan.CI.ToString();
                info.bcchBsic = cellScan.BCCH.ToString() + "_" + cellScan.BSIC.ToString();
                info.sampleDtCount = 0;
                info.sampleScanCount = strongestCellScanDic[cellScan];
                info.avgRxlevScan = strongestCellScanRxlevDic[cellScan] / strongestCellScanDic[cellScan];
                info.dtPct = 0;
                info.scanPct = Math.Round(100 * (double)strongestCellScanDic[cellScan] / (double)sumScan, 2);
                info.differPct = info.scanPct;

                info.scanTestpointList.AddRange(strongestCellTestpointScanDic[cellScan.LAC + "_" + cellScan.CI]);

                cellComparisonInfoList.Add(info);
            }
        }

        private void addUnknownCellScanInfo(List<cellComparisonInfo> cellComparisonInfoList, List<string> unknownCellOnlyInScan, int sumScan)
        {
            foreach (string unknownCellScan in unknownCellOnlyInScan)
            {
                cellComparisonInfo info = new cellComparisonInfo();
                info.lacCi = "";
                info.bcchBsic = unknownCellScan;
                info.sampleDtCount = 0;
                info.sampleScanCount = strongestUnknownCellScanDic[unknownCellScan];
                info.avgRxlevScan = strongestUnknownCellScanRxlevDic[unknownCellScan] / strongestUnknownCellScanDic[unknownCellScan];
                info.dtPct = 0;
                info.scanPct = Math.Round(100 * (double)strongestUnknownCellScanDic[unknownCellScan] / (double)sumScan, 2);
                info.differPct = info.scanPct;

                info.scanTestpointList.AddRange(strongestCellTestpointScanDic[unknownCellScan]);

                cellComparisonInfoList.Add(info);
            }
        }

        protected void fireShowFormResult()
        {
            CellComparisonForm frm = MainModel.GetInstance().CreateResultForm(typeof(CellComparisonForm)) as CellComparisonForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class DIYSampleToGridQueryByRegion : DIYQueryFileInfoByRegion
    {
        public DIYSampleToGridQueryByRegion(MainModel mainModel)
            : base(mainModel)
        { 
        }

        public List<int> projectIds { get; set; }
        public List<int> serviceIds { get; set; }
        public QueryCondition CurCondition { get; set; }
        GridMatrix<ColorUnit> grids { get; set; }
        public DbRect bounds { get; set; }

        public double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG;//当前运算中使用的值
        public double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT;//当前运算中使用的值

        /// <summary>
        /// 按采样点保存其对应的栅格，为随后路测数据和扫频数据的匹配上，找出共同的使用中的栅格
        /// </summary>
        public Dictionary<TestPoint, tpGridposition> tpGridDic { get; set; }

        protected override void query()
        {
            MapGridLayer gridShowLayer = this.MainModel.MainForm.GetMapForm().GetGridShowLayer();
            GRID_SPAN_LONG = gridShowLayer.GRID_SPAN_LONG;
            GRID_SPAN_LAT = gridShowLayer.GRID_SPAN_LAT;

            getStatGrid();
            tpGridDic = new Dictionary<TestPoint, tpGridposition>();

            MainModel.ClearDTData();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            base.SetQueryCondition(CurCondition);
            base.query();

            WaitBox.CanCancel = true;
            WaitBox.Show(analyseFiles);
            
            MainModel.ClearDTData();//清空文件，采样点等占用内存的对象
        }

        private void analyseFiles()
        {
            try
            {
                int iloop = 0;
                if (MainModel.FileInfos.Count == 0)
                {
                    MessageBox.Show("查询不到合条件的数据。");
                    return;
                }
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    WaitBox.Text = "正在分析文件的数据";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);

                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
             
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = true;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();

            doStat();
        }

        /// <summary>
        /// 查询栅格数据
        /// </summary>
        private void getStatGrid()
       {
            DIYQueryCoverGridByRegion GridQuery = new DIYQueryCoverGridByRegion(MainModel);
            GridQuery.SetQueryCondition(CurCondition);
            GridQuery.Query();
            grids = MainModel.CurGridColorUnitMatrix;
        }
       
        private void doStat()
        {
            tpGridposition position=null;
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;

                    foreach (TestPoint testpoint in testPointList)
                    {
                        if (isValidTestPoint(testpoint))
                        {
                            int pGridUnitRow, pGridUnitCol;
                            GridHelper.GetIndexOfDefaultSizeGrid(testpoint.Longitude, testpoint.Latitude, out pGridUnitRow, out pGridUnitCol);
                            ColorUnit cu = grids[pGridUnitRow, pGridUnitCol];
                            position=new tpGridposition(pGridUnitRow,pGridUnitCol,cu);
                            tpGridDic.Add(testpoint,position);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        protected virtual bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }
    }
   
    public class tpGridposition
    {
        public tpGridposition(int row,int col,ColorUnit colorUnit)
        {
            this.row=row;
            this.col=col;
            this.cu=colorUnit;
        }
        public int row { get; set; }//采样点所在区域栅格的行号
        public int col { get; set; }//采样点所在区域栅格的列号
        public ColorUnit cu { get; set; }
    }

    public class markGrid
    {
        public markGrid(int row,int col)
        {
            this.row=row;
            this.col=col;
        }
        public int row { get; set; }
        public int col { get; set; }
        public bool hasDtSample { get; set; } = false;
        public bool hasScanSample { get; set; } = false;

        public List<TestPoint> testPointDtList { get; set; } = new List<TestPoint>();
        public List<TestPoint> testPointScanList { get; set; } = new List<TestPoint>();
    }

    public class cellComparisonInfo
    {
        public string cellName { get; set; }
        public string lacCi { get; set; }
        public string bcchBsic { get; set; }
        public int sampleDtCount { get; set; }
        public int sampleScanCount { get; set; }
        public int avgRxlevDt { get; set; }
        public int avgRxlevScan { get; set; }
        public double scanPct { get; set; }
        public double dtPct { get; set; }
        public double differPct { get; set; }

        public List<TestPoint> dtTestpointList { get; set; } = new List<TestPoint>();
        public List<TestPoint> scanTestpointList { get; set; } = new List<TestPoint>();
    }

    public class conditionRecorder
    {
        public DateTime dateTimePickerBeginTimeDTValue { get; set; }
        public DateTime dateTimePickerBeginTimeScanValue { get; set; }
        public DateTime dateTimePickerEndTimeDTValue { get; set; }
        public DateTime dateTimePickerEndTimeScanValue { get; set; }
        public int trackBarChannelValue { get; set; }
        public int numUdMakeupRxlevValue { get; set; }
        public Kind kind { get; set; }
        public List<saveItem> listViewProjectDTItems { get; set; } = new List<saveItem>();
        public List<saveItem> listViewProjectScanItems { get; set; } = new List<saveItem>();
        public List<saveItem> listViewServiceDTItems { get; set; } = new List<saveItem>();
        public List<saveItem> listViewServiceScanItems { get; set; } = new List<saveItem>();
        public enum Kind
        {
            CellComp,
            RxlevComp,
            StrongCellDeficiencyByRegion,
            StrongCellDeficencyByCell
        }

        /// <summary>
        /// 保存操作条件
        /// </summary>
        /// <param name="conditionKind"></param>
        /// <param name="beginTimeDTValue"></param>
        /// <param name="beginTimeScanValue"></param>
        /// <param name="endTimeDTValue"></param>
        /// <param name="endTimeScanValue"></param>
        /// <param name="projectDTItems"></param>
        /// <param name="projectScanItems"></param>
        /// <param name="serviceDTItems"></param>
        /// <param name="serviceScanItems"></param>
        /// <param name="channelValue"></param>
        public void fill(Kind conditionKind, DateTime beginTimeDTValue, DateTime beginTimeScanValue, DateTime endTimeDTValue, DateTime endTimeScanValue
            , int channelValue)
        {
            kind = conditionKind;
            dateTimePickerBeginTimeDTValue = beginTimeDTValue;
            dateTimePickerBeginTimeScanValue = beginTimeScanValue;
            dateTimePickerEndTimeDTValue = endTimeDTValue;
            dateTimePickerEndTimeScanValue = endTimeScanValue;
            
            trackBarChannelValue = channelValue;
        }

        /// <summary>
        /// 保存操作条件
        /// </summary>
        /// <param name="conditionKind"></param>
        /// <param name="beginTimeDTValue"></param>
        /// <param name="beginTimeScanValue"></param>
        /// <param name="endTimeDTValue"></param>
        /// <param name="endTimeScanValue"></param>
        /// <param name="projectDTItems"></param>
        /// <param name="projectScanItems"></param>
        /// <param name="serviceDTItems"></param>
        /// <param name="serviceScanItems"></param>
        /// <param name="channelValue"></param>
        /// <param name="makeupRxlevValue"></param>
        public void fill(Kind conditionKind, DateTime beginTimeDTValue, DateTime beginTimeScanValue, DateTime endTimeDTValue, DateTime endTimeScanValue
            , int channelValue, int makeupRxlevValue)
        {
            kind = conditionKind;
            dateTimePickerBeginTimeDTValue = beginTimeDTValue;
            dateTimePickerBeginTimeScanValue = beginTimeScanValue;
            dateTimePickerEndTimeDTValue = endTimeDTValue;
            dateTimePickerEndTimeScanValue = endTimeScanValue;
            
            trackBarChannelValue = channelValue;
            numUdMakeupRxlevValue = makeupRxlevValue;
        }

        public void fillCollection(ListView.ListViewItemCollection projectDTItems, ListView.ListViewItemCollection projectScanItems, ListView.ListViewItemCollection serviceDTItems, ListView.ListViewItemCollection serviceScanItems)
        {
            foreach (ListViewItem lvi in projectDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectDTItems.Add(i);
            }
            foreach (ListViewItem lvi in projectScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectScanItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceDTItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceScanItems.Add(i);
            }
        }
    }

    public class saveItem
    {
        public string text { get; set; }
        public object tag { get; set; }
        public saveItem(string text, object tag)
        {
            this.text = text;
            this.tag = tag;
        }
    }
}
