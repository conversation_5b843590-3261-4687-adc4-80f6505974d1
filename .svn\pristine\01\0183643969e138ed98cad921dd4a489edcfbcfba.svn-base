using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class BTSInfoForm : Form
    {
        public BTSInfoForm(MainModel mainModel, BTS bts)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.bts = bts;
            textBoxName.Text = bts.Name;
            textBoxType.Text = bts.Type.ToString();
            textBoxBand.Text = bts.BandType.ToString();
            textBoxMSC.Text = bts.BelongBSC.BelongMSC.Name;
            textBoxBSC.Text = bts.BelongBSC.BelongMSC.Name;
            textBoxLongitude.Text = bts.Longitude.ToString();
            textBoxLatitude.Text = bts.Latitude.ToString();
            textBoxDescription.Text = bts.Description;
            List<Cell> cells = new List<Cell>();
            foreach (Cell cell in bts.Cells)
            {
                if (MapCellLayer.DrawCurrent)
                {
                    if (cell.ValidPeriod.Contains(DateTime.Now.Date))
                    {
                        cells.Add(cell);
                    }
                }
                else
                {
                    if (cell.ValidPeriod.Contains(MapCellLayer.CurShowTimeAt))
                    {
                        cells.Add(cell);
                    }
                }
            }
            listBoxCell.DataSource = cells;
            listBoxCell.DisplayMember = "Name";
            checkButtonState();
        }

        private void listBoxCell_SelectedIndexChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }

        private void buttonLocationBTS_Click(object sender, EventArgs e)
        {
            mainModel.SelectedBTS = bts;
            mainModel.FireSelectedBTSChanged(this);
        }

        private void buttonInfo_Click(object sender, EventArgs e)
        {
            new CellInfoForm(mainModel, (Cell)listBoxCell.SelectedItem).Show(Owner);
        }

        private void buttonNeighbours_Click(object sender, EventArgs e)
        {
            new NeighboursForm(mainModel, (Cell)listBoxCell.SelectedItem).Show(Owner);
        }

        private void buttonInterference_Click(object sender, EventArgs e)
        {
            int mode = 0;
            if (radioButtonBCCHTCH.Checked)
            {
                mode = 0;
            }
            else if (radioButtonBCCH.Checked)
            {
                mode = 1;
            }
            else if (radioButtonTCH.Checked)
            {
                mode = 2;
            }
            new InterferenceForm(mainModel, (Cell)listBoxCell.SelectedItem, mode).Show(Owner);
        }

        private void buttonCoBSIC_Click(object sender, EventArgs e)
        {
            new CoBSICForm(mainModel, (Cell)listBoxCell.SelectedItem).Show(Owner);
        }

        private void buttonLocation_Click(object sender, EventArgs e)
        {
            mainModel.SelectedCell = (Cell)listBoxCell.SelectedItem;
            mainModel.FireSelectedCellChanged(this);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void checkButtonState()
        {
            bool cellSelected = listBoxCell.SelectedItem != null;
            buttonInfo.Enabled = cellSelected;
            buttonNeighbours.Enabled = cellSelected;
            radioButtonBCCHTCH.Enabled = cellSelected;
            radioButtonBCCH.Enabled = cellSelected;
            radioButtonTCH.Enabled = cellSelected;
            buttonInterference.Enabled = cellSelected;
            buttonCoBSIC.Enabled = cellSelected;
            buttonLocation.Enabled = cellSelected;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.GroupBox groupBox2;
            System.Windows.Forms.GroupBox groupBox3;
            System.Windows.Forms.Label label8;
            System.Windows.Forms.Label label10;
            System.Windows.Forms.Label label11;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label labelTCH;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BTSInfoForm));
            this.buttonLocationBTS = new System.Windows.Forms.Button();
            this.radioButtonTCH = new System.Windows.Forms.RadioButton();
            this.radioButtonBCCH = new System.Windows.Forms.RadioButton();
            this.radioButtonBCCHTCH = new System.Windows.Forms.RadioButton();
            this.buttonCoBSIC = new System.Windows.Forms.Button();
            this.buttonInterference = new System.Windows.Forms.Button();
            this.buttonNeighbours = new System.Windows.Forms.Button();
            this.buttonInfo = new System.Windows.Forms.Button();
            this.buttonLocation = new System.Windows.Forms.Button();
            this.listBoxCell = new System.Windows.Forms.ListBox();
            this.buttonOK = new System.Windows.Forms.Button();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.textBoxLatitude = new System.Windows.Forms.TextBox();
            this.textBoxBand = new System.Windows.Forms.TextBox();
            this.textBoxType = new System.Windows.Forms.TextBox();
            this.textBoxLongitude = new System.Windows.Forms.TextBox();
            this.textBoxMSC = new System.Windows.Forms.TextBox();
            this.textBoxBSC = new System.Windows.Forms.TextBox();
            this.textBoxDescription = new System.Windows.Forms.TextBox();
            groupBox2 = new System.Windows.Forms.GroupBox();
            groupBox3 = new System.Windows.Forms.GroupBox();
            label8 = new System.Windows.Forms.Label();
            label10 = new System.Windows.Forms.Label();
            label11 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            labelTCH = new System.Windows.Forms.Label();
            groupBox2.SuspendLayout();
            groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(this.buttonLocationBTS);
            groupBox2.Controls.Add(this.radioButtonTCH);
            groupBox2.Controls.Add(this.radioButtonBCCH);
            groupBox2.Controls.Add(this.radioButtonBCCHTCH);
            groupBox2.Controls.Add(this.buttonCoBSIC);
            groupBox2.Controls.Add(this.buttonInterference);
            groupBox2.Controls.Add(this.buttonNeighbours);
            groupBox2.Controls.Add(this.buttonInfo);
            groupBox2.Controls.Add(this.buttonLocation);
            groupBox2.Location = new System.Drawing.Point(193, 142);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new System.Drawing.Size(121, 262);
            groupBox2.TabIndex = 17;
            groupBox2.TabStop = false;
            groupBox2.Text = "Operation";
            // 
            // buttonLocationBTS
            // 
            this.buttonLocationBTS.Location = new System.Drawing.Point(6, 19);
            this.buttonLocationBTS.Name = "buttonLocationBTS";
            this.buttonLocationBTS.Size = new System.Drawing.Size(109, 23);
            this.buttonLocationBTS.TabIndex = 0;
            this.buttonLocationBTS.Text = "&Location";
            this.buttonLocationBTS.UseVisualStyleBackColor = true;
            this.buttonLocationBTS.Click += new System.EventHandler(this.buttonLocationBTS_Click);
            // 
            // radioButtonTCH
            // 
            this.radioButtonTCH.AutoSize = true;
            this.radioButtonTCH.Location = new System.Drawing.Point(9, 152);
            this.radioButtonTCH.Name = "radioButtonTCH";
            this.radioButtonTCH.Size = new System.Drawing.Size(71, 16);
            this.radioButtonTCH.TabIndex = 5;
            this.radioButtonTCH.Text = "&TCH Only";
            this.radioButtonTCH.UseVisualStyleBackColor = true;
            // 
            // radioButtonBCCH
            // 
            this.radioButtonBCCH.AutoSize = true;
            this.radioButtonBCCH.Location = new System.Drawing.Point(9, 129);
            this.radioButtonBCCH.Name = "radioButtonBCCH";
            this.radioButtonBCCH.Size = new System.Drawing.Size(77, 16);
            this.radioButtonBCCH.TabIndex = 4;
            this.radioButtonBCCH.Text = "&BCCH Only";
            this.radioButtonBCCH.UseVisualStyleBackColor = true;
            // 
            // radioButtonBCCHTCH
            // 
            this.radioButtonBCCHTCH.AutoSize = true;
            this.radioButtonBCCHTCH.Checked = true;
            this.radioButtonBCCHTCH.Location = new System.Drawing.Point(9, 106);
            this.radioButtonBCCHTCH.Name = "radioButtonBCCHTCH";
            this.radioButtonBCCHTCH.Size = new System.Drawing.Size(83, 16);
            this.radioButtonBCCHTCH.TabIndex = 3;
            this.radioButtonBCCHTCH.TabStop = true;
            this.radioButtonBCCHTCH.Text = "&BCCH && TCH";
            this.radioButtonBCCHTCH.UseVisualStyleBackColor = true;
            // 
            // buttonCoBSIC
            // 
            this.buttonCoBSIC.Location = new System.Drawing.Point(6, 204);
            this.buttonCoBSIC.Name = "buttonCoBSIC";
            this.buttonCoBSIC.Size = new System.Drawing.Size(109, 23);
            this.buttonCoBSIC.TabIndex = 7;
            this.buttonCoBSIC.Text = "&Co-BSIC...";
            this.buttonCoBSIC.UseVisualStyleBackColor = true;
            this.buttonCoBSIC.Click += new System.EventHandler(this.buttonCoBSIC_Click);
            // 
            // buttonInterference
            // 
            this.buttonInterference.Location = new System.Drawing.Point(6, 175);
            this.buttonInterference.Name = "buttonInterference";
            this.buttonInterference.Size = new System.Drawing.Size(109, 23);
            this.buttonInterference.TabIndex = 6;
            this.buttonInterference.Text = "&Interference...";
            this.buttonInterference.UseVisualStyleBackColor = true;
            this.buttonInterference.Click += new System.EventHandler(this.buttonInterference_Click);
            // 
            // buttonNeighbours
            // 
            this.buttonNeighbours.Location = new System.Drawing.Point(6, 77);
            this.buttonNeighbours.Name = "buttonNeighbours";
            this.buttonNeighbours.Size = new System.Drawing.Size(109, 23);
            this.buttonNeighbours.TabIndex = 2;
            this.buttonNeighbours.Text = "&Neighbours...";
            this.buttonNeighbours.UseVisualStyleBackColor = true;
            this.buttonNeighbours.Click += new System.EventHandler(this.buttonNeighbours_Click);
            // 
            // buttonInfo
            // 
            this.buttonInfo.Location = new System.Drawing.Point(6, 48);
            this.buttonInfo.Name = "buttonInfo";
            this.buttonInfo.Size = new System.Drawing.Size(109, 23);
            this.buttonInfo.TabIndex = 1;
            this.buttonInfo.Text = "&Cell Info...";
            this.buttonInfo.UseVisualStyleBackColor = true;
            this.buttonInfo.Click += new System.EventHandler(this.buttonInfo_Click);
            // 
            // buttonLocation
            // 
            this.buttonLocation.Location = new System.Drawing.Point(6, 233);
            this.buttonLocation.Name = "buttonLocation";
            this.buttonLocation.Size = new System.Drawing.Size(109, 23);
            this.buttonLocation.TabIndex = 8;
            this.buttonLocation.Text = "&Location Cell";
            this.buttonLocation.UseVisualStyleBackColor = true;
            this.buttonLocation.Click += new System.EventHandler(this.buttonLocation_Click);
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(this.listBoxCell);
            groupBox3.Location = new System.Drawing.Point(12, 142);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new System.Drawing.Size(175, 262);
            groupBox3.TabIndex = 16;
            groupBox3.TabStop = false;
            groupBox3.Text = "&Cell";
            // 
            // listBoxCell
            // 
            this.listBoxCell.Anchor = (((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right);
            this.listBoxCell.FormattingEnabled = true;
            this.listBoxCell.HorizontalScrollbar = true;
            this.listBoxCell.ItemHeight = 12;
            this.listBoxCell.Location = new System.Drawing.Point(6, 17);
            this.listBoxCell.Name = "listBoxCell";
            this.listBoxCell.Size = new System.Drawing.Size(163, 232);
            this.listBoxCell.TabIndex = 0;
            this.listBoxCell.SelectedIndexChanged += new System.EventHandler(this.listBoxCell_SelectedIndexChanged);
            this.listBoxCell.DoubleClick += new System.EventHandler(this.buttonLocation_Click);
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(12, 15);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 0;
            label8.Text = "&Name:";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new System.Drawing.Point(12, 67);
            label10.Name = "label10";
            label10.Size = new System.Drawing.Size(29, 12);
            label10.TabIndex = 6;
            label10.Text = "&MSC:";
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(158, 67);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(29, 12);
            label11.TabIndex = 8;
            label11.Text = "&BSC:";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(158, 41);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(35, 12);
            label2.TabIndex = 4;
            label2.Text = "&Band:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(12, 41);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(35, 12);
            label4.TabIndex = 2;
            label4.Text = "&Type:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(12, 93);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(35, 12);
            label5.TabIndex = 10;
            label5.Text = "&Long:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(158, 93);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(29, 12);
            label6.TabIndex = 12;
            label6.Text = "&Lat:";
            // 
            // labelTCH
            // 
            labelTCH.AutoSize = true;
            labelTCH.Location = new System.Drawing.Point(12, 119);
            labelTCH.Name = "labelTCH";
            labelTCH.Size = new System.Drawing.Size(35, 12);
            labelTCH.TabIndex = 14;
            labelTCH.Text = "&Desc:";
            // 
            // buttonOK
            // 
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(227, 410);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 23);
            this.buttonOK.TabIndex = 18;
            this.buttonOK.Text = "&OK";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // textBoxName
            // 
            this.textBoxName.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxName.Location = new System.Drawing.Point(56, 12);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.ReadOnly = true;
            this.textBoxName.Size = new System.Drawing.Size(258, 21);
            this.textBoxName.TabIndex = 1;
            // 
            // textBoxLatitude
            // 
            this.textBoxLatitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLatitude.Location = new System.Drawing.Point(202, 90);
            this.textBoxLatitude.Name = "textBoxLatitude";
            this.textBoxLatitude.ReadOnly = true;
            this.textBoxLatitude.Size = new System.Drawing.Size(112, 21);
            this.textBoxLatitude.TabIndex = 13;
            // 
            // textBoxBand
            // 
            this.textBoxBand.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBand.Location = new System.Drawing.Point(202, 38);
            this.textBoxBand.Name = "textBoxBand";
            this.textBoxBand.ReadOnly = true;
            this.textBoxBand.Size = new System.Drawing.Size(112, 21);
            this.textBoxBand.TabIndex = 5;
            // 
            // textBoxType
            // 
            this.textBoxType.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxType.Location = new System.Drawing.Point(56, 38);
            this.textBoxType.Name = "textBoxType";
            this.textBoxType.ReadOnly = true;
            this.textBoxType.Size = new System.Drawing.Size(96, 21);
            this.textBoxType.TabIndex = 3;
            // 
            // textBoxLongitude
            // 
            this.textBoxLongitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLongitude.Location = new System.Drawing.Point(56, 90);
            this.textBoxLongitude.Name = "textBoxLongitude";
            this.textBoxLongitude.ReadOnly = true;
            this.textBoxLongitude.Size = new System.Drawing.Size(96, 21);
            this.textBoxLongitude.TabIndex = 11;
            // 
            // textBoxMSC
            // 
            this.textBoxMSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxMSC.Location = new System.Drawing.Point(56, 64);
            this.textBoxMSC.Name = "textBoxMSC";
            this.textBoxMSC.ReadOnly = true;
            this.textBoxMSC.Size = new System.Drawing.Size(96, 21);
            this.textBoxMSC.TabIndex = 7;
            // 
            // textBoxBSC
            // 
            this.textBoxBSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBSC.Location = new System.Drawing.Point(202, 64);
            this.textBoxBSC.Name = "textBoxBSC";
            this.textBoxBSC.ReadOnly = true;
            this.textBoxBSC.Size = new System.Drawing.Size(112, 21);
            this.textBoxBSC.TabIndex = 9;
            // 
            // textBoxDescription
            // 
            this.textBoxDescription.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxDescription.Location = new System.Drawing.Point(56, 116);
            this.textBoxDescription.Name = "textBoxDescription";
            this.textBoxDescription.ReadOnly = true;
            this.textBoxDescription.Size = new System.Drawing.Size(258, 21);
            this.textBoxDescription.TabIndex = 15;
            // 
            // BTSInfoForm
            // 
            this.AcceptButton = this.buttonOK;
            this.CancelButton = this.buttonOK;
            this.ClientSize = new System.Drawing.Size(326, 445);
            this.Controls.Add(this.textBoxDescription);
            this.Controls.Add(labelTCH);
            this.Controls.Add(label10);
            this.Controls.Add(this.textBoxLatitude);
            this.Controls.Add(label11);
            this.Controls.Add(this.textBoxBand);
            this.Controls.Add(this.textBoxType);
            this.Controls.Add(label2);
            this.Controls.Add(this.textBoxLongitude);
            this.Controls.Add(label4);
            this.Controls.Add(this.textBoxMSC);
            this.Controls.Add(label5);
            this.Controls.Add(this.textBoxBSC);
            this.Controls.Add(label6);
            this.Controls.Add(label8);
            this.Controls.Add(groupBox2);
            this.Controls.Add(this.textBoxName);
            this.Controls.Add(groupBox3);
            this.Controls.Add(this.buttonOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BTSInfoForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "BTS Info";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox3.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private readonly MainModel mainModel;

        private readonly BTS bts;

        private TextBox textBoxName;

        private TextBox textBoxType;

        private TextBox textBoxBand;

        private TextBox textBoxMSC;

        private TextBox textBoxBSC;

        private TextBox textBoxLongitude;

        private TextBox textBoxLatitude;

        private TextBox textBoxDescription;

        private ListBox listBoxCell;

        private Button buttonLocationBTS;

        private Button buttonInfo;

        private Button buttonNeighbours;

        private RadioButton radioButtonBCCHTCH;

        private RadioButton radioButtonBCCH;

        private RadioButton radioButtonTCH;

        private Button buttonInterference;

        private Button buttonCoBSIC;

        private Button buttonLocation;

        private Button buttonOK;
    }
}
