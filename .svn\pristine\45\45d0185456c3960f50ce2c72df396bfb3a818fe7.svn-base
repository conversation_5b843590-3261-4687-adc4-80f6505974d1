﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.MTGis;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;


namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByRegionAsRoad : QueryKPIStatByRegion
    {/*
      * 1、先临时保存为栅格矩阵数据
      * 2、区域内道路与栅格矩阵数据碰撞，最终以道路维度保存数据
      */
        public override string Name
        {
            get { return "KPI统计(按区域内道路)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11019, this.Name);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            WaitTextBox.Show("正在分析数据所属道路...", roadHitTestMatrixInThread);
        }

        DIYStatByStreetsOfRegionSettingDlg conditionDlg = null;
        protected override bool getConditionBeforeQuery()
        {
            if (conditionDlg == null)
            {
                conditionDlg = new DIYStatByStreetsOfRegionSettingDlg();
            }
            if (conditionDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            bool ready = base.getConditionBeforeQuery();
            if (ready)
            {
                streetMapList = conditionDlg.GetCondition();
                roadDataDic = new Dictionary<string, KPIDataGroup>();
                gridMatrix = new GridMatrix<GridDataHub>();
            }
            return ready;
        }

        protected override void fireShowResult()
        {
            if (roadDataDic.Count == 0)
            {
                MessageBox.Show("无道路统计信息！");
                roadDataDic = null;
                return;
            }
            ActionCreateChildFrame action = new ActionCreateChildFrame();
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = typeof(KPIReportMainForm).FullName;
            actionParam["Text"] = "统计结果";
            actionParam["ImageFilePath"] = @"images\stat.gif";
            action.Param = actionParam;
            action.OnAction();
            KPIReportMainForm statForm = action.CreatedForm as KPIReportMainForm;
            List<KPIDataGroup> grps = new List<KPIDataGroup>(roadDataDic.Values);
            statForm.ShowReport(KPIReportManager.Instance.Reports, curReportStyle, grps);
            roadDataDic = null;
            gridMatrix = null;
            streetMapList = null;
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package
            , List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng,grid.CenterLat))
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);
            saveGridData(singleStatData, this.curReportStyle.HasGridPerCell);
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            saveEventData(evt);
        }

        private GridMatrix<GridDataHub> gridMatrix = null;

        private void saveGridData(KPIStatDataBase data, bool makeMatrix)
        {
            DbRect rect = GridHelper.GetDefaultSizeGridBoundsByLeftTopPoint(data.LTLng, data.LTLat);
            int rowIdx;
            int colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(rect.Center().x, rect.Center().y, out rowIdx, out colIdx);
            GridDataHub hub = gridMatrix[rowIdx, colIdx];
            if (hub == null)
            {
                hub = new GridDataHub();
                hub.LTLng = rect.x1;
                hub.LTLat = rect.y2;
                gridMatrix[rowIdx, colIdx] = hub;
            }
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(data.FileID);
            hub.DataHub.AddStatData(fi, data, makeMatrix);
        }

        private void saveEventData(Event e)
        {
            int rowIdx;
            int colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(e.Longitude, e.Latitude, out rowIdx, out colIdx);
            double ltLng = GridHelper.RoundAsLeft(e.Longitude);
            double ltLat = GridHelper.RoundAsTop(e.Latitude);
            GridDataHub hub = gridMatrix[rowIdx, colIdx];
            if (hub == null)
            {
                hub = new GridDataHub();
                hub.LTLng = ltLng;
                hub.LTLat = ltLat;
                gridMatrix[rowIdx, colIdx] = hub;
            }
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(e.FileID);
            hub.DataHub.AddStatData(fi, new StatDataEvent(e,needSeparateByServiceID(e), needSeparateByFileName(e)), this.curReportStyle.HasGridPerCell);
        }

        private class GridDataHub : GridUnitBase
        {
            public KPIDataGroup DataHub = new KPIDataGroup(null);
        }

        /// <summary>
        /// 道路图层列表
        /// </summary>
        List<StreetInjectTableInfo> streetMapList = null;

        private void roadHitTestMatrixInThread()
        {
            Dictionary<GridDataHub, bool> gridDic = new Dictionary<GridDataHub, bool>();
            foreach (StreetInjectTableInfo tableInfo in streetMapList)
            {
                WaitTextBox.Text = string.Format("正在分析图层{0}...", tableInfo.FileName);
                try
                {
                    string streetColumnName = tableInfo.ColumnName;
                    MapWinGIS.Shapefile shpFile = new MapWinGIS.Shapefile();
                    if (!shpFile.Open(tableInfo.FilePath, null))
                    {
                        XtraMessageBox.Show("打开图层文件失败：" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    }
                    else
                    {
                        dealShpFile(gridDic, streetColumnName, shpFile);
                        shpFile.Close();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message + Environment.NewLine + ex.Source);
                }
            }
            addRoadDataDic(gridDic);
            System.Threading.Thread.Sleep(10);
            WaitTextBox.Close();
        }

        private void dealShpFile(Dictionary<GridDataHub, bool> gridDic, string streetColumnName, MapWinGIS.Shapefile shpFile)
        {
            int nmAreaFieldIdx = MapOperation.GetColumnFieldIndex(shpFile, streetColumnName);
            for (int i = 0; i < shpFile.NumShapes; i++)
            {
                MapWinGIS.Shape geome = shpFile.get_Shape(i);
                object fieldValue = shpFile.get_CellValue(nmAreaFieldIdx, i);//先用object保存，避免不是字符串时，转换失败
                if (fieldValue != null)
                {
                    string streetName = fieldValue.ToString();

                    if (streetName.Trim().Length > 0
                        && (geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON
                       || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINE))
                    {
                        saveValidRoadData(gridDic, geome, streetName);
                    }
                }
            }
        }

        private void saveValidRoadData(Dictionary<GridDataHub, bool> gridDic, MapWinGIS.Shape geome, string streetName)
        {
            MTPolygon roadPly = new MTPolygon();
            roadPly.AppendRoadToPolygon(geome);
            if (condition.Geometorys.GeoOp.CheckStreetInRegion(geome))
            {
                foreach (GridDataHub data in gridMatrix)
                {
                    if (roadPly.CheckPointInRegion(data.CenterLng, data.CenterLat)
                        && !isDataCalced(gridDic, data))
                    {
                        saveRoadData(streetName, data);
                    }
                }
            }
        }

        private void addRoadDataDic(Dictionary<GridDataHub, bool> gridDic)
        {
            if (gridDic.Count > 0)
            {
                foreach (KPIDataGroup roadGrp in roadDataDic.Values)
                {
                    roadGrp.FinalMtMoGroup();
                }
                KPIDataGroup summaryGrp = new KPIDataGroup(string.Empty, true);
                foreach (GridDataHub gridData in gridDic.Keys)
                {
                    gridData.DataHub.FinalMtMoGroup();
                    summaryGrp.Merge(gridData.DataHub);
                }
                roadDataDic.Add(Guid.NewGuid().ToString(), summaryGrp);
            }
        }

        private bool isDataCalced(Dictionary<GridDataHub, bool> dic, GridDataHub data)
        {
            bool calced = false;
            if (!dic.ContainsKey(data))
            {
                dic.Add(data, true);
            }
            else
            {
                calced = true;
            }
#if DEBUG
            Console.Write(calced);
#endif

#if GridSingleStat
            return calced;
#else
            return false;
#endif
        }

        private Dictionary<string, KPIDataGroup> roadDataDic = null;

        private void saveRoadData(string roadName, GridDataHub dataHub)
        {
            KPIDataGroup dataGrp = null;
            if (!roadDataDic.TryGetValue(roadName, out dataGrp))
            {
                dataGrp = new KPIDataGroup(roadName);
                roadDataDic[roadName] = dataGrp;
            }
            dataGrp.Merge(dataHub.DataHub);
        }

    }


}
