﻿namespace MasterCom.RAMS.Func
{
    partial class CellFusionDataFormSettingBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.treeViewFusion = new System.Windows.Forms.TreeView();
            this.btnOk = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuAddKpi = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuDelKpi = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuEditKpi = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuReset = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.treeViewFusion);
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(378, 280);
            this.panel1.TabIndex = 0;
            // 
            // treeViewFusion
            // 
            this.treeViewFusion.CheckBoxes = true;
            this.treeViewFusion.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewFusion.FullRowSelect = true;
            this.treeViewFusion.HideSelection = false;
            this.treeViewFusion.Location = new System.Drawing.Point(0, 0);
            this.treeViewFusion.Name = "treeViewFusion";
            this.treeViewFusion.Size = new System.Drawing.Size(378, 280);
            this.treeViewFusion.TabIndex = 0;
            // 
            // btnOk
            // 
            this.btnOk.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOk.Location = new System.Drawing.Point(232, 286);
            this.btnOk.Name = "btnOk";
            this.btnOk.Size = new System.Drawing.Size(60, 23);
            this.btnOk.TabIndex = 1;
            this.btnOk.Text = "确定";
            this.btnOk.UseVisualStyleBackColor = true;
            this.btnOk.Click += new System.EventHandler(this.btnOk_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(310, 286);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(60, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuAddKpi,
            this.ToolStripMenuDelKpi,
            this.ToolStripMenuEditKpi,
            this.ToolStripMenuReset});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(125, 92);
            this.contextMenuStrip1.Text = "恢复默认";
            // 
            // ToolStripMenuAddKpi
            // 
            this.ToolStripMenuAddKpi.Name = "ToolStripMenuAddKpi";
            this.ToolStripMenuAddKpi.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuAddKpi.Text = "增加指标";
            this.ToolStripMenuAddKpi.Click += new System.EventHandler(this.ToolStripMenuAddKpi_Click);
            // 
            // ToolStripMenuDelKpi
            // 
            this.ToolStripMenuDelKpi.Name = "ToolStripMenuDelKpi";
            this.ToolStripMenuDelKpi.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuDelKpi.Text = "删除指标";
            this.ToolStripMenuDelKpi.Click += new System.EventHandler(this.ToolStripMenuDelKpi_Click);
            // 
            // ToolStripMenuEditKpi
            // 
            this.ToolStripMenuEditKpi.Name = "ToolStripMenuEditKpi";
            this.ToolStripMenuEditKpi.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuEditKpi.Text = "修改指标";
            this.ToolStripMenuEditKpi.Click += new System.EventHandler(this.ToolStripMenuEditKpi_Click);
            // 
            // ToolStripMenuReset
            // 
            this.ToolStripMenuReset.Name = "ToolStripMenuReset";
            this.ToolStripMenuReset.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuReset.Text = "恢复默认";
            this.ToolStripMenuReset.Click += new System.EventHandler(this.ToolStripMenuReset_Click);
            // 
            // CellFusionDataFormSettingBox
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(378, 312);
            this.ContextMenuStrip = this.contextMenuStrip1;
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOk);
            this.Controls.Add(this.panel1);
            this.Name = "CellFusionDataFormSettingBox";
            this.Text = "小区关联数据-显示列设置";
            this.panel1.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.TreeView treeViewFusion;
        private System.Windows.Forms.Button btnOk;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuAddKpi;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuReset;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuDelKpi;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuEditKpi;
    }
}