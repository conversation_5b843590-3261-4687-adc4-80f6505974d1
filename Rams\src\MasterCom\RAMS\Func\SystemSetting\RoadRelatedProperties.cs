﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class RoadRelatedProperties : PropertiesControl
    {
        public RoadRelatedProperties(ConfigInfo configInfo)
        {
            InitializeComponent();
            this.configInfo = configInfo;
            cbxDistrictName.Properties.Items.Clear();
            foreach (string districtName in DistrictManager.GetInstance().DistrictNames)
            {
                if (!string.IsNullOrEmpty(districtName))
                {
                    cbxDistrictName.Properties.Items.Add(districtName);
                }
            }
            if (cbxDistrictName.Properties.Items.Count > 0)
            {
                cbxDistrictName.SelectedIndex = 0;
            }
        }

        public override string ParentName
        {
            get { return "地图设置"; }
        }

        public override string SelfName
        {
            get { return "道路关联设置"; }
        }

        public override string TabPageName
        {
            get { return "道路关联设置"; }
        }

        public override bool IsValid()
        {
            foreach (RoadRelevanceOption option in configInfo.GISManager.RoadRelevanceOptions)
            {
                if (option.RelevanceType == RoadRelevanceType.ByMatrix
                    && (!option.IsAllRoadLayer
                    && option.RoadLayerNameColIdx == -1))
                {
                    MessageBox.Show("道路关联设置，【" + DistrictManager.GetInstance().getDistrictName(option.DistrictID) + "】按指定图层栅格化关联道路名称时，需选择对应的道路名称列！");
                    return false;
                }
            }
            return true;
        }

        private void radioByMwgSelf_CheckedChanged(object sender, EventArgs e)
        {
            btnGridNow.Enabled = groupBox1.Enabled = !radioByMwgSelf.Checked;
            if (radioByMwgSelf.Checked)
            {
                curOption.RelevanceType = RoadRelevanceType.ByMapWinGis;
            }
            else
            {
                curOption.RelevanceType = RoadRelevanceType.ByMatrix;
            }
        }

        private void radioAllRoadLayer_CheckedChanged(object sender, EventArgs e)
        {
            edtLayerPath.Enabled = btnSelectPath.Enabled = cbxRoadNameCol.Enabled = !radioAllRoadLayer.Checked;
            if (radioAllRoadLayer.Checked)
            {
                curOption.SetLayerFileName(null);
                edtLayerPath.Text = null;
            }
        }

        private void btnGridNow_Click(object sender, EventArgs e)
        {
            if (radioSpecifiedLayer.Checked && cbxRoadNameCol.SelectedItem == null)
            {
                MessageBox.Show("请选择道路名称所在列！");
                return;
            }
            if (MainModel.GetInstance().DistrictID != cbxDistrictName.SelectedIndex + 1)
            {
                MessageBox.Show("选择的地市与当前登录地市不一致，系统将在下一次登录该地市时根据设定进行栅格化处理。");
                return;
            }
            List<SfLayerInfo> layers = getLayers();
            doGrid(layers);
        }

        private List<SfLayerInfo> getLayers()
        {
            List<SfLayerInfo> layers = new List<SfLayerInfo>();
            if (radioAllRoadLayer.Checked)
            {
                MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
                if (mf == null)
                {
                    MessageBox.Show("地图窗口不可见，无法栅格化道路图层!");
                    return new List<SfLayerInfo>();
                }
                foreach (SfLayerInfo layerInfo in mf.AllLayerList)
                {
                    if (layerInfo.vecItem.is_road_layer == 1)
                    {
                        layers.Add(layerInfo);
                    }
                }
            }
            else
            {
                string fileName = edtLayerPath.Text;
                if (File.Exists(fileName))
                {
                    Shapefile shapeFile = getShapefile(fileName);
                    if (shapeFile == null)
                    {
                        return new List<SfLayerInfo>();
                    }
                    SfLayerInfo lyrInfo = new SfLayerInfo();
                    lyrInfo.sf = shapeFile;
                    VecLayerItem vec = new VecLayerItem();
                    vec.label_field = cbxRoadNameCol.SelectedItem.ToString();
                    lyrInfo.vecItem = vec;
                    layers.Add(lyrInfo);
                }
                else
                {
                    MessageBox.Show("请选择道路图层文件！");
                    return new List<SfLayerInfo>();
                }
            }

            return layers;
        }

        private Shapefile getShapefile(string fileName)
        {
            Shapefile shapeFile = null;
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            if (mf != null)
            {
                foreach (SfLayerInfo layerInfo in mf.AllLayerList)
                {
                    if (layerInfo.sf.Filename.Equals(fileName))
                    {
                        shapeFile = layerInfo.sf;
                        break;
                    }
                }
            }
            if (shapeFile == null)
            {
                shapeFile = new Shapefile();
                bool opened = shapeFile.Open(fileName, null);
                if (!opened)
                {
                    MessageBox.Show("打开图层失败！\r\n" + shapeFile.get_ErrorMsg(shapeFile.LastErrorCode));
                    return null;
                }
            }

            return shapeFile;
        }

        private void doGrid(List<SfLayerInfo> layers)
        {
            if (layers.Count > 0)
            {
                try
                {
                    WaitBox.Show(MainModel.GetInstance().MainForm, makeShpFileGridInThread, layers);
                    GISManager.GetInstance().Save(MainModel.GetInstance().DistrictID);
                    GISManager.GetInstance().CurRoadRelevanceOption = curOption;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
                //finally
                //{
                //    GC.Collect();
                //}
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="o">参数应为List<SfLayerInfo></param>
        private void makeShpFileGridInThread(object o)
        {
            try
            {
                List<SfLayerInfo> layers = o as List<SfLayerInfo>;
                if (layers == null)
                {
                    return;
                }
                GISManager.GetInstance().ClearGridedRoadInfo();
                foreach (SfLayerInfo layerInfo in layers)
                {
                    int lbFldIdx = MapOperation.GetColumnFieldIndex(layerInfo.sf, layerInfo.vecItem.label_field);
                    if (lbFldIdx < 0)
                    {
                        continue;
                    }
                    int shpCnt=layerInfo.sf.NumShapes;
                    WaitBox.ProgressPercent = 2;
                    WaitBox.Text = "正在栅格化图层:" + System.IO.Path.GetFileNameWithoutExtension(layerInfo.sf.Filename);
                    int progress = 0;
                    int reportPer = shpCnt / 50;
                    for (int s = 0; s < shpCnt; s++)
                    {
                        progress++;
                        if (progress == reportPer)
                        {
                            progress = 0;
                            WaitBox.ProgressPercent += 2;
                        }
                        MapWinGIS.Shape shape = layerInfo.sf.get_Shape(s);
                        object obj = layerInfo.sf.get_CellValue(lbFldIdx, s);
                        if (obj == null || string.IsNullOrEmpty(obj.ToString().Trim()))
                        {
                            continue;
                        }
                        GISManager.GetInstance().MakeRoadShapeGrid(shape, obj.ToString());
                    }
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void btnSelectPath_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Multiselect = false;
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                curOption.SetLayerFileName(dlg.FileName);
                edtLayerPath.Text = curOption.RoadLayerFileName;
                flushFieldCbx(curOption.RoadLayerNameColIdx);
            }
        }

        private void flushFieldCbx(int idx)
        {
            cbxRoadNameCol.Properties.Items.Clear();
            cbxRoadNameCol.SelectedItem = null;
            if (curOption.LayerFields==null)
            {
                return;
            }
            foreach (string colName in curOption.LayerFields)
            {
                cbxRoadNameCol.Properties.Items.Add(colName);
            }
            if (cbxRoadNameCol.Properties.Items.Count > 0)
            {
                cbxRoadNameCol.SelectedIndex = idx;
            }
        }

        RoadRelevanceOption curOption = null;
        private void cbxDistrictName_SelectedIndexChanged(object sender, EventArgs e)
        {
            int districtID = -1;
            if (cbxDistrictName.SelectedIndex >= 0)
            {
                districtID = cbxDistrictName.SelectedIndex + 1;
            }
            curOption = configInfo.GISManager.GetRoadRelevanceOption(districtID);
            if (curOption == null)
            {
                curOption = new RoadRelevanceOption(districtID);
                configInfo.GISManager.SetRoadRelevanceOption(curOption);
            }
            visiualOption();
        }

        private void visiualOption()
        {
            edtLayerPath.Text = null;
            radioByMwgSelf.Checked = curOption.RelevanceType == RoadRelevanceType.ByMapWinGis;
            radioByGrid.Checked = !radioByMwgSelf.Checked;
            radioAllRoadLayer.Checked = curOption.IsAllRoadLayer;
            radioSpecifiedLayer.Checked = !radioAllRoadLayer.Checked;
            edtLayerPath.Text = curOption.RoadLayerFileName;
            flushFieldCbx(curOption.RoadLayerNameColIdx);
        }

        private void cbxRoadNameCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            curOption.RoadLayerNameColIdx = cbxRoadNameCol.SelectedIndex;
        }

    }
}
