using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class CDCellInfoForm : Form
    {
        public CDCellInfoForm(MainModel mainModel, CDCell cell)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.cell = cell;
            textBoxID.Text = cell.ID.ToString();
            textBoxName.Text = cell.Name;
            textBoxCode.Text = cell.Code;
            textBoxSTime.Text = cell.STime.ToString();
            textBoxETime.Text = cell.ETime.ToString();
            textBoxPN.Text = cell.PN.ToString();
            textBoxUARFCN.Text = cell.UARFCN.ToString();
            textBoxUARFCNList.Text = cell.UARFCNList;
            textBoxType.Text = cell.Type.ToString();
            textBoxDirection.Text = cell.Direction.ToString();
            textBoxMSC.Text = cell.BelongNodeBs[0].BelongRNC.BelongMSC.Name;
            textBoxRNC.Text = cell.BelongNodeBs[0].BelongRNC.Name;
            textBoxNodeB.Text = cell.BelongNodeBs[0].Name;
            textBoxLongitude.Text = cell.Longitude.ToString();
            textBoxLatitude.Text = cell.Latitude.ToString();
            List<CDAntenna> antennas = new List<CDAntenna>();
            foreach (CDAntenna antenna in cell.Antennas)
            {
                if (antenna.Current == antenna)
                {
                    antennas.Add(antenna);
                }
            }
            listBoxAntenna.DataSource = antennas;
            listBoxAntenna.DisplayMember = "SimpleInfo";
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            Close();
        }
        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelID;
            System.Windows.Forms.Label labelCI;
            System.Windows.Forms.Label labelPN;
            System.Windows.Forms.Label labelUARFCN;
            System.Windows.Forms.Label labelUARFCNList;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label labelNodeB;
            System.Windows.Forms.GroupBox groupBox1;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label labelMSC;
            System.Windows.Forms.Label label1RNC;
            System.Windows.Forms.Label label9;
            System.Windows.Forms.Label label8;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CDCellInfoForm));
            this.listBoxAntenna = new System.Windows.Forms.ListBox();
            this.textBoxNodeB = new System.Windows.Forms.TextBox();
            this.textBoxLatitude = new System.Windows.Forms.TextBox();
            this.textBoxDirection = new System.Windows.Forms.TextBox();
            this.textBoxType = new System.Windows.Forms.TextBox();
            this.textBoxLongitude = new System.Windows.Forms.TextBox();
            this.textBoxMSC = new System.Windows.Forms.TextBox();
            this.textBoxRNC = new System.Windows.Forms.TextBox();
            this.textBoxCode = new System.Windows.Forms.TextBox();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.textBoxSTime = new System.Windows.Forms.TextBox();
            this.textBoxUARFCNList = new System.Windows.Forms.TextBox();
            this.textBoxUARFCN = new System.Windows.Forms.TextBox();
            this.textBoxPN = new System.Windows.Forms.TextBox();
            this.textBoxETime = new System.Windows.Forms.TextBox();
            this.textBoxID = new System.Windows.Forms.TextBox();
            this.buttonOK = new System.Windows.Forms.Button();
            labelID = new System.Windows.Forms.Label();
            labelCI = new System.Windows.Forms.Label();
            labelPN = new System.Windows.Forms.Label();
            labelUARFCN = new System.Windows.Forms.Label();
            labelUARFCNList = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            labelNodeB = new System.Windows.Forms.Label();
            groupBox1 = new System.Windows.Forms.GroupBox();
            label12 = new System.Windows.Forms.Label();
            labelMSC = new System.Windows.Forms.Label();
            label1RNC = new System.Windows.Forms.Label();
            label9 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(6, 22);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(23, 12);
            labelID.TabIndex = 0;
            labelID.Text = "&ID:";
            // 
            // labelCI
            // 
            labelCI.AutoSize = true;
            labelCI.Location = new System.Drawing.Point(137, 48);
            labelCI.Name = "labelCI";
            labelCI.Size = new System.Drawing.Size(41, 12);
            labelCI.TabIndex = 8;
            labelCI.Text = "&ETime:";
            // 
            // labelPN
            // 
            labelPN.AutoSize = true;
            labelPN.Location = new System.Drawing.Point(136, 76);
            labelPN.Name = "labelPN";
            labelPN.Size = new System.Drawing.Size(23, 12);
            labelPN.TabIndex = 10;
            labelPN.Text = "&PN:";
            // 
            // labelUARFCN
            // 
            labelUARFCN.AutoSize = true;
            labelUARFCN.Location = new System.Drawing.Point(7, 74);
            labelUARFCN.Name = "labelUARFCN";
            labelUARFCN.Size = new System.Drawing.Size(47, 12);
            labelUARFCN.TabIndex = 12;
            labelUARFCN.Text = "&UARFCN:";
            // 
            // labelUARFCNList
            // 
            labelUARFCNList.AutoSize = true;
            labelUARFCNList.Location = new System.Drawing.Point(6, 100);
            labelUARFCNList.Name = "labelUARFCNList";
            labelUARFCNList.Size = new System.Drawing.Size(77, 12);
            labelUARFCNList.TabIndex = 16;
            labelUARFCNList.Text = "&UARFCN List:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(6, 48);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(41, 12);
            label1.TabIndex = 6;
            label1.Text = "&STime:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(135, 126);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(29, 12);
            label3.TabIndex = 22;
            label3.Text = "&Dir:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(6, 126);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(35, 12);
            label4.TabIndex = 18;
            label4.Text = "&Type:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(6, 204);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(35, 12);
            label5.TabIndex = 31;
            label5.Text = "&Long:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(137, 204);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(29, 12);
            label6.TabIndex = 33;
            label6.Text = "&Lat:";
            // 
            // labelNodeB
            // 
            labelNodeB.AutoSize = true;
            labelNodeB.Location = new System.Drawing.Point(6, 178);
            labelNodeB.Name = "labelNodeB";
            labelNodeB.Size = new System.Drawing.Size(41, 12);
            labelNodeB.TabIndex = 28;
            labelNodeB.Text = "&NodeB:";
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(label12);
            groupBox1.Controls.Add(this.listBoxAntenna);
            groupBox1.Controls.Add(this.textBoxNodeB);
            groupBox1.Controls.Add(labelMSC);
            groupBox1.Controls.Add(this.textBoxLatitude);
            groupBox1.Controls.Add(this.textBoxDirection);
            groupBox1.Controls.Add(label1RNC);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(label9);
            groupBox1.Controls.Add(this.textBoxType);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(this.textBoxLongitude);
            groupBox1.Controls.Add(labelNodeB);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(this.textBoxMSC);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(this.textBoxRNC);
            groupBox1.Controls.Add(label6);
            groupBox1.Controls.Add(this.textBoxCode);
            groupBox1.Controls.Add(this.textBoxName);
            groupBox1.Controls.Add(this.textBoxSTime);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(this.textBoxUARFCNList);
            groupBox1.Controls.Add(this.textBoxUARFCN);
            groupBox1.Controls.Add(this.textBoxPN);
            groupBox1.Controls.Add(this.textBoxETime);
            groupBox1.Controls.Add(labelUARFCNList);
            groupBox1.Controls.Add(labelUARFCN);
            groupBox1.Controls.Add(labelPN);
            groupBox1.Controls.Add(labelCI);
            groupBox1.Controls.Add(this.textBoxID);
            groupBox1.Controls.Add(labelID);
            groupBox1.Location = new System.Drawing.Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(400, 276);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "Info";
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(8, 231);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(35, 12);
            label12.TabIndex = 35;
            label12.Text = "&Ante:";
            // 
            // listBoxAntenna
            // 
            this.listBoxAntenna.FormattingEnabled = true;
            this.listBoxAntenna.HorizontalScrollbar = true;
            this.listBoxAntenna.ItemHeight = 12;
            this.listBoxAntenna.Location = new System.Drawing.Point(51, 227);
            this.listBoxAntenna.Name = "listBoxAntenna";
            this.listBoxAntenna.Size = new System.Drawing.Size(210, 40);
            this.listBoxAntenna.TabIndex = 36;
            // 
            // textBoxNodeB
            // 
            this.textBoxNodeB.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxNodeB.Location = new System.Drawing.Point(51, 175);
            this.textBoxNodeB.Name = "textBoxNodeB";
            this.textBoxNodeB.ReadOnly = true;
            this.textBoxNodeB.Size = new System.Drawing.Size(80, 21);
            this.textBoxNodeB.TabIndex = 29;
            // 
            // labelMSC
            // 
            labelMSC.AutoSize = true;
            labelMSC.Location = new System.Drawing.Point(6, 152);
            labelMSC.Name = "labelMSC";
            labelMSC.Size = new System.Drawing.Size(29, 12);
            labelMSC.TabIndex = 24;
            labelMSC.Text = "&MSC:";
            // 
            // textBoxLatitude
            // 
            this.textBoxLatitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLatitude.Location = new System.Drawing.Point(181, 201);
            this.textBoxLatitude.Name = "textBoxLatitude";
            this.textBoxLatitude.ReadOnly = true;
            this.textBoxLatitude.Size = new System.Drawing.Size(80, 21);
            this.textBoxLatitude.TabIndex = 34;
            // 
            // textBoxDirection
            // 
            this.textBoxDirection.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxDirection.Location = new System.Drawing.Point(181, 123);
            this.textBoxDirection.Name = "textBoxDirection";
            this.textBoxDirection.ReadOnly = true;
            this.textBoxDirection.Size = new System.Drawing.Size(80, 21);
            this.textBoxDirection.TabIndex = 23;
            // 
            // label1RNC
            // 
            label1RNC.AutoSize = true;
            label1RNC.Location = new System.Drawing.Point(137, 152);
            label1RNC.Name = "label1RNC";
            label1RNC.Size = new System.Drawing.Size(29, 12);
            label1RNC.TabIndex = 26;
            label1RNC.Text = "&RNC:";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(267, 22);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(35, 12);
            label9.TabIndex = 4;
            label9.Text = "&Code:";
            // 
            // textBoxType
            // 
            this.textBoxType.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxType.Location = new System.Drawing.Point(51, 123);
            this.textBoxType.Name = "textBoxType";
            this.textBoxType.ReadOnly = true;
            this.textBoxType.Size = new System.Drawing.Size(80, 21);
            this.textBoxType.TabIndex = 19;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(137, 22);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 2;
            label8.Text = "&Name:";
            // 
            // textBoxLongitude
            // 
            this.textBoxLongitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLongitude.Location = new System.Drawing.Point(51, 201);
            this.textBoxLongitude.Name = "textBoxLongitude";
            this.textBoxLongitude.ReadOnly = true;
            this.textBoxLongitude.Size = new System.Drawing.Size(80, 21);
            this.textBoxLongitude.TabIndex = 32;
            // 
            // textBoxMSC
            // 
            this.textBoxMSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxMSC.Location = new System.Drawing.Point(51, 149);
            this.textBoxMSC.Name = "textBoxMSC";
            this.textBoxMSC.ReadOnly = true;
            this.textBoxMSC.Size = new System.Drawing.Size(80, 21);
            this.textBoxMSC.TabIndex = 25;
            // 
            // textBoxRNC
            // 
            this.textBoxRNC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxRNC.Location = new System.Drawing.Point(181, 149);
            this.textBoxRNC.Name = "textBoxRNC";
            this.textBoxRNC.ReadOnly = true;
            this.textBoxRNC.Size = new System.Drawing.Size(80, 21);
            this.textBoxRNC.TabIndex = 27;
            // 
            // textBoxCode
            // 
            this.textBoxCode.AcceptsReturn = true;
            this.textBoxCode.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxCode.Location = new System.Drawing.Point(308, 19);
            this.textBoxCode.Name = "textBoxCode";
            this.textBoxCode.ReadOnly = true;
            this.textBoxCode.Size = new System.Drawing.Size(80, 21);
            this.textBoxCode.TabIndex = 5;
            this.textBoxCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // textBoxName
            // 
            this.textBoxName.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxName.Location = new System.Drawing.Point(181, 19);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.ReadOnly = true;
            this.textBoxName.Size = new System.Drawing.Size(80, 21);
            this.textBoxName.TabIndex = 3;
            // 
            // textBoxSTime
            // 
            this.textBoxSTime.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxSTime.Location = new System.Drawing.Point(51, 45);
            this.textBoxSTime.Name = "textBoxSTime";
            this.textBoxSTime.ReadOnly = true;
            this.textBoxSTime.Size = new System.Drawing.Size(80, 21);
            this.textBoxSTime.TabIndex = 7;
            // 
            // textBoxUARFCNList
            // 
            this.textBoxUARFCNList.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxUARFCNList.Location = new System.Drawing.Point(83, 97);
            this.textBoxUARFCNList.Name = "textBoxUARFCNList";
            this.textBoxUARFCNList.ReadOnly = true;
            this.textBoxUARFCNList.Size = new System.Drawing.Size(311, 21);
            this.textBoxUARFCNList.TabIndex = 17;
            // 
            // textBoxUARFCN
            // 
            this.textBoxUARFCN.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxUARFCN.Location = new System.Drawing.Point(51, 70);
            this.textBoxUARFCN.Name = "textBoxUARFCN";
            this.textBoxUARFCN.ReadOnly = true;
            this.textBoxUARFCN.Size = new System.Drawing.Size(80, 21);
            this.textBoxUARFCN.TabIndex = 13;
            // 
            // textBoxPN
            // 
            this.textBoxPN.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxPN.Location = new System.Drawing.Point(181, 72);
            this.textBoxPN.Name = "textBoxPN";
            this.textBoxPN.ReadOnly = true;
            this.textBoxPN.Size = new System.Drawing.Size(80, 21);
            this.textBoxPN.TabIndex = 11;
            // 
            // textBoxETime
            // 
            this.textBoxETime.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxETime.Location = new System.Drawing.Point(181, 45);
            this.textBoxETime.Name = "textBoxETime";
            this.textBoxETime.ReadOnly = true;
            this.textBoxETime.Size = new System.Drawing.Size(80, 21);
            this.textBoxETime.TabIndex = 9;
            // 
            // textBoxID
            // 
            this.textBoxID.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxID.Location = new System.Drawing.Point(51, 19);
            this.textBoxID.Name = "textBoxID";
            this.textBoxID.ReadOnly = true;
            this.textBoxID.Size = new System.Drawing.Size(80, 21);
            this.textBoxID.TabIndex = 1;
            // 
            // buttonOK
            // 
            this.buttonOK.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right);
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(291, 294);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 23);
            this.buttonOK.TabIndex = 2;
            this.buttonOK.Text = "&OK";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // CDCellInfoForm
            // 
            this.AcceptButton = this.buttonOK;
            this.CancelButton = this.buttonOK;
            this.ClientSize = new System.Drawing.Size(424, 329);
            this.Controls.Add(groupBox1);
            this.Controls.Add(this.buttonOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CDCellInfoForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Cell Info";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        private readonly MainModel mainModel;

        private readonly CDCell cell;

        private TextBox textBoxID;

        private TextBox textBoxName;

        private TextBox textBoxCode;

        private TextBox textBoxSTime;

        private TextBox textBoxETime;

        private TextBox textBoxPN;

        private TextBox textBoxUARFCN;

        private TextBox textBoxUARFCNList;

        private TextBox textBoxType;

        private TextBox textBoxDirection;

        private TextBox textBoxMSC;

        private TextBox textBoxRNC;

        private TextBox textBoxNodeB;

        private TextBox textBoxLongitude;

        private TextBox textBoxLatitude;

        private ListBox listBoxAntenna;

        private Button buttonOK;
    }
}
