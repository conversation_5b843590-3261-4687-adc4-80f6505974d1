﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class ChangeFileProject : DIYSQLBase
    {
        readonly List<FileInfo> files;
        readonly List<FileInfo> curRecordFiles = new List<FileInfo>();
        string curFileids;
        public ChangeFileProject(List<FileInfo> files)
            : base(MainModel.GetInstance())
        {
            this.files = files;
        }
        FileProjectChangeCondition cond = null;

        protected override bool isValidCondition()
        {
            if (files == null || files.Count == 0)
            {
                MessageBox.Show("请选择文件！", this.Name, MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            //进行栅格统计需要使用Sam文件,<PERSON>文件路径是根据项目类型来匹配的,修改项目类型后Sam文件却不会移动
            //导致再进行栅格统计时找不到对应Sam文件,故未进行栅格统计或栅格统计失败的文件禁止修改项目类型
            bool flag = false;
            foreach (FileInfo file in files)
            {
                if (file.StatStatus < 3 || file.StatStatus == 103)
                {
                    flag = true;
                    break;
                }
            }
            if (flag)
            {
                MessageBox.Show("未进行栅格统计或栅格统计失败的文件禁止修改项目类型,请重新选择文件", this.Name, MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            FileProjectChangeSetForm setForm = new FileProjectChangeSetForm();
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            this.cond = setForm.GetCondition();
            return true;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在修改文件项目...", this.queryInThread);
        }

        private void queryInThread()
        {
            Dictionary<int, List<FileInfo>> cityFileDic = new Dictionary<int, List<FileInfo>>();
            foreach (FileInfo file in files)
            {
                List<FileInfo> fs = null;
                if (!cityFileDic.TryGetValue(file.DistrictID, out fs))
                {
                    fs = new List<FileInfo>();
                    cityFileDic[file.DistrictID] = fs;
                }
                fs.Add(file);
            }
            int curNum = 0;
            foreach (int cityID in cityFileDic.Keys)
            {
                List<FileInfo> fs = cityFileDic[cityID];
                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, cityID)
                        != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                    }

                    int step = 10;
                    for (int i = 0; i < fs.Count / step + 1; i++)
                    {
                        addCurFileids(fs, step, i);
                        curNum += curRecordFiles.Count;
                        WaitTextBox.Text = string.Format("{0}/{1} 正在修改文件项目...", curNum, files.Count);
                        if (curFileids.Length > 0)
                        {
                            curStep = stepType.change;
                            queryInThread(clientProxy);

                            curStep = stepType.record;
                            queryInThread(clientProxy);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
                finally
                {
                    clientProxy.Close();
                    WaitTextBox.Close();
                }
            }
        }

        private void addCurFileids(List<FileInfo> fs, int step, int i)
        {
            curRecordFiles.Clear();
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < step; j++)
            {
                int idx = step * i + j;
                if (idx >= fs.Count)
                {
                    break;
                }
                if (sb.Length > 0)
                {
                    sb.Append(",");
                }
                sb.Append(fs[idx].ID);
                curRecordFiles.Add(fs[idx]);
            }

            curFileids = sb.ToString();
        }

        enum stepType
        {
            change, record
        }
        stepType curStep = stepType.change;
        protected override string getSqlTextString()
        {
            StringBuilder sqlText = new StringBuilder();
            switch (curStep)
            {
                case stepType.change:
                    sqlText.AppendFormat("mc_sp_change_project_of_file '{0}',{1}"
                        , curFileids, cond.ProjectID);
                    break;
                case stepType.record:
                    foreach (FileInfo curFile in curRecordFiles)
                    {
                        string time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        sqlText.AppendFormat("exec sp_move_file_record '{0}', '{1}', '{2}', '{3}', '{4}', {5}, {6}, {7};"
                            , MainModel.User.LoginName, cond.Person, time,
                            cond.Reason, curFile.LogTable, curFile.ProjectID,
                            cond.ProjectID, curFile.ID);
                    }
                    break;
                default:
                    break;
            }
            return sqlText.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[1];
            types[0] = E_VType.E_Int;
            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    package.Content.GetParamInt();//num
                }
                else if (package.Content.Type == Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "修改文件项目类型"; }
        }
    }

}
