﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTConvergeEventToOrderDlg : BaseDialog
    {
        public ZTConvergeEventToOrderDlg()
        {
            InitializeComponent();
        }

        public SettingCondition Condition { get; set; } = new SettingCondition();

        private void btnPath_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Excel|*.xlsx;*.xls";
            openDlg.Title = "请选择事件列表Excel";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                txtImportPath.Text = openDlg.FileName;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (judgeValidSetting())
            {
                Condition.FilePath = txtImportPath.Text;
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("文件路径不能为空");
            }
        }

        private bool judgeValidSetting()
        {
            if (string.IsNullOrEmpty(txtImportPath.Text))
            {
                return false;
            }
            return true;
        } 

        public class SettingCondition
        {
            public string FilePath { get; set; }
        }
    }
}
