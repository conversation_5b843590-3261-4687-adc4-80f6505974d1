﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc.SiteCellInfo
{
    public partial class SiteCellResultForm : MinCloseForm
    {
        public SiteCellResultForm()
        {
            InitializeComponent();
            gv.DoubleClick += gv_DoubleClick;
        }

        void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            CellSiteInfo cellInfo = gv.GetRow(info.RowHandle) as CellSiteInfo;
            if (cellInfo == null)
            {
                return;
            }

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (cellInfo.Site == null)
            {
                mf.GoToView(cellInfo.BTSInfo.BTS.Longitude, cellInfo.BTSInfo.BTS.Latitude, 6000);
                return;
            }
            makeSureLayerVisible();
            layer.CellSiteInfo = cellInfo;
            DbRect rect = new DbRect(cellInfo.Cell.Longitude, cellInfo.Cell.Latitude
                , cellInfo.Site.CenterLng, cellInfo.Site.CenterLat);
            rect.x1 -= 0.01;
            rect.y1 -= 0.01;
            rect.x2 += 0.01;
            rect.y2 += 0.01;
            mf.GoToView(rect);
        }

        SiteCellLayer layer = null;
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            layer = mf.GetLayerBase(typeof(SiteCellLayer)) as SiteCellLayer;
        }

        public void FillData(List<CellSiteInfo> cellInfos)
        {
            this.gridCtrl.DataSource = cellInfos;
            this.gridCtrl.RefreshDataSource();
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            int maxCnt = 200000;
            if (gv.DataRowCount> 200000)
            {
                if (MessageBox.Show("数据量已超过" + maxCnt + "行，将导出CSV格式文件？", "提醒", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    SaveFileDialog dlg = new SaveFileDialog();
                    dlg.Filter = FilterHelper.Csv;
                    if (dlg.ShowDialog() == DialogResult.OK)
                    {
                        WaitTextBox.Show("", exportCsv, dlg.FileName);
                    }
                }
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(gv);
            }
        }

        private void exportCsv(object fileName)
        {
            FileStream fs = null;
            StreamWriter streamWriter = null;
            try
            {
                fs = new FileStream(fileName.ToString(), FileMode.Create, FileAccess.Write);
                streamWriter = new StreamWriter(fs, Encoding.UTF8);
                StringBuilder sb = new StringBuilder();
                for (int c = 0; c < gv.Columns.Count; c++)
                {
                    if (sb.Length > 0)
                    {
                        sb.Append(",");
                    }
                    sb.Append(gv.Columns[c].Caption);
                }
                streamWriter.WriteLine(sb.ToString());

                List<CellSiteInfo> dataSrc = gridCtrl.DataSource as List<CellSiteInfo>;
                int x = 1;
                int count = gv.DataRowCount;
                int bat = 2000;
                sb = new StringBuilder();
                foreach (CellSiteInfo item in dataSrc)
                {
                    sb.Append(item.CityName);
                    sb.Append(",");
                    sb.Append(item.CellName);
                    sb.Append(",");
                    sb.Append(item.ENodeBID);
                    sb.Append(",");
                    sb.Append(item.CellCode);
                    sb.Append(",");
                    sb.Append(item.GridName);
                    sb.Append(",");
                    sb.Append(item.CellCount);
                    sb.Append(",");
                    sb.Append(item.CellAvgDis);
                    sb.Append(",");
                    sb.Append(item.CellMinDis);
                    sb.Append(",");
                    sb.Append(item.SN);
                    if (item.Site == null)
                    {
                        sb.Append(",,,,");
                    }
                    else
                    {
                        sb.Append(",");
                        sb.Append(item.SiteName);
                        sb.Append(",");
                        sb.Append(item.SiteType);
                        sb.Append(",");
                        sb.Append(item.SiteDis);
                        sb.Append(",");
                        sb.Append(item.SiteCellCount);
                    }
                    sb.Append(Environment.NewLine);
                    if (x % bat == 0)
                    {
                        WaitTextBox.Text = string.Format("{0}/{1}", x, count);
                        streamWriter.Write(sb.ToString());
                        sb.Remove(0, sb.Length);
                    }
                    x++;
                }
                if (sb.Length > 0)
                {
                    streamWriter.Write(sb.ToString());
                }
            }
            finally
            {
                if (streamWriter != null)
                {
                    streamWriter.Close();
                }
                if (fs != null)
                {
                    fs.Close();
                }
            }
            WaitTextBox.Close();
        }

    }
}
