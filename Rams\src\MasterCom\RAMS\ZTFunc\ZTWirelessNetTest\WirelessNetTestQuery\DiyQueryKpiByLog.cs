﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public class DiyQueryKpiByLog : QueryKPIStatBase
    {
        readonly int districtID;
        readonly List<string> img;
        public List<KPIStatDataBase> DataList { get; set; }
        public List<KPIStatDataBase> EventList { get; set; }

        public DiyQueryKpiByLog(int districtID, List<string> img)
        : base(MainModel.GetInstance())
        {
            this.districtID = districtID;
            this.img = img;
        }

        public override string Name
        {
            get { return "按文件查询指标"; }
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.log;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            DataList = new List<KPIStatDataBase>();
            EventList = new List<KPIStatDataBase>();

            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;

            string imgTriadIDSet = getStatImgNeededTriadID();
            foreach (FileInfo file in condition.FileInfos)
            {
                WaitBox.ProgressPercent += 10;

                queryPeriodInfo(null, clientProxy, imgTriadIDSet, file);
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(img);
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
                package.Content.PrepareAddParam();
            }
        }

        protected override void preparePackageCondition(Package package, TimePeriod period, params object[] reservedParams)
        {
            FileInfo fi = reservedParams[1] as FileInfo;
            TimePeriod logTbPeriod;
            DateTime bTime = getFirstDateFromLogTbName(fi.LogTable);
            DateTime eTime = bTime.AddMonths(1).AddSeconds(-1);
            logTbPeriod = new TimePeriod(bTime, eTime);
            AddDIYPeriod(package, logTbPeriod);
            AddDIYFileID(package, fi.ID);
            AddDIYStatStatus(package);
            AddGeographicFilter(package);
        }

        private DateTime getFirstDateFromLogTbName(string strname)
        {
            string[] vec = strname.Split('_');
            if (vec.Length == 5)
            {
                int year;
                int month;
                int.TryParse(vec[3], out year);
                int.TryParse(vec[4], out month);
                return new DateTime(year, month, 1);
            }
            throw (new Exception("格式错误，时间获取失败！"));
        }

        protected void AddDIYFileID(Package package, int fileID)
        {
            package.Content.AddParam((byte)OpOptionDef.Equal);
            package.Content.AddParam("0,1,1");//fileid
            package.Content.AddParam("" + fileID);
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            DataList.Add(singleStatData);
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));

            EventList.Add(eventData);
        }
    }
}
