﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class IpDownloadForm : MinCloseForm
    {
        public IpDownloadForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(Dictionary<string, DownloadInfo> ipDownloadInfoDic)
        {
            List<DownloadInfo> downloadInfoList = new List<DownloadInfo>();
            foreach (string key in ipDownloadInfoDic.Keys)
            {
                if (ipDownloadInfoDic[key].DlValidTimes > 0) //统计有下载次数的记录
                    downloadInfoList.Add(ipDownloadInfoDic[key]);
            }

            gridControl1.DataSource = downloadInfoList;
            gridControl1.RefreshDataSource();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            //SaveFileDialog dlg = new SaveFileDialog();
            //dlg.Title = "导出IP下载统计表";
            //dlg.RestoreDirectory = true;
            //dlg.Filter = "Excel文件(*.xls)|*.xls";
            //if (dlg.ShowDialog() == DialogResult.OK)
            //{
            //    string filename = dlg.FileName;
            //    this.gridControl1.ExportToExcelOld(filename);
            //}
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl1);
            ExcelNPOIManager.ExportToExcel(exportList);
        }
    }
}
