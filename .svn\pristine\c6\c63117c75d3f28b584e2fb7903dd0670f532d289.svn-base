﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTPoorRSRQCell;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPoorRsrqCell : QueryPoorRsrqCellBase
    {
        protected override string themeName { get { return "LTE_TDD:RSRQ"; } }
        protected override string rsrpName { get { return "lte_RSRP"; } }
        protected override string sinrName { get { return "lte_SINR"; } }
        protected override string rsrqName { get { return "lte_RSRQ"; } }

        protected static readonly object lockObj = new object();
        private static QueryPoorRsrqCell intance = null;
        public static QueryPoorRsrqCell Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new QueryPoorRsrqCell();
                        }
                    }
                }
                return intance;
            }
        }

        protected QueryPoorRsrqCell()
            : base()
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
        }

        public QueryPoorRsrqCell(ServiceName serviceName)
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "低RSRQ小区"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22009, this.Name);//////
        }
    }

    public class QueryPoorRsrqCell_FDD : QueryPoorRsrqCell
    {
        protected override string themeName { get { return "LTE_FDD:RSRQ"; } }
        protected override string rsrpName { get { return "lte_fdd_RSRP"; } }
        protected override string sinrName { get { return "lte_fdd_SINR"; } }
        protected override string rsrqName { get { return "lte_fdd_RSRQ"; } }

        private static QueryPoorRsrqCell_FDD instance = null;
        public static QueryPoorRsrqCell_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryPoorRsrqCell_FDD();
                    }
                }
            }
            return instance;
        }

        public QueryPoorRsrqCell_FDD()
            : base()
        {

        }

        public override string Name
        {
            get { return "低RSRQ小区LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26065, this.Name);//////
        }
    }

    public class QueryPoorRsrqCell_FDD_VOLTE : QueryPoorRsrqCell_FDD
    {
        private static QueryPoorRsrqCell_FDD_VOLTE instance = null;
        public new static QueryPoorRsrqCell_FDD_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryPoorRsrqCell_FDD_VOLTE();
                    }
                }
            }
            return instance;
        }
        public QueryPoorRsrqCell_FDD_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD低RSRQ小区"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30018, this.Name);//////
        }
    }

    public class QueryPoorRsrqCell_NBIOTScan : QueryPoorRsrqCell
    {
        protected override string themeName { get { return "LTE_SCAN:LTESCAN_TopN_CELL_Specific_RSRQ"; } }

        public QueryPoorRsrqCell_NBIOTScan(ServiceName serviceName)
            : base(serviceName)
        {
        }

        protected override float? getRsrq(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSRQ", 0];
        }

        public override string Name
        {
            get { return "低RSRQ小区_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33021, this.Name);
        }
    }
}
