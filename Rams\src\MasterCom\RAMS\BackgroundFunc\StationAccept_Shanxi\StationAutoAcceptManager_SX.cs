﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public abstract class StationAutoAcceptManager_SX<T> where T : ICell
    {
        public bool HasFoundSrcCell { get; set; }
        public CellAcceptFileInfo_SX<T> AcceptFileInfo { get; set; }
        protected string cellNameKey { get; set; }

        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                cellNameKey = FileNameRuleHelper_SX.GetCellNameKeyByFile(fileInfo.Name);
                if (string.IsNullOrEmpty(cellNameKey))
                {
                    HasFoundSrcCell = false;
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("文件{0}名称中无法匹配到小区,请检查文件名格式!", fileInfo.Name));
                    return;
                }

                T targetCell = getTargetCell(fileManager);
                if (targetCell == null)
                {
                    HasFoundSrcCell = false;
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                    return;
                }

                HasFoundSrcCell = true;
                int testPointCount = fileManager.TestPoints.Count;
                if (testPointCount >= 0)
                {
                    fileInfo.EndTime = fileManager.TestPoints[testPointCount - 1].Time;
                }

                initAcceptFileInfo(fileInfo, targetCell);
                AcceptFileInfo.PointCount = fileManager.TestPoints.Count;

                FileNameKey key = FileNameRuleHelper_SX.GetFileNameKey(fileManager.FileName);
                doStatWithData(fileManager, key);
            }
            catch (Exception ee)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ee);
            }
        }

        public virtual T getTargetCell(DTFileDataManager fileManager)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                T cell = getTpSrcCell(tp);
                if (cell != null && fileManager.FileName.Contains(cell.Name))
                {
                    return cell;
                }
            }
            return default;
        }

        protected abstract T getTpSrcCell(TestPoint tp);

        protected abstract void initAcceptFileInfo(FileInfo fileInfo, T targetCell);

        protected abstract void doStatWithData(DTFileDataManager fileManager, FileNameKey key);
    }

    public class StationAutoAcceptManager_SX_LTE : StationAutoAcceptManager_SX<LTECell>
    {
        protected override void initAcceptFileInfo(FileInfo fileInfo, LTECell targetCell)
        {
            AcceptFileInfo = new CellAcceptFileInfo_SX_LTE(fileInfo, targetCell);
        }

        protected override void doStatWithData(DTFileDataManager fileManager, FileNameKey key)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell == null || cell.Token != AcceptFileInfo.Cell.Token)
                {
                    continue;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                {
                    AcceptFileInfo.Rsrp.Add((float)rsrp);
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr >= -50 && sinr <= 50)
                {
                    AcceptFileInfo.Sinr.Add((float)sinr);
                }

                float? speed = getPdcpSpeed(tp, key);
                if (speed != null)
                {
                    AcceptFileInfo.Speed.Add((float)speed);
                }
            }
        }

        #region 获取采样点小区
        protected override LTECell getTpSrcCell(TestPoint tp)
        {
            if (tp is LTETestPointDetail)
            {
                //由于目前存在电梯和地库文件,故匹配时小区名要能与文件名匹配
                int? eci = (int?)tp["lte_ECI"];
                List<LTECell> cells;
                if (eci != null)
                {
                    cells = CellManager.GetInstance().GetLTECellsByECI((int)eci);
                }
                else
                {
                    int? earfcn = (int?)tp["lte_EARFCN"];
                    int? pci = (int?)(short?)tp["lte_PCI"];
                    cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(tp.DateTime, earfcn, pci);
                }

                if (cells != null && cells.Count > 0)
                {
                    foreach (LTECell cell in cells)
                    {
                        if (cell.Name == cellNameKey)
                        {
                            return cell;
                        }
                    }
                }


                //cell = CellManager.GetInstance().GetNearestLTECellByTACCI(tp.DateTime, (int?)(ushort?)tp["lte_TAC"], (int?)tp["lte_ECI"]
                //    , tp.Longitude, tp.Latitude);

                //int? earfcn = (int?)tp["lte_EARFCN"];
                //int? pci = (int?)(short?)tp["lte_PCI"];

                //if (cell == null)
                //{
                //    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.FileName);
                //}
            }
            return null;
        }

        protected LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0 && !string.IsNullOrEmpty(fileName))
            {
                LTECell cell = getCellByName(time, fileName, cells);
                if (cell != null)
                {
                    return cell;
                }
            }
            return null;
        }

        private LTECell getCellByName(DateTime time, string fileName, List<LTECell> cells)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.ValidPeriod.Contains(time) && fileName.Contains(cell.Name))
                {
                    return cell;
                }
            }
            return null;
        }
        #endregion

        private float? getPdcpSpeed(TestPoint tp, FileNameKey key)
        {
            float? speed;
            if (key == FileNameKey.Download)
            {
                speed = (float?)(double?)tp["lte_PDCP_DL_Mb"];
            }
            else
            {
                speed = (float?)(double?)tp["lte_PDCP_UL_Mb"];
            }
            return speed;
        }
    }

    public class StationAutoAcceptManager_SX_NR : StationAutoAcceptManager_SX<NRCell>
    {
        protected override void initAcceptFileInfo(FileInfo fileInfo, NRCell targetCell)
        {
            AcceptFileInfo = new CellAcceptFileInfo_SX_NR(fileInfo, targetCell);
        }

        protected override void doStatWithData(DTFileDataManager fileManager, FileNameKey key)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                NRCell cell = getTpSrcCell(tp);
                if (cell == null || cell.Token != AcceptFileInfo.Cell.Token)
                {
                    continue;
                }

                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
                if (rsrp != null)
                {
                    AcceptFileInfo.Rsrp.Add((float)rsrp);
                }

                float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
                if (sinr != null)
                {
                    AcceptFileInfo.Sinr.Add((float)sinr);
                }

                double? speed = getPdcpSpeed(tp, key);
                if (speed != null)
                {
                    AcceptFileInfo.Speed.Add((float)speed);
                }
            }

            dealEvts(fileManager, key);
        }

        #region 获取采样点小区
        protected override NRCell getTpSrcCell(TestPoint tp)
        {
            NRCell cell = null;
            if (tp is TestPoint_NR)
            {
                int? tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
                long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);

                cell = CellManager.GetInstance().GetNearestNRCellByTACCI(tp.DateTime, tac, nci, tp.Longitude, tp.Latitude);
                if (cell == null)
                {
                    int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                    int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                    cell = getNRCellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.FileName);
                }
            }
            return cell;
        }

        protected NRCell getNRCellByEARFCNPCI(DateTime time, int? earfcn, int? pci, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            List<NRCell> cells = CellManager.GetInstance().GetNRCellListByARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0 && !string.IsNullOrEmpty(fileName))
            {
                NRCell cell = getCellByName(time, fileName, cells);
                if (cell != null)
                {
                    return cell;
                }
            }
            return null;
        }

        private NRCell getCellByName(DateTime time, string fileName, List<NRCell> cells)
        {
            foreach (NRCell cell in cells)
            {
                if (cell.ValidPeriod.Contains(time) && fileName.Contains(cell.Name))
                {
                    return cell;
                }
            }
            return null;
        }
        #endregion

        private double? getPdcpSpeed(TestPoint tp, FileNameKey key)
        {
            double? speed;
            if (key == FileNameKey.Download)
            {
                speed = NRTpHelper.NrTpManager.GetPdcpDLMb(tp);
            }
            else
            {
                speed = NRTpHelper.NrTpManager.GetPdcpULMb(tp);
            }
            return speed;
        }

        private void dealEvts(DTFileDataManager fileManager, FileNameKey key)
        {
            DealEvtFunc func = null;
            switch (key)
            {
                case FileNameKey.PingBig:
                case FileNameKey.PingSmall:
                    func += dealPingDelay;
                    break;
            }

            if (func != null)
            {
                func(fileManager.Events, key);
            }
        }

        private void dealPingDelay(List<Event> events, FileNameKey key)
        {
            var successEvts = new List<int>() { (int)NREventManager.Ping_Success };
            foreach (var evt in events)
            {
                if (successEvts.Contains(evt.ID))
                {
                    int delay = int.Parse(evt["Value1"].ToString());
                    if (key == FileNameKey.PingBig)
                    {
                        AcceptFileInfo.PingBig.Add(delay);
                    }
                    else if (key == FileNameKey.PingSmall)
                    {
                        AcceptFileInfo.PingSmall.Add(delay);
                    }
                }
            }
        }

        delegate void DealEvtFunc(List<Event> events, FileNameKey key);
    }
}
