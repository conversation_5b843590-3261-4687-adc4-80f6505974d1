﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakSINRRoadSettingDlg : BaseForm
    {
        public WeakSINRRoadSettingDlg(WeakSINRRoadCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(WeakSINRRoadCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numMaxValue.Value = (decimal)condition.MaxSINR;
            numMinValue.Value = (decimal)condition.MinRSRP;
            chkRSRP.Checked = condition.CheckRSRP;
            numMinDistance.Value = (decimal)condition.MinCoverRoadDistance;
            numMaxTPDistance.Value = (decimal)condition.Max2TPDistance;
            numWeakSINRPercent.Value = (decimal)condition.WeakSINRPercent;
            chkMinDistance.Checked = condition.CheckMinDistance;
            chkMinDuration.Checked = condition.CheckMinDuration;
            numMinDuration.Value = (decimal)condition.MinDuration;

            chkShowFDDPoint.Checked = condition.IsShowFDDPointFreq;
            //设置频点
            if (condition.ListFreqRange == null)
            {
                //默认FDD频点
                setDefaultFDDFreq();
            }
            else
            {
                listBoxControlFDDFreq.Items.Clear();
                foreach (FreqRange freq in condition.ListFreqRange)
                {
                    listBoxControlFDDFreq.Items.Add(freq);
                }
            }
        }

        /// <summary>
        /// 设置默认FDD频段
        /// </summary>
        private void setDefaultFDDFreq()
        {
            listBoxControlFDDFreq.Items.Clear();
            FreqRange freq = new FreqRange(1200, 1400);
            freq.Name = 1200 + "-" + 1400;
            spinEditStart.Value = 1200;
            spinEditEnd.Value = 1400;
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(1425, 1425);
            freq.Name = "  1425  ";
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(3532, 3532);
            freq.Name = "  3532  ";
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(3576, 3576);
            freq.Name = "  3576  ";
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(3657, 3707);
            freq.Name = 3657 + "-" + 3707;
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(3715, 3715);
            freq.Name = "  3715  ";
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(19200, 19400);
            freq.Name = 19200 + "-" + 19400;
            listBoxControlFDDFreq.Items.Add(freq);

            freq = new FreqRange(21657, 21707);
            freq.Name = 21657 + "-" + 21707;
            listBoxControlFDDFreq.Items.Add(freq);
        }

        /// <summary>
        /// 获得查询条件
        /// </summary>
        /// <returns></returns>
        public WeakSINRRoadCondition GetCondition()
        {
            WeakSINRRoadCondition condition = new WeakSINRRoadCondition();
            condition.MaxSINR = (float)numMaxValue.Value;
            condition.MinRSRP = (float)numMinValue.Value;
            condition.CheckRSRP = chkRSRP.Checked;
            condition.MinCoverRoadDistance = (double)numMinDistance.Value;
            condition.Max2TPDistance = (double)numMaxTPDistance.Value;
            condition.WeakSINRPercent = (double)numWeakSINRPercent.Value;
            condition.CheckMinDistance = chkMinDistance.Checked;
            condition.CheckMinDuration = chkMinDuration.Checked;
            condition.MinDuration = (double)numMinDuration.Value;

            condition.IsShowFDDPointFreq = chkShowFDDPoint.Checked;
            //FDD频段
            if (listBoxControlFDDFreq.Items.Count <= 0)
            {
                condition.ListFreqRange = null;
            }
            else
            {
                condition.ListFreqRange = new List<FreqRange>();
                foreach (FreqRange freq in listBoxControlFDDFreq.Items)
                {
                    condition.ListFreqRange.Add(freq);
                }
            }
            return condition;
        }

        private void chkMinDuration_CheckedChanged(object sender, EventArgs e)
        {
            numMinDuration.Enabled = chkMinDuration.Checked;
            if (!chkMinDuration.Checked)
            {
                chkMinDistance.Checked = true;
            }
        }

        private void chkMinDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMinDistance.Enabled = chkMinDistance.Checked;
            if (!chkMinDistance.Checked)
            {
                chkMinDuration.Checked = true;
            }
        }

        /// <summary>
        /// 增加频段
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAddFreBand_Click(object sender, EventArgs e)
        {
            FreqRange freq = new FreqRange((int)spinEditStart.Value, (int)spinEditEnd.Value);
            if (spinEditStart.Value == spinEditEnd.Value)
            {
                freq.Name = freq.FreqStart.ToString();
            }
            else
            {
                freq.Name = freq.FreqStart + "-" + freq.FreqEnd;
            }
            if (!freq.IsLegal())
            {
                XtraMessageBox.Show("频段输入有误！");
                return;
            }
            foreach (object o in listBoxControlFDDFreq.Items)
            {
                if (freq.Equals(o))
                {
                    return;
                }
            }
            btnOK.DialogResult = DialogResult.OK;
            listBoxControlFDDFreq.Items.Add(freq);
            listBoxControlFDDFreq.SelectedValue = freq;
        }

        /// <summary>
        /// 删除频段
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRemove_Click(object sender, EventArgs e)
        {
            if (listBoxControlFDDFreq.SelectedItem != null)
            {
                listBoxControlFDDFreq.Items.Remove(listBoxControlFDDFreq.SelectedItem);
            }
            if (listBoxControlFDDFreq.SelectedItem == null)
            {
                btnOK.DialogResult = DialogResult.None;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (chkShowFDDPoint.Checked && listBoxControlFDDFreq.Items.Count <= 0)
            {
                XtraMessageBox.Show("需至少设置一个频段！");
                btnOK.DialogResult = DialogResult.None;
            }
        }

        /// <summary>
        /// 是否显示FDD采样点占比
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void chkShowFDDPoint_CheckedChanged(object sender, EventArgs e)
        {
            if (chkShowFDDPoint.Checked)
            {
                btnAddFreBand.Enabled = true;
                btnRemove.Enabled = true;
                spinEditEnd.Enabled = true;
                spinEditStart.Enabled = true;
                listBoxControlFDDFreq.Enabled = true;
            }
            else
            {
                btnAddFreBand.Enabled = false;
                btnRemove.Enabled = false;
                spinEditEnd.Enabled = false;
                spinEditStart.Enabled = false;
                listBoxControlFDDFreq.Enabled = false;
            }
        }

        /// <summary>
        /// 选中的Item改变事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void listBoxControlFDDFreq_SelectedIndexChanged(object sender, EventArgs e)
        {
            FreqRange freq = listBoxControlFDDFreq.SelectedItem as FreqRange;
            if (freq != null)
            {
                spinEditStart.Value = freq.FreqStart;
                spinEditEnd.Value = freq.FreqEnd;
            }
        }
    }

}
