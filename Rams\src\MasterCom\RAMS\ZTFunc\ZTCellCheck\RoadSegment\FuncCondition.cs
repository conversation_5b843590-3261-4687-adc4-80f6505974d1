﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    public class FuncCondition
    {
        public FuncCondition()
        {
            WeakCover = new WeakCoverCondition();
            MultiCover = new MultiCoverCondition();
            PoorSINR = new PoorSINRCondition();
            CoverLap = new CoverLapCondition();
            OverHandover = new OverHandoverCondition();
            UltraSiteCondition = new UltraSiteCondition();
            FarCover = new FarCoverCondition();
            UltraCellByFile = true;
        }
        public UltraSiteCondition UltraSiteCondition
        {
            get;
            set;
        }
        public FarCoverCondition FarCover
        { get; set; }

        public WeakCoverCondition WeakCover
        {
            get;
            set;
        }
        public MultiCoverCondition MultiCover
        {
            get;
            set;
        }
        public PoorSINRCondition PoorSINR
        {
            get;
            set;
        }
        public CoverLapCondition CoverLap
        { get; set; }
        public OverHandoverCondition OverHandover
        { get; set; }


        public bool UltraCellByFile { get; set; }

        public string FileName { get; set; }
    }

    public abstract class SiglePointCondition
    {
        public abstract string Name { get; }
        public abstract bool Filter(IProblemData probData);
    }

    public class CoverLapCondition : SiglePointCondition
    {
        public override string Name
        {
            get { return "过覆盖"; }
        }
        public float CoverlapRSRPMin { get; set; } = -90;
        public double CvrDisFactorMax { get; set; } = 1.6f;
        public int CoverSiteNum { get; set; } = 3;
        internal bool IsCoverLap(float rsrp, double distance, double cellMaxDis)
        {
            return rsrp >= CoverlapRSRPMin && distance > cellMaxDis;
        }

        internal bool IsValid(CoverLapCell cell)
        {
            return !cell.Filter(Rate);
        }

        public double Rate { get; set; }

        public override bool Filter(IProblemData probData)
        {
            return false;
        }
    }

    public class OverHandoverCondition : SiglePointCondition
    {
        public override string Name
        {
            get { return "频繁切换"; }
        }
        public OverHandoverCondition()
        {
            HoCount = 3;
            Second = 15;
            Distance = 150;
        }
        public int HoCount
        {
            get;
            set;
        }
        public int Second
        {
            get;
            set;
        }
        public double Distance
        {
            get;
            set;
        }

        public override bool Filter(IProblemData probData)
        {
            return false;
        }
    }

    public class FarCoverCondition : SiglePointCondition
    {
        public override string Name
        {
            get { return "超远覆盖"; }
        }
        public FarCoverCondition()
        {
            RSRP = -105;
            TestPointNum = 15;
            Distance = 1500;
        }
        public float RSRP
        {
            get;
            set;
        }

        public int TestPointNum
        {
            get;
            set;
        }

        public double Distance
        {
            get;
            set;
        }

        public bool IsValidDistance(double dis)
        {
            return dis >= Distance;
        }

        public bool IsValid(float rsrp)
        {
            return rsrp > RSRP;
        }

        public override bool Filter(IProblemData probData)
        {
            if (!(probData is FarCoverCell))
            {
                return true;
            }
            FarCoverCell far = probData as FarCoverCell;
            return far.FarCoverPointNum < TestPointNum;
        }
    }


}
