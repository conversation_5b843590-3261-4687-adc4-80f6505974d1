﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public class GroupTemplate
    {
        public override string ToString()
        {
            if (string.IsNullOrEmpty(Name))
            {
                return string.Empty;
            }
            return Name;
        }
        public GroupTemplate(string name):this()
        {
            this.Name = name;
        }

        public GroupTemplate()
        {
            Options = new List<GroupIndicatorOption>();
            LogicType = ELogicalType.或;
            RangeColorSet = new List<DTParameterRangeColor>();
        }

        public string Name
        {
            get;
            set;
        }
        public ELogicalType LogicType
        {
            get;
            set;
        }

        public List<DTParameterRangeColor> RangeColorSet
        {
            get;
            set;
        }

        public List<GroupIndicatorOption> Options
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = this.Name;
                dic["LogicType"] = this.LogicType.ToString();
                List<object> list = new List<object>();
                foreach (GroupIndicatorOption col in this.Options)
                {
                    list.Add(col.Param);
                }
                dic["IndicatorOptions"] = list;

                list = new List<object>();
                foreach (DTParameterRangeColor rng in RangeColorSet)
                {
                    list.Add(rng.Params);
                }
                dic["RangeColorSet"] = list;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Name = value["Name"].ToString();
                LogicType = (ELogicalType)Enum.Parse(typeof(ELogicalType), value["LogicType"].ToString());
                List<object> list = value["IndicatorOptions"] as List<object>;
                foreach (object objParam in list)
                {
                    GroupIndicatorOption opt = new GroupIndicatorOption();
                    opt.Param = objParam as Dictionary<string, object>;
                    Options.Add(opt);
                }

                if (value.ContainsKey("RangeColorSet"))
                {
                    list = value["RangeColorSet"] as List<object>;
                    foreach (object item in list)
                    {
                        DTParameterRangeColor rng = new DTParameterRangeColor();
                        rng.Params = item as Dictionary<string, object>;
                        RangeColorSet.Add(rng);
                    }
                }
            }
        }

    }
}
