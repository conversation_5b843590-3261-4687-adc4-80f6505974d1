﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    class GSMComparePoorRxQualityRoad : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        protected List<FileInfo> p1Files = null;
        protected List<FileInfo> p2Files = null;
        protected List<string> roadgridIdxList = null;
        protected DbRect curRegionGridBounds = null;
        protected double curGridSpan = double.NaN;

        private static GSMComparePoorRxQualityRoad instance = null;
        protected GSMComparePoorRxQualityRoadCondition poorRxQualCondition = null;
        private List<GSMComparePoorRxQualityRoadGrid> poorRxQualOfPeriod1 = null;
        private List<GSMComparePoorRxQualityRoadGrid> poorRxQualOfPeriod2 = null;
        private List<GSMComparePoorRxQualityRoadGrid> repeatGrids = null;
        private List<GSMComparePoorRxQualityRoadGrid> newGrids = null;

        public static GSMComparePoorRxQualityRoad GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new GSMComparePoorRxQualityRoad();
                    }
                }
            }
            return instance;
        }

        protected GSMComparePoorRxQualityRoad()
            : base(MainModel.GetInstance())
        {
        }

        public override string Description
        {
            get
            {
                return "选择2个时间段分析文件，做质差路段的对比分析。";
            }
        }

        public override string Name
        {
            get { return "质差路段对比_GSM"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12079, this.Name);
        }

        protected override void getReadyBeforeQuery()
        {
            p1Files = new List<FileInfo>();
            p2Files = new List<FileInfo>();
            poorRxQualOfPeriod1 = new List<GSMComparePoorRxQualityRoadGrid>();
            poorRxQualOfPeriod2 = new List<GSMComparePoorRxQualityRoadGrid>();
            roadgridIdxList = new List<string>();
            repeatGrids = new List<GSMComparePoorRxQualityRoadGrid>();
            newGrids = new List<GSMComparePoorRxQualityRoadGrid>();
            curRegionGridBounds = getGridBounds(condition.Geometorys.RegionBounds);
        }

        /// <summary>
        /// 已经获取了2个时间段的所有文件信息，这里要先分析时间段1的文件，再分析时间段2的文件，从而对比计算
        /// </summary>
        protected override void analyseFiles()
        {
            try
            {
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (poorRxQualCondition.Period1.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L)) 
                        || poorRxQualCondition.Period1.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.EndTime * 1000L)))
                    {
                        p1Files.Add(fileInfo);//时间段1的文件
                    }
                    else if (poorRxQualCondition.Period2.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L)) 
                        || poorRxQualCondition.Period2.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.EndTime * 1000L)))
                    {
                        p2Files.Add(fileInfo);//时间段2的文件
                    }
                }
                clearDataBeforeAnalyseFiles();
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + MainModel.FileInfos.Count + "个...");
                }
                replayFiles(p1Files);//先分析时间段1的文件
                replayFiles(p2Files);
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 逐个回放对应的时间段文件
        /// </summary>
        /// <param name="files2replay">要回放的文件</param>
        protected void replayFiles(List<FileInfo> files2replay)
        {
            int iloop = 0;
            foreach (FileInfo fileInfo in files2replay)
            {
                if (MainModel.IsBackground)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析" + FuncType.ToString() +
                        SubFuncType.ToString() + "类" + Name + "，当前文件" + (++iloop) + "/" + files2replay.Count +
                        "个...文件名：" + fileInfo.Name);
                }
                else
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files2replay.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files2replay.Count);
                }
                if (filterFile(fileInfo))
                {
                    continue;
                }

                curAnaFileInfo = fileInfo;
                Condition.FileInfos.Clear();
                Condition.FileInfos.Add(fileInfo);
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                replay();//回放
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        /// <summary>
        /// 文件已回放，开始统计该文件
        /// </summary>
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                if (isFileInPeriod(fileDataManager.FileID, p1Files))//时段1的文件分析
                {
                    doWithFileOfPeriod(true, fileDataManager);
                }
                else if (isFileInPeriod(fileDataManager.FileID, p2Files))//时间段2的文件分析
                {
                    doWithFileOfPeriod(false, fileDataManager);
                }
            }
        }

        /// <summary>
        /// 判断某文件是否为某时间段内的文件
        /// </summary>
        /// <param name="fileId">要判断的文件ID</param>
        /// <param name="filesInPeroid">某时间段内的文件</param>
        /// <returns>在时间段内true;不在false</returns>
        protected bool isFileInPeriod(int fileId, List<FileInfo> filesInPeroid)
        {
            bool inThisPeriod = false;
            foreach (FileInfo fi in filesInPeroid)
            {
                if (fi.ID == fileId)
                {
                    inThisPeriod = true;
                    break;
                }
            }
            return inThisPeriod;
        }
        protected virtual int? get_RxQualSub(TestPoint tp)
        {
            return (int?)(byte?)tp["RxQualSub"]; 
        }
        protected virtual GSMComparePoorRxQualityRoadGrid getGridInstance(double gridSpan, DbRect regionGrdiBounds)
        {
            return new GSMComparePoorRxQualityRoadGrid(gridSpan, regionGrdiBounds);
        }
        protected virtual void doWithFileOfPeriod(bool isPeriod1File, DTFileDataManager fileMng)
        {
            List<TestPoint> testPointList = fileMng.TestPoints;
            GSMComparePoorRxQualityRoadGrid info = null;
            TestPoint prePoint = null;//前一点
            try
            {
                for (int i = 0; i < testPointList.Count; i++)
                {
                    TestPoint testPoint = testPointList[i];
                    if (!isValidTestPoint(testPoint))
                    {
                        savePoorRxQualInfo(isPeriod1File, info);
                        info = null;
                        continue;
                    }
                    if (isPeriod1File)
                    {//生成时间段1的测试路线栅格
                        makeRoadGridPath(testPoint);
                    }
                    int? rxQual = this.get_RxQualSub(testPoint);
                    if (!poorRxQualCondition.MatchPoorRxQuality(rxQual))//先作指标的判断，后作距离的判断
                    {//不符合设置的质差条件 
                        savePoorRxQualInfo(isPeriod1File, info);
                        info = null;
                    }
                    else//指标符合质差条件，还需要进行距离条件判断
                    {
                        info = judgeDistance(isPeriod1File, testPointList, info, prePoint, i, testPoint);
                    }
                    prePoint = testPoint;
                }
            }
            catch
            {
                //忽略
            }
        }

        private GSMComparePoorRxQualityRoadGrid judgeDistance(bool isPeriod1File, List<TestPoint> testPointList, GSMComparePoorRxQualityRoadGrid info, TestPoint prePoint, int i, TestPoint testPoint)
        {
            if (info == null)//质差路段开始
            {
                info = this.getGridInstance(curGridSpan, curRegionGridBounds);
                info.SetGridInfo(testPoint, 0);
                if (i == testPointList.Count - 1)//最后一采样点
                {
                    savePoorRxQualInfo(isPeriod1File, info);
                }
            }
            else
            {
                double dis = MathFuncs.GetDistance(prePoint.Longitude, prePoint.Latitude, testPoint.Longitude, testPoint.Latitude);
                if (poorRxQualCondition.MatchTestPointDistance(dis))
                {
                    //符合两采样点之间的距离门限
                    info.SetGridInfo(testPoint, dis);
                    if (i == testPointList.Count - 1)//最后一采样点
                    {
                        savePoorRxQualInfo(isPeriod1File, info);
                    }
                }
                else
                {//两采样点距离不符合，该点开始新的质差路段
                    savePoorRxQualInfo(isPeriod1File, info);
                    info = this.getGridInstance(curGridSpan, curRegionGridBounds);
                    info.SetGridInfo(testPoint, 0);
                }
            }

            return info;
        }

        protected override void doSomethingAfterAnalyseFiles()
        {
            foreach (GSMComparePoorRxQualityRoadGrid grid in poorRxQualOfPeriod2)
            {
                bool repeat = false;
                foreach (GSMComparePoorRxQualityRoadGrid gridBase in poorRxQualOfPeriod1)
                {
                    if (gridBase.CanMerge(grid))
                    {//有重叠
                        repeatGrids.Add(grid);
                        grid.SN = repeatGrids.Count;
                        repeat = true;
                        break;
                    }
                }
                if (!repeat)
                {//新的质差路段
                    newGrids.Add(grid);
                    grid.SN = newGrids.Count;
                }
            }
        }

        protected void savePoorRxQualInfo(bool isPeriod1File, GSMComparePoorRxQualityRoadGrid info)
        {
            if (info == null || !poorRxQualCondition.MatchRoadDistance(info.distance))
            {//不符合最小持续距离
                return;
            }
            if (isPeriod1File)
            {
                info.SN = poorRxQualOfPeriod1.Count + 1;
                info.GetResult();
                poorRxQualOfPeriod1.Add(info);
            }
            else
            {
                info.SN = poorRxQualOfPeriod2.Count + 1;
                info.GetResult();
                poorRxQualOfPeriod2.Add(info);
            }
        }

        protected void makeRoadGridPath(TestPoint tp)
        {
            string idxStr = getGridIdx(tp);
            if (!roadgridIdxList.Contains(idxStr))
            {
                roadgridIdxList.Add(idxStr);
            }
        }

        protected DbRect getGridBounds(DbRect queryBounds)
        {
            curGridSpan = poorRxQualCondition.RoadGridSpan / 10 * 0.0001;
            DbRect gridBounds = new DbRect();
            gridBounds.x1 = 0.0000001 * ((int)(queryBounds.x1 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000));
            //避免漏掉最右边栅格
            gridBounds.x2 = 0.0000001 * ((int)(queryBounds.x2 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000)) + curGridSpan;
            //避免漏掉最上面栅格
            gridBounds.y2 = 0.0000001 * ((int)(queryBounds.y2 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000)) + curGridSpan;
            gridBounds.y1 = 0.0000001 * ((int)(queryBounds.y1 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000));
            return gridBounds;
        }

        protected string getGridIdx(TestPoint tp)
        {
            double xDis = tp.Longitude - curRegionGridBounds.x1;
            int cAt = (int)(xDis / curGridSpan);
            double yDis = curRegionGridBounds.y2 - tp.Latitude;
            int rAt = (int)(yDis / curGridSpan);
            return rAt.ToString() + "_" + cAt.ToString();
        }

        protected bool isInRoadGridPath(TestPoint tp)
        {
            string idxStr = getGridIdx(tp);
            return roadgridIdxList.Contains(idxStr);
        }

        protected override void fireShowForm()
        {
            GSMComparePoorRxQualityRoadForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(GSMComparePoorRxQualityRoadForm).FullName) as GSMComparePoorRxQualityRoadForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new GSMComparePoorRxQualityRoadForm(MainModel);
            }
            frm.FillData(poorRxQualOfPeriod1, repeatGrids, newGrids);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            poorRxQualOfPeriod1 = new List<GSMComparePoorRxQualityRoadGrid>();
            repeatGrids = new List<GSMComparePoorRxQualityRoadGrid>();
            newGrids = new List<GSMComparePoorRxQualityRoadGrid>();
        }
        
        /// <summary>
        /// 弹出条件设置窗口，获取条件
        /// </summary>
        /// <returns></returns>
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                poorRxQualCondition = new GSMComparePoorRxQualityRoadCondition();
                return true;
            }
            GSMComparePoorRxQualityRoadSettingDlg conditionDlg = new GSMComparePoorRxQualityRoadSettingDlg(poorRxQualCondition);
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            poorRxQualCondition = conditionDlg.GetCondition();
            condition.Periods.Clear();
            condition.Periods.Add(poorRxQualCondition.Period1);
            condition.Periods.Add(poorRxQualCondition.Period2);
            return true;
        }
    }

    public class GSMComparePoorRxQualityRoadCondition
    {
        public int RxQual { get; set; } = 5;
        public double RoadDistance { get; set; } = 100;
        public double TestPointDistance { get; set; } = 20;
        public TimePeriod Period1 { get; set; }
        public TimePeriod Period2 { get; set; }
        public double RoadGridSpan { get; set; } = 50;

        public bool MatchPoorRxQuality(int? rxQual)
        {
            return rxQual != null && rxQual <= 7 && rxQual >= 0 && rxQual >= this.RxQual;
        }

        public bool MatchRoadDistance(double distance)
        {
            return distance >= RoadDistance;
        }

        public bool MatchTestPointDistance(double distance)
        {
            return distance <= TestPointDistance;
        }
    }
}
