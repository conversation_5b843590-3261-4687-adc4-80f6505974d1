﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellReverseQueryBase : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        protected static readonly object lockObj = new object();

        protected CellReverseDirSettingDlg_CP conditionDlg;
        public CellWrongDirCondition cellWrongCond { get; set; }

        protected Dictionary<ICell, TDCellWrongDir> cellDic;

        protected CellWrongBatch cellWrongBatch;

        protected ZTDIYCellReverseQueryBase(MainModel mainModel)
            : base(mainModel)
        {
            DateTime dtDay = DateTime.Now.Date.AddDays(-1);
            TimePeriod tPeriod = new TimePeriod(dtDay, dtDay.AddDays(2).AddMilliseconds(-1));
            cellWrongCond = new CellWrongDirCondition(-80, 100, 50, 50, 30, false, null, null);
            cellDic = new Dictionary<ICell, TDCellWrongDir>();
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDic.Clear();
            if (cellWrongCond.IsTwiceBatch)
            {
                condition.Periods = cellWrongCond.GetTimePeriods;
            }
            cellWrongBatch = CellWrongBatch.First;
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            if (conditionDlg == null)
            {
                conditionDlg = new CellReverseDirSettingDlg_CP();
            }

            conditionDlg.SetCondition(cellWrongCond);
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                cellWrongCond = conditionDlg.GetConditon();
                return true;
            }
            return false;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);

                        cellWrongBatch = CellWrongBatch.Second;
                    }
                }
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }

    public abstract class CellReverseQueryByFileBase : DIYAnalyseByCellBackgroundBaseByFile
    {//逐个文件分析，与ZTDIYCellReverseQueryBase相比较慢

        protected static readonly object lockObj = new object();
        protected CellReverseDirSettingDlg_CP conditionDlg;
        public CellWrongDirCondition cellWrongCond { get; set; }

        protected Dictionary<ICell, TDCellWrongDir> cellDic;
        protected CellWrongBatch cellWrongBatch;

        protected CellReverseQueryByFileBase(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterSampleByRegion = true;
            this.IncludeEvent = false;
            DateTime dtDay = DateTime.Now.Date.AddDays(-1);
            TimePeriod tPeriod = new TimePeriod(dtDay, dtDay.AddDays(2).AddMilliseconds(-1));
            cellWrongCond = new CellWrongDirCondition(-80, 100, 50, 50, 30, false, null, null);
            cellDic = new Dictionary<ICell, TDCellWrongDir>();
        }

        protected override void getReadyBeforeQuery()
        {
            cellDic.Clear();
            if (cellWrongCond.IsTwiceBatch)
            {
                condition.Periods = cellWrongCond.GetTimePeriods;
            }
            cellWrongBatch = CellWrongBatch.First;
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            if (conditionDlg == null)
            {
                conditionDlg = new CellReverseDirSettingDlg_CP();
            }
            conditionDlg.SetCondition(cellWrongCond);
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                cellWrongCond = conditionDlg.GetConditon();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    if (MainModel.IsBackground)
                    {
                        FileInfo fileInfo = file.GetFileInfo();
                        curAnaFileInfo = fileInfo == null ? curAnaFileInfo : fileInfo;
                    }
                    else
                    {
                        setCellWrongBatch();
                    }

                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithDTData(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void setCellWrongBatch()
        {
            if (cellWrongCond.IsTwiceBatch)
            {
                DateTime fileTime = JavaDate.GetDateTimeFromMilliseconds(curAnaFileInfo.BeginTime * 1000L);
                if (condition.Periods[0].Contains(fileTime))
                {
                    cellWrongBatch = CellWrongBatch.First;
                }
                else
                {
                    cellWrongBatch = CellWrongBatch.Second;
                }
            }
        }

        protected abstract void doWithDTData(TestPoint tp);
    }
}
