﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTTDLTECellCoverLapAnaSetForm : BaseForm
    {
        public ZTTDLTECellCoverLapAnaSetForm()
        {
            InitializeComponent();
        } 
        private void btnOK_Click(object sender, EventArgs e)
        {
            if ((int)numDistanceMin.Value > (int)numDistanceMax.Value)
            {
                MessageBox.Show("距离下限超过上限，请核查！");
                DialogResult = DialogResult.Retry;
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public void GetCondition(out ZTTDLTECellCoverLapAnaCondition condition)
        {
            condition = new ZTTDLTECellCoverLapAnaCondition();

            if (chkRxlevDiff.Checked)
            {
                condition.RxlevDiff = (int)numRxlevDiff.Value;
            }

            if (chkRxlevMin.Checked)
            {
                condition.RxlevMin = (int)numRxlevMin.Value;
            }

            condition.SiteCount = (int)numSiteCount.Value;
            condition.DisFactor = (float)numDisFactor.Value;

            if (cbxSampleCount.Checked)
            {
                int sampleCount = 0;
                int.TryParse(tbxSampleCount.Text.Trim(), out sampleCount);
                condition.FilterSampleCountMin = sampleCount;
            }

            if (cbxPercent.Checked)
            {
                int iv = 0;
                int.TryParse(tbxNumPercent.Text, out iv);
                condition.FilterPercentMin = 0.01f * iv;
            }

            if (chkDistance.Checked)
            {
                int minDist = 0;
                int maxDist = 0;

                int.TryParse(numDistanceMin.Value.ToString(), out minDist);
                int.TryParse(numDistanceMax.Value.ToString(), out maxDist);

                condition.FilterDistanceMin = minDist;
                condition.FilterDistanceMax = maxDist;
            }

            condition.IsByBand = chkFreqBand.Checked;
            condition.FreqBand = cbxFreqBand.SelectedItem.ToString();
        }

        private void cbxSampleCount_CheckedChanged(object sender, EventArgs e)
        {
            tbxSampleCount.Enabled = cbxSampleCount.Checked;
        }

        private void cbxPercent_CheckedChanged(object sender, EventArgs e)
        {
            tbxNumPercent.Enabled = cbxPercent.Checked;
        }

        private void chkDistance_CheckedChanged(object sender, EventArgs e)
        {
            numDistanceMin.Enabled = chkDistance.Checked;
            numDistanceMax.Enabled = chkDistance.Checked;
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            cbxFreqBand.Enabled = chkFreqBand.Checked;
        }

        private void chkRxlevDiff_CheckedChanged(object sender, EventArgs e)
        {
            numRxlevDiff.Enabled = chkRxlevDiff.Checked;
        }

        private void chkRxlevMin_CheckedChanged(object sender, EventArgs e)
        {
            numRxlevMin.Enabled = chkRxlevMin.Checked;
        }

        private void chkFreqBand_CheckStateChanged(object sender, EventArgs e)
        {
            cbxFreqBand.Enabled = chkFreqBand.Checked;
        }
    }
}