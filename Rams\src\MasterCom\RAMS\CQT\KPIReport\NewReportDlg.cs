﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.CQT
{
    public partial class NewReportDlg : BaseFormStyle
    {
        public NewReportDlg()
        {
            InitializeComponent();
        }

        private void radioGroupType_SelectedIndexChanged(object sender, EventArgs e)
        {
            radioGroupCarreerID2.Visible = labelControl4.Visible = radioGroupType.SelectedIndex == 1;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (textEditName.Text.Trim().Length==0)
            {
                XtraMessageBox.Show("报表名不能为空！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        public int ReportType
        {
            get { return radioGroupType.SelectedIndex; }
        }
        public int CarreeID1
        {
            get { return radioCarreerID.SelectedIndex + 1; }
        }
        public int CarreeID2
        {
            get { return radioGroupCarreerID2.SelectedIndex + 1; }
        }
        public string ReportName
        {
            get {return textEditName.Text; }
        }
    }
}
