<?xml version="1.0"?>
<Configs>
  <!--配置文件。 Author: Gene-->
  <!--！！！配置文件自动生成，请勿随便更改。！！！-->
  <Config name="EsColorConfig">
    <Item name="ConfigParames" typeName="IDictionary">
      <Item typeName="IDictionary" key="EventColorItems">
        <Item typeName="IDictionary" key="WCDMA未接通">
          <Item typeName="String" key="Name">WCDMA未接通</Item>
          <Item typeName="String" key="Des">[WCDMA_MO_CallFail]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="String" key="Expression">[高BLER]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[高BLER]</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
              <Item typeName="String" key="Name">高BLER</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">Ec/Io差</Item>
              <Item typeName="String" key="Expression">[Ec/Io差]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[Ec/Io差]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">弱覆盖</Item>
              <Item typeName="String" key="Expression">[弱覆盖]</Item>
              <Item typeName="Color" key="Color">255,192,0,0</Item>
              <Item typeName="String" key="Des">[弱覆盖]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">手机主动释放</Item>
              <Item typeName="String" key="Expression">[手机主动释放]</Item>
              <Item typeName="Color" key="Color">255,0,255,0</Item>
              <Item typeName="String" key="Des">[手机主动释放]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">网络侧释放</Item>
              <Item typeName="String" key="Expression">[网络侧释放]</Item>
              <Item typeName="Color" key="Color">255,255,128,128</Item>
              <Item typeName="String" key="Des">[网络侧释放]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[WCDMA_MO_CallFail]</Item>
              <Item typeName="Color" key="Color">255,0,255,255</Item>
              <Item typeName="String" key="Des">[WCDMA_MO_CallFail]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="WCDMA掉话">
          <Item typeName="String" key="Name">WCDMA掉话</Item>
          <Item typeName="String" key="Des">[WCDMA_MO_Drop]|[WCDMA_MT_Drop]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">下行干扰</Item>
              <Item typeName="String" key="Expression">[下行干扰]</Item>
              <Item typeName="Color" key="Color">255,255,128,128</Item>
              <Item typeName="String" key="Des">[下行干扰]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">高TxPower</Item>
              <Item typeName="String" key="Expression">[高TxPower]</Item>
              <Item typeName="Color" key="Color">255,0,0,255</Item>
              <Item typeName="String" key="Des">[高TxPower]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">邻区漏配</Item>
              <Item typeName="String" key="Expression">[邻区漏配]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[邻区漏配]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">高BLER</Item>
              <Item typeName="String" key="Expression">[高BLER]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[高BLER]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[WCDMA_MO_Drop]|[WCDMA_MT_Drop]</Item>
              <Item typeName="Color" key="Color">255,0,255,255</Item>
              <Item typeName="String" key="Des">[WCDMA_MO_Drop]|[WCDMA_MT_Drop]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="TD切换失败">
          <Item typeName="String" key="Name">TD切换失败</Item>
          <Item key="Des" />
          <Item typeName="IList" key="ColorItems" />
        </Item>
        <Item typeName="IDictionary" key="TD未接通">
          <Item typeName="String" key="Name">TD未接通</Item>
          <Item typeName="String" key="Des">[TD_MO_CallFail]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[TD_MO_CallFail]</Item>
              <Item typeName="Color" key="Color">255,0,192,192</Item>
              <Item typeName="String" key="Des">[TD_MO_CallFail]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="GSM掉话">
          <Item typeName="String" key="Name">GSM掉话</Item>
          <Item typeName="String" key="Des">[MODropCall]|[MTDropCall]|[MODropCall_NotNormal]|[MTDropCall_NotNormal]|[MODropCall_ReEstablish]|[MTDropCall_ReEstablish]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">测试设备问题</Item>
              <Item typeName="String" key="Expression">[设备断连或信令丢失]</Item>
              <Item typeName="Color" key="Color">255,192,0,0</Item>
              <Item typeName="String" key="Des">[设备断连或信令丢失]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">过覆盖</Item>
              <Item typeName="String" key="Expression">[过覆盖]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[过覆盖]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">切换不合理</Item>
              <Item typeName="String" key="Expression">[切换问题]</Item>
              <Item typeName="Color" key="Color">255,0,0,255</Item>
              <Item typeName="String" key="Des">[切换问题]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">弱覆盖</Item>
              <Item typeName="String" key="Expression">[弱覆盖]</Item>
              <Item typeName="Color" key="Color">255,255,128,255</Item>
              <Item typeName="String" key="Des">[弱覆盖]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">质差</Item>
              <Item typeName="String" key="Expression">[严重质差]|[持续严重质差]|[一般质差] </Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[严重质差]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">NoConnect</Item>
              <Item typeName="String" key="Expression">[NoConnect]</Item>
              <Item typeName="Color" key="Color">255,255,255,128</Item>
              <Item typeName="String" key="Des">[NoConnect]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[MODropCall]|[MTDropCall]|[MODropCall_NotNormal]|[MTDropCall_NotNormal]|[MODropCall_ReEstablish]|[MTDropCall_ReEstablish]</Item>
              <Item typeName="Color" key="Color">255,0,192,192</Item>
              <Item typeName="String" key="Des">[MODropCall]|[MTDropCall]|[MODropCall_NotNormal]|[MTDropCall_NotNormal]|[MODropCall_ReEstablish]|[MTDropCall_ReEstablish]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="GSM未接通">
          <Item typeName="String" key="Name">GSM未接通</Item>
          <Item typeName="String" key="Des">[MOBlockedCall]|[MO_NoConnect]|[疑似MOBlockCall]|[MOCallFail]|[MTCallFail]|[MTBlockedCall]|[MT_NoConnect]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">900重选至1800持续质差</Item>
              <Item typeName="String" key="Expression">[900重选至1800持续质差]</Item>
              <Item typeName="Color" key="Color">255,255,128,0</Item>
              <Item typeName="String" key="Des">[900重选至1800持续质差]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">CMServiceReject</Item>
              <Item typeName="String" key="Expression">[过滤-CMServiceReject]</Item>
              <Item typeName="Color" key="Color">255,128,255,255</Item>
              <Item typeName="String" key="Des">[过滤-CMServiceReject]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">主动释放</Item>
              <Item typeName="String" key="Expression">[过滤-主动释放]</Item>
              <Item typeName="Color" key="Color">255,128,255,255</Item>
              <Item typeName="String" key="Des">[过滤-主动释放]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">同位置的连续事件</Item>
              <Item typeName="String" key="Expression">[过滤-同位置的连续事件]</Item>
              <Item typeName="Color" key="Color">255,128,255,255</Item>
              <Item typeName="String" key="Des">[过滤-同位置的连续事件]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">被叫信令缺失</Item>
              <Item typeName="String" key="Expression">[被叫信令缺失]</Item>
              <Item typeName="Color" key="Color">255,255,0,255</Item>
              <Item typeName="String" key="Des">[被叫信令缺失]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">设备DSP错误</Item>
              <Item typeName="String" key="Expression">[设备DSP错误{被叫位置更新，RFPowerCapacityClass=3非法}]</Item>
              <Item typeName="Color" key="Color">255,0,0,255</Item>
              <Item typeName="String" key="Des">[设备DSP错误{被叫位置更新，RFPowerCapacityClass=3非法}]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">主叫主动释放</Item>
              <Item typeName="String" key="Expression">[主叫主动释放]</Item>
              <Item typeName="Color" key="Color">255,138,43,226</Item>
              <Item typeName="String" key="Des">[主叫主动释放]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">被叫位置更新</Item>
              <Item typeName="String" key="Expression">[被叫位置更新]|[被叫正常位置更新]|[被叫位置更新{BJ}]</Item>
              <Item typeName="Color" key="Color">255,255,255,128</Item>
              <Item typeName="String" key="Des">[被叫位置更新]|[被叫正常位置更新]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">弱覆盖</Item>
              <Item typeName="String" key="Expression">[弱覆盖]|[瞬间弱覆盖]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[弱覆盖]|[瞬间弱覆盖]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">质差</Item>
              <Item typeName="String" key="Expression">[持续严重质差]|[严重质差]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[持续严重质差]|[严重质差]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">CMServiceReject</Item>
              <Item typeName="String" key="Expression">[CMServiceReject]</Item>
              <Item typeName="Color" key="Color">255,255,192,203</Item>
              <Item typeName="String" key="Des">[CMServiceReject]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">NoConnect</Item>
              <Item typeName="String" key="Expression">[NoConnect]</Item>
              <Item typeName="Color" key="Color">255,255,255,128</Item>
              <Item typeName="String" key="Des">[NoConnect]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">设备断连或信令丢失</Item>
              <Item typeName="String" key="Expression">[设备断连或信令丢失]</Item>
              <Item typeName="Color" key="Color">255,128,128,128</Item>
              <Item typeName="String" key="Des">[设备断连或信令丢失]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">被叫质差</Item>
              <Item typeName="String" key="Expression">[被叫质差]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[被叫质差]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">起呼后无响应</Item>
              <Item typeName="String" key="Expression">[起呼后无响应]</Item>
              <Item typeName="Color" key="Color">255,46,139,87</Item>
              <Item typeName="String" key="Des">[起呼后无响应]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">主叫异常振铃</Item>
              <Item typeName="String" key="Expression">[主叫异常振铃]</Item>
              <Item typeName="Color" key="Color">255,127,255,0</Item>
              <Item typeName="String" key="Des">[主叫异常振铃]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">被叫收到短信</Item>
              <Item typeName="String" key="Expression">[被叫收到短信]</Item>
              <Item typeName="Color" key="Color">255,128,0,0</Item>
              <Item typeName="String" key="Des">[被叫收到短信]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">主叫TCH失败</Item>
              <Item typeName="String" key="Expression">[主叫TCH指配失败]</Item>
              <Item typeName="Color" key="Color">255,153,50,204</Item>
              <Item typeName="String" key="Des">[主叫TCH指配失败]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">被叫无响应</Item>
              <Item typeName="String" key="Expression">[被叫无响应]</Item>
              <Item typeName="Color" key="Color">255,0,128,128</Item>
              <Item typeName="String" key="Des">[被叫无响应]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[MOBlockedCall]|[MO_NoConnect]|[疑似MOBlockCall]|[MOCallFail]|[MTCallFail]|[MTBlockedCall]|[MT_NoConnect]</Item>
              <Item typeName="Color" key="Color">255,0,192,192</Item>
              <Item typeName="String" key="Des">[MOBlockedCall]|[MO_NoConnect]|[疑似MOBlockCall]|[MOCallFail]|[MTCallFail]|[MTBlockedCall]|[MT_NoConnect]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="CDMA切换失败">
          <Item typeName="String" key="Name">CDMA切换失败</Item>
          <Item key="Des" />
          <Item typeName="IList" key="ColorItems" />
        </Item>
        <Item typeName="IDictionary" key="CDMA掉话">
          <Item typeName="String" key="Name">CDMA掉话</Item>
          <Item typeName="String" key="Des">[CDMAMODropCall]|[CDMAMTDropCall]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">导频污染</Item>
              <Item typeName="String" key="Expression">[导频污染]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[导频污染]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">小区列表问题</Item>
              <Item typeName="String" key="Expression">[小区列表问题]</Item>
              <Item typeName="Color" key="Color">255,219,112,147</Item>
              <Item typeName="String" key="Des">[小区列表问题]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">搜索窗设置问题</Item>
              <Item typeName="String" key="Expression">[搜索窗设置过小]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[搜索窗设置过小]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">设备问题</Item>
              <Item typeName="String" key="Expression">[设备断连或信令缺失]</Item>
              <Item typeName="Color" key="Color">255,127,255,212</Item>
              <Item typeName="String" key="Des">[设备断连或信令缺失]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[CDMAMODropCall]|[CDMAMTDropCall]</Item>
              <Item typeName="Color" key="Color">255,0,128,128</Item>
              <Item typeName="String" key="Des">[CDMAMODropCall]|[CDMAMTDropCall]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="CDMA未接通">
          <Item typeName="String" key="Name">CDMA未接通</Item>
          <Item key="Des" />
          <Item typeName="IList" key="ColorItems" />
        </Item>
        <Item typeName="IDictionary" key="TD掉话">
          <Item typeName="String" key="Name">TD掉话</Item>
          <Item typeName="String" key="Des">[TD_MT_Drop]|[TD_MO_Drop]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[TD_MT_Drop]|[TD_MO_Drop]</Item>
              <Item typeName="Color" key="Color">255,0,192,192</Item>
              <Item typeName="String" key="Des">[TD_MT_Drop]|[TD_MO_Drop]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="WCDMA切换失败">
          <Item typeName="String" key="Name">WCDMA切换失败</Item>
          <Item typeName="String" key="Des">[WCDMA_HandoverFail_T2G]|[WCDMA_HandoverFail_IntraT]|[WCDMA_HandoverFail_Baton]|[WCDMA_HandoverFail_IntraG]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[WCDMA_HandoverFail_T2G]|[WCDMA_HandoverFail_IntraT]|[WCDMA_HandoverFail_Baton]|[WCDMA_HandoverFail_IntraG]</Item>
              <Item typeName="Color" key="Color">255,0,255,255</Item>
              <Item typeName="String" key="Des">[WCDMA_HandoverFail_T2G]|[WCDMA_HandoverFail_IntraT]|[WCDMA_HandoverFail_Baton]|[WCDMA_HandoverFail_IntraG]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="GSM切换失败">
          <Item typeName="String" key="Name">GSM切换失败</Item>
          <Item typeName="String" key="Des">[HandoverFailure]</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">手机记忆效应</Item>
              <Item typeName="String" key="Expression">[手机记忆效应]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[手机记忆效应]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">小区间同频干扰</Item>
              <Item typeName="String" key="Expression">[小区间同频干扰]</Item>
              <Item typeName="Color" key="Color">255,255,128,0</Item>
              <Item typeName="String" key="Des">[小区间同频干扰]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">TCH拥塞或载频故障</Item>
              <Item typeName="String" key="Expression">[TCH拥塞或载频故障]</Item>
              <Item typeName="Color" key="Color">255,192,0,0</Item>
              <Item typeName="String" key="Des">[TCH拥塞或载频故障]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">切向室内站</Item>
              <Item typeName="String" key="Expression">[切向室内站]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[切向室内站]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">质差</Item>
              <Item typeName="String" key="Expression">[持续严重质差]|[严重质差]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[持续严重质差]|[严重质差]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">弱覆盖</Item>
              <Item typeName="String" key="Expression">[弱覆盖]</Item>
              <Item typeName="Color" key="Color">255,255,0,255</Item>
              <Item typeName="String" key="Des">[弱覆盖]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">伴随掉话</Item>
              <Item typeName="String" key="Expression">[伴随掉话]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[伴随掉话]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="String" key="Expression">[HandoverFailure]</Item>
              <Item typeName="Color" key="Color">255,0,192,192</Item>
              <Item typeName="String" key="Des">[HandoverFailure]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="call attempt">
          <Item typeName="String" key="Name">call attempt</Item>
          <Item typeName="String" key="Des">call attempt</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">callattempt</Item>
              <Item typeName="String" key="Expression">[MOCallAttempt]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[MOCallAttempt]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="VOLTE掉话">
          <Item typeName="String" key="Name">VOLTE掉话</Item>
          <Item typeName="String" key="Des">VOLTE_Drop_Call</Item>
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">信令丢失掉话事件</Item>
              <Item typeName="String" key="Expression">([VOLTEMODropCall]&amp;[信令缺失掉话])|([VOLTEMTDropCall]&amp;[信令缺失掉话])</Item>
              <Item typeName="Color" key="Color">255,0,0,192</Item>
              <Item typeName="String" key="Des">([VOLTEMODropCall]&amp;[信令缺失掉话])|([VOLTEMTDropCall]&amp;[信令缺失掉话])</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">重注册掉话事件</Item>
              <Item typeName="String" key="Expression">([VOLTEMODropCall]&amp;[重注册掉话])|([VOLTEMTDropCall]&amp;[重注册掉话])|[VOLTEMOReregisterDropCall]|[VOLTEMTReregisterDropCall]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">([VOLTEMODropCall]&amp;[重注册掉话])|([VOLTEMTDropCall]&amp;[重注册掉话])|[VOLTEMOReregisterDropCall]|[VOLTEMTReregisterDropCall]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="111">
          <Item typeName="String" key="Name">111</Item>
          <Item key="Des" />
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">重注册掉话事件</Item>
              <Item typeName="String" key="Expression">([VOLTEMODropCall]|[VOLTEMTDropCall])&amp;[重注册掉话]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">([VOLTEMODropCall]|[VOLTEMTDropCall])&amp;[重注册掉话]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="VoLTE">
          <Item typeName="String" key="Name">VoLTE</Item>
          <Item key="Des" />
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">掉话</Item>
              <Item typeName="String" key="Expression">[VOLTEMODropCall]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[VOLTEMODropCall]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="volte drop">
          <Item typeName="String" key="Name">volte drop</Item>
          <Item key="Des" />
          <Item typeName="IList" key="ColorItems">
            <Item typeName="IDictionary">
              <Item key="Name" />
              <Item typeName="String" key="Expression">[VoLTEAudioMOBlockCall]|[VoLTEAudioMODropCall]|[VoLTEAudioMTBlockCall]|[VoLTEAudioMTDropCall]</Item>
              <Item typeName="Color" key="Color">255,255,0,0</Item>
              <Item typeName="String" key="Des">[VoLTEAudioMOBlockCall]|[VoLTEAudioMODropCall]|[VoLTEAudioMTBlockCall]|[VoLTEAudioMTDropCall]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">False</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name" />
              <Item typeName="String" key="Expression">[无线网问题]</Item>
              <Item typeName="Color" key="Color">255,255,255,0</Item>
              <Item typeName="String" key="Des">[无线网问题]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">False</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name" />
              <Item typeName="String" key="Expression">[弱覆盖]</Item>
              <Item typeName="Color" key="Color">255,165,42,42</Item>
              <Item typeName="String" key="Des">[弱覆盖]</Item>
              <Item typeName="Int32" key="Size">8</Item>
              <Item typeName="Boolean" key="IsChecked">True</Item>
            </Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Pretypes">
        <Item typeName="String">弱覆盖</Item>
        <Item typeName="String">持续严重质差</Item>
        <Item typeName="String">严重质差</Item>
        <Item typeName="String">切换问题</Item>
        <Item typeName="String">未知</Item>
        <Item typeName="String">瞬间弱覆盖</Item>
        <Item typeName="String">事件信令异常</Item>
        <Item typeName="String">正常接通释放</Item>
        <Item typeName="String">一般质差</Item>
        <Item typeName="String">异MSC切换</Item>
        <Item typeName="String">同MSC切换</Item>
        <Item typeName="String">掉话</Item>
        <Item typeName="String">异常试呼_滤掉</Item>
        <Item typeName="String">有信令丢失情况_没设置过滤</Item>
        <Item typeName="String">信令异常</Item>
        <Item typeName="String">过覆盖</Item>
        <Item typeName="String">室内覆盖外泄</Item>
        <Item typeName="String">鉴权后未接通-正常释放</Item>
        <Item typeName="String">网络侧释放</Item>
        <Item typeName="String">小区间同频干扰</Item>
        <Item typeName="String">载频故障</Item>
        <Item typeName="String">手机记忆效应</Item>
        <Item typeName="String">伴随掉话</Item>
        <Item typeName="String">同频点切换</Item>
        <Item typeName="String">设备断连或信令丢失</Item>
        <Item typeName="String">存在丢信令现象</Item>
        <Item typeName="String">长呼掉话</Item>
        <Item typeName="String">过滤-被叫同时掉话</Item>
        <Item typeName="String">丢信令前存在质差</Item>
        <Item typeName="String">900重选至1800持续质差</Item>
        <Item typeName="String">过滤-同位置的连续事件</Item>
        <Item typeName="String">过滤-主动释放</Item>
        <Item typeName="String">过滤-CMServiceReject</Item>
        <Item typeName="String">被叫正常位置更新</Item>
        <Item typeName="String">被叫位置更新</Item>
        <Item typeName="String">主动释放</Item>
        <Item typeName="String">Ec/Io差</Item>
        <Item typeName="String">高BLER</Item>
        <Item typeName="String">手机主动释放</Item>
        <Item typeName="String">邻区漏配</Item>
        <Item typeName="String">高TxPower</Item>
        <Item typeName="String">下行干扰</Item>
        <Item typeName="String">NoConnect</Item>
        <Item typeName="String">小区列表问题</Item>
        <Item typeName="String">设备断连或信令缺失</Item>
        <Item typeName="String">过滤</Item>
        <Item typeName="String">搜索窗设置过小</Item>
        <Item typeName="String">导频污染</Item>
        <Item typeName="String">IDLE下主服C2&lt;邻区C2</Item>
        <Item typeName="String">切向室内站</Item>
        <Item typeName="String">TCH拥塞或载频故障</Item>
        <Item typeName="String">过滤-被叫无响应</Item>
        <Item typeName="String">被叫位置更新(BJ)</Item>
        <Item typeName="String">被叫处于专有模式</Item>
        <Item typeName="String">分析异常</Item>
        <Item typeName="String">起呼后无响应</Item>
        <Item typeName="String">建议过滤</Item>
        <Item typeName="String">CMServiceReject</Item>
        <Item typeName="String">被叫无响应</Item>
        <Item typeName="String">设备DSP错误(被叫位置更新，RF Power Capacity Class=3非法)</Item>
        <Item typeName="String">被叫信令缺失</Item>
        <Item typeName="String">CMServiceAbort</Item>
        <Item typeName="String">主叫位置更新</Item>
        <Item typeName="String">主叫主动释放</Item>
        <Item typeName="String">被叫覆盖差</Item>
        <Item typeName="String">被叫质差</Item>
        <Item typeName="String">主叫异常振铃</Item>
        <Item typeName="String">noconnect主叫主动释放</Item>
        <Item typeName="String">Noconnect_其它</Item>
        <Item typeName="String">主叫TCH指配失败</Item>
        <Item typeName="String">被叫用户忙</Item>
        <Item typeName="String">被叫处于上次通话</Item>
        <Item typeName="String">被叫已接通</Item>
        <Item typeName="String">信令缺失</Item>
        <Item typeName="String">被叫收到短信</Item>
        <Item typeName="String">重注册掉话</Item>
        <Item typeName="String">信令缺失掉话</Item>
        <Item typeName="String">无线网问题</Item>
      </Item>
      <Item typeName="String" key="LastCategoryName">volte drop</Item>
    </Item>
  </Config>
</Configs>