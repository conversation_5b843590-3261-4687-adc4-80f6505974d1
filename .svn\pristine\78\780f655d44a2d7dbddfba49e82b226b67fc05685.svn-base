﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FartherCoverInfoForm : MinCloseForm
    {
        private List<FartherCoverInfo> fatherVec;
        public FartherCoverInfoForm()
            : base()
        {
            InitializeComponent();
        }

        public FartherCoverInfoForm(MainModel mModel)
            : base(mModel)
        {
            InitializeComponent();
        }

        public void FillData(List<FartherCoverInfo> fartherVec)
        {
            this.fatherVec = fartherVec;
            gridControl1.DataSource = fartherVec;
            gridControl1.RefreshDataSource();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] hRow = gridView1.GetSelectedRows();
            if (hRow.Length <= 0) return;
            FartherCoverInfo info = gridView1.GetRow(hRow[0]) as FartherCoverInfo;
            if (info == null) return;
            mModel.SelectedLTECell = info.TheCell;
            mModel.DTDataManager.Clear();
            foreach (TestPoint tp in info.TestPointVec)
            {
                mModel.DTDataManager.Add(tp);
            }
            mModel.FireDTDataChanged(null);
            refreshLayer(info);
        }

        private void refreshLayer(FartherCoverInfo info)
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null) return;
            FartherCoverLayer layer = (FartherCoverLayer)mf.GetCustomLayer(typeof(FartherCoverLayer));
            if (layer == null)
            {
                layer = new FartherCoverLayer(mf.GetMapOperation(), "超远覆盖图层");
                mf.AddTempCustomLayer(layer);
            }
            layer.FillData(info);
            layer.Invalidate();
            mModel.MainForm.GetMapForm().GetDTLayer().Invalidate();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView1);
            }
            catch
            {
                MessageBox.Show("导出到xls失败");
            }
        }
    }
}
