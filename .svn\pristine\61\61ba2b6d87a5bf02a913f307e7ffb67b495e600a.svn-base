﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanMod3IndexDrawItem : IGis
    {
        public ScanMod3IndexDrawItem(TestPoint testPoint)
        {
            this.TestPoint = testPoint;
        }

        public TestPoint TestPoint
        {
            get;
            private set;
        }

        public bool Within(DbRect rect)
        {
            return TestPoint.Within(rect);
        }

        public bool Selected
        {
            get { return this.TestPoint.Selected; }
            set { this.TestPoint.Selected = value; }
        }

        public double IndexValue
        {
            get;
            set;
        }

        public bool IsInvalid
        {
            get;
            set;
        }
    }

    public class ScanMod3IndexSubLegend : LegendGroupSubItem<ScanMod3IndexDrawItem>
    {
        public ScanMod3IndexSubLegend(float minValue, float maxValue, Color color, string desc)
        {
            ColorRange = new ColorRange(minValue, maxValue, color, desc);
            Desc = desc;
            Color = color;
            Path = SymbolManager.GetInstance().Paths[0];
        }

        public ScanMod3IndexSubLegend(Color invalidColor, string desc)
        {
            ColorRange = null;
            Desc = desc;
            Color = invalidColor;
            Path = SymbolManager.GetInstance().Paths[0];
        }

        public ColorRange ColorRange
        {
            get;
            set;
        }

        public override bool IsMatch(ScanMod3IndexDrawItem obj)
        {
            if (obj.IsInvalid && ColorRange == null)
            {
                return true;
            }
            else if (ColorRange == null)
            {
                return false;
            }

            return obj.IndexValue >= ColorRange.minValue && obj.IndexValue < ColorRange.maxValue;
        }

        public override void DrawOnListBox(ListBox listBox, DrawItemEventArgs e)
        {
            string text = string.Format("[{0}] ", Visible ? "√" : "  ");
            e.Graphics.DrawString(text, listBox.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y + 2);
            e.Graphics.FillRectangle(new SolidBrush(this.Color), e.Bounds.X + 25, e.Bounds.Y, 16, 16);
            e.Graphics.DrawString(this.Desc, listBox.Font, Brushes.Black, e.Bounds.X + 45, e.Bounds.Y);
        }
    }

    public class ScanMod3IndexLegend : LegendGroup<ScanMod3IndexDrawItem>
    {
        public override void DrawOnLayer(MapOperation map, Graphics graphics, PointF location, ScanMod3IndexDrawItem entity2Draw)
        {
            Color color;
            GraphicsPath path = null;

            if (!getDisplayStyle(entity2Draw, out color, out path))
            {
                return;
            }
            float radius = CustomDrawLayer.GetTestPointDisplayRatio(map.Scale) * 20 * (entity2Draw.Selected ? 2 : 1);
            Brush brush = new SolidBrush(color);
            graphics.TranslateTransform(location.X, location.Y);
            graphics.ScaleTransform(radius, radius);
            graphics.FillPath(brush, path);
            if (entity2Draw.Selected)
            {
                graphics.DrawPath(PenSelected, path);
            }
            graphics.ResetTransform();

            if (entity2Draw.Selected)
            {
                DrawFlyLine(map, graphics, entity2Draw);
                //MainModel.GetInstance().SelectedTestPoints.Add(entity2Draw.TestPoint);
            }
        }

        protected bool getDisplayStyle(ScanMod3IndexDrawItem drawItem, out Color color, out GraphicsPath shape)
        {
            color = Color.Empty;
            shape = null;
            if (Items == null)
            {
                return false;
            }
            foreach (LegendGroupSubItem<ScanMod3IndexDrawItem> item in Items)
            {
                if (item.IsMatch(drawItem))
                {
                    color = item.Color;
                    shape = item.Path;
                    return item.Visible;
                }
            }
            return false;
        }

        public bool GetDisplayStyle(ScanMod3IndexDrawItem drawItem, out Color color, out GraphicsPath shape)
        {
            return getDisplayStyle(drawItem, out  color, out  shape);
        }
        private void DrawFlyLine(MapOperation map, Graphics graphics, ScanMod3IndexDrawItem entity2Draw)
        {
            PointF pSrc;
            map.ToDisplay(new DbPoint(entity2Draw.TestPoint.Longitude, entity2Draw.TestPoint.Latitude), out pSrc);

            for (int i = 0; i < 12; ++i)
            {
                LTECell lteCell = entity2Draw.TestPoint.GetCell_LTEScan(i);
                if (lteCell != null)
                {
                    int modValue = lteCell.PCI % 3;
                    PointF pTar;
                    map.ToDisplay(new DbPoint(lteCell.EndPointLongitude, lteCell.EndPointLatitude), out pTar);
                    graphics.DrawLine(FlyLineColors[modValue], pSrc, pTar);
                }
            }
        }

        private static Pen PenSelected = new Pen(Color.Black, 2);

        private static Pen[] FlyLineColors = { new Pen(Color.Red, 2), new Pen(Color.Blue, 2), new Pen(Color.Green, 2), };
    }

    public class ScanMod3IndexLayer : LayerWithLegendBase<ScanMod3IndexDrawItem>
    {
        public ScanMod3IndexLayer(MapOperation mapOp, string name)
            : base(mapOp, name)
        {
            LoadDefaultLegend();
            MainModel.GetInstance().MainForm.LegendPanel.FillLegendEvent += FillLegengBox;
        }

        public override object Tag
        {
            get
            {
                return base.Tag;
            }
            set
            {
                base.Tag = value;
                if (Legends != null)
                {
                    foreach (LegendGroup<ScanMod3IndexDrawItem> item in Legends)
                    {
                        item.Tag = this.Tag;
                    }
                }
            }
        }

        public ScanMod3IndexLegend Legend
        {
            get;
            private set;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (Entitys2Draw == null || Entitys2Draw.Count == 0
            || Legends == null || Legends.Count == 0)
            {
                return;
            }

            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            updateRect.Inflate((int)(64 * ratio), (int)(64 * ratio));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);

            foreach (ScanMod3IndexDrawItem drawItem in Entitys2Draw)
            {
                if (drawItem.Within(dRect))
                {
                    PointF location = getDisplayLocation(drawItem);
                    foreach (LegendGroup<ScanMod3IndexDrawItem> item in Legends)
                    {
                        ScanMod3IndexLegend legend = item as ScanMod3IndexLegend;
                        legend.DrawOnLayer(Map, graphics, location, drawItem);
                    }
                }
            }
        }

        public override void Clear()
        {
            Legends.Clear();
            if (Entitys2Draw != null)
            {
                Entitys2Draw.Clear();
            }
        }

        public void SetLegend()
        {
            List<ColorRange> colorRanges = new List<ColorRange>();
            ScanMod3IndexSubLegend invalidLegend = new ScanMod3IndexSubLegend(Color.DarkGray, "无效点");
            
            foreach (LegendGroupSubItem<ScanMod3IndexDrawItem> item in Legend.Items)
            {
                ScanMod3IndexSubLegend subLegend = item as ScanMod3IndexSubLegend;
                if (subLegend.ColorRange == null)
                {
                    invalidLegend = subLegend;
                }
                else
                {
                    colorRanges.Add(subLegend.ColorRange);
                }
            }

            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(0f, 1f);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(colorRanges);
            dlg.InvalidatePointColor = invalidLegend.Color;
            if (DialogResult.OK != dlg.ShowDialog())
            {
                return;
            }

            Legend.Items.Clear();
            foreach (ColorRange colorRange in dlg.ColorRanges)
            {
                ScanMod3IndexSubLegend subLegend = new ScanMod3IndexSubLegend(colorRange.minValue, colorRange.maxValue, colorRange.color, colorRange.desInfo);
                Legend.Items.Add(subLegend);
            }
            Legend.Items.Add(new ScanMod3IndexSubLegend(dlg.InvalidatePointColor, "无效点"));

            MainModel.MainForm.GetMapForm().updateMap();
            MainModel.RefreshLegend();
        }

        public void Select(MapOperation2 mop2)
        {
            DbRect rect = mop2.GetRegion().Bounds;

            if (Entitys2Draw == null || Entitys2Draw.Count == 0)
            {
                return;
            }

            MainModel.ClearSelectedTestPoints();
            MainModel.DTDataManager.Clear();
            foreach (ScanMod3IndexDrawItem drawItem in Entitys2Draw)
            {
                drawItem.Selected = false;
            }
            foreach (ScanMod3IndexDrawItem drawItem in Entitys2Draw)
            {
                if (drawItem.Within(rect))
                {
                    drawItem.Selected = true;
                    MainModel.SelectedTestPoints.Add(drawItem.TestPoint);
                    MainModel.DTDataManager.Add(drawItem.TestPoint);
                    break;
                }
            }
        }

        protected override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            //MapForm.MapEventArgs ea = e as MapForm.MapEventArgs;
            //if (ea == null)
            //{
            //    return;
            //}
            //DbRect rect = ea.MapOp2.GetRegion().Bounds;

            //if (Entitys2Draw == null || Entitys2Draw.Count == 0)
            //{
            //    return;
            //}

            //MainModel.ClearSelectedTestPoints();
            //foreach (ScanMod3IndexDrawItem drawItem in Entitys2Draw)
            //{
            //    drawItem.Selected = false;
            //}
            //foreach (ScanMod3IndexDrawItem drawItem in Entitys2Draw)
            //{
            //    if (drawItem.Within(rect))
            //    {
            //        drawItem.Selected = true;
            //        //MainModel.SelectedTestPoints.Add(drawItem.TestPoint);
            //        break;
            //    }
            //}
        }

        private PointF getDisplayLocation(ScanMod3IndexDrawItem drawItem)
        {
            PointF pntF;
            Map.ToDisplay(new DbPoint(drawItem.TestPoint.Longitude, drawItem.TestPoint.Latitude), out pntF);
            return pntF;
        }

        private void LoadDefaultLegend()
        {
            ScanMod3IndexLegend legend = new ScanMod3IndexLegend();
            legend.Title = "模三干扰指数";
            this.Legend = legend;

            ScanMod3IndexSubLegend subLegend = new ScanMod3IndexSubLegend(0f, 0.2f, Color.Lime, "0 <= x < 0.2");
            legend.Items.Add(subLegend);

            subLegend = new ScanMod3IndexSubLegend(0.2f, 0.4f, Color.Aqua, "0.2 <= x < 0.4");
            legend.Items.Add(subLegend);

            subLegend = new ScanMod3IndexSubLegend(0.4f, 0.6f, Color.Magenta, "0.4 <= x < 0.6");
            legend.Items.Add(subLegend);

            subLegend = new ScanMod3IndexSubLegend(0.6f, 0.8f, Color.Red, "0.6 <= x < 0.8");
            legend.Items.Add(subLegend);

            subLegend = new ScanMod3IndexSubLegend(Color.DarkGray, "无效点");
            legend.Items.Add(subLegend);
        }

        private void FillLegengBox(object sender, EventArgs e)
        {
            if (!this.IsVisible)
            {
                return;
            }

            ListBox listBoxLegend = sender as ListBox;
            
            foreach (LegendGroup<ScanMod3IndexDrawItem> item in Legends)
            {
                ScanMod3IndexLegend legend = item as ScanMod3IndexLegend;
                listBoxLegend.Items.Add(legend);
                
                foreach (LegendGroupSubItem<ScanMod3IndexDrawItem> subItem in legend.Items)
                {
                    ScanMod3IndexSubLegend subLeg = subItem as ScanMod3IndexSubLegend;
                    listBoxLegend.Items.Add(subLeg);
                }
                listBoxLegend.Items.Add(string.Empty);
            }
        }
    }
}
