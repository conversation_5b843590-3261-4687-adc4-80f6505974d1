﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptManager_QH : MultiStationAutoAcceptManager
    {
        protected override List<AcpAutoKpiBase> getAcceptAnaList(LTEBTSType btsType)
        {
            List<AcpAutoKpiBase> acceptAnaList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptAnaList = new List<AcpAutoKpiBase>()
                {
                    new AcpAutoFtpDownloadGood_QH(),
                    new AcpAutoFtpUploadGood_QH(),
                    new AcpAutoFtpDownloadBad_QH(),
                    new AcpAutoFtpUploadBad_QH(),
                    new AcpAutoRrcRate(),
                    new AcpAutoAccRate(),
                    new AcpAutoErabRate(),
                    new AcpAuto34ReselectRate(),
                    new AcpAuto24ReselectRate(),
                    new AcpAutoCsfbInfo_QH(),
                    new AcpAutoSrvccRate(),
                    new AcpAutoVolteAudioRate(),
                    new AcpAutoVolteVideoRate()
                };
            }
            else
            {
                acceptAnaList = new List<AcpAutoKpiBase>() 
                {
                    new AcpAutoIndoorRrcRate(),
                    new AcpAutoIndoorAccRate(),
                    new AcpAutoIndoorErabRate(),
                    new AcpAutoCsfbInfo_QH(),
                    new AcpAutoSrvccRate(),
                    new AcpAutoVolteAudioRate(),
                    new AcpAutoVolteVideoRate(),
                    new AcpAutoIndoorLevelTestKpi(),
                    new AcpAutoIndoorCoverFloor()
                };
            }
            return acceptAnaList;
        }

        protected override void anaWithHandoverFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            LTEBTS bts = getFileTestBts(fileManager);//一个站一个系统内切换文件
            if (bts != null && bts.Cells.Count > 0)
            {
                AcpAutoInnerHandover handOverAna = new AcpAutoInnerHandover();
                doStatWithData(handOverAna, fileInfo, fileManager, bts.Cells[0]);
            }
            else
            {
                reportInfo(string.Format("文件{0}未找到目标基站", fileInfo.Name));
            }
        }
    }
    public class StationAcceptAutoSet_QH : StationAcceptAutoSetBase
    {
        public FtpSettings FtpSetInfo { get; set; } = new FtpSettings();
        public string PhoneTestDbName { get; set; }
    }
    public class StaionAcceptResultHelper
    {
        protected StaionAcceptResultHelper()
        {

        }

        public static OutDoorBtsAcceptInfo_QH GetOutDoorBtsResultByBgData(List<BackgroundResult> bgResultList
            , int enodeBid)
        {
            OutDoorBtsAcceptInfo_QH btsAcceptInfo = null;
            foreach (BackgroundResult bgResult in bgResultList)
            {
                if (bgResult.StrDesc != "室外")
                {
                    continue;
                }

                DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
                LTECell lteCell = CellManager.GetInstance().GetLTECell(bgResultTime, bgResult.LAC, bgResult.CI);
                if (lteCell == null || lteCell.BelongBTS.BTSID != enodeBid)
                {
                    reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
                    continue;
                }

                if (btsAcceptInfo == null)
                {
                    btsAcceptInfo = new OutDoorBtsAcceptInfo_QH(lteCell.BelongBTS);
                    btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgResultTime);
                }
                btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgResultTime);

                addResultInfo(btsAcceptInfo, bgResult, lteCell);
            }

            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static void addResultInfo(OutDoorBtsAcceptInfo_QH btsAcceptInfo, BackgroundResult bgResult, LTECell lteCell)
        {
            try
            {
                byte[] bytes = bgResult.GetImageValueBytes();
                Dictionary<uint, object> kpiDic = MasterCom.RAMS.NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                if (bgResult.FileName != null && bgResult.FileName.Contains("系统内切换"))
                {
                    btsAcceptInfo.AddHandOverKpiInfo(kpiDic);
                }
                else
                {
                    OutDoorCellAcceptInfo_QH cellAcceptInfo;
                    if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(lteCell.CellID, out cellAcceptInfo))
                    {
                        cellAcceptInfo = new OutDoorCellAcceptInfo_QH(lteCell);
                        btsAcceptInfo.CellsAcceptDic.Add(lteCell.CellID, cellAcceptInfo);
                    }
                    cellAcceptInfo.AddAcceptKpiInfo(kpiDic);
                }
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
        }

        public static InDoorBtsAcceptInfo_QH GetInDoorBtsResultByBgData(List<BackgroundResult> bgResultList
            , int enodeBid)
        {
            InDoorBtsAcceptInfo_QH btsAcceptInfo = null;
            foreach (BackgroundResult bgResult in bgResultList)
            {
                if (bgResult.StrDesc == "室外")
                {
                    continue;
                }

                DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
                LTECell lteCell = CellManager.GetInstance().GetLTECell(bgResultTime, bgResult.LAC, bgResult.CI);
                if (lteCell == null || lteCell.BelongBTS.BTSID != enodeBid)
                {
                    reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
                    continue;
                }

                if (btsAcceptInfo == null)
                {
                    btsAcceptInfo = new InDoorBtsAcceptInfo_QH(lteCell.BelongBTS);
                    btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgResultTime);
                }
                btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgResultTime);

                try
                {
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = MasterCom.RAMS.NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    InDoorCellAcceptInfo_QH cellAcceptInfo;
                    if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(lteCell.CellID, out cellAcceptInfo))
                    {
                        cellAcceptInfo = new InDoorCellAcceptInfo_QH(lteCell);
                        btsAcceptInfo.CellsAcceptDic.Add(lteCell.CellID, cellAcceptInfo);
                    }
                    cellAcceptInfo.AddAcceptKpiInfo(bgResult.FileName, kpiDic);
                }
                catch (Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }

            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }
        protected static void reportBackgroundInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        protected static void reportBackgroundError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
}
