﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRNCellLevelHigherForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvTP = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.columnHeaderName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderHostRSRP = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderNCellRSPR = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderDiff = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderLng = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlCell = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gvRes = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gvTP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvRes)).BeginInit();
            this.SuspendLayout();
            // 
            // gvTP
            // 
            this.gvTP.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.columnHeaderName,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeaderHostRSRP,
            this.gridColumn5,
            this.gridColumn6,
            this.columnHeaderNCellRSPR,
            this.columnHeaderDiff,
            this.gridColumn9,
            this.gridColumn8,
            this.columnHeaderLng,
            this.columnHeaderLat,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13});
            this.gvTP.GridControl = this.gridControlCell;
            this.gvTP.Name = "gvTP";
            this.gvTP.OptionsBehavior.Editable = false;
            this.gvTP.OptionsDetail.ShowDetailTabs = false;
            this.gvTP.OptionsView.ColumnAutoWidth = false;
            this.gvTP.OptionsView.ShowGroupPanel = false;
            this.gvTP.DoubleClick += new System.EventHandler(this.gvTP_DoubleClick);
            // 
            // columnHeaderName
            // 
            this.columnHeaderName.Caption = "小区名称";
            this.columnHeaderName.FieldName = "CellName";
            this.columnHeaderName.Name = "columnHeaderName";
            this.columnHeaderName.Visible = true;
            this.columnHeaderName.VisibleIndex = 0;
            this.columnHeaderName.Width = 103;
            // 
            // columnHeaderLAC
            // 
            this.columnHeaderLAC.Caption = "主服频点";
            this.columnHeaderLAC.FieldName = "EARFCN";
            this.columnHeaderLAC.Name = "columnHeaderLAC";
            this.columnHeaderLAC.Visible = true;
            this.columnHeaderLAC.VisibleIndex = 1;
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Caption = "主服PCI";
            this.columnHeaderCI.FieldName = "PCI";
            this.columnHeaderCI.Name = "columnHeaderCI";
            this.columnHeaderCI.Visible = true;
            this.columnHeaderCI.VisibleIndex = 2;
            // 
            // columnHeaderHostRSRP
            // 
            this.columnHeaderHostRSRP.Caption = "主服电平";
            this.columnHeaderHostRSRP.FieldName = "RSRP";
            this.columnHeaderHostRSRP.Name = "columnHeaderHostRSRP";
            this.columnHeaderHostRSRP.Visible = true;
            this.columnHeaderHostRSRP.VisibleIndex = 5;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "邻区频点";
            this.gridColumn5.FieldName = "NEARFCN";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 6;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "邻区PCI";
            this.gridColumn6.FieldName = "NPCI";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 7;
            // 
            // columnHeaderNCellRSPR
            // 
            this.columnHeaderNCellRSPR.Caption = "最强邻服电平";
            this.columnHeaderNCellRSPR.FieldName = "NRSRP";
            this.columnHeaderNCellRSPR.Name = "columnHeaderNCellRSPR";
            this.columnHeaderNCellRSPR.Visible = true;
            this.columnHeaderNCellRSPR.VisibleIndex = 10;
            this.columnHeaderNCellRSPR.Width = 90;
            // 
            // columnHeaderDiff
            // 
            this.columnHeaderDiff.Caption = "电平差";
            this.columnHeaderDiff.DisplayFormat.FormatString = "n2";
            this.columnHeaderDiff.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.columnHeaderDiff.FieldName = "RSRPDiff";
            this.columnHeaderDiff.Name = "columnHeaderDiff";
            this.columnHeaderDiff.Visible = true;
            this.columnHeaderDiff.VisibleIndex = 11;
            this.columnHeaderDiff.Width = 61;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "是否是高电平点";
            this.gridColumn9.FieldName = "IsHighRsrpPointDesc";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 12;
            this.gridColumn9.Width = 115;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "时间";
            this.gridColumn8.FieldName = "Time";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 13;
            this.gridColumn8.Width = 138;
            // 
            // columnHeaderLng
            // 
            this.columnHeaderLng.Caption = "经度";
            this.columnHeaderLng.FieldName = "Longitude";
            this.columnHeaderLng.Name = "columnHeaderLng";
            this.columnHeaderLng.Visible = true;
            this.columnHeaderLng.VisibleIndex = 14;
            this.columnHeaderLng.Width = 106;
            // 
            // columnHeaderLat
            // 
            this.columnHeaderLat.Caption = "纬度";
            this.columnHeaderLat.FieldName = "Latitude";
            this.columnHeaderLat.Name = "columnHeaderLat";
            this.columnHeaderLat.Visible = true;
            this.columnHeaderLat.VisibleIndex = 15;
            this.columnHeaderLat.Width = 113;
            // 
            // gridControlCell
            // 
            this.gridControlCell.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlCell.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gvTP;
            gridLevelNode1.RelationName = "TPResList";
            this.gridControlCell.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlCell.Location = new System.Drawing.Point(0, 0);
            this.gridControlCell.MainView = this.gvRes;
            this.gridControlCell.Name = "gridControlCell";
            this.gridControlCell.ShowOnlyPredefinedDetails = true;
            this.gridControlCell.Size = new System.Drawing.Size(952, 526);
            this.gridControlCell.TabIndex = 0;
            this.gridControlCell.UseEmbeddedNavigator = true;
            this.gridControlCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRes,
            this.gvTP});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gvRes
            // 
            this.gvRes.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colFileName,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn7});
            this.gvRes.GridControl = this.gridControlCell;
            this.gvRes.Name = "gvRes";
            this.gvRes.OptionsBehavior.Editable = false;
            this.gvRes.OptionsDetail.ShowDetailTabs = false;
            this.gvRes.OptionsView.ShowGroupPanel = false;
            this.gvRes.VertScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Always;
            this.gvRes.DoubleClick += new System.EventHandler(this.gvRes_DoubleClick);
            // 
            // colFileName
            // 
            this.colFileName.Caption = "文件名";
            this.colFileName.FieldName = "FileName";
            this.colFileName.Name = "colFileName";
            this.colFileName.Visible = true;
            this.colFileName.VisibleIndex = 1;
            this.colFileName.Width = 313;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "起始时间";
            this.gridColumn1.FieldName = "ErrorStartTime";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 170;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "持续距离";
            this.gridColumn2.FieldName = "Distance";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 168;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "持续时间";
            this.gridColumn3.FieldName = "Duration";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 173;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "邻区高电平占比";
            this.gridColumn4.FieldName = "Rate";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            this.gridColumn4.Width = 145;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "邻区高电平采样点数";
            this.gridColumn7.FieldName = "TPCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            this.gridColumn7.Width = 174;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "主服TAC";
            this.gridColumn10.FieldName = "TAC";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 3;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "主服NCI";
            this.gridColumn11.FieldName = "NCI";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 4;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "邻区TAC";
            this.gridColumn12.FieldName = "NTAC";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 8;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "邻区NCI";
            this.gridColumn13.FieldName = "NNCI";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 9;
            // 
            // NRNCellLevelHigherForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(952, 526);
            this.Controls.Add(this.gridControlCell);
            this.Name = "NRNCellLevelHigherForm";
            this.Text = "邻区强覆盖";
            ((System.ComponentModel.ISupportInitialize)(this.gvTP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvRes)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRes;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraGrid.Columns.GridColumn colFileName;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderName;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderLAC;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderCI;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderHostRSRP;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderNCellRSPR;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderDiff;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderLng;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderLat;
        private DevExpress.XtraGrid.Views.Grid.GridView gvTP;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
    }
}