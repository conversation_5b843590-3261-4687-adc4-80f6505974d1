﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakRxqualitySetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.spinEditRxlevMean = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditRxqual = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditStru = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditStruPercent = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditTotal900 = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditTotal1800 = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMean.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxqual.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStru.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStruPercent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal900.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal1800.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // spinEditRxlevMean
            // 
            this.spinEditRxlevMean.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMean.Location = new System.Drawing.Point(140, 16);
            this.spinEditRxlevMean.Name = "spinEditRxlevMean";
            this.spinEditRxlevMean.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxlevMean.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxlevMean.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlevMean.Properties.Mask.EditMask = "f0";
            this.spinEditRxlevMean.Size = new System.Drawing.Size(100, 20);
            this.spinEditRxlevMean.TabIndex = 0;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(30, 19);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(102, 12);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "最强与次强均值 ≤";
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(79, 215);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(165, 215);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // spinEditRxqual
            // 
            this.spinEditRxqual.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditRxqual.Location = new System.Drawing.Point(140, 46);
            this.spinEditRxqual.Name = "spinEditRxqual";
            this.spinEditRxqual.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxqual.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxqual.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxqual.Properties.Mask.EditMask = "f0";
            this.spinEditRxqual.Size = new System.Drawing.Size(100, 20);
            this.spinEditRxqual.TabIndex = 0;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(90, 49);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(42, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "质量 ≥";
            // 
            // spinEditStru
            // 
            this.spinEditStru.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            65536});
            this.spinEditStru.Location = new System.Drawing.Point(140, 136);
            this.spinEditStru.Name = "spinEditStru";
            this.spinEditStru.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditStru.Properties.Appearance.Options.UseFont = true;
            this.spinEditStru.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditStru.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.spinEditStru.Properties.Mask.EditMask = "f";
            this.spinEditStru.Size = new System.Drawing.Size(100, 20);
            this.spinEditStru.TabIndex = 0;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(42, 139);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(90, 12);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "道路结构指数 ≥";
            // 
            // spinEditStruPercent
            // 
            this.spinEditStruPercent.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditStruPercent.Location = new System.Drawing.Point(140, 166);
            this.spinEditStruPercent.Name = "spinEditStruPercent";
            this.spinEditStruPercent.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditStruPercent.Properties.Appearance.Options.UseFont = true;
            this.spinEditStruPercent.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditStruPercent.Properties.Mask.EditMask = "P0";
            this.spinEditStruPercent.Size = new System.Drawing.Size(100, 20);
            this.spinEditStruPercent.TabIndex = 0;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(18, 169);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(114, 12);
            this.labelControl4.TabIndex = 1;
            this.labelControl4.Text = "道路结构指数占比 ≥";
            // 
            // spinEditTotal900
            // 
            this.spinEditTotal900.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            0});
            this.spinEditTotal900.Location = new System.Drawing.Point(140, 76);
            this.spinEditTotal900.Name = "spinEditTotal900";
            this.spinEditTotal900.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditTotal900.Properties.Appearance.Options.UseFont = true;
            this.spinEditTotal900.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTotal900.Properties.Mask.EditMask = "f0";
            this.spinEditTotal900.Size = new System.Drawing.Size(100, 20);
            this.spinEditTotal900.TabIndex = 0;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(66, 79);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(66, 12);
            this.labelControl5.TabIndex = 1;
            this.labelControl5.Text = "900总频点数";
            // 
            // spinEditTotal1800
            // 
            this.spinEditTotal1800.EditValue = new decimal(new int[] {
            125,
            0,
            0,
            0});
            this.spinEditTotal1800.Location = new System.Drawing.Point(140, 106);
            this.spinEditTotal1800.Name = "spinEditTotal1800";
            this.spinEditTotal1800.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditTotal1800.Properties.Appearance.Options.UseFont = true;
            this.spinEditTotal1800.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTotal1800.Properties.Mask.EditMask = "f0";
            this.spinEditTotal1800.Size = new System.Drawing.Size(100, 20);
            this.spinEditTotal1800.TabIndex = 0;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(60, 109);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(72, 12);
            this.labelControl6.TabIndex = 1;
            this.labelControl6.Text = "1800总频点数";
            // 
            // WeakRxqualitySetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(262, 263);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.spinEditStruPercent);
            this.Controls.Add(this.spinEditStru);
            this.Controls.Add(this.spinEditTotal1800);
            this.Controls.Add(this.spinEditTotal900);
            this.Controls.Add(this.spinEditRxqual);
            this.Controls.Add(this.spinEditRxlevMean);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "WeakRxqualitySetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "弱覆盖条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMean.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxqual.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStru.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStruPercent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal900.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTotal1800.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit spinEditRxlevMean;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SpinEdit spinEditRxqual;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditStru;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit spinEditStruPercent;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinEditTotal900;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditTotal1800;
        private DevExpress.XtraEditors.LabelControl labelControl6;
    }
}