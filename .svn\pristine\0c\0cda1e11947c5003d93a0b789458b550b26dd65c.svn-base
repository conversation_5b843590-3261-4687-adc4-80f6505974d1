﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Data.SqlClient;
using MasterCom;
using System.Data;
using System.Threading;

namespace MasterCom.RAMS.ZTFunc
{
    public class Rule
    {
        public int id { get; set; }//--ID
        public string rulename { get; set; }//-- 规则名称
        public string content { get; set; }// -- 规则内容
        public int type { get; set; }//  --1 必须遵守 2 可选规则 3 不需要的规则

        public void Fill(SqlDataReader reader)
        {
            int idx = 0;
            this.id = reader.GetInt32(idx++);
            this.rulename = reader.GetString(idx++);
            this.content = reader.GetString(idx++);
            this.type = reader.GetInt32(idx);
        }
    }

    public class DiyQueryRuleFixDB
    {
        readonly string strConnDB;
        public List<Rule> RuleList { get; set; }

        public DiyQueryRuleFixDB(CityInfo curCity)
        {
            if (curCity != null)
            {
                strConnDB = curCity.GetDBConn();
            }
            RuleList = new List<Rule>();
        }

        public void Query()
        {
            WaitBox.Show("正在查询规则信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "select id,rulename,content,[type] from tb_cfg_rule";

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        Rule rule = new Rule();
                        rule.Fill(reader);
                        RuleList.Add(rule);
                    }
                }
            }
            finally
            {
                Thread.Sleep(10);
                WaitBox.Close();
            }
        }
    }
}
