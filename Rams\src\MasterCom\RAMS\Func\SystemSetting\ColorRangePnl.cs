﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MControls;
using MasterCom.RAMS.Func.GridQueryHistogram;

namespace MasterCom.RAMS.Func
{
    public partial class ColorRangePnl : UserControl
    {
        private float min = 0, max = 100;
        
        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();

        public ColorRangePnl()
        {
            InitializeComponent();
        }

        public void SetRange(float min, float max)
        {
            this.min = min;
            this.max = max;
        }

        public void FillData(List<ColorRange> colorRngs)
        {
            this.ColorRanges = colorRngs;
            rowCountChanged();
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (ColorRanges.Count == 0) return;
            if (e.RowIndex > ColorRanges.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = ColorRanges[e.RowIndex].minValue + " <= X < " + ColorRanges[e.RowIndex].maxValue;
            }
        }

        private void dataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (ColorRanges.Count == 0) return;
            if (e.ColumnIndex == 1)
            {
                e.CellStyle.BackColor = ColorRanges[e.RowIndex].color;
                e.CellStyle.SelectionBackColor = ColorRanges[e.RowIndex].color;
            }
            if (e.ColumnIndex == 2)
            {
                e.Value = ColorRanges[e.RowIndex].desInfo;
            }
        }

        private void dataGridView_DoubleClick(object sender, EventArgs e)
        {
            buttonModify_Click(null, null);
        }

        private void addRangeValue(ColorRange colorrange)
        {
            for (int i = 0; i < ColorRanges.Count; i++)
            {
                if (colorrange.minValue < ColorRanges[i].minValue)
                {
                    ColorRanges.Insert(i, colorrange);
                    return;
                }
            }
            ColorRanges.Add(colorrange);
        }

        private void rowCountChanged()
        {
            dataGridView.RowCount = ColorRanges.Count;
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                row.Height = 18;
            }
            dataGridView.Invalidate();
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        {
            ColorRange colorrange = null;
            colorrange = new ColorRange(min, max, Color.Green);
            ParameterSettingForm parSet = new ParameterSettingForm(colorrange, min, max);
            if (parSet.ShowDialog() == DialogResult.OK)
            {
                addRangeValue(parSet.ColorRange1);
                rowCountChanged();
            }
        }

        private void buttonDel_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0) return;
            ColorRanges.RemoveAt(dataGridView.SelectedRows[0].Index);
            rowCountChanged();
        }

        private void buttonModify_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                ColorRange colorrange = ColorRanges[dataGridView.SelectedRows[0].Index];
                ParameterSettingForm parSet = new ParameterSettingForm(colorrange, min, max);
                if (parSet.ShowDialog() == DialogResult.OK)
                {
                    ColorRanges.Remove(colorrange);
                    addRangeValue(parSet.ColorRange1);
                    rowCountChanged();
                    dataGridView.Invalidate();
                }
            }
        }

        private void buttonAuto_Click(object sender, EventArgs e)
        {
            AutoSettingForm auto = new AutoSettingForm(min, max);
            if (auto.ShowDialog() == DialogResult.OK)
            {
                ColorRanges.Clear();
                foreach (ColorRange colorrange in auto.ColorRanges)
                {
                    addRangeValue(colorrange);
                }
                rowCountChanged();
                dataGridView.Invalidate();
            }
        }
    }
}
