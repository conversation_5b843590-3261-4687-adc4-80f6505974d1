﻿using MasterCom.RAMS.Func;
using System;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNRLTECollaborativeAnaDlg : BaseDialog
    {
        public ZTNRLTECollaborativeAnaDlg(ZTNRLTECollaborativeAnaCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(ZTNRLTECollaborativeAnaCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            num4GBetterThan5G.Value = condition.Num4GBetterThan5G;
            num5GBetterThan4G.Value = condition.Num5GBetterThan4G;
            num4GWeakCover.Value = condition.Num4GWeakCover;
            num5GWeakCover.Value = condition.Num5GWeakCover;
            numMinDistance.Value = (decimal)condition.MinDistance;
            numMinDuration.Value = (decimal)condition.MinDuration;
            chkMinDistance.Checked = condition.CheckMinDistance;
            chkMinDuration.Checked = condition.CheckMinDuration;
        }

        public ZTNRLTECollaborativeAnaCondition GetCondition()
        {
            ZTNRLTECollaborativeAnaCondition condition = new ZTNRLTECollaborativeAnaCondition();
            condition.Num4GBetterThan5G = (int)num4GBetterThan5G.Value;
            condition.Num5GBetterThan4G = (int)num5GBetterThan4G.Value;
            condition.Num4GWeakCover = (int)num4GWeakCover.Value;
            condition.Num5GWeakCover = (int)num5GWeakCover.Value;
            condition.MinDistance = (double)numMinDistance.Value;
            condition.MinDuration = (double)numMinDuration.Value;
            condition.CheckMinDistance = chkMinDistance.Checked;
            condition.CheckMinDuration = chkMinDuration.Checked;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!chkMinDistance.Checked && !chkMinDuration.Checked)
            {
                MessageBox.Show(this, "请至少选择“持续距离”和“时长”中的一项！");
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
        }
    }

    public class ZTNRLTECollaborativeAnaCondition
    {
        public bool CheckMinDistance { get; set; } = true;
        public bool CheckMinDuration { get; set; }
        public double MinDuration { get; set; } = 10;
        public double MinDistance { get; set; } = 50;
        public int Num4GBetterThan5G { get; set; } = 20;
        public int Num5GBetterThan4G { get; set; } = 10;
        public int Num4GWeakCover { get; set; } = -95;
        public int Num5GWeakCover { get; set; } = -93;

        public int MaxTPDistance { get; set; } = 50;
        public int MinWeakPointPercent { get; set; } = 100;

        public bool CheckStayTime(double second)
        {
            if (CheckMinDuration)
            {
                return second >= MinDuration;
            }
            return true;
        }

        public bool CheckStayDistance(double dis)
        {
            if(CheckMinDistance)
            {
                return dis >= MinDistance;
            }
            return true;
        }
    }
}
