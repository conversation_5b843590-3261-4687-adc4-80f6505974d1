﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.AnyStat;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TAAnalyseSettingDlg : BaseDialog
    {
        List<ValueRange> ranges = new List<ValueRange>();
        public TAAnalyseSettingDlg()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            ValueRange range = new ValueRange();
            range.minValue = 0;
            range.maxValue = 2;
            ranges.Add(range);
            listRange.Items.Add(range);

            range = new ValueRange();
            range.minValue = 2;
            range.maxValue = 5;
            ranges.Add(range);
            listRange.Items.Add(range);

            range = new ValueRange();
            range.minValue = 5;
            range.maxValue = 100;
            ranges.Add(range);
            listRange.Items.Add(range);

            listRange_SelectedIndexChanged(null, null);
        }

        public List<ValueRange> GetValueRanges()
        {
            if (ranges.Count == 0)
            {
                init();
            }
            return ranges;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            DlgAddRange addRangeDlg = new DlgAddRange();
            if (addRangeDlg.ShowDialog() == DialogResult.OK)
            {
                ValueRange range = addRangeDlg.getResult();
                ranges.Add(range);
                listRange.Items.Add(range);
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            int index = listRange.SelectedIndex;
            ValueRange rangeOld = ranges[index];
            DlgAddRange addRangeDlg = new DlgAddRange(rangeOld.minValue, rangeOld.maxValue);
            if (addRangeDlg.ShowDialog() == DialogResult.OK)
            {
                ValueRange range = addRangeDlg.getResult();
                rangeOld.minValue = range.minValue;
                rangeOld.maxValue = range.maxValue;
                listRange.Items[index] = rangeOld;
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            int index = listRange.SelectedIndex;
            ranges.RemoveAt(index);
            listRange.Items.RemoveAt(index);
        }

        private void listRange_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnEdit.Enabled = listRange.SelectedIndex >= 0;
            btnDelete.Enabled = btnEdit.Enabled;
        }
    }
}
