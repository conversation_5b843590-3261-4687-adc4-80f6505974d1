﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlHandoverProblem : ReasonPanelBase
    {
        public ReasonPnlHandoverProblem()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            timePersistMax.ValueChanged -= timePersistMax_ValueChanged;
            timePersistMax.Value = (decimal)ZTWeakSINRReason.timePersist;
            timePersistMax.ValueChanged += timePersistMax_ValueChanged;
            timeLimitMax.ValueChanged -= timeLimitMax_ValueChanged;
            timeLimitMax.Value = (decimal)ZTWeakSINRReason.timeBeforeWeakSinr;
            timeLimitMax.ValueChanged += timeLimitMax_ValueChanged;
        }

        void timeLimitMax_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.timeBeforeWeakSinr = (int)timeLimitMax.Value;
        }
        void timePersistMax_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.timePersist = (int)timePersistMax.Value;
        }
    }
}
