﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause;
using MasterCom.Util;
using NPOI.HSSF.Record;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class CsfbFailureCauseQuery : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static CsfbFailureCauseQuery instance = null;
        public static CsfbFailureCauseQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new CsfbFailureCauseQuery();
                    }
                }
            }
            return instance;
        }

        protected CsfbFailureCauseQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }


        public override string Name
        {
            get
            {
                return "CSFB回落失败原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22040, this.Name);
        }

        protected CsfbFailureCondition funcCond;
        protected override bool getCondition()
        {
            CsfbFailureSettingDlg dlg = new CsfbFailureSettingDlg();
            dlg.Condition = funcCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return funcCond != null;
        }

        protected override void fireShowForm()
        {
            if (failures == null || failures.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            CsfbFailureCauseResultForm frm = MainModel.CreateResultForm(typeof(CsfbFailureCauseResultForm)) as CsfbFailureCauseResultForm;
            frm.FillData(failures);
            frm.Visible = true;
            frm.BringToFront();
            failures = null;
            filePairFailureDic = null;
            filePairMsgDic = null;
        }

        /// <summary>
        /// 已经获取了2个时间段的所有文件信息，这里要先分析时间段1的文件，再分析时间段2的文件，从而对比计算
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo , FileInfo> filePair = new Dictionary<FileInfo , FileInfo>();
            Dictionary<int , FileInfo> idDic = new Dictionary<int , FileInfo>();
            try
            {
                addFilePair(filePair, idDic);

                clearDataBeforeAnalyseFiles();
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + MainModel.FileInfos.Count
                                                                             + "个...");
                }
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in filePair)
                {
                    onePairEnd = false;
                    filePairFailureDic = new Dictionary<int, List<CsfbFailureEntity>>();
                    filePairMsgDic = new Dictionary<int, List<MasterCom.RAMS.Model.Message>>();
                    WaitBox.Text = "正在分析文件对(" + (++iloop) + "/" + filePair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / filePair.Count);

                    condition.FileInfos.Clear();
                    condition.FileInfos.Add(pair.Key);
                    filePairFailureDic[pair.Key.ID] = new List<CsfbFailureEntity>();
                    filePairMsgDic[pair.Key.ID] = new List<Message>();
                    if (pair.Value == null)
                    {//无对端文件
                        onePairEnd = true;
                    }
                    replay(); //回放主端
                    condition.FileInfos.Clear();
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                        filePairFailureDic[pair.Value.ID] = new List<CsfbFailureEntity>();
                        filePairMsgDic[pair.Value.ID] = new List<Message>();
                        onePairEnd = true;
                        replay(); //回放对端
                    }
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private void addFilePair(Dictionary<FileInfo, FileInfo> filePair, Dictionary<int, FileInfo> idDic)
        {
            List<FileInfo> mtFiles = new List<FileInfo>();

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                idDic[fileInfo.ID] = fileInfo;
                if (fileInfo.Momt != (int)MoMtFile.MtFlag)
                { //主叫为key，被叫可能为null
                    filePair[fileInfo] = null;
                }
                else
                {
                    mtFiles.Add(fileInfo);
                }
            }

            //被叫关联主叫，若未能关联上，则以被叫为key，value为null
            foreach (FileInfo mtFi in mtFiles)
            {
                FileInfo moFi;
                if (idDic.TryGetValue(mtFi.EventCount, out moFi))
                {
                    filePair[moFi] = mtFi;
                }
                else
                {
                    filePair[mtFi] = null;
                }
            }
        }

        protected bool onePairEnd = true;
        protected Dictionary<int, List<CsfbFailureEntity>> filePairFailureDic = null;
        protected Dictionary<int, List<MasterCom.RAMS.Model.Message>> filePairMsgDic = null;
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    int requestIdx = -1;
                    for(int i = 0 ; i < file.Events.Count ; i++)
                    {
                        Event evt = file.Events[i];
                        switch(evt.ID)
                        {
                            case 885: //MT CSFB request
                            case 877: //MO CSFB request
                                requestIdx = i;
                                break;
                            case 886://MT CSFB LTE Release
                                break;
                            case 882://MO CSFB Failure
                            case 890://MT CSFB Failure
                                if(requestIdx != -1)
                                {
                                    anaOneFailureCause(file, requestIdx, i);
                                    requestIdx = -1;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    filePairMsgDic[file.FileID] = file.Messages;
                }
                if(onePairEnd)
                {
                    anaBothSideFiles();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }


        protected void anaBothSideFiles()
        {
            if(filePairFailureDic.Count == 1)
            {//无对端文件
                foreach(List<CsfbFailureEntity> failureEntitys in filePairFailureDic.Values)
                {
                    foreach (CsfbFailureEntity failure in failureEntitys)
                    {
                        failure.OtherSideCause = "无对端文件";
                    }
                }
            }
            else
            {
                List<CsfbFailureEntity> moFailures, mtFailures;
                List<Message> mtMsgs;
                getFailusersInfo(out moFailures, out mtFailures, out mtMsgs);

                if (moFailures == null)
                {
                    return;
                }
                int lastMtIdx = 0;
                int lastMsgIdx = 0;
                foreach (CsfbFailureEntity failure in moFailures)
                {
                    bool findOther = false;
                    anaMtFailureEntity(mtFailures, failure, ref lastMtIdx, ref findOther);

                    if (!findOther)
                    { //对端无failure，开始分析对端信令是否有CSFB request
                        anaMtCsfbRequestMsg(mtMsgs, failure, ref lastMsgIdx, ref findOther);
                    }
                }

            }
        }

        private void getFailusersInfo(out List<CsfbFailureEntity> moFailures, out List<CsfbFailureEntity> mtFailures, out List<Message> mtMsgs)
        {
            //mt一定会在mo前面，分别主被叫文件的Failure集，然后联合分析
            moFailures = null;
            mtFailures = null;
            mtMsgs = null;
            foreach (int fileID in filePairFailureDic.Keys)
            {
                if (moFailures == null)
                {
                    moFailures = filePairFailureDic[fileID];
                }
                else
                {
                    mtFailures = filePairFailureDic[fileID];
                    mtMsgs = filePairMsgDic[fileID];
                }
            }
        }

        private void anaMtFailureEntity(List<CsfbFailureEntity> mtFailures, CsfbFailureEntity failure
            , ref int lastMtIdx, ref bool findOther)
        {
            if (mtFailures == null)
            {
                return;
            }
            for (; lastMtIdx < mtFailures.Count; lastMtIdx++)
            {
                CsfbFailureEntity other = mtFailures[lastMtIdx];
                if (other.RequestTime >= failure.RequestTime
                   && other.RequestTime <= failure.FailureTime)
                {
                    findOther = true;
                    failure.OtherSideCause = other.Cause.ToString();
                    other.OtherSideCause = failure.Cause.ToString();
                    break;
                }
                if (other.RequestTime > failure.FailureTime)
                { 
                    //被叫RequestTime大于当前主叫失败，跳出
                    break;
                }
            }
        }

        private void anaMtCsfbRequestMsg(List<Message> mtMsgs, CsfbFailureEntity failure
            , ref int lastMsgIdx, ref bool findOther)
        {
            for (; lastMsgIdx < mtMsgs.Count; lastMsgIdx++)
            {
                Message msg = mtMsgs[lastMsgIdx];
                if (msg.DateTime > failure.FailureTime)
                {
                    break;
                }
                if (msg.ID == (int)CSFBMsg.ExtendedServiceRequest &&
                   msg.DateTime >= failure.RequestTime)
                {
                    findOther = true;
                    failure.OtherSideCause = "正常";
                    break;
                }
            }
            if (!findOther)
            {
                failure.OtherSideCause = "对应时间段无CSFB Request";
            }
        }

        protected List<CsfbFailureEntity> failures = null;
        protected virtual void anaOneFailureCause(DTFileDataManager fi , int fromEvtIdx , int toEvtIdx)
        {
            CsfbFailureEntity failure = new CsfbFailureEntity(fi, fromEvtIdx, toEvtIdx, funcCond.ForwardSec);
            failure.Summary(funcCond);
            if (failures == null)
            {
                failures = new List<CsfbFailureEntity>();
            }
            filePairFailureDic[fi.FileID].Add(failure);
            failures.Add(failure);
        }

    }
    public class CsfbFailureCauseQuery_Fdd : CsfbFailureCauseQuery
    {
        private static CsfbFailureCauseQuery_Fdd instance = null;
        public new static CsfbFailureCauseQuery_Fdd GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new CsfbFailureCauseQuery_Fdd();
                    }
                }
            }
            return instance;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26075, this.Name);
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    int requestIdx = -1;
                    for (int i = 0; i < file.Events.Count; i++)
                    {
                        Event evt = file.Events[i];
                        switch (evt.ID)
                        {
                            case 3071: //MT CSFB request
                            case 3070: //MO CSFB request
                                requestIdx = i;
                                break;
                            case 3073://MT CSFB LTE Release
                                break;
                            case 3080://MO CSFB Failure
                            case 3081://MT CSFB Failure
                                if (requestIdx != -1)
                                {
                                    anaOneFailureCause(file, requestIdx, i);
                                    requestIdx = -1;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    filePairMsgDic[file.FileID] = file.Messages;
                }
                if (onePairEnd)
                {
                    anaBothSideFiles();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }
        protected override void anaOneFailureCause(DTFileDataManager fi, int fromEvtIdx, int toEvtIdx)
        {
            CsfbFailureEntity_Fdd failure = new CsfbFailureEntity_Fdd(fi, fromEvtIdx, toEvtIdx, funcCond.ForwardSec);
            failure.Summary(funcCond);
            if (failures == null)
            {
                failures = new List<CsfbFailureEntity>();
            }
            filePairFailureDic[fi.FileID].Add(failure);
            failures.Add(failure);
        }
    }
}
