﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public class PointStatusColoringReader
    {
        public PointStatusColoringReader()
        {
            StatusPointStyle style = new StatusPointStyle();
            style.Name = "已覆盖";
            style.PointSize = 20;
            style.Symbol = 0;
            style.FillColor = Color.Green;
            style.Visible = true;
            curStyles.Add(style);

            style = new StatusPointStyle();
            style.Name = "工程解决";
            style.Symbol = 1;
            style.FillColor = Color.Blue;
            style.PointSize = 20;
            style.Visible = true;
            curStyles.Add(style);

            style = new StatusPointStyle();
            style.Name = "未解决";
            style.Symbol = 2;
            style.FillColor = Color.Red;
            style.PointSize = 20;
            style.Visible = true;
            curStyles.Add(style);

            style = new StatusPointStyle();
            style.Name = "未知状态";
            style.Symbol = 2;
            style.FillColor = Color.Black;
            style.PointSize = 20;
            style.Visible = false;
            curStyles.Add(style);
        }

        public List<StatusPointStyle> StatusStyles
        {
            get { return curStyles; }
        }

        public virtual void ColorFile(string fileName)
        {
            curPoints.Clear();
            PointStatusColoringLayer.PointList.Clear();
            
            DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
            DataTable dt = null;
            foreach (DataTable table in ds.Tables)
            {
                if (table.Columns.Contains("经度") && table.Columns.Contains("纬度"))
                {
                    dt = table;
                    break;
                }
            }
            if (dt == null)
            {
                XtraMessageBox.Show("未找到包含'经度'和'纬度'列名的工作表", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            foreach (DataRow row in dt.Rows)
            {
                StatusPointItem curPoint = new StatusPointItem();
                curPoint.Style = curStyles[3]; // default style
                bool isValidRow = true;
                foreach (DataColumn col in dt.Columns)
                {
                    isValidRow = ProcessCell(row, col, curPoint);
                    if (!isValidRow)
                    {
                        break;
                    }
                }
                if (isValidRow)
                {
                    curPoints.Add(curPoint);
                }
            }

            PointStatusColoringLayer.PointList.AddRange(curPoints);
            Refresh();
        }

        public virtual void Refresh()
        {
            if (curPoints.Count == 0)
            {
                return;
            }

            MainModel.GetInstance().MainForm.GetMapForm().MakeSureCustomLayerVisible(PointStatusColoringLayer.Instance, false);
            MainModel.GetInstance().MainForm.GetMapForm().updateMap();
        }

        public virtual void Clear()
        {
            PointStatusColoringLayer.PointList.Clear();
            curPoints.Clear();
            MainModel.GetInstance().MainForm.GetMapForm().updateMap();
        }

        protected virtual bool ProcessCell(DataRow row, DataColumn col, StatusPointItem curPoint)
        {
            string value = row[col].ToString();
            string attrDesc = string.Format("{0}: {1}", col.ColumnName, value);
            curPoint.ShowAttributes.Add(attrDesc);

            if (col.ColumnName.IndexOf("经度") != -1)
            {
                string tmpValue = value.LastIndexOf("°") != -1 ? value.Substring(0, value.Length - 1) : value;
                double longitude;
                if (!double.TryParse(tmpValue, out longitude))
                {
                    return false;
                }
                curPoint.Longitude = longitude;
            }
            else if (col.ColumnName.IndexOf("纬度") != -1)
            {
                string tmpValue = value.LastIndexOf("°") != -1 ? value.Substring(0, value.Length - 1) : value;
                double latitude;
                if (!double.TryParse(tmpValue, out latitude))
                {
                    return false;
                }
                curPoint.Latitude = latitude;
            }
            else if (col.ColumnName.IndexOf("是否移动室分覆盖") != -1)
            {
                if (value == "已覆盖")
                {
                    curPoint.Style = curStyles[0];
                }
                else if (value == "未覆盖")
                {
                    curPoint.Style = curStyles[1];
                }
                else if (value.IndexOf("工程解决") != -1)
                {
                    curPoint.Style = curStyles[2];
                }
            }
            return true;
        }

        protected List<StatusPointStyle> curStyles = new List<StatusPointStyle>();
        protected List<StatusPointItem> curPoints = new List<StatusPointItem>();
    }
}
