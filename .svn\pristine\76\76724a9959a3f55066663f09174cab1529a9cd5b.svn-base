﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsOverlapCoverageRatioStater : NbIotMgrsStaterBase
    {
        private NbIotMgrsFuncItem tmpFuncItem;
        public NbIotMgrsOverlapCoverageRatioStater()
        {
         
        }
        public double GridSize
        {
            get
            {
                return NbIotMgrsBaseSettingManager.Instance.GridSize;
            }
        }
        public int Coverage { get; set; }
        public double CurRsrpMin { get; set; }
        public double RsrpDis { get; set; }
        public int SerialGridCount { get; set; }

        private string type = "";

        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override void SetResultControl()
        {
            NbIotMgrsOverlapCoverageRatioResult resultControl = new NbIotMgrsOverlapCoverageRatioResult();
            staterName = resultControl.Desc;
            object[] values = tmpFuncItem.FuncCondtion as object[];
            SerialGridCount = (int)values[0];
            Coverage = (int)values[1];
            CurRsrpMin = (double)values[2];
            RsrpDis = (double)values[3];
            resultControl.FillData(tmpFuncItem);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }

        public virtual List<CarrierCoverage> GetViews(string type)
        {
            this.type = type;
            resultList = new Dictionary<string, string>();
            List<CarrierCoverage> carrierList = new List<CarrierCoverage>();
            CarrierCoverage cm = getSerialGridViews(CarrierType.ChinaMobile, cmCarrierAreaResult);
            carrierList.Add(cm);

            CarrierCoverage cu = getSerialGridViews(CarrierType.ChinaUnicom, cuCarrierAreaResult);
            carrierList.Add(cu);

            CarrierCoverage ct = getSerialGridViews(CarrierType.ChinaTelecom, ctCarrierAreaResult);
            carrierList.Add(ct);

            return carrierList;
        }

        protected CarrierCoverage getSerialGridViews(CarrierType carierType, CarrierAreaResult areaResult)
        {
            CarrierCoverage carrierData = new CarrierCoverage();
            switch (carierType)
            {
                case CarrierType.ChinaMobile:
                    carrierData.Name = "中国移动";
                    break;
                case CarrierType.ChinaUnicom:
                    carrierData.Name = "中国联通";
                    break;
                case CarrierType.ChinaTelecom:
                    carrierData.Name = "中国电信";
                    break;
            }

            List<SerialOverlapCoverage> serialGridViews = dealWithData(areaResult.GridList, carierType);
            carrierData.AreaGridViews = getGridArea(serialGridViews);
            addResultInfo(carrierData.AreaGridViews, areaResult);
            return carrierData;
        }

        protected virtual void addResultInfo(List<AreaOverlapGridData> areaDataList, CarrierAreaResult carrierAreaResult)
        {
            foreach (var area in areaDataList)
            {
                foreach (var item in carrierAreaResult.carrierResult.AreaList)
                {
                    if (item.AreaName == area.AreaName)
                    {
                        item.SerialOverlapCoverage = carrierAreaResult.getAreaResultData(area.AreaName, area.IssuesGridCount);
                        break;
                    }
                }
            }

            carrierAreaResult.carrierResult.SerialOverlapCoverage = carrierAreaResult.getTotalResultData(serialWeakGridCount);
        }

        protected virtual List<SerialOverlapCoverage> dealWithData(Dictionary<string, List<ScanGridInfo>> gridList, CarrierType carrierType)
        {
            List<CoverageRegion> coverageRegionList;

            totalGridCount = gridList.Count;
            coverageRegionList = GetCoverageRegionList(gridList);

            NbIotMgrsSerialGridFinder<CoverageRegion> finder = new NbIotMgrsSerialGridFinder<CoverageRegion>(GridSize);
            Dictionary<int, List<CoverageRegion>> idItemsDic = finder.SerialGridFinder(coverageRegionList, SerialGridCount != 1);

            serialWeakGridCount = 0d;
            List<SerialOverlapCoverage> serialOverlapCoverageList = new List<SerialOverlapCoverage>();
            //对结果处理
            foreach (var item in idItemsDic.Values)
            {
                if (item.Count >= SerialGridCount)
                {
                    SerialOverlapCoverage serialOverlapGrid = new SerialOverlapCoverage();
                    serialOverlapGrid.GridViews = item;
                    serialOverlapGrid.SetFileNameByFileID(MainModel.FileInfos);
                    serialOverlapCoverageList.Add(serialOverlapGrid);
                    serialWeakGridCount += item.Count;
                }
            }
            return serialOverlapCoverageList;
        }

        public virtual List<AreaOverlapGridData> getGridArea(List<SerialOverlapCoverage> serialOverlapGridList)
        {
            List<AreaOverlapGridData> areaGridDataList = new List<AreaOverlapGridData>();
            Dictionary<string, List<SerialOverlapCoverage>> gridList = new Dictionary<string, List<SerialOverlapCoverage>>();
            foreach (SerialOverlapCoverage serialOverlapGrid in serialOverlapGridList)
            {
                if (serialOverlapGrid.GridViews.Count > 0)
                {
                    //连续栅格的第一个栅格属于那个区域,该结果就分给哪个区域
                    CoverageRegion overlapGrid = serialOverlapGrid.GridViews[0];
                    addGridArea(gridList, serialOverlapGrid, overlapGrid);
                }
            }

            foreach (var item in gridList)
            {
                AreaOverlapGridData AreaOverlapGridData = new AreaOverlapGridData();
                AreaOverlapGridData.AreaName = item.Key;
                AreaOverlapGridData.SerialGridViews = item.Value;
                areaGridDataList.Add(AreaOverlapGridData);
            }

            return areaGridDataList;
        }

        private void addGridArea(Dictionary<string, List<SerialOverlapCoverage>> gridList, SerialOverlapCoverage serialOverlapGrid, CoverageRegion overlapGrid)
        {
            foreach (MTPolygon polygon in selectPolygons)
            {
                if (polygon.CheckPointInRegion(overlapGrid.CentLng, overlapGrid.CentLat))
                {
                    if (!gridList.ContainsKey(polygon.Name))
                    {
                        gridList[polygon.Name] = new List<SerialOverlapCoverage>() { serialOverlapGrid };
                    }
                    else
                    {
                        gridList[polygon.Name].Add(serialOverlapGrid);
                    }
                    break;
                }
            }
        }

        protected virtual List<CoverageRegion> GetCoverageRegionList(Dictionary<string, List<ScanGridInfo>> gridList)
        {
            List<CoverageRegion> coverageRegionList = new List<CoverageRegion>();
            //按栅格中最强rsrp小区判断弱覆盖栅格
            foreach (var item in gridList)
            {
                //将栅格中的小区按rsrp降序排序
                item.Value.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                //最强RSRP小区
                ScanGridInfo grid = item.Value[0];
                CoverageRegion coverageRegion = new CoverageRegion();
                if (grid.R0_RP >= CurRsrpMin)
                {
                    List<ScanGridInfo> cellList = NbIotMgrsGridHelper.FilterCells(item.Value, type);
                    if (cellList.Count > 0)
                    {
                        addCoverageRegionList(coverageRegionList, item, grid, coverageRegion);
                    }
                }
            }
            return coverageRegionList;
        }

        private void addCoverageRegionList(List<CoverageRegion> coverageRegionList, KeyValuePair<string, List<ScanGridInfo>> item, ScanGridInfo grid, CoverageRegion coverageRegion)
        {
            coverageRegion.CellGrid = new List<ScanGridInfo>();
            for (int i = 0; i < item.Value.Count; i++)
            {
                double curRsrpDis = grid.R0_RP - item.Value[i].R0_RP;
                if (curRsrpDis < RsrpDis)
                {
                    coverageRegion.CellGrid.Add(item.Value[i]);
                }
                else
                {
                    break;
                }
            }

            if (coverageRegion.CellGrid.Count >= Coverage)
            {
                coverageRegionList.Add(coverageRegion);
            }
        }

        public override Dictionary<string, string> GetResultData()
        {
            return resultList;
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }
    }

    public class CarrierCoverage
    {
        public string Name
        {
            get;
            set;
        }

        public int IssuesCount
        {
            get
            {
                int result = 0;
                foreach (AreaOverlapGridData item in AreaGridViews)
                {
                    result += item.SerialGridViews.Count;
                }
                return result;
            }
        }

        public List<AreaOverlapGridData> AreaGridViews
        {
            get;
            set;
        }
    }

    public class AreaOverlapGridData
    {
        public string AreaName
        {
            get;
            set;
        }

        public int IssuesCount
        {
            get
            {
                return SerialGridViews.Count;
            }
        }

        public int IssuesGridCount
        {
            get
            {
                int res = 0;
                foreach (var grid in SerialGridViews)
                {
                    res += grid.WeakGridCount;
                }
                return res;
            }
        }

        public List<SerialOverlapCoverage> SerialGridViews
        {
            get;
            set;
        }
    }

    public class SerialOverlapCoverage
    {
        /// <summary>
        /// 连续弱覆盖栅格数
        /// </summary>
        public int WeakGridCount
        {
            get
            {
                return GridViews.Count;
            }
        }

        /// <summary>
        /// 连续弱覆盖栅格序号合集
        /// </summary>
        public string WeakGridDesc
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                foreach (var item in GridViews)
                {
                    sb.Append(item.MGRTIndex);
                    sb.Append(",");
                }

                return sb.ToString().TrimEnd(',');
            }
        }

        public string MaxOverlapCoverage
        {
            get
            {
                int max = -2000;
                foreach (var item in GridViews)
                {
                    max = Math.Max(max, item.CellGrid.Count);
                }

                return max.ToString();
            }
        }

        public string MinOverlapCoverage
        {
            get
            {
                int min = 2000;
                foreach (var item in GridViews)
                {
                    min = Math.Min(min, item.CellGrid.Count);
                }

                return min.ToString();
            }
        }

        public string AvgOverlapCoverage
        {
            get
            {
                int sum = 0;
                foreach (var item in GridViews)
                {
                    sum += item.CellGrid.Count;
                }

                return (sum / GridViews.Count).ToString();
            }
        }

        private string fileNames;
        public string FileNames
        {
            get
            {
                return fileNames;
            }
        }

        public void SetFileNameByFileID(List<FileInfo> fileList)
        {
            StringBuilder sb = new StringBuilder();
            List<int> fileIDList = new List<int>();
            foreach (var item in GridViews)
            {
                foreach (var file in item.CellGrid)
                {
                    if (!fileIDList.Contains(file.FileID))
                    {
                        fileIDList.Add(file.FileID);
                    }
                }
            }

            foreach (var item in fileIDList)
            {
                foreach (var file in fileList)
                {
                    if (item == file.ID)
                    {
                        sb.Append(file.Name);
                        sb.Append(",");
                    }
                }
            }
            fileNames = sb.ToString().TrimEnd(',');
        }

        /// <summary>
        /// 连续弱覆盖栅格(图层显示显示)
        /// </summary>
        public List<CoverageRegion> GridViews
        {
            get;
            set;
        }
    }

    public class CoverageRegion : ILteMgrsBlockItem
    {
        public CoverageRegion()
        {
            Invaild = false;
        }

        public bool Invaild { get; set; }

        /// <summary>
        /// 重叠覆盖度
        /// </summary>
        public int OverlapCoverage
        {
            get
            {
                if (!Invaild)
                {
                    return CellGrid.Count;
                }
                else
                {
                    return -1;
                }
            }
        }

        public double CentLng
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].CentLng;
                }
                return 0;
            }
        }

        public double CentLat
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].CentLat;
                }
                return 0;
            }
        }

        public string MGRTIndex
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].MGRTIndex;
                }
                return "";
            }
        }

        public string MaxRsrp
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].R0_RP.ToString("f2");
                }
                return "";
            }
        }

        public string MinRsrp
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[CellGrid.Count - 1].R0_RP.ToString("f2");
                }
                return "";
            }
        }

        public string AvgRsrp
        {
            get
            {
                double sum = 0;
                int count = 0;
                foreach (var item in CellGrid)
                {
                    sum += item.R0_RP * item.SampleCount;
                    count += item.SampleCount;
                }

                return (sum / count).ToString("f2");
            }
        }

        public string MaxSinr
        {
            get
            {
                double max = -200;
                foreach (var item in CellGrid)
                {
                    max = Math.Max(max, item.R0_CINR);
                }

                return max.ToString("f2");
            }
        }

        public string AvgSinr
        {
            get
            {
                double sum = 0;
                int count = 0;
                foreach (var item in CellGrid)
                {
                    sum += item.R0_CINR * item.SampleCount;
                    count += item.SampleCount;
                }

                return (sum / count).ToString("f2");
            }
        }

        public double TLLongitude
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].TLLongitude;
                }
                return 0;
            }
        }

        public double TLLatitude
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].TLLatitude;
                }
                return 0;
            }
        }

        public double BRLongitude
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].BRLongitude;
                }
                return 0;
            }
        }

        public double BRLatitude
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].BRLatitude;
                }
                return 0;
            }
        }

        public int GridSize
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].GridSize;
                }
                return 0;
            }
        }

        /// <summary>
        /// 重叠覆盖区域的小区栅格列表
        /// </summary>
        public List<ScanGridInfo> CellGrid
        {
            get;
            set;
        }

        public bool Within(DbRect rect)
        {
            if (BRLongitude < rect.x1 || TLLongitude > rect.x2 || TLLatitude < rect.y1 || BRLatitude > rect.y2)
            {
                return false;
            }
            return true;
        }
    }
}
