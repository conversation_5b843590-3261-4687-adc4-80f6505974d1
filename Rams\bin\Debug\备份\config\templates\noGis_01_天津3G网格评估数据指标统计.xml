<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">01_天津3G网格评估数据指标统计</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">0</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试网格</Item>
              <Item typeName="String" key="Exp">{kAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">网格场景（本地）</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">维护区域</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长（小时)</Item>
              <Item typeName="String" key="Exp">{Wx_086A/(60*60*1000) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试里程（公里）</Item>
              <Item typeName="String" key="Exp">{Wx_086B/1000 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">厂家</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">LOG名称</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">覆盖采样点</Item>
              <Item typeName="String" key="Exp">{Wx_710A33+Wx_710A34+Wx_710A35+Wx_710A36+Wx_710B33+Wx_710B34+Wx_710B35+Wx_710B36+Wx_710F33+Wx_710F34+Wx_710F35+Wx_710F36+Wx_711C33+Wx_711C34+Wx_711C35+Wx_711C36 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">总采样点</Item>
              <Item typeName="String" key="Exp">{Wx_710A3C+Wx_710B3C+Wx_710F3C+Wx_711C3C }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RSCP（平均值）</Item>
              <Item typeName="String" key="Exp">{(Wx_710A3F*Wx_710A3C+Wx_710B3F*Wx_710B3C+Wx_710F3F*Wx_710F3C+Wx_711C3F*Wx_711C3C )/(Wx_710A3C+Wx_710B3C+Wx_710F3C+Wx_711C3C ) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TotalRSCP采样点比例（TotalRSCP≥-85dBm的采样点数</Item>
              <Item typeName="String" key="Exp">{100*(Wx_710A33+Wx_710A34+Wx_710A35+Wx_710A36+Wx_710B33+Wx_710B34+Wx_710B35+Wx_710B36+Wx_710F33+Wx_710F34+Wx_710F35+Wx_710F36+Wx_711C33+Wx_711C34+Wx_711C35+Wx_711C36  )/(Wx_710A3C+Wx_710B3C+Wx_710F3C+Wx_711C3C ) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TotalEc/Io（平均值）</Item>
              <Item typeName="String" key="Exp">{(Wx_6A0A0A*Wx_6A0A09+Wx_6A0B0A*Wx_6A0B09+Wx_6A0F0A*Wx_6A0F09+Wx_6A1C0A*Wx_6A1C09 )/(Wx_6A0A09+Wx_6A0B09+Wx_6A0F09+Wx_6A1C09) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TotalEc/Io采样点比例（TotalEc/Io≥-12dB的采样点</Item>
              <Item typeName="String" key="Exp">{100*(Wx_6A0A06+Wx_6A0A05+Wx_6A0A04+Wx_6A0A03+Wx_6A0B06+Wx_6A0B05+Wx_6A0B04+Wx_6A0B03+Wx_6A0F06+Wx_6A0F05+Wx_6A0F04+Wx_6A0F03+Wx_6A1C06+Wx_6A1C05+Wx_6A1C04+Wx_6A1C03 )/(Wx_6A0A09+Wx_6A1C09+Wx_6A0F09+Wx_6A0B09 ) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">软切换比例</Item>
              <Item typeName="String" key="Exp">{100*(Wx_5D0B0402+Wx_5D0B0403+Wx_5D0B0404+Wx_5D0B0405+Wx_5D0B0406+Wx_5D0F0402+Wx_5D0F0403+Wx_5D0F0404+Wx_5D0F0405+Wx_5D0F0406+Wx_5D1C0402+Wx_5D1C0403+Wx_5D1C0404+Wx_5D1C0405+Wx_5D1C0406 )/(Wx_5D0B0401+Wx_5D0B0402+Wx_5D0B0403+Wx_5D0B0404+Wx_5D0B0405+Wx_5D0B0406+Wx_5D0F0401+Wx_5D0F0402+Wx_5D0F0403+Wx_5D0F0404+Wx_5D0F0405+Wx_5D0F0406+Wx_5D1C0401+Wx_5D1C0402+Wx_5D1C0403+Wx_5D1C0404+Wx_5D1C0405+Wx_5D1C0406 ) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">建立成功率</Item>
              <Item typeName="String" key="Exp">{evtIdCount[565]*100/evtIdCount[564]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉线率</Item>
              <Item typeName="String" key="Exp">{evtIdCount[933]*100/evtIdCount[56]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接入时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[72]*0.001/evtIdCount[72]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">上传速率(kbps)</Item>
              <Item typeName="String" key="Exp">{(Wx_050B02050301+Wx_050F02050301+Wx_051C02050301)*8/((Wx_050B02050302+Wx_050F02050302+Wx_051C02050302)/1024)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">下载速率(kbps)</Item>
              <Item typeName="String" key="Exp">{(Wx_050B01050201+Wx_050F01050201+Wx_051C01050201)*8/((Wx_050B01050202+Wx_050F01050202+Wx_051C01050202)/1024)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试设备</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>