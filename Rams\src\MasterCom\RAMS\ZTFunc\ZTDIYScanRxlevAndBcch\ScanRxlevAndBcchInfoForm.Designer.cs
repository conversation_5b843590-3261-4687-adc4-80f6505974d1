﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanRxlevAndBcchInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListViewRxlevBcch = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLac = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBcch = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBsic = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxlev = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewRxlevBcch)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListViewRxlevBcch
            // 
            this.treeListViewRxlevBcch.AllColumns.Add(this.olvColumnSN);
            this.treeListViewRxlevBcch.AllColumns.Add(this.olvColumnLac);
            this.treeListViewRxlevBcch.AllColumns.Add(this.olvColumnCI);
            this.treeListViewRxlevBcch.AllColumns.Add(this.olvColumnBcch);
            this.treeListViewRxlevBcch.AllColumns.Add(this.olvColumnBsic);
            this.treeListViewRxlevBcch.AllColumns.Add(this.olvColumnRxlev);
            this.treeListViewRxlevBcch.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnLac,
            this.olvColumnCI,
            this.olvColumnBcch,
            this.olvColumnBsic,
            this.olvColumnRxlev});
            this.treeListViewRxlevBcch.ContextMenuStrip = this.contextMenuStrip1;
            this.treeListViewRxlevBcch.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewRxlevBcch.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewRxlevBcch.FullRowSelect = true;
            this.treeListViewRxlevBcch.GridLines = true;
            this.treeListViewRxlevBcch.Location = new System.Drawing.Point(0, 0);
            this.treeListViewRxlevBcch.Name = "treeListViewRxlevBcch";
            this.treeListViewRxlevBcch.OwnerDraw = true;
            this.treeListViewRxlevBcch.ShowGroups = false;
            this.treeListViewRxlevBcch.Size = new System.Drawing.Size(511, 227);
            this.treeListViewRxlevBcch.TabIndex = 3;
            this.treeListViewRxlevBcch.UseCompatibleStateImageBehavior = false;
            this.treeListViewRxlevBcch.View = System.Windows.Forms.View.Details;
            this.treeListViewRxlevBcch.VirtualMode = true;
            this.treeListViewRxlevBcch.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListViewRxlevBcch_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 75;
            // 
            // olvColumnLac
            // 
            this.olvColumnLac.HeaderFont = null;
            this.olvColumnLac.Text = "LAC";
            this.olvColumnLac.Width = 91;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 84;
            // 
            // olvColumnBcch
            // 
            this.olvColumnBcch.HeaderFont = null;
            this.olvColumnBcch.Text = "BCCH";
            this.olvColumnBcch.Width = 80;
            // 
            // olvColumnBsic
            // 
            this.olvColumnBsic.HeaderFont = null;
            this.olvColumnBsic.Text = "BSIC";
            this.olvColumnBsic.Width = 79;
            // 
            // olvColumnRxlev
            // 
            this.olvColumnRxlev.HeaderFont = null;
            this.olvColumnRxlev.Text = "场强";
            this.olvColumnRxlev.Width = 84;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(143, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(142, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ScanRxlevAndBcchInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(511, 227);
            this.Controls.Add(this.treeListViewRxlevBcch);
            this.Name = "ScanRxlevAndBcchInfoForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "场强频点列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewRxlevBcch)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListViewRxlevBcch;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnLac;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBcch;
        private BrightIdeasSoftware.OLVColumn olvColumnBsic;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
    }
}