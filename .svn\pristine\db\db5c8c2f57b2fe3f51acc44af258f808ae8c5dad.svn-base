﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class FindDlg : BaseFormStyle
    {
        public FindDlg()
        {
            InitializeComponent();
        }
        public string getInputText()
        {
            return tbxInput.Text.Trim();
        }
        private void btnFind_Click(object sender, EventArgs e)
        {
            if(!tbxInput.Text.Trim().Equals(""))
            {
                this.DialogResult = DialogResult.OK; 
            }
        }
    }
}