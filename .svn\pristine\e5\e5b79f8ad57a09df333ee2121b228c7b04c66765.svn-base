﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class UpdateUserDataSourceRole: DIYSQLBase
    {
        private readonly List<User> user2Update;
        public UpdateUserDataSourceRole(MainModel mainModel, List<User> user2Update)
            : base(mainModel)
        {
            this.user2Update = user2Update;
        }

        protected override void query()
        {
            Dictionary<int, bool> districtDic = new Dictionary<int, bool>();
            foreach (User usr in user2Update)
            {
                foreach (int id in usr.DistrictDataSrcRoleDic.Keys)
                {
                    districtDic[id] = true;
                }
            }
            foreach (int id in districtDic.Keys)
            {
                this.dbid = id;
                base.query();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            foreach (User usr in user2Update)
            {
                sb.Append("delete from tb_cfg_static_user_dataSourceRole where user_id=" + usr.ID.ToString() + ";");
                List<int> roles;
                if (usr.DistrictDataSrcRoleDic.TryGetValue(dbid, out roles))
                {//更新当前地市权限设置
                    foreach (int roleID in roles)
                    {
                        sb.Append("INSERT INTO tb_cfg_static_user_dataSourceRole (user_id, role_id) VALUES ("
                                + usr.ID.ToString() + "," + roleID.ToString() + ");");
                    }
                }
            }
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                   //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "更新用户数据源权限"; }
        }
    }
}
