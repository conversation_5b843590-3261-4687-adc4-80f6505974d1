﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoCoverRoadSetDlg : BaseDialog
    {
        public NoCoverRoadSetDlg()
        {
            InitializeComponent();
            cbxBandType.SelectedIndex = 0;
        }

        public void GetFilterCondition(out int bandType, out int rxLev, out int distance)
        {
            bandType = cbxBandType.SelectedIndex;
            rxLev = (int)numRxLevThreshold.Value;
            distance = (int)numDistance.Value;
        }
    }
}
