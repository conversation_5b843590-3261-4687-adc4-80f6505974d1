﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// TD测试指标门限
    /// </summary>
    [XmlRoot("TD测试指标门限")]
    public class TDTestThreadholdInf
    {
        [XmlArray("items"), XmlArrayItem("item")]
        private IndicatorThresholdInf[] thresholdInf;

        public IndicatorThresholdInf[] ThresholdInf
        {
            get { return thresholdInf; }
            set { thresholdInf = value; }
        }
        public TDTestThreadholdInf()
        {
            thresholdInf = new IndicatorThresholdInf[16];
            thresholdInf[0] = new IndicatorThresholdInf("平均车速");
            thresholdInf[1] = new IndicatorThresholdInf("接通率");
            thresholdInf[2] = new IndicatorThresholdInf("掉话率");

            thresholdInf[3] = new IndicatorThresholdInf("切换成功率");
            thresholdInf[4] = new IndicatorThresholdInf("里程互操作比");
            thresholdInf[5] = new IndicatorThresholdInf("85覆盖率");

            thresholdInf[6] = new IndicatorThresholdInf("85覆盖率(C/I>-3)");
            thresholdInf[7] = new IndicatorThresholdInf("DPCH覆盖率");
            thresholdInf[8] = new IndicatorThresholdInf("导频污染比");

            thresholdInf[9] = new IndicatorThresholdInf("语音BLER");            
            thresholdInf[10] = new IndicatorThresholdInf("MOS2.8占比");
            thresholdInf[11] = new IndicatorThresholdInf("每呼切换数");

            thresholdInf[12] = new IndicatorThresholdInf("TD时长占比");
            thresholdInf[13] = new IndicatorThresholdInf("HSPA时长占比");
            thresholdInf[14] = new IndicatorThresholdInf("FTP平均速率");

            thresholdInf[15] = new IndicatorThresholdInf("FTP下载速率占比");
        }
    }
}
