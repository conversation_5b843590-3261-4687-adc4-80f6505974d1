﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class ProblemBlockInfoPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions1 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnShowHideChart = new System.Windows.Forms.Button();
            this.btnExp2Xls = new System.Windows.Forms.Button();
            this.btnExpAll2Xls = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.dataGridViewProblemBlockStat = new System.Windows.Forms.DataGridView();
            this.columnCityName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnTimePeriod = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBRemainCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBCreateCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBClosedCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBDropCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnBlockCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBWeakQualCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBWeakMosCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.columnPBWeakCoverCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.chartControl = new DevExpress.XtraCharts.ChartControl();
            this.btnPBFresh = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.comboBoxPBPeriod = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.btnSelectCity = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.checkedComboBoxDate = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.rtBoxMsg = new System.Windows.Forms.RichTextBox();
            this.panel1.SuspendLayout();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewProblemBlockStat)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkedComboBoxDate.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.btnShowHideChart);
            this.panel1.Controls.Add(this.btnExp2Xls);
            this.panel1.Controls.Add(this.btnExpAll2Xls);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1093, 30);
            this.panel1.TabIndex = 2;
            // 
            // btnShowHideChart
            // 
            this.btnShowHideChart.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnShowHideChart.Location = new System.Drawing.Point(835, 3);
            this.btnShowHideChart.Name = "btnShowHideChart";
            this.btnShowHideChart.Size = new System.Drawing.Size(93, 23);
            this.btnShowHideChart.TabIndex = 4;
            this.btnShowHideChart.Text = "显示/隐藏图表";
            this.btnShowHideChart.UseVisualStyleBackColor = true;
            this.btnShowHideChart.Click += new System.EventHandler(this.btnShowHideChart_Click);
            // 
            // btnExp2Xls
            // 
            this.btnExp2Xls.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExp2Xls.Location = new System.Drawing.Point(934, 3);
            this.btnExp2Xls.Name = "btnExp2Xls";
            this.btnExp2Xls.Size = new System.Drawing.Size(75, 23);
            this.btnExp2Xls.TabIndex = 3;
            this.btnExp2Xls.Text = "导出本页";
            this.btnExp2Xls.UseVisualStyleBackColor = true;
            this.btnExp2Xls.Click += new System.EventHandler(this.btnExp2Xls_Click);
            // 
            // btnExpAll2Xls
            // 
            this.btnExpAll2Xls.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExpAll2Xls.Location = new System.Drawing.Point(1015, 3);
            this.btnExpAll2Xls.Name = "btnExpAll2Xls";
            this.btnExpAll2Xls.Size = new System.Drawing.Size(75, 23);
            this.btnExpAll2Xls.TabIndex = 1;
            this.btnExpAll2Xls.Text = "导出所有页";
            this.btnExpAll2Xls.UseVisualStyleBackColor = true;
            this.btnExpAll2Xls.Click += new System.EventHandler(this.btnExpAll2Xls_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(19, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(82, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "问题点情况";
            // 
            // splitContainer
            // 
            this.splitContainer.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitContainer.Location = new System.Drawing.Point(3, 105);
            this.splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.dataGridViewProblemBlockStat);
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.rtBoxMsg);
            this.splitContainer.Panel2.Controls.Add(this.chartControl);
            this.splitContainer.Size = new System.Drawing.Size(1090, 282);
            this.splitContainer.SplitterDistance = 638;
            this.splitContainer.TabIndex = 3;
            // 
            // dataGridViewProblemBlockStat
            // 
            this.dataGridViewProblemBlockStat.AllowUserToAddRows = false;
            this.dataGridViewProblemBlockStat.AllowUserToDeleteRows = false;
            this.dataGridViewProblemBlockStat.BackgroundColor = System.Drawing.Color.White;
            this.dataGridViewProblemBlockStat.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridViewProblemBlockStat.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewProblemBlockStat.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.columnCityName,
            this.columnTimePeriod,
            this.columnPBRemainCount,
            this.columnPBCreateCount,
            this.columnPBClosedCount,
            this.columnPBDropCount,
            this.columnBlockCount,
            this.columnPBWeakQualCount,
            this.columnPBWeakMosCount,
            this.columnPBWeakCoverCount});
            this.dataGridViewProblemBlockStat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewProblemBlockStat.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewProblemBlockStat.Name = "dataGridViewProblemBlockStat";
            this.dataGridViewProblemBlockStat.ReadOnly = true;
            this.dataGridViewProblemBlockStat.RowHeadersVisible = false;
            this.dataGridViewProblemBlockStat.RowTemplate.Height = 23;
            this.dataGridViewProblemBlockStat.Size = new System.Drawing.Size(638, 282);
            this.dataGridViewProblemBlockStat.TabIndex = 11;
            this.dataGridViewProblemBlockStat.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridViewProblemBlockStat_CellClick);
            // 
            // columnCityName
            // 
            this.columnCityName.HeaderText = "地市名";
            this.columnCityName.Name = "columnCityName";
            this.columnCityName.ReadOnly = true;
            // 
            // columnTimePeriod
            // 
            this.columnTimePeriod.HeaderText = "时间段";
            this.columnTimePeriod.Name = "columnTimePeriod";
            this.columnTimePeriod.ReadOnly = true;
            this.columnTimePeriod.Width = 200;
            // 
            // columnPBRemainCount
            // 
            this.columnPBRemainCount.HeaderText = "上周未关闭问题点数";
            this.columnPBRemainCount.Name = "columnPBRemainCount";
            this.columnPBRemainCount.ReadOnly = true;
            this.columnPBRemainCount.Width = 150;
            // 
            // columnPBCreateCount
            // 
            this.columnPBCreateCount.HeaderText = "问题点新增数量";
            this.columnPBCreateCount.Name = "columnPBCreateCount";
            this.columnPBCreateCount.ReadOnly = true;
            this.columnPBCreateCount.Width = 150;
            // 
            // columnPBClosedCount
            // 
            this.columnPBClosedCount.HeaderText = "问题点关闭数量";
            this.columnPBClosedCount.Name = "columnPBClosedCount";
            this.columnPBClosedCount.ReadOnly = true;
            this.columnPBClosedCount.Width = 150;
            // 
            // columnPBDropCount
            // 
            this.columnPBDropCount.HeaderText = "新增掉话事件数量";
            this.columnPBDropCount.Name = "columnPBDropCount";
            this.columnPBDropCount.ReadOnly = true;
            this.columnPBDropCount.Width = 150;
            // 
            // columnBlockCount
            // 
            this.columnBlockCount.HeaderText = "新增主叫未接通事件数量";
            this.columnBlockCount.Name = "columnBlockCount";
            this.columnBlockCount.ReadOnly = true;
            this.columnBlockCount.Width = 150;
            // 
            // columnPBWeakQualCount
            // 
            this.columnPBWeakQualCount.HeaderText = "新增持续质差事件数量";
            this.columnPBWeakQualCount.Name = "columnPBWeakQualCount";
            this.columnPBWeakQualCount.ReadOnly = true;
            this.columnPBWeakQualCount.Width = 150;
            // 
            // columnPBWeakMosCount
            // 
            this.columnPBWeakMosCount.HeaderText = "新增持续弱MOS事件数量";
            this.columnPBWeakMosCount.Name = "columnPBWeakMosCount";
            this.columnPBWeakMosCount.ReadOnly = true;
            this.columnPBWeakMosCount.Width = 150;
            // 
            // columnPBWeakCoverCount
            // 
            this.columnPBWeakCoverCount.HeaderText = "新增弱覆盖路段事件数量";
            this.columnPBWeakCoverCount.Name = "columnPBWeakCoverCount";
            this.columnPBWeakCoverCount.ReadOnly = true;
            this.columnPBWeakCoverCount.Width = 150;
            // 
            // chartControl
            // 
            this.chartControl.AppearanceName = "Northern Lights";
            xyDiagram1.AxisX.AutoScaleBreaks.Enabled = true;
            xyDiagram1.AxisX.AutoScaleBreaks.MaxCount = 2;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            this.chartControl.Diagram = xyDiagram1;
            this.chartControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl.Location = new System.Drawing.Point(0, 0);
            this.chartControl.Name = "chartControl";
            this.chartControl.PaletteName = "Nature Colors";
            this.chartControl.RuntimeSelection = true;
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            pointOptions1.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            series1.PointOptions = pointOptions1;
            sideBySideBarSeriesLabel2.LineVisible = true;
            sideBySideBarSeriesLabel2.Visible = false;
            series2.Label = sideBySideBarSeriesLabel2;
            series2.Name = "Series 2";
            this.chartControl.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1,
        series2};
            sideBySideBarSeriesLabel3.LineVisible = true;
            this.chartControl.SeriesTemplate.Label = sideBySideBarSeriesLabel3;
            this.chartControl.Size = new System.Drawing.Size(448, 282);
            this.chartControl.TabIndex = 1;
            // 
            // btnPBFresh
            // 
            this.btnPBFresh.Location = new System.Drawing.Point(434, 69);
            this.btnPBFresh.Name = "btnPBFresh";
            this.btnPBFresh.Size = new System.Drawing.Size(90, 30);
            this.btnPBFresh.TabIndex = 10;
            this.btnPBFresh.Text = "刷新";
            this.btnPBFresh.UseVisualStyleBackColor = true;
            this.btnPBFresh.Click += new System.EventHandler(this.btnPBFresh_Click);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(16, 45);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(41, 12);
            this.label9.TabIndex = 8;
            this.label9.Text = "地市：";
            // 
            // comboBoxPBPeriod
            // 
            this.comboBoxPBPeriod.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxPBPeriod.FormattingEnabled = true;
            this.comboBoxPBPeriod.Location = new System.Drawing.Point(360, 43);
            this.comboBoxPBPeriod.Name = "comboBoxPBPeriod";
            this.comboBoxPBPeriod.Size = new System.Drawing.Size(164, 20);
            this.comboBoxPBPeriod.TabIndex = 7;
            this.comboBoxPBPeriod.SelectedIndexChanged += new System.EventHandler(this.comboBoxPBPeriod_SelectedIndexChanged);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(289, 45);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(65, 12);
            this.label8.TabIndex = 6;
            this.label8.Text = "周期时间：";
            // 
            // btnSelectCity
            // 
            this.btnSelectCity.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnSelectCity.Location = new System.Drawing.Point(52, 39);
            this.btnSelectCity.Name = "btnSelectCity";
            this.btnSelectCity.Size = new System.Drawing.Size(231, 23);
            this.btnSelectCity.TabIndex = 11;
            this.btnSelectCity.Text = "请选择地市↓";
            this.btnSelectCity.UseVisualStyleBackColor = true;
            this.btnSelectCity.Click += new System.EventHandler(this.btnSelectCity_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(16, 78);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 12;
            this.label2.Text = "日期：";
            // 
            // checkedComboBoxDate
            // 
            this.checkedComboBoxDate.Location = new System.Drawing.Point(52, 73);
            this.checkedComboBoxDate.Name = "checkedComboBoxDate";
            this.checkedComboBoxDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.checkedComboBoxDate.Size = new System.Drawing.Size(231, 21);
            this.checkedComboBoxDate.TabIndex = 13;
            // 
            // rtBoxMsg
            // 
            this.rtBoxMsg.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rtBoxMsg.Location = new System.Drawing.Point(97, 110);
            this.rtBoxMsg.Name = "rtBoxMsg";
            this.rtBoxMsg.ReadOnly = true;
            this.rtBoxMsg.Size = new System.Drawing.Size(205, 32);
            this.rtBoxMsg.TabIndex = 3;
            this.rtBoxMsg.Text = "数据量过多，不显示图表";
            this.rtBoxMsg.Visible = false;
            // 
            // ProblemBlockInfoPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.checkedComboBoxDate);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.btnSelectCity);
            this.Controls.Add(this.splitContainer);
            this.Controls.Add(this.btnPBFresh);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.comboBoxPBPeriod);
            this.Name = "ProblemBlockInfoPanel";
            this.Size = new System.Drawing.Size(1099, 390);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewProblemBlockStat)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkedComboBoxDate.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button btnExp2Xls;
        private System.Windows.Forms.Button btnExpAll2Xls;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.Button btnPBFresh;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.ComboBox comboBoxPBPeriod;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.DataGridView dataGridViewProblemBlockStat;
        private System.Windows.Forms.Button btnShowHideChart;
        private DevExpress.XtraCharts.ChartControl chartControl;
        private System.Windows.Forms.Button btnSelectCity;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.CheckedComboBoxEdit checkedComboBoxDate;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnCityName;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnTimePeriod;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBRemainCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBCreateCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBClosedCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBDropCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnBlockCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBWeakQualCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBWeakMosCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn columnPBWeakCoverCount;
        private System.Windows.Forms.RichTextBox rtBoxMsg;
    }
}
