﻿using DevExpress.XtraEditors;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTTroubleSpotsAutoAnalysisDlg : BaseDialog
    {
        public ZTTroubleSpotsAutoAnalysisDlg()
        {
            InitializeComponent();
            loadConfig();
        }

        #region 读取保存配置文件
        /// <summary>
        /// 加载配置文件,根据配置文件的图层路径,加载图层
        /// </summary>
        private void loadConfig()
        {
            string configFileName = Application.StartupPath + @"\config\TroubleSpotsAutoAnalysis.xml";
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(configFileName);
                try
                {
                    XmlElement config = configFile.GetConfig("MapSetting");
                    Param = configFile.GetItemValue(config, "MapInfoSetting") as Dictionary<string, object>;

                    edtMapPathCompany.Text = ((Dictionary<string, object>)Param["Company"])["MapPath"].ToString();
                    string companyColumn = ((Dictionary<string, object>)Param["Company"])["ColumnName"].ToString();
                    List<string> companyColumnNames = getTableColumns(edtMapPathCompany.Text);
                    setCbxColumnNameInfo(companyColumn, companyColumnNames, cbxColumnNameCompany, edtMapPathCompany);

                    edtMapPathArea.Text = ((Dictionary<string, object>)Param["Area"])["MapPath"].ToString();
                    string areaColumn = ((Dictionary<string, object>)Param["Area"])["ColumnName"].ToString();
                    List<string> areaColumnNames = getTableColumns(edtMapPathCompany.Text);
                    setCbxColumnNameInfo(areaColumn, areaColumnNames, cbxColumnNameArea, edtMapPathArea);
                }
                catch
                {
                    edtMapPathCompany.Text = "";
                    cbxColumnNameCompany.Properties.Items.Clear();
                    edtMapPathArea.Text = "";
                    cbxColumnNameArea.Properties.Items.Clear();
                }
            }
        }

        private void setCbxColumnNameInfo(string column, List<string> columnNames, ComboBoxEdit cbx, TextEdit edtMapPath)
        {
            if (columnNames.Count > 0)
            {
                cbx.Properties.Items.Clear();
                int selectedIndex = 0;
                for (int i = 0; i < columnNames.Count; i++)
                {
                    if (columnNames[i] == column)
                    {
                        selectedIndex = i;
                    }
                    cbx.Properties.Items.Add(columnNames[i]);
                }
                cbx.SelectedIndex = selectedIndex;
            }
            else
            {
                edtMapPath.Text = "";
            }
        }

        private void saveConfig()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement element = configFile.AddConfig("MapSetting");
                Dictionary<string, object> param = new Dictionary<string, object>();
                Dictionary<string, object> companyInfo = new Dictionary<string, object>();
                companyInfo["MapPath"] = edtMapPathCompany.Text;
                companyInfo["MapName"] = "";
                companyInfo["ColumnName"] = cbxColumnNameCompany.Text;
                param["Company"] = companyInfo;
                Dictionary<string, object> areaInfo = new Dictionary<string, object>();
                areaInfo["MapPath"] = edtMapPathArea.Text;
                areaInfo["MapName"] = "";
                areaInfo["ColumnName"] = cbxColumnNameArea.Text;
                param["Area"] = areaInfo;
                Param = param;
                configFile.AddItem(element, "MapInfoSetting", param);
                StringBuilder path = new StringBuilder(Application.StartupPath);
                path.Append(@"/config/TroubleSpotsAutoAnalysis.xml");
                configFile.Save(path.ToString());
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private Dictionary<string, MapInfo> layerInfosDic = new Dictionary<string, MapInfo>();
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();

                foreach (var item in layerInfosDic)
                {
                    param[item.Key] = item.Value.Param;
                }
                return param;
            }
            set
            {
                try
                {
                    MapInfo companyInfo = new MapInfo();
                    companyInfo.Param = (Dictionary<string, object>)value["Company"];
                    layerInfosDic["Company"] = companyInfo;

                    MapInfo areaInfo = new MapInfo();
                    areaInfo.Param = (Dictionary<string, object>)value["Area"];
                    layerInfosDic["Area"] = areaInfo;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }
        #endregion

        #region 打开图层,获取图层字段
        private void btnMapPathCompany_Click(object sender, EventArgs e)
        {
            string mapPath;
            List<string> columnNames = openMap(out mapPath);
            if (string.IsNullOrEmpty(mapPath))
            {
                return;
            }
            if (columnNames.Count > 0)
            {
                edtMapPathCompany.Text = mapPath;
                cbxColumnNameCompany.Properties.Items.Clear();
                foreach (string columnName in columnNames)
                {
                    cbxColumnNameCompany.Properties.Items.Add(columnName);
                }
                cbxColumnNameCompany.SelectedIndex = 0;
            }
            else
            {
                XtraMessageBox.Show(this, "打开Shape图层失败，所选图层不支持选定操作或者图层的区域名称为空！");
            }
        }

        private void btnMapPathArea_Click(object sender, EventArgs e)
        {
            string mapPath;
            List<string> columnNames = openMap(out mapPath);
            if (string.IsNullOrEmpty(mapPath))
            {
                return;
            }
            if (columnNames.Count > 0)
            {
                edtMapPathArea.Text = mapPath;
                cbxColumnNameArea.Properties.Items.Clear();
                foreach (string columnName in columnNames)
                {
                    cbxColumnNameArea.Properties.Items.Add(columnName);
                }
                cbxColumnNameArea.SelectedIndex = 0;
            }
            else
            {
                XtraMessageBox.Show(this, "打开Shape图层失败，所选图层不支持选定操作或者图层的区域名称为空！");
            }
        }

        private List<string> openMap(out string mapPath)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = false;
            openFileDialog.CheckFileExists = true;
            openFileDialog.DefaultExt = "SHP";
            openFileDialog.Filter = FilterHelper.Shp;
            openFileDialog.InitialDirectory = Application.StartupPath + @"\GEOGRAPHIC";
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                mapPath = openFileDialog.FileName;
                return getTableColumns(mapPath);
            }
            else
            {
                mapPath = "";
                return new List<string>();
            }
        }

        private List<string> getTableColumns(string regionTableName)
        {
            List<string> columnList = new List<string>();
            Shapefile table = null;
            try
            {
                table = new Shapefile();
                if (!table.Open(regionTableName, null))
                {
                    return columnList;
                }

                if (table.ShapefileType != ShpfileType.SHP_POLYGON
                 && table.ShapefileType != ShpfileType.SHP_POLYGONZ)
                {
                    return columnList;
                }

                int numFields = table.NumFields;
                for (int x = 0; x < numFields; x++)
                {
                    Field field = table.get_Field(x);
                    columnList.Add(field.Name);
                }
            }
            catch
            {
                if (table != null)
                {
                    table.Close();
                }
            }
            return columnList;
        }
        #endregion

        private bool checkPath()
        {
            if (!edtMapPathCompany.Text.Trim().Equals("") && !File.Exists(edtMapPathCompany.Text.Trim()))
            {
                XtraMessageBox.Show("厂家图层不存在，请重新配置。");
                edtMapPathCompany.Focus();
                return false;
            }
            if (!edtMapPathArea.Text.Trim().Equals("") && !File.Exists(edtMapPathArea.Text.Trim()))
            {
                XtraMessageBox.Show("分公司图层不存在，请重新配置。");
                edtMapPathArea.Focus();
                return false;
            }
            return true;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (checkPath())
            {
                saveConfig();
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                this.DialogResult = DialogResult.Retry;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        MapRegionCondition mapCondition = null;
        /// <summary>
        /// 打开选择的图层保存为查询条件的区域列表
        /// </summary>
        /// <returns></returns>
        public MapRegionCondition GetCondition()
        {
            mapCondition = new MapRegionCondition();
            Shapefile regionShp = new Shapefile();
            if (regionShp.Open(edtMapPathCompany.Text, null))
            {
                int nmFldIndex = MapOperation.GetColumnFieldIndex(regionShp, cbxColumnNameCompany.Text);
                for (int i = 0; i < regionShp.NumShapes; i++)
                {
                    MapWinGIS.Shape shp = regionShp.get_Shape(i);
                    string areaName = regionShp.get_CellValue(nmFldIndex, i) as string;

                    ResvRegion rg = new ResvRegion();
                    rg.Shape = shp;
                    rg.RegionName = areaName;
                    mapCondition.CompanyRegionList.Add(rg);
                }
            }
            if (regionShp.Open(edtMapPathArea.Text, null))
            {
                int nmFldIndex = MapOperation.GetColumnFieldIndex(regionShp, cbxColumnNameArea.Text);
                for (int i = 0; i < regionShp.NumShapes; i++)
                {
                    MapWinGIS.Shape shp = regionShp.get_Shape(i);
                    string areaName = regionShp.get_CellValue(nmFldIndex, i) as string;

                    ResvRegion rg = new ResvRegion();
                    rg.Shape = shp;
                    rg.RegionName = areaName;
                    mapCondition.AreaRegionList.Add(rg);
                }
            }
            return mapCondition;
        }
    }

    public class MapRegionCondition
    {
        public MapRegionCondition()
        {
            CompanyRegionList = new List<ResvRegion>();
            AreaRegionList = new List<ResvRegion>();
        }

        public List<ResvRegion> CompanyRegionList { get; set; }
        public List<ResvRegion> AreaRegionList { get; set; }
    }
}
