﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class IntegritySettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelUnAchieve = new System.Windows.Forms.Label();
            this.labelAchieve = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.spinEditTimes = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBoxPermeate = new System.Windows.Forms.GroupBox();
            this.spinEditPermeate = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTimes.Properties)).BeginInit();
            this.groupBoxPermeate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPermeate.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelUnAchieve);
            this.groupBox2.Controls.Add(this.labelAchieve);
            this.groupBox2.Location = new System.Drawing.Point(18, 147);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(255, 65);
            this.groupBox2.TabIndex = 18;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "颜色";
            // 
            // labelUnAchieve
            // 
            this.labelUnAchieve.BackColor = System.Drawing.Color.Red;
            this.labelUnAchieve.ForeColor = System.Drawing.Color.White;
            this.labelUnAchieve.Location = new System.Drawing.Point(147, 29);
            this.labelUnAchieve.Name = "labelUnAchieve";
            this.labelUnAchieve.Size = new System.Drawing.Size(60, 18);
            this.labelUnAchieve.TabIndex = 10;
            this.labelUnAchieve.Text = "未达标";
            this.labelUnAchieve.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // labelAchieve
            // 
            this.labelAchieve.BackColor = System.Drawing.Color.Green;
            this.labelAchieve.ForeColor = System.Drawing.Color.White;
            this.labelAchieve.Location = new System.Drawing.Point(52, 29);
            this.labelAchieve.Name = "labelAchieve";
            this.labelAchieve.Size = new System.Drawing.Size(60, 18);
            this.labelAchieve.TabIndex = 10;
            this.labelAchieve.Text = "达标";
            this.labelAchieve.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.spinEditTimes);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Location = new System.Drawing.Point(18, 6);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(255, 64);
            this.groupBox1.TabIndex = 17;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "村庄测试标准";
            // 
            // spinEditTimes
            // 
            this.spinEditTimes.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spinEditTimes.Location = new System.Drawing.Point(124, 25);
            this.spinEditTimes.Name = "spinEditTimes";
            this.spinEditTimes.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTimes.Properties.Mask.EditMask = "f0";
            this.spinEditTimes.Size = new System.Drawing.Size(100, 21);
            this.spinEditTimes.TabIndex = 11;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F);
            this.label3.Location = new System.Drawing.Point(53, 30);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 10;
            this.label3.Text = "测试次数≥";
            // 
            // groupBoxPermeate
            // 
            this.groupBoxPermeate.Controls.Add(this.spinEditPermeate);
            this.groupBoxPermeate.Controls.Add(this.label5);
            this.groupBoxPermeate.Controls.Add(this.label6);
            this.groupBoxPermeate.Location = new System.Drawing.Point(18, 76);
            this.groupBoxPermeate.Name = "groupBoxPermeate";
            this.groupBoxPermeate.Size = new System.Drawing.Size(255, 65);
            this.groupBoxPermeate.TabIndex = 16;
            this.groupBoxPermeate.TabStop = false;
            this.groupBoxPermeate.Text = "区域测试标准";
            // 
            // spinEditPermeate
            // 
            this.spinEditPermeate.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditPermeate.Location = new System.Drawing.Point(124, 27);
            this.spinEditPermeate.Name = "spinEditPermeate";
            this.spinEditPermeate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPermeate.Properties.Mask.EditMask = "f0";
            this.spinEditPermeate.Size = new System.Drawing.Size(100, 21);
            this.spinEditPermeate.TabIndex = 11;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F);
            this.label5.Location = new System.Drawing.Point(17, 31);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(101, 12);
            this.label5.TabIndex = 10;
            this.label5.Text = "村庄测试合格率≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(234, 32);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(19, 14);
            this.label6.TabIndex = 9;
            this.label6.Text = "%";
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Location = new System.Drawing.Point(108, 236);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 19;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Location = new System.Drawing.Point(198, 236);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 19;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // IntegritySettingDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(294, 272);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBoxPermeate);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(476, 310);
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(310, 225);
            this.Name = "IntegritySettingDlg";
            this.Text = "完整性分析设置";
            this.groupBox2.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTimes.Properties)).EndInit();
            this.groupBoxPermeate.ResumeLayout(false);
            this.groupBoxPermeate.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPermeate.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label labelUnAchieve;
        private System.Windows.Forms.Label labelAchieve;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit spinEditTimes;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBoxPermeate;
        private DevExpress.XtraEditors.SpinEdit spinEditPermeate;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
    }
}