using System;
using System.Collections.Generic;
using System.Text;
using BrightIdeasSoftware;
using System.Windows.Forms;

namespace MasterCom.RAMS.Util
{
    class ListViewManager
    {
        readonly ListView listView;
        public ListViewManager(ListView listView)
        {
            this.listView = listView;
        }

        public void DoExcel()
        {
            if(this.listView is ObjectListView)
                MasterCom.RAMS.Util.ExcelManager.ExportExcel(listView as ObjectListView, false);
            else
                MasterCom.RAMS.Util.ExcelManager.ExportExcel(this.listView);
        }

        public void DoPrint()
        {
            new MasterCom.RAMS.Util.ListViewPrinterForm(this.listView).Show(this.listView);
        }

        public void DoSort()
        {
            MasterCom.Util.SortForm sortForm = new MasterCom.Util.SortForm(this.listView);
            sortForm.Show(this.listView);
        }
    }
}
