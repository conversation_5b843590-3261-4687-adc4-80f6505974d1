﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class FartherCoverInfoNR : FartherCoverInfo
    {
        public NRCell Cell { get; set; }
        public override string CellName { get { return Cell.Name; } }
        public override int TAC { get { return Cell.TAC; } }
        public long NCI { get { return Cell.NCI; } }
        public int ARFCN { get { return Cell.SSBARFCN; } }
        public int PCI { get { return Cell.PCI; } }
        public override double Longitude { get { return Cell.Longitude; } }
        public override double Latitude { get { return Cell.Latitude; } }

        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public float? AvgLteRSRP { get { return caculateAvg(lteRsrpCount, lteRsrpSum); } }

        protected int lteSinrCount = 0;
        protected float lteSinrSum = 0;
        public float? AvgLteSINR { get { return caculateAvg(lteSinrCount, lteSinrSum); } }
        public string AreaName { get; set; }

        public FartherCoverInfoNR(NRCell cell)
            : base()
        {
            this.Cell = cell;
            AreaName = GISManager.GetInstance().GetAreaPlaceDesc(cell.Longitude, cell.Latitude);
        }

        protected override void addOtherTPInfo(TestPoint testPoint)
        {
            float? lteRsrp = getValidData(testPoint, "NR_lte_RSRP", -200, 100);
            if (lteRsrp != null)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRsrp;
            }

            float? lteSinr = getValidData(testPoint, "NR_lte_SINR", -50, 50);
            if (lteSinr != null)
            {
                lteSinrCount++;
                lteSinrSum += (float)lteSinr;
            }
        }

        protected float? getValidData(TestPoint tp, string name, float min, float max)
        {
            float? data = (float?)tp[name];
            if (data != null && data >= min && data <= max)
            {
                return data;
            }
            return null;
        }

        protected float? caculateAvg(int count, float sum)
        {
            float? res = null;
            if (count > 0)
            {
                res = (float?)Math.Round(sum / count, 2);
            }
            return res;
        }
    }
}
