﻿using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsQueryFuncBase : QueryBase
    {
        public NbIotMgrsQueryFuncBase(MainModel mainModel)
            : base(mainModel)
        {
        }

        public const string ModelTableprefix = "tb_stat_scan_nbiot_cell_grid_mgrt_";

        /// <summary>
        /// 地图框选的区域
        /// </summary>
        protected List<MTPolygon> selectPolygons = new List<MTPolygon>();

        /// <summary>
        /// 在区域内的栅格信息合集
        /// </summary>
        public static List<ScanGridInfo> CMScanGridInfoList { get; set; }//移动
        public static List<ScanGridInfo> CUScanGridInfoList { get; set; }//联通
        public static List<ScanGridInfo> CTScanGridInfoList { get; set; }//电信

        #region 基础数据重写
        public override string Name
        {
            get { return "NBIOT专题分析"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 33000, 33003, this.Name);
        }

        protected override bool isValidCondition()
        {
            RegisterFuncs();
            if (condForm == null)
            {
                condForm = new NbIotMgrsConditionForm(funcList);
            }
            return condForm.ShowDialog() == DialogResult.OK;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        #endregion

        #region 查询流程
        protected override void query()
        {
            selectPolygons.Clear();
            if (mainModel.MultiGeometrys)
            {
                foreach (ResvRegion item in condition.Geometorys.SelectedResvRegions)
                {
                    item.GeoOp.Name = item.RegionName;
                    selectPolygons.Add(item.GeoOp);
                }
            }
            else
            {
                condition.Geometorys.GeoOp.Name = "选择区域";
                selectPolygons.Add(condition.Geometorys.GeoOp);
            }

            if (selectPolygons.Count == 0)
            {
                return;
            }

            //查询区域内所有文件
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            condition.ServiceTypes.Clear();
            //NBIOT扫频
            condition.ServiceTypes.Add(55);
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            //查询所有区域内文件栅格合集
            CMScanGridInfoList = new List<ScanGridInfo>();
            CUScanGridInfoList = new List<ScanGridInfo>();
            CTScanGridInfoList = new List<ScanGridInfo>();
            NbIotMgrsQueryFileData queryDataByFile = new NbIotMgrsQueryFileData(MainModel);
            queryDataByFile.SetCondition(condition.Geometorys.GeoOp);
            queryDataByFile.Query(); 

            if (CMScanGridInfoList.Count <= 0 && CUScanGridInfoList.Count <= 0 && CTScanGridInfoList.Count <= 0)
            {
                MessageBox.Show("没有查到相关数据");
                return;
            }

            WaitBox.Show("对结果数据进行处理", dealWithData);

            fireShowForm();
        }
        #endregion

        protected void dealWithData()
        {
            Dictionary<string, string> resultData = new Dictionary<string, string>();

            int i = 0;
            foreach (NbIotMgrsFuncItem item in funcList)
            {
                i++;
                if (item.Stater is NbIotMgrsResultStatisticsStater)
                {
                    NbIotMgrsResultStatisticsStater stater = item.Stater as NbIotMgrsResultStatisticsStater;
                    stater.SetSelectRegion(condition.Geometorys.RegionBounds);
                    stater.SetResultData(resultData);
                }
                item.Stater.SetPolygon(selectPolygons);
                item.Stater.DoStat(item);
                item.Stater.SetResultControl();

                //添加结果集
                Dictionary<string, string> tmp = item.Stater.GetResultData();
                foreach (var result in tmp)
                {
                    if (!resultData.ContainsKey(result.Key))
                    {
                        resultData.Add(result.Key, result.Value);
                    }
                }
                WaitBox.ProgressPercent = i * 100 / funcList.Count;
            }
            WaitBox.Close();
        }

        protected void fireShowForm()
        {
            NbIotMgrsResultForm resultForm = MainModel.CreateResultForm(typeof(NbIotMgrsResultForm)) as NbIotMgrsResultForm;
            resultForm.FillData(funcList);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        /// <summary>
        /// 添加功能点
        /// </summary>
        protected virtual void RegisterFuncs()
        {
            if (funcList == null)
            {
                funcList = new List<NbIotMgrsFuncItem>();
                funcList.Add(new NbIotMgrsFuncItem(new NbIotMgrsBaseSettingControl(), new NbIotMgrsStaterBase()));
                funcList.Add(new NbIotMgrsFuncItem(null, new NbIotMgrsSampleRateStater()));
                funcList.Add(new NbIotMgrsFuncItem(new NbIotMgrsWeakRsrpSetting(), new NbIotMgrsWeakRsrpStater()));
                funcList.Add(new NbIotMgrsFuncItem(new NbIotMgrsNoRsrpSetting(), new NbIotMgrsNoRsrpStater()));
                funcList.Add(new NbIotMgrsFuncItem(new NbIotMgrsWeakSinrSetting(), new NbIotMgrsWeakSinrStater()));
                funcList.Add(new NbIotMgrsFuncItem(new NbIotMgrsOverlapCoverageSetting(), new NbIotMgrsOverlapCoverageStater()));
                funcList.Add(new NbIotMgrsFuncItem(new NbIotMgrsOverlapCoverageRatioSetting(), new NbIotMgrsOverlapCoverageRatioStater()));
                funcList.Add(new NbIotMgrsFuncItem(null, new NbIotMgrsResultStatisticsStater()));
            }
        }

        protected NbIotMgrsConditionForm condForm = null;
        protected List<NbIotMgrsFuncItem> funcList = null;

        public static void Clear()
        {
            CMScanGridInfoList = null;
            CUScanGridInfoList = null;
            CTScanGridInfoList = null;
        }
    }

    public class NbIotMgrsFuncItem
    {
        public LteMgrsConditionControlBase ConditionControl { get; set; }
        public object FuncCondtion { get; set; }

        public NbIotMgrsStaterBase Stater { get; set; }
        public QueryCondition QueryCondition { get; set; }

        public NbIotMgrsFuncItem(LteMgrsConditionControlBase conditionControl, NbIotMgrsStaterBase stater)
        {
            ConditionControl = conditionControl;
            this.Stater = stater;
        }

        public string ConditionTitle
        {
            get { return ConditionControl == null ? null : ConditionControl.Title; }
        }

        public virtual void Clear()
        {
            if (Stater != null)
            {
                Stater.Clear();
            }
        }
    }
}
