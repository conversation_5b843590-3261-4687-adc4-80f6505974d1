﻿namespace MasterCom.RAMS.Frame
{
    partial class CellFcnInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListCI = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn2 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn3 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn4 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn5 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn6 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn7 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn8 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn9 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.contextMenuStrip_Export = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemFoldAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListCI)).BeginInit();
            this.contextMenuStrip_Export.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListCI
            // 
            this.treeListCI.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1,
            this.treeListColumn2,
            this.treeListColumn3,
            this.treeListColumn4,
            this.treeListColumn5,
            this.treeListColumn6,
            this.treeListColumn7,
            this.treeListColumn8,
            this.treeListColumn9});
            this.treeListCI.ContextMenuStrip = this.contextMenuStrip_Export;
            this.treeListCI.Location = new System.Drawing.Point(0, 0);
            this.treeListCI.Name = "treeListCI";
            this.treeListCI.OptionsBehavior.Editable = false;
            this.treeListCI.Size = new System.Drawing.Size(901, 379);
            this.treeListCI.TabIndex = 0;
            this.treeListCI.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListCI_MouseDoubleClick);
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "小区名";
            this.treeListColumn1.FieldName = "CellName";
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            // 
            // treeListColumn2
            // 
            this.treeListColumn2.Caption = "频点";
            this.treeListColumn2.FieldName = "Fcn";
            this.treeListColumn2.Name = "treeListColumn2";
            this.treeListColumn2.Visible = true;
            this.treeListColumn2.VisibleIndex = 1;
            // 
            // treeListColumn3
            // 
            this.treeListColumn3.Caption = "C/I均值";
            this.treeListColumn3.FieldName = "C_I_Mean";
            this.treeListColumn3.Name = "treeListColumn3";
            this.treeListColumn3.Visible = true;
            this.treeListColumn3.VisibleIndex = 2;
            // 
            // treeListColumn4
            // 
            this.treeListColumn4.Caption = "C/I(0-6)%";
            this.treeListColumn4.FieldName = "C_I_0_6";
            this.treeListColumn4.Name = "treeListColumn4";
            this.treeListColumn4.Visible = true;
            this.treeListColumn4.VisibleIndex = 3;
            // 
            // treeListColumn5
            // 
            this.treeListColumn5.Caption = "C/I[6-9)%";
            this.treeListColumn5.FieldName = "C_I_6_9";
            this.treeListColumn5.Name = "treeListColumn5";
            this.treeListColumn5.Visible = true;
            this.treeListColumn5.VisibleIndex = 4;
            // 
            // treeListColumn6
            // 
            this.treeListColumn6.Caption = "C/I[9-12)%";
            this.treeListColumn6.FieldName = "C_I_9_12";
            this.treeListColumn6.Name = "treeListColumn6";
            this.treeListColumn6.Visible = true;
            this.treeListColumn6.VisibleIndex = 5;
            // 
            // treeListColumn7
            // 
            this.treeListColumn7.Caption = "C/I[12-18)%";
            this.treeListColumn7.FieldName = "C_I_12_18";
            this.treeListColumn7.Name = "treeListColumn7";
            this.treeListColumn7.Visible = true;
            this.treeListColumn7.VisibleIndex = 6;
            // 
            // treeListColumn8
            // 
            this.treeListColumn8.Caption = "C/I(>=18)%";
            this.treeListColumn8.FieldName = "C_I_18";
            this.treeListColumn8.Name = "treeListColumn8";
            this.treeListColumn8.Visible = true;
            this.treeListColumn8.VisibleIndex = 7;
            // 
            // treeListColumn9
            // 
            this.treeListColumn9.Caption = "采样点数";
            this.treeListColumn9.FieldName = "C_I_SampleNum";
            this.treeListColumn9.Name = "treeListColumn9";
            this.treeListColumn9.Visible = true;
            this.treeListColumn9.VisibleIndex = 8;
            // 
            // contextMenuStrip_Export
            // 
            this.contextMenuStrip_Export.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemFoldAll,
            this.ToolStripMenuItemExpandAll,
            this.ToolStripMenuItemExport});
            this.contextMenuStrip_Export.Name = "contextMenuStrip_Export";
            this.contextMenuStrip_Export.Size = new System.Drawing.Size(149, 70);
            // 
            // ToolStripMenuItemFoldAll
            // 
            this.ToolStripMenuItemFoldAll.Name = "ToolStripMenuItemFoldAll";
            this.ToolStripMenuItemFoldAll.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemFoldAll.Text = "折叠所有节点";
            this.ToolStripMenuItemFoldAll.Click += new System.EventHandler(this.ToolStripMenuItemFoldAll_Click);
            // 
            // ToolStripMenuItemExpandAll
            // 
            this.ToolStripMenuItemExpandAll.Name = "ToolStripMenuItemExpandAll";
            this.ToolStripMenuItemExpandAll.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExpandAll.Text = "展开所有节点";
            this.ToolStripMenuItemExpandAll.Click += new System.EventHandler(this.ToolStripMenuItemExpandAll_Click);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExport.Text = "导出列表";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // CellFcnInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(901, 379);
            this.Controls.Add(this.treeListCI);
            this.Name = "CellFcnInfoForm";
            this.Text = "小区频点C/I分析列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListCI)).EndInit();
            this.contextMenuStrip_Export.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTreeList.TreeList treeListCI;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn2;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn3;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn4;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn5;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn6;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn7;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn8;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip_Export;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemFoldAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn9;





    }
}