﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    public class BlockCallCondition
    {
        public int WeakRsrpSec { get; set; }
        public int PoorSinrSec { get; set; }

        public float WeakRsrp { get; set; }

        public int PoorSinr { get; set; }

        public int HoNum { get; set; }

        public int HoSec { get; set; }

        public int MultiSec { get; set; }

        public int MultiBand { get; set; }

        public float MultiPer { get; set; }

        public int MultiValue { get; set; }

        public BlockCallCondition()
        {
            CauseSet = new List<BlockCallCauseBase>();
            CauseSet.Add(new VoiceHangupCause());
            CauseSet.Add(new IMSErrorCause());
            CauseSet.Add(new UECancelCause());
        }
        public List<BlockCallCauseBase> CauseSet
        {
            get;
            set;
        }
    }


}
