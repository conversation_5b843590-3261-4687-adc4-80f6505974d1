﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class LTEScanTestPointSplitter
    {
        public static LTEScanTestPointSplitter Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LTEScanTestPointSplitter();
                }
                return instance;
            }
        }
        
        public LTEScanWorkMode JudgeWorkMode(int earfcn)
        {
            return (earfcn >= 37800 && earfcn <= 38400)
                || (earfcn >= 38250 && earfcn <= 38450)
                || (earfcn >= 38850 && earfcn <= 39350)
                || (earfcn >= 38650 && earfcn < 38850)
                || (earfcn >= 40240 && earfcn <= 40440)
                || (earfcn >= 39350 && earfcn <= 39550)
                || (earfcn >= 41040 && earfcn <= 41240) ?
                    LTEScanWorkMode.TDD : LTEScanWorkMode.FDD;
        }

        public bool Split(TestPoint tp, LTEScanWorkMode byMode)
        {
            if (!(tp is ScanTestPoint_LTE) || (byMode != LTEScanWorkMode.FDD && byMode != LTEScanWorkMode.TDD))
            {
                return true; // 原样返回
            }

            // 指定模式下没有频点
            List<int> indexs = GetWorkModeIndexs(tp, byMode);
            if (indexs.Count == 0)
            {
                return false;
            }

            Dictionary<DTParameter, object> tmpDic = new Dictionary<DTParameter, object>();
            foreach (DTParameterInfo dpInfo in dtParamInfos)
            {
                int arrayBound = dpInfo.ArrayBounds;
                for (int i = 0; i < indexs.Count; ++i)
                {
                    int idx = indexs[i];
                    if (idx >= arrayBound)
                    {
                        continue;
                    }

                    DTParameter dtParam = dpInfo[idx];
                    DTParameter keyParam = dpInfo[i];
                    if (!tp.Parameters.Map.ContainsKey(dtParam))
                    {
                        continue;
                    }

                    tmpDic.Add(keyParam, tp.Parameters.Map[dtParam]);
                }
            }

            tp.Parameters.Map.Clear();
            tp.Parameters.Map = tmpDic;
            return true;
        }

        public void OutputDebugCsv(string fileName, List<string> paramNames)
        {
            int iLoop = 0;
            using (System.IO.StreamWriter sw = new System.IO.StreamWriter(fileName, false, Encoding.Default))
            {
                foreach (DTFileDataManager file in MainModel.GetInstance().DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        iLoop = addDataToStream(paramNames, iLoop, sw, tp);
                    }
                }
            }
        }

        private static int addDataToStream(List<string> paramNames, int iLoop, System.IO.StreamWriter sw, TestPoint tp)
        {
            sw.Write(++iLoop + "," + tp.DateTimeStringWithMillisecond + ",");
            for (int i = 0; i < 50; ++i)
            {
                object v = tp["LTESCAN_TopN_EARFCN", i];
                sw.Write(v == null ? "," : v.ToString() + ",");
            }

            foreach (string pName in paramNames)
            {
                sw.WriteLine();
                sw.Write(",,");
                for (int i = 0; i < 50; ++i)
                {
                    object v = tp[pName, i];
                    sw.Write(v == null ? "," : v.ToString() + ",");
                }
            }
            sw.WriteLine();
            return iLoop;
        }

        private List<int> GetWorkModeIndexs(TestPoint tp, LTEScanWorkMode wMode)
        {
            List<int> indexs = new List<int>();
            for (int i = 0; i < 50; ++i)
            {
                int? earfcn = (int?)tp[earfcnParamInfo[i]];
                if (earfcn == null)
                {
                    break;
                }

                if (JudgeWorkMode((int)earfcn) == wMode)
                {
                    indexs.Add(i);
                }
            }
            return indexs;
        }

        private LTEScanTestPointSplitter()
        {
            this.dtParamInfos = new List<DTParameterInfo>();
            foreach (DTDisplayParameterInfo dpInfo in DTDisplayParameterManager.GetInstance()["LTE_SCAN"].DisplayParamInfos)
            {
                dtParamInfos.Add(dpInfo.ParamInfo);
            }

            earfcnParamInfo = DTParameterManager.GetInstance().GetParameterInfo("LTESCAN_TopN_EARFCN");
        }

        private readonly List<DTParameterInfo> dtParamInfos;

        private readonly DTParameterInfo earfcnParamInfo;

        private static LTEScanTestPointSplitter instance;
    }

    public enum LTEScanWorkMode
    {
        ALL,
        TDD,
        FDD,
    }
}
