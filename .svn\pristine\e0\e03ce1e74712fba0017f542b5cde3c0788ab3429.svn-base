﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class QueryAllDistrictCategory : QueryBase
    {
        public QueryAllDistrictCategory()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "查询所有地市数据源类别"; }
        }

        public override string IconName
        {
            get { return "Images/fileq.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            List<Stat.IDNamePair> districtSet = DistrictManager.GetInstance().GetAvailableDistrict();
            foreach (Stat.IDNamePair districtP in districtSet)
            {
                MainModel.PermissionManager.ClearDataSource(districtP.id);
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(1, "Project", "项目"));
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(2, "TestType", "测试类型"));
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(3, "DeviceType", "测试设备"));
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(4, "FileType", "测试文件类型"));
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(5, "ServiceType", "业务类型"));
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(6, "Carrier", "运营商"));
                MainModel.PermissionManager.AddDataSourceCategory(districtP.id, new CategoryEnum(7, "Agent", "代维公司"));

                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port
                        , MainModel.User.LoginName, MainModel.User.Password, districtP.id) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        continue;
                    }
                    Package package = clientProxy.Package;
                    package.Command = Command.CellConfigManage;
                    package.SubCommand = SubCommand.Request;
                    package.Content.Type = RequestType.GetCategoryInfo;
                    package.Content.PrepareAddParam();
                    clientProxy.Send();
                    recieve(clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
        }

        private void recieve(ClientProxy proxy)
        {
            ClientProxy clientProxy = proxy;
            Package package = clientProxy.Package;
            try
            {
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    int areaType = -1;
                    if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else if (package.Content.Type == ResponseType.Area)
                    {
                        areaType = package.Content.GetParamInt();
                    }
                    CategoryEnumItem item = new CategoryEnumItem(package.Content.GetParamInt(), package.Content.GetParamString(), package.Content.GetParamString());
                    if (package.Content.Type == ResponseType.Project)
                    {
                        MainModel.PermissionManager.GetDataSourceCategory(clientProxy.DbID, "Project").Add(item);
                    }
                    else if (package.Content.Type == ResponseType.TestType
                        || package.Content.Type == ResponseType.DeviceType
                        || package.Content.Type == ResponseType.FileType
                        || package.Content.Type == ResponseType.Staff)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.ServiceType)
                    {
                        MainModel.PermissionManager.GetDataSourceCategory(clientProxy.DbID, "ServiceType").Add(item);
                    }
                    else if (package.Content.Type == ResponseType.Carrier)
                    {
                        MainModel.PermissionManager.GetDataSourceCategory(clientProxy.DbID, "Carrier").Add(item);
                    }
                    else if (package.Content.Type == ResponseType.Agent)
                    {
                        MainModel.PermissionManager.GetDataSourceCategory(clientProxy.DbID, "Agent").Add(item);
                    }
                    else if (package.Content.Type == ResponseType.AreaType)
                    {
                        MainModel.PermissionManager.AddAreaType(clientProxy.DbID, item);
                    }
                    else if (package.Content.Type == ResponseType.Area)
                    {
                        MainModel.PermissionManager.AddSubAreaItem(clientProxy.DbID, areaType, item);
                    }
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.ToString());
            }
        }

    }
}
