﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.IO;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPIStatSettingDlg : BaseForm
    {
        public CQTKPIStatSettingDlg()
        {
            InitializeComponent();
            Disposed += CQTKPIStatSettingDlg_Disposed;
            listView.ItemChecked += new ItemCheckedEventHandler(listView_ItemChecked);
        }

        void CQTKPIStatSettingDlg_Disposed(object sender, EventArgs e)
        {
            Application.Idle -= Application_Idle;
        }

        void listView_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            CQTPoint pnt = e.Item.Tag as CQTPoint;
            if (pnt != null)
            {
                if (e.Item.Checked)
                {
                    if (!selPnts.Contains(pnt))
                    {
                        selPnts.Add(pnt);
                    }
                }
                else
                {
                    if (selPnts.Contains(pnt))
                    {
                        selPnts.Remove(pnt);
                    }
                }
            }
        }

        private List<CQTPoint> selPnts = new List<CQTPoint>();
        public List<CQTPoint> SelCQTPnts
        {
            get { return selPnts; }
        }
        private CQTKPIReport selRpt = null;
        public CQTKPIReport SelReport
        {
            get { return selRpt; }
        }
        private List<CQTPoint> allPnts
        {
            get { return CQTPointManager.GetInstance().CQTPoints; }
        }

        CQTPoint selPnt = null;
        List<CQTPoint> regionPnts = null;
        private List<CQTPoint> curPnts;
        CQTKPIReportCfgManager rptMng;
        public void FillData(CQTPoint selPnt,List<CQTPoint> regionPnts, CQTKPIReportCfgManager rptMng)
        {
            this.selPnt = selPnt;
            this.regionPnts = regionPnts;
            btnShowSelPnt.Enabled = selPnt != null;
            btnShowRegionPnt.Enabled = regionPnts != null && regionPnts.Count > 0;
            if (selPnt != null)
            {
                curPnts = new List<CQTPoint>();
                curPnts.Add(selPnt);
            }
            else if (regionPnts != null && regionPnts.Count > 0)
            {
                curPnts = regionPnts;
            }
            else
            {
                curPnts = allPnts;
            }
            fillLVCQTPnts(curPnts);
            this.rptMng = rptMng;
            fillReportPicker(rptMng);
            checkBoxAll.Checked = true;
        }

        private void fillLVCQTPnts(List<CQTPoint> pnts)
        {
            listView.ItemChecked -= listView_ItemChecked;
            listView.BeginUpdate();
            listView.Items.Clear();
            ListViewItem[] items = new ListViewItem[pnts.Count];
            int idx = 0;
            foreach (CQTPoint pnt in pnts)
            {
                ListViewItem item = new ListViewItem(pnt.Name);
                item.Tag = pnt;
                items[idx] = item;
                idx++;
            }
            listView.Items.AddRange(items);
            listView.ItemChecked += listView_ItemChecked;
            listView.EndUpdate();
        }

        private void fillReportPicker(CQTKPIReportCfgManager rptMng)
        {
            cbxSelReport.Properties.Items.Clear();
            foreach (CQTKPIReport rpt in rptMng.Reports)
            {
                cbxSelReport.Properties.Items.Add(rpt);
            }
            if (cbxSelReport.Properties.Items.Count>0)
            {
                cbxSelReport.SelectedIndex = 0;
            }
            else
            {
                cbxSelReport.SelectedIndex = -1;
            }
            btnEditFolder.Text = CQTKPIReportCfgManager.FolderName;
        }

        private void btnEditFolder_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            dlg.RootFolder = Environment.SpecialFolder.Startup;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                btnEditFolder.Text = dlg.SelectedPath;
            }
        }

        private void btnReload_Click(object sender, EventArgs e)
        {
            if (Directory.Exists(btnEditFolder.Text))
            {
                rptMng.Load(btnEditFolder.Text);
                fillReportPicker(rptMng);
            }
        }

        private void btnEditRpt_Click(object sender, EventArgs e)
        {
            CQTKPIReportEditForm editFrm = new CQTKPIReportEditForm();
            editFrm.FillData(rptMng, selRpt);
            editFrm.ShowDialog();
            fillReportPicker(rptMng);
        }

        private void checkBoxAll_CheckedChanged(object sender, EventArgs e)
        {
            listView.Enabled = !checkBoxAll.Checked;
            buttonEditSearch.Enabled = listView.Enabled;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!checkBoxAll.Checked && selPnts.Count == 0)
            {
                MessageBox.Show("请选择地点！");
                return;
            }
            if (selRpt == null)
            {
                MessageBox.Show("请选择报表！");
                return;
            }
            if (checkBoxAll.Checked)
            {
                selPnts = curPnts;
            }
            DialogResult = DialogResult.OK;
        }

        private void cbxSelReport_SelectedIndexChanged(object sender, EventArgs e)
        {
            selRpt = cbxSelReport.SelectedItem as CQTKPIReport;
        }

        private void buttonEditSearch_EditValueChanged(object sender, EventArgs e)
        {
            Application.Idle += Application_Idle;
        }

        void Application_Idle(object sender, EventArgs e)
        {
            Application.Idle -= Application_Idle;
            searchAddr(buttonEditSearch.Text,true);
        }

        private void searchAddr(string txt,bool isFindNext)
        {
            int startIdx = 0;
            if (listView.FocusedItem != null)
            {
                listView.FocusedItem.ForeColor = Color.Black;
                listView.FocusedItem.Font = new Font(listView.FocusedItem.Font, FontStyle.Regular);
                if (isFindNext)
                {
                    startIdx = listView.FocusedItem.Index + 1;
                }
                else
                {
                    startIdx = listView.FocusedItem.Index - 1;
                }
            }
       
            if (isFindNext)
            {
                findNext(txt, startIdx);
            }
            else
            {
                findPrev(txt, startIdx);
            }
        }

        private void findNext(string txt, int startIdx)
        {
            bool found = false;
            for (int i = startIdx; i < listView.Items.Count; i++)
            {
                found = setListViewItem(txt, found, i);
            }
            if (!found)
            {
                for (int i = 0; i < startIdx; i++)
                {
                    found = setListViewItem(txt, found, i);
                }
            }
        }

        private void findPrev(string txt, int startIdx)
        {
            bool found = false;
            for (int i = startIdx; i >= 0; i--)
            {
                found = setListViewItem(txt, found, i);
            }
            if (!found)
            {
                for (int i = listView.Items.Count - 1; i > startIdx; i--)
                {
                    found = setListViewItem(txt, found, i);
                }
            }
        }

        private bool setListViewItem(string txt, bool found, int i)
        {
            ListViewItem item = listView.Items[i];
            item.ForeColor = Color.Black;
            item.Font = new Font(item.Font, FontStyle.Regular);
            if (!string.IsNullOrEmpty(txt) && item.Text.Contains(txt) && !found)
            {
                item.Focused = true;
                item.ForeColor = Color.Red;
                item.Font = new Font(item.Font, FontStyle.Bold);
                item.EnsureVisible();
                found = true;
            }

            return found;
        }

        private void buttonEditSearch_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (buttonEditSearch.Text.Trim().Length>0)
            {
                searchAddr(buttonEditSearch.Text, e.Button.Index == 1);
            }
        }

        private void buttonEditSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                string txt = buttonEditSearch.Text;
                if (!string.IsNullOrEmpty(txt))
                {
                    searchAddr(buttonEditSearch.Text, true);
                }
            }
        }

        private void btnShowAllPnt_Click(object sender, EventArgs e)
        {
            curPnts = allPnts;
            fillLVCQTPnts(curPnts);
        }

        private void btnShowSelPnt_Click(object sender, EventArgs e)
        {
            curPnts = new List<CQTPoint>();
            curPnts.Add(selPnt);
            fillLVCQTPnts(curPnts);
        }

        private void btnShowRegionPnt_Click(object sender, EventArgs e)
        {
            curPnts = regionPnts;
            fillLVCQTPnts(curPnts);
        }



        public bool IsStatMainPointOnly
        {
            get
            { return chkStatMainPointOnly.Checked; }
            set
            {
                chkStatMainPointOnly.Checked = value;
            }
        }
    }
}
