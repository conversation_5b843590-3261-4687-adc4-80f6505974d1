﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMMemoryProblemListForm : MinCloseForm
    {
        private List<FileForGSMMemoryProblem> list_info = null;
        MapDTLayer dtLayer = MainModel.GetInstance().MainForm.GetMapForm().GetDTLayer();

        public GSMMemoryProblemListForm(List<FileForGSMMemoryProblem> infos)
        {
            InitializeComponent();
            initLv_ProblemCells();
            this.list_info = infos;
            fillLv_ProblemCells(infos);
        }

        public void fillLv_ProblemCells(List<FileForGSMMemoryProblem> infos)
        {
            lv_ProblemCells.Items.Clear();
            lv_ProblemCells.ClearObjects();
            lv_ProblemCells.SetObjects(infos);
        }

        public void initLv_ProblemCells()
        {
            olvFileName.AspectGetter = delegate(object row)
            {
                if (row is FileForGSMMemoryProblem)
                {
                    FileForGSMMemoryProblem item = row as FileForGSMMemoryProblem;
                    return item.FileName;
                }
                return "";
            };

            olvTime.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    return item.OtestPoint.TimeStringWithMillisecond;
                }
                return "";
            };

            olvLongitude.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.OtestPoint.GetMainCell() != null)
                        return item.OtestPoint.GetMainCell().Longitude;
                }
                return "";
            };

            olvLatitude.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.OtestPoint.GetMainCell() != null)
                        return item.OtestPoint.GetMainCell().Latitude;
                }
                return "";
            };

            olvOCellName.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.OtestPoint.GetMainCell() != null)
                        return item.OtestPoint.GetMainCell().Name;
                    return "";
                }
                return "";
            };

            olvOLAC.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.OtestPoint.GetMainCell() != null)
                        return item.OtestPoint.GetMainCell().Token.Split('_')[0];
                    return "";
                }
                return "";
            };

            olvOCI.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.OtestPoint.GetMainCell() != null)
                        return item.OtestPoint.GetMainCell().Token.Split('_')[1];
                    return "";
                }
                return "";
            };

            olvOBCCH.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    return item.OtestPoint["BCCH"];
                }
                return "";
            };

            olvOBSIC.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    return item.OtestPoint["BSIC"];
                }
                return "";
            };

            olvORxlev.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    return item.OtestPoint["RxLevSub"];
                }
                return "";
            };

            olvDistance.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    return item.Distance;
                }
                return "";
            };

            olvPCellName.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        ICell problemCell = null;
                        if (item.MemoryProblemCellInfos[0].BCNum == 7)
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetMainCell();
                        else
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetNBCell(item.MemoryProblemCellInfos[0].BCNum);

                        if (problemCell != null)
                        {
                            return problemCell.Name;
                        }
                    }
                    return "";
                }
                return "";
            };

            olvPCellID.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        ICell problemCell = null;
                        if (item.MemoryProblemCellInfos[0].BCNum == 7)
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetMainCell();
                        else
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetNBCell(item.MemoryProblemCellInfos[0].BCNum);

                        if (problemCell != null)
                        {
                            return problemCell.ID;
                        }
                    }
                    return "";
                }
                return "";
            };

            olvPCellLAC.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        ICell problemCell = null;
                        if (item.MemoryProblemCellInfos[0].BCNum == 7)
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetMainCell();
                        else
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetNBCell(item.MemoryProblemCellInfos[0].BCNum);

                        if (problemCell != null)
                        {
                            return problemCell.Token.Split('_')[0];
                        }
                    }
                    return "";
                }
                return "";
            };

            olvPCellCI.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        ICell problemCell = null;
                        if (item.MemoryProblemCellInfos[0].BCNum == 7)
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetMainCell();
                        else
                            problemCell = item.MemoryProblemCellInfos[0].ProblemTestPoint.GetNBCell(item.MemoryProblemCellInfos[0].BCNum);

                        if (problemCell != null)
                        {
                            return problemCell.Token.Split('_')[1];
                        }
                    }
                    return "";
                }
                return "";
            };

            olvPBCCH.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        if (item.MemoryProblemCellInfos[0].BCNum == 7)
                            return item.MemoryProblemCellInfos[0].ProblemTestPoint["BCCH"];
                        else
                            return item.MemoryProblemCellInfos[0].ProblemTestPoint["N_BCCH", item.MemoryProblemCellInfos[0].BCNum];                        
                    }
                    return "";
                }
                return "";
            };

            olvPBSIC.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        if (item.MemoryProblemCellInfos[0].BCNum == 7)
                            return item.MemoryProblemCellInfos[0].ProblemTestPoint["BSIC"];
                        else
                            return item.MemoryProblemCellInfos[0].ProblemTestPoint["N_BSIC", item.MemoryProblemCellInfos[0].BCNum];                        
                    }
                    return "";
                }
                return "";
            };

            olvPRxlev.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    if (item.MemoryProblemCellInfos.Count > 0)
                    {
                        return getAverageValue(item.MemoryProblemCellInfos,"RxLev", "N_RxLev");
                    }
                    return "";
                }
                return "";
            };

            olvSixScale.AspectGetter = delegate(object row)
            {
                if (row is ZTGSMMemoryProblemInfo)
                {
                    ZTGSMMemoryProblemInfo item = row as ZTGSMMemoryProblemInfo;
                    return (Convert.ToDouble(item.ProblemTestPointCount) / Convert.ToDouble(item.testPointCount)).ToString("0.00%");
                }
                return "";
            };

            //获取折叠类
            lv_ProblemCells.CanExpandGetter += delegate(object row)
            {
                return row is FileForGSMMemoryProblem;
            };

            //获取子类
            lv_ProblemCells.ChildrenGetter = delegate(object row)
            {
                if (row is FileForGSMMemoryProblem)
                {
                    FileForGSMMemoryProblem info = row as FileForGSMMemoryProblem;
                    return info.ProblemInfos;
                }
                else
                {
                    return "";
                }
            };
        }



        #region 右击菜单点击事件
        // 展开全部节点
        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lv_ProblemCells.ExpandAll();
        }

        // 收缩全部节点
        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lv_ProblemCells.CollapseAll();
        }

        // 导出Excel
        private void miExportExcel_Click(object sender, EventArgs e)
        {
            lv_ProblemCells.ExpandAll();
            if (lv_ProblemCells.Items.Count > 65535)
            {
                TxtExporter.Export(lv_ProblemCells, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lv_ProblemCells);
            }
        }

        // 导出TXT
        private void miExportTxt_Click(object sender, EventArgs e)
        {
            lv_ProblemCells.ExpandAll();
            TxtExporter.Export(lv_ProblemCells, true);
        }

        // 显示问题采样点
        private void miShowTestPoint_Click(object sender, EventArgs e)
        {
            if (list_info.Count <= 0)
                return;
            lines.Clear();
            foreach (FileForGSMMemoryProblem file in list_info)
            {
                foreach (ZTGSMMemoryProblemInfo info in file.ProblemInfos)
                {
                    for (int i = 0; i < info.MemoryProblemCellInfos.Count; i++)
                    {
                        MainModel.DTDataManager.Add(info.MemoryProblemCellInfos[i].ProblemTestPoint);
                    }
                }
            }
            TempLayer.Instance.Draw(DrawLine);
            //this.MainModel.IsFileReplayByCompareMode = false;
            this.MainModel.FireSetDefaultMapSerialTheme("GSM", "RxLevSub");
            this.MainModel.FireDTDataChanged(this);
            //MainModel.MainForm.GetMapForm().GoToView(list_info[0].ProblemInfos[0].MemoryProblemCellInfos[0].ProblemTestPoint.Longitude,
            //    list_info[0].ProblemInfos[0].MemoryProblem1CellInfos[0].ProblemTestPoint.Latitude);
        }


        // 显示问题采样点（链接同隐患小区的采样点）
        private void miShowTestPointLink_Click(object sender, EventArgs e)
        {
            if (list_info.Count <= 0)
                return;
            lines.Clear();
            foreach (FileForGSMMemoryProblem file in list_info)
            {
                foreach (ZTGSMMemoryProblemInfo info in file.ProblemInfos)
                {
                    //foreach (ZTGSMMemoryProblemInfo.MemoryProblemCellInfo cellInfo in info.MemoryProblemCellInfos)
                    //{
                    //    MainModel.DTDataManager.Add(cellInfo.ProblemTestPoint);
                    //}
                    for (int i = 0; i < info.MemoryProblemCellInfos.Count; i++)
                    {
                        MainModel.DTDataManager.Add(info.MemoryProblemCellInfos[i].ProblemTestPoint);
                        if (i >= 1)
                        {
                            Line line = new Line();
                            line.P1 = new DbPoint(info.MemoryProblemCellInfos[i].ProblemTestPoint.Longitude, info.MemoryProblemCellInfos[i].ProblemTestPoint.Latitude);
                            line.P2 = new DbPoint(info.MemoryProblemCellInfos[i - 1].ProblemTestPoint.Longitude, info.MemoryProblemCellInfos[i - 1].ProblemTestPoint.Latitude);
                            line.LineColor = Color.Red;
                            lines.Add(line);
                        }
                    }
                }
            }
            TempLayer.Instance.Draw(DrawLine);
            //this.MainModel.IsFileReplayByCompareMode = false;
            this.MainModel.FireSetDefaultMapSerialTheme("GSM", "RxLevSub");
            this.MainModel.FireDTDataChanged(this);
            //MainModel.MainForm.GetMapForm().GoToView(list_info[0].ProblemInfos[0].MemoryProblemCellInfos[0].ProblemTestPoint.Longitude,
            //    list_info[0].ProblemInfos[0].MemoryProblemCellInfos[0].ProblemTestPoint.Latitude);
        }

        private string getAverageValue(List<ZTGSMMemoryProblemInfo.MemoryProblemCellInfo> memoryProblemCellInfos, string keySC, string keyNB)
        {
            try
            {
                double sum = 0;
                foreach (ZTGSMMemoryProblemInfo.MemoryProblemCellInfo info in memoryProblemCellInfos)
                {
                    if (info.BCNum == 7)
                        sum += Convert.ToDouble(info.ProblemTestPoint[keySC]);
                    else
                        sum += Convert.ToDouble(info.ProblemTestPoint[keyNB, info.BCNum]);                    
                }
                return Math.Round(sum / memoryProblemCellInfos.Count, 2).ToString();
            }
            catch
            {
                return "";
            }
        } 
        #endregion     

        private void lv_ProblemCells_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListView lv = sender as TreeListView;
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            ZTGSMMemoryProblemInfo problemInfo = null;

            if (info.RowObject is ZTGSMMemoryProblemInfo)
            {
                problemInfo = info.RowObject as ZTGSMMemoryProblemInfo;                

                lines.Clear();
                this.MainModel.DTDataManager.Clear();
                this.MainModel.DTDataManager.Add(problemInfo.OtestPoint);
                Cell SCcell = problemInfo.OtestPoint.GetMainCell() as Cell;
                Cell Pcell = problemInfo.MemoryProblemCellInfos[0].ProblemTestPoint.GetNBCell
                    (problemInfo.MemoryProblemCellInfos[0].BCNum) as Cell;
                
                if(SCcell != null)
                {
                    Line line = new Line();
                    line.P1 = new DbPoint(problemInfo.OtestPoint.Longitude, problemInfo.OtestPoint.Latitude);
                    DbPoint point = dtLayer.GetGSMAntennaEndPoint(SCcell, problemInfo.OtestPoint.Longitude, problemInfo.OtestPoint.Latitude);
                    line.P2 = new DbPoint(point.x, point.y);
                    line.LineColor = Color.Green;
                    lines.Add(line);
                }
                if (Pcell != null)
                {
                    Line line = new Line();
                    line.P1 = new DbPoint(problemInfo.OtestPoint.Longitude, problemInfo.OtestPoint.Latitude);
                    DbPoint point = dtLayer.GetGSMAntennaEndPoint(Pcell, problemInfo.OtestPoint.Longitude, problemInfo.OtestPoint.Latitude);
                    line.P2 = new DbPoint(point.x, point.y);
                    line.LineColor = Color.Red;
                    line.isDash = true;
                    lines.Add(line);
                }
                
                TempLayer.Instance.Draw(DrawLine);
                this.MainModel.IsFileReplayByCompareMode = false;                        
                this.MainModel.FireSetDefaultMapSerialTheme("GSM", "RxLevSub");
                this.MainModel.FireDTDataChanged(this);

            }
        }

        private void DrawLine(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (lines == null || lines.Count == 0)
            {
                return;
            }

            foreach (Line line in lines)
            {
                PointF p1, p2;
                mop.ToDisplay(line.P1, out p1);
                mop.ToDisplay(line.P2, out p2);
                Pen pen = new Pen(line.LineColor, 1);
                if(line.isDash)
                    pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                graphics.DrawLine(pen, p1, p2);
            }
        }
        private List<Line> lines = new List<Line>();

        private class Line
        {
            public DbPoint P1;
            public DbPoint P2;
            public Color LineColor;
            public bool isDash = false;

        }


    }
}
