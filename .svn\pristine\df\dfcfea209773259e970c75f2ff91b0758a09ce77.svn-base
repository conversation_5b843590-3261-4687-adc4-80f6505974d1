﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func.ProblemBlock;
namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class ZTDIYQueryScanAnalysisEventOpLayer : CustomDrawLayer
    {
        public Pen selPen { get; set; } = new Pen(Color.Cyan, 3);
        public ZTDIYQueryScanAnalysisEventOpLayer(MapOperation mp, string name)
            : base(mp, name)
        {
           
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if(!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(30 * 10000 / Map.Scale), (int)(30 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951
            double llGap = (0.000195 / 20) * 100;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);

            DrawItem(MainModel.SelectedEvents, graphics);
        }

        private void DrawItem(List<Event> eventList, Graphics graphics)
        {
            foreach (Event evt in eventList)
            {
                Region blockReg = null;
                DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                float radius = 15;
                RectangleF rectN = new RectangleF(point.X - radius, point.Y - radius, radius * 3 / 2, radius * 3 / 2);
                GraphicsPath gp = new GraphicsPath();
                gp.AddEllipse(rectN);
                blockReg = new Region(gp);
                graphics.DrawImage(EventInfoManager.GetInstance()[evt.ID].Image, rectN);
                RectangleF rctf = blockReg.GetBounds(graphics);
                graphics.DrawRectangle(selPen, (int)rctf.Left, (int)rctf.Top, (int)rctf.Width, (int)rctf.Height);
            }
        }
    }
}
