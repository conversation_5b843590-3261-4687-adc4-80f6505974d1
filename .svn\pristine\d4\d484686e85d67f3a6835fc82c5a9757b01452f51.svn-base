using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func.PopShow
{
    class WelFrmMainPanel:Panel
    {
        protected override void OnMouseWheel(MouseEventArgs e)
        {
            KPIInfoPanel_ng kpiPanel = this.Controls[0].Controls[0] as KPIInfoPanel_ng;
            if (kpiPanel != null && kpiPanel.IsChartFocused)
            {
                return;
            }
            base.OnMouseWheel(e);
        }
    }
}
