﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRDropCallAnaDlg : BaseDialog
    {
        public NRDropCallAnaDlg()
        {
            InitializeComponent();
        }

        public NRDropCallAnaCondtion GetCondition()
        {
            NRDropCallAnaCondtion cond = new NRDropCallAnaCondtion();
            cond.HoNum = (int)numHoNum.Value;
            cond.HoSec = (int)numHoSec.Value;
            cond.PoorSinr = (int)numSinr.Value;
            cond.PoorSinrSec = (int)numSinrSec.Value;
            cond.WeakRsrp = (float)numRsrp.Value;
            cond.WeakRsrpSec = (int)numRsrpSec.Value;
            cond.MultiSec = (int)numMultiSec.Value;
            cond.MultiBand = (int)numMultiBand.Value;
            cond.MultiPer = (float)numMultiPer.Value;
            cond.MultiValue = (int)numMultiValue.Value;
            return cond;
        }

        public void SetCondition(NRDropCallAnaCondtion value)
        {
            if (value == null)
            {
                return;
            }
            numHoNum.Value = value.HoNum;
            numHoSec.Value = value.HoSec;
            numRsrp.Value = (decimal)value.WeakRsrp;
            numRsrpSec.Value = value.WeakRsrpSec;
            numSinr.Value = value.PoorSinr;
            numSinrSec.Value = value.PoorSinrSec;

            numMultiSec.Value = value.MultiSec;
            numMultiBand.Value = value.MultiBand;
            numMultiPer.Value = (decimal)value.MultiPer;
            numMultiValue.Value = value.MultiValue;
        }
    }
}
