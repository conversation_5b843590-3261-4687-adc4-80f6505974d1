﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LogDownloadInfoForm : MinCloseForm
    {
        public LogDownloadInfoForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
        }

        public void Fill(object data)
        {
            gridControl1.DataSource = data;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
