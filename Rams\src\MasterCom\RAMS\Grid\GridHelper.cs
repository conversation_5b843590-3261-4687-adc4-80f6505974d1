﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Grid
{
    /* 全图栅格的位置是固定的，以经纬度（0,0）的点为原点，北半球地图时，是在屏幕的左下方。
     * 随着纬度的增大，对应行数增大；经度增加，对应列数增加。
     * 以屏幕来看，从左往右为列数增大，从下往上为行数增大。
     */
    /// <summary>
    /// 说明请见该类注释
    /// </summary>
    public static class GridHelper
    {
        public static void GetIndexOfGrid(double longitude, double latitude, double lngSpan, double latSpan, out int rowIdx, out int colIdx)
        {
            GridIndex idx = new GridIndex();
            GetIndexOfCustomSizeGrid(1, lngSpan, latSpan, longitude, latitude, idx);
            rowIdx = idx.RowIdx;
            colIdx = idx.ColIdx;
        }

        /// <summary>
        ///  默认的栅格大小下，获取某点的对应的绝对行列位置索引（默认的栅格大小，只能为 40*40 或100*100由编译开关决定）
        ///  绝对位置是指，相对于（0,0）经纬度点为栅格原点的行列位置。北半球地图看，是左下角。
        /// </summary>
        /// <param name="longitude">某点的经度</param>
        /// <param name="latitude">某点的纬度</param>
        /// <param name="rowIdx">栅格的绝对行位置索引</param>
        /// <param name="colIdx">栅格的绝对列位置索引</param>
        public static void GetIndexOfDefaultSizeGrid(double longitude, double latitude, out int rowIdx, out int colIdx)
        {
            GridIndex idx = new GridIndex();
            GetIndexOfCustomSizeGrid(1,CD.ATOM_SPAN_LONG,CD.ATOM_SPAN_LAT, longitude, latitude, idx);
            rowIdx = idx.RowIdx;
            colIdx = idx.ColIdx;
        }

        public static void GetIndexOfDefaultSizeGrid(double longitude, double latitude, out int rowIdx, out int colIdx, out int nearRowIdx, out int nearColIdx)
        {
            GridIndex idx = new GridIndex();
            GetIndexOfCustomSizeGrid(1, CD.ATOM_SPAN_LONG, CD.ATOM_SPAN_LAT, longitude, latitude, idx);
            rowIdx = idx.RowIdx;
            colIdx = idx.ColIdx;
            nearRowIdx = idx.NearRowIdx;
            nearColIdx = idx.NearColIdx;
        }

        /// <summary>
        /// 获取sizeRadio整数倍默认栅格大小的自定义栅格，绝对行列位置索引
        /// </summary>
        /// <param name="sizeRadio">正整数倍默认栅格大小</param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="rowIdx"></param>
        /// <param name="colIdx"></param>
        public static void GetIndexOfCustomSizeGrid(int sizeFactor, double longitude, double latitude, out int rowIdx, out int colIdx)
        {
            //行位置由纬度计算：纬度 / 单个栅格纬度的跨度 向下取整。
            //double精度问题，需要转成decimal计算
            double row = (double)((decimal)latitude / ((decimal)CD.ATOM_SPAN_LAT * sizeFactor));
            rowIdx = (int)Math.Floor(row);
            //列位置由经度计算：经度 / 单个栅格经度的跨度 向下取整。
            //double精度问题，需要转成decimal计算
            double col = (double)((decimal)longitude / ((decimal)CD.ATOM_SPAN_LONG * sizeFactor));
            colIdx = (int)Math.Floor(col);
        }

        /// <summary>
        /// 获取sizeRadio整数倍默认栅格大小的自定义栅格，绝对行列位置索引
        /// </summary>
        /// <param name="sizeRadio">正整数倍默认栅格大小</param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="rowIdx"></param>
        /// <param name="colIdx"></param>
        public static void GetIndexOfCustomSizeGrid(int sizeFactor,double lngSpan,double latSpan, double longitude, double latitude, GridIndex idx)
        {
            //行位置由纬度计算：纬度 / 单个栅格纬度的跨度 向下取整。
            //double精度问题，需要转成decimal计算
            double row = (double)((decimal)latitude / ((decimal)latSpan * sizeFactor));
            idx.RowIdx = (int)Math.Floor(row);
            if (row - Math.Floor(row) >= 0.5)
            {//更靠右
                idx.NearRowIdx = idx.RowIdx + 1;
            }
            else
            {
                idx.NearRowIdx = idx.RowIdx - 1;
            }
            //列位置由经度计算：经度 / 单个栅格经度的跨度 向下取整。
            //double精度问题，需要转成decimal计算
            double col = (double)((decimal)longitude / ((decimal)lngSpan * sizeFactor));
            idx.ColIdx = (int)Math.Floor(col);
            if (col - Math.Floor(col) >= 0.5)
            {
                idx.NearColIdx = idx.ColIdx + 1;
            }
            else
            {
                idx.NearColIdx = idx.ColIdx - 1;
            }
        }

        public class GridIndex
        {
            public int RowIdx { get; set; }
            public int ColIdx { get; set; }
            public int NearRowIdx { get; set; }
            public int NearColIdx { get; set; }
        }

        /// <summary>
        /// 通过栅格的行列，获取栅格左下角的经纬度
        /// </summary>
        /// <param name="rowIdx">栅格所在行位置索引</param>
        /// <param name="colIdx">栅格所在列位置索引</param>
        /// <param name="longitude">栅格左下角的经度</param>
        /// <param name="latitude">栅格左下角的纬度</param>
        public static void GetLeftBottomByDefaultSizeGridIndex(int rowIdx, int colIdx, out double longitude, out double latitude)
        {
            GetLeftBottomByCustomSizeGridIndex(1, rowIdx, colIdx, out longitude, out latitude);
        }

        public static void GetLeftBottomByCustomSizeGridIndex(int sizeFactor, int rowIdx, int colIdx, out double longitude, out double latitude)
        {
            longitude = (double)(colIdx * (decimal)CD.ATOM_SPAN_LONG * (decimal)sizeFactor);
            latitude = (double)(rowIdx * (decimal)CD.ATOM_SPAN_LAT * (decimal)sizeFactor);
        }

        public static void GetLeftTopByCustomSizeGridIndex(int sizeFactor, int rowIdx, int colIdx, out double longitude, out double latitude)
        {
            GetLeftBottomByCustomSizeGridIndex(sizeFactor, rowIdx + 1, colIdx, out longitude, out latitude);
        }

        /// <summary>
        /// 根据默认栅格大小，圆整区域。
        /// </summary>
        /// <param name="rect2Round"></param>
        /// <returns></returns>
        public static DbRect RoundRectByDefaultSizeGrid(DbRect rect2Round)
        {
            return RoundRectByCustomSizeGrid(1, rect2Round);
        }

        /// <summary>
        /// 圆整某个矩形区域以适应栅格，保证栅格都该区域内
        /// </summary>
        /// <param name="sizeFactor"></param>
        /// <param name="rect2Round"></param>
        /// <returns></returns>
        public static DbRect RoundRectByCustomSizeGrid(int sizeFactor, DbRect rect2Round)
        {
            GridIndex idx = new GridIndex();
            GetIndexOfCustomSizeGrid(sizeFactor,CD.ATOM_SPAN_LONG,CD.ATOM_SPAN_LAT, rect2Round.x1, rect2Round.y1, idx);
            int bottomLeftRowIdx = idx.RowIdx;
            int bottomLeftColIdx = idx.ColIdx;
            idx = new GridIndex();
            GetIndexOfCustomSizeGrid(sizeFactor,CD.ATOM_SPAN_LONG,CD.ATOM_SPAN_LAT, rect2Round.x2, rect2Round.y2, idx);
            int topRightRowIdx = idx.RowIdx;
            int topRightColIdx = idx.ColIdx;
            return new DbRect((double)(bottomLeftColIdx * (decimal)CD.ATOM_SPAN_LONG * sizeFactor)
                            , (double)(bottomLeftRowIdx * (decimal)CD.ATOM_SPAN_LAT * sizeFactor)
                            , (double)((topRightColIdx + 1) * (decimal)CD.ATOM_SPAN_LONG * sizeFactor)//栅格的右边，需加1
                            , (double)((topRightRowIdx + 1) * (decimal)CD.ATOM_SPAN_LAT * sizeFactor));//栅格的上边，需加1
        }


        public static DbRect GetDefaultSizeGridBoundsByLeftTopPoint(double ltLng, double ltLat)
        {
            DbRect rect = new DbRect();
            rect.x1 = ltLng;
            rect.y2 = ltLat;
            rect.x2 = ltLng + CD.ATOM_SPAN_LONG;
            rect.y1 = ltLat - CD.ATOM_SPAN_LAT;
            return rect;
        }

        public static DbRect GetCustomSizeBounds(double lng , double lat , double gridSpanDegree)
        {
            DbRect rect = new DbRect();
            int xIdx = (int)(lng/gridSpanDegree);
            int yIdx = (int)(lat/gridSpanDegree);
            rect.x1 = gridSpanDegree*xIdx;
            rect.x2 = rect.x1 + gridSpanDegree;
            rect.y1 = gridSpanDegree*yIdx;
            rect.y2 = rect.y1 + gridSpanDegree;
            return rect;
        }

        /// <summary>
        /// 得到某经度所在栅格的左边经度
        /// </summary>
        /// <param name="lng"></param>
        /// <returns></returns>
        public static double RoundAsLeft(double lng)
        {
            int count = (int)(lng / CD.ATOM_SPAN_LONG);
            return (double)((decimal)CD.ATOM_SPAN_LONG * count);
        }

        /// <summary>
        /// 得到某纬度所在栅格的上边纬度
        /// </summary>
        /// <param name="lat"></param>
        /// <returns></returns>
        public static double RoundAsTop(double lat)
        {
            int count = (int)((decimal)lat / (decimal)CD.ATOM_SPAN_LAT) + 1;
            return (double)((decimal)CD.ATOM_SPAN_LAT * count);
        }

    }
}
