﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanRelatedDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.spinEditAgo = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditLater = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAgo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLater.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // spinEditAgo
            // 
            this.spinEditAgo.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditAgo.Location = new System.Drawing.Point(57, 19);
            this.spinEditAgo.Name = "spinEditAgo";
            this.spinEditAgo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditAgo.Properties.Mask.EditMask = "f0";
            this.spinEditAgo.Size = new System.Drawing.Size(82, 21);
            this.spinEditAgo.TabIndex = 0;
            // 
            // spinEditLater
            // 
            this.spinEditLater.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditLater.Location = new System.Drawing.Point(198, 19);
            this.spinEditLater.Name = "spinEditLater";
            this.spinEditLater.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditLater.Properties.Mask.EditMask = "f0";
            this.spinEditLater.Size = new System.Drawing.Size(82, 21);
            this.spinEditLater.TabIndex = 0;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(18, 23);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 14);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "关联前";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(143, 23);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(28, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "个月,";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(182, 23);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 14);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "后";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(284, 23);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 14);
            this.labelControl4.TabIndex = 1;
            this.labelControl4.Text = "个月的数据";
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Location = new System.Drawing.Point(182, 71);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Location = new System.Drawing.Point(269, 71);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // ScanRelatedDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(371, 122);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.spinEditLater);
            this.Controls.Add(this.spinEditAgo);
            this.Name = "ScanRelatedDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "扫频关联时间设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAgo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLater.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit spinEditAgo;
        private DevExpress.XtraEditors.SpinEdit spinEditLater;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
    }
}