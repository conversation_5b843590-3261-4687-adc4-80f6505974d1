﻿using MasterCom.Util;
using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsSerialGridFinder<T>  where T : ILteMgrsBlockItem
    {
        /// <summary>
        /// 理论上2相邻栅格中心经纬度最远为√2倍的栅格边长,考虑偏差取1.5倍
        /// </summary>
        protected const double gridSideScale = 1.5d;
        int[] parents = null;
        readonly double gridSize;

        public NbIotMgrsSerialGridFinder(double gridSize)
        {
            this.gridSize = gridSize;
        }

        /// <summary>
        /// 根据栅格集合使用并查集计算连续栅格合集
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        public Dictionary<int, List<T>> SerialGridFinder(List<T> items, bool isSerial)
        {
            Dictionary<int, List<T>> idItemsDic = new Dictionary<int, List<T>>();

            //建立一个新的并查集,包含n个元素的合集
            parents = new int[items.Count];
            for (int i = 0; i < parents.Length; ++i)
            {
                parents[i] = i;
            }
           
            //计算连续栅格
            if (isSerial)
            {
                calculateSerialGrid(items);
            }

            // 同类合并
            for (int i = 0; i < parents.Length; ++i)
            {
                int par = Find(i);
                if (!idItemsDic.ContainsKey(par))
                {
                    idItemsDic.Add(par, new List<T>());
                }
                idItemsDic[par].Add(items[i]);
            }

            return idItemsDic;
        }

        private void calculateSerialGrid(List<T> items)
        {
            for (int i = 0; i < items.Count - 1; ++i)
            {
                for (int j = 0; j < items.Count; ++j)
                {
                    double dDistance = MathFuncs.GetDistance(items[i].CentLng, items[i].CentLat, items[j].CentLng, items[j].CentLat);
                    if (dDistance < gridSideScale * gridSize)
                    {
                        Union(i, j);
                    }
                }
            }
        }

        private int Find(int x)
        {
            if (x != parents[x])
            {
                //路径压缩 一直找到父节点,将子节点直接指向父节点
                parents[x] = Find(parents[x]);
            }
            return parents[x];
        }

        private void Union(int x, int y)
        {
            int px = Find(x);
            int py = Find(y);
            if(px != py)
                parents[py] = px;
        }
    }
}
