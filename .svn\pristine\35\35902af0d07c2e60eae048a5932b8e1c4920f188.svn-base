﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYWeakCoverTDPccpchRscpAnaByRegion : ZTDIYGSMWeakCovRoadQueryByRegion
    {
        public ZTWeakCovRoadCondition_TD weakCovRoadCond_TD { get; set; } = new ZTWeakCovRoadCondition_TD();

        private static DIYWeakCoverTDPccpchRscpAnaByRegion intance = null;
        public static new DIYWeakCoverTDPccpchRscpAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYWeakCoverTDPccpchRscpAnaByRegion();
                    }
                }
            }
            return intance;
        }

        protected DIYWeakCoverTDPccpchRscpAnaByRegion()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            ServiceTypes.Add(ServiceType.TDSCDMA_VIDEO);
            ServiceTypes.Add(ServiceType.TDSCDMA_IDLE);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSDPA);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖路段_TD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13001, this.Name);
        }

        TDPccpchRscpWeakCoverConditionDlg setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new TDPccpchRscpWeakCoverConditionDlg();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                weakCovRoadCond_TD = setForm.GetSetCondition();
                return true;
            }
            return false;
        } 

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    TDWeakCovRoadInfo info = new TDWeakCovRoadInfo();
                    info = getTDWeakCovRoadInfo(fileDataManager, info);

                    saveWeakCovRoadInfo(info);     ////最后一段
                }
            }
            catch
            {
                //continue
            }
        }

        private TDWeakCovRoadInfo getTDWeakCovRoadInfo(DTFileDataManager fileDataManager, TDWeakCovRoadInfo info)
        {
            List<TestPoint> testPointList = fileDataManager.TestPoints;
            double tmpLongitude = 0;
            double tmpLatitude = 0;
            if (testPointList.Count > 0)
            {
                tmpLongitude = testPointList[0].Longitude;
                tmpLatitude = testPointList[0].Latitude;
            }

            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                double distance = 0;

                if (isValidTestPoint(testPoint))
                {
                    setTDWeakCovRoadInfo(ref info, testPointList, tmpLongitude, tmpLatitude, i, testPoint, ref distance);
                }
                else
                {
                    saveWeakCovRoadInfo(info);
                    info = new TDWeakCovRoadInfo();    //重置
                }
                tmpLongitude = testPointList[i].Longitude;
                tmpLatitude = testPointList[i].Latitude;
            }

            return info;
        }

        private void setTDWeakCovRoadInfo(ref TDWeakCovRoadInfo info, List<TestPoint> testPointList, double tmpLongitude, double tmpLatitude, int i, TestPoint testPoint, ref double distance)
        {
            float? rscp = (float?)testPoint["TD_PCCPCH_RSCP"];
            int? pccpchC2i = (int?)testPoint["TD_PCCPCH_C2I"];
            int? dpchC2i = (int?)testPoint["TD_DPCH_C2I"];
            int maxNCellRscp = int.MinValue;
            for (int n = 0; n < 20; n++)
            {
                int? ncellRscp = (int?)testPoint["TD_NCell_PCCPCH_RSCP", n];
                if (ncellRscp != null && ncellRscp <= -10 && ncellRscp >= -140)
                {
                    maxNCellRscp = Math.Max((int)ncellRscp, maxNCellRscp);
                }
                else
                {
                    break;
                }
            }
            if (weakCovRoadCond_TD.IsValidate(testPoint, rscp, pccpchC2i, dpchC2i))
            {
                //指标符合弱覆盖条件，再判断2采样点距离
                distance = MathFuncs.GetDistance(testPoint.Longitude, testPoint.Latitude, tmpLongitude, tmpLatitude);
                if (weakCovRoadCond_TD.MatchMinDisanceOf2Point(distance))
                { //采样点距离符合条件
                    info.Add((float)rscp, pccpchC2i, dpchC2i, distance, testPoint, maxNCellRscp);
                    if (i == testPointList.Count - 1)//最后一点
                    {
                        saveWeakCovRoadInfo(info);
                        info = new TDWeakCovRoadInfo();
                    }
                }
                else
                {
                    saveWeakCovRoadInfo(info);
                    info = new TDWeakCovRoadInfo();
                    info.Add((float)rscp, pccpchC2i, dpchC2i, distance, testPoint, maxNCellRscp);
                }
            }
            else
            {
                saveWeakCovRoadInfo(info);
                info = new TDWeakCovRoadInfo();    //重置
            }
        }

        protected virtual void saveWeakCovRoadInfo(TDWeakCovRoadInfo info)
        {
            if (weakCovRoadCond_TD.MatchCoverMinDistance(info.Distance ))  //超过距离门限
            {
                info.SN = ZTWeakCovRoadInfoList.Count + 1;
                info.GetResult(saveTestPoints);
                ZTWeakCovRoadInfoList.Add(info);

                expandSampleRect(info.SampleList);
            }
        }

        protected override void getResultsAfterQuery()
        {
            List<TDCell> cellInLst = getTDCellInGeometry();

            foreach (ZTWeakCovRoadInfo info in ZTWeakCovRoadInfoList)
            {
                TDWeakCovRoadInfo tdInfo = info as TDWeakCovRoadInfo;
                foreach (TestPoint tp in tdInfo.SampleList)
                {
                    bool isPlanProblem = judgePlanProblem(cellInLst, tp);

                    if (isPlanProblem)
                    {
                        tdInfo.SampleCountPlan++;
                    }
                    else
                    {
                        tdInfo.SampleCountOp++;
                    }

                    if (saveTestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
        }

        private bool judgePlanProblem(List<TDCell> cellInLst, TestPoint tp)
        {
            bool isPlanProblem = true;   //判断是优化问题，还是规划问题
            foreach (TDCell cell in cellInLst)
            {
                if (cell.GetDistance(tp.Longitude, tp.Latitude) <= weakCovRoadCond_TD.sampleCellDistance)
                {
                    bool isValid = MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, tp.Longitude, tp.Latitude, (int)cell.Direction, weakCovRoadCond_TD.sampleCellAngle);
                    if (isValid)
                    {
                        isPlanProblem = false;  //采样点在小区的方向角内，是优化问题
                        break;
                    }
                }
            }

            return isPlanProblem;
        }

        private List<TDCell> getTDCellInGeometry()
        {
            ////外扩sampleRect
            double offset = (double)weakCovRoadCond_TD.sampleCellDistance / 100000;
            sampleRect.x1 -= offset;
            sampleRect.x2 += offset;
            sampleRect.y1 -= offset;
            sampleRect.y2 += offset;

            List<TDCell> cells = this.MainModel.CellManager.GetCurrentTDCells();
            List<TDCell> cellInLst = new List<TDCell>();

            foreach (TDCell cell in cells)
            {
                if (cell.Type == TDNodeBType.Indoor)
                {
                    continue;
                }

                if (sampleRect.IsPointInThisRect(cell.Longitude, cell.Latitude))
                {
                    cellInLst.Add(cell);
                }
            }

            return cellInLst;
        }


        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RSCPThreshold"] = weakCovRoadCond_TD.rscpThreshold;
                param["PccpchC_IThreshold"] = weakCovRoadCond_TD.pcchC_iThreshold;
                param["DpchC_IThreshold"] = weakCovRoadCond_TD.dpchC_iThreshold;
                param["SampleDistance"] = weakCovRoadCond_TD.sampleDistance;
                param["CheckPccpchC2I"] = weakCovRoadCond_TD.checkPccpchC2I;
                param["CheckDpchC2I"] = weakCovRoadCond_TD.checkDpchC2I;
                param["MinCovrDistance"] = weakCovRoadCond_TD.roadDistance;
                param["SampleCellDistance"] = weakCovRoadCond_TD.sampleCellDistance;
                param["SampleCellAngle"] = weakCovRoadCond_TD.sampleCellAngle;
                param["CheckNCellRscp"] = weakCovRoadCond_TD.checkNCellRscp;
                param["NcellRscpMax"] = weakCovRoadCond_TD.ncellRscpMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                weakCovRoadCond_TD = new ZTWeakCovRoadCondition_TD();
                if (param.ContainsKey("RSCPThreshold"))
                {
                    weakCovRoadCond_TD.rscpThreshold = int.Parse(param["RSCPThreshold"].ToString());
                }
                if (param.ContainsKey("PccpchC_IThreshold"))
                {
                    weakCovRoadCond_TD.pcchC_iThreshold = int.Parse(param["PccpchC_IThreshold"].ToString());
                }
                if (param.ContainsKey("DpchC_IThreshold"))
                {
                    weakCovRoadCond_TD.dpchC_iThreshold = int.Parse(param["DpchC_IThreshold"].ToString());
                }
                if (param.ContainsKey("SampleDistance"))
                {
                    weakCovRoadCond_TD.sampleDistance = int.Parse(param["SampleDistance"].ToString());
                }
                if (param.ContainsKey("CheckPccpchC2I"))
                {
                    weakCovRoadCond_TD.checkPccpchC2I = (bool)param["CheckPccpchC2I"];
                }
                if (param.ContainsKey("CheckDpchC2I"))
                {
                    weakCovRoadCond_TD.checkDpchC2I = (bool)param["CheckDpchC2I"];
                }                
                if (param.ContainsKey("MinCovrDistance"))
                {
                    weakCovRoadCond_TD.roadDistance = int.Parse(param["MinCovrDistance"].ToString());
                }
                if (param.ContainsKey("SampleCellDistance"))
                {
                    weakCovRoadCond_TD.sampleCellDistance = int.Parse(param["SampleCellDistance"].ToString());
                }
                if (param.ContainsKey("SampleCellAngle"))
                {
                    weakCovRoadCond_TD.sampleCellAngle = int.Parse(param["SampleCellAngle"].ToString());
                }
                if (param.ContainsKey("CheckNCellRscp"))
                {
                    weakCovRoadCond_TD.checkNCellRscp = (bool)param["CheckNCellRscp"];
                }
                if (param.ContainsKey("NcellRscpMax"))
                {
                    weakCovRoadCond_TD.ncellRscpMax = int.Parse(param["NcellRscpMax"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new WeakCovRoadProperties_TD(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (ZTWeakCovRoadInfo item in ZTWeakCovRoadInfoList)
            {
                TDWeakCovRoadInfo info = item as TDWeakCovRoadInfo;
                BackgroundResult result = info.ConvertToBackgroundResult();
                result.SN = ISnIndex++;
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            ZTWeakCovRoadInfoList.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                float fSampleCountOp = bgResult.GetImageValueFloat();
                float fSampleCountPlan = bgResult.GetImageValueFloat();
                string strLacCi = bgResult.GetImageValueString();
                string strProject = bgResult.GetImageValueString();
                if (strProject != "" && !BackgroundFuncBaseSetting.GetInstance().projectType.Equals(strProject))
                {
                    continue;
                }
                float pccpchC2iAvg = bgResult.GetImageValueFloat();
                int pccpchC2iMin = bgResult.GetImageValueInt();
                int pccpchC2iMax = bgResult.GetImageValueInt();
                float dpchC2iAvg = bgResult.GetImageValueFloat();
                int dpchC2iMin = bgResult.GetImageValueInt();
                int dpchC2iMax = bgResult.GetImageValueInt();
                StringBuilder sb = new StringBuilder();
                sb.Append("序号：");
                sb.Append(bgResult.SN);
                sb.Append("\r\n");
                sb.Append("优化问题比例：");
                sb.Append(fSampleCountOp);
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("规划问题比例：");
                sb.Append(fSampleCountPlan);
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("小区信息：");
                sb.Append(strLacCi);
                sb.Append("PccpchC/I均值：");
                sb.Append(pccpchC2iAvg);
                sb.Append("\r\n");
                sb.Append("PccpchC/I最小值：");
                sb.Append(pccpchC2iMin);
                sb.Append("\r\n");
                sb.Append("PccpchC/I最大值：");
                sb.Append(pccpchC2iMax);
                sb.Append("\r\n");
                sb.Append("DpchC/I均值：");
                sb.Append(dpchC2iAvg);
                sb.Append("\r\n");
                sb.Append("DpchC/I最小值：");
                sb.Append(dpchC2iMin);
                sb.Append("\r\n");
                sb.Append("DpchC/I最大值：");
                sb.Append(dpchC2iMax);
                sb.Append("\r\n");
                sb.Append("项目ID：");
                sb.Append(strProject);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    public class TDWeakCovRoadInfo : ZTWeakCovRoadInfo
    {
        private int pccpchC2iMax=int.MinValue;
        public int PccpchC2iMax
        {
            get { return pccpchC2iMax; }
        }
        private int pccpchC2iMin=int.MaxValue;
        public int PccpchC2iMin
        {
            get { return pccpchC2iMin; }
        }
        private float pccpchC2iAvg=0;
        public float PccpchC2iAvg
        {
            get { return pccpchC2iAvg; }
        }
        private int dpchC2iMax=int.MinValue;
        public int? DpchC2iMax
        {
            get
            {
                if (int.MinValue == dpchC2iMax)
                {
                    return null;
                }
                return dpchC2iMax;
            }
        }
        private int dpchC2iMin=int.MaxValue;
        public int? DpchC2iMin
        {
            get {
                if (int.MaxValue==dpchC2iMin)
                {
                    return null;
                }
                return dpchC2iMax; }
        }
        private float dpchC2iAvg=0;
        public float? DpchC2iAvg
        {
            get
            {
                if (float.IsNaN(dpchC2iAvg))
                {
                    return null;
                }
                return dpchC2iAvg;
            }
        }

        private int maxNCellMinRscp = int.MaxValue;
        public int MaxNCellMinRscp
        {
            get { return maxNCellMinRscp; }
        }
        private int maxNCellMaxRscp = int.MinValue;
        public int MaxNCellMaxRscp
        {
            get { return maxNCellMaxRscp; }
        }
        private float maxNCellSumRscp = 0;
        private int maxNTDCellCnt = 0;
        public float MaxNCellAvgRscp
        {
            get
            {
                if (maxNTDCellCnt != 0)
                {
                    return (float)Math.Round(maxNCellSumRscp / maxNTDCellCnt, 2);
                }
                return float.NaN;
            }
        }

        private int p_c2iCnt = 0;
        private int d_c2iCnt = 0;
        public void Add(float rxlev, int? pccpchC2i, int? dpchC2i, double distance, TestPoint tp, int maxNCellRscp)
        {
            DTDisplayParameterInfo p_c2iInfo = DTDisplayParameterManager.GetInstance()["TD-SCDMA", "PCCPCH_C2I"];
            if (p_c2iInfo!=null&&p_c2iInfo.ValueMin <= pccpchC2i && pccpchC2i <= p_c2iInfo.ValueMax)
            {
                pccpchC2iMax = Math.Max(pccpchC2iMax, (int)pccpchC2i);
                pccpchC2iMin = Math.Min(pccpchC2iMin, (int)pccpchC2i);
                pccpchC2iAvg += (int)pccpchC2i;
                p_c2iCnt++;
            }
           
            DTDisplayParameterInfo d_c2iInfo = DTDisplayParameterManager.GetInstance()["TD-SCDMA", "DPCH_C2I"];
            if (d_c2iInfo != null && d_c2iInfo.ValueMin <= dpchC2i && dpchC2i <= d_c2iInfo.ValueMax)
            {
                dpchC2iMax = Math.Max(dpchC2iMax, (int)dpchC2i);
                dpchC2iMin = Math.Min(dpchC2iMin, (int)dpchC2i);
                dpchC2iAvg += (int)dpchC2i;
                d_c2iCnt++;
            }
            if (maxNCellRscp>=-140&&maxNCellRscp<=-10)
            {
                maxNTDCellCnt++;
                maxNCellSumRscp += maxNCellRscp;
                maxNCellMinRscp = Math.Min(maxNCellMinRscp, maxNCellRscp);
                maxNCellMaxRscp = Math.Max(maxNCellMaxRscp, maxNCellRscp);
            }
            base.Add(rxlev, distance, tp);
        }

        public override void GetResult(bool saveTestPoints)
        {
            pccpchC2iAvg = (float)Math.Round(1.0 * pccpchC2iAvg / p_c2iCnt, 2);
            dpchC2iAvg = (float)Math.Round(1.0 * dpchC2iAvg / d_c2iCnt, 2);
            base.GetResult(saveTestPoints);
        }

        protected override void getImage(BackgroundResult bgResult)
        {
            bgResult.AddImageValue(pccpchC2iAvg);
            bgResult.AddImageValue(pccpchC2iMin);
            bgResult.AddImageValue(pccpchC2iMax);
            bgResult.AddImageValue(dpchC2iAvg);
            bgResult.AddImageValue(dpchC2iMin);
            bgResult.AddImageValue(dpchC2iMax);
        }
    }

    public class ZTWeakCovRoadCondition_TD: ZTWeakCovRoadCondition
    {
        public int rscpThreshold { get; set; } = -95;
        public int ncellRscpMax { get; set; } = -95;
        public int pcchC_iThreshold { get; set; } = -3;
        public int dpchC_iThreshold { get; set; } = -3;
        public bool checkPccpchC2I { get; set; } = false;
        public bool checkDpchC2I { get; set; } = false;
        public bool checkNCellRscp { get; set; } = true;

        public bool IsValidate(TestPoint tp, float? rscp, int? pcchC2I, int? dpchC2I)
        {
            bool validate = false;
            validate = (rscp != null) && (rscp <= -10) && (rscp >= -140) && rscp <= rscpThreshold;
            if (validate)
            {
                if (checkDpchC2I)
                {
                    validate = (dpchC2I != null) && (dpchC2I >= -20 && dpchC2I <= 25) && dpchC2I <= dpchC_iThreshold;
                }
                if (validate && checkPccpchC2I)
                {
                    validate = pcchC2I != null && pcchC2I >= -20 && pcchC2I <= 50 && pcchC2I <= pcchC_iThreshold;
                }
                if (validate && checkNCellRscp)
                {
                    validate = getMaxData(tp, validate);
                }
            }
            return validate;
        }

        private bool getMaxData(TestPoint tp, bool validate)
        {
            int max = int.MinValue;
            for (int i = 0; i < 20; i++)
            {
                int? ncellRscp = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                if (ncellRscp != null && ncellRscp <= -10 && ncellRscp >= -140)
                {
                    max = Math.Max((int)ncellRscp, max);
                }
                else
                {
                    break;
                }
            }
            if (max != int.MinValue)
            {
                validate = max <= ncellRscpMax;
            }

            return validate;
        }

        public bool MatchCoverMinDistance(double dis)
        {
            return dis >= roadDistance;
        }

        public bool MatchMinDisanceOf2Point(double dis)
        {
            return dis <= sampleDistance;
        }
    }
}
