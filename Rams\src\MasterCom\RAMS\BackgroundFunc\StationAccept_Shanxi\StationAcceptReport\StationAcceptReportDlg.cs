﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public partial class StationAcceptReportDlg : BaseDialog
    {
        public StationAcceptReportCondtion Condtion { get; set; } = new StationAcceptReportCondtion();

        public StationAcceptReportDlg(StationAcceptReportCondtion condtion)
        {
            InitializeComponent();
            init(condtion);
        }

        private void init(StationAcceptReportCondtion condtion)
        {
            ckcCityStatt.Properties.Items.Clear();
            ckcCityStatt.Properties.Items.Add("安康");
            ckcCityStatt.Properties.Items.Add("宝鸡");
            ckcCityStatt.Properties.Items.Add("汉中");
            ckcCityStatt.Properties.Items.Add("商洛");
            ckcCityStatt.Properties.Items.Add("铜川");
            ckcCityStatt.Properties.Items.Add("渭南");
            ckcCityStatt.Properties.Items.Add("西安");
            ckcCityStatt.Properties.Items.Add("咸阳");
            ckcCityStatt.Properties.Items.Add("延安");
            ckcCityStatt.Properties.Items.Add("榆林");
            if (condtion != null)
            {
                ckcCityStatt.Text = condtion.ConDistrictNames;
                ckbType.Text = condtion.ConTypes;
                dPSTime.Value = condtion.StartDate;
                dPETime.Value = condtion.EndDate;
            }
            else
            {
                dPSTime.Value = DateTime.Now.Date.AddMonths(-1);
                dPETime.Value = DateTime.Now.Date;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            Condtion.StartDate = DateTime.Parse(dPSTime.Value.ToString("yyyy-MM-dd"));
            Condtion.EndDate = DateTime.Parse(dPETime.Value.ToString("yyyy-MM-dd")).AddDays(1);
            Condtion.ConDistrictNames = ckcCityStatt.Text.Trim();

            string[] names = Condtion.ConDistrictNames.Split(',');
            StringBuilder sb = new StringBuilder();
            foreach (var name in names)
            {
                sb.Append("'" + name.Trim() + "',");
            }
            Condtion.DistrictNames = sb.ToString().TrimEnd(',');

            Condtion.ConTypes = ckbType.Text.Trim();
            string[] types = Condtion.ConTypes.Split(',');
            foreach (var name in types)
            {
                if (name.Contains("4G"))
                {
                    Condtion.TypeList.Add(NetType.LTE);
                }
                else if (name.Contains("5G"))
                {
                    Condtion.TypeList.Add(NetType.NR);
                }
            }

            List<string> btsNameList = new List<string>();
            string[] split = new string[] { "or" };
            string[] btsNames = textBoxFileNameKeys.Text.Split(split, StringSplitOptions.RemoveEmptyEntries);
            foreach (var btsName in btsNames)
            {
                btsNameList.Add(btsName.Trim());
            }

            StringBuilder strbFilter = new StringBuilder();
            foreach (string info in btsNameList)
            {
                strbFilter.Append(" or ");
                strbFilter.Append(info);
            }
            Condtion.BtsNames = strbFilter.ToString();
        }
    }

    public class StationAcceptReportCondtion
    { 
        public string DistrictNames { get; set; }
        public string ConDistrictNames { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string BtsNames { get; set; }
        public List<NetType> TypeList { get; set; } = new List<NetType>();
        public string ConTypes { get; set; }
    }
}
