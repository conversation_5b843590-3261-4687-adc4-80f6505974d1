﻿namespace MasterCom.RAMS.Util
{
    partial class ScoreColorRangeSettingBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScoreColorRangeSettingBox));
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditMin = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditMax = new DevExpress.XtraEditors.SpinEdit();
            this.lblScoreLeft = new DevExpress.XtraEditors.LabelControl();
            this.lblScoreRight = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditScoreLeft = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditScoreRight = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.lblMax = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.colorEdit = new DevExpress.XtraEditors.ColorEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.textEdit = new DevExpress.XtraEditors.TextEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditScoreLeft.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditScoreRight.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(22, 15);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(28, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "指标:";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(22, 42);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(28, 14);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "评分:";
            // 
            // spinEditMin
            // 
            this.spinEditMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditMin.Location = new System.Drawing.Point(64, 12);
            this.spinEditMin.Name = "spinEditMin";
            this.spinEditMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMin.Size = new System.Drawing.Size(61, 21);
            this.spinEditMin.TabIndex = 1;
            // 
            // spinEditMax
            // 
            this.spinEditMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditMax.Location = new System.Drawing.Point(208, 12);
            this.spinEditMax.Name = "spinEditMax";
            this.spinEditMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMax.Size = new System.Drawing.Size(61, 21);
            this.spinEditMax.TabIndex = 1;
            this.spinEditMax.EditValueChanged += new System.EventHandler(this.spinEditMax_EditValueChanged);
            // 
            // lblScoreLeft
            // 
            this.lblScoreLeft.Location = new System.Drawing.Point(131, 42);
            this.lblScoreLeft.Name = "lblScoreLeft";
            this.lblScoreLeft.Size = new System.Drawing.Size(18, 14);
            this.lblScoreLeft.TabIndex = 0;
            this.lblScoreLeft.Text = "<=";
            // 
            // lblScoreRight
            // 
            this.lblScoreRight.Location = new System.Drawing.Point(184, 42);
            this.lblScoreRight.Name = "lblScoreRight";
            this.lblScoreRight.Size = new System.Drawing.Size(18, 14);
            this.lblScoreRight.TabIndex = 0;
            this.lblScoreRight.Text = "<=";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(155, 42);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(24, 14);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "分数";
            // 
            // spinEditScoreLeft
            // 
            this.spinEditScoreLeft.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditScoreLeft.Location = new System.Drawing.Point(64, 39);
            this.spinEditScoreLeft.Name = "spinEditScoreLeft";
            this.spinEditScoreLeft.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditScoreLeft.Size = new System.Drawing.Size(61, 21);
            this.spinEditScoreLeft.TabIndex = 1;
            // 
            // spinEditScoreRight
            // 
            this.spinEditScoreRight.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditScoreRight.Location = new System.Drawing.Point(208, 39);
            this.spinEditScoreRight.Name = "spinEditScoreRight";
            this.spinEditScoreRight.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditScoreRight.Size = new System.Drawing.Size(61, 21);
            this.spinEditScoreRight.TabIndex = 1;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(131, 15);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 14);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "<=";
            // 
            // lblMax
            // 
            this.lblMax.Location = new System.Drawing.Point(184, 15);
            this.lblMax.Name = "lblMax";
            this.lblMax.Size = new System.Drawing.Size(18, 14);
            this.lblMax.TabIndex = 0;
            this.lblMax.Text = "<=";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(155, 15);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 14);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "数值";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(22, 96);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(28, 14);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "颜色:";
            // 
            // colorEdit
            // 
            this.colorEdit.EditValue = System.Drawing.Color.Empty;
            this.colorEdit.Location = new System.Drawing.Point(64, 93);
            this.colorEdit.Name = "colorEdit";
            this.colorEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit.Properties.ShowWebColors = false;
            this.colorEdit.Size = new System.Drawing.Size(61, 21);
            this.colorEdit.TabIndex = 2;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(22, 69);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(28, 14);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "描述:";
            // 
            // textEdit
            // 
            this.textEdit.Location = new System.Drawing.Point(64, 66);
            this.textEdit.Name = "textEdit";
            this.textEdit.Size = new System.Drawing.Size(205, 21);
            this.textEdit.TabIndex = 3;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(65, 129);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 4;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(150, 129);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            // 
            // ScoreColorRangeSettingBox
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("ScoreColorRangeSettingBox.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.ClientSize = new System.Drawing.Size(290, 163);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.textEdit);
            this.Controls.Add(this.colorEdit);
            this.Controls.Add(this.spinEditMax);
            this.Controls.Add(this.spinEditScoreRight);
            this.Controls.Add(this.spinEditScoreLeft);
            this.Controls.Add(this.spinEditMin);
            this.Controls.Add(this.lblMax);
            this.Controls.Add(this.lblScoreRight);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.lblScoreLeft);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "ScoreColorRangeSettingBox";
            this.Text = "指标值评分范围设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditScoreLeft.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditScoreRight.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TextBox DesctextBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label labelColorLabel;
        private System.Windows.Forms.Label labelColor;
        private System.Windows.Forms.NumericUpDown nudScoreMin;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown nudScoreMax;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditMin;
        private DevExpress.XtraEditors.SpinEdit spinEditMax;
        private DevExpress.XtraEditors.LabelControl lblScoreLeft;
        private DevExpress.XtraEditors.LabelControl lblScoreRight;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit spinEditScoreLeft;
        private DevExpress.XtraEditors.SpinEdit spinEditScoreRight;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl lblMax;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.ColorEdit colorEdit;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.TextEdit textEdit;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
    }
}