﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.Net
{
    public class GridPartParam:GridUnitBase
    {
        public PartialData partialData { get; set; }
        public FileBase filebase { get; set; }
    }

    public class GridUnit:GridUnitBase
    {
        public DataWCDMA_AMR wAmrParamNew  { get; set; }//新W AMR VP image 格式
        public DataCDMA_Voice cdmaVParamNew  { get; set; }//新CDMA voice image格式
        public DataTDSCDMA_NewImg tdParamNewImg  { get; set; }//新的TD PS image格式
        public DataGSM_NewImg gsmParamNewImg  { get; set; }//新的GSM PS or Voice image格式
        public DataEVDO_Data cdmaEVDONewImg  { get; set; }//CDMA新Image格式
        public DataLTE lteData  { get; set; }
        public DataLTE_FDD lteFddData  { get; set; }
        public DataLTE_Signal lteSignalData  { get; set; }
        public DataScan_GSM gsmScanNewImg  { get; set; }
        public DataScan_TD tdScanNewImg  { get; set; }
        public DataScan_WCDMA wScanNewImg  { get; set; }
        public DataScan_CDMA cScanNewImg  { get; set; }
        public DataScan_LTE lteScanNewImg  { get; set; }

        public DataMTR_GSM mtrNewImg  { get; set; }//MTR统计
        public GridUnit()
        {

        }
        internal void MergeGridUnit(GridUnit gridUnit)
        {
            if (gridUnit.wAmrParamNew != null)
            {
                if (wAmrParamNew == null)
                {
                    wAmrParamNew = new DataWCDMA_AMR();
                }
                wAmrParamNew.addStatData(gridUnit.wAmrParamNew);
            }
            if (gridUnit.cdmaVParamNew != null)
            {
                if (cdmaVParamNew == null)
                {
                    cdmaVParamNew = new DataCDMA_Voice();
                }
                cdmaVParamNew.addStatData(gridUnit.cdmaVParamNew);
            }
            if (gridUnit.lteData != null)
            {
                if (lteData == null)
                {
                    lteData = new DataLTE();
                }
                lteData.addStatData(gridUnit.lteData);
            }
            if (gridUnit.lteFddData != null)
            {
                if (lteFddData == null)
                {
                    lteFddData = new DataLTE_FDD();
                }
                lteFddData.addStatData(gridUnit.lteFddData);
            }
            addNewImg(gridUnit);
        }

        private void addNewImg(GridUnit gridUnit)
        {
            if (gridUnit.tdParamNewImg != null)
            {
                if (tdParamNewImg == null)
                {
                    tdParamNewImg = new DataTDSCDMA_NewImg();
                }
                tdParamNewImg.addStatData(gridUnit.tdParamNewImg);
            }
            if (gridUnit.gsmParamNewImg != null)
            {
                if (gsmParamNewImg == null)
                {
                    gsmParamNewImg = new DataGSM_NewImg();
                }
                gsmParamNewImg.addStatData(gridUnit.gsmParamNewImg);
            }
            if (gridUnit.cdmaEVDONewImg != null)
            {
                if (cdmaEVDONewImg == null)
                {
                    cdmaEVDONewImg = new DataEVDO_Data();
                }
                cdmaEVDONewImg.addStatData(gridUnit.cdmaEVDONewImg);
            }
            addScanNewImg(gridUnit);
        }

        private void addScanNewImg(GridUnit gridUnit)
        {
            if (gridUnit.gsmScanNewImg != null)
            {
                if (gsmScanNewImg == null)
                {
                    gsmScanNewImg = new DataScan_GSM();
                }
                gsmScanNewImg.addStatData(gridUnit.gsmScanNewImg);
            }
            if (gridUnit.tdScanNewImg != null)
            {
                if (tdScanNewImg == null)
                {
                    tdScanNewImg = new DataScan_TD();
                }
                tdScanNewImg.addStatData(gridUnit.tdScanNewImg);
            }
            if (gridUnit.lteScanNewImg != null)
            {
                if (lteScanNewImg == null)
                {
                    lteScanNewImg = new DataScan_LTE();
                }
                lteScanNewImg.AddStatData(gridUnit.lteScanNewImg);
            }
            if (gridUnit.wScanNewImg != null)
            {
                if (wScanNewImg == null)
                {
                    wScanNewImg = new DataScan_WCDMA();
                }
                wScanNewImg.addStatData(gridUnit.wScanNewImg);
            }
        }

        public GridUnit(GridPartParam gpp)
        {
            LTLng = gpp.LTLng;
            LTLat = gpp.LTLat;
            doMergeIt(gpp);
        }
       
        public void doMergeIt(GridPartParam param)
        {
            if (param.partialData is DataLTE)
            {
                doMerge(param.partialData as DataLTE);
            }
            else if (param.partialData is DataWCDMA_AMR)
            {
                doMerge(param.partialData as DataWCDMA_AMR);
            }
            else if (param.partialData is DataCDMA_Voice)
            {
                doMerge(param.partialData as DataCDMA_Voice);
            }
            else if (param.partialData is DataTDSCDMA_NewImg)
            {
                doMerge(param.partialData as DataTDSCDMA_NewImg);
            }
            else if (param.partialData is DataGSM_NewImg)
            {
                doMerge(param.partialData as DataGSM_NewImg);
            }
            else if (param.partialData is DataScan_GSM)
            {
                doMerge(param.partialData as DataScan_GSM);
            }
            else if (param.partialData is DataEVDO_Data)
            {
                doMerge(param.partialData as DataEVDO_Data);
            }
            else if (param.partialData is DataScan_TD)
            {
                doMerge(param.partialData as DataScan_TD);
            }
            else if (param.partialData is DataScan_WCDMA)
            {
                doMerge(param.partialData as DataScan_WCDMA);
            }
            else if (param.partialData is DataMTR_GSM)
            {
                doMerge(param.partialData as DataMTR_GSM);
            }
            else if (param.partialData is DataScan_LTE)
            {
                doMerge(param.partialData as DataScan_LTE);
            }
        }

        public void doMerge(DataWCDMA_AMR param)
        {
            if (wAmrParamNew == null)
            {
                wAmrParamNew = new DataWCDMA_AMR();
            }
            this.wAmrParamNew.addStatData(param);
        }
        public void doMerge(DataTDSCDMA_NewImg param)
        {
            if (tdParamNewImg == null)
            {
                tdParamNewImg = new DataTDSCDMA_NewImg();
            }
            this.tdParamNewImg.addStatData(param);
        }
        public void doMerge(DataGSM_NewImg param)
        {
            if (gsmParamNewImg == null)
            {
                gsmParamNewImg = new DataGSM_NewImg();
            }
            this.gsmParamNewImg.addStatData(param);
        }
        public void doMerge(DataLTE lteParam)
        {
            if (lteData==null)
            {
                lteData = new DataLTE();
            }
            lteData.addStatData(lteParam);
        }
        public void doMerge(DataLTE_FDD lteParam)
        {
            if (lteFddData == null)
            {
                lteFddData = new DataLTE_FDD();
            }
            lteFddData.addStatData(lteParam);
        }
        public void doMerge(DataCDMA_Voice param)
        {
            if (cdmaVParamNew == null)
            {
                cdmaVParamNew = new DataCDMA_Voice();
            }
            this.cdmaVParamNew.addStatData(param);
        }
        public void doMerge(DataEVDO_Data param)
        {
            if (cdmaEVDONewImg == null)
            {
                cdmaEVDONewImg = new DataEVDO_Data();
            }
            this.cdmaEVDONewImg.addStatData(param);
        }
        public void doMerge(DataScan_GSM param)
        {
            if (gsmScanNewImg == null)
            {
                gsmScanNewImg = new DataScan_GSM();
            }
            this.gsmScanNewImg.addStatData(param);
        }
        public void doMerge(DataScan_TD param)
        {
            if (tdScanNewImg == null)
            {
                tdScanNewImg = new DataScan_TD();
            }
            this.tdScanNewImg.addStatData(param);
        }
        public void doMerge(DataScan_LTE param)
        {
            if (lteScanNewImg == null)
            {
                lteScanNewImg = new DataScan_LTE();
            }
            this.lteScanNewImg.AddStatData(param);
        }
        public void doMerge(DataScan_WCDMA param)
        {
            if (wScanNewImg == null)
            {
                wScanNewImg = new DataScan_WCDMA();
            }
            this.wScanNewImg.addStatData(param);
        }
        public void doMerge(DataMTR_GSM param)
        {
            if (mtrNewImg == null)
            {
                mtrNewImg = new DataMTR_GSM();
            }
            this.mtrNewImg.addStatData(param);
        }

        /*
         *注意，当新添加某种业务的Image时，需同步在GetFieldValue，getFixedFieldValue
         *这2个方法里面添加对应的获取字段数值支持。
         */
        /// <summary>
        /// 获取指标字段对应的数值
        /// </summary>
        /// <param name="fieldName">指标字段名</param>
        /// <param name="args">数组参数</param>
        /// <returns></returns>
        public object GetFieldValue(string fieldName, string args)
        {
            int arg = -1;
            if (args != "")
            {
                arg = int.Parse(args);
            }
            object v = 0;
            v = getFieldValue(this, fieldName, arg);
            if (!v.Equals(false))
            {
                return v;
            }
            else if (fieldName.IndexOf("Lte_") == 0 && lteData != null)
            {
                return lteData.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Lf_") == 0 && lteFddData != null)
            {
                return lteFddData.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Sn_") == 0 && lteSignalData != null)
            {
                return lteSignalData.getFieldValue(fieldName, arg);
            }
            object obj = setFieldValueNew(fieldName, arg);
            if (obj != null)
            {
                return obj;
            }
            obj = setFieldValueScan(fieldName, arg);
            if (obj != null)
            {
                return obj;
            }

            double fixedValue = getFixedFieldValue(fieldName);
            return fixedValue;
        }

        private object setFieldValueNew(string fieldName, int arg)
        {
            if (fieldName.IndexOf("Wx_") == 0 && wAmrParamNew != null)
            {
                return wAmrParamNew.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Tx_") == 0 && tdParamNewImg != null)
            {
                return tdParamNewImg.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Cx_") == 0 && cdmaVParamNew != null)
            {
                return cdmaVParamNew.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Mx_") == 0 && gsmParamNewImg != null)
            {
                return gsmParamNewImg.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Ux_") == 0 && mtrNewImg != null)
            {
                return mtrNewImg.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Ex_") == 0 && cdmaEVDONewImg != null)
            {
                return cdmaEVDONewImg.getFieldValue(fieldName, arg);
            }
            return null;
        }

        private object setFieldValueScan(string fieldName, int arg)
        {
            if (fieldName.IndexOf("Gc_") == 0 && gsmScanNewImg != null)
            {
                return gsmScanNewImg.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Tc_") == 0 && tdScanNewImg != null)
            {
                return tdScanNewImg.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Lc_") == 0 && lteScanNewImg != null)
            {
                return lteScanNewImg.GetFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Wc_") == 0 && wScanNewImg != null)
            {
                return wScanNewImg.getFieldValue(fieldName, arg);
            }
            else if (fieldName.IndexOf("Cc_") == 0 && cScanNewImg != null)
            {
                return cScanNewImg.getFieldValue(fieldName, arg);
            }
            return null;
        }

        /// <summary>
        /// 获取固定指标字段对应的数值
        /// </summary>
        /// <returns></returns>
        private double getFixedFieldValue(string fixedFieldName)
        {
            if (wAmrParamNew != null)
            {
                return wAmrParamNew.WInfoDic[fixedFieldName];
            }
            else if (tdParamNewImg != null)
            {
                return tdParamNewImg.WInfoDic[fixedFieldName];
            }
            else if (cdmaVParamNew != null)
            {
                return cdmaVParamNew.WInfoDic[fixedFieldName];
            }
            else if (gsmParamNewImg != null)
            {
                return gsmParamNewImg.WInfoDic[fixedFieldName];
            }
            else if (lteData!=null)
            {
                return lteData.WInfoDic[fixedFieldName];
            }
            else if (lteFddData != null)
            {
                return lteFddData.WInfoDic[fixedFieldName];
            }
            else if (mtrNewImg != null)
            {
                return mtrNewImg.WInfoDic[fixedFieldName];
            }
            else if (cdmaEVDONewImg != null)
            {
                return cdmaEVDONewImg.WInfoDic[fixedFieldName];
            }
            else if (gsmScanNewImg != null)
            {
                return gsmScanNewImg.WInfoDic[fixedFieldName];
            }
            else if (tdScanNewImg != null)
            {
                return tdScanNewImg.WInfoDic[fixedFieldName];
            }
            else if (wScanNewImg != null)
            {
                return wScanNewImg.WInfoDic[fixedFieldName];
            }
            else if (cScanNewImg != null)
            {
                return cScanNewImg.WInfoDic[fixedFieldName];
            }
            return double.NaN;
        }

        private object getFieldValue(object obj, string fieldName, int arg)
        {
            if (obj == null)
            {
                return false;
            }
            Type tp = obj.GetType();
            FieldInfo fi = tp.GetField(fieldName);
            if (fi == null)
            {
                return false;
            }
            if (arg != -1)
            {
                if (!fi.FieldType.IsArray)
                {
                    throw new InvalidOperationException("用数组取值，但字段" + fieldName + "非数组");
                }
                Object arr = fi.GetValue(obj);
                if (arr is int[])
                {
                    int[] aa = (int[])arr;
                    judgeError(fieldName, arg, aa.Length);
                    return aa[arg];
                }
                if (arr is float[])
                {
                    float[] aa = (float[])arr;
                    judgeError(fieldName, arg, aa.Length);
                    return aa[arg];
                }
                else if (arr is short[])
                {
                    short[] aa = (short[])arr;
                    judgeError(fieldName, arg, aa.Length);
                    return aa[arg];
                }
                else if (arr is byte[])
                {
                    byte[] aa = (byte[])arr;
                    judgeError(fieldName, arg, aa.Length);
                    return aa[arg];
                }
                else
                {
                    throw new InvalidOperationException("数组类型非预期:" + arr);
                }
            }
            else
            {
                if (fi.FieldType.IsArray)
                {
                    throw new InvalidOperationException("字段" + fieldName + "缺少数组索引");
                }
                return fi.GetValue(obj);
            }
        }

        private void judgeError(string fieldName, int arg, int length)
        {
            if (arg >= length)
            {
                throw new InvalidOperationException("字段" + fieldName + "缺少数组索引超限");
            }
        }

        public bool HasValidField(string fieldName, string args)
        {
            int arg = -1;
            if (args != "")
            {
                arg = int.Parse(args);
            }

            bool ret = hasValidField(this, fieldName, arg);
            if (!ret)
            {
                return getValidName(fieldName);
            }
            return ret;
        }

        private bool getValidName(string fieldName)
        {
            if (fieldName.IndexOf("Wx_") == 0
                || fieldName.IndexOf("Cx_") == 0 || fieldName.IndexOf("Tx_") == 0
                || fieldName.IndexOf("Mx_") == 0 || fieldName.IndexOf("Ex_") == 0
                || fieldName.IndexOf("Gc_") == 0 || fieldName.IndexOf("Tc_") == 0
                || fieldName.IndexOf("Wc_") == 0 || fieldName.IndexOf("Cc_") == 0
                || fieldName.IndexOf("Ux_") == 0 || fieldName.IndexOf("Mt_") == 0
                || fieldName.IndexOf("Lc_") == 0 || fieldName.IndexOf("Lte_") == 0
                || fieldName.IndexOf("Lf_") == 0 || fieldName.IndexOf("Sn_") == 0
                || fieldName.IndexOf("Nr_") == 0 || fieldName.IndexOf("Nc_") == 0)
            {
                return (fieldName.Length - fieldName.IndexOf("_") - 1) > 0;
            }
            return false;
        }

        private bool hasValidField(object obj, string fieldname, int arg)
        {
            if (obj == null)
                return false;

            Type tp = obj.GetType();
            FieldInfo fi = tp.GetField(fieldname);
            if (fi == null)
            {
                return false;
            }
            if (arg != -1)
            {
                if (!fi.FieldType.IsArray)
                {
                    return false;
                }
                Object arr = fi.GetValue(obj);
                return getValidAryTypeLen(arg, arr);
            }
            else
            {
                if (fi.FieldType.IsArray)
                {
                    return false;
                }
            }
            return true;
        }

        private static bool getValidAryTypeLen(int arg, object arr)
        {
            if (arr is int[])
            {
                int[] aa = (int[])arr;
                int len = aa.Length;
                if (arg >= len)
                {
                    return false;
                }
            }
            else if (arr is float[])
            {
                float[] aa = (float[])arr;
                int len = aa.Length;
                if (arg >= len)
                {
                    return false;
                }
            }
            else if (arr is short[])
            {
                short[] aa = (short[])arr;
                int len = aa.Length;
                if (arg >= len)
                {
                    return false;
                }
            }
            else if (arr is byte[])
            {
                byte[] aa = (byte[])arr;
                int len = aa.Length;
                if (arg >= len)
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
            return true;
        }

    }

    public class GridBase
    {
        public double ltLong { get; set; }
        public double ltLat { get; set; }
        public double brLong { get; set; }
        public double brLat { get; set; }
        public double midLong { get; set; }
        public double midLat { get; set; }

        public GridBase()
        {
        }

        public GridBase(double ltLongitude, double ltLatitude)
        {
            this.ltLong = ltLongitude;
            this.ltLat = ltLatitude;
            brLong = ltLong + 0.0004;   //40米栅格
            brLat = ltLat - 0.00036;
            this.midLong = ltLong + 0.0002;
            this.midLat = ltLat - 0.00018;
        }

        public GridBase(double ltLongitude, double ltLatitude, double brLongitude, double brLatitude)
        {
            this.ltLong = ltLongitude;
            this.ltLat = ltLatitude;
            this.brLong = brLongitude;
            this.brLat = brLatitude;
            this.midLong = (ltLong + brLong) / 2;
            this.midLat = (ltLat + brLat) / 2;
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            if (brLong < x1 || ltLong > x2 || ltLat < y1 || brLat > y2)
            {
                return false;
            }
            return true;
        }

        public static double GetGridTLLong(double longitude)
        {
            int tllongitude = (int)(longitude * 10000000) / 4000 * 4000;
            return tllongitude / 10000000.0D;
        }

        public static double GetGridTLLat(double latitude)
        {
            int tllatitude = (int)(latitude * 10000000) / 3600 * 3600 + 3600;
            return tllatitude / 10000000.0D;
        }
    }
}