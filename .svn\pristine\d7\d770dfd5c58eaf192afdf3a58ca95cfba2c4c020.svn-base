﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// Written by Wu<PERSON>un<PERSON>ong 2012.11.10
    /// </summary>
    public class DIYDropPerceptionAna:DIYQueryFileInfoByRegion
    {
        public DIYDropPerceptionAna(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "TD感知掉线分析(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/事件检索/区域.png"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13022, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = true;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;
            base.query();

            analyseFiles();
            MainModel.ClearDTData();
            MainModel.EventDropPerceptionDic = resultEventDropDic;
            fireShowForm();
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
        }

        protected void fireShowForm()
        {
            MasterCom.RAMS.ZTFunc.DropPerceptionForm frm = MainModel.GetInstance().CreateResultForm(typeof(MasterCom.RAMS.ZTFunc.DropPerceptionForm)) as MasterCom.RAMS.ZTFunc.DropPerceptionForm;
            frm.LoadDropPerceptionInfo();
            frm.Visible = true;
            frm.BringToFront();
        }

        Dictionary<int, string> lacCityNameMap = null; //查询Lac与地市名关联的集合
        Dictionary<string, List<DropPerceptionInfo>> resultEventDropDic = null;
        private void analyseFiles()
        {
            try
            {
                queryLacCity();
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    files.Add(fileInfo);
                }
                int iloop = 0;
                resultEventDropDic = new Dictionary<string, List<DropPerceptionInfo>>();
                foreach (FileInfo fileInfo in files)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    string curWaitBoxText = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    int curProgressPercent = (int)(iloop * 100.0 / files.Count);
                    replay(curWaitBoxText, curProgressPercent);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryLacCity()
        {    
            lacCityNameMap = new Dictionary<int, string>();
            DIYSQLQueryLacCityName diySQLQueryLacCityName = new DIYSQLQueryLacCityName(MainModel);
            diySQLQueryLacCityName.Query();
            lacCityNameMap = diySQLQueryLacCityName.LacCityNameMap;   //查询Lac与地市名关联的集合
        }

        private void replay(string curWaitBoxText, int curProgressPercent)
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFile query = new DIYReplayFile(MainModel, curWaitBoxText, curProgressPercent);
            query.SetQueryCondition(condition);
            query.Query();
            doStat();
        }

        /// <summary>
        /// 判断是否有效采样点，数据值没异常则返回真
        /// </summary>
        /// <param name="testpoint"></param>
        /// <returns></returns>
        private bool isValidPoint(TestPoint testpoint)
        {
            if ((int)testpoint[MainModel.TD_SCell_LAC] != -255 || ((int)testpoint[MainModel.TD_SCell_CI] != -255))
                return true;
            return false;
        }
        
        private void doStat()
        {
            try
            {
                List<TestPoint> testPointAll = new List<TestPoint>();  //记录全部采样点
                List<Event> eventAll = new List<Event>();  //记录全部事件
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    testPointAll.AddRange(fileDataManager.TestPoints);
                    eventAll.AddRange(fileDataManager.Events);
                }

                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<Event> eventList = fileDataManager.Events;
                    foreach (Event e in eventList)
                    {
                        ///1、感知掉线（FTP零下载速率_30S），事件id：404
                        //    Value1: 断流开始时间

                        //2、FTP下载掉线_网络PDP去激活，事件id：98
                        //3、FTP Download Fail_路由区更新拒绝，事件id：93 
                        if (e.ID == 404)
                        {
                            setPerceptionDropInfo(testPointAll, eventAll, e);
                        }
                        else if(e.ID == 98 || e.ID == 93)
                        {
                            setFtpDropInfo(testPointAll, eventAll, e);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private void setPerceptionDropInfo(List<TestPoint> testPointAll, List<Event> eventAll, Event e)
        {
            int cutoffStarttimeTp, beginDropTpIndex;
            getBeginTpInfo(testPointAll, int.Parse(e["Value1"].ToString()), out cutoffStarttimeTp, out beginDropTpIndex);

            if (beginDropTpIndex != -1)//-1为此事件无合适断流采样点，跳过
            {
                DropPerceptionInfo dInfo = new DropPerceptionInfo();
                setDropPerceptionAvgData(testPointAll, beginDropTpIndex, dInfo);

                dInfo.eventName = (e.EventInfo == null ? "感知掉线" : e.EventInfo.Name);
                dInfo.cutoffStarttimeTp = JavaDate.GetDateTimeFromMilliseconds(cutoffStarttimeTp * 1000L);
                setDropPerceptionInfoBase(testPointAll, e, dInfo);

                if (testPointAll[beginDropTpIndex][MainModel.TD_SCell_LAC] != null)
                {
                    if (lacCityNameMap.ContainsKey((int)testPointAll[beginDropTpIndex][MainModel.TD_SCell_LAC]))
                    {
                        dInfo.district = lacCityNameMap[(int)testPointAll[beginDropTpIndex][MainModel.TD_SCell_LAC]];
                    }
                    dInfo.dropTpLac = (int)testPointAll[beginDropTpIndex][MainModel.TD_SCell_LAC];
                }
                if (testPointAll[beginDropTpIndex][MainModel.TD_SCell_CI] != null)
                    dInfo.dropTpCi = (int)testPointAll[beginDropTpIndex][MainModel.TD_SCell_CI];

                dInfo.dropTpLongitude = testPointAll[beginDropTpIndex].Longitude;
                dInfo.dropTpLatitude = testPointAll[beginDropTpIndex].Latitude;
                setDropPerceptionInfoByTP(testPointAll, beginDropTpIndex, dInfo);

                setSwitchTimes(eventAll, dInfo, dInfo.cutoffStarttimeTp);

                #region 邻区1~5主频，扰码，场强
                setNBDropPerceptionInfo(testPointAll, beginDropTpIndex, dInfo);
                #endregion

                addResultEventDropDic(dInfo, "404");
            }
        }

        private void setFtpDropInfo(List<TestPoint> testPointAll, List<Event> eventAll, Event e)
        {
            int cutoffStarttimeTp, beginDropTpIndex;
            getBeginTpInfo(testPointAll, e.Time, out cutoffStarttimeTp, out beginDropTpIndex);
            if (beginDropTpIndex != -1)
            {
                DropPerceptionInfo dInfo = new DropPerceptionInfo();
                setDropPerceptionAvgData(testPointAll, beginDropTpIndex, dInfo);

                dInfo.eventName = (e.EventInfo == null ? "事件ID：" + e.ID.ToString() : e.EventInfo.Name);
                setDropPerceptionInfoBase(testPointAll, e, dInfo);

                setDropPerceptionInfoByTP(testPointAll, beginDropTpIndex, dInfo);

                setSwitchTimes(eventAll, dInfo, dInfo.eventHappenTime);

                #region 邻区1~5主频，扰码，场强
                setNBDropPerceptionInfo(testPointAll, beginDropTpIndex, dInfo);
                #endregion

                addResultEventDropDic(dInfo, "9893");
            }
        }

        private void getBeginTpInfo(List<TestPoint> testPointAll, int time, out int cutoffStarttimeTp, out int beginDropTpIndex)
        {
            int cutoffStarttime = time;
            cutoffStarttimeTp = -1;
            int timeDif = 0;
            beginDropTpIndex = -1;
            bool isfirst = true;
            for (int i = 0; i < testPointAll.Count; i++)
            {
                getValidTPInfo(testPointAll, ref cutoffStarttimeTp, ref beginDropTpIndex, cutoffStarttime, ref timeDif, ref isfirst, i);
            }
        }

        private void getValidTPInfo(List<TestPoint> testPointAll, ref int cutoffStarttimeTp, ref int beginDropTpIndex, 
            int cutoffStarttime, ref int timeDif, ref bool isfirst, int i)
        {
            int diff = testPointAll[i].Time - cutoffStarttime;
            if (isfirst && diff < 0)
            {
                timeDif = Math.Abs(diff);
                cutoffStarttimeTp = testPointAll[i].Time;
                if (isValidPoint(testPointAll[i]))
                {
                    beginDropTpIndex = i;
                    isfirst = false;
                }
                else
                {
                    isfirst = true;
                }
            }
            else if (!isfirst && diff < 0)
            {
                int absDiff = Math.Abs(diff);
                if (timeDif > absDiff)
                {
                    timeDif = absDiff;
                    cutoffStarttimeTp = testPointAll[i].Time;
                    if (isValidPoint(testPointAll[i]))
                    {
                        beginDropTpIndex = i;
                    }
                }
            }
        }

        private void setDropPerceptionInfoBase(List<TestPoint> testPointAll, Event e, DropPerceptionInfo dInfo)
        {
            dInfo.fileName = e.FileName;
            dInfo.eventHappenTime = JavaDate.GetDateTimeFromMilliseconds(e.Time * 1000L);
            dInfo.evtLongitude = e.Longitude;
            dInfo.evtLatitude = e.Latitude;
            dInfo.evtLac = (int)e["LAC"];
            dInfo.evtCi = (int)e["CI"];
            for (int i = 0; i < testPointAll.Count; i++)
            {
                if (lacCityNameMap.ContainsKey((int)testPointAll[i][MainModel.TD_SCell_LAC]))
                {
                    dInfo.district = lacCityNameMap[(int)testPointAll[i][MainModel.TD_SCell_LAC]];
                    break;
                }
            }
        }

        private void setDropPerceptionAvgData(List<TestPoint> testPointAll, int beginDropTpIndex, DropPerceptionInfo dInfo)
        {
            int PCCPCH_RSCP_Total = -1;
            int countPCCPCH_RSCP = 0;
            int PCCPCH_C2I_Total = -1;
            int countPCCPCH_C2I = 0;
            int DPCH_RSCP_Total = -1;
            int countDPCH_RSCP = 0;
            int DPCH_C2I_Total = -1;
            int countDPCH_C2I = 0;
            DateTime beginDropTpTime = JavaDate.GetDateTimeFromMilliseconds(testPointAll[beginDropTpIndex].Time * 1000L);
            for (int i = 0; i < testPointAll.Count; i++)
            {
                DateTime dt = JavaDate.GetDateTimeFromMilliseconds(testPointAll[i].Time * 1000L);
                if (dt.CompareTo(beginDropTpTime) <= 0 && dt.AddSeconds(5).CompareTo(beginDropTpTime) >= 0    //时间在断流5秒前内
                    && testPointAll[beginDropTpIndex]["TD_PCCPCH_RSCP"] != null && testPointAll[beginDropTpIndex]["TD_PCCPCH_C2I"] != null
                    && testPointAll[beginDropTpIndex]["TD_DPCH_RSCP"] != null && testPointAll[beginDropTpIndex]["TD_DPCH_C2I"] != null
                    && (int)(float)testPointAll[beginDropTpIndex]["TD_PCCPCH_RSCP"] != -10000000 && (int)testPointAll[beginDropTpIndex]["TD_PCCPCH_C2I"] != -10000000
                    && (int)(float)testPointAll[beginDropTpIndex]["TD_DPCH_RSCP"] != -10000000 && (int)testPointAll[beginDropTpIndex]["TD_DPCH_C2I"] != -10000000)
                {
                    PCCPCH_RSCP_Total += (int)(float)testPointAll[beginDropTpIndex]["TD_PCCPCH_RSCP"];
                    countPCCPCH_RSCP++;
                    PCCPCH_C2I_Total += (int)testPointAll[beginDropTpIndex]["TD_PCCPCH_C2I"];
                    countPCCPCH_C2I++;
                    DPCH_RSCP_Total += (int)(float)testPointAll[beginDropTpIndex]["TD_DPCH_RSCP"];
                    countDPCH_RSCP++;
                    DPCH_C2I_Total += (int)testPointAll[beginDropTpIndex]["TD_DPCH_C2I"];
                    countDPCH_C2I++;
                }
            }

            if (countPCCPCH_RSCP > 0)
                dInfo.avgPCCPCH_RSCP = PCCPCH_RSCP_Total / countPCCPCH_RSCP;
            if (countPCCPCH_C2I > 0)
                dInfo.avgPCCPCH_C2I = PCCPCH_C2I_Total / countPCCPCH_C2I;
            if (countDPCH_RSCP > 0)
                dInfo.avgDPCH_RSCP = DPCH_RSCP_Total / countDPCH_RSCP;
            if (countDPCH_C2I > 0)
                dInfo.avgDPCH_C2I = DPCH_C2I_Total / countDPCH_C2I;
        }

        private void setDropPerceptionInfoByTP(List<TestPoint> testPointAll, int beginDropTpIndex, DropPerceptionInfo dInfo)
        {
            if (testPointAll[beginDropTpIndex][MainModel.TD_SCell_UARFCN] != null)  //TD网络下的频点
            {
                int value = (int)testPointAll[beginDropTpIndex][MainModel.TD_SCell_UARFCN];
                if (value != -10000000)
                {
                    dInfo.arfcn = value;
                    dInfo.kind = "TD";
                }
            }
            else if (testPointAll[beginDropTpIndex]["BCCH"] != null)  //在TD网咯下找不到频点，转载GSM下BCCH作频点
            {
                int value = (int)testPointAll[beginDropTpIndex]["BCCH"];
                if (value != -255)
                {
                    dInfo.arfcn = value;
                    dInfo.kind = "GSM";
                }
            }
            
            dInfo.cpi = getValidTPInfo(testPointAll, beginDropTpIndex, MainModel.TD_SCell_CPI, -10000000);
            dInfo.dpch = getValidTPInfo(testPointAll, beginDropTpIndex, "TD_DPCH_UARFCN", -10000000);
            dInfo.HS_PDSCH_RSCP = getValidTPInfo(testPointAll, beginDropTpIndex, "TD_HSDPA_HS_PDSCH_RSCP", -10000);
            dInfo.HS_PDSCH_CI = getValidTPInfo(testPointAll, beginDropTpIndex, "TD_HSDPA_HS_PDSCH_CI", -10000);
            dInfo.HS_WorkUARFCN = getValidTPInfo(testPointAll, beginDropTpIndex, "TD_HSDPA_HS_WorkUARFCN", -10000000);
        }

        private int getValidTPInfo(List<TestPoint> testPointAll, int beginDropTpIndex, string paramName, int invalidData)
        {
            if (testPointAll[beginDropTpIndex][paramName] != null && (int)testPointAll[beginDropTpIndex][paramName] != invalidData)
            {
                return (int)testPointAll[beginDropTpIndex][paramName];
            }
            return 0;
        }

        private void setSwitchTimes(List<Event> eventAll, DropPerceptionInfo dInfo, DateTime dInfoTime)
        {
            int switchTimes = 0;
            foreach (Event evt in eventAll)
            {
                if (evt.ID == 142 || evt.ID == 145 || evt.ID == 148 || evt.ID == 151)//切换事件
                {
                    DateTime dt = JavaDate.GetDateTimeFromMilliseconds(evt.Time * 1000L);
                    if (dt.CompareTo(dInfoTime) <= 0 && dt.AddSeconds(10).CompareTo(dInfoTime) >= 0)
                    {
                        switchTimes++;
                    }
                }
            }
            dInfo.switchTimes = switchTimes;
        }

        private void setNBDropPerceptionInfo(List<TestPoint> testPointAll, int beginDropTpIndex, DropPerceptionInfo dInfo)
        {
            int? nbCell1arfcn = (int?)testPointAll[beginDropTpIndex]["TD_NCell_UARFCN", 0];
            int? nbCell1cpi = (int?)testPointAll[beginDropTpIndex]["TD_NCell_CPI", 0];
            int? nbCell1RSCP = (int?)testPointAll[beginDropTpIndex]["TD_NCell_PCCPCH_RSCP", 0];
            if (nbCell1arfcn != null)
                dInfo.nbCell1Arfcn = (int)nbCell1arfcn;
            if (nbCell1cpi != null)
            {
                dInfo.nbCell1Cpi = (int)nbCell1cpi;
            }
            if (nbCell1RSCP != null)
            {
                dInfo.nbCell1PCCPCH_RSCP = (int)nbCell1RSCP;
            }

            int? nbCell2arfcn = (int?)testPointAll[beginDropTpIndex]["TD_NCell_UARFCN", 1];
            int? nbCell2cpi = (int?)testPointAll[beginDropTpIndex]["TD_NCell_CPI", 1];
            int? nbCell2RSCP = (int?)testPointAll[beginDropTpIndex]["TD_NCell_PCCPCH_RSCP", 1];
            if (nbCell2arfcn != null)
                dInfo.nbCell2Arfcn = (int)nbCell2arfcn;
            if (nbCell2cpi != null)
            {
                dInfo.nbCell2Cpi = (int)nbCell2cpi;
            }
            if (nbCell2RSCP != null)
            {
                dInfo.nbCell2PCCPCH_RSCP = (int)nbCell2RSCP;
            }

            int? nbCell3arfcn = (int?)testPointAll[beginDropTpIndex]["TD_NCell_UARFCN", 2];
            int? nbCell3cpi = (int?)testPointAll[beginDropTpIndex]["TD_NCell_CPI", 2];
            int? nbCell3RSCP = (int?)testPointAll[beginDropTpIndex]["TD_NCell_PCCPCH_RSCP", 2];
            if (nbCell3arfcn != null)
                dInfo.nbCell3Arfcn = (int)nbCell3arfcn;
            if (nbCell3cpi != null)
            {
                dInfo.nbCell3Cpi = (int)nbCell3cpi;
            }
            if (nbCell3RSCP != null)
            {
                dInfo.nbCell3PCCPCH_RSCP = (int)nbCell3RSCP;
            }

            int? nbCell4arfcn = (int?)testPointAll[beginDropTpIndex]["TD_NCell_UARFCN", 3];
            int? nbCell4cpi = (int?)testPointAll[beginDropTpIndex]["TD_NCell_CPI", 3];
            int? nbCell4RSCP = (int?)testPointAll[beginDropTpIndex]["TD_NCell_PCCPCH_RSCP", 3];
            if (nbCell4arfcn != null)
                dInfo.nbCell4Arfcn = (int)nbCell4arfcn;
            if (nbCell4cpi != null)
            {
                dInfo.nbCell4Cpi = (int)nbCell4cpi;
            }
            if (nbCell4RSCP != null)
            {
                dInfo.nbCell4PCCPCH_RSCP = (int)nbCell4RSCP;
            }

            int? nbCell5arfcn = (int?)testPointAll[beginDropTpIndex]["TD_NCell_UARFCN", 4];
            int? nbCell5cpi = (int?)testPointAll[beginDropTpIndex]["TD_NCell_CPI", 4];
            int? nbCell5RSCP = (int?)testPointAll[beginDropTpIndex]["TD_NCell_PCCPCH_RSCP", 4];
            if (nbCell5arfcn != null)
                dInfo.nbCell5Arfcn = (int)nbCell5arfcn;
            if (nbCell5cpi != null)
            {
                dInfo.nbCell5Cpi = (int)nbCell5cpi;
            }
            if (nbCell5RSCP != null)
            {
                dInfo.nbCell5PCCPCH_RSCP = (int)nbCell5RSCP;
            }
        }

        private void addResultEventDropDic(DropPerceptionInfo dInfo, string evt)
        {
            if (!resultEventDropDic.ContainsKey(evt))
            {
                List<DropPerceptionInfo> dropList = new List<DropPerceptionInfo>();
                dropList.Add(dInfo);
                resultEventDropDic.Add(evt, dropList);
            }
            else
            {
                resultEventDropDic[evt].Add(dInfo);
            }
        }

        public class DIYReplayFile : DIYReplayFileQuery
        {
            /// <summary>
            /// 采样点按区域过滤
            /// </summary>
            public bool FilterSampleByRegion { get; set; }
            /// <summary>
            /// 事件按区域过滤
            /// </summary>
            public bool FilterEventByRegion { get; set; }
            /// <summary>
            /// 是否查询消息
            /// </summary>
            public bool IncludeMessage { get; set; }
            /// <summary>
            /// 是否查询事件
            /// </summary>
            public bool IncludeEvent { get; set; }
            public DIYReplayFile(MainModel mainModel, string curWaitBoxText, int curProgressPercent)
                : base(mainModel)
            {
                this.curWaitBoxText = curWaitBoxText;
                this.curProgressPercent = curProgressPercent;

                FilterSampleByRegion = true;
                FilterEventByRegion = true;
                IncludeMessage = false;
                IncludeEvent = true;
            }

            readonly string curWaitBoxText;
            readonly int curProgressPercent;
            protected override void query()
            {
                replayContentOption = getDIYReplayContent();
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = new ClientProxy();
                try
                {
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    MainModel.DTDataManager.Clear();
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedEvents.Clear();
                    MainModel.IsDrawEventResult = false;
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }

            protected override void queryInThread(object o)
            {
                try
                {
                    ClientProxy clientProxy = (ClientProxy)o;
                    Package package = clientProxy.Package;
                    int index = 0;
                    foreach (MasterCom.RAMS.Model.FileInfo fileInfo in Condition.FileInfos)
                    {
                        if (fileInfo.FileTypeDescription.Contains("鼎利"))
                        {//鼎利测试文件以左下角为坐标原点
                            MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftBottom;
                        }
                        else if (fileInfo.FileTypeDescription.Contains("烽火"))
                        {//烽火测试文件以左上角为坐标原点
                            MainModel.CQTPlanImgOrigin = MainModel.OriginType.LeftTop;
                        }
                        index = setMTRModeInfo(index, fileInfo);
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = curWaitBoxText;
                        WaitBox.ProgressPercent = curProgressPercent;
                        queryReplayInfo(clientProxy, package, fileInfo);
                    }
                }
                catch (DebugAssertException dbgE)
                {
                    MessageBox.Show("Debug Assert 失败:" + dbgE.ToString());
                }
                catch (Exception ex)
                {
                    log.Error(ex.StackTrace);
                }
                finally
                {
                    WaitBox.Close();
                }
            }

            private int setMTRModeInfo(int index, FileInfo fileInfo)
            {
                if (MainModel.IsFileReplayByMTRMode)
                {
                    if (index == 1)
                    {
                        fileIdMTR = fileInfo.ID;
                        canGetHeaderMTR = true;
                    }
                    if (index > 0)
                    {
                        fileOffsetTimeMS = fileInfo.OffsetTimeMS;
                    }
                }
                if (MainModel.IsFileReplayByMTRToLogMode && index == 0)
                {
                    fileOffsetTimeMS = fileInfo.OffsetTimeMS;
                }
                index++;
                fileIndex++;
                return index;
            }

            protected override DIYReplayContentOption getDIYReplayContent()
            {
                DIYReplayContentOption option = new DIYReplayContentOption();
                addSampleColumns(option, "isampleid");
                addSampleColumns(option, "itime");
                addSampleColumns(option, "FileName");
                addSampleColumns(option, "ilongitude");
                addSampleColumns(option, "ilatitude");
                addSampleColumns(option, "LAC");
                addSampleColumns(option, "CI");
                addSampleColumns(option, "BCCH");
                addSampleColumns(option, "BSIC");
                addSampleColumns(option, MainModel.TD_SCell_LAC);
                addSampleColumns(option, MainModel.TD_SCell_CI);
                addSampleColumns(option, MainModel.TD_SCell_UARFCN);
                addSampleColumns(option, MainModel.TD_SCell_CPI);
                addSampleColumns(option, "TD_PCCPCH_RSCP");
                addSampleColumns(option, "TD_PCCPCH_C2I");
                addSampleColumns(option, "TD_NCell_PCCPCH_RSCP");
                addSampleColumns(option, "TD_NCell_UARFCN");
                addSampleColumns(option, "TD_NCell_CPI");
                addSampleColumns(option, "TDS_PCCPCH_RSCP");
                addSampleColumns(option, "TDS_PCCPCH_Channel");
                addSampleColumns(option, "TDS_PCCPCH_CPI");
                addSampleColumns(option, "TD_HSDPA_HS_PDSCH_RSCP");
                addSampleColumns(option, "TD_HSDPA_HS_PDSCH_CI");
                addSampleColumns(option, "TD_HSDPA_HS_WorkUARFCN");
                addSampleColumns(option, "TD_DPCH_RSCP");
                addSampleColumns(option, "TD_DPCH_C2I");
                addSampleColumns(option, "TD_DPCH_UARFCN");
                option.EventInclude = true;
                return option;
            }

            private void addSampleColumns(DIYReplayContentOption option, string paramName)
            {
                List<ColumnDefItem> columns = InterfaceManager.GetInstance().GetColumnDefByShowName(paramName);
                if (columns != null && columns.Count > 0)
                {
                    option.SampleColumns.AddRange(columns);
                }
            }

            protected override bool isValidTestPoint(TestPoint tp)
            {
                try
                {
                    if (FilterSampleByRegion && condition.Geometorys != null && condition.Geometorys.Region != null && !condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
                    {
                        return false;
                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }

            protected override bool isValidEvent(Event e)
            {
                try
                {
                    if (FilterEventByRegion && condition.Geometorys != null && condition.Geometorys.Region != null && !condition.Geometorys.GeoOp.Contains(e.Longitude, e.Latitude))
                    {
                        return false;
                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }
        }
    }

    public class DIYSQLQueryLacCityName : DIYSQLBase
    {
        public DIYSQLQueryLacCityName(MainModel mainModel)
            : base(mainModel)
        {
            LacCityNameMap = new Dictionary<int, string>();
        }

        public override string Name
        {
            get { return "DiySqlQueryLacCityName"; }
        }

        /// <summary>
        /// 查询LAC相关的地市名
        /// </summary>
        public Dictionary<int, string> LacCityNameMap { get; set; }
        protected override string getSqlTextString()
        {
            string sql = "SELECT DISTINCT lac,city FROM dbo.tb_gd_platform_lac";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            return rType;
        }


        protected override void receiveRetData(ClientProxy clientProxy)
        {
            LacCityNameMap.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LacCityName lacCityName = LacCityName.FillData(package.Content);
                    LacCityNameMap.Add(lacCityName.lac, lacCityName.cityName);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class LacCityName
    {
        public int lac { get; set; }
        public string cityName { get; set; }
        public static LacCityName FillData(Content content)
        {
            LacCityName lacCityName = new LacCityName();
            lacCityName.lac = content.GetParamInt();
            lacCityName.cityName = content.GetParamString();
            return lacCityName;
        }
    }

    public class DropPerceptionInfo
    {
        public string district { get; set; }
        public string fileName { get; set; }
        public DateTime eventHappenTime { get; set; }
        public string eventName { get; set; }
        public double evtLongitude { get; set; }
        public double evtLatitude { get; set; }
        public int evtLac { get; set; }
        public int evtCi { get; set; }
        public double dropTpLongitude { get; set; }
        public double dropTpLatitude { get; set; }
        public int dropTpLac { get; set; }
        public int dropTpCi { get; set; }
        public DateTime cutoffStarttimeTp { get; set; }
        public int arfcn { get; set; } //开始断流频点
        public int cpi { get; set; }  //开始断流扰码
        public int dpch { get; set; }  //开始断流DPCH频点
        public string kind { get; set; }  //显示TD网络或GSM网络
        public int HS_PDSCH_RSCP { get; set; } //开始断流TD_HSDPA_HS_PDSCH_RSCP
        public int HS_PDSCH_CI { get; set; } //开始断流TD_HSDPA_HS_PDSCH_CI
        public int HS_WorkUARFCN { get; set; } //开始断流HS_WorkUARFCN
        public int avgPCCPCH_RSCP { get; set; } //平均PCCPCH_RSCP
        public int avgPCCPCH_C2I { get; set; } //平均PCCPCH_C2I
        public int avgDPCH_RSCP { get; set; } //平均DPCH_RSCP
        public int avgDPCH_C2I { get; set; } //平均DPCH_C2I
        public int switchTimes { get; set; }  //切换次数
        public int nbCell1Arfcn { get; set; }  //邻区1主频
        public int nbCell1Cpi { get; set; } //邻区1扰码
        public int nbCell1PCCPCH_RSCP { get; set; } //邻区1PCCPCH_RSCP
        public int nbCell2Arfcn { get; set; } //邻区2主频
        public int nbCell2Cpi { get; set; } //邻区2扰码
        public int nbCell2PCCPCH_RSCP { get; set; } //邻区2PCCPCH_RSCP
        public int nbCell3Arfcn { get; set; }  //邻区3主频
        public int nbCell3Cpi { get; set; } //邻区3扰码
        public int nbCell3PCCPCH_RSCP { get; set; } //邻区3PCCPCH_RSCP
        public int nbCell4Arfcn { get; set; } //邻区4主频
        public int nbCell4Cpi { get; set; } //邻区4扰码
        public int nbCell4PCCPCH_RSCP { get; set; } //邻区4PCCPCH_RSCP
        public int nbCell5Arfcn { get; set; } //邻区5主频
        public int nbCell5Cpi { get; set; } //邻区5扰码
        public int nbCell5PCCPCH_RSCP { get; set; } //邻区5PCCPCH_RSCP
    }
}
