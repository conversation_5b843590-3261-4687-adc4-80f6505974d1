﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonOptionDlg : BaseDialog
    {
        public ReasonOptionDlg()
        {
            InitializeComponent();
            this.MouseWheel += new MouseEventHandler(ReasonOptionDlg_MouseWheel);
        }
        public ReasonOptionDlg(FuncCondition condition)
            : this()
        {
            if (condition is FuncCondition_Scan)
            {
                reasonPnlChangeFreq.Visible = false;
                reasonPnlHandOverUnTimely.Visible = false;
                reasonPnlHandoverProblem.Visible = false;
            }

            AttachCondition(condition);
            this.group.MouseWheel += new System.Windows.Forms.MouseEventHandler(this.group_MouseWheel);
        }
        private FuncCondition condition=null;
        public void AttachCondition(FuncCondition condition)
        {
            this.condition = condition;
            numMaxSINR.Value = (decimal)condition.MaxSINR;
            fillChkList();
        }
        private void group_MouseWheel(object sender, MouseEventArgs e)
        {
            int pScrollValueDelta = e.Delta / 2;
            int mVSValue = this.grpDetails.VerticalScroll.Value;

            if ((mVSValue - pScrollValueDelta) <= this.grpDetails.VerticalScroll.Minimum)
            {
                this.grpDetails.VerticalScroll.Value = this.grpDetails.VerticalScroll.Minimum;
            }
            else if ((mVSValue - pScrollValueDelta) >= this.grpDetails.VerticalScroll.Maximum)
            {
                this.grpDetails.VerticalScroll.Value = this.grpDetails.VerticalScroll.Maximum;
            }
            else
            {
                this.grpDetails.VerticalScroll.Value -= pScrollValueDelta;
            }
            this.grpDetails.Refresh();
            this.grpDetails.Invalidate();
            this.grpDetails.Update();

            this.group.Refresh();
            this.group.Invalidate();
            this.group.Update();
        }

        private void fillChkList()
        {
            this.chkList.Items.Clear();
            foreach (ReasonBase item in condition.Reasons)
            {
                chkList.Items.Add(item, item.Enable);
                if (item is ReasonWeakCover)
                {
                    this.pnlWeakCover.AttachReason(item);
                }
                else if (item is ReasonMultiCover)
                {
                    this.pnlMultiCover.AttachReason(item);
                }
                //else if (item is ReasonMixCover)
                //{
                //    this.reasonPnlMixCover.AttachReason(item);
                //}
                else if (item is ReasonChangeFreq)
                {
                    this.reasonPnlChangeFreq.AttachReason(item);
                }
                else if (item is ReasonHandOverUnTimely)
                {
                    this.reasonPnlHandOverUnTimely.AttachReason(item);
                }
                else if (item is ReasonHandOverProblem)
                {
                    this.reasonPnlHandoverProblem.AttachReason(item);
                }
                else if (item is ReasonMod3)
                {
                    this.reasonPnlMod3.AttachReason(item);
                }
                else if (item is ReasonsOverCover)
                {
                    this.reasonPnlOverCover.AttachReason(item);
                }
                else if (item is ReasonsBackCover)
                {
                    this.reasonPnlBackCover.AttachReason(item);
                }
                else if (item is ReasonSuddenWeak)
                {
                    this.reasonPnlSuddenWeak.AttachReason(item);
                }
            }
            chkList.SelectedIndex = 0;
        }

        private void chkList_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (chkList.SelectedIndex < 0)
            {
                return;
            }
            ReasonBase reason = ((DevExpress.XtraEditors.Controls.CheckedListBoxItem)chkList.GetItem(chkList.SelectedIndex)).Value as ReasonBase;
            btnUp.Enabled = chkList.SelectedIndex > 0 && reason != ReasonUnknow.Instance;
            btnDown.Enabled = chkList.SelectedIndex < chkList.ItemCount - 2;
        }

        private void ReasonOptionDlg_MouseWheel(object sender, MouseEventArgs e)
        {
            Point mousePoint = new Point(e.X, e.Y);

            mousePoint.Offset(Location.X, Location.Y);

            if (group.RectangleToScreen(group.DisplayRectangle).Contains(mousePoint))
            {
                group.AutoScrollOffset = mousePoint;
            }
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            moveChkListCurSelectedItem(true);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            moveChkListCurSelectedItem(false);
        }

        private void moveChkListCurSelectedItem(bool up)
        {
            int index = chkList.SelectedIndex;
            object item = chkList.SelectedItem;
            chkList.Items.RemoveAt(index);
            condition.Reasons.RemoveAt(index);
            int newIdx=up ? index - 1 : index + 1;
            chkList.Items.Insert(newIdx, item);
            DevExpress.XtraEditors.Controls.CheckedListBoxItem lbItem = item as DevExpress.XtraEditors.Controls.CheckedListBoxItem;
            condition.Reasons.Insert(newIdx, lbItem.Value as ReasonBase);
            chkList.SelectedIndex = newIdx;
        }

        private void chkList_ItemCheck(object sender, DevExpress.XtraEditors.Controls.ItemCheckEventArgs e)
        {
            ReasonBase reason = ((DevExpress.XtraEditors.Controls.CheckedListBoxItem)chkList.GetItem(e.Index)).Value as ReasonBase;
            if (reason==null)
            {
                return;
            }
            reason.Enable = e.State == CheckState.Checked;
        }

        private void chkList_ItemChecking(object sender, DevExpress.XtraEditors.Controls.ItemCheckingEventArgs e)
        {
            ReasonBase reason = ((DevExpress.XtraEditors.Controls.CheckedListBoxItem)chkList.GetItem(e.Index)).Value as ReasonBase;
            e.Cancel = reason == ReasonUnknow.Instance;
        }

        private void numMaxSINR_EditValueChanged(object sender, EventArgs e)
        {
            condition.MaxSINR = (float)numMaxSINR.Value;
        }

        private void group_MouseEnter(object sender, EventArgs e)
        {
            this.group.Focus();
        }
    }
}
