﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.CheckCellOccupation
{ 
    public class CheckCellOccupationLayer : LayerBase
    {
        //需要绘制的图元信息
        private CellDataBase cellData = null;
        public CheckCellOccupationLayer()
            : base("小区占用核查图层")
        {
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            Pen pen_centerPoint = new Pen(new SolidBrush(Color.Red), 3);
            Pen pen_line = new Pen(new SolidBrush(Color.Red), 1);
            //Font StringFont = new Font("宋体", 15, FontStyle.Regular);
            if (this.cellData == null)
            {
                return;
            }
            PointF centerPoint;
            PointF mainCellPoint;
            PointF nearestBTSPoint;
            float radius = 15;
            float cenRadius = 3;
            gisAdapter.ToDisplay(new DbPoint(cellData.argLon, cellData.argLat), out centerPoint);
            gisAdapter.ToDisplay(new DbPoint(cellData.Cell.Longitude, cellData.Cell.Latitude), out mainCellPoint);
            gisAdapter.ToDisplay(new DbPoint(cellData.BTS.Longitude, cellData.BTS.Latitude), out nearestBTSPoint);
            //画平均点
            graphics.DrawEllipse(pen_centerPoint, centerPoint.X-radius, centerPoint.Y-radius, radius*2, radius*2);
            graphics.DrawLine(pen_centerPoint, centerPoint.X - radius, centerPoint.Y, centerPoint.X - cenRadius, centerPoint.Y);
            graphics.DrawLine(pen_centerPoint, centerPoint.X + cenRadius, centerPoint.Y, centerPoint.X + radius, centerPoint.Y);
            graphics.DrawLine(pen_centerPoint, centerPoint.X , centerPoint.Y- radius, centerPoint.X , centerPoint.Y- cenRadius);
            graphics.DrawLine(pen_centerPoint, centerPoint.X , centerPoint.Y+cenRadius, centerPoint.X, centerPoint.Y+radius);
            //画平均点和主服小区、最近基站的连线
            graphics.DrawLine(pen_line, centerPoint, mainCellPoint);
            graphics.DrawLine(pen_line, centerPoint, nearestBTSPoint);
        }

        public void SetData(CellDataBase cd)
        {
            this.cellData = cd;
        }
    }
}
