﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTTDLTECellCoverLapAnaSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.numRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxSampleCount = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.chkDistance = new System.Windows.Forms.CheckBox();
            this.numDistanceMax = new System.Windows.Forms.NumericUpDown();
            this.numDistanceMin = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.tbxNumPercent = new System.Windows.Forms.TextBox();
            this.tbxSampleCount = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxPercent = new System.Windows.Forms.CheckBox();
            this.numDisFactor = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.numSiteCount = new System.Windows.Forms.NumericUpDown();
            this.cbxFreqBand = new DevExpress.XtraEditors.ComboBoxEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chkFreqBand = new System.Windows.Forms.CheckBox();
            this.label5 = new System.Windows.Forms.Label();
            this.numRxlevDiff = new System.Windows.Forms.NumericUpDown();
            this.chkRxlevMin = new System.Windows.Forms.CheckBox();
            this.chkRxlevDiff = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDisFactor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBand.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevDiff)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnOK.Location = new System.Drawing.Point(404, 314);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnCancel.Location = new System.Drawing.Point(498, 314);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // numRxlevMin
            // 
            this.numRxlevMin.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxlevMin.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxlevMin.Location = new System.Drawing.Point(112, 59);
            this.numRxlevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Name = "numRxlevMin";
            this.numRxlevMin.Size = new System.Drawing.Size(75, 21);
            this.numRxlevMin.TabIndex = 2;
            this.numRxlevMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxlevMin.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(192, 64);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(23, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "dBm";
            // 
            // cbxSampleCount
            // 
            this.cbxSampleCount.AutoSize = true;
            this.cbxSampleCount.Location = new System.Drawing.Point(28, 29);
            this.cbxSampleCount.Name = "cbxSampleCount";
            this.cbxSampleCount.Size = new System.Drawing.Size(96, 16);
            this.cbxSampleCount.TabIndex = 1;
            this.cbxSampleCount.Text = "采样点数量≥";
            this.cbxSampleCount.UseVisualStyleBackColor = true;
            this.cbxSampleCount.CheckedChanged += new System.EventHandler(this.cbxSampleCount_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.chkDistance);
            this.groupBox1.Controls.Add(this.numDistanceMax);
            this.groupBox1.Controls.Add(this.numDistanceMin);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.tbxNumPercent);
            this.groupBox1.Controls.Add(this.tbxSampleCount);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.cbxPercent);
            this.groupBox1.Controls.Add(this.cbxSampleCount);
            this.groupBox1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox1.Location = new System.Drawing.Point(28, 160);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(545, 136);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "结果过滤";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(367, 99);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(17, 12);
            this.label11.TabIndex = 13;
            this.label11.Text = "米";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(204, 29);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 12;
            this.label10.Text = "个";
            // 
            // chkDistance
            // 
            this.chkDistance.AutoSize = true;
            this.chkDistance.Location = new System.Drawing.Point(27, 99);
            this.chkDistance.Name = "chkDistance";
            this.chkDistance.Size = new System.Drawing.Size(84, 16);
            this.chkDistance.TabIndex = 9;
            this.chkDistance.Text = "过覆盖距离";
            this.chkDistance.UseVisualStyleBackColor = true;
            this.chkDistance.CheckedChanged += new System.EventHandler(this.chkDistance_CheckedChanged);
            // 
            // numDistanceMax
            // 
            this.numDistanceMax.Enabled = false;
            this.numDistanceMax.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDistanceMax.Location = new System.Drawing.Point(286, 95);
            this.numDistanceMax.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDistanceMax.Name = "numDistanceMax";
            this.numDistanceMax.Size = new System.Drawing.Size(75, 21);
            this.numDistanceMax.TabIndex = 8;
            this.numDistanceMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistanceMax.Value = new decimal(new int[] {
            3000,
            0,
            0,
            0});
            // 
            // numDistanceMin
            // 
            this.numDistanceMin.Enabled = false;
            this.numDistanceMin.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDistanceMin.Location = new System.Drawing.Point(126, 95);
            this.numDistanceMin.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDistanceMin.Name = "numDistanceMin";
            this.numDistanceMin.Size = new System.Drawing.Size(75, 21);
            this.numDistanceMin.TabIndex = 7;
            this.numDistanceMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistanceMin.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(208, 99);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 6;
            this.label6.Text = "米";
            // 
            // tbxNumPercent
            // 
            this.tbxNumPercent.Enabled = false;
            this.tbxNumPercent.Location = new System.Drawing.Point(125, 60);
            this.tbxNumPercent.Name = "tbxNumPercent";
            this.tbxNumPercent.Size = new System.Drawing.Size(75, 21);
            this.tbxNumPercent.TabIndex = 3;
            this.tbxNumPercent.Text = "10";
            this.tbxNumPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // tbxSampleCount
            // 
            this.tbxSampleCount.Enabled = false;
            this.tbxSampleCount.Location = new System.Drawing.Point(125, 24);
            this.tbxSampleCount.Name = "tbxSampleCount";
            this.tbxSampleCount.Size = new System.Drawing.Size(75, 21);
            this.tbxSampleCount.TabIndex = 3;
            this.tbxSampleCount.Text = "100";
            this.tbxSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(227, 99);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "≤距离≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(205, 65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(11, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "%";
            // 
            // cbxPercent
            // 
            this.cbxPercent.AutoSize = true;
            this.cbxPercent.Location = new System.Drawing.Point(27, 64);
            this.cbxPercent.Name = "cbxPercent";
            this.cbxPercent.Size = new System.Drawing.Size(96, 16);
            this.cbxPercent.TabIndex = 1;
            this.cbxPercent.Text = "过覆盖比例≥";
            this.cbxPercent.UseVisualStyleBackColor = true;
            this.cbxPercent.CheckedChanged += new System.EventHandler(this.cbxPercent_CheckedChanged);
            // 
            // numDisFactor
            // 
            this.numDisFactor.DecimalPlaces = 1;
            this.numDisFactor.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDisFactor.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numDisFactor.Location = new System.Drawing.Point(91, 58);
            this.numDisFactor.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numDisFactor.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numDisFactor.Name = "numDisFactor";
            this.numDisFactor.Size = new System.Drawing.Size(75, 21);
            this.numDisFactor.TabIndex = 2;
            this.numDisFactor.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDisFactor.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(29, 63);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "覆盖系数=";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(29, 30);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(77, 12);
            this.label12.TabIndex = 13;
            this.label12.Text = "参考基站数≤";
            // 
            // numSiteCount
            // 
            this.numSiteCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSiteCount.Location = new System.Drawing.Point(112, 24);
            this.numSiteCount.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSiteCount.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numSiteCount.Name = "numSiteCount";
            this.numSiteCount.Size = new System.Drawing.Size(75, 21);
            this.numSiteCount.TabIndex = 12;
            this.numSiteCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSiteCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // cbxFreqBand
            // 
            this.cbxFreqBand.EditValue = "D";
            this.cbxFreqBand.Enabled = false;
            this.cbxFreqBand.Location = new System.Drawing.Point(111, 94);
            this.cbxFreqBand.Name = "cbxFreqBand";
            this.cbxFreqBand.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxFreqBand.Properties.Appearance.Options.UseFont = true;
            this.cbxFreqBand.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxFreqBand.Properties.Items.AddRange(new object[] {
            "D",
            "F"});
            this.cbxFreqBand.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxFreqBand.Size = new System.Drawing.Size(75, 20);
            this.cbxFreqBand.TabIndex = 16;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkFreqBand);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.numRxlevDiff);
            this.groupBox2.Controls.Add(this.cbxFreqBand);
            this.groupBox2.Controls.Add(this.chkRxlevMin);
            this.groupBox2.Controls.Add(this.chkRxlevDiff);
            this.groupBox2.Controls.Add(this.numRxlevMin);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox2.Location = new System.Drawing.Point(28, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(293, 130);
            this.groupBox2.TabIndex = 17;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "信号强度设置";
            // 
            // chkFreqBand
            // 
            this.chkFreqBand.AutoSize = true;
            this.chkFreqBand.Location = new System.Drawing.Point(27, 98);
            this.chkFreqBand.Name = "chkFreqBand";
            this.chkFreqBand.Size = new System.Drawing.Size(84, 16);
            this.chkFreqBand.TabIndex = 14;
            this.chkFreqBand.Text = "按频段分析";
            this.chkFreqBand.UseVisualStyleBackColor = true;
            this.chkFreqBand.CheckStateChanged += new System.EventHandler(this.chkFreqBand_CheckStateChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(263, 27);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 16;
            this.label5.Text = "dB";
            // 
            // numRxlevDiff
            // 
            this.numRxlevDiff.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxlevDiff.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxlevDiff.Location = new System.Drawing.Point(182, 24);
            this.numRxlevDiff.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numRxlevDiff.Name = "numRxlevDiff";
            this.numRxlevDiff.Size = new System.Drawing.Size(75, 21);
            this.numRxlevDiff.TabIndex = 15;
            this.numRxlevDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxlevDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // chkRxlevMin
            // 
            this.chkRxlevMin.AutoSize = true;
            this.chkRxlevMin.Checked = true;
            this.chkRxlevMin.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRxlevMin.Location = new System.Drawing.Point(27, 63);
            this.chkRxlevMin.Name = "chkRxlevMin";
            this.chkRxlevMin.Size = new System.Drawing.Size(84, 16);
            this.chkRxlevMin.TabIndex = 14;
            this.chkRxlevMin.Text = "信号强度≥";
            this.chkRxlevMin.UseVisualStyleBackColor = true;
            this.chkRxlevMin.CheckedChanged += new System.EventHandler(this.chkRxlevMin_CheckedChanged);
            // 
            // chkRxlevDiff
            // 
            this.chkRxlevDiff.AutoSize = true;
            this.chkRxlevDiff.Checked = true;
            this.chkRxlevDiff.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRxlevDiff.Location = new System.Drawing.Point(27, 28);
            this.chkRxlevDiff.Name = "chkRxlevDiff";
            this.chkRxlevDiff.Size = new System.Drawing.Size(156, 16);
            this.chkRxlevDiff.TabIndex = 14;
            this.chkRxlevDiff.Text = "与第一强信号强度差异≤";
            this.chkRxlevDiff.UseVisualStyleBackColor = true;
            this.chkRxlevDiff.CheckedChanged += new System.EventHandler(this.chkRxlevDiff_CheckedChanged);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.numSiteCount);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.numDisFactor);
            this.groupBox3.Location = new System.Drawing.Point(337, 12);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(236, 98);
            this.groupBox3.TabIndex = 18;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "理想覆盖距离设置";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(193, 27);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 17;
            this.label7.Text = "个";
            // 
            // ZTTDLTECellCoverLapAnaSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(596, 349);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "ZTTDLTECellCoverLapAnaSetForm";
            this.Text = "过覆盖分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDisFactor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBand.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevDiff)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.NumericUpDown numRxlevMin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox cbxSampleCount;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxPercent;
        private System.Windows.Forms.TextBox tbxSampleCount;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox tbxNumPercent;
        private System.Windows.Forms.NumericUpDown numDisFactor;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numDistanceMax;
        private System.Windows.Forms.NumericUpDown numDistanceMin;
        private System.Windows.Forms.CheckBox chkDistance;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numSiteCount;
        private DevExpress.XtraEditors.ComboBoxEdit cbxFreqBand;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numRxlevDiff;
        private System.Windows.Forms.CheckBox chkRxlevMin;
        private System.Windows.Forms.CheckBox chkRxlevDiff;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox chkFreqBand;
    }
}