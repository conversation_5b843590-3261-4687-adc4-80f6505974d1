﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;
using System.IO;
using System.Threading;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteAntSimulatorForm : MinCloseForm
    {
        MapForm mapForm;
        public LteAntSimulatorForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
            city = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            PicFactory = new LteAntSimulatorConfig(city);
        }

        protected LteAntSimulatorForm()
        {

        }

        string city = "广州";
        LteCellAntSimulator curLteCell = new LteCellAntSimulator();
        EPictrueType ePicType = EPictrueType.Default; //权值类型
        EAntKPIType eKPIType = EAntKPIType.RsrpMax;   //渲染指标类型
        EAntAreaType eAreaType = EAntAreaType.AreaInside; //渲染区域
        LteAntSimulatorFullBandType eBandType = LteAntSimulatorFullBandType.All; //频点
        EModelType eModelType = EModelType.Port1;
        Dictionary<LteCellAntSimulator, ZTLteAntSimulator.AntParaArray> lteCellParaDic = new Dictionary<LteCellAntSimulator, ZTLteAntSimulator.AntParaArray>();

        /// <summary>
        /// 数据初始化
        /// </summary>
        public void FillData(Dictionary<LTECell, ZTLteAntSimulator.AntParaArray> resultDic)
        {
            foreach (LTECell lteCell in resultDic.Keys)
            {
                LteCellAntSimulator lcAnt = new LteCellAntSimulator(lteCell);
                if (string.IsNullOrEmpty(lcAnt.lteCell.Name))
                    continue;
                if (!lteCellParaDic.ContainsKey(lcAnt))
                    lteCellParaDic.Add(lcAnt, resultDic[lteCell]);
            }
            Register();
            fillCheckBox();
            fillPortValue();
            paraTypeCb_SelectedIndexChanged(null, null);
        }

        /// <summary>
        /// 下拉选择框填值
        /// </summary>
        private void fillCheckBox()
        {
            cellPart.Items.Clear();
            cellPart.Text = "";
            int idx = 0;
            foreach (LteCellAntSimulator lteCell in lteCellParaDic.Keys)
            {
                string cellName = lteCell.lteCell.Name;
                if (cellPart.Text == "")
                {
                    cellPart.Text = cellName;
                    curLteCell = lteCell;
                }
                ShowDefaultBackgroundPic(lteCell, idx++);
                cellPart.Items.Add(cellName);
            }

            #region 下拉选项
            paraTypeCb.Items.Clear();
            paraTypeCb.Items.Add(EnumDescriptionAttribute.GetText(EPictrueType.Default));
            paraTypeCb.Items.Add(EnumDescriptionAttribute.GetText(EPictrueType.Assess));
            paraTypeCb.SelectedIndex = 0;

            ccbKPIDraw.Items.Clear();
            ccbKPIDraw.Items.Add(EnumDescriptionAttribute.GetText(EAntKPIType.RsrpMax));
            ccbKPIDraw.Items.Add(EnumDescriptionAttribute.GetText(EAntKPIType.RsrpAvg));
            ccbKPIDraw.Items.Add(EnumDescriptionAttribute.GetText(EAntKPIType.Sinr));
            ccbKPIDraw.Items.Add(EnumDescriptionAttribute.GetText(EAntKPIType.Sinr3));
            ccbKPIDraw.Items.Add(EnumDescriptionAttribute.GetText(EAntKPIType.Overlap));
            ccbKPIDraw.SelectedIndex = 0;

            ccbArea.Items.Clear();
            ccbArea.Items.Add(EnumDescriptionAttribute.GetText(EAntAreaType.AreaInside));
            ccbArea.Items.Add(EnumDescriptionAttribute.GetText(EAntAreaType.All));
            ccbArea.SelectedIndex = 0;

            ccbBandType.Items.Clear();
            ccbBandType.Items.Add(EnumDescriptionAttribute.GetText(LteAntSimulatorFullBandType.All));
            ccbBandType.Items.Add(EnumDescriptionAttribute.GetText(LteAntSimulatorFullBandType.SingleD));
            ccbBandType.Items.Add(EnumDescriptionAttribute.GetText(LteAntSimulatorFullBandType.SingleD1));
            ccbBandType.Items.Add(EnumDescriptionAttribute.GetText(LteAntSimulatorFullBandType.SingleD2));
            ccbBandType.Items.Add(EnumDescriptionAttribute.GetText(LteAntSimulatorFullBandType.SingleD3));
            ccbBandType.Items.Add(EnumDescriptionAttribute.GetText(LteAntSimulatorFullBandType.SingleF));
            //ccbBandType.Items.Add(EnumDescription.GetText(LteAntSimulatorFullBandType.SingleF1))暂未确定F1/F2/F3
            //ccbBandType.Items.Add(EnumDescription.GetText(LteAntSimulatorFullBandType.SingleF2))
            //ccbBandType.Items.Add(EnumDescription.GetText(LteAntSimulatorFullBandType.SingleF3))
            ccbBandType.SelectedIndex = 0;

            ccbModel.Items.Add(EnumDescriptionAttribute.GetText(EModelType.Max));
            ccbModel.Items.Add(EnumDescriptionAttribute.GetText(EModelType.Port1));
            ccbModel.Items.Add(EnumDescriptionAttribute.GetText(EModelType.Port2));
            ccbModel.SelectedIndex = 1;
            #endregion 
        }

        /// <summary>
        /// 各端口权值赋值
        /// </summary>
        private void fillPortValue()
        {
            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(curLteCell, out apa))
            {
                AntennaPara antPara = new AntennaPara();
                if (ePicType == EPictrueType.Default)
                {
                    antPara = apa.antParaOrg;
                }
                else if (ePicType == EPictrueType.Assess)
                {
                    antPara = apa.antParaNew;
                }
                txtBoxOverlay.Text = antPara.dDownward == 9999 ? curLteCell.lteCell.Antennas[0].Downward.ToString() : antPara.dDownward.ToString();
                antPara.dDownward = antPara.dDownward == 9999 ? curLteCell.lteCell.Antennas[0].Downward : antPara.dDownward;
                #region txtBox幅度相位填值
                tbRange1.Text = getValidData(antPara.drangeport1, apa.antParaOrg.drangeport1);
                tbRange2.Text = getValidData(antPara.drangeport2, apa.antParaOrg.drangeport2);
                tbRange3.Text = getValidData(antPara.drangeport3, apa.antParaOrg.drangeport3);
                tbRange4.Text = getValidData(antPara.drangeport4, apa.antParaOrg.drangeport4);
                tbRange5.Text = getValidData(antPara.drangeport5, apa.antParaOrg.drangeport5);
                tbRange6.Text = getValidData(antPara.drangeport6, apa.antParaOrg.drangeport6);
                tbRange7.Text = getValidData(antPara.drangeport7, apa.antParaOrg.drangeport7);
                tbRange8.Text = getValidData(antPara.drangeport8, apa.antParaOrg.drangeport8);

                tbPhase1.Text = getValidData(antPara.dphaseport1, apa.antParaOrg.dphaseport1);
                tbPhase2.Text = getValidData(antPara.dphaseport2, apa.antParaOrg.dphaseport2);
                tbPhase3.Text = getValidData(antPara.dphaseport3, apa.antParaOrg.dphaseport3);
                tbPhase4.Text = getValidData(antPara.dphaseport4, apa.antParaOrg.dphaseport4);
                tbPhase5.Text = getValidData(antPara.dphaseport5, apa.antParaOrg.dphaseport5);
                tbPhase6.Text = getValidData(antPara.dphaseport6, apa.antParaOrg.dphaseport6);
                tbPhase7.Text = getValidData(antPara.dphaseport7, apa.antParaOrg.dphaseport7);
                tbPhase8.Text = getValidData(antPara.dphaseport8, apa.antParaOrg.dphaseport8);

                txtBoxRange_TextUpdate(tbRange1, null);
                txtBoxRange_TextUpdate(tbRange2, null);
                txtBoxRange_TextUpdate(tbRange3, null);
                txtBoxRange_TextUpdate(tbRange4, null);
                txtBoxRange_TextUpdate(tbRange5, null);
                txtBoxRange_TextUpdate(tbRange6, null);
                txtBoxRange_TextUpdate(tbRange7, null);
                txtBoxRange_TextUpdate(tbRange8, null);

                txtBoxPhase_TextUpdate(tbPhase1, null);
                txtBoxPhase_TextUpdate(tbPhase2, null);
                txtBoxPhase_TextUpdate(tbPhase3, null);
                txtBoxPhase_TextUpdate(tbPhase4, null);
                txtBoxPhase_TextUpdate(tbPhase5, null);
                txtBoxPhase_TextUpdate(tbPhase6, null);
                txtBoxPhase_TextUpdate(tbPhase7, null);
                txtBoxPhase_TextUpdate(tbPhase8, null);
                #endregion
            }
        }

        private string getValidData(float data, float def)
        {
            if (data == 9999)
            {
                return def.ToString();
            }
            return data.ToString();
        }

        /// <summary>
        /// 取各端口权值
        /// </summary>
        private void getPortValue()
        {
            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(curLteCell, out apa))
            {
                AntennaPara antPara = new AntennaPara();
                if (ePicType == EPictrueType.Default)
                {
                    antPara = apa.antParaOrg;
                }
                else if (ePicType == EPictrueType.Assess)
                {
                    antPara = apa.antParaNew;
                }
                #region txtBox幅度相位取值
                antPara.drangeport1 = Convert.ToSingle(tbRange1.Text.ToString());
                antPara.drangeport2 = Convert.ToSingle(tbRange2.Text.ToString());
                antPara.drangeport3 = Convert.ToSingle(tbRange3.Text.ToString());
                antPara.drangeport4 = Convert.ToSingle(tbRange4.Text.ToString());
                antPara.drangeport5 = Convert.ToSingle(tbRange5.Text.ToString());
                antPara.drangeport6 = Convert.ToSingle(tbRange6.Text.ToString());
                antPara.drangeport7 = Convert.ToSingle(tbRange7.Text.ToString());
                antPara.drangeport8 = Convert.ToSingle(tbRange8.Text.ToString());

                antPara.dphaseport1 = Convert.ToSingle(tbPhase1.Text.ToString());
                antPara.dphaseport2 = Convert.ToSingle(tbPhase2.Text.ToString());
                antPara.dphaseport3 = Convert.ToSingle(tbPhase3.Text.ToString());
                antPara.dphaseport4 = Convert.ToSingle(tbPhase4.Text.ToString());
                antPara.dphaseport5 = Convert.ToSingle(tbPhase5.Text.ToString());
                antPara.dphaseport6 = Convert.ToSingle(tbPhase6.Text.ToString());
                antPara.dphaseport7 = Convert.ToSingle(tbPhase7.Text.ToString());
                antPara.dphaseport8 = Convert.ToSingle(tbPhase8.Text.ToString());

                antPara.strcity = apa.antParaOrg.strcity;
                antPara.strdevvender = apa.antParaOrg.strdevvender;
                antPara.strcellname = apa.antParaOrg.strcellname;
                antPara.strcellnameen = apa.antParaOrg.strcellnameen;
                antPara.ienodebid = apa.antParaOrg.ienodebid;
                antPara.isectorid = apa.antParaOrg.isectorid;
                antPara.strWeightValue = apa.antParaOrg.strWeightValue;
                antPara.strcgi = apa.antParaOrg.strcgi;
                antPara.strtype = apa.antParaOrg.strtype;
                antPara.strbandtype = apa.antParaOrg.strbandtype;
                antPara.strbeamwidth = apa.antParaOrg.strbeamwidth;
                antPara.isSmartAnt = apa.antParaOrg.isSmartAnt;

                antPara.iStatus = apa.antParaOrg.iStatus;
                antPara.dDownward = Convert.ToInt32(txtBoxOverlay.Text.ToString());
                #endregion
            }
        }
       
        private void getPortValue(List<ControlGroup> cGroupList)
        {
            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(curLteCell, out apa))
            {
                AntennaPara antPara = new AntennaPara();
                if (ePicType == EPictrueType.Default)
                {
                    antPara = apa.antParaOrg;
                }
                else if (ePicType == EPictrueType.Assess)
                {
                    antPara = apa.antParaNew;
                }
                #region txtBox幅度相位取值
                antPara.drangeport1 = cGroupList[0].fRange;
                antPara.drangeport2 = cGroupList[1].fRange;
                antPara.drangeport3 = cGroupList[2].fRange;
                antPara.drangeport4 = cGroupList[3].fRange;
                antPara.drangeport5 = cGroupList[4].fRange;
                antPara.drangeport6 = cGroupList[5].fRange;
                antPara.drangeport7 = cGroupList[6].fRange;
                antPara.drangeport8 = cGroupList[7].fRange;

                antPara.dphaseport1 = cGroupList[0].fPhase;
                antPara.dphaseport2 = cGroupList[1].fPhase;
                antPara.dphaseport3 = cGroupList[2].fPhase;
                antPara.dphaseport4 = cGroupList[3].fPhase;
                antPara.dphaseport5 = cGroupList[4].fPhase;
                antPara.dphaseport6 = cGroupList[5].fPhase;
                antPara.dphaseport7 = cGroupList[6].fPhase;
                antPara.dphaseport8 = cGroupList[7].fPhase;

                antPara.strcity = apa.antParaOrg.strcity;
                antPara.strdevvender = apa.antParaOrg.strdevvender;
                antPara.strcellname = apa.antParaOrg.strcellname;
                antPara.strcellnameen = apa.antParaOrg.strcellnameen;
                antPara.ienodebid = apa.antParaOrg.ienodebid;
                antPara.isectorid = apa.antParaOrg.isectorid;
                antPara.strWeightValue = apa.antParaOrg.strWeightValue;
                antPara.strcgi = apa.antParaOrg.strcgi;
                antPara.strtype = apa.antParaOrg.strtype;
                antPara.strbandtype = apa.antParaOrg.strbandtype;
                antPara.strbeamwidth = apa.antParaOrg.strbeamwidth;
                antPara.isSmartAnt = apa.antParaOrg.isSmartAnt;

                antPara.iStatus = apa.antParaOrg.iStatus;
                antPara.dDownward = Convert.ToInt32(txtBoxOverlay.Text.ToString());
                #endregion
            }
        }
        
        /// <summary>
        /// 指定小区数据赋值
        /// </summary>
        private void cellPart_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComboBox cbBox = (ComboBox)sender;
            foreach (LteCellAntSimulator lteCell in lteCellParaDic.Keys)
            {
                if (cbBox.Text == lteCell.lteCell.Name)
                {
                    curLteCell = lteCell;
                }

                PictureBox picBox = lteCellParaDic[lteCell].PicBox;
                if (lteCell.lteCell.Name == cbBox.Text)
                {
                    setPicClicked(picBox, null);
                }
                else
                {
                    picBox.BackgroundImageLayout = ImageLayout.Zoom;
                }
            }
            changeCellClear();
            fillPortValue();
        }

        /// <summary>
        /// 下拉搜索
        /// </summary>
        private void cellPart_TextUpdate(object sender, EventArgs e)
        {
            ComboBox cbBox = (ComboBox)sender;
            List<string> listNew = new List<string>();
            cbBox.Items.Clear();
            foreach (var cell in lteCellParaDic.Keys)
            {
                if (cell.lteCell.Name.Contains(cbBox.Text))
                {
                    listNew.Add(cell.lteCell.Name);
                }
            }
            cbBox.Items.AddRange(listNew.ToArray());
            cbBox.SelectionStart = cbBox.Text.Length;
            Cursor = Cursors.Default;
            cbBox.DroppedDown = true;
        }

        /// <summary>
        /// 选择权值类型
        /// </summary>
        private void paraTypeCb_SelectedIndexChanged(object sender, EventArgs e)
        {
            string strParaType = paraTypeCb.Text;
            switch (strParaType)
            {
                case "原始值":
                    ePicType = EPictrueType.Default;
                    checkBoxStatubyAssess(false);
                    break;
                case "评估值":
                    ePicType = EPictrueType.Assess;
                    checkBoxStatubyAssess(true);
                    break;
                case "最优值":
                    ePicType = EPictrueType.Optimal;
                    break;
            }
            setTextBoxEnable();

            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(curLteCell, out apa))
            {
                apa.PicBox.BackgroundImage.Dispose();
                apa.PicBox.BackgroundImage = Image.FromFile(PicFactory.GetPicFilePath(curLteCell.lteCell.Name, ePicType));
            }
            fillPortValue();
            int canMoveIdx = 1;
            if (ePicType == EPictrueType.Default || sender.GetType() == typeof(Button))
            {
                canMoveIdx = setPicturePanel(canMoveIdx);
            }
            btnSetting.Visible = ePicType != EPictrueType.Default;
            if (canMoveIdx == 8)
                btnSetting.Visible = false;
        }

        private int setPicturePanel(int canMoveIdx)
        {
            for (int idx = 1; idx <= 8; idx++)
            {
                if ((PortStatuDic[idx].isSetting || ePicType == EPictrueType.Default) && PortStatuDic[idx].isCanMove)
                {
                    canMoveIdx++;
                    PictureBox picBoxPort = (PictureBox)(antParaControl.Controls.Find("picBoxP" + idx, true)[0]);
                    picBoxPort.Image = Image.FromFile(PicFactory.config_PortPic_True);
                    picBoxPort.SizeMode = PictureBoxSizeMode.Zoom;

                    Panel panel = new Panel();
                    for (int i = 1; i <= 8; i++)
                    {
                        panel = (Panel)(antParaControl.Controls.Find("PanelPort" + i, true)[0]);
                        if (panel.AllowDrop)
                            break;
                    }
                    panel.BackgroundImage = Image.FromFile(PicFactory.setPortNum(idx));
                    panel.BackgroundImageLayout = ImageLayout.Zoom;
                    panel.DragDrop -= panel_DragDrop;
                    panel.DragEnter -= panel_DragEnter;
                    panel.AllowDrop = false;
                    if (ePicType == EPictrueType.Default)
                    {
                        panel.Click -= panel_Clear_Click;
                    }
                    PortStatuDic[idx].SettingMoveStatu(panel, true);
                }
            }

            return canMoveIdx;
        }

        private void setTextBoxEnable()
        {
            for (int idx = 1; idx <= 8; idx++)
            {
                Panel panel = (Panel)(antParaControl.Controls.Find("PanelPort" + idx, true)[0]);
                panel.Click -= panel_Clear_Click;
                panel.Click += panel_Clear_Click;
                TextBox tbRange = (TextBox)(antParaControl.Controls.Find("tbRange" + idx, true)[0]);
                TextBox tbPhase = (TextBox)(antParaControl.Controls.Find("tbPhase" + idx, true)[0]);

                panel_Clear_Click(panel, null);

                if (ePicType == EPictrueType.Default)
                {
                    tbRange.Enabled = false;
                    tbPhase.Enabled = false;
                }
                else
                {
                    tbRange.Enabled = true;
                    tbPhase.Enabled = true;
                }
            }
        }

        private void checkBoxStatubyAssess(bool bEnable)
        {
            chkOverlay.Checked = false;
            chkSave.Checked = false;
            txtBoxOverlay.Enabled = bEnable;
            chkSave.Enabled = bEnable;
        }

        /// <summary>
        /// 分析指标类型
        /// </summary>
        private void ccbKPIDraw_SelectedIndexChanged(object sender, EventArgs e)
        {
            string strKPIType = ccbKPIDraw.Text;
            switch (strKPIType)
            {
                case "最大RSRP":
                    eKPIType = EAntKPIType.RsrpMax;
                    btnSetColor.Visible = false;
                    break;
                case "平均RSRP":
                    eKPIType = EAntKPIType.RsrpAvg;
                    btnSetColor.Visible = false;
                    break;
                case "SINR":
                    eKPIType = EAntKPIType.Sinr;
                    btnSetColor.Visible = false;
                    break;
                case "SINR(仅Mod3)":
                    eKPIType = EAntKPIType.Sinr3;
                    btnSetColor.Visible = false;
                    break;
                case "重叠覆盖度":
                    eKPIType = EAntKPIType.Overlap;
                    btnSetColor.Visible = true;
                    break;
            }
        }
        
        /// <summary>
        /// 区域范围
        /// </summary>
        private void ccbArea_SelectedIndexChanged(object sender, EventArgs e)
        {
            string strAreaType = ccbArea.Text;
            switch (strAreaType)
            {
                case "区域内":
                    eAreaType = EAntAreaType.AreaInside;
                    break;
                case "全部":
                    eAreaType = EAntAreaType.All;
                    break;
            }
        }

        /// <summary>
        /// 频点选择
        /// </summary>
        private void ccbBandType_SelectedIndexChanged(object sender, EventArgs e)
        {
            #region 
            string strBandType = ccbBandType.Text;
            switch (strBandType)
            {
                case "全部":
                    eBandType = LteAntSimulatorFullBandType.All;
                    break;
                case "D频段":
                    eBandType = LteAntSimulatorFullBandType.SingleD;
                    break;
                case "D1频段":
                    eBandType = LteAntSimulatorFullBandType.SingleD1;
                    break;
                case "D2频段":
                    eBandType = LteAntSimulatorFullBandType.SingleD2;
                    break;
                case "D3频段":
                    eBandType = LteAntSimulatorFullBandType.SingleD3;
                    break;
                case "F频段":
                    eBandType = LteAntSimulatorFullBandType.SingleF;
                    break;
                case "F1频段":
                    eBandType = LteAntSimulatorFullBandType.SingleF1;
                    break;
                case "F2频段":
                    eBandType = LteAntSimulatorFullBandType.SingleF2;
                    break;
                case "F3频段":
                    eBandType = LteAntSimulatorFullBandType.SingleF3;
                    break;
            }
            hideCellByBand();
            #endregion
        }
        
        private void hideCellByBand()
        {
            PanelCells.Controls.Clear();
            int idx = 0;
            foreach (LteCellAntSimulator lteCell in lteCellParaDic.Keys)
            {
                if ((eBandType == LteAntSimulatorFullBandType.All)
                    || (eBandType == LteAntSimulatorFullBandType.SingleD && LTECell.GetBandTypeByEarfcn(lteCell.lteCell.EARFCN) == LTEBandType.D)
                    || (eBandType == LteAntSimulatorFullBandType.SingleD1 && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.D1)
                    || (eBandType == LteAntSimulatorFullBandType.SingleD2 && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.D2)
                    || (eBandType == LteAntSimulatorFullBandType.SingleD3 && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.D3)
                    || (eBandType == LteAntSimulatorFullBandType.SingleF && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.F)
                    )
                {
                    lteCellParaDic[lteCell].PicBox.Location = new Point(idx * 120 + 1, 0);
                    PanelCells.Controls.Add(lteCellParaDic[lteCell].PicBox);
                    idx++;
                }
            }
        }

        Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo> GridKpi = null;
        Dictionary<GridLongLat, Color> GridColor = null;
        public static LteAntSimulatorRange Ranges { get; set; } = new LteAntSimulatorRange();

        /// <summary>
        /// 栅格GIS渲染
        /// </summary>
        private void btnShowGrid_Click(object sender, EventArgs e)
        {
            GridKpi = new Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo>();
            GridColor = new Dictionary<GridLongLat, Color>();

            WaitBox.Show("分析各小区指标...", calcGridKpi);
            getGridColor();
            ZTLteAntSimulatorLayer glayer = mapForm.GetLayerBase(typeof(ZTLteAntSimulatorLayer)) as ZTLteAntSimulatorLayer;
            if (glayer != null)
            {
                glayer.GridColor = GridColor;
                glayer.Invalidate();
            }
            MainModel.FireDTDataChanged(MainModel.MainForm);
            foreach (LteCellAntSimulator lteCell in lteCellParaDic.Keys)
            {
                MainModel.SelectedLTECells.Add(lteCell.lteCell);
            }
        }

        private void calcGridKpi()
        {
            WaitBox.ProgressPercent = 40;
            try
            {
                ZTAntPropagationModel ztAntPropagationModel = new ZTAntPropagationModel();
                Dictionary<LTECell, ZTLteAntSimulator.CellModelInfo> modelArrayDic = new Dictionary<LTECell, ZTLteAntSimulator.CellModelInfo>();
                foreach (LteCellAntSimulator lteCell in lteCellParaDic.Keys)
                {
                    if ((eBandType == LteAntSimulatorFullBandType.All)
                        || (eBandType == LteAntSimulatorFullBandType.SingleD && LTECell.GetBandTypeByEarfcn(lteCell.lteCell.EARFCN) == LTEBandType.D)
                        || (eBandType == LteAntSimulatorFullBandType.SingleD1 && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.D1)
                        || (eBandType == LteAntSimulatorFullBandType.SingleD2 && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.D2)
                        || (eBandType == LteAntSimulatorFullBandType.SingleD3 && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.D3)
                        || (eBandType == LteAntSimulatorFullBandType.SingleF && LTECell.GetBandTypeByJT(lteCell.lteCell.EARFCN) == LTEBandTypeJT.F)
                    )
                    {
                        addModelArrayDic(modelArrayDic, lteCell);
                    }
                }
                ztAntPropagationModel.calcGridRsrp(modelArrayDic, ref GridKpi);
            }
            catch
            {
                //continue
            }
            Thread.Sleep(1000);
            WaitBox.Close();
        }

        private void addModelArrayDic(Dictionary<LTECell, ZTLteAntSimulator.CellModelInfo> modelArrayDic, LteCellAntSimulator lteCell)
        {
            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(lteCell, out apa))
            {
                ZTLteAntSimulator.CellModelInfo modelInfo = new ZTLteAntSimulator.CellModelInfo();
                AntennaPara antPara = new AntennaPara();
                if (ePicType == EPictrueType.Default)
                {
                    antPara = apa.antParaOrg;
                }
                else if (ePicType == EPictrueType.Assess)
                {
                    checkAssessValue(ref apa);
                    antPara = apa.antParaNew;
                }

                List<double[]> modelList = apa.cGroupList.Count == 0 ? CalcAntDetailValueList(antPara) : CalcAntDetailValueList(antPara, apa.cGroupList);
                switch (eModelType)
                {
                    case EModelType.Port1:
                        modelInfo.powerArray = modelList[0];
                        break;
                    case EModelType.Port2:
                        modelInfo.powerArray = modelList[1];
                        break;
                    case EModelType.Max:
                        modelInfo.powerArray = modelList[2];
                        break;
                }
                modelInfo.dDownward = antPara.dDownward == 9999 ? lteCell.lteCell.Antennas[0].Downward : antPara.dDownward;
                modelArrayDic.Add(lteCell.lteCell, modelInfo);

            }
        }

        private void calcGridKpi(object ObjLteCell, List<double[]> modelArray)
        {
            LteCellAntSimulator lteCell = ObjLteCell as LteCellAntSimulator;
            WaitBox.ProgressPercent = 40;
            ZTAntPropagationModel ztAntPropagationModel = new ZTAntPropagationModel();
            Dictionary<LTECell, ZTLteAntSimulator.CellModelInfo> modelArrayDic = new Dictionary<LTECell, ZTLteAntSimulator.CellModelInfo>();
            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(lteCell, out apa))
            {
                ZTLteAntSimulator.CellModelInfo modelInfo = new ZTLteAntSimulator.CellModelInfo();
                AntennaPara antPara = new AntennaPara();
                if (ePicType == EPictrueType.Default)
                {
                    antPara = apa.antParaOrg;
                }
                else if (ePicType == EPictrueType.Assess)
                {
                    antPara = apa.antParaNew;
                }

                switch (eModelType)
                {
                    case EModelType.Port1:
                        modelInfo.powerArray = modelArray[0];
                        break;
                    case EModelType.Port2:
                        modelInfo.powerArray = modelArray[1];
                        break;
                    case EModelType.Max:
                        modelInfo.powerArray = modelArray[2];
                        break;
                }
                modelInfo.dDownward = antPara.dDownward == 9999 ? lteCell.lteCell.Antennas[0].Downward : antPara.dDownward;
                modelArrayDic.Add(lteCell.lteCell, modelInfo);
            }
            ztAntPropagationModel.calcGridRsrp(modelArrayDic, ref GridKpi);
            WaitBox.Close();
        }

        private void checkAssessValue(ref ZTLteAntSimulator.AntParaArray apa)
        {
            apa.antParaNew.drangeport1 = getValidport(apa.antParaNew.drangeport1, apa.antParaOrg.drangeport1);
            apa.antParaNew.drangeport2 = getValidport(apa.antParaNew.drangeport2, apa.antParaOrg.drangeport2);
            apa.antParaNew.drangeport3 = getValidport(apa.antParaNew.drangeport3, apa.antParaOrg.drangeport3);
            apa.antParaNew.drangeport4 = getValidport(apa.antParaNew.drangeport4, apa.antParaOrg.drangeport4);
            apa.antParaNew.drangeport5 = getValidport(apa.antParaNew.drangeport5, apa.antParaOrg.drangeport5);
            apa.antParaNew.drangeport6 = getValidport(apa.antParaNew.drangeport6, apa.antParaOrg.drangeport6);
            apa.antParaNew.drangeport7 = getValidport(apa.antParaNew.drangeport7, apa.antParaOrg.drangeport7);
            apa.antParaNew.drangeport8 = getValidport(apa.antParaNew.drangeport8, apa.antParaOrg.drangeport8);

            apa.antParaNew.dphaseport1 = getValidport(apa.antParaNew.dphaseport1, apa.antParaOrg.dphaseport1);
            apa.antParaNew.dphaseport2 = getValidport(apa.antParaNew.dphaseport2, apa.antParaOrg.dphaseport2);
            apa.antParaNew.dphaseport3 = getValidport(apa.antParaNew.dphaseport3, apa.antParaOrg.dphaseport3);
            apa.antParaNew.dphaseport4 = getValidport(apa.antParaNew.dphaseport4, apa.antParaOrg.dphaseport4);
            apa.antParaNew.dphaseport5 = getValidport(apa.antParaNew.dphaseport5, apa.antParaOrg.dphaseport5);
            apa.antParaNew.dphaseport6 = getValidport(apa.antParaNew.dphaseport6, apa.antParaOrg.dphaseport6);
            apa.antParaNew.dphaseport7 = getValidport(apa.antParaNew.dphaseport7, apa.antParaOrg.dphaseport7);
            apa.antParaNew.dphaseport8 = getValidport(apa.antParaNew.dphaseport8, apa.antParaOrg.dphaseport8);

            apa.antParaNew.strcity = apa.antParaOrg.strcity;
            apa.antParaNew.strdevvender = apa.antParaOrg.strdevvender;
            apa.antParaNew.strcellname = apa.antParaOrg.strcellname;
            apa.antParaNew.strcellnameen = apa.antParaOrg.strcellnameen;
            apa.antParaNew.ienodebid = apa.antParaOrg.ienodebid;
            apa.antParaNew.isectorid = apa.antParaOrg.isectorid;
            apa.antParaNew.strWeightValue = apa.antParaOrg.strWeightValue;
            apa.antParaNew.strcgi = apa.antParaOrg.strcgi;
            apa.antParaNew.strtype = apa.antParaOrg.strtype;
            apa.antParaNew.strbandtype = apa.antParaOrg.strbandtype;
            apa.antParaNew.strbeamwidth = apa.antParaOrg.strbeamwidth;
            apa.antParaNew.isSmartAnt = apa.antParaOrg.isSmartAnt;
            apa.antParaNew.dDownward = apa.antParaOrg.dDownward;

            apa.antParaNew.iStatus = apa.antParaOrg.iStatus;
        }

        private float getValidport(float antParaNew, float antParaOrg)
        {
            if (antParaNew == 9999)
            {
                return antParaOrg;
            }
            else
            {
                return antParaNew;
            }
        }

        private void getGridColor()
        {
            WaitBox.ProgressPercent = 80;
            if (eKPIType == EAntKPIType.Overlap)
            {
                GetRectangleColor();
                return;
            }
            string kpiType = "RSRP";
            foreach (GridLongLat gridLongLat in GridKpi.Keys)
            {
                if (eAreaType != EAntAreaType.AreaInside || isContainPoint(gridLongLat))
                {
                    kpiType = getGridColor(kpiType, gridLongLat);
                }
            }
        }

        private string getGridColor(string kpiType, GridLongLat gridLongLat)
        {
            Color color = Color.Empty;
            double dKPI = 9999;
            switch (eKPIType)
            {
                case EAntKPIType.RsrpMax:
                    dKPI = GridKpi[gridLongLat].getMaxRsrp();
                    kpiType = "RSRP";
                    break;
                case EAntKPIType.RsrpAvg:
                    dKPI = GridKpi[gridLongLat].getAvgRsrp();
                    kpiType = "RSRP";
                    break;
                case EAntKPIType.Sinr:
                    dKPI = GridKpi[gridLongLat].getSinr();
                    kpiType = "SINR";
                    break;
                case EAntKPIType.Sinr3:
                    dKPI = GridKpi[gridLongLat].getMod3Sinr();
                    kpiType = "SINR";
                    break;
            }
            DTDisplayParameterInfo colorSection = DTDisplayParameterManager.GetInstance()["LTE", kpiType];
            foreach (DTParameterRangeColor item in colorSection.RangeColors)
            {
                if ((item.MinIncluded ? dKPI >= item.Min : dKPI > item.Min)
                           && (item.MaxIncluded ? dKPI <= item.Max : dKPI < item.Max))
                {
                    color = item.Value;
                    break;

                }
            }
            if (color == Color.Empty)
            {
                color = Color.Orange;
            }
            if (!GridColor.ContainsKey(gridLongLat))
                GridColor[gridLongLat] = Color.Empty;
            GridColor[gridLongLat] = color;
            return kpiType;
        }

        private void GetRectangleColor()
        {
            foreach (GridLongLat gridLongLat in GridKpi.Keys)
            {
                if (eAreaType != EAntAreaType.AreaInside || isContainPoint(gridLongLat))
                {
                    Color color;
                    int iCoverage = GridKpi[gridLongLat].getOverlapCoverage();
                    color = Ranges.GetColor(iCoverage);
                    if (!GridColor.ContainsKey(gridLongLat))
                        GridColor[gridLongLat] = Color.Empty;
                    GridColor[gridLongLat] = color;
                }
            }
        }

        /// <summary>
        /// 重叠覆盖度 设置窗体
        /// </summary>
        private void SetRectangleColor(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(Ranges.Minimum, Ranges.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(Ranges.ColorRanges);
            dlg.InvalidatePointColor = Ranges.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }

            Ranges.ColorRanges = new List<MasterCom.MControls.ColorRange>(dlg.ColorRanges);
            Ranges.InvalidColor = dlg.InvalidatePointColor;
        }

        /// <summary>
        /// 覆盖波形图表模拟
        /// </summary>
        private void btnShowChart_Click(object sender, EventArgs e)
        {
            List<ControlGroup> cGroupList = getGroupList();
            if(cGroupList.Count<8)
            {
                MessageBox.Show("端口未绑定完全！");
                return;
            }

            ZTLteAntSimulator.AntParaArray apa;
            if (lteCellParaDic.TryGetValue(curLteCell, out apa))
            {
                AntennaPara antPara = new AntennaPara();
                if (ePicType == EPictrueType.Default)
                {
                    antPara = apa.antParaOrg;
                }
                else if (ePicType == EPictrueType.Assess)
                {
                    getPortValue(cGroupList);
                    antPara = apa.antParaNew;
                    apa.fillControlData(cGroupList);
                }
                
                List<double[]> modelArray = apa.cGroupList.Count == 0 ? CalcAntDetailValueList(antPara) : CalcAntDetailValueList(antPara, apa.cGroupList);
                drawAntRadarSeries(modelArray, antPara);
                lteCellParaDic[curLteCell].PicBox.BackgroundImage.Dispose();
                lteCellParaDic[curLteCell].PicBox.BackgroundImage = Image.FromFile(SavePrintScreen(curLteCell.lteCell.Name, ePicType));

                showGMaxInfo(antPara, modelArray);
                //栅格着色
                GridKpi = new Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo>();
                GridColor = new Dictionary<GridLongLat, Color>();

                calcGridKpi(curLteCell, modelArray);
                getGridColor();
                ZTLteAntSimulatorLayer glayer = mapForm.GetLayerBase(typeof(ZTLteAntSimulatorLayer)) as ZTLteAntSimulatorLayer;
                if (glayer != null)
                {
                    glayer.GridColor = GridColor;
                    glayer.Invalidate();
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                GisShow(modelArray);
                if(chkSave.Checked)
                {
                    WaitBox.Show("保存评估值...", saveAssessData, new List<AntennaPara> { antPara });
                    MessageBox.Show("保存成功");
                }
            }
        }

        private void showGMaxInfo(AntennaPara antPara, List<double[]> modelMaxArrayList)
        {
            MasterCom.RAMS.ZTFunc.ZTLteAntenna.CellInfoItem cellInfo = new MasterCom.RAMS.ZTFunc.ZTLteAntenna.CellInfoItem();
            double[] model1Array = modelMaxArrayList[0];
            double[] model2Array = modelMaxArrayList[1];
            double[] modelMaxArray = modelMaxArrayList[2];
            if (!chkOverlay.Checked)
            {
                cellInfo.modelMaxArray = model1Array;
                double[] objectValue1 = cellInfo.calcCellPara();
                cellInfo.modelMaxArray = model2Array;
                double[] objectValue2 = cellInfo.calcCellPara();

                antPara.GMax = Convert.ToSingle(objectValue1[0]);
                antPara._3dbValue = Convert.ToSingle(objectValue1[1]);
                antPara._6dbValue = Convert.ToSingle(objectValue1[2]);
                string strInfo = string.Format("PORT1\r\nGMax：{0}\r\n3dbValue：{1}\r\n6dbValue：{2}\r\n", Math.Round(objectValue1[0], 3), objectValue1[1], objectValue1[2]);
                strInfo += string.Format("PORT2\r\nGMax：{0}\r\n3dbValue：{1}\r\n6dbValue：{2}", Math.Round(objectValue2[0], 3), objectValue2[1], objectValue2[2]);
                txtBoxGMax.Text = strInfo;
            }
            else
            {
                cellInfo.modelMaxArray = modelMaxArray;
                double[] objectValue = cellInfo.calcCellPara();
                antPara.GMax = Convert.ToSingle(objectValue[0]);
                antPara._3dbValue = Convert.ToSingle(objectValue[1]);
                antPara._6dbValue = Convert.ToSingle(objectValue[2]);
                string strInfo = string.Format("GMax：{0}\r\n3dbValue：{1}\r\n6dbValue：{2}", Math.Round(antPara.GMax, 3), antPara._3dbValue, antPara._6dbValue);
                txtBoxGMax.Text = strInfo;
            }
        }

        private void saveAssessData(object objList)
        {
            WaitBox.Text = "保存评估值...";
            WaitBox.ProgressPercent = 50;
            List<AntennaPara> antAssessList = objList as List<AntennaPara>;
            DiyAntSimulatorAssess diyInsert = new DiyAntSimulatorAssess(MainModel);
            diyInsert.SetCondition(antAssessList);
            diyInsert.Query();
            WaitBox.Close();
        }

        #region 权值计算
        private List<double[]> CalcAntDetailValueList(AntennaPara antPara)
        {
            List<double[]> listArray = new List<double[]>();
            AntParaItem ant1Item = new AntParaItem(antPara.strbandtype, antPara.drangeport1, antPara.drangeport2, antPara.drangeport3, antPara.drangeport4, antPara.strdevvender);
            ant1Item.Init(antPara.dphaseport1, antPara.dphaseport2, antPara.dphaseport3, antPara.dphaseport4);
            AntParaItem ant2Item = new AntParaItem(antPara.strbandtype, antPara.drangeport5, antPara.drangeport6, antPara.drangeport7, antPara.drangeport8, antPara.strdevvender);
            ant2Item.Init(antPara.dphaseport5, antPara.dphaseport6, antPara.dphaseport7, antPara.dphaseport8);
            double[] model1Array = ant1Item.getPowerArray();
            double[] model2Array = ant2Item.getPowerArray();
            double[] modelMaxArray = AntParaItem.getMaxPowerArray(model1Array, model2Array);
            listArray.Add(model1Array);
            listArray.Add(model2Array);
            listArray.Add(modelMaxArray);
            return listArray;
        }

        private List<double[]> CalcAntDetailValueList(AntennaPara antPara, List<MasterCom.RAMS.ZTFunc.ZTLteAntSimulator.ControlGroupSingle> cGroupList)
        {
            AntParaItem ant1Plus =new AntParaItem();
            ant1Plus.strBandType = antPara.strbandtype;
            ant1Plus.strVendor = antPara.strdevvender;
            AntParaItem ant1Minus = new AntParaItem();
            ant1Minus.strBandType = antPara.strbandtype;
            ant1Minus.strVendor = antPara.strdevvender;

            AntParaItem ant2Plus = new AntParaItem();
            ant2Plus.strBandType = antPara.strbandtype;
            ant2Plus.strVendor = antPara.strdevvender;
            AntParaItem ant2Minus = new AntParaItem();
            ant2Minus.strBandType = antPara.strbandtype;
            ant2Minus.strVendor = antPara.strdevvender;

            bool isPositive = true;
            for (int i = 0; i < 8; i++)
            {
                setMinusPlus(cGroupList, ant1Plus, ant1Minus, ant2Plus, ant2Minus, i);
                if (isPositive)
                {
                    if (i < 4)
                        isPositive = (cGroupList[i].iSourcePortNum <= 4);
                    if (i >= 4)
                        isPositive = (cGroupList[i].iSourcePortNum >= 4);
                }
            }
            if (isPositive || ePicType == EPictrueType.Default)
                return CalcAntDetailValueList(antPara);

            double[] model1PlusArray = ant1Plus.getPowerArray();
            double[] model1MinusArray = ant1Minus.getPowerArray();
            double[] model1Array = AntParaItem.getSumPowerArray(model1PlusArray, model1MinusArray);

            double[] model2PlusArray = ant2Plus.getPowerArray();
            double[] model2MinusArray = ant2Minus.getPowerArray();
            double[] model2Array = AntParaItem.getSumPowerArray(model2PlusArray, model2MinusArray);

            double[] modelMaxArray = AntParaItem.getMaxPowerArray(model1Array, model2Array);
            List<double[]> listArray = new List<double[]>();
            listArray.Add(model1Array);
            listArray.Add(model2Array);
            listArray.Add(modelMaxArray);
            return listArray;
        }

        private void setMinusPlus(List<ZTLteAntSimulator.ControlGroupSingle> cGroupList, AntParaItem ant1Plus, AntParaItem ant1Minus, AntParaItem ant2Plus, AntParaItem ant2Minus, int i)
        {
            switch (i)
            {
                //正极化
                case 0:
                    ant1Plus.w1 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant1Plus.delta1 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    ant1Minus.w1 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant1Minus.delta1 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    break;
                case 1:
                    ant1Plus.w2 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant1Plus.delta2 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    ant1Minus.w2 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant1Minus.delta2 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    break;
                case 2:
                    ant1Plus.w3 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant1Plus.delta3 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    ant1Minus.w3 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant1Minus.delta3 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    break;
                case 3:
                    ant1Plus.w4 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant1Plus.delta4 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    ant1Minus.w4 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant1Minus.delta4 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    break;
                //负极化
                case 4:
                    ant2Plus.w1 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant2Plus.delta1 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    ant2Minus.w1 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant2Minus.delta1 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    break;
                case 5:
                    ant2Plus.w2 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant2Plus.delta2 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    ant2Minus.w2 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant2Minus.delta2 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    break;
                case 6:
                    ant2Plus.w3 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant2Plus.delta3 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    ant2Minus.w3 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant2Minus.delta3 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    break;
                case 7:
                    ant2Plus.w4 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fRange);
                    ant2Plus.delta4 = getValidValue(cGroupList[i].iSourcePortNum <= 4, cGroupList[i].fPhase);
                    ant2Minus.w4 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fRange);
                    ant2Minus.delta4 = getValidValue(cGroupList[i].iSourcePortNum > 4, cGroupList[i].fPhase);
                    break;
            }
        }

        private double getValidValue(bool judge, float def)
        {
            if (judge)
            {
                return 0;
            }
            return def;
        }

        #endregion

        /// <summary>
        /// 按平滑线绘雷达图
        /// </summary>
        private void drawAntRadarSeries(List<double[]> modelMaxArrayList, AntennaPara antPara)
        {
            try
            {
                chartCellLine.Series.Clear();
                int iNum = 0;
                double[] model1Array = modelMaxArrayList[0];
                double[] model2Array = modelMaxArrayList[1];
                double[] modelMaxArray = modelMaxArrayList[2];
                int iMaxValue = -19;
                int iMinValue = 50;

                #region 权值模型数据
                if (!chkOverlay.Checked)
                {
                    if (model1Array != null && model1Array.Length == 180)
                    {
                        setSeries(antPara, iNum, model1Array, "POST 1", Color.Red, ref iMaxValue, ref iMinValue);
                        iNum++;
                    }
                    if (model2Array != null && model2Array.Length == 180)
                    {
                        setSeries(antPara, iNum, model2Array, "POST 2", Color.Blue, ref iMaxValue, ref iMinValue);
                    }
                }
                else
                {
                    if (modelMaxArray != null && modelMaxArray.Length == 180)
                    {
                        setSeries(antPara, iNum, modelMaxArray, "波形叠加", Color.Red, ref iMaxValue, ref iMinValue);
                        iMinValue = -20;
                    }
                }
                #endregion

                ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MinValue = iMinValue;
                ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MaxValue = iMaxValue;

                ((RadarDiagram)chartCellLine.Diagram).AxisX.GridSpacing = 20;

                ((RadarDiagram)chartCellLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartCellLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
                chartCellLine.Focus();
            }
            catch
            {
                MessageBox.Show("模拟数据有误，出现的可能原因：\r\n没有匹配该小区的频点。");
            }
        }

        private void setSeries(AntennaPara antPara, int iNum, double[] modelArray, string text, Color color,ref int iMaxValue, ref int iMinValue)
        {
            Series seriesModel = new Series();
            seriesModel.PointOptions.PointView = PointView.Values;
            seriesModel.ShowInLegend = true;
            seriesModel.LegendText = text;

            RadarLineSeriesView lineSeriesView2 = new RadarLineSeriesView();
            lineSeriesView2.Color = color;
            lineSeriesView2.LineMarkerOptions.Size = 2;

            seriesModel.View = lineSeriesView2;
            seriesModel.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesModel.Label.Visible = false;
            if (antPara.iStatus == 1)
            {
                for (int i = 0; i < 360; i++)
                {
                    double tmpValue = -120;
                    int iDir = (i + curLteCell.lteCell.Direction) % 360;
                    if (i >= 270)
                        tmpValue = modelArray[i - 270];
                    else if (i < 90)
                        tmpValue = modelArray[i + 90];
                    seriesModel.Points.Add(new SeriesPoint(iDir.ToString(), Math.Round(tmpValue, 2)));
                }
            }

            chartCellLine.Series.Insert(iNum, seriesModel);
            ZTAntFuncHelper.getMaxAndMinValue(modelArray, ref iMaxValue, ref iMinValue);
        }

        /// <summary>
        /// GIS呈现波形模拟图
        /// </summary>
        private void GisShow(List<double[]> modelMaxArrayList)
        {
            List<LongLat> longLatModel1lList = new List<LongLat>();
            List<LongLat> longLatModel2lList = new List<LongLat>();
            List<LongLat> longLatModelList = new List<LongLat>();
            double[] model1Array = modelMaxArrayList[0];
            double[] model2Array = modelMaxArrayList[1];
            double[] modelMaxArray = modelMaxArrayList[2];
            LongLat ll = new LongLat();
            ll.fLongitude = (float)(curLteCell.lteCell.Longitude);
            ll.fLatitude = (float)(curLteCell.lteCell.Latitude);
            int iMaxValue = -19;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(modelMaxArrayList[2], ref iMaxValue, ref iMinValue);
         
            if (chkOverlay.Checked)
            {
                iMinValue = -20;
                longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, modelMaxArray, iMaxValue, iMinValue, curLteCell.lteCell.Direction);
            }
            else
            {
                iMaxValue = -19;
                iMinValue = 50;
                ZTAntFuncHelper.getMaxAndMinValue(model1Array, ref iMaxValue, ref iMinValue);
                longLatModel1lList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, model1Array, iMaxValue, iMinValue, curLteCell.lteCell.Direction);

                iMaxValue = -19;
                iMinValue = 50;
                ZTAntFuncHelper.getMaxAndMinValue(model2Array, ref iMaxValue, ref iMinValue);
                longLatModel2lList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, model2Array, iMaxValue, iMinValue, curLteCell.lteCell.Direction);
   
            }
            MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(curLteCell.lteCell.Name);
            MainModel.MainForm.GetMapForm().GoToView(ll.fLongitude, ll.fLatitude);
            AntLineLayer antLayer = mapForm.GetLayerBase(typeof(AntLineLayer)) as AntLineLayer;
            if (antLayer != null)
            {
                antLayer.Invalidate();
                antLayer.scanLongLatList = longLatModel2lList;
                antLayer.modelLongLatList = chkOverlay.Checked ? longLatModelList : longLatModel1lList;
                antLayer.Invalidate();
            }
        }

        /// <summary>
        /// 获取按照1~8端口排序的列表
        /// </summary>
        private List<ControlGroup> getGroupList()
        {
            List<ControlGroup> cGroupList = new List<ControlGroup>();
            foreach (int i in PortStatuDic.Keys)
            {
                if (PortStatuDic[i].isSetting)
                {
                    break;
                }
                cGroupList.Add(PortStatuDic[i]);
            }
            if (cGroupList.Count < 8)
            {
                return new List<ControlGroup>();
            }
            cGroupList.Sort(ControlGroupByPortNum.GetCompareByPortNum());
            return cGroupList;
        }

        #region 显示模拟小区背景图
        private static LteAntSimulatorConfig PicFactory { get; set; }
        private Dictionary<int, ControlGroup> PortStatuDic = new Dictionary<int, ControlGroup>();
        /// <summary>
        /// 端口状态
        /// </summary>
        private void ShowDefaultBackgroundPic(LteCellAntSimulator lteCell, int idx)
        {
            string cellPic = PicFactory.GetPicFilePath(lteCell.lteCell.Name, EPictrueType.Default);
            PictureBox picBox = new PictureBox();
            picBox.Name = "CellPic" + idx;
            picBox.BackgroundImageLayout = ImageLayout.Zoom;
            picBox.Size = new System.Drawing.Size(120, 120);
            picBox.Location = new Point(idx * 120 + 1, 0);
            picBox.MouseClick += setPicClicked;
            picBox.MouseEnter += setPicSlideStatu;
            picBox.MouseLeave += setPicSlideStatu;
            picBox.Tag = idx;
            ToolTip tp = new ToolTip();
            tp.SetToolTip(picBox, lteCell.lteCell.Name);
            picBox.BackgroundImage = Image.FromFile(cellPic);
            PanelCells.Controls.Add(picBox);

            lteCellParaDic[lteCell].PicBox = picBox;
        }

        /// <summary>
        /// 图片选中事件
        /// </summary>
        private void setPicClicked(object sender, EventArgs e)
        {
            PictureBox picBox = (PictureBox)sender;
            foreach (LteCellAntSimulator lteCell in lteCellParaDic.Keys)
            {
                ZTLteAntSimulator.AntParaArray OtherPic = lteCellParaDic[lteCell];
                if (Convert.ToInt32(picBox.Tag) != Convert.ToInt32(OtherPic.PicBox.Tag))
                {
                    OtherPic.PicBox.Image = null;
                }
                else
                {
                    curLteCell = lteCell;
                    cellPart.Text = curLteCell.lteCell.Name;
                    fillPortValue();
                    picBox.Image = null;
                    picBox.Image = Image.FromFile(PicFactory.config_CheckedLayer);
                    picBox.Focus();
                }
            }
            changeCellClear();
        }

        /// <summary>
        /// 图片滑动事件
        /// </summary>
        private void setPicSlideStatu(object sender, EventArgs e)
        {
            PictureBox picBox = (PictureBox)sender;
            if (picBox.Image != null)
            {
                if (Convert.ToInt32(picBox.Tag) != Convert.ToInt32(lteCellParaDic[curLteCell].PicBox.Tag))
                {
                    picBox.Image = null;
                }
            }
            else
            {
                picBox.Image = Image.FromFile(PicFactory.config_ShadeLayer);
                picBox.SizeMode = PictureBoxSizeMode.Zoom;
            }
        }
        #endregion

        #region 拖动事件
        private void panel_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Move | DragDropEffects.Copy;
        }


        private void panel_DragDrop(object sender, DragEventArgs e)
        {
            //改变被拖拽的PicBox的背景
            PictureBox picBoxPort = (PictureBox)(e.Data.GetData(typeof(PictureBox)));
            picBoxPort.Image = Image.FromFile(PicFactory.config_PortPic_True);
            picBoxPort.SizeMode = PictureBoxSizeMode.Zoom;

            //放置到Panel中，改成Panel的背景
            Panel panel = (Panel)sender;
            int idx = Convert.ToInt32(picBoxPort.Tag);
            panel.BackgroundImage = Image.FromFile(PicFactory.setPortNum(idx));
            panel.BackgroundImageLayout = ImageLayout.Zoom;
            panel.DragDrop -= panel_DragDrop;
            panel.DragEnter -= panel_DragEnter;
            panel.AllowDrop = false;
            PortStatuDic[idx].SettingMoveStatu(panel, true);
        }
        private void panel_Clear_Click(object sender, EventArgs e)
        {
            Panel panel = (Panel)sender;
            if (panel.BackgroundImage != null)
            {
                panel.BackgroundImage = null;
                panel.AllowDrop = true;
                panel.DragDrop += panel_DragDrop;
                panel.DragEnter += panel_DragEnter;
                foreach (ControlGroup cGroup in PortStatuDic.Values)
                {
                    if (cGroup.PanelPort == null)
                        continue;
                    if (cGroup.PanelPort.Name == panel.Name)
                    {
                        cGroup.PicBoxPort.Image = Image.FromFile(PicFactory.config_PortPic_False);
                        cGroup.PicBoxPort.SizeMode = PictureBoxSizeMode.Zoom;
                        cGroup.SettingMoveStatu(panel, false);
                    }
                }
                btnSetting.Visible = ePicType != EPictrueType.Default;
            }
        }

        private void changeCellClear()
        {
            if (ePicType == EPictrueType.Default)
                return;
            foreach (ControlGroup cGroup in PortStatuDic.Values)
            {
                if (cGroup.PanelPort == null)
                    continue;
                cGroup.PicBoxPort.Image = Image.FromFile(PicFactory.config_PortPic_False);
                cGroup.PicBoxPort.SizeMode = PictureBoxSizeMode.Zoom;
                cGroup.SettingMoveStatu(null, false);
            }
            for (int i = 1; i <= 8; i++)
            {
                Panel panel = (Panel)(antParaControl.Controls.Find("PanelPort" + i, true)[0]);
                if (panel.BackgroundImage != null)
                {
                    panel.BackgroundImage = null;
                    panel.AllowDrop = true;
                    panel.DragDrop += panel_DragDrop;
                    panel.DragEnter += panel_DragEnter;
                }
            }
        }

        private void picBoxPort_Click(object sender, EventArgs e)
        {
            //改变被点击的PicBox的背景
            PictureBox picBoxPort = (PictureBox)sender;
            int idx = Convert.ToInt32(picBoxPort.Tag);
            if (PortStatuDic[idx].iPortNum != 9999)
                return;
            picBoxPort.Image = Image.FromFile(PicFactory.config_PortPic_True);
            picBoxPort.SizeMode = PictureBoxSizeMode.Zoom;

            Panel panel = new Panel();
            for (int i = 1; i <= 8; i++)
            {
                panel = (Panel)(antParaControl.Controls.Find("PanelPort" + i, true)[0]);
                if (panel.AllowDrop)
                    break;
            }
            panel.BackgroundImage = Image.FromFile(PicFactory.setPortNum(idx));
            panel.BackgroundImageLayout = ImageLayout.Zoom;
            panel.DragDrop -= panel_DragDrop;
            panel.DragEnter -= panel_DragEnter;
            panel.AllowDrop = false;
            PortStatuDic[idx].SettingMoveStatu(panel, true);
        }
        private void Pic_MouseDown(object sender, MouseEventArgs e)
        {
            PictureBox picPort = (PictureBox)sender;
            if (!PortStatuDic[(int)picPort.Tag].isCanMove)
            {
                MessageBox.Show("幅度或相位填写不正确，不能设置端口信息！");
                return;
            }
            if (!PortStatuDic[(int)picPort.Tag].isSetting)
            {
                return;
            }
            picPort.DoDragDrop(picPort, DragDropEffects.Move | DragDropEffects.Copy);
            picBoxPort_Click(sender, null);
        }
        #endregion

        #region 幅度、相位 输入值判断正确性
        /// <summary>
        /// 幅度 输入值判断正确性
        /// </summary>
        private void txtBoxRange_TextUpdate(object sender, EventArgs e)
        {
            TextBox txtBox = (TextBox)sender;
            int idx = Convert.ToInt32(txtBox.Tag);
            float fRange;
            if (!float.TryParse(txtBox.Text, out fRange))
            {
                checkValue(idx, false, "Range");
            }
            else
            {
                if (fRange > 1 || -1 > fRange)
                {
                    checkValue(idx, false, "Range");
                }
                else
                {
                    checkValue(idx, true, "Range");
                }
            }
        }
      
        /// <summary>
        /// 相位 输入值判断正确性
        /// </summary>
        private void txtBoxPhase_TextUpdate(object sender, EventArgs e)
        {
            TextBox txtBox = (TextBox)sender;
            int idx = Convert.ToInt32(txtBox.Tag);
            float fPhase;
            if (!float.TryParse(txtBox.Text, out fPhase))
            {
                checkValue(idx, false, "Phase");
            }
            else
            {
                if (fPhase > 180 || -180 > fPhase)
                {
                    checkValue(idx, false, "Phase");
                }
                else
                {
                    checkValue(idx, true, "Phase");
                }
            }
        }

        private void checkValue(int idx, bool bTrue, string strType)
        {
            PictureBoxSizeMode ImgSize = PictureBoxSizeMode.CenterImage;
            if (strType == "Range")
            {
                #region 幅度
                switch (idx)
                {
                    case 1:
                        setPicInfo(bTrue, ImgSize, picBoxRange1);
                        break;
                    case 2:
                        setPicInfo(bTrue, ImgSize, picBoxRange2);
                        break;
                    case 3:
                        setPicInfo(bTrue, ImgSize, picBoxRange3);
                        break;
                    case 4:
                        setPicInfo(bTrue, ImgSize, picBoxRange4);
                        break;
                    case 5:
                        setPicInfo(bTrue, ImgSize, picBoxRange5);
                        break;
                    case 6:
                        setPicInfo(bTrue, ImgSize, picBoxRange6);
                        break;
                    case 7:
                        setPicInfo(bTrue, ImgSize, picBoxRange7);
                        break;
                    case 8:
                        setPicInfo(bTrue, ImgSize, picBoxRange8);
                        break;
                }
                #endregion
            }
            else if (strType == "Phase")
            {
                #region 相位
                switch (idx)
                {
                    case 1:
                        setPicInfo(bTrue, ImgSize, picBoxPhase1);
                        break;
                    case 2:
                        setPicInfo(bTrue, ImgSize, picBoxPhase2);
                        break;
                    case 3:
                        setPicInfo(bTrue, ImgSize, picBoxPhase3);
                        break;
                    case 4:
                        setPicInfo(bTrue, ImgSize, picBoxPhase4);
                        break;
                    case 5:
                        setPicInfo(bTrue, ImgSize, picBoxPhase5);
                        break;
                    case 6:
                        setPicInfo(bTrue, ImgSize, picBoxPhase6);
                        break;
                    case 7:
                        setPicInfo(bTrue, ImgSize, picBoxPhase7);
                        break;
                    case 8:
                        setPicInfo(bTrue, ImgSize, picBoxPhase8);
                        break;
                }
                #endregion
            }
            if (ePicType != EPictrueType.Default)
                btnSetting.Visible = bTrue;
        }

        private void setPicInfo(bool bTrue, PictureBoxSizeMode ImgSize, PictureBox picBox)
        {
            if (bTrue)
                picBox.Image = Image.FromFile(PicFactory.config_RangePhase_True);
            else
                picBox.Image = Image.FromFile(PicFactory.config_RangePhase_False);
            picBox.SizeMode = ImgSize;
        }
        #endregion

        private void Register()
        {
            cellPart.SelectedIndexChanged += new EventHandler(cellPart_SelectedIndexChanged);
            cellPart.TextUpdate += new EventHandler(cellPart_TextUpdate);
            for (int i = 1; i <= 8; i++)
            {
                TextBox tbRange = (TextBox)(antParaControl.Controls.Find("tbRange" + i, true)[0]);
                tbRange.Tag = i;

                TextBox tbPhase = (TextBox)(antParaControl.Controls.Find("tbPhase" + i, true)[0]);
                tbPhase.Tag = i;

                PictureBox picBoxPort = (PictureBox)(antParaControl.Controls.Find("picBoxP" + i, true)[0]);
                picBoxPort.Image = Image.FromFile(PicFactory.config_PortPic_False);
                picBoxPort.SizeMode =  PictureBoxSizeMode.Zoom;
                picBoxPort.Tag = i;

                ControlGroup cGroup = new ControlGroup(tbRange, tbPhase, picBoxPort);
                PortStatuDic.Add(i, cGroup);

                Panel panel = (Panel)(antParaControl.Controls.Find("PanelPort" + i, true)[0]);
                panel.Tag = i;
            }

            //重叠覆盖度 颜色默认配置
            Ranges.Minimum = 0;
            Ranges.Maximum = 10000;
            Ranges.ColorRanges.Add(new ColorRange(Ranges.Minimum, 1, Color.White, "0 <= x < 1"));
            Ranges.ColorRanges.Add(new ColorRange(1, 2, Color.Blue, "1 <= x < 2"));
            Ranges.ColorRanges.Add(new ColorRange(2, 3, Color.Aqua, "2 <= x < 3"));
            Ranges.ColorRanges.Add(new ColorRange(3, 4, Color.Green, "3 <= x < 4"));
            Ranges.ColorRanges.Add(new ColorRange(4, Ranges.Maximum, Color.Red, "4 <= x < +∞"));
        }

        /// <summary>
        /// 自动保存截图
        /// </summary>
        private string SavePrintScreen(string cellName, EPictrueType eType)
        {
            string strPicPath = string.Format(@"{0}\{1}_{2}.jpg", PicFactory.city_PicRootPath, cellName, EnumDescriptionAttribute.GetText(eType));
            if (File.Exists(strPicPath))
                File.Delete(strPicPath);
            chartCellLine.ExportToImage(strPicPath, System.Drawing.Imaging.ImageFormat.Jpeg);
            return strPicPath;
        }

        #region 指标评估统计
        private void btnKPIStat_Click(object sender, EventArgs e)
        {
            if (lteCellParaDic.Count > 0) 
            {
                EPictrueType userConfig = ePicType;
                doWithKPIStat();
                ePicType = userConfig;
                xtraTabControl.SelectedTabPageIndex = 1;
            }
        }

        private void doWithKPIStat()
        {
            GridKpi = new Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo>();
            Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo> GridResult = new Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo>();
            double[] dRsrpValue = new double[3] { 0, 0, 0 };
            double[] dSinrValue = new double[3] { 0, 0, 0 };

            ePicType = EPictrueType.Default;
            WaitBox.Show("分析各小区指标(原始值)...", calcGridKpi);
            foreach (GridLongLat LongLatKey in GridKpi.Keys)
            {
                GridResult.Add(LongLatKey, GridKpi[LongLatKey]);
            }
            dRsrpValue[1] = calcKPIResult(GridResult, EAntKPIType.RsrpMax);
            dSinrValue[1] = calcKPIResult(GridResult, EAntKPIType.Sinr);
            GridKpi.Clear();
            GridResult.Clear();

            ePicType = EPictrueType.Assess;
            WaitBox.Show("分析各小区指标(评估值)...", calcGridKpi);
            foreach (GridLongLat LongLatKey in GridKpi.Keys)
            {
                GridResult.Add(LongLatKey, GridKpi[LongLatKey]);
            }
            dRsrpValue[2] = calcKPIResult(GridResult, EAntKPIType.RsrpMax);
            dSinrValue[2] = calcKPIResult(GridResult, EAntKPIType.Sinr);
            GridResult.Clear();
            GridKpi.Clear();

            #region RSRP 表格配置
            DataTable dt = new DataTable();
            dt.Columns.Add("指标项");
            dt.Columns.Add("均值");
            addRowData(dRsrpValue, dt);
            gridViewRSRP.Columns.Clear();
            gridControlRSRP.DataSource = dt;
            gridViewRSRP.OptionsBehavior.Editable = false;

            Series seriesRsrp = chartControlRSRP.Series[0];
            seriesRsrp.Points.Clear();
            seriesRsrp.LegendText = "RSRP";
            seriesRsrp.Points.Add(new SeriesPoint("测试值", dRsrpValue[0]));
            seriesRsrp.Points.Add(new SeriesPoint("原始值", dRsrpValue[1]));
            seriesRsrp.Points.Add(new SeriesPoint("评估值", dRsrpValue[2]));

            ((XYDiagram)chartControlRSRP.Diagram).AxisY.Range.MinValue = -140;
            ((XYDiagram)chartControlRSRP.Diagram).AxisY.Range.MaxValue = -40;
            ((XYDiagram)chartControlRSRP.Diagram).AxisY.Reverse = true;     //Y坐标反转
            #endregion

            #region Sinr 表格配置
            dt = new DataTable();
            dt.Columns.Add("指标项");
            dt.Columns.Add("均值");
            addRowData(dSinrValue, dt);

            gridViewSinr.Columns.Clear();
            gridControlSinr.DataSource = dt;
            gridViewSinr.OptionsBehavior.Editable = false;

            Series seriesSinr = chartControlSinr.Series[0];
            seriesSinr.Points.Clear();
            seriesSinr.LegendText = "SINR";
            seriesSinr.Points.Add(new SeriesPoint("测试值", dSinrValue[0]));
            seriesSinr.Points.Add(new SeriesPoint("原始值", dSinrValue[1]));
            seriesSinr.Points.Add(new SeriesPoint("评估值", dSinrValue[2]));
            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MinValue = -50;
            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MaxValue = 50;
            #endregion
        }

        private void addRowData(double[] dValue, DataTable dt)
        {
            for (int idx = 0; idx < dValue.Length; idx++)
            {
                DataRow dr = dt.NewRow();
                if (idx == 0)
                    dr["指标项"] = "测试指标";
                if (idx == 1)
                    dr["指标项"] = "评估指标（原始值）";
                if (idx == 2)
                    dr["指标项"] = "评估指标（评估值）";
                dr["均值"] = dValue[idx];
                dt.Rows.Add(dr);
            }
        }

        private double calcKPIResult(Dictionary<GridLongLat, ZTAntPropagationModel.GridRsrpInfo> GridResult, EAntKPIType eAntKpiType)
        {
            double dSum = 0;
            int idx = 0;
            foreach (GridLongLat LongLatKey in GridResult.Keys)
            {
                if (eAreaType != EAntAreaType.AreaInside || isContainPoint(LongLatKey))
                {
                    switch (eAntKpiType)
                    {
                        case EAntKPIType.RsrpMax:
                            dSum += GridResult[LongLatKey].getMaxRsrp();
                            break;
                        case EAntKPIType.Sinr:
                            dSum += GridResult[LongLatKey].getSinr();
                            break;
                    }
                    idx++;
                }
            }
            return Math.Round(dSum / idx, 2) ;
        }

        /// <summary>
        /// 判断是否在区域内
        /// </summary>
        private bool isContainPoint(GridLongLat LongLat)
        {
            string gridTypeGrid = "";
            LongLat centreLongLat = LongLat.calcCenterLongLat();
            ZTLteAntSimulator.GetInstance().isContainPoint(centreLongLat.fLongitude, centreLongLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid == "")
                return false;
            return true;
        }
        #endregion

        private void cbbModel_SelectedIndexChanged(object sender, EventArgs e)
        {
            string strModelType = ccbModel.Text;
            switch (strModelType)
            {
                case "Max":
                    eModelType = EModelType.Max;
                    break;
                case "Port1":
                    eModelType = EModelType.Port1;
                    break;
                case "Port2":
                    eModelType = EModelType.Port2;
                    break;
            }
        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            getPortValue();
            paraTypeCb_SelectedIndexChanged(sender, null);
        }

        #region 条件枚举
        /// <summary>
        /// 原始值，评估值，最优值
        /// </summary>
        public enum EPictrueType
        {
            [EnumDescriptionAttribute("原始值")]
            Default = 1,

            [EnumDescriptionAttribute("评估值")]
            Assess,

            [EnumDescriptionAttribute("最优值")]
            Optimal
        }

        /// <summary>
        ///渲染指标
        /// </summary>
        public enum EAntKPIType
        {
            [EnumDescriptionAttribute("最大RSRP")]
            RsrpMax = 1,

            [EnumDescriptionAttribute("平均RSRP")]
            RsrpAvg,

            [EnumDescriptionAttribute("SINR")]
            Sinr,

            [EnumDescriptionAttribute("SINR(仅Mod3)")]
            Sinr3,

            [EnumDescriptionAttribute("重叠覆盖度")]
            Overlap
        }

        /// <summary>
        ///渲染区域
        /// </summary>
        public enum EAntAreaType
        {
            [EnumDescriptionAttribute("区域内")]
            AreaInside = 1,

            [EnumDescriptionAttribute("全部")]
            All
        }

        /// <summary>
        ///渲染区域
        /// </summary>
        public enum EModelType
        {
            [EnumDescriptionAttribute("Max")]
            Max = 1,

            [EnumDescriptionAttribute("Port1")]
            Port1,

            [EnumDescriptionAttribute("Port2")]
            Port2
        }
        #endregion

    }

    public class ControlGroup
    {
        public TextBox TbRange { get; set; }
        public float fRange
        {
            get 
            {
                float fvalue;
                if (TbRange != null)
                {
                    if (!float.TryParse(TbRange.Text, out fvalue))
                    {
                        return 9999;
                    }
                }
                else
                    return 9999;
                return fvalue;
            }
        }
        public TextBox TbPhase { get; set; }
        public float fPhase
        {
            get
            {
                float fvalue;
                if (TbPhase != null)
                {
                    if (!float.TryParse(TbPhase.Text, out fvalue))
                    {
                        return 9999;
                    }
                }
                else
                    return 9999;
                return fvalue;
            }
        }
        public PictureBox PicBoxPort { get; set; }
        public bool isCanMove
        {
            get
            {
                if (fRange > 1 || -1 > fRange || fPhase > 180 || -180 > fPhase)
                {
                    return false;
                }
                return true;
            }
        }
        public Panel PanelPort { get; set; }
        public int iPortNum
        {
            get
            {
                if (PanelPort != null)
                {
                    return Convert.ToInt32(PanelPort.Tag);
                }
                else
                    return 9999;
            }
        }
        public int iSourcePortNum
        {
            get
            {
                if (PicBoxPort != null)
                {
                    return Convert.ToInt32(PicBoxPort.Tag);
                }
                else
                    return 9999;
            }

        }
        /// <summary>
        /// 如果已经绑定端口，则不能设置到其他端口
        /// </summary>
        public bool isSetting
        {
            get 
            {
                if (PanelPort != null)
                    return false;
                return true;
            }

        }
        public ControlGroup()
        {
        }
        public ControlGroup(TextBox tbRange, TextBox tbPhase, PictureBox picBoxPort)
        {
            this.TbRange = tbRange;
            this.TbPhase = tbPhase;
            this.PicBoxPort = picBoxPort;
        }
        public void SettingMoveStatu(Panel panel,bool panelStatu)
        {
            if (!panelStatu)
            {
                TbRange.Enabled = true;
                TbPhase.Enabled = true;
                this.PanelPort = null;
            }
            else
            {
                TbRange.Enabled = false;
                TbPhase.Enabled = false;
                this.PanelPort = panel;
            }

        }
    }
    public class LteAntSimulatorConfig
    {
        public LteAntSimulatorConfig()
        { }
        public LteAntSimulatorConfig(string city)
        {
            this.city = city;
        }
        public string city { get; set; } = "广州";

        /// <summary>
        /// 默认图片
        /// </summary>
        public string config_DefaultPic { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "Default_Pic.jpg");
        public string config_Port_Tick_Num { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "port_Tick_1.png");
        public string config_RangePhase_True { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "tick_true.png");
        public string config_RangePhase_False { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "tick_false.png");
        public string config_PortPic_True { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "port_Tick_True.png");
        public string config_PortPic_False { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "port_Tick_False.png");
        public string config_ShadeLayer { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "ShadeLayer.png");
        public string config_CheckedLayer { get; set; } = string.Format(@"{0}\images\天线分析\{1}", Application.StartupPath, "CheckLayer.png");
        public string city_PicRootPath
        {
            get
            {
                string rootPath = string.Format(@"{0}\userData\LTE天线权值模拟器\{1}", Application.StartupPath, city);
                if (!Directory.Exists(rootPath))
                    Directory.CreateDirectory(rootPath);
                return rootPath;
            }
        }

        public string GetPicFilePath(string cellName, LteAntSimulatorForm.EPictrueType eType)
        {
            string rootPath = string.Format(@"{0}\userData\LTE天线权值模拟器\{1}", Application.StartupPath, city);
            if (!Directory.Exists(rootPath))
                Directory.CreateDirectory(rootPath);

            string picPath = string.Format(@"{0}\{1}_{2}.jpg", rootPath, cellName, EnumDescriptionAttribute.GetText(eType));
            if (!File.Exists(picPath))
            {
                picPath = config_DefaultPic;
            }
            return picPath;
        }

        public string setPortNum(int iPicNum)
        {
            string strPicPath = string.Format(@"{0}\images\天线分析\port_Tick_{1}.png", Application.StartupPath, iPicNum);
            if (File.Exists(strPicPath))
                return strPicPath;
            return config_Port_Tick_Num;
        }
    }

    public class LteCellAntSimulator
    {
        public LTECell lteCell { get; set; }
        public LteCellAntSimulator(LTECell lc)
        {
            this.lteCell = lc;
        }
        public LteCellAntSimulator() { }
        public override bool Equals(object obj)
        {
            LteCellAntSimulator other = obj as LteCellAntSimulator;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.lteCell.Name.Equals(other.lteCell.Name));
        }
        public override int GetHashCode()
        {
            return (this.lteCell.Name).GetHashCode();
        }
    }
    /// <summary>
    /// 频段枚举
    /// </summary>
    public enum LteAntSimulatorFullBandType
    {
        [EnumDescriptionAttribute("全部")]
        All,

        [EnumDescriptionAttribute("D频段")]
        SingleD,

        [EnumDescriptionAttribute("D1频段")]
        SingleD1,

        [EnumDescriptionAttribute("D2频段")]
        SingleD2,

        [EnumDescriptionAttribute("D3频段")]
        SingleD3,

        [EnumDescriptionAttribute("F频段")]
        SingleF,

        [EnumDescriptionAttribute("F1频段")]
        SingleF1,

        [EnumDescriptionAttribute("F2频段")]
        SingleF2,

        [EnumDescriptionAttribute("F3频段")]
        SingleF3
      
    }

    public class ControlGroupByPortNum
    {
        //实现排序的接口
        public static IComparer<ControlGroup> GetCompareByPortNum()
        {
            if (comparerByPortNum == null)
            {
                comparerByPortNum = new CompareByPortNum();
            }
            return comparerByPortNum;
        }
        public class CompareByPortNum : IComparer<ControlGroup>
        {
            public int Compare(ControlGroup x, ControlGroup y)
            {
                return x.iPortNum.CompareTo(y.iPortNum);
            }
        }
        private static IComparer<ControlGroup> comparerByPortNum;
    }

    public class LteAntSimulatorRange : LteMgrsColorRange
    {
        public LteAntSimulatorRange()
        {
        }

        public override LteMgrsLegendGroup GetLegend()
        {
            return null;
        }

        public override Color GetColor(float value)
        {
            int idx = GetIndex(value);
            return idx < 0 ? InvalidColor : ColorRanges[idx].color;
        }
    }
}
