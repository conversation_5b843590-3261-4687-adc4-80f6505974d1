﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public class NRRepeatCoverCause : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get { return "重叠覆盖"; }
        }

        public float RSRPDiff { get; set; } = 10;
        public int Second { get; set; } = 5;
        public override string Desc
        {
            get
            {
                return string.Format("在低速率发生{0}秒内，主服小区以及邻区电平值在{1}dB以内", Second, RSRPDiff);
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        public override void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (segItem.IsNeedJudge(pnt))
                {
                    //记录低速率前n秒时间
                    int bTime = pnt.Time - Second;
                    foreach (TestPoint testPoint in allTP)
                    {
                        dealTPInfo(segItem, pnt, bTime, testPoint, nRCond);
                    }
                }
            }
        }

        private void dealTPInfo(NRLowSpeedSeg segItem, TestPoint pnt, int bTime, TestPoint testPoint, NRTpManagerBase nRCond)
        {
            if (bTime <= testPoint.Time && testPoint.Time <= pnt.Time)
            {
                float? rsrp = nRCond.GetSCellRsrp(testPoint);
                for (int i = 0; i < 10; i++)
                {
                    float? nRsrp = nRCond.GetNCellRsrp(testPoint, i);
                    //主服小区以及邻区电平值在n dB以内
                    if (nRsrp != null && rsrp != null
                            && Math.Abs((float)nRsrp - (float)rsrp) <= RSRPDiff)
                    {
                        NRRepeatCoverCause cln = this.Clone() as NRRepeatCoverCause;
                        segItem.SetReason(new NRLowSpeedPointDetail(testPoint, cln, nRCond));
                        break;
                    }
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpDiff"] = this.RSRPDiff;
                paramDic["second"] = this.Second;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPDiff = (float)value["rsrpDiff"];
                this.Second = (int)value["second"];
            }
        }
    }
}