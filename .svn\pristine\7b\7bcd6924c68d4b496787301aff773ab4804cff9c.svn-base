﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    #region 室外站

    public class OutDoorBtsAcceptInfo : BtsAcceptInfoBase
    {
        public OutDoorBtsAcceptInfo(LTEBTS lteBts)
            : base(lteBts)
        {
        }

        //各小区验收信息key:cellid
        public Dictionary<int, OutDoorCellAcceptInfo> CellsAcceptDic { get; set; } = new Dictionary<int, OutDoorCellAcceptInfo>();

        private readonly RateKpiInfo handOverInfo = new RateKpiInfo("系统内切换");
        public RateKpiInfo HandOverInfo { get { return handOverInfo; } }

        public string Is34G_ReselectAccordDes { get; set; }
        public string Is24G_ReselectAccordDes { get; set; }
        public string IsCsfbAccordDes { get; set; }

        public void AddHandOverKpiInfo(Dictionary<uint, object> kpiDic)
        {
            //系统内切换 只取最新一个文件的指标
            if (handOverInfo.TotalCount == null || this.handOverInfo.ValidCount == null)
            {
                foreach (uint key in kpiDic.Keys)
                {
                    KpiKey kpiKey = (KpiKey)key;
                    object objValue = kpiDic[key];

                    switch (kpiKey)
                    {
                        case KpiKey.InHandoverRequestCnt:
                            this.handOverInfo.TotalCount = (int)objValue;
                            break;
                        case KpiKey.InHandoverSucceedCnt:
                            this.handOverInfo.ValidCount = (int)objValue;
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public override void CheckBtsIsAccordAccept()
        {
            StringBuilder strbNotAccordDes = new StringBuilder();
            bool allCellAccord = true;
            foreach (LTECell cell in LteBts.Cells)
            {
                OutDoorCellAcceptInfo cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    cellInfo.CheckCellIsAccordAccept();
                    setBtsKpiInfo(cellInfo);
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                }
                else
                {
                    allCellAccord = false;
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }
            this.handOverInfo.CheckIsAccord(10, 100);
            if (!handOverInfo.IsAccord)
            {
                allCellAccord = false;
                strbNotAccordDes.AppendLine("整站的" + handOverInfo.KpiName + "指标不达标;");
            }
            this.NotAccordKpiDes = strbNotAccordDes.ToString();
            this.IsAccordAccept = allCellAccord;
        }
        protected void setBtsKpiInfo(OutDoorCellAcceptInfo cellInfo)
        {
            this.Is34G_ReselectAccordDes = isKpiAccord(this.Is34G_ReselectAccordDes, cellInfo.Reselect34Info.IsAccord);
            this.Is24G_ReselectAccordDes = isKpiAccord(this.Is24G_ReselectAccordDes, cellInfo.Reselect24Info.IsAccord);
            this.IsCsfbAccordDes = isKpiAccord(this.IsCsfbAccordDes, cellInfo.CsfbInfo.IsAccord);
        }
        private string isKpiAccord(string btsKpiDes, bool cellKpi)
        {
            if (btsKpiDes == null)
            {
                return cellKpi ? "是" : "否";
            }

            if (cellKpi && (btsKpiDes == "是"))
            {
                return "是";
            }
            return "否";
        }
    }
    public class OutDoorCellAcceptInfo : CellAcceptInfoBase, IComparable<OutDoorCellAcceptInfo>
    {
        public OutDoorCellAcceptInfo(LTECell cell)
            : base(cell)
        {
        }

        private readonly RateKpiInfo reselect34Info = new RateKpiInfo("3/4G互操作（重选）");
        public RateKpiInfo Reselect34Info { get { return reselect34Info; } }

        private readonly RateKpiInfo reselect24Info = new RateKpiInfo("2/4G互操作（重选）");
        public RateKpiInfo Reselect24Info { get { return reselect24Info; } }

        private readonly FtpKpiInfo ftpDlInfo = new FtpKpiInfo("FTP好点下行指标");
        public FtpKpiInfo FtpDlInfo { get { return ftpDlInfo; } }

        private readonly FtpKpiInfo ftpUlInfo = new FtpKpiInfo("FTP好点上行指标");
        public FtpKpiInfo FtpUlInfo { get { return ftpUlInfo; } }

        public void AddAcceptKpiInfo(Dictionary<uint, object> kpiDic)
        {
            statsKpiNewestValues(kpiDic);
        }

        //统计指标的最新一个指标值
        protected override void statsKpiNewestValue(KpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey.Reselect34RequestCnt:
                    this.reselect34Info.TotalCount = (int)objValue;
                    break;
                case KpiKey.Reselect34SucceedCnt:
                    this.reselect34Info.ValidCount = (int)objValue;
                    break;
                case KpiKey.Reselect24RequestCnt:
                    this.reselect24Info.TotalCount = (int)objValue;
                    break;
                case KpiKey.Reselect24SucceedCnt:
                    this.reselect24Info.ValidCount = (int)objValue;
                    break;
                case KpiKey.FtpDlRsrpAvg:
                    this.ftpDlInfo.RsrpAvg = (double)objValue;
                    break;
                case KpiKey.FtpDlSinrAvg:
                    this.ftpDlInfo.SinrAvg = (double)objValue;
                    break;
                case KpiKey.FtpDlSpeedAvg:
                    this.ftpDlInfo.SpeedAvg = (double)objValue;
                    break;
                case KpiKey.FtpUlRsrpAvg:
                    this.ftpUlInfo.RsrpAvg = (double)objValue;
                    break;
                case KpiKey.FtpUlSinrAvg:
                    this.ftpUlInfo.SinrAvg = (double)objValue;
                    break;
                case KpiKey.FtpUlSpeedAvg:
                    this.ftpUlInfo.SpeedAvg = (double)objValue;
                    break;
                case KpiKey.FtpDlCoverPntCount_Valid:
                    this.dlCoverRate.ValidCount = (int)objValue;
                    break;
                case KpiKey.FtpDlCoverPntCount_Sum:
                    this.dlCoverRate.TotalCount = (int)objValue;
                    break;
                default:
                    base.statsKpiNewestValue(kpiKey, objValue);
                    break;
            }
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public override void CheckCellIsAccordAccept()
        {
            this.RrcInfo.CheckIsAccord(10, 100);
            this.ErabInfo.CheckIsAccord(10, 100);
            this.AccessInfo.CheckIsAccord(10, 100);
            this.Reselect34Info.CheckIsAccord(5, 100);
            this.Reselect24Info.CheckIsAccord(5, 100);
            this.CsfbInfo.CheckIsAccord(10, 100);
            this.FtpDlInfo.CheckIsAccord(50);
            this.FtpUlInfo.CheckIsAccord(7.5);

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            if (!(this.Reselect34Info.IsAccord || this.Reselect24Info.IsAccord))
            {
                isAllKpiAccord = false;
                strbNotAccordKpiName.Append(this.Reselect34Info.KpiName + "或" + this.Reselect24Info.KpiName + ";");
            }
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.RrcInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ErabInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.AccessInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.CsfbInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.FtpDlInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.FtpUlInfo, ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }

        public int CompareTo(OutDoorCellAcceptInfo other)
        {
            return this.CellId.CompareTo(other.CellId);
        }
    }

    #endregion


    #region 室内站
    public class InDoorBtsAcceptInfo : BtsAcceptInfoBase
    {
        public InDoorBtsAcceptInfo(LTEBTS lteBts)
            : base(lteBts)
        {
        }
        public string CoveredFloors { get; set; }
        public string CoveredFloorsDes_Lack { get; set; }

        /// <summary>
        /// 单双路
        /// </summary>
        public string RoadTypeDes { get; set; }

        //key:CellID
        public Dictionary<int, InDoorCellAcceptInfo> CellsAcceptDic { get; set; } = new Dictionary<int, InDoorCellAcceptInfo>(); 
        public string IsHandoverAccordDes { get; set; }
        public string IsLeakOutCheckAccordDes { get; set; }

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public override void CheckBtsIsAccordAccept()
        {
            getCoveredFloors();

            this.IsHandoverAccordDes = "是";
            this.IsLeakOutCheckAccordDes = "是";
            bool allCellAccord = true;
            StringBuilder strbNotAccordDes = new StringBuilder();

            foreach (LTECell cell in LteBts.Cells)
            {
                InDoorCellAcceptInfo cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    if (string.IsNullOrEmpty(this.RoadTypeDes))
                    {
                        this.RoadTypeDes = cellInfo.RoadTypeDes;
                    }

                    cellInfo.CheckCellIsAccordAccept();
                    allCellAccord = setAccord(allCellAccord, strbNotAccordDes, cell, cellInfo);
                }
                else
                {
                    allCellAccord = false;
                    this.IsHandoverAccordDes = "否";
                    this.IsLeakOutCheckAccordDes = "否";
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }
            this.NotAccordKpiDes = strbNotAccordDes.ToString();
            this.IsAccordAccept = allCellAccord;
        }

        private bool setAccord(bool allCellAccord, StringBuilder strbNotAccordDes, LTECell cell, InDoorCellAcceptInfo cellInfo)
        {
            if (!cellInfo.IsAccord)
            {
                allCellAccord = false;
                strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
            }
            if (!cellInfo.HandOverInfo.IsAccord)
            {
                this.IsHandoverAccordDes = "否";
            }
            if (!cellInfo.IsLeakoutRateAccord)
            {
                this.IsLeakOutCheckAccordDes = "否";
            }

            return allCellAccord;
        }

        private void getCoveredFloors()
        {
            List<int> floorList = new List<int>();
            foreach (InDoorCellAcceptInfo cellInfo in this.CellsAcceptDic.Values)
            {
                floorList.AddRange(cellInfo.CoveredFloorList);
            }
            if (floorList.Count > 0)
            {
                floorList.Sort();
                int minFloor = floorList[0];
                int maxFloor = floorList[floorList.Count -1];

                if (minFloor == maxFloor)
                {
                    this.CoveredFloors = (minFloor + "F").Replace("-", "B");
                }
                else
                {
                    this.CoveredFloors = string.Format("{0}F—{1}F", minFloor, maxFloor).Replace("-", "B");

                    setCoveredFloorsDes(floorList, minFloor, maxFloor);
                }
            }

        }

        private void setCoveredFloorsDes(List<int> floorList, int minFloor, int maxFloor)
        {
            StringBuilder lackFloorDesStrb = new StringBuilder();
            for (int i = minFloor; i <= maxFloor; i++)
            {
                if (i == 0)
                {
                    continue;
                }
                if (!floorList.Contains(i))
                {
                    lackFloorDesStrb.Append(i + "F" + "、");
                }
            }
            if (lackFloorDesStrb.Length > 0)
            {
                this.CoveredFloorsDes_Lack = "缺少"
                    + lackFloorDesStrb.Remove(lackFloorDesStrb.Length - 1, 1).ToString() + "文件...";
            }
        }
    }
    public class InDoorCellAcceptInfo : CellAcceptInfoBase
    {
        public InDoorCellAcceptInfo(LTECell cell)
            : base(cell)
        {
        }
        /// <summary>
        /// 单双路
        /// </summary>
        public string RoadTypeDes { get; set; }

        public AvgKpiInfo FtpRsrpInfo { get; set; } = new AvgKpiInfo();// RSRP
        public AvgKpiInfo FtpSinrInfo { get; set; } = new AvgKpiInfo();// SINR
        #region 下载
        public string IsFtpDlAccordDes { get { return IsFtpDlAccord ? "是" : "否"; } }
        public bool IsFtpDlAccord { get; set; }
        public AvgKpiInfo FtpDlSpeedInfo { get; set; } = new AvgKpiInfo();//下行吞吐率
        #endregion

        #region 上传
        public string IsFtpUlAccordDes { get { return IsFtpUlAccord ? "是" : "否"; } }
        public bool IsFtpUlAccord { get; set; }
        public double FtpUlSpeedMax { get; set; } = double.MinValue;
        public AvgKpiInfo FtpUlSpeedInfo { get; set; } = new AvgKpiInfo();//上行吞吐率
        #endregion

        private readonly RateKpiInfo handOverInfo = new RateKpiInfo("系统内切换");
        public RateKpiInfo HandOverInfo { get { return handOverInfo; } }
        public bool IsLeakoutRateAccord { get; set; }
        public double? LeakoutRate_LockEarfcn { get; set; }//锁频模式下，弱外泄采样点占比
        public double? LeakoutRate_Scan { get; set; }//扫频频模式下，弱外泄采样点占比

        public List<int> CoveredFloorList { get; set; } = new List<int>();//楼层
        public string CoveredFloorDes
        {
            get 
            {
                CoveredFloorList.Sort();
                StringBuilder strb = new StringBuilder();
                foreach (int floor in CoveredFloorList)
                {
                    strb.Append(floor.ToString().Replace("-", "B") + "F,");
                }

                if (strb.Length > 0)
                {
                    strb.Remove(strb.Length - 1, 1);
                }
                return strb.ToString();
            }
        }

        public List<LevelTestKpiInfo> LevelTestInfoList { get; set; } = new List<LevelTestKpiInfo>();//平层测试指标信息
        public virtual void AddAcceptKpiInfo(string fileName, Dictionary<uint, object> kpiDic)
        {
            object floorObj = getKpiFloor(kpiDic);
            LevelTestKpiInfo kpiInfo = null;
            statsLevelTestKpi(fileName, floorObj, kpiDic, ref kpiInfo);
            statsCapabilityTestKpi(fileName, kpiDic, kpiInfo);
        }
        protected object getKpiFloor(Dictionary<uint, object> kpiDic)
        {
            object floorObj;
            int floor;
            if (kpiDic.TryGetValue((uint)KpiKey.IndoorCoveredFloor, out floorObj))
            {
                floor = (int)floorObj;
                if (!CoveredFloorList.Contains(floor))
                {
                    CoveredFloorList.Add(floor);
                }
                kpiDic.Remove((uint)KpiKey.IndoorCoveredFloor);//获取值后移除该键值
            }
            return floorObj;
        }

        //统计平层测试指标（切换类文件、楼层上传测试、楼层下载测试），然后从kpiDic移除平层测试指标信息
        protected virtual void statsLevelTestKpi(string fileName, object floorObj, Dictionary<uint, object> kpiDic
            , ref LevelTestKpiInfo kpiInfo)
        {
            string fileNameKey;
            if (fileName.Contains("切换"))
            {
                fileNameKey = "切换";
            }
            else if (fileName.Contains("上传") && floorObj != null)
            {
                fileNameKey = floorObj.ToString() + "F上传";
            }
            else if (fileName.Contains("下载") && floorObj != null)
            {
                fileNameKey = floorObj.ToString() + "F下载";
            }
            else
            {
                return;
            }

            if (hasGotKpiKeyList.Contains(fileNameKey))
            {
                return;
            }
            hasGotKpiKeyList.Add(fileNameKey);

            kpiInfo = new LevelTestKpiInfo(fileName);
            kpiInfo.FillKpiInfo(kpiDic);
            this.LevelTestInfoList.Add(kpiInfo);
        }

        #region 统计性能测试指标
        protected virtual void statsCapabilityTestKpi(string fileName, Dictionary<uint, object> kpiDic
            , LevelTestKpiInfo levelInfo)
        {
            //从平层测试指标中汇总各小区的遍历性测试性能指标
            if (levelInfo != null)
            {
                if (fileName.Contains("切换"))
                {
                    handOverInfo.AddInfo(levelInfo.HandoverSucceedCnt, levelInfo.HandoverRequestCnt);
                }
                else if (fileName.Contains("上传"))
                {
                    statsFtpUlInfo(levelInfo);
                }
                else if (fileName.Contains("下载"))
                {
                    statsFtpDlInfo(levelInfo);
                }
            }

            //其他未在平层测试中获取到的指标，只取最新一个文件的指标
            statsKpiNewestValues(kpiDic);
        }

        //统计指标的最新一个指标值
        protected override void statsKpiNewestValue(KpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey.WeakLeakoutRate_LockEarfcn:
                    LeakoutRate_LockEarfcn = (double)objValue;
                    break;
                case KpiKey.WeakLeakoutRate_Scan:
                    LeakoutRate_Scan = (double)objValue;
                    break;
                default:
                    base.statsKpiNewestValue(kpiKey, objValue);
                    break;
            }
        }
        protected virtual void statsFtpUlInfo(LevelTestKpiInfo levelInfo)
        {
            this.FtpUlSpeedInfo.AddKpi(levelInfo.PointCount_UlSpeed, levelInfo.AvgULSpeed);
            this.FtpUlSpeedMax = Math.Max(this.FtpUlSpeedMax, levelInfo.MaxULSpeed);
        }
        protected virtual void statsFtpDlInfo(LevelTestKpiInfo levelInfo)
        {
            setRoadType(levelInfo.FileName);
            dlCoverRate.AddInfo(levelInfo.PointCount_RsrpB105sinr6, levelInfo.PointCount_RsrpAndSinr);

            this.FtpRsrpInfo.AddKpi(levelInfo.PointCount_Rsrp, levelInfo.AvgRsrp);
            this.FtpSinrInfo.AddKpi(levelInfo.PointCount_Sinr, levelInfo.AvgSinr);
            this.FtpDlSpeedInfo.AddKpi(levelInfo.PointCount_DlSpeed, levelInfo.AvgDLSpeed);
        }
        protected void setRoadType(string fileName)
        {
            if (string.IsNullOrEmpty(this.RoadTypeDes))
            {
                if (fileName.Contains("单路"))
                {
                    this.RoadTypeDes = "单路";
                }
                else if (fileName.Contains("移频双路"))
                {
                    this.RoadTypeDes = "移频双路";
                }
                else if (fileName.Contains("双路"))
                {
                    this.RoadTypeDes = "双路";
                }
            }
        }
        #endregion

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public override void CheckCellIsAccordAccept()
        {
            this.RrcInfo.CheckIsAccord(25, 100);
            this.ErabInfo.CheckIsAccord(25, 100);
            this.AccessInfo.CheckIsAccord(25, 100);
            this.CsfbInfo.CheckIsAccord(10, 100);
            this.HandOverInfo.CheckIsAccord(5, 98);
            this.DlCoverRate.CheckIsAccord(1, 95);

            if (this.RoadTypeDes == "单路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 28;
            }
            else if (this.RoadTypeDes == "双路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 45;
            }
            else if (this.RoadTypeDes == "移频双路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 40;
            }
            this.IsFtpUlAccord = FtpUlSpeedInfo.KpiAvgValue >= 6 && FtpUlSpeedMax >= 9;
            this.IsLeakoutRateAccord = (LeakoutRate_LockEarfcn != null && LeakoutRate_LockEarfcn >= 95)
                || (LeakoutRate_Scan != null && LeakoutRate_Scan > 95);

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.RrcInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ErabInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.AccessInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.CsfbInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.HandOverInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.DlCoverRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(IsFtpDlAccord, "下行吞吐率", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(IsFtpUlAccord, "上行吞吐率", ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }
    }

    #endregion

    public abstract class BtsAcceptInfoBase
    {
        protected BtsAcceptInfoBase(LTEBTS lteBts)
        {
            this.LteBts = lteBts;
        }
        public int SN { get; set; }
        public string Time
        {
            get
            {
                AccpetTimePeriod.showDayFormat = true;
                return AccpetTimePeriod.ToString();
            }
        }

        public TimePeriod AccpetTimePeriod { get; set; } = new TimePeriod();

        public BtsFusionInfoBase FusionInfo { get; set; }
        public LTEBTS LteBts { get; set; }
        public string BtsName { get { return LteBts.Name.Trim(); } }
        public int BtsId { get { return LteBts.BTSID; } }
        public double Longitude { get { return LteBts.Longitude; } }
        public double Latitude { get { return LteBts.Latitude; } }
        public string NotAccordKpiDes { get; set; }
        public string IsAccordAcceptStr { get { return IsAccordAccept ? "是" : "否"; } }
        
        public bool IsAccordAccept { get; set; } = false;
        public abstract void CheckBtsIsAccordAccept();
    }
    public abstract class CellAcceptInfoBase
    {
        protected CellAcceptInfoBase(LTECell cell)
        {
            this.LteCell = cell;
        }
        public LTECell LteCell { get; set; }
        public int CellId
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.CellID;
                }
                return 0;
            }
        }
        public string CellName
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.Name;
                }
                return "";
            }
        }
        public double Longitude
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.Longitude;
                }
                return 0;
            }
        }
        public double Latitude
        {
            get
            {
                if (LteCell != null)
                {
                    return LteCell.Latitude;
                }
                return 0;
            }
        }

        protected List<string> hasGotKpiKeyList = new List<string>();//已经获取到的指标Key集合

        protected bool isAccord = false;
        public bool IsAccord { get { return isAccord; } }
        public string IsAccordDes { get { return isAccord ? "是" : "否"; } }

        public string NotAccordKpiDes { get; set; }
        protected string getNotAccordKpiInfo(RateKpiInfo kpi, ref bool isAllKpiAccord)
        {
            return getNotAccordKpiInfo(kpi.IsAccord, kpi.KpiName, ref isAllKpiAccord);
        }
        protected string getNotAccordKpiInfo(FtpKpiInfo kpi, ref bool isAllKpiAccord)
        {
            return getNotAccordKpiInfo(kpi.IsAccord, kpi.KpiName, ref isAllKpiAccord);
        }
        protected string getNotAccordKpiInfo(bool isKpiAccord, string strKpiName, ref bool isAllKpiAccord)
        {
            if (!isKpiAccord)
            {
                isAllKpiAccord = false;
                return strKpiName + ";";
            }
            return string.Empty;
        }

        private readonly RateKpiInfo rrcInfo = new RateKpiInfo("RRC Setup Success Rate");
        public RateKpiInfo RrcInfo { get { return rrcInfo; } }

        private readonly RateKpiInfo erabInfo = new RateKpiInfo("ERAB Setup Success Rate");
        public RateKpiInfo ErabInfo { get { return erabInfo; } }

        private readonly RateKpiInfo accessInfo = new RateKpiInfo("Access Success Rate");
        public RateKpiInfo AccessInfo { get { return accessInfo; } }

        private readonly RateKpiInfo csfbInfo = new RateKpiInfo("CSFB呼叫成功率");
        public RateKpiInfo CsfbInfo { get { return csfbInfo; } }

        protected RateKpiInfo dlCoverRate = new RateKpiInfo("覆盖率");
        public RateKpiInfo DlCoverRate { get { return dlCoverRate; } }

        /// <summary>
        /// 统计各指标的最新一个指标值
        /// </summary>
        /// <param name="kpiDic"></param>
        protected void statsKpiNewestValues(Dictionary<uint, object> kpiDic)
        {
            foreach (uint key in kpiDic.Keys)
            {
                KpiKey kpiKey = (KpiKey)key;

                if (!hasGotKpiKeyList.Contains(kpiKey.ToString()))
                {
                    object objValue = kpiDic[key];
                    hasGotKpiKeyList.Add(kpiKey.ToString());

                    statsKpiNewestValue(kpiKey, objValue);
                }
            }
        }

        /// <summary>
        /// 统计指标的最新一个指标值
        /// </summary>
        /// <param name="kpiKey"></param>
        /// <param name="objValue"></param>
        protected virtual void statsKpiNewestValue(KpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey.RrcRequestCnt:
                    this.rrcInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.RrcSucceedCnt:
                    this.rrcInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.ErabRequestCnt:
                    this.erabInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.ErabSucceedCnt:
                    this.erabInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.AccRequestCnt:
                    this.accessInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.AccSucceedCnt:
                    this.accessInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.CsfbRequestCnt:
                    this.csfbInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.CsfbSucceedCnt:
                    this.csfbInfo.ValidCount = (int)objValue;
                    break;
                default:
                    break;
            }
        }

        public abstract void CheckCellIsAccordAccept();
    }
    public class AvgKpiInfo
    {
        public double KpiSum { get; set; }

        public int PointCount { get; set; }
        public double? KpiAvgValue
        {
            get
            {
                if (PointCount > 0)
                {
                    return Math.Round(KpiSum / (double)PointCount, 2);
                }
                return null;
            }
        }

        public string KpiAvgValueDes
        {
            get { return KpiAvgValue == null ? "" : KpiAvgValue.ToString(); }
        }
        public void AddKpi(int pnt, double kpiAvg)
        {
            this.PointCount += pnt;
            this.KpiSum += pnt * kpiAvg;
        }
        public void AddSumKpi(int pnt, double kpiSum)
        {
            this.PointCount += pnt;
            this.KpiSum += kpiSum;
        }
        public void Merge(AvgKpiInfo otherInfo)
        {
            if (otherInfo != null)
            {
                this.PointCount += otherInfo.PointCount;
                this.KpiSum += otherInfo.KpiSum;
            }
        }
    }
    public class RateKpiInfo
    {
        public RateKpiInfo(string strKpiName)
        {
            this.KpiName = strKpiName;
        }
        public RateKpiInfo(string strKpiName, int validCount, int totalCount)
            : this(strKpiName)
        {
            this.ValidCount = validCount;
            this.TotalCount = totalCount;
        }
        public string KpiName { get; set; }
        /// <summary>
        /// 有效个数或成功次数
        /// </summary>
        public int? ValidCount { get; set; }

        /// <summary>
        /// 总个数或拨测尝试次数
        /// </summary>
        public int? TotalCount { get; set; }
        public double? Rate
        {
            get
            {
                if (TotalCount > 0)
                {
                    return Math.Round((double)ValidCount * 100 / (double)TotalCount, 2);
                }
                return null;
            }
        }
        public bool IsAccord { get; private set; }
        public string IsAccordDes { get { return IsAccord ? "是" : "否"; } }

        public void AddInfo(int validCount, int totalCount)
        {
            if (ValidCount == null)
            {
                ValidCount = 0;
            }
            ValidCount += validCount;

            if (TotalCount == null)
            {
                TotalCount = 0;
            }
            TotalCount += totalCount;
        }
        public void Merge(RateKpiInfo otherInfo)
        {
            if (otherInfo != null)
            {
                this.ValidCount += otherInfo.ValidCount;
                this.TotalCount += otherInfo.TotalCount;
            }
        }

        /// <summary>
        /// 检验是否通过验收或高于门限值（拨测次数合格门限，成功率合格门限）
        /// </summary>
        /// <param name="minValidCount"></param>
        /// <param name="minValidRate"></param>
        public void CheckIsAccord(int minValidCount, double minValidRate)
        {
            if (ValidCount >= minValidCount && Rate >= minValidRate)
            {
                this.IsAccord = true;
            }
            else
            {
                this.IsAccord = false;
            }
        }
        public override string ToString()
        {
            return IsAccordDes;
        }
    }

    public class FtpKpiInfo
    {
        public FtpKpiInfo(string strKpiName)
        {
            this.KpiName = strKpiName;
        }
        public string KpiName { get; set; }
        public double? RsrpAvg { get; set; }
        public double? SinrAvg { get; set; }
        public double? SpeedAvg { get; set; }

        public bool IsAccord { get; private set; }
        public string IsAccordDes { get { return IsAccord ? "是" : "否"; } }

        /// <summary>
        /// 检验是否通过验收
        /// </summary>
        /// <param name="minRsrp"></param>
        /// <param name="minSinr"></param>
        /// <param name="minSpeed"></param>
        /// <returns></returns>
        public void CheckIsAccord(double minRsrp, double minSinr, double minSpeed)
        {
            if (RsrpAvg > minRsrp && SinrAvg >= minSinr && SpeedAvg > minSpeed)
            {
                this.IsAccord = true;
            }
            else
            {
                this.IsAccord = false;
            }
        }

        /// <summary>
        /// 只检查速率是否通过验收
        /// </summary>
        /// <param name="minSpeed"></param>
        public void CheckIsAccord(double minSpeed)
        {
            if (SpeedAvg > minSpeed)
            {
                this.IsAccord = true;
            }
            else
            {
                this.IsAccord = false;
            }
        }
        public override string ToString()
        {
            return IsAccordDes;
        }
    }

    public class LevelTestKpiInfo : LevelTestKpiBase
    {
        public LevelTestKpiInfo(string fileName)
            : base(fileName)
        {
        }

        public double HandoverSucceedRate
        {
            get
            {
                if (HandoverRequestCnt > 0)
                {
                    return Math.Round(100 * (double)HandoverSucceedCnt / HandoverRequestCnt, 2);
                }
                return double.MinValue;
            }
        }

        public int HandoverRequestCnt { get; set; } = 0;
        public int HandoverSucceedCnt { get; set; } = 0;

        //从kpiDic获取指标，获取值后从kpiDic移除该键值
        public void FillKpiInfo(Dictionary<uint, object> kpiDic)
        {
            this.HandoverRequestCnt = getKpiIntValue(KpiKey.InHandoverRequestCnt, kpiDic);
            this.HandoverSucceedCnt = getKpiIntValue(KpiKey.InHandoverSucceedCnt, kpiDic);
            this.BalanceRate = getKpiDoubleValue(KpiKey.DoubleBalanceRate, kpiDic);
            this.AvgRsrp = getKpiDoubleValue(KpiKey.FtpRsrpAvg, kpiDic);
            this.AvgSinr = getKpiDoubleValue(KpiKey.FtpSinrAvg, kpiDic);
            this.SinrRate_HigherThan6 = getKpiDoubleValue(KpiKey.SinrRateHigherThan6, kpiDic);
            this.SinrRate_HigherThan9 = getKpiDoubleValue(KpiKey.SinrRateHigherThan9, kpiDic);
            this.RsrpB105Sinr6Rate = getKpiDoubleValue(KpiKey.RsrpB105Sinr6Rate, kpiDic);
            this.RsrpB95Sinr9Rate = getKpiDoubleValue(KpiKey.RsrpB95Sinr9Rate, kpiDic);
            this.RsrpRate_HigerThanB85 = getKpiDoubleValue(KpiKey.RsrpRateHigerThanB85, kpiDic);
            if (FileName.Contains("上传"))
            {
                this.PointCount_RsrpAndSinr = getKpiIntValue(KpiKey.FtpUlCoverPntCount_Sum, kpiDic);
                this.PointCount_RsrpB105sinr6 = getKpiIntValue(KpiKey.FtpUlCoverPntCount_Valid, kpiDic);
                this.PointCount_Rsrp = getKpiIntValue(KpiKey.FtpUlPntCount_Rsrp, kpiDic);
                this.PointCount_Sinr = getKpiIntValue(KpiKey.FtpUlPntCount_Sinr, kpiDic);
                this.AvgULSpeed = getKpiDoubleValue(KpiKey.FtpUlSpeedAvg, kpiDic);
                this.MaxULSpeed = getKpiDoubleValue(KpiKey.FtpUlSpeedMax, kpiDic);
                this.PointCount_UlSpeed = getKpiIntValue(KpiKey.FtpUlPntCount_Speed, kpiDic);
            }
            else if (FileName.Contains("下载"))
            {
                this.PointCount_RsrpAndSinr = getKpiIntValue(KpiKey.FtpDlCoverPntCount_Sum, kpiDic);
                this.PointCount_RsrpB105sinr6 = getKpiIntValue(KpiKey.FtpDlCoverPntCount_Valid, kpiDic);
                this.PointCount_Rsrp = getKpiIntValue(KpiKey.FtpDlPntCount_Rsrp, kpiDic);
                this.PointCount_Sinr = getKpiIntValue(KpiKey.FtpDlPntCount_Sinr, kpiDic);
                this.MaxDLSpeed = getKpiDoubleValue(KpiKey.FtpDlSpeedMax, kpiDic);
                this.PointCount_DlSpeed = getKpiIntValue(KpiKey.FtpDlPntCount_Speed, kpiDic);
                this.AvgDLSpeed = getKpiDoubleValue(KpiKey.FtpDlSpeedAvg, kpiDic);
            }
        }

        private int getKpiIntValue(KpiKey key, Dictionary<uint, object> kpiDic)
        {
            object valueObj;
            if (kpiDic.TryGetValue((uint)key, out valueObj))
            {
                kpiDic.Remove((uint)key);//获取值后移除该键值
                return (int)valueObj;
            }
            return 0;
        }
        private double getKpiDoubleValue(KpiKey key, Dictionary<uint, object> kpiDic)
        {
            object valueObj;
            if (kpiDic.TryGetValue((uint)key, out valueObj))
            {
                kpiDic.Remove((uint)key);//获取值后移除该键值
                return (double)valueObj;
            }
            return 0.0;
        }
    }

    /// <summary>
    /// 平层测试信息基类
    /// </summary>
    public class LevelTestKpiBase
    {
        public LevelTestKpiBase(string fileName)
        {
            this.FileName = fileName;
        }
        public string FileName { get; protected set; }
        public virtual double BalanceRate { get; set; }
        public virtual double AvgRsrp { get; set; }
        public virtual double AvgSinr { get; set; }
        public virtual double AvgULSpeed { get; set; }
        public virtual double AvgDLSpeed { get; set; }
        public virtual double SinrRate_HigherThan6 { get; set; }
        public virtual double SinrRate_HigherThan9 { get; set; }
        public virtual double RsrpB105Sinr6Rate { get; set; }
        public virtual double RsrpB95Sinr9Rate { get; set; }
        public virtual double RsrpRate_HigerThanB85 { get; set; }

        public int PointCount_Rsrp { get; set; } = 0;
        public int PointCount_Sinr { get; set; } = 0;
        public int PointCount_RsrpB105sinr6 { get; set; }
        public int PointCount_RsrpAndSinr { get; set; } = 0;
        public int PointCount_DlSpeed { get; set; } = 0;
        public int PointCount_UlSpeed { get; set; } = 0;
        public double MaxULSpeed { get; set; } = 0;
        public double MaxDLSpeed { get; set; } = 0;
    }
}
