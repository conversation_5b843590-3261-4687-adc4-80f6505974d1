﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class QueryPermissionDataSrc : DIYSQLBase
    {
        private enum Step
        {
            S1_Role,
            S2_Role_Cat,
            S3_Role_Area
        }
        public QueryPermissionDataSrc(int dbID)
            : base(MainModel.GetInstance(), dbID)
        {
            this.MainDB = false;
        }

        Step curStep;
        protected override string getSqlTextString()
        {
            switch (curStep)
            {
                case Step.S1_Role:
                    return "select iid, strname, strcomment from dbo.tb_cfg_static_dataSourceRole order by iid";
                case Step.S2_Role_Cat:
                    return "select iRoleID, strCategoryName,iCategorySubItemID from dbo.tb_cfg_static_dataSourceRole_category order by iRoleID;";
                case Step.S3_Role_Area:
                    return "select iRoleID, iAreaTypeID,iAreaID from dbo.tb_cfg_static_dataSourceRole_area order by iRoleID;";
                default:
                    return null;
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            switch (curStep)
            {
                case Step.S1_Role:
                    {
                        E_VType[] rType = new E_VType[3];
                        rType[0] = E_VType.E_Int;
                        rType[1] = E_VType.E_String;
                        rType[2] = E_VType.E_String;
                        return rType;
                    }
                case Step.S2_Role_Cat:
                    {
                        E_VType[] rType = new E_VType[3];
                        rType[0] = E_VType.E_Int;
                        rType[1] = E_VType.E_String;
                        rType[2] = E_VType.E_Int;
                        return rType;
                    }
                case Step.S3_Role_Area:
                    {
                        E_VType[] rType = new E_VType[3];
                        rType[0] = E_VType.E_Int;
                        rType[1] = E_VType.E_Int;
                        rType[2] = E_VType.E_Int;
                        return rType;
                    }
                default:
                    return new E_VType[0];
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {

                WaitTextBox.Text = "1/3 数据源信息";
                queryFromDB((ClientProxy)o, Step.S1_Role);
                if (roles.Count > 0)
                {
                    WaitTextBox.Text = "2/3 数据源信息";
                    queryFromDB((ClientProxy)o, Step.S2_Role_Cat);
                    WaitTextBox.Text = "3/3 数据源信息";
                    queryFromDB((ClientProxy)o, Step.S3_Role_Area);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        private void queryFromDB(ClientProxy proxy, Step step)
        {
            curStep = step;
            Package package = proxy.Package;
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }

            package.Content.AddParam(sb.ToString().TrimEnd(','));
            proxy.Send();
            receiveRetData(proxy);
        }

        private readonly List<DataSourceRole> roles = new List<DataSourceRole>();
        public List<DataSourceRole> Roles
        {
            get { return roles; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(clientProxy, package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(ClientProxy clientProxy, Package package)
        {
            switch (curStep)
            {
                case Step.S1_Role:
                    {
                        DataSourceRole role = DataSourceRole.Fill(package.Content);
                        role.DistrictID = clientProxy.DbID;
                        roles.Add(role);
                    }
                    break;
                case Step.S2_Role_Cat:
                    {
                        int roleID = package.Content.GetParamInt();
                        string categoryName = package.Content.GetParamString();
                        int subID = package.Content.GetParamInt();
                        DataSourceRole role = roles.Find(delegate (DataSourceRole r)
                        {
                            return r.ID == roleID;
                        });
                        if (role != null)
                        {
                            role.UpdatePermission(categoryName, subID, true, false);
                        }
                    }
                    break;
                case Step.S3_Role_Area:
                    {
                        int roleID = package.Content.GetParamInt();
                        int areaTypeID = package.Content.GetParamInt();
                        int areaID = package.Content.GetParamInt();
                        DataSourceRole role = roles.Find(delegate (DataSourceRole r)
                        {
                            return r.ID == roleID;
                        });
                        if (role != null)
                        {
                            if (areaTypeID == -1 && areaID == -1)
                            {
                                role.HasAllAreaRightIncludeNew = true;
                            }
                            else
                            {
                                role.UpdatePermission(areaTypeID, areaID, true, false);
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        public override string Name
        {
            get { return "初始化数据源权限信息"; }
        }
    }

}
