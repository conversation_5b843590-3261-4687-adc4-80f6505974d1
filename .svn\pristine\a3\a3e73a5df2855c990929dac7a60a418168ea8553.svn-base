﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEFddCSFBSignalAnaByFile : ZTLTECSFBSignalAnaByFile
    {
        public LTEFddCSFBSignalAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
            evtId_MO_CSFB_LTE_Release = 3072;
            evtId_MT_CSFB_LTE_Release = 3073;
            evtId_Return_Back_to_LTE_Complete = 3084;
            evtId_CSFB_Handover_Success = 3147;
        }

        public override int ServiceType
        {
            get { return 45; }
        }

        public override string Name
        {
            get { return "CSFB信令分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26021, this.Name);
        }
        protected override void setMsgInfos(CSFBCallInfo callInfo, long callHead, long callTail, DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            dealMoMsg(callInfo, callHead, callTail, moFile);

            dealMtMsg(callInfo, callHead, callTail, mtFile);
        }

        private static void dealMoMsg(CSFBCallInfo callInfo, long callHead, long callTail, DTFileDataManager moFile)
        {
            foreach (MasterCom.RAMS.Model.Message msg in moFile.Messages) //主叫文件
            {
                if ((msg.Time * 1000L + msg.Millisecond) >= callHead && (msg.Time * 1000L + msg.Millisecond) <= callTail)
                {
                    if (msg.ID == (int)CSFBMsg_FDD.Setup)
                    {
                        callInfo.moSetupTime = msg.Time * 1000L + msg.Millisecond;
                    }
                    else if (msg.ID == (int)CSFBMsg_FDD.AssignmentComplete)
                    {
                        callInfo.moAssignmentCompleteTime = msg.Time * 1000L + msg.Millisecond;
                    }
                }
            }
        }

        private static void dealMtMsg(CSFBCallInfo callInfo, long callHead, long callTail, DTFileDataManager mtFile)
        {
            foreach (MasterCom.RAMS.Model.Message msg in mtFile.Messages) //被叫文件
            {
                callInfo.isFdd = true;
                if ((msg.Time * 1000L + msg.Millisecond) >= callHead && (msg.Time * 1000L + msg.Millisecond) <= callTail)
                {
                    if (msg.ID == (int)CSFBMsg_FDD.RRCConnectionRelease)
                    {
                        callInfo.mtRrcConnectionReleaseTime = msg.Time * 1000L + msg.Millisecond;
                    }
                    else if (msg.ID == (int)CSFBMsg_FDD.Setup)
                    {
                        callInfo.mtSetupTime = msg.Time * 1000L + msg.Millisecond;
                    }
                    else if (msg.ID == (int)CSFBMsg_FDD.RRCConnectionSetupComplete)
                    {
                        callInfo.mtrrcConnectionSetupCompleteTime = msg.Time * 1000L + msg.Millisecond;
                    }
                }
            }
        }
    }
    public enum CSFBMsg_FDD
    {
        RRCConnectionRelease = MessageManager.LTE_RRC_RRC_Connection_Release,
        Setup = 1899627269,
        AssignmentComplete = 1577,
        RRCConnectionSetupComplete=1093599507,
        ExtendedServiceRequest = 0x416B074c,
        Alterting = 1899627265,
        Disconnect = 1899627301,
        CmServiceAbort = 1315,
    }
}