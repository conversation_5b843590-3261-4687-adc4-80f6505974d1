﻿using System;
using System.Collections.Generic;
using System.Text;
using GMap.NET;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ExMap
{
    public class ExMapRsvStreetLayer : ExMapDrawBaseLayer
    {
        public ExMapRsvStreetLayer(MTExGMap mapCtrl)
            : base(mapCtrl)
        {
        }

        private List<List<PointLatLng>> streetPointSet = null;

        private readonly System.Drawing.Pen linePen = new System.Drawing.Pen(System.Drawing.Color.Red, 2);
        internal void ApplyStreets(List<MapWinGIS.Shape> list)
        {
            streetPointSet = new List<List<PointLatLng>>();
            if (list == null)
            {
                return;
            }
            foreach (MapWinGIS.Shape shape in list)
            {
                for (int x = 0; x < shape.NumParts; x++)
                {
                    List<PointLatLng> pnts = ShapeHelper.GetPartShapePointsEx(shape, x);
                    if (pnts != null && pnts.Count > 0)
                    {
                        streetPointSet.Add(pnts);
                    }
                }
            }
        }

        public override void Draw(System.Drawing.Graphics g, PointLatLng ltPt, PointLatLng brPt)
        {
            if (streetPointSet == null)
            {
                return;
            }

            foreach (List<PointLatLng> streetPnts in streetPointSet)
            {
                System.Drawing.Point[] pnts = new System.Drawing.Point[streetPnts.Count];
                for (int i = 0; i < streetPnts.Count; i++)
                {
                    GPoint gPnt = this.exMap.FromLatLngToLocalAdaptered(streetPnts[i]);
                    pnts[i] = new System.Drawing.Point(gPnt.X, gPnt.Y);
                }
                g.DrawLines(linePen, pnts);
            }
        }

        public override string Alias
        {
            get { return "预存道路图层"; }
        }
    }
}
