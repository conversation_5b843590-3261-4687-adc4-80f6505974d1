﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellWrongDirSettingDlg : BaseDialog
    {
        public CellWrongDirSettingDlg()
        {
            InitializeComponent();
        }

        public void getCondition(out int minRxLev, out int minDistance, out int badSampleRate)
        {
            minRxLev = (int)numMinRxLev.Value;
            minDistance = (int)numMinDistance.Value;
            badSampleRate = (int)numBadSampleRate.Value;
        }
    }
}
