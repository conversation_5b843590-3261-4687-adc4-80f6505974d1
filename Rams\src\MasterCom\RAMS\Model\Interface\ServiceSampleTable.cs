﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.Interface
{
    public class ServiceSampleTable
    {
        public string ServiceName { get; set; }
        public string TableModelName { get; set; }
        public int ServiceTypeV { get; set; }
        public ServiceSampleTable(string servicename,string tableModelname,int svtype)
        {
            this.ServiceName = servicename;
            this.TableModelName = tableModelname;
            this.ServiceTypeV = svtype;
        }
        public override string  ToString()
        {
 	         return ServiceName;
        }
    }
}
