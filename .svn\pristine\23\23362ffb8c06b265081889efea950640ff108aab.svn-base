using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model
{
    public class Company : IUserInfoObject
    {
        public Company()
        {

        }

        public int ID { get; set; }

        public string LoginName { get; set; }

        public string Description { get; set; }

        public List<Department> DepartmentList
        {
            get { return departmentList; }
        }

        public override string ToString()
        {
            return LoginName;
        }

        public void AddDepartment(Department department)
        {
            if (!departmentList.Contains(department))
            {
                departmentList.Add(department);
            }
        }

        public void AddDepartments(List<Department> departments)
        {
            foreach (Department department in departments)
            {
                if (!departmentList.Contains(department))
                {
                    departmentList.Add(department);
                }
            }
        }

        public void AddOffice(Office office)
        {
            foreach (Department department in departmentList)
            {
                if (department.ID == office.DepartmentID)
                {
                    department.AddOffice(office);
                    return;
                }
            }
        }

        public static Company Fill(Content content)
        {
            Company company = new Company();
            company.ID = content.GetParamInt();
            company.LoginName = content.GetParamString();
            company.Description = content.GetParamString();
            return company;
        }

        public static IComparer<Company> GetCompareByCompanyID()
        {
            if (compareByCompanyID == null)
            {
                compareByCompanyID = new CompareByCompanyID();
            }
            return compareByCompanyID;
        }

        private static IComparer<Company> compareByCompanyID;

        private class CompareByCompanyID : IComparer<Company>
        {
            public int Compare(Company x, Company y)
            {
                return x.ID.CompareTo(y.ID);
            }
        }
        
        private readonly List<Department> departmentList = new List<Department>();
    }
}
