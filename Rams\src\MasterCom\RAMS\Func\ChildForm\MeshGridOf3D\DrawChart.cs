using System;
using System.Drawing;
using System.Runtime.Serialization;

namespace MasterCom.RAMS.Func.MeshGridOf3D
{
    /// <summary>
    /// A class handler all methods for drawing cuver on the chart area
    /// </summary>
    /// 
    [Serializable]
    public class DrawChart : ISerializable
    {
        #region Variables
        private MeshGridOf3DForm form1;
        #endregion

        #region Enum

        public enum ChartTypeDef
        {
            Line,
            Mesh,
            MeshZ,
            Waterfall,
            Surface,
        }
        #endregion

        #region Constructors

        public DrawChart(MeshGridOf3DForm fm1)
        {
            form1 = fm1;
        }
        #endregion

        #region Properties

        public int NumberContours { get; set; } = 10;
        public int NumberInterp { get; set; } = 2;
        public bool IsInterp { get; set; } = false;
        public bool IsColorMap { get; set; } = true;
        public bool IsHiddenLine { get; set; } = false;
        public int[,] CMap { get; set; }
        public ChartTypeDef ChartType { get; set; }
        #endregion

        #region Serialization

        /// <summary>
        /// Constructor for deserializing objects
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data
        /// </param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data
        /// </param>
        /// 
        protected DrawChart(SerializationInfo info, StreamingContext context)
        {
            ChartType = (ChartTypeDef)info.GetValue("chartType", typeof(ChartTypeDef));
            IsColorMap = info.GetBoolean("isColorMap");
            IsHiddenLine = info.GetBoolean("isHiddenLine");
            IsInterp = info.GetBoolean("isInterp");
            NumberInterp = info.GetInt32("numberInterp");
            NumberContours = info.GetInt32("numberContours");
        }
        /// <summary>
        /// Populates a <see cref="SerializationInfo"/> instance with the data needed to serialize the target object
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data</param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data</param>
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            info.AddValue("chartType", ChartType);
            info.AddValue("isColorMap", IsColorMap);
            info.AddValue("isHiddenLine", IsHiddenLine);
            info.AddValue("isInterp", IsInterp);
            info.AddValue("numberInterp", NumberInterp);
            info.AddValue("numberContours", NumberContours);

        }
        #endregion

        #region Methods

        public void AddChart(Graphics g, DataSeries ds, ChartStyle cs, ChartStyle2D cs2d)
        {
            switch (ChartType)
            {
                case ChartTypeDef.Line:
                    AddLine(g, ds, cs);
                    break;
                case ChartTypeDef.Mesh:
                    AddMesh(g, ds, cs);
                    AddColorBar(g, ds, cs, cs2d);
                    break;
                case ChartTypeDef.MeshZ:
                    AddMeshZ(g, ds, cs);
                    AddColorBar(g, ds, cs, cs2d);
                    break;
                case ChartTypeDef.Waterfall:
                    AddWaterfall(g, ds, cs);
                    AddColorBar(g, ds, cs, cs2d);
                    break;
                case ChartTypeDef.Surface:
                    AddSurface(g, ds, cs, cs2d);
                    AddColorBar(g, ds, cs, cs2d);
                    break;
            }
        }

        private void AddLine(Graphics g, DataSeries ds, ChartStyle cs)
        {
            Pen aPen = new Pen(ds.LineStyle.LineColor, ds.LineStyle.Thickness);
            aPen.DashStyle = ds.LineStyle.Pattern;
            Matrix3 m = Matrix3.AzimuthElevation(cs.Elevation, cs.Azimuth);
            Point3[] pts = new Point3[ds.PointList.Count];

            // Find zmin and zmax values, then perform transformation on points:
            float zmin = 0;
            float zmax = 0;
            for (int i = 0; i < pts.Length; i++)
            {
                pts[i] = (Point3)ds.PointList[i];
                zmin = Math.Min(zmin, pts[i].Z);
                zmax = Math.Max(zmax, pts[i].Z);
                pts[i].Transform(m, form1, cs);
            }

            // Draw line:
            if (ds.LineStyle.IsVisible)
            {
                for (int i = 1; i < pts.Length; i++)
                {
                    Color color = AddColor(pts[i], zmin, zmax);
                    if (IsColorMap)
                    {
                        aPen = new Pen(color, ds.LineStyle.Thickness);
                        aPen.DashStyle = ds.LineStyle.Pattern;
                    }
                    g.DrawLine(aPen, pts[i - 1].X, pts[i - 1].Y, pts[i].X, pts[i].Y);
                }
            }
            aPen.Dispose();
        }

        private void AddMesh(Graphics g, DataSeries ds, ChartStyle cs)
        {
            DrawChartInfo info = new DrawChartInfo(g, ds, cs);

            // Perform transformations on points:
            for (int i = 0; i < info.pts.GetLength(0); i++)
            {
                for (int j = 0; j < info.pts.GetLength(1); j++)
                {
                    info.pts[i, j].Transform(info.m, form1, cs);
                }
            }

            // Draw color mesh:
            DrawColorMesh(info, !IsHiddenLine);
            info.aPen.Dispose();
            info.aBrush.Dispose();
        }

        private void DrawColorMesh(DrawChartInfo info, bool isDrawLine)
        {
            for (int i = 0; i < info.pts.GetLength(0) - 1; i++)
            {
                for (int j = 0; j < info.pts.GetLength(1) - 1; j++)
                {
                    int ii = i;
                    if (info.cs.Azimuth >= -180 && info.cs.Azimuth < 0)
                    {
                        ii = info.pts.GetLength(0) - 2 - i;
                    }
                    info.pta[0] = new PointF(info.pts[ii, j].X, info.pts[ii, j].Y);
                    info.pta[1] = new PointF(info.pts[ii, j + 1].X, info.pts[ii, j + 1].Y);
                    info.pta[2] = new PointF(info.pts[ii + 1, j + 1].X, info.pts[ii + 1, j + 1].Y);
                    info.pta[3] = new PointF(info.pts[ii + 1, j].X, info.pts[ii + 1, j].Y);
                    if (isDrawLine)
                    {
                        info.g.FillPolygon(info.aBrush, info.pta);
                    }
                    if (IsColorMap)
                    {
                        Color color = AddColor(info.pts[ii, j], info.zmin, info.zmax);
                        info.aPen = new Pen(color, info.ds.LineStyle.Thickness);
                        info.aPen.DashStyle = info.ds.LineStyle.Pattern;
                    }
                    info.g.DrawPolygon(info.aPen, info.pta);
                }
            }
        }

        private void AddMeshZ(Graphics g, DataSeries ds, ChartStyle cs)
        {
            DrawChartInfo info = new DrawChartInfo(g, ds, cs);
            Point3[,] pts1 = new Point3[info.pts.GetLength(0), info.pts.GetLength(1)];

            for (int i = 0; i < info.pts.GetLength(0); i++)
            {
                for (int j = 0; j < info.pts.GetLength(1); j++)
                {
                    // Make a deep copy the points array:
                    pts1[i, j] = new Point3(info.pts[i, j].X, info.pts[i, j].Y, info.pts[i, j].Z, 1);
                    // Perform transformations on points:
                    info.pts[i, j].Transform(info.m, form1, cs);
                }
            }
            //Draw mesh using Z-order method:
            DrawColorMesh(info, true);

            //Draw cyrtain lines
            drawVerticalCyrtainLines(info, pts1);
            drawHorizontalCyrtainLines(info, pts1);
            info.aPen.Dispose();
            info.aBrush.Dispose();
        }

        private void drawVerticalCyrtainLines(DrawChartInfo info, Point3[,] pts1)
        {
            Point3[] pt3 = new Point3[4];
            for (int i = 0; i < pts1.GetLength(0); i++)
            {
                int jj = pts1.GetLength(0) - 1;
                if (info.cs.Elevation >= 0)
                {
                    if (info.cs.Azimuth >= -90 && info.cs.Azimuth <= 90)
                        jj = 0;
                }
                else if (info.cs.Elevation < 0)
                {
                    jj = 0;
                    if (info.cs.Azimuth >= -90 && info.cs.Azimuth <= 90)
                        jj = pts1.GetLength(0) - 1;
                }

                if (i < pts1.GetLength(0) - 1)
                {
                    pt3[0] = new Point3(pts1[i, jj].X, pts1[i, jj].Y, pts1[i, jj].Z, 1);
                    pt3[1] = new Point3(pts1[i + 1, jj].X, pts1[i + 1, jj].Y, pts1[i + 1, jj].Z, 1);
                    pt3[2] = new Point3(pts1[i + 1, jj].X, pts1[i + 1, jj].Y, info.cs.ZMin, 1);
                    pt3[3] = new Point3(pts1[i, jj].X, pts1[i, jj].Y, info.cs.ZMin, 1);
                    drawLine(info, pt3);
                }
            }
        }

        private void drawHorizontalCyrtainLines(DrawChartInfo info, Point3[,] pts1)
        {
            Point3[] pt3 = new Point3[4];
            for (int j = 0; j < pts1.GetLength(1); j++)
            {
                int ii = 0;
                if (info.cs.Elevation >= 0)
                {
                    if (info.cs.Azimuth >= 0 && info.cs.Azimuth <= 180)
                        ii = pts1.GetLength(1) - 1;
                }
                else if (info.cs.Elevation < 0 && info.cs.Azimuth >= -180 && info.cs.Azimuth <= 0)
                {
                    ii = pts1.GetLength(1) - 1;
                }
                if (j < pts1.GetLength(1) - 1)
                {
                    pt3[0] = new Point3(pts1[ii, j].X, pts1[ii, j].Y, pts1[ii, j].Z, 1);
                    pt3[1] = new Point3(pts1[ii, j + 1].X, pts1[ii, j + 1].Y, pts1[ii, j + 1].Z, 1);
                    pt3[2] = new Point3(pts1[ii, j + 1].X, pts1[ii, j + 1].Y, info.cs.ZMin, 1);
                    pt3[3] = new Point3(pts1[ii, j].X, pts1[ii, j].Y, info.cs.ZMin, 1);
                    drawLine(info, pt3);
                }
            }
        }

        private void drawLine(DrawChartInfo info, Point3[] pt3)
        {
            for (int k = 0; k < 4; k++)
                pt3[k].Transform(info.m, form1, info.cs);
            info.pta[0] = new PointF(pt3[0].X, pt3[0].Y);
            info.pta[1] = new PointF(pt3[1].X, pt3[1].Y);
            info.pta[2] = new PointF(pt3[2].X, pt3[2].Y);
            info.pta[3] = new PointF(pt3[3].X, pt3[3].Y);
            info.g.FillPolygon(info.aBrush, info.pta);
            if (IsColorMap)
            {
                Color color = AddColor(pt3[0], info.zmin, info.zmax);
                info.aPen = new Pen(color, info.ds.LineStyle.Thickness);
                info.aPen.DashStyle = info.ds.LineStyle.Pattern;
            }
            info.g.DrawPolygon(info.aPen, info.pta);
        }

        private void AddWaterfall(Graphics g, DataSeries ds, ChartStyle cs)
        {
            DrawChartInfo info = new DrawChartInfo(g, ds, cs);

            Point3[] pt3 = new Point3[info.pts.GetLength(0) + 2];
            PointF[] pta = new PointF[info.pts.GetLength(0) + 2];

            for (int j = 0; j < info.pts.GetLength(1); j++)
            {
                int jj = getIndex(info, j);
                for (int i = 0; i < info.pts.GetLength(0); i++)
                {
                    pt3[i + 1] = info.pts[i, jj];
                    if (i == 0)
                    {
                        pt3[0] = new Point3(pt3[i + 1].X, pt3[i + 1].Y, cs.ZMin, 1);
                    }
                    if (i == info.pts.GetLength(0) - 1)
                    {
                        pt3[info.pts.GetLength(0) + 1] = new Point3(pt3[i + 1].X,
                            pt3[i + 1].Y, cs.ZMin, 1);
                    }
                }

                drawWaterfall(g, ds, cs, info, pt3, pta);
            }
            info.aPen.Dispose();
            info.aBrush.Dispose();
        }

        private void drawWaterfall(Graphics g, DataSeries ds, ChartStyle cs, DrawChartInfo info, Point3[] pt3, PointF[] pta)
        {
            for (int i = 0; i < pt3.Length; i++)
            {
                pt3[i].Transform(info.m, form1, cs);
                pta[i] = new PointF(pt3[i].X, pt3[i].Y);
            }
            g.FillPolygon(info.aBrush, pta);

            for (int i = 1; i < pt3.Length; i++)
            {
                if (IsColorMap)
                {
                    Color color = AddColor(pt3[i], info.zmin, info.zmax);
                    info.aPen = new Pen(color, ds.LineStyle.Thickness);
                    info.aPen.DashStyle = ds.LineStyle.Pattern;
                }
                g.DrawLine(info.aPen, pta[i - 1], pta[i]);
            }
        }

        private int getIndex(DrawChartInfo info, int j)
        {
            int jj = j;
            if (info.cs.Elevation >= 0)
            {
                if (info.cs.Azimuth >= -90 && info.cs.Azimuth < 90)
                {
                    jj = info.pts.GetLength(1) - 1 - j;
                }
            }
            else if (info.cs.Elevation < 0)
            {
                jj = info.pts.GetLength(1) - 1 - j;
                if (info.cs.Azimuth >= -90 && info.cs.Azimuth < 90)
                {
                    jj = j;
                }
            }

            return jj;
        }

        private void AddSurface(Graphics g, DataSeries ds, ChartStyle cs, ChartStyle2D cs2d)
        {
            DrawChartInfo info = new DrawChartInfo(g, ds, cs);
            Point3[,] pts1 = new Point3[info.pts.GetLength(0), info.pts.GetLength(1)];

            // Perform transformation on points:
            for (int i = 0; i < info.pts.GetLength(0); i++)
            {
                for (int j = 0; j < info.pts.GetLength(1); j++)
                {
                    // Make a deep copy the points array:
                    pts1[i, j] = new Point3(info.pts[i, j].X, info.pts[i, j].Y, info.pts[i, j].Z, 1);
                    // Perform transformation on points:
                    info.pts[i, j].Transform(info.m, form1, cs);
                }
            }

            // Draw surface:
            drawSurface(info, IsInterp, pts1, cs2d);
        }

        private void drawSurface(DrawChartInfo info, bool IsInterp, Point3[,] pts1, ChartStyle2D cs2d)
        {
            for (int i = 0; i < info.pts.GetLength(0) - 1; i++)
            {
                for (int j = 0; j < info.pts.GetLength(1) - 1; j++)
                {
                    int ii = i;
                    if (info.cs.Azimuth >= -180 && info.cs.Azimuth < 0)
                    {
                        ii = info.pts.GetLength(0) - 2 - i;
                    }
                    if (IsInterp)
                    {
                        // Draw refined surface:
                        Point3[] points = new Point3[4];

                        points[0] = pts1[ii, j];
                        points[1] = pts1[ii, j + 1];
                        points[2] = pts1[ii + 1, j + 1];
                        points[3] = pts1[ii + 1, j];

                        Interp(info, cs2d, points, 1);

                        info.pta[0] = new PointF(info.pts[ii, j].X, info.pts[ii, j].Y);
                        info.pta[1] = new PointF(info.pts[ii, j + 1].X, info.pts[ii, j + 1].Y);
                        info.pta[2] = new PointF(info.pts[ii + 1, j + 1].X, info.pts[ii + 1, j + 1].Y);
                        info.pta[3] = new PointF(info.pts[ii + 1, j].X, info.pts[ii + 1, j].Y);
                        info.ds.LineStyle.IsVisible = true;//gsh add
                    }
                    else
                    {
                        info.pta[0] = new PointF(info.pts[ii, j].X, info.pts[ii, j].Y);
                        info.pta[1] = new PointF(info.pts[ii, j + 1].X, info.pts[ii, j + 1].Y);
                        info.pta[2] = new PointF(info.pts[ii + 1, j + 1].X, info.pts[ii + 1, j + 1].Y);
                        info.pta[3] = new PointF(info.pts[ii + 1, j].X, info.pts[ii + 1, j].Y);
                        Color color = AddColor(info.pts[ii, j], info.zmin, info.zmax);
                        info.aBrush = new SolidBrush(color);
                        info.g.FillPolygon(info.aBrush, info.pta);
                    }

                    if (info.ds.LineStyle.IsVisible)
                    {
                        info.g.DrawPolygon(info.aPen, info.pta);
                    }
                }
            }
        }


        public void AddColorBar(Graphics g, DataSeries ds, ChartStyle cs, ChartStyle2D cs2d)
        {
            if (cs.IsColorBar && IsColorMap)
            {
                Pen aPen = new Pen(Color.Black, 1);
                SolidBrush aBrush;
                StringFormat sFormat = new StringFormat();
                sFormat.Alignment = StringAlignment.Near;
                SizeF size = g.MeasureString("A", cs.TickFont);

                int x, y, width, height;
                Point3[] pts = new Point3[64];
                PointF[] pta = new PointF[4];
                float zmin, zmax;
                zmin = ds.ZDataMin();
                zmax = ds.ZDataMax();
                float dz = (zmax - zmin) / 63;
                x = 5 * form1.PlotPanel.Width / 6;
                y = form1.PlotPanel.Height / 10;
                width = form1.PlotPanel.Width / 25;
                height = 8 * form1.PlotPanel.Height / 10;

                // Add color bar:
                for (int i = 0; i < 64; i++)
                {
                    pts[i] = new Point3(x, y, zmin + i * dz, 1);
                }
                for (int i = 0; i < 63; i++)
                {
                    Color color = AddColor(pts[i], zmin, zmax);
                    aBrush = new SolidBrush(color);
                    float y1 = y + height - (pts[i].Z - zmin) * height / (zmax - zmin);
                    float y2 = y + height - (pts[i + 1].Z - zmin) * height / (zmax - zmin);
                    pta[0] = new PointF(x, y2);
                    pta[1] = new PointF(x + width, y2);
                    pta[2] = new PointF(x + width, y1);
                    pta[3] = new PointF(x, y1);
                    g.FillPolygon(aBrush, pta);
                }
                if (zmin < zmax)
                {
                    g.DrawRectangle(aPen, x, y, width, height);

                    // Add ticks and labels to the color bar:
                    float ticklength = 0.1f * width;
                    for (float z = zmin; z <= zmax; z = z + (zmax - zmin) / 6)
                    {
                        float yy = y + height - (z - zmin) * height / (zmax - zmin);
                        g.DrawLine(aPen, x, yy, x + ticklength, yy);
                        g.DrawLine(aPen, x + width, yy, x + width - ticklength, yy);
                        g.DrawString((Math.Round(z, 2)).ToString(), cs.TickFont, Brushes.Black,
                            new PointF(x + width + 5, yy - size.Height / 2), sFormat);
                    }
                }

            }
        }

        private Color AddColor(Point3 pt, float zmin, float zmax)
        {
            int colorLength = CMap.GetLength(0);
            int cindex = (int)Math.Round((colorLength * (pt.Z - zmin) +
                        (zmax - pt.Z)) / (zmax - zmin));
            if (cindex < 1)
                cindex = 1;
            if (cindex > colorLength)
                cindex = colorLength;
            Color color = Color.FromArgb(CMap[cindex - 1, 0],
                CMap[cindex - 1, 1], CMap[cindex - 1, 2],
                CMap[cindex - 1, 3]);
            return color;
        }

        private void Interp(DrawChartInfo info, ChartStyle2D cs2d, Point3[] pta, int flag)
        {
            PointF[] points = new PointF[4];
            int npoints = NumberInterp;
            Point3[,] pts = new Point3[npoints + 1, npoints + 1];
            Point3[,] pts1 = new Point3[npoints + 1, npoints + 1];

            float dx = (pta[2].X - pta[0].X) / npoints;
            float dy = (pta[2].Y - pta[0].Y) / npoints;

            if (flag == 1) // For Surface chart
            {
                setSurfaceChartPoints(info, pta, npoints, pts, dx, dy);

                drawSurfaceChart(info, points, npoints, pts);
            }
            else if (flag == 2) // For XYColor chart
            {
                setXYColorChartPoints(pta, npoints, pts, dx, dy);

                drawXYColorChart(info, cs2d, points, npoints, pts);
            }
            else if (flag == 3)  // For XYColor3D chart
            {
                setXYColor3DChartPoints(info, pta, npoints, pts, pts1, dx, dy);

                drawXYColor3DChart(info, points, npoints, pts, pts1);
            }
        }

        private void setSurfaceChartPoints(DrawChartInfo info, Point3[] pta, int npoints, Point3[,] pts, float dx, float dy)
        {
            for (int i = 0; i <= npoints; i++)
            {
                float x = pta[0].X + i * dx;
                for (int j = 0; j <= npoints; j++)
                {
                    float y = pta[0].Y + j * dy;
                    float C = (pta[2].Y - y) * ((pta[2].X - x) * pta[0].Z +
                        (x - pta[0].X) * pta[3].Z) / (pta[2].X - pta[0].X) / (pta[2].Y - pta[0].Y) +
                        (y - pta[0].Y) * ((pta[2].X - x) * pta[1].Z +
                        (x - pta[0].X) * pta[2].Z) / (pta[2].X - pta[0].X) / (pta[2].Y - pta[0].Y);
                    pts[i, j] = new Point3(x, y, C, 1);
                    pts[i, j].Transform(info.m, form1, info.cs);
                }
            }
        }

        private void setXYColorChartPoints(Point3[] pta, int npoints, Point3[,] pts, float dx, float dy)
        {
            for (int i = 0; i <= npoints; i++)
            {
                float x = pta[0].X + i * dx;
                for (int j = 0; j <= npoints; j++)
                {
                    float y = pta[0].Y + j * dy;
                    float C = (pta[2].Y - y) * ((pta[2].X - x) * pta[0].Z +
                        (x - pta[0].X) * pta[3].Z) / (pta[2].X - pta[0].X) / (pta[2].Y - pta[0].Y) +
                        (y - pta[0].Y) * ((pta[2].X - x) * pta[1].Z +
                        (x - pta[0].X) * pta[2].Z) / (pta[2].X - pta[0].X) / (pta[2].Y - pta[0].Y);
                    pts[i, j] = new Point3(x, y, C, 1);
                }
            }
        }

        private void setXYColor3DChartPoints(DrawChartInfo info, Point3[] pta, int npoints, Point3[,] pts, Point3[,] pts1, float dx, float dy)
        {
            for (int i = 0; i <= npoints; i++)
            {
                float x = pta[0].X + i * dx;
                for (int j = 0; j <= npoints; j++)
                {
                    float y = pta[0].Y + j * dy;
                    float C = (pta[2].Y - y) * ((pta[2].X - x) * pta[0].Z +
                        (x - pta[0].X) * pta[3].Z) / (pta[2].X - pta[0].X) / (pta[2].Y - pta[0].Y) +
                        (y - pta[0].Y) * ((pta[2].X - x) * pta[1].Z +
                        (x - pta[0].X) * pta[2].Z) / (pta[2].X - pta[0].X) / (pta[2].Y - pta[0].Y);
                    pts1[i, j] = new Point3(x, y, C, 1);
                    pts[i, j] = new Point3(x, y, info.cs.ZMin, 1);
                    pts[i, j].Transform(info.m, form1, info.cs);
                }
            }
        }

        private void drawSurfaceChart(DrawChartInfo info, PointF[] points, int npoints, Point3[,] pts)
        {
            for (int i = 0; i < npoints; i++)
            {
                for (int j = 0; j < npoints; j++)
                {
                    Color color = AddColor(pts[i, j], info.zmin, info.zmax);
                    SolidBrush aBrush = new SolidBrush(color);
                    points[0] = new PointF(pts[i, j].X, pts[i, j].Y);
                    points[1] = new PointF(pts[i + 1, j].X, pts[i + 1, j].Y);
                    points[2] = new PointF(pts[i + 1, j + 1].X, pts[i + 1, j + 1].Y);
                    points[3] = new PointF(pts[i, j + 1].X, pts[i, j + 1].Y);
                    info.g.FillPolygon(aBrush, points);
                    aBrush.Dispose();
                }
            }
        }

        private void drawXYColorChart(DrawChartInfo info, ChartStyle2D cs2d, PointF[] points, int npoints, Point3[,] pts)
        {
            for (int i = 0; i < npoints; i++)
            {
                for (int j = 0; j < npoints; j++)
                {
                    Color color = AddColor(pts[i, j], info.zmin, info.zmax);
                    SolidBrush aBrush = new SolidBrush(color);
                    points[0] = cs2d.Point2D(new PointF(pts[i, j].X, pts[i, j].Y), info.cs);
                    points[1] = cs2d.Point2D(new PointF(pts[i + 1, j].X, pts[i + 1, j].Y), info.cs);
                    points[2] = cs2d.Point2D(new PointF(pts[i + 1, j + 1].X, pts[i + 1, j + 1].Y), info.cs);
                    points[3] = cs2d.Point2D(new PointF(pts[i, j + 1].X, pts[i, j + 1].Y), info.cs);
                    info.g.FillPolygon(aBrush, points);
                    aBrush.Dispose();
                }
            }
        }

        private void drawXYColor3DChart(DrawChartInfo info, PointF[] points, int npoints, Point3[,] pts, Point3[,] pts1)
        {
            for (int i = 0; i < npoints; i++)
            {
                for (int j = 0; j < npoints; j++)
                {
                    Color color = AddColor(pts1[i, j], info.zmin, info.zmax);
                    SolidBrush aBrush = new SolidBrush(color);
                    points[0] = new PointF(pts[i, j].X, pts[i, j].Y);
                    points[1] = new PointF(pts[i + 1, j].X, pts[i + 1, j].Y);
                    points[2] = new PointF(pts[i + 1, j + 1].X, pts[i + 1, j + 1].Y);
                    points[3] = new PointF(pts[i, j + 1].X, pts[i, j + 1].Y);
                    info.g.FillPolygon(aBrush, points);
                    aBrush.Dispose();
                }
            }
        }
        #endregion

        public class DrawChartInfo
        {
            public DrawChartInfo(Graphics g, DataSeries ds, ChartStyle cs)
            {
                this.g = g;
                this.ds = ds;
                this.cs = cs;
                Init();
            }

            public Graphics g { get; set; }
            public DataSeries ds { get; set; }
            public ChartStyle cs { get; set; }

            public Pen aPen { get; set; }
            public SolidBrush aBrush { get; set; }
            public Matrix3 m { get; set; }
            public PointF[] pta { get; set; }
            public Point3[,] pts { get; set; }
            public float zmin { get; set; }
            public float zmax { get; set; }

            public void Init()
            {
                aPen = new Pen(ds.LineStyle.LineColor, ds.LineStyle.Thickness);
                aPen.DashStyle = ds.LineStyle.Pattern;
                aBrush = new SolidBrush(Color.White);

                m = Matrix3.AzimuthElevation(cs.Elevation, cs.Azimuth);
                pta = new PointF[4];
                pts = ds.PointArray;

                // Find the minumum and maximum z values:
                zmin = ds.ZDataMin();
                zmax = ds.ZDataMax();
            }
        }
    }
}
