﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTEpsfbDelayAna
{
    public class EpsfbCallInfo
    {
        public EpsfbCallInfo(string fileName)
        {
            this.FileName = fileName;
            CallResultDesc = "Fail";
        }
        public string FileName { get; set; }
        public List<Event> Events { get; set; } = new List<Event>();

        readonly List<int> attemptEvt = new List<int>() { };

        readonly List<int> establishedEvt = new List<int>() { 9338, 9339, 9350, 9351, 9358, 9359, 9366, 9367 };

        readonly List<int> blockDropEvt = new List<int>() { 9342, 9343, 9344, 9345, 9354, 9355, 9356, 9357, 
            9362, 9363, 9364, 9365, 9370, 9371, 9372, 9373 };

        readonly List<int> callEndEvt = new List<int>() { 9340, 9341, 9352, 9353 };

        readonly List<int> NrHOLteRequestEvt = new List<int>() { 9540, 9546 };

        readonly List<int> NrHOLteSuccessEvt = new List<int>() { 9541, 9542, 9547, 9548 };

        readonly List<int> LteHONrRequestEvt = new List<int>() { 9543, 9549 };

        readonly List<int> LteHONrSuccessEvt = new List<int>() { 9550, 9551, 9544, 9545 };


        /// <summary>
        /// 添加事件
        /// </summary>
        /// <param name="evt"></param>
        /// <returns>通话是否已有结果（含成功、失败）</returns>
        public bool AddEvent(Event evt)
        {
            Events.Add(evt);

            if (NrHOLteRequestEvt.Contains(evt.ID))
            {
                // MO/MT EPSFB, [IRAT NR_LTE HO Request]、[IRAT NR_L Redirect Request]
                EvtEpsfbCallRequest = evt;
            }
            else if (NrHOLteSuccessEvt.Contains(evt.ID))
            {
                // MO/MT EPSFB, [IRAT NR_LTE HO Success]、[IRAT NR_LTE HO Failure]、[IRAT NR_L Redirect Success]、[IRAT NR_L Redirect Failure]
                EvtRelease = evt;
                FBSuccTime = evt.DateTime;
                NowLng = evt.Longitude;
                NowLat = evt.Latitude;
                NowNetType = "EPSFB";
                NowLteTac = Convert.ToInt32(evt.LAC);
                NowLteEci = Convert.ToInt64(evt.CI);
            }
            else if (evt.ID == 9334 || evt.ID == 9335 || evt.ID == 9346 || evt.ID == 9347)
            {
                // EPSFB Call Attempt
                CallAttemptTime = evt.DateTime;
                EvtCallAttempt = evt;
                BeforeLng = evt.Longitude;
                BeforeLat = evt.Latitude;
                BeforeNetType = "EPSFB";
                BeforeNrTac = Convert.ToInt32(evt.LAC);
                BeforeNrNci = Convert.ToInt64(evt.CI);
            }
            else if (establishedEvt.Contains(evt.ID))
            {
                // MO/MT EPSFB Established
                CallEndTime = evt.DateTime;
                CallResultDesc = "Success";
                //return true;
            }
            else if (blockDropEvt.Contains(evt.ID) || callEndEvt.Contains(evt.ID))
            {
                // MO/MT EPSFB Block/Drop
                CallEndTime = evt.DateTime;
                CallResultDesc = "Fail";
            }
            else if (LteHONrRequestEvt.Contains(evt.ID))
            {
                // MO/MT EPSFB, [IRAT LTE_NR HO Request]、[IRAT L_NR Redirect Request]

            }
            else if (LteHONrSuccessEvt.Contains(evt.ID))
            {
                // MO/MT EPSFB, [IRAT LTE_NR HO Success]、[IRAT LTE_NR HO Failure]、[IRAT L_NR Redirect Success]、[IRAT L_NR Redirect Failure]
                FRSuccTime = evt.DateTime;
                AfterLng = evt.Longitude;
                AfterLat = evt.Latitude;
                AfterNrTac = Convert.ToInt32(evt.LAC);
                AfterNrNci = Convert.ToInt64(evt.CI);

                return true;
            }

            return false;
        }

        public Event EvtEpsfbCallRequest { get; set; }
        public Event EvtRelease { get; set; }
        public Event EvtCallAttempt { get; set; }


        // EPSFB 回落前 NR 小区
        public DateTime CallAttemptTime { get; set; }    // 呼叫请求时间
        public double? BeforeLng { get; set; }
        public double? BeforeLat { get; set; }
        public string BeforeNetType { get; set; }
        public int? BeforeNrTac { get; set; }
        public long? BeforeNrNci { get; set; }
        //public int? BeforeEnodebid { get; set; }
        //public int? BeforeCellid { get; set; }
        public float? BeforeSSRsrp { get; set; }


        // EPSFB 回落后 LTE 小区
        public DateTime FBSuccTime { get; set; }    // 回落成功时间(NR HO LTE Success)
        public double? NowLng { get; set; }
        public double? NowLat { get; set; }
        public string NowNetType { get; set; }
        public int? NowLteTac { get; set; }
        public long? NowLteEci { get; set; }
        //public int? NowEnodebid { get; set; }
        //public int? NowCellid { get; set; }
        public int? NowLteRsrp { get; set; }

        public DateTime CallEndTime { get; set; }    // 通话结束时间

        public string CallResultDesc { get; set; }


        // EPSFB 返回后 NR 小区
        public DateTime FRSuccTime { get; set; }    // 返回成功时间(LTE HO NR Success)
        public double? AfterLng { get; set; }
        public double? AfterLat { get; set; }
        public string AfterNetType { get; set; }
        public int? AfterNrTac { get; set; }
        public long? AfterNrNci { get; set; }
        //public int? AfterEnodebid { get; set; }
        //public int? AfterCellid { get; set; }
        public float? AfterSSRsrp { get; set; }




        public List<Message> Messages { get; set; } = new List<Message>();

        public Message MsgServiceNotification { get; set; }
        public Message MsgExtendedServicerequest { get; set; }
        public Message MsgAlerting { get; set; }
        public Message MsgRrcConnectionRelease { get; set; }
        public Message MsgFirstSysInfo { get; set; }
        public Message MsgCmServiceRequest { get; set; }
        public Message MsgCmServiceAccept { get; set; }
        public Message MsgAuthenticationRequest { get; set; }
        public Message MsgAuthenticationResponse { get; set; }
        public Message MsgSetup { get; set; }
        public Message MsgCallProceeding { get; set; }
        public Message MsgRrAssignmentCommand { get; set; }
        public Message MsgRrAssignmentComplete { get; set; }
        public Message MsgChannelModeModify { get; set; }
        public Message MsgChannelModeModifyAcknowledge { get; set; }

        public Message MsgMtPaging { get; set; }
        public List<Message> MsgMtPagingList { get; set; } = new List<Message>();
        public Message MsgMtPagingResponse { get; set; }
        public Message MsgMtCallConfirmed { get; set; }
        public Message MsgMtAuthenticationRequest { get; set; }
        public Message MsgMtAuthenticationResponse { get; set; }



        public int? BeforeGNodeBId
        {
            get
            {
                if (BeforeNrTac != null && BeforeNrNci != null && EvtEpsfbCallRequest != null)
                {
                    var nrCell = CellManager.GetInstance().GetNRCell(EvtEpsfbCallRequest.DateTime, (int)BeforeNrTac, (long)BeforeNrNci);
                    if (nrCell != null)
                    {
                        return nrCell.BelongBTS.BTSID;
                    }
                }
                return null;
            }
        }

        public int? BeforeCellId
        {
            get
            {
                if (BeforeNrTac != null && BeforeNrNci != null && EvtEpsfbCallRequest != null)
                {
                    var nrCell = CellManager.GetInstance().GetNRCell(EvtEpsfbCallRequest.DateTime, (int)BeforeNrTac, (long)BeforeNrNci);
                    if (nrCell != null)
                    {
                        return nrCell.CellID;
                    }
                }
                return null;
            }
        }


        public int? NowENodeBId
        {
            get
            {
                if (NowLteTac != null && NowLteEci != null && EvtEpsfbCallRequest != null)
                {
                    var lteCell = CellManager.GetInstance().GetLTECell(EvtEpsfbCallRequest.DateTime, (int)NowLteTac, (int)NowLteEci);
                    if (lteCell != null)
                    {
                        return lteCell.BelongBTS.BTSID;
                    }
                }
                return null;
            }
        }

        public int? NowCellId
        {
            get
            {
                if (NowLteTac != null && NowLteEci != null && EvtEpsfbCallRequest != null)
                {
                    var lteCell = CellManager.GetInstance().GetLTECell(EvtEpsfbCallRequest.DateTime, (int)NowLteTac, (int)NowLteEci);
                    if (lteCell != null)
                    {
                        return lteCell.CellID;
                    }
                }
                return null;
            }
        }


        public int? AfterGNodeBId
        {
            get
            {
                if (AfterNrTac != null && AfterNrNci != null && EvtEpsfbCallRequest != null)
                {
                    var nrCell = CellManager.GetInstance().GetNRCell(EvtEpsfbCallRequest.DateTime, (int)AfterNrTac, (long)AfterNrNci);
                    if (nrCell != null)
                    {
                        return nrCell.BelongBTS.BTSID;
                    }
                }
                return null;
            }
        }

        public int? AfterCellId
        {
            get
            {
                if (AfterNrTac != null && AfterNrNci != null && EvtEpsfbCallRequest != null)
                {
                    var nrCell = CellManager.GetInstance().GetNRCell(EvtEpsfbCallRequest.DateTime, (int)AfterNrTac, (long)AfterNrNci);
                    if (nrCell != null)
                    {
                        return nrCell.CellID;
                    }
                }
                return null;
            }
        }

    }
}
