﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanLowSinrRoad
{
    public partial class NRScanLowSinrRoadForm : MinCloseForm
    {
        public NRScanLowSinrRoadForm()
        {
            InitializeComponent();
        }

        public void FillData(List<NRLowSinrRoad> roadLst)
        {
            gridControl1.DataSource = roadLst;
            gridControl1.RefreshDataSource();
        }

        private void gridControl1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.SelectedRowsCount > 0)
            {
                NRLowSinrRoad info = gridView1.GetRow(gridView1.GetSelectedRows()[0]) as NRLowSinrRoad;
                MainModel.ClearDTData();

                foreach (TestPoint tp in info.TestPntLst)
                {
                    MainModel.DTDataManager.Add(tp);
                }

                MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.SinrFullThemeName);
                mModel.DrawFlyLines = true;
                MainModel.FireDTDataChanged(this);
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView1);
            }
            catch
            {
                MessageBox.Show("导出到xls失败!");
            }
        }
    }
}
