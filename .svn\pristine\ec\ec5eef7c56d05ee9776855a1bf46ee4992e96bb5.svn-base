﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid;
using DevExpress.XtraEditors;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMComparePoorRxQualityRoadForm : MinCloseForm
    {
        public GSMComparePoorRxQualityRoadForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }

        GSMComparePoorRxQualityRoadLayer layer = null;
        public void FillData(List<GSMComparePoorRxQualityRoadGrid> period1Info, List<GSMComparePoorRxQualityRoadGrid> period2RepeatGrid, List<GSMComparePoorRxQualityRoadGrid> period2NewGrid)
        {
            gridControlPeriod1Grid.DataSource = period1Info;
            gridControlPeriod1Grid.RefreshDataSource();
            gridControlRepeatGrid.DataSource = period2RepeatGrid;
            gridControlRepeatGrid.RefreshDataSource();
            gridControlNewGrid.DataSource = period2NewGrid;
            gridControlNewGrid.RefreshDataSource();

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            MainModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(GSMComparePoorRxQualityRoadLayer));
            if (cLayer == null)
            {
                layer = new GSMComparePoorRxQualityRoadLayer(mf.GetMapOperation(), "质差路段栅格图层");
                mf.AddTempCustomLayer(layer);
            }
            else
            {
                layer = cLayer as GSMComparePoorRxQualityRoadLayer;
            }
            layer.Peroid1PoorGrids = period1Info;
            layer.Peroid2RepeatPoorGrids = period2RepeatGrid;
            layer.Peroid2NewPoorGrids = period2NewGrid;
            layer.Invalidate();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            if (gv==null)
            {
                return;
            }
            int[] rs = gv.GetSelectedRows();
            if (rs.Length > 0)
            {
                MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
                if (mf != null)
                {
                    GSMComparePoorRxQualityRoadGrid grid = gv.GetRow(rs[0]) as GSMComparePoorRxQualityRoadGrid;
                    if (grid != null)
                    {
                        MainModel.GetInstance().ClearDTData();
                        foreach (TestPoint tp in grid.sampleLst)
                        {
                            MainModel.GetInstance().DTDataManager.Add(tp);
                        }
                        MainModel.GetInstance().FireDTDataChanged(this);
                        MainModel.FireSetDefaultMapSerialTheme("GSM RxQual");
                    }
                }
            }
        }

        private void checkPeriod1_CheckedChanged(object sender, EventArgs e)
        {
            if (layer!=null)
            {
                layer.ShowPeriod1Grid = checkPeriod1.Checked;
                layer.Invalidate();
            }
        }

        private void checkNew_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2NewGrid = checkNew.Checked;
                layer.Invalidate();
            }
        }

        private void checkRepeat_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2RepeatGrid = checkRepeat.Checked;
                layer.Invalidate();
            }
        }

        private void colorPeriod1_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period1GridColor = colorPeriod1.Color;
                layer.Invalidate();
            }
        }

        private void colorNew_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2NewGridColor = colorNew.Color;
                layer.Invalidate();
            }
        }

        private void colorRepeat_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2RepeatGridColor = colorRepeat.Color;
                layer.Invalidate();
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView2);
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView3);
        }
    }
}
