﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.ComponentModel;

namespace MasterCom.MControls
{
    [Serializable()]
    public class ColorRange
    {
        [NonSerialized]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public float minValue;
        [NonSerialized]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public float maxValue;
        [NonSerialized]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Color color;
        [NonSerialized]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string desInfo;
        [NonSerialized]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool visible = true;
        public ColorRange()
        {

        }
        public ColorRange(float min, float max,Color clr)
        {
            this.minValue = min;
            this.maxValue = max;
            this.color = clr;
        }
        public ColorRange(float min, float max, Color clr, string desInfo)
        {
            this.minValue = min;
            this.maxValue = max;
            this.color = clr;
            this.desInfo = desInfo;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["MinV"] = minValue;
                param["MaxV"] = maxValue;
                param["ColorR"] = (int)(color.R);
                param["ColorG"] = (int)(color.G);
                param["ColorB"] = (int)(color.B);
                param["desInfo"] = desInfo;
                return param;
            }
            set
            {
                minValue = (float)value["MinV"];
                maxValue = (float)value["MaxV"];
                int r = (int)value["ColorR"];
                int g = (int)value["ColorG"];
                int b = (int)value["ColorB"];
                if (value.ContainsKey("desInfo"))
                {
                    desInfo = (string)value["desInfo"];
                }
                else 
                {
                    desInfo = "";
                }
                color = Color.FromArgb(r, g, b);
            }
        }

        public String GetRangeDesc(bool last)
        {
            return string.Format("{0:F2} <= x <"+(last?"=":"")+" {1:F2}", minValue, maxValue); 
        }

        public ColorRange newInstance()
        {
            ColorRange cr = new ColorRange();
            cr.minValue = this.minValue;
            cr.maxValue = this.maxValue;
            cr.color = this.color;
            cr.desInfo = this.desInfo;
            return cr;
        }
    }
}
