﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTETMQueryByRegion : LTETMQueryBase
    {
        private LTETMQueryByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static LTETMQueryByRegion instance = null;
        public static LTETMQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTETMQueryByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "TM占比分析(按区域)"; }
        }

    }

    public class LTETMQueryByRegion_FDD : LTETMQueryBase_FDD
    {
        private static LTETMQueryByRegion_FDD instance = null;
        public static LTETMQueryByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTETMQueryByRegion_FDD();
                    }
                }
            }
            return instance;
        }

        private LTETMQueryByRegion_FDD()
            : base()
        {
            FilterSampleByRegion = true;
        }

        public override string Name
        {
            get { return "LTE_FDD TM占比分析(按区域)"; }
        }
    }
}
