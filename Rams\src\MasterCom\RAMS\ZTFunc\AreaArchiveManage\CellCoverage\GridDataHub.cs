﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public class GridDataHub : GridUnitBase
    {
        public GridDataHub()
        {
        }
        public GridDataHub(double ltLng, double ltLat)
            : base(ltLng, ltLat)
        {
        }

        readonly KPIDataGroup dataGrp = new KPIDataGroup(null);
        public KPIDataGroup DataGroup
        {
            get { return dataGrp; }
        }

        public void AddStatData(FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            dataGrp.AddStatData(fileInfo, data, saveAsGrid);
        }

    }
}
