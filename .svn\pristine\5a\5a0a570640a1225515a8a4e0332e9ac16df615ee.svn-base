﻿namespace MasterCom.RAMS.KPI_Statistics.ReportForm
{
    partial class CellOptionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.ColumnHeader columnHeader2;
            this.tabCtrl = new System.Windows.Forms.TabControl();
            this.pageFormula = new System.Windows.Forms.TabPage();
            this.layoutPnlC = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.rbtnChinaTelecom = new System.Windows.Forms.RadioButton();
            this.rbtnChinaUnicom = new System.Windows.Forms.RadioButton();
            this.rbtnChinaMobile = new System.Windows.Forms.RadioButton();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.rbtnMt = new System.Windows.Forms.RadioButton();
            this.rbtnMo = new System.Windows.Forms.RadioButton();
            this.rbtnAll = new System.Windows.Forms.RadioButton();
            this.grpTitle = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.tbxTitle = new System.Windows.Forms.TextBox();
            this.rBtnLeft = new System.Windows.Forms.RadioButton();
            this.rBtnTop = new System.Windows.Forms.RadioButton();
            this.rBtnNoTitle = new System.Windows.Forms.RadioButton();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.groupBoxFileName = new System.Windows.Forms.GroupBox();
            this.ccbePort = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.cbxfileNumType = new System.Windows.Forms.ComboBox();
            this.btnClearPort = new System.Windows.Forms.Button();
            this.label6 = new System.Windows.Forms.Label();
            this.label_fileName = new System.Windows.Forms.Label();
            this.textBoxFileName = new System.Windows.Forms.TextBox();
            this.checkBoxFileName = new System.Windows.Forms.CheckBox();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.listViewService = new System.Windows.Forms.ListView();
            this.lbSvCount = new System.Windows.Forms.Label();
            this.btnPopupService = new System.Windows.Forms.Button();
            this.chkGridAvg = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.kpiExpEditor = new MasterCom.RAMS.Util.KPIFormulaEditor();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.numericUpDownDeciNum = new System.Windows.Forms.NumericUpDown();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.clrCellFore = new DevExpress.XtraEditors.ColorEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numRngMax = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numRngMin = new System.Windows.Forms.NumericUpDown();
            this.rngColorPnl = new MasterCom.RAMS.CQT.ScoreRangeColorSettingPanel();
            this.clrCellBkStatic = new DevExpress.XtraEditors.ColorEdit();
            this.rBtnDynamic = new System.Windows.Forms.RadioButton();
            this.rBtnStaticBk = new System.Windows.Forms.RadioButton();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.clrTitleBk = new DevExpress.XtraEditors.ColorEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.clrTitle = new DevExpress.XtraEditors.ColorEdit();
            this.label8 = new System.Windows.Forms.Label();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.toolStripDropDownService = new System.Windows.Forms.ToolStripDropDown();
            columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.tabCtrl.SuspendLayout();
            this.pageFormula.SuspendLayout();
            this.layoutPnlC.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.grpTitle.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBoxFileName.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ccbePort.Properties)).BeginInit();
            this.groupBox7.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDeciNum)).BeginInit();
            this.groupBox10.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.clrCellFore.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.clrCellBkStatic.Properties)).BeginInit();
            this.groupBox11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.clrTitleBk.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.clrTitle.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // columnHeader2
            // 
            columnHeader2.Width = 140;
            // 
            // tabCtrl
            // 
            this.tabCtrl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabCtrl.Controls.Add(this.pageFormula);
            this.tabCtrl.Controls.Add(this.tabPage3);
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedIndex = 0;
            this.tabCtrl.Size = new System.Drawing.Size(904, 617);
            this.tabCtrl.TabIndex = 9;
            // 
            // pageFormula
            // 
            this.pageFormula.Controls.Add(this.layoutPnlC);
            this.pageFormula.Location = new System.Drawing.Point(4, 23);
            this.pageFormula.Name = "pageFormula";
            this.pageFormula.Padding = new System.Windows.Forms.Padding(3);
            this.pageFormula.Size = new System.Drawing.Size(896, 590);
            this.pageFormula.TabIndex = 1;
            this.pageFormula.Text = "内容";
            this.pageFormula.UseVisualStyleBackColor = true;
            // 
            // layoutPnlC
            // 
            this.layoutPnlC.ColumnCount = 3;
            this.layoutPnlC.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 32.13483F));
            this.layoutPnlC.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20.11236F));
            this.layoutPnlC.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 47.64045F));
            this.layoutPnlC.Controls.Add(this.groupBox3, 0, 0);
            this.layoutPnlC.Controls.Add(this.groupBox6, 1, 0);
            this.layoutPnlC.Controls.Add(this.grpTitle, 0, 1);
            this.layoutPnlC.Controls.Add(this.groupBox5, 2, 0);
            this.layoutPnlC.Controls.Add(this.groupBox2, 0, 2);
            this.layoutPnlC.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutPnlC.Location = new System.Drawing.Point(3, 3);
            this.layoutPnlC.Name = "layoutPnlC";
            this.layoutPnlC.RowCount = 3;
            this.layoutPnlC.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 76F));
            this.layoutPnlC.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 90F));
            this.layoutPnlC.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.layoutPnlC.Size = new System.Drawing.Size(890, 584);
            this.layoutPnlC.TabIndex = 21;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.rbtnChinaTelecom);
            this.groupBox3.Controls.Add(this.rbtnChinaUnicom);
            this.groupBox3.Controls.Add(this.rbtnChinaMobile);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Location = new System.Drawing.Point(3, 3);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(280, 70);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "运营商";
            // 
            // rbtnChinaTelecom
            // 
            this.rbtnChinaTelecom.AutoSize = true;
            this.rbtnChinaTelecom.Location = new System.Drawing.Point(194, 31);
            this.rbtnChinaTelecom.Name = "rbtnChinaTelecom";
            this.rbtnChinaTelecom.Size = new System.Drawing.Size(71, 16);
            this.rbtnChinaTelecom.TabIndex = 2;
            this.rbtnChinaTelecom.TabStop = true;
            this.rbtnChinaTelecom.Text = "中国电信";
            this.rbtnChinaTelecom.UseVisualStyleBackColor = true;
            // 
            // rbtnChinaUnicom
            // 
            this.rbtnChinaUnicom.AutoSize = true;
            this.rbtnChinaUnicom.Location = new System.Drawing.Point(105, 31);
            this.rbtnChinaUnicom.Name = "rbtnChinaUnicom";
            this.rbtnChinaUnicom.Size = new System.Drawing.Size(71, 16);
            this.rbtnChinaUnicom.TabIndex = 1;
            this.rbtnChinaUnicom.TabStop = true;
            this.rbtnChinaUnicom.Text = "中国联通";
            this.rbtnChinaUnicom.UseVisualStyleBackColor = true;
            // 
            // rbtnChinaMobile
            // 
            this.rbtnChinaMobile.AutoSize = true;
            this.rbtnChinaMobile.Checked = true;
            this.rbtnChinaMobile.Location = new System.Drawing.Point(16, 31);
            this.rbtnChinaMobile.Name = "rbtnChinaMobile";
            this.rbtnChinaMobile.Size = new System.Drawing.Size(71, 16);
            this.rbtnChinaMobile.TabIndex = 0;
            this.rbtnChinaMobile.TabStop = true;
            this.rbtnChinaMobile.Text = "中国移动";
            this.rbtnChinaMobile.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.rbtnMt);
            this.groupBox6.Controls.Add(this.rbtnMo);
            this.groupBox6.Controls.Add(this.rbtnAll);
            this.groupBox6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox6.Location = new System.Drawing.Point(289, 3);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(173, 70);
            this.groupBox6.TabIndex = 1;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "主被叫";
            // 
            // rbtnMt
            // 
            this.rbtnMt.AutoSize = true;
            this.rbtnMt.Location = new System.Drawing.Point(120, 31);
            this.rbtnMt.Name = "rbtnMt";
            this.rbtnMt.Size = new System.Drawing.Size(47, 16);
            this.rbtnMt.TabIndex = 12;
            this.rbtnMt.TabStop = true;
            this.rbtnMt.Text = "被叫";
            this.rbtnMt.UseVisualStyleBackColor = true;
            // 
            // rbtnMo
            // 
            this.rbtnMo.AutoSize = true;
            this.rbtnMo.Location = new System.Drawing.Point(68, 32);
            this.rbtnMo.Name = "rbtnMo";
            this.rbtnMo.Size = new System.Drawing.Size(47, 16);
            this.rbtnMo.TabIndex = 11;
            this.rbtnMo.TabStop = true;
            this.rbtnMo.Text = "主叫";
            this.rbtnMo.UseVisualStyleBackColor = true;
            // 
            // rbtnAll
            // 
            this.rbtnAll.AutoSize = true;
            this.rbtnAll.Checked = true;
            this.rbtnAll.Location = new System.Drawing.Point(17, 32);
            this.rbtnAll.Name = "rbtnAll";
            this.rbtnAll.Size = new System.Drawing.Size(47, 16);
            this.rbtnAll.TabIndex = 10;
            this.rbtnAll.TabStop = true;
            this.rbtnAll.Text = "不限";
            this.rbtnAll.UseVisualStyleBackColor = true;
            // 
            // grpTitle
            // 
            this.layoutPnlC.SetColumnSpan(this.grpTitle, 2);
            this.grpTitle.Controls.Add(this.label2);
            this.grpTitle.Controls.Add(this.tbxTitle);
            this.grpTitle.Controls.Add(this.rBtnLeft);
            this.grpTitle.Controls.Add(this.rBtnTop);
            this.grpTitle.Controls.Add(this.rBtnNoTitle);
            this.grpTitle.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpTitle.Location = new System.Drawing.Point(3, 79);
            this.grpTitle.Name = "grpTitle";
            this.grpTitle.Size = new System.Drawing.Size(459, 84);
            this.grpTitle.TabIndex = 2;
            this.grpTitle.TabStop = false;
            this.grpTitle.Text = "标题";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(7, 51);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "文字：";
            // 
            // tbxTitle
            // 
            this.tbxTitle.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbxTitle.Enabled = false;
            this.tbxTitle.Location = new System.Drawing.Point(54, 48);
            this.tbxTitle.Name = "tbxTitle";
            this.tbxTitle.Size = new System.Drawing.Size(387, 21);
            this.tbxTitle.TabIndex = 3;
            // 
            // rBtnLeft
            // 
            this.rBtnLeft.AutoSize = true;
            this.rBtnLeft.Location = new System.Drawing.Point(289, 14);
            this.rBtnLeft.Name = "rBtnLeft";
            this.rBtnLeft.Size = new System.Drawing.Size(71, 16);
            this.rBtnLeft.TabIndex = 2;
            this.rBtnLeft.Text = "往左一格";
            this.rBtnLeft.UseVisualStyleBackColor = true;
            // 
            // rBtnTop
            // 
            this.rBtnTop.AutoSize = true;
            this.rBtnTop.Location = new System.Drawing.Point(171, 14);
            this.rBtnTop.Name = "rBtnTop";
            this.rBtnTop.Size = new System.Drawing.Size(71, 16);
            this.rBtnTop.TabIndex = 1;
            this.rBtnTop.Text = "往上一格";
            this.rBtnTop.UseVisualStyleBackColor = true;
            // 
            // rBtnNoTitle
            // 
            this.rBtnNoTitle.AutoSize = true;
            this.rBtnNoTitle.Checked = true;
            this.rBtnNoTitle.Location = new System.Drawing.Point(54, 14);
            this.rBtnNoTitle.Name = "rBtnNoTitle";
            this.rBtnNoTitle.Size = new System.Drawing.Size(59, 16);
            this.rBtnNoTitle.TabIndex = 0;
            this.rBtnNoTitle.TabStop = true;
            this.rBtnNoTitle.Text = "不输入";
            this.rBtnNoTitle.UseVisualStyleBackColor = true;
            this.rBtnNoTitle.CheckedChanged += new System.EventHandler(this.rBtnNoTitle_CheckedChanged);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.groupBoxFileName);
            this.groupBox5.Controls.Add(this.groupBox7);
            this.groupBox5.Controls.Add(this.chkGridAvg);
            this.groupBox5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox5.Location = new System.Drawing.Point(468, 3);
            this.groupBox5.Name = "groupBox5";
            this.layoutPnlC.SetRowSpan(this.groupBox5, 2);
            this.groupBox5.Size = new System.Drawing.Size(419, 160);
            this.groupBox5.TabIndex = 8;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "高级";
            // 
            // groupBoxFileName
            // 
            this.groupBoxFileName.Controls.Add(this.ccbePort);
            this.groupBoxFileName.Controls.Add(this.cbxfileNumType);
            this.groupBoxFileName.Controls.Add(this.btnClearPort);
            this.groupBoxFileName.Controls.Add(this.label6);
            this.groupBoxFileName.Controls.Add(this.label_fileName);
            this.groupBoxFileName.Controls.Add(this.textBoxFileName);
            this.groupBoxFileName.Controls.Add(this.checkBoxFileName);
            this.groupBoxFileName.Location = new System.Drawing.Point(31, 77);
            this.groupBoxFileName.Name = "groupBoxFileName";
            this.groupBoxFileName.Size = new System.Drawing.Size(382, 78);
            this.groupBoxFileName.TabIndex = 32;
            this.groupBoxFileName.TabStop = false;
            // 
            // ccbePort
            // 
            this.ccbePort.EditValue = "";
            this.ccbePort.Enabled = false;
            this.ccbePort.Location = new System.Drawing.Point(55, 20);
            this.ccbePort.Name = "ccbePort";
            this.ccbePort.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ccbePort.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("1", "1"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2", "2"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("3", "3"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("4", "4"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("5", "5"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("6", "6"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("7", "7"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("8", "8"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("9", "9")});
            this.ccbePort.Size = new System.Drawing.Size(145, 21);
            this.ccbePort.TabIndex = 48;
            this.ccbePort.EditValueChanged += new System.EventHandler(this.ccbePort_EditValueChanged);
            // 
            // cbxfileNumType
            // 
            this.cbxfileNumType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxfileNumType.FormattingEnabled = true;
            this.cbxfileNumType.Items.AddRange(new object[] {
            "RCU",
            "ATU"});
            this.cbxfileNumType.Location = new System.Drawing.Point(206, 19);
            this.cbxfileNumType.Name = "cbxfileNumType";
            this.cbxfileNumType.Size = new System.Drawing.Size(53, 22);
            this.cbxfileNumType.TabIndex = 47;
            // 
            // btnClearPort
            // 
            this.btnClearPort.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.btnClearPort.Location = new System.Drawing.Point(272, 17);
            this.btnClearPort.Name = "btnClearPort";
            this.btnClearPort.Size = new System.Drawing.Size(53, 22);
            this.btnClearPort.TabIndex = 13;
            this.btnClearPort.Text = "清空";
            this.btnClearPort.UseVisualStyleBackColor = true;
            this.btnClearPort.Click += new System.EventHandler(this.btnClearPort_Click);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(5, 25);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(41, 12);
            this.label6.TabIndex = 12;
            this.label6.Text = "端口号";
            // 
            // label_fileName
            // 
            this.label_fileName.AutoSize = true;
            this.label_fileName.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(34)))), ((int)(((byte)(57)))), ((int)(((byte)(94)))));
            this.label_fileName.Location = new System.Drawing.Point(7, 0);
            this.label_fileName.Name = "label_fileName";
            this.label_fileName.Size = new System.Drawing.Size(41, 12);
            this.label_fileName.TabIndex = 2;
            this.label_fileName.Text = "文件名";
            // 
            // textBoxFileName
            // 
            this.textBoxFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileName.Enabled = false;
            this.textBoxFileName.Location = new System.Drawing.Point(6, 50);
            this.textBoxFileName.Name = "textBoxFileName";
            this.textBoxFileName.Size = new System.Drawing.Size(369, 21);
            this.textBoxFileName.TabIndex = 0;
            // 
            // checkBoxFileName
            // 
            this.checkBoxFileName.AutoSize = true;
            this.checkBoxFileName.Location = new System.Drawing.Point(55, 0);
            this.checkBoxFileName.Name = "checkBoxFileName";
            this.checkBoxFileName.Padding = new System.Windows.Forms.Padding(2, 0, 5, 0);
            this.checkBoxFileName.Size = new System.Drawing.Size(22, 14);
            this.checkBoxFileName.TabIndex = 1;
            this.checkBoxFileName.UseVisualStyleBackColor = true;
            this.checkBoxFileName.CheckedChanged += new System.EventHandler(this.checkBoxFileName_CheckedChanged);
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.groupBox7.Controls.Add(this.listViewService);
            this.groupBox7.Controls.Add(this.lbSvCount);
            this.groupBox7.Controls.Add(this.btnPopupService);
            this.groupBox7.Location = new System.Drawing.Point(133, 18);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(280, 52);
            this.groupBox7.TabIndex = 31;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "业务类型限定";
            // 
            // listViewService
            // 
            this.listViewService.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader2});
            this.listViewService.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewService.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.listViewService.Location = new System.Drawing.Point(3, 17);
            this.listViewService.Name = "listViewService";
            this.listViewService.Size = new System.Drawing.Size(274, 32);
            this.listViewService.TabIndex = 18;
            this.toolTip.SetToolTip(this.listViewService, "test");
            this.listViewService.UseCompatibleStateImageBehavior = false;
            this.listViewService.View = System.Windows.Forms.View.List;
            // 
            // lbSvCount
            // 
            this.lbSvCount.AutoSize = true;
            this.lbSvCount.BackColor = System.Drawing.Color.White;
            this.lbSvCount.Font = new System.Drawing.Font("宋体", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbSvCount.ForeColor = System.Drawing.Color.Black;
            this.lbSvCount.Location = new System.Drawing.Point(111, 1);
            this.lbSvCount.Name = "lbSvCount";
            this.lbSvCount.Size = new System.Drawing.Size(23, 11);
            this.lbSvCount.TabIndex = 17;
            this.lbSvCount.Text = "[1]";
            // 
            // btnPopupService
            // 
            this.btnPopupService.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopupService.BackColor = System.Drawing.Color.White;
            this.btnPopupService.FlatAppearance.BorderSize = 0;
            this.btnPopupService.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.btnPopupService.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPopupService.Location = new System.Drawing.Point(213, 0);
            this.btnPopupService.Name = "btnPopupService";
            this.btnPopupService.Size = new System.Drawing.Size(61, 20);
            this.btnPopupService.TabIndex = 4;
            this.btnPopupService.Text = "请选择↓";
            this.btnPopupService.UseVisualStyleBackColor = true;
            this.btnPopupService.Click += new System.EventHandler(this.btnPopupService_Click);
            // 
            // chkGridAvg
            // 
            this.chkGridAvg.AutoSize = true;
            this.chkGridAvg.Location = new System.Drawing.Point(31, 32);
            this.chkGridAvg.Name = "chkGridAvg";
            this.chkGridAvg.Size = new System.Drawing.Size(96, 16);
            this.chkGridAvg.TabIndex = 0;
            this.chkGridAvg.Text = "区域栅格均化";
            this.toolTip.SetToolTip(this.chkGridAvg, "勾选此项后，区域KPI结果将取栅格的平均值");
            this.chkGridAvg.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.layoutPnlC.SetColumnSpan(this.groupBox2, 3);
            this.groupBox2.Controls.Add(this.kpiExpEditor);
            this.groupBox2.Location = new System.Drawing.Point(3, 169);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(884, 412);
            this.groupBox2.TabIndex = 7;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "文字或表达式";
            // 
            // kpiExpEditor
            // 
            this.kpiExpEditor.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.kpiExpEditor.AutoSize = true;
            this.kpiExpEditor.Formula = "";
            this.kpiExpEditor.Location = new System.Drawing.Point(3, 17);
            this.kpiExpEditor.Name = "kpiExpEditor";
            this.kpiExpEditor.Size = new System.Drawing.Size(878, 395);
            this.kpiExpEditor.TabIndex = 1;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.groupBox8);
            this.tabPage3.Controls.Add(this.groupBox10);
            this.tabPage3.Controls.Add(this.groupBox11);
            this.tabPage3.Location = new System.Drawing.Point(4, 23);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(896, 590);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "样式";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // groupBox8
            // 
            this.groupBox8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox8.Controls.Add(this.label4);
            this.groupBox8.Controls.Add(this.numericUpDownDeciNum);
            this.groupBox8.Location = new System.Drawing.Point(9, 232);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(876, 72);
            this.groupBox8.TabIndex = 26;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "格式";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(46, 36);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(95, 12);
            this.label4.TabIndex = 30;
            this.label4.Text = "取值小数点位数:";
            // 
            // numericUpDownDeciNum
            // 
            this.numericUpDownDeciNum.Location = new System.Drawing.Point(147, 31);
            this.numericUpDownDeciNum.Maximum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownDeciNum.Name = "numericUpDownDeciNum";
            this.numericUpDownDeciNum.Size = new System.Drawing.Size(56, 21);
            this.numericUpDownDeciNum.TabIndex = 29;
            this.numericUpDownDeciNum.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // groupBox10
            // 
            this.groupBox10.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox10.Controls.Add(this.groupBox1);
            this.groupBox10.Controls.Add(this.groupBox4);
            this.groupBox10.Location = new System.Drawing.Point(9, 15);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(876, 211);
            this.groupBox10.TabIndex = 25;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "单元格颜色设置";
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.clrCellFore);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Location = new System.Drawing.Point(6, 20);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(253, 186);
            this.groupBox1.TabIndex = 27;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "前景颜色";
            // 
            // clrCellFore
            // 
            this.clrCellFore.EditValue = System.Drawing.Color.Black;
            this.clrCellFore.Location = new System.Drawing.Point(111, 25);
            this.clrCellFore.Name = "clrCellFore";
            this.clrCellFore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.clrCellFore.Properties.ExportMode = DevExpress.XtraEditors.Repository.ExportMode.Value;
            this.clrCellFore.Properties.ShowWebColors = false;
            this.clrCellFore.Size = new System.Drawing.Size(46, 21);
            this.clrCellFore.TabIndex = 24;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(40, 30);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "文字颜色：";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.numRngMax);
            this.groupBox4.Controls.Add(this.label3);
            this.groupBox4.Controls.Add(this.numRngMin);
            this.groupBox4.Controls.Add(this.rngColorPnl);
            this.groupBox4.Controls.Add(this.clrCellBkStatic);
            this.groupBox4.Controls.Add(this.rBtnDynamic);
            this.groupBox4.Controls.Add(this.rBtnStaticBk);
            this.groupBox4.Location = new System.Drawing.Point(265, 20);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(605, 186);
            this.groupBox4.TabIndex = 23;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "背景颜色";
            // 
            // numRngMax
            // 
            this.numRngMax.Enabled = false;
            this.numRngMax.Location = new System.Drawing.Point(456, 20);
            this.numRngMax.Name = "numRngMax";
            this.numRngMax.Size = new System.Drawing.Size(96, 21);
            this.numRngMax.TabIndex = 28;
            this.numRngMax.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numRngMax.Validating += new System.ComponentModel.CancelEventHandler(this.numRngMax_Validating);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(385, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "≤指标值≤";
            // 
            // numRngMin
            // 
            this.numRngMin.Enabled = false;
            this.numRngMin.Location = new System.Drawing.Point(283, 20);
            this.numRngMin.Name = "numRngMin";
            this.numRngMin.Size = new System.Drawing.Size(96, 21);
            this.numRngMin.TabIndex = 28;
            this.numRngMin.Validating += new System.ComponentModel.CancelEventHandler(this.numRngMin_Validating);
            // 
            // rngColorPnl
            // 
            this.rngColorPnl.DescColumnsVisible = true;
            this.rngColorPnl.Enabled = false;
            this.rngColorPnl.Location = new System.Drawing.Point(229, 47);
            this.rngColorPnl.Name = "rngColorPnl";
            this.rngColorPnl.Size = new System.Drawing.Size(370, 133);
            this.rngColorPnl.TabIndex = 0;
            // 
            // clrCellBkStatic
            // 
            this.clrCellBkStatic.EditValue = System.Drawing.Color.White;
            this.clrCellBkStatic.Location = new System.Drawing.Point(77, 20);
            this.clrCellBkStatic.Name = "clrCellBkStatic";
            this.clrCellBkStatic.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.clrCellBkStatic.Properties.ShowWebColors = false;
            this.clrCellBkStatic.Size = new System.Drawing.Size(46, 21);
            this.clrCellBkStatic.TabIndex = 24;
            // 
            // rBtnDynamic
            // 
            this.rBtnDynamic.AutoSize = true;
            this.rBtnDynamic.Location = new System.Drawing.Point(230, 23);
            this.rBtnDynamic.Name = "rBtnDynamic";
            this.rBtnDynamic.Size = new System.Drawing.Size(47, 16);
            this.rBtnDynamic.TabIndex = 26;
            this.rBtnDynamic.Text = "动态";
            this.rBtnDynamic.UseVisualStyleBackColor = true;
            // 
            // rBtnStaticBk
            // 
            this.rBtnStaticBk.AutoSize = true;
            this.rBtnStaticBk.Checked = true;
            this.rBtnStaticBk.Location = new System.Drawing.Point(15, 23);
            this.rBtnStaticBk.Name = "rBtnStaticBk";
            this.rBtnStaticBk.Size = new System.Drawing.Size(47, 16);
            this.rBtnStaticBk.TabIndex = 27;
            this.rBtnStaticBk.TabStop = true;
            this.rBtnStaticBk.Text = "固定";
            this.rBtnStaticBk.UseVisualStyleBackColor = true;
            this.rBtnStaticBk.CheckedChanged += new System.EventHandler(this.rBtnStaticBk_CheckedChanged);
            // 
            // groupBox11
            // 
            this.groupBox11.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox11.Controls.Add(this.clrTitleBk);
            this.groupBox11.Controls.Add(this.label1);
            this.groupBox11.Controls.Add(this.clrTitle);
            this.groupBox11.Controls.Add(this.label8);
            this.groupBox11.Location = new System.Drawing.Point(9, 321);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(876, 254);
            this.groupBox11.TabIndex = 25;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "标题";
            // 
            // clrTitleBk
            // 
            this.clrTitleBk.EditValue = System.Drawing.Color.White;
            this.clrTitleBk.Location = new System.Drawing.Point(281, 35);
            this.clrTitleBk.Name = "clrTitleBk";
            this.clrTitleBk.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.clrTitleBk.Properties.ShowWebColors = false;
            this.clrTitleBk.Size = new System.Drawing.Size(46, 21);
            this.clrTitleBk.TabIndex = 24;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(46, 40);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "文字颜色：";
            // 
            // clrTitle
            // 
            this.clrTitle.EditValue = System.Drawing.Color.Black;
            this.clrTitle.Location = new System.Drawing.Point(117, 35);
            this.clrTitle.Name = "clrTitle";
            this.clrTitle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.clrTitle.Properties.ShowWebColors = false;
            this.clrTitle.Size = new System.Drawing.Size(46, 21);
            this.clrTitle.TabIndex = 24;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(210, 40);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(65, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "背景颜色：";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(711, 623);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(804, 623);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(85, 27);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            // 
            // toolStripDropDownService
            // 
            this.toolStripDropDownService.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownService.Name = "toolStripDropDown1";
            this.toolStripDropDownService.Size = new System.Drawing.Size(2, 4);
            // 
            // CellOptionForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(904, 662);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.tabCtrl);
            this.MinimumSize = new System.Drawing.Size(920, 700);
            this.Name = "CellOptionForm";
            this.Text = "单元格设置";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.CellOptionForm_FormClosing);
            this.tabCtrl.ResumeLayout(false);
            this.pageFormula.ResumeLayout(false);
            this.layoutPnlC.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.grpTitle.ResumeLayout(false);
            this.grpTitle.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBoxFileName.ResumeLayout(false);
            this.groupBoxFileName.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ccbePort.Properties)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDeciNum)).EndInit();
            this.groupBox10.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.clrCellFore.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.clrCellBkStatic.Properties)).EndInit();
            this.groupBox11.ResumeLayout(false);
            this.groupBox11.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.clrTitleBk.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.clrTitle.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabCtrl;
        private System.Windows.Forms.TabPage pageFormula;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.RadioButton rbtnMt;
        private System.Windows.Forms.RadioButton rbtnMo;
        private System.Windows.Forms.RadioButton rbtnAll;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.RadioButton rbtnChinaTelecom;
        private System.Windows.Forms.RadioButton rbtnChinaUnicom;
        private System.Windows.Forms.RadioButton rbtnChinaMobile;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.RadioButton rBtnDynamic;
        private System.Windows.Forms.RadioButton rBtnStaticBk;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TableLayoutPanel layoutPnlC;
        private Util.KPIFormulaEditor kpiExpEditor;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.ColorEdit clrCellFore;
        private DevExpress.XtraEditors.ColorEdit clrCellBkStatic;
        private System.Windows.Forms.GroupBox grpTitle;
        private System.Windows.Forms.TextBox tbxTitle;
        private System.Windows.Forms.RadioButton rBtnLeft;
        private System.Windows.Forms.RadioButton rBtnTop;
        private System.Windows.Forms.RadioButton rBtnNoTitle;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.ColorEdit clrTitleBk;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ColorEdit clrTitle;
        private System.Windows.Forms.Label label2;
        private CQT.ScoreRangeColorSettingPanel rngColorPnl;
        private System.Windows.Forms.NumericUpDown numRngMax;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRngMin;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.CheckBox chkGridAvg;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.ListView listViewService;
        private System.Windows.Forms.Button btnPopupService;
        private System.Windows.Forms.Label lbSvCount;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownService;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numericUpDownDeciNum;
        private System.Windows.Forms.GroupBox groupBoxFileName;
        private DevExpress.XtraEditors.CheckedComboBoxEdit ccbePort;
        private System.Windows.Forms.ComboBox cbxfileNumType;
        private System.Windows.Forms.Button btnClearPort;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label_fileName;
        private System.Windows.Forms.TextBox textBoxFileName;
        private System.Windows.Forms.CheckBox checkBoxFileName;
    }
}