﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.Util;

using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc
{
    public class CreatePolygonFromPointInExcel : ShowFuncForm
    {
        public CreatePolygonFromPointInExcel(MainModel mm) : base(mm)
        {

        }

        public override string Name
        {
            get { return "Xls点生成多边形"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20036, this.Name);
        }

        private CreatePolygonSettingForm setForm = null;
        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new CreatePolygonSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            excelFile = setForm.ExcelFile;
            shpFile = setForm.ShpFile;
            return true;
        }

        private List<string> warns = null;
        protected override void showForm()
        {
            WaitBox.Show("正在读取Excel...", ProcessInThread);
            CreatePolygonLogForm form = new CreatePolygonLogForm(MainModel, warns);
            form.Show(MainModel.MainForm);
        }

        private void ProcessInThread()
        {
            warns = new List<string>();
            Shapefile shp = null;
            try
            {
                ExcelInput input = new ExcelInput(excelFile, PolygonRegionCreater.ColumnNames);
                ExcelReader reader = new ExcelReader(input);
                ExcelOutput output = reader.Read();
                if (output.Error != null)
                {
                    throw output.Error;
                }

                Dictionary<int, string> errorDic = new Dictionary<int, string>();
                PolygonRegionCreater creater = new PolygonRegionCreater(output.Result, errorDic);
                shp = creater.CreateShapefile();

                warns.AddRange(output.Warns);
                foreach (int key in errorDic.Keys)
                {
                    warns.Add(string.Format("Row {0}, {1}", output.RowsIndex[key], errorDic[key]));
                }

                if (!shp.SaveAs(shpFile, null))
                {
                    throw (new Exception("保存Shapefile错误: " + shp.get_ErrorMsg(shp.LastErrorCode)));
                }
                warns.Insert(0, "生成图层成功！");
            }
            catch (Exception ex)
            {
                warns.Insert(0, "生成图层失败: ");
                warns.Add(ex.Message);
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                WaitBox.Close();
                if (shp != null)
                {
                    shp.Close();
                }
            }
        }


        protected string excelFile;
        protected string shpFile;
    }
}
