﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRMobileServiceInfoBase
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        //相关采样点
        public List<TestPoint> TpsList { get; set; }
        //需要统计的采样点及其对应的最强邻区索引 <Tp,maxNBIndex>
        public List<KeyValuePair<TestPoint, int>> StatTpsList { get; set; }
        //最强邻区采样点索引表
        public List<int> MaxNBIndexList { get; set; }

        public int TpCount { get { return TpsList.Count; } }

        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public string BeginTimeStr { get; set; }
        public string EndTimeStr { get; set; }

        public double BeginLongitude { get; set; }
        public double BeginLatitude { get; set; }
        public double EndLongitude { get; set; }
        public double EndLatitude { get; set; }

        public string TacCIStr { get; protected set; }
        public string CellNameStr { get; protected set; }

        public DataInfo Rsrp { get; set; } = new DataInfo();
        public DataInfo Sinr { get; set; } = new DataInfo();
        public DataInfo MaxNbRsrp { get; set; } = new DataInfo();
        public DataInfo AppSpeed { get; set; } = new DataInfo();

        public virtual void FillItem()
        {
            BeginTimeStr = BeginTime.ToString("yy-MM-dd HH:mm:ss.fff");
            EndTimeStr = EndTime.ToString("yy-MM-dd HH:mm:ss.fff");
            MaxNBIndexList = new List<int>();

            foreach (TestPoint tp in TpsList)
            {
                float? rsrp = getRsrp(tp);
                Rsrp.Add(rsrp);

                float? sinr = getSinr(tp);
                Sinr.Add(sinr);

                double? appSpeedMb = getAppSpeedMb(tp);
                AppSpeed.Add(appSpeedMb);

                float? maxNbRsrp = findMaxNBCell(tp, MaxNBIndexList);
                MaxNbRsrp.Add(maxNbRsrp);
            }

            Rsrp.CalCulate();
            Sinr.CalCulate();
            MaxNbRsrp.CalCulate();
            AppSpeed.CalCulate();

            fillCellInfo();
        }

        #region 获取采样点信息
        protected virtual float? getRsrp(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            return rsrp;
        }

        protected virtual float? getSinr(TestPoint tp)
        {
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            return sinr;
        }

        protected virtual double? getAppSpeedMb(TestPoint tp)
        {
            double? appSpeedMb = NRTpHelper.NrTpManager.GetAppSpeedMb(tp);
            return appSpeedMb;
        }

        protected virtual float? findMaxNBCell(TestPoint tp, List<int> maxNBIndexList)
        {
            float? nCellRsrpMax = null;
            int maxNBIndex = -1;
            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type != NRTpHelper.NRNCellType.NCELL)
                {
                    continue;
                }
                float? nRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i, true);
                if (nRsrp != null)
                {
                    nCellRsrpMax = nRsrp;
                    maxNBIndex = i;
                }
                break;
            }
            maxNBIndexList.Add(maxNBIndex);
            if (nCellRsrpMax != float.MinValue)
            {
                return nCellRsrpMax;
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region 填充小区信息
        protected virtual void fillCellInfo()
        {
            List<string> tacciList = new List<string>();
            List<string> cellNameList = new List<string>();
            foreach (TestPoint tp in TpsList)
            {
                dealTP(tacciList, cellNameList, tp);
            }
            if (tacciList.Count != 0)
            {
                TacCIStr = gatherStringListToString(tacciList);
            }
            if (cellNameList.Count != 0)
            {
                CellNameStr = gatherStringListToString(cellNameList);
            }
        }

        protected void dealTP(List<string> tacciList, List<string> cellNameList, TestPoint tp)
        {
            int? tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);

            var cell = CellManager.GetInstance().GetNearestNRCellByTACCI(tp.DateTime, tac, nci, tp.Longitude, tp.Latitude);
            if (cell != null)
            {
                string tacci = tac.ToString() + "_" + nci.ToString();
                if (!tacciList.Contains(tacci))
                {
                    tacciList.Add(tacci);
                }
                if (!cellNameList.Contains(cell.Name))
                {
                    cellNameList.Add(cell.Name);
                }
            }
        }

        /// <summary>
        /// 字符串表合成字符串
        /// </summary>
        /// <param name="stringList"></param>
        /// <returns></returns>
        protected string gatherStringListToString(List<string> stringList)
        {
            StringBuilder strbDes = new StringBuilder();
            foreach (string des in stringList)
            {
                strbDes.Append(des + "|");
            }
            if (strbDes.Length > 0)
            {
                strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }
        #endregion

        public class DataInfo
        {
            public double Min { get; set; } = double.MaxValue;
            public double Max { get; set; } = double.MinValue;
            public double Sum { get; set; }
            public int Count { get; set; }
            public double? Avg { get; set; }

            public void Add(double? data)
            {
                if (data == null)
                {
                    return;
                }
                double validData = (double)data;
                Sum += validData;
                Count++;

                if (data > Max)
                {
                    Max = validData;
                }
                if (data < Min)
                {
                    Min = validData;
                }
            }

            public void Merge(DataInfo info)
            {
                Count += info.Count;
                Sum += info.Sum;
            }

            public void CalCulate()
            {
                if (Count > 0)
                {
                    Max = Math.Round(Max, 2);
                    Min = Math.Round(Min, 2);
                    Avg = Math.Round(Sum / Count, 2);
                }
            }
        }
    }
}
