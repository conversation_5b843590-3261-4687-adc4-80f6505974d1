﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsOverlapCoverageRatioResult : NbIotMgrsResultControlBase
    {
        protected NbIotMgrsFuncItem funcItem = null;
        protected ZTScanGridCoverageLayer layer;
        protected List<CoverageRegion> scanGridInfoList = new List<CoverageRegion>();
        protected List<CarrierCoverage> carrierDataList = new List<CarrierCoverage>();

        public NbIotMgrsOverlapCoverageRatioResult()
        {
            InitializeComponent();

            InitCbxFreqType();
            cbxFreqType.SelectedIndexChanged += CbxFreqType_SelectedChanged;

            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            miExportDetailExcel.Click += MiExportDetailExcel_Click;
            gvSerialGrid.DoubleClick += GridView_DoubleClick;
            gvGrid.DoubleClick += GridView_DoubleClick;
            miEditRange.Click += MiEditRange_Click;

            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportList.Click += MiExportGridList_Click;

            miExportAllShp.Click += base.MiExportShpAll_Click;
            miExportShp.Click += MiExportShp_Click;
        }

        public override string Desc
        {
            get { return "连续重叠覆盖"; }
        }

        public virtual void FillData(NbIotMgrsFuncItem curFuncItem)
        {
            this.funcItem = curFuncItem;
            RefreshResult();
        }

        public virtual void RefreshResult()
        {
            GetData();
            scanGridInfoList = new List<CoverageRegion>();
            foreach (var areaWeakGrid in carrierDataList)
            {
                foreach (var serialWeakGrid in areaWeakGrid.AreaGridViews)
                {
                    foreach (var gridInfo in serialWeakGrid.SerialGridViews)
                    {
                        foreach (var item in gridInfo.GridViews)
                        {
                            scanGridInfoList.Add(item);
                        }
                    }
                }
            }

            gridControl1.DataSource = carrierDataList;
            gridControl1.RefreshDataSource();
        }

        protected virtual void GetData()
        {
            NbIotMgrsOverlapCoverageRatioStater stater = this.funcItem.Stater as NbIotMgrsOverlapCoverageRatioStater;
            string type = cbxFreqType.SelectedItem as string;
            carrierDataList = stater.GetViews(type);
        }

        #region 导出
        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = Desc;
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            List<NPOIRow> rowList = getNPOIRow();
            ExcelNPOIManager.ExportToExcel(rowList, fileName, sheetName);
        }

        private void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void MiExportGridList_Click(object sender, EventArgs e)
        {
            List<CarrierCoverage> viewList = gridControl1.DataSource as List<CarrierCoverage>;
            if (viewList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("序号");
            title.Add("栅格编号");
            title.Add("重叠覆盖度");
            title.Add("最大场强");
            title.Add("平均场强");
            title.Add("最大SINR");
            title.Add("平均SINR");
            title.Add("中心经度");
            title.Add("中心纬度");
            content.Add(title);

            int idIndex = 0;
            foreach (CarrierCoverage carrier in viewList)
            {
                foreach (AreaOverlapGridData area in carrier.AreaGridViews)
                {
                    foreach (SerialOverlapCoverage view in area.SerialGridViews)
                    {
                        foreach (CoverageRegion grid in view.GridViews)
                        {
                            List<object> row = new List<object>();
                            row.Add(++idIndex);
                            row.Add(grid.MGRTIndex);
                            row.Add(grid.OverlapCoverage);
                            row.Add(grid.MaxRsrp);
                            row.Add(grid.AvgRsrp);
                            row.Add(grid.MaxSinr);
                            row.Add(grid.AvgSinr);
                            row.Add(grid.CentLng);
                            row.Add(grid.CentLat);
                            content.Add(row);
                        }
                    }
                }
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void MiExportDetailExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rowList = getNPOIRow();
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        protected virtual List<NPOIRow> getNPOIRow()
        {
            List<NPOIRow> rowList = new List<NPOIRow>(carrierDataList.Count + 1);
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("运营商名称");
            titleRow.AddCellValue("连续问题点数");

            titleRow.AddCellValue("区域名称");
            titleRow.AddCellValue("区域问题点数");

            titleRow.AddCellValue("序号");
            titleRow.AddCellValue("文件名");
            titleRow.AddCellValue("连续栅格号");
            titleRow.AddCellValue("连续栅格数");
            titleRow.AddCellValue("最大重叠覆盖度");
            titleRow.AddCellValue("最小重叠覆盖度");
            titleRow.AddCellValue("平均重叠覆盖度");

            titleRow.AddCellValue("栅格编号");
            titleRow.AddCellValue("小区数");
            titleRow.AddCellValue("小区平均电平");
            titleRow.AddCellValue("小区最强电平");
            titleRow.AddCellValue("小区平均SINR");
            titleRow.AddCellValue("小区最强SINR");
            titleRow.AddCellValue("中心经度");
            titleRow.AddCellValue("中心纬度");
            
            titleRow.AddCellValue("EARFCN");
            titleRow.AddCellValue("PCI");
            titleRow.AddCellValue("采样点数");
            titleRow.AddCellValue("采样点平均RSRP");
            titleRow.AddCellValue("采样点平均SINR");

            rowList.Add(titleRow);

            int index = 1;
            foreach (CarrierCoverage item in carrierDataList)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, item, index);
                rowList.Add(row);
            }
            return rowList;
        }

        protected virtual void fillRow(ref NPOIRow row, CarrierCoverage item, int index)
        {
            if (row == null || item == null)
                return;
            //添加一级数据
            row.AddCellValue(item.Name);
            row.AddCellValue(item.IssuesCount);
            foreach (var area in item.AreaGridViews)
            {
                //添加二级数据
                NPOIRow subRow = new NPOIRow();
                subRow.AddCellValue(area.AreaName);
                subRow.AddCellValue(area.IssuesCount);
                index = addSerialGridData(index, area, subRow);
                row.AddSubRow(subRow);
            }
        }

        private int addSerialGridData(int index, AreaOverlapGridData area, NPOIRow subRow)
        {
            //添加三级数据
            foreach (var serialGrid in area.SerialGridViews)
            {
                NPOIRow thirdRow = new NPOIRow();
                thirdRow.AddCellValue(index++);
                thirdRow.AddCellValue(serialGrid.FileNames);
                thirdRow.AddCellValue(serialGrid.WeakGridDesc);
                thirdRow.AddCellValue(serialGrid.WeakGridCount);
                thirdRow.AddCellValue(serialGrid.MaxOverlapCoverage);
                thirdRow.AddCellValue(serialGrid.MinOverlapCoverage);
                thirdRow.AddCellValue(serialGrid.AvgOverlapCoverage);

                addGridData(serialGrid, thirdRow);
                subRow.AddSubRow(thirdRow);
            }

            return index;
        }

        private void addGridData(SerialOverlapCoverage serialGrid, NPOIRow thirdRow)
        {
            //添加四级数据
            foreach (var subItem in serialGrid.GridViews)
            {
                NPOIRow fouthRow = new NPOIRow();
                fouthRow.AddCellValue(subItem.MGRTIndex);
                fouthRow.AddCellValue(subItem.OverlapCoverage);
                fouthRow.AddCellValue(subItem.AvgRsrp);
                fouthRow.AddCellValue(subItem.MaxRsrp);
                fouthRow.AddCellValue(subItem.AvgSinr);
                fouthRow.AddCellValue(subItem.MaxSinr);
                fouthRow.AddCellValue(subItem.CentLng);
                fouthRow.AddCellValue(subItem.CentLat);

                addCellData(subItem, fouthRow);
                thirdRow.AddSubRow(fouthRow);
            }
        }

        private void addCellData(CoverageRegion subItem, NPOIRow fouthRow)
        {
            //添加五级数据
            foreach (var thirdItem in subItem.CellGrid)
            {
                NPOIRow fifthRow = new NPOIRow();
                fifthRow.AddCellValue(thirdItem.EARFCN);
                fifthRow.AddCellValue(thirdItem.PCI);
                fifthRow.AddCellValue(thirdItem.SampleCount);
                fifthRow.AddCellValue(thirdItem.R0_RP);
                fifthRow.AddCellValue(thirdItem.R0_CINR);
                fouthRow.AddSubRow(fifthRow);
            }
        }

        private void MiExportShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "另存为";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = saveFileDlg.FileName;
                int expRet = layer.MakeShpFile_inject(fileName);
                if (expRet == 0)
                {
                    MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
                }
                else if (expRet == 1)
                {
                    MessageBox.Show(this, "导出成功！");
                }
                else if (expRet == 2)
                {
                    //取消导出
                }
                else
                {
                    MessageBox.Show(this, "导出图层发生错误！");
                }
            }
        }

        protected override void ExportAllShp(string savePath)
        {
            string fileName = System.IO.Path.Combine(savePath, Desc + ".shp");
            if (System.IO.File.Exists(fileName))
            {
                System.IO.File.Delete(fileName);
            }
            string filedbf = System.IO.Path.Combine(savePath, Desc + ".dbf");
            if (System.IO.File.Exists(filedbf))
            {
                System.IO.File.Delete(filedbf);
            }
            string fileprj = System.IO.Path.Combine(savePath, Desc + ".prj");
            if (System.IO.File.Exists(fileprj))
            {
                System.IO.File.Delete(fileprj);
            }
            string fileshx = System.IO.Path.Combine(savePath, Desc + ".shx");
            if (System.IO.File.Exists(fileshx))
            {
                System.IO.File.Delete(fileshx);
            }

            if (layer == null)
            {
                mf = MainModel.MainForm.GetMapForm();
                MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridCoverageLayer));
                layer = clayer as ZTScanGridCoverageLayer;
            }
            layer.GridInfos = scanGridInfoList;
            layer.MakeShpFile_inject(fileName);
        }
        #endregion

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView curGridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = curGridView.GetRow(curGridView.GetSelectedRows()[0]);
            if (row is CoverageRegion)
            {
                CoverageRegion grid = row as CoverageRegion;
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
                layer.SelectedGrid = grid;
            }
            else if (row is SerialOverlapCoverage)
            {
                SerialOverlapCoverage view = row as SerialOverlapCoverage;
                CoverageRegion grid = view.GridViews[view.GridViews.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
            }
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        protected virtual void GotoSelectedViewGV(SerialOverlapCoverage selectedData, int zoom)
        {
            double fLong = selectedData.GridViews[0].CentLng;
            double fLat = selectedData.GridViews[0].CentLat;
            if (zoom == 0)
            {
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat);
            }
            else
            {
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat, zoom);
            }
        }

        public override void DrawOnLayer()
        {
            mf = MainModel.MainForm.GetMapForm();
            MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridCoverageLayer));
            layer = clayer as ZTScanGridCoverageLayer;
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
            ZTScanGridCoverageLayer.IsActived = true;
            mf.updateMap();
            MainModel.RefreshLegend();
            if (carrierDataList[0].AreaGridViews.Count > 0 && carrierDataList[0].AreaGridViews[0].SerialGridViews.Count > 0)
            {
                GotoSelectedViewGV(carrierDataList[0].AreaGridViews[0].SerialGridViews[0], 0);
            }
        }

        private void gvSerialGrid_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle > -1)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
        }

        public override void LayerDataClear()
        {
            layer.GridInfos = null;
            layer.SelectedGrid = null;
            ZTScanGridCoverageLayer.IsActived = false;
        }

        private void MiEditRange_Click(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(ZTScanGridCoverageLayer.Ranges.Minimum, ZTScanGridCoverageLayer.Ranges.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(ZTScanGridCoverageLayer.Ranges.ColorRanges);
            dlg.InvalidatePointColor = ZTScanGridCoverageLayer.Ranges.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }
            ZTScanGridCoverageLayer.Ranges.ColorRanges = new List<MasterCom.MControls.ColorRange>(dlg.ColorRanges);
            ZTScanGridCoverageLayer.Ranges.InvalidColor = dlg.InvalidatePointColor;
            ZTScanGridCoverageLayer.Ranges.SaveColorRange("Coverage");

            DrawOnLayer();
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            foreach (var item in NbIotMgrsBaseSettingManager.Instance.BandType.BandType)
            {
                cbxFreqType.Items.Add(item.Value);
            }
            cbxFreqType.SelectedIndex = 0;
        }

        private void CbxFreqType_SelectedChanged(object sender, EventArgs e)
        {
            RefreshResult();
            DrawOnLayer();
        }
    }
}
