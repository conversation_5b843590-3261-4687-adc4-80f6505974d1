﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetTransferedTimeLimitForm : Form
    {
        public SetTransferedTimeLimitForm()
        {
            InitializeComponent();
        }

        public void GetTransferedTimeLimit(out int transferedTimeLimit)
        {
            transferedTimeLimit = (int)this.transferedTimeLimit.Value;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

}
