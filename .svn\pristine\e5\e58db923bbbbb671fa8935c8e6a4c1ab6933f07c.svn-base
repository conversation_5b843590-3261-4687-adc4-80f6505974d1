using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Grid;
namespace MasterCom.RAMS.ZTFunc
{
    public partial class BcchTchScanRelatedInfoForm : MinCloseForm
    {
        private List<BcchTchScanRelatedInfo> btsrInfoList;
        public BcchTchScanRelatedInfoForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
         listView.ListViewItemSorter = new ListViewSorter(listView);
        }

        public void FillDatas(GridMatrix<GridNBCellsInfo> cellGrids)
        {
            btsrInfoList = new List<BcchTchScanRelatedInfo>();
            foreach (GridNBCellsInfo curGridNBCell in cellGrids)
            {
                if (curGridNBCell == null)
                {
                    continue;
                }
                foreach (Cell cellMain in curGridNBCell.mainCellList)
                {
                    foreach (int cellID in curGridNBCell.cellInfo.Keys)
                    {
                        addValidBtsrInfoList(curGridNBCell, cellMain, cellID);
                    }
                }
            }
            freshData(btsrInfoList);
        }

        private void addValidBtsrInfoList(GridNBCellsInfo curGridNBCell, Cell cellMain, int cellID)
        {
            if (cellID != 0)
            {
                Cell cellCur = CellManager.GetInstance().GetCurrentCell(cellID);
                if (cellCur != null)
                {
                    addBtsrInfoList(curGridNBCell, cellMain, cellID, cellCur);
                }
            }
        }

        private void addBtsrInfoList(GridNBCellsInfo curGridNBCell, Cell cellMain, int cellID, Cell cellCur)
        {
            if (!(cellCur.LAC == cellMain.LAC && cellCur.CI == cellMain.CI) && isBcchTchValidate(cellMain, cellCur))
            {
                BcchTchScanRelatedInfo btsrInfo;
                if ((btsrInfo = isExit(cellMain, cellCur)) == null)
                {
                    btsrInfo = new BcchTchScanRelatedInfo(cellMain, cellCur);
                    btsrInfo.mainRxlevMean = curGridNBCell.RxlevMeanMain;
                    btsrInfo.scanRxlevMean = curGridNBCell.cellInfo[cellID].rxlevAvg;
                    btsrInfo.fillBcchTch();
                    btsrInfoList.Add(btsrInfo);
                }
                btsrInfo.gridNum++;
            }
        }

        public bool isBcchTchValidate(Cell cellMain, Cell cellCur)
        {
            if (Math.Abs(cellMain.BCCH - cellCur.BCCH) <= 1)
            {
                return true;
            }
            else
            {
                List<short> tchList = getTchList(cellMain);
                foreach (short tch in cellCur.TCH)
                {
                    if (tchList.Contains(tch))
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        private static List<short> getTchList(Cell cellMain)
        {
            List<short> tchList = new List<short>();
            foreach (short tch in cellMain.TCH)
            {
                if (tch - 1 > 0 && !tchList.Contains((short)(tch - 1)))
                {
                    tchList.Add((short)(tch - 1));
                }
                if (!tchList.Contains(tch))
                {
                    tchList.Add(tch);
                }
                if (!tchList.Contains((short)(tch + 1)))
                {
                    tchList.Add((short)(tch + 1));
                }
            }

            return tchList;
        }

        public void freshData(List<BcchTchScanRelatedInfo> btsrInfoList)
        {
            listView.Items.Clear();
            int sn = 0;
            listView.OwnerDraw = true;
            foreach (BcchTchScanRelatedInfo info in btsrInfoList)
            {
                ListViewItem lvItem = new ListViewItem();
                lvItem.SubItems[0].Text = (++sn).ToString();
                lvItem.SubItems.Add(info.mainCell.Name);
                lvItem.SubItems.Add(info.mainCell.LAC.ToString());
                lvItem.SubItems.Add(info.mainCell.CI.ToString());
                lvItem.SubItems.Add(info.mainRxlevMean.ToString());
                lvItem.SubItems.Add(info.mainCell.BCCH.ToString());
                lvItem.SubItems.Add(info.getMainTch());
                lvItem.SubItems.Add(info.scanCell.Name);
                lvItem.SubItems.Add(info.scanCell.BCCH.ToString());
                lvItem.SubItems.Add(info.getScanTch());
                lvItem.SubItems.Add(info.scanRxlevMean.ToString());
                lvItem.SubItems.Add(info.gridNum.ToString());
                lvItem.SubItems.Add(info.GetDistance().ToString());
                lvItem.Tag = info;
                listView.Items.Add(lvItem);
            }
        }

        private BcchTchScanRelatedInfo isExit(Cell cellMain, Cell cellCur)
        {
            foreach (BcchTchScanRelatedInfo info in btsrInfoList)
            {
                if(info.mainCell == cellMain && info.scanCell == cellCur)
                {
                    return info;
                }
            }
            return null;
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(listView);
            }
            catch
            {
            	//continue
            }
        }

        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listView.SelectedItems[0] != null)
            {
                BcchTchScanRelatedInfo btsrInfo = listView.SelectedItems[0].Tag as BcchTchScanRelatedInfo;
                mModel.SelectedCells.Clear();
                mModel.SelectedCells.Add(btsrInfo.mainCell);
                mModel.SelectedCells.Add(btsrInfo.scanCell);
                mModel.MainForm.FireGotoView((btsrInfo.mainCell.Longitude + btsrInfo.scanCell.Longitude) / 2, (btsrInfo.mainCell.Latitude + btsrInfo.scanCell.Latitude) / 2);
            }
        }

        private void listView_DrawColumnHeader(object sender, DrawListViewColumnHeaderEventArgs e)
        {
            e.DrawDefault = true;
        }

        private void listView_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            SizeF sizeFColumn = e.Graphics.MeasureString(e.Item.SubItems[5].Text.Length > listView.Columns[5].Text.Length ? e.Item.SubItems[5].Text : listView.Columns[5].Text, e.Item.SubItems[5].Font);
            listView.Columns[5].Width = sizeFColumn.Width <= listView.Columns[5].Width ? listView.Columns[5].Width : (int)sizeFColumn.Width;
            sizeFColumn = e.Graphics.MeasureString(e.Item.SubItems[6].Text.Length > listView.Columns[6].Text.Length ? e.Item.SubItems[6].Text : listView.Columns[6].Text, e.Item.SubItems[6].Font);
            listView.Columns[6].Width = sizeFColumn.Width <= listView.Columns[6].Width ? listView.Columns[6].Width : (int)sizeFColumn.Width;
            sizeFColumn = e.Graphics.MeasureString(e.Item.SubItems[8].Text.Length > listView.Columns[8].Text.Length ? e.Item.SubItems[8].Text : listView.Columns[8].Text, e.Item.SubItems[8].Font);
            listView.Columns[8].Width = sizeFColumn.Width <= listView.Columns[8].Width ? listView.Columns[8].Width : (int)sizeFColumn.Width;
            sizeFColumn = e.Graphics.MeasureString(e.Item.SubItems[9].Text.Length > listView.Columns[9].Text.Length ? e.Item.SubItems[9].Text : listView.Columns[9].Text, e.Item.SubItems[9].Font);
            listView.Columns[9].Width = sizeFColumn.Width <= listView.Columns[9].Width ? listView.Columns[9].Width : (int)sizeFColumn.Width;
            BcchTchScanRelatedInfo btsrInfo = e.Item.Tag as BcchTchScanRelatedInfo;
            e.Graphics.DrawString(e.Item.SubItems[0].Text, e.Item.SubItems[0].Font, Brushes.Black, e.Item.SubItems[0].Bounds.X, e.Item.SubItems[0].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[1].Text, e.Item.SubItems[1].Font, Brushes.Black, e.Item.SubItems[1].Bounds.X, e.Item.SubItems[1].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[2].Text, e.Item.SubItems[2].Font, Brushes.Black, e.Item.SubItems[2].Bounds.X, e.Item.SubItems[2].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[3].Text, e.Item.SubItems[3].Font, Brushes.Black, e.Item.SubItems[3].Bounds.X, e.Item.SubItems[3].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[4].Text, e.Item.SubItems[4].Font, Brushes.Black, e.Item.SubItems[4].Bounds.X, e.Item.SubItems[4].Bounds.Y);
            if (btsrInfo.mainBcchList.Count > 0)
            {
                e.Graphics.DrawString(e.Item.SubItems[5].Text, e.Item.SubItems[5].Font, Brushes.Red, e.Item.SubItems[5].Bounds.X, e.Item.SubItems[5].Bounds.Y);
                e.Graphics.DrawString(e.Item.SubItems[8].Text, e.Item.SubItems[8].Font, Brushes.Red, e.Item.SubItems[8].Bounds.X, e.Item.SubItems[8].Bounds.Y);
            }
            else
            {
                e.Graphics.DrawString(e.Item.SubItems[5].Text, e.Item.SubItems[5].Font, Brushes.Black, e.Item.SubItems[5].Bounds.X, e.Item.SubItems[5].Bounds.Y);
                e.Graphics.DrawString(e.Item.SubItems[8].Text, e.Item.SubItems[8].Font, Brushes.Black, e.Item.SubItems[8].Bounds.X, e.Item.SubItems[8].Bounds.Y);
            }
            if (btsrInfo.mainTchList.Count > 0)
            {
                SizeF sizeFRed = e.Graphics.MeasureString(btsrInfo.mainCellRedTCH, e.Item.SubItems[6].Font);
                e.Graphics.DrawString(btsrInfo.mainCellRedTCH, e.Item.SubItems[6].Font, Brushes.Red, e.Item.SubItems[6].Bounds.X, e.Item.SubItems[6].Bounds.Y);
                e.Graphics.DrawString(btsrInfo.mainCellBlackTCH, e.Item.SubItems[6].Font, Brushes.Black, e.Item.SubItems[6].Bounds.X + sizeFRed.Width, e.Item.SubItems[6].Bounds.Y);
                sizeFRed = e.Graphics.MeasureString(btsrInfo.scanCellRedTCH, e.Item.SubItems[9].Font);
                e.Graphics.DrawString(btsrInfo.scanCellRedTCH, e.Item.SubItems[9].Font, Brushes.Red, e.Item.SubItems[9].Bounds.X, e.Item.SubItems[9].Bounds.Y);
                e.Graphics.DrawString(btsrInfo.scanCellBlackTCH, e.Item.SubItems[9].Font, Brushes.Black, e.Item.SubItems[9].Bounds.X + sizeFRed.Width, e.Item.SubItems[9].Bounds.Y);
            }
            e.Graphics.DrawString(e.Item.SubItems[7].Text, e.Item.SubItems[7].Font, Brushes.Black, e.Item.SubItems[7].Bounds.X, e.Item.SubItems[7].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[10].Text, e.Item.SubItems[10].Font, Brushes.Black, e.Item.SubItems[10].Bounds.X, e.Item.SubItems[10].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[11].Text, e.Item.SubItems[11].Font, Brushes.Black, e.Item.SubItems[11].Bounds.X, e.Item.SubItems[11].Bounds.Y);
            e.Graphics.DrawString(e.Item.SubItems[12].Text, e.Item.SubItems[12].Font, Brushes.Black, e.Item.SubItems[12].Bounds.X, e.Item.SubItems[12].Bounds.Y);
        }
    }

    public class BcchTchScanRelatedInfo
    {
        public Cell mainCell { get; set; }
        public double mainRxlevMean { get; set; }
        public List<short> mainBcchList { get; set; }
        public List<short> mainTchList { get; set; }
        public Cell scanCell { get; set; }
        public double scanRxlevMean { get; set; }
        public List<short> scanBcchList { get; set; }
        public List<short> scanTchList { get; set; }
        public int gridNum { get; set; }

        public BcchTchScanRelatedInfo(Cell mainCell, Cell scanCell)
        {
            gridNum = 0;
            this.mainCell = mainCell;
            this.scanCell = scanCell;
            mainBcchList = new List<short>();
            mainTchList = new List<short>();
            scanBcchList = new List<short>();
            scanTchList = new List<short>();
        }

        public string getMainTch()
        {
            StringBuilder tchStringB = new StringBuilder();
            if (mainCell != null)
            {
                List<short> tchList = mainCell.TCH;
                tchList.Sort();
                for (int i = 0; i < tchList.Count; i++)
                {
                    tchStringB.Append(tchList[i]);
                    if (i < tchList.Count - 1)
                    {
                        tchStringB.Append(',');
                    }
                }
            }
            return tchStringB.ToString();
        }

        public string getScanTch()
        {
            StringBuilder tchStringB = new StringBuilder();
            if (mainCell != null)
            {
                List<short> tchList = scanCell.TCH;
                tchList.Sort();
                for (int i = 0; i < tchList.Count; i++)
                {
                    tchStringB.Append(tchList[i]);
                    if (i < tchList.Count - 1)
                    {
                        tchStringB.Append(',');
                    }
                }
            }
            return tchStringB.ToString();
        }

        public string mainCellRedTCH
        {
            get
            {
                StringBuilder tchStringB = new StringBuilder();
                if (mainCell != null)
                {
                    List<short> tchList = mainTchList;
                    tchList.Sort();
                    for (int i = 0; i < tchList.Count; i++)
                    {
                        tchStringB.Append(tchList[i]);
                        if (i < tchList.Count - 1)
                        {
                            tchStringB.Append(',');
                        }
                    }
                }
                return tchStringB.ToString();
            }
        }

        public string mainCellBlackTCH
        {
            get
            {
                StringBuilder tchStringB = new StringBuilder();
                if (mainCell != null)
                {
                    List<short> tchList = mainCell.TCH;
                    tchList.Sort();
                    for (int i = 0; i < tchList.Count; i++)
                    {
                        if(mainTchList.Contains(tchList[i]))
                            continue;
                        tchStringB.Append(tchList[i]);
                        if (i < tchList.Count - 1)
                        {
                            tchStringB.Append(',');
                        }
                    }
                    if (tchStringB.Length > 0 && tchStringB[tchStringB.Length - 1] == ',')
                    {
                        tchStringB.Remove(tchStringB.Length - 1, 1);
                    }
                }
                return tchStringB.ToString();
            }
        }

        public string scanCellRedTCH
        {
            get
            {
                StringBuilder tchStringB = new StringBuilder();
                if (scanCell != null)
                {
                    List<short> tchList = scanTchList;
                    tchList.Sort();
                    for (int i = 0; i < tchList.Count; i++)
                    {
                        tchStringB.Append(tchList[i]);
                        if (i < tchList.Count - 1)
                        {
                            tchStringB.Append(',');
                        }
                    }
                }
                return tchStringB.ToString();
            }
        }

        public string scanCellBlackTCH
        {
            get
            {
                StringBuilder tchStringB = new StringBuilder();
                if (scanCell != null)
                {
                    List<short> tchList = scanCell.TCH;
                    tchList.Sort();
                    for (int i = 0; i < tchList.Count; i++)
                    {
                        if (scanTchList.Contains(tchList[i]))
                            continue;
                        tchStringB.Append(tchList[i]);
                        if (i < tchList.Count - 1)
                        {
                            tchStringB.Append(',');
                        }
                    }
                    if (tchStringB.Length > 0 && tchStringB[tchStringB.Length - 1] == ',')
                    {
                        tchStringB.Remove(tchStringB.Length - 1, 1);
                    }
                }
                return tchStringB.ToString();
            }
        }

        public void fillBcchTch()
        {
            if (Math.Abs(mainCell.BCCH - scanCell.BCCH) <= 1)
            {
                mainBcchList.Add(mainCell.BCCH);
                scanBcchList.Add(scanCell.BCCH);
            }

            List<short> tchList = new List<short>();
            foreach (short tch in mainCell.TCH)
            {
                if (tch - 1 > 0 && !tchList.Contains((short)(tch - 1)))
                {
                    tchList.Add((short)(tch - 1));
                }
                if (!tchList.Contains(tch))
                {
                    tchList.Add(tch);
                }
                if (!tchList.Contains((short)(tch + 1)))
                {
                    tchList.Add((short)(tch + 1));
                }
            }
            foreach (short tch in scanCell.TCH)
            {
                if (tchList.Contains(tch))
                {
                    scanTchList.Add(tch);
                    addMainTch(tch);
                }
            }
        }

        private void addMainTch(short tch)
        {
            if (mainCell.TCH.Contains(tch) && !mainTchList.Contains(tch))
            {
                mainTchList.Add(tch);
            }
            else if (mainCell.TCH.Contains((short)(tch - 1)) && !mainTchList.Contains((short)(tch - 1)))
            {
                mainTchList.Add((short)(tch - 1));
            }
            else if (mainCell.TCH.Contains((short)(tch + 1)) && !mainTchList.Contains((short)(tch + 1)))
            {
                mainTchList.Add((short)(tch + 1));
            }
        }

        public double GetDistance()
        {
            double distance = MathFuncs.GetDistance(mainCell.Longitude, mainCell.Latitude, scanCell.Longitude, scanCell.Latitude);
            return Math.Round(distance, 2);
        }
    }
}