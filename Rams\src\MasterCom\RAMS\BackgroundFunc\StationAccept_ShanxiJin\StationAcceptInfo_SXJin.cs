﻿using MasterCom.RAMS.Stat;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptInfo_SXJin : CellAcceptInfo_SXJin
    {
        public StationAcceptInfo_SXJin()
        {
            CellAcceptInfoDic = new Dictionary<int, CellAcceptInfo_SXJin>();
        }
        public Dictionary<int, CellAcceptInfo_SXJin> CellAcceptInfoDic { get; set; }
    }
    public class CellAcceptInfo_SXJin
    {
        protected CellAcceptInfo_SXJin()
        { 
        }
        public CellAcceptInfo_SXJin(CellWorkParam cellParamInfo, int fileNameEnodType)
            : base()
        {
            this.CellParamInfo = cellParamInfo;
            FileNameEnodType = fileNameEnodType;
        }
        public CellWorkParam CellParamInfo { get; set; }

        /// <summary>
        /// 文件名中ENodeBID进制： 0位10进制，1位16进制
        /// </summary>
        public int FileNameEnodType { get; set; }
        public string StrENodeBID
        {
            get
            {
                string strEnod = "";
                if (FileNameEnodType == 1)
                {
                    strEnod = Convert.ToString(this.CellParamInfo.ENodeBID, 16).ToUpper();
                }
                else
                {
                    strEnod = this.CellParamInfo.ENodeBID.ToString();
                }
                return strEnod;
            }
        }
        public bool HasFoundFile { get; set; }
        public SingleCellStatInfo StatInfo { get; set; }
    }
}
