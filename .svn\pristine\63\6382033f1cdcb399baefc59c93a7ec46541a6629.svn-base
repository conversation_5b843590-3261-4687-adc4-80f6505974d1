﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model.CellParam
{
    public partial class CellParamProblemPointForm : MinCloseForm
    {
        public CellParamProblemPointForm(MainModel mm):base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<CellProblemPoint> pnts)
        {
            gridCtrl.DataSource = pnts;
            gridCtrl.RefreshDataSource();
            gv.BestFitColumns();
        }

        private void gv_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            CellProblemPoint pnt = gv.GetRow(e.FocusedRowHandle) as CellProblemPoint;
            if (pnt==null)
            {
                return;
            }
            rTxt.Text = pnt.RuleDescription;
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gv.GetSelectedRows();
            if (rows.Length==0)
            {
                return;
            }
            CellProblemPoint pnt = gv.GetRow(rows[0]) as CellProblemPoint;
            if (pnt==null)
            {
                return;
            }
            MainModel.SelectedCell=null;
            MainModel.SelectedTDCell=null;
            if (pnt.Cell is Cell)
            {
                Cell cell = pnt.Cell as Cell;
                MainModel.SelectedCell = cell;
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude, 6000);
            }
            else if (pnt.Cell is TDCell)
            {
                TDCell cell = pnt.Cell as TDCell;
                MainModel.SelectedTDCell = cell;
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude, 6000);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Excel2007文件(*.xlsx)|*.xlsx";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                DevExpress.XtraPrinting.XlsxExportOptions options = new DevExpress.XtraPrinting.XlsxExportOptions();
                options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                gridCtrl.ExportToXlsx(dlg.FileName, options);
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
        }

    }
}
