﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing.Design;
using System.Windows.Forms.Design;
using System.ComponentModel;
using System.Windows.Forms;

namespace MasterCom.ES.UI
{
    public class SetExpFormulaEditor : UITypeEditor
    {
        public override UITypeEditorEditStyle GetEditStyle(System.ComponentModel.ITypeDescriptorContext context)
        {
            return UITypeEditorEditStyle.Modal;
        }
        public override object EditValue(System.ComponentModel.ITypeDescriptorContext context, IServiceProvider provider, object value)
        {
            ExpFomularEditForm form = ExpFomularEditForm.GetInstance();
            IWindowsFormsEditorService editorService =
                    (IWindowsFormsEditorService)provider.GetService(typeof(IWindowsFormsEditorService));
            if (editorService != null)
            {
                if(value!=null)
                {
                    form.FillCurrentInfo(value.ToString());
                }
                else
                {
                    form.FillCurrentInfo("");
                }
                form.FreshResvTreeShow(ESProcGraphForm.GetInstance().CurSelRoutine);
                form.FreshInProcModule();
                if(editorService.ShowDialog(form) == DialogResult.OK)
                {
                    return form.InputFormula;
                }
            }
            return value;
        }

        public class EditFormulaConverter : System.ComponentModel.StringConverter
        {
            //true: support drop-down style.       false: text style.
            public override bool GetStandardValuesSupported(ITypeDescriptorContext context)
            {
                return false;
            }

            /**/
            /*
            content of drop-down    
            public override TypeConverter.StandardValuesCollection 
                     GetStandardValues(ITypeDescriptorContext context)
            {
               return new StandardValuesCollection
                   (new string[] { "File1.bat", "File2.exe", "File3.dll" });
            }
            */

            //true: disable text editting.    false: enable text editting;
            public override bool GetStandardValuesExclusive(ITypeDescriptorContext context)
            {
                return true;
            }
        }

    }
}
