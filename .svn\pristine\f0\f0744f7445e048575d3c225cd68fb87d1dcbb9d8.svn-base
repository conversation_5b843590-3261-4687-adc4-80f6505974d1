﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMCellEmulateCovMRForm : MinCloseForm
    {
        List<CellEmulateCovResult> retList = new List<CellEmulateCovResult>();
        public GSMCellEmulateCovMRForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();

            initListView();

            initDefaultColor();

        }

        public void FillData(List<CellEmulateCovResult> retList)
        {
            this.retList = retList;
            listView1.Items.Clear();

            freshListView();

            foreach (CellEmulateCovResult ret in retList)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text =ret.region;
                lvi.SubItems.Add(ret.totalGrid.ToString());
                lvi.SubItems.Add(ret.totalSquare);
                if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 1)
                {
                    lvi.SubItems.Add(ret.coverGrid70.ToString());
                    lvi.SubItems.Add(ret.coverSquare70.ToString());
                    lvi.SubItems.Add(ret.coverRate70.ToString());
                }
                if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 2)
                {
                    lvi.SubItems.Add(ret.coverGrid80.ToString());
                    lvi.SubItems.Add(ret.coverSquare80.ToString());
                    lvi.SubItems.Add(ret.coverRate80.ToString());
                }
                if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 3)
                {
                    lvi.SubItems.Add(ret.coverGrid85.ToString());
                    lvi.SubItems.Add(ret.coverSquare85.ToString());
                    lvi.SubItems.Add(ret.coverRate85.ToString());
                }
                if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 4)
                {
                    lvi.SubItems.Add(ret.coverGrid90.ToString());
                    lvi.SubItems.Add(ret.coverSquare90.ToString());
                    lvi.SubItems.Add(ret.coverRate90.ToString());
                }
                if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 5)
                {
                    lvi.SubItems.Add(ret.coverGrid94.ToString());
                    lvi.SubItems.Add(ret.coverSquare94.ToString());
                    lvi.SubItems.Add(ret.coverRate94.ToString());
                }
                if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 6)
                {
                    lvi.SubItems.Add(ret.coverGrid95.ToString());
                    lvi.SubItems.Add(ret.coverSquare95.ToString());
                    lvi.SubItems.Add(ret.coverRate95.ToString());
                }
                listView1.Items.Add(lvi);
            }

            initRxlevLegendVisible();
        }

        ColumnHeader columnHeaderGrid70;
        ColumnHeader columnHeaderGrid80;
        ColumnHeader columnHeaderGrid85;
        ColumnHeader columnHeaderGrid90;
        ColumnHeader columnHeaderGrid94;
        ColumnHeader columnHeaderGrid95;
        ColumnHeader columnHeaderSquare70;
        ColumnHeader columnHeaderSquare80;
        ColumnHeader columnHeaderSquare85;
        ColumnHeader columnHeaderSquare90;
        ColumnHeader columnHeaderSquare94;
        ColumnHeader columnHeaderSquare95;
        ColumnHeader columnHeaderRate70;
        ColumnHeader columnHeaderRate80;
        ColumnHeader columnHeaderRate85;
        ColumnHeader columnHeaderRate90;
        ColumnHeader columnHeaderRate94;
        ColumnHeader columnHeaderRate95;

        private void initListView()
        {
            columnHeaderGrid70 = new ColumnHeader();
            columnHeaderGrid70.Text = "70覆盖栅格数";
            columnHeaderGrid70.Width = 100;
            columnHeaderGrid80 = new ColumnHeader();
            columnHeaderGrid80.Text = "80覆盖栅格数";
            columnHeaderGrid80.Width = 100;
            columnHeaderGrid85 = new ColumnHeader();
            columnHeaderGrid85.Text = "85覆盖栅格数";
            columnHeaderGrid85.Width = 100;
            columnHeaderGrid90 = new ColumnHeader();
            columnHeaderGrid90.Text = "90覆盖栅格数";
            columnHeaderGrid90.Width = 100;
            columnHeaderGrid94 = new ColumnHeader();
            columnHeaderGrid94.Text = "94覆盖栅格数";
            columnHeaderGrid94.Width = 100;
            columnHeaderGrid95 = new ColumnHeader();
            columnHeaderGrid95.Text = "95覆盖栅格数";
            columnHeaderGrid95.Width = 100;

            columnHeaderSquare70 = new ColumnHeader();
            columnHeaderSquare70.Text = "70覆盖面积(平方公里)";
            columnHeaderSquare70.Width = 135;
            columnHeaderSquare80 = new ColumnHeader();
            columnHeaderSquare80.Text = "80覆盖面积(平方公里)";
            columnHeaderSquare80.Width = 135;
            columnHeaderSquare85 = new ColumnHeader();
            columnHeaderSquare85.Text = "85覆盖面积(平方公里)";
            columnHeaderSquare85.Width = 135;
            columnHeaderSquare90 = new ColumnHeader();
            columnHeaderSquare90.Text = "90覆盖面积(平方公里)";
            columnHeaderSquare90.Width = 135;
            columnHeaderSquare94 = new ColumnHeader();
            columnHeaderSquare94.Text = "94覆盖面积(平方公里)";
            columnHeaderSquare94.Width = 135;
            columnHeaderSquare95 = new ColumnHeader();
            columnHeaderSquare95.Text = "95覆盖面积(平方公里)";
            columnHeaderSquare95.Width = 135;

            columnHeaderRate70 = new ColumnHeader();
            columnHeaderRate70.Text = "70面积覆盖率(%)";
            columnHeaderRate70.Width = 115;
            columnHeaderRate80 = new ColumnHeader();
            columnHeaderRate80.Text = "80面积覆盖率(%)";
            columnHeaderRate80.Width = 115;
            columnHeaderRate85 = new ColumnHeader();
            columnHeaderRate85.Text = "85面积覆盖率(%)";
            columnHeaderRate85.Width = 115;
            columnHeaderRate90 = new ColumnHeader();
            columnHeaderRate90.Text = "90面积覆盖率(%)";
            columnHeaderRate90.Width = 115;
            columnHeaderRate94 = new ColumnHeader();
            columnHeaderRate94.Text = "94面积覆盖率(%)";
            columnHeaderRate94.Width = 115;
            columnHeaderRate95 = new ColumnHeader();
            columnHeaderRate95.Text = "95面积覆盖率(%)";
            columnHeaderRate95.Width = 115;

        }

        private void freshListView()
        {
            listView1.Columns.Clear();
            listView1.Columns.AddRange(new ColumnHeader[] { columnHeader1, columnHeader2, columnHeader3 });  //区域名称，总栅格数，总面积的列必备

            if (MainModel.cellEmulateShowItem.visibleRxlevTopN>=1)
            {
                listView1.Columns.Add(columnHeaderGrid70);
                listView1.Columns.Add(columnHeaderSquare70);
                listView1.Columns.Add(columnHeaderRate70);
            }
            if (MainModel.cellEmulateShowItem.visibleRxlevTopN>=2)
            {
                listView1.Columns.Add(columnHeaderGrid80);
                listView1.Columns.Add(columnHeaderSquare80);
                listView1.Columns.Add(columnHeaderRate80);
            }
            if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 3)
            {
                listView1.Columns.Add(columnHeaderGrid85);
                listView1.Columns.Add(columnHeaderSquare85);
                listView1.Columns.Add(columnHeaderRate85);
            }
            if (MainModel.cellEmulateShowItem.visibleRxlevTopN>=4)
            {
                listView1.Columns.Add(columnHeaderGrid90);
                listView1.Columns.Add(columnHeaderSquare90);
                listView1.Columns.Add(columnHeaderRate90);
            }
            if (MainModel.cellEmulateShowItem.visibleRxlevTopN>=5)
            {
                listView1.Columns.Add(columnHeaderGrid94);
                listView1.Columns.Add(columnHeaderSquare94);
                listView1.Columns.Add(columnHeaderRate94);
            }
            if (MainModel.cellEmulateShowItem.visibleRxlevTopN >= 6)
            {
                listView1.Columns.Add(columnHeaderGrid95);
                listView1.Columns.Add(columnHeaderSquare95);
                listView1.Columns.Add(columnHeaderRate95);
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(listView1);
        }

        bool isDataVisible = true;
        private void button1_Click(object sender, EventArgs e)
        {
            if (isDataVisible)
            {
                this.groupControl1.Dock = DockStyle.None;
                this.Width = 253;
                this.Height = 356;
                this.button1.Text = ">";

                this.isDataVisible = false;
            }
            else
            {
                this.groupControl1.Dock = DockStyle.Left;
                this.Width = 890;
                this.Height = 478;
                this.button1.Text = "<";

                this.isDataVisible = true;
            }
        }

        private void initDefaultColor()
        {
            this.colorEdit70.Color = Color.Lime;
            this.colorEdit80.Color = Color.Green;
            this.colorEdit85.Color = Color.Yellow;
            this.colorEdit90.Color = Color.Orange;
            this.colorEdit94.Color = Color.Purple;
            this.colorEdit95.Color = Color.Purple;
            this.colorEditNonCov.Color = Color.Red;
        }

        private void initRxlevLegendVisible()
        {
            if (MainModel.cellEmulateShowItem.visibleRxlevTopN==1)
            {
                labelControl80.Visible = false;
                colorEdit80.Visible = false;
                labelControl85.Visible = false;
                colorEdit85.Visible = false;
                labelControl90.Visible = false;
                colorEdit90.Visible = false;
                labelControl94.Visible = false;
                colorEdit94.Visible = false;
                labelControl95.Visible = false;
                colorEdit95.Visible = false;
                btnDraw.Location = new Point(78, 128);
            }
            else if (MainModel.cellEmulateShowItem.visibleRxlevTopN==2)
            {
                labelControl85.Visible = false;
                colorEdit85.Visible = false;
                labelControl90.Visible = false;
                colorEdit90.Visible = false;
                labelControl94.Visible = false;
                colorEdit94.Visible = false;
                labelControl95.Visible = false;
                colorEdit95.Visible = false;
                btnDraw.Location = new Point(78, 158);
            }
            else if (MainModel.cellEmulateShowItem.visibleRxlevTopN == 3)
            {
                labelControl90.Visible = false;
                colorEdit90.Visible = false;
                labelControl94.Visible = false;
                colorEdit94.Visible = false;
                labelControl95.Visible = false;
                colorEdit95.Visible = false;
                btnDraw.Location = new Point(78, 188);
            }
            else if (MainModel.cellEmulateShowItem.visibleRxlevTopN==4)
            {
                labelControl94.Visible = false;
                colorEdit94.Visible = false;
                labelControl95.Visible = false;
                colorEdit95.Visible = false;
                btnDraw.Location = new Point(78, 218);
            }
            else if (MainModel.cellEmulateShowItem.visibleRxlevTopN==5)
            {
                labelControl95.Visible = false;
                colorEdit95.Visible = false;
                btnDraw.Location = new Point(78, 248);
            }
            else if (MainModel.cellEmulateShowItem.visibleRxlevTopN==6)
            {
                btnDraw.Location = new Point(78, 278);
            }
        }

        private void btnDraw_Click(object sender, EventArgs e)
        {
            MainModel.cellEmulateShowItem.rxlev70Color = this.colorEdit70.Color;
            MainModel.cellEmulateShowItem.rxlev80Color = this.colorEdit80.Color;
            MainModel.cellEmulateShowItem.rxlev85Color = this.colorEdit85.Color;
            MainModel.cellEmulateShowItem.rxlev90Color = this.colorEdit90.Color;
            MainModel.cellEmulateShowItem.rxlev94Color = this.colorEdit94.Color;
            MainModel.cellEmulateShowItem.rxlev95Color = this.colorEdit95.Color;
            MainModel.cellEmulateShowItem.rxlevNonCovColor = this.colorEditNonCov.Color;

            MainModel.cellEmulateShowItem.mapCellEmulateNeedFreshImg = true;
            MainModel.cellEmulateShowItem.exMapCellEmulateNeedFreshImg = true;
            MainModel.MainForm.GetMapForm().GetCellEmulateCovLayer().Invalidate();
        }

        private void exportShapeFile(object o)
        {
            int expRet = 2;
            try
            {
                string filename = o as string;
                if (filename!=null)
                {
                     expRet = MainModel.MainForm.GetMapForm().GetCellEmulateCovLayer().MakeShpFile(filename);
                }
               
            }
            finally
            {
                WaitBox.Close();
            }

            if (expRet == 0)
            {
                MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
            }
            else if (expRet == 1)
            {
                MessageBox.Show(this, "导出成功！");
                this.DialogResult = DialogResult.OK;
            }
            else if (expRet == 2)
            {
                //取消导出
            }
            else
            {
                MessageBox.Show(this, "导出图层发生错误！");
            }
        }

        private void miExportShapefile_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("开始导出图层…", exportShapeFile, dlg.FileName);
        }

        private void miExportTxt_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "文本文档 (*.txt)|*.txt";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("开始导出文档…", exportTxtFile, dlg.FileName);
        }

        private void exportTxtFile(object o)
        {
            int expRet = 2;
            try
            {
                string filename=o as string;
                if (filename != null)
                {
                    expRet = MainModel.MainForm.GetMapForm().GetCellEmulateCovLayer().MakeTxtFile(filename);
                }
            }
            finally
            {
                WaitBox.Close();
            }

            if (expRet == 0)
            {
                MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
            }
            else if (expRet == 1)
            {
                MessageBox.Show(this, "导出成功！");
                this.DialogResult = DialogResult.OK;
            }
            else if (expRet == 2)
            {
                //取消导出
            }
            else
            {
                MessageBox.Show(this, "导出文本文档发生错误！");
            }
        }

        private void miExportCheckBcp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "bcp文档 (*.bcp)|*.bcp";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("开始导出文档…", exportCheckBcpFile, dlg.FileName);
        }

        private void exportCheckBcpFile(object o)
        {
            int expRet = 2;
            try
            {
                string filename = o as string;
                if (filename != null)
                {
                    expRet = MainModel.MainForm.GetMapForm().GetCellEmulateCovLayer().MakeCheckBcpFile(filename);
                }
            }
            finally
            {
                WaitBox.Close();
            }

            if (expRet == 0)
            {
                MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
            }
            else if (expRet == 1)
            {
                MessageBox.Show(this, "导出成功！");
                this.DialogResult = DialogResult.OK;
            }
            else if (expRet == 2)
            {
                //取消导出
            }
            else
            {
                MessageBox.Show(this, "导出bcp发生错误！");
            }
        }

        private void miExportMapBcp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "bcp文档 (*.bcp)|*.bcp";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("开始导出文档…", exportMapBcpFile, dlg.FileName);
        }

        private void exportMapBcpFile(object o)
        {
            int expRet = 2;
            try
            {
                string filename = o as string;
                if (filename != null)
                {
                    expRet = MainModel.MainForm.GetMapForm().GetCellEmulateCovLayer().MakeMapBcpFile(filename);
                }
            }
            finally
            {
                WaitBox.Close();
            }

            if (expRet == 0)
            {
                MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
            }
            else if (expRet == 1)
            {
                MessageBox.Show(this, "导出成功！");
                this.DialogResult = DialogResult.OK;
            }
            else if (expRet == 2)
            {
                //取消导出
            }
            else
            {
                MessageBox.Show(this, "导出bcp发生错误！");
            }
        }

        private void miExportResultBcp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "bcp文档 (*.bcp)|*.bcp";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("开始导出文档…", exportResultBcpFile, dlg.FileName);
        }

        private void exportResultBcpFile(object o)
        {
            int expRet = 2;
            try
            {
                string filename = o as string;
                if (filename != null)
                {
                    expRet = MainModel.MainForm.GetMapForm().GetCellEmulateCovLayer().MakeResultBcpFile(filename, retList);
                }
            }
            finally
            {
                WaitBox.Close();
            }

            if (expRet == 0)
            {
                MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
            }
            else if (expRet == 1)
            {
                MessageBox.Show(this, "导出成功！");
                this.DialogResult = DialogResult.OK;
            }
            else if (expRet == 2)
            {
                //取消导出
            }
            else
            {
                MessageBox.Show(this, "导出bcp发生错误！");
            }
        }
    }
}
