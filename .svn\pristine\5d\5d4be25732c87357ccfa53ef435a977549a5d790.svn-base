﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.Utils.Drawing;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class AreaListForm : MinCloseForm
    {
        public AreaListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            //helper = new TreeListSearchHelper(treeList);
        }

        //TreeListSearchHelper helper;

        public void FillData()
        {
            this.areaListPnl1.FillData(ZTAreaManager.Instance);
        }


        //private void appendTreeNode(AreaBase area, TreeList treeList, TreeListNode parentNode)
        //{
        //    TreeListNode node = treeList.AppendNode(new object[] { area.Name }, parentNode);
        //    node.Tag = area;
        //    if (area.SubAreas != null)
        //    {
        //        foreach (AreaBase subArea in area.SubAreas)
        //        {
        //            appendTreeNode(subArea, treeList, node);
        //        }
        //    }
        //}

        //private void treeList_CustomDrawNodeIndicator(object sender, CustomDrawNodeIndicatorEventArgs e)
        //{
        //    if (e.Node != null)
        //    {
        //        AreaBase area = e.Node.Tag as AreaBase;
        //        e.Graphics.DrawString(area.Rank.Name, this.Font, Brushes.Black, e.Bounds);
        //        IndicatorObjectInfoArgs args = e.ObjectArgs as IndicatorObjectInfoArgs;
        //        args.DisplayText = area.Rank.Name;
        //        e.ImageIndex = -1;
        //    }
        //}



    }
}
