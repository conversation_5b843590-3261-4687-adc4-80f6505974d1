using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.CQT
{
    public class CQTPointCell
    {
        public int PointID { get; set; }
        public int NetWorkType { get; set; }
        public int Lac { get; set; }
        public int CI { get; set; }
        public string CellName { get; set; }
        public string BSCName { get; set; }
        public int Priority { get; set; }

        public CQTPointCell()
        {
        }

        public CQTPointCell(int pointID, int netWorkType, int lac, int ci, string cellName, string bscName)
        {
            PointID = pointID;
            NetWorkType = netWorkType;
            Lac = lac;
            CI = ci;
            CellName = cellName;
            BSCName = bscName;
        }
    }
}
