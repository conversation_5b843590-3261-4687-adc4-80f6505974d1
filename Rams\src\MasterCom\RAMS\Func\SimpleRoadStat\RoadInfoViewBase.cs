﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

using DevExpress.XtraGrid.Columns;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public class RoadInfoViewBase
    {
        public double RoadLength
        {
            get;
            protected set;
        }

        public double RoadDuration
        {
            get;
            protected set;
        }

        public double RoadSampleCount
        {
            get;
            protected set;
        }

        public string RoadName
        {
            get;
            protected set;
        }

        public double CentLng
        {
            get;
            protected set;
        }

        public double CentLat
        {
            get;
            protected set;
        }

        public List<TestPoint> RoadPoints
        {
            get;
            protected set;
        }

        public virtual GridColumn[] GridColumns
        {
            get { return new List<GridColumn>(sGridColumns).ToArray(); }
        }

        public RoadInfoViewBase(List<TestPoint> roadPoints, double roadLength)
        {
            RoadLength = roadLength;
            SetBaseInfo(roadPoints);
        }

        public RoadInfoViewBase(List<TestPoint> roadPoints)
        {
            double roadLength = 0;
            for (int i = 1; i < roadPoints.Count; ++i)
            {
                TestPoint lastPoint = roadPoints[i - 1];
                TestPoint curPoint = roadPoints[i];
                roadLength += MathFuncs.GetDistance(lastPoint.Longitude, lastPoint.Latitude, curPoint.Longitude, curPoint.Latitude);
            }
            RoadLength = roadLength;
            SetBaseInfo(roadPoints);
        }

        protected GridColumn GetBaseColumnByFieldName(string fieldName)
        {
            GridColumn col = null;
            sFieldColumnDic.TryGetValue(fieldName, out col);
            return col;
        }

        private void SetBaseInfo(List<TestPoint> roadPoints)
        {
            RoadDuration = roadPoints.Count <= 1 ? 0 : roadPoints[roadPoints.Count - 1].Time - roadPoints[0].Time;
            RoadSampleCount = roadPoints.Count;
            RoadPoints = roadPoints;
            CentLng = roadPoints[roadPoints.Count / 2].Longitude;
            CentLat = roadPoints[roadPoints.Count / 2].Latitude;
            RoadName = GISManager.GetInstance().GetAreaPlaceDesc(CentLng, CentLat);
            RoadPoints = new List<TestPoint>(roadPoints);
        }

        private static Dictionary<string, GridColumn> sFieldColumnDic = new Dictionary<string, GridColumn>();
        private static List<GridColumn> sGridColumns = new List<GridColumn>();
        static RoadInfoViewBase()
        {
            GridColumn colRoadName = new GridColumn();
            colRoadName.Caption = "道路名称";
            colRoadName.FieldName = "RoadName";
            sGridColumns.Add(colRoadName);

            GridColumn colRoadLength = new GridColumn();
            colRoadLength.Caption = "道路长度(米)";
            colRoadLength.FieldName = "RoadLength";
            colRoadLength.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            colRoadLength.DisplayFormat.FormatString = "F2";
            sGridColumns.Add(colRoadLength);

            GridColumn colRoadDuration = new GridColumn();
            colRoadDuration.Caption = "持续时间(秒)";
            colRoadDuration.FieldName = "RoadDuration";
            sGridColumns.Add(colRoadDuration);

            GridColumn colSampleCount = new GridColumn();
            colSampleCount.Caption = "总采样点数";
            colSampleCount.FieldName = "RoadSampleCount";
            sGridColumns.Add(colSampleCount);

            GridColumn colCentLng = new GridColumn();
            colCentLng.Caption = "中心经度";
            colCentLng.FieldName = "CentLng";
            sGridColumns.Add(colCentLng);

            GridColumn colCentLat = new GridColumn();
            colCentLat.Caption = "中心纬度";
            colCentLat.FieldName = "CentLat";
            sGridColumns.Add(colCentLat);

            foreach (GridColumn col in sGridColumns)
            {
                sFieldColumnDic.Add(col.FieldName, col);
            }
        }
    }
}
