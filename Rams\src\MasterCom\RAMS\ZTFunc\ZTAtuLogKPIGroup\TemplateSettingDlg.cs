﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public partial class TemplateSettingDlg : BaseForm
    {
        public TemplateSettingDlg()
        {
            InitializeComponent();
            viewOption.AutoGenerateColumns = false;
            foreach (string item in Enum.GetNames(typeof(ELogicalType)))
            {
                cbxLogicalType.Properties.Items.Add(item);
            }
            cbxLogicalType.SelectedIndex = 1;
            rngClrSetting.SetScoreColumnCaption("符合条件文件占比(%)");
            rngClrSetting.DescColumnsVisible = false;
        }

        public TemplateSettingDlg(GroupTemplate selTemplate)
            : this()
        {
            fillTemplateList(selTemplate);
        }

        private void fillTemplateList(GroupTemplate selRpt)
        {
            gridCtrlTmpl.DataSource = TemplateManager.Instance.Templates;
            gridCtrlTmpl.RefreshDataSource();
            if (selRpt != null)
            {
                int idx = TemplateManager.Instance.Templates.IndexOf(selRpt);
                if (idx != -1)
                {
                    gvTmpl.FocusedRowHandle = gvTmpl.GetRowHandle(idx);
                }
            }
        }

        GroupTemplate curTemplate = null;
        public GroupTemplate SelectedTemplate { get { return curTemplate; } }


        FormulaEditor formulaEditor = null;
        private void viewOption_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 1 && e.RowIndex >= 0)
            {
                GroupIndicatorOption ind = viewOption.Rows[e.RowIndex].DataBoundItem as GroupIndicatorOption;
                if (ind != null)
                {
                    if (formulaEditor == null)
                    {
                        formulaEditor = new FormulaEditor();
                    }
                    formulaEditor.IndicatorOption = ind;
                    formulaEditor.ShowDialog();
                    //if ( == DialogResult.OK)
                    //{
                    //    if (!string.IsNullOrEmpty(formulaEditor.Caption))
                    //    {
                    //        ind.KPIName = formulaEditor.Caption;
                    //    }
                    //}
                    viewOption.Invalidate();
                }
            }
        }

        private DataGridViewTextBoxEditingControl dgvTbx = null;
        private void viewOption_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (dgvTbx != null)
            {
                dgvTbx.KeyPress -= dgvTbx_KeyPress;
            }
            dgvTbx = null;
        }

        private void viewOption_EditingControlShowing(object sender, DataGridViewEditingControlShowingEventArgs e)
        {
            if (dgvTbx != null)
            {
                dgvTbx.KeyPress -= dgvTbx_KeyPress;
            }
            if (viewOption.CurrentCell != null
                && viewOption.CurrentCell.ColumnIndex == 3
                && e.Control is DataGridViewTextBoxEditingControl)
            {
                dgvTbx = e.Control as DataGridViewTextBoxEditingControl;
                dgvTbx.KeyPress += dgvTbx_KeyPress;
            }
        }

        private void dgvTbx_KeyPress(object sender, KeyPressEventArgs e)
        {
            //if (e.KeyChar == (char)Keys.Back || e.KeyChar == (char)Keys.Enter)
            //{
            //    return;
            //}
            ////负号时，只能在最前面出现，且只能输入一次
            //if (e.KeyChar == 45
            //    && (((TextBox)sender).SelectionStart != 0 || ((TextBox)sender).Text.IndexOf("-") >= 0))
            //{
            //    e.Handled = true;
            //    return;
            //}
            ////小数点时，只能输入一次且只能输入一次
            //if (e.KeyChar == 46 && ((TextBox)sender).Text.IndexOf(".") >= 0)
            //{
            //    e.Handled = true;
            //    return;
            //}
            ////只能输入数字
            //e.Handled = !char.IsDigit(e.KeyChar);
        }

        private void viewOption_CellValidating(object sender, DataGridViewCellValidatingEventArgs e)
        {
            if (e.ColumnIndex == 3)
            {
                double value;
                bool cancel = !double.TryParse(e.FormattedValue.ToString(), out value);
                if (cancel)
                {
                    MessageBox.Show("请输入数字！");
                }
                e.Cancel = cancel;
            }
            else if (e.FormattedValue == null || string.IsNullOrEmpty(e.ToString()))
            {
                e.Cancel = true;
                MessageBox.Show("内容不能为空！");
            }
        }

        private void viewOption_SelectionChanged(object sender, EventArgs e)
        {
            btnRemoveRow.Enabled = viewOption.CurrentRow != null && viewOption.CurrentRow.Index >= 0;
        }

        private void btnRemoveRow_Click(object sender, EventArgs e)
        {
            GroupIndicatorOption ind = viewOption.CurrentRow.DataBoundItem as GroupIndicatorOption;
            if (ind!=null)
            {
                curTemplate.Options.Remove(ind);
                fillViewOption(curTemplate);
            }
        }

        private void btnAddCol_Click(object sender, EventArgs e)
        {
            GroupIndicatorOption ind = new GroupIndicatorOption();
            curTemplate.Options.Add(ind);
            fillViewOption(curTemplate);
        }

        private void gvTmpl_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            curTemplate = gvTmpl.GetRow(e.FocusedRowHandle) as GroupTemplate;
            fillViewOption(curTemplate);
        }

        private void fillViewOption(GroupTemplate curTemplate)
        {
            viewOption.DataSource = null;
            viewOption.Rows.Clear();
            btnRemoveTemplate.Enabled = groupControl3.Enabled = curTemplate != null;
            if (curTemplate == null)
            {
                return;
            }
            cbxLogicalType.SelectedItem = curTemplate.LogicType.ToString();
            if (curTemplate.Options.Count > 0)
            {
                viewOption.DataSource = curTemplate.Options;
            }
            if (viewOption.Rows.Count > 1)
            {
                viewOption.CurrentCell = viewOption.Rows[0].Cells[1];
            }
            rngClrSetting.SetScoreColorRanges(curTemplate.RangeColorSet, 0, 100);
            viewOption.Invalidate();
        }

        private void btnNewReport_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建模板", "模板名称", "未命名模板");
            if (box.ShowDialog() == DialogResult.OK)
            {
                GroupTemplate template = new GroupTemplate(box.TextInput);
                TemplateManager.Instance.Templates.Add(template);
                fillTemplateList(template);
            }
        }

        private void btnRemoveTemplate_Click(object sender, EventArgs e)
        {
            GroupTemplate t = gvTmpl.GetRow(gvTmpl.FocusedRowHandle) as GroupTemplate;
            if (t != null)
            {
                int idx = TemplateManager.Instance.Templates.IndexOf(t);
                if (idx != -1
                    && MessageBox.Show(this, "确定删除该模板？", "确定", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    TemplateManager.Instance.Templates.RemoveAt(idx);
                    gridCtrlTmpl.RefreshDataSource();
                    curTemplate = gvTmpl.GetRow(gvTmpl.FocusedRowHandle) as GroupTemplate;
                    fillViewOption(curTemplate);
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            TemplateManager.Instance.Save();
        }

        private void cbxLogicalType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curTemplate!=null)
            {
                curTemplate.LogicType = (ELogicalType)Enum.Parse(typeof(ELogicalType), cbxLogicalType.SelectedItem.ToString());
            }
        }




    }
}
