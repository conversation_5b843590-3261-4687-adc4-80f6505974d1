﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellDlg : BaseDialog
    {
        public LowSpeedCellDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(ref int minRxLev, ref int maxRxLev, ref int minSpeed, ref int maxSpeed, ref bool saveTestPoint)
        {
            minRxLev = (int)numMinRxLev.Value;
            maxRxLev = (int)numMaxRxLev.Value;
            minSpeed = (int)numMinSpeed.Value;
            maxSpeed = (int)numMaxSpeed.Value;
            saveTestPoint = chkSaveTestPoint.Checked;
        }
    }
}
