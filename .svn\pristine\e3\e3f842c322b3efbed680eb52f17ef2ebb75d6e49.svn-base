﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class DownloadStationAcceptPic : QueryBase
    {
        private readonly List<string> filesPath;
        private readonly string saveDirectory;
        public DownloadStationAcceptPic(List<string> filesPath, string saveDirectory)
            : base()
        {
            this.filesPath = filesPath;
            this.saveDirectory = saveDirectory;
        }

        public override string Name
        {
            get { return "下载单站验收图片"; }
        }

        public override string IconName
        {
            get { return "Images/download.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void prepareSearchPackage(Package package, string filePath)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_FILEDOWNLOADByPath;
            package.Content.PrepareAddParam();
            package.Content.AddParam(filePath);
        }
        
        Dictionary<string, bool> fileNameDownLoaded = new Dictionary<string, bool>();
        protected override void query()
        {
            fileNameDownLoaded = new Dictionary<string, bool>();
            WaitBox.CanCancel = true;

            downloadFile();
        }

        private void downloadFile()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, 
                MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            for (int i = 0; i < filesPath.Count; i++)
            {
                string filePath = filesPath[i];
                string fileName = filePath.Substring(filePath.LastIndexOf('\\') + 1);
                if (!fileNameDownLoaded.ContainsKey(filePath))
                {
                    fileNameDownLoaded[filePath] = true;
                    string saveFileName = saveDirectory + Path.DirectorySeparatorChar + fileName;

                    queryInThread(new object[] { clientProxy, filePath, saveFileName });
                }
            }
            clientProxy.Close();
        }
        
        private void queryInThread(object o)
        {
            try
            {
                object[] param = o as object[];
                ClientProxy clientProxy = (ClientProxy)param[0];
                string filePath = param[1] as string;
                string saveName = param[2] as string;

                Package package = clientProxy.Package;
                prepareSearchPackage(package, filePath);
                clientProxy.Send();
                receiveOneFile(clientProxy, saveName, filePath);
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
        }

        private void receiveOneFile(ClientProxy clientProxy, string saveName, string filePath)
        {
            FileStream fileStream = null;
            Package package = clientProxy.Package;
            try
            {
                receiveData(clientProxy, saveName, filePath, ref fileStream, package);
            }
            catch (Exception e)
            {
                log.Error("[" + filePath + "]异常" + e.Message);
            }
            finally
            {
                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        private void receiveData(ClientProxy clientProxy, string saveName, string filePath, ref FileStream fileStream, Package package)
        {
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.FileBegin || package.Content.Type == ResponseType.FileContinue || package.Content.Type == ResponseType.FileEnd)
                {
                    package.Content.GetParamString();//realFileName
                    package.Content.GetParamInt();//pNum
                    package.Content.GetParamInt();//length
                    if (fileStream == null)
                    {
                        fileStream = new FileStream(saveName, FileMode.Create);
                    }
                    fileStream.Write(package.Content.Buff, package.Content.CurOffset
                        , package.Content.Buff.Length - package.Content.CurOffset);
                    if (package.Content.Type == ResponseType.FileEnd)
                    {
                        break;
                    }
                }
                else if (package.Content.Type == ResponseType.FileNotFound)
                {
                    log.Error("[" + filePath + "]未找到");
                    break;
                }
                else
                {
                    log.Error("[" + filePath + "]未知命令:" + package.Content.Type);
                    break;
                }
            }
        }
    }
}
