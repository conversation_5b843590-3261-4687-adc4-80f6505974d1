﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class PoorRsrqCell_NR : PoorRsrqCell
    {
        public new NRCell Cell
        {
            get;
            set;
        }

        public override string Name
        {
            get { return Cell.Name; }
        }

        public override int Tac
        {
            get { return Cell.TAC; }
        }

        public long Nci
        {
            get { return Cell.NCI; }
        }

        public override int CellID
        {
            get { return Cell.CellID; }
        }

        public string AreaName { get; protected set; }

        public int ARFCN { get { return Cell.SSBARFCN; } }
        public int PCI { get { return Cell.PCI; } }

        public PoorRsrqCell_NR(NRCell cell)
        {
            this.Cell = cell;
            AreaName = GISManager.GetInstance().GetAreaPlaceDesc(cell.Longitude, cell.Latitude);
        }

        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public float? AvgLteRsrp { get { return caculateAvg(lteRsrpCount, lteRsrpSum); } }

        protected int lteSinrCount = 0;
        protected float lteSinrSum = 0;
        public float? AvgLteSinr { get { return caculateAvg(lteSinrCount, lteSinrSum); } }

        public override void AddOtherTPInfo(TestPoint testPoint)
        {
            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(testPoint, true);
            if (lteRsrp != null)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRsrp;
            }

            float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(testPoint, true);
            if (lteSinr != null)
            {
                lteSinrCount++;
                lteSinrSum += (float)lteSinr;
            }
        }

        protected float? caculateAvg(int count, float sum)
        {
            float? res = null;
            if (count > 0)
            {
                res = (float?)Math.Round(sum / count, 2);
            }
            return res;
        }

    }
}
