﻿using System.Collections.Generic;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public class DIYCellSetBrief : DIYSampleByRegion
    {
        public DIYCellSetBrief(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "精简模式查询区域小区集"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 12000, 12076, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            addDTParameter(cellSetGroup, "LAC");
            addDTParameter(cellSetGroup, "CI");
            addDTParameter(cellSetGroup, "BCCH");
            addDTParameter(cellSetGroup, "BSIC");
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("C_I", 0);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            addDTParameter(cellSetGroup, MainModel.TD_SCell_LAC);
            addDTParameter(cellSetGroup, MainModel.TD_SCell_CI);
            addDTParameter(cellSetGroup, MainModel.TD_SCell_UARFCN);
            addDTParameter(cellSetGroup, MainModel.TD_SCell_CPI);
            addDTParameter(cellSetGroup, MainModel.TD_GSM_SCell_LAC);
            addDTParameter(cellSetGroup, MainModel.TD_GSM_SCell_CI);
            addDTParameter(cellSetGroup, MainModel.TD_GSM_SCell_ARFCN);
            addDTParameter(cellSetGroup, MainModel.TD_GSM_SCell_BSIC);
            addDTParameter(cellSetGroup, "W_SysLAI");
            addDTParameter(cellSetGroup, "W_SysCellID");
            addDTParameter(cellSetGroup, "W_Reference_PSC");
            addDTParameter(cellSetGroup, "W_frequency");
            addDTParameter(cellSetGroup, "W_Sgsmbcch_arfcn");
            addDTParameter(cellSetGroup, "W_Sgsmbsic");
            addDTParameter(cellSetGroup, "CD_BID");
            addDTParameter(cellSetGroup, "CD_SID");
            addDTParameter(cellSetGroup, "CD_ReferPN");
            addDTParameter(cellSetGroup, "CD_Frequency");
            addDTParameter(cellSetGroup, "GSCAN_BCCH");
            addDTParameter(cellSetGroup, "GSCAN_BSIC");

            return cellSetGroup;
        }

        private void addDTParameter(DIYSampleGroup cellSetGroup, string name)
        {
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter(name);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
        }



        readonly Dictionary<string, CellSetGenaral> cellSetGenaralDic = new Dictionary<string, CellSetGenaral>();
        readonly List<CellBriefStater> cellBriefStaterList = new List<CellBriefStater>();

        readonly Dictionary<object, CellBriefStater> cellStaterMap = new Dictionary<object, CellBriefStater>();
        readonly Dictionary<object, CellBriefStater> tdcellStaterMap = new Dictionary<object, CellBriefStater>();
        readonly Dictionary<object, CellBriefStater> tdGsmcellStaterMap = new Dictionary<object, CellBriefStater>();
        readonly Dictionary<object, CellBriefStater> wcellStaterMap = new Dictionary<object, CellBriefStater>();
        readonly Dictionary<object, CellBriefStater> wGsmcellStaterMap = new Dictionary<object, CellBriefStater>();
        readonly Dictionary<object, CellBriefStater> cdcellStaterMap = new Dictionary<object, CellBriefStater>();

        readonly Dictionary<string, CellBriefStater> unknownCellStaterMap = new Dictionary<string, CellBriefStater>();
        readonly Dictionary<string, CellBriefStater> unknownTdcellStaterMap = new Dictionary<string, CellBriefStater>();
        readonly Dictionary<string, CellBriefStater> unknownTdGsmcellStaterMap = new Dictionary<string, CellBriefStater>();
        readonly Dictionary<string, CellBriefStater> unknownWcellStaterMap = new Dictionary<string, CellBriefStater>();
        readonly Dictionary<string, CellBriefStater> unknownWGsmcellStaterMap = new Dictionary<string, CellBriefStater>();
        readonly Dictionary<string, CellBriefStater> unknownCdcellStaterMap = new Dictionary<string, CellBriefStater>();

        readonly Dictionary<int, Dictionary<string, List<string>>> districtGridGsmBtsMap = new Dictionary<int, Dictionary<string, List<string>>>();
        readonly Dictionary<int, Dictionary<string, List<string>>> districtGridTdBtsMap = new Dictionary<int, Dictionary<string, List<string>>>();
        readonly Dictionary<int, Dictionary<string, List<string>>> districtGridTdGsmBtsMap = new Dictionary<int, Dictionary<string, List<string>>>();
        readonly Dictionary<int, Dictionary<string, List<string>>> districtGridWNodeBMap = new Dictionary<int, Dictionary<string, List<string>>>();
        readonly Dictionary<int, Dictionary<string, List<string>>> districtGridWGsmNodeBMap = new Dictionary<int, Dictionary<string, List<string>>>();
        readonly Dictionary<int, Dictionary<string, List<string>>> districtGridCdNodeBMap = new Dictionary<int, Dictionary<string, List<string>>>();

        //已经查询到的小区lac，ci字典
        readonly Dictionary<string, string> existCellMap = new Dictionary<string, string>();
        readonly Dictionary<string, string> existTdCellMap = new Dictionary<string, string>();
        readonly Dictionary<string, string> existTdGsmCellMap = new Dictionary<string, string>();
        readonly Dictionary<string, string> existWCellMap = new Dictionary<string, string>();
        readonly Dictionary<string, string> existWGsmCellMap = new Dictionary<string, string>();
        readonly Dictionary<string, string> existCdCellMap = new Dictionary<string, string>();

        private void initDataMap()
        {
            cellSetGenaralDic.Clear();
            cellBriefStaterList.Clear();

            cellStaterMap.Clear();
            tdcellStaterMap.Clear();
            tdGsmcellStaterMap.Clear();
            wcellStaterMap.Clear();
            wGsmcellStaterMap.Clear();
            cdcellStaterMap.Clear();
            unknownCellStaterMap.Clear();
            unknownTdcellStaterMap.Clear();
            unknownTdGsmcellStaterMap.Clear();
            unknownWcellStaterMap.Clear();
            unknownWGsmcellStaterMap.Clear();
            unknownCdcellStaterMap.Clear();

            districtGridGsmBtsMap.Clear();
            districtGridTdBtsMap.Clear();
            districtGridTdGsmBtsMap.Clear();
            districtGridWNodeBMap.Clear();
            districtGridWGsmNodeBMap.Clear();
            districtGridCdNodeBMap.Clear();

            existCellMap.Clear();
            existTdCellMap.Clear();
            existTdGsmCellMap.Clear();
            existWCellMap.Clear();
            existWGsmCellMap.Clear();
            existCdCellMap.Clear();
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            //每个地市按id生成基站集合空间
            districtGridGsmBtsMap.Add(curDistricID, new Dictionary<string, List<string>>());
            districtGridTdBtsMap.Add(curDistricID, new Dictionary<string, List<string>>());
            districtGridTdGsmBtsMap.Add(curDistricID, new Dictionary<string, List<string>>());
            districtGridWNodeBMap.Add(curDistricID, new Dictionary<string, List<string>>());
            districtGridWGsmNodeBMap.Add(curDistricID, new Dictionary<string, List<string>>());
            districtGridCdNodeBMap.Add(curDistricID, new Dictionary<string, List<string>>());
        }

        int curDistricID = -1;
        protected override void query()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }

            regionMopDic = new Dictionary<string, MapOperation2>();
            InitRegionMop2();
            initDataMap();


            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                foreach (int DistrictID in condition.DistrictIDs)
                {
                    WaitBox.CanCancel = true;
                    curDistricID = DistrictID;
                    clientProxy = new ClientProxy();
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        curDistricID = -1;
                        return;
                    }
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                }

                MergeCellInfo();
                FireShowFormAfterQuery();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CellSetBriefForm).FullName);
            CellSetBriefForm cellSetBriefForm = obj == null ? null : obj as CellSetBriefForm;
            if (cellSetBriefForm == null || cellSetBriefForm.IsDisposed)
            {
                cellSetBriefForm = new CellSetBriefForm(MainModel);
            }
            cellSetBriefForm.fillData(cellSetGenaralDic, cellBriefStaterList);
            if (!cellSetBriefForm.Visible)
            {
                cellSetBriefForm.Show(MainModel.MainForm);
            }
        }

        /// <summary>
        /// 合并小区与基站数目信息
        /// </summary>
        private void MergeCellInfo()
        {
            mergeData(cellStaterMap, unknownCellStaterMap, districtGridGsmBtsMap, "GSM");
            mergeData(tdcellStaterMap, unknownTdcellStaterMap, districtGridTdBtsMap, "TD");
            mergeData(tdGsmcellStaterMap, unknownTdGsmcellStaterMap, districtGridTdGsmBtsMap, "TD-GSM");
            mergeData(wcellStaterMap, unknownWcellStaterMap, districtGridWNodeBMap, "WCDMA");
            mergeData(wGsmcellStaterMap, unknownWGsmcellStaterMap, districtGridWGsmNodeBMap, "WCDMA-GSM");
            mergeData(cdcellStaterMap, unknownCdcellStaterMap, districtGridCdNodeBMap, "CDMA");
        }

        private void mergeData(Dictionary<object, CellBriefStater> cellStaterMap, Dictionary<string, CellBriefStater> unknownCellStaterMap, Dictionary<int, Dictionary<string, List<string>>> districtGridBtsMap, string desc)
        {
            List<CellBriefStater> staterList = new List<CellBriefStater>();
            staterList.AddRange(cellStaterMap.Values);
            staterList.AddRange(unknownCellStaterMap.Values);
            foreach (CellBriefStater cbs in staterList)
            {
                string gStr = cbs.district + cbs.carrier + cbs.cellType + cbs.grid;
                if (cellSetGenaralDic.ContainsKey(gStr))
                {
                    CellSetGenaral csg = cellSetGenaralDic[gStr];
                    csg.cellCount++;
                    if (cbs.isIndoor)
                        csg.indoorCellCount++;
                }
                else
                {
                    CellSetGenaral csg = new CellSetGenaral();
                    csg.district = DistrictManager.GetInstance().getDistrictName(cbs.district);
                    csg.carrier = cbs.carrier;
                    csg.cellType = desc;
                    csg.grid = cbs.grid;
                    if (districtGridBtsMap[cbs.district].ContainsKey(cbs.grid))
                    {
                        csg.btsCount = districtGridBtsMap[cbs.district][cbs.grid].Count;
                    }
                    csg.cellCount++;
                    if (cbs.isIndoor)
                        csg.indoorCellCount++;
                    cellSetGenaralDic.Add(gStr, csg);
                }
                cellBriefStaterList.Add(cbs);
            }
        }

        Dictionary<string, MapOperation2> regionMopDic = null;
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        /// <summary>
        /// 定位是否为包括的采样点
        /// </summary>
        /// <param name="iType"></param>
        /// <returns>返回区域名称</returns>
        private string isContainPoint(DbPoint dPoint)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return strKey;
                }
            }
            return null;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (tp is TestPointDetail)
                {
                    doWithGSMData(tp);
                }
                else if (tp is TDTestPointDetail)
                {
                    doWithTDData(tp);
                }
                else if (tp is WCDMATestPointDetail)
                {
                    doWithWCDMAData(tp);
                }
                else if (tp is CDMATestPointDetail)
                {
                    doWithCDMAData(tp);
                }
            }
            catch (System.Exception ex)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.ToString());
            }
        }

        private void doWithGSMData(TestPoint tpPoint)
        {
            if (tpPoint["LAC"] == null || tpPoint["CI"] == null)
            {
                return;
            }
            string lac = tpPoint["LAC"].ToString();
            string ci = tpPoint["CI"].ToString();
            string lacciStr = lac + "_" + ci;
            if (existCellMap.ContainsKey(lacciStr) && existCellMap[lacciStr] != "unknownCell")
            {
                return;
            }

            Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["LAC"], (ushort?)(int?)tpPoint["CI"], (short?)tpPoint["BCCH"], (byte?)tpPoint["BSIC"], tpPoint.Longitude, tpPoint.Latitude);
            if (cell != null)
            {
                dealCell(tpPoint, lac, ci, lacciStr, cell);
                return;
            }

            //================== 主服没有匹配到工参信息 ==================
            if (tpPoint["LAC"] != null && tpPoint["CI"] != null && (int)tpPoint["LAC"] != -255 && (int)tpPoint["CI"] != -255)
            {
                addUnknownGSMCell(tpPoint);
                existCellMap[lacciStr] = "unknownCell";
            }
        }

        private void dealCell(TestPoint tpPoint, string lac, string ci, string lacciStr, Cell cell)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return;
            }

            if (MainModel.SystemConfigInfo.distLimit && cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_GSM)//距离限制设置
            {
                return;
            }
            string gridname = isContainPoint(new DbPoint(cell.Longitude, cell.Latitude));
            if (gridname == null)
            {
                return;
            }

            addGSMCell(cell, tpPoint, gridname);

            string btsMark = ci.Substring(0, ci.Length - 1);
            if (btsMark.Length == 0)
            {
                btsMark = ci;
            }
            string btsKey = lac + "_" + btsMark;
            if (districtGridGsmBtsMap[curDistricID].ContainsKey(gridname) && districtGridGsmBtsMap[curDistricID][gridname].Contains(btsKey))
            {
                districtGridGsmBtsMap[curDistricID][gridname].Remove(btsKey);
            }
            if (unknownCellStaterMap.ContainsKey(lacciStr))
            {
                unknownCellStaterMap.Remove(lacciStr);
            }

            existCellMap[lacciStr] = cell.Name;
        }

        private void addGSMCell(Cell cell, TestPoint tpPoint, string gridname)
        {
            CellBriefStater stater = null;
            if (!cellStaterMap.ContainsKey(cell))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = cell.Name;
                stater.cellType = "GSM";
                switch (tpPoint.CarrierType)
                {
                    case 1:
                        stater.carrier = "移动";
                        break;
                    case 2:
                        stater.carrier = "联通";
                        break;
                    default:
                        break;
                }
                stater.lac = cell.LAC;
                stater.ci = cell.CI;
                stater.grid = gridname;
                stater.isIndoor = cell.Type == BTSType.Indoor;
                cellStaterMap[cell] = stater;
            }

            BTS bts = cell.BelongBTS;

            if (districtGridGsmBtsMap[curDistricID].ContainsKey(gridname))
            {
                if (!districtGridGsmBtsMap[curDistricID][gridname].Contains(bts.Name))
                {
                    districtGridGsmBtsMap[curDistricID][gridname].Add(bts.Name);
                }
            }
            else
            {
                districtGridGsmBtsMap[curDistricID].Add(gridname, new List<string>());
                districtGridGsmBtsMap[curDistricID][gridname].Add(bts.Name);
            }
        }

        private void addUnknownGSMCell(TestPoint tpPoint)
        {
            string gridname = isContainPoint(new DbPoint(tpPoint.Longitude, tpPoint.Latitude)); //未知小区缺失经纬度，用采样点的经纬度代替之定位所在网格
            if (gridname == null)
                return;

            CellBriefStater stater = null;
            string key = tpPoint["LAC"].ToString() + "_" + tpPoint["CI"].ToString();
            if (!unknownCellStaterMap.ContainsKey(key))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = "未知小区 " + key;
                stater.cellType = "GSM";
                switch (tpPoint.CarrierType)
                {
                    case 1:
                        stater.carrier = "移动";
                        break;
                    case 2:
                        stater.carrier = "联通";
                        break;
                    default:
                        break;
                }
                stater.lac = (int)tpPoint["LAC"];
                stater.ci = (int)tpPoint["CI"];
                stater.grid = gridname;
                unknownCellStaterMap[key] = stater;
            }
        }

        private void doWithTDGSMData(TestPoint tpPoint)
        {
            if (tpPoint["TD_GSM_SCell_LAC"] == null || tpPoint["TD_GSM_SCell_CI"] == null)
                return;
            string lac = tpPoint["TD_GSM_SCell_LAC"].ToString();
            string ci = tpPoint["TD_GSM_SCell_CI"].ToString();
            string lacciStr = lac + "_" + ci;
            if (existTdGsmCellMap.ContainsKey(lacciStr) && existTdGsmCellMap[lacciStr] != "unknownCell")
            {
                return;
            }

            Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["TD_GSM_SCell_LAC"], (ushort?)(int?)tpPoint["TD_GSM_SCell_CI"], (short?)(int?)tpPoint["TD_GSM_SCell_ARFCN"], (byte?)tpPoint["TD_GSM_SCell_BSIC"], tpPoint.Longitude, tpPoint.Latitude);
            if (cell != null)
            {
                dealTDGsmCell(tpPoint, lac, ci, lacciStr, cell);
                return;
            }

            //================== 主服没有匹配到工参信息 ==================
            if (tpPoint["TD_GSM_SCell_LAC"] != null && tpPoint["TD_GSM_SCell_CI"] != null && (int)tpPoint["TD_GSM_SCell_LAC"] != -255 && (int)tpPoint["TD_GSM_SCell_CI"] != -255)
            {
                addUnknownTDGSMCell(tpPoint);

                existTdGsmCellMap[lacciStr] = "unknownCell";
            }
        }

        private void dealTDGsmCell(TestPoint tpPoint, string lac, string ci, string lacciStr, Cell cell)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return;
            }
            if (MainModel.SystemConfigInfo.distLimit
                && cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_GSM)//距离限制设置
            {
                return;
            }
            string gridname = isContainPoint(new DbPoint(cell.Longitude, cell.Latitude));
            if (gridname == null)
                return;

            addTDGSMCell(cell, gridname);

            string btsMark = ci.Substring(0, ci.Length - 1);
            if (btsMark.Length == 0)
            {
                btsMark = ci;
            }
            string btsKey = lac + "_" + btsMark;
            if (districtGridTdGsmBtsMap[curDistricID].ContainsKey(gridname)
                && districtGridTdGsmBtsMap[curDistricID][gridname].Contains(btsKey))
            {
                districtGridTdGsmBtsMap[curDistricID][gridname].Remove(btsKey);
            }
            if (unknownTdGsmcellStaterMap.ContainsKey(lacciStr))
                unknownTdGsmcellStaterMap.Remove(lacciStr);

            existTdGsmCellMap[lacciStr] = cell.Name;
        }

        private void addTDGSMCell(Cell cell, string gridname)
        {
            CellBriefStater stater = null;
            if (!tdGsmcellStaterMap.ContainsKey(cell))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = cell.Name;
                stater.cellType = "TD-GSM";
                stater.carrier = "移动";
                stater.lac = cell.LAC;
                stater.ci = cell.CI;
                stater.grid = gridname;
                stater.isIndoor = cell.Type == BTSType.Indoor;
                tdGsmcellStaterMap[cell] = stater;
            }

            BTS bts = cell.BelongBTS;

            if (districtGridTdGsmBtsMap[curDistricID].ContainsKey(gridname))
            {
                if (!districtGridTdGsmBtsMap[curDistricID][gridname].Contains(bts.Name))
                {
                    districtGridTdGsmBtsMap[curDistricID][gridname].Add(bts.Name);
                }
            }
            else
            {
                districtGridTdGsmBtsMap[curDistricID].Add(gridname, new List<string>());
                districtGridTdGsmBtsMap[curDistricID][gridname].Add(bts.Name);
            }
        }

        private void addUnknownTDGSMCell(TestPoint tpPoint)
        {
            string gridname = isContainPoint(new DbPoint(tpPoint.Longitude, tpPoint.Latitude)); //未知小区缺失经纬度，用采样点的经纬度代替之定位所在网格
            if (gridname == null)
                return;

            CellBriefStater stater = null;
            string key = tpPoint[MainModel.TD_GSM_SCell_LAC].ToString() + "_" + tpPoint[MainModel.TD_GSM_SCell_CI].ToString();
            if (!unknownTdGsmcellStaterMap.ContainsKey(key))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = "未知小区 " + key;
                stater.cellType = "TD-GSM";
                stater.carrier = "移动";
                stater.lac = (int)tpPoint[MainModel.TD_GSM_SCell_LAC];
                stater.ci = (int)tpPoint[MainModel.TD_GSM_SCell_CI];
                stater.grid = gridname;
                unknownTdGsmcellStaterMap[key] = stater;
            }
        }

        private void doWithTDData(TestPoint tpPoint)
        {
            //================== 首先判断是否占用到GSM网络 ==================
            if ((int?)tpPoint[MainModel.TD_GSM_SCell_LAC] != null && (int?)tpPoint[MainModel.TD_GSM_SCell_CI] != null
                && (int?)tpPoint[MainModel.TD_GSM_SCell_LAC] != -255 && (int?)tpPoint[MainModel.TD_GSM_SCell_CI] != -255)
            {
                doWithTDGSMData(tpPoint);
                return;
            }

            if (tpPoint[MainModel.TD_SCell_LAC] == null || tpPoint[MainModel.TD_SCell_CI] == null)
                return;
            string lac = tpPoint[MainModel.TD_SCell_LAC].ToString();
            string ci = tpPoint[MainModel.TD_SCell_CI].ToString();
            string lacciStr = lac + "_" + ci;
            if (existTdCellMap.ContainsKey(lacciStr) && existTdCellMap[lacciStr] != "unknownCell")
            {
                return;
            }
            TDCell tdcell = tpPoint.GetMainCell_TD_TDCell();
            if (tdcell != null)
            {
                dealTDCell(tpPoint, lac, ci, lacciStr, tdcell);
                return;
            }

            //================== 主服没有匹配到工参信息 ==================
            if (tpPoint[MainModel.TD_SCell_LAC] != null && tpPoint[MainModel.TD_SCell_CI] != null && (int)tpPoint[MainModel.TD_SCell_LAC] != -255 && (int)tpPoint[MainModel.TD_SCell_CI] != -255)
            {
                addUnknownTDCell(tpPoint);

                existTdCellMap[lacciStr] = "unknownCell";
            }
        }

        private void dealTDCell(TestPoint tpPoint, string lac, string ci, string lacciStr, TDCell tdcell)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return;
            }
            if (MainModel.SystemConfigInfo.distLimit
                && tdcell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_TD)
            {
                return;
            }
            string gridname = isContainPoint(new DbPoint(tdcell.Longitude, tdcell.Latitude));
            if (gridname == null)
                return;

            addTDCell(tdcell, gridname);

            string btsMark = ci.Substring(0, ci.Length - 1);
            if (btsMark.Length == 0)
            {
                btsMark = ci;
            }
            string btsKey = lac + "_" + btsMark;
            if (districtGridTdBtsMap[curDistricID].ContainsKey(btsKey)
                && districtGridTdBtsMap[curDistricID][gridname].Contains(btsKey))
            {
                districtGridTdBtsMap[curDistricID][gridname].Remove(btsKey); //除去之前查出的被当做未知小区的所属基站
            }
            if (unknownTdcellStaterMap.ContainsKey(lacciStr))
                unknownTdcellStaterMap.Remove(lacciStr);

            existTdCellMap[lacciStr] = tdcell.Name;
        }

        private void addTDCell(TDCell tdcell, string gridname)
        {
            CellBriefStater stater = null;
            if (!tdcellStaterMap.ContainsKey(tdcell))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = tdcell.Name;
                stater.cellType = "TD";
                stater.carrier = "移动";
                stater.lac = tdcell.LAC;
                stater.ci = tdcell.CI;
                stater.grid = gridname;
                stater.isIndoor = tdcell.Type == TDNodeBType.Indoor;
                tdcellStaterMap[tdcell] = stater;
            }

            TDNodeB nodeb = tdcell.BelongBTS;

            if (districtGridTdBtsMap[curDistricID].ContainsKey(gridname))
            {
                if (!districtGridTdBtsMap[curDistricID][gridname].Contains(nodeb.Name))
                {
                    districtGridTdBtsMap[curDistricID][gridname].Add(nodeb.Name);
                }
            }
            else
            {
                districtGridTdBtsMap[curDistricID].Add(gridname, new List<string>());
                districtGridTdBtsMap[curDistricID][gridname].Add(nodeb.Name);
            }
        }

        private void addUnknownTDCell(TestPoint tpPoint)
        {
            string gridname = isContainPoint(new DbPoint(tpPoint.Longitude, tpPoint.Latitude)); //未知小区缺失经纬度，用采样点的经纬度代替之定位所在网格
            if (gridname == null)
                return;

            CellBriefStater stater = null;
            string key = tpPoint[MainModel.TD_SCell_LAC].ToString() + "_" + tpPoint[MainModel.TD_SCell_CI].ToString();
            if (!unknownTdcellStaterMap.ContainsKey(key))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = "未知小区 " + key;
                stater.cellType = "TD";
                stater.carrier = "移动";
                stater.lac = (int)tpPoint[MainModel.TD_SCell_LAC];
                stater.ci = (int)tpPoint[MainModel.TD_SCell_CI];
                stater.grid = gridname;
                unknownTdcellStaterMap[key] = stater;
            }
        }

        private void doWithWCDMAData(TestPoint tpPoint)
        {
            //================== 首先判断是否占用到GSM网络 ==================
            if (tpPoint["W_Sgsmbcch_arfcn"] != null && tpPoint["W_Sgsmbsic"] != null &&
                (short)tpPoint["W_Sgsmbcch_arfcn"] != -255 && (short)tpPoint["W_Sgsmbsic"] != 255)
            {
                doWithWCDMAGSMData(tpPoint);
                return;
            }

            if (tpPoint["W_SysLAI"] == null || tpPoint["W_SysCellID"] == null)
                return;
            string lac = tpPoint["W_SysLAI"].ToString();
            string ci = tpPoint["W_SysCellID"].ToString();
            string lacciStr = lac + "_" + ci;
            if (existWCellMap.ContainsKey(lacciStr) && existWCellMap[lacciStr] != "unknownCell")
            {
                return;
            }

            WCell wcell = CellManager.GetInstance().GetNearestWCell(tpPoint.DateTime, (int?)tpPoint["W_SysLAI"], (int?)tpPoint["W_SysCellID"], (int?)tpPoint["W_frequency"], (int?)tpPoint["W_Reference_PSC"], tpPoint.Longitude, tpPoint.Latitude);
            if (wcell != null)
            {
                dealWCell(tpPoint, lac, ci, lacciStr, wcell);
                return;
            }

            if (tpPoint["W_SysLAI"] != null && tpPoint["W_SysCellID"] != null && (int)tpPoint["W_SysLAI"] != -255 && (int)tpPoint["W_SysCellID"] != -255)
            {
                addUnknownWCDMACell(tpPoint);

                existWCellMap[lacciStr] = "unknownCell";
            }
        }

        private void dealWCell(TestPoint tpPoint, string lac, string ci, string lacciStr, WCell wcell)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return;
            }
            if (MainModel.SystemConfigInfo.distLimit
                && wcell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_W)//距离限制设置
            {
                return;
            }
            string gridname = isContainPoint(new DbPoint(wcell.Longitude, wcell.Latitude));
            if (gridname == null)
                return;

            addWCDMACell(wcell, gridname);

            string btsMark = ci.Substring(0, ci.Length - 1);
            if (btsMark.Length == 0)
            {
                btsMark = ci;
            }
            string btsKey = lac + "_" + btsMark;
            if (districtGridWNodeBMap[curDistricID].ContainsKey(gridname)
                && districtGridWNodeBMap[curDistricID][gridname].Contains(btsKey))
            {
                districtGridWNodeBMap[curDistricID][gridname].Remove(btsKey);
            }
            if (unknownWcellStaterMap.ContainsKey(lacciStr))
                unknownWcellStaterMap.Remove(lacciStr);

            existWCellMap[lacciStr] = wcell.Name;
        }

        private void addWCDMACell(WCell wcell, string gridname)
        {
            CellBriefStater stater = null;
            if (!wcellStaterMap.ContainsKey(wcell))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = wcell.Name;
                stater.cellType = "WCDMA";
                stater.carrier = "联通";
                stater.lac = wcell.LAC;
                stater.ci = wcell.CI;
                stater.grid = gridname;
                stater.isIndoor = wcell.Type == WNodeBType.Indoor;
                wcellStaterMap[wcell] = stater;
            }

            WNodeB nodeb = wcell.BelongNodeBs[0];

            if (districtGridWNodeBMap[curDistricID].ContainsKey(gridname))
            {
                if (!districtGridWNodeBMap[curDistricID][gridname].Contains(nodeb.Name))
                {
                    districtGridWNodeBMap[curDistricID][gridname].Add(nodeb.Name);
                }
            }
            else
            {
                districtGridWNodeBMap[curDistricID].Add(gridname, new List<string>());
                districtGridWNodeBMap[curDistricID][gridname].Add(nodeb.Name);
            }
        }

        private void addUnknownWCDMACell(TestPoint tpPoint)
        {
            string gridname = isContainPoint(new DbPoint(tpPoint.Longitude, tpPoint.Latitude)); //未知小区缺失经纬度，用采样点的经纬度代替之定位所在网格
            if (gridname == null)
                return;

            CellBriefStater stater = null;
            string key = tpPoint["W_SysLAI"].ToString() + "_" + tpPoint["W_SysCellID"].ToString();
            if (!unknownWcellStaterMap.ContainsKey(key))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = "未知小区 " + key;
                stater.cellType = "WCDMA";
                stater.carrier = "联通";
                stater.lac = (int)tpPoint["W_SysLAI"];
                stater.ci = (int)tpPoint["W_SysCellID"];
                stater.grid = gridname;
                unknownWcellStaterMap[key] = stater;
            }
        }

        private void doWithWCDMAGSMData(TestPoint tpPoint)
        {
            if (tpPoint["W_SysLAI"] == null || tpPoint["W_SysCellID"] == null)
                return;
            string lac = tpPoint["W_SysLAI"].ToString();
            string ci = tpPoint["W_SysCellID"].ToString();
            string lacciStr = lac + "_" + ci;
            if (existWGsmCellMap.ContainsKey(lacciStr) && existWGsmCellMap[lacciStr] != "unknownCell")
            {
                return;
            }

            Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["W_SysLAI"], (ushort?)(int?)tpPoint["W_SysCellID"], (short?)tpPoint["W_Sgsmbcch_arfcn"], (byte?)(short?)tpPoint["W_Sgsmbsic"], tpPoint.Longitude, tpPoint.Latitude);
            if (cell != null)
            {
                dealWGsmCell(tpPoint, lac, ci, lacciStr, cell);
                return;
            }

            //================== 主服没有匹配到工参信息 ==================
            if (tpPoint["W_SysLAI"] != null && tpPoint["W_SysCellID"] != null && (int)tpPoint["W_SysLAI"] != -255 && (int)tpPoint["W_SysCellID"] != -255)
            {
                addUnknownWCDMAGSMCell(tpPoint);

                existWGsmCellMap[lacciStr] = "unknownCell";
            }
        }

        private void dealWGsmCell(TestPoint tpPoint, string lac, string ci, string lacciStr, Cell cell)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return;
            }
            if (MainModel.SystemConfigInfo.distLimit
                && cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_W)//距离限制设置
            {
                return;
            }
            string gridname = isContainPoint(new DbPoint(cell.Longitude, cell.Latitude));
            if (gridname == null)
                return;

            addWCDMAGSMCell(cell, gridname);

            string btsMark = ci.Substring(0, ci.Length - 1);
            if (btsMark.Length == 0)
            {
                btsMark = ci;
            }
            string btsKey = lac + "_" + btsMark;
            if (districtGridWGsmNodeBMap[curDistricID].ContainsKey(gridname)
                && districtGridWGsmNodeBMap[curDistricID][gridname].Contains(btsKey))
            {
                districtGridWGsmNodeBMap[curDistricID][gridname].Remove(btsKey);
            }
            if (unknownWGsmcellStaterMap.ContainsKey(lacciStr))
                unknownWGsmcellStaterMap.Remove(lacciStr);

            existWGsmCellMap[lacciStr] = cell.Name;
        }

        private void addWCDMAGSMCell(Cell cell, string gridname)
        {
            CellBriefStater stater = null;
            if (!wGsmcellStaterMap.ContainsKey(cell))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = cell.Name;
                stater.cellType = "WCDMA-GSM";
                stater.carrier = "联通";
                stater.lac = cell.LAC;
                stater.ci = cell.CI;
                stater.grid = gridname;
                stater.isIndoor = cell.Type == BTSType.Indoor;
                wGsmcellStaterMap[cell] = stater;
            }

            BTS bts = cell.BelongBTS;

            if (districtGridWGsmNodeBMap[curDistricID].ContainsKey(gridname))
            {
                if (!districtGridWGsmNodeBMap[curDistricID][gridname].Contains(bts.Name))
                {
                    districtGridWGsmNodeBMap[curDistricID][gridname].Add(bts.Name);
                }
            }
            else
            {
                districtGridWGsmNodeBMap[curDistricID].Add(gridname, new List<string>());
                districtGridWGsmNodeBMap[curDistricID][gridname].Add(bts.Name);
            }
        }

        private void addUnknownWCDMAGSMCell(TestPoint tpPoint)
        {
            string gridname = isContainPoint(new DbPoint(tpPoint.Longitude, tpPoint.Latitude)); //未知小区缺失经纬度，用采样点的经纬度代替之定位所在网格
            if (gridname == null)
                return;

            CellBriefStater stater = null;
            string key = tpPoint["W_SysLAI"].ToString() + "_" + tpPoint["W_SysCellID"].ToString();
            if (!unknownWGsmcellStaterMap.ContainsKey(key))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = "未知小区 " + key;
                stater.cellType = "WCDMA-GSM";
                stater.carrier = "联通";
                stater.lac = (int)tpPoint["W_SysLAI"];
                stater.ci = (int)tpPoint["W_SysCellID"];
                stater.grid = gridname;
                unknownWGsmcellStaterMap[key] = stater;
            }
        }

        private void doWithCDMAData(TestPoint tpPoint)
        {
            if (tpPoint["CD_SID"] == null || tpPoint["CD_BID"] == null)
                return;
            string lac = tpPoint["CD_SID"].ToString();
            string ci = tpPoint["CD_BID"].ToString();
            string lacciStr = lac + "_" + ci;
            if (existCdCellMap.ContainsKey(lacciStr) && existCdCellMap[lacciStr] != "unknownCell")
            {
                return;
            }
            CDCell cdcell = CellManager.GetInstance().GetNearestCDCell(tpPoint.DateTime, (int?)tpPoint["CD_SID"], (int?)tpPoint["CD_BID"], (int?)tpPoint["CD_Frequency"], (int?)tpPoint["CD_ReferPN"], tpPoint.Longitude, tpPoint.Latitude);
            if (cdcell != null)
            {
                dealCDCell(tpPoint, lac, ci, lacciStr, cdcell);
                return;
            }

            if (tpPoint["CD_SID"] != null && tpPoint["CD_BID"] != null && (int)tpPoint["CD_SID"] != -10000000 && (int)tpPoint["CD_BID"] != -10000000)
            {
                addUnknownCDMACell(tpPoint);
                existCdCellMap[lacciStr] = "unknownCell";
            }
        }

        private void dealCDCell(TestPoint tpPoint, string lac, string ci, string lacciStr, CDCell cdcell)
        {
            if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
            {
                return;
            }
            if (MainModel.SystemConfigInfo.distLimit
                && cdcell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) >= CD.MAX_COV_DISTANCE_CD)//距离限制设置
            {
                return;
            }
            string gridname = isContainPoint(new DbPoint(cdcell.Longitude, cdcell.Latitude));
            if (gridname == null)
                return;

            addCDMACell(cdcell, gridname);

            string btsMark = ci.Substring(0, ci.Length - 1);
            if (btsMark.Length == 0)
            {
                btsMark = ci;
            }
            string btsKey = lac + "_" + btsMark;
            if (districtGridCdNodeBMap[curDistricID].ContainsKey(gridname)
                && districtGridCdNodeBMap[curDistricID][gridname].Contains(btsKey))
            {
                districtGridCdNodeBMap[curDistricID][gridname].Remove(btsKey);//除去之前查出的被当做未知小区的所属基站
            }
            if (unknownCdcellStaterMap.ContainsKey(lacciStr))
                unknownCdcellStaterMap.Remove(lacciStr);

            existCdCellMap[lacciStr] = cdcell.Name;
        }

        private void addCDMACell(CDCell cdcell, string gridname)
        {
            CellBriefStater stater = null;
            if (!cdcellStaterMap.ContainsKey(cdcell))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = cdcell.Name;
                stater.cellType = "CDMA";
                stater.carrier = "电信";
                stater.lac = cdcell.LAC;
                stater.ci = cdcell.CI;
                stater.grid = gridname;
                stater.isIndoor = cdcell.Type == CDNodeBType.Indoor;
                cdcellStaterMap[cdcell] = stater;
            }

            CDNodeB nodeb = cdcell.BelongNodeBs[0];

            if (districtGridCdNodeBMap[curDistricID].ContainsKey(gridname))
            {
                if (!districtGridCdNodeBMap[curDistricID][gridname].Contains(nodeb.Name))
                {
                    districtGridCdNodeBMap[curDistricID][gridname].Add(nodeb.Name);
                }
            }
            else
            {
                districtGridCdNodeBMap[curDistricID].Add(gridname, new List<string>());
                districtGridCdNodeBMap[curDistricID][gridname].Add(nodeb.Name);
            }
        }
        
        private void addUnknownCDMACell(TestPoint tpPoint)
        {
            string gridname = isContainPoint(new DbPoint(tpPoint.Longitude, tpPoint.Latitude)); //未知小区缺失经纬度，用采样点的经纬度代替之定位所在网格
            if (gridname == null)
                return;

            CellBriefStater stater = null;
            string key = tpPoint["CD_SID"].ToString() + "_" + tpPoint["CD_BID"].ToString();
            if (!unknownCdcellStaterMap.ContainsKey(key))
            {
                stater = new CellBriefStater();
                stater.district = curDistricID;
                stater.cellName = "未知小区 " + key;
                stater.cellType = "CDMA";
                stater.carrier = "电信";
                stater.lac = (int)tpPoint["CD_SID"];
                stater.ci = (int)tpPoint["CD_BID"];
                stater.grid = gridname;
                unknownCdcellStaterMap[key] = stater;
            }
        }

    }

    public class CellBriefStater
    {
        public CellBriefStater()
        {
            isIndoor = false;
        }

        public int district { get; set; }//城市
        public string carrier { get; set; }//运营商
        public string cellType { get; set; }//小区类型
        public string grid { get; set; }//网格

        public string cellName { get; set; }//小区名称
        public int lac { get; set; }
        public int ci { get; set; }
        public bool isIndoor { get; set; }//是否室内小区
    }

    public class CellSetGenaral
    {
        public string district { get; set; }//城市
        public string carrier { get; set; }//运营商
        public string cellType { get; set; }//小区类型
        public string grid { get; set; }//网格
        public int btsCount { get; set; }//基站数目
        public int cellCount { get; set; }//小区数目
        public int indoorCellCount { get; set; }//室内小区数目
    }

}
