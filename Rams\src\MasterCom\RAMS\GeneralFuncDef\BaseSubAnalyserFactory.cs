﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.GeneralFuncDef.OwnSampleAnalyse;

namespace MasterCom.RAMS.GeneralFuncDef
{
    public static class BaseSubAnalyserFactory
    {
        public static BaseSubAnalyser CreateAnalyser(int nodetype,string commanderName)
        {
            switch(nodetype)
            {
                case 1://采样点
                    {
                        SampleAnalyserDef sampleDef = new SampleAnalyserDef();
                        sampleDef.commander = SampleAnalyserDef.GetSampleCommander(commanderName);
                        return sampleDef;
                    }
                case 2://栅格
                    {
                        return null;

                    }
            }
            return null;
        }
    }
}
