using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class NBCell : Snapshot<NBCell>
    {

        public NBCell()
        {
            Value = this;
        }

        public int CellID { get; set; }

        public Cell GetCell(DateTime dateTime)
        {
            return CellManager.GetInstance().GetCell(CellID, dateTime);
        }

        public List<NBCellInfo> NBCellInfoList
        {
            get { return nbCellInfoList; }
        }

        public List<Cell> GetNBCellList(DateTime dateTime)
        {
            List<Cell> cellList = new List<Cell>();
            foreach (NBCellInfo nbCellInfo in NBCellInfoList)
            {
                Cell cell = CellManager.GetInstance().GetCell(nbCellInfo.nbCellID, dateTime);
               if (cell != null)
               {
                   cellList.Add(cell);
               }        
            }
            return cellList;
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();

                return info.ToString();
            }
        }

        public void Fill(int cellid, int istime, int ietime, List<NBCellInfo> nbCells)
        {
            base.Fill(cellid, istime, ietime);

            nbCellInfoList = new List<NBCellInfo>(nbCells);
            CellID = cellid;
            nbCellList = getNBCell(nbCells);

            foreach (Cell cell in CellManager.GetInstance().GetCells(cellid, ValidPeriod))
            {
                foreach (Cell nbCell in nbCellList)
                {
                    cell.AddNeighbourCellId(nbCell.ID);
                }
            }        
        }

        private List<Cell> getNBCell(List<NBCellInfo> nbCells)
        {
            List<Cell> nbCellListTmp = new List<Cell>();
            foreach (NBCellInfo nbCellInfo in nbCells)
            {
                List<Cell> theCells = CellManager.GetInstance().GetCells(nbCellInfo.nbCellID, ValidPeriod);
                if (theCells.Count > 0)
                {
                    nbCellListTmp.Add(theCells[0]);
                }
            }
            return nbCellListTmp;
        }
        
        private List<NBCellInfo> nbCellInfoList;
        private List<Cell> nbCellList { get; set; }
    }

    public class NBCellInfo
    {
        public int nbCellID { get; set; }
        public int type { get; set; }
    }


}
