﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEHighWayWeakCoverRoadQuery : ZTWeakCoverRoadQueryModel
    {
        protected new WeakCoverRoadCondition_LTEHighRailWay weakCondition = new WeakCoverRoadCondition_LTEHighRailWay();
        private static ZTLTEHighWayWeakCoverRoadQuery intance = null;
        public static new ZTLTEHighWayWeakCoverRoadQuery GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTEHighWayWeakCoverRoadQuery();
                    }
                }
            }
            return intance;
        }

        //最终的弱覆盖路段结果集合
        protected List<WeakCoverRoadLTE> ZTWeakCovRoadInfoList = new List<WeakCoverRoadLTE>();

        protected ZTLTEHighWayWeakCoverRoadQuery()
        {
            init();
        }

        private void init()
        {
            this.IsCanExportResultMapToWord = true;
            name = "弱覆盖路段_LTE";
            type = 2;
            funcId = 22000;
            subfuncId = 22009;
            desc = "分析";
            tpStr = "lte_NCell_RSRP";
            tpRSRP = "lte_RSRP";
            tpSINR = "lte_SINR";
            tpTac = "lte_TAC";
            tpSpeed = "lte_APP_Speed_Mb";
            themeName = "TD_LTE_RSRP";
            tpNCell_EARFCN = "lte_NCell_EARFCN";
            tpNCell_PCI = "lte_NCell_PCI";
            tpAppType = "lte_APP_type";

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "弱覆盖路段(按采样点)"; }
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            LTEHighRailwayWeakCoverRoadDlg dlg = new LTEHighRailwayWeakCoverRoadDlg(weakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                weakCondition = dlg.GetConditon();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (ZTWeakCovRoadInfoList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            WeakCoverRoadLTEHighRailWayForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(WeakCoverRoadLTEHighRailWayForm)) as WeakCoverRoadLTEHighRailWayForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new WeakCoverRoadLTEHighRailWayForm(MainModel);
            }
            frm.FillData(ZTWeakCovRoadInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            ZTWeakCovRoadInfoList = null;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            ZTWeakCovRoadInfoList = new List<WeakCoverRoadLTE>();
        }
        
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                //弱覆盖路段采样点集合
                List<TestPoint> weakRoadTPList = new List<TestPoint>();
                //保存连续的正常覆盖采样点集合
                List<TestPoint> continuousTPList = new List<TestPoint>();
                //保存弱覆盖采样点集合(不一定连续)
                List<TestPoint> weakTPList = new List<TestPoint>();
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    if (!isValidTestPoint(tp))
                    {
                        continue;
                    }
                    float? tpRSRP = getRsrp(tp);
                    if (tpRSRP != null)
                    {
                        addWeakRoadTP(weakRoadTPList, continuousTPList, weakTPList, tp, tpRSRP);
                    }
                }
            }
        }

        private void addWeakRoadTP(List<TestPoint> weakRoadTPList, List<TestPoint> continuousTPList, List<TestPoint> weakTPList, TestPoint tp, float? tpRSRP)
        {
            if (tpRSRP <= weakCondition.RSRP)
            {
                //rsrp满足条件时添加弱覆盖采样点
                weakTPList.Add(tp);
                //添加2个弱覆盖采样点间的正常覆盖采样点到弱覆盖路段集合
                foreach (TestPoint normalTP in continuousTPList)
                {
                    weakRoadTPList.Add(normalTP);
                }
                weakRoadTPList.Add(tp);
                continuousTPList.Clear();
            }
            else if (weakTPList.Count > 0)
            {
                //当存在弱覆盖采样点时(弱覆盖路段首个采样点要为弱覆盖采样点),添加正常覆盖采样点
                continuousTPList.Add(tp);
                if (continuousTPList.Count >= weakCondition.NormalTestPoints)
                {
                    //正常覆盖采样点数大于NormalTestPoints,退出弱覆盖路段统计
                    if (weakTPList.Count >= weakCondition.WeakCoverRoadTestPoints)
                    {
                        //弱覆盖采样点数大于WeakCoverRoadTestPoints,添加弱覆盖路段
                        saveWeakCovRoadInfo(weakRoadTPList, weakTPList.Count);
                    }
                    //清空以保存的数据或不满足条件舍弃
                    weakRoadTPList.Clear();
                    continuousTPList.Clear();
                    weakTPList.Clear();
                }
            }
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ret;
            try
            {
                ret = condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
            }
            catch
            {
                ret = false;
            }
            return ret;
        }

        protected string getAreaNames(TestPoint tp)
        {
            string areaNames = "";
            foreach (var item in condition.Geometorys.SelectedResvRegions)
            {
                if (item.GeoOp.Contains(tp.Longitude, tp.Latitude))
                {
                    areaNames = item.RegionName;
                    break;
                }
            }
            return areaNames;
        }

        //循环弱覆盖路段的采样点集合,将数据处理保存到结果集
        protected virtual void saveWeakCovRoadInfo(List<TestPoint> weakRoadTPList, int weakTestPointCount)
        {
            WeakCoverRoadLTE info = new WeakCoverRoadLTE();
            foreach (TestPoint testPoint in weakRoadTPList)
            {
                float? rsrp = getRsrp(testPoint);
                float? nbRSRP = getNbMaxRSRP(testPoint);
                float? sinr = getSINR(testPoint);
                short? type = getAppType(testPoint);
                double? speed = getSpeed(testPoint);
                int? tac = getTac(testPoint);
                LTECell lteCell = getMainCell(testPoint);
                info.SN = ZTWeakCovRoadInfoList.Count + 1;
                info.Add(rsrp, nbRSRP, sinr, tac, 0, testPoint, lteCell);
                info.AddSpeed(type, speed);
                info.FindRoadName();
            }
            info.AreaName = getAreaNames(weakRoadTPList[0]);
            info.WeakTestPointCount = weakTestPointCount;
            info.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            ZTWeakCovRoadInfoList.Add(info);
        }
        
        protected override void getResultsAfterQuery()
        {
            //父类对数据进行了一些处理,这里不需要所以重写,不能删除
        }
    }

    public class ZTLTEFDHighWayDWeakCoverRoadQuery : ZTLTEHighWayWeakCoverRoadQuery
    {
        public ZTLTEFDHighWayDWeakCoverRoadQuery()
        {
            
        }

        public ZTLTEFDHighWayDWeakCoverRoadQuery(MainModel mainModel)
            : base()
        {
            init();
        }

        private void init()
        {
            name = "弱覆盖路段_LTE_FDD";
            type = 2;
            funcId = 26000;
            subfuncId = 26003;
            desc = "分析";
            tpStr = "lte_fdd_NCell_RSRP";
            tpRSRP = "lte_fdd_RSRP";
            tpSINR = "lte_fdd_SINR";
            tpTac = "lte_fdd_TAC";
            tpSpeed = "lte_fdd_APP_Speed_Mb";
            themeName = "LTE_FDD:RSRP";
            tpNCell_EARFCN = "lte_fdd_NCell_EARFCN";
            tpNCell_PCI = "lte_fdd_NCell_PCI";
            tpAppType = "lte_fdd_APP_type";
        }
    }

}
