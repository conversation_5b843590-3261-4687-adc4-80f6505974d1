﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{ 
    public partial class NRReasonPnlOverCover : NRReasonPanelBase
    {
        public NRReasonPnlOverCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numMainRsrp.ValueChanged -= numMainRsrp_ValueChanged;
            numMainRsrp.Value = (decimal)((NRReasonsOverCover)reason).OverRsrpMin;
            numMainRsrp.ValueChanged += numMainRsrp_ValueChanged;
            numOverCoverFactor.ValueChanged -= numOverCoverFactor_ValueChanged;
            numOverCoverFactor.Value = (decimal)((NRReasonsOverCover)reason).CoverFactor;
            numOverCoverFactor.ValueChanged += numOverCoverFactor_ValueChanged;
        }

        void numMainRsrp_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonsOverCover)reason).OverRsrpMin = (double)numMainRsrp.Value;
        }
        void numOverCoverFactor_ValueChanged(object sender, EventArgs e)
        {
            ((NRReasonsOverCover)reason).CoverFactor = (double)numOverCoverFactor.Value;
        }
    }
}
