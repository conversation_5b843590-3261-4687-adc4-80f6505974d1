﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ExportVolteMOSSettingDlg : BaseDialog
    {
        public ExportVolteMOSSettingDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(out double lossPackageSecond)
        {
            lossPackageSecond = (double)numSiteLossTime.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
