﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using DevExpress.XtraCharts;

using DevExpress.XtraGrid.Views.Grid;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna
{
    public partial class TdXtraLastWeakPoadForm : DevExpress.XtraEditors.XtraForm
    {
        public TdXtraLastWeakPoadForm(MainModel mainmodel)
        {
            InitializeComponent();
            this.mainmodel = mainmodel;
        }
        MainModel mainmodel;
        public void setData(Dictionary<int, List<TdSampleStat>> typeDic, List<GsmSampleStat> ifileidList)
        {

            ShowSituation(typeDic,ifileidList);

            foreach (int item in typeDic.Keys)
            {
                if (item == 1)//TD弱覆盖
                {
                    gridControl1.DataSource = typeDic[item];
                }
                else if (item == 2)//TD弱MOS
                {
                    gridControl2.DataSource = typeDic[item];
                }
                else if (item == 3)//TD干扰
                {
                    gridControl3.DataSource = typeDic[item];
                }
                else if (item == 4)//弱TD上行链路差
                {
                    gridControl4.DataSource = typeDic[item];
                }
                else if (item == 5)//TD深度覆盖不足
                {
                    gridControl6.DataSource = typeDic[item];
                }
                else if (item == 6)//不连续覆盖
                {
                    gridControl7.DataSource = typeDic[item];
                }
            }
        }

        public void setDataNext1(Dictionary<int, List<TdSampleStat>> typeDic)
        {

            foreach (int item in typeDic.Keys)
            {
                if (item == 1)//TD弱覆盖
                {
                    setGridControl(typeDic, item, gridControl1);
                }
                else if (item == 2)//TD弱MOS
                {
                    setGridControl(typeDic, item, gridControl2);
                }
                else if (item == 3)//TD干扰
                {
                    setGridControl(typeDic, item, gridControl3);
                }
                else if (item == 4)//弱TD上行链路差
                {
                    setGridControl(typeDic, item, gridControl4);
                }
                else if (item == 5)//TD深度覆盖不足
                {
                    setGridControl(typeDic, item, gridControl6);
                }
                else if (item == 6)//不连续覆盖
                {
                    setGridControl(typeDic, item, gridControl7);
                }
            }
        }

        private void setGridControl(Dictionary<int, List<TdSampleStat>> typeDic, int item, DevExpress.XtraGrid.GridControl gc)
        {
            foreach (GridView view in gc.ViewCollection)
            {
                GridView view2 = view;
                GridColumn column = new GridColumn();
                column.VisibleIndex = 0;
                column.Caption = "编号";
                column.FieldName = "Code";
                column.Visible = true;
                view2.Columns.Add(column);
            }
            gc.DataSource = typeDic[item];
        }
        
        public Dictionary<string, string> GridDic { get; set; } = new Dictionary<string, string>();//以栅格表ID为主键
        public int Idistance { get; set; }

        /// <summary>
        /// 呈现概况数据
        /// </summary>
        /// <param name="typeDic"></param>
        private void ShowSituation(Dictionary<int, List<TdSampleStat>> typeDic, List<GsmSampleStat> ifileidList)
        {
            List<SummaryShowItem> ssList = getSsList(typeDic, ifileidList);

            this.gridControl5.DataSource = ssList;


            //初始化图表数据
            this.gridView9.Columns[1].AppearanceCell.BackColor = Color.Red;
            this.chartControl1.Series.Clear();
            this.chartControl1.Titles.Clear();
            for (int i = 0; i < gridView9.RowCount; i++)
            {
                Series situationType = new Series(gridView9.GetRowCellValue(i, "StrType").ToString(), ViewType.Bar);
                try
                {
                    int situationValue = Convert.ToInt32(gridView9.GetRowCellValue(i, gridView9.Columns[1]).ToString());
                    situationType.Points.Add(new SeriesPoint(gridView9.GetRowCellValue(i, "StrType").ToString(), situationValue));
                    this.chartControl1.Series.Add(situationType);
                }
                catch (Exception)
                {
                    double situationValue = Convert.ToDouble(gridView9.GetRowCellValue(i, gridView9.Columns[1]).ToString());
                    situationType.Points.Add(new SeriesPoint(gridView9.GetRowCellValue(i, "StrType").ToString(), situationValue));
                    this.chartControl1.Series.Add(situationType);
                }
            }

            ChartTitle chartitle = new ChartTitle();
            chartitle.Text = gridView9.Columns[1].Caption;

            this.chartControl1.Titles.Add(chartitle);
        }

        private List<SummaryShowItem> getSsList(Dictionary<int, List<TdSampleStat>> typeDic, List<GsmSampleStat> ifileidList)
        {
            List<LaiKey> lList = new List<LaiKey>();
            List<SummaryShowItem> ssList = new List<SummaryShowItem>();
            queryGridDistance(ifileidList);

            foreach (int iType in typeDic.Keys)
            {
                lList.Clear();
                SummaryShowItem ssItem = new SummaryShowItem();

                if (iType == 1) { ssItem.StrType = "TD弱覆盖"; }
                else if (iType == 2) { ssItem.StrType = "TD弱MOS"; }
                else if (iType == 3) { ssItem.StrType = "TD干扰"; }
                else if (iType == 4) { ssItem.StrType = "TD上行链路差"; }
                else if (iType == 5) { ssItem.StrType = "TD深度覆盖不足"; }
                else if (iType == 6) { ssItem.StrType = "不连续覆盖"; }

                List<TdSampleStat> gssList = typeDic[iType];
                foreach (TdSampleStat gssItem in gssList)
                {
                    ssItem.INum += 1;
                    ssItem.IDuration += gssItem.Iduration;
                    ssItem.IDistance += (int)(gssItem.Idistance);
                    ssItem.ISampleId += gssItem.getLlInfo().Count;

                    addlList(lList, gssItem);
                }
                ssItem.ICellNum = lList.Count;
                ssItem.IAllDistance = Idistance;
                ssItem.FRate = ((float)(ssItem.IDistance) / ssItem.IAllDistance) * 100;
                ssList.Add(ssItem);
            }

            return ssList;
        }

        private static void addlList(List<LaiKey> lList, TdSampleStat gssItem)
        {
            foreach (No1Info ni in gssItem.getLlInfo())
            {
                LaiKey lKey = new LaiKey();
                lKey.ILac = ni.ILac;
                lKey.ICi = ni.ICi;
                if (!lList.Contains(lKey))
                {
                    lList.Add(lKey);
                }
            }
        }

        /// <summary>
        /// 查询栅格里程
        /// </summary>
        protected void queryGridDistance(List<GsmSampleStat> ifileidList)
        {
            try
            {
                Dictionary<int, string> fileDic = new Dictionary<int, string>();//以文件ID为主键
                GridDic.Clear();
                foreach (GsmSampleStat iKey in ifileidList)
                {
                    if (!fileDic.ContainsKey(iKey.Ifileid))
                        {
                            DateTime dTime = JavaDate.GetDateTimeFromMilliseconds(iKey.Istime * 1000L);
                            int iWeek = dTime.DayOfWeek.GetHashCode();
                            DateTime tTime = dTime.AddDays(1 - iWeek);
                            string strFileName = string.Format("tb_tdscdma_stati_amr_grid_{0:yyMMdd}", tTime);
                            fileDic.Add(iKey.Ifileid, strFileName);
                        }
                }

                foreach (int i in fileDic.Keys)
                {
                    string tb = fileDic[i];
                    if (GridDic.ContainsKey(tb))
                    {
                        string s = GridDic[tb];
                        s += "," + i.ToString();
                        GridDic[tb] = s;
                    }
                    else
                    {
                        GridDic.Add(tb, i.ToString());
                    }
                }
                WaitBox.Text = "准备统计...";

                WaitBox.CanCancel = true;
                foreach (string tbName in GridDic.Keys)
                {
                    DiySqlQueryGridDistanceInfo dsqgInfo = new DiySqlQueryGridDistanceInfo(mainmodel);
                    DiySqlQueryGridDistanceInfo.strSQL = "select ifileid,iduration,idistance,itllongitude,itllatitude,ibrlongitude,ibrlatitude from " + tbName + " where ifileid in  (" + GridDic[tbName] + ")";
                    dsqgInfo.Query();
                    List<GridDistanceItem> gdiList = VerificationRegion(dsqgInfo.GdiList);
                    foreach (GridDistanceItem i in gdiList)
                    {
                        this.Idistance += i.Idistance;
                    }
                }
            }
            catch
            {
                //continue
            }
        }
        
        /// <summary>
        /// 验证数据是否在所选区域内
        /// </summary>
        private List<GridDistanceItem> VerificationRegion(List<GridDistanceItem> gdiList)
        {
            List<GridDistanceItem> newGdilist = new List<GridDistanceItem>();

            foreach (GridDistanceItem gss in gdiList)
            {
                float fLong = (gss.Ftllongitude + gss.Fbrlongitude) / 2;
                float fLat = (gss.Ftllatitude + gss.Fbrlatitude) / 2;
                if (mainmodel.SearchGeometrys.GeoOp.Contains(fLong, fLat))
                {
                    newGdilist.Add(gss);
                }
            }
            return newGdilist;
        }

        //回放
        private void ToolStripMenuItemDIYReplay_Click(object sender, EventArgs e)
        {

            string ifileid = "";
            string istime = "";
            string ietime = "";

            switch (this.xtraTabControl1.SelectedTabPageIndex)
            {
                case 1:
                    ifileid = bandedGridView1.GetFocusedRowCellValue("Ifileid").ToString();
                    istime = bandedGridView1.GetFocusedRowCellValue("Istime").ToString();
                    ietime = bandedGridView1.GetFocusedRowCellValue("Ietime").ToString();
                    break;
                case 2:
                    ifileid = bandedGridView2.GetFocusedRowCellValue("Ifileid").ToString();
                    istime = bandedGridView2.GetFocusedRowCellValue("Istime").ToString();
                    ietime = bandedGridView2.GetFocusedRowCellValue("Ietime").ToString();
                    break;
                case 3:
                    ifileid = bandedGridView3.GetFocusedRowCellValue("Ifileid").ToString();
                    istime = bandedGridView3.GetFocusedRowCellValue("Istime").ToString();
                    ietime = bandedGridView3.GetFocusedRowCellValue("Ietime").ToString();
                    break;
                case 4:
                    ifileid = bandedGridView4.GetFocusedRowCellValue("Ifileid").ToString();
                    istime = bandedGridView4.GetFocusedRowCellValue("Istime").ToString();
                    ietime = bandedGridView4.GetFocusedRowCellValue("Ietime").ToString();
                    break;
                case 5:
                    ifileid = bandedGridView5.GetFocusedRowCellValue("Ifileid").ToString();
                    istime = bandedGridView5.GetFocusedRowCellValue("Istime").ToString();
                    ietime = bandedGridView5.GetFocusedRowCellValue("Ietime").ToString();
                    break;
                case 6:
                    ifileid = bandedGridView6.GetFocusedRowCellValue("Ifileid").ToString();
                    istime = bandedGridView6.GetFocusedRowCellValue("Istime").ToString();
                    ietime = bandedGridView6.GetFocusedRowCellValue("Ietime").ToString();
                    break;
                default:
                    break;
            }
            
            if (ifileid != "")
            {
                QueryItem queryitem=new QueryItem();
                queryitem.IFileid = Convert.ToInt32(ifileid);
                queryitem.Istime=Convert.ToInt32(istime);
                queryitem.Ietime=Convert.ToInt32(ietime);

                DiySqlQueryReplaySampleInfo diyreplay = new DiySqlQueryReplaySampleInfo(mainmodel);
                DiySqlQueryReplaySampleInfo.QItem = queryitem;
                DiySqlQueryReplaySampleInfo.mergeSql();
                diyreplay.Query();
                ReplaySampleItem replaysample = diyreplay.RsItem;


                mainmodel.MainForm.NeedChangeWorkSpace(false);
                replayFile(replaysample);
            }
        }

        private void replayFile(ReplaySampleItem replaysample)
        {
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = replaysample.StrFileName;
            fileInfo.ProjectID = replaysample.IProjectType;
            fileInfo.ID = replaysample.IFileid;
            fileInfo.LogTable = replaysample.StrLogFileName;
            fileInfo.ServiceType = replaysample.IServicetType;
            fileInfo.SampleTbName = replaysample.StrSampleName;
            DateTime timeStart = JavaDate.GetDateTimeFromMilliseconds(replaysample.Istime * 1000L);
            DateTime timeEnd = JavaDate.GetDateTimeFromMilliseconds(replaysample.Ietime * 1000L);
            FileReplayer.Replay(fileInfo, new TimePeriod(timeStart, timeEnd));
        }


        //双击定位到GIS
        private void gridControl1_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = bandedGridView1.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = bandedGridView1.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude!="")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = bandedGridView1.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }

        private void gridControl2_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = bandedGridView2.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = bandedGridView2.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = bandedGridView2.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }

        private void gridControl3_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = bandedGridView3.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = bandedGridView3.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = bandedGridView3.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }

        private void gridView10_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = gridView10.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = gridView10.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = gridView10.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }

        private void gridControl4_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = bandedGridView4.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = bandedGridView4.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = bandedGridView4.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }


        private void gridView12_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = gridView12.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = gridView12.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = gridView12.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }


        //隐藏至路网通右下角
        private void TdXtraLastWeakPoadForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainmodel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }

        //选中单元格 高亮列
        private void gridView9_EndSorting(object sender, EventArgs e)
        {
            Color clr = gridView9.Appearance.Row.BackColor;
            foreach (DevExpress.XtraGrid.Columns.GridColumn dc in gridView9.Columns)
            {
                if (dc.VisibleIndex == gridView9.SortedColumns[0].VisibleIndex)
                {
                    dc.AppearanceCell.BackColor = Color.Red;
                }
                else
                {
                    dc.AppearanceCell.BackColor = clr;
                }
            }
        }

        //选中一列 开始呈现柱状图
        private void gridView9_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {
            Color clr = gridView9.Appearance.Row.BackColor;
            foreach (DevExpress.XtraGrid.Columns.GridColumn dc in gridView9.Columns)
            {
                if (dc.VisibleIndex == gridView9.FocusedColumn.VisibleIndex)
                {
                    dc.AppearanceCell.BackColor = Color.Red;
                }
                else
                {
                    dc.AppearanceCell.BackColor = clr;
                }
            }
            gridView9.FocusedColumn.AppearanceCell.BackColor = Color.Red;


            if (gridView9.FocusedColumn.VisibleIndex == 0)
                return;

            this.chartControl1.Series.Clear();
            this.chartControl1.Titles.Clear();
            for (int i = 0; i < gridView9.RowCount; i++)
            {
                Series situationType = new Series(gridView9.GetRowCellValue(i, "StrType").ToString(), ViewType.Bar);
                try
                {
                    int situationValue = Convert.ToInt32(gridView9.GetRowCellValue(i, gridView9.FocusedColumn).ToString());
                    situationType.Points.Add(new SeriesPoint(gridView9.GetRowCellValue(i, "StrType").ToString(), situationValue));
                    this.chartControl1.Series.Add(situationType);
                }
                catch (Exception)
                {
                    double situationValue = Convert.ToDouble(gridView9.GetRowCellValue(i, gridView9.FocusedColumn).ToString());
                    situationType.Points.Add(new SeriesPoint(gridView9.GetRowCellValue(i, "StrType").ToString(), situationValue));
                    this.chartControl1.Series.Add(situationType);

                }
            }

            ChartTitle chartitle = new ChartTitle();
            chartitle.Text = gridView9.FocusedColumn.Caption;

            this.chartControl1.Titles.Add(chartitle);
        }

        //清除飞线
        private void ToolStripMenuItemClearFly_Click(object sender, EventArgs e)
        {
            mainmodel.ShowLastWeakRoadFly = false;
            mainmodel.FireDTDataChanged(this);
        }

        //显示飞线
        private void ToolStripMenuItemShowFly_Click(object sender, EventArgs e)
        {
            mainmodel.ShowLastWeakRoadFly = true;
            mainmodel.FireDTDataChanged(this);
        }

        //导出Excel
        private void ToolStripMenuItemToExcel_Click(object sender, EventArgs e)
        {
            ExcelHelper.XtraTabControlToExcel(xtraTabControl1, "CustomizationCaption");
        }


        //道路合并 第一步
        private void btnNext1_Click(object sender, EventArgs e)
        {
            if (!isSelectRegion())
            {
                MessageBox.Show("请选择区域!");
                return;
            }

            double MaxMileage = 0;  //总里程
            this.btnNext1.Enabled = false;

            //过滤区域外的道路数据
            List<GridRoadInfo> gridroadList = VerificationRegion(mainmodel.GridRoadInfoList);

            //道路数据分组
            Dictionary<string, List<GridRoadInfo>> gridroadDic = new Dictionary<string, List<GridRoadInfo>>();


            foreach (GridRoadInfo grinfo in gridroadList)
            {
                if (!gridroadDic.ContainsKey(grinfo.Strroad))
                {
                    List<GridRoadInfo> templist = new List<GridRoadInfo>();
                    templist.Add(grinfo);
                    gridroadDic.Add(grinfo.Strroad, templist);
                }
                else
                {
                    gridroadDic[grinfo.Strroad].Add(grinfo);
                }
            }

            //计算总里程
            foreach (List<GridRoadInfo> gridListitem in gridroadDic.Values)
            {
                foreach (List<GridRoadInfo> item in NextNo1(gridListitem).Values)
                {
                    MaxMileage += calculate(item);
                }
            }

            mainmodel.NextNo = 1;


            if (mainmodel.LastWeakRoadTdDic.Count > 0)
            {
                addDistanceDic();
            }

            //呈现数据
            List<SummaryShowItem> ssList = new List<SummaryShowItem>();

            foreach (int ikey in mainmodel.DistanceDic.Keys)
            {
                addSsList(MaxMileage, ssList, ikey);
            }

            this.gridControl5.DataSource = ssList;

            this.gridView9.Columns[1].Visible = false;
            this.gridView9.Columns[2].Visible = false;
            this.gridView9.Columns[6].Visible = false;
            this.gridView9.Columns[7].Visible = false;

            setDataNext1(mainmodel.LastWeakRoadTdDic);

            mainmodel.FireDTDataChanged(this);
        }

        private void addSsList(double MaxMileage, List<SummaryShowItem> ssList, int ikey)
        {
            SummaryShowItem ssitem = new SummaryShowItem();

            if (ikey == 1) { ssitem.StrType = "TD弱覆盖"; }
            else if (ikey == 2) { ssitem.StrType = "TD弱MOS"; }
            else if (ikey == 3) { ssitem.StrType = "TD干扰"; }
            else if (ikey == 4) { ssitem.StrType = "TD上行链路差"; }
            else if (ikey == 5) { ssitem.StrType = "TD深度覆盖不足"; }
            else if (ikey == 6) { ssitem.StrType = "不连续覆盖"; }

            ssitem.IAllDistance = (int)MaxMileage;
            ssitem.IDistance = (int)mainmodel.DistanceDic[ikey];
            ssitem.FRate = ((float)(ssitem.IDistance) / ssitem.IAllDistance) * 100;
            ssList.Add(ssitem);
        }

        private void addDistanceDic()
        {
            //初始化里程
            mainmodel.DistanceDic.Clear();
            string lablename = "";
            foreach (int iKey in mainmodel.LastWeakRoadTdDic.Keys)
            {
                if (iKey == 1) { lablename = "TWR"; }
                else if (iKey == 2) { lablename = "TWM"; }
                else if (iKey == 3) { lablename = "TCI"; }
                else if (iKey == 4) { lablename = "TWL"; }
                else if (iKey == 5) { lablename = "TWD"; }
                else if (iKey == 6) { lablename = "TCR"; }

                mainmodel.DistanceDic.Add(iKey, 0);
                foreach (TdSampleStat gss in mainmodel.LastWeakRoadTdDic[iKey])
                {
                    setGssCode(lablename, iKey, gss);
                }
            }
        }

        private void setGssCode(string lablename, int iKey, TdSampleStat gss)
        {
            List<No1Info> no1List = gss.getLlInfo();
            gss.Idistance = 0;
            //第一步开始 next1
            if (no1List.Count > 1 && mainmodel.NextNo == 1)
            {
                StringBuilder sb = new StringBuilder(gss.Code);
                foreach (List<No1Info> no1ListItem in NextNo1(no1List).Values)
                {
                    if (no1ListItem[no1ListItem.Count - 1].Isampleid >= 1001001 && no1ListItem[0].Isampleid >= 1001001)
                    {
                        //计算里程
                        mainmodel.DistanceDic[iKey] += calculate(no1ListItem);
                        gss.Idistance += (float)calculate(no1ListItem);
                        sb.Append(lablename + no1ListItem[0].Isampleid.ToString() + ",");
                    }
                }
                gss.Code = sb.ToString();
            }
            try
            {
                gss.Code = gss.Code.TrimEnd(',');
            }
            catch
            {
                gss.Code = " ";
            }
        }

        /// <summary>
        /// 验证数据是否在所选区域内
        /// </summary>
        private List<GridRoadInfo> VerificationRegion(List<GridRoadInfo> gssList)
        {
            List<GridRoadInfo> newGsslist = new List<GridRoadInfo>();
            foreach (GridRoadInfo gss in gssList)
            {
                if (mainmodel.SearchGeometrys.GeoOp.Contains(gss.Ilongitude, gss.Ilatitude))
                {
                    newGsslist.Add(gss);
                }
            }
            return newGsslist;
        }

        //计算里程
        private double calculate(object o)
        {
            double Mileage = 0;
            if (o is List<GridRoadInfo>)
            {
                List<GridRoadInfo> temp = o as List<GridRoadInfo>;
                foreach (GridRoadInfo item in temp)
                {
                    Mileage += item.Strdesc3;
                }
                return Mileage - temp[temp.Count - 1].Strdesc3;
            }
            else if (o is List<No1Info>)
            {
                List<No1Info> temp = o as List<No1Info>;
                foreach (No1Info item in temp)
                {
                    Mileage += getRoadInfo(item.Isampleid).Strdesc3;
                }
                return Mileage - getRoadInfo(temp[temp.Count - 1].Isampleid).Strdesc3;
            }
            else
            {
                return 0;
            }


        }

        //获取 对应的道路节点配置
        private GridRoadInfo getRoadInfo(int roadid)
        {
            if (mainmodel.GridRoadInfoDic.ContainsKey(roadid))
                return mainmodel.GridRoadInfoDic[roadid];
            return null;
        }

        //匹配第一步数据 判断道路是否连续 如果中断太长则拆分
        private Dictionary<int, List<No1Info>> NextNo1(List<No1Info> info)
        {
            //初始化对Isampleid排序
            info.Sort(delegate(No1Info a, No1Info b) { return a.Isampleid.CompareTo(b.Isampleid); });

            Dictionary<int, List<No1Info>> noinDic = new Dictionary<int, List<No1Info>>();
            List<No1Info> niList = new List<No1Info>();

            int maxi = 0;
            int i = 0;
            foreach (No1Info no in info)
            {
                GridRoadInfo gridroad = getRoadInfo(no.Isampleid);
                if (gridroad != null && no.Isampleid >= 1001001)
                {

                    if (maxi != 0 && gridroad.Order - maxi > 11)// 1个单位5米
                    {
                        noinDic.Add(++i, niList);
                        niList = new List<No1Info>();
                    }


                    niList.Add(no);
                    maxi = gridroad.Order;

                }
            }

            if (i != 0 || (niList.Count > 1 && niList[niList.Count - 1].Isampleid - niList[0].Isampleid > 0))
            {
                noinDic.Add(++i, niList);
            }


            return noinDic;
        }


        //判断道路是否连续 如果中断太长则拆分
        private Dictionary<int, List<GridRoadInfo>> NextNo1(List<GridRoadInfo> info)
        {
            //初始化对Isampleid排序
            info.Sort(delegate(GridRoadInfo a, GridRoadInfo b) { return a.Isampleid.CompareTo(b.Isampleid); });

            Dictionary<int, List<GridRoadInfo>> noinDic = new Dictionary<int, List<GridRoadInfo>>();
            List<GridRoadInfo> niList = new List<GridRoadInfo>();

            int maxi = 0;
            int i = 0;
            foreach (GridRoadInfo no in info)
            {
                if (no.Isampleid >= 1001001)
                {

                    if (maxi != 0 && no.Order - maxi > 11)// 1个单位5米
                    {
                        noinDic.Add(++i, niList);
                        niList = new List<GridRoadInfo>();
                    }


                    niList.Add(no);
                    maxi = no.Order;

                }
            }

            if (i != 0 || (niList.Count > 1 && niList[niList.Count - 1].Isampleid - niList[0].Isampleid > 0))
            {
                noinDic.Add(++i, niList);
            }


            return noinDic;
        }

        private bool isSelectRegion() //是否框选区域
        {
            if (mainmodel.SearchGeometrys.Region != null)
            {
                DbRect rect = MapOperation.GetShapeBounds(mainmodel.SearchGeometrys.Region);
                if (rect.x1 != rect.x2 && rect.y1 != rect.y2)
                {
                    return true;
                }
            }
            return false;
        }

        private void ToolStripMenuItemLable_Click(object sender, EventArgs e)
        {
            if (mainmodel.ShowLastWeakRoadLable)
            {
                mainmodel.ShowLastWeakRoadLable = false;
                mainmodel.FireDTDataChanged(this);
            }
            else
            {
                mainmodel.ShowLastWeakRoadLable = true;
                mainmodel.FireDTDataChanged(this);
            }
        }

        private void gridControl6_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = bandedGridView5.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = bandedGridView5.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = bandedGridView5.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }

        private void gridControl7_DoubleClick(object sender, EventArgs e)
        {
            string imlongitude = bandedGridView6.GetFocusedRowCellValue("Imlongitude").ToString();
            string imlatitude = bandedGridView6.GetFocusedRowCellValue("Imlatitude").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            try
            {
                string code = bandedGridView6.GetFocusedRowCellValue("Iid").ToString();
                if (code != null && code != "")
                {
                    mainmodel.lastWeakRoadiid = Convert.ToInt32(code);
                }
            }
            catch
            {
                //continue
            }
        }
    }
}