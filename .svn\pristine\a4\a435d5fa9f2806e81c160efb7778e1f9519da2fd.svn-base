﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Threading;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePCIOptimizeQuery : QueryBase
    {
        private List<LTECell> regionCells;
        private PCIOptimizeManager manager { get; set; }
        private readonly Dictionary<string, LTECell> allNameCellDic;
        private bool isSearchRegionCell;

        public LtePCIOptimizeQuery(MainModel mainModel)
            : base(mainModel)
        {
            regionCells = new List<LTECell>();
            manager = new PCIOptimizeManager();
            allNameCellDic = new Dictionary<string, LTECell>();
            isSearchRegionCell = true;
        }

        public override MasterCom.RAMS.Model.MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override string Name
        {
            get { return "PCI优化"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23023, this.Name);
        }
        public void SetRegionCell(List<LTECell> lteCells)
        {
            this.isSearchRegionCell = false;
            this.regionCells = lteCells;

            foreach (LTECell cell in regionCells)
            {
                if (!allNameCellDic.ContainsKey(cell.Name))
                {
                    allNameCellDic.Add(cell.Name, cell);
                }
            }
        }

        protected override void query()
        {
            manager = new PCIOptimizeManager();
            manager.ClearData();
            if (!System.IO.File.Exists(System.Windows.Forms.Application.StartupPath + "\\config\\PCIOptConfig.xml"))
            {
                System.Windows.Forms.MessageBox.Show("缺少配置文件PCIOptConfig.xml", "提示");
                return;
            }
            if (DbConfig.GetInstance().CurDb == null)
            {
                ServerForm servform = new ServerForm(DbConfig.GetInstance().DbVec);
                if (servform.ShowDialog() != DialogResult.OK) return;
                DbConfig.GetInstance().CurDb = servform.SelServer;
            }

            if (isSearchRegionCell)
            {
                WaitBox.Show("正在查询区域内LTE小区...", GetCellInRegion);
            }
            manager.RegionCells = regionCells;
            manager.SetAllNameCellDic(allNameCellDic);
            manager.SetTimePeriod(this.Condition.Periods[0]);

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LtePCIOptimizeNewSettingDlg).FullName);
            LtePCIOptimizeNewSettingDlg form = obj == null ? null : obj as LtePCIOptimizeNewSettingDlg;
            if (form == null || form.IsDisposed)
            {
                form = new LtePCIOptimizeNewSettingDlg(manager);
            }
            form.Show(MainModel.MainForm);
        }

        private void GetCellInRegion()
        {
            try
            {
                regionCells.Clear();
                List<LTECell> lteCellList = CellManager.GetInstance().GetCurrentLTECells();
                int idx = 0;
                foreach (LTECell cell in lteCellList)
                {
                    WaitBox.ProgressPercent = ++idx;
                    if (this.Condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                    {
                        regionCells.Add(cell);
                    }
                    if (!allNameCellDic.ContainsKey(cell.Name))
                    {
                        allNameCellDic.Add(cell.Name, cell);
                    }
                }
            }
            finally
            {
                Thread.Sleep(100);
                WaitBox.Close();
            }
        }
    }

    public class LtePCIOptimizeQuery_FDD : LtePCIOptimizeQuery
    {
        public LtePCIOptimizeQuery_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "PCI优化LTE_FDD"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26053, this.Name);
        }
    }
}
