﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFastFadingByRegion_TD : DIYFastFadingByRegion_GSM
    {
        public DIYFastFadingByRegion_TD(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            ServiceTypes.Add(ServiceType.TDSCDMA_VIDEO);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSDPA);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13025, this.Name);//////
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    dealTPInfo(testPointList);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTPInfo(List<TestPoint> testPointList)
        {
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
                    float? rxLevMain = (float?)testPoint["TD_PCCPCH_RSCP"];
                    TDCell mainCell = testPoint.GetMainCell_TD_TDCell();
                    if (mainCell != null)
                    {
                        cellRxLevDic[mainCell.Name] = (float)rxLevMain;
                        judgeCell(rxLevMain, mainCell.Name, testPointList, i);
                    }

                    judgeTestPoint(testPointList, i, cellRxLevDic);
                }
                else
                {
                    clearIndermediateVariable();
                }
            }
        }
    }
}
