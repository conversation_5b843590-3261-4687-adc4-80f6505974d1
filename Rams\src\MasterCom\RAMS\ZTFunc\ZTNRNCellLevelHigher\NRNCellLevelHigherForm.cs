﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRNCellLevelHigherForm : MinCloseForm
    {
        MapForm mapForm;
        //private  List<NRNCellLevelHigherInfo> listCellInfo = null;

        List<NRNCellLevelHigherResultInfo> curResList = null;


        public NRNCellLevelHigherForm() : base(MainModel.GetInstance())
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
            DisposeWhenClose = true;
        }

        public void FillData(List<NRNCellLevelHigherResultInfo> resList)
        {
            curResList = resList;
            foreach (var res in resList)
            {
                foreach (var tpRes in res.TPResList)
                {
                    MainModel.DTDataManager.Add(tpRes.TestPoint);
                }
            }


            //listCellInfo = new List<NRNCellLevelHigherInfo>();
            //int sn = 0;
            //foreach (NRNCellLevelHigherInfo lcInfo in CelllteInfos)
            //{
            //    //lcInfo.SN = ++sn;
            //    lcInfo.CellType = "NR";
            //    MainModel.DTDataManager.Add(lcInfo.TestPoint);
            //    listCellInfo.Add(lcInfo);
            //}
            gridControlCell.DataSource = curResList;
            gridControlCell.RefreshDataSource();
            MainModel.FireDTDataChanged(this);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gvRes);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void gvRes_DoubleClick(object sender, EventArgs e)
        {
            if (gvRes.SelectedRowsCount > 0)
            {
                MainModel.SelectedNRCells.Clear();
                MainModel.SelectedTestPoints.Clear();
                int i = gvRes.GetSelectedRows()[0];
                NRNCellLevelHigherResultInfo info = curResList[i];
                //mModel.DrawFlyLines = true;
                foreach (var item in info.TPResList)
                {
                    MainModel.SelectedTestPoints.Add(item.TestPoint);
                }

                MainModel.FireSelectedTestPointsChanged(this);
                double Longitude = info.TPResList[0].Longitude;
                double Latitude = info.TPResList[0].Latitude;
                mapForm.GoToView(Longitude, Latitude, 5000);
            }
        }

        private void gvTP_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.SelectedRowsCount > 0)
            {
                MainModel.SelectedNRCells.Clear();
                MainModel.SelectedTestPoints.Clear();
                int i = gv.GetSelectedRows()[0];
                object row = gv.GetRow(i);
                NRNCellLevelHigherInfo info = row as NRNCellLevelHigherInfo;
                //mModel.SetSelectedNRCell();
                //mModel.DrawFlyLines = true;
                MainModel.SelectedTestPoints.Add(info.TestPoint);

                //MainModel.FireDTDataChanged(this);
                MainModel.FireSelectedTestPointsChanged(this);
                double Longitude = info.Longitude;
                double Latitude = info.Latitude;
                mapForm.GoToView(Longitude, Latitude, 4000);
            }
        }
    }
}
