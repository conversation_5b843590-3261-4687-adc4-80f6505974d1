﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public abstract class DiyQueryDataBase : DIYSQLBase
    {
        protected string btsName = "";
        protected DiyQueryDataBase()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }

        public override string Name { get { return ""; } }

        public virtual void SetCondition(string btsName)
        {
            this.btsName = btsName;
        }

        protected override string getSqlTextString()
        {
            throw (new Exception("没有需要执行的SQL语句"));
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            throw (new Exception("没有查询结果的返回类型"));
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            initData();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        /// <summary>
        /// 接收结果前初始化
        /// </summary>
        protected virtual void initData()
        {
        }

        protected abstract void dealReceiveData(Package package);
    }
}
