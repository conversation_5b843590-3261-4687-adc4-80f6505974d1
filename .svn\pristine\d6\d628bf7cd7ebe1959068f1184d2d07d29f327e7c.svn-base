﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEBTSVoiCoverManager : GSMBTSVoiCoverManager
    {
        public Dictionary<LTEBTS, List<Vertex[]>> LTEBtsToPolygonDict { get; set; }

        public new static LTEBTSVoiCoverManager GetInstance()
        {
            if (instance == null)
            {
                instance = new LTEBTSVoiCoverManager();
            }
            return instance;
        }

        public override void Show()
        {
            if (LTEBtsToPolygonDict == null)
            {
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (LTEBTS bts in LTEBtsToPolygonDict.Keys)
            {
                drawList.Add(LTEBtsToPolygonDict[bts]);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }

        public override void Clear()
        {
            Reset();
            VoronoiLayer.GetInstance().Clear();
        }

        protected override void Construct()
        {
            List<LTEBTS> lst = null;
            if (MapCellLayer.DrawCurrent)
            {
                lst = MainModel.GetInstance().CellManager.GetCurrentLTEBTSs();
            }
            else
            {
                lst = MainModel.GetInstance().CellManager.GetLTEBTSs(MapCellLayer.CurShowTimeAt);
            }
            LTEBtsToPolygonDict = VoronoiManager<LTEBTS>.GetInstance().Construct(lst, ValidFilter, isShowProgress);
            LastErrorText = VoronoiManager<LTEBTS>.GetInstance().LastErrorText;
        }

        private bool ValidFilter(LTEBTS bts, MapOperation2 mop2)
        {
            if (bts.Type != Condition.LteType)
            {
                return false;
            }
            return mop2.CheckPointInRegion(bts.VertexX, bts.VertexY);
        }

        protected LTEBTSVoiCoverManager()
        {
        }

        private static LTEBTSVoiCoverManager instance;
    }
}
