﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.AnyStat
{
    public partial class DlgAddRange : BaseDialog
    {
        public DlgAddRange()
        {
            InitializeComponent();
        }
        public DlgAddRange(float min, float max)
        {
            InitializeComponent();
            tbxMinValue.Text = min.ToString();
            tbxMaxValue.Text = max.ToString();
        }
        public ValueRange getResult()
        {
            ValueRange vr = new ValueRange();
            vr.minValue = float.Parse(tbxMinValue.Text.Trim());
            vr.maxValue = float.Parse(tbxMaxValue.Text.Trim());
            return vr;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            float minv = 0;
            float maxv = 0;
            if(!float.TryParse(tbxMinValue.Text.Trim(),out minv))
            {
                XtraMessageBox.Show(this, "值域下限中请输入数值型！");
                return;
            }
            if (!float.TryParse(tbxMaxValue.Text.Trim(), out maxv))
            {
                XtraMessageBox.Show(this, "值域上限中请输入数值型！");
                return;
            }
            if(minv>=maxv)
            {
                XtraMessageBox.Show(this, "值域上限应大于值域下限！");
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }

        }
    }
}