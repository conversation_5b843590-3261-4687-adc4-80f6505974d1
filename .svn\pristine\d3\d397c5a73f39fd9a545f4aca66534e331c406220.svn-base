﻿namespace MasterCom.RAMS.KPI_Statistics
{
    partial class ReportFilterControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.popupContainerCtr = new DevExpress.XtraEditors.PopupContainerControl();
            this.listBoxCtrData = new DevExpress.XtraEditors.ListBoxControl();
            this.label4 = new System.Windows.Forms.Label();
            this.txtFilter = new System.Windows.Forms.TextBox();
            this.popupCntEdit = new DevExpress.XtraEditors.PopupContainerEdit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerCtr)).BeginInit();
            this.popupContainerCtr.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxCtrData)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupCntEdit.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // popupContainerCtr
            // 
            this.popupContainerCtr.Controls.Add(this.listBoxCtrData);
            this.popupContainerCtr.Controls.Add(this.label4);
            this.popupContainerCtr.Controls.Add(this.txtFilter);
            this.popupContainerCtr.Location = new System.Drawing.Point(0, 49);
            this.popupContainerCtr.Name = "popupContainerCtr";
            this.popupContainerCtr.Size = new System.Drawing.Size(358, 217);
            this.popupContainerCtr.TabIndex = 24;
            // 
            // listBoxCtrData
            // 
            this.listBoxCtrData.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBoxCtrData.Location = new System.Drawing.Point(3, 3);
            this.listBoxCtrData.Name = "listBoxCtrData";
            this.listBoxCtrData.Size = new System.Drawing.Size(352, 177);
            this.listBoxCtrData.TabIndex = 4;
            this.listBoxCtrData.SelectedIndexChanged += new System.EventHandler(this.listBoxCtrData_SelectedIndexChanged);
            this.listBoxCtrData.DoubleClick += new System.EventHandler(this.listBoxCtrData_DoubleClick);
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(5, 190);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(71, 12);
            this.label4.TabIndex = 3;
            this.label4.Text = "关键字筛选:";
            // 
            // txtFilter
            // 
            this.txtFilter.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFilter.Location = new System.Drawing.Point(89, 186);
            this.txtFilter.Name = "txtFilter";
            this.txtFilter.Size = new System.Drawing.Size(263, 21);
            this.txtFilter.TabIndex = 2;
            this.txtFilter.TextChanged += new System.EventHandler(this.txtFilter_TextChanged);
            // 
            // popupCntEdit
            // 
            this.popupCntEdit.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.popupCntEdit.Location = new System.Drawing.Point(0, 0);
            this.popupCntEdit.Name = "popupCntEdit";
            this.popupCntEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.popupCntEdit.Properties.CloseOnOuterMouseClick = false;
            this.popupCntEdit.Properties.PopupControl = this.popupContainerCtr;
            this.popupCntEdit.Size = new System.Drawing.Size(380, 21);
            this.popupCntEdit.TabIndex = 23;
            this.popupCntEdit.QueryPopUp += new System.ComponentModel.CancelEventHandler(this.popupContainerEdit_QueryPopUp);
            // 
            // ReportFilterControl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.popupContainerCtr);
            this.Controls.Add(this.popupCntEdit);
            this.Name = "ReportFilterControl";
            this.Size = new System.Drawing.Size(380, 273);
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerCtr)).EndInit();
            this.popupContainerCtr.ResumeLayout(false);
            this.popupContainerCtr.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxCtrData)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupCntEdit.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PopupContainerControl popupContainerCtr;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtFilter;
        private DevExpress.XtraEditors.PopupContainerEdit popupCntEdit;
        private DevExpress.XtraEditors.ListBoxControl listBoxCtrData;
    }
}
