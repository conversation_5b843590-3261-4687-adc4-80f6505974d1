﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    public abstract class DiyInsertResultInfo : DiySqlMultiNonQuery
    {
        public string ErrMsg { get; protected set; }
        protected abstract string tableName { get; }
        protected Dictionary<int, List<CellParamInfo>> cityCellInfos = null;
        protected DiyInsertResultInfo(Dictionary<int, List<CellParamInfo>> cityCellInfos)
        {
            MainDB = true;
            this.cityCellInfos = cityCellInfos;
        }

        #region 通过服务端插入(弃用,速度太慢)
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrMsg = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = $"正在向数据库导入{Name}数据......";
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                ErrMsg = ee.Message + ee.Source + ee.StackTrace;
                MessageBox.Show(ErrorInfo);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Text = $"导入{Name}完毕.....";
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();

            strb.Append($@"IF NOT EXISTS(SELECT 1 FROM sysobjects WHERE type = 'U' AND name = '{tableName}')
                BEGIN
                    CREATE TABLE {tableName} (
                        [province]      VARCHAR(10), 
                        [provinceid]    INT, 
                        [city]          VARCHAR(30), 
                        [cityid]        INT,
                        [region]        VARCHAR(20), 
                        [grid]          VARCHAR(50), 
                        [enodebid]      INT,
                        [cellname]      VARCHAR(200),
                        [type]          VARCHAR(10), 
                        [tac]           INT, 
                        [ci]            BIGINT,
                        [tac16]         VARCHAR(50), 
                        [ci16]          VARCHAR(50),
                        [number]        VARCHAR(50)
                    );
                END;
                truncate table {tableName};
            ");
            foreach (var city in cityCellInfos.Values)
            {
                foreach (var info in city)
                {
                    string sql = $"insert into [{tableName}] " +
                    $"values('{info.Province}',{info.ProvinceID},'{info.City}',{info.CityID}," +
                    $"'{info.Region}','{info.Grid}',{info.Enodebid},'{info.CellName}','{info.Type}'," +
                    $"{info.TAC},{info.CI},'{info.TAC16}','{info.CI16}','{info.Number}')";
                    sql = sql.Replace(';', ',') + ";";
                    strb.Append(sql);
                }
            }
            return strb.ToString();
        }

        protected override void setWaitBoxText(int curIdx, int len)
        {
            WaitBox.Text = $"正在导入{Name}数据[{curIdx}]/[{len}]...";
        }
        #endregion

        #region 直连主库进行BCP(服务端插入用时10min,Bcp 1s)
        public virtual bool Bcp(SqlConnectionStringBuilder sb)
        {
            string errMsg = BcpHelper<Dictionary<int, List<CellParamInfo>>>.Bcp(sb, tableName, addDatas, cityCellInfos);

            if (!string.IsNullOrEmpty(errMsg))
            {
                MessageBox.Show(errMsg);
                return false;
            }

            return true;
        }

        protected virtual void addDatas(BCPStore bcp, Dictionary<int, List<CellParamInfo>> cityCellInfos)
        {

        }
        #endregion
    }

    public class DiyInsertNRResultInfo : DiyInsertResultInfo
    {
        protected override string tableName { get { return "tb_high_reverse_flow_coverage_nr_cell_result"; } }

        public DiyInsertNRResultInfo(Dictionary<int, List<CellParamInfo>> cityCellInfos)
            : base(cityCellInfos)
        {
        }

        protected override void addDatas(BCPStore bcp, Dictionary<int, List<CellParamInfo>> cityCellInfos)
        {
            foreach (var city in cityCellInfos.Values)
            {
                foreach (var cellInfo in city)
                {
                    object[] values = new object[] { cellInfo.Province, cellInfo.ProvinceID, cellInfo.City
                        , cellInfo.CityID, cellInfo.Region, cellInfo.Grid, cellInfo.Enodebid, cellInfo.CellName
                        , cellInfo.Type, cellInfo.TAC, cellInfo.CI, cellInfo.TAC16, cellInfo.CI16
                        , cellInfo.Number};
                    bcp.AddData(values);
                }
            }
        }

        public override string Name
        {
            get { return "5G工参结果表"; }
        }
    }

    public class DiyInsertLTEResultInfo : DiyInsertResultInfo
    {
        protected override string tableName { get { return "tb_high_reverse_flow_coverage_lte_cell_result"; } }

        public DiyInsertLTEResultInfo(Dictionary<int, List<CellParamInfo>> cityCellInfos)
            : base(cityCellInfos)
        {
        }

        public override string Name
        {
            get { return "4G工参结果表"; }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();

            strb.Append($@"IF NOT EXISTS(SELECT 1 FROM sysobjects WHERE type = 'U' AND name = '{tableName}')
                BEGIN
                    CREATE TABLE {tableName} (
                        [province]      VARCHAR(10), 
                        [provinceid]    INT, 
                        [city]          VARCHAR(30), 
                        [cityid]        INT,
                        [region]        VARCHAR(20), 
                        [grid]          VARCHAR(50), 
                        [enodebid]      INT,
                        [cellname]      VARCHAR(200),
                        [type]          VARCHAR(10), 
                        [tac]           INT, 
                        [ci]            BIGINT,
                        [tac16]         VARCHAR(50), 
                        [ci16]          VARCHAR(50),
                        [number]        VARCHAR(50),
                        [distance]      float
                    );
                END;
                truncate table {tableName};
            ");
            foreach (var city in cityCellInfos.Values)
            {
                foreach (var info in city)
                {
                    var lte = info as LTECellInfo;
                    string sql = $"insert into [{tableName}] " +
                    $"values('{info.Province}',{info.ProvinceID},'{info.City}',{info.CityID}," +
                    $"'{info.Region}','{info.Grid}',{info.Enodebid},'{info.CellName}','{info.Type}'," +
                    $"{info.TAC},{info.CI},'{info.TAC16}','{info.CI16}','{info.Number}',{lte.Distance})";
                    sql = sql.Replace(';', ',') + ";";
                    strb.Append(sql);
                }
            }
            return strb.ToString();
        }

        protected override void addDatas(BCPStore bcp, Dictionary<int, List<CellParamInfo>> cityCellInfos)
        {
            foreach (var city in cityCellInfos.Values)
            {
                foreach (var cellInfo in city)
                {
                    var lte = cellInfo as LTECellInfo;
                    object[] values = new object[] { cellInfo.Province, cellInfo.ProvinceID, cellInfo.City
                        , cellInfo.CityID, cellInfo.Region, cellInfo.Grid, cellInfo.Enodebid, cellInfo.CellName
                        , cellInfo.Type, cellInfo.TAC, cellInfo.CI, cellInfo.TAC16, cellInfo.CI16
                        , cellInfo.Number, lte.Distance };
                    bcp.AddData(values);
                }
            }
        }
    }
}
