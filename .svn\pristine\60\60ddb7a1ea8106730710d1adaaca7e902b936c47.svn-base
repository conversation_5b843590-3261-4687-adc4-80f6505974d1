﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    class ChkMgrsLibTestQuery : DIYReplayFileQuery
    {
        public ChkMgrsLibTestQuery(MainModel mModel) : base(mModel)
        {
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get { return "栅格转换库测试"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override bool isValidCondition()
        {
            ChkMgrsLibTestSetForm setForm = new ChkMgrsLibTestSetForm();
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            maxDistance = setForm.GetCondition();
            return true;
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = false;
            option.MessageInclude = false;

            return option;
        }

        protected override void fireShowResult()
        {
            ChkMgrsLibTestResultForm resultForm = MainModel.GetObjectFromBlackboard(
                typeof(ChkMgrsLibTestResultForm).FullName) as ChkMgrsLibTestResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ChkMgrsLibTestResultForm(MainModel);
            }
            resultForm.FillData(resultList);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
            resultList.Clear();
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            this.AnalyzeFile();
            MainModel.DTDataManager.Clear();
        }

        private void AnalyzeFile()
        {
            foreach (DTFileDataManager fileManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in fileManager.TestPoints)
                {
                    MgrsLibTestItem item = new MgrsLibTestItem(tp);
                    if (!item.IsValid(maxDistance))
                    {
                        resultList.Add(item);
                    }
                }
            }
        }

        private readonly List<MgrsLibTestItem> resultList = new List<MgrsLibTestItem>();

        private double maxDistance;

        #region queryColumns
        private readonly List<string> queryColumns = new List<string>()
        {
            "isampleid",
            "itime",
            "ilongitude",
            "ilatitude",
            "lte_TAC", 
            "lte_ECI",
            "lte_RSRP",
            "lte_SINR",
            "lte_EARFCN",
            "lte_PCI",
        };
        #endregion
    }

    class MgrsLibTestItem
    {
        public MgrsLibTestItem(TestPoint tp)
        {
            this.FileName = tp.FileName;
            this.TpSN = tp.SN;
            this.Longitude = tp.Longitude;
            this.Latitude = tp.Latitude;
        }

        public string FileName
        {
            get;
            private set;
        }

        public int TpSN
        {
            get;
            private set;
        }

        public double Longitude
        {
            get;
            private set;
        }

        public double Latitude
        {
            get;
            private set;
        }

        public double LtLng
        {
            get;
            private set;
        }

        public double LtLat
        {
            get;
            private set;
        }

        public double BrLng
        {
            get;
            private set;
        }

        public double BrLat
        {
            get;
            private set;
        }

        public string Mgrs
        {
            get;
            private set;
        }

        public double LtDistance
        {
            get;
            private set;
        }

        public double BrDistance
        {
            get;
            private set;
        }

        public bool IsValid(double maxDistance)
        {
            this.Mgrs = MgrsGridConverter.GetMgrsString(this.Longitude, this.Latitude, 50);
            double ltLng, ltLat, brLng, brLat;
            MgrsGridConverter.GetGridLngLat(this.Mgrs, out ltLng, out ltLat, out brLng, out brLat);
            this.LtLng = ltLng;
            this.LtLat = ltLat;
            this.BrLng = brLng;
            this.BrLat = brLat;
            this.LtDistance = Math.Round(MathFuncs.GetDistance(this.Longitude, this.Latitude, this.LtLng, this.LtLat), 2);
            this.BrDistance = Math.Round(MathFuncs.GetDistance(this.Longitude, this.Latitude, this.BrLng, this.BrLat), 2);
            return this.LtDistance <= maxDistance && this.BrDistance <= maxDistance;
        }
    }
}
