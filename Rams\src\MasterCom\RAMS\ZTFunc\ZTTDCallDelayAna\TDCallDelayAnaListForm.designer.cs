﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDCallDelayAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TDCallDelayAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCallDelay = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMO = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMT = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCallAttempt = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAlerting = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDelay = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalTime = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallDelay)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 76);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewCallDelay
            // 
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnSN);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnMO);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnMT);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnCallAttempt);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnAlerting);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnDelay);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnSignalName);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnSignalTime);
            this.ListViewCallDelay.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnMO,
            this.olvColumnMT,
            this.olvColumnCallAttempt,
            this.olvColumnAlerting,
            this.olvColumnDelay,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnSignalName,
            this.olvColumnSignalTime});
            this.ListViewCallDelay.ContextMenuStrip = this.ctxMenu;
            this.ListViewCallDelay.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCallDelay.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCallDelay.FullRowSelect = true;
            this.ListViewCallDelay.GridLines = true;
            this.ListViewCallDelay.HeaderWordWrap = true;
            this.ListViewCallDelay.IsNeedShowOverlay = false;
            this.ListViewCallDelay.Location = new System.Drawing.Point(0, 0);
            this.ListViewCallDelay.Name = "ListViewCallDelay";
            this.ListViewCallDelay.OwnerDraw = true;
            this.ListViewCallDelay.ShowGroups = false;
            this.ListViewCallDelay.Size = new System.Drawing.Size(1329, 502);
            this.ListViewCallDelay.TabIndex = 5;
            this.ListViewCallDelay.UseCompatibleStateImageBehavior = false;
            this.ListViewCallDelay.View = System.Windows.Forms.View.Details;
            this.ListViewCallDelay.VirtualMode = true;
            this.ListViewCallDelay.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnMO
            // 
            this.olvColumnMO.HeaderFont = null;
            this.olvColumnMO.Text = "主叫";
            this.olvColumnMO.Width = 100;
            // 
            // olvColumnMT
            // 
            this.olvColumnMT.HeaderFont = null;
            this.olvColumnMT.Text = "被叫";
            this.olvColumnMT.Width = 100;
            // 
            // olvColumnCallAttempt
            // 
            this.olvColumnCallAttempt.HeaderFont = null;
            this.olvColumnCallAttempt.Text = "起呼时间";
            this.olvColumnCallAttempt.Width = 200;
            // 
            // olvColumnAlerting
            // 
            this.olvColumnAlerting.HeaderFont = null;
            this.olvColumnAlerting.Text = "振铃时间";
            this.olvColumnAlerting.Width = 200;
            // 
            // olvColumnDelay
            // 
            this.olvColumnDelay.HeaderFont = null;
            this.olvColumnDelay.Text = "接续时长（毫秒）";
            this.olvColumnDelay.Width = 110;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnSignalName
            // 
            this.olvColumnSignalName.HeaderFont = null;
            this.olvColumnSignalName.Text = "信令名称";
            this.olvColumnSignalName.Width = 200;
            // 
            // olvColumnSignalTime
            // 
            this.olvColumnSignalTime.HeaderFont = null;
            this.olvColumnSignalTime.Text = "信令时间";
            this.olvColumnSignalTime.Width = 200;
            // 
            // TDCallDelayAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1329, 502);
            this.Controls.Add(this.ListViewCallDelay);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "TDCallDelayAnaListForm";
            this.Text = "TD接续分析";
            this.Load += new System.EventHandler(this.TDCallDelayAnaListForm_Load);
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallDelay)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewCallDelay;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMT;
        private BrightIdeasSoftware.OLVColumn olvColumnMO;
        private BrightIdeasSoftware.OLVColumn olvColumnCallAttempt;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDelay;
        private BrightIdeasSoftware.OLVColumn olvColumnAlerting;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalName;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalTime;

    }
}