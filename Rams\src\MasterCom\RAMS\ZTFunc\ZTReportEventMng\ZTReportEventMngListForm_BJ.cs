using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.ES.ColorManager;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTReportEventMngListForm_BJ : MinCloseForm
    {
        public ZTReportEventMngListForm_BJ(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }
        private List<ZTReportEventInfo_BJ> reportEventInfoList = null;

        public void FillData(List<ZTReportEventInfo_BJ> reportEventInfoList)
        {
            this.reportEventInfoList = new List<ZTReportEventInfo_BJ>();
            this.reportEventInfoList = reportEventInfoList;

            BindingSource source = new BindingSource();
            source.DataSource = this.reportEventInfoList;
            gridControlReportEvent.DataSource = source;
            gridControlReportEvent.RefreshDataSource();
            gridView.BestFitColumns();
        }


        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            Event eventInfo = getEventInfoFromGridView();

            if (eventInfo != null)
            {
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(eventInfo);
            }
        }

        private void miCompareReplayEvent_Click(object sender, EventArgs e)
        {
            Event eventInfo = getEventInfoFromGridView();
            if (eventInfo != null)
            {
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePartBothSides(eventInfo);
            }
        }

        private Event getEventInfoFromGridView()
        {
            if (gridView.SelectedRowsCount > 0)
            {
                ZTReportEventInfo_BJ info = gridView.GetRow(gridView.GetSelectedRows()[0]) as ZTReportEventInfo_BJ;

                if (info != null)
                {
                    Event eventInfo = new Event();
                    eventInfo.ApplyHeader(info.FileInfo);
                    eventInfo.Time = info.EventTimeSec;
                    eventInfo.Millisecond = (short)info.EventTimeMs;
                    eventInfo.ID = info.EventInfo.ID;
                    eventInfo["Value7"] = info.EventTimeSec - 180;

                    return eventInfo;
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        ZTReportEventToOtherAreaForm_BJ toOtherAreaForm = null;
        private void miToOtherArea_Click(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                ZTReportEventInfo_BJ info = gridView.GetRow(gridView.GetSelectedRows()[0]) as ZTReportEventInfo_BJ;

                if (info != null)
                {
                    if (toOtherAreaForm == null || toOtherAreaForm.IsDisposed)
                    {
                        toOtherAreaForm = new ZTReportEventToOtherAreaForm_BJ(this.mModel, this);
                    }

                    toOtherAreaForm.FillData(info);

                    if (toOtherAreaForm.ShowDialog() == DialogResult.OK && toOtherAreaForm.GetResult())
                    {
                        reportEventInfoList.Remove(info);
                        FillData(reportEventInfoList);
                    }
                }
            }
        }

        ZTReportEventProcessForm_BJ processForm = null;
        ZTReportEventProcessNormalForm_BJ processNormalForm = null;
        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hitInfo = gridView.CalcHitInfo(dxEvt.Location);
            if (hitInfo == null || !hitInfo.InRow)
            {
                return;
            }
            ZTReportEventInfo_BJ info = gridView.GetRow(hitInfo.RowHandle) as ZTReportEventInfo_BJ;

            if (info != null)
            {
                showProcess(info);
            }
        }

        private void showProcess(ZTReportEventInfo_BJ info)
        {
            if (info.NetType == "GSM"
                && (ZTReportEventMngQuery_BJ.isPreprocess || info.PreprocessTime != ""))
            {
                if (processForm == null || processForm.IsDisposed)
                {
                    processForm = new ZTReportEventProcessForm_BJ(this.mModel, this);
                }
                processForm.FillData(info);
                if (!processForm.Visible)
                {
                    processForm.ShowDialog(this);
                }
            }
            else
            {
                if (processNormalForm == null || processNormalForm.IsDisposed)
                {
                    processNormalForm = new ZTReportEventProcessNormalForm_BJ(this.mModel, this);
                }
                processNormalForm.FillData(info);
                if (!processNormalForm.Visible)
                {
                    processNormalForm.ShowDialog(this);
                }
            }
        }
    }
}