﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class CellSignManager
    {
        private static CellSignManager instance = null;
        private CellSignManager()
        {
            queryGsmCellSign(CellParamCfgManager.GetInstance().DBConnectionStr);
            queryTDCellSign(CellParamCfgManager.GetInstance().DBConnectionStr);
        }
        public static CellSignManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CellSignManager();
            }
            return instance;
        }

        private void queryGsmCellSign(string dbConnStr)
        {
            using (SqlConnection conn = new SqlConnection(dbConnStr))
            {
                string sqlTxt = @"SELECT
       [id]
      ,[begin_time]
      ,[end_time]
      ,[自维护ID]
      ,[小区号]
      ,[LAC]
      ,[CI]
  FROM [MTNOH_AAA_Resource2].[dbo].[TB_GSM_小区标识]";
                SqlCommand command = new SqlCommand(sqlTxt, conn);
                conn.Open();
                SqlDataReader reader = command.ExecuteReader();
                while (reader.Read())
                {
                    try
                    {
                        GSMCellSign cellSign = GSMCellSign.Fill(reader);
                        if (cellSign != null)
                        {
                            add(cellSign);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                    }
                }
            }
        }

        private void queryTDCellSign(string dbConnStr)
        {
            using (SqlConnection conn = new SqlConnection(dbConnStr))
            {
                string sqlTxt = @"SELECT
       [id]
      ,[begin_time]
      ,[end_time]
      ,[自维护ID]
      ,[小区号]
      ,[LAC]
      ,[CI]
      ,[RNCID]
      ,[RNCNAME]
  FROM [MTNOH_AAA_Resource2].[dbo].[TB_TD_小区标识]";
                SqlCommand command = new SqlCommand(sqlTxt, conn);
                conn.Open();
                SqlDataReader reader = command.ExecuteReader();
                while (reader.Read())
                {
                    try
                    {
                        TDCellSign cellSign = TDCellSign.Fill(reader);
                        if (cellSign != null)
                        {
                            add(cellSign);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                    }
                }
            }
        }

        private readonly Dictionary<int, List<TDCellSign>> tdSignIDCellMap = new Dictionary<int, List<TDCellSign>>();
        private readonly Dictionary<int, List<TDCellSign>> tdLacCiCellMap = new Dictionary<int, List<TDCellSign>>();
        private void add(TDCellSign cellSign)
        {
            int key = CellManager.MakeLACCIKey((ushort)cellSign.LAC, (ushort)cellSign.CI);
            List<TDCellSign> cells = null;
            if (lacciCellMap.ContainsKey(key))
            {
                cells = tdLacCiCellMap[key];
            }
            else
            {
                cells = new List<TDCellSign>();
                tdLacCiCellMap[key] = cells;
            }
            cells.Add(cellSign);

            if (!tdSignIDCellMap.ContainsKey(cellSign.SignID))
            {
                tdSignIDCellMap.Add(cellSign.SignID, new List<TDCellSign>());
            }
            tdSignIDCellMap[cellSign.SignID].Add(cellSign);
        }

        /// <summary>
        /// SignID,SignCell字典
        /// </summary>
        public Dictionary<int, List<GSMCellSign>> SignCellMap
        {
            get { return signIDCellMap; }
        }

        private readonly Dictionary<int, List<GSMCellSign>> signIDCellMap = new Dictionary<int,List<GSMCellSign>>();
        private readonly Dictionary<int, List<GSMCellSign>> lacciCellMap = new Dictionary<int, List<GSMCellSign>>();
        public void add(GSMCellSign cell)
        {
            int key = CellManager.MakeLACCIKey((ushort)cell.LAC, (ushort)cell.CI);
            List<GSMCellSign> cells = null;
            if (lacciCellMap.ContainsKey(key))
            {
                cells = lacciCellMap[key];
            }
            else
            {
                cells = new List<GSMCellSign>();
                lacciCellMap[key] = cells;
            }
            cells.Add(cell);

            if (!signIDCellMap.ContainsKey(cell.SignID))
            {
                signIDCellMap.Add(cell.SignID, new List<GSMCellSign>());
            }
            signIDCellMap[cell.SignID].Add(cell);
        }

        /// <summary>
        /// 根据小区获取对应的小区标记（由于有效时间段的不同，可能会关联到多个小区标记）
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public List<GSMCellSign> GetCellSign(Cell cell)
        {
            return GetCellSign(cell.ValidPeriod, cell);
        }

        public List<GSMCellSign> GetCellSign(TimePeriod period,Cell cell)
        {
            List<GSMCellSign> signs = new List<GSMCellSign>();
            int key = CellManager.MakeLACCIKey((ushort)cell.LAC, (ushort)cell.CI);
            if (lacciCellMap.ContainsKey(key))
            {
                foreach (GSMCellSign sign in lacciCellMap[key])
                {
                    if (sign.ValidPeriod.IsIntersect(period))
                    {
                        signs.Add(sign);
                    }
                }
            }
            return signs;
        }

        public GSMCellSign GetCellSign(DateTime time, Cell cell)
        {
            int key = CellManager.MakeLACCIKey((ushort)cell.LAC, (ushort)cell.CI);
            if (lacciCellMap.ContainsKey(key))
            {
                foreach (GSMCellSign sign in lacciCellMap[key])
                {
                    if (sign.ValidPeriod.Contains(time))
                    {
                        return sign;
                    }
                }
            }
            return null;
        }

        internal List<TDCellSign> GetCellSign(TimePeriod period, TDCell cell)
        {
            List<TDCellSign> signs = new List<TDCellSign>();
            int key = CellManager.MakeLACCIKey((ushort)cell.LAC, (ushort)cell.CI);
            if (tdLacCiCellMap.ContainsKey(key))
            {
                foreach (TDCellSign sign in tdLacCiCellMap[key])
                {
                    if (sign.ValidPeriod.IsIntersect(period))
                    {
                        signs.Add(sign);
                    }
                }
            }
            return signs;
        }

        public TDCellSign GetCellSign(DateTime time, TDCell cell)
        {
            int key = CellManager.MakeLACCIKey((ushort)cell.LAC, (ushort)cell.CI);
            if (tdLacCiCellMap.ContainsKey(key))
            {
                foreach (TDCellSign sign in tdLacCiCellMap[key])
                {
                    if (sign.ValidPeriod.Contains(time))
                    {
                        return sign;
                    }
                }
            }
            return null;
        }


    }
}
