﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat.Data;
using MasterCom.Util;
using System.Drawing;
using System.Drawing.Imaging;
using DevExpress.XtraTreeList;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.CQT
{
    public class CQTKPIDataManager
    {
        public event EventHandler GisShowColumnChanged;
        public class ColumnShowEventArgs : EventArgs
        {
            public object column { get; set; }
        }
        public void FireGisShowColumnChange(object sender, ColumnShowEventArgs args)
        {
            if (GisShowColumnChanged != null)
            {
                GisShowColumnChanged(sender, args);
            }
        }

        private static CQTKPIDataManager instance;
        public static CQTKPIDataManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CQTKPIDataManager();
            }
            return instance;
        }
        private CQTKPIDataManager()
        {
            this.GisShowColumnChanged += new EventHandler(CQTKPIDataManager_GisShowColumnChanged);
        }

        public bool IsStatMainPointOnly
        {
            get;
            set;
        }

        public void Clear()
        {
            curColorImageDic.Clear();
            curColorColumn = null;
            CurStatPoints.Clear();
            if (curPointNameDataDic != null)
            {
                curPointNameDataDic.Clear();
            }
            periodPntDataDic.Clear();
        }
        void CQTKPIDataManager_GisShowColumnChanged(object sender, EventArgs e)
        {
            ColumnShowEventArgs eA = (ColumnShowEventArgs)e;
            if (eA == null || eA.column == null)
            {
                return;
            }
            curColorColumn = eA.column;
            refreshColorImageByShowedColumn(eA.column);
        }

        private Dictionary<Color, Image> curColorImageDic = new Dictionary<Color, Image>();
        private object curColorColumn;
        public object CurColorColumn
        {
            get { return curColorColumn; }
        }

        private void refreshColorImageByShowedColumn(object colorColumn)
        {
            curColorImageDic = new Dictionary<Color, Image>();
            Image img = Properties.Resources.cqtDefault;
            Dictionary<Color, bool> clrDic = new Dictionary<Color, bool>();
            if (colorColumn is CQTKPIReportColumn)
            {
                foreach (CQTKPIScoreColorRange clrRng in (colorColumn as CQTKPIReportColumn).ScoreScheme.ScoreColorRanges)
                {
                    clrDic[clrRng.Color] = true;
                }
            }
            else if (colorColumn is CQTKPISummaryColumn)
            {
                foreach (DTParameterRangeColor clrRng in (colorColumn as CQTKPISummaryColumn).ScoreRangeColors)
                {
                    clrDic[clrRng.Value] = true;
                }
            }
            foreach (Color clr in clrDic.Keys)
            {
                Image imgClone = (Image)img.Clone();
                ImageAttributes imageAttributes = new ImageAttributes();
                int width = img.Width;
                int height = img.Height;
                ColorMap colorMap = new ColorMap();
                colorMap.OldColor = Color.FromArgb(255, 104, 51);
                colorMap.NewColor = clr;
                ColorMap[] remapTable = { colorMap };
                imageAttributes.SetRemapTable(remapTable, ColorAdjustType.Bitmap);
                Graphics g = Graphics.FromImage(imgClone);
                g.DrawImage(
                   imgClone,
                   new Rectangle(0, 0, width, height),  // destination rectangle 
                   0, 0,        // upper-left corner of source rectangle 
                   width,       // width of source rectangle
                   height,      // height of source rectangle
                   GraphicsUnit.Pixel,
                   imageAttributes);
                curColorImageDic[clr] = imgClone;
            }
        }

        /// <summary>
        ///  按当前着色指标列，获取CQT地点的GIS显示图片
        /// </summary>
        /// <param name="point">CQT地点</param>
        /// <param name="img">当前着色图片</param>
        /// <returns>true:统计的地点包括point；false:统计的地点不包括point</returns>
        public bool GetCQTPointImageByCurShowColorColumn(CQTPoint point, out Image img, out Color color)
        {
            bool contains = CurStatPoints.Contains(point);
            img = null;
            color = Color.Empty;
            if (contains && curPointNameDataDic != null)
            {
                getImgAndColor(point, ref img, ref color);
            }
            return contains;
        }

        private void getImgAndColor(CQTPoint point, ref Image img, ref Color color)
        {
            CQTMainPointKPI pntKPI = null;
            if (curPointNameDataDic.TryGetValue(point.Name, out pntKPI))
            {
                if (curColorColumn is CQTKPIReportColumn)
                {
                    color = pntKPI.ColumnKPIResultDic[curColorColumn as CQTKPIReportColumn].Color;
                    if (!color.IsEmpty)
                    {
                        img = curColorImageDic[color];
                    }
                }
                else if (curColorColumn is CQTKPISummaryColumn)
                {
                    color = pntKPI.SummaryResultDic[curColorColumn as CQTKPISummaryColumn].Color;
                    if (!color.IsEmpty)
                    {
                        img = curColorImageDic[color];
                    }
                }
            }
            if (pntKPI == null)//无该点的统计信息
            {
                img = Properties.Resources.cqtDefault;
            }
        }

        /// <summary>
        /// 当前统计的地点
        /// </summary>
        public List<CQTPoint> CurStatPoints { get; set; } = new List<CQTPoint>();

        /// <summary>
        /// 当前统计的时间段，CQT地点数据信息
        /// </summary>
        Dictionary<string, CQTMainPointKPI> curPointNameDataDic = new Dictionary<string, CQTMainPointKPI>();

        /// <summary>
        /// 时间段-CQT地点统计信息 字典
        /// </summary>
        readonly Dictionary<TimePeriod, Dictionary<string, CQTMainPointKPI>> periodPntDataDic = new Dictionary<TimePeriod, Dictionary<string, CQTMainPointKPI>>();

        public void AddKPIStatData(FileInfo file, KPIStatDataBase parttialStatData, TimePeriod period)
        {
            string fileName = file.Name;
            string subPointName = "";
            string floorName = string.Empty;
            CQTPoint pnt = GetCQTPointByFileName(fileName, out subPointName, out floorName);//根据文件名获取CQT地点
            if (pnt == null)
            {
                return;
            }
            if (IsStatMainPointOnly)
            {
                subPointName = null;
            }

            Dictionary<string, CQTMainPointKPI> pointDataDic = null;//某时间段的 地点KPI数据
            if (!periodPntDataDic.TryGetValue(period, out pointDataDic))
            {
                pointDataDic = new Dictionary<string, CQTMainPointKPI>();
                periodPntDataDic.Add(period, pointDataDic);
            }
            CQTMainPointKPI mainPntData = null;//地点KPI数据
            if (!pointDataDic.TryGetValue(pnt.Name, out mainPntData))
            {//对应时间段未有当前地点信息，新建
                mainPntData = new CQTMainPointKPI(pnt);
                pointDataDic.Add(pnt.Name, mainPntData);
            }
            mainPntData.AddKPIStatData(file, subPointName, floorName, parttialStatData);
        }

        private CQTPoint GetCQTPointByFileName(string fileName, out string subName, out string floorName)
        {
            string pointName;
            GetPointName(fileName, out pointName, out subName, out floorName);
            if (pointName != null && pointName.Length > 0)
            {
                foreach (CQTPoint pnt in CurStatPoints)
                {
                    if (pnt.Name == pointName)
                    {
                        return pnt;
                    }
                }
            }
            return null;
        }


        public static void GetPointName(string fileName, out string mainPointName, out string subPointName, out string floorName)
        {//RCU_20120524_枫叶新新家园_@2号楼1单元1层_语音.rcu(02)
            //rcu_20121130_药监局家属楼_@1号楼1单元_1F_GSMDT1130110438.rcu
            mainPointName = "";
            subPointName = "";
            floorName = "";
            string[] atArr = fileName.Split('@');
            if (atArr.Length < 2)
            {
                return;
            }
            string mainStr = atArr[0];//RCU_20120524_枫叶新新家园_    rcu_20121130_药监局家属楼_
            string[] tmpArr = mainStr.Split('_');
            if (tmpArr.Length < 3)
            {
                return;
            }
            mainPointName = tmpArr[tmpArr.Length - 2];

            string subStr = atArr[1];//2号楼1单元1层_语音.rcu(02)  1号楼1单元_1F_GSMDT1130110438.rcu
            tmpArr = subStr.Split('_');
            if (tmpArr.Length > 0)
            {
                subPointName = tmpArr[0];
                if (tmpArr.Length > 2)
                {
                    floorName = tmpArr[1];
                }
            }
            else if (subStr.IndexOf(".") != -1)
            {
                subPointName = subStr.Substring(0, subStr.IndexOf("."));
            }
        }

        private int CurCarreerID = 0;
        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, TimePeriod period, CQTKPIReport report)
        {
            CurCarreerID = report.CarreerID;
            makeReportColumn(treeList, report);
            treeList.Nodes.Clear();
            if (periodPntDataDic.TryGetValue(period, out curPointNameDataDic))
            {
                foreach (CQTMainPointKPI mainPntData in curPointNameDataDic.Values)
                {
                    mainPntData.MakeReportData(treeList, report);
                }
            }
        }

        public void MakeReport(TimePeriod period, CQTKPIReport report)
        {
            CurCarreerID = report.CarreerID;
            if (periodPntDataDic.TryGetValue(period, out curPointNameDataDic))
            {
                foreach (CQTMainPointKPI mainPntData in curPointNameDataDic.Values)
                {
                    mainPntData.Stat(report);
                }
            }
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="point"></param>
        /// <param name="mainPointResult"></param>
        /// <param name="filesResult"></param>
        /// <returns></returns>
        public void GetCurShowPointKPI(CQTPoint point, out CQTMainPointKPI mainPointResult, out List<CQTFileKPIData> filesResult)
        {
            mainPointResult = null;
            if (curPointNameDataDic.ContainsKey(point.Name))
            {
                mainPointResult = curPointNameDataDic[point.Name];
            }
            filesResult = null;
            if (mainPointResult != null)
            {
                filesResult = new List<CQTFileKPIData>();
                foreach (CQTSubPointKPI subPoint in mainPointResult.SubPointNameDataDic.Values)
                {
                    foreach (CQTFileKPIData fileData in subPoint.CarreerFileDataDic[CurCarreerID])
                    {
                        filesResult.Add(fileData);
                    }
                }
            }
        }

        private void makeReportColumn(TreeList treeList, CQTKPIReport report)
        {
            treeList.Columns.Clear();
            DevExpress.XtraTreeList.Columns.TreeListColumn colPntName = treeList.Columns.Add();
            colPntName.Caption = "地点/文件名称";
            colPntName.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colPntName.OptionsColumn.AllowEdit = false;
            colPntName.OptionsColumn.AllowMoveToCustomizationForm = false;
            colPntName.OptionsColumn.ReadOnly = true;
            colPntName.Name = "pointName";
            colPntName.Visible = true;
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Caption = rptCol.Name;
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;
                col.Tag = rptCol;
            }

            foreach (CQTKPISummaryColumn smCol in report.SummaryColumns)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Caption = smCol.Name;
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;
                col.Tag = smCol;
            }

        }

    }

}
