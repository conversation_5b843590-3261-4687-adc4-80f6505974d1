﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraGrid.Views.Grid;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func.EventSearchForm
{
    public class GridViewSerchInfo : ASerchInfo
    {
        int rowIndex = 0;
        readonly GridView gridView;
        public GridViewSerchInfo(GridView gridView)
        {
            this.gridView = gridView;
            this.gridView.Click += gridView_Click;
            this.gridView.KeyDown += gridView_KeyDown;
        }
        private void gridView_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
            }
            else if (e.Control)
            {
                bool pressF = e.KeyCode == Keys.F;
                if (pressF)
                {
                    e.Handled = true;
                    SearchInfoForm.OneInstence.Show(gridView as IWin32Window);
                }
            }
        }

        private void gridView_Click(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Base.GridCell[] gc = gridView.GetSelectedCells();

            if (gc.Length > 0 && isFireEvent && gc[0].ToString() != null)
            {
                this.FireSerchTextChanged(gc[0].ToString());
            }
        }

        #region ISerchInfo 成员

        public override bool DoSerch(SearchType type, string str)
        {
            if (gridView.GetSelectedRows().Length > 0)
            {
                rowIndex = gridView.GetSelectedRows()[0];
            }
            if ((type & SearchType.MatchUp) != 0x00) //向上搜索
            {
                return searchPrevious(type, str);
            }
            else //向下搜索
            {
                return searchNext(type, str);
            }
        }

        private bool searchPrevious(SearchType type, string str)
        {
            rowIndex = rowIndex - 1 < 0 ? gridView.RowCount - 1 : rowIndex - 1;
            for (int i = rowIndex; i >= 0; i--)
            {
                if (SelectedItem(i, type, str))
                    return true;
            }
            for (int i = gridView.RowCount - 1; i > rowIndex + 1; i--)
            {
                if (SelectedItem(i, type, str))
                    return true;
            }
            return false;
        }

        private bool searchNext(SearchType type, string str)
        {
            rowIndex = rowIndex + 1 > this.gridView.RowCount - 1 ? 0 : rowIndex + 1;
            for (int i = rowIndex; i < this.gridView.RowCount; i++)
            {
                if (SelectedItem(i, type, str))
                    return true;
            }
            for (int i = 0; i < rowIndex - 1; i++)
            {
                if (SelectedItem(i, type, str))
                    return true;
            }
            return false;
        }

        private bool SelectedItem(int rowId,SearchType type,string str)
        {
            System.Web.UI.WebControls.GridViewRow item = gridView.GetRow(rowId) as System.Web.UI.WebControls.GridViewRow;
            foreach (System.Web.UI.WebControls.TableCell subItem in item.Cells)
            {
                if (IsMatch(type, subItem.Text.ToString(), str))
                {
                    this.gridView.ClearSelection();
                    //item.Selected = true;
                    this.gridView.DefaultRelationIndex = rowId - 20 > 0 ? rowId - 20 : 0;
                    rowIndex = rowId;
                    this.gridView.Invalidate();
                    return true;
                }
            }
            return false;
        }

        #endregion

       
    }
}
