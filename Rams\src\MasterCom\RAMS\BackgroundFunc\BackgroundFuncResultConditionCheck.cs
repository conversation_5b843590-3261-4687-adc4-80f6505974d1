﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class BackgroundFuncResultConditionCheck : DIYSQLBase
    {
        int funcId = -1;
        string strConditon = "";
        string strDes = "";

        private static BackgroundFuncResultConditionCheck intance = null;
        public static BackgroundFuncResultConditionCheck GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncResultConditionCheck();
            }
            return intance;
        }
        protected BackgroundFuncResultConditionCheck()
            : base(MainModel.GetInstance())
        {
        }
        public override string Name
        {
            get { return "核查体检结果条件"; }
        }
        public void SetCond(BackgroundQueryBase queryFunc)
        {
            if (queryFunc == null)
            {
                return;
            }
            funcId = queryFunc.GetSubFuncID();
            strConditon = queryFunc.GetQueryCondParams();
            strDes = queryFunc.Name;
        }
        protected override string getSqlTextString()
        {
            if (funcId == -1)
            {
                return "";
            }
            string strProject = BackgroundFuncBaseSetting.GetInstance().projectType;

            string sql = string.Format("exec sp_probchk_result_condition_save {0},'{1}','{2}','{3}'"
                , funcId, strConditon, strProject, strDes);

            initDatasAfterGetSql();
            return sql;
        }
        private void initDatasAfterGetSql()
        {
            funcId = -1;
            strConditon = strDes = "";

        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    package.Content.GetParamInt();//status
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
