﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public class OrderGridItem : GridUnitBase
    {
        internal static OrderGridItem Create(Net.Content content)
        {
            OrderGridItem item = new OrderGridItem();
            item.DistrictID = content.GetParamInt();
            item.SetOrderType = content.GetParamString();
            item.SetID = content.GetParamInt();
            item.ItemID = content.GetParamInt();
            item.GridSN = content.GetParamString();
            string[] arr = item.GridSN.Split('-');
            int colIdx = int.Parse(arr[0]);
            int rowIdx = int.Parse(arr[1]);
            double lng, lat;
            GridHelper.GetLeftTopByCustomSizeGridIndex(1, rowIdx, colIdx, out lng, out lat);
            item.LTLng = lng;
            item.LTLat = lat;

            item.TAC = content.GetParamInt();
            item.ECI = content.GetParamInt();
            item.Cell = CellManager.GetInstance().GetCurrentLTECell(item.TAC, item.ECI);
            return item;
        }

        public int TAC { get; set; }

        public int ECI { get; set; }

        public int SetID { get; set; }

        public int ItemID { get; set; }

        public string SetOrderType { get; set; }

        public string OrderKey
        {
            get { return string.Format("{0}.{1}.{2}", DistrictID, SetOrderType, SetID); }
        }

        public int DistrictID { get; set; }

        public string GridSN { get; set; }

        public ICell Cell { get; set; }

        public GridOrder Order { get; set; }
    }
}
