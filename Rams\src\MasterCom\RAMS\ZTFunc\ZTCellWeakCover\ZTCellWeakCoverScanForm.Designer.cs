﻿namespace MasterCom.RAMS.Func
{
    partial class ZTCellWeakCoverScanForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewCellWeakCover = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCellID = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCellName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLAC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCI = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBCCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBSIC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxlevMax = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxLevMin = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxlevMean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderWeak = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTotal = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRatio = new System.Windows.Forms.ColumnHeader();
            this.contextMenuStripCellWeakCover = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStripCellWeakCover.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewCellWeakCover
            // 
            this.listViewCellWeakCover.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderCellID,
            this.columnHeaderCellName,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeaderBCCH,
            this.columnHeaderBSIC,
            this.columnHeaderRxlevMax,
            this.columnHeaderRxLevMin,
            this.columnHeaderRxlevMean,
            this.columnHeaderWeak,
            this.columnHeaderTotal,
            this.columnHeaderRatio});
            this.listViewCellWeakCover.ContextMenuStrip = this.contextMenuStripCellWeakCover;
            this.listViewCellWeakCover.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCellWeakCover.FullRowSelect = true;
            this.listViewCellWeakCover.GridLines = true;
            this.listViewCellWeakCover.Location = new System.Drawing.Point(0, 0);
            this.listViewCellWeakCover.Name = "listViewCellWeakCover";
            this.listViewCellWeakCover.Size = new System.Drawing.Size(1023, 311);
            this.listViewCellWeakCover.TabIndex = 3;
            this.listViewCellWeakCover.UseCompatibleStateImageBehavior = false;
            this.listViewCellWeakCover.View = System.Windows.Forms.View.Details;
            this.listViewCellWeakCover.SelectedIndexChanged += new System.EventHandler(this.listViewCellWeakCover_SelectedIndexChanged);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            // 
            // columnHeaderCellID
            // 
            this.columnHeaderCellID.Text = "小区ID";
            // 
            // columnHeaderCellName
            // 
            this.columnHeaderCellName.Text = "小区名";
            // 
            // columnHeaderLAC
            // 
            this.columnHeaderLAC.Text = "LAC";
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Text = "CI";
            // 
            // columnHeaderBCCH
            // 
            this.columnHeaderBCCH.Text = "BCCH";
            this.columnHeaderBCCH.Width = 65;
            // 
            // columnHeaderBSIC
            // 
            this.columnHeaderBSIC.Text = "BSIC";
            // 
            // columnHeaderRxlevMax
            // 
            this.columnHeaderRxlevMax.Text = "最大场强";
            this.columnHeaderRxlevMax.Width = 75;
            // 
            // columnHeaderRxLevMin
            // 
            this.columnHeaderRxLevMin.Text = "最小场强";
            this.columnHeaderRxLevMin.Width = 82;
            // 
            // columnHeaderRxlevMean
            // 
            this.columnHeaderRxlevMean.Text = "平均场强";
            this.columnHeaderRxlevMean.Width = 90;
            // 
            // columnHeaderWeak
            // 
            this.columnHeaderWeak.Text = "弱覆盖采样点数";
            this.columnHeaderWeak.Width = 110;
            // 
            // columnHeaderTotal
            // 
            this.columnHeaderTotal.Text = "总采样点数";
            this.columnHeaderTotal.Width = 88;
            // 
            // columnHeaderRatio
            // 
            this.columnHeaderRatio.Text = "弱覆盖采样点比例(%)";
            this.columnHeaderRatio.Width = 148;
            // 
            // contextMenuStripCellWeakCover
            // 
            this.contextMenuStripCellWeakCover.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStripCellWeakCover.Name = "contextMenuStripCellWeakCover";
            this.contextMenuStripCellWeakCover.Size = new System.Drawing.Size(125, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuItemExport.Text = "导出列表";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ZTCellWeakCoverScanForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1023, 311);
            this.Controls.Add(this.listViewCellWeakCover);
            this.Name = "ZTCellWeakCoverScanForm";
            this.Text = "弱覆盖小区分析列表";
            this.contextMenuStripCellWeakCover.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listViewCellWeakCover;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderCellID;
        private System.Windows.Forms.ColumnHeader columnHeaderCellName;
        private System.Windows.Forms.ColumnHeader columnHeaderLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderCI;
        private System.Windows.Forms.ColumnHeader columnHeaderBCCH;
        private System.Windows.Forms.ColumnHeader columnHeaderBSIC;
        private System.Windows.Forms.ColumnHeader columnHeaderRxlevMax;
        private System.Windows.Forms.ColumnHeader columnHeaderRxLevMin;
        private System.Windows.Forms.ColumnHeader columnHeaderRxlevMean;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripCellWeakCover;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private System.Windows.Forms.ColumnHeader columnHeaderWeak;
        private System.Windows.Forms.ColumnHeader columnHeaderTotal;
        private System.Windows.Forms.ColumnHeader columnHeaderRatio;
    }
}