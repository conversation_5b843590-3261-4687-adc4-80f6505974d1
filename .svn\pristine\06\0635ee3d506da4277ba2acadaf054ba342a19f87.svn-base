﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.NOP
{
    public class DiySqlTaskEvent : DIYSQLBase
    {
        public DiySqlTaskEvent(MainModel mainModel)
            : base(mainModel)
        {
            this.dbid = mainModel.User.DBID;
            this.curCityID = -1;

            ResultTable = new DataTable();
            ResultTable.Columns.Add("cityid", typeof(int));
            ResultTable.Columns.Add("fileid", typeof(int));
            ResultTable.Columns.Add("filename", typeof(string));
            ResultTable.Columns.Add("evtid", typeof(int));
            ResultTable.Columns.Add("evtname", typeof(string));
            ResultTable.Columns.Add("seqid", typeof(int));
            ResultTable.Columns.Add("ctime", typeof(DateTime));
            ResultTable.Columns.Add("evttime", typeof(DateTime));
            ResultTable.Columns.Add("stime", typeof(DateTime));
            ResultTable.Columns.Add("etime", typeof(DateTime));
            ResultTable.Columns.Add("midlongitude", typeof(double));
            ResultTable.Columns.Add("midlatitude", typeof(double));
            ResultTable.Columns.Add("primarytype", typeof(string));
            ResultTable.Columns.Add("specifictype", typeof(string));
            ResultTable.Columns.Add("detail", typeof(string));
            ResultTable.Columns.Add("suggest", typeof(string));
        }

        public override string Name
        {
            get { return "DiySqlTaskEvent"; }
        }

        public DateTime STime
        {
            get;
            set;
        }

        public DateTime ETime
        {
            get;
            set;
        }

        public DataTable ResultTable
        {
            get;
            private set;
        }

        protected override string getSqlTextString()
        {
#if DEBUG
            return string.Format("rams_wuhan.dbo.sp_focus_query_event '{0}', '{1}'",
                STime.ToString("yyyy-MM-dd HH:mm:ss"), ETime.ToString("yyyy-MM-dd HH:mm:ss"));
#else
            return string.Format("sp_focus_query_event '{0}', '{1}'",
                STime.ToString("yyyy-MM-dd HH:mm:ss"), ETime.ToString("yyyy-MM-dd HH:mm:ss"));
#endif
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[15];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_IntFloat;
            rType[10] = E_VType.E_IntFloat;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_String;
            rType[13] = E_VType.E_String;
            rType[14] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    object[] row = new object[this.ResultTable.Columns.Count];
                    row[0] = this.curCityID;
                    row[1] = package.Content.GetParamInt();
                    row[2] = package.Content.GetParamString();
                    row[3] = package.Content.GetParamInt();
                    row[4] = package.Content.GetParamString();
                    row[5] = package.Content.GetParamInt();

                    string tmpStr = package.Content.GetParamString();
                    row[6] = DateTime.Parse(tmpStr);
                    tmpStr = package.Content.GetParamString();
                    row[7] = DateTime.Parse(tmpStr);
                    tmpStr = package.Content.GetParamString();
                    row[8] = DateTime.Parse(tmpStr);
                    tmpStr = package.Content.GetParamString();
                    row[9] = DateTime.Parse(tmpStr);

                    //tmpStr = package.Content.GetParamString();
                    //row[9] = double.Parse(tmpStr);
                    //tmpStr = package.Content.GetParamString();
                    //row[10] = double.Parse(tmpStr);
                    row[10] = package.Content.GetParamDouble();
                    row[11] = package.Content.GetParamDouble();

                    row[12] = package.Content.GetParamString();
                    row[13] = package.Content.GetParamString();
                    row[14] = package.Content.GetParamString();
                    row[15] = package.Content.GetParamString();

                    this.ResultTable.Rows.Add(row);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        protected override void query()
        {
            List<int> districtIds = new List<int>();
            if (dbid > 0)
            {
                districtIds.Add(dbid);
            }
            else
            {
                for (int i = 2; i < DistrictManager.GetInstance().DistrictNames.Length; ++i)
                {
                    districtIds.Add(i);
                }
            }

            foreach (int cityid in districtIds)
            {
                ClientProxy clientProxy = new ClientProxy();
                try
                {
#if DEBUG
                    this.curCityID = 2;
#else
                    this.curCityID = cityid;
#endif
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, cityid) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        log.Error(string.Format("连接服务器失败, ip={0}, port={1}, username={2}, password={3}, cityid={4}",
                            MainModel.Server.IP, MainModel.Server.Port, userName, password, cityid));
                        return;
                    }
                    queryInThread(clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
        }

        private int curCityID;
    }
}
