﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDScanCellInfoQueryByRegion : DIYSampleByRegion
    {
        private readonly TDScanCellInfoStater stater;
        public TDScanCellInfoQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            stater = new TDScanCellInfoStater();
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16026, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_Channel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSSI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_C_I";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_SIR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", "TD扫频");
            tmpDic.Add("themeName", "TDSCAN_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            stater.StatTestPoint(tp);
        }

        protected override void FireShowFormAfterQuery()
        {
            List<TDScanCellInfo> cellInfoList = stater.GetStatResult();
            stater.Clear();

            TDScanCellInfoResultForm form = MainModel.GetObjectFromBlackboard(typeof(TDScanCellInfoResultForm).FullName) as TDScanCellInfoResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new TDScanCellInfoResultForm(MainModel);
            }
            form.FillData(cellInfoList);
            form.Show(MainModel.MainForm);
        }
    }

    public class WScanCellInfoQueryByRegion : DIYSampleByRegion
    {
        private readonly WScanCellInfoStater stater;
        public WScanCellInfoQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            stater = new WScanCellInfoStater();
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 32000, 32013, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHTotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHChannel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHPilot";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHSIR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", "WCDMA扫频");
            tmpDic.Add("themeName", "WCDAMSCAN_CPICHTotalRSCP");
            tmpDic.Add("columnsDef", columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            stater.StatTestPoint(tp);
        }

        protected override void FireShowFormAfterQuery()
        {
            List<WScanCellInfo> cellInfoList = stater.GetStatResult();
            stater.Clear();

            WScanCellInfoResultForm form = MainModel.GetObjectFromBlackboard(typeof(WScanCellInfoResultForm).FullName) as WScanCellInfoResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new WScanCellInfoResultForm(MainModel);
            }
            form.FillData(cellInfoList);
            form.Show(MainModel.MainForm);
        }
    }
}
