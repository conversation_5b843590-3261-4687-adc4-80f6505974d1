﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanCellMultiCoverageDlg : BaseDialog
    {
        public NRScanCellMultiCoverageDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(ScanMultiCoverageCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numAbsValue.Value = condition.AbsoluteValue;
            numBandValue.Value = condition.CoverBandDiff;
            numMaxValidValue.Value = condition.ValidValue;
            chkSaveTestPoint.Checked = condition.SaveTestPoint;
        }

        public ScanMultiCoverageCondition GetCondition()
        {
            ScanMultiCoverageCondition condition = new ScanMultiCoverageCondition();
            condition.AbsoluteValue = (int)numAbsValue.Value;
            condition.CoverBandDiff = (int)numBandValue.Value;
            condition.ValidValue = (int)numMaxValidValue.Value;
            condition.SaveTestPoint = chkSaveTestPoint.Checked;
            return condition;
        }

        /// <summary>
        /// 确认操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }
    }
}
