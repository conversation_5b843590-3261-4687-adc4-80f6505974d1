﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem;

namespace MasterCom.RAMS.ZTFunc
{
    public class ProblemAreaQuery : AreaKpiBaseQuery
    {
        ProblemCondition probCond;
        protected ArchiveCondition archiveCondition { get; set; }
        protected Dictionary<AreaBase, List<AreaBase>> rootLeafDic { get; set; }

        protected void initAreaTypeIDDic()
        {
            Dictionary<int, Dictionary<int, AreaBase>> areaTypeIDDic = new Dictionary<int, Dictionary<int, AreaBase>>();
            foreach (AreaBase root in rootLeafDic.Keys)
            {
                foreach (AreaBase area in rootLeafDic[root])
                {
                    Dictionary<int, AreaBase> leafDic;
                    if (!areaTypeIDDic.TryGetValue(area.AreaTypeID, out leafDic))
                    {
                        leafDic = new Dictionary<int, AreaBase>();
                        areaTypeIDDic[area.AreaTypeID] = leafDic;
                    }
                    leafDic[area.AreaID] = area;
                }
            }
            SetTypes(areaTypeIDDic);
        }

        protected override bool getConditionBeforeQuery()
        {
            archiveCondition = ArchiveSettingManager.GetInstance().Condition;
            rootLeafDic = archiveCondition.VillageCondition.RootLeafDic;

            if (rootLeafDic.Count == 0)
            {
                MessageBox.Show("尚未选择村庄，请设置基础配置....", "提醒");
                return false;
            }

            ProblemOptionDlg dlg = new ProblemOptionDlg();
            if (probCond == null && !ProblemCondition.LoadFromLocal(out probCond))
            {
                probCond = new ProblemCondition();
            }
            dlg.Condition = probCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            IsShowResultForm = true;
            probCond = dlg.Condition;
            probCond.Save();
            condition = archiveCondition.GetBaseConditionBackUp();
            initAreaTypeIDDic();
            areaDataGrpDic = new Dictionary<AreaBase, KPIDataGroup>();
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> expSet = new List<string>();
            expSet.Add(probCond.WeakCvrExpCM);
            expSet.Add(probCond.WeakCvrExpCT);
            expSet.Add(probCond.WeakCvrExpCU);
            expSet.Add(probCond.PoorQualExp);
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            foreach (int evtID in probCond.EventIDDic.Keys)
            {
                evtIDSvrIDDic[evtID] = null;
            }
            return getTriadIDIgnoreServiceType(expSet);
        }

        public override string Name
        {
            get { return "问题村庄分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31007, this.Name);
        }

        private Dictionary<AreaBase, KPIDataGroup> areaDataGrpDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef
         , KPIStatDataBase singleStatData)
        {
            int areaTypeID = package.Content.GetParamInt();
            int areaSubID = package.Content.GetParamInt();
            AreaBase area = getArea(areaTypeID, areaSubID);
            if (area == null)
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);

            KPIDataGroup areaDataGrp = null;
            if (!areaDataGrpDic.TryGetValue(area, out areaDataGrp))
            {
                areaDataGrp = new KPIDataGroup(area);
                areaDataGrpDic[area] = areaDataGrp;
            }
            areaDataGrp.AddStatData(fi, singleStatData, false);
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!probCond.EventIDDic.ContainsKey(evt.ID))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, false, null);
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            AreaBase area = getArea(evt.AreaTypeID, evt.AreaID);
            if (area == null)
            {
                return;
            }

            KPIDataGroup areaDataGrp = null;
            if (!areaDataGrpDic.TryGetValue(area, out areaDataGrp))
            {
                areaDataGrp = new KPIDataGroup(area);
                areaDataGrpDic[area] = areaDataGrp;
            }
            areaDataGrp.AddStatData(fi, eventData, false);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            MasterCom.Util.UiEx.WaitTextBox.Show("正在分析村庄指标...", makeSummary);
        }

        private List<ProblemArea> probAreas = null;
        protected virtual void makeSummary()
        {
            try
            {
                probAreas = new List<ProblemArea>();
                foreach (KPIDataGroup areaGrp in areaDataGrpDic.Values)
                {
                    areaGrp.FinalMtMoGroup();
                    ProblemArea prob = probCond.Evaluate(areaGrp);
                    if (prob!=null)
                    {
                        probAreas.Add(prob);
                    }
                }
                areaDataGrpDic.Clear();
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(1);
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

        protected override void fireShowResult()
        {
            if (probAreas == null || probAreas.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("问题村庄个数为0");
                return;
            }
            ProblemAreaListForm frm = MainModel.GetObjectFromBlackboard(typeof(ProblemAreaListForm)) as ProblemAreaListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ProblemAreaListForm();
                frm.Owner = MainModel.MainForm;
            }
            frm.FillData(probAreas, probCond);
            frm.Visible = true;
            frm.BringToFront();
            probAreas = null;
        }

    }

}
