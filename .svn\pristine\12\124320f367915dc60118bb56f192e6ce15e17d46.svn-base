﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class EndToEndByFile : EndToEndQuery
    {
        private static EndToEndByFile instance = null;
        public static new EndToEndByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EndToEndByFile();
                    }
                }
            }
            return instance;
        }

        public EndToEndByFile()
            : base()
        {

        }

        public override string Name
        {
            get
            {
                return "端对端问题分析(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }

    public class EndToEndByFile_FDD : EndToEndQuery_FDD
    {
        private static EndToEndByFile_FDD instance = null;
        public static new EndToEndByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EndToEndByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected EndToEndByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD端对端问题分析(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
