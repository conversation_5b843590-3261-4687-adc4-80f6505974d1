﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePlanningCellSettingForm : BaseForm
    {
        public LtePlanningCellSettingForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnReset.Click += BtnReset_Click;
            btnExcel.Click += BtnXls_Click;
            chkSampleLess.CheckedChanged += ChkSampleLess_CheckedChanged;
            chkWeakCoverage.CheckedChanged += ChkWeakCover_CheckedChanged;
            chkOverCoverage.CheckedChanged += ChkOverCover_CheckedChanged;
            chkWeakQuality.CheckedChanged += ChkWeakQual_CheckedChanged;
            BtnReset_Click(btnReset, new EventArgs());
        }

        public LtePlanningCellCondition GetCondition()
        {
            LtePlanningCellCondition cond = new LtePlanningCellCondition();
            cond.XlsFileName = txtExcel.Text;

            cond.OpenedTestEnable = chkNotOpened.Checked;

            cond.SampleLessEnable = chkSampleLess.Checked;
            cond.SampleLessCount = (int)numSampleLessCount.Value;

            cond.WeakCoverageEnable = chkWeakCoverage.Checked;
            cond.WeakCoverageRsrp = (double)numWeakCoverRsrp.Value;
            cond.WeakCoverageRate = (double)numWeakCoverRate.Value / 100;
            cond.WeakCoverageMinSampleCount = (int)numWeakCoverSampleCount.Value;
            cond.WeakCoverageMaxCellDistance = (double)numWeakCoverCellDistance.Value;

            cond.OverCoverageEnable = chkOverCoverage.Checked;
            cond.OverCoverageRadiusRatio = (double)numOverCoverRadiusRatio.Value;
            cond.OverCoverageRate = (double)numOverCoverRate.Value / 100;
            cond.OverCoverageMinSampleCount = (int)numOverCoverSampleCount.Value;
            cond.OverCoverageMaxCellDistance = (double)numOverCoverCellDistance.Value;

            cond.WeakQualityEnable = chkWeakQuality.Checked;
            cond.WeakCoverageRsrp = (double)numWeakQualRsrp.Value;
            cond.WeakQualitySinr = (double)numWeakQualSinr.Value;
            cond.WeakQualityRate = (double)numWeakQualRate.Value / 100;
            cond.WeakQualityMinSampleCount = (int)numWeakQualSampleCount.Value;
            cond.WeakQualityMaxCellDistance = (double)numWeakQualCellDistance.Value;

            return cond;
        }

        private void ChkWeakQual_CheckedChanged(object sender, EventArgs e)
        {
            numWeakQualRate.Enabled = chkWeakQuality.Checked;
            numWeakQualSinr.Enabled = chkWeakQuality.Checked;
            numWeakQualRsrp.Enabled = chkWeakQuality.Checked;
            numWeakQualSampleCount.Enabled = chkWeakQuality.Checked;
            numWeakQualCellDistance.Enabled = chkWeakQuality.Checked;
        }

        private void ChkOverCover_CheckedChanged(object sender, EventArgs e)
        {
            numOverCoverRadiusRatio.Enabled = chkOverCoverage.Checked;
            numOverCoverRate.Enabled = chkOverCoverage.Checked;
            numOverCoverRadiusRatio.Enabled = chkOverCoverage.Checked;
            numOverCoverSampleCount.Enabled = chkOverCoverage.Checked;
            numOverCoverCellDistance.Enabled = chkOverCoverage.Checked;
        }

        private void ChkWeakCover_CheckedChanged(object sender, EventArgs e)
        {
            numWeakCoverRsrp.Enabled = chkWeakCoverage.Checked;
            numWeakCoverRate.Enabled = chkWeakCoverage.Checked;
            numWeakCoverSampleCount.Enabled = chkWeakCoverage.Checked;
            numWeakCoverCellDistance.Enabled = chkWeakCoverage.Checked;
        }

        private void ChkSampleLess_CheckedChanged(object sender, EventArgs e)
        {
            numSampleLessCount.Enabled = chkSampleLess.Checked;
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            LtePlanningCellCondition cond = new LtePlanningCellCondition();

            chkNotOpened.Checked = cond.OpenedTestEnable;

            chkSampleLess.Checked = cond.SampleLessEnable;
            numSampleLessCount.Value = cond.SampleLessCount;

            chkWeakCoverage.Checked = cond.WeakCoverageEnable;
            numWeakCoverRsrp.Value = (decimal)cond.WeakCoverageRsrp;
            numWeakCoverRate.Value = (decimal)cond.WeakCoverageRate * 100;
            numWeakCoverSampleCount.Value = (decimal)cond.WeakCoverageMinSampleCount;
            numWeakCoverCellDistance.Value = (decimal)cond.WeakCoverageMaxCellDistance;

            chkOverCoverage.Checked = cond.OverCoverageEnable;
            numOverCoverRadiusRatio.Value = (decimal)cond.OverCoverageRadiusRatio;
            numOverCoverRate.Value = (decimal)cond.OverCoverageRate * 100;
            numOverCoverSampleCount.Value = (decimal)cond.OverCoverageMinSampleCount;
            numOverCoverCellDistance.Value = (decimal)cond.OverCoverageMaxCellDistance;

            chkWeakQuality.Checked = cond.WeakQualityEnable;
            numWeakQualRsrp.Value = (decimal)cond.WeakCoverageRsrp;
            numWeakQualSinr.Value = (decimal)cond.WeakQualitySinr;
            numWeakQualRate.Value = (decimal)cond.WeakQualityRate * 100;
            numWeakQualSampleCount.Value = (decimal)cond.WeakQualityMinSampleCount;
            numWeakQualCellDistance.Value = (decimal)cond.WeakQualityMaxCellDistance;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtExcel.Text))
            {
                MessageBox.Show("请选择Excel文件", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnXls_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtExcel.Text = dlg.FileName;
        }
    }
}
