﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.NOP
{
    public class TaskFileCompareReplay : DIYReplayFileQuery
    {
        readonly FileInfo refFile = null;
        readonly TimePeriod refPeriod;
        FileInfo checkFile = null;
        readonly string checkFileName;
        public TaskFileCompareReplay(FileInfo refFile, TimePeriod refPeriod, string checkFileName)
            : base(MainModel.GetInstance())
        {
            this.refFile = refFile;
            this.refPeriod = refPeriod;
            this.checkFileName = checkFileName;
            condition = new QueryCondition();
            condition.DistrictID = refFile.DistrictID;
            condition.DistrictIDs.Add(refFile.DistrictID);
            condition.FileInfos.Add(refFile);
        }

        protected override bool isValidCondition()
        {
            WaitTextBox.Show(MainModel.MainForm, "正在查询待验证文件...", queryCheckFileInfo);
            if (checkFile == null)
            {
                MessageBox.Show(MainModel.MainForm, "未能查询到待验证文件");
                return false;
            }
            return true;
        }
        private void addColumnDefItem(DIYReplayContentOption option, string defItemShowName)
        {
            ColumnDefItem defItem = option.SampleColumns.Find(delegate (ColumnDefItem x) { return x.showName == defItemShowName; });
            if (defItem == null)
            {
                List<ColumnDefItem> defItemList = InterfaceManager.GetInstance().GetColumnDefByShowName(defItemShowName);
                if (defItemList != null && defItemList.Count > 0)
                {
                    option.SampleColumns.Add(defItemList[0]);
                }
            }
        }
        protected override Model.Interface.DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = base.getDIYReplayContent();
            if (option != null)
            {
                addColumnDefItem(option, "lte_APP_Speed");
                addColumnDefItem(option, "lte_APP_type");
                addColumnDefItem(option, "lte_PDCP_DL");
                addColumnDefItem(option, "lte_PDCP_UL");
                addColumnDefItem(option, "lte_RSRP");
                addColumnDefItem(option, "lte_SINR");
                addColumnDefItem(option, "lte_gsm_DM_RxLevSub");
                addColumnDefItem(option, "lte_gsm_DM_RxQualSub");
                addColumnDefItem(option, "lte_gsm_SC_LAC");
                addColumnDefItem(option, "lte_gsm_SC_CI");
                addColumnDefItem(option, "lte_PESQMos");
                addColumnDefItem(option, "lte_POLQA_Score_SWB");
            }
            return option;
        }

        private void queryCheckFileInfo()
        {
            QueryCondition cond = new QueryCondition();
            cond.FileName = checkFileName;
            cond.FileNameOrNum = 1;
            TimePeriod period = new TimePeriod(refPeriod.EndTime, DateTime.Now);
            cond.Periods.Add(period);
            cond.DistrictIDs.Add(refFile.DistrictID);
            DIYQueryFileInfo queryCheckFile = new DIYQueryFileInfo(MainModel);
            queryCheckFile.IsShowFileInfoForm = false;
            queryCheckFile.SetQueryCondition(cond);
            queryCheckFile.Query();
            if (MainModel.FileInfos.Count > 0)
            {
                checkFile = MainModel.FileInfos[MainModel.FileInfos.Count - 1];
                DateTime date = DateTime.Parse(checkFile.BeginTimeString);
                List<FileInfo> fs = new List<FileInfo>();
                fs.Add(checkFile);
                foreach (FileInfo f in MainModel.FileInfos)
                {
                    if (f != checkFile)
                    {
                        DateTime dateTemp = DateTime.Parse(f.BeginTimeString);
                        if (dateTemp.Date == date.Date)
                        {
                            fs.Add(f);
                        }
                    }
                }
                condition.FileInfos.AddRange(fs);
            }
            System.Threading.Thread.Sleep(100);
            WaitTextBox.Close();
        }

        protected override void prepareStatPackage_Sample_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_SAMPLE;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);

            if (fileInfo == refFile)
            {
                package.Content.AddParam((byte)OpOptionDef.iTimeSelect);
                package.Content.AddParam((int)(JavaDate.GetMilliseconds(refPeriod.BeginTime) / 1000));
                package.Content.AddParam((int)(JavaDate.GetMilliseconds(refPeriod.EndTime) / 1000));
            }
            AddDIYEndOpFlag(package);
        }

        protected override void prepareStatPackage_Event_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_EVENT;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);

            if (fileInfo == refFile)
            {
                package.Content.AddParam((byte)OpOptionDef.iTimeSelect);
                package.Content.AddParam((int)(JavaDate.GetMilliseconds(refPeriod.BeginTime) / 1000));
                package.Content.AddParam((int)(JavaDate.GetMilliseconds(refPeriod.EndTime) / 1000));
            }
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Message_FileFilter(Package package, FileInfo fileInfo)
        {
            base.prepareStatPackage_Message_FileFilter(package, fileInfo);

            if (fileInfo == refFile)
            {
                package.Content.AddParam((byte)OpOptionDef.iTimeSelect);
                package.Content.AddParam((int)(JavaDate.GetMilliseconds(refPeriod.BeginTime) / 1000));
                package.Content.AddParam((int)(JavaDate.GetMilliseconds(refPeriod.EndTime) / 1000));
            }
            AddDIYEndOpFlag(package);
        }

        protected override void prepareStatPackage_Sample_SampleFilter(Package package)
        {
            //
        }

        protected override void prepareStatPackage_Event_EventFilter(Package package)
        {
            //
        }

        protected override void prepareStatPackage_Message_MessageFilter(Package package)
        {
            //
        }




    }
}
