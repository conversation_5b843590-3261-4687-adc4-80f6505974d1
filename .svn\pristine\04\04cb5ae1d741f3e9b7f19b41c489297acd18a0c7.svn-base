﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTEHandOverAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLTEHandOverAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewHandOverAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDownLostNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSrcPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSrcEarfcn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSrcFreqBand = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeRsrp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeSinr = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeAppSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforePdcpSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeRsrq = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeforeRssi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDestPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDestEarfcn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDestFreqBand = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDestDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHandOverType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterRsrp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterSrcRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterSinr = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterAppSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterPdcpSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterRsrq = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAfterRssi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSameSite = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewHandOverAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewHandOverAna
            // 
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnGridName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnTime);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDownLostNum);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellTAC);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellECI);
            this.ListViewHandOverAna.AllColumns.Add(this.colSrcPCI);
            this.ListViewHandOverAna.AllColumns.Add(this.colSrcEarfcn);
            this.ListViewHandOverAna.AllColumns.Add(this.colSrcFreqBand);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellID);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnCellDistance);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeSample);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeRsrp);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeSinr);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeAppSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforePdcpSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeRsrq);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnBeforeRssi);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestCellName);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestTAC);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestECI);
            this.ListViewHandOverAna.AllColumns.Add(this.colDestPCI);
            this.ListViewHandOverAna.AllColumns.Add(this.colDestEarfcn);
            this.ListViewHandOverAna.AllColumns.Add(this.colDestFreqBand);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestCellID);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnDestDistance);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterSample);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnHandOverType);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterRsrp);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterSrcRSRP);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterSinr);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterAppSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterPdcpSpeed);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterRsrq);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnAfterRssi);
            this.ListViewHandOverAna.AllColumns.Add(this.olvColumnSameSite);
            this.ListViewHandOverAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewHandOverAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnGridName,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellName,
            this.olvColumnCellTAC,
            this.olvColumnCellECI,
            this.colSrcPCI,
            this.colSrcEarfcn,
            this.colSrcFreqBand,
            this.olvColumnCellID,
            this.olvColumnCellDistance,
            this.olvColumnBeforeSample,
            this.olvColumnBeforeRsrp,
            this.olvColumnBeforeSinr,
            this.olvColumnBeforeAppSpeed,
            this.olvColumnBeforeRsrq,
            this.olvColumnBeforeRssi,
            this.olvColumnDestCellName,
            this.olvColumnDestTAC,
            this.olvColumnDestECI,
            this.colDestPCI,
            this.colDestEarfcn,
            this.colDestFreqBand,
            this.olvColumnDestCellID,
            this.olvColumnDestDistance,
            this.olvColumnAfterSample,
            this.olvColumnHandOverType,
            this.olvColumnAfterRsrp,
            this.olvColumnAfterSrcRSRP,
            this.olvColumnAfterSinr,
            this.olvColumnAfterAppSpeed,
            this.olvColumnAfterRsrq,
            this.olvColumnAfterRssi,
            this.olvColumnSameSite});
            this.ListViewHandOverAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewHandOverAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewHandOverAna.FullRowSelect = true;
            this.ListViewHandOverAna.GridLines = true;
            this.ListViewHandOverAna.HeaderWordWrap = true;
            this.ListViewHandOverAna.HideSelection = false;
            this.ListViewHandOverAna.IsNeedShowOverlay = false;
            this.ListViewHandOverAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewHandOverAna.Name = "ListViewHandOverAna";
            this.ListViewHandOverAna.OwnerDraw = true;
            this.ListViewHandOverAna.ShowGroups = false;
            this.ListViewHandOverAna.Size = new System.Drawing.Size(1352, 501);
            this.ListViewHandOverAna.TabIndex = 7;
            this.ListViewHandOverAna.UseCompatibleStateImageBehavior = false;
            this.ListViewHandOverAna.View = System.Windows.Forms.View.Details;
            this.ListViewHandOverAna.VirtualMode = true;
            this.ListViewHandOverAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewHandOverAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "网格名称";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 80;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnDownLostNum
            // 
            this.olvColumnDownLostNum.DisplayIndex = 8;
            this.olvColumnDownLostNum.HeaderFont = null;
            this.olvColumnDownLostNum.IsVisible = false;
            this.olvColumnDownLostNum.Text = "下行切换过程中RTP丢包数（handsettime）";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "源小区";
            // 
            // olvColumnCellTAC
            // 
            this.olvColumnCellTAC.HeaderFont = null;
            this.olvColumnCellTAC.Text = "源TAC";
            // 
            // olvColumnCellECI
            // 
            this.olvColumnCellECI.HeaderFont = null;
            this.olvColumnCellECI.Text = "源ECI";
            // 
            // colSrcPCI
            // 
            this.colSrcPCI.HeaderFont = null;
            this.colSrcPCI.Text = "源PCI";
            // 
            // colSrcEarfcn
            // 
            this.colSrcEarfcn.HeaderFont = null;
            this.colSrcEarfcn.Text = "源EARFCN";
            // 
            // colSrcFreqBand
            // 
            this.colSrcFreqBand.HeaderFont = null;
            this.colSrcFreqBand.Text = "源频段";
            // 
            // olvColumnCellID
            // 
            this.olvColumnCellID.HeaderFont = null;
            this.olvColumnCellID.Text = "源CellID";
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "源小区与事件距离";
            // 
            // olvColumnBeforeSample
            // 
            this.olvColumnBeforeSample.HeaderFont = null;
            this.olvColumnBeforeSample.Text = "前采样点数";
            // 
            // olvColumnBeforeRsrp
            // 
            this.olvColumnBeforeRsrp.HeaderFont = null;
            this.olvColumnBeforeRsrp.Text = "前RSRP";
            // 
            // olvColumnBeforeSinr
            // 
            this.olvColumnBeforeSinr.HeaderFont = null;
            this.olvColumnBeforeSinr.Text = "前SINR";
            // 
            // olvColumnBeforeAppSpeed
            // 
            this.olvColumnBeforeAppSpeed.HeaderFont = null;
            this.olvColumnBeforeAppSpeed.Text = "前APP速率";
            // 
            // olvColumnBeforePdcpSpeed
            // 
            this.olvColumnBeforePdcpSpeed.DisplayIndex = 14;
            this.olvColumnBeforePdcpSpeed.HeaderFont = null;
            this.olvColumnBeforePdcpSpeed.IsVisible = false;
            this.olvColumnBeforePdcpSpeed.Text = "前PDCP速率";
            // 
            // olvColumnBeforeRsrq
            // 
            this.olvColumnBeforeRsrq.HeaderFont = null;
            this.olvColumnBeforeRsrq.Text = "前RSRQ";
            // 
            // olvColumnBeforeRssi
            // 
            this.olvColumnBeforeRssi.HeaderFont = null;
            this.olvColumnBeforeRssi.Text = "前RSSI";
            // 
            // olvColumnDestCellName
            // 
            this.olvColumnDestCellName.HeaderFont = null;
            this.olvColumnDestCellName.Text = "目的小区";
            // 
            // olvColumnDestTAC
            // 
            this.olvColumnDestTAC.HeaderFont = null;
            this.olvColumnDestTAC.Text = "目的TAC";
            // 
            // olvColumnDestECI
            // 
            this.olvColumnDestECI.HeaderFont = null;
            this.olvColumnDestECI.Text = "目的ECI";
            // 
            // colDestPCI
            // 
            this.colDestPCI.HeaderFont = null;
            this.colDestPCI.Text = "目的PCI";
            // 
            // colDestEarfcn
            // 
            this.colDestEarfcn.HeaderFont = null;
            this.colDestEarfcn.Text = "目的EARFCN";
            // 
            // colDestFreqBand
            // 
            this.colDestFreqBand.HeaderFont = null;
            this.colDestFreqBand.Text = "目的频段";
            // 
            // olvColumnDestCellID
            // 
            this.olvColumnDestCellID.HeaderFont = null;
            this.olvColumnDestCellID.Text = "目的CellID";
            // 
            // olvColumnDestDistance
            // 
            this.olvColumnDestDistance.HeaderFont = null;
            this.olvColumnDestDistance.Text = "目的小区与事件距离";
            // 
            // olvColumnAfterSample
            // 
            this.olvColumnAfterSample.HeaderFont = null;
            this.olvColumnAfterSample.Text = "后采样点数";
            // 
            // olvColumnHandOverType
            // 
            this.olvColumnHandOverType.HeaderFont = null;
            this.olvColumnHandOverType.Text = "切换类型";
            // 
            // olvColumnAfterRsrp
            // 
            this.olvColumnAfterRsrp.HeaderFont = null;
            this.olvColumnAfterRsrp.Text = "后RSRP";
            // 
            // olvColumnAfterSrcRSRP
            // 
            this.olvColumnAfterSrcRSRP.HeaderFont = null;
            this.olvColumnAfterSrcRSRP.Text = "后源小区RSRP";
            // 
            // olvColumnAfterSinr
            // 
            this.olvColumnAfterSinr.HeaderFont = null;
            this.olvColumnAfterSinr.Text = "后SINR";
            // 
            // olvColumnAfterAppSpeed
            // 
            this.olvColumnAfterAppSpeed.HeaderFont = null;
            this.olvColumnAfterAppSpeed.Text = "后APP速率";
            // 
            // olvColumnAfterPdcpSpeed
            // 
            this.olvColumnAfterPdcpSpeed.DisplayIndex = 23;
            this.olvColumnAfterPdcpSpeed.HeaderFont = null;
            this.olvColumnAfterPdcpSpeed.IsVisible = false;
            this.olvColumnAfterPdcpSpeed.Text = "后PDCP速率";
            // 
            // olvColumnAfterRsrq
            // 
            this.olvColumnAfterRsrq.HeaderFont = null;
            this.olvColumnAfterRsrq.Text = "后RSRQ";
            // 
            // olvColumnAfterRssi
            // 
            this.olvColumnAfterRssi.HeaderFont = null;
            this.olvColumnAfterRssi.Text = "后RSSI";
            // 
            // olvColumnSameSite
            // 
            this.olvColumnSameSite.HeaderFont = null;
            this.olvColumnSameSite.Text = "同站";
            // 
            // ZTLTEHandOverAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1354, 502);
            this.Controls.Add(this.ListViewHandOverAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLTEHandOverAnaListForm";
            this.Text = "切换前后指标分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewHandOverAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewHandOverAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRsrp;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeSinr;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeAppSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnDestTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnDestECI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnDestDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforePdcpSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRsrp;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterSinr;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterAppSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterPdcpSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnSameSite;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRsrq;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeRssi;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRsrq;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterRssi;
        private BrightIdeasSoftware.OLVColumn colSrcPCI;
        private BrightIdeasSoftware.OLVColumn colDestPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeSample;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterSample;
        private BrightIdeasSoftware.OLVColumn olvColumnHandOverType;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterSrcRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnDownLostNum;
        private BrightIdeasSoftware.OLVColumn colSrcEarfcn;
        private BrightIdeasSoftware.OLVColumn colSrcFreqBand;
        private BrightIdeasSoftware.OLVColumn colDestEarfcn;
        private BrightIdeasSoftware.OLVColumn colDestFreqBand;
    }
}