﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;



namespace MasterCom.RAMS.ZTFunc
{
    public partial class AcceptHistoryResultForm : MinCloseForm
    {
        public AcceptHistoryResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;

            ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
            ToolStripMenuItem itemExportToExcel = new ToolStripMenuItem("导出到Excel");
            itemExportToExcel.Click += itemExportToExcel_Click;
            contextMenuStrip.Items.Add(itemExportToExcel);
            this.gcHistory.ContextMenuStrip = contextMenuStrip;
        }

        void itemExportToExcel_Click(object sender, EventArgs e)
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "Excel|*.xls";
            if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            string fileFullName = sfd.FileName;
            string sheetName = "Sheet1";
            List<AcceptHistorySite> listDataSet = this.gcHistory.DataSource as List<AcceptHistorySite>;
            List<NPOIRow> listResult = new List<NPOIRow>();
            NPOIRow rowTitle = new NPOIRow();
            List<string> listTitle = new List<string>{
            #region title
                "审核日期","所属地州","基站名称","基站类型","EnodeBid","是否通过（基站）","小区","是否通过（小区）",
                "下载SINR（好点）", "下载SINR（中点）","下载SINR（差点）",
                "下载RSRP(好点）","下载RSRP(中点）","下载RSRP(差点）",
                "上传SINR（好点）","上传SINR（中点）","上传SINR（差点）",
                "上传RSRP（好点）","上传RSRP（中点）","上传RSRP（差点）",
                "下载速率（好点）","下载速率（中点）","下载速率（差点）",
                "上传速率（好点）","上传速率（中点）","上传速率（差点）",
                "RRC连接请求次数","RRC连接成功次数","RRC连接成功率",
                "ERAB连接请求次数","ERAB连接成功次数","ERAB连接成功率",
                "RRC连接成功率*ERAB连接成功率",
                "Access尝试次数","Access成功次数","Access成功率",
                "CSFB尝试次数","CSFB成功次数","CSFB成功率",
                @"3\4G互操作尝试次数",@"3\4G互操作成功次数",@"3\4G互操作成功率",
                "系统内切换尝试次数","系统内切换成功次数","系统内切换成功率"};
#endregion
            foreach(string title in listTitle)
            {
                rowTitle.cellValues.Add(title);
            }
            listResult.Add(rowTitle);
            //获取内容
            foreach (AcceptHistorySite item in listDataSet)
            {
                foreach (AcceptHistoryCell itemCell in item.HistoryCells)
                { 
                    NPOIRow dataRow = new NPOIRow();
                    #region get value
                    dataRow.cellValues.Add(item.DateTimeStr);//审核日期
                    dataRow.cellValues.Add(item.CityName);//所属地州
                    dataRow.cellValues.Add(item.SiteName);//基站名称
                    dataRow.cellValues.Add(item.SiteTypeName);//基站类型
                    dataRow.cellValues.Add(item.EnodebID);//EnodeBid
                    dataRow.cellValues.Add(item.IsPass);//是否通过（基站）
                    dataRow.cellValues.Add(itemCell.CellStr);//小区
                    dataRow.cellValues.Add(itemCell.IsPass);//是否通过（小区）
                    dataRow.cellValues.Add(itemCell.DownSinrGood);//下载SINR（好点）
                    dataRow.cellValues.Add(itemCell.DownSinrTall);//下载SINR（中点）
                    dataRow.cellValues.Add(itemCell.DownSinrBad);//下载SINR（差点）
                    dataRow.cellValues.Add(itemCell.DownRsrpGood);//下载RSRP(好点）
                    dataRow.cellValues.Add(itemCell.DownRsrpTall);//下载RSRP(中点）
                    dataRow.cellValues.Add(itemCell.DownRsrpBad);//下载RSRP(差点）
                    dataRow.cellValues.Add(itemCell.UpSinrGood);//上传SINR（好点）
                    dataRow.cellValues.Add(itemCell.UpSinrTall);//上传SINR（中点）
                    dataRow.cellValues.Add(itemCell.UpSinrBad);//上传SINR（差点）
                    dataRow.cellValues.Add(itemCell.UpRsrpGood);//上传RSRP（好点）
                    dataRow.cellValues.Add(itemCell.UpRsrpTall);//上传RSRP（中点）
                    dataRow.cellValues.Add(itemCell.UpRsrpBad);//上传RSRP（差点）
                    dataRow.cellValues.Add(itemCell.DownSpeedGood);//下载速率（好点）
                    dataRow.cellValues.Add(itemCell.DownSpeedTall);//下载速率（中点）
                    dataRow.cellValues.Add(itemCell.DownSpeedBad);//下载速率（差点）
                    dataRow.cellValues.Add(itemCell.UpSpeedGood);//上传速率（好点）
                    dataRow.cellValues.Add(itemCell.UpSpeedTall);//上传速率（中点）
                    dataRow.cellValues.Add(itemCell.UpSpeedBad);//上传速率（差点）
                    dataRow.cellValues.Add(itemCell.RrcRequestCnt);//RRC连接请求次数
                    dataRow.cellValues.Add(itemCell.RrcSuccessCnt);//RRC连接成功次数
                    dataRow.cellValues.Add(this.getRateValue(itemCell.RrcSuccessRate));//RRC连接成功率
                    dataRow.cellValues.Add(itemCell.ErabRequestCnt);//ERAB连接请求次数
                    dataRow.cellValues.Add(itemCell.ErabSuccessCnt);//ERAB连接成功次数
                    dataRow.cellValues.Add(this.getRateValue(itemCell.ErabSuccessRate));//ERAB连接成功率
                    dataRow.cellValues.Add(this.getRateValue(itemCell.RrcSuccessRate * itemCell.ErabSuccessRate));//RRC连接成功率*ERAB连接成功率
                    dataRow.cellValues.Add(itemCell.AccessRequestCnt);
                    dataRow.cellValues.Add(itemCell.AccessSuccessCnt);
                    dataRow.cellValues.Add(this.getRateValue(itemCell.AccessSuccessRate));
                    dataRow.cellValues.Add(itemCell.CsfbRequestCnt);//CSFB尝试次数
                    dataRow.cellValues.Add(itemCell.CsfbSuccessCnt);//CSFB成功次数
                    dataRow.cellValues.Add(this.getRateValue(itemCell.CsfbSuccessRate));//CSFB成功率
                    dataRow.cellValues.Add(itemCell.HandoverRequestCnt);//3\4G互操作尝试次数
                    dataRow.cellValues.Add(itemCell.HandoverSuccessCnt);//3\4G互操作成功次数
                    dataRow.cellValues.Add(this.getRateValue(itemCell.HandoverSuccessRate));//3\4G互操作成功率
                    dataRow.cellValues.Add(itemCell.ReselectRequestCnt);//系统内切换尝试次数
                    dataRow.cellValues.Add(itemCell.ReselectSuccessCnt);//系统内切换成功次数
                    dataRow.cellValues.Add(this.getRateValue(itemCell.ReselectSuccessRate));//系统内切换成功率
                    #endregion
                    listResult.Add(dataRow);
                }
            }
            ExcelNPOIManager.ExportToExcel(listResult, fileFullName, sheetName);
            MessageBox.Show("导出到Excel完毕!");
        }

        private string getRateValue(double? value)
        {
            if (value == null) return null;
            return Math.Round((double)value * 100.0, 2).ToString() + "%";
        }

        public void FillData(object result)
        {
            this.gcHistory.DataSource = result;
        }
    }

}
