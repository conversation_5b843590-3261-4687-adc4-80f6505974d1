﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryTDScoreData : QueryBase   
    {
        public QueryTDScoreData(MainModel mainModel)
            : base(mainModel)
        { }
        public string SelectName { get; set; }
        public string SelectTable { get; set; }
        public DateTime SelectTime { get; set; }
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "T网评分表"; }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        /// <summary>
        /// 判断是否选择区域
        /// </summary>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return searchGeometrys.IsValidRegion;
        }
        
        public List<TDScore> TDScoreList { get; set; } = new List<TDScore>();

        /// <summary>
        /// 查询接口
        /// </summary>
        protected override void query()
        {
            TDScoreFrm tdFrm = new TDScoreFrm(MainModel);
            SelectName = "";
            SelectTable = "";
            SelectTime = new DateTime();

            TDScoreList.Clear();

            WaitBox.Text = "准备查询...";
            WaitBox.CanCancel = true;
            WaitBox.Show("开始接收统计数据...", queryStatData);
            tdFrm.Source.DataSource = TDScoreList;
            if (tdFrm.ShowDialog() != DialogResult.OK)
            {
                //
            }
        }

        /// <summary>
        /// 查询统计数据
        /// </summary>
        protected void queryStatData()
        {
            try
            {
                this.TDScoreList.Clear();
                TDScoreQuery tdQuery = new TDScoreQuery(MainModel);
                tdQuery.Query();
                List<TDScore> tdScoreList = tdQuery.TdScoreList;

                TDScoreQueryIndicators tdQureyIndicators = new TDScoreQueryIndicators(MainModel);
                tdQureyIndicators.Query();
                List<TDScore> indicatorsList = tdQureyIndicators.TDScoreList;

                foreach (TDScore tdScoreindicators in indicatorsList)
                {
                    foreach (TDScore tdScore in tdScoreList)
                    {
                        if (tdScore.Strproject == tdScoreindicators.Strproject)
                        {
                            tdScore.Ftrueindicators = tdScoreindicators.Ftrueindicators;
                        }
                    }
                }
                this.TDScoreList.AddRange(tdScoreList);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
               
        }
    }
}
