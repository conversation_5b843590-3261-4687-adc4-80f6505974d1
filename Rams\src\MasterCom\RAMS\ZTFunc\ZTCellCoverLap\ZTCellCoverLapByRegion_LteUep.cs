﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_LteUep : ZTCellCoverLapByRegion_LTE
   {
        private static ZTCellCoverLapByRegion_LteUep instance = null;
        public new static ZTCellCoverLapByRegion_LteUep GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverLapByRegion_LteUep();
                    }
                }
            }
            return instance;
        }

        protected ZTCellCoverLapByRegion_LteUep()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "过覆盖分析_LTE_UEP"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 24000, 24002, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            leakGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_uep_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_uep_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_uep_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_uep_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_uep_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }

            return leakGroup;
        }

        protected override void getResultAfterQuery()
        {
            curSelDIYSampleGroup.ThemeName = "LTE_UEP_RSRP";
            FilterCellCoverLap();
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_uep_RSRP"];
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
                //return new CoverLapProperties_TD(this);
            }
        }
        #endregion
    }
}
