﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.Util;
using System.Collections;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTMainCellLastOccupyInfoForm : BaseFormStyle
    {
        private ParamName baseParamName;
        private List<BrightIdeasSoftware.OLVColumn> tempCols = new List<BrightIdeasSoftware.OLVColumn>();

        public ZTMainCellLastOccupyInfoForm()
        {
            InitializeComponent();
            init();
            baseParamName = new ParamName("RxLevSub", "RxQualSub");
        }

        private void init()
        {
            this.olvColumnStatSN.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyFileInfo)
                {
                    CellOccupyFileInfo file = row as CellOccupyFileInfo;
                    return file.Sn;
                }
                else if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.Sn;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyFileInfo)
                {
                    CellOccupyFileInfo file = row as CellOccupyFileInfo;
                    return file.FileName;
                }
                return "";
            };

            this.treeViewCellOccupy.CanExpandGetter = delegate (object row)
            {
                return row is CellOccupyFileInfo;
            };

            this.treeViewCellOccupy.ChildrenGetter = delegate (object row)
            {
                if (row is CellOccupyFileInfo)
                {
                    CellOccupyFileInfo file = row as CellOccupyFileInfo;
                    return file.CellInfoList;
                }
                else
                {
                    return new ArrayList();
                }
            };

            setCellOccupyCellInfo();
        }

        private void setCellOccupyCellInfo()
        {
            this.olvColumnEventName.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.StrEventName;
                }
                return "";
            };

            this.olvColumnStatCellName.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.CellName;
                }
                return "";
            };

            this.olvColumnLACSrc.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.LACSrc;
                }
                return "";
            };

            this.olvColumnCISrc.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.CISrc;
                }
                return "";
            };

            this.olvColumnLACTar.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.LACTar;
                }
                return "";
            };

            this.olvColumnCITar.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.CITar;
                }
                return "";
            };

            this.olvColumnDuration.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.Duration;
                }
                return "";
            };

            this.olvColumnLongitude.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.Longitude;
                }
                return "";
            };

            this.olvColumnLatitude.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.Latitude;
                }
                return "";
            };

            this.olvColumnTime.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.Time;
                }
                return "";
            };

            setCellTPInfo();
        }

        private void setCellTPInfo()
        {
            this.olvColumnAvgRSRP.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    if (cell.AvgRSRP == 0)
                    {
                        return "";
                    }
                    return cell.AvgRSRP;
                }
                return "";
            };

            this.olvColumnAvgSINR.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    if (cell.AvgSINR == 0)
                    {
                        return "";
                    }
                    return cell.AvgSINR;
                }
                return "";
            };

            this.olvColumnRSRPNum.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.SmallRSRPNum;
                }
                return "";
            };

            this.olvColumnSINRNum.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.SmallSINRNum;
                }
                return "";
            };

            this.olvColumnTestPointNum.AspectGetter = delegate (object row)
            {
                if (row is CellOccupyCellInfo)
                {
                    CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                    return cell.TestPntList.Count;
                }
                return "";
            };
        }

        public void FillData(List<CellOccupyFileInfo> occupyFileList, ParamName baseParamName)
        {
            this.baseParamName = baseParamName;
            this.treeViewCellOccupy.SetObjects(occupyFileList);
        }

        private void toolStripButtonSection_Click(object sender, EventArgs e)
        {
            RxlevRxqualSectionDlg dlg = new RxlevRxqualSectionDlg(baseParamName.RangeSetRxlev, baseParamName.RangeSetRxqual);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                baseParamName.RangeSetRxlev = dlg.RangeSetRxlev;
                baseParamName.RangeSetRxqual = dlg.RangeSetRxqual;
            }
            refreshColumns();
        }

        private void clearCols()
        {
            foreach (BrightIdeasSoftware.OLVColumn col in tempCols)
            {
                treeViewCellOccupy.Columns.Remove(col);
                treeViewCellOccupy.AllColumns.Remove(col);
            }
            tempCols.Clear();
        }

        Dictionary<string, Range> nameRangeDic = new Dictionary<string, Range>();
        private void refreshColumns()
        {
            clearCols();
            nameRangeDic.Clear();
            treeViewCellOccupy.BeginUpdate();
            foreach (Range rge in baseParamName.RangeSetRxlev.Values)
            {
                string title = string.Format("场强{0}", rge.ToString());
                if (!nameRangeDic.ContainsKey(title))
                    nameRangeDic.Add(title, rge);
                BrightIdeasSoftware.OLVColumn col = new BrightIdeasSoftware.OLVColumn(title, "");
                col.AspectGetter = row => {
                    if (row is CellOccupyCellInfo)
                    {
                        CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                        return cell.GetResult(nameRangeDic[col.Text], baseParamName.RxlevName);
                    }
                    return "";
                };
                tempCols.Add(col);
                treeViewCellOccupy.AllColumns.Add(col);
                treeViewCellOccupy.Columns.Add(col);
            }

            foreach (Range rge in baseParamName.RangeSetRxqual.Values)
            {
                string title = string.Format("质量{0}", rge.ToString());
                if (!nameRangeDic.ContainsKey(title))
                {
                    nameRangeDic.Add(title, rge);
                }
                BrightIdeasSoftware.OLVColumn col = new BrightIdeasSoftware.OLVColumn(title, "");
                col.AspectGetter = row => {
                    if (row is CellOccupyCellInfo)
                    {
                        CellOccupyCellInfo cell = row as CellOccupyCellInfo;
                        return cell.GetResult(nameRangeDic[col.Text], baseParamName.RxqualName);
                    }
                    return "";
                };
                tempCols.Add(col);
                treeViewCellOccupy.AllColumns.Add(col);
                treeViewCellOccupy.Columns.Add(col);
            }
            treeViewCellOccupy.EndUpdate();
        }

        private void ToolStripMenuItemExpand_Click(object sender, EventArgs e)
        {
            this.treeViewCellOccupy.ExpandAll();
        }

        private void ToolStripMenuItemCollapse_Click(object sender, EventArgs e)
        {
            this.treeViewCellOccupy.CollapseAll();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            this.treeViewCellOccupy.ExpandAll();
            try
            {
                ExcelNPOIManager.ExportToExcel(treeViewCellOccupy);
            }
            catch
            {
                MessageBox.Show("导出到xls失败");
            }
        }

        private void treeViewCellOccupy_DoubleClick(object sender, EventArgs e)
        {
            object obj = treeViewCellOccupy.GetSelectedObject();
            if (obj == null) return;
            if (obj is CellOccupyFileInfo)
            {
                CellOccupyFileInfo file = obj as CellOccupyFileInfo;

                mainModel.DTDataManager.Clear();
                foreach (CellOccupyCellInfo cell in file.CellInfoList)
                {
                    foreach (MasterCom.RAMS.Model.TestPoint tp in cell.TestPntList)
                    {
                        mainModel.DTDataManager.Add(tp);
                    }
                }
                refreshDTLayer();
            }
            else if (obj is CellOccupyCellInfo)
            {
                CellOccupyCellInfo cell = obj as CellOccupyCellInfo;

                mainModel.DTDataManager.Clear();
                foreach (MasterCom.RAMS.Model.TestPoint tp in cell.TestPntList)
                {
                    mainModel.DTDataManager.Add(tp);
                }
                refreshDTLayer();
            }
        }

        private void refreshDTLayer()
        {
            MainModel.FireDTDataChanged(this);
            foreach (MasterCom.RAMS.Func.MapSerialInfo serial in mainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (baseParamName.RxlevName.Contains(serial.Name))
                {
                    serial.Visible = true;
                }
            }
            mainModel.DrawFlyLines = true;
            mainModel.MainForm.GetMapForm().GetDTLayer().Invalidate();
        }

        private void ToolStripMenuItemReplay_Click(object sender, EventArgs e)
        {
            object obj = treeViewCellOccupy.GetSelectedObject();
            if (obj == null) return;
            if (obj is CellOccupyFileInfo)
            {
                CellOccupyFileInfo file = obj as CellOccupyFileInfo;

                DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                QueryCondition codition = new QueryCondition();
                codition.DistrictID = MainModel.DistrictID;
                codition.FileInfos.Add(file.File);
                qry.SetQueryCondition(codition);
                qry.Query();
            }
        }

        private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
        {
            object obj = treeViewCellOccupy.GetSelectedObject();
            if (obj == null || obj is CellOccupyCellInfo)
            {
                ToolStripMenuItemReplay.Enabled = false;
            }
            else
            {
                ToolStripMenuItemReplay.Enabled = true;
            }
        }
    }
}
