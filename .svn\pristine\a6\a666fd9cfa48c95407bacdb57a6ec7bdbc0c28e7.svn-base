﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEModRoadItem : ModRoadItemBase
    {
        public LTEModRoadItem(TestPoint firstPoint, string fileName, LTEModRoadCondition cond) : base(firstPoint, fileName, cond)
        {
            this.roadCond = cond;
        }

        // 道路序号
        public int SN 
        { 
            get; 
            set; 
        }

        // 路段采样点能匹配到的小区个数
        public int CellCount
        {
            get;
            private set;
        }

        // 路段采样点能匹配到的主服个数
        public int MainCellCount
        {
            get;
            private set;
        }

        // 路段采样点能匹配到的邻区个数
        public int NbCellCount
        {
            get;
            private set;
        }

        // 被干扰的主服个数
        public int InterMainCellCount
        {
            get;
            private set;
        }

        // 干扰主服的邻区个数
        public int InterNbCellCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点个数
        public int InterSampleCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点比例
        public double InterSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1d * InterSampleCount / SampleCount; }
        }

        public List<LTEModCellItem> InterCellList
        {
            get;
            private set;
        }

        protected override void ProcessInfo()
        {
            mrTestPoints.Clear();
            Dictionary<LTECell, int[]> cellSampleCountDic = new Dictionary<LTECell, int[]>();

            foreach (TestPoint tp in TestPoints)
            {
                ModRoadTestPoint mrPt = new ModRoadTestPoint(tp);
                mrTestPoints.Add(mrPt);
                if (mrPt.MainCell == null)
                {
                    continue;
                }

                LTECell mainCell = mrPt.MainCell;
                if (!cellSampleCountDic.ContainsKey(mainCell))
                {
                    cellSampleCountDic.Add(mainCell, new int[2] { 0, 0 });
                }
                ++cellSampleCountDic[mainCell][0]; // 主服采样点数加1

                foreach (LTECell nbCell in mrPt.NbCellList)
                {
                    if (!cellSampleCountDic.ContainsKey(nbCell))
                    {
                        cellSampleCountDic.Add(nbCell, new int[2] { 0, 0 });
                    }
                    ++cellSampleCountDic[nbCell][1]; // 邻区采样点数加1
                }
            }

            this.CellCount = cellSampleCountDic.Count;
            this.MainCellCount = this.NbCellCount = 0;
            foreach (int[] cnts in cellSampleCountDic.Values)
            {
                this.MainCellCount += cnts[0] > 0 ? 1 : 0;
                this.NbCellCount += cnts[1] > 0 ? 1 : 0;
            }

            cellSampleCountDic.Clear();
        }

        public override void Clear()
        {
            if (InterCellList != null)
            {
                InterCellList.Clear();
            }
        }

        public void FilterInterfere(LTEModInterfereCondition interfereCond)
        {
            this.InterSampleCount = this.InterMainCellCount = this.InterNbCellCount = 0;
            Dictionary<LTECell, int[]> cellSampleCountDic = new Dictionary<LTECell, int[]>();
            Dictionary<LTECell, LTEModCellItem> cellItemDic = new Dictionary<LTECell, LTEModCellItem>();
            Dictionary<LTECell, double> interNbCellDic = new Dictionary<LTECell, double>();

            foreach (ModRoadTestPoint mrPt in mrTestPoints)
            {
                List<LTEModInterfereCell> interCells = null;
                if (mrPt.MainCell == null)
                {
                    continue;
                }

                // 干扰邻区列表
                interNbCellDic.Clear();
                bool isInterPoint = IsInterferePoint(mrPt, interfereCond, out interCells);
                if (isInterPoint)
                {
                    foreach (LTEModInterfereCell interCell in interCells)
                    {
                        //interNbCellDic.Add(interCell.LteCell, interCell.Distance);
                        interNbCellDic[interCell.LteCell] = interCell.Distance; // 同一采样点有可能出现两个频点完全一样的情况
                    }
                }

                // 主服
                LTEModCellItem mainCellItem = dealMainCell(cellItemDic, mrPt, isInterPoint);
                if (mrPt.NbCellCount == 0)
                {
                    continue;
                }

                // 所有邻区
                dealNBCell(cellItemDic, interNbCellDic, mrPt, mainCellItem);
                if (!isInterPoint)
                {
                    continue; // 该点没有发生干扰
                }

                // 干扰统计
                caculateInterfereInfo(cellSampleCountDic, mrPt, interCells);
            }
            dealResultCount(cellSampleCountDic);

            cellSampleCountDic.Clear();
            CalcResult(cellItemDic);
            cellItemDic.Clear();
        }

        private void dealResultCount(Dictionary<LTECell, int[]> cellSampleCountDic)
        {
            foreach (int[] cnts in cellSampleCountDic.Values)
            {
                this.InterMainCellCount += cnts[0] > 0 ? 1 : 0;
                this.InterNbCellCount += cnts[1] > 0 ? 1 : 0;
            }
        }

        private static LTEModCellItem dealMainCell(Dictionary<LTECell, LTEModCellItem> cellItemDic, ModRoadTestPoint mrPt, bool isInterPoint)
        {
            LTEModCellItem mainCellItem = null;
            if (!cellItemDic.TryGetValue(mrPt.MainCell, out mainCellItem))
            {
                mainCellItem = new LTEModCellItem(mrPt.MainCell);
                cellItemDic.Add(mrPt.MainCell, mainCellItem);
            }
            mainCellItem.AddSample(mrPt.Rsrp, mrPt.Sinr, mrPt.PdcpDl, mrPt.PdcpUl, false, isInterPoint);
            return mainCellItem;
        }

        private static void dealNBCell(Dictionary<LTECell, LTEModCellItem> cellItemDic, Dictionary<LTECell, double> interNbCellDic, ModRoadTestPoint mrPt, LTEModCellItem mainCellItem)
        {
            for (int i = 0; i < mrPt.NbCellCount; ++i)
            {
                LTECell nbCell = mrPt.NbCellList[i];
                LTEModCellItem nbCellItem = null;
                if (!cellItemDic.TryGetValue(nbCell, out nbCellItem))
                {
                    nbCellItem = new LTEModCellItem(nbCell);
                    cellItemDic.Add(nbCell, nbCellItem);
                }

                bool isInterNb = interNbCellDic.ContainsKey(nbCell);

                nbCellItem.AddSample(mrPt.NbRsrpList[i], mrPt.NbSinrList[i], null, null, isInterNb, false);
                mainCellItem.AddNbCell(nbCellItem, isInterNb);
            }
        }

        private void caculateInterfereInfo(Dictionary<LTECell, int[]> cellSampleCountDic, ModRoadTestPoint mrPt, List<LTEModInterfereCell> interCells)
        {
            ++InterSampleCount;
            if (!cellSampleCountDic.ContainsKey(mrPt.MainCell))
            {
                cellSampleCountDic.Add(mrPt.MainCell, new int[] { 0, 0 });
            }
            ++cellSampleCountDic[mrPt.MainCell][0]; // 主服被干扰次数加1，即干扰主服采样点数加1
            foreach (LTEModInterfereCell interCell in interCells)
            {
                if (!cellSampleCountDic.ContainsKey(interCell.LteCell))
                {
                    cellSampleCountDic.Add(interCell.LteCell, new int[] { 0, 0 });
                }
                ++cellSampleCountDic[interCell.LteCell][1]; // 邻区中干扰主服的采样点数加1
            }
        }

        private bool IsInterferePoint(ModRoadTestPoint mrPt, LTEModInterfereCondition interfereCond, out List<LTEModInterfereCell> interCells)
        {
            interCells = null;
            if (mrPt.NbCellCount == 0)
            {
                return false;
            }

            List<LTECell> nbCells = new List<LTECell>();
            for (int i = 0; i < mrPt.NbCellList.Count; ++i)
            {
                if (mrPt.Rsrp - mrPt.NbRsrpList[i] <= interfereCond.RxlevDiff)
                {
                    nbCells.Add(mrPt.NbCellList[i]);
                }
            }

            interCells = LTEModInterferer.Instance.Stat(mrPt.MainCell, nbCells, interfereCond);
            return interCells.Count > 0;
        }

        private void CalcResult(Dictionary<LTECell, LTEModCellItem> cellItemDic)
        {
            InterCellList = new List<LTEModCellItem>();
            foreach (LTEModCellItem cellItem in cellItemDic.Values)
            {
                cellItem.CalcResult();
                if (cellItem.InterNbCellCount > 0)
                {
                    InterCellList.Add(cellItem);
                }
            }
        }

        private LTEModRoadCondition roadCond { get; set; }
        private readonly List<ModRoadTestPoint> mrTestPoints = new List<ModRoadTestPoint>();
    }

    public class LTEModCellItem
    {
        public LTEModCellItem(LTECell cell)
        {
            this.lteCell = cell;
        }

        public LTECell LteCell
        {
            get { return lteCell; }
        }

        public string CellName
        {
            get { return lteCell.Name; }
        }

        public int Earfcn
        {
            get { return lteCell.EARFCN; }
        }

        public int Pci
        {
            get { return lteCell.PCI; }
        }

        public int Tac
        {
            get { return lteCell.TAC; }
        }

        public int Eci
        {
            get { return lteCell.ECI; }
        }

        public int CellID
        {
            get { return lteCell.SCellID; }
        }

        // 作为干扰邻区时与主服的区里
        public double Distance
        {
            get;
            set;
        }

        public string RsrpAvgStr
        {
            get;
            private set;
        }

        public string SinrAvgStr
        {
            get;
            private set;
        }

        public string PdcpDlAvgStr
        {
            get;
            private set;
        }

        public string PdcpUlAvgStr
        {
            get;
            private set;
        }

        // 邻区个数
        public int NbCellCount
        {
            get;
            private set;
        }

        // 产生干扰的邻区个数
        public int InterNbCellCount
        {
            get;
            private set;
        }

        // 产生干扰的邻区占比
        public double InterNbCellRate
        {
            get;
            private set;
        }

        // 采样点总数
        public int SampleCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点数
        public int InterSampleCount
        {
            get;
            private set;
        }

        // 干扰其他主服的采样点个数
        public int InterOtherSampleCount
        {
            get;
            private set;
        }

        // 被邻区干扰的采样点个数
        public int OtherInterSampleCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点个数
        public double InterSampleRate
        {
            get;
            private set;
        }

        // 受干扰的邻区列表
        public List<LTEModCellItem> InterNbCellList
        {
            get;
            private set;
        }

        public void AddNbCell(LTEModCellItem nbCellItem, bool isInterfere)
        {
            if (!nbCellItemDic.ContainsKey(nbCellItem))
            {
                nbCellItemDic.Add(nbCellItem, isInterfere);
            }
            else if (isInterfere) // 发生了干扰，更新状态
            {
                nbCellItemDic[nbCellItem] = isInterfere;
            }
        }

        public void AddSample(float? rsrp, float? sinr, double? pdcpDl, double? pdcpUl, bool isInterOther, bool isOtherInter)
        {
            ++SampleCount;
            rsrpSum += rsrp == null ? 0 : (float)rsrp;
            if (sinr > -50 && sinr < 50)
            {
                sinrSum += (float)sinr;
            }
            pdcpDlSum += pdcpDl == null ? 0 : (int)pdcpDl;
            pdcpUlSum += pdcpUl == null ? 0 : (int)pdcpUl;

            if (isInterOther)
            {
                ++InterOtherSampleCount;
                ++InterSampleCount;
            }
            if (isOtherInter)
            {
                ++OtherInterSampleCount;
                ++InterSampleCount;
            }
        }

        public void CalcResult()
        {
            NbCellCount = nbCellItemDic.Count;
            InterNbCellCount = 0;
            InterNbCellList = new List<LTEModCellItem>();
            foreach (LTEModCellItem nbCellItem in nbCellItemDic.Keys)
            {
                if (nbCellItemDic[nbCellItem])
                {
                    InterNbCellList.Add(nbCellItem);
                    nbCellItem.Distance = MathFuncs.GetDistance(lteCell.Longitude, lteCell.Latitude, nbCellItem.LteCell.Longitude, nbCellItem.LteCell.Latitude);
                    ++InterNbCellCount;
                }
            }
            InterNbCellRate = NbCellCount == 0 ? 0 : 1d * InterNbCellCount / NbCellCount;
            InterSampleRate = SampleCount == 0 ? 0 : 1d * InterSampleCount / SampleCount;

            RsrpAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", rsrpSum / SampleCount);
            SinrAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", sinrSum / SampleCount);
            PdcpDlAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", pdcpDlSum / SampleCount);
            PdcpUlAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", pdcpUlSum / SampleCount);

            nbCellItemDic.Clear();
        }

        private double rsrpSum;
        private double sinrSum;
        private double pdcpDlSum;
        private double pdcpUlSum;
        private readonly LTECell lteCell;

        private readonly Dictionary<LTEModCellItem, bool> nbCellItemDic = new Dictionary<LTEModCellItem, bool>();

        public BackgroundResult ConvertToBackgroundResult(FileInfo file, LTEModRoadItem item)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.LTE;
            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
            }

            if (item.TestPoints.Count > 0)
            {
                TestPoint tpStart = item.TestPoints[0];
                TestPoint tpMid = item.TestPoints[item.TestPoints.Count / 2];
                TestPoint tpEnd = item.TestPoints[item.TestPoints.Count - 1];

                bgResult.ISTime = tpStart.Time;
                bgResult.IETime = tpEnd.Time;

                bgResult.LongitudeStart = tpStart.Longitude;
                bgResult.LatitudeStart = tpStart.Latitude;

                bgResult.LongitudeMid = tpMid.Longitude;
                bgResult.LatitudeMid = tpMid.Latitude;

                bgResult.LongitudeEnd = tpEnd.Longitude;
                bgResult.LatitudeEnd = tpEnd.Latitude;
            }
            bgResult.DistanceLast = item.Length;
            bgResult.SampleCount = item.SampleCount;
            bgResult.RoadDesc = item.RoadDesc;

            bgResult.LAC = this.Tac;
            bgResult.CI = this.Eci;
            bgResult.BCCH = this.Earfcn;
            bgResult.BSIC = this.Pci;
            bgResult.RxLevMean = float.Parse(RsrpAvgStr);
            bgResult.RxQualMean = float.Parse(SinrAvgStr);

            bgResult.AddImageValue(item.MainCellCount);
            bgResult.AddImageValue(item.NbCellCount);
            bgResult.AddImageValue(item.InterMainCellCount);
            bgResult.AddImageValue(item.InterNbCellCount);
            bgResult.AddImageValue(item.InterSampleCount);
            bgResult.AddImageValue((float)item.InterSampleRate * 100);

            bgResult.AddImageValue(this.CellName);
            bgResult.AddImageValue(this.CellID);
            bgResult.AddImageValue(this.NbCellCount);
            bgResult.AddImageValue(this.InterNbCellCount);//干扰邻区个数,与InterNbCellList集合相对应
            bgResult.AddImageValue((float)this.InterNbCellRate * 100);
            bgResult.AddImageValue(this.SampleCount);
            bgResult.AddImageValue(this.InterSampleCount);
            bgResult.AddImageValue((float)this.InterSampleRate * 100);
            bgResult.AddImageValue(this.PdcpUlAvgStr);
            bgResult.AddImageValue(this.PdcpDlAvgStr);

            foreach (LTEModCellItem nbCell in this.InterNbCellList)
            {
                bgResult.AddImageValue(nbCell.CellName);
                bgResult.AddImageValue(nbCell.Tac);
                bgResult.AddImageValue(nbCell.Eci);
                bgResult.AddImageValue(nbCell.CellID);
                bgResult.AddImageValue(nbCell.Earfcn);
                bgResult.AddImageValue(nbCell.Pci);
                bgResult.AddImageValue(nbCell.SampleCount);
                bgResult.AddImageValue(nbCell.InterSampleCount);
                bgResult.AddImageValue((float)nbCell.InterSampleRate * 100);
                bgResult.AddImageValue(nbCell.RsrpAvgStr);
                bgResult.AddImageValue(nbCell.SinrAvgStr);
                bgResult.AddImageValue((float)nbCell.Distance);
            }

            return bgResult;
        }
    }

    public class ModRoadTestPoint
    {
        public ModRoadTestPoint(TestPoint tp)
        {
            this.TestPoint = tp;
            Dictionary<LTECell, bool> nbCellDic = new Dictionary<LTECell, bool>();
            if (tp is LTEFddTestPoint)
            {
                Tac = (int?)(ushort?)tp["lte_fdd_TAC"];
                Eci = (int?)tp["lte_fdd_ECI"];
                Earfcn = (int?)tp["lte_fdd_EARFCN"];
                Pci = (int?)(short?)tp["lte_fdd_PCI"];
                Rsrp = (float?)tp["lte_fdd_RSRP"];
                Sinr = (float?)tp["lte_fdd_SINR"];
                PdcpDl = (int?)tp["lte_fdd_PDCP_DL"] / 8d / 1024 / 1024;
                PdcpUl = (int?)tp["lte_fdd_PDCP_UL"] / 8d / 1024 / 1024;
                MainCell = tp.GetMainCell_LTE_FDD();
                if (MainCell == null)
                {
                    return;
                }

                addLteFddNCellInfo(tp, nbCellDic);
            }
            else
            {
                Tac = (int?)(ushort?)tp["lte_TAC"];
                Eci = (int?)tp["lte_ECI"];
                Earfcn = (int?)tp["lte_EARFCN"];
                Pci = (int?)(short?)tp["lte_PCI"];
                Rsrp = (float?)tp["lte_RSRP"];
                Sinr = (float?)tp["lte_SINR"];
                PdcpDl = (int?)tp["lte_PDCP_DL"] / 8d / 1024 / 1024;
                PdcpUl = (int?)tp["lte_PDCP_UL"] / 8d / 1024 / 1024;
                MainCell = tp.GetMainCell_LTE();
                if (MainCell == null)
                {
                    return;
                }

                addLteNCellInfo(tp, nbCellDic);
            }
        }

        private void addLteFddNCellInfo(TestPoint tp, Dictionary<LTECell, bool> nbCellDic)
        {
            for (int i = 0; i < 50; ++i)
            {
                float? nbRsrp = (float?)tp["lte_fdd_NCell_RSRP", i];
                float? nbSinr = (float?)tp["lte_fdd_NCell_SINR", i];
                int? nbEarfcn = (int?)tp["lte_fdd_NCell_EARFCN", i];
                int? nbPci = (int?)(short?)tp["lte_fdd_NCell_PCI", i];
                if (nbRsrp == null || nbEarfcn == null || nbPci == null)
                {
                    break;
                }
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, nbEarfcn, nbPci, tp.Longitude, tp.Latitude);
                if (nbCell == null)
                {
                    break;
                }
                if (nbCellDic.ContainsKey(nbCell))
                {
                    continue;
                }

                NbCellList.Add(nbCell);
                NbRsrpList.Add(nbRsrp);
                NbSinrList.Add(nbSinr);
                NbEarfcnList.Add(nbEarfcn);
                NbPciList.Add(nbPci);
                nbCellDic.Add(nbCell, true);
                ++NbCellCount;
            }
        }

        private void addLteNCellInfo(TestPoint tp, Dictionary<LTECell, bool> nbCellDic)
        {
            for (int i = 0; i < 50; ++i)
            {
                float? nbRsrp = (float?)tp["lte_NCell_RSRP", i];
                float? nbSinr = (float?)tp["lte_NCell_SINR", i];
                int? nbEarfcn = (int?)tp["lte_NCell_EARFCN", i];
                int? nbPci = (int?)(short?)tp["lte_NCell_PCI", i];
                if (nbRsrp == null || nbEarfcn == null || nbPci == null)
                {
                    break;
                }
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, nbEarfcn, nbPci, tp.Longitude, tp.Latitude);
                if (nbCell == null)
                {
                    break;
                }
                if (nbCellDic.ContainsKey(nbCell))
                {
                    continue;
                }

                NbCellList.Add(nbCell);
                NbRsrpList.Add(nbRsrp);
                NbSinrList.Add(nbSinr);
                NbEarfcnList.Add(nbEarfcn);
                NbPciList.Add(nbPci);
                nbCellDic.Add(nbCell, true);
                ++NbCellCount;
            }
        }

        public TestPoint TestPoint { get; set; } 

        public LTECell MainCell { get; set; } 
        public int? Tac { get; set; } 
        public int? Eci { get; set; } 
        public int? Earfcn { get; set; } 
        public int? Pci { get; set; } 
        public float? Rsrp { get; set; } 
        public float? Sinr { get; set; } 
        public double? PdcpDl { get; set; } 
        public double? PdcpUl { get; set; } 

        public int NbCellCount  { get; set; } = 0;
        public List<LTECell> NbCellList  { get; set; } = new List<LTECell>();
        public List<float?> NbRsrpList  { get; set; } = new List<float?>();
        public List<float?> NbSinrList  { get; set; } = new List<float?>();
        public List<int?> NbEarfcnList  { get; set; } = new List<int?>();
        public List<int?> NbPciList  { get; set; } = new List<int?>();
    }

    /// <summary>
    /// 道路查询条件
    /// </summary>
    public class LTEModRoadCondition : ModRoadConditionBase
    {
        public LTEModInterfereCondition FilterCond { get; set; } = new LTEModInterfereCondition();
    }

    /// <summary>
    /// 结果过滤条件
    /// </summary>
    public class LTEModInterfereCondition : LTEModInterfereCond
    {
        public double InterfereRate { get; set; } = 0.1;
        public double RxlevDiff { get; set; } = 6;
    }
}
