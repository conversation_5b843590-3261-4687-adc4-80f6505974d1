﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using AxMapWinGIS;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using System.Drawing.Drawing2D;
using System.IO;

namespace MasterCom.RAMS.MapControlTool
{
    class MapControlToolAddPointMark
    {
        private MapOperation mapOP { get; set; }
        private readonly MapForm mf;
        private readonly AxMap mapControl;
        private bool isActive = false;

        public MapControlToolAddPointMark(MapForm mapForm, AxMap map)
        {
            mapControl = map;
            mapOP = new MapOperation(mapControl);
            mf = mapForm;
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent += new EventHandler(mapForm_ClearDataEvent);
        }

        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDownEvent += mapControl_MouseDownEvent;
            }
            isActive = true;
            mapControl.CursorMode = tkCursorMode.cmNone;
            mapControl.MapCursor = tkCursor.crsrCross;
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.MouseDownEvent -= mapControl_MouseDownEvent;
            isActive = false;
        }

        void mapForm_ClearDataEvent(object sender, EventArgs e)
        {
            //
        }

        public void Draw()
        {
            //
        }

        void mapControl_MouseDownEvent(object sender, _DMapEvents_MouseDownEvent e)
        {
            if (e.button == 1)
            {
                ProcessMouseLeftDown(e);
            }
            else if (e.button == 2)
            {
                ProcessMouseRightDown(e);
            }
        }

        private void ProcessMouseLeftDown(_DMapEvents_MouseDownEvent e)
        {
            double longi = 0;
            double lati = 0;

            mapControl.PixelToProj(e.x, e.y, ref longi, ref lati);
            PointMarkCreateDlg dlg = new PointMarkCreateDlg(longi, lati, mapControl, mf);
            dlg.ShowDialog();
        }

        private void ProcessMouseRightDown(_DMapEvents_MouseDownEvent e)
        {
            PointMarkEditDlg dlg = new PointMarkEditDlg(this.mapControl, this.mf, e.x, e.y);
            // 如果没有选中点标签
            if (dlg.DialogResult == System.Windows.Forms.DialogResult.None)
            {
                return;
            }
            dlg.ShowDialog();
        }

    } // end class
} // end namespace 
