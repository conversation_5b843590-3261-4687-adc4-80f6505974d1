<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStripDropDownProjectOld.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="toolStripDropDownProjectNow.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>247, 17</value>
  </metadata>
  <metadata name="toolStripDropDownServiceNow.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>483, 17</value>
  </metadata>
  <metadata name="toolStripDropDownServiceOld.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>715, 17</value>
  </metadata>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>945, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>52</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAAAAEACACoCAAAJgAAABAQAAABACAAaAQAAM4IAAAoAAAAIAAAAEAAAAABAAgAAAAAAIAE
        AAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAMDAwACAgIAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAkJCQASEhIAHx8fACwsLAA5OTkARUVFAFJSUgBfX18AbGxsAHh4
        eACFhYUAkpKSAJ+fnwCrq6sAuLi4AMXFxQDS0tIA3t7eAOvr6wD4+PgA8Pv/AKSgoADA3MAA8MqmAAAA
        PgAAAF0AAAB8AAAAmwAAALoAAADZAAAA8AAkJP8ASEj/AGxs/wCQkP8AtLT/AAAUPgAAHl0AACh8AAAy
        mwAAPLoAAEbZAABV8AAkbf8ASIX/AGyd/wCQtf8AtM3/AAAqPgAAP10AAFR8AABpmwAAfroAAJPZAACq
        8AAktv8ASML/AGzO/wCQ2v8AtOb/AAA+PgAAXV0AAHx8AACbmwAAuroAANnZAADw8AAk//8ASP//AGz/
        /wCQ//8AtP//AAA+KgAAXT8AAHxUAACbaQAAun4AANmTAADwqgAk/7YASP/CAGz/zgCQ/9oAtP/mAAA+
        FAAAXR4AAHwoAACbMgAAujwAANlGAADwVQAk/20ASP+FAGz/nQCQ/7UAtP/NAAA+AAAAXQAAAHwAAACb
        AAAAugAAANkAAADwAAAk/yQASP9IAGz/bACQ/5AAtP+0ABQ+AAAeXQAAKHwAADKbAAA8ugAARtkAAFXw
        AABt/yQAhf9IAJ3/bAC1/5AAzf+0ACo+AAA/XQAAVHwAAGmbAAB+ugAAk9kAAKrwAAC2/yQAwv9IAM7/
        bADa/5AA5v+0AD4+AABdXQAAfHwAAJubAAC6ugAA2dkAAPDwAAD//yQA//9IAP//bAD//5AA//+0AD4q
        AABdPwAAfFQAAJtpAAC6fgAA2ZMAAPCqAAD/tiQA/8JIAP/ObAD/2pAA/+a0AD4UAABdHgAAfCgAAJsy
        AAC6PAAA2UYAAPBVAAD/bSQA/4VIAP+dbAD/tZAA/820AD4AAABdAAAAfAAAAJsAAAC6AAAA2QAAAPAA
        AAD/JCQA/0hIAP9sbAD/kJAA/7S0AD4AFABdAB4AfAAoAJsAMgC6ADwA2QBGAPAAVQD/JG0A/0iFAP9s
        nQD/kLUA/7TNAD4AKgBdAD8AfABUAJsAaQC6AH4A2QCTAPAAqgD/JLYA/0jCAP9szgD/kNoA/7TmAD4A
        PgBdAF0AfAB8AJsAmwC6ALoA2QDZAPAA8AD/JP8A/0j/AP9s/wD/kP8A/7T/ACoAPgA/AF0AVAB8AGkA
        mwB+ALoAkwDZAKoA8AC2JP8Awkj/AM5s/wDakP8A5rT/ABQAPgAeAF0AKAB8ADIAmwA8ALoARgDZAFUA
        8ABtJP8AhUj/AJ1s/wC1kP8AzbT/AAAAAAAAAAAAAAAAAACurq6urq6uAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAK6urq6urq6urq6urgAAAAAAAAAAAAAAAAAAAAAAAK6urq6urq6urq6urq6urq4AAAAAAAAAAAAA
        AAAAAACioqKioq6urq6urq6urq6urq6uAAAAAAAAAAAAAAAAoqKioqKioqKioq6urq6urq6urq6uAAAA
        AAAAAAAAAKKioqKioqKioqKioqKurq6urq6urq6uAAAAAAAAAACio6Ojo6OjoqKioqKioqKirq6urq6u
        rq6uAAAAAAAAo6Ojo6Ojo6MZJRmioqKioqKirq6urq6urq6uAAAAAKOjo6Ojo6OjoycPDyejoqOiGqKu
        oh8HHh6urq4AAAAAo6Ojo6Ojo6OjoxqjHSCjo6IHJa6uHw+irq6urq4AAKOjo6Ojo6Ojo6Ojo6OjJSej
        oiUPo66jDxuurq6urgAAo6Ojo6OjpKSko6Ojo6OjJyWiGg8hoq4gIa6urq6urgCjo6OkpKSkpKSkpKSk
        o6OjJ6OjDyEerggPGK6urq6uo6OkpKSkpKSkpKSkpKSkpKMlJaMPJSclriIfrq6urq6jpKSkpKSXtLS0
        mKSkpKSko6MnoyEnoiGjGw+irq6urqSkpKS0tLW1tbW1qLSkpKSkoyclJyGiGSCjIyWurq6upKSktLW1
        tbW1tbW1tbSYpKSkpCcdI6OiGx4dI66urq6kpLS1tbW1tra2trW1taiYpKSjJycPo6OiHR4PGq6urqSY
        tbW1ticnJycnJ7W1tbSkpKS1Jw8ao6OiJyMhrq6uALS1tbYnJycnJycnJ7W1tbSkpKSrD7Wjo6KiIQ8Y
        rq4AtbW1JycnJ6urJycntrW1tKSkoycPHaKjoqKjDx+urgC1tbYnJ6shIiKrJycntrW1tKSktQ8go6Oj
        o64lDxkAAAAnJ6urIiIjIyIhqycnJye2tbW2JyclGxsbGhsdJdEAALW2JyerISIiqycnJ7W1tKSkpKOj
        o6OjoqKirq4AAAAAALYnJyerIaurJycntbW0pKSko6Ojo6OioqKurgAAAAAAALYnJycnJycnJ7a1tbSk
        pKSjo6Ojo6Kioq4AAAAAAAAAACcnJycnJye2tbWomKSkpKOjo6OjoqKiAAAAAAAAAAAAALYnJye2trW1
        tbSkpKSjo6Ojo6OiogAAAAAAAAAAAAAAALW1tbW1tbW0pKSkpKOjo6OjoqIAAAAAAAAAAAAAAAAAALW1
        tbWotKSkpKSjo6Ojo6MAAAAAAAAAAAAAAAAAAAAAAAC0tKSkpKSko6Ojo6MAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAKSkpKOjo6MAAAAAAAAAAAAAAAD/+A///8AD//8AAP/+AAA//AAAH/gAAA/wAAAH4AAAA8AA
        AAPAAAABgAAAAYAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAIAAAACAAAABwAAAAMAA
        AAPgAAAD8AAAB/gAAA/8AAAf/gAAP/8AAP//wAP///gP/ygAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH41Ixt+NyB2fjUi03wzI+F7MSObei4mMgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAfTQbEIE9G4iBPRzcgDsd/YA6Hv9/OCD/fjUi/3wy
        JOt6LyWseC0lMQAAAAAAAAAAAAAAAAAAAAAAAAAAh0cYJ4hIGtuISRr/h0cY/4RCFP+EQhr/gj4b/4A7
        Hf9+OCD/fTUj/3owJPt5LSViAAAAAAAAAAAAAAAAik0eD5BVG+CRWBv/kVka/5VeIv+dajf/iUwS/4hK
        GP+DPxT/gDwZ/3w1Gf98NCD/fDMo/3csJEYAAAAAAAAAAJZgG5CYZBv/m2kb/5hkE/+ofDj/18Sl/7+d
        df+LTw3/o3NH/4RCFf+2jnr/wZ6R/4ZCMf94Kx/ZeConD5ZjGh2dbRvnonQb/6R5F/+lexb/o3YV/5ln
        CP+yjEj/tI1Z/7KKWP/Nsp3/nWhC/82wpP9zJQn/fTQl/3kuJYOhdB2FpnwV/quFDP+vixD/sI0T/6+L
        D/+rhQ3/n3IE/7ybXP+2klr/6+HT/6FwSP/OtKH/kVQ7/3owG/97MCTlqoEN3a+LEP+2ly7/u51G/7yg
        TP+6nUX/tpYu/66KC/+rhRv/yK56/861k/+tglX/ya2W/7uWg/90KAz/fTQl/7GOEt24mTr/w6he/8uz
        b//Ot3X/y7Nu/8OoXf+5mj3/qYMA/8GkWv/l2ML/lF0Z/7qWdf/k1s3/gDod/3syH/+3mD2FwKVW/tC6
        ef/cy5T/4NGf/9zLk//QuXj/waZZ/7GPHv+xjB7/8ere/6BvLf+FRgj/5tnN/6t9a/9vHgXhuZxJHcy1
        c+fezZn/69+///Hq1f/q377/3c2Z/822dv+/o03/r4sZ/+7l0P+7mWj/j1UY/6JwS//XwLj/ezMlmgAA
        AADVwYiR4M+e/+/mzf/59ez/7+XL/+HRov/Ru37/w6hX/7eXKv+yjTz/qX49/55sNv+SWC7/mF9F25li
        cTMAAAAAt5Y3D9bAguDi06T/6d24/+PUp//WwoT/xKpg/7aWMf+qhAb/nm8Q/5ZhE/+NUhb/g0EU/24f
        AEUAAAAAAAAAAAAAAADOuHUn1L+A29fDh//Uv4H/yrJs/76hUP+zkRv/qYAQ/59xG/+XYRv/jVEb+4ZG
        GWIAAAAAAAAAAAAAAAAAAAAAAAAAAMCiVBDHrWWIwqdb3LydR/20kiP/q4UM/6N3Gv+baRrrk1scrIxP
        GzEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALGPLxuykR92rIcH06V7GOGfbxubl2EaMgAA
        AAAAAAAAAAAAAAAAAAAAAAAA+B8AAOAHAADAAwAAgAEAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACAAAAAgAEAAMADAADgBwAA+B8AAA==
</value>
  </data>
</root>