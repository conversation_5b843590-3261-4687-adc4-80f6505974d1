﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCellCoverLapSetForm : BaseDialog
    {
        public ZTCellCoverLapSetForm(Type ztcellCoverLapByRegionLTE)
        {
            InitializeComponent();
            numDistanceMin.Enabled = false;
            numDistanceMax.Enabled = false;
            this.cbxFreqBand.Visible = false;
            this.chkFreqBand.Visible = false;
            this.chbNearBTS.Visible = false;
            groupBox2.Height = 128;
            groupBox1.Height = 97;
            this.groupBox1.Top = 146;
            this.Height = 331;
            if (ztcellCoverLapByRegionLTE.Name.Equals("ZTCellCoverLapByRegion_LTE")
                || ztcellCoverLapByRegionLTE.Name.Equals("ZTCellCoverLapByRegion_LTEScan"))
            {
                this.chkFreqBand.Visible = true;
                this.cbxFreqBand.Visible = true;
                groupBox1.Height = 127;
                this.Height = 361;
            }
            else if (ztcellCoverLapByRegionLTE.Name.Equals("ZTCellCoverLapByRegion"))
            {
                this.groupBox1.Top = 174;
                groupBox2.Height = 156;
                this.Height = 359;
                this.chbNearBTS.Visible = true;
            }
        }

        public void SetSameNbhNumVisible(bool visible)
        {
            lblCoFreq.Visible = lblCoFreq2.Visible = numCoFreq.Visible = lblAdjFreq.Visible = lblAdjFreq2.Visible = numAdjFreq.Visible = visible;
            if (visible)
            {
                groupBox1.Height = 151;
                this.Height = 385;
            }
        }

        public void GetSameNbhNumValue(out int sameValue, out int nbhValue)
        {
            sameValue = (int)numCoFreq.Value;
            nbhValue = (int)numAdjFreq.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        internal bool GetSettingFilterRet(CellCoverLapCondition settingCondition)
        {
            settingCondition.CheckNearBTS = this.chbNearBTS.Checked;
            settingCondition.IsBand = this.chkFreqBand.Checked;
            settingCondition.BandType = this.cbxFreqBand.SelectedItem.ToString();

            settingCondition.CurFilterRxlev = (int)numFilter.Value;
            if (!cbxSampleCount.Checked)
            {
                settingCondition.CurMinSampleCount = 0;
            }
            else
            {
                settingCondition.CurMinSampleCount = (int)numSampleCount.Value;
            }
            if (!cbxPercent.Checked)
            {
                settingCondition.CurMinPercent = 0;
            }
            else
            {
                settingCondition.CurMinPercent = (int)numPercent.Value * 0.01f;
            }

            if (!chkDistance.Checked)
            {
                settingCondition.CurMinDistance = 0;
                settingCondition.CurMaxDistance = 1000000;
            }
            else
            {
                int minDistance;
                int maxDistance;
                int.TryParse(numDistanceMin.Value.ToString(), out minDistance);
                int.TryParse(numDistanceMax.Value.ToString(), out maxDistance);
                settingCondition.CurMinDistance = minDistance;
                settingCondition.CurMaxDistance = maxDistance;
            }
            settingCondition.NearestCellCount = (int)numNearestCellCount.Value;
            settingCondition.DisFactor = (float)numDisFactor.Value;
            return true;
        }

        private void cbxSampleCount_CheckedChanged(object sender, EventArgs e)
        {
            numSampleCount.Enabled = cbxSampleCount.Checked;
        }

        private void cbxPercent_CheckedChanged(object sender, EventArgs e)
        {
            numPercent.Enabled = cbxPercent.Checked;
        }

        private void chkDistance_CheckedChanged(object sender, EventArgs e)
        {
            numDistanceMin.Enabled = chkDistance.Checked;
            numDistanceMax.Enabled = chkDistance.Checked;
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            cbxFreqBand.Enabled = chkFreqBand.Checked;
        }
    }

    public class CellCoverLapCondition
    {
        public CellCoverLapCondition()
        {

        }

        public int CurFilterRxlev { get; set; } = -90;
        public int CurMinSampleCount { get; set; } = 0;
        public float CurMinPercent { get; set; } = 0;
        public int CurMinDistance { get; set; } = 0;
        public int CurMaxDistance { get; set; } = 1000000;
        public float DisFactor { get; set; } = 1.6f;
        public int NearestCellCount { get; set; } = 3;
        public bool IsBand { get; set; } = false;
        public bool CheckNearBTS { get; set; } = false;
        public string BandType { get; set; } = "";
    }
}