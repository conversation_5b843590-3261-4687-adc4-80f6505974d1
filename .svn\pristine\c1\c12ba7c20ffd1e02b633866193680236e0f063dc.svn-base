﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using System.Runtime.Serialization;
using System.Drawing;
using System.Drawing.Imaging;

namespace MasterCom.RAMS.CQT
{
    public class CQTZTMapLayer : CustomDrawLayer, IKMLExport
    {
        public CQTZTMapLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            this.VisibleScaleEnabled = true;
            this.VisibleScale = new VisibleScale(0, 10000000);
            CQTPoints2Show = new List<CQTLibrary.PublicItem.EvaluateResult>();
        }
        
        public List<CQTLibrary.PublicItem.EvaluateResult> CQTPoints2Show { get; set; }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (CQTPoints2Show.Count == 0)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            drawCQTPoints(graphics, dRect);
        }

        private void drawCQTPoints(Graphics graphics, DbRect dRect)
        {
            if (CQTPoints2Show==null)
            {
                return;
            }

            foreach (CQTLibrary.PublicItem.EvaluateResult pnt in CQTPoints2Show)
            {
                if (dRect.IsPointInThisRect(pnt.FLongitude, pnt.FLatitude))
                {
                    DbPoint dPoint = new DbPoint(pnt.FLongitude, pnt.FLatitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    float radius = (float)(2000 / Map.Scale);
                    RectangleF rect = new RectangleF(point.X - radius * 128, point.Y - radius * 256, radius * 256, radius * 256);
                    Image img = Properties.Resources.cqtDefault;


                    Image imgClone = (Image)img.Clone();
                    ImageAttributes imageAttributes = new ImageAttributes();
                    int width = img.Width;
                    int height = img.Height;
                    ColorMap colorMap = getColorMap(pnt);

                    ColorMap[] remapTable = { colorMap };
                    imageAttributes.SetRemapTable(remapTable, ColorAdjustType.Bitmap);
                    Graphics g = Graphics.FromImage(imgClone);
                    g.DrawImage(
                       imgClone,
                       new Rectangle(0, 0, width, height),  // destination rectangle 
                       0, 0,        // upper-left corner of source rectangle 
                       width,       // width of source rectangle
                       height,      // height of source rectangle
                       GraphicsUnit.Pixel,
                       imageAttributes);


                    // img = Properties.Resources.cqtDefault;

                    graphics.DrawImage(imgClone, rect);
                    if (mainModel.SelCQTPoint2 == pnt)
                    {
                        graphics.DrawRectangle(new Pen(Brushes.Red), rect.Left, rect.Top, rect.Width, rect.Height);
                    }
                    if (MainModel.SelCQTPoint2 == pnt)
                    {
                        graphics.DrawRectangle(new Pen(Brushes.Red, 2), rect.Left, rect.Top, rect.Width, rect.Height);
                    }

                }
            }
        }

        private static ColorMap getColorMap(CQTLibrary.PublicItem.EvaluateResult pnt)
        {
            ColorMap colorMap = new ColorMap();
            colorMap.OldColor = Color.FromArgb(255, 104, 51);

            if (pnt.StrValue == "健康")
            {
                colorMap.NewColor = Color.Green;
            }
            else if (pnt.StrValue == "良好")
            {
                colorMap.NewColor = Color.DarkGreen;
            }
            else if (pnt.StrValue == "合格")
            {
                colorMap.NewColor = Color.Orange;
            }
            else if (pnt.StrValue == "不合格")
            {
                colorMap.NewColor = Color.Red;
            }
            else
            {
                colorMap.NewColor = Color.FromArgb(255, 104, 51);
            }

            return colorMap;
        }

        protected override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            mainModel.SelCQTPoint2 = null;
            MapForm.MapEventArgs ea = e as MapForm.MapEventArgs;
            if (ea == null || CQTPoints2Show == null || CQTPoints2Show.Count == 0)
                return;
            MapOperation2 mop2 = ea.MapOp2;
            selectPoint(mop2);
        }

        CQTBubbleXtraForm infoDlg = new CQTBubbleXtraForm();
        protected void selectPoint(MapOperation2 mop2)
        {
            if (!IsVisible)
                return;
            PointF selPoint;
            DbRect gbound = mop2.GetRegion().Bounds;
            this.Map.ToDisplay(new DbPoint(gbound.x1, gbound.y1), out selPoint);
            foreach (CQTLibrary.PublicItem.EvaluateResult addr in CQTPoints2Show)
            {
                DbPoint dPoint = new DbPoint(addr.FLongitude, addr.FLatitude);
                PointF point;
                this.Map.ToDisplay(dPoint, out point);
                if (point.X < 0 || point.Y < 0)
                {
                    continue;
                }
                float radius = 3;
                RectangleF rect = new RectangleF(point.X - radius * 8, point.Y - radius * 16, radius * 16, radius * 16);
                if (rect.Contains(selPoint.X, selPoint.Y))
                {
                    mainModel.SelCQTPoint2 = addr;
                    if (infoDlg==null ||infoDlg.IsDisposed)
                    {
                        infoDlg = new CQTBubbleXtraForm();
                    }
                    infoDlg.setData(addr);
                    infoDlg.Location = new Point((int)(rect.Right + rect.Width), (int)(rect.Bottom + rect.Height));
                    if (!infoDlg.Visible)
                    {
                        infoDlg.Show(mainModel.MainForm);
                    }
                    return;
                }
            }
        }

        #region IKMLExport Members

        public void ExportKml(KMLExporter exporter, System.Xml.XmlElement parentElem)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
