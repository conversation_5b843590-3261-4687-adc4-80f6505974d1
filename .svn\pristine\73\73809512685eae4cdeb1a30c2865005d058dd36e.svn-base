﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.EventBlock
{
    public class EventBlockCompetitionQuery : DIYEventByRegion
    {
        public EventBlockCompetitionQuery(MainModel mm)
            : base(mm)
        {
            isAddEventToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 20015, this.Name);
        }

        public override string Name
        {
            get
            {
                return "区域事件汇聚竞比";
            }
        }

        /// <summary>
        /// 由于是竞比，时间，运营商，项目类型等条件，都有2个，在功能点内弹窗设置条件，而不用主界面上的条件。
        /// </summary>
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            MainModel.ClearDTData();
            hostBlocks = new List<EventBlock>();
            guestBlocks = new List<EventBlock>();
            overlapBlocks = new List<EventBlock>();
            hostEvents = new List<Event>();
            guestEvents = new List<Event>();
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            fireShowFormAfterQuery();
            clientProxy.Close();
        }

        protected override void fireShowFormAfterQuery()
        {
            if (hostBlocks.Count==0&&guestBlocks.Count==0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的事件！");
                return;
            }
            EventBlockCompetitionResultForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(EventBlockCompetitionResultForm).FullName) as EventBlockCompetitionResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new EventBlockCompetitionResultForm(MainModel);
            }
            frm.FillData(hostBlocks,guestBlocks,overlapBlocks,blockCondition.BlockRadius);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
        }

        /// <summary>
        /// 主队事件，true;否则为false;
        /// </summary>
        protected override void queryInThread(object o)
        {
            doSomethingBeforeQueryInThread();
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                queryByCondition(conditionOfHost, clientProxy);
                queryByCondition(conditionOfGuest, clientProxy);
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(ex.StackTrace + "\n" + ex.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryByCondition(QueryCondition cond, ClientProxy proxy)
        {
            this.condition = cond;
            bool isHostEvent = condition.Equals(conditionOfHost);
            foreach (TimePeriod period in condition.Periods)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在统计[" + (isHostEvent ? "主队，" : "客队,") + period.GetShortString() + "]内的数据...";
                queryPeriodInfo(proxy, proxy.Package, period, false);
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            bool hasSetCondition = false;
            if (blockCondition == null)
            {
                blockCondition = new EventBlockCondition();
            }
            EventBlockCompetitionConditionDlg dlg = new EventBlockCompetitionConditionDlg(MainModel, conditionOfHost, conditionOfGuest, blockCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                dlg.GetCondition(out blockCondition, out conditionOfHost, out conditionOfGuest);
                conditionOfHost.Geometorys = MainModel.SearchGeometrys;
                conditionOfGuest.Geometorys = MainModel.SearchGeometrys;
                hasSetCondition = true;
            }
            return hasSetCondition;
        }

        protected override void getResultAfterQuery()
        {
            hostBlocks = collectBlocks(hostEvents);
            guestBlocks = collectBlocks(guestEvents);
            overlapBlocks = analyseOverlapBlock();
        }

        /// <summary>
        /// 主客队汇聚块分析
        /// </summary>
        private List<EventBlock> analyseOverlapBlock()
        {
            List<EventBlock> overlayEventBlockList = new List<EventBlock>();
            foreach (EventBlock hostBlk in hostBlocks)
            {
                foreach (EventBlock guestBlk in guestBlocks)
                {
                    foreach (Event e in guestBlk.AbnormalEvents)
                    {
                        if (hostBlk.Intersect(e.Longitude, e.Latitude, blockCondition.BlockRadius))
                        {
                            EventBlock eb = new EventBlock(1, hostBlk.AbnormalEvents[0]);
                            eb.Join(hostBlk);
                            eb.Join(guestBlk);
                            overlayEventBlockList.Add(eb);
                            guestBlk.Repeat = true;
                            hostBlk.Repeat = true;
                            break;
                        }
                    }
                }
            }
            return overlayEventBlockList;
        }


        /// <summary>
        /// 主队条件
        /// </summary>
        protected QueryCondition conditionOfHost = null;
        /// <summary>
        /// 客队条件
        /// </summary>
        protected QueryCondition conditionOfGuest = null;
        protected EventBlockCondition blockCondition = null;

        private List<EventBlock> hostBlocks = null;
        private List<EventBlock> guestBlocks = null;
        private List<EventBlock> overlapBlocks = null;
        private List<Event> hostEvents = null;
        private List<Event> guestEvents = null;
        protected override void doWithDTData(Event evt)
        {
            if (condition.Equals(conditionOfHost))
            {
                hostEvents.Add(evt);
            }
            else
            {
                guestEvents.Add(evt);
            }
        }

        private List<EventBlock> collectBlocks(List<Event> evts)
        {
            List<EventBlock> blocks = new List<EventBlock>();
            List<EventBlock> canMergeBlocks = new List<EventBlock>();
            int totalCnt = evts.Count;
            int idx = 0;
            foreach (Event evt in evts)
            {
                WaitBox.ProgressPercent = (int)(++idx * 100.0 / totalCnt);
                canMergeBlocks.Clear();
                foreach (EventBlock block in blocks)
                {
                    if (block.Intersect(evt.Longitude, evt.Latitude, blockCondition.BlockRadius))
                    {
                        canMergeBlocks.Add(block);
                    }
                }
                if (canMergeBlocks.Count == 0)
                {
                    EventBlock newBlk = new EventBlock(hostBlocks.Count + 1, evt);
                    blocks.Add(newBlk);
                }
                else
                {
                    EventBlock firstBlk = canMergeBlocks[0];
                    firstBlk.AddAbnormalEvent(evt);
                    for (int i = 1; i < canMergeBlocks.Count; i++)
                    {
                        EventBlock otherBlk = canMergeBlocks[i];
                        firstBlk.Join(otherBlk);
                        blocks.Remove(otherBlk);
                    }
                }
            }
            for (int i = 0; i < blocks.Count; i++)
            {
                EventBlock blk = blocks[i];
                if (blk.AbnormalEventCount < blockCondition.MinEventCount)
                {
                    blocks.RemoveAt(i);
                    i--;
                }
            }
            return blocks;
        }


    }

    public class EventBlockCondition
    {
        public EventBlockCondition()
        {
        }
        public EventBlockCondition(double blockRadius, int minEventCount)
        {
            this.radius = blockRadius;
            this.minEvtCnt = minEventCount;
        }
        private readonly double radius = 100;
        public double BlockRadius
        {
            get { return radius; }
        }

        private readonly int minEvtCnt = 3;
        public int MinEventCount
        {
            get { return minEvtCnt; }
        }
    }
}
