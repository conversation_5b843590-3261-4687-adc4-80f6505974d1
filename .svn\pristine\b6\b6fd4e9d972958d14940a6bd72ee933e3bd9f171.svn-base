﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryTestPlanGridKPI : QueryKPIStatByRegion
    {
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11048, this.Name);
        }

        private List<int> rounds = null;

        protected override bool getConditionBeforeQuery()
        {
            if (condition.Geometorys.SelectedResvRegions.Count == 0)
            {
                MessageBox.Show("请在预存区域添加网格图层！");
                return false;
            }

            ReportPickerDlg dlg = new ReportPickerDlg();
            dlg.DisplayRounds = true;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            rounds = dlg.Rounds;
            if (!getTestPlanGridFiles())
            {
                MessageBox.Show("无符合选定轮次的文件!");
                return false;
            }
            KpiDataManager = new KPIDataManager();
            return true;
        }

        /// <summary>
        /// 文件ID对应网格名
        /// </summary>
        private Dictionary<int, string> fileAreanameDic = null;

        private bool getTestPlanGridFiles()
        {
            fileAreanameDic = new Dictionary<int, string>();
            DIYQueryFileInfoByRegion query = new DIYQueryFileInfoByRegion(MainModel);
            query.IsShowFileInfoForm = false;
            query.SetQueryCondition(this.condition);
            query.Query();

            List<string> areaNames = new List<string>();

            foreach (ResvRegion reg in condition.Geometorys.SelectedResvRegions)
            {
                areaNames.Add(reg.RegionName);
            }
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo fi in MainModel.FileInfos)
            {
                string areaName;
                int round = 0;
                extractPropFromFileName(fi.Name, out areaName, out round);
                if (rounds.Contains(round) && areaNames.Contains(areaName))
                {//文件的测试轮次和网格符合条件
                    sb.Append(fi.ID.ToString() + ",");
                    fileAreanameDic[fi.ID] = areaName;
                }
            }
            string fileIds = sb.ToString().TrimEnd(',');
            if (string.IsNullOrEmpty(fileIds))
            {
                return false;
            }
            //限定后续的查询文件，确保统计文件的有效性
            condition.FileName = fileIds;
            condition.NameFilterType = FileFilterType.ByMark_ID;
            return true;
        }

        /// <summary>
        /// 从文件名解析出所属网格，测试轮次
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="areaName">网格名</param>
        /// <param name="round">轮次</param>
        private void extractPropFromFileName(string fileName, out string areaName, out int round)
        {
            /*
             * 文件名命名规则：xxx_ProjectDesc_GridDesc#RoundDesc.Extension
             * 从#往前“_”之间为网格名
             * 从#往后“.”之间为轮次
             */
            areaName = string.Empty;
            round = -1;
            int idx = fileName.LastIndexOf('#');
            if (idx==-1)
            {
                return;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = idx-1; i >= 0; i--)
            {
                char c = fileName[i];
                if (c == '_')
                {
                    break;
                }
                sb.Insert(0, c);
            }
            areaName = sb.ToString();

            sb = new StringBuilder();
            for (int i = idx + 1; i < fileName.Length; i++)
            {
                char c = fileName[i];
                if (c == '.')
                {
                    break;
                }
                sb.Append(c);
            }
            int.TryParse(sb.ToString(), out round);
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();

            GridUnitBase grid = new GridUnitBase(lng, lat);
            DbRect rect = grid.Bounds;

            //if (!isValidStatImg(lng, lat))
            //{
            //    return;
            //}

            fillStatData(package, curImgColumnDef, singleStatData);

            string areaName;
            if (!fileAreanameDic.TryGetValue(singleStatData.FileID, out areaName))
            {
                return;
            }
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            foreach (ResvRegion reg in condition.Geometorys.SelectedResvRegions)
            {
                if (reg.RegionName.Equals(areaName) && reg.GeoOp.CheckRectCenterInRegion(rect))
                {
                    KpiDataManager.AddStatData(areaName, reg, fi, singleStatData, this.curReportStyle.HasGridPerCell);
                    break;
                }
            }

        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, this.needSeparateByServiceID(evt), needSeparateByFileName(evt));

            string areaName;
            if (!fileAreanameDic.TryGetValue(evt.FileID, out areaName))
            {
                return;
            }
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            foreach (ResvRegion reg in condition.Geometorys.SelectedResvRegions)
            {
                if (reg.RegionName.Equals(areaName)
                    && reg.GeoOp.CheckPointInRegion(evt.Longitude, evt.Latitude))
                {
                    KpiDataManager.AddStatData(reg.RegionName, reg, fi, eventData, this.curReportStyle.HasGridPerCell);
                    break;
                }
            }
        }

    }
}
