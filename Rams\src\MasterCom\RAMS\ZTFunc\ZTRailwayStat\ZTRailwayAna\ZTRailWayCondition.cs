﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRailWayCondition : BaseDialog
    {
        public ZTRailWayCondition()
        {
            InitializeComponent();
        }

        public RailWayZWCell zwCell { get; set; }
        private void btnSearch_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Excel|*.xlsx;*.xls";
            openDlg.Title = "请选择高铁专网小区表格清单";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                txtBox.Text = openDlg.FileName;
            }
        }

        private void readZWExcel(object obj)
        {
            string fileName = obj as string;
            WaitBox.ProgressPercent = 10;
            DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
            {
                try
                {
                    foreach (DataTable dt in ds.Tables)
                    {
                        dealZWCell(dt);
                    }
                }
                catch (Exception ex)
                {
                    WaitBox.Text = "读取数据失败，失败原因：" + ex.Message;
                    System.Threading.Thread.Sleep(2000);
                }
            }
            WaitBox.Close();
        }

        private void dealZWCell(DataTable dt)
        {
            if (dt.TableName == "4G专网信息表")
            {
                WaitBox.Text = "正在读取专网小区信息..." + dt.TableName;
                WaitBox.ProgressPercent += 40;
                foreach (DataRow dr in dt.Rows)
                {
                    string eci = dr["归属ECI"].ToString();
                    string cityName = dr["所属地市"].ToString();
                    int iCi;
                    if (Int32.TryParse(eci, out iCi))
                    {
                        zwCell.Add(-10000000, iCi, "LTE", cityName);
                    }
                }
            }
            else if (dt.TableName == "2G专网信息表")
            {
                WaitBox.Text = "正在读取专网小区信息..." + dt.TableName;
                WaitBox.ProgressPercent += 40;
                foreach (DataRow dr in dt.Rows)
                {
                    string lac = dr["归属LAC"].ToString();
                    string ci = dr["归属CI"].ToString();
                    string cityName = dr["所属地市"].ToString();
                    int iLac, iCi;
                    if (Int32.TryParse(lac, out iLac) && Int32.TryParse(ci, out iCi))
                    {
                        zwCell.Add(iLac, iCi, "GSM", cityName);
                    }
                }
            }
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            string file = txtBox.Text;
            if (!File.Exists(file))
            {
                MessageBox.Show(file + "\r\n不存在！", "提示");
                return;
            }
            zwCell = new RailWayZWCell();
            WaitBox.Show("正在读取专网小区信息...", readZWExcel, file);
            if (!zwCell.isExistData)
            {
                MessageBox.Show("读取专网小区信息表失败了！", "提示");
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnDownLoad_Click(object sender, EventArgs e)
        {
            List<ExportToExcelModel> lsData = new List<ExportToExcelModel>();
            ExportToExcelModel sheet4G = new ExportToExcelModel();
            sheet4G.SheetName = "4G专网信息表";
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.cellValues.Add("所属地市");
            nr.cellValues.Add("所属小区中文名");
            nr.cellValues.Add("归属ECI");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.cellValues.Add("韶关");
            nr.cellValues.Add("韶关武广01BBU01F-HLR-1");
            nr.cellValues.Add("181391361");
            rows.Add(nr);

            sheet4G.Data = rows;

            ExportToExcelModel sheet2G = new ExportToExcelModel();
            sheet2G.SheetName = "2G专网信息表";
            rows = new List<NPOIRow>();
            nr = new NPOIRow();
            nr.cellValues.Add("所属地市");
            nr.cellValues.Add("所属小区中文名");
            nr.cellValues.Add("归属LAC");
            nr.cellValues.Add("归属CI");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.cellValues.Add("韶关");
            nr.cellValues.Add("SHGGT韶关武广RU14-01");
            nr.cellValues.Add("9067");
            nr.cellValues.Add("32023");
            rows.Add(nr);

            sheet2G.Data = rows;

            lsData.Add(sheet4G);
            lsData.Add(sheet2G);
            MasterCom.Util.ExcelNPOIManager.ExportToExcelMore(lsData);
        }
    }
}
