﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.NewBlackBlock;
using System.Drawing;

namespace MasterCom.RAMS.Func
{
    public class CellGridBlockInfo
    {
        public int SN
        {
            get;
            set;
        }
        public string Type
        {
            get;
            set;
        }
        public int CityID
        {
            get;
            set;
        }
        public int TokenID
        {
            get;
            set;
        }
        public int LAC
        {
            get;
            set;
        }
        public int CI
        {
            get;
            set;
        }
        public int BlockID
        {
            get;
            set;
        }
        public DateTime Time
        {
            get;
            set;
        }
        public DateTime CreateTime
        {
            get;
            set;
        }
        public int EsStatus
        {
            get;
            set;
        }
        public List<CellGridInfo> CellGrids
        {
            get;
            set;
        }
        public List<CellGridAllInfo> CellGridAll
        {
            get;
            set;
        }

        public static string GetColumnSelSql()
        {
            string col = @"[iCityID]
                    ,[iTokenID]
                    ,[iLAC]
                    ,[iCI]
                    ,[iBlockID]
                    ,[iTime]
                    ,[iCreateTime]
                    ,[iESStatus]";
            return col;
        }
        public static CellGridBlockInfo FillFrom(Content content)
        {
            CellGridBlockInfo block = new CellGridBlockInfo();
            block.CityID = content.GetParamInt();
            block.TokenID = content.GetParamInt();
            block.LAC = content.GetParamInt();
            block.CI = content.GetParamInt();
            block.BlockID = content.GetParamInt();
            block.Time = Convert.ToDateTime("1970-01-01 8:00:00").AddSeconds((double)content.GetParamInt());
            block.CreateTime = Convert.ToDateTime("1970-01-01 8:00:00").AddSeconds((double)content.GetParamInt());
            block.EsStatus = content.GetParamInt();
            block.PrimaryType = content.GetParamString();
            block.SpecificType = content.GetParamString();
            block.Detail = content.GetParamString();
            block.Suggest = content.GetParamString();

            return block;
        }

        public string PrimaryType { get; set; }

        public string SpecificType { get; set; }

        public string Detail { get; set; }

        public string Suggest { get; set; }
    }           
    public class CellGridInfo
    {
        public int BlockID
        {
            get;
            set;
        }
        public int ID
        {
            get;
            set;
        }
        public int TlLongitude
        {
            get;
            set;
        }
        public int TlLatitude
        {
            get;
            set;
        }
        public int BrLongitude
        {
            get;
            set;
        }
        public int BrLatitude
        {
            get;
            set;
        }
        public int IsProb
        {
            get;
            set;
        }
        public Dictionary<uint, object> Extend
        {
            get;
            set;
        }
        public double AvgRSRP
        {
            get
            {
                uint key = 0x00010001;
                if (Extend == null || !Extend.ContainsKey(key))
                {
                    return 0.0;
                }
                double avg = 0.0;
                if (double.TryParse(Extend[key].ToString(), out avg))
                {
                    return avg;
                }
                return 0.0;
            }
        }
        public double MidLongitude
        {
            get
            {
                return ((double)TlLongitude + (double)BrLongitude) / 2;
            }
        }
        public double MidLatitude
        {
            get
            {
                return ((double)TlLatitude + (double)BrLatitude) / 2;
            }
        }
        public DbRect Bounds
        {
            get { return new DbRect((double)TlLongitude / 10000000, (double)BrLatitude / 10000000, (double)BrLongitude / 10000000, (double)TlLatitude / 10000000); }
        }
        public static string GetColumnSelSql()
        {
            string col = @"[iBlockID]
                    ,[iID]
                    ,[iTLLng]
                    ,[iTLLat]
                    ,[iBRLng]
                    ,[iBRLat]
                    ,[iIsProb]
                    ,[img]";
            return col;
        }
        public static CellGridInfo FillFrom(Content content)
        {
            CellGridInfo cg = new CellGridInfo();
            cg.BlockID = content.GetParamInt();
            cg.ID = content.GetParamInt();
            cg.TlLongitude = content.GetParamInt();
            cg.TlLatitude = content.GetParamInt();
            cg.BrLongitude = content.GetParamInt();
            cg.BrLatitude = content.GetParamInt();
            cg.IsProb = content.GetParamInt();
            byte[] bytes = content.GetParamBytes();
            if (bytes != null)
            {
                cg.Extend = KeyValueImageParser.FromImage(bytes);
            }

            return cg;
        }
    }

    public class CellGridAllInfo
    {
        public int ID
        {
            get;
            set;
        }
        public int LAC
        {
            get;
            set;
        }
        public int CI
        {
            get;
            set;
        }
        public int TlLongitude
        {
            get;
            set;
        }
        public int TlLatitude
        {
            get;
            set;
        }
        public int BrLongitude
        {
            get;
            set;
        }
        public int BrLatitude
        {
            get;
            set;
        }
        public int IsProb
        {
            get;
            set;
        }

        public LTECell LteCell
        {
            get;
            set;
        }

        public int Angle
        {
            get
            {
                if (this.LteCell != null)
                {
                    PointF oPoint = new PointF((float)this.LteCell.Longitude, (float)this.LteCell.Latitude);
                    PointF aPoint = new PointF((float)this.LteCell.EndPointLongitude, (float)this.LteCell.EndPointLatitude);
                    PointF bPoint = new PointF((float)this.MidLongitude / 10000000, (float)this.MidLatitude / 10000000);
                    int angle = (int)MathFuncs.CalAngle(oPoint, aPoint, bPoint);
                    return angle;
                }
                return 0;
            }
        }
        public Dictionary<uint, object> Extend
        {
            get;
            set;
        }
        public double AvgRSRP
        {
            get
            {
                uint key = 0x00010001;
                if (Extend == null || !Extend.ContainsKey(key))
                {
                    return 0.0;
                }
                double avg = 0.0;
                if (double.TryParse(Extend[key].ToString(), out avg))
                {
                    return avg;
                }
                return 0.0;
            }
        }
        public int InBlock
        {
            get;
            set;
        }
        public double MidLongitude
        {
            get
            {
                return ((double)TlLongitude + (double)BrLongitude) / 2;
            }
        }
        public double MidLatitude
        {
            get
            {
                return ((double)TlLatitude + (double)BrLatitude) / 2;
            }
        }
        public DbRect Bounds
        {
            get { return new DbRect((double)TlLongitude / 10000000, (double)BrLatitude / 10000000, (double)BrLongitude / 10000000, (double)TlLatitude / 10000000); }
        }

        public static string GetColumnSelSql()
        {
            string col = @"[iID]
                    ,[iLAC]
                    ,[iCI]
                    ,[iTLLng]
                    ,[iTLLat]
                    ,[iBRLng]
                    ,[iBRLat]
                    ,[iIsProb]
                    ,[img]";
            return col;
        }
        public static CellGridAllInfo FillFrom(Content content)
        {
            CellGridAllInfo cg = new CellGridAllInfo();
            cg.ID = content.GetParamInt();
            cg.LAC = content.GetParamInt();
            cg.CI = content.GetParamInt();
            cg.TlLongitude = content.GetParamInt();
            cg.TlLatitude = content.GetParamInt();
            cg.BrLongitude = content.GetParamInt();
            cg.BrLatitude = content.GetParamInt();
            cg.IsProb = content.GetParamInt();
            byte[] bytes = content.GetParamBytes();
            if (bytes != null)
            {
                cg.Extend = KeyValueImageParser.FromImage(bytes);
            }

            return cg;
        }
    }

    public class DIYSQLCellGridBlock : DIYSQLBase
    {
        private readonly List<CellGridBlockInfo> blocks = new List<CellGridBlockInfo>();
        private readonly string type;
        private int cityID { get; set; }
        public List<CellGridBlockInfo> Blocks
        {
            get { return blocks; }
        }
        public DIYSQLCellGridBlock(MainModel mainModel, string type, int cityID)
            : base(mainModel)
        {
            this.type = type;
            this.cityID = cityID;
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string tbName = "tb_prob_" + this.type + "_cell_grid_block";
            string strSql = @" select "
                        + CellGridBlockInfo.GetColumnSelSql()
                        + ",primarytype,specificType,detail,suggest from " + tbName;

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[12];
            types[0] = E_VType.E_Int;
            types[1] = E_VType.E_Int;
            types[2] = E_VType.E_Int;
            types[3] = E_VType.E_Int;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Int;
            types[7] = E_VType.E_Int;
            types[8] = E_VType.E_String;
            types[9] = E_VType.E_String;
            types[10] = E_VType.E_String;
            types[11] = E_VType.E_String;
            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellGridBlockInfo block = CellGridBlockInfo.FillFrom(package.Content);
                    blocks.Add(block);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        public override string Name
        {
            get { return "DIYSQLCellGridBlock"; }
        }
    }

    public class DIYSQLCellGrid : DIYSQLBase
    {
        private readonly Dictionary<int, List<CellGridInfo>> cellGrids = new Dictionary<int, List<CellGridInfo>>();
        private readonly string type;
        public Dictionary<int, List<CellGridInfo>> CellGrids
        {
            get { return cellGrids; }
        }
        public DIYSQLCellGrid(MainModel mainModel, string type)
            : base(mainModel)
        {
            this.type = type;
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string tbName = "tb_prob_" + this.type + "_cell_grid";
            string strSql = @" select "
                        + CellGridInfo.GetColumnSelSql()
                        + " from " + tbName;

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[8];
            types[0] = E_VType.E_Int;
            types[1] = E_VType.E_Int;
            types[2] = E_VType.E_Int;
            types[3] = E_VType.E_Int;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Int;
            types[7] = E_VType.E_VARYBIN;

            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellGridInfo cg = CellGridInfo.FillFrom(package.Content);
                    if (cellGrids.ContainsKey(cg.BlockID))
                    {
                        cellGrids[cg.BlockID].Add(cg);
                    }
                    else
                    {
                        cellGrids[cg.BlockID] = new List<CellGridInfo>();
                        cellGrids[cg.BlockID].Add(cg);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        public override string Name
        {
            get { return "DIYSQLCellGrid"; }
        }
    }

    public class DIYSQLCellGridType : DIYSQLBase
    {
        public List<string> Types { get; private set; } = new List<string>();
        protected override string getSqlTextString()
        {
            MainDB = true;
            string tbName = "tb_prob_cell_grid_block_token";
            string strSql = @" select strToken "
                        + " from " + tbName;

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[1];
            types[0] = E_VType.E_String;

            return types;
        }

        private string getData(Content content)
        {
            return content.GetParamString();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string type = getData(package.Content);
                    Types.Add(type);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        public override string Name
        {
            get { return "DIYSQLCellGridType"; }
        }
    }

    public class DIYSQLCellGridAll : DIYSQLBase
    {
        private readonly Dictionary<string, List<CellGridAllInfo>> cellGridAll = new Dictionary<string, List<CellGridAllInfo>>();
        private readonly string type;
        public Dictionary<string, List<CellGridAllInfo>> CellGridAll
        {
            get { return cellGridAll; }
        }
        public DIYSQLCellGridAll(MainModel mainModel, string type)
            : base(mainModel)
        {
            this.type = type;
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string tbName = "tb_prob_" + this.type + "_cell_grid_all";
            string strSql = @" select "
                        + CellGridAllInfo.GetColumnSelSql()
                        + " from " + tbName
                        + " order by iIsProb desc ";

            return strSql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[9];
            types[0] = E_VType.E_Int;
            types[1] = E_VType.E_Int;
            types[2] = E_VType.E_Int;
            types[3] = E_VType.E_Int;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Int;
            types[7] = E_VType.E_Int;
            types[8] = E_VType.E_VARYBIN;

            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            CellGridAllInfo cg = CellGridAllInfo.FillFrom(package.Content);
            string k = cg.LAC.ToString() + "-" + cg.CI.ToString();
            if (cellGridAll.ContainsKey(k))
            {
                cellGridAll[k].Add(cg);
            }
            else
            {
                cellGridAll[k] = new List<CellGridAllInfo>();
                cellGridAll[k].Add(cg);
            }
        }

        public override string Name
        {
            get { return "DIYSQLCellGrid"; }
        }
    }
}
