﻿using MasterCom.RAMS.Model.Interface;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class BtsFusionDataQuery_QH : BtsFusionDataQuery_XJ
    {
        public static BtsFusionInfo_QH GetFusionInfos(BtsWorkParam_QH btsWorkParamInfo
            , DateTime beginTime, DateTime endTime)
        {
            string cgiConditions = getCgiConditions(btsWorkParamInfo.CellWorkParams);
            QueryFusionPerfData_QH perfQuery = new QueryFusionPerfData_QH(beginTime, endTime, cgiConditions, btsWorkParamInfo.IsOutDoorBts);
            perfQuery.Query();

            QueryFusionMRData_QH mrQuery = new QueryFusionMRData_QH(beginTime, endTime, cgiConditions, btsWorkParamInfo.IsOutDoorBts);
            mrQuery.Query();

            QueryFusionAlarmData_QH alarmQuery = new QueryFusionAlarmData_QH(beginTime, endTime, btsWorkParamInfo.BtsName);
            alarmQuery.Query();

            BtsFusionInfo_QH btsFusionInfo = new BtsFusionInfo_QH(btsWorkParamInfo, beginTime, endTime);
            btsFusionInfo.CellPerfInfoDic = perfQuery.PerfInfoDic;
            btsFusionInfo.CellMRInfoDic = mrQuery.MRInfoDic;
            Dictionary<string, BtsAlarmDataBase> btsAlarmInfoDic;
            alarmQuery.AlarmInfoDic.TryGetValue(btsWorkParamInfo.BtsName, out btsAlarmInfoDic);
            btsFusionInfo.BtsAlarmInfoDic = btsAlarmInfoDic;

            return btsFusionInfo;
        }
    }

    public class QueryFusionPerfData_QH : QueryFusionPerfData_XJ
    {
        readonly bool isOutDoor;
        public QueryFusionPerfData_QH(DateTime beginTime, DateTime endTime, string cgiConditions, bool isOutDoor)
            : base(beginTime, endTime, cgiConditions)
        {
            this.isOutDoor = isOutDoor;
        }
        protected override CellPerfDataBase getInitCellPerfData()
        {
            return new CellPerfData_QH(isOutDoor);
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_qinghai_fusionPerf_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), cgiConditions);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[15];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i] = E_VType.E_Float;
            return arr;
        }
    }
    public class QueryFusionMRData_QH : QueryFusionMRData_XJ
    {
        readonly bool isOutDoor;
        public QueryFusionMRData_QH(DateTime beginTime, DateTime endTime, string cgiConditions, bool isOutDoor)
            : base(beginTime, endTime, cgiConditions)
        {
            this.isOutDoor = isOutDoor;
        }
        protected override CellMRDataBase getInitCellPerfData()
        {
            return new CellMRData_QH(isOutDoor);
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_qinghai_fusionMr_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), cgiConditions);
        }
    }
    public class QueryFusionAlarmData_QH : QueryFusionAlarmData_XJ
    {
        public QueryFusionAlarmData_QH(DateTime beginTime, DateTime endTime, string btsNameConditions)
            : base(beginTime, endTime, btsNameConditions)
        {
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_qinghai_fusionAlarm_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), btsNameConditions);
        }
        protected override BtsAlarmDataBase getInitBtsAlarmData()
        {
            return new BtsAlarmData_QH();
        }
    }
}
