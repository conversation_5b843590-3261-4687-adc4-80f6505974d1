using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace MasterCom.Util
{
    public partial class ComboboxSelectForm : Form
    {
        public ComboboxSelectForm(string title,string showName,List<object> dropDownList)
        {
            InitializeComponent();
            comboBox.Items.Clear();
            this.Text = title;
            lbShowName.Text = showName;
            if (dropDownList != null && dropDownList.Count > 0)
            {
                foreach (object obj in dropDownList)
                {
                    comboBox.Items.Add(obj);
                }
                comboBox.SelectedIndex = 0;
            }
        }

        public object SelectedItem
        {
            get { return comboBox.SelectedItem; }
        }
    }
}