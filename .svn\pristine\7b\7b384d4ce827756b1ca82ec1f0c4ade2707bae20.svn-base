﻿namespace MasterCom.MTGis
{
    partial class CommonCustBaseLayerSetProperties
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.cbxRange = new System.Windows.Forms.CheckBox();
            this.cbxVisible = new System.Windows.Forms.CheckBox();
            this.btnScaleCurMin = new System.Windows.Forms.Button();
            this.btnScaleCurMax = new System.Windows.Forms.Button();
            this.txtRangMin = new System.Windows.Forms.TextBox();
            this.txtRangMax = new System.Windows.Forms.TextBox();
            this.最小可见范围 = new System.Windows.Forms.Label();
            this.最大可见范围 = new System.Windows.Forms.Label();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.trackBarAlpha = new System.Windows.Forms.TrackBar();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarAlpha)).BeginInit();
            this.SuspendLayout();
            // 
            // cbxRange
            // 
            this.cbxRange.AutoSize = true;
            this.cbxRange.Location = new System.Drawing.Point(25, 60);
            this.cbxRange.Name = "cbxRange";
            this.cbxRange.Size = new System.Drawing.Size(96, 16);
            this.cbxRange.TabIndex = 16;
            this.cbxRange.Text = "指定可视范围";
            this.cbxRange.UseVisualStyleBackColor = true;
            this.cbxRange.CheckedChanged += new System.EventHandler(this.cbxRange_CheckedChanged);
            // 
            // cbxVisible
            // 
            this.cbxVisible.AutoSize = true;
            this.cbxVisible.Location = new System.Drawing.Point(25, 20);
            this.cbxVisible.Name = "cbxVisible";
            this.cbxVisible.Size = new System.Drawing.Size(48, 16);
            this.cbxVisible.TabIndex = 17;
            this.cbxVisible.Text = "可视";
            this.cbxVisible.UseVisualStyleBackColor = true;
            this.cbxVisible.CheckedChanged += new System.EventHandler(this.cbxVisible_CheckedChanged);
            // 
            // btnScaleCurMin
            // 
            this.btnScaleCurMin.Location = new System.Drawing.Point(251, 105);
            this.btnScaleCurMin.Name = "btnScaleCurMin";
            this.btnScaleCurMin.Size = new System.Drawing.Size(75, 23);
            this.btnScaleCurMin.TabIndex = 46;
            this.btnScaleCurMin.Text = "当前比例";
            this.btnScaleCurMin.UseVisualStyleBackColor = true;
            this.btnScaleCurMin.Click += new System.EventHandler(this.btnScaleCurMin_Click);
            // 
            // btnScaleCurMax
            // 
            this.btnScaleCurMax.Location = new System.Drawing.Point(251, 76);
            this.btnScaleCurMax.Name = "btnScaleCurMax";
            this.btnScaleCurMax.Size = new System.Drawing.Size(75, 23);
            this.btnScaleCurMax.TabIndex = 45;
            this.btnScaleCurMax.Text = "当前比例";
            this.btnScaleCurMax.UseVisualStyleBackColor = true;
            this.btnScaleCurMax.Click += new System.EventHandler(this.btnScaleCurMax_Click);
            // 
            // txtRangMin
            // 
            this.txtRangMin.Enabled = false;
            this.txtRangMin.Location = new System.Drawing.Point(127, 104);
            this.txtRangMin.Name = "txtRangMin";
            this.txtRangMin.Size = new System.Drawing.Size(100, 21);
            this.txtRangMin.TabIndex = 43;
            this.txtRangMin.TextChanged += new System.EventHandler(this.txtRangMin_TextChanged);
            // 
            // txtRangMax
            // 
            this.txtRangMax.Enabled = false;
            this.txtRangMax.Location = new System.Drawing.Point(127, 76);
            this.txtRangMax.Name = "txtRangMax";
            this.txtRangMax.Size = new System.Drawing.Size(100, 21);
            this.txtRangMax.TabIndex = 44;
            this.txtRangMax.TextChanged += new System.EventHandler(this.txtRangMax_TextChanged);
            // 
            // 最小可见范围
            // 
            this.最小可见范围.AutoSize = true;
            this.最小可见范围.Location = new System.Drawing.Point(44, 107);
            this.最小可见范围.Name = "最小可见范围";
            this.最小可见范围.Size = new System.Drawing.Size(83, 12);
            this.最小可见范围.TabIndex = 41;
            this.最小可见范围.Text = "最小可见范围:";
            // 
            // 最大可见范围
            // 
            this.最大可见范围.AutoSize = true;
            this.最大可见范围.Location = new System.Drawing.Point(44, 84);
            this.最大可见范围.Name = "最大可见范围";
            this.最大可见范围.Size = new System.Drawing.Size(83, 12);
            this.最大可见范围.TabIndex = 42;
            this.最大可见范围.Text = "最大可见范围:";
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.SystemColors.ControlDark;
            this.panel1.Location = new System.Drawing.Point(25, 66);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(398, 1);
            this.panel1.TabIndex = 40;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(100, 171);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 54;
            this.label3.Text = "透明";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(243, 171);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 53;
            this.label2.Text = "不透明";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(22, 142);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 52;
            this.label1.Text = "透明度设置：";
            // 
            // trackBarAlpha
            // 
            this.trackBarAlpha.Location = new System.Drawing.Point(96, 139);
            this.trackBarAlpha.Maximum = 255;
            this.trackBarAlpha.Name = "trackBarAlpha";
            this.trackBarAlpha.Size = new System.Drawing.Size(189, 45);
            this.trackBarAlpha.TabIndex = 51;
            this.trackBarAlpha.TickFrequency = 10;
            this.trackBarAlpha.Value = 255;
            this.trackBarAlpha.Scroll += new System.EventHandler(this.trackBarAlpha_Scroll);
            // 
            // CommonCustLayerSetProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.trackBarAlpha);
            this.Controls.Add(this.cbxRange);
            this.Controls.Add(this.btnScaleCurMin);
            this.Controls.Add(this.btnScaleCurMax);
            this.Controls.Add(this.txtRangMin);
            this.Controls.Add(this.txtRangMax);
            this.Controls.Add(this.最小可见范围);
            this.Controls.Add(this.最大可见范围);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.cbxVisible);
            this.Name = "CommonCustLayerSetProperties";
            this.Size = new System.Drawing.Size(450, 196);
            ((System.ComponentModel.ISupportInitialize)(this.trackBarAlpha)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.CheckBox cbxRange;
        private System.Windows.Forms.CheckBox cbxVisible;
        private System.Windows.Forms.Button btnScaleCurMin;
        private System.Windows.Forms.Button btnScaleCurMax;
        private System.Windows.Forms.TextBox txtRangMin;
        private System.Windows.Forms.TextBox txtRangMax;
        private System.Windows.Forms.Label 最小可见范围;
        private System.Windows.Forms.Label 最大可见范围;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TrackBar trackBarAlpha;

    }
}
