﻿using System;
using System.Collections.Generic;

namespace MasterCom.RAMS.Model.Interface
{
    public enum E_VType
    {
        E_Byte = 1,
        E_Short = 2,
        E_UShort = 3,
        E_Float = 5,
        E_String = 6,
        E_IntFloat =7,
        E_Int = 8,
        E_VARYBIN = 10,
        E_ImgString = 12,
        E_UInt64 = 13,
        E_Int64 = 14
    }

    /// <summary>
    /// 表结构定义字段
    /// </summary>
    public class ColumnDefItem
    {
        /// <summary>
        /// imageID
        /// </summary>
        public int imgID { get; set; }
        /// <summary>
        /// paraid
        /// </summary>
        public int paraID { get; set; }
        /// <summary>
        /// 表id
        /// </summary>
        public int tableID { get; set; }
        /// <summary>
        /// 表名称
        /// </summary>
        public string tableName { get; set; }
        /// <summary>
        /// 数组的最大个数
        /// </summary>
        public int maxArrCount { get; set; }
        /// <summary>
        /// 数据类型
        /// </summary>
        public E_VType vType { get; set; }
        /// <summary>
        /// 异常值
        /// </summary>
        public string nullValue { get; set; }
        /// <summary>
        /// 表中相应字段名称
        /// </summary>
        public string tableColumnName { get; set; }
        /// <summary>
        /// 是否固定
        /// </summary>
        public bool fix { get; set; }
        /// <summary>
        /// from
        /// </summary>
        public int posFrom { get; set; }
        /// <summary>
        /// to
        /// </summary>
        public int posTo { get; set; }
        /// <summary>
        /// 界面显示名称
        /// </summary>
        public string showName { get; set; }

        public string ShowArrayName { get; set; }

        private int showArrayIndex = -1;
        public int ShowArrayIndex {
            get { return showArrayIndex; }
        }

        internal static ColumnDefItem FillFrom(Net.Content c)
        {
            ColumnDefItem item = new ColumnDefItem();
            item.imgID = c.GetParamInt();
            item.paraID = c.GetParamInt();
            item.tableID = c.GetParamInt();
            item.tableName = string.Intern(c.GetParamString());
            item.maxArrCount = c.GetParamInt();
            item.vType = (E_VType)c.GetParamInt();
            item.nullValue = c.GetParamString();
            item.tableColumnName = c.GetParamString();
            item.fix = c.GetParamInt() == 1;
            item.posFrom = c.GetParamInt();
            item.posTo = c.GetParamInt();
            item.showName = c.GetParamString();
            item.recalcShowArray();
            return item;
        }

        internal static ColumnDefItem FillFrom(string[] str)
        {
            int idx = 0;
            ColumnDefItem item = new ColumnDefItem();
            item.imgID = Convert.ToInt32(str[idx++]);
            item.paraID = Convert.ToInt32(str[idx++]);
            item.tableID = Convert.ToInt32(str[idx++]);
            item.tableName = string.Intern(str[idx++].ToLower().Trim());
            item.maxArrCount = Convert.ToInt32(str[idx++]);
            item.vType = (E_VType)Convert.ToInt32(str[idx++]);
            item.nullValue = str[idx++].Trim();
            if(item.vType == E_VType.E_String ||
                item.vType == E_VType.E_VARYBIN ||
                item.vType == E_VType.E_ImgString)
            {
                item.nullValue = "NullText";
            }
            item.tableColumnName = str[idx++].ToLower().Trim();
            item.fix = Convert.ToInt32(str[idx++]) == 1;
            item.posFrom = Convert.ToInt32(str[idx++]);
            item.posTo = Convert.ToInt32(str[idx++]);
            item.showName = str[idx].Trim();
            item.recalcShowArray();
            return item;
        }
        /// <summary>
        /// 解析showName若为数组则取出
        /// </summary>
        private void recalcShowArray()
        {
            if(showName!=null)
            {
                int pos = showName.IndexOf('[');
                if(pos!=-1)
                {
                    int pos2 = showName.IndexOf(']');
                    if(pos2>pos)
                    {
                        ShowArrayName = showName.Substring(0, pos);
                        string arrStr = showName.Substring(pos + 1, pos2 - pos-1);
                        int.TryParse(arrStr, out showArrayIndex);
                    }
                }
            }
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["ImgID"] = imgID;
                param["ParaID"] = paraID;
                param["TableID"] = tableID;
                return param;
            }
        }
        public string GetTriIdStr()
        {
            return imgID + "," + paraID + "," + tableID;
        }
        public string GetTriIdStrIgnoreTable()
        {
            return imgID + "," + paraID + ",-1";
        }
        public override string ToString()
        {
            return showName;
        }
    }
}
