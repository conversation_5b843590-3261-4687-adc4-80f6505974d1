﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.ES.Data;
using DevExpress.XtraEditors;

namespace MasterCom.ES.UI
{
    public partial class ESCommanderEditorDlg : BaseFormStyle
    {
        CryptionData cdata = new CryptionData();
        public ESCommanderEditorDlg()
        {
            InitializeComponent();
        }

        internal void InitShow(Dictionary<string,ESOwnFuncCommander> dic)
        {
            cbxESOwnFunc.Items.Clear();
            foreach(ESOwnFuncCommander cmd in dic.Values)
            {
                cbxESOwnFunc.Items.Add(cmd);
            }
        }

        internal Dictionary<string, ESOwnFuncCommander> GetCommanderDic()
        {
            Dictionary<string, ESOwnFuncCommander> dic = new Dictionary<string, ESOwnFuncCommander>();
            foreach(object item in cbxESOwnFunc.Items)
            {
                ESOwnFuncCommander cmd = item as ESOwnFuncCommander;
                dic[cmd.desc] = cmd;
            }
            return dic;
        }

        private void btnSaveCommand_Click(object sender, EventArgs e)
        {
            ESOwnFuncCommander commander = new ESOwnFuncCommander();
            commander.funcName = tbxFuncName.Text.Trim();
            commander.desc = tbxDesc.Text.Trim();
            commander.descriptionNote = tbxNoteDesc.Text.Trim();
            commander.codeString = cdata.EncryptionStringData(tbxCodeInput.Text);
            commander._classReady = false;
            commander._hasError = false;
            string retStringErr = commander.initFuncClass();
            if (!commander._classReady)
            {
                XtraMessageBox.Show("Check Error" + retStringErr);
                return;
            }
            foreach (ESOwnFuncCommander cmd in cbxESOwnFunc.Items)
            {
                if (cmd.funcName == commander.funcName || cmd.desc == commander.desc)
                {
                    DialogResult res = XtraMessageBox.Show(this, "该操作函数" + commander.funcName + "," + commander.desc + "存在同名,是否替换？", "保存", MessageBoxButtons.OKCancel);
                    if (res == DialogResult.OK)
                    {
                        cmd.funcName = commander.funcName;
                        cmd.desc = commander.desc;
                        cmd.codeString = commander.codeString;
                        cmd.descriptionNote = commander.descriptionNote;
                        cmd._classReady = false;
                        cmd._hasError = false;
                        cmd.clzzInst = null;
                        return;
                    }
                }
            }
            cbxESOwnFunc.Items.Add(commander);
            cbxESOwnFunc.SelectedItem = commander;
        }

        private void cbxESOwnFunc_SelectedIndexChanged(object sender, EventArgs e)
        {
            ESOwnFuncCommander cmder = cbxESOwnFunc.SelectedItem as ESOwnFuncCommander;
            if(cmder!=null)
            {
                tbxDesc.Text = cmder.desc;
                tbxFuncName.Text = cmder.funcName;
                tbxNoteDesc.Text = cmder.descriptionNote;
                if(tbxPwdShow.Text == "yuehaitao")
                {
                    tbxCodeInput.Text = cdata.DecryptionStringdata(cmder.codeString);
                }
                else
                {
                    tbxCodeInput.Text = "";
                }
                
            }
        }

        private void cbxCodeDetail_CheckedChanged(object sender, EventArgs e)
        {
            tbxPwdShow.Visible = cbxCodeDetail.Checked;
        }

        private void tbxPwdShow_TextChanged(object sender, EventArgs e)
        {
            if(tbxPwdShow.Text=="yuehaitao")
            {
                tbxPwdShow.Visible = false;
                cbxCodeDetail.Visible = false;
            }
        }
    }
}