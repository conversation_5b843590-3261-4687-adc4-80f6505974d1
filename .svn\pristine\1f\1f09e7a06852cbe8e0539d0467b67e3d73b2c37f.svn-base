﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Collections;
using System.IO;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class CombineGridUnitExportDlg : BaseDialog
    {
        bool dataValid = true;
        public CombineGridUnitExportDlg(bool combineGrid)
        {
            InitializeComponent();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("文件说明：");
            sb.AppendLine("第一行为标题；");
            sb.AppendLine("各列依次为基站名、经度、纬度。");
            edtPS.Text = sb.ToString();

            lblCoverRate1.Visible = lblCoverRate2.Visible = edtCoverRate.Visible = combineGrid;
            chkSetColor_CheckedChanged(null, null);
        }

        public void getCondition(ref int coverRadius, ref int coverRate, ref bool fixColor, ref Color colorSelected)
        {
            coverRadius = (int)edtCoverRadius.Value;
            coverRate = (int)edtCoverRate.Value;
            fixColor = chkSetColor.Checked;
            colorSelected = cbxColorSelect.Color;
        }

        public void getCondition(ref int coverRadius, ref bool fixColor, ref Color colorSelected)
        {
            coverRadius = (int)edtCoverRadius.Value;
            fixColor = chkSetColor.Checked;
            colorSelected = cbxColorSelect.Color;
        }

        private void edtFile_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog openFileDlg = new OpenFileDialog();
            openFileDlg.Filter = "Excel文件(*.xls)|*.xls";
            openFileDlg.FilterIndex = 1;
            openFileDlg.RestoreDirectory = true;
            if (openFileDlg.ShowDialog(this) == DialogResult.OK)
            {
                edtFile.Text = openFileDlg.FileName;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (edtFile.Text.Trim().Equals(""))
            {
                XtraMessageBox.Show("请选择文件。");
                DialogResult = DialogResult.Retry;
                return;
            }
            if (!File.Exists(edtFile.Text.Trim()))
            {
                XtraMessageBox.Show("文件不存在，请重新选择。");
                DialogResult = DialogResult.Retry;
                return;
            }
            WaitBox.Show("正在读取Excel数据...", readExcel);
            if (!dataValid)
            {
                dataValid = true;
                DialogResult = DialogResult.Retry;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void readExcel()
        {
            try
            {
                WaitBox.Text = "开始读取Excel...";
                ArrayList sheetList = ExcelTools.GetSheetNameList(edtFile.Text.Trim());
                DataTable dataTable = ExcelTools.ExcelToDataTable(edtFile.Text.Trim(), sheetList[0].ToString());
                List<int> badRows = new List<int>();
                MapGridLayer gridLayer = MainModel.GetInstance().MainForm.GetMapForm().GetGridShowLayer();
                gridLayer.BTSLonLatList.Clear();
                WaitBox.Text = "验证Excel数据...";
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    double longitude = 0;
                    double latitude = 0;
                    if (!double.TryParse(dataTable.Rows[i][1].ToString(), out longitude))
                    {
                        badRows.Add(i);
                        continue;
                    }
                    if (!double.TryParse(dataTable.Rows[i][2].ToString(), out latitude))
                    {
                        badRows.Add(i);
                        continue;
                    }
                    MapGridLayer.BTSLonLat btsLonLat = new MapGridLayer.BTSLonLat(dataTable.Rows[i][0].ToString(), longitude, latitude);
                    gridLayer.BTSLonLatList.Add(btsLonLat);
                    WaitBox.ProgressPercent = (int)(100.0 * (i + 1) / dataTable.Rows.Count);
                }
                if (badRows.Count > 0)
                {
                    StringBuilder badRowString = new StringBuilder();
                    //string badRowString = "";
                    foreach (int badRow in badRows)
                    {
                        if (badRowString.Length  > 0)
                        {
                            badRowString .Append( ",");
                        }
                        badRowString.Append(badRow.ToString());
                    }
                    if (XtraMessageBox.Show("以下行数据有误：" + badRowString + "。是否继续？", "警告", MessageBoxButtons.YesNo) == DialogResult.No)
                    {
                        dataValid = false;
                        return;
                    }
                }
            }
            catch (System.Exception e)
            {
                XtraMessageBox.Show("读取Excel出错：" + e.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void chkSetColor_CheckedChanged(object sender, EventArgs e)
        {
            cbxColorSelect.Enabled = chkSetColor.Checked;
        }
    }
}
