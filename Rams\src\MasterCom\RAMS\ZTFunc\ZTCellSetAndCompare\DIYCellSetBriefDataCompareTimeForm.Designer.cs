﻿namespace MasterCom.RAMS.Model
{
    partial class DIYCellSetBriefDataCompareTimeForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelNow = new System.Windows.Forms.Label();
            this.labelOld = new System.Windows.Forms.Label();
            this.dTimeNowFrom = new System.Windows.Forms.DateTimePicker();
            this.labelNTo = new System.Windows.Forms.Label();
            this.dTimeNowTo = new System.Windows.Forms.DateTimePicker();
            this.dTimeOldFrom = new System.Windows.Forms.DateTimePicker();
            this.labelOTo = new System.Windows.Forms.Label();
            this.dTimeOldTo = new System.Windows.Forms.DateTimePicker();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.radBtnNearCell = new System.Windows.Forms.CheckBox();
            this.radBtnMaiCell = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelNow
            // 
            this.labelNow.AutoSize = true;
            this.labelNow.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelNow.Location = new System.Drawing.Point(26, 26);
            this.labelNow.Name = "labelNow";
            this.labelNow.Size = new System.Drawing.Size(65, 12);
            this.labelNow.TabIndex = 0;
            this.labelNow.Text = "当前时段：";
            // 
            // labelOld
            // 
            this.labelOld.AutoSize = true;
            this.labelOld.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelOld.Location = new System.Drawing.Point(26, 58);
            this.labelOld.Name = "labelOld";
            this.labelOld.Size = new System.Drawing.Size(65, 12);
            this.labelOld.TabIndex = 1;
            this.labelOld.Text = "前一时段：";
            // 
            // dTimeNowFrom
            // 
            this.dTimeNowFrom.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dTimeNowFrom.Location = new System.Drawing.Point(91, 19);
            this.dTimeNowFrom.Name = "dTimeNowFrom";
            this.dTimeNowFrom.Size = new System.Drawing.Size(111, 21);
            this.dTimeNowFrom.TabIndex = 2;
            // 
            // labelNTo
            // 
            this.labelNTo.AutoSize = true;
            this.labelNTo.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelNTo.Location = new System.Drawing.Point(215, 25);
            this.labelNTo.Name = "labelNTo";
            this.labelNTo.Size = new System.Drawing.Size(17, 12);
            this.labelNTo.TabIndex = 3;
            this.labelNTo.Text = "至";
            // 
            // dTimeNowTo
            // 
            this.dTimeNowTo.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dTimeNowTo.Location = new System.Drawing.Point(238, 19);
            this.dTimeNowTo.Name = "dTimeNowTo";
            this.dTimeNowTo.Size = new System.Drawing.Size(111, 21);
            this.dTimeNowTo.TabIndex = 4;
            // 
            // dTimeOldFrom
            // 
            this.dTimeOldFrom.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dTimeOldFrom.Location = new System.Drawing.Point(91, 53);
            this.dTimeOldFrom.Name = "dTimeOldFrom";
            this.dTimeOldFrom.Size = new System.Drawing.Size(111, 21);
            this.dTimeOldFrom.TabIndex = 5;
            // 
            // labelOTo
            // 
            this.labelOTo.AutoSize = true;
            this.labelOTo.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelOTo.Location = new System.Drawing.Point(215, 58);
            this.labelOTo.Name = "labelOTo";
            this.labelOTo.Size = new System.Drawing.Size(17, 12);
            this.labelOTo.TabIndex = 6;
            this.labelOTo.Text = "至";
            // 
            // dTimeOldTo
            // 
            this.dTimeOldTo.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dTimeOldTo.Location = new System.Drawing.Point(238, 53);
            this.dTimeOldTo.Name = "dTimeOldTo";
            this.dTimeOldTo.Size = new System.Drawing.Size(111, 21);
            this.dTimeOldTo.TabIndex = 7;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(178, 179);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(274, 179);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 9;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // radBtnNearCell
            // 
            this.radBtnNearCell.AutoSize = true;
            this.radBtnNearCell.Location = new System.Drawing.Point(163, 26);
            this.radBtnNearCell.Name = "radBtnNearCell";
            this.radBtnNearCell.Size = new System.Drawing.Size(62, 18);
            this.radBtnNearCell.TabIndex = 12;
            this.radBtnNearCell.Text = "邻小区";
            this.radBtnNearCell.UseVisualStyleBackColor = true;
            // 
            // radBtnMaiCell
            // 
            this.radBtnMaiCell.AutoSize = true;
            this.radBtnMaiCell.Location = new System.Drawing.Point(63, 26);
            this.radBtnMaiCell.Name = "radBtnMaiCell";
            this.radBtnMaiCell.Size = new System.Drawing.Size(74, 18);
            this.radBtnMaiCell.TabIndex = 11;
            this.radBtnMaiCell.Text = "主服小区";
            this.radBtnMaiCell.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radBtnMaiCell);
            this.groupBox1.Controls.Add(this.radBtnNearCell);
            this.groupBox1.Location = new System.Drawing.Point(28, 92);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(319, 62);
            this.groupBox1.TabIndex = 13;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "查询类型";
            // 
            // DIYCellSetBriefDataCompareTimeForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(376, 223);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.dTimeOldTo);
            this.Controls.Add(this.labelOTo);
            this.Controls.Add(this.dTimeOldFrom);
            this.Controls.Add(this.dTimeNowTo);
            this.Controls.Add(this.labelNTo);
            this.Controls.Add(this.dTimeNowFrom);
            this.Controls.Add(this.labelOld);
            this.Controls.Add(this.labelNow);
            this.Name = "DIYCellSetBriefDataCompareTimeForm";
            this.Text = "小区集对比查询条件设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label labelNow;
        private System.Windows.Forms.Label labelOld;
        private System.Windows.Forms.DateTimePicker dTimeNowFrom;
        private System.Windows.Forms.Label labelNTo;
        private System.Windows.Forms.DateTimePicker dTimeNowTo;
        private System.Windows.Forms.DateTimePicker dTimeOldFrom;
        private System.Windows.Forms.Label labelOTo;
        private System.Windows.Forms.DateTimePicker dTimeOldTo;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.CheckBox radBtnNearCell;
        private System.Windows.Forms.CheckBox radBtnMaiCell;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}