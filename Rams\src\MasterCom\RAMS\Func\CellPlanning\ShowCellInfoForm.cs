﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ShowCellInfoForm : ShowFuncForm
    {
        public ShowCellInfoForm(MainModel mm)
            : base(mm)
        { }
        FindCellForm findCellForm = null;
        protected override void showForm()
        {
            if (findCellForm == null || findCellForm.IsDisposed)
            {
                findCellForm = new FindCellForm(MainModel);
            }
            if (!findCellForm.Visible)
            {
                findCellForm.Show(MainModel.MainForm);
            }
        }

        public override string Name
        {
            get { return "呈现小区信息窗口"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19016, this.Name);
        }
    }
}
