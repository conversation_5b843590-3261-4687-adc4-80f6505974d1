﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.Util.UiEx;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP.BatchImport
{
    public abstract class BatchImportQueryBase : QueryBase
    {
        protected BatchImportQueryBase(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "工单修改批量导入"; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new BatchImportSettingForm();
            }
            return setForm.ShowDialog() != System.Windows.Forms.DialogResult.Cancel;
        }

        protected override void query()
        {
            try
            {
                WaitTextBox.Show("正在加载工单信息...", LoadInThread);
                if (errEx != null)
                {
                    MessageBox.Show(errEx.Message, this.Name, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                bool isSubmit = setForm.DialogResult == DialogResult.Yes;
                foreach (BatchImportItem item in itemList)
                {
                    ValidateImportItem(item, isSubmit);
                }

                BatchImportConfirmForm confirmForm = new BatchImportConfirmForm();
                confirmForm.FillData(itemList);
                if (confirmForm.ShowDialog() != DialogResult.OK)
                {
                    return;
                }

                WaitTextBox.Show("正在将修改写入工单库...", CommitInThread);
                BatchImportResultForm resultForm = MainModel.CreateResultForm(typeof(BatchImportResultForm)) as BatchImportResultForm;
                resultForm.FillData(itemList);
                resultForm.Show();
            }
            finally
            {
                controller = null;
                itemList = null;
                setForm = null;
                errEx = null;
                nopUserDic = null;
            }
        }

        protected Dictionary<string, string> NopUserDic
        {
            get
            {
                if (nopUserDic == null)
                {
                    nopUserDic = new Dictionary<string, string>();
                    nopUserDic.Add("T1_测试数据管理", "T1");
                    nopUserDic.Add("T3_疑难问题处理", "T3");
                    nopUserDic.Add("T21_片区优化组", "T2_1");
                    nopUserDic.Add("T22_参数维护组", "T2_2");
                    nopUserDic.Add("T23_室分维护组", "T2_3");
                    nopUserDic.Add("T24_无线规划组", "T2_4");
                    nopUserDic.Add("T25_干线优化组", "T2_5");
                    nopUserDic.Add("武汉", "wuhan");
                    nopUserDic.Add("黄石", "huangshi");
                    nopUserDic.Add("襄阳", "xiangyang");
                    nopUserDic.Add("十堰", "shiyan");
                    nopUserDic.Add("荆州", "jingzhou");
                    nopUserDic.Add("宜昌", "yichang");
                    nopUserDic.Add("荆门", "jingmen");
                    nopUserDic.Add("鄂州", "ezhou");
                    nopUserDic.Add("孝感", "xiaogan");
                    nopUserDic.Add("黄冈", "huanggang");
                    nopUserDic.Add("咸宁", "xianning");
                    nopUserDic.Add("随州", "suizhou");
                    nopUserDic.Add("恩施", "enshi");
                    nopUserDic.Add("江汉", "jianghan");
                }
                return nopUserDic;
            }
        }

        protected abstract TaskOrderQuerier CreateTaskQuerier();

        protected virtual void ValidateImportItem(BatchImportItem item, bool isSubmit)
        {
            item.IsValid = true;

            string fieldName = "用户组别";
            string taskUser = item.NopTask.GetValue(fieldName) as string;
            string targetUser = item.FileRow[fieldName] as string;
            if (!NopUserDic.ContainsKey(taskUser) || NopUserDic[taskUser] != MainModel.User.LoginName)
            {
                item.IsValid = false;
                item.InvalidReason = "工单用户组别与当前登录名不匹配";
                return;
            }
            if (!NopUserDic.ContainsKey(targetUser))
            {
                item.IsValid = false;
                item.InvalidReason = "指定的用户组别无效";
                return;
            }

            item.NopTask.AddValue(fieldName, targetUser);
            item.NopTask.AddValue("鉴权组别", targetUser);
        }

        private void LoadInThread()
        {
            try
            {
                if (controller == null)
                {
                    controller = new BatchImportController(MainModel);
                }
                itemList = controller.Load(setForm.FileName, CreateTaskQuerier());
            }
            catch (Exception ex)
            {
                errEx = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitTextBox.Close();
            }
        }

        private void CommitInThread()
        {
            try
            {
                if (controller == null)
                {
                    controller = new BatchImportController(MainModel);
                }
                controller.Import(itemList);
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitTextBox.Close();
            }
        }

        private Exception errEx = null;

        private BatchImportController controller = null;

        private List<BatchImportItem> itemList = null;

        private BatchImportSettingForm setForm = null;

        private Dictionary<string, string> nopUserDic = null;
    }
}
