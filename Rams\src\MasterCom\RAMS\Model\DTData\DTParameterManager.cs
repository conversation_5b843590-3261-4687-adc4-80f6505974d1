using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;
using System.IO;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model
{
    public class DTParameterManager
    {
        public static DTParameterManager GetInstance()
        {
            return instance;
        }

        private static DTParameterManager instance = new DTParameterManager();

        private DTParameterManager()
        {
            load();
        }

        public DTParameterInfo GetParameterInfo(String name)
        {
            DTParameterInfo ret = null;
            if (!nameMap.TryGetValue(name, out ret))
            {
                return null;
            }
            return ret;
        }

        public DTParameter GetParameter(String name, int arrayIndex)
        {
            DTParameterInfo info = GetParameterInfo(name);
            if (info != null)
            {
                return info[arrayIndex];
            }
            return null;
        }

        public DTParameter GetParameter(String name)
        {
            return GetParameter(name, 0);
        }

        public bool CanConvertToFloat(object value, DTParameterValueType valueType)
        {
            return value != null && valueType != DTParameterValueType.String;
        }

        public float ConvertToFloat(object value, DTParameterValueType valueType)
        {
            if(value is string)
            {
                return 0F;
            }
            float vv;
            if(float.TryParse(value.ToString(), out vv))
            {
                return vv;
            }
            return 0F;
            /*
            if (valueType == DTParameterValueType.Byte)
            {
                return (float)(byte)value;
            }
            else if (valueType == DTParameterValueType.Short)
            {
                return (float)(short)value;
            }
            else if (valueType == DTParameterValueType.Int)
            {
                return (float)(int)value;
            }
            else if (valueType == DTParameterValueType.Long)
            {
                return (float)(long)value;
            }
            else if (valueType == DTParameterValueType.Float)
            {
                return (float)value;
            }
            else if (valueType == DTParameterValueType.Double)
            {
                return (float)(double)value;
            }*/
        }

        public void addInfo(DTParameterInfo info)  //private -> public
        {
            if (nameMap.ContainsKey(info.Name))
            {
                return;
            }
            nameMap[info.Name] = info;
        }

        public void removeInfo(DTParameterInfo info)   //new
        {
            if (nameMap.ContainsKey(info.Name))
            {
                nameMap.Remove(info.Name);
            }
        }

        public void editInfo(DTParameterInfo info, string newName)   //new
        {
            if (nameMap.ContainsKey(info.Name))
            {
                nameMap.Remove(info.Name);
                info.Name = newName;
                nameMap.Add(newName, info);
            }
        }

        public bool isValid(string infoName)   //new add
        {
            return !nameMap.ContainsKey(infoName);
        }

        private void load()
        {
            if (File.Exists(configFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(configFileName);
                List<object> list = configFile.GetItemValue("Common", "DTParameterInfos", DTParameterInfo.GetItemValue) as List<object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        addInfo((DTParameterInfo)value);
                    }
                }
                DTParameterInfo info = new DTParameterInfo(0, "DistrictID", 1, DTParameterValueType.Int);
                addInfo(info);
                info = new DTParameterInfo(0, "DistrictName", 1, DTParameterValueType.String);
                addInfo(info);
            }
        }

        public void save()           //private -> public
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configCommon = configFile.AddConfig("Common");
            configFile.AddItem(configCommon, "DTParameterInfos", new List<DTParameterInfo>(nameMap.Values), DTParameterInfo.AddItem);
            configFile.Save(configFileName);
        }

        private readonly Dictionary<String, DTParameterInfo> nameMap = new Dictionary<string, DTParameterInfo>();

        private readonly String configFileName = Application.StartupPath+@"\config\App.pic";
    }
}
