﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ZTFunc.ZTNRBlockCallAna;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRBlockCallAnaCondtion
    {
        public int WeakRsrpSec { get; set; }
        public int PoorSinrSec { get; set; }

        public float WeakRsrp { get; set; }

        public int PoorSinr { get; set; }

        public int HoNum { get; set; }

        public int HoSec { get; set; }

        public int MultiSec { get; set; }

        public int MultiBand { get; set; }

        public float MultiPer { get; set; }

        public int MultiValue { get; set; }

        public NRBlockCallAnaCondtion()
        {
            CauseSet = new List<NRBlockCallAnaCauseBase>();
            CauseSet.Add(new VoiceHangupCause());
            CauseSet.Add(new IMSErrorCause());
            CauseSet.Add(new UECancelCause());
        }

        public List<NRBlockCallAnaCauseBase> CauseSet { get; set; }
    }
}
