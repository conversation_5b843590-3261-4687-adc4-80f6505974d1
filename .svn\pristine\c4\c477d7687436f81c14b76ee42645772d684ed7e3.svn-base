﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList.Nodes;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EleNameSelectGSM : DevExpress.XtraEditors.XtraUserControl
    {
        public string MainSelectedEleName { get; set; }

        public delegate void GetSelectedEleNames(List<string> selectedEleNames);
        public GetSelectedEleNames GetSelectedEleNamesDo { get; set; }
        private List<string> selectedEleNames;

        public EleNameSelectGSM()
        {
            InitializeComponent();
            selectedEleNames = new List<string>();
            this.LostFocus += new EventHandler(LostFocusDo);
        }
        private void LostFocusDo(object sender, EventArgs e)
        {
            this.Hide();
        }
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            selectedEleNames = new List<string>();
            for (int i = 0; i < treeList1.Nodes[0].Nodes.Count; i++)
            {
                if (treeList1.Nodes[0].Nodes[i].Checked)
                {
                    selectedEleNames.Add(treeList1.Nodes[0].Nodes[i].GetValue(0).ToString());
                }
            }
            GetSelectedEleNamesDo(selectedEleNames);
            this.Hide();
        }

        private void EleNameSelect_Leave(object sender, EventArgs e)
        {
            this.Hide();
        }

        #region 设置关联显示
        private void treeList1_BeforeCheckNode(object sender, DevExpress.XtraTreeList.CheckNodeEventArgs e)
        {
            e.State = (e.PrevState == CheckState.Checked ? CheckState.Unchecked : CheckState.Checked);
        }

        private void treeList1_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            SetCheckedChildNodes(e.Node, e.Node.CheckState);
            SetCheckedParentNodes(e.Node, e.Node.CheckState);

        }
        /// <summary>
        /// 设置子节点的状态
        /// </summary>
        /// <param name="node"></param>
        /// <param name="check"></param>
        private void SetCheckedChildNodes(TreeListNode node, CheckState check)
        {
            for (int i = 0; i < node.Nodes.Count; i++)
            {
                node.Nodes[i].CheckState = check;
                SetCheckedChildNodes(node.Nodes[i], check);
            }
        }
        /// <summary>
        /// 设置父节点的状态
        /// </summary>
        /// <param name="node"></param>
        /// <param name="check"></param>
        private void SetCheckedParentNodes(TreeListNode node, CheckState check)
        {
            if (node.ParentNode != null)
            {
                bool b = false;
                CheckState state;
                for (int i = 0; i < node.ParentNode.Nodes.Count; i++)
                {
                    state = node.ParentNode.Nodes[i].CheckState;
                    if (!check.Equals(state))
                    {
                        b = !b;
                        break;
                    }
                }
                node.ParentNode.CheckState = b ? CheckState.Indeterminate : check;
                SetCheckedParentNodes(node.ParentNode, check);
            }
        }
        #endregion 

        private void EleNameSelect_Load(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(MainSelectedEleName))
            {
                for (int i = 0; i < treeList1.Nodes.Count; i++)
                {
                    if (treeList1.Nodes[i].GetValue(0).ToString() == MainSelectedEleName)
                    {
                        treeList1.Nodes[i].Visible = false;
                    }
                }
            }
        }
    }
}
