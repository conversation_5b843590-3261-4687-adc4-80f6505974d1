﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class BlackBlockInfoDlg_Handle
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BlackBlockInfoDlg_Handle));
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnHistory = new System.Windows.Forms.Button();
            this.tbxHandleAdvice = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.tbxHandleStatus = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.tbxAreaName = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.tbxReason = new System.Windows.Forms.TextBox();
            this.btnModify = new System.Windows.Forms.Button();
            this.tbxName = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.tbxEventDes = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.tbxLastTest = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.tbxCellDesc = new System.Windows.Forms.TextBox();
            this.tbxPlaceDesc = new System.Windows.Forms.TextBox();
            this.tbxFirstAbDate = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.tbxLastAbEvent = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.tbxCloseDate = new System.Windows.Forms.TextBox();
            this.tbxCreateDate = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tbxAbEventCount = new System.Windows.Forms.TextBox();
            this.tbxStatus = new System.Windows.Forms.TextBox();
            this.tbxNormalDays = new System.Windows.Forms.TextBox();
            this.tbxAbDays = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.tbxBlockID = new System.Windows.Forms.TextBox();
            this.gbxBlockDate = new System.Windows.Forms.GroupBox();
            this.lvBlockDate = new BrightIdeasSoftware.ObjectListView();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn2 = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gbxGrids = new System.Windows.Forms.GroupBox();
            this.lvGridsDate = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnDate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTestResult = new BrightIdeasSoftware.OLVColumn();
            this.lvGrids = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnXH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnStatus = new BrightIdeasSoftware.OLVColumn();
            this.tbxGridsInfo = new System.Windows.Forms.TextBox();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.gbxBlockDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvBlockDate)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.gbxGrids.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvGridsDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lvGrids)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(41, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "名称：";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnHistory);
            this.groupBox1.Controls.Add(this.tbxHandleAdvice);
            this.groupBox1.Controls.Add(this.label18);
            this.groupBox1.Controls.Add(this.tbxHandleStatus);
            this.groupBox1.Controls.Add(this.label17);
            this.groupBox1.Controls.Add(this.tbxAreaName);
            this.groupBox1.Controls.Add(this.label16);
            this.groupBox1.Controls.Add(this.tbxReason);
            this.groupBox1.Controls.Add(this.btnModify);
            this.groupBox1.Controls.Add(this.tbxName);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(675, 202);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "信息内容";
            // 
            // btnHistory
            // 
            this.btnHistory.Location = new System.Drawing.Point(480, 17);
            this.btnHistory.Name = "btnHistory";
            this.btnHistory.Size = new System.Drawing.Size(116, 23);
            this.btnHistory.TabIndex = 15;
            this.btnHistory.Text = "查看历史处理进度";
            this.btnHistory.UseVisualStyleBackColor = true;
            this.btnHistory.Click += new System.EventHandler(this.btnHistory_Click);
            // 
            // tbxHandleAdvice
            // 
            this.tbxHandleAdvice.Location = new System.Drawing.Point(88, 147);
            this.tbxHandleAdvice.Multiline = true;
            this.tbxHandleAdvice.Name = "tbxHandleAdvice";
            this.tbxHandleAdvice.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.tbxHandleAdvice.Size = new System.Drawing.Size(579, 44);
            this.tbxHandleAdvice.TabIndex = 9;
            this.tbxHandleAdvice.TextChanged += new System.EventHandler(this.tbxHandleAdvice_TextChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(17, 150);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(65, 12);
            this.label18.TabIndex = 8;
            this.label18.Text = "处理建议：";
            // 
            // tbxHandleStatus
            // 
            this.tbxHandleStatus.Location = new System.Drawing.Point(88, 97);
            this.tbxHandleStatus.Multiline = true;
            this.tbxHandleStatus.Name = "tbxHandleStatus";
            this.tbxHandleStatus.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.tbxHandleStatus.Size = new System.Drawing.Size(579, 44);
            this.tbxHandleStatus.TabIndex = 7;
            this.tbxHandleStatus.TextChanged += new System.EventHandler(this.tbxHandleStatus_TextChanged);
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(17, 100);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(65, 12);
            this.label17.TabIndex = 6;
            this.label17.Text = "处理情况：";
            // 
            // tbxAreaName
            // 
            this.tbxAreaName.Location = new System.Drawing.Point(305, 18);
            this.tbxAreaName.Name = "tbxAreaName";
            this.tbxAreaName.ReadOnly = true;
            this.tbxAreaName.Size = new System.Drawing.Size(132, 21);
            this.tbxAreaName.TabIndex = 5;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(258, 22);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(41, 12);
            this.label16.TabIndex = 4;
            this.label16.Text = "片区：";
            // 
            // tbxReason
            // 
            this.tbxReason.Location = new System.Drawing.Point(88, 47);
            this.tbxReason.Multiline = true;
            this.tbxReason.Name = "tbxReason";
            this.tbxReason.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.tbxReason.Size = new System.Drawing.Size(579, 44);
            this.tbxReason.TabIndex = 1;
            this.tbxReason.TextChanged += new System.EventHandler(this.tbxReason_TextChanged);
            // 
            // btnModify
            // 
            this.btnModify.Enabled = false;
            this.btnModify.Location = new System.Drawing.Point(602, 17);
            this.btnModify.Name = "btnModify";
            this.btnModify.Size = new System.Drawing.Size(65, 23);
            this.btnModify.TabIndex = 3;
            this.btnModify.Text = "应用修改";
            this.btnModify.UseVisualStyleBackColor = true;
            this.btnModify.Click += new System.EventHandler(this.btnModify_Click);
            // 
            // tbxName
            // 
            this.tbxName.Location = new System.Drawing.Point(88, 18);
            this.tbxName.Name = "tbxName";
            this.tbxName.Size = new System.Drawing.Size(132, 21);
            this.tbxName.TabIndex = 1;
            this.tbxName.TextChanged += new System.EventHandler(this.tbxName_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(17, 50);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "原因描述：";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.tbxEventDes);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.tbxLastTest);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.tbxCellDesc);
            this.groupBox3.Controls.Add(this.tbxPlaceDesc);
            this.groupBox3.Controls.Add(this.tbxFirstAbDate);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.tbxLastAbEvent);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.tbxCloseDate);
            this.groupBox3.Controls.Add(this.tbxCreateDate);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.tbxAbEventCount);
            this.groupBox3.Controls.Add(this.tbxStatus);
            this.groupBox3.Controls.Add(this.tbxNormalDays);
            this.groupBox3.Controls.Add(this.tbxAbDays);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.tbxBlockID);
            this.groupBox3.Location = new System.Drawing.Point(12, 220);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(675, 212);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "黑点情况";
            // 
            // tbxEventDes
            // 
            this.tbxEventDes.Location = new System.Drawing.Point(88, 125);
            this.tbxEventDes.Name = "tbxEventDes";
            this.tbxEventDes.ReadOnly = true;
            this.tbxEventDes.Size = new System.Drawing.Size(579, 21);
            this.tbxEventDes.TabIndex = 11;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(17, 129);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(65, 12);
            this.label15.TabIndex = 10;
            this.label15.Text = "事件详情：";
            // 
            // tbxLastTest
            // 
            this.tbxLastTest.Location = new System.Drawing.Point(535, 71);
            this.tbxLastTest.Name = "tbxLastTest";
            this.tbxLastTest.ReadOnly = true;
            this.tbxLastTest.Size = new System.Drawing.Size(132, 21);
            this.tbxLastTest.TabIndex = 5;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(17, 183);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(65, 12);
            this.label14.TabIndex = 8;
            this.label14.Text = "相关小区：";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(17, 156);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(65, 12);
            this.label13.TabIndex = 8;
            this.label13.Text = "位置描述：";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(17, 104);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(65, 12);
            this.label11.TabIndex = 8;
            this.label11.Text = "第一异常：";
            // 
            // tbxCellDesc
            // 
            this.tbxCellDesc.Location = new System.Drawing.Point(88, 179);
            this.tbxCellDesc.Name = "tbxCellDesc";
            this.tbxCellDesc.ReadOnly = true;
            this.tbxCellDesc.Size = new System.Drawing.Size(579, 21);
            this.tbxCellDesc.TabIndex = 9;
            // 
            // tbxPlaceDesc
            // 
            this.tbxPlaceDesc.Location = new System.Drawing.Point(88, 152);
            this.tbxPlaceDesc.Name = "tbxPlaceDesc";
            this.tbxPlaceDesc.ReadOnly = true;
            this.tbxPlaceDesc.Size = new System.Drawing.Size(579, 21);
            this.tbxPlaceDesc.TabIndex = 9;
            // 
            // tbxFirstAbDate
            // 
            this.tbxFirstAbDate.Location = new System.Drawing.Point(88, 98);
            this.tbxFirstAbDate.Name = "tbxFirstAbDate";
            this.tbxFirstAbDate.ReadOnly = true;
            this.tbxFirstAbDate.Size = new System.Drawing.Size(132, 21);
            this.tbxFirstAbDate.TabIndex = 9;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(234, 104);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(65, 12);
            this.label10.TabIndex = 6;
            this.label10.Text = "最后异常：";
            // 
            // tbxLastAbEvent
            // 
            this.tbxLastAbEvent.Location = new System.Drawing.Point(305, 98);
            this.tbxLastAbEvent.Name = "tbxLastAbEvent";
            this.tbxLastAbEvent.ReadOnly = true;
            this.tbxLastAbEvent.Size = new System.Drawing.Size(132, 21);
            this.tbxLastAbEvent.TabIndex = 7;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(440, 77);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(89, 12);
            this.label9.TabIndex = 4;
            this.label9.Text = "最后测试时间：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(234, 77);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(65, 12);
            this.label8.TabIndex = 4;
            this.label8.Text = "关闭时间：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(17, 77);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "创建时间：";
            // 
            // tbxCloseDate
            // 
            this.tbxCloseDate.Location = new System.Drawing.Point(305, 71);
            this.tbxCloseDate.Name = "tbxCloseDate";
            this.tbxCloseDate.ReadOnly = true;
            this.tbxCloseDate.Size = new System.Drawing.Size(132, 21);
            this.tbxCloseDate.TabIndex = 5;
            // 
            // tbxCreateDate
            // 
            this.tbxCreateDate.Location = new System.Drawing.Point(88, 71);
            this.tbxCreateDate.Name = "tbxCreateDate";
            this.tbxCreateDate.ReadOnly = true;
            this.tbxCreateDate.Size = new System.Drawing.Size(132, 21);
            this.tbxCreateDate.TabIndex = 5;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(452, 50);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(77, 12);
            this.label7.TabIndex = 2;
            this.label7.Text = "异常事件数：";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(234, 21);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(65, 12);
            this.label12.TabIndex = 2;
            this.label12.Text = "当前状态：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(234, 50);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "正常天数：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(17, 50);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "问题天数：";
            // 
            // tbxAbEventCount
            // 
            this.tbxAbEventCount.Location = new System.Drawing.Point(535, 44);
            this.tbxAbEventCount.Name = "tbxAbEventCount";
            this.tbxAbEventCount.ReadOnly = true;
            this.tbxAbEventCount.Size = new System.Drawing.Size(132, 21);
            this.tbxAbEventCount.TabIndex = 3;
            // 
            // tbxStatus
            // 
            this.tbxStatus.Location = new System.Drawing.Point(305, 17);
            this.tbxStatus.Name = "tbxStatus";
            this.tbxStatus.ReadOnly = true;
            this.tbxStatus.Size = new System.Drawing.Size(132, 21);
            this.tbxStatus.TabIndex = 3;
            // 
            // tbxNormalDays
            // 
            this.tbxNormalDays.Location = new System.Drawing.Point(305, 44);
            this.tbxNormalDays.Name = "tbxNormalDays";
            this.tbxNormalDays.ReadOnly = true;
            this.tbxNormalDays.Size = new System.Drawing.Size(132, 21);
            this.tbxNormalDays.TabIndex = 3;
            // 
            // tbxAbDays
            // 
            this.tbxAbDays.Location = new System.Drawing.Point(88, 44);
            this.tbxAbDays.Name = "tbxAbDays";
            this.tbxAbDays.ReadOnly = true;
            this.tbxAbDays.Size = new System.Drawing.Size(132, 21);
            this.tbxAbDays.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(17, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "黑点编号：";
            // 
            // tbxBlockID
            // 
            this.tbxBlockID.Location = new System.Drawing.Point(88, 17);
            this.tbxBlockID.Name = "tbxBlockID";
            this.tbxBlockID.ReadOnly = true;
            this.tbxBlockID.Size = new System.Drawing.Size(132, 21);
            this.tbxBlockID.TabIndex = 1;
            // 
            // gbxBlockDate
            // 
            this.gbxBlockDate.Controls.Add(this.lvBlockDate);
            this.gbxBlockDate.Location = new System.Drawing.Point(693, 12);
            this.gbxBlockDate.Name = "gbxBlockDate";
            this.gbxBlockDate.Size = new System.Drawing.Size(172, 420);
            this.gbxBlockDate.TabIndex = 9;
            this.gbxBlockDate.TabStop = false;
            this.gbxBlockDate.Text = "黑点测试情况";
            // 
            // lvBlockDate
            // 
            this.lvBlockDate.AllColumns.Add(this.olvColumn1);
            this.lvBlockDate.AllColumns.Add(this.olvColumn2);
            this.lvBlockDate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1,
            this.olvColumn2});
            this.lvBlockDate.ContextMenuStrip = this.ctxMenu;
            this.lvBlockDate.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvBlockDate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvBlockDate.FullRowSelect = true;
            this.lvBlockDate.GridLines = true;
            this.lvBlockDate.Location = new System.Drawing.Point(3, 17);
            this.lvBlockDate.MultiSelect = false;
            this.lvBlockDate.Name = "lvBlockDate";
            this.lvBlockDate.ShowGroups = false;
            this.lvBlockDate.Size = new System.Drawing.Size(166, 400);
            this.lvBlockDate.TabIndex = 9;
            this.lvBlockDate.UseCompatibleStateImageBehavior = false;
            this.lvBlockDate.View = System.Windows.Forms.View.Details;
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "Date";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "测试时间";
            this.olvColumn1.Width = 100;
            // 
            // olvColumn2
            // 
            this.olvColumn2.AspectName = "TestResult";
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "结果";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(125, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(124, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gbxGrids
            // 
            this.gbxGrids.Controls.Add(this.lvGridsDate);
            this.gbxGrids.Controls.Add(this.lvGrids);
            this.gbxGrids.Controls.Add(this.tbxGridsInfo);
            this.gbxGrids.Location = new System.Drawing.Point(871, 12);
            this.gbxGrids.Name = "gbxGrids";
            this.gbxGrids.Size = new System.Drawing.Size(294, 420);
            this.gbxGrids.TabIndex = 8;
            this.gbxGrids.TabStop = false;
            this.gbxGrids.Text = "栅格测试情况";
            // 
            // lvGridsDate
            // 
            this.lvGridsDate.AllColumns.Add(this.olvColumnDate);
            this.lvGridsDate.AllColumns.Add(this.olvColumnTestResult);
            this.lvGridsDate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnDate,
            this.olvColumnTestResult});
            this.lvGridsDate.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvGridsDate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvGridsDate.FullRowSelect = true;
            this.lvGridsDate.GridLines = true;
            this.lvGridsDate.Location = new System.Drawing.Point(127, 17);
            this.lvGridsDate.MultiSelect = false;
            this.lvGridsDate.Name = "lvGridsDate";
            this.lvGridsDate.ShowGroups = false;
            this.lvGridsDate.Size = new System.Drawing.Size(164, 379);
            this.lvGridsDate.TabIndex = 12;
            this.lvGridsDate.UseCompatibleStateImageBehavior = false;
            this.lvGridsDate.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnDate
            // 
            this.olvColumnDate.AspectName = "Date";
            this.olvColumnDate.HeaderFont = null;
            this.olvColumnDate.Text = "测试时间";
            this.olvColumnDate.Width = 100;
            // 
            // olvColumnTestResult
            // 
            this.olvColumnTestResult.AspectName = "TestResult";
            this.olvColumnTestResult.HeaderFont = null;
            this.olvColumnTestResult.Text = "结果";
            // 
            // lvGrids
            // 
            this.lvGrids.AllColumns.Add(this.olvColumnXH);
            this.lvGrids.AllColumns.Add(this.olvColumnStatus);
            this.lvGrids.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnXH,
            this.olvColumnStatus});
            this.lvGrids.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvGrids.Dock = System.Windows.Forms.DockStyle.Left;
            this.lvGrids.FullRowSelect = true;
            this.lvGrids.GridLines = true;
            this.lvGrids.Location = new System.Drawing.Point(3, 17);
            this.lvGrids.MultiSelect = false;
            this.lvGrids.Name = "lvGrids";
            this.lvGrids.ShowGroups = false;
            this.lvGrids.Size = new System.Drawing.Size(124, 379);
            this.lvGrids.TabIndex = 11;
            this.lvGrids.UseCompatibleStateImageBehavior = false;
            this.lvGrids.View = System.Windows.Forms.View.Details;
            this.lvGrids.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvGrids_MouseDoubleClick);
            this.lvGrids.SelectedIndexChanged += new System.EventHandler(this.lvGrids_SelectedIndexChanged);
            // 
            // olvColumnXH
            // 
            this.olvColumnXH.HeaderFont = null;
            this.olvColumnXH.Text = "序号";
            // 
            // olvColumnStatus
            // 
            this.olvColumnStatus.AspectName = "Status";
            this.olvColumnStatus.HeaderFont = null;
            this.olvColumnStatus.Text = "状态";
            // 
            // tbxGridsInfo
            // 
            this.tbxGridsInfo.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.tbxGridsInfo.Location = new System.Drawing.Point(3, 396);
            this.tbxGridsInfo.Name = "tbxGridsInfo";
            this.tbxGridsInfo.ReadOnly = true;
            this.tbxGridsInfo.Size = new System.Drawing.Size(288, 21);
            this.tbxGridsInfo.TabIndex = 10;
            // 
            // BlackBlockInfoDlg_Handle
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(693, 443);
            this.Controls.Add(this.gbxBlockDate);
            this.Controls.Add(this.gbxGrids);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BlackBlockInfoDlg_Handle";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "问题黑点信息";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.gbxBlockDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvBlockDate)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.gbxGrids.ResumeLayout(false);
            this.gbxGrids.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvGridsDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lvGrids)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox tbxName;
        private System.Windows.Forms.TextBox tbxReason;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnModify;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox tbxBlockID;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox tbxAbDays;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox tbxCreateDate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox tbxAbEventCount;
        private System.Windows.Forms.TextBox tbxNormalDays;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox tbxCloseDate;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox tbxFirstAbDate;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox tbxLastAbEvent;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox tbxLastTest;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox tbxStatus;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox tbxCellDesc;
        private System.Windows.Forms.TextBox tbxPlaceDesc;
        private System.Windows.Forms.TextBox tbxEventDes;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.GroupBox gbxBlockDate;
        private BrightIdeasSoftware.ObjectListView lvBlockDate;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private System.Windows.Forms.GroupBox gbxGrids;
        private BrightIdeasSoftware.ObjectListView lvGridsDate;
        private BrightIdeasSoftware.OLVColumn olvColumnDate;
        private BrightIdeasSoftware.OLVColumn olvColumnTestResult;
        private BrightIdeasSoftware.ObjectListView lvGrids;
        private BrightIdeasSoftware.OLVColumn olvColumnXH;
        private BrightIdeasSoftware.OLVColumn olvColumnStatus;
        private System.Windows.Forms.TextBox tbxGridsInfo;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.TextBox tbxAreaName;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Button btnHistory;
        private System.Windows.Forms.TextBox tbxHandleAdvice;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox tbxHandleStatus;
        private System.Windows.Forms.Label label17;
    }
}