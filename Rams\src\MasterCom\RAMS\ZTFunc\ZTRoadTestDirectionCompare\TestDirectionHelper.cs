﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestDirectionHelper
    {
        protected TestDirectionHelper()
        {

        }

        public static EnumDirection GetDireciton(TestPoint startTp, TestPoint endTp)
        {
            if (startTp == null || endTp == null)
            {
                return EnumDirection.None;
            }

            double angle = getTwoTestPointAngle(startTp, endTp);

            List<DirectionInfo> directionInfoList = new List<DirectionInfo>();
            directionInfoList.Add(new DirectionInfo(EnumDirection.北, 0, 22.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.北, 337.5, 360));
            directionInfoList.Add(new DirectionInfo(EnumDirection.东北, 22.5, 67.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.东, 67.5, 112.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.东南, 112.5, 157.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.南, 157.5, 202.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.西南, 202.5, 247.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.西, 247.5, 292.5));
            directionInfoList.Add(new DirectionInfo(EnumDirection.西北, 292.5, 337.5));

            foreach (var direction in directionInfoList)
            {
                if (angle >= direction.Min && angle < direction.Max)
                {
                    return direction.Direction;
                }
            }
            return EnumDirection.None;
        }

        private static double getTwoTestPointAngle(TestPoint startTp, TestPoint endTp)
        {
            double startLongitude = startTp.Longitude;
            double startLatitude = startTp.Latitude;
            double endLongitude = endTp.Longitude;
            double endLatitude = endTp.Latitude;

            double distance = MathFuncs.GetDistance(startLongitude, startLatitude, endLongitude, endLatitude);

            ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angle;
            double ygap = MathFuncs.GetDistance(startLongitude, startLatitude, startLongitude, endLatitude);
            double angleV = Math.Acos(ygap / distance);
            if (endLongitude >= startLongitude && endLatitude >= startLatitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (endLongitude <= startLongitude && endLatitude >= startLatitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (endLongitude <= startLongitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            return angle;
        }

        /// <summary>
        /// 方向是否相反
        /// </summary>
        /// <param name="curDireciton"></param>
        /// <param name="lastDiretion"></param>
        /// <returns></returns>
        public static bool IsOppositeDireciton(EnumDirection curDireciton, EnumDirection lastDiretion)
        {
            switch (curDireciton)
            {
                case EnumDirection.北:
                    return lastDiretion == EnumDirection.南;
                case EnumDirection.南:
                    return lastDiretion == EnumDirection.北;
                case EnumDirection.西:
                    return lastDiretion == EnumDirection.东;
                case EnumDirection.东:
                    return lastDiretion == EnumDirection.西;
                case EnumDirection.东北:
                    return lastDiretion == EnumDirection.西南;
                case EnumDirection.西南:
                    return lastDiretion == EnumDirection.东北;
                case EnumDirection.东南:
                    return lastDiretion == EnumDirection.西北;
                case EnumDirection.西北:
                    return lastDiretion == EnumDirection.东南;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取方向符号
        /// </summary>
        /// <param name="direction"></param>
        /// <returns></returns>
        public static string GetDirectionSign(EnumDirection direction)
        {
            switch (direction)
            {
                case EnumDirection.北:
                    return "↑";
                case EnumDirection.南:
                    return "↓";
                case EnumDirection.西:
                    return "←";
                case EnumDirection.东:
                    return "→";
                case EnumDirection.东北:
                    return "↗";
                case EnumDirection.西南:
                    return "↙";
                case EnumDirection.东南:
                    return "↘";
                case EnumDirection.西北:
                    return "↖";
                default:
                    return "";
            }
        }

        class DirectionInfo
        {
            public DirectionInfo(EnumDirection direction, double min, double max)
            {
                Direction = direction;
                Min = min;
                Max = max;
            }
            public EnumDirection Direction { get; set; }
            public double Min { get; set; }
            public double Max { get; set; }
        }
    }
}
