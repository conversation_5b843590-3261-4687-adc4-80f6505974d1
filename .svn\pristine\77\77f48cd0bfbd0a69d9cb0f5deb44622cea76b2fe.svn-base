﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS
{
    public partial class CPGridCellChoseWarningForm : Form
    {
        public CPGridCellChoseWarningForm(List<string> itemList)
        {
            InitializeComponent();
            initUI(itemList);
        }

        private void initUI(List<string> itemList)
        {
            foreach(string item in itemList)
            {
                chclistbox.Items.Add(item);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            List<string> list = new List<string>();
            foreach(string item in chclistbox.CheckedItems)
            {
                list.Add(item);
            }
            CPGridCellShowForm.chosedWarnings = list;
            this.DialogResult = DialogResult.OK;
        }
    }
}
