﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEHandoverBehindTime
    {
        protected delegate void Func<T>(StringBuilder str, List<T> tempList, TestPoint tp);

        protected string getData<T>(List<TestPoint> points, Func<T> func)
        {
            StringBuilder str = new StringBuilder();
            List<T> tempList = new List<T>();
            foreach (TestPoint tp in points)
            {
                func(str, tempList, tp);
            }
            if (str.Length != 0)
            {
                str.Remove(str.Length - 1, 1);
            }

            return str.ToString();
        }


        public String SvrCellName
        {
            get
            {
                return getData<string>(points, getCellNameByTP);
            }
        }

        public string SvrCellECI
        {
            get
            {
                return getData<int>(points, getCellECIByTP);
            }
        }

        protected virtual void getCellNameByTP(StringBuilder svrCellName, List<string> tempList, TestPoint tp)
        {
            if (tp is WCDMATestPointDetail)
            {
                WCell cell = tp.GetMainCell_W();
                if (cell != null)
                {
                    addCellName(svrCellName, tempList, cell.Name);
                }
            }
            else
            {
                LTECell lteCell = tp.GetMainLTECell_TdOrFdd();
                if (lteCell != null)
                {
                    addCellName(svrCellName, tempList, lteCell.Name);
                }
            }
        }

        private void getCellECIByTP(StringBuilder svrCellECI, List<int> tempList, TestPoint tp)
        {
            int? eciTmp = (int?)tp["lte_ECI"];
            if (eciTmp != null)
            {
                int eci = (int)eciTmp;
                if (!tempList.Contains(eci))
                {
                    tempList.Add(eci);
                    svrCellECI.Append(eci);
                    svrCellECI.Append(";");
                }
            }
        }

        public String NBCellName
        {
            get
            {
                return getData<string>(points, getNbCellNameByTP);
            }
        }

        protected virtual void getNbCellNameByTP(StringBuilder nbCellName, List<string> tempList, TestPoint tp)
        {
            if (tp is WCDMATestPointDetail)
            {
                for (int i = 0; i < 10; i++)
                {
                    WCell cell = tp.GetNBCell_W_WCell(i);
                    if (cell != null)
                    {
                        addCellName(nbCellName, tempList, cell.Name);
                    }
                }
            }
            else
            {
                for (int i = 0; i < 10; i++)
                {
                    LTECell lteCell = tp.GetNBLTECell_TdOrFdd(i);
                    if (lteCell != null)
                    {
                        addCellName(nbCellName, tempList, lteCell.Name);
                    }
                }
            }
        }

        protected static void addCellName(StringBuilder sbCellName, List<string> tempList, string cellName)
        {
            if (!tempList.Contains(cellName))
            {
                tempList.Add(cellName);
                sbCellName.Append(cellName);
                sbCellName.Append(";");
            }
        }

        protected readonly List<TestPoint> points = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return points; }
        }
        private float minSvrRsrp = float.MaxValue;
        public float MinSvrRsrp
        {
            get
            {
                return minSvrRsrp == float.MaxValue ? float.NaN : minSvrRsrp;
            }
        }
        private float maxSvrRsrp = float.MinValue;
        public float MaxSvrRsrp
        {
            get
            {
                return maxSvrRsrp == float.MinValue ? float.NaN : maxSvrRsrp;
            }
        }
        private float sumSvrRsrp = 0;
        public float AvgSvrRsrp
        {
            get { return (float)Math.Round(sumSvrRsrp / points.Count, 2); }
        }

        public double MidLng
        {
            get
            {
                double lng = 0;
                if (points.Count > 0)
                {
                    lng = points[(points.Count / 2)].Longitude;
                }
                return lng;
            }
        }

        public double MidLat
        {
            get
            {
                double lat = 0;
                if (points.Count > 0)
                {
                    lat = points[(points.Count / 2)].Latitude;
                }
                return lat;
            }
        }

        public int StaySeconds
        {
            get
            {
                int sec = 0;
                if (points.Count > 0)
                {
                    sec = (int)(points[points.Count - 1].DateTime - points[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }

        public int SN
        {
            get;
            set;
        }

        public string RoadName
        {
            get;
            set;
        }
        public void FindRoadName()
        {
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            TestPoint tp = points[0];
            lngs.Add(tp.Longitude);
            lats.Add(tp.Latitude);
            tp = points[(points.Count / 2)];
            lngs.Add(tp.Longitude);
            lats.Add(tp.Latitude);
            tp = points[points.Count - 1];
            lngs.Add(tp.Longitude);
            lats.Add(tp.Latitude);
            RoadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (points.Count > 0)
                {
                    name = points[0].FileName;
                }
                return name;
            }
        }
        private double distance = -1;
        public double Distance
        {
            get
            {
                if (distance < 0)
                {
                    distance = 0;
                    for (int i = 1; i < points.Count; i++)
                    {
                        distance += MasterCom.Util.MathFuncs.GetDistance(points[i - 1].Longitude, points[i - 1].Latitude, points[i].Longitude, points[i].Latitude);
                    }
                    distance = Math.Round(distance, 2);
                }
                return distance;
            }
        }

        public int TestPointCount
        {
            get { return points.Count; }
        }

        public void AddTestPoint(TestPoint tp, float rsrp)
        {
            points.Add(tp);
            minSvrRsrp = Math.Min(minSvrRsrp, rsrp);
            maxSvrRsrp = Math.Max(maxSvrRsrp, rsrp);
            sumSvrRsrp += rsrp;
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.LongitudeStart = 0;
            bgResult.LatitudeStart = 0;
            bgResult.LongitudeEnd = 0;
            bgResult.LatitudeEnd = 0;
            bgResult.ISTime = 0;
            bgResult.IETime = 0;
            if (points.Count > 0)
            {
                bgResult.FileID = points[0].FileID;
                bgResult.FileName = points[0].FileName;
                bgResult.LongitudeStart = points[0].Longitude;
                bgResult.LatitudeStart = points[0].Latitude;
                bgResult.LongitudeEnd = points[points.Count - 1].Longitude;
                bgResult.LatitudeEnd = points[points.Count - 1].Latitude;
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(points[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(points[points.Count - 1].DateTime) / 1000);
            }
            bgResult.LongitudeMid = MidLng;
            bgResult.LatitudeMid = MidLat;
            bgResult.DistanceLast = Distance;
            bgResult.SampleCount = TestPointCount;
            bgResult.RxLevMean = AvgSvrRsrp;
            bgResult.RxLevMin = MinSvrRsrp;
            bgResult.RxLevMax = MaxSvrRsrp;
            bgResult.RoadDesc = RoadName;
            bgResult.CellIDDesc = NBCellName;

            bgResult.AddImageValue(SvrCellName);
            return bgResult;
        }
    }
}
