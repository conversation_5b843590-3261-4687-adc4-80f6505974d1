using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class BSC
    {
        public BSC()
        {
        }

        public BSC(string name)
        {
            Name = name;
        }

        public string Name { get; set; }

        public MSC BelongMSC { get; set; }

        public List<BTS> BTSs
        {
            get { return btss; }
        }

        public void AddBTS(BTS bts)
        {
            btss.Add(bts);
            bts.BelongBSC = this;
        }

        private readonly List<BTS> btss = new List<BTS>();

        public static IComparer<BSC> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<BSC> comparerByName;

        public class ComparerByName : IComparer<BSC>
        {
            public int Compare(BSC x, BSC y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}
