﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.Model.PerformanceParam.TD切换分析;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public class DiySqlQueryJointHandoverAnalysis : QueryBase
    {
        private readonly bool IsTDAnalysis = false;
        public DiySqlQueryJointHandoverAnalysis(MainModel mainModel,bool IsTDAnalysis)
            : base(mainModel)
        {
            this.IsTDAnalysis = IsTDAnalysis;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public string SelectName { get; set; }
        public string SelectTable { get; set; }
        public DateTime SelectTime { get; set; }
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "联合切换分析"; }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        /// <summary>
        /// 判断是否选择区域
        /// </summary>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return searchGeometrys.IsValidRegion;
        }
        
        public List<string> IMonList { get; set; } = new List<string>();
        /// <summary>
        /// 查询接口
        /// </summary>
        protected override void query()
        {
            JointHandoverAnalysisTimeFrm analysisTimeFrm = new JointHandoverAnalysisTimeFrm(MainModel,IsTDAnalysis);
            SelectName = "";
            SelectTable = "";
            SelectTime = new DateTime();
            IMonList.Clear();
            WaitBox.CanCancel = true;
            WaitBox.Show("正在初始化联合切换分析.......", queryStatData);
            analysisTimeFrm.comboBoxInit(IMonList);
            analysisTimeFrm.ShowDialog();
        }

        /// <summary>
        /// 查询统计数据
        /// </summary>
        protected void queryStatData()
        {
            try
            {
                this.IMonList.Clear();
                DiySqlQueryJointHandOverAnalysisDate analysisDate;
                if (IsTDAnalysis)
                {
                    analysisDate = new DiySqlQueryJointHandOverAnalysisDataTD(MainModel);
                }
                else
                {
                    analysisDate = new DiySqlQueryJointHandOverAnalysisDate(MainModel);
                }
                analysisDate.Query();
                this.IMonList.AddRange(analysisDate.IMonList);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }
}
