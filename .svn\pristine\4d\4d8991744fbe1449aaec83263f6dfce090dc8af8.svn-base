﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellSetForm : MinCloseForm
    {
        public NRCellSetForm()
            :base()
        {
            InitializeComponent();
        }

        public void FillData(List<NrCellInfo> resInfoLst)
        {
            this.gridControl1.DataSource = resInfoLst;
            this.gridControl1.RefreshDataSource();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            NrCellInfo cellInfo = gv.GetRow(gv.GetSelectedRows()[0]) as NrCellInfo;
            if (cellInfo.Cell != null)
            {
                MainModel.SetSelectedNRCell(MainModel.GetInstance().CellManager.GetCurrentNRCell(cellInfo.Cell.TAC, cellInfo.Cell.NCI));
                MainModel.FireSelectedCellChanged(MainModel.MainForm);
                MainModel.MainForm.GetMapForm().GoToView(cellInfo.Longitude, cellInfo.Latitude, 5000);
            }
        }
    }
}
