﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteCellAngleForm : MinCloseForm
    {
        MapForm mapForm;
        public LteCellAngleForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        public int iResultNum { get; set; }

        public void FillData()
        {
            try
            {
                labNum.Text = iResultNum.ToString();
                int iPage = iResultNum % 200 > 0 ? iResultNum / 200 + 1 : iResultNum / 200;
                labPage.Text = iPage.ToString();

                dataGridViewCell.Columns.Clear();
                dataGridViewCell.Rows.Clear();//小区级

                int rowCellAt = 0;
                foreach (NPOIRow data in nrDatasList[0])
                {
                    if (rowCellAt == 0)
                    {
                        intDataViewColumn(dataGridViewCell, data.cellValues);
                        rowCellAt++;
                        continue;
                    }
                    if (rowCellAt > 200)
                        break;
                    initDataRow(dataGridViewCell, data);
                    rowCellAt++;
                }
                txtPage.Text = "1";
            }
            catch
            {
                //continue
            }
        }

        private void intDataViewColumn(DataGridView dataGridView,List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && data.cellValues[2].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell,data);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell,data);
                rowCellAt++;
            }
        }

        private void initDataRow(DataGridView datatGridView ,NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[2] + "|" + nop.cellValues[12];//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                double dValue = 0;
                if (double.TryParse(obj.ToString(), out dValue))
                    boxcell.Value = dValue;
                else
                    boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        private void 显示小区ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
            string strCells = dataGridViewCell.Rows[iRowId].Tag.ToString();
            List<LTECell> cellList = new List<LTECell>();
            string[] cells = strCells.Split('|');
            foreach (string strCellName in cells)
            {
                LTECell tmpCell = CellManager.GetInstance().GetLTECellLatest(strCellName);
                if(tmpCell != null)
                    cellList.Add(tmpCell);
            }
            MainModel.SelectedLTECells = cellList;
            if (cellList.Count > 0)
            {
                MainModel.MainForm.GetMapForm().GoToView(cellList[0].Longitude, cellList[0].Latitude);
            }
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void 导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iResultNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iResultNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }
    }
}
