﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public partial class VVipEventLogEditForm : BaseForm
    {
        public VVipEventLogEditForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        private EventLog log = null;
        public void FillData(EventLog log)
        {
            this.log = log;
            lblPhoneModel.Visible = txtPhoneModel.Visible = false;
            if (log is EventLogTD)
            {
                lblPhoneModel.Visible = txtPhoneModel.Visible = true;
                txtPhoneModel.Text = (log as EventLogTD).PhoneModelNumber;
            }
            txtBTSType.Text = log.BTSType;
            txtCI.Text = log.CI;
            txtDate.Text = log.Date.ToString("yyyy/MM/dd");
            txtTime.Text = log.Time.ToString();
            txtEventName.Text = log.EventName;
            txtLogNumber.Text = log.LogNumber;
            txtMoMt.Text = log.MoMt;
            txtOwnRegion.Text = log.OwnRegion;
            txtPhoneNumber.Text = log.PhoneNumber;
            txtPlace.Text = log.Place;
            txtSN.Text = log.SN.ToString();
            cbxClosedLoop.SelectedItem = log.IsCloseLoop;
            cbxErrorType.SelectedItem = log.HasTrafficLog;
            cbxErrorType.SelectedItem = log.ErrorType;
            rtbAnalytics.Text = log.Analytics;
            rtbSuggestion.Text = log.Suggestion;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (txtBTSType.Text.Length==0)
            {
                MessageBox.Show("请填写基站类型！");
                return;
            }
            if (numLng.Value < 70)
            {
                MessageBox.Show("经度输入错误！");
                return;
            }
            if (numLat.Value<3)
            {
                MessageBox.Show("纬度输入错误！");
                return;
            }
            if (txtOwnRegion.Text.Length==0)
            {
                MessageBox.Show("请填写所属片！");
                return;
            }
            if (cbxTraffic.SelectedItem==null)
            {
                MessageBox.Show("请选择是否有Traffic记录！");
                return;
            }
            if (txtPlace.Text.Length==0)
            {
                MessageBox.Show("请填写产生地点！");
                return;
            }
            if (cbxClosedLoop.SelectedItem==null)
            {
                MessageBox.Show("请选择写是否闭环！");
                return;
            }
            if (cbxErrorType.SelectedItem==null)
            {
                MessageBox.Show("请选择分类原因！");
                return;
            }
            if (rtbAnalytics.Text.Length==0)
            {
                MessageBox.Show("请填写事件分析！");
                return;
            }
            if (rtbSuggestion.Text.Length==0)
            {
                MessageBox.Show("请填写解决建议！");
                return;
            }
            log.BTSType = txtBTSType.Text;
            log.Longitude = (double)numLng.Value;
            log.Latitude = (double)numLat.Value;
            log.OwnRegion = txtOwnRegion.Text;
            log.HasTrafficLog = cbxTraffic.SelectedItem.ToString();
            log.IsCloseLoop = cbxClosedLoop.SelectedItem.ToString();
            log.ErrorType = cbxErrorType.SelectedItem.ToString();
            log.Place = txtPlace.Text;
            log.Analytics = rtbAnalytics.Text;
            log.Suggestion = rtbSuggestion.Text;
            UpdateVVIPEventLog update = new UpdateVVIPEventLog(log);
            update.Query();
        }

        private void VVipEventLogEditForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            e.Cancel = true;
            Visible = false;
        }
    }
}
