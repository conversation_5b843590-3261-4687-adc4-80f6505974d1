﻿namespace MasterCom.RAMS.Model.BaseInfo
{
    partial class UserDataSrcRightsOptionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.grpUser = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlUser = new DevExpress.XtraGrid.GridControl();
            this.gvUser = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcUserRole = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.treeListRole = new DevExpress.XtraTreeList.TreeList();
            this.colRoleDistrict = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colRoleName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colRoleDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.grpDataSrc = new DevExpress.XtraEditors.GroupControl();
            this.tab = new DevExpress.XtraTab.XtraTabControl();
            this.pageProj = new DevExpress.XtraTab.XtraTabPage();
            this.treeListDataSrc = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn2 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn3 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.pageArea = new DevExpress.XtraTab.XtraTabPage();
            this.chkAllArea = new System.Windows.Forms.CheckBox();
            this.treeListArea = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn4 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.label1 = new System.Windows.Forms.Label();
            this.btnAddRole = new DevExpress.XtraEditors.SimpleButton();
            this.btnSubmit = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).BeginInit();
            this.grpUser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlUser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListRole)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpDataSrc)).BeginInit();
            this.grpDataSrc.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tab)).BeginInit();
            this.tab.SuspendLayout();
            this.pageProj.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListDataSrc)).BeginInit();
            this.pageArea.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListArea)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.grpUser);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.panelControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1265, 499);
            this.splitContainerControl1.SplitterPosition = 387;
            this.splitContainerControl1.TabIndex = 8;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // grpUser
            // 
            this.grpUser.Controls.Add(this.gridCtrlUser);
            this.grpUser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpUser.Location = new System.Drawing.Point(0, 0);
            this.grpUser.Name = "grpUser";
            this.grpUser.Size = new System.Drawing.Size(387, 499);
            this.grpUser.TabIndex = 3;
            this.grpUser.Text = "用户";
            // 
            // gridCtrlUser
            // 
            this.gridCtrlUser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlUser.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlUser.MainView = this.gvUser;
            this.gridCtrlUser.Name = "gridCtrlUser";
            this.gridCtrlUser.Size = new System.Drawing.Size(383, 474);
            this.gridCtrlUser.TabIndex = 2;
            this.gridCtrlUser.UseEmbeddedNavigator = true;
            this.gridCtrlUser.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvUser});
            // 
            // gvUser
            // 
            this.gvUser.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gvUser.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.gvUser.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvUser.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcName,
            this.gcDesc,
            this.gcUserRole});
            this.gvUser.GridControl = this.gridCtrlUser;
            this.gvUser.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvUser.Name = "gvUser";
            this.gvUser.OptionsBehavior.Editable = false;
            this.gvUser.OptionsDetail.EnableMasterViewMode = false;
            this.gvUser.OptionsDetail.ShowDetailTabs = false;
            this.gvUser.OptionsSelection.MultiSelect = true;
            this.gvUser.OptionsView.EnableAppearanceEvenRow = true;
            this.gvUser.OptionsView.EnableAppearanceOddRow = true;
            this.gvUser.PaintStyleName = "Skin";
            // 
            // gcName
            // 
            this.gcName.Caption = "用户名";
            this.gcName.FieldName = "LoginName";
            this.gcName.Name = "gcName";
            this.gcName.Visible = true;
            this.gcName.VisibleIndex = 0;
            this.gcName.Width = 114;
            // 
            // gcDesc
            // 
            this.gcDesc.Caption = "描述";
            this.gcDesc.FieldName = "Description";
            this.gcDesc.Name = "gcDesc";
            this.gcDesc.Visible = true;
            this.gcDesc.VisibleIndex = 1;
            this.gcDesc.Width = 91;
            // 
            // gcUserRole
            // 
            this.gcUserRole.Caption = "权限组";
            this.gcUserRole.FieldName = "DataSrcRolesDesc";
            this.gcUserRole.Name = "gcUserRole";
            this.gcUserRole.Visible = true;
            this.gcUserRole.VisibleIndex = 2;
            this.gcUserRole.Width = 160;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.grpDataSrc);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(872, 456);
            this.splitContainerControl2.SplitterPosition = 322;
            this.splitContainerControl2.TabIndex = 8;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.treeListRole);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(322, 456);
            this.groupControl1.TabIndex = 4;
            this.groupControl1.Text = "数据源权限组";
            // 
            // treeListRole
            // 
            this.treeListRole.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.treeListRole.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.treeListRole.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colRoleDistrict,
            this.colRoleName,
            this.colRoleDesc});
            this.treeListRole.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListRole.Location = new System.Drawing.Point(2, 23);
            this.treeListRole.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListRole.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListRole.Name = "treeListRole";
            this.treeListRole.BeginUnboundLoad();
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, 0);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, 0);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, 0);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, 6);
            this.treeListRole.AppendNode(new object[] {
            null,
            null,
            null}, 6);
            this.treeListRole.EndUnboundLoad();
            this.treeListRole.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListRole.OptionsView.ShowCheckBoxes = true;
            this.treeListRole.Size = new System.Drawing.Size(318, 431);
            this.treeListRole.TabIndex = 3;
            // 
            // colRoleDistrict
            // 
            this.colRoleDistrict.Caption = "地市";
            this.colRoleDistrict.FieldName = "DistrictName";
            this.colRoleDistrict.MinWidth = 51;
            this.colRoleDistrict.Name = "colRoleDistrict";
            this.colRoleDistrict.OptionsColumn.AllowEdit = false;
            this.colRoleDistrict.Visible = true;
            this.colRoleDistrict.VisibleIndex = 0;
            this.colRoleDistrict.Width = 99;
            // 
            // colRoleName
            // 
            this.colRoleName.Caption = "名称";
            this.colRoleName.FieldName = "Name";
            this.colRoleName.MinWidth = 51;
            this.colRoleName.Name = "colRoleName";
            this.colRoleName.OptionsColumn.AllowSort = false;
            this.colRoleName.Visible = true;
            this.colRoleName.VisibleIndex = 1;
            this.colRoleName.Width = 137;
            // 
            // colRoleDesc
            // 
            this.colRoleDesc.Caption = "描述";
            this.colRoleDesc.FieldName = "Description";
            this.colRoleDesc.Name = "colRoleDesc";
            this.colRoleDesc.OptionsColumn.AllowSort = false;
            this.colRoleDesc.Visible = true;
            this.colRoleDesc.VisibleIndex = 2;
            this.colRoleDesc.Width = 61;
            // 
            // grpDataSrc
            // 
            this.grpDataSrc.Controls.Add(this.tab);
            this.grpDataSrc.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpDataSrc.Location = new System.Drawing.Point(0, 0);
            this.grpDataSrc.Name = "grpDataSrc";
            this.grpDataSrc.Size = new System.Drawing.Size(544, 456);
            this.grpDataSrc.TabIndex = 4;
            this.grpDataSrc.Text = "数据源组";
            // 
            // tab
            // 
            this.tab.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.tab.Appearance.ForeColor = System.Drawing.SystemColors.ActiveCaption;
            this.tab.Appearance.Options.UseBackColor = true;
            this.tab.Appearance.Options.UseForeColor = true;
            this.tab.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tab.Location = new System.Drawing.Point(2, 23);
            this.tab.Name = "tab";
            this.tab.SelectedTabPage = this.pageProj;
            this.tab.Size = new System.Drawing.Size(540, 431);
            this.tab.TabIndex = 3;
            this.tab.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageProj,
            this.pageArea});
            // 
            // pageProj
            // 
            this.pageProj.Controls.Add(this.treeListDataSrc);
            this.pageProj.Name = "pageProj";
            this.pageProj.Size = new System.Drawing.Size(533, 401);
            this.pageProj.Text = "数据源";
            // 
            // treeListDataSrc
            // 
            this.treeListDataSrc.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn2,
            this.treeListColumn3});
            this.treeListDataSrc.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListDataSrc.Enabled = false;
            this.treeListDataSrc.Location = new System.Drawing.Point(0, 0);
            this.treeListDataSrc.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListDataSrc.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListDataSrc.Name = "treeListDataSrc";
            this.treeListDataSrc.BeginUnboundLoad();
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListDataSrc.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListDataSrc.EndUnboundLoad();
            this.treeListDataSrc.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListDataSrc.OptionsView.ShowCheckBoxes = true;
            this.treeListDataSrc.Size = new System.Drawing.Size(533, 401);
            this.treeListDataSrc.TabIndex = 2;
            // 
            // treeListColumn2
            // 
            this.treeListColumn2.Caption = "名称";
            this.treeListColumn2.FieldName = "Name";
            this.treeListColumn2.MinWidth = 51;
            this.treeListColumn2.Name = "treeListColumn2";
            this.treeListColumn2.OptionsColumn.AllowEdit = false;
            this.treeListColumn2.OptionsColumn.AllowSort = false;
            this.treeListColumn2.Visible = true;
            this.treeListColumn2.VisibleIndex = 0;
            this.treeListColumn2.Width = 140;
            // 
            // treeListColumn3
            // 
            this.treeListColumn3.Caption = "描述";
            this.treeListColumn3.FieldName = "Description";
            this.treeListColumn3.Name = "treeListColumn3";
            this.treeListColumn3.OptionsColumn.AllowEdit = false;
            this.treeListColumn3.OptionsColumn.AllowSort = false;
            this.treeListColumn3.Visible = true;
            this.treeListColumn3.VisibleIndex = 1;
            this.treeListColumn3.Width = 141;
            // 
            // pageArea
            // 
            this.pageArea.Controls.Add(this.chkAllArea);
            this.pageArea.Controls.Add(this.treeListArea);
            this.pageArea.Name = "pageArea";
            this.pageArea.Size = new System.Drawing.Size(533, 401);
            this.pageArea.Text = "区域";
            // 
            // chkAllArea
            // 
            this.chkAllArea.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.chkAllArea.AutoSize = true;
            this.chkAllArea.Location = new System.Drawing.Point(6, 378);
            this.chkAllArea.Name = "chkAllArea";
            this.chkAllArea.Size = new System.Drawing.Size(168, 16);
            this.chkAllArea.TabIndex = 3;
            this.chkAllArea.Text = "所有区域权限包括后续新增";
            this.chkAllArea.UseVisualStyleBackColor = true;
            // 
            // treeListArea
            // 
            this.treeListArea.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeListArea.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1,
            this.treeListColumn4});
            this.treeListArea.Enabled = false;
            this.treeListArea.Location = new System.Drawing.Point(0, 0);
            this.treeListArea.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListArea.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListArea.Name = "treeListArea";
            this.treeListArea.BeginUnboundLoad();
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListArea.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListArea.EndUnboundLoad();
            this.treeListArea.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListArea.OptionsView.ShowCheckBoxes = true;
            this.treeListArea.Size = new System.Drawing.Size(533, 369);
            this.treeListArea.TabIndex = 2;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "名称";
            this.treeListColumn1.FieldName = "Name";
            this.treeListColumn1.MinWidth = 51;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.OptionsColumn.AllowEdit = false;
            this.treeListColumn1.OptionsColumn.AllowSort = false;
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            this.treeListColumn1.Width = 140;
            // 
            // treeListColumn4
            // 
            this.treeListColumn4.Caption = "描述";
            this.treeListColumn4.FieldName = "Description";
            this.treeListColumn4.Name = "treeListColumn4";
            this.treeListColumn4.OptionsColumn.AllowEdit = false;
            this.treeListColumn4.OptionsColumn.AllowSort = false;
            this.treeListColumn4.Visible = true;
            this.treeListColumn4.VisibleIndex = 1;
            this.treeListColumn4.Width = 141;
            // 
            // panelControl2
            // 
            this.panelControl2.Controls.Add(this.label1);
            this.panelControl2.Controls.Add(this.btnAddRole);
            this.panelControl2.Controls.Add(this.btnSubmit);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl2.Location = new System.Drawing.Point(0, 456);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(872, 43);
            this.panelControl2.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(7, 19);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(317, 12);
            this.label1.TabIndex = 8;
            this.label1.Text = "注意：修改后，点击【提交修改】按钮才会更新到数据库。";
            // 
            // btnAddRole
            // 
            this.btnAddRole.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddRole.Location = new System.Drawing.Point(638, 8);
            this.btnAddRole.Name = "btnAddRole";
            this.btnAddRole.Size = new System.Drawing.Size(112, 27);
            this.btnAddRole.TabIndex = 7;
            this.btnAddRole.Text = "添加数据源组";
            this.btnAddRole.Click += new System.EventHandler(this.btnAddRole_Click);
            // 
            // btnSubmit
            // 
            this.btnSubmit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSubmit.Location = new System.Drawing.Point(773, 8);
            this.btnSubmit.Name = "btnSubmit";
            this.btnSubmit.Size = new System.Drawing.Size(87, 27);
            this.btnSubmit.TabIndex = 7;
            this.btnSubmit.Text = "提交修改";
            this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
            // 
            // UserDataSrcRightsOptionForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1265, 499);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "UserDataSrcRightsOptionForm";
            this.Text = "用户数据源权限设定";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).EndInit();
            this.grpUser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlUser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListRole)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpDataSrc)).EndInit();
            this.grpDataSrc.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tab)).EndInit();
            this.tab.ResumeLayout(false);
            this.pageProj.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListDataSrc)).EndInit();
            this.pageArea.ResumeLayout(false);
            this.pageArea.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListArea)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.panelControl2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl grpUser;
        private DevExpress.XtraGrid.GridControl gridCtrlUser;
        private DevExpress.XtraGrid.Views.Grid.GridView gvUser;
        private DevExpress.XtraGrid.Columns.GridColumn gcName;
        private DevExpress.XtraGrid.Columns.GridColumn gcDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gcUserRole;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraTreeList.TreeList treeListRole;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleDesc;
        private DevExpress.XtraEditors.GroupControl grpDataSrc;
        private DevExpress.XtraTreeList.TreeList treeListDataSrc;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn2;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn3;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SimpleButton btnAddRole;
        private DevExpress.XtraEditors.SimpleButton btnSubmit;
        private DevExpress.XtraTab.XtraTabControl tab;
        private DevExpress.XtraTab.XtraTabPage pageProj;
        private DevExpress.XtraTab.XtraTabPage pageArea;
        private DevExpress.XtraTreeList.TreeList treeListArea;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn4;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleDistrict;
        private System.Windows.Forms.CheckBox chkAllArea;
    }
}