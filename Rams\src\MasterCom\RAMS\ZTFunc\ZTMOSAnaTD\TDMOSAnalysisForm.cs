﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using Excel = Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Utils;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public delegate bool MosComparer(float mosValue);

    public partial class TDMOSAnalysisForm : MinCloseForm
    {
        public TDMOSAnalysisForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            txtMos.Text = mosThreshold.ToString();
            btnMos.Click += btnOK_Click;
            btnExpore.Click += btnExport_Click;
        }

        public void Analyze(List<TDMOSParam> mosParamList)
        {
            SetCondition();
            analyst = new TDMOSAnalyst(mosParamList, mosComparer);
            List<DataTable> tbList = analyst.Analyze();
            UpdateGridChart(tbList);
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (analyst == null)
            {
                return;
            }

            SetCondition();
            List<DataTable> tbList = analyst.Analyze(mosComparer);
            UpdateGridChart(tbList);
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            MOSFactorSettingForm form = new MOSFactorSettingForm();
            if (form.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            ExcelExporter.TableList = savedTableList;
            ExcelExporter.FactorTable = GetFactorTable(form.MosValue);
            ExcelExporter.MosTitle = GetTitleName();
            ExcelExporter.Tips = new string[] {
                labelControl1.Text, 
                labelControl2.Text, 
                labelControl3.Text,
                "注：以上比例是由所有MOS周期得出"
            };
            ExcelExporter.Export();
        }

        private void FillData(DataTable table, GridControl gc, ChartControl cc)
        {
            int fixit = table.TableName == "MOS Value Section" ? 1 : 0;
            Series series = cc.Series[0];
            series.Points.Clear();
            for (int i = 0; i < table.Rows.Count - fixit; ++i)
            {
                series.Points.Add(
                    new SeriesPoint(table.Rows[i][0], table.Rows[i][1]));
            }
            gc.DataSource = table;
            gc.Refresh();
            cc.Refresh();
        }

        private void SetCondition()
        {
            float tmpMos = 0;
            if (float.TryParse(txtMos.Text, out tmpMos))
            {
                mosThreshold = tmpMos;
            }

            if (cbxCompareType.Text == "<=")
            {
                mosComparer = LessEqual;
            }
            else
            {
                mosComparer = GreatEqual;
            }
            labelControl1.Text = "注：MOS均值与MOS" + cbxCompareType.Text + txtMos.Text + " 是在固定 C/I>=3 并且 BLER<=1 得出。比例是在所有MOS周期得出";
            labelControl2.Text = "注：MOS均值与MOS" + cbxCompareType.Text + txtMos.Text + " 是在固定 切换次数为0 并且 BLER<=1 得出。比例是在所有MOS周期得出";
            labelControl3.Text = "注：MOS均值与MOS" + cbxCompareType.Text + txtMos.Text + " 是在固定 切换次数为0 并且 C/I>=3 得出。比例是在所有MOS周期得出";
        }

        private DataTable GetFactorTable(float factorMos)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("影响因素", typeof(string));
            dt.Columns.Add("影响因素项", typeof(string));
            dt.Columns.Add("MOS均值", typeof(double));
            dt.Columns.Add(GetTitleName(), typeof(double));
            dt.Columns.Add("比例分布", typeof(double));
            dt.Columns.Add("影响因子", typeof(double));
            dt.Columns.Add("实际影响因子", typeof(double));

            // 基本数据收集，同时计算MOS差值
            List<double> diffList = new List<double>();
            double diffSum = 0;
            int tbIdx = 1;
            //float factorMos = 3.5f;
            while (tbIdx < savedTableList.Count)
            {
                for (int i = 0; i < savedTableList[tbIdx].Rows.Count; ++i)
                {
                    double value = (double)savedTableList[tbIdx].Rows[i][1];
                    if (value < factorMos)
                    {
                        dt.Rows.Add(new object[] { 
                            savedTableList[tbIdx].Columns[0].ColumnName,
                            savedTableList[tbIdx].Rows[i][0],
                            savedTableList[tbIdx].Rows[i][1],
                            savedTableList[tbIdx + 1].Rows[i][1],
                            savedTableList[tbIdx + 2].Rows[i][1],
                            0,
                            0
                        });
                        double diff = factorMos - value;
                        diffList.Add(diff);
                        diffSum += diff;
                    }
                }
                tbIdx += 3;
            }

            // 使用MOS差值计算影响因子，同时计算影响因子与比例分布的乘积
            List<double> multiList = new List<double>();
            double multiSum = 0;
            for (int i = 0; i < dt.Rows.Count; ++i)
            {
                double value = diffList[i] / diffSum;
                dt.Rows[i][5] = value;
                double multi = value * (double)dt.Rows[i][4];
                multiList.Add(multi);
                multiSum += multi;
            }

            // 使用乘积计算实际影响因子
            for (int i = 0; i < dt.Rows.Count; ++i)
            {
                dt.Rows[i][6] = multiList[i] / multiSum;
            }

            return dt;
        }

        private bool LessEqual(float mosValue)
        {
            return mosValue <= mosThreshold;   
        }

        private bool GreatEqual(float mosValue)
        {
            return mosValue >= mosThreshold;
        }

        private string GetTitleName()
        {
            return "MOS " + cbxCompareType.Text + " " + txtMos.Text + "比例";
        }

        private void UpdateGridChart(List<DataTable> tbList)
        {
            // grid, chart全部填充
            if (tbList.Count == 10)
            {
                FillData(tbList[0], gridControl1, chartControl1);
                (gridControl1.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl1.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";

                FillData(tbList[1], gridControl2, chartControl2);
                (gridControl2.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl2.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:F}";

                FillData(tbList[2], gridControl3, chartControl3);
                (gridControl3.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl3.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";
                (gridControl3.MainView as GridView).Columns[1].Caption = GetTitleName();
                chartControl3.Titles[0].Text = GetTitleName();

                FillData(tbList[3], gridControl4, chartControl4);
                (gridControl4.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl4.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";

                FillData(tbList[4], gridControl5, chartControl5);
                (gridControl5.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl5.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:F}";

                FillData(tbList[5], gridControl6, chartControl6);
                (gridControl6.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl6.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";
                (gridControl6.MainView as GridView).Columns[1].Caption = GetTitleName();
                chartControl6.Titles[0].Text = GetTitleName();

                FillData(tbList[6], gridControl7, chartControl7);
                (gridControl7.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl7.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";

                FillData(tbList[7], gridControl8, chartControl8);
                (gridControl8.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl8.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:F}";

                FillData(tbList[8], gridControl9, chartControl9);
                (gridControl9.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl9.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";
                (gridControl9.MainView as GridView).Columns[1].Caption = GetTitleName();
                chartControl9.Titles[0].Text = GetTitleName();

                FillData(tbList[9], gridControl10, chartControl10);
                (gridControl10.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl10.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";

                savedTableList = new List<DataTable>(tbList);
            }
            // grid chart部分刷新
            else if (tbList.Count == 6)
            {
                FillData(tbList[0], gridControl2, chartControl2);
                (gridControl2.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl2.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:F}";

                FillData(tbList[1], gridControl3, chartControl3);
                (gridControl3.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl3.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";
                (gridControl3.MainView as GridView).Columns[1].Caption = GetTitleName();
                chartControl3.Titles[0].Text = GetTitleName();

                FillData(tbList[2], gridControl5, chartControl5);
                (gridControl5.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl5.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:F}";

                FillData(tbList[3], gridControl6, chartControl6);
                (gridControl6.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl6.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";
                (gridControl6.MainView as GridView).Columns[1].Caption = GetTitleName();
                chartControl6.Titles[0].Text = GetTitleName();

                FillData(tbList[4], gridControl8, chartControl8);
                (gridControl8.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl8.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:F}";

                FillData(tbList[5], gridControl9, chartControl9);
                (gridControl9.MainView as GridView).Columns[1].DisplayFormat.FormatType = FormatType.Numeric;
                (gridControl9.MainView as GridView).Columns[1].DisplayFormat.FormatString = "{0:P}";
                (gridControl9.MainView as GridView).Columns[1].Caption = GetTitleName();
                chartControl9.Titles[0].Text = GetTitleName();

                savedTableList[1] = tbList[0];
                savedTableList[2] = tbList[1];
                savedTableList[4] = tbList[2];
                savedTableList[5] = tbList[3];
                savedTableList[7] = tbList[4];
                savedTableList[8] = tbList[5];
            }
        }

        private float mosThreshold = 2.8f;
        private MosComparer mosComparer = null;
        private TDMOSAnalyst analyst = null;
        private List<DataTable> savedTableList = null;
    }

    public class TDMOSAnalyst
    {
        public TDMOSAnalyst(List<TDMOSParam> tdMosParamList, MosComparer comparer)
        {
            IsValidMosValue = comparer;
            this.tdMosParamList = tdMosParamList;

            fixedC2I = 3;
            fixedBler = 1;
            fixedHandover = 0;
        }

        /// <summary>
        /// 分析全部数据
        /// </summary>
        /// <returns></returns>
        public List<DataTable> Analyze()
        {
            List<DataTable> tbList = new List<DataTable>();
            PrepareDataInvariable();
            PrepareDataVariable();
            tbList.Add(AnalyzeGridChart1());
            tbList.Add(AnalyzeGridChart2());
            tbList.Add(AnalyzeGridChart3());
            tbList.Add(AnalyzeGridChart4());
            tbList.Add(AnalyzeGridChart5());
            tbList.Add(AnalyzeGridChart6());
            tbList.Add(AnalyzeGridChart7());
            tbList.Add(AnalyzeGridChart8());
            tbList.Add(AnalyzeGridChart9());
            tbList.Add(AnalyzeGridChart10());
            return tbList;
        }

        /// <summary>
        /// 分析部分数据，即当MOS门限改变时
        /// </summary>
        /// <param name="comparer"></param>
        /// <returns></returns>
        public List<DataTable> Analyze(MosComparer comparer)
        {
            IsValidMosValue = comparer;
            if (allMosValueList == null)
            {
                return Analyze();
            }

            List<DataTable> tbList = new List<DataTable>();
            //tbList.Add(AnalyzeGridChart1());
            tbList.Add(AnalyzeGridChart2());
            tbList.Add(AnalyzeGridChart3());
            //tbList.Add(AnalyzeGridChart4());
            tbList.Add(AnalyzeGridChart5());
            tbList.Add(AnalyzeGridChart6());
            //tbList.Add(AnalyzeGridChart7());
            tbList.Add(AnalyzeGridChart8());
            tbList.Add(AnalyzeGridChart9());
            //tbList.Add(AnalyzeGridChart10());
            return tbList;
        }

        /// <summary>
        /// 以下变量与设置条件无关
        /// </summary>
        private void PrepareDataInvariable()
        {
            allMosValueList = new List<float>();
            allModeList = new List<int>();
            allHandoverValueList = new List<int>();
            allC2IValueList = new List<float>();
            allBlerValueList = new List<float>();
            allHandoverSampleArray = new int[SectionManager.HandoverSectionCount];
            allC2ISampleArray = new int[SectionManager.C2ISectionCount];
            allBlerSampleArray = new int[SectionManager.BlerSectionCount];
            foreach (TDMOSParam param in tdMosParamList)
            {
                allMosValueList.Add(param.MOS);

                int handover = param.OwnParam.HandoverCount + param.OtherParam.HandoverCount;
                float c2i = (param.OwnParam.TD_PCCPCH_C2I_Mean + param.OtherParam.TD_PCCPCH_C2I_Mean) / 2;
                float bler = (param.OwnParam.TD_BLER_Mean + param.OtherParam.TD_BLER_Mean) / 2;
                int mode = param.MSMode;

                ++allHandoverSampleArray[SectionManager.GetHandoverIndex(handover)];
                ++allC2ISampleArray[SectionManager.GetC2IIndex(c2i)];
                ++allBlerSampleArray[SectionManager.GetBlerIndex(bler)];
                allHandoverValueList.Add(handover);
                allC2IValueList.Add(c2i);
                allBlerValueList.Add(bler);
                allModeList.Add(mode);
            }
        }

        /// <summary>
        /// 以下变量与设置条件相关（目前固定）
        /// </summary>
        private void PrepareDataVariable()
        {
            handoverMosValueList = new List<float>();
            handoverValueList = new List<int>();
            c2iMosValueList = new List<float>();
            c2iValueList = new List<float>();
            blerMosValueList = new List<float>();
            blerValueList = new List<float>();

            handoverSampleArray = new int[SectionManager.HandoverSectionCount];
            c2iSampleArray = new int[SectionManager.C2ISectionCount];
            blerSampleArray = new int[SectionManager.BlerSectionCount];

            foreach (TDMOSParam param in tdMosParamList)
            {
                int handover = param.OwnParam.HandoverCount + param.OtherParam.HandoverCount;
                float c2i = (param.OwnParam.TD_DPCH_C2I_Mean + param.OtherParam.TD_DPCH_C2I_Mean)/2;
                float bler = (param.OwnParam.TD_BLER_Mean + param.OtherParam.TD_BLER_Mean)/2;

                if (c2i >= fixedC2I && bler <= fixedBler)
                {
                    handoverMosValueList.Add(param.MOS);
                    handoverValueList.Add(handover);
                    int idx = SectionManager.GetHandoverIndex(handover);
                    ++handoverSampleArray[idx];
                }
                if (handover == fixedHandover && bler <= fixedBler)
                {
                    c2iMosValueList.Add(param.MOS);
                    c2iValueList.Add(c2i);
                    int idx = SectionManager.GetC2IIndex(c2i);
                    ++c2iSampleArray[idx];
                }
                if (handover == fixedHandover && c2i >= fixedC2I)
                {
                    blerMosValueList.Add(param.MOS);
                    blerValueList.Add(bler);
                    int idx = SectionManager.GetBlerIndex(bler);
                    ++blerSampleArray[idx];
                }
            }
        }

        private DataTable AnalyzeGridChart1()
        {
            DataTable dt = new DataTable();
            dt.TableName = "MOS Value Section";
            dt.Columns.Add("MOS值", typeof(string));
            dt.Columns.Add("比例", typeof(double));
            dt.Columns.Add("总样本", typeof(string));
            dt.Columns.Add("GSM样本", typeof(string));
            dt.Columns.Add("TD样本", typeof(string));

            // 数据处理
            int sectionCount = SectionManager.MosValueSectionCount;
            int count = Math.Min(allMosValueList.Count, allModeList.Count);

            int[] sampleSum = new int[sectionCount];
            int[] sampleGsm = new int[sectionCount];
            int[] sampleTd = new int[sectionCount];
            float mosValueSum = 0, mosValueGsm = 0, mosValueTd = 0;
            int cntSum = 0, cntGsm = 0, cntTd = 0;

            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetMosValueIndex(allMosValueList[i]);

                ++sampleSum[idx];
                ++cntSum;
                mosValueSum += allMosValueList[i];

                if (allModeList[i] != 2)
                {
                    ++sampleGsm[idx];
                    ++cntGsm;
                    mosValueGsm += allMosValueList[i];
                }
                else
                {
                    ++sampleTd[idx];
                    ++cntTd;
                    mosValueTd += allMosValueList[i];
                }
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { 
                    SectionManager.GetMosValueString(i), 
                    count == 0 ? 0 : sampleSum[i] * 1d / count,
                    sampleSum[i], sampleGsm[i], sampleTd[i] 
                });
            }
            dt.Rows.Add(new object[] {
                "MOS均值",
                null,
                string.Format("{0:F2}", cntSum == 0 ? 0 : mosValueSum / cntSum),
                string.Format("{0:F2}", cntGsm == 0 ? 0 : mosValueGsm / cntGsm),
                string.Format("{0:F2}", cntTd == 0 ? 0 : mosValueTd / cntTd),
            });

            return dt;
        }

        /// <summary>
        /// 每段切数的MOS总值 / 该段切数的发生次数
        /// </summary>
        /// <returns></returns>
        private DataTable AnalyzeGridChart2()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("切换次数", typeof(string));
            dt.Columns.Add("MOS均值", typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.HandoverSectionCount;
            float[] sumArray = new float[sectionCount];
            int[] cntArray = new int[sectionCount];
            int count = Math.Min(handoverMosValueList.Count, handoverValueList.Count);
            for (int i = 0; i < count; ++i)
            {
                if (!IsValidMosValue(handoverMosValueList[i]))
                {
                    continue;
                }
                int idx = SectionManager.GetHandoverIndex(handoverValueList[i]);
                sumArray[idx] += handoverMosValueList[i];
                ++cntArray[idx];
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { 
                    SectionManager.GetHandoverString(i), 
                    cntArray[i] != 0 ? sumArray[i] * 1d / cntArray[i] : 0,
                    handoverSampleArray[i]});
            }

            return dt;
        }

        /// <summary>
        /// 每段满足条件的切数 / 该段切数的发生次数
        /// </summary>
        /// <returns></returns>
        private DataTable AnalyzeGridChart3()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("切换次数", typeof(string));
            dt.Columns.Add(GetColumnName(), typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.HandoverSectionCount;
            int[] cntArray = new int[sectionCount];
            int[] validArray = new int[sectionCount];
            int count = Math.Min(handoverMosValueList.Count, handoverValueList.Count);
            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetHandoverIndex(handoverValueList[i]);
                ++cntArray[idx];
                validArray[idx] += IsValidMosValue(handoverMosValueList[i]) ? 1 : 0;
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { 
                    SectionManager.GetHandoverString(i), 
                    cntArray[i] != 0 ? validArray[i] * 1d / cntArray[i] : 0,
                    handoverSampleArray[i]});
            }

            return dt;
        }

        /// <summary>
        /// 每段切数 / 各段切数总和
        /// </summary>
        /// <returns></returns>
        private DataTable AnalyzeGridChart4()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("切换次数", typeof(string));
            dt.Columns.Add("比例", typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.HandoverSectionCount;
            int[] cntArray = new int[sectionCount];
            int count = allHandoverValueList.Count;
            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetHandoverIndex(allHandoverValueList[i]);
                ++cntArray[idx];
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { 
                    SectionManager.GetHandoverString(i), 
                    count != 0 ? cntArray[i] * 1d / count : 0, 
                    allHandoverSampleArray[i]});
            }

            return dt;
        }

        private DataTable AnalyzeGridChart5()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("C/I", typeof(string));
            dt.Columns.Add("MOS均值", typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.C2ISectionCount;
            float[] sumArray = new float[sectionCount];
            int[] cntArray = new int[sectionCount];
            int count = Math.Min(c2iMosValueList.Count, c2iValueList.Count);
            for (int i = 0; i < count; ++i)
            {
                if (!IsValidMosValue(c2iMosValueList[i]))
                {
                    continue;
                }
                int idx = SectionManager.GetC2IIndex(c2iValueList[i]);
                sumArray[idx] += c2iMosValueList[i];
                ++cntArray[idx];
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                int j = sectionCount - i - 1;
                dt.Rows.Add(new object[] { 
                    SectionManager.GetC2IString(j), 
                    cntArray[j] != 0 ? sumArray[j] * 1d / cntArray[j] : 0,
                    c2iSampleArray[j]});
            }

            return dt;
        }

        private DataTable AnalyzeGridChart6()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("C/I", typeof(string));
            dt.Columns.Add(GetColumnName(), typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.C2ISectionCount;
            int[] cntArray = new int[sectionCount];
            int[] validArray = new int[sectionCount];
            int count = Math.Min(c2iMosValueList.Count, c2iValueList.Count);
            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetC2IIndex(c2iValueList[i]);
                ++cntArray[idx];
                validArray[idx] += IsValidMosValue(c2iMosValueList[i]) ? 1 : 0;
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                int j = sectionCount - i - 1;
                dt.Rows.Add(new object[] {
                    SectionManager.GetC2IString(j), 
                    cntArray[j] != 0 ? validArray[j] * 1d / cntArray[j] : 0,
                    c2iSampleArray[j]});
            }

            return dt;
        }

        private DataTable AnalyzeGridChart7()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("C/I", typeof(string));
            dt.Columns.Add("比例", typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.C2ISectionCount;
            int[] cntArray = new int[sectionCount];
            int count = allC2IValueList.Count;
            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetC2IIndex(allC2IValueList[i]);
                ++cntArray[idx];
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                int j = sectionCount - i - 1;
                dt.Rows.Add(new object[] {
                    SectionManager.GetC2IString(j), 
                    count != 0 ? cntArray[j] * 1d / count : 0, 
                    allC2ISampleArray[j]});
            }

            return dt;
        }

        private DataTable AnalyzeGridChart8()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("BLER", typeof(string));
            dt.Columns.Add("MOS均值", typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.BlerSectionCount;
            float[] sumArray = new float[sectionCount];
            int[] cntArray = new int[sectionCount];
            int count = Math.Min(blerMosValueList.Count, blerValueList.Count);
            for (int i = 0; i < count; ++i)
            {
                if (!IsValidMosValue(blerMosValueList[i]))
                {
                    continue;
                }
                int idx = SectionManager.GetBlerIndex(blerValueList[i]);
                sumArray[idx] += blerMosValueList[i];
                ++cntArray[idx];
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { SectionManager.GetBlerString(i), 
                    cntArray[i] != 0 ? sumArray[i] * 1d / cntArray[i] : 0,
                    blerSampleArray[i]});
            }

            return dt;
        }

        private DataTable AnalyzeGridChart9()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("BLER", typeof(string));
            dt.Columns.Add(GetColumnName(), typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.BlerSectionCount;
            int[] cntArray = new int[sectionCount];
            int[] validArray = new int[sectionCount];
            int count = Math.Min(blerMosValueList.Count, blerValueList.Count);
            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetBlerIndex(blerValueList[i]);
                ++cntArray[idx];
                validArray[idx] += IsValidMosValue(blerMosValueList[i]) ? 1 : 0;
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { SectionManager.GetBlerString(i), 
                    cntArray[i] != 0 ? validArray[i] * 1d / cntArray[i] : 0,
                    blerSampleArray[i]});
            }

            return dt;
        }

        private DataTable AnalyzeGridChart10()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("BLER", typeof(string));
            dt.Columns.Add("比例", typeof(double));
            dt.Columns.Add("样本数", typeof(int));

            // 数据处理
            int sectionCount = SectionManager.BlerSectionCount;
            int[] cntArray = new int[sectionCount];
            int count = allBlerValueList.Count;
            for (int i = 0; i < count; ++i)
            {
                int idx = SectionManager.GetBlerIndex(allBlerValueList[i]);
                ++cntArray[idx];
            }
            for (int i = 0; i < sectionCount; ++i)
            {
                dt.Rows.Add(new object[] { 
                    SectionManager.GetBlerString(i), 
                    count != 0 ? cntArray[i] * 1d / count : 0, 
                    allBlerSampleArray[i]});
            }

            return dt;
        }

        private string GetColumnName()
        {
            string name = "...";
            return name;
        }

        private MosComparer IsValidMosValue;            // 用于分析的输入
        private readonly List<TDMOSParam> tdMosParamList;        // 用于分析的输入
        private List<int> allModeList;                  // 全周期的MSMODE列表
        private List<float> allMosValueList;            // 全周期的MOS值列表
        private List<int> allHandoverValueList;         
        private List<float> allC2IValueList;            
        private List<float> allBlerValueList;           

        private List<float> handoverMosValueList;       // 固定模式的MOS值列表
        private List<int> handoverValueList;            // 固定模式的切换次数列表
        private List<float> c2iMosValueList;
        private List<float> c2iValueList;
        private List<float> blerMosValueList;
        private List<float> blerValueList;

        private int[] allHandoverSampleArray;           // 全周期分段后的切换次数的样本个数
        private int[] allC2ISampleArray;                
        private int[] allBlerSampleArray;
        private int[] handoverSampleArray;              // 固定模式分段后的切换次数的样本个数
        private int[] c2iSampleArray;
        private int[] blerSampleArray;

        private readonly float fixedC2I;                         // c2i固定值
        private readonly float fixedBler;
        private readonly int fixedHandover;
    }

    public static class SectionManager
    {
        private static int minMosValue = 1;
        private static int maxMosValue = 5;
        private static float mosStep = 0.2f;
        public static int GetMosValueIndex(float mos)
        {
            int len = MosValueSectionCount;
            for (int i = 0; i < len; ++i)
            {
                if (mos < mosStep * i + minMosValue)
                {
                    return i;
                }
            }
            return len - 1;
        }
        public static int MosValueSectionCount
        {
            get { return Convert.ToInt32((maxMosValue - minMosValue) / mosStep); }
        }
        public static string GetMosValueString(int index)
        {
            index = Math.Min(MosValueSectionCount, Math.Max(index, 0));
            return string.Format("[{0:F1}~{1:F1})",
                minMosValue + index * mosStep,
                minMosValue + (index + 1) * mosStep);
        }


        private static int[] handoverSection = { 0, 1, 2, 3, 4 };
        public static int GetHandoverIndex(int handover)
        {
            return Math.Max(0, Math.Min(handover, handoverSection.Length - 1));
        }

        public static string GetHandoverString(int handover)
        {
            if (handover >= handoverSection[handoverSection.Length - 1])
            {
                return string.Format("[{0}~Max)", handoverSection[handoverSection.Length - 1]);
            }
            return handover.ToString();
        }

        public static int HandoverSectionCount
        {
            get { return handoverSection.Length; }
        }

        private static float[] c2iSection = { float.MinValue, -3, 10, 15, 20, float.MaxValue,};
        public static int GetC2IIndex(float c2iValue)
        {
            for (int i = 0; i < c2iSection.Length; ++i)
            {
                if (c2iValue < c2iSection[i])
                {
                    return i - 1;
                }
            }
            return 0;
        }
        public static string GetC2IString(int index)
        {
            if (index <= 0)
            {
                return string.Format("[Min~{0:F0})", c2iSection[1]);
            }
            if (index >= c2iSection.Length - 2)
            {
                return string.Format("[{0:F0}~Max)", c2iSection[c2iSection.Length - 2]);
            }
            return string.Format("[{0:F0}~{1:F0})", c2iSection[index], c2iSection[index + 1]);
        }
        public static int C2ISectionCount
        {
            get { return c2iSection.Length - 1; }
        }

        private static float[] blerSection = { 0, 1, 3, float.MaxValue, };
        public static int GetBlerIndex(float blerValue)
        {
            for (int i = 0; i < blerSection.Length; ++i)
            {
                if (blerValue < blerSection[i])
                {
                    return Math.Max(0, i - 1);
                }
            }
            return 0;
        }
        public static string GetBlerString(int index)
        {
            index = Math.Max(0, index);
            if (index >= blerSection.Length - 2)
            {
                return string.Format("[{0:F0}~Max)", blerSection[blerSection.Length - 2]);
            }
            return string.Format("[{0:F0}~{1:F0})", blerSection[index], blerSection[index + 1]);
        }
        public static int BlerSectionCount
        {
            get { return blerSection.Length - 1; }
        }
    }
}
