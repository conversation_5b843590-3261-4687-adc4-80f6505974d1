using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Collections;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FarCellCoverForm : MinCloseForm
    {
        public FarCellCoverForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;
        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate(object row)
            {
                if (row is FarCellCover)
                {
                    FarCellCover item = row as FarCellCover;
                    IList list = objectListView.Objects as IList;
                    return list.IndexOf(item) + 1;
                }
                return "";
            };
       }

        public void FillData(List<FarCellCover> farCellCoverList)
        {
            FarCellCover item = farCellCoverList[0];
            if (item.testPointType is ScanTestPoint_TD)
            {
                olvColumnLAC.Text = "LAC";
                olvColumnCI.Text = "CI";
                olvColumnBCCH.Text = "ARFCN";
                olvColumnBSIC.Text = "CPI";
            }
            else if (item.testPointType is ScanTestPoint_LTE)
            {
                olvColumnLAC.Text = "TAC";
                olvColumnCI.Text = "ECI";
                olvColumnBCCH.Text = "EARFCN";
                olvColumnBSIC.Text = "PCI";
            }
            else if (item.testPointType is ScanTestPoint_NR)
            {
                olvColumnLAC.Text = "TAC";
                olvColumnCI.AspectName = "NCI";
                olvColumnCI.Text = "NCI";
                olvColumnBCCH.Text = "ARFCN";
                olvColumnBSIC.Text = "PCI";
            }
            else
            {
                olvColumnLAC.Text = "LAC";
                olvColumnCI.Text = "CI";
                olvColumnBCCH.Text = "BCCH";
                olvColumnBSIC.Text = "BSIC";
            }
            objectListView.ClearObjects();
            objectListView.SetObjects(farCellCoverList);
            MainModel.RefreshLegend();
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (objectListView.SelectedObject is FarCellCover)
            {
                FarCellCover info = objectListView.SelectedObject as FarCellCover;
                MainModel.CurFarCellCover = info;
                MainModel.ClearDTData();
                foreach (TestPoint tp in info.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }

                if (info.TestPoints[0] is ScanTestPoint_TD)
                {
                    MainModel.FireSetDefaultMapSerialTheme("TDSCDMA_SCAN", "PCCPCH_RSCP");
                }
                else if (info.TestPoints[0] is ScanTestPoint_LTE) 
                {
                    MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
                }
                else if (info.TestPoints[0] is ScanTestPoint_G) 
                {
                    MainModel.FireSetDefaultMapSerialTheme("GSM_SCAN", "RxLev");
                }
                else if (info.TestPoints[0] is ScanTestPoint_NR)
                {
                    MainModel.FireSetDefaultMapSerialTheme("NR_SCAN", "SSB_RSRP");
                }
                
                MainModel.FireDTDataChanged(this);
                mModel.DrawFlyLines = true;
                GoToView(info);
                mapForm.GetDTLayer().Invalidate();
            }
        }

        private void GoToView(FarCellCover farCellCover)
        {
            double ltLong = 100000;
            double ltLat = -100000;
            double brLong = -100000;
            double brLat = 100000;
            foreach (TestPoint tp in farCellCover.TestPoints)
            {
                if (tp.Longitude < ltLong)
                {
                    ltLong = tp.Longitude;
                }
                if (tp.Longitude > brLong)
                {
                    brLong = tp.Longitude;
                }
                if (tp.Latitude < brLat)
                {
                    brLat = tp.Latitude;
                }
                if (tp.Latitude > ltLat)
                {
                    ltLat = tp.Latitude;
                }
            }

            if (farCellCover.LongitudeCell < ltLong)
            {
                ltLong = farCellCover.LongitudeCell;
            }
            if (farCellCover.LongitudeCell > brLong)
            {
                brLong = farCellCover.LongitudeCell;
            }
            if (farCellCover.LatitudeCell < brLat)
            {
                brLat = farCellCover.LatitudeCell;
            }
            if (farCellCover.LatitudeCell > ltLat)
            {
                ltLat = farCellCover.LatitudeCell;
            }
            mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(objectListView);
        }
    }
}