﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;


namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckCellItem
    {
        public int SN { get; set; }
        public LTECell ServCell { get; set; }
        public int RSRPCount { get; set; }
        public float RSRPTotal { get; set; }
        public Dictionary<string, ZTLteNBCellCheckNBItem> NBDic { get; set; }   //Dictionary<cellName,ZTLteNBCellCheckNBItem> 

        public ZTLteNBCellCheckCellItem(LTECell lteCell, float rsrp)
        {
            ServCell = lteCell;
            RSRPCount = 1;
            RSRPTotal = rsrp;
            NBDic = new Dictionary<string, ZTLteNBCellCheckNBItem>();
        }

        public void MergeData(float rsrp)
        {
            RSRPCount++;
            RSRPTotal += rsrp;
        }

 
        #region 预处理
        public string AvgRSRP
        {
            get
            {
                if (RSRPCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)RSRPCount),2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        #endregion
    }

    public class ZTLteNBCellCheckNBItem
    {
        public LTECell NBCell_LTE { get; set; }
        public Cell NBCell_GSM { get; set; }
        public WCell NBCell_W { get; set; }
        public int RSRPCount { get; set; }
        public float RSRPTotal { get; set; }
        public int RxQualCount { get; set; }    //GSM/W 小区做主服时的质量
        public float RxQualTotal { get; set; }
        public int Score { get; set; }
        public string Status { get; set; }
        public string StatusByMsg { get; set; }
        public double Distance { get; set; }

        private readonly List<int> msgFreqs = new List<int>();

        public ZTLteNBCellCheckNBItem(LTECell nbCell, float RSRPTotal, int index, LTECell servCell)
        {
            this.NBCell_LTE = nbCell;
            this.RSRPCount = 1;
            this.RSRPTotal = RSRPTotal;
            this.Status = "未知";
            this.StatusByMsg = "未知";
            this.Distance = nbCell.GetDistance(servCell.Longitude, servCell.Latitude);
            this.Score = getScoreByIndex(index);
        }

        public ZTLteNBCellCheckNBItem(Cell nbCell, float RSRPTotal, float? RxQualTotal, int index, LTECell servCell)
        {
            this.NBCell_GSM = nbCell;
            this.RSRPCount = 1;
            this.RSRPTotal = RSRPTotal;

            if (RxQualTotal != null && RxQualTotal >= 0 && RxQualTotal <= 7)
            {
                this.RxQualCount = 1;
                this.RxQualTotal = (float)RxQualTotal;
            }

            this.Status = "未知";
            this.StatusByMsg = "未知";
            this.Distance = nbCell.GetDistance(servCell.Longitude, servCell.Latitude);
            this.Score = getScoreByIndex(index);
        }
        public ZTLteNBCellCheckNBItem(WCell nbCell, float RSRPTotal, float? RxQualTotal, int index, LTECell servCell)
        {
            this.NBCell_W = nbCell;
            this.RSRPCount = 1;
            this.RSRPTotal = RSRPTotal;

            if (RxQualTotal != null && RxQualTotal >= -40 && RxQualTotal <= 0)
            {
                this.RxQualCount = 1;
                this.RxQualTotal = (float)RxQualTotal;
            }

            this.Status = "未知";
            this.StatusByMsg = "未知";
            this.Distance = nbCell.GetDistance(servCell.Longitude, servCell.Latitude);
            this.Score = getScoreByIndex(index);
        }
        public void AddRangeFreq(List<int> freqs)
        {
            msgFreqs.AddRange(freqs);
        }
        public void SetMsgStatus()
        {
            if (msgFreqs.Count <= 0)
            {
                //
            }
            else if (msgFreqs.Contains(NBCell_GSM.BCCH))
            {
                StatusByMsg = "在配置中";
            }
            else
            {
                StatusByMsg = "漏配";
            }
        }

        private readonly List<int> msgFddPCIs = new List<int>();
        public void AddRangePci(List<int> pcis)
        {
            msgFddPCIs.AddRange(pcis);
        }

        public void SetMsgPciStatus()
        {
            if (msgFddPCIs.Count <= 0)
            {
                //
            }
            else if (msgFddPCIs.Contains(NBCell_W.PSC))
            {
                StatusByMsg = "在配置中";
            }
            else
            {
                StatusByMsg = "漏配";
            }
        }

        public void AccuScore(int index)
        {
            this.Score += getScoreByIndex(index);
        }

        public int getScoreByIndex(int index)
        {
            int score = 0;
            switch (index)
            {
                case 0:
                    score = 6;
                    break;
                case 1:
                    score = 5;
                    break;
                case 2:
                    score = 4;
                    break;
                case 3:
                    score = 3;
                    break;
                case 4:
                    score = 2;
                    break;
                case 5:
                    score = 1;
                    break;
                default:
                    break;
            }

            return score;
        }

        public void MergeData(float rsrp, int index)
        {
            RSRPCount ++;
            RSRPTotal += rsrp;
            Score += getScoreByIndex(index);
        }

        public void MergeData(float rsrp, float? rxqual, int index)
        {
            RSRPCount++;
            RSRPTotal += rsrp;
            Score += getScoreByIndex(index);

            if (rxqual != null && rxqual >= 0 && rxqual <= 7)
            {
                RxQualCount++;
                RxQualTotal += (float)rxqual;
            }
        }


        public static IComparer<ZTLteNBCellCheckNBItem> GetCompareByScore()
        {
            if (comparerByScore == null)
            {
                comparerByScore = new ComparerByScore();
            }
            return comparerByScore;
        }
        public class ComparerByScore : IComparer<ZTLteNBCellCheckNBItem>
        {
            public int Compare(ZTLteNBCellCheckNBItem x, ZTLteNBCellCheckNBItem y)
            {
                return (y.Score - x.Score);
            }
        }
        private static IComparer<ZTLteNBCellCheckNBItem> comparerByScore;

        #region 预处理
        public string AvgRSRP
        {
            get
            {
                if (RSRPCount > 0)
                {
                    return Math.Round((RSRPTotal / (float)RSRPCount), 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string AvgRxQual
        {
            get
            {
                if (RxQualCount > 0)
                {
                    return Math.Round((RxQualTotal / (float)RxQualCount), 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string DistanceStr
        {
            get
            {
                if (Distance != -999)
                {
                    return Math.Round(Distance, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string CellName
        {
            get
            {
                if (NBCell_LTE != null)
                {
                    return NBCell_LTE.Name;
                }
                else if (NBCell_GSM != null)
                {
                    return NBCell_GSM.Name;
                }
                else if (NBCell_W != null)
                {
                    return NBCell_W.Name;
                }
                else
                {
                    return "";
                }
            }
        }

        public int TAC
        {
            get
            {
                if (NBCell_LTE != null)
                {
                    return NBCell_LTE.TAC;
                }
                else if (NBCell_GSM != null)
                {
                    return NBCell_GSM.LAC;
                }
                else if (NBCell_W != null)
                {
                    return NBCell_W.LAC;
                }
                else
                {
                    return -255;
                }
            }
        }

        public int CellID
        {
            get
            {
                if (NBCell_LTE != null)
                {
                    return NBCell_LTE.SCellID;
                }
                else if (NBCell_GSM != null)
                {
                    return NBCell_GSM.CI;
                }
                else if (NBCell_W != null)
                {
                    return NBCell_W.CI;
                }
                else
                {
                    return -255;
                }
            }
        }

        public int EARFCN
        {
            get
            {
                if (NBCell_LTE != null)
                {
                    return NBCell_LTE.EARFCN;
                }
                else if (NBCell_GSM != null)
                {
                    return NBCell_GSM.BCCH;
                }
                else if (NBCell_W != null)
                {
                    return NBCell_W.UARFCN;
                }
                else
                {
                    return -255;
                }
            }
        }

        public int PCI
        {
            get
            {
                if (NBCell_LTE != null)
                {
                    return NBCell_LTE.PCI;
                }
                else if (NBCell_GSM != null)
                {
                    return NBCell_GSM.BSIC;
                }
                else if (NBCell_W != null)
                {
                    return NBCell_W.PSC;
                }
                else
                {
                    return -255;
                }
            }
        }

        public string NBNetType
        {
            get
            {
                if (NBCell_LTE != null)
                {
                    return "LTE";
                }
                else if (NBCell_GSM != null)
                {
                    return "GSM";
                }
                else if (NBCell_W != null)
                {
                    return "WCDMA";
                }
                else
                {
                    return "";
                }
            }
        }



        #endregion
    }

    public class ZTLteNBCellCheckCellMsgCfg
    {
        public LTECell CurCell { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }

        public Dictionary<int, Dictionary<int, int>> NBConfigDic { get; set; }    //Dictionary<EARFCN,Dictionary<PCI,count>>

        public string DateTimeHoMsg { get; set; }
        public string DateTimeNBConfigMsg { get; set; }
        public string DateTimeSIB1Msg { get; set; }
        public string DateTimeMRMsg { get; set; }

        public bool IsGotHOMsg { get; set; }
        public bool IsGotNBConfigMsg { get; set; }
        public bool IsGotSIB1Msg { get; set; }
        public bool IsGotMRMsg { get; set; }

        public TestPoint Tp { get; set; }

        public ZTLteNBCellCheckCellMsgCfg()
        {
            CurCell = null;
            TAC = 0;
            ECI = 0;
            EARFCN = 0;
            PCI = 0;

            NBConfigDic = new Dictionary<int, Dictionary<int, int>>();

            IsGotHOMsg = false;
            IsGotNBConfigMsg = false;
            IsGotSIB1Msg = false;
            IsGotMRMsg = false;
            DateTimeHoMsg = "";
            DateTimeNBConfigMsg = "";
            DateTimeSIB1Msg = "";
            DateTimeMRMsg = "";
        }
    }

    public class ZTLteNBCellCheckCondition
    {
        public int SampleCount { get; set; }        //采样点门限
        public int RSRP { get; set; }               //信号强度门限
        public int Distance { get; set; }           //距离门限
        public int TimeSpan { get; set; }           //时间跨度门限，用于GSM漏配核查

        /// <summary>
        /// 移动的只显示GSM，联通的只显示WCDMA
        /// </summary>
        public bool IsShowGSMNbCellOnly { get; set; }

        public ZTLteNBCellCheckCondition()
        {
            SampleCount = 15;
            RSRP = -110;
            Distance = 1000;
            TimeSpan = 10;
            IsShowGSMNbCellOnly = false;
        }
    }
}

