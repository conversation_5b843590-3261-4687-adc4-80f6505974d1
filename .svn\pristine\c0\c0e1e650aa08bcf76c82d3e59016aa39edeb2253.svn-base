﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class AnalyzeTDMOSByRegion : TDMOSAnaByRegion
    {
        public AnalyzeTDMOSByRegion(MainModel mm) : base(mm)
        {
            mainModel = mm;
        }

        public override string Name
        {
            get { return "TD-MOS关联分析(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13043, this.Name);
        }

        protected override void doWithMOSResult(List<TDMOSParam> mosParamList)
        {
            //keepParamList = mosParamList;

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(TDMOSAnalysisForm).FullName);
            TDMOSAnalysisForm form = obj == null ? null : obj as TDMOSAnalysisForm;
            if (form == null || form.IsDisposed)
            {
                form = new TDMOSAnalysisForm(mainModel);
            }
            form.Analyze(mosParamList);
            if (!form.Visible)
            {
                form.Show(mainModel.MainForm);
            }
        }
        
        /*
        private static List<TDMOSParam> keepParamList;
        protected override void query()
        {
            if (keepParamList != null)
            {
                doWithMOSResult(keepParamList);
                return;
            }
            base.query();
        }
         */
    }
}
