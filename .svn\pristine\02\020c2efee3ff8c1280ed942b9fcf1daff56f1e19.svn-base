﻿namespace MasterCom.RAMS.Func
{
    partial class ZTRoutingAreaUpdateTooMachForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTRoutingAreaUpdateTooMachForm));
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.lblTime = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel4 = new System.Windows.Forms.ToolStripLabel();
            this.lblCount = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel3 = new System.Windows.Forms.ToolStripLabel();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTimes = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTA = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxlev = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemFoldAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miES = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.SystemColors.Control;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblTime,
            this.toolStripLabel4,
            this.lblCount,
            this.toolStripLabel3});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(991, 25);
            this.toolStrip1.TabIndex = 3;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // lblTime
            // 
            this.lblTime.Name = "lblTime";
            this.lblTime.Size = new System.Drawing.Size(65, 22);
            this.lblTime.Text = "重选时间：";
            // 
            // toolStripLabel4
            // 
            this.toolStripLabel4.Name = "toolStripLabel4";
            this.toolStripLabel4.Size = new System.Drawing.Size(47, 22);
            this.toolStripLabel4.Text = "秒以内 ";
            // 
            // lblCount
            // 
            this.lblCount.Name = "lblCount";
            this.lblCount.Size = new System.Drawing.Size(71, 22);
            this.lblCount.Text = "重选次数 ≥";
            // 
            // toolStripLabel3
            // 
            this.toolStripLabel3.Name = "toolStripLabel3";
            this.toolStripLabel3.Size = new System.Drawing.Size(17, 22);
            this.toolStripLabel3.Text = "次";
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnName);
            this.treeListView.AllColumns.Add(this.olvColumnTimes);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnTime);
            this.treeListView.AllColumns.Add(this.olvColumnLat);
            this.treeListView.AllColumns.Add(this.olvColumnLong);
            this.treeListView.AllColumns.Add(this.olvColumnTA);
            this.treeListView.AllColumns.Add(this.olvColumnRxlev);
            this.treeListView.AllowColumnReorder = true;
            this.treeListView.AlternateRowBackColor = System.Drawing.Color.Ivory;
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnName,
            this.olvColumnTimes,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnTime,
            this.olvColumnLat,
            this.olvColumnLong,
            this.olvColumnTA,
            this.olvColumnRxlev});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip1;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.IsNeedShowOverlay = false;
            this.treeListView.Location = new System.Drawing.Point(0, 25);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(991, 360);
            this.treeListView.TabIndex = 6;
            this.treeListView.UseAlternatingBackColors = true;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.UseHotItem = true;
            this.treeListView.UseTranslucentHotItem = true;
            this.treeListView.UseTranslucentSelection = true;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            this.treeListView.ObjectExpandChanged += new BrightIdeasSoftware.TreeListView.ObjectExpandProxy(this.treeListView_ObjectExpandChanged);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 76;
            // 
            // olvColumnName
            // 
            this.olvColumnName.AspectName = "";
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "名称";
            this.olvColumnName.Width = 234;
            // 
            // olvColumnTimes
            // 
            this.olvColumnTimes.AspectName = "";
            this.olvColumnTimes.HeaderFont = null;
            this.olvColumnTimes.Text = "过频繁组数/次数";
            this.olvColumnTimes.Width = 106;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 64;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.AspectName = "";
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 86;
            // 
            // olvColumnLat
            // 
            this.olvColumnLat.AspectName = "";
            this.olvColumnLat.HeaderFont = null;
            this.olvColumnLat.Text = "经度";
            this.olvColumnLat.Width = 91;
            // 
            // olvColumnLong
            // 
            this.olvColumnLong.AspectName = "";
            this.olvColumnLong.HeaderFont = null;
            this.olvColumnLong.Text = "纬度";
            this.olvColumnLong.Width = 85;
            // 
            // olvColumnTA
            // 
            this.olvColumnTA.AspectName = "";
            this.olvColumnTA.HeaderFont = null;
            this.olvColumnTA.Text = "TA";
            this.olvColumnTA.Width = 63;
            // 
            // olvColumnRxlev
            // 
            this.olvColumnRxlev.AspectName = "";
            this.olvColumnRxlev.HeaderFont = null;
            this.olvColumnRxlev.Text = "Rxlev/PCCPCH_RSCP";
            this.olvColumnRxlev.Width = 119;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpandAll,
            this.ToolStripMenuItemFoldAll,
            this.ToolStripMenuItemExport,
            this.miReplay,
            this.miES});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(143, 114);
            this.contextMenuStrip1.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip1_Opening);
            // 
            // ToolStripMenuItemExpandAll
            // 
            this.ToolStripMenuItemExpandAll.Name = "ToolStripMenuItemExpandAll";
            this.ToolStripMenuItemExpandAll.Size = new System.Drawing.Size(142, 22);
            this.ToolStripMenuItemExpandAll.Text = "全部展开";
            this.ToolStripMenuItemExpandAll.Click += new System.EventHandler(this.ToolStripMenuItemExpandAll_Click);
            // 
            // ToolStripMenuItemFoldAll
            // 
            this.ToolStripMenuItemFoldAll.Name = "ToolStripMenuItemFoldAll";
            this.ToolStripMenuItemFoldAll.Size = new System.Drawing.Size(142, 22);
            this.ToolStripMenuItemFoldAll.Text = "全部折叠";
            this.ToolStripMenuItemFoldAll.Click += new System.EventHandler(this.ToolStripMenuItemFoldAll_Click);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(142, 22);
            this.ToolStripMenuItemExport.Text = "导出Excel...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // miReplay
            // 
            this.miReplay.Image = ((System.Drawing.Image)(resources.GetObject("miReplay.Image")));
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(142, 22);
            this.miReplay.Text = "回放事件(&R)";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miES
            // 
            this.miES.Name = "miES";
            this.miES.Size = new System.Drawing.Size(142, 22);
            this.miES.Text = "智能分析...";
            this.miES.Click += new System.EventHandler(this.miES_Click);
            // 
            // ZTRoutingAreaUpdateTooMachForm
            // 
            this.ClientSize = new System.Drawing.Size(991, 385);
            this.Controls.Add(this.treeListView);
            this.Controls.Add(this.toolStrip1);
            this.Name = "ZTRoutingAreaUpdateTooMachForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "路由更新过频繁分析";
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel lblTime;
        private System.Windows.Forms.ToolStripLabel lblCount;
        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnTimes;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLat;
        private BrightIdeasSoftware.OLVColumn olvColumnLong;
        private BrightIdeasSoftware.OLVColumn olvColumnTA;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemFoldAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripMenuItem miES;
    }
}