﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteAntMRForm : MinCloseForm
    {
        MapForm mapForm;
        public LteAntMRForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            rtbDesc.Text = LteScanAntennaForm.WriteMRDesc();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }     

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        private Dictionary<string, ZTLteAntMR.CellParaData> DicCellParaData = new Dictionary<string, ZTLteAntMR.CellParaData>();

        /// <summary>
        /// 数据初始化，加载前200个小区
        /// </summary>
        public void FillData(List<ZTLteAntMR.CellParaData> listCellParaData)
        {
            foreach (ZTLteAntMR.CellParaData cellPara in listCellParaData)
            {
                if (!DicCellParaData.ContainsKey(cellPara.cellname))
                    DicCellParaData.Add(cellPara.cellname, cellPara);
            }

            labNum.Text = listCellParaData.Count.ToString();
            int iPage = listCellParaData.Count % 200 > 0 ? listCellParaData.Count / 200 + 1 : listCellParaData.Count / 200;
            labPage.Text = iPage.ToString();

            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (rowCellAt == 0)
                {
                    intiDataViewColumn(dataGridViewCell, row.cellValues);
                    rowCellAt++;
                    continue;
                }
                if (rowCellAt > 200)
                    break;
                initDataRow(dataGridViewCell, row);
                rowCellAt++;
            }

            txtPage.Text = "1";
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && row.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;

                initDataRow(dataGridViewCell, row);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;

                initDataRow(dataGridViewCell, row);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 初始化数据列头名称
        /// </summary>
        private void intiDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 数据赋赋值
        /// </summary>
        private void initDataRow(DataGridView datatGridView, NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[3];//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        ZTLteAntMR.CellParaData data;
        private void miShwoChart_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;

            if (data != null)
            {
                groupControl4.Text = string.Format("MR测量项数据图表({0})", data.cellname);
                this.xtraTabControl1.SelectedTabPageIndex = 2;

                DrawPowerTableSeries(data.cellMrData.lteMRPowerHeadRoomItem.dataValue);
                DrawRsrpTableSeries(data.cellMrData.lteMRRsrpItem.dataValue);
                DrawAoaTableSeries(data.cellMrData.lteMRAoaItem.dataValue);
                DrawSinrTableSeries(data.cellMrData.lteMRSinrUlItem.dataValue);
                DrawTATableSeries(data.cellMrData.lteMRTaItem.dataValue);
                drawCellMRRadarSeries();
                int[] dirArray = new int[72];
                for (int i = 0; i < 72; i++)
                {
                    if (data.dirSampleDic.ContainsKey(i))
                        dirArray[i] = data.dirSampleDic[i];
                }
                drawCellCoverRadarSeries(dirArray);
            }
        }

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;

            if (data != null)
            {
                ZTAntennaBase.SimulationPoints simulationPoints = new ZTAntennaBase.SimulationPoints();
                simulationPoints.cellLongitude = data.dLongitude;
                simulationPoints.cellLatitude = data.dLatitude;
                simulationPoints.strNet = "LTE";
                simulationPoints.longLatMRList.AddRange(data.mrAoaList);

                MainModel.SimulationPoints = simulationPoints;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(data.dLongitude, data.dLatitude);
                MainModel.FireCellDrawInfoChanged(this);

                Dictionary<int, List<string>> colorDic = getMRDataColorDic();
                Dictionary<int, List<LongLat>> gisSampleDic = new Dictionary<int, List<LongLat>>();
                foreach (int c in colorDic.Keys)
                {
                    List<LongLat> longLatList = new List<LongLat>();
                    foreach (string strInfo in colorDic[c])
                    {
                        string[] str = strInfo.Split('_');
                        int iAngle = 0;
                        int iDist = 0;
                        int.TryParse(str[0], out iAngle);
                        int.TryParse(str[1], out iDist);
                        LongLat cellLongLat = new LongLat();
                        cellLongLat.fLongitude = (float)data.dLongitude;
                        cellLongLat.fLatitude = (float)data.dLatitude;
                        LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, cellLongLat);
                        longLatList.Add(tmpLongLat);
                    }
                    gisSampleDic.Add(c, longLatList);
                }

                AntPointLayer antLayer = mapForm.GetLayerBase(typeof(AntPointLayer)) as AntPointLayer;
                if (antLayer != null)
                {
                    MainModel.MainForm.GetMapForm().GoToView(data.dLongitude, data.dLatitude);
                    antLayer.iFunc = 2;
                    antLayer.gisSampleDic = gisSampleDic;
                    antLayer.Invalidate();
                }
            }
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            doExport(nrDatasList, sheetNames[0]);
        }

        private void 导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }
        public void doExport(List<List<NPOIRow>> nrDatasList, string strSheetName)
        {
            string fileName;
            if (!ExportResultSecurityHelper.GetExportPermit(FileSimpleTypeHelper.Excel, out fileName))
            {
                return;
            }
            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
            Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Add(Missing.Value);
            Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetDest.Name = strSheetName;
            for (int i = bookDest.Worksheets.Count; i > 1; i--)
            {
                Microsoft.Office.Interop.Excel.Worksheet wt = (Microsoft.Office.Interop.Excel.Worksheet)bookDest.Worksheets[i];
                if (wt.Name != strSheetName)
                {
                    wt.Delete();
                }
            }
            try
            {
                Microsoft.Office.Interop.Excel.Range rngRow = (Microsoft.Office.Interop.Excel.Range)sheetDest.Columns[1, Type.Missing];
                rngRow.UseStandardWidth = 70;
                int idx = 0;
                setExcelRange(sheetDest, "小区状态库信息", 14, ref idx);
                setExcelRange(sheetDest, "小区性能统计", 7, ref idx);
                setExcelRange(sheetDest, "小区测量统计", 4, ref idx);
                excel.Application.Workbooks.Add(true);

                //导入数据行
                foreach (List<NPOIRow> listNPOI in nrDatasList)
                {
                    int row = 2;
                    foreach (NPOIRow npoi in listNPOI)
                    {
                        idx = npoi.cellValues.Count;
                        Microsoft.Office.Interop.Excel.Range cell1ran = sheetDest.get_Range(sheetDest.Cells[row, 1], sheetDest.Cells[row++, idx]);
                        cell1ran.Value2 = npoi.cellValues.ToArray();
                    }
                }
                bookDest.Saved = true;
                bookDest.SaveCopyAs(fileName);//保存
                MessageBox.Show("导出成功！");
            }
            catch (Exception w)
            {
                MessageBox.Show("导出异常:" + w.Message);
            }
            finally
            {
                excel.Quit();
                //GC.Collect();//垃圾回收   
            }
        }

        private void setExcelRange(Microsoft.Office.Interop.Excel.Worksheet sheetDest, string value, int col, ref int idx)
        {
            int row = 1;
            idx += 1;
            int cell1Col = idx;
            idx += col;
            int cell2Col = idx;
            Microsoft.Office.Interop.Excel.Range ran = sheetDest.get_Range(sheetDest.Cells[row, cell1Col], sheetDest.Cells[row, (cell2Col)]);
            ran.Merge(ran.MergeCells);//合并单元格
            ran.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
            ran.Value2 = value;
        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex == 0 || xtraTabControl1.SelectedTabPageIndex == 1)
            {
                miShwoChart.Visible = true;
                miShowSimulation.Visible = true;
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        #region MR统计图表绘制

        /// <summary>
        /// 按MR发射功率余量图表
        /// </summary>
        private void DrawPowerTableSeries(int[] modelSeries)
        {
            chartControlPower.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 64; i++)
            {
                int iPower = i - 23;
                series.Points.Add(new SeriesPoint(iPower.ToString(), modelSeries[i]));
            }
            chartControlPower.Series.Insert(0, series);

            ((XYDiagram)chartControlPower.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlPower.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlPower.Focus();
        }

        /// <summary>
        /// 按MR参考信号接收功率余量图表
        /// </summary>
        private void DrawRsrpTableSeries(int[] modelSeries)
        {
            chartControlRsrp.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 48; i++)
            {
                int iRsrp = calcRsrpByNum(i);
                series.Points.Add(new SeriesPoint(iRsrp.ToString(), modelSeries[i]));
            }
            chartControlRsrp.Series.Insert(0, series);

            ((XYDiagram)chartControlRsrp.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlRsrp.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlRsrp.Focus();
        }

        /// <summary>
        /// 按时间差计算RSRP
        /// </summary>
        private int calcRsrpByNum(int iNum)
        {
            int iRsrp = 0;
            if (iNum == 0)
            {
                iRsrp = -120;
            }
            else if (iNum == 1)
            {
                iRsrp = -116;
            }
            else if (iNum < 37)
            {
                iRsrp = iNum - 117;
            }
            else if (iNum < 47)
            {
                iRsrp = (iNum - 37) * 2 + 37 - 116;
            }
            else
            {
                iRsrp = -60;
            }
            return iRsrp;
        }

        /// <summary>
        /// 按MR天线到达角图表
        /// </summary>
        private void DrawAoaTableSeries(int[] modelSeries)
        {
            chartControlAoa.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int iAoa = (i + 1) * 5;
                series.Points.Add(new SeriesPoint(iAoa.ToString(), modelSeries[i]));
            }
            chartControlAoa.Series.Insert(0, series);

            ((XYDiagram)chartControlAoa.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlAoa.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlAoa.Focus();
        }

        /// <summary>
        /// 按MR上行信噪比图表
        /// </summary>
        private void DrawSinrTableSeries(int[] modelSeries)
        {
            chartControlSinr.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 37; i++)
            {
                int iSinr = i - 11;
                series.Points.Add(new SeriesPoint(iSinr.ToString(), modelSeries[i]));
            }
            chartControlSinr.Series.Insert(0, series);

            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlSinr.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlSinr.Focus();
        }

        /// <summary>
        /// 按MR时间提前量图表
        /// </summary>
        private void DrawTATableSeries(int[] modelSeries)
        {
            chartControlTA.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 44; i++)
            {
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(i);
                series.Points.Add(new SeriesPoint(iDist.ToString(), modelSeries[i]));
            }
            chartControlTA.Series.Insert(0, series);

            ((XYDiagram)chartControlTA.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartControlTA.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(modelSeries);

            chartControlTA.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图
        /// </summary>
        private void drawCellMRRadarSeries()
        {
            chartControl8.Series.Clear();

            int idx = 0;
            Dictionary<int, List<string>> colorDic = getMRDataColorDic();
            int iMaxValue = 0;
            for (int c = 0; c <= 10; c++)
            {
                if (!colorDic.ContainsKey(c))
                    continue;

                AntLegend antLegend = ZTAntFuncHelper.GetMRDataLegend(c);
                Series series = new Series();
                series.ShowInLegend = true;
                series.LegendText = antLegend.strLegend;
                series.PointOptions.PointView = PointView.Values;

                RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                pointSeriesView.Color = antLegend.colorType;
                pointSeriesView.PointMarkerOptions.Size = 3;

                series.View = pointSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;

                foreach (string strInfo in colorDic[c])
                {
                    string[] str = strInfo.Split('_');
                    int iTmpValue = 0;
                    int.TryParse(str[1], out iTmpValue);
                    if (iTmpValue > iMaxValue)
                        iMaxValue = iTmpValue;
                    series.Points.Add(new SeriesPoint(str[0], str[1]));
                }

                chartControl8.Series.Insert(idx, series);
                idx++;
            }

            #region 增加天线方位角连线
            RadarLineSeriesView lineSeriesCellView = new RadarLineSeriesView();
            lineSeriesCellView.Color = Color.Red;
            lineSeriesCellView.LineMarkerOptions.Size = 2;

            Series seriesCell = new Series();
            seriesCell.ShowInLegend = true;
            seriesCell.LegendText = "工参";
            seriesCell.PointOptions.PointView = PointView.Values;

            seriesCell.View = lineSeriesCellView;
            seriesCell.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesCell.Label.Visible = false;
            seriesCell.Points.Add(new SeriesPoint(data.antCfg.方向角, 0));
            seriesCell.Points.Add(new SeriesPoint(data.antCfg.方向角, iMaxValue));
            chartControl8.Series.Insert(idx, seriesCell);
            idx++;

            RadarLineSeriesView lineSeriesMRView = new RadarLineSeriesView();
            lineSeriesMRView.Color = Color.DarkBlue;
            lineSeriesMRView.LineMarkerOptions.Size = 2;

            Series seriesMR = new Series();
            seriesMR.ShowInLegend = true;
            seriesMR.LegendText = "MR";
            seriesMR.PointOptions.PointView = PointView.Values;

            seriesMR.View = lineSeriesMRView;
            seriesMR.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesMR.Label.Visible = false;
            seriesMR.Points.Add(new SeriesPoint(data.iAntMaxDir, 0));
            seriesMR.Points.Add(new SeriesPoint(data.iAntMaxDir, iMaxValue));
            chartControl8.Series.Insert(idx, seriesMR);

            #endregion

            if (colorDic.Count > 0)
            {
                ((RadarDiagram)chartControl8.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartControl8.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;

                ((RadarDiagram)chartControl8.Diagram).AxisX.GridSpacing = 20;

                ((RadarDiagram)chartControl8.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartControl8.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartControl8.Series.Insert(0, series);
            }
            chartControl8.Focus();
        }

        /// <summary>
        /// 获取MR二维数据
        /// </summary>
        private Dictionary<int, List<string>> getMRDataColorDic()
        {
            Dictionary<int, List<string>> colorDic = new Dictionary<int, List<string>>();
            if (data != null)
            {
                for (int i = 0; i < 44; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        addColorDic(colorDic, i, j);
                    }
                }
            }
            return colorDic;
        }

        private void addColorDic(Dictionary<int, List<string>> colorDic, int i, int j)
        {
            int n = j * 5;
            if (data.cellMrData.AnaRttdAoa[i, j] > 0)
            {
                int c = ZTAntFuncHelper.GetMRDataLevel(data.cellMrData.AnaRttdAoa[i, j]);
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(i);
                string strDesc = string.Format("{0}_{1}", n.ToString(), iDist);
                if (colorDic.ContainsKey(c))
                {
                    colorDic[c].Add(strDesc);
                }
                else
                {
                    List<string> descList = new List<string>();
                    descList.Add(strDesc);
                    colorDic.Add(c, descList);
                }
            }
        }

        /// <summary>
        /// 按MR模拟覆盖图
        /// </summary>
        private void drawCellCoverRadarSeries(int[] seriesValues)
        {
            chartControl1.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int j = i * 5;
                series.Points.Add(new SeriesPoint(j, seriesValues[i]));
            }
            chartControl1.Series.Insert(0, series);

            ((RadarDiagram)chartControl1.Diagram).AxisY.Range.MinValue = -1;
            ((RadarDiagram)chartControl1.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(seriesValues);
            ((RadarDiagram)chartControl1.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartControl1.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl1.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartControl1.Focus();
        }
        #endregion
    }
}
