﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonUnknow : ReasonBase
    {
        private static ReasonUnknow instance = null;
        public static ReasonUnknow Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ReasonUnknow();
                }
                return instance;
            }
        }
        protected ReasonUnknow()
        {
            Name = "未知原因";
        }
        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            return true;
        }
        public override bool Equals(object obj)
        {
            if (obj == null)
            {
                return false;
            }
            return obj.GetType() == this.GetType();
        }

        private const int hash = -999;
        public override int GetHashCode()
        {
            return hash.GetHashCode();
        }
    }
}
