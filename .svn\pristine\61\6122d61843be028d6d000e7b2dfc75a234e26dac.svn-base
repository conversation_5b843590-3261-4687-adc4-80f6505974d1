﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class BasePanel : UserControl
    {
        public BasePanel()
        {
            InitializeComponent();

            DateTime dtNow = DateTime.Now.Date;
            dateTimePickerStart.Value = dtNow.AddDays(-1);
            dateTimePickerEnd.Value = dtNow.AddDays(1).AddMilliseconds(-1);

            attributePanelProj.SetAttribute("Project", "项目来源");
            attributePanelServ.SetAttribute("ServiceType", "业务类型");

            checkedListBoxControlCarrier.Items.Clear();
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                DevExpress.XtraEditors.Controls.CheckedListBoxItem item = new DevExpress.XtraEditors.Controls.CheckedListBoxItem(car);
                checkedListBoxControlCarrier.Items.Add(item);
            }

            checkEditFilter_CheckedChanged(null, null);
        }

        private List<int> getCarrierVec()
        {
            List<int> carrierVec = new List<int>();
            foreach (DevExpress.XtraEditors.Controls.CheckedListBoxItem item in checkedListBoxControlCarrier.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    carrierVec.Add((int)item.Value);
                }
            }
            return carrierVec;
        }

        public void SetCondition(QueryCondition cond)
        {
            if (cond == null) return;

            if (cond.Periods.Count > 0)
            {
                this.dateTimePickerStart.Value = cond.Periods[0].BeginTime;
                this.dateTimePickerEnd.Value = cond.Periods[0].EndTime;
            }

            if (cond.CarrierTypes.Count > 0)
            {
                for (int idx = 0; idx < checkedListBoxControlCarrier.Items.Count; idx++)
                {
                    DevExpress.XtraEditors.Controls.CheckedListBoxItem item = checkedListBoxControlCarrier.Items[idx];
                    item.CheckState = cond.CarrierTypes.Contains((int)item.Value) ? CheckState.Checked : CheckState.Unchecked;
                    checkedListBoxControlCarrier.SetItemCheckState(idx, cond.CarrierTypes.Contains((int)item.Value) ? CheckState.Checked : CheckState.Unchecked);
                }
            }

            attributePanelProj.FillData(cond.Projects);
            attributePanelServ.FillData(cond.ServiceTypes);

            radioGroupFileFilter.SelectedIndex = (int)cond.NameFilterType;
            textBoxFileName.Text = cond.FileName;

            checkEditFilter.Checked = textBoxFileName.Text.Trim() != string.Empty;
        }

        public QueryCondition GetCondition()
        {
            QueryCondition condition = new QueryCondition();
            condition.Periods.Clear();
            condition.Periods.Add(new MasterCom.Util.TimePeriod(dateTimePickerStart.Value, dateTimePickerEnd.Value));
            condition.Projects = attributePanelProj.GetAttribute();
            condition.ServiceTypes = attributePanelServ.GetAttribute();
            condition.CarrierTypes = getCarrierVec();

            if (checkEditFilter.Checked && textBoxFileName.Text.Trim() != "")
            {
                condition.NameFilterType = (FileFilterType)radioGroupFileFilter.SelectedIndex;
                condition.FileName = textBoxFileName.Text.Trim();
            }

            return condition;
        }

        public bool CheckCondition()
        {
            if (dateTimePickerStart.Value > dateTimePickerEnd.Value)
            {
                MessageBox.Show("开始时间大于结束时间...");
                return false;
            }
            else if (attributePanelProj.GetAttribute().Count == 0)
            {
                MessageBox.Show("未设置项目来源...");
                return false;
            }
            else if (attributePanelServ.GetAttribute().Count == 0)
            {
                MessageBox.Show("未设置业务类型...");
                return false;
            }
            else if (getCarrierVec().Count == 0)
            {
                MessageBox.Show("未设置运营商...");
                return false;
            }
            return true;
        }

        private void checkEditFilter_CheckedChanged(object sender, EventArgs e)
        {
            bool bCheck = checkEditFilter.Checked;
            radioGroupFileFilter.Enabled = bCheck;
            textBoxFileName.Enabled = bCheck;
        }
    }
}
