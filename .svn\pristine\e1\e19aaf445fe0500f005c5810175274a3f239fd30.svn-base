﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LtePCIOptimizeInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlSummary = new DevExpress.XtraGrid.GridControl();
            this.gridViewSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStripCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemDrawAllSinr = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.textBoxAfter = new System.Windows.Forms.TextBox();
            this.textBoxUnchange = new System.Windows.Forms.TextBox();
            this.textBoxRemove = new System.Windows.Forms.TextBox();
            this.textBoxAdd = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.textBoxBefore = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.comboBoxCell = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlPCI = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripSinr = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemRefreshAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExportSinr = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlCell = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPageFilter = new DevExpress.XtraTab.XtraTabPage();
            this.simpleButtonOptimize = new DevExpress.XtraEditors.SimpleButton();
            this.listViewBtsInfo = new System.Windows.Forms.ListView();
            this.columnHeaderSn = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBtsName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderSampleNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderScoreMean = new System.Windows.Forms.ColumnHeader();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.spinEditScoreMean = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditSampleNum = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripButtonFilter = new System.Windows.Forms.ToolStripButton();
            this.toolStripBtnTimeFilter = new System.Windows.Forms.ToolStripButton();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSummary)).BeginInit();
            this.contextMenuStripCell.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPCI)).BeginInit();
            this.contextMenuStripSinr.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            this.xtraTabPageFilter.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditScoreMean.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleNum.Properties)).BeginInit();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridViewDetail
            // 
            this.gridViewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn28,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34});
            this.gridViewDetail.GridControl = this.gridControlSummary;
            this.gridViewDetail.Name = "gridViewDetail";
            this.gridViewDetail.OptionsBehavior.Editable = false;
            this.gridViewDetail.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "主服小区";
            this.gridColumn25.FieldName = "cellname";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 0;
            this.gridColumn25.Width = 126;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "邻区(前)";
            this.gridColumn26.FieldName = "BeforeNbCellName";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 1;
            this.gridColumn26.Width = 126;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "邻区(后)";
            this.gridColumn28.FieldName = "AfterNbCellName";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 2;
            this.gridColumn28.Width = 158;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "原PCI";
            this.gridColumn31.FieldName = "orig_iScrambleCode";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.FixedWidth = true;
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 3;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "新PCI";
            this.gridColumn32.FieldName = "iScrambleCode";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.OptionsColumn.FixedWidth = true;
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 4;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "经度";
            this.gridColumn33.FieldName = "longitude";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.OptionsColumn.FixedWidth = true;
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 5;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "维度";
            this.gridColumn34.FieldName = "latitude";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.OptionsColumn.FixedWidth = true;
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 6;
            // 
            // gridControlSummary
            // 
            this.gridControlSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewDetail;
            gridLevelNode1.RelationName = "SinrVec";
            this.gridControlSummary.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlSummary.Location = new System.Drawing.Point(0, 0);
            this.gridControlSummary.MainView = this.gridViewSummary;
            this.gridControlSummary.Name = "gridControlSummary";
            this.gridControlSummary.Size = new System.Drawing.Size(701, 273);
            this.gridControlSummary.TabIndex = 0;
            this.gridControlSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSummary,
            this.gridViewDetail});
            // 
            // gridViewSummary
            // 
            this.gridViewSummary.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15});
            this.gridViewSummary.GridControl = this.gridControlSummary;
            this.gridViewSummary.Name = "gridViewSummary";
            this.gridViewSummary.OptionsBehavior.Editable = false;
            this.gridViewSummary.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "主服小区";
            this.gridColumn2.FieldName = "MainCellName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "邻区";
            this.gridColumn3.FieldName = "NbCellName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "新增个数";
            this.gridColumn13.FieldName = "SinrNumAdd";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.OptionsColumn.FixedWidth = true;
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 2;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "消除个数";
            this.gridColumn14.FieldName = "SinrNumRemove";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsColumn.FixedWidth = true;
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "不变个数";
            this.gridColumn15.FieldName = "SinrNumUnChange";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.OptionsColumn.FixedWidth = true;
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 4;
            // 
            // contextMenuStripCell
            // 
            this.contextMenuStripCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemDrawAllSinr,
            this.ToolStripMenuItemExport});
            this.contextMenuStripCell.Name = "contextMenuStrip1";
            this.contextMenuStripCell.Size = new System.Drawing.Size(161, 48);
            // 
            // ToolStripMenuItemDrawAllSinr
            // 
            this.ToolStripMenuItemDrawAllSinr.Name = "ToolStripMenuItemDrawAllSinr";
            this.ToolStripMenuItemDrawAllSinr.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemDrawAllSinr.Text = "渲染所有质差点";
            this.ToolStripMenuItemDrawAllSinr.Click += new System.EventHandler(this.ToolStripMenuItemDrawAllSinr_Click);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.textBoxAfter);
            this.groupBox1.Controls.Add(this.textBoxUnchange);
            this.groupBox1.Controls.Add(this.textBoxRemove);
            this.groupBox1.Controls.Add(this.textBoxAdd);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.textBoxBefore);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.comboBoxCell);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(2, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(706, 78);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "优化前后汇总对比";
            // 
            // textBoxAfter
            // 
            this.textBoxAfter.Location = new System.Drawing.Point(593, 22);
            this.textBoxAfter.Name = "textBoxAfter";
            this.textBoxAfter.ReadOnly = true;
            this.textBoxAfter.Size = new System.Drawing.Size(100, 22);
            this.textBoxAfter.TabIndex = 2;
            // 
            // textBoxUnchange
            // 
            this.textBoxUnchange.Location = new System.Drawing.Point(593, 51);
            this.textBoxUnchange.Name = "textBoxUnchange";
            this.textBoxUnchange.ReadOnly = true;
            this.textBoxUnchange.Size = new System.Drawing.Size(100, 22);
            this.textBoxUnchange.TabIndex = 2;
            // 
            // textBoxRemove
            // 
            this.textBoxRemove.Location = new System.Drawing.Point(381, 51);
            this.textBoxRemove.Name = "textBoxRemove";
            this.textBoxRemove.ReadOnly = true;
            this.textBoxRemove.Size = new System.Drawing.Size(100, 22);
            this.textBoxRemove.TabIndex = 2;
            // 
            // textBoxAdd
            // 
            this.textBoxAdd.Location = new System.Drawing.Point(151, 51);
            this.textBoxAdd.Name = "textBoxAdd";
            this.textBoxAdd.ReadOnly = true;
            this.textBoxAdd.Size = new System.Drawing.Size(100, 22);
            this.textBoxAdd.TabIndex = 2;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(509, 55);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(79, 14);
            this.label6.TabIndex = 0;
            this.label6.Text = "不变质差点数";
            // 
            // textBoxBefore
            // 
            this.textBoxBefore.Location = new System.Drawing.Point(381, 22);
            this.textBoxBefore.Name = "textBoxBefore";
            this.textBoxBefore.ReadOnly = true;
            this.textBoxBefore.Size = new System.Drawing.Size(100, 22);
            this.textBoxBefore.TabIndex = 2;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(297, 55);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(79, 14);
            this.label5.TabIndex = 0;
            this.label5.Text = "消除质差点数";
            // 
            // comboBoxCell
            // 
            this.comboBoxCell.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxCell.FormattingEnabled = true;
            this.comboBoxCell.Location = new System.Drawing.Point(72, 22);
            this.comboBoxCell.Name = "comboBoxCell";
            this.comboBoxCell.Size = new System.Drawing.Size(179, 22);
            this.comboBoxCell.TabIndex = 1;
            this.comboBoxCell.SelectedIndexChanged += new System.EventHandler(this.comboBoxCell_SelectedIndexChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(69, 55);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(79, 14);
            this.label4.TabIndex = 0;
            this.label4.Text = "新增质差点数";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(495, 26);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(91, 14);
            this.label3.TabIndex = 0;
            this.label3.Text = "优化后质差点数";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(284, 26);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(91, 14);
            this.label2.TabIndex = 0;
            this.label2.Text = "优化前质差点数";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(11, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(55, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "显示小区";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlPCI);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(701, 273);
            this.xtraTabPage2.Text = "质差点信息";
            // 
            // gridControlPCI
            // 
            this.gridControlPCI.ContextMenuStrip = this.contextMenuStripSinr;
            this.gridControlPCI.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlPCI.Location = new System.Drawing.Point(0, 0);
            this.gridControlPCI.MainView = this.gridView3;
            this.gridControlPCI.Name = "gridControlPCI";
            this.gridControlPCI.Size = new System.Drawing.Size(701, 273);
            this.gridControlPCI.TabIndex = 1;
            this.gridControlPCI.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3,
            this.gridView1});
            // 
            // contextMenuStripSinr
            // 
            this.contextMenuStripSinr.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemRefreshAll,
            this.ToolStripMenuItemExportSinr,
            this.ToolStripMenuItemExportShp,
            this.ToolStripMenuItemReplay});
            this.contextMenuStripSinr.Name = "contextMenuStripSinr";
            this.contextMenuStripSinr.Size = new System.Drawing.Size(161, 92);
            this.contextMenuStripSinr.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStripSinr_Opening);
            // 
            // ToolStripMenuItemRefreshAll
            // 
            this.ToolStripMenuItemRefreshAll.Name = "ToolStripMenuItemRefreshAll";
            this.ToolStripMenuItemRefreshAll.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemRefreshAll.Text = "渲染所有质差点";
            this.ToolStripMenuItemRefreshAll.Click += new System.EventHandler(this.ToolStripMenuItemRefreshAll_Click);
            // 
            // ToolStripMenuItemExportSinr
            // 
            this.ToolStripMenuItemExportSinr.Name = "ToolStripMenuItemExportSinr";
            this.ToolStripMenuItemExportSinr.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemExportSinr.Text = "导出到xls...";
            this.ToolStripMenuItemExportSinr.Click += new System.EventHandler(this.ToolStripMenuItemExportSinr_Click);
            // 
            // ToolStripMenuItemExportShp
            // 
            this.ToolStripMenuItemExportShp.Name = "ToolStripMenuItemExportShp";
            this.ToolStripMenuItemExportShp.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemExportShp.Text = "导出图层";
            this.ToolStripMenuItemExportShp.Click += new System.EventHandler(this.ToolStripMenuItemExportShp_Click);
            // 
            // ToolStripMenuItemReplay
            // 
            this.ToolStripMenuItemReplay.Name = "ToolStripMenuItemReplay";
            this.ToolStripMenuItemReplay.Size = new System.Drawing.Size(160, 22);
            this.ToolStripMenuItemReplay.Text = "回放文件...";
            this.ToolStripMenuItemReplay.Click += new System.EventHandler(this.ToolStripMenuItemReplay_Click);
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn27,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn9,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn29,
            this.gridColumn30});
            this.gridView3.GridControl = this.gridControlPCI;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsDetail.ShowDetailTabs = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.Click += new System.EventHandler(this.gridView3_Click);
            this.gridView3.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView3_CustomDrawCell);
            this.gridView3.DoubleClick += new System.EventHandler(this.gridView3_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "Sn";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 41;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "城市名称";
            this.gridColumn22.FieldName = "cityname";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 1;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "文件名";
            this.gridColumn23.FieldName = "FileName";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 2;
            this.gridColumn23.Width = 150;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "状态";
            this.gridColumn24.FieldName = "Stage";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 3;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "主服小区名";
            this.gridColumn27.FieldName = "cellname";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 4;
            this.gridColumn27.Width = 126;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "主服RSRP";
            this.gridColumn4.FieldName = "PccpchMain";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 5;
            this.gridColumn4.Width = 76;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "主服PCI(前)";
            this.gridColumn5.FieldName = "orig_iScrambleCode";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 6;
            this.gridColumn5.Width = 94;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "主服PCI(后)";
            this.gridColumn9.FieldName = "iScrambleCode";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 7;
            this.gridColumn9.Width = 98;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "邻区名(前)";
            this.gridColumn6.FieldName = "BeforeNbCellName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 8;
            this.gridColumn6.Width = 94;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "邻区RSRP(前)";
            this.gridColumn7.FieldName = "BeforePccpchNB";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 9;
            this.gridColumn7.Width = 104;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "邻区PCI(前)";
            this.gridColumn8.FieldName = "BeforeNbCellPCI";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 10;
            this.gridColumn8.Width = 97;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "邻区名(后)";
            this.gridColumn10.FieldName = "AfterNbCellName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 11;
            this.gridColumn10.Width = 107;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "邻区RSRP(后)";
            this.gridColumn11.FieldName = "AfterPccpchNB";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 12;
            this.gridColumn11.Width = 101;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "邻区PCI(后)";
            this.gridColumn12.FieldName = "AfterNbCellPCI";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 13;
            this.gridColumn12.Width = 94;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "质差点经度";
            this.gridColumn29.FieldName = "longitude";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 14;
            this.gridColumn29.Width = 107;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "质差点纬度";
            this.gridColumn30.FieldName = "latitude";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 15;
            this.gridColumn30.Width = 93;
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.gridControlPCI;
            this.gridView1.Name = "gridView1";
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlCell);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(701, 273);
            this.xtraTabPage1.Text = "小区信息";
            // 
            // gridControlCell
            // 
            this.gridControlCell.ContextMenuStrip = this.contextMenuStripCell;
            this.gridControlCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCell.Location = new System.Drawing.Point(0, 0);
            this.gridControlCell.MainView = this.gridView2;
            this.gridControlCell.Name = "gridControlCell";
            this.gridControlCell.Size = new System.Drawing.Size(701, 273);
            this.gridControlCell.TabIndex = 0;
            this.gridControlCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView2.GridControl = this.gridControlCell;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsDetail.ShowDetailTabs = false;
            this.gridView2.OptionsView.ShowDetailButtons = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.DoubleClick += new System.EventHandler(this.gridView2_DoubleClick);
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "小区名称";
            this.gridColumn16.FieldName = "cellname";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 0;
            this.gridColumn16.Width = 357;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "ENODEB_ID";
            this.gridColumn17.FieldName = "lac";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.OptionsColumn.FixedWidth = true;
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 1;
            this.gridColumn17.Width = 83;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "SECTOR_ID";
            this.gridColumn18.FieldName = "sectorid";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.OptionsColumn.FixedWidth = true;
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 2;
            this.gridColumn18.Width = 77;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "频点";
            this.gridColumn19.FieldName = "orig_iUtranFreq";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.OptionsColumn.FixedWidth = true;
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 3;
            this.gridColumn19.Width = 55;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "原PCI";
            this.gridColumn20.FieldName = "orig_iScrambleCode";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.OptionsColumn.FixedWidth = true;
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 4;
            this.gridColumn20.Width = 55;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "新PCI";
            this.gridColumn21.FieldName = "iScrambleCode";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.OptionsColumn.FixedWidth = true;
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 5;
            this.gridColumn21.Width = 55;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.xtraTabControl1.ContextMenuStrip = this.contextMenuStripCell;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 108);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(708, 303);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPageFilter});
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControlSummary);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(701, 273);
            this.xtraTabPage3.Text = "质差点汇总";
            // 
            // xtraTabPageFilter
            // 
            this.xtraTabPageFilter.Controls.Add(this.simpleButtonOptimize);
            this.xtraTabPageFilter.Controls.Add(this.listViewBtsInfo);
            this.xtraTabPageFilter.Controls.Add(this.groupBox2);
            this.xtraTabPageFilter.Name = "xtraTabPageFilter";
            this.xtraTabPageFilter.Size = new System.Drawing.Size(701, 273);
            this.xtraTabPageFilter.Text = "基站筛选";
            // 
            // simpleButtonOptimize
            // 
            this.simpleButtonOptimize.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonOptimize.Location = new System.Drawing.Point(604, 18);
            this.simpleButtonOptimize.Name = "simpleButtonOptimize";
            this.simpleButtonOptimize.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOptimize.TabIndex = 2;
            this.simpleButtonOptimize.Text = "再次优化";
            this.simpleButtonOptimize.Click += new System.EventHandler(this.simpleButtonOptimize_Click);
            // 
            // listViewBtsInfo
            // 
            this.listViewBtsInfo.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewBtsInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSn,
            this.columnHeaderBtsName,
            this.columnHeaderSampleNum,
            this.columnHeaderScoreMean});
            this.listViewBtsInfo.FullRowSelect = true;
            this.listViewBtsInfo.GridLines = true;
            this.listViewBtsInfo.Location = new System.Drawing.Point(3, 64);
            this.listViewBtsInfo.Name = "listViewBtsInfo";
            this.listViewBtsInfo.Size = new System.Drawing.Size(695, 209);
            this.listViewBtsInfo.TabIndex = 1;
            this.listViewBtsInfo.UseCompatibleStateImageBehavior = false;
            this.listViewBtsInfo.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderSn
            // 
            this.columnHeaderSn.Text = "序号";
            // 
            // columnHeaderBtsName
            // 
            this.columnHeaderBtsName.Text = "基站名称";
            this.columnHeaderBtsName.Width = 228;
            // 
            // columnHeaderSampleNum
            // 
            this.columnHeaderSampleNum.Text = "采样点数";
            // 
            // columnHeaderScoreMean
            // 
            this.columnHeaderScoreMean.Text = "平均得分";
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.spinEditScoreMean);
            this.groupBox2.Controls.Add(this.labelControl2);
            this.groupBox2.Controls.Add(this.spinEditSampleNum);
            this.groupBox2.Controls.Add(this.labelControl1);
            this.groupBox2.Location = new System.Drawing.Point(3, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(439, 55);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "筛选设置";
            // 
            // spinEditScoreMean
            // 
            this.spinEditScoreMean.EditValue = new decimal(new int[] {
            55,
            0,
            0,
            131072});
            this.spinEditScoreMean.Location = new System.Drawing.Point(312, 21);
            this.spinEditScoreMean.Name = "spinEditScoreMean";
            this.spinEditScoreMean.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditScoreMean.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.spinEditScoreMean.Properties.Mask.EditMask = "f2";
            this.spinEditScoreMean.Size = new System.Drawing.Size(100, 21);
            this.spinEditScoreMean.TabIndex = 1;
            this.spinEditScoreMean.ValueChanged += new System.EventHandler(this.spinEditScoreMean_ValueChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(232, 24);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(61, 14);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "平均得分 ≥";
            // 
            // spinEditSampleNum
            // 
            this.spinEditSampleNum.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditSampleNum.Location = new System.Drawing.Point(92, 21);
            this.spinEditSampleNum.Name = "spinEditSampleNum";
            this.spinEditSampleNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSampleNum.Properties.Mask.EditMask = "f0";
            this.spinEditSampleNum.Size = new System.Drawing.Size(100, 21);
            this.spinEditSampleNum.TabIndex = 1;
            this.spinEditSampleNum.ValueChanged += new System.EventHandler(this.spinEditSampleNum_ValueChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(11, 24);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(61, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "采样点数 ≥";
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripButtonFilter,
            this.toolStripBtnTimeFilter});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(708, 25);
            this.toolStrip1.TabIndex = 3;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripButtonFilter
            // 
            this.toolStripButtonFilter.Image = global::MasterCom.RAMS.Properties.Resources.CELL;
            this.toolStripButtonFilter.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButtonFilter.Name = "toolStripButtonFilter";
            this.toolStripButtonFilter.Size = new System.Drawing.Size(76, 22);
            this.toolStripButtonFilter.Text = "评估筛选";
            this.toolStripButtonFilter.Click += new System.EventHandler(this.toolStripButtonFilter_Click);
            // 
            // toolStripBtnTimeFilter
            // 
            this.toolStripBtnTimeFilter.Image = global::MasterCom.RAMS.Properties.Resources.nextWorkSheetToolStripMenuItem;
            this.toolStripBtnTimeFilter.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripBtnTimeFilter.Name = "toolStripBtnTimeFilter";
            this.toolStripBtnTimeFilter.Size = new System.Drawing.Size(76, 22);
            this.toolStripBtnTimeFilter.Text = "时间筛选";
            this.toolStripBtnTimeFilter.Click += new System.EventHandler(this.toolStripBtnTimeFilter_Click);
            // 
            // LtePCIOptimizeInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(708, 410);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "LtePCIOptimizeInfoForm";
            this.Text = "PCI优化评估结果";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSummary)).EndInit();
            this.contextMenuStripCell.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPCI)).EndInit();
            this.contextMenuStripSinr.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage3.ResumeLayout(false);
            this.xtraTabPageFilter.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditScoreMean.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleNum.Properties)).EndInit();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripCell;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemDrawAllSinr;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox comboBoxCell;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBoxBefore;
        private System.Windows.Forms.TextBox textBoxAfter;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlPCI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl gridControlCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private System.Windows.Forms.TextBox textBoxUnchange;
        private System.Windows.Forms.TextBox textBoxRemove;
        private System.Windows.Forms.TextBox textBoxAdd;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripSinr;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemRefreshAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportSinr;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportShp;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemReplay;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripButton toolStripButtonFilter;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageFilter;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.SpinEdit spinEditScoreMean;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditSampleNum;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.ListView listViewBtsInfo;
        private System.Windows.Forms.ColumnHeader columnHeaderSn;
        private System.Windows.Forms.ColumnHeader columnHeaderBtsName;
        private System.Windows.Forms.ColumnHeader columnHeaderSampleNum;
        private System.Windows.Forms.ColumnHeader columnHeaderScoreMean;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOptimize;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl gridControlSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSummary;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private System.Windows.Forms.ToolStripButton toolStripBtnTimeFilter;
    }
}