﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedInfoForm_W
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLowPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHighSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLowSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMeanSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHandOver_count = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFreq = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(895, 407);
            this.gridControl.TabIndex = 2;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnTime,
            this.gridColumnTestType,
            this.gridColumnNetType,
            this.gridColumnPointCount,
            this.gridColumnLowPercent,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnDuration,
            this.gridColumnDistance,
            this.gridColumnHighSpeed,
            this.gridColumnLowSpeed,
            this.gridColumnMeanSpeed,
            this.gridColumnHandOver_count,
            this.gridColumnCellName,
            this.gridColumnFreq,
            this.gridColumnRoadDesc,
            this.gridColumn2});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 151;
            // 
            // gridColumnTime
            // 
            this.gridColumnTime.Caption = "时间";
            this.gridColumnTime.FieldName = "DateTimeString";
            this.gridColumnTime.Name = "gridColumnTime";
            this.gridColumnTime.Visible = true;
            this.gridColumnTime.VisibleIndex = 1;
            this.gridColumnTime.Width = 146;
            // 
            // gridColumnTestType
            // 
            this.gridColumnTestType.Caption = "测试业务";
            this.gridColumnTestType.FieldName = "StrAppType";
            this.gridColumnTestType.Name = "gridColumnTestType";
            this.gridColumnTestType.Visible = true;
            this.gridColumnTestType.VisibleIndex = 2;
            // 
            // gridColumnNetType
            // 
            this.gridColumnNetType.Caption = "网络类型";
            this.gridColumnNetType.FieldName = "NetType";
            this.gridColumnNetType.Name = "gridColumnNetType";
            this.gridColumnNetType.Visible = true;
            this.gridColumnNetType.VisibleIndex = 3;
            // 
            // gridColumnPointCount
            // 
            this.gridColumnPointCount.Caption = "采样点数";
            this.gridColumnPointCount.FieldName = "PointCount";
            this.gridColumnPointCount.Name = "gridColumnPointCount";
            this.gridColumnPointCount.Visible = true;
            this.gridColumnPointCount.VisibleIndex = 4;
            // 
            // gridColumnLowPercent
            // 
            this.gridColumnLowPercent.Caption = "低速率点占比（%）";
            this.gridColumnLowPercent.FieldName = "LowPercent";
            this.gridColumnLowPercent.Name = "gridColumnLowPercent";
            this.gridColumnLowPercent.Visible = true;
            this.gridColumnLowPercent.VisibleIndex = 5;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 6;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 7;
            this.gridColumnLatitude.Width = 66;
            // 
            // gridColumnDuration
            // 
            this.gridColumnDuration.Caption = "时长(秒)";
            this.gridColumnDuration.FieldName = "Duration";
            this.gridColumnDuration.Name = "gridColumnDuration";
            this.gridColumnDuration.Visible = true;
            this.gridColumnDuration.VisibleIndex = 8;
            this.gridColumnDuration.Width = 66;
            // 
            // gridColumnDistance
            // 
            this.gridColumnDistance.Caption = "里程(米)";
            this.gridColumnDistance.FieldName = "Distance";
            this.gridColumnDistance.Name = "gridColumnDistance";
            this.gridColumnDistance.Visible = true;
            this.gridColumnDistance.VisibleIndex = 9;
            this.gridColumnDistance.Width = 73;
            // 
            // gridColumnHighSpeed
            // 
            this.gridColumnHighSpeed.Caption = "最高速率(Kbps)";
            this.gridColumnHighSpeed.FieldName = "HighSpeed";
            this.gridColumnHighSpeed.Name = "gridColumnHighSpeed";
            this.gridColumnHighSpeed.Visible = true;
            this.gridColumnHighSpeed.VisibleIndex = 10;
            this.gridColumnHighSpeed.Width = 98;
            // 
            // gridColumnLowSpeed
            // 
            this.gridColumnLowSpeed.Caption = "最低速率(Kbps)";
            this.gridColumnLowSpeed.FieldName = "LowSpeed";
            this.gridColumnLowSpeed.Name = "gridColumnLowSpeed";
            this.gridColumnLowSpeed.Visible = true;
            this.gridColumnLowSpeed.VisibleIndex = 11;
            this.gridColumnLowSpeed.Width = 99;
            // 
            // gridColumnMeanSpeed
            // 
            this.gridColumnMeanSpeed.Caption = "平均速率(Kbps)";
            this.gridColumnMeanSpeed.FieldName = "MeanSpeed";
            this.gridColumnMeanSpeed.Name = "gridColumnMeanSpeed";
            this.gridColumnMeanSpeed.Visible = true;
            this.gridColumnMeanSpeed.VisibleIndex = 12;
            this.gridColumnMeanSpeed.Width = 104;
            // 
            // gridColumnHandOver_count
            // 
            this.gridColumnHandOver_count.Caption = "切换次数";
            this.gridColumnHandOver_count.FieldName = "HandOver_count";
            this.gridColumnHandOver_count.Name = "gridColumnHandOver_count";
            this.gridColumnHandOver_count.Visible = true;
            this.gridColumnHandOver_count.VisibleIndex = 13;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "关联小区";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 14;
            // 
            // gridColumnFreq
            // 
            this.gridColumnFreq.Caption = "频点";
            this.gridColumnFreq.FieldName = "BCCHs";
            this.gridColumnFreq.Name = "gridColumnFreq";
            this.gridColumnFreq.Visible = true;
            this.gridColumnFreq.VisibleIndex = 15;
            this.gridColumnFreq.Width = 115;
            // 
            // gridColumnRoadDesc
            // 
            this.gridColumnRoadDesc.Caption = "道路";
            this.gridColumnRoadDesc.FieldName = "RoadDesc";
            this.gridColumnRoadDesc.Name = "gridColumnRoadDesc";
            this.gridColumnRoadDesc.Visible = true;
            this.gridColumnRoadDesc.VisibleIndex = 16;
            this.gridColumnRoadDesc.Width = 115;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "网格";
            this.gridColumn2.FieldName = "GridName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 17;
            // 
            // LowSpeedInfoForm_W
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(895, 407);
            this.Controls.Add(this.gridControl);
            this.Name = "LowSpeedInfoForm_W";
            this.Text = "低速率里程分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistance;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHighSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLowSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMeanSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFreq;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHandOver_count;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPointCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLowPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
    }
}