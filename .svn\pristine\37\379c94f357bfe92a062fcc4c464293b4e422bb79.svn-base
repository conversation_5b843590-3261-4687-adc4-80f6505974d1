﻿using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class MapNRCellLayer : LayerBase
    {
        /// <summary>
        /// 是否显示最新工参，true：是；false:否。可在对应的图层控制里面选择，或在功能点工参显示时间设置统一设置所有图层工参时间点。
        /// </summary>
        public static bool DrawCurrent { get; set; } = true;
        /// <summary>
        /// 当DrawCurrent为false时，显示该快照时间的工参
        /// </summary>
        public static DateTime CurShowSnapshotTime { get; set; } = DateTime.Now;
    
        public MapNRCellLayer(string name) : base(name)
        {
            VisibleScaleEnabled = true;
            VisibleScale = new VisibleScale(0, 80000);
            initPath();
        }

        #region 初始化图元
        /// <summary>
        /// 小区默认显示长度
        /// </summary>
        public static readonly float CellDefaultDisplayLength = 20f;
        /// <summary>
        /// 全向小区默认显示长度
        /// </summary>
        public static readonly float CellOmniDefaultDisplayLength = 3f;
        /// <summary>
        /// 单位长度、宽度的小区path，通过Matrix放大缩小
        /// </summary>
        private static List<GraphicsPath> cellPathFactor { get; set; }
        /// <summary>
        ///  单位长度、宽度的天线path，通过Matrix放大缩小
        /// </summary>
        private List<GraphicsPath> antennaPathFactor { get; set; }

        /// <summary>
        /// 图元长度比例，数值越大，长度越大。值域为【1，10】
        /// </summary>
        private static float shapeLengthScale = 1;
        public static float ShapeLengthScale
        {
            get { return shapeLengthScale; }
            set
            {
                if (value < 1)
                {
                    shapeLengthScale = 1;
                }
                else if (value > 10)
                {
                    shapeLengthScale = 10;
                }
                else
                {
                    shapeLengthScale = value;
                }
            }
        }

        /// <summary>
        /// 图元宽度比例，数值越大，宽度越大。值域为【1，10】
        /// </summary>
        private static float shapeWidthScale = 1;
        public static float ShapeWidthScale
        {
            get { return shapeWidthScale; }
            set
            {
                if (value < 1)
                {
                    shapeWidthScale = 1;
                }
                else if (value > 10)
                {
                    shapeWidthScale = 10;
                }
                else
                {
                    shapeWidthScale = value;
                }
            }
        }

        #region path
        public List<GraphicsPath> CellPaths { get; private set; } = new List<GraphicsPath>();
        public List<GraphicsPath> AntennaPaths { get; private set; } = new List<GraphicsPath>();
        #endregion

        private void initPath()
        {
            #region 小区图形
            GraphicsPath path = new GraphicsPath();
            cellPathFactor = new List<GraphicsPath>();
            float length = CellOmniDefaultDisplayLength;
            path.AddEllipse(0, 0, length, length);//室内
            cellPathFactor.Add(path);

            #region 扇形
            path = new GraphicsPath();
            length = CellDefaultDisplayLength;
            //扇形的圆心坐标应为0,0
            path.AddPie(-length, -length, length + length, length + length, -30, 60);
            cellPathFactor.Add(path);
            //D-F
            for (int i = 1; i < 4; i++)
            {
                cellPathFactor.Add(transformPath(path, 1 + (0.2f * i), 1));
            }
            #endregion

            #region 纺锤形
            float x1 = 0.75f;
            float y1 = 0.16f;
            float x2 = 0.85f;
            float y2 = 0.18f;
            float x3 = 0.92f;
            float y3 = 0.16f;
            float x4 = 0.96f;
            float y4 = 0.12f;
            float x5 = 0.99f;
            float y5 = 0.07f;
            PointF[] cellPoints = new PointF[]
                {
                    new PointF(0, 0),
                    new PointF(length * x1, -length * y1),
                    new PointF(length * x2, -length * y2),
                    new PointF(length * x3, -length * y3),
                    new PointF(length * x4, -length * y4),
                    new PointF(length * x5, -length * y5),
                    new PointF(length, 0),
                    new PointF(length * x5, length * y5),
                    new PointF(length * x4, length * y4),
                    new PointF(length * x3, length * y3),
                    new PointF(length * x2, length * y2),
                    new PointF(length * x1, length * y1)
                };
            path = new GraphicsPath();
            path.AddPolygon(cellPoints);

            cellPathFactor.Add(path);
            //D-F
            for (int i = 1; i < 4; i++)
            {
                cellPathFactor.Add(transformPath(path, 1 + (0.2f * i), 1));
            }
            #endregion
            #endregion

            #region 天线图形
            path = new GraphicsPath();
            antennaPathFactor = new List<GraphicsPath>();
            length = CellOmniDefaultDisplayLength;
            path.AddEllipse(0, 0, length, length);//室内
            antennaPathFactor.Add(path);

            length = CellDefaultDisplayLength;
            path = new GraphicsPath();
            path.AddPolygon(new PointF[]
                {
                    new PointF(0, 0), //第一点为原点
                    new PointF(0, -1),
                    new PointF(length - 3, -1),
                    new PointF(length - 5, -5),
                    new PointF(length, 0),
                    new PointF(length - 5, 5),
                    new PointF(length - 3, 1),
                    new PointF(0, 1),
                });
            antennaPathFactor.Add(path);
            for (int i = 1; i < 4; i++)
            {
                antennaPathFactor.Add(transformPath(path, 1 + (0.2f * i), 1));
            }
            #endregion

            TransformDisplayShape(shapeLengthScale, shapeWidthScale);
        }

        private GraphicsPath transformPath(GraphicsPath orgPath, float xScale, float yScale)
        {
            //保持基础形状不变，只转换克隆的path
            GraphicsPath clonePath = orgPath.Clone() as GraphicsPath;
            Matrix matrix = new Matrix();
            matrix.Reset();
            matrix.Scale(xScale, yScale);
            //原点，必须为0,0
            PointF oldOrigin = new PointF(clonePath.PathPoints[0].X, clonePath.PathPoints[0].Y);
            clonePath.Transform(matrix);
            //缩放后的“原点”
            PointF newOrigin = clonePath.PathPoints[0];
            matrix.Reset();
            //图形偏移回原点
            matrix.Translate(oldOrigin.X - newOrigin.X, oldOrigin.Y - newOrigin.Y);
            clonePath.Transform(matrix);
            return clonePath;
        }

        /// <summary>
        /// 按比例转变小区、天线图元（放大、缩小）
        /// </summary>
        /// <param name="lengthScale"></param>
        /// <param name="widthScale"></param>
        public void TransformDisplayShape(float lengthScale, float widthScale)
        {
            ShapeLengthScale = lengthScale;
            ShapeWidthScale = widthScale;
            CellPaths = new List<GraphicsPath>();
            transformPathOriginAlign(cellPathFactor, CellPaths, lengthScale, widthScale);
            AntennaPaths = new List<GraphicsPath>();
            transformPathOriginAlign(antennaPathFactor, AntennaPaths, lengthScale, widthScale);
        }

        /// <summary>
        /// 保持原点，放大缩小图形
        /// </summary>
        /// <param name="basePaths"></param>
        /// <param name="transformPaths"></param>
        /// <param name="xScale"></param>
        /// <param name="yScale"></param>
        private void transformPathOriginAlign(List<GraphicsPath> basePaths, List<GraphicsPath> transformPaths, float xScale, float yScale)
        {
            transformPaths.Clear();
            foreach (GraphicsPath path in basePaths)
            {
                transformPaths.Add(transformPath(path, xScale, yScale));
            }
        }
        #endregion

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!VisibleScale.IsWithinScale(mapScale))
            {
                return;
            }
            cellsInView = null;
            antennasInView = null;
            btssInView = null;
            updateRect.Inflate((int)(400000 / mapScale), (int)(400000 / mapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = SmoothingMode.HighQuality;

            drawCells(graphics, dRect, mapScale);
            drawAntennas(graphics, dRect, mapScale);
            drawBTSs(graphics, dRect);

            drawBTSLabels(graphics, dRect);
            drawAntennasLabel(graphics, dRect);
            drawCellsLabel(graphics, dRect);
        }

        #region 绘制图层的方法
        #region Draw WorkParams Base Func
        private int getCellShapeIdx(NRCell cell)
        {
            int index = 0;//默认为室内小区index
            if (cell.Type != NRBTSType.Indoor)
            {
                index = 1;
            }
            return index;
        }

        private bool judgeServerCell(NRCell cell)
        {
            if (DrawServer)
            {
                for (int i = 0; i < mainModel.ServerNRCells.Count; i++)
                {
                    if (cell == mainModel.ServerNRCells[i])
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private List<NRCell> getCells()
        {
            List<NRCell> cells;
            if (DrawCurrent)
            {
                cells = CellManager.GetInstance().GetCurrentNRCells();
            }
            else
            {
                cells = CellManager.GetInstance().GetNRCells(CurShowSnapshotTime);
            }

            return cells;
        }
        #endregion

        #region DrawCell
        private void drawCells(Graphics graphics, DbRect dRect, double mapScale)
        {
            if (DrawCell)
            {
                cellsInView = getCellsInView(dRect);
                if (cellsInView != null)
                {
                    foreach (NRCell cell in cellsInView)
                    {
                        paintCell(cell, graphics, mapScale);
                    }
                }
            }
        }

        private List<NRCell> getCellsInView(DbRect dRect)
        {
            List<NRCell> cellsView = null;
            List<NRCell> cells = getCells();
            if (cells != null)
            {
                cellsView = new List<NRCell>();
                addCellsView(dRect, cellsView, cells);
            }
            if (DrawServer && mainModel.ServerNRCells != null && cellsView != null)
            {
                addCellsView(dRect, cellsView, mainModel.ServerNRCells);
            }
            return cellsView;
        }

        private void addCellsView(DbRect dRect, List<NRCell> cellsView, List<NRCell> cells)
        {
            foreach (NRCell cell in cells)
            {
                if (cell.Antennas.Count > 0
                    && cell.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                    && ((cell.Type == NRBTSType.Outdoor && DrawOutdoor)
                       || (cell.Type == NRBTSType.Indoor && DrawIndoor)))
                {
                    cellsView.Add(cell);
                }
            }
        }

        private void paintCell(NRCell cell, Graphics graphics, double scale)
        {
            if (cell.Antennas.Count == 0)
            {
                return;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);       
            int index = getCellShapeIdx(cell);

            Brush brush = brushCell;
            bool isServerCell = judgeServerCell(cell);
            if (isServerCell)
            {
                if (mainModel.DrawDifferentServerColor)
                {
                    brush = new SolidBrush(cell.ServerCellColor);
                }
                else
                {
                    brush = brushServerCell;
                }
            }

            if (curMapType == LayerMapType.MTGis)
            {
                getCellMultiCovInfoBrush(cell, ref brush);
            }

            List<GraphicsPath> paths = CellPaths;
            graphics.TranslateTransform(point.X, point.Y);//把小区坐标当作坐标原点
            graphics.RotateTransform(cell.Direction - 90);//方向角的计算，以-90°为坐标0°，故要选中旋转坐标为方向角度-90
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            //绘制小区图形
            graphics.FillPath(brush, paths[index]);

            //绘制选择小区边框
            if (mainModel.SelectedNRCell == cell 
                || (mainModel.SelectedNRCells != null && mainModel.SelectedNRCells.Contains(cell)))
            {
                graphics.DrawPath(penSelected, paths[index]);
            }

            //绘制小区边框
            graphics.DrawPath(penFrameCell, paths[index]);
            graphics.ResetTransform();//还原坐标原点
        }

        #region 扫频重叠覆盖小区
        private void getCellMultiCovInfoBrush(NRCell cell, ref Brush brush)
        {
            var multiCov = ZTFunc.NRScanCellMultiCoverage.GetInstance();
            if (multiCov.ResList == null || multiCov.ResList.Count == 0)
            {
                return;
            }
            if (multiCov.SelectedMultiCovCell != null)
            {//双击小区重叠覆盖窗口行时，只显示对应的小区情况
                brush = getSelectedBrush(cell, brush, multiCov);
            }
            else
            {
                brush = getNormalBrush(cell, brush, multiCov);
            }
        }

        private Brush getSelectedBrush(NRCell cell, Brush brush, ZTFunc.NRScanCellMultiCoverage multiCov)
        {
            Color clr = Color.Empty;

            if (multiCov.SelectedMultiCovCell.Cell == cell)
            {
                clr = Color.Red;
            }
            if (Color.Empty.Equals(clr) && multiCov.SelectedMultiCovCell.OtherCellDic.ContainsKey(cell.Name))
            {
                clr = Color.Cyan;
            }
            if (!Color.Empty.Equals(clr))
            {
                brush = new SolidBrush(clr);
            }

            return brush;
        }

        private Brush getNormalBrush(NRCell cell, Brush brush, ZTFunc.NRScanCellMultiCoverage multiCov)
        {
            var info = multiCov.ResList.Find(delegate (ZTFunc.NRCellMultiCovInfo item) { return item.Cell == cell; });
            if (info != null)
            {
                float rate = 0;
                switch (multiCov.ShowCoverageType)
                {
                    case ZTFunc.ShowCoverage.Absolute:
                        rate = info.AbsRate;
                        break;
                    case ZTFunc.ShowCoverage.Relative:
                        rate = info.RelRate;
                        break;
                    case ZTFunc.ShowCoverage.Synthesize:
                        rate = info.MulRate;
                        break;
                    default:
                        break;
                }
                
                foreach (var rng in ZTFunc.NRScanCellMultiCoverageColorRanges.Instance.ColorRanges)
                {
                    if (rate >= rng.minValue && rate < rng.maxValue)
                    {
                        brush = new SolidBrush(rng.color);
                    }
                }
            }

            return brush;
        }
        #endregion
        #endregion

        #region DrawAntenna
        private void drawAntennas(Graphics graphics, DbRect dRect, double mapScale)
        {
            if (DrawAntenna)
            {
                antennasInView = getAntennasInView(dRect);
                foreach (NRAntenna antenna in antennasInView)
                {
                    paintAntenna(antenna, graphics, mapScale);
                }
            }
        }

        private void paintAntenna(NRAntenna antenna, Graphics graphics, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            Brush brush = brushAntenna;
            Pen pen = penFrameAntenna;
            int index = getCellShapeIdx(antenna.Cell);

            bool isServerCell = judgeServerCell(antenna.Cell);
            if (isServerCell)
            {
                brush = brushServerCell;
            }

            List<GraphicsPath> paths = AntennaPaths;
            graphics.TranslateTransform(point.X, point.Y);
            graphics.RotateTransform(antenna.Direction - 90);
            scale = getDisplayScale(scale);
            graphics.ScaleTransform((float)(10000 / scale), (float)(10000 / scale));
            if (index == 0)
            {
                //室内天线
                graphics.DrawPath(pen, paths[index]);
            }
            else
            {
                graphics.FillPath(brush, paths[index]);
            }

            if (mainModel.SelectedNRCell == antenna.Cell
             || (mainModel.SelectedNRCells != null && mainModel.SelectedNRCells.Contains(antenna.Cell)))
            {
                graphics.DrawPath(penSelected, paths[index]);
            }

            graphics.ResetTransform();
        }

        /// <summary>
        /// 获取可视区域内的天线（包括服务小区天线，历史天线）
        /// </summary>
        /// <param name="dRect"></param>
        /// <returns></returns>
        private List<NRAntenna> getAntennasInView(DbRect dRect)
        {
            List<NRCell> cells = getCells();

            List<NRAntenna> curAntennasInView = new List<NRAntenna>();
            foreach (NRCell cell in cells)
            {
                foreach (NRAntenna antenna in cell.Antennas)
                {
                    if (antenna.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2)
                        && ((antenna.Type == NRBTSType.Outdoor && DrawOutdoor)
                           || (antenna.Type == NRBTSType.Indoor && DrawIndoor)))
                    {
                        curAntennasInView.Add(antenna);
                    }
                }
            }

            if (DrawServer && mainModel.ServerNRCells != null)
            {
                foreach (NRCell cell in mainModel.ServerNRCells)
                {
                    curAntennasInView.AddRange(cell.Antennas);
                }
            }
            return curAntennasInView;
        }
        #endregion

        #region DrawBTS
        private void drawBTSs(Graphics graphics, DbRect dRect)
        {
            if (DrawBTS)
            {
                btssInView = getBTSsInView(dRect);
                foreach (NRBTS bts in btssInView)
                {
                    paintBTS(bts, graphics);
                }
            }
        }

        private void paintBTS(NRBTS bts, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            if (bts == mainModel.SelectedNRBTS && curMapType == LayerMapType.MTGis)
            {
                graphics.DrawRectangle(penSelected, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
            }
            else
            {
                graphics.DrawRectangle(PenBTS, -SizeBTS / 2, -SizeBTS / 2, SizeBTS, SizeBTS);
            }
            graphics.ResetTransform();
        }

        private List<NRBTS> getBTSsInView(DbRect dRect)
        {
            List<NRBTS> curBtssInView = new List<NRBTS>();
            List<NRBTS> btss = getBTSs();
            if (btss != null)
            {
                foreach (NRBTS bts in btss)
                {
                    if (bts.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                    {
                        curBtssInView.Add(bts);
                    }
                }
            }
            return curBtssInView;
        }

        private List<NRBTS> getBTSs()
        {
            List<NRBTS> btss;
            if (DrawCurrent)
            {
                btss = mainModel.CellManager.GetCurrentNRBTSs();
            }
            else
            {
                btss = mainModel.CellManager.GetNRBTSs(CurShowSnapshotTime);
            }

            return btss;
        }
        #endregion

        #region Draw Label Base Func
        private bool judgeValidRange()
        {
            if (curMapType == LayerMapType.MTGis || curMapType == LayerMapType.Google && mapScale < 50000)
            {
                return true;
            }
            return false;
        }

        private string addDes(bool isDraw, string des, string str)
        {
            if (isDraw)
            {
                des += str + " ";
            }
            return des;
        }

        private string addFinalDes(int length, string des)
        {
            if (des.Length > length)
            {
                des = des.Substring(0, length);
                des += "...";
            }
            return des;
        }

        public void paintLabel(Graphics graphics, DbPoint dPoint, string des, Font font, List<Rectangle> LabelRectangles)
        {
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            SizeF size = graphics.MeasureString(des, font);
            size.Height *= 0.8f;
            Rectangle rectangle = new Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            bool needDraw = true;
            foreach (Rectangle rectangleTemp in LabelRectangles)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(des, font, Brushes.Black, 3, -size.Height / 2);
                LabelRectangles.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region Draw Cell Label
        private void drawCellsLabel(Graphics graphics, DbRect dRect)
        {
            if (DrawCellLabel)
            {
                bool isValid = judgeValidRange();
                if (isValid)
                {
                    if (!DrawCell)
                    {
                        //之前没有绘制小区的话,需要获取区域内小区信息
                        cellsInView = getCellsInView(dRect);
                    }
                    drawCellsLabelInView(cellsInView, graphics);
                }
            }
        }

        private void drawCellsLabelInView(List<NRCell> cellsInView, Graphics graphics)
        {
            if (cellsInView == null)
            {
                return;
            }
            drawedCellLabelRectangles = new List<Rectangle>();
            foreach (NRCell cell in cellsInView)
            {
                DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                string des = getCellLabelDes(cell, 100);
                paintLabel(graphics, dPoint, des, FontCellLabel, drawedCellLabelRectangles);
            }
        }

        public string getCellLabelDes(NRCell cell, int length)
        {
            string des = "";
            des = addDes(DrawCellName, des, cell.Name);
            des = addDes(DrawCellCode, des, cell.Code);
            des = addDes(DrawCellTAC, des, cell.TAC.ToString());
            des = addDes(DrawCellNCI, des, cell.NCI.ToString());
            des = addDes(DrawCellARFCN, des, cell.SSBARFCN.ToString());
            des = addDes(DrawCellPCI, des, cell.PCI.ToString());
            des = addDes(DrawCellDes, des, cell.DESC);
            des = addFinalDes(length, des);
            return des;
        }

        private List<Rectangle> drawedCellLabelRectangles { get; set; } = new List<Rectangle>();
        #endregion

        #region Draw Antenna Label
        private void drawAntennasLabel(Graphics graphics, DbRect dRect)
        {
            if (DrawAntennaLabel)
            {
                bool isValid = judgeValidRange();
                if (isValid)
                {
                    if (!DrawAntenna)
                    {
                        antennasInView = getAntennasInView(dRect);
                    }
                    drawAntennasLabelInView(antennasInView, graphics);
                }
            }
        }

        private void drawAntennasLabelInView(List<NRAntenna> antennasInView, Graphics graphics)
        {
            if (antennasInView == null)
            {
                return;
            }
            drawedAntennaLabelRectangles = new List<Rectangle>();
            foreach (NRAntenna antenna in antennasInView)
            {
                DbPoint dPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
                string des = getAntennaLabelDes(antenna, 100);
                paintLabel(graphics, dPoint, des, FontCellLabel, drawedAntennaLabelRectangles);
            }
        }

        public string getAntennaLabelDes(NRAntenna antenna, int length)
        {
            string des = "";
            des = addDes(DrawAntennaLongitude, des, antenna.Longitude.ToString());
            des = addDes(DrawAntennaLatitude, des, antenna.Latitude.ToString());
            des = addDes(DrawAntennaDirectionType, des, antenna.DirectionType.ToString());
            des = addDes(DrawAntennaDirection, des, antenna.Direction.ToString());
            des = addDes(DrawAntennaDownward, des, antenna.Downward.ToString());
            des = addDes(DrawAntennaAltitude, des, antenna.Altitude.ToString());
            des = addDes(DrawAntennaDescription, des, antenna.Description);
            des = addFinalDes(length, des);
            return des;
        }
        #endregion

        #region Draw BTS Label
        private void drawBTSLabels(Graphics graphics, DbRect dRect)
        {
            if (DrawBTSLabel)
            {
                bool isValid = judgeValidRange();
                if (isValid)
                {
                    if (!DrawBTS)
                    {
                        btssInView = getBTSsInView(dRect);
                    }
                    drawBTSLabelInView(btssInView, graphics);
                }
            }
        }

        private void drawBTSLabelInView(List<NRBTS> btssInView, Graphics graphics)
        {
            if (btssInView == null)
            {
                return;
            }
            drawedBTSLabelRectangles = new List<Rectangle>();
            foreach (NRBTS bts in btssInView)
            {
                DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
                string des = getBTSLabelDes(bts, 100);
                paintLabel(graphics, dPoint, des, FontBTSLabel, drawedBTSLabelRectangles);
            }
        }

        public string getBTSLabelDes(NRBTS bts, int length)
        {
            string des = "";
            des = addDes(DrawBTSName, des, bts.Name.ToString());
            des = addDes(DrawBTSLongitude, des, bts.Longitude.ToString());
            des = addDes(DrawBTSLatitude, des, bts.Latitude.ToString());
            des = addDes(DrawBTSType, des, bts.TypeStringDesc);
            des = addDes(DrawBTSDescription, des, bts.Description);
            des = addFinalDes(length, des);
            return des;
        }
        #endregion
        #endregion

        #region 图层点选联动
        #region 精确模式
        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            MapForm mf = sender as MapForm;
            Select(((MapForm.MapEventArgs)e).MapOp2, mf.NRBTSs, mf.NRCells, mf.NRAntennas);
        }

        public void Select(MapOperation2 mop2, List<NRBTS> selectedBTSs, List<NRCell> selectedCells, List<NRAntenna> selectedAntennas)
        {
            select(mop2, selectedBTSs, selectedCells, selectedAntennas, DrawBTS, DrawAntenna, DrawCell);
        }

        private void select(MapOperation2 mop2, List<NRBTS> selectedBTSs, List<NRCell> selectedCells, List<NRAntenna> selectedAntennas, bool drawBTS, bool drawAntenna, bool drawCell)
        {
            if (!IsVisible)
            {
                return;
            }
            DbRect dRect = mop2.GetRegion().Bounds;
            dealBTSSelect(mop2, selectedBTSs, drawBTS, dRect);

            NRAntenna selectedAntenna = dealAntennaSelect(mop2, drawAntenna, dRect);
            if (selectedAntenna != null)
            {
                selectedAntennas.Add(selectedAntenna);
            }

            NRCell selectedCell = dealCellSelect(mop2, dRect, drawCell);
            if (selectedCell != null && !selectedCells.Contains(selectedCell))
            {
                selectedCells.Add(selectedCell);
                addInViewSelectedCellAntenna(selectedAntennas, drawAntenna, selectedCell);
            }
        }

        private void dealBTSSelect(MapOperation2 mop2, List<NRBTS> selectedBTSs, bool DrawBTS, DbRect dRect)
        {
            if (DrawBTS && btssInView != null)
            {
                foreach (NRBTS bts in btssInView)
                {
                    addInViewSelectedBTS(mop2, selectedBTSs, dRect, bts);
                }
            }
        }

        private void addInViewSelectedBTS(MapOperation2 mop2, List<NRBTS> selectedBTSs, DbRect dRect, NRBTS bts)
        {
            if (bts.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01)
               && ((bts.Type == NRBTSType.Outdoor && DrawOutdoor)
                  || (bts.Type == NRBTSType.Indoor && DrawIndoor)))
            {
                NRBTS selectedBTS = selectBTS(bts, mop2);
                if (selectedBTS != null)
                {
                    selectedBTSs.Add(selectedBTS);
                }
            }
        }

        private NRAntenna dealAntennaSelect(MapOperation2 mop2, bool drawAntenna, DbRect dRect)
        {
            if (drawAntenna && antennasInView != null)
            {
                foreach (NRAntenna antenna in antennasInView)
                {
                    NRAntenna validAntenna = getValidSelectedAntenna(mop2, dRect, antenna);
                    if (validAntenna != null)
                    {
                        return validAntenna;
                    }
                }
            }
            return null;
        }

        private NRAntenna getValidSelectedAntenna(MapOperation2 mop2, DbRect dRect, NRAntenna antenna)
        {
            if (antenna.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
            {
                NRAntenna selectedAntenna = selectAntenna(antenna, mop2, mapScale);
                if (selectedAntenna != null)
                {
                    return selectedAntenna;
                }
            }
            return null;
        }

        private NRCell dealCellSelect(MapOperation2 mop2, DbRect dRect, bool drawCell)
        {
            if (drawCell && cellsInView != null)
            {
                foreach (NRCell cell in cellsInView)
                {
                    NRCell validCell = getValidSelectedCell(mop2, dRect, cell);
                    if (validCell != null)
                    {
                        return validCell;
                    }
                }
            }
            return null;
        }

        private NRCell getValidSelectedCell(MapOperation2 mop2, DbRect dRect, NRCell cell)
        {
            if (cell.Antennas.Count > 0
                && cell.Within(dRect.x1 - 0.01, dRect.y1 - 0.01, dRect.x2 + 0.01, dRect.y2 + 0.01))
            {
                NRCell selectedCell = selectCell(cell, mop2, mapScale);
                if (selectedCell != null)
                {
                    return selectedCell;
                }
            }
            return null;
        }

        private void addInViewSelectedCellAntenna(List<NRAntenna> selectedAntennas, bool drawAntenna, NRCell cell)
        {
            if (drawAntenna)
            {
                foreach (NRAntenna ant in cell.Antennas)
                {
                    if (!selectedAntennas.Contains(ant))
                    {
                        selectedAntennas.Add(ant);
                    }
                }
            }
        }
        #endregion

        #region 粗略模式
        public NRCell SelectCell(MapOperation2 mop2)
        {
            if (!IsVisible)
            {
                return null;
            }
            DbRect dRect = mop2.GetRegion().Bounds;
            NRCell selectedCell = dealCellSelect(mop2, dRect, DrawCell);
            if (selectedCell != null)
            {
                return selectedCell;
            }

            NRAntenna selectedAntenna = dealAntennaSelect(mop2, DrawAntenna, dRect);
            if (selectedAntenna != null)
            {
                return selectedAntenna.Cell;
            }
            return null;
        }
        #endregion

        private NRCell selectCell(NRCell cell, MapOperation2 mop2, double scale)
        {
            if (cell.Antennas.Count == 0)
            {
                return null;
            }
            DbPoint dPoint = new DbPoint(cell.Longitude, cell.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            scale = getDisplayScale(scale);
            float ratio = (float)(10000 / scale);

            int index;
            bool circle = false;
            if (cell.Type == NRBTSType.Indoor)
            {
                index = 0;
                circle = true;
            }
            else
            {
                index = getCellShapeIdx(cell);
            }

            GraphicsPath path = CellPaths[index].Clone() as GraphicsPath;
            bool isInRegion = judgeInRegion(mop2, point, ratio, circle, cell.Direction, path);
            if (isInRegion)
            {
                return cell;
            }
            return null;
        }

        private NRBTS selectBTS(NRBTS bts, MapOperation2 mop2)
        {
            DbPoint dPoint = new DbPoint(bts.Longitude, bts.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            RectangleF rect;

            rect = new RectangleF(point.X - SizeBTS / 2, point.Y - SizeBTS / 2, SizeBTS, SizeBTS);

            DbRect dRect;
            gisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return bts;
            }
            return null;
        }

        private NRAntenna selectAntenna(NRAntenna antenna, MapOperation2 mop2, double scale)
        {
            DbPoint dPoint = new DbPoint(antenna.Longitude, antenna.Latitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            scale = getDisplayScale(scale);
            float ratio = (float)(10000 / scale);

            int index = 1;
            bool circle = false;         
            if (antenna.DirectionType == NRAntennaDirectionType.Omni)
            {
                index = 0;
                circle = true;
            }
            else if (antenna.Cell != null)
            {
                index = getCellShapeIdx(antenna.Cell);
            }

            GraphicsPath path = AntennaPaths[index].Clone() as GraphicsPath;
            bool isInRegion = judgeInRegion(mop2, point, ratio, circle, antenna.Direction, path);
            if (isInRegion)
            {
                return antenna;
            }
            return null;
        }

        private bool judgeInRegion(MapOperation2 mop2, PointF point, float ratio, bool circle, short direction, GraphicsPath path)
        {
            if (circle)
            {
                RectangleF rect = new RectangleF(point.X - (CellOmniDefaultDisplayLength * shapeLengthScale * ratio)
                  , point.Y - (CellOmniDefaultDisplayLength * shapeLengthScale * ratio)
                  , (CellOmniDefaultDisplayLength * shapeLengthScale * ratio) * 2
                  , (CellOmniDefaultDisplayLength * shapeLengthScale * ratio) * 2);
                DbRect dRect;
                gisAdapter.FromDisplay(rect, out dRect);
                if (mop2.CheckCenterInDRect(dRect))
                {
                    return true;
                }
            }
            else
            {
                Matrix translateMatrix = new Matrix();
                translateMatrix.Translate(point.X, point.Y);
                translateMatrix.Rotate(direction - 90);
                translateMatrix.Scale(ratio, ratio);
                path.Transform(translateMatrix);
                PointF selPnt;
                gisAdapter.ToDisplay(mop2.GetRegion().Bounds.Center(), out selPnt);
                if (path.IsVisible(selPnt))
                {
                    return true;
                }
            }
            return false;
        }
        #endregion

        #region 绘制图层的属性
        #region Base
        public override void SetOpacity(int curAlpha)
        {
            ColorSelected = Color.FromArgb(curAlpha, ColorSelected);
            ColorServerCell = Color.FromArgb(curAlpha, ColorServerCell);

            ColorCell = Color.FromArgb(curAlpha, ColorCell);
            ColorFrameCell = Color.FromArgb(curAlpha, ColorFrameCell);
            ColorBTS = Color.FromArgb(curAlpha, ColorBTS);
            ColorAntenna = Color.FromArgb(curAlpha, ColorAntenna);
        }

        public bool DrawServer { get; set; } = true;
        public bool DrawOutdoor { get; set; } = true;
        public bool DrawIndoor { get; set; } = true;

        /// <summary>
        /// 选择画笔(边框)
        /// </summary>
        private Pen penSelected = new Pen(Color.Red, 4);
        public Color ColorSelected
        {
            get { return penSelected.Color; }
            set { penSelected = new Pen(value, 4); }
        }

        /// <summary>
        /// 主服小区画刷(填充图形)
        /// </summary>
        private SolidBrush brushServerCell = new SolidBrush(Color.Red);
        public Color ColorServerCell
        {
            get { return brushServerCell.Color; }
            set { brushServerCell = new SolidBrush(value); }
        }
        #endregion

        #region Draw Cell
        /// <summary>
        /// 当前可视区域内小区
        /// </summary>
        List<NRCell> cellsInView = null;
        public bool DrawCell { get; set; } = true;

        /// <summary>
        /// 普通小区画刷(填充图形)
        /// </summary>
        private SolidBrush brushCell = new SolidBrush(Color.Gold);
        public Color ColorCell
        {
            get { return brushCell.Color; }
            set { brushCell = new SolidBrush(value); }
        }

        /// <summary>
        /// 普通小区画笔(边框)
        /// </summary>
        private Pen penFrameCell = new Pen(Color.FromArgb(255,128,0), 2);
        public Color ColorFrameCell
        {
            get { return penFrameCell.Color; }
            set { penFrameCell = new Pen(value, 2); }
        }
        #region Draw Cell Label
        public bool DrawCellLabel { get; set; } = true;
        public Font FontCellLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawCellName { get; set; } = true;
        public bool DrawCellCode { get; set; } = false;
        public bool DrawCellTAC { get; set; } = false;
        public bool DrawCellNCI { get; set; } = false;
        public bool DrawCellDes { get; set; } = false;
        public bool DrawCellPCI { get; set; } = false;
        public bool DrawCellARFCN { get; set; } = false;
        public bool DrawCellFreqs { get; set; } = false;
        #endregion
        #endregion

        #region Draw BTS
        /// <summary>
        /// 当期可视区域内BTS
        /// </summary>
        List<NRBTS> btssInView = null;
        public bool DrawBTS { get; set; }
        public int SizeBTS { get; set; } = 6;

        public Pen PenBTS { get; private set; } = new Pen(Color.Black, 2);
        public Color ColorBTS
        {
            get { return PenBTS.Color; }
            set { PenBTS = new Pen(value, 2); }
        }
        #region Draw BTS Label
        public bool DrawBTSLabel { get; set; } = false;
        public Font FontBTSLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawBTSName { get; set; } = false;
        public bool DrawBTSLongitude { get; set; } = false;
        public bool DrawBTSLatitude { get; set; } = false;
        public bool DrawBTSType { get; set; } = false;
        public bool DrawBTSDescription { get; set; } = false;

        private List<Rectangle> drawedAntennaLabelRectangles { get; set; } = new List<Rectangle>();
        #endregion
        #endregion

        #region Draw Antenna
        /// <summary>
        /// 当前可视区域内天线
        /// </summary>
        List<NRAntenna> antennasInView = null;
        public bool DrawAntenna { get; set; }

        private SolidBrush brushAntenna = new SolidBrush(Color.Orange);
        private Pen penFrameAntenna = new Pen(Color.Orange, 2);
        public Color ColorAntenna
        {
            get { return brushAntenna.Color; }
            set
            { 
                brushAntenna = new SolidBrush(value);
                penFrameAntenna = new Pen(value, 2);
            }
        }
        #region Draw Antenna Label
        public bool DrawAntennaLabel { get; set; } = false;
        public Font FontAntennaLabel { get; set; } = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        public bool DrawAntennaLongitude { get; set; } = false;
        public bool DrawAntennaLatitude { get; set; } = false;
        public bool DrawAntennaDirectionType { get; set; } = false;
        public bool DrawAntennaDirection { get; set; } = false;
        public bool DrawAntennaDownward { get; set; } = false;
        public bool DrawAntennaAltitude { get; set; } = false;
        public bool DrawAntennaDescription { get; set; } = false;

        private List<Rectangle> drawedBTSLabelRectangles { get; set; } = new List<Rectangle>();
        #endregion
        #endregion
        #endregion

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["featrueMaxZoomScale"] = FeatrueMaxZoomScale,
                    ["drawCurrent"] = DrawCurrent,
                    ["CurShowSnapshotTime"] = CurShowSnapshotTime.Ticks,
                    ["drawServer"] = DrawServer,
                    ["drawIndoor"] = DrawIndoor,
                    ["drawOutdoor"] = DrawOutdoor,
                    ["colorSelected"] = penSelected.Color.ToArgb(),

                    //BTS
                    ["drawBTS"] = DrawBTS,
                    ["colorBTS"] = PenBTS.Color.ToArgb(),
                    ["sizeBTS"] = SizeBTS,
                    ["drawBTSLabel"] = DrawBTSLabel,
                    ["fontBTSLabelFontFamilyName"] = FontBTSLabel.FontFamily.Name,
                    ["fontBTSLabelFontSize"] = FontBTSLabel.Size,
                    ["fontBTSLabelFontStyle"] = (int)FontBTSLabel.Style,
                    ["drawBTSName"] = DrawBTSName,
                    ["drawBTSLongitude"] = DrawBTSLongitude,
                    ["drawBTSLatitude"] = DrawBTSLatitude,
                    ["drawBTSType"] = DrawBTSType,
                    ["drawBTSDescription"] = DrawBTSDescription,

                    //cell
                    ["drawCell"] = DrawCell,
                    ["colorCell"] = brushCell.Color.ToArgb(),
                    ["drawCellLabel"] = DrawCellLabel,
                    ["fontCellLabelFamilyName"] = FontCellLabel.FontFamily.Name,
                    ["fontCellLabelFontSize"] = FontCellLabel.Size,
                    ["fontCellLabelFontStyle"] = (int)FontCellLabel.Style,
                    ["drawCellName"] = DrawCellName,
                    ["drawCellCode"] = DrawCellCode,
                    ["drawCellTAC"] = DrawCellTAC,
                    ["drawCellNCI"] = DrawCellNCI,
                    ["drawCellDes"] = DrawCellDes,
                    ["drawCellPCI"] = DrawCellPCI,
                    ["drawCellARFCN"] = DrawCellARFCN,
                    ["drawCellFreqs"] = DrawCellFreqs,

                    //Antenna
                    ["drawAntenna"] = DrawAntenna,
                    ["colorAntenna"] = brushAntenna.Color.ToArgb(),
                    ["drawAntennaLabel"] = DrawAntennaLabel,
                    ["fontAntennaLabelFamilyName"] = FontAntennaLabel.FontFamily.Name,
                    ["fontAntennaLabelFontSize"] = FontAntennaLabel.Size,
                    ["fontAntennaLabelFontStyle"] = (int)FontAntennaLabel.Style,
                    ["drawAntennaLongitude"] = DrawAntennaLongitude,
                    ["drawAntennaLatitude"] = DrawAntennaLatitude,
                    ["drawAntennaDirectionType"] = DrawAntennaDirectionType,
                    ["drawAntennaDirection"] = DrawAntennaDirection,
                    ["drawAntennaDownward"] = DrawAntennaDownward,
                    ["drawAntennaAltitude"] = DrawAntennaAltitude,
                    ["drawAntennaDescription"] = DrawAntennaDescription,

                    ["isVisible"] = IsVisible
                };
                return param;
            }
            set
            {
                FeatrueMaxZoomScale = (double)value["featrueMaxZoomScale"];
                DrawCurrent = (bool)value["drawCurrent"];
                if (value.ContainsKey("CurShowSnapshotTime"))
                {
                    CurShowSnapshotTime = new DateTime((long)(value["CurShowSnapshotTime"]));
                }
                DrawServer = (bool)value["drawServer"];
                DrawIndoor = (bool)value["drawIndoor"];
                DrawOutdoor = (bool)value["drawOutdoor"];
                penSelected = new Pen(Color.FromArgb((int)value["colorSelected"]), 4);

                DrawBTS = (bool)value["drawBTS"];
                PenBTS = new Pen(Color.FromArgb((int)value["colorBTS"]), 2);
                SizeBTS = (int)value["sizeBTS"];
                DrawBTSLabel = (bool)value["drawBTSLabel"];
                FontBTSLabel = new Font(new FontFamily((String)value["fontBTSLabelFontFamilyName"]), (float)value["fontBTSLabelFontSize"], (FontStyle)(int)value["fontBTSLabelFontStyle"]);
                DrawBTSName = (bool)value["drawBTSName"];
                DrawBTSLongitude = (bool)value["drawBTSLongitude"];
                DrawBTSLatitude = (bool)value["drawBTSLatitude"];
                DrawBTSType = (bool)value["drawBTSType"];
                DrawBTSDescription = (bool)value["drawBTSDescription"];

                DrawCell = (bool)value["drawCell"];
                brushCell = new SolidBrush(Color.FromArgb((int)(value["colorCell"])));              
                FontCellLabel = new Font(new FontFamily((String)value["fontCellLabelFamilyName"]), (float)value["fontCellLabelFontSize"], (FontStyle)(int)value["fontCellLabelFontStyle"]);
                DrawCellLabel = (bool)value["drawCellLabel"];
                DrawCellName = (bool)value["drawCellName"];
                DrawCellCode = (bool)value["drawCellCode"];
                DrawCellTAC = (bool)value["drawCellTAC"];
                DrawCellNCI = (bool)value["drawCellNCI"];
                DrawCellDes = (bool)value["drawCellDes"];
                DrawCellPCI = (bool)value["drawCellPCI"];
                DrawCellARFCN = (bool)value["drawCellARFCN"];
                DrawCellFreqs = (bool)value["drawCellFreqs"];

                DrawAntenna = (bool)value["drawAntenna"];
                Color antennaColor = Color.FromArgb((int)value["colorAntenna"]);
                penFrameAntenna = new Pen(antennaColor, 2);
                brushAntenna = new SolidBrush(antennaColor);
                DrawAntennaLabel = (bool)value["drawAntennaLabel"];
                FontAntennaLabel = new Font(new FontFamily((string)value["fontAntennaLabelFamilyName"]), (float)value["fontAntennaLabelFontSize"], (FontStyle)(int)value["fontAntennaLabelFontStyle"]);
                DrawAntennaLongitude = (bool)value["drawAntennaLongitude"];
                DrawAntennaLatitude = (bool)value["drawAntennaLatitude"];
                DrawAntennaDirectionType = (bool)value["drawAntennaDirectionType"];
                DrawAntennaDirection = (bool)value["drawAntennaDirection"];
                DrawAntennaDownward = (bool)value["drawAntennaDownward"];
                DrawAntennaAltitude = (bool)value["drawAntennaAltitude"];
                DrawAntennaDescription = (bool)value["drawAntennaDescription"];

                IsVisible = (bool)value["isVisible"];
            }
        }
    }
}
