﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.ZTFunc
{
    public class ImportAreaCfgEntry : QueryBase
    {
        public ImportAreaCfgEntry()
            : base(MainModel.GetInstance())
        {
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        ImportAreaForm importForm = null;
        protected override void query()
        {
            if (importForm == null || importForm.IsDisposed)
            {
                importForm = new ImportAreaForm();
            }
            importForm.ShowDialog();
        }


        public override string Name
        {
            get { return string.Empty; }
        }
    }
}
