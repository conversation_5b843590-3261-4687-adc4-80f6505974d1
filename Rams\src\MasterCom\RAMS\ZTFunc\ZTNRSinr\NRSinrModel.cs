﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRSinrResultModel
    {
        public NRSinrResultModel(int opre, float extremum, string kpiname, int carrierType)
        {
            NRSinrModel = new NRSinrModel(opre, extremum, kpiname);
            totalModel = new NRSinrModel();
            CarrierType = carrierType;
        }

        public NRSinrModel NRSinrModel { get; set; }
        public NRSinrModel totalModel { get; set; }
        public int CarrierType { get; set; }
        public string CarrierName
        {
            get
            {
                if (CarrierType == 1)
                {
                    return "移动";
                }
                else if (CarrierType == 2)
                {
                    return "联通";
                }
                else
                {
                    return "电信";
                }
            }
        }
    }

    public class NRSinrModel
    {
        public NRSinrModel(int opre, float extremum, string kpiname)
        {
            SINRExtremum = extremum;
            Items = new List<NRSinrItemModel>();
            KPIName = kpiname;
        }
        public NRSinrModel()
        {
            Items = new List<NRSinrItemModel>();
            opre = -1;
        }

        private readonly int opre;
        public string Name
        {
            get
            {
                if (opre != -1)
                {
                    return "SINR" + getType(opre) + SINRExtremum + "采样点";
                }
                else
                {
                    return "SINR总采样点";
                }
            }
        }

        public float SINRExtremum { get; }
        public string KPIName { get; set; }
        public List<NRSinrItemModel> Items { get; set; }

        private string getType(int type)
        {
            switch (type)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                default:
                    return "";
            }
        }
    }

    public class NRSinrItemModel
    {
        public NRSinrItemModel(int opreType, float extremumFirst, float extremumSecond, string kpiName)
        {
            this.OperType = opreType;
            this.ExtremumFirst = extremumFirst;
            this.ExtremumSecond = extremumSecond;
            this.kpiName = kpiName;
        }

        public NRSinrItemModel()
        {
        }

        public int OperType { get; }
        public float ExtremumFirst { get; }
        public float ExtremumSecond { get; }
        private readonly string kpiName;
        //满足条件的采样点数
        public int PointCount { get; set; }

        public string getType()
        {
            switch (OperType)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                case 3:
                    return "≤";
                default:
                    return "";
            }
        }

        public string Name
        {
            get
            {
                if (OperType == (int)NRSinrOperatorsType.GreaterThanOrEqual)
                {
                    return ExtremumFirst + "<" + kpiName + "≤" + ExtremumSecond;
                }
                else
                {
                    return kpiName + getType() + ExtremumFirst;
                }
            }
        }
    }
}
