﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Stat;
using System.Collections;

namespace MasterCom.RAMS.Func
{
    public partial class RenderingFilterControl : UserControl
    {
        /// <summary>
        /// 原始数据
        /// </summary>
        List<string> listOriginalData = new List<string>();

        /// <summary>
        /// 是否排序
        /// </summary>
        bool toSort = true;

        /// <summary>
        /// 排序类型
        /// </summary>
        string sortType = "string";

        public RenderingFilterControl()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 设置初始化数据
        /// </summary>
        /// <param name="colName">列名</param>
        /// <param name="colWidth">列宽</param>
        /// <param name="filter">初始筛选条件</param>
        /// <param name="infos">数据源</param>
        /// <param name="toSort">是否排序(false为按数据源顺序显示)</param>
        /// <param name="sortType">排序类型(默认按string类型排序)</param>
        public void SetInitData(string colName, int colWidth, string filter, List<string> infos, bool toSort, string sortType = "string")
        {
            this.toSort = toSort;
            this.sortType = sortType;
            Clear();
            listViewData.Columns.Add(colName, colWidth);
            listViewData.HideSelection = false;
            //避免相同条件多选
            bool isSelected = false;
            foreach (string info in infos)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Tag = info;
                lvi.Text = info.ToString();
                if (!isSelected && info.ToString() == filter)
                {
                    lvi.Selected = true;
                    isSelected = true;
                    popupCntEdit.Text = info.ToString();
                }
                listViewData.Items.Add(lvi);
                listOriginalData.Add(info);
            }
            if (toSort)
            {
                listViewData_ColumnClick(null, null);
            }
        }

        public void Clear()
        {
            listOriginalData.Clear();
            listViewData.Items.Clear();
            listViewData.Columns.Clear();
        }

        /// <summary>
        /// 获取选择的文本
        /// </summary>
        /// <returns></returns>
        public new string Text
        {
            get { return popupCntEdit.Text; }
        }

        public int SelectedIndex
        {
            get 
            {
                if (listViewData.SelectedItems.Count == 0 && listViewData.Items.Count > 0)
                {
                    return 0;
                }
                return listViewData.SelectedItems[0].Index; 
            }
        }

        public List<string> Items
        {
            get { return listOriginalData; }
        }

        /// <summary>
        /// 2个筛选用的控件宽度显示效果对齐
        /// </summary>
        private void popupContainerEdit_QueryPopUp(object sender, CancelEventArgs e)
        {
            PopupContainerEdit popupedit = (PopupContainerEdit)sender;
            popupContainerCtr.Width = popupedit.Width - 4;
        }

        /// <summary>
        /// 筛选条件改变时对应listbox数据改变
        /// </summary>
        private void txtFilter_TextChanged(object sender, EventArgs e)
        {
            List<string> listInfo = new List<string>();
            foreach (string item in listOriginalData)
            {
                if (string.IsNullOrEmpty(txtFilter.Text) || item.ToUpper().IndexOf(txtFilter.Text.ToUpper()) >= 0)
                {
                    listInfo.Add(item);
                }
            }
            listViewData.Items.Clear();
            foreach (string info in listInfo)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Tag = info;
                lvi.Text = info;
                listViewData.Items.Add(lvi);
            }
        }

        private void listViewData_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewData.SelectedItems != null && listViewData.SelectedItems.Count > 0)
            {
                StringBuilder name = new StringBuilder();
                foreach (ListViewItem item in listViewData.SelectedItems)
                {
                    name.Append((item.Tag as string) + ",");
                }
                popupCntEdit.Text = name.ToString().TrimEnd(',');
            }
        }

        /// <summary>
        /// 双击关闭选择框
        /// </summary>
        private void listViewData_DoubleClick(object sender, EventArgs e)
        {
            popupCntEdit.ClosePopup();
        }

        private void listViewData_ColumnClick(object sender, ColumnClickEventArgs e)
        {
            if (toSort)
            {
                int column = 0;
                if (e != null)
                {
                    column = e.Column;
                }
                //使用列Tag标记是否为倒序
                if (this.listViewData.Columns[column].Tag == null)
                {
                    this.listViewData.Columns[column].Tag = true;
                }
                bool flag = (bool)this.listViewData.Columns[column].Tag;
                //每次点击正序倒序切换
                if (flag)
                {
                    //第一次点击为正序排序
                    this.listViewData.Columns[column].Tag = false;
                }
                else
                {
                    this.listViewData.Columns[column].Tag = true;
                }

                this.listViewData.ListViewItemSorter = new ListViewSort(column, this.listViewData.Columns[column].Tag, sortType);
                this.listViewData.Sort();
            }
        }

        //自定义事件
        public delegate void EditValueChangedHandle(object sender, EventArgs e);
        public event EditValueChangedHandle UserControlEditValueChanged;
        private void popupCntEdit_EditValueChanged(object sender, EventArgs e)
        {
            if (UserControlEditValueChanged != null)
                UserControlEditValueChanged(sender, new EventArgs());
        }
    }

    public class ListViewSort : IComparer
    {
        private readonly int col;
        private readonly bool desc;
        readonly string sortType;
        public ListViewSort()
        {
            col = 0;
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="column">列index</param>
        /// <param name="Desc">是否倒序</param>
        public ListViewSort(int column, object Desc, string sortType = "string")
        {
            desc = (bool)Desc;
            col = column;
            this.sortType = sortType;
        }

        public int Compare(object x, object y)
        {
            int tempInt = 0;
            switch (sortType)
            {
                case "int":
                    tempInt = Convert.ToInt32(((ListViewItem)x).SubItems[col].Text) -  Convert.ToInt32(((ListViewItem)y).SubItems[col].Text);
                    break;
                default:
                    tempInt = String.Compare(((ListViewItem)x).SubItems[col].Text, ((ListViewItem)y).SubItems[col].Text);
                    break;
            }

            if (desc)
            {
                //如果倒序则取负数
                return -tempInt;
            }
            else
            {
                //正序排序
                return tempInt;
            }
        }
    }

}
