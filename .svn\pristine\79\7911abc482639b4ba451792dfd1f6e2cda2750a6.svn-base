﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellCoverTypeAnaQuery : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static NRCellCoverTypeAnaQuery instance = null;
        public static NRCellCoverTypeAnaQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRCellCoverTypeAnaQuery();
                    }
                }
            }
            return instance;
        }

        private List<NRRegionCellCoverTypeAnaInfo> regionCells = null;
        public Dictionary<string, Dictionary<string, NRCellCoverTypeAnaInfo>> regionCellsDic { get; set; } = new Dictionary<string, Dictionary<string, NRCellCoverTypeAnaInfo>>();
        public List<string> regionNames { get; set; } = new List<string>();
        readonly NRCellCoverTypeAnaCond curCondtion = new NRCellCoverTypeAnaCond();

        protected NRCellCoverTypeAnaQuery()
            : base(MainModel.GetInstance())
        {
            this.Columns = new List<string>();
            Columns.Add("isampleid");
            Columns.Add("itime");
            Columns.Add("FileName");
            Columns.Add("ilongitude");
            Columns.Add("ilatitude");
            Columns.Add("NR_SSB_ARFCN");
            Columns.Add("NR_PCI");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get
            {
                return "小区占用情况分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35036, this.Name);
        }

        protected override bool getCondition()
        {
            NRCellCoverTypeAnaDlg dlg = new NRCellCoverTypeAnaDlg();
            dlg.SetCondition(curCondtion);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(curCondtion);
            
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            regionNames.Clear();
            regionCellsDic = new Dictionary<string, Dictionary<string, NRCellCoverTypeAnaInfo>>();
            regionCells = new List<NRRegionCellCoverTypeAnaInfo>();
        }

        /// <summary>
        /// 数据处理
        /// </summary>
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (isValidTestPoint(tp))
                    {
                        ICell cell = tp.GetMainCell_NR();
                        if (cell != null)
                        {
                            judgeCell(cell, tp);
                        }
                    }
                }
            }
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is TestPoint_NR)
            {
                if (condition.Geometorys != null && condition.Geometorys.Region != null
                    && !condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
                {
                    return false;
                }
                return true;
            }
            return false;
        }

        protected virtual void judgeCell(ICell cell, TestPoint testPoint)
        {
            string regionName = getRegionName(testPoint.Longitude, testPoint.Latitude);

            if (regionCellsDic.ContainsKey(regionName))
            {
                if (regionCellsDic[regionName].ContainsKey(cell.Name))
                {
                    regionCellsDic[regionName][cell.Name].AddTestPoint(testPoint);
                }
                else
                {
                    NRCellCoverTypeAnaInfo cellAnaInfo = new NRCellCoverTypeAnaInfo(cell as NRCell, testPoint);
                    regionCellsDic[regionName][cell.Name] = cellAnaInfo;
                }
            }
            else
            {
                NRCellCoverTypeAnaInfo cellAnaInfo = new NRCellCoverTypeAnaInfo(cell as NRCell, testPoint);
                Dictionary<string, NRCellCoverTypeAnaInfo> cellDic = new Dictionary<string, NRCellCoverTypeAnaInfo>();
                cellDic[cell.Name] = cellAnaInfo;
                regionCellsDic[regionName] = cellDic;
                regionNames.Add(regionName);
            }
            regionCellsDic[regionName][cell.Name].ServiceTimes++;
        }

        #region 获取区域名
        public string getRegionName(double x, double y)
        {
            if ((MainModel.MultiGeometrys && Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count > 0)
                || (Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count == 1))
            {
                return getValidRegionName(x, y);
            }
            else if (Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count == 1)
            {
                return getValidRegionName(x, y);
            }
            return "区域";
        }

        private string getValidRegionName(double x, double y)
        {
            foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
            {
                if (resvRegion.GeoOp.CheckPointInRegion(x, y))
                {
                    return resvRegion.RegionName;
                }
            }
            return "区域";
        }
        #endregion

        protected override void fireShowForm()
        {
            WaitBox.Show("获取B、C类小区...", getOtherCoverTypeCell);
            if (regionCellsDic == null || regionCellsDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            NRCellCoverTypeAnaForm frm = MainModel.GetObjectFromBlackboard(typeof(NRCellCoverTypeAnaForm)) as NRCellCoverTypeAnaForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRCellCoverTypeAnaForm();
            }
            frm.FillData(regionCells);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            regionCellsDic = null;
        }

        #region 处理其他类别小区
        protected virtual void getOtherCoverTypeCell()
        {
            try
            {
                List<NRCell> cells = MainModel.CellManager.GetCurrentNRCells();
                int iLoop = 0;
                foreach (string regionName in regionNames)
                {
                    Dictionary<string, NRCellCoverTypeAnaInfo> cellDic = regionCellsDic[regionName];
                    List<string> cellNames = new List<string>();
                    List<string> cellCodes = new List<string>();
                    addCellNames(cellDic, cellNames, cellCodes);
                    foreach (NRCell cell in cells)
                    {
                        iLoop++;
                        if (cell.BelongBTS.Type != NRBTSType.Indoor && !cellDic.ContainsKey(cell.Name))
                        {
                            addCellTestPoint(regionName, cellDic, cellNames, cell);
                            WaitBox.ProgressPercent = (int)(100.8 * iLoop / cells.Count);
                        }
                    }
                }
                foreach (var keyValue in regionCellsDic)
                {
                    List<NRCellCoverTypeAnaInfo> cellServiceCondtionList = new List<NRCellCoverTypeAnaInfo>();
                    foreach (var cellResult in keyValue.Value)
                    {
                        cellResult.Value.Calcluate();
                        cellServiceCondtionList.Add(cellResult.Value);
                    }
                    NRRegionCellCoverTypeAnaInfo regionCell = new NRRegionCellCoverTypeAnaInfo(keyValue.Key, cellServiceCondtionList);
                    regionCells.Add(regionCell);
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private static void addCellNames(Dictionary<string, NRCellCoverTypeAnaInfo> cellsDic, List<string> cellNames, List<string> cellCodes)
        {
            foreach (string cellName in cellsDic.Keys)
            {
                if (cellsDic[cellName].Category == 0)
                {
                    cellNames.Add(cellName);
                    cellCodes.Add(cellsDic[cellName].Cell.Code);
                }
            }
        }

        private void addCellTestPoint(string regionName, Dictionary<string, NRCellCoverTypeAnaInfo> cellsDic, List<string> cellNames, NRCell cell)
        {
            for (int i = 0; i < cellNames.Count; i++)
            {
                string cellName = cellNames[i];

                NRCell otherCell = CellManager.GetInstance().GetNRCellLatest(cellName);
                if (otherCell != null && cell.BelongBTS.Name == otherCell.BelongBTS.Name)
                {
                    if (Math.Abs(cellsDic[cellName].Cell.Direction - cell.Direction) <= curCondtion.MaxDirection)
                    {
                        addTestPoint(cell, null, 1, regionName);
                    }
                    else
                    {
                        addTestPoint(cell, null, 2, regionName);
                    }
                }
            }
        }

        public virtual void addTestPoint(NRCell cell, TestPoint testPoint, int category, string regionName)
        {
            NRCellCoverTypeAnaInfo cellAnaInfo = new NRCellCoverTypeAnaInfo(cell, testPoint);
            cellAnaInfo.Category = category;
            regionCellsDic[regionName][cell.Name] = cellAnaInfo;
        }
        #endregion
    }
}
