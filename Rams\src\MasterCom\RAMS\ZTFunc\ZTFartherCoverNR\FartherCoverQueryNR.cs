﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class FartherCoverQueryNRBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        public FartherCoverConditionNR FartherCoverCond { get; set; }
        protected Dictionary<string, FartherCoverInfoNR> nameFartherMap;
        protected List<FartherCoverInfoNR> fartherVec;
        public string themeName { get; set; } = "";//默认选中指标

        protected static readonly object lockObj = new object();
        protected FartherCoverQueryNRBase()
            : base(MainModel.GetInstance())
        {
            IncludeEvent = false;
            FartherCoverCond = new FartherCoverConditionNR();
            nameFartherMap = new Dictionary<string, FartherCoverInfoNR>();
            fartherVec = new List<FartherCoverInfoNR>();
            init();
        }

        protected void init()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");

            themeName = "NR:SS_RSRP";
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public override string Name
        {
            get { return "超远覆盖_NR"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 3500, 35021, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            FartherCoverNRSettingDlg dlg = new FartherCoverNRSettingDlg(FartherCoverCond);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                FartherCoverCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            nameFartherMap.Clear();
            fartherVec.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        protected virtual void doWithTestPoint(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            int? tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            if (rsrp == null)
            {
                return;
            }

            NRCell nrCell = CellManager.GetInstance().GetNearestNRCell(tp.DateTime, tac, nci, earfcn, pci, tp.Longitude, tp.Latitude);
            if (nrCell == null)
            {
                return;
            }

            double distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nrCell.Longitude, nrCell.Latitude);
            addCoverInfo(nrCell, tp, (float)rsrp, distance);
        }

        protected void addCoverInfo(NRCell nrCell, TestPoint tp, float rsrp, double distance)
        {
            if (distance >= FartherCoverCond.DistanceMin && distance <= FartherCoverCond.DistanceMax && rsrp >= FartherCoverCond.RsrpThreshold)
            {
                FartherCoverInfoNR info;
                if (!nameFartherMap.TryGetValue(nrCell.Name, out info))
                {
                    info = new FartherCoverInfoNR(nrCell);
                    nameFartherMap.Add(nrCell.Name, info);
                }
                info.DealTestPoint(tp, rsrp, distance);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (KeyValuePair<string, FartherCoverInfoNR> pair in nameFartherMap)
            {
                if (pair.Value.SampleNum >= FartherCoverCond.SampleNum)
                {
                    fartherVec.Add(pair.Value);
                    pair.Value.SN = fartherVec.Count;
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override void fireShowForm()
        {
            if (fartherVec.Count <= 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            FartherCoverInfoNRForm frm = MainModel.CreateResultForm(typeof(FartherCoverInfoNRForm)) as FartherCoverInfoNRForm;
            frm.FillData(fartherVec);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class FartherCoverQueryNRByRegion : FartherCoverQueryNRBase
    {
        private FartherCoverQueryNRByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static FartherCoverQueryNRByRegion instance = null;
        public static FartherCoverQueryNRByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FartherCoverQueryNRByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "超远覆盖_NR(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35004, this.Name);
        }
    }

    public class FartherCoverQueryNRByFile : FartherCoverQueryNRBase
    {
        private FartherCoverQueryNRByFile()
            : base()
        {
        }

        private static FartherCoverQueryNRByFile instance = null;
        public static FartherCoverQueryNRByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FartherCoverQueryNRByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "超远覆盖_NR(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35004, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
