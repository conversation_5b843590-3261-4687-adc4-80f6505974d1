﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XJLTEBtsCheckInfoForm : MinCloseForm
    {
        public XJLTEBtsCheckInfoForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }
        public void FillData(List<XJBaseSitesInfo> infoList)
        {
            gridControl.DataSource = infoList;
            gridControl.RefreshDataSource();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            } 
        }
    }
}
