﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedSeg
    {
        private string roadName = string.Empty;
        public string RoadName
        {
            get { return roadName; }
        }
        public string FileName
        {
            get
            {
                string fName = string.Empty;
                if (TestPoints.Count > 0)
                {
                    fName = TestPoints[0].FileName;
                }
                return fName;
            }
        }
        public double MidLng
        {
            get
            {
                return TestPoints[TestPoints.Count / 2].Longitude;
            }
        }

        public double MidLat
        {
            get
            {
                return TestPoints[TestPoints.Count / 2].Latitude;
            }
        }

        public double AvgSpeed
        {
            get;
            private set;
        }

        public void MakeSummary()
        {
            this.PointDetails.Sort();

            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(TestPoints[0].Longitude);
            lats.Add(TestPoints[0].Latitude);
            lngs.Add(MidLng);
            lats.Add(MidLat);
            lngs.Add(TestPoints[TestPoints.Count - 1].Longitude);
            lats.Add(TestPoints[TestPoints.Count - 1].Latitude);
            roadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);

            Dictionary<string, int> causeNumDic = new Dictionary<string, int>();
            double totalSpeed = 0;
            foreach (NRLowSpeedPointDetail detail in pntReasonDic.Values)
            {
                if (!causeNumDic.ContainsKey(detail.CauseScene))
                {
                    causeNumDic[detail.CauseScene] = 1;
                }
                else
                {
                    causeNumDic[detail.CauseScene]++;
                }

                double? speed = NRTpHelper.NrTpManager.GetAppSpeedMb(detail.TestPoint);
                if (speed != null)
                {
                    totalSpeed += (double)speed;
                }
            }
            AvgSpeed = Math.Round(totalSpeed / TestPoints.Count, 2);

            int maxNum = -1;
            foreach (string name in causeNumDic.Keys)
            {
                int num = causeNumDic[name];
                if (num > maxNum)
                {
                    MainCause = name;
                    maxNum = num;
                }
            }
            if (string.IsNullOrEmpty(MainCause))
            {
                MainCause = "未知原因";
            }
            MainCausePer = Math.Round(100.0 * maxNum / pntReasonDic.Count, 2);
        }
        public string MainCause { get; private set; }
        public double? MainCausePer { get; private set; }

        public int SampleCount
        {
            get { return TestPoints.Count; }
        }

        public List<TestPoint> TestPoints
        {
            get;
            private set;
        } = new List<TestPoint>();

        public List<Event> Events
        {
            get;
            private set;
        } = new List<Event>();

        public void AddEvent(Event e)
        {
            if (!Events.Contains(e))
            {
                Events.Add(e);
            }
        }

        private List<NRLowSpeedPointDetail> details = null;
        public List<NRLowSpeedPointDetail> PointDetails
        {
            get
            {
                if (details == null)
                {
                    details = new List<NRLowSpeedPointDetail>(pntReasonDic.Values);
                }
                return details;
            }
        }
        private readonly Dictionary<TestPoint, NRLowSpeedPointDetail> pntReasonDic = new Dictionary<TestPoint, NRLowSpeedPointDetail>();
        public bool NeedJudge
        {
            get { return pntReasonDic.Count != TestPoints.Count; }
        }

        public void SetUnknowReason(NRTpManagerBase nRCond)
        {
            foreach (TestPoint pnt in TestPoints)
            {
                if (!pntReasonDic.ContainsKey(pnt))
                {
                    pntReasonDic[pnt] = new NRLowSpeedPointDetail(pnt, new NRLowSpeedUnknowReason(), nRCond);
                }
            }
        }

        public bool IsNeedJudge(TestPoint pnt)
        {
            return !pntReasonDic.ContainsKey(pnt);
        }

        internal void SetReason(NRLowSpeedPointDetail reasonInfo)
        {
            if (TestPoints.Contains(reasonInfo.TestPoint))
            {
                pntReasonDic[reasonInfo.TestPoint] = reasonInfo;
            }
        }

        public double Distance
        {
            get;
            private set;
        }

        public double Second
        {
            get { return TestPoints[TestPoints.Count - 1].Time - TestPoints[0].Time; }
        }

        internal void AddTestPoint(TestPoint tp)
        {
            if (TestPoints.Count > 0)
            {
                Distance += TestPoints[TestPoints.Count - 1].Distance2(tp);
            }
            TestPoints.Add(tp);
        }
    }
}
