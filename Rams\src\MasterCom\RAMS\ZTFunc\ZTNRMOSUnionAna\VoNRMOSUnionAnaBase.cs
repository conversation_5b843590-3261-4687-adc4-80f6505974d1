﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Chris.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoNRMOSUnionAnaBase : VoNRMOSQueryAnaBase
    {
        public VoNRMOSUnionAnaBase(MainModel mm) : base(mm)
        {
            mainModel = mm;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27001, this.Name);
        }

        public bool NeedJudgeTestPointByRegion { get; set; } = false;

        NRMosAnaCondition mosCondition = null;

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            var dlg = new VoNRMosSettingDlg(mosCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                mosCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void queryMOSFiles()
        {
            List<string> colNames = null;
            prepareSampleQryColumnNames(out colNames);
            DIYReplayFileQueryByCustom repFile = new DIYReplayFileQueryByCustom(MainModel);
            if (!NeedJudgeTestPointByRegion)
            {
                condition.Geometorys = null;
            }

            repFile.SetQueryCondition(condition);
            repFile.SetReplayContent(colNames, false, false);
            repFile.Query();
        }

        double moskeymax = 0;
        int evenkeymax = 0;
        readonly Dictionary<double, List<float>> dicMos = new Dictionary<double, List<float>>();
        readonly Dictionary<int, List<float>> dicEvenCountMos = new Dictionary<int, List<float>>();
        readonly Dictionary<string, List<float>> dicSinrCountMos = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicRsrpCountMos = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicModulationModeMos = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicDlQam16Mos = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicDlQam64Mos = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicDlQpskMos = new Dictionary<string, List<float>>();

        readonly Dictionary<int, List<float>> dicEvenCountPer = new Dictionary<int, List<float>>();
        readonly Dictionary<string, List<float>> dicSinrCountPer = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicRsrpCountPer = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicModulationModePer = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicDlQam16Per = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicDlQam64Per = new Dictionary<string, List<float>>();
        readonly Dictionary<string, List<float>> dicDlQpskPer = new Dictionary<string, List<float>>();

        protected override void FireShowForm(List<VoNRMosParam> mosParamList)
        {
            doSomethingAfterQuery(mosParamList);

            if (dicMos.Values.Count != 0)
            {
                var frm = MainModel.CreateResultForm(typeof(VoNRMosAnalysisShow)) as VoNRMosAnalysisShow;
                var info = new VoNRMosAnalysisInfo()
                {
                    DicMos = dicMos,
                    DicEvenCountMos = dicEvenCountMos,
                    DicSinrCountMos = dicSinrCountMos,
                    DicModulationMos = dicModulationModeMos,
                    DicEvenCountPer = dicEvenCountPer,
                    DicSinrCountPer = dicSinrCountPer,
                    DicModulationPer = dicModulationModePer,
                    DicRsrpCountMos = dicRsrpCountMos,
                    DicRsrpCountPer = dicRsrpCountPer,
                    DicDlQam16Mos = dicDlQam16Mos,
                    DicDlQam16Per = dicDlQam16Per,
                    DicDlQam64Mos = dicDlQam64Mos,
                    DicDlQam64Per = dicDlQam64Per,
                    DicDlQpskMos = dicDlQpskMos,
                    DicDlQpskPer = dicDlQpskPer
                };

                frm.FillData(moskeymax, evenkeymax, info, mosCondition);
                frm.Visible = true;
            }
            else
            {
                XtraMessageBox.Show("未查询到结果。");
            }
        }

        protected void doSomethingAfterQuery(List<VoNRMosParam> mosParams)
        {
            foreach (VoNRMosParam mosParam in mosParams)
            {
                double mos01 = (int)(double.Parse(mosParam.MOS.ToString()) * 10.0d) / 10.0;
                //mos区间集合
                if (!dicMos.ContainsKey(mos01))
                {
                    List<float> mosLst = new List<float>();
                    mosLst.Add(mosParam.MOS);
                    dicMos.Add(mos01, mosLst);
                }
                else
                {
                    dicMos[mos01].Add(mosParam.MOS);
                }

                moskeymax = Math.Max(moskeymax, mos01);
            }
            InitData();
            StatMOS(mosParams);
        }

        public void InitData()
        {
            moskeymax = 0;
            evenkeymax = 0;
            dicMos.Clear();
            dicEvenCountMos.Clear();
            dicSinrCountMos.Clear();
            dicRsrpCountMos.Clear();
            dicModulationModeMos.Clear();
            dicDlQam16Mos.Clear();
            dicDlQam64Mos.Clear();
            dicDlQpskMos.Clear();
            dicEvenCountPer.Clear();
            dicSinrCountPer.Clear();
            dicRsrpCountPer.Clear();
            dicModulationModePer.Clear();
            dicDlQam16Per.Clear();
            dicDlQam64Per.Clear();
            dicDlQpskPer.Clear();
        }

        public void StatMOS(List<VoNRMosParam> mosParams)
        {
            foreach (var mosParam in mosParams)
            {
                //满功率
                bool IsMSPowerFull = mosParam.ownParam.IsTxPowerDFull || mosParam.ownParam.IsTxPowerAFull || mosParam.ownParam.IsTxPowerEFull
                    || mosParam.ownParam.IsTxPowerFFull || mosParam.otherParam.IsTxPowerDFull || mosParam.otherParam.IsTxPowerAFull
                    || mosParam.otherParam.IsTxPowerEFull || mosParam.otherParam.IsTxPowerFFull;

                double? sinrAvg = mosParam.ownParam.SinrAvg;

                bool isValueSinr = sinrAvg > -3;

                double? rsrpAvg = mosParam.ownParam.RsrpAvg;

                double perQAM16 = mosParam.ownParam.QAM16UlPerAvg;

                double dlPerQam16 = mosParam.ownParam.QAM16DlPerAvg;

                double dlPerQam64 = mosParam.ownParam.QAM64DlPerAvg;

                double dlPerQpsk = mosParam.ownParam.QPSKDlPerAvg;

                bool isValueModulationMode = perQAM16 > 50;

                //总切换次数 = 切换次数 + 对端切换次数
                int allHOCount = mosParam.AllHORequestCount;

                if (sinrAvg != null)
                {
                    //截取实际MOS值到小数点后一位
                    // double mos01 = (int)mosParam.MOS * 10.0 / 10.0;
                    //转成string后再转double类型，实际计算的数值才是见到的数值
                    double mos01 = (int)(double.Parse(mosParam.MOS.ToString()) * 10.0d) / 10.0;
                    moskeymax = Math.Max(moskeymax, mos01);
                    //mos区间集合
                    if (!dicMos.ContainsKey(mos01))
                    {
                        List<float> mosLst = new List<float>();
                        mosLst.Add(mosParam.MOS);
                        dicMos.Add(mos01, mosLst);
                    }
                    else
                    {
                        dicMos[mos01].Add(mosParam.MOS);
                    }

                    //////////////切换次数与MOS///比例//////////////
                    evenkeymax = Math.Max(evenkeymax, allHOCount);
                    if (!dicEvenCountPer.ContainsKey(allHOCount))
                    {
                        List<float> mosLst = new List<float>();
                        mosLst.Add(mosParam.MOS);
                        dicEvenCountPer.Add(allHOCount, mosLst);
                    }
                    else
                    {
                        dicEvenCountPer[allHOCount].Add(mosParam.MOS);
                    }

                    //////////////切换次数与MOS//////////////
                    if (isValueModulationMode && isValueSinr && !IsMSPowerFull)
                    {
                        if (!dicEvenCountMos.ContainsKey(allHOCount))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicEvenCountMos.Add(allHOCount, mosLst);
                        }
                        else
                        {
                            dicEvenCountMos[allHOCount].Add(mosParam.MOS);
                        }
                    }

                    //////////////Sinr与MOS//////////////////////
                    string sinrRegion = "";
                    foreach (Range range in mosCondition.SinrRangeSet.Values)
                    {
                        if (range.Contains((double)sinrAvg))
                        {
                            sinrRegion = range.ToString();
                        }
                    }

                    if (isValueModulationMode && allHOCount == 0 && !IsMSPowerFull)
                    {
                        if (!dicSinrCountMos.ContainsKey(sinrRegion))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicSinrCountMos.Add(sinrRegion, mosLst);
                        }
                        else
                        {
                            dicSinrCountMos[sinrRegion].Add(mosParam.MOS);
                        }
                    }

                    ///////////////////Sinr与MOS//比例///////////////////////
                    if (!dicSinrCountPer.ContainsKey(sinrRegion))
                    {
                        List<float> mosLst = new List<float>();
                        mosLst.Add(mosParam.MOS);
                        dicSinrCountPer.Add(sinrRegion, mosLst);
                    }
                    else
                    {
                        dicSinrCountPer[sinrRegion].Add(mosParam.MOS);
                    }

                    //////////////Rsrp与MOS//////////////////////
                    string rsrpRegion = "";
                    foreach (Range range in mosCondition.RsrpRangeSet.Values)
                    {
                        if (range.Contains((double)rsrpAvg))
                        {
                            rsrpRegion = range.ToString();
                        }
                    }
                    if (isValueModulationMode && isValueSinr && allHOCount == 0 && !IsMSPowerFull)
                    {
                        if (!dicRsrpCountMos.ContainsKey(rsrpRegion))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicRsrpCountMos.Add(rsrpRegion, mosLst);
                        }
                        else
                        {
                            dicRsrpCountMos[rsrpRegion].Add(mosParam.MOS);
                        }
                    }

                    ///////////////////Rsrp与MOS//比例///////////////////////
                    if (!dicRsrpCountPer.ContainsKey(rsrpRegion))
                    {
                        List<float> mosLst = new List<float>();
                        mosLst.Add(mosParam.MOS);
                        dicRsrpCountPer.Add(rsrpRegion, mosLst);
                    }
                    else
                    {
                        dicRsrpCountPer[rsrpRegion].Add(mosParam.MOS);
                    }

                    //////////////调制方式与MOS/////////////////
                    string modulationMode = "";
                    foreach (Range range in mosCondition.QamPerRangeSet.Values)
                    {
                        if (range.Contains(perQAM16))
                        {
                            modulationMode = range.ToString();
                        }
                    }
                    if (allHOCount == 0 && isValueSinr && !IsMSPowerFull)
                    {
                        if (!dicModulationModeMos.ContainsKey(modulationMode))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicModulationModeMos.Add(modulationMode, mosLst);
                        }
                        else
                        {
                            dicModulationModeMos[modulationMode].Add(mosParam.MOS);
                        }
                    }

                    //////////////调制方式与MOS////比例/////////////////
                    if (!dicModulationModePer.ContainsKey(modulationMode))
                    {
                        List<float> mosList = new List<float>();
                        mosList.Add(mosParam.MOS);
                        dicModulationModePer.Add(modulationMode, mosList);
                    }
                    else
                    {
                        dicModulationModePer[modulationMode].Add(mosParam.MOS);
                    }

                    #region DlQam16

                    modulationMode = "";
                    foreach (Range range in mosCondition.DlQam16PerRangeSet.Values)
                    {
                        if (range.Contains(dlPerQam16))
                        {
                            modulationMode = range.ToString();
                        }
                    }
                    if (allHOCount == 0 && isValueSinr && !IsMSPowerFull)
                    {
                        if (!dicDlQam16Mos.ContainsKey(modulationMode))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicDlQam16Mos.Add(modulationMode, mosLst);
                        }
                        else
                        {
                            dicDlQam16Mos[modulationMode].Add(mosParam.MOS);
                        }
                    }

                    if (!dicDlQam16Per.ContainsKey(modulationMode))
                    {
                        List<float> mosList = new List<float>();
                        mosList.Add(mosParam.MOS);
                        dicDlQam16Per.Add(modulationMode, mosList);
                    }
                    else
                    {
                        dicDlQam16Per[modulationMode].Add(mosParam.MOS);
                    }

                    #endregion

                    #region DlQam64

                    modulationMode = "";
                    foreach (Range range in mosCondition.DlQam64PerRangeSet.Values)
                    {
                        if (range.Contains(dlPerQam64))
                        {
                            modulationMode = range.ToString();
                        }
                    }
                    if (allHOCount == 0 && isValueSinr && !IsMSPowerFull)
                    {
                        if (!dicDlQam64Mos.ContainsKey(modulationMode))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicDlQam64Mos.Add(modulationMode, mosLst);
                        }
                        else
                        {
                            dicDlQam64Mos[modulationMode].Add(mosParam.MOS);
                        }
                    }

                    if (!dicDlQam64Per.ContainsKey(modulationMode))
                    {
                        List<float> mosList = new List<float>();
                        mosList.Add(mosParam.MOS);
                        dicDlQam64Per.Add(modulationMode, mosList);
                    }
                    else
                    {
                        dicDlQam64Per[modulationMode].Add(mosParam.MOS);
                    }

                    #endregion

                    #region DlQpsk

                    modulationMode = "";
                    foreach (Range range in mosCondition.DlQpskPerRangeSet.Values)
                    {
                        if (range.Contains(dlPerQpsk))
                        {
                            modulationMode = range.ToString();
                        }
                    }
                    if (allHOCount == 0 && isValueSinr && !IsMSPowerFull)
                    {
                        if (!dicDlQpskMos.ContainsKey(modulationMode))
                        {
                            List<float> mosLst = new List<float>();
                            mosLst.Add(mosParam.MOS);
                            dicDlQpskMos.Add(modulationMode, mosLst);
                        }
                        else
                        {
                            dicDlQpskMos[modulationMode].Add(mosParam.MOS);
                        }
                    }

                    if (!dicDlQpskPer.ContainsKey(modulationMode))
                    {
                        List<float> mosList = new List<float>();
                        mosList.Add(mosParam.MOS);
                        dicDlQpskPer.Add(modulationMode, mosList);
                    }
                    else
                    {
                        dicDlQpskPer[modulationMode].Add(mosParam.MOS);
                    }

                    #endregion

                }
            }
        }
    }
}
