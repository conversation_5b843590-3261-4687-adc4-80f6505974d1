﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.CQTSiteStat
{
    public class CQTSiteStatTemplate
    {
        public string Name { get; set; }
        public List<Column> Columns { get; set; }
        public List<ColumnGroup> ColGroups { get; set; }
        public int KeyColNum { get; set; }

        public CQTSiteStatTemplate CreateDefault()
        {
            CQTSiteStatTemplate tpl = new CQTSiteStatTemplate();
            tpl.Name = "默认报表";
            tpl.Columns = new List<Column>();
            tpl.ColGroups = new List<ColumnGroup>();
            tpl.KeyColNum = 4;
            ColumnGroup grp = new ColumnGroup();
            grp.Name = "地市";
            tpl.ColGroups.Add(grp);
            //Column col = new Column(tpl, grp, grp.Name, KeyWords.DistrictName.ToString(), ECarrier.移动)

            grp = new ColumnGroup();
            grp.Name = "站点名称";
            tpl.ColGroups.Add(grp);
            //col = new Column(tpl, grp, grp.Name, KeyWords.SiteName.ToString(), ECarrier.移动)

            grp = new ColumnGroup();
            grp.Name = "小区名称";
            tpl.ColGroups.Add(grp);
            //col = new Column(tpl, grp, grp.Name, KeyWords.CellName.ToString(), ECarrier.移动)

            grp = new ColumnGroup();
            grp.Name = "CGI";
            tpl.ColGroups.Add(grp);
            //col = new Column(tpl, grp, grp.Name, KeyWords.CGI.ToString(), ECarrier.移动)

            grp = new ColumnGroup();
            grp.Name = "测试日期";
            tpl.ColGroups.Add(grp);
            //col = new Column(tpl, grp, grp.Name, KeyWords.TestDate.ToString(), ECarrier.移动)

            grp = new ColumnGroup();
            grp.Name = "综合覆盖率";
            tpl.ColGroups.Add(grp);
            //List<string> fileNames = new List<string>() { "ms2", "MS2" }*/
            //col = new Column(tpl, grp, "移动"
            //    , "{(100*(Lte_61210107/Lte_61210101 ))*((Lte_0874/Lte_0805))}%"
            //    , ECarrier.移动)
            //col.FileNames = fileNames

            //col = new Column(tpl, grp, "联通"
            //    , "{(Lf_612D0105/Lf_612D0101)*(Lf_0874/(Lf_0874+Lf_0827+Lf_0823+Lf_087C)) *100}%"
            //    ,ECarrier.联通)
            //col = new Column(tpl, grp, "电信"
            //    , "{(Lf_612D0105/Lf_612D0101)*(Lf_0874/(Lf_0874+Lf_0827+Lf_0823+Lf_087C)) *100}%"
            //    , ECarrier.电信)

            grp = new ColumnGroup();
            grp.Name = "竞对指标";
            tpl.ColGroups.Add(grp);
            ColumnGroup subGrp = new ColumnGroup();
            subGrp.Name = "TD-LTE占网时长占比";
            subGrp.IsCompeted = true;
            grp.AddChildren(subGrp);
            //col = new Column(tpl, subGrp, "移动", "{(Lte_0874/Lte_0805)*100 }%", ECarrier.移动)
            //col.FileNames = fileNames
            //col = new Column(tpl, subGrp, "联通", "{100*(Lf_0874/(Lf_0874+Lf_0827+Lf_0823+Lf_087C)) }%", ECarrier.联通)
            //col = new Column(tpl, subGrp, "电信", "{100*(Lf_0874/(Lf_0874+Lf_0827+Lf_0823+Lf_087C)) }%", ECarrier.电信)

            subGrp = new ColumnGroup();
            subGrp.Name = "RSRP >= -110dBm以上占比";
            subGrp.IsCompeted = true;
            grp.AddChildren(subGrp);
            //col = new Column(tpl, subGrp, "移动"
            //    , "{100*(Lte_61210304+Lte_61210305+Lte_61210306+Lte_61210307+Lte_61210308)/Lte_61210301 }%"
            //    , ECarrier.移动)
            //col.FileNames = fileNames
            //col = new Column(tpl, subGrp, "联通"
            //    , "{100*((Lf_612D037D+Lf_612D037E+Lf_612D037F+Lf_612D0380+Lf_612D0381+Lf_612D0382+Lf_612D0349+Lf_612D034A+Lf_612D034B+Lf_612D0307+Lf_612D0308)/Lf_612D0301)}%"
            //    , ECarrier.联通)
            //col = new Column(tpl, subGrp, "电信"
            //    , "{100*((Lf_612D037D+Lf_612D037E+Lf_612D037F+Lf_612D0380+Lf_612D0381+Lf_612D0382+Lf_612D0349+Lf_612D034A+Lf_612D034B+Lf_612D0307+Lf_612D0308)/Lf_612D0301)}%"
            //    , ECarrier.电信)

            subGrp = new ColumnGroup();
            subGrp.Name = "SINR>=0dB占比";
            subGrp.IsCompeted = true;
            grp.AddChildren(subGrp);
            //col = new Column(tpl, subGrp, "移动"
            //    , "{100*(Lte_61210405+Lte_61210406+Lte_61210407+Lte_61210408+Lte_61210409+Lte_6121040A+Lte_6121040B)/Lte_61210401}%"
            //    , ECarrier.移动)
            //col.FileNames = fileNames
            //col = new Column(tpl, subGrp, "联通"
            //    , "{100*((Lf_612D0405+Lf_612D0406+Lf_612D0407+Lf_612D0408+Lf_612D0409+Lf_612D040A+Lf_612D040B)/Lf_612D0401)}%"
            //    , ECarrier.联通)
            //col = new Column(tpl, subGrp, "电信"
            //    , "{100*((Lf_612D0405+Lf_612D0406+Lf_612D0407+Lf_612D0408+Lf_612D0409+Lf_612D040A+Lf_612D040B)/Lf_612D0401)}%"
            //    , ECarrier.电信)

            subGrp = new ColumnGroup();
            subGrp.Name = "下载2M以上占比";
            subGrp.IsCompeted = true;
            grp.AddChildren(subGrp);
            //col = new Column(tpl, subGrp, "移动"
            //    , "{100*((Lte_052164020108+Lte_052164020109+Lte_0521640201D0+Lte_05216402010C+Lte_05216402010D+Lte_05216402010E+Lte_0521640201EB+Lte_0521640201EC+Lte_0521640201CE+Lte_0521640201CF+Lte_05216402010208+Lte_05216402010209+Lte_052164020102D0+Lte_0521640201020C+Lte_0521640201020D+Lte_0521640201020E+Lte_052164020102EB+Lte_052164020102EC+Lte_052164020102CE+Lte_052164020102CF)/(Lte_052164020103+Lte_05216402010203))}%"
            //    , ECarrier.移动)
            //col.FileNames = fileNames
            //col = new Column(tpl, subGrp, "联通"
            //    , "{100*((Lf_052D64020108+Lf_052D64020109+Lf_052D640201D0+Lf_052D6402010C+Lf_052D6402010D+Lf_052D6402010E+Lf_052D640201EB+Lf_052D640201EC+Lf_052D640201CE+Lf_052D640201CF+Lf_052D6402010208+Lf_052D6402010209+Lf_052D64020102D0+Lf_052D640201020C+Lf_052D640201020D+Lf_052D640201020E+Lf_052D64020102EB+Lf_052D64020102EC+Lf_052D64020102CE+Lf_052D64020102CF)/(Lf_052D64020103+Lf_052D6402010203))}%"
            //    , ECarrier.联通)
            //col = new Column(tpl, subGrp, "电信"
            //    , "{100*((Lf_052D64020108+Lf_052D64020109+Lf_052D640201D0+Lf_052D6402010C+Lf_052D6402010D+Lf_052D6402010E+Lf_052D640201EB+Lf_052D640201EC+Lf_052D640201CE+Lf_052D640201CF+Lf_052D6402010208+Lf_052D6402010209+Lf_052D64020102D0+Lf_052D640201020C+Lf_052D640201020D+Lf_052D640201020E+Lf_052D64020102EB+Lf_052D64020102EC+Lf_052D64020102CE+Lf_052D64020102CF)/(Lf_052D64020103+Lf_052D6402010203))}%"
            //    , ECarrier.电信)

            subGrp = new ColumnGroup();
            subGrp.Name = "上传速率512k以上采样点占比";
            subGrp.IsCompeted = true;
            grp.AddChildren(subGrp);
            //col = new Column(tpl, subGrp, "移动"
            //    , "{100*((Lte_0521640302D3+Lte_052164030207+Lte_052164030208+Lte_052164030209+Lte_0521640302D0+Lte_05216403020C+Lte_05216403020D+Lte_05216403020E+Lte_0521640302EB+Lte_0521640302EC+Lte_0521640302CE+Lte_0521640302CF+Lte_052164030202D3+Lte_05216403020207+Lte_05216403020208+Lte_05216403020209+Lte_052164030202D0+Lte_0521640302020C+Lte_0521640302020D+Lte_0521640302020E+Lte_052164030202EB+Lte_052164030202EC+Lte_052164030202CE+Lte_052164030202CF)/(Lte_052164030203+Lte_05216403020203)) }%"
            //    , ECarrier.移动)
            //fileNames = new List<string>() { "ms3", "MS3" }*/
            //col.FileNames = fileNames

            //col = new Column(tpl, subGrp, "联通"
            //    , "{100*((Lf_052D640302D3+Lf_052D64030207+Lf_052D64030208+Lf_052D64030209+Lf_052D640302D0+Lf_052D6403020C+Lf_052D6403020D+Lf_052D6403020E+Lf_052D640302EB+Lf_052D640302EC+Lf_052D640302CE+Lf_052D640302CF+Lf_052D64030202D3+Lf_052D6403020207+Lf_052D6403020208+Lf_052D6403020209+Lf_052D64030202D0+Lf_052D640302020C+Lf_052D640302020D+Lf_052D640302020E+Lf_052D64030202EB+Lf_052D64030202EC+Lf_052D64030202CE+Lf_052D64030202CF)/(Lf_052D64030203+Lf_052D6403020203))}%"
            //    , ECarrier.联通)
            //col = new Column(tpl,subGrp, "电信"
            //    , "{100*((Lf_052D640302D3+Lf_052D64030207+Lf_052D64030208+Lf_052D64030209+Lf_052D640302D0+Lf_052D6403020C+Lf_052D6403020D+Lf_052D6403020E+Lf_052D640302EB+Lf_052D640302EC+Lf_052D640302CE+Lf_052D640302CF+Lf_052D64030202D3+Lf_052D6403020207+Lf_052D6403020208+Lf_052D6403020209+Lf_052D64030202D0+Lf_052D640302020C+Lf_052D640302020D+Lf_052D640302020E+Lf_052D64030202EB+Lf_052D64030202EC+Lf_052D64030202CE+Lf_052D64030202CF)/(Lf_052D64030203+Lf_052D6403020203))}%"
            //    , ECarrier.电信)
            return tpl;
        }

    }

    public enum KeyWords
    {
        DistrictName,
        SiteName,
        TestDate,
        CellName,
        CGI
    }

    public class Column
    {
        public Column() { }

        public Column(CQTSiteStatTemplate tpl, ColumnGroup grp, string name, string formula, ECarrier carrier)
        {
            this.Name = name;
            this.Formula = formula;
            this.CarrierType = carrier;
            grp.AddColumn(this);
            tpl.Columns.Add(this);
        }

        public override string ToString()
        {
            return Group.Name + "." + Name;
        }
        public ColumnGroup Group { get; set; }
        public string Name { get; set; }
        public string Formula { get; set; }
        public ECarrier CarrierType { get; set; }
        public List<string> FileNames { get; set; }
    }

    public class ColumnGroup 
    {
        public override string ToString()
        {
            return this.Name;
        }
        public string Name { get; set; }
        public List<ColumnGroup> Children { get; set; }
        public List<Column> Columns { get; set; }
        public bool IsCompeted { get; set; }
        public void AddColumn(Column col)
        {
            if (Columns==null)
            {
                Columns = new List<Column>();
            }
            Columns.Add(col);
            col.Group = this;
        }

        public void AddChildren(ColumnGroup sub)
        {
            if (Children == null)
            {
                Children = new List<ColumnGroup>();
            }
            Children.Add(sub);
        }
    }

}
