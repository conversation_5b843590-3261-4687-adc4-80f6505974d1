﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanCellMultiCoverage : ZTDiyCellMultiCoverageQueryByRegion
    {
        private ScanMultiCoverageCondition settingCondition = new ScanMultiCoverageCondition();
        protected Dictionary<string, NRCellMultiCovInfo> cellCovDic = new Dictionary<string, NRCellMultiCovInfo>();

        public List<NRCellMultiCovInfo> ResList { get; set; }
        public NRCellMultiCovInfo SelectedMultiCovCell { get; set; }
        public ShowCoverage ShowCoverageType { get; set; } = ShowCoverage.Absolute;

        private static NRScanCellMultiCoverage intance = null;
        public new static NRScanCellMultiCoverage GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRScanCellMultiCoverage();
                    }
                }
            }
            return intance;
        }

        protected NRScanCellMultiCoverage()
            : base()
        {
            settingCondition.CoverBandDiff = 6;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);

            Columns = NRTpHelper.InitNrScanParamBackground();
        }

        public void ClearData()
        {
            MainModel.DTDataManager.Clear();
            MainModel.DTDataManager.ClearHistory();
            SelectedMultiCovCell = null;
            ResList.Clear();
            cellCovDic.Clear();
        }

        public override string Name
        {
            get { return "小区重叠覆盖度分析_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36005, this.Name);//////
        }

        protected override bool getCondition()
        {
            NRScanCellMultiCoverageDlg settingDlg = new NRScanCellMultiCoverageDlg();
            settingDlg.SetCondition(settingCondition);
            if (settingDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            settingCondition = settingDlg.GetCondition();
            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            if (cellCovDic == null)
            {
                cellCovDic = new Dictionary<string, NRCellMultiCovInfo>();
            }
            else
            {
                cellCovDic.Clear();
            }
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    for (int i = 0; i < fileDataManager.TestPoints.Count; i++)
                    {
                        TestPoint testPoint = fileDataManager.TestPoints[i];
                        if (isValidTestPoint(testPoint))
                        {
                            doWithDTData(testPoint);
                        }
                    }
                    if (!settingCondition.SaveTestPoint)
                    {
                        fileDataManager.ClearTestPoints();
                        fileDataManager.ClearDTDatas();
                    }
                }
                if (!settingCondition.SaveTestPoint)
                {
                    MainModel.DTDataManager.Clear();
                    MainModel.DTDataManager.ClearHistory();
                }
            }
            catch
            {
                //continue
            }
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys == null
                || Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                float? top1Rscp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, 0);
                if (top1Rscp != null && top1Rscp >= settingCondition.ValidValue)
                {
                    return true;
                }
            }
            return false;
        }

        private void doWithDTData(TestPoint testPoint)
        {
            int? top1Earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(testPoint, 0);
            int? top1pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(testPoint, 0);
            float? top1Rscp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, 0);

            NRCell servCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(testPoint.DateTime, top1Earfcn, top1pci, testPoint.Longitude, testPoint.Latitude);
            if (servCell != null)
            {
                dealTestPoint(testPoint, top1Rscp, servCell);
            }
        }

        private void dealTestPoint(TestPoint testPoint, float? top1Rscp, NRCell servCell)
        {
            SampleCount sampleCount = new SampleCount();
            Dictionary<string, NRCellInterference> otherCellDic = new Dictionary<string, NRCellInterference>();
            if (cellCovDic.ContainsKey(servCell.Name))
            {
                otherCellDic = cellCovDic[servCell.Name].OtherCellDic;
            }

            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
            foreach (var index in groupDic.Values)
            {
                addSampleCount(testPoint, top1Rscp, servCell, sampleCount, otherCellDic, index);
            }

            saveCellInCovRegionInfo(testPoint, servCell, sampleCount, otherCellDic);
        }

        private void addSampleCount(TestPoint testPoint, float? top1Rscp, NRCell servCell
            , SampleCount sampleCount, Dictionary<string, NRCellInterference> otherCellDic, int index)
        {
            float maxRsrp = -10;
            float minRsrp = -140;
            int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(testPoint, index);
            int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(testPoint, index);
            float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, index);

            if ((rsrp != null) && (rsrp < maxRsrp) && (rsrp > minRsrp) && (earfcn != null) && (pci != null))
            {
                NRCell otherCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(testPoint.DateTime, earfcn, pci, testPoint.Longitude, testPoint.Latitude);
                if (rsrp > settingCondition.AbsoluteValue)
                {
                    sampleCount.AbsSampleCount++;  //绝对
                }
                if ((top1Rscp - rsrp) < settingCondition.CoverBandDiff)
                {
                    sampleCount.RelSampleCount++;  //相对

                    addOtherCell(testPoint, servCell, otherCellDic, rsrp, earfcn, pci, otherCell);
                }
                if ((rsrp > settingCondition.AbsoluteValue) && ((top1Rscp - rsrp) < settingCondition.CoverBandDiff))
                {
                    sampleCount.MulSampleCount++;  //综合
                }
            }
        }

        private void addOtherCell(TestPoint testPoint, NRCell servCell
            , Dictionary<string, NRCellInterference> otherCellDic, float? rscp
            , int? uarfcn, int? cpi, NRCell otherCell)
        {
            //相对覆盖度需要保存进入覆盖带的小区
            string otherCellName;
            if (otherCell != null)
            {
                otherCellName = otherCell.Name;
            }
            else
            {
                otherCellName = uarfcn.ToString() + "_" + cpi.ToString();
            }
            if (servCell.Name != otherCellName)     //第一强小区不需要保存
            {
                saveOtherCell(otherCellDic, testPoint, otherCell, otherCellName, (float)rscp);
                //覆盖带内小区保存完毕
            }
        }

        private void saveOtherCell(Dictionary<string, NRCellInterference> otherCellDic, TestPoint testPoint,
            NRCell otherCell, string otherCellName, float rscp)
        {
            if (!otherCellDic.TryGetValue(otherCellName, out var otherCellCovInfo))
            {
                otherCellCovInfo = new NRCellInterference
                {
                    Cell = otherCell,
                    SN = otherCellDic.Count + 1
                };
                otherCellDic.Add(otherCellName, otherCellCovInfo);
            }

            otherCellCovInfo.CellName = otherCellName;
            otherCellCovInfo.Rsrp.Add(rscp);
            //otherCellCovInfo.SampleCount++;
            //otherCellCovInfo.RsrpTotal += rscp;
            if (settingCondition.SaveTestPoint)
            {
                otherCellCovInfo.TestPoints.Add(testPoint);
            }
        }

        private void saveCellInCovRegionInfo(TestPoint testPoint, NRCell servCell
            , SampleCount sampleCount, Dictionary<string, NRCellInterference> otherCellDic)
        {
            if (!cellCovDic.TryGetValue(servCell.Name, out var cellCovInfo))
            {
                cellCovInfo = new NRCellMultiCovInfo
                {
                    Cell = servCell,
                    SN = cellCovDic.Count + 1
                };
                cellCovDic.Add(servCell.Name, cellCovInfo);
            }

            //foreach (var item in otherCellDic.Values)
            //{
            //    item.Calculate();
            //}
            cellCovInfo.Location.Add(new DbPoint(testPoint.Longitude, testPoint.Latitude));

            cellCovInfo.AbsSampleCount += sampleCount.AbsSampleCount;
            cellCovInfo.RelSampleCount += sampleCount.RelSampleCount;
            cellCovInfo.MulSampleCount += sampleCount.MulSampleCount;
            cellCovInfo.Top1Count++;
            cellCovInfo.OtherCellDic = otherCellDic;
            cellCovInfo.RelFactor4Rate.TotalCount++;

            float? top1Rscp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, 0);
            cellCovInfo.Rsrp.Add(top1Rscp);
            //double distance = 0;
            //if (cellCovInfo.LastPoint != null)
            //{
            //    distance = MathFuncs.GetDistance(cellCovInfo.LastPoint.x, cellCovInfo.LastPoint.y, testPoint.Longitude, testPoint.Latitude);
            //}
            //cellCovInfo.Distance += distance;

            if (sampleCount.RelSampleCount >= 4)
            {
                cellCovInfo.RelFactor4Rate.Count++;
            }
            if (settingCondition.SaveTestPoint)
            {
                cellCovInfo.TestPoints.Add(testPoint);
            }
            else if (cellCovInfo.TestPoints.Count == 0)
            {
                cellCovInfo.TestPoints.Add(testPoint);
            }
        }

        protected override void getResultsAfterQuery()
        {
            ResList = new List<NRCellMultiCovInfo>();

            foreach (var curCellInfo in cellCovDic.Values)
            {
                curCellInfo.Calculate();
                ResList.Add(curCellInfo);
            }
        }

        protected override void fireShowForm()
        {
            var fmr = MainModel.CreateResultForm(typeof(NRScanCellMultiCoverageForm))
            as NRScanCellMultiCoverageForm;
            fmr.FillData(settingCondition, ResList);
            fmr.Visible = true;
            fmr.BringToFront();
        }

        public class SampleCount
        {
            public int AbsSampleCount { get; set; }
            public int RelSampleCount { get; set; }
            public int MulSampleCount { get; set; }
        }
    }

    public class NRCellMultiCovInfo
    {
        public NRCell Cell { get; set; }

        public int SN { get; set; }

        public float Top1Count { get; set; }
        public float AbsSampleCount { get; set; }
        public float RelSampleCount { get; set; }
        public float MulSampleCount { get; set; }

        public float AbsRate { get; set; }
        public float RelRate { get; set; }
        public float MulRate { get; set; }

        //保存相对重叠覆盖度下进入覆盖带的小区信息
        public Dictionary<string, NRCellInterference> OtherCellDic { get; set; } = new Dictionary<string, NRCellInterference>();
        public List<NRCellInterference> OtherCellList { get; set; }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public List<DbPoint> Location { get; set; } = new List<DbPoint>();

        //public DbPoint LastPoint { get; set; }
        public double Distance { get; set; }

        public AvgInfo Rsrp { get; set; } = new AvgInfo();
        /// <summary>
        /// 相对覆盖度大于等于4的采样个数
        /// </summary>
        public RateInfo RelFactor4Rate { get; set; } = new RateInfo();

        public void Calculate()
        {
            if (Top1Count > 0)
            {
                AbsRate = (float)Math.Round((double)(AbsSampleCount / Top1Count), 2);
                RelRate = (float)Math.Round((double)(RelSampleCount / Top1Count), 2);
                MulRate = (float)Math.Round((double)(MulSampleCount / Top1Count), 2);
            }

            DbPoint lastPoint = null;
            double distance = 0;
            foreach (var point in Location)
            {
                if (lastPoint != null)
                {
                    distance = MathFuncs.GetDistance(lastPoint.x, lastPoint.y, point.x, point.y);
                }
                lastPoint = point;
            }
            Distance = distance;

            foreach (var otherCell in OtherCellDic.Values)
            {
                otherCell.Calculate();
                if (otherCell.Cell != null)
                {
                    otherCell.Distance2ServCell = Math.Round(Cell.GetDistance(otherCell.Cell.Longitude, otherCell.Cell.Latitude), 2);
                }
            }
            OtherCellList = new List<NRCellInterference>(OtherCellDic.Values);

            RelFactor4Rate.Calculate(true);
            Rsrp.Calculate();
        }
    }

    public class NRCellInterference
    {
        public NRCell Cell { get; set; }

        public int SN { get; set; }

        public string CellName { get; set; } = "";

        public AvgInfo Rsrp { get; set; } = new AvgInfo();

        public double Distance2ServCell { get; set; }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();

        public void Calculate()
        {
            Rsrp.Calculate();
        }
    }
}
