<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">请输入模板名</Item>
      <Item typeName="IDictionary" key="HubContext">
        <Item typeName="IDictionary" key="移动">
          <Item typeName="String" key="Name">移动</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">12</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Lte_61210403}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">0</Item>
            <Item typeName="Boolean" key="MinIncluded">False</Item>
            <Item typeName="Double" key="Max">10</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="联通">
          <Item typeName="String" key="Name">联通</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">26</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{(value10[57])*(1000*8)/((value4[57])*(1024*1024))}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">2</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">3</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="电信">
          <Item typeName="String" key="Name">电信</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">29</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{100*((evtIdCount[506]+value9[506])-(evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]))/(evtIdCount[506]+value9[506])}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-4</Item>
            <Item typeName="Boolean" key="MinIncluded">False</Item>
            <Item typeName="Double" key="Max">5</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IList" key="AlghirithmVec">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">ffff</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16192</Item>
          <Item typeName="String" key="ColorRGB">255,192,192</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">0</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">1</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16711936</Item>
          <Item typeName="String" key="ColorRGB">0,255,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">0</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="OtherAlghirithm">
        <Item typeName="String" key="Name">其他</Item>
        <Item typeName="Boolean" key="IsSpecial">False</Item>
        <Item typeName="String" key="ENaNCM">有数据</Item>
        <Item typeName="String" key="ENaNCU">有数据</Item>
        <Item typeName="String" key="ENaNCT">有数据</Item>
        <Item typeName="Int32" key="Color">-16777216</Item>
        <Item typeName="String" key="ColorRGB">0,0,0</Item>
        <Item typeName="IList" key="ValueRangeVec" />
      </Item>
    </Item>
  </Config>
</Configs>