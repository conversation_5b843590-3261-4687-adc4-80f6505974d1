﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public abstract class FreaBandBase
    {
        protected List<FreqBandRange> freqBandRangeList;
        public virtual void InitFreqBand()
        {
            freqBandRangeList = new List<FreqBandRange>();
        }

        public virtual string GetFreqBandByEarfcn(int earfcn)
        {
            if (earfcn == 0)
            {
                return "";
            }

            foreach (var freqBandRange in freqBandRangeList)
            {
                if (freqBandRange.Judge(earfcn))
                {
                    return freqBandRange.BandName;
                }
            }

            return "";
        }

        protected class FreqBandRange
        {
            public string BandName { get; set; }
            public List<Range> RangeList { get; set; } = new List<Range>();

            public bool Judge(int earfcn)
            {
                foreach (var range in RangeList)
                {
                    if (range.Min <= earfcn && earfcn <= range.Max)
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        protected class Range
        {
            public int Max { get; set; }
            public int Min { get; set; }
        }
    }

    public class LteFreqBand_BJ : FreaBandBase
    {
        public LteFreqBand_BJ()
        {
            InitFreqBand();
        }

        public override void InitFreqBand()
        {
            freqBandRangeList = new List<FreqBandRange>
            {
                new FreqBandRange{
                    BandName = "A",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 36200, Max = 36349 }
                    }
                },
                new FreqBandRange{
                    BandName = "D",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 37750, Max = 38249 },
                        new Range() {  Min = 39650, Max = 41589 }
                    }
                },
                new FreqBandRange{
                    BandName = "E",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 38650, Max = 39649 }
                    }
                },
                new FreqBandRange{
                    BandName = "F",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 38250, Max = 38649 }
                    }
                },
                new FreqBandRange{
                    BandName = "FDD900",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 21450, Max = 21799 },
                        new Range() {  Min = 3450, Max = 3799 }
                    }
                },
                new FreqBandRange{
                    BandName = "FDD1800",
                    RangeList = new List<Range>()
                    {
                        new Range() {  Min = 19200, Max = 19949 },
                        new Range() {  Min = 1200, Max = 1949 }
                    }
                },
            };
        }
    }
}
