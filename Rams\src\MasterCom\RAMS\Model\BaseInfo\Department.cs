using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model
{
    public class Department : IUserInfoObject
    {
        public Department()
        {

        }

        public int CompanyID { get; set; }

        public int ID { get; set; }

        public string LoginName { get; set; }

        public string Description { get; set; }

        public List<Office> OfficeList
        {
            get { return officeList; }
        }

        public override string ToString()
        {
            return LoginName;
        }

        public void AddOffice(Office office)
        {
            if (!officeList.Contains(office))
            {
                officeList.Add(office);
            }
        }

        public static Department Fill(Content content)
        {
            Department department = new Department();
            department.CompanyID = content.GetParamInt();
            department.ID = content.GetParamInt();
            department.LoginName = content.GetParamString();
            department.Description = content.GetParamString();
            return department;
        }

        public static IComparer<Department> GetCompareByDepartmentID()
        {
            if (compareByDepartmentID == null)
            {
                compareByDepartmentID = new CompareByDepartmentID();
            }
            return compareByDepartmentID;
        }

        private static IComparer<Department> compareByDepartmentID;

        private class CompareByDepartmentID : IComparer<Department>
        {
            public int Compare(Department x, Department y)
            {
                return x.ID.CompareTo(y.ID);
            }
        }
        
        private readonly List<Office> officeList = new List<Office>();
    }
}
