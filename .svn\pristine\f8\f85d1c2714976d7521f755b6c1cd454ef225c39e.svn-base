﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ModRoadStaterBase
    {
        protected ModRoadStaterBase(MainModel mm, ModRoadConditionBase cond)
        {
            this.mainModel = mm;
            this.cond = cond;
            this.roadList = new List<ModRoadItemBase>();
        }

        public abstract object GetStatResult(object param);

        public virtual void AddRoad(ModRoadItemBase road)
        {
            if (road.Length >= cond.RoadLength)
            {
                this.roadList.Add(road);
            }
        }

        public virtual void Clear()
        {
            foreach (ModRoadItemBase road in roadList)
            {
                road.Clear();
            }
        }

        protected List<ModRoadItemBase> roadList;
        protected MainModel mainModel;
        protected ModRoadConditionBase cond;
    }
}
