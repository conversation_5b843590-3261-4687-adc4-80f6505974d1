﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat.Data;
using MasterCom.Util;
using System.Drawing;
using System.Drawing.Imaging;
using DevExpress.XtraTreeList;
using MasterCom.RAMS.Net;
using System.Diagnostics;

namespace MasterCom.RAMS.CQT
{
    /// <summary>
    /// 按统计时间段-运营商-存放统计数据
    /// </summary>
    public class CQTKPIFineDataManager
    {
        public event EventHandler GisShowColumnChanged;
        public class ColumnShowEventArgs:EventArgs
        {
            public object column { get; set; }
        }
        public void FireGisShowColumnChange(object sender, ColumnShowEventArgs args)
        {
            if (GisShowColumnChanged != null)
            {
                GisShowColumnChanged(sender, args);
            }
        }

        private static CQTKPIFineDataManager instance { get; set; }
        public static CQTKPIFineDataManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CQTKPIFineDataManager();
            }
            return instance;
        }
        /// <summary>
        /// 全部点
        /// </summary>
        public Dictionary<string, List<CQTKPIFinePoint>> AllPoints { get; set; } = new Dictionary<string,List<CQTKPIFinePoint>>();

        private CQTKPIFineDataManager()
        {
            this.GisShowColumnChanged += new EventHandler(CQTKPIFineDataManager_GisShowColumnChanged);
        }

        public void Clear()
        {
            instance = new CQTKPIFineDataManager();
        }
        void CQTKPIFineDataManager_GisShowColumnChanged(object sender, EventArgs e)
        {
            ColumnShowEventArgs eA = (ColumnShowEventArgs)e;
            if (eA == null || eA.column == null)
            {
                return;
            }
            curColorColumn = eA.column;
            refreshColorImageByShowedColumn(eA.column);
        }

        private Dictionary<Color, Image> curColorImageDic = new Dictionary<Color, Image>();
        private object curColorColumn;
        public object CurColorColumn
        {
            get { return curColorColumn; }
        }

        private void refreshColorImageByShowedColumn(object colorColumn)
        {
            curColorImageDic = new Dictionary<Color, Image>();
            Image img = Properties.Resources.cqtDefault;
            Dictionary<Color, bool> clrDic = new Dictionary<Color, bool>();
            if (colorColumn is CQTKPIReportColumn)
            {
                foreach (CQTKPIScoreColorRange clrRng in (colorColumn as CQTKPIReportColumn).ScoreScheme.ScoreColorRanges)
                {
                    clrDic[clrRng.Color] = true;
                }
            }
            else if (colorColumn is CQTKPISummaryColumn)
            {
                foreach (DTParameterRangeColor clrRng in (colorColumn as CQTKPISummaryColumn).ScoreRangeColors)
                {
                    clrDic[clrRng.Value] = true;
                }
            }
            foreach (Color clr in clrDic.Keys)
            {
                Image imgClone = (Image)img.Clone();
                ImageAttributes imageAttributes = new ImageAttributes();
                int width = img.Width;
                int height = img.Height;
                ColorMap colorMap = new ColorMap();
                colorMap.OldColor = Color.FromArgb(255, 104, 51);
                colorMap.NewColor = clr;
                ColorMap[] remapTable = { colorMap };
                imageAttributes.SetRemapTable(remapTable, ColorAdjustType.Bitmap);
                Graphics g = Graphics.FromImage(imgClone);
                g.DrawImage(
                   imgClone,
                   new Rectangle(0, 0, width, height),  // destination rectangle 
                   0, 0,        // upper-left corner of source rectangle 
                   width,       // width of source rectangle
                   height,      // height of source rectangle
                   GraphicsUnit.Pixel,
                   imageAttributes);
                curColorImageDic[clr] = imgClone;
            }
        }

        /// <summary>
        ///  按当前着色指标列，获取CQT地点的GIS显示图片
        /// </summary>
        /// <param name="point">CQT地点</param>
        /// <param name="img">当前着色图片</param>
        /// <returns>true:统计的地点包括point；false:统计的地点不包括point</returns>
        public bool GetCQTPointImageByCurShowColorColumn(CQTKPIFinePoint point, out Image img, out Color color)
        {
            bool contains = CurStatPoints.Contains(point);
            img = null;
            color = Color.Empty;
            if (contains)
            {
                CQTMainPointKPIFine pntKPI = null;
                if (curPointNameDataDic != null)
                {
                    pntKPI = getPntKPI(point, ref img, ref color);
                    if (pntKPI == null)//无该点的统计信息
                    {
                        img = Properties.Resources.cqtDefault;
                    }
                }
            }
            return contains;
        }

        private CQTMainPointKPIFine getPntKPI(CQTKPIFinePoint point, ref Image img, ref Color color)
        {
            CQTMainPointKPIFine pntKPI;
            if (curPointNameDataDic.TryGetValue(point.Name, out pntKPI))
            {
                if (curColorColumn is CQTKPIReportColumn)
                {
                    color = pntKPI.ColumnKPIFineResultDic[curColorColumn as CQTKPIReportColumn].Color;
                    if (!color.IsEmpty)
                    {
                        img = curColorImageDic[color];
                    }
                }
                else if (curColorColumn is CQTKPISummaryColumn)
                {
                    color = pntKPI.SummaryResultDic[curColorColumn as CQTKPISummaryColumn].Color;
                    if (!color.IsEmpty)
                    {
                        img = curColorImageDic[color];
                    }
                }
            }

            return pntKPI;
        }

        /// <summary>
        /// 当前统计的地点
        /// </summary>
        public List<CQTKPIFinePoint> CurStatPoints { get; set; } = new List<CQTKPIFinePoint>();

        /// <summary>
        /// 当前统计的时间段，CQT地点数据信息
        /// </summary>
        Dictionary<string, CQTMainPointKPIFine> curPointNameDataDic = new Dictionary<string, CQTMainPointKPIFine>();

        /// <summary>
        /// 时间段-CQT地点统计信息 字典
        /// </summary>
        readonly Dictionary<TimePeriod, Dictionary<string, CQTMainPointKPIFine>> periodPntDataDic = new Dictionary<TimePeriod, Dictionary<string, CQTMainPointKPIFine>>();
        public int DataCount
        {
            get { return periodPntDataDic.Count; }
        }

        public void AddKPIStatData(DTDataHeader dataHeader, PartialData partialStatData, TimePeriod period)
        {
            string fileName = "";
            if (dataHeader == null)
            {
                return;
            }
            fileName = dataHeader.Name;
            string subPointName = "";
            string floorName = string.Empty;
            CQTKPIFinePoint pnt = GetCQTPointByFileName(fileName, out subPointName, out floorName);//根据文件名获取CQT地点
            if (pnt == null)
            {
                return;
            }

            Dictionary<string, CQTMainPointKPIFine> pointDataDic = null;//某时间段的 地点KPI数据
            if (!periodPntDataDic.TryGetValue(period, out pointDataDic))
            {
                pointDataDic = new Dictionary<string, CQTMainPointKPIFine>();
                periodPntDataDic.Add(period, pointDataDic);
            }
            CQTMainPointKPIFine mainPntData = null;//地点KPI数据
            if (!pointDataDic.TryGetValue(pnt.Name, out mainPntData))
            {//对应时间段未有当前地点信息，新建
                mainPntData = new CQTMainPointKPIFine(pnt);
                pointDataDic.Add(pnt.Name, mainPntData);
            }
            //按文件整理
            addFile(dataHeader);
            int carreerID = dataHeader.CarrierType;
            Dictionary<string ,List<CQTFileKPIFineData>> fileDataDic;//文件集
            if (!CarreerFileDataDic.TryGetValue(carreerID, out fileDataDic))
            {
                fileDataDic = new Dictionary<string ,List<CQTFileKPIFineData>>();
                CarreerFileDataDic[carreerID] = fileDataDic;
            }
            if (!fileDataDic.ContainsKey(pnt.Name))
            {
                fileDataDic[pnt.Name] = new List<CQTFileKPIFineData>();
            }
            bool existFileData = false;
            foreach (CQTFileKPIFineData item in fileDataDic[pnt.Name])
            {
                if (item.DataHeader.ID == dataHeader.ID)
                {
                    item.AddKPIStatData(partialStatData);
                    existFileData = true;
                    break;
                }
            }
            if (!existFileData)
            {
                CQTFileKPIFineData fileData = new CQTFileKPIFineData(dataHeader);
                fileData.AddKPIStatData(partialStatData);
                fileDataDic[pnt.Name].Add(fileData);
            }
            mainPntData.AddKPIStatData(dataHeader, subPointName, floorName, partialStatData);
        }

        protected void addFile(DTDataHeader dataHeader)
        {
            if (carreerFiles.ContainsKey(dataHeader.CarrierType))
            {
                List<FileInfo> files = carreerFiles[dataHeader.CarrierType];
                for (int i = 0; i < files.Count; i++)
                {
                    if (dataHeader.ID == files[i].ID)
                    {
                        return;
                    }
                }
                files.Add(dataHeader);
            }
            else
            {
                List<FileInfo> files = new List<FileInfo>();
                files.Add(dataHeader);
                carreerFiles.Add(dataHeader.CarrierType, files);
            }
        }
        protected Dictionary<int, List<FileInfo>> carreerFiles = new Dictionary<int, List<FileInfo>>();
        public Dictionary<int, Dictionary<string, List<CQTFileKPIFineData>>> CarreerFileDataDic { get; set; } = new Dictionary<int, Dictionary<string, List<CQTFileKPIFineData>>>();

        private CQTKPIFinePoint GetCQTPointByFileName(string fileName, out string subName, out string floorName)
        {
            string pointName;
            GetPointName(fileName, out pointName, out subName,out floorName);
            if (pointName != null && pointName.Length > 0)
            {
                foreach (CQTKPIFinePoint pnt in CurStatPoints)
                {
                    if (pnt.Name == pointName)
                    {
                        return pnt;
                    }
                }
            }
            return null;
        }


        public static void GetPointName(string fileName, out string mainPointName, out string subPointName, out string floorName)
        {//RCU_20120524_枫叶新新家园_@2号楼1单元1层_语音.rcu(02)//rcu_20121130_药监局家属楼_@1号楼1单元_1F_GSMDT1130110438.rcu
            mainPointName = "";
            subPointName = "";
            floorName = "";
            string str = fileName.Replace("@","");
            string[] atArr = str.Split('_');
            if (atArr.Length >= 5)
            {
                mainPointName = atArr[2];
                subPointName = atArr[3];
                floorName = atArr[1];//此处更改为统计日期
            }
        }

        private int CurCarreerID = 0;
        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, TimePeriod period
            , CQTKPIReport report, Dictionary<string, CQTKPIFinePoint> cqtPointStatDic)
        {
            CurCarreerID = report.CarreerID;
            makeReportColumn(treeList, report);
            CQTPointKPIFineData.qCondition.Periods.Clear();
            treeList.Nodes.Clear();
            List<CQTFileKPIFineData> fileDataList = new List<CQTFileKPIFineData>();
            DIYCQTCellStatByFile diyCQTCellStatByFile = new DIYCQTCellStatByFile(CQTPointKPIFineData.mainModel);
            foreach (string pntName in CarreerFileDataDic[report.CarreerID].Keys)
            {
                fileDataList.AddRange(CarreerFileDataDic[report.CarreerID][pntName]);
            }
            List<CQTFileKPIFineData> fileList = new List<CQTFileKPIFineData>();
            int iFileCount = fileDataList.Count;
            for (int i = 0; i < iFileCount; i++)
            {
                fileList.Add(fileDataList[i]);
                if (fileList.Count % 400 == 0 || i == iFileCount - 1)
                {
                    diyCQTCellStatByFile.FillSubAreaFileByList(fileList, cqtPointStatDic);
                    diyCQTCellStatByFile.SetQueryCondition(CQTPointKPIFineData.qCondition);
                    diyCQTCellStatByFile.Query();
                    fileList.Clear();
                    CQTPointKPIFineData.qCondition.Periods.Clear();
                }
            }
            fileDataList.Clear();
            List<object> objects = new List<object>();
            objects.Add(treeList);
            objects.Add(period);
            objects.Add(report);
            DIYQueryCQTKPIFine.DiyCQTCellStatByFile = diyCQTCellStatByFile;
            MasterCom.Util.UiEx.WaitTextBox.Show("正在计算指标...", StatReport, objects);
        }

        private void StatReport(object paramSet)
        {
            List<object> infos = paramSet as List<object>;
            DevExpress.XtraTreeList.TreeList treeList = infos[0] as DevExpress.XtraTreeList.TreeList;
            TimePeriod period = infos[1] as TimePeriod;
            CQTKPIReport report = infos[2] as CQTKPIReport;
            Debug.Assert(infos != null, "传入参数不能为null");
            infos.Clear();
            try
            {
                if (periodPntDataDic.TryGetValue(period, out curPointNameDataDic))
                {
                    foreach (CQTMainPointKPIFine mainPntData in curPointNameDataDic.Values)
                    {
                        mainPntData.MakeReportData(treeList, report);
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }

        }

        public void MakeReport(TimePeriod period, CQTKPIReport report)
        {
            CurCarreerID = report.CarreerID;
            if (periodPntDataDic.TryGetValue(period, out curPointNameDataDic))
            {
                foreach (CQTMainPointKPIFine mainPntData in curPointNameDataDic.Values)
                {
                    mainPntData.Stat(report);
                }
            }
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="point"></param>
        /// <param name="mainPointResult"></param>
        /// <param name="filesResult"></param>
        /// <returns></returns>
        public void GetCurShowPointKPI(CQTPoint point,out CQTMainPointKPIFine mainPointResult,out List<CQTFileKPIFineData> filesResult)
        {
            mainPointResult = null;
            if (curPointNameDataDic.ContainsKey(point.Name))
            {
                mainPointResult = curPointNameDataDic[point.Name];
            }
            filesResult = null;
            if (mainPointResult!=null)
            {
                filesResult = new List<CQTFileKPIFineData>();
                foreach (CQTSubPointKPIFine subPoint in mainPointResult.SubPointNameDataDic.Values)
                {
                    foreach (CQTFileKPIFineData fileData in subPoint.CarreerFileDataDic[CurCarreerID])
                    {
                        filesResult.Add(fileData);
                    }
                }
            }
        }

        private void makeReportColumn(TreeList treeList,CQTKPIReport report)
        {
            treeList.Columns.Clear();
            DevExpress.XtraTreeList.Columns.TreeListColumn colPntName = treeList.Columns.Add();
            colPntName.Caption = "地点/文件名称";
            colPntName.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colPntName.OptionsColumn.AllowEdit = false;
            colPntName.OptionsColumn.AllowMoveToCustomizationForm = false;
            colPntName.OptionsColumn.ReadOnly = true;
            colPntName.Name = "pointName";
            colPntName.Visible = true;
            colPntName.Width = 300;

            if (DIYQueryCQTKPIFine.isStatByCell)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn colLac = treeList.Columns.Add();
                colLac.Caption = "LAC";
                colLac.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colLac.OptionsColumn.AllowEdit = false;
                colLac.OptionsColumn.AllowMoveToCustomizationForm = false;
                colLac.OptionsColumn.ReadOnly = true;
                colLac.Name = "colLac";
                colLac.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colCi = treeList.Columns.Add();
                colCi.Caption = "CI";
                colCi.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colCi.OptionsColumn.AllowEdit = false;
                colCi.OptionsColumn.AllowMoveToCustomizationForm = false;
                colCi.OptionsColumn.ReadOnly = true;
                colCi.Name = "colCi";
                colCi.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn col频点 = treeList.Columns.Add();
                col频点.Caption = "频点";
                col频点.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col频点.OptionsColumn.AllowEdit = false;
                col频点.OptionsColumn.AllowMoveToCustomizationForm = false;
                col频点.OptionsColumn.ReadOnly = true;
                col频点.Name = "col频点";
                col频点.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn col扰码 = treeList.Columns.Add();
                col扰码.Caption = "扰码";
                col扰码.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col扰码.OptionsColumn.AllowEdit = false;
                col扰码.OptionsColumn.AllowMoveToCustomizationForm = false;
                col扰码.OptionsColumn.ReadOnly = true;
                col扰码.Name = "col扰码";
                col扰码.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colCGI = treeList.Columns.Add();
                colCGI.Caption = "CGI";
                colCGI.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colCGI.OptionsColumn.AllowEdit = false;
                colCGI.OptionsColumn.AllowMoveToCustomizationForm = false;
                colCGI.OptionsColumn.ReadOnly = true;
                colCGI.Name = "colCGI";
                colCGI.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colLng = treeList.Columns.Add();
                colLng.Caption = "经度";
                colLng.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colLng.OptionsColumn.AllowEdit = false;
                colLng.OptionsColumn.AllowMoveToCustomizationForm = false;
                colLng.OptionsColumn.ReadOnly = true;
                colLng.Name = "colLng";
                colLng.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colLat = treeList.Columns.Add();
                colLat.Caption = "纬度";
                colLat.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colLat.OptionsColumn.AllowEdit = false;
                colLat.OptionsColumn.AllowMoveToCustomizationForm = false;
                colLat.OptionsColumn.ReadOnly = true;
                colLat.Name = "colCi";
                colLat.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colDistance = treeList.Columns.Add();
                colDistance.Caption = "主地点与小区距离";
                colDistance.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colDistance.OptionsColumn.AllowEdit = false;
                colDistance.OptionsColumn.AllowMoveToCustomizationForm = false;
                colDistance.OptionsColumn.ReadOnly = true;
                colDistance.Name = "colDistance";
                colDistance.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colNetType = treeList.Columns.Add();
                colNetType.Caption = "网络类型";
                colNetType.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colNetType.OptionsColumn.AllowEdit = false;
                colNetType.OptionsColumn.AllowMoveToCustomizationForm = false;
                colNetType.OptionsColumn.ReadOnly = true;
                colNetType.Name = "colNetType";
                colNetType.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colCoverType = treeList.Columns.Add();
                colCoverType.Caption = "覆盖类型";
                colCoverType.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colCoverType.OptionsColumn.AllowEdit = false;
                colCoverType.OptionsColumn.AllowMoveToCustomizationForm = false;
                colCoverType.OptionsColumn.ReadOnly = true;
                colCoverType.Name = "colCoverType";
                colCoverType.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colAddress = treeList.Columns.Add();
                colAddress.Caption = "地址";
                colAddress.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colAddress.OptionsColumn.AllowEdit = false;
                colAddress.OptionsColumn.AllowMoveToCustomizationForm = false;
                colAddress.OptionsColumn.ReadOnly = true;
                colAddress.Name = "colAddress";
                colAddress.Visible = true;

                DevExpress.XtraTreeList.Columns.TreeListColumn colFileName = treeList.Columns.Add();
                colFileName.Caption = "文件名";
                colFileName.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                colFileName.OptionsColumn.AllowEdit = false;
                colFileName.OptionsColumn.AllowMoveToCustomizationForm = false;
                colFileName.OptionsColumn.ReadOnly = true;
                colFileName.Name = "colFileName";
                colFileName.Visible = true;
            }

            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Caption = rptCol.Name;
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;
                col.Tag = rptCol;
            }

            foreach (CQTKPISummaryColumn smCol in report.SummaryColumns)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Caption = smCol.Name;
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;
                col.Tag = smCol;
            }

        }
  
    }
}
