﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using GeneLau.WinFormsUI.Docking;
using MasterCom.RAMS.KPI_Statistics.ReportForm;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class KPIReportFormSheet : DockContent
    {
        public KPIReportFormSheet()
            : base()
        {
            InitializeComponent();
            GroupGapRowNum = 0;
        }

        public int GroupHeaderInfoNum
        { get; set; }

        public int GroupHeaderRowIndex
        { get; set; }

        private bool isMultiGroup = false;
        /// <summary>
        /// 各组数据间相隔的行数
        /// </summary>
        public int GroupGapRowNum
        {
            get;
            set;
        }

        public void BestFitColWidth()
        {
            dataView.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
        }

        List<KPIDataGroup> dataSet = null;
        ReportStyle report = null;
        ReportStyle originalReport = null;
        public ReportStyle OrgReport
        {
            get { return originalReport; }
        }
        public ReportStyle CurReport
        {
            get { return report; }
        }

        /// <summary>
        /// 保存表表模板
        /// </summary>
        /// <param name="saveAs">true 为另存为；false为保存原报表</param>
        public void SaveReportTemplate(string name)
        {
            report.name = name;
            report.Save();
            int idx = KPIReportManager.Instance.Reports.IndexOf(originalReport);
            originalReport = report.copyInstance();
            KPIReportManager.Instance.Reports[idx] = originalReport;
            //KPIReportManager.Instance.Init();
        }

        public void ShowReport(ReportStyle report, List<KPIDataGroup> dataSet)
        {
            this.originalReport = report;
            this.report = report.copyInstance();
            this.dataSet = dataSet;
            this.isMultiGroup = dataSet.Count > 1;
            if (isMultiGroup)
            {
                GroupHeaderInfoNum = 1;
            }
            RefreshReport();
        }

        /// <summary>
        /// 初始化表格
        /// </summary>
        private void initGridView()
        {
            int grpNum = dataSet.Count;
            dataView.Columns.Clear();
            dataView.Rows.Clear();

            //添加组头信息列
            for (int i = 0; i < GroupHeaderInfoNum; i++)
            {
                DataGridViewTextBoxColumn col = new DataGridViewTextBoxColumn();
                col.Frozen = true;
                dataView.Columns.Add(col);
            }

            //添加实际报表列
            int viewColCnt = report.ColCount;
            for (int i = 0; i < viewColCnt; i++)
            {
                DataGridViewTextBoxColumn col = new DataGridViewTextBoxColumn();
                dataView.Columns.Add(col);
            }

            if (isMultiGroup && report.IsSimpleStyleReport)
            {//多组且报表为2行时，默认显示成单表头样式，符合实际报表排版
                dataView.RowCount = report.RowCount + (grpNum * GroupGapRowNum) + grpNum - 1;
            }
            else if (grpNum == 0)
            {
                dataView.RowCount = 1 * (report.RowCount + GroupGapRowNum);
            }
            else
            {
                dataView.RowCount = grpNum * (report.RowCount + GroupGapRowNum);
            }
        }

        /// <summary>
        /// 根据映射得到报表单元格
        /// </summary>
        /// <param name="viewRowIdx"></param>
        /// <param name="viewColIdx"></param>
        /// <returns></returns>
        private RptCell getReportCellByMapping(int viewRowIdx, int viewColIdx)
        {
            int cellColIdx = viewColIdx - GroupHeaderInfoNum;
            int cellRowIdx = viewRowIdx ;
            int templateRowNum = report.RowCount;
            if (viewRowIdx >= templateRowNum)
            {//行数比单报表的行数大
                if (report.IsSimpleStyleReport)
                {//单表头模式，各组间没有空隙行，因此大于单一报表行数的，皆为1
                    cellRowIdx = 1;
                }
                else if (viewRowIdx >= templateRowNum + GroupGapRowNum)
                {
                    cellRowIdx = viewRowIdx % (templateRowNum + GroupGapRowNum);
                }
            }
            RptCell cell = report.GetCell(cellRowIdx, cellColIdx);
            if (cell == null)
            {
                cell = new RptCell();
                cell.colAt = cellColIdx;
                cell.rowAt = cellRowIdx;
            }
            return cell;
        }

        public string Caption
        {
            get { return this.Text; }
            set
            {
                this.Text = value;
                this.groupBox.Text = value;
            }
        }

        private void RefreshReport()
        {
            this.Text = report.name;
            groupBox.Text = report.name;

            initGridView();

            bool isSimpleReport = report.IsSimpleStyleReport;
            int rowCount = report.RowCount;
            int templateRowNum = report.RowCount;
            if (dataSet.Count > 1)
            {
                for (int i = 0; i < dataSet.Count; i++)
                {
                    KPIDataGroup dataGrp = dataSet[i];
                    int rowOffset = 0;
                    if (i != 0)
                    {//非第一组信息，需计算行位置偏移
                        if (isSimpleReport)
                        {//只显示第一组表头时，行偏移量计算去掉表头一行
                            rowOffset = templateRowNum + i * GroupGapRowNum + i - 2;
                        }
                        else
                        {
                            rowOffset = (templateRowNum + GroupGapRowNum) * i;
                        }
                    }
                    int rowIdx = rowCount > 1 ? 1 + rowOffset : 0 + rowOffset;
                    DataGridViewCell viewCell = dataView.Rows[rowIdx].Cells[0];
                    viewCell.Value = dataGrp.ToString();
                }
            }

            foreach (RptCell cell in report.rptCellList)
            {
                refreshCells(cell, isSimpleReport);
            }
            BestFitColWidth();
        }

        private void refreshCells(RptCell cell, bool isSimpleReport)
        {
            if (cell == null)
            {
                return;
            }
            int templateRowNum = report.RowCount;
            int grpCnt = dataSet.Count;
            int colOffset = GroupHeaderInfoNum;
            for (int i = 0; i < grpCnt; i++)
            {
                KPIDataGroup dataGrp = dataSet[i];
                int rowOffset = 0;
                bool isValid = getRowOffset(cell, isSimpleReport, templateRowNum, i, ref rowOffset);
                if (isValid)
                {
                    return;
                }

                int rowIdx = cell.rowAt + rowOffset;
                int colIdx = cell.colAt + colOffset;
                DataGridViewCell viewCell = dataView.Rows[rowIdx].Cells[colIdx];
                //viewCell.Style.BackColor = cell.bkColor;
                viewCell.Style.ForeColor = cell.foreColor;
                if (cell.exp != null && cell.exp.Contains("{") && cell.exp.Contains("}"))
                {
                    string exp = getExp(cell);

                    ReservedField field;
                    if (StatReserved.IsContainsReserved(exp, out field))
                    {
                        viewCell.Value = dataGrp.GetReservedInfo((Model.CarrierType)cell.carrierID, field);
                        continue;
                    }

                    double val = dataGrp.CalcFormula((Model.CarrierType)cell.carrierID, cell.momt, exp, cell.ExtraExp, cell.ServiceIDSet, cell.FileNameKeyValueList);
                    val = Math.Round(val, cell.deciNum);
                    setViewCell(cell, viewCell, exp, val);
                }
                else
                {
                    viewCell.Value = cell.exp;
                }
                isValid = setViewCellColor(cell, viewCell);
                if (isValid)
                {
                    return;
                }
            }
        }

        private string getExp(RptCell cell)
        {
            string exp = cell.exp;
            if (!exp.Contains("@"))
            {
                int beginIdx = cell.exp.IndexOf("{");
                int endIdx = cell.exp.IndexOf("}");
                exp = cell.exp.Substring(beginIdx, endIdx - beginIdx + 1);
            }

            return exp;
        }

        private bool getRowOffset(RptCell cell, bool isSimpleReport, int templateRowNum, int i, ref int rowOffset)
        {
            if (i != 0)
            {//非第一组信息，需计算行位置偏移
                if (isSimpleReport)
                {//只显示第一组表头时，行偏移量计算去掉表头一行
                    if (cell.rowAt == 0)
                    {
                        return true;
                    }
                    rowOffset = templateRowNum + i * GroupGapRowNum + i - 2;
                }
                else
                {
                    rowOffset = (templateRowNum + GroupGapRowNum) * i;
                }
            }

            return false;
        }

        private void setViewCell(RptCell cell, DataGridViewCell viewCell, string exp, double val)
        {
            if (double.IsNaN(val) || double.IsInfinity(val))
            {
                viewCell.Value = string.Intern("-");
            }
            else
            {
                if (exp.Equals(cell.exp.Trim()))
                {
                    viewCell.Value = val;
                }
                else
                {
                    viewCell.Value = cell.exp.Replace(exp, val.ToString());
                }
            }
        }

        private bool setViewCellColor(RptCell cell, DataGridViewCell viewCell)
        {
            if (cell.IsDynamicBKColor && cell.DynamicBKColorRanges != null)
            {
                try
                {
                    string valStr = viewCell.Value.ToString().Replace("%", "").Trim();
                    float value = Convert.ToSingle(valStr);
                    foreach (MasterCom.RAMS.Model.DTParameterRangeColor range in cell.DynamicBKColorRanges)
                    {
                        if (value >= range.Min && range.Max >= value)
                        {
                            viewCell.Style.BackColor = range.Value;
                            return true;
                        }
                    }
                }
                catch
                {
                    viewCell.Style.BackColor = cell.bkColor;
                }
            }
            else
            {
                viewCell.Style.BackColor = cell.bkColor;
            }
            return false;
        }

        private void miCellOption_Click(object sender, EventArgs e)
        {
            performCellOption(dataView.CurrentCell);
        }
        private bool isCanEdit()
        {
            return KPIReportManager.Instance.IsReportCanEdit(report);
        }
        private void performCellOption(DataGridViewCell viewCell)
        {
            if (!isCanEdit())
            {
                return;
            }

            if (viewCell == null)
            {
                MessageBox.Show("请选取单元格！");
                return;
            }
            RptCell rptCell = getReportCellByMapping(viewCell.RowIndex, viewCell.ColumnIndex);
            bool newCell = !report.rptCellList.Contains(rptCell);
            CellOptionForm cellForm = CellOptionForm.Instance;
            if (newCell)
            {
                cellForm.SetReportCells(null, rptCell.rowAt, rptCell.colAt);
            }
            else
            {
                cellForm.SetReportCells(rptCell, rptCell.rowAt, rptCell.colAt);
            }
            if (cellForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            rptCell = cellForm.ExpCell;
            report.AddCell(rptCell);
            if (cellForm.TitleCell != null)
            {
                report.AddCell(cellForm.TitleCell);
            }
            bool isSimpleStyleReport = report.IsSimpleStyleReport;
            refreshCells(rptCell, isSimpleStyleReport);
            refreshCells(cellForm.TitleCell, isSimpleStyleReport);
        }

        private void miInsertRow_Click(object sender, EventArgs e)
        {
            insertRow(true);
        }

        private void miInsertRowSuf_Click(object sender, EventArgs e)
        {
            insertRow(false);
        }

        private void insertRow(bool isPre)
        {
            if (!isCanEdit())
            {
                return;
            }
            DataGridViewCell cell = dataView.CurrentCell;
            if (isMultiGroup)
            {//多组信息时，需更新报表，再刷新所有组
                RptCell rptCell = getReportCellByMapping(cell.RowIndex, cell.ColumnIndex);
                report.InsertRows(isPre, rptCell.rowAt, 1);
                RefreshReport();
            }
            else
            {//单组时，简单刷新
                report.InsertRows(isPre, cell.RowIndex, 1);
                dataView.Rows.Insert(isPre ? cell.RowIndex : cell.RowIndex + 1, 1);
            }
        }

        private void miInsertColSuf_Click(object sender, EventArgs e)
        {
            insertCol(false);
        }

        private void miInsertCol_Click(object sender, EventArgs e)
        {
            insertCol(true);
        }

        private void insertCol(bool isPre)
        {
            if (!isCanEdit())
            {
                return;
            }
            DataGridViewCell cell = dataView.CurrentCell;
            if (cell.ColumnIndex < GroupHeaderInfoNum)
            {
                MessageBox.Show("不能往分组信息列前插列");
                return;
            }
            //报表实际列
            int rptColIdx = cell.ColumnIndex - GroupHeaderInfoNum;
            report.InsertColumns(isPre, rptColIdx, 1);

            DataGridViewTextBoxColumn col = new DataGridViewTextBoxColumn();
            dataView.Columns.Insert(isPre ? cell.ColumnIndex : cell.ColumnIndex + 1, col);
        }

        private void miRemoveRows_Click(object sender, EventArgs e)
        {
            if (!isCanEdit())
            {
                return;
            }
            DataGridViewCell cell = dataView.CurrentCell;
            if (isMultiGroup)
            {
                RptCell rptCell = getReportCellByMapping(cell.RowIndex, cell.ColumnIndex);
                report.RemoveRows(rptCell.rowAt, 1);
                RefreshReport();
            }
            else
            {
                report.RemoveRows(cell.RowIndex, 1);
                dataView.Rows.RemoveAt(cell.RowIndex);
            }
        }

        private void miRemoveCols_Click(object sender, EventArgs e)
        {
            if (!isCanEdit())
            {
                return;
            }
            DataGridViewCell cell = dataView.CurrentCell;
            if (cell.ColumnIndex < GroupHeaderInfoNum)
            {
                MessageBox.Show("不能删除分组信息列");
                return;
            }
            //报表实际列
            int rptColIdx = cell.ColumnIndex - GroupHeaderInfoNum;
            report.RemoveColumns(rptColIdx, 1);
            dataView.Columns.RemoveAt(cell.ColumnIndex);
        }

        private void dataView_DoubleClick(object sender, EventArgs e)
        {
            MouseEventArgs me = e as MouseEventArgs;
            DataGridView.HitTestInfo info = dataView.HitTest(me.X, me.Y);
            if (info.Type != DataGridViewHitTestType.Cell)
            {
                return;
            }
            DataGridViewCell cell = dataView[info.ColumnIndex, info.RowIndex];
            if (cell != dataView.CurrentCell)
            {
                return;
            }
            if (dataView.CurrentCell.ColumnIndex<GroupHeaderInfoNum)
            {//组头信息

            }
            else
            {//报表单元格
                performCellOption(dataView.CurrentCell);
            }
        }

        private void dataView_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {//鼠标右键弹起时，设置所在单元格为选中单元格
                dataView.CurrentCell = dataView[e.ColumnIndex, e.RowIndex];
            }
        }

        #region 导出
        internal void Export2Xls()
        {
            ExcelNPOIManager.ExportToExcel(this.dataView);
        }

        internal void Export2XlsDetails()
        {
            ExportResultSecurityHelper.ExportToExcel(export2XlsDetailsThread, ExportResultSecurityHelper.ObjFileName, true);
            //MasterCom.RAMS.Util.ExcelManager.ExportExcel(dataView, dataView.ColumnCount);
        }
        private void export2XlsDetailsThread(object obj)
        {
            string fileName = (string)obj;
            try
            {
                MasterCom.RAMS.Util.ExcelManager.ExportExcel(dataView, dataView.ColumnCount, fileName);
            }
            catch(Exception e)
            {
                MessageBox.Show(string.Format("导出异常:{0}", e.StackTrace));
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void miExportToExcelSimple_Click(object sender, EventArgs e)
        {
            Export2Xls();
        }

        private void exportExcel_Click(object sender, EventArgs e)
        {
            Export2XlsDetails();
        }

        private string exportError;
        internal void Export2XlsFormula()
        {
            exportError = "";
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;//默认xlsx
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                object obj = dlg.FileName;
                WaitBox.Show("正在导出报表公式到Excel...", doExport, obj);
            }
            if (!string.IsNullOrEmpty(exportError))
            {
                MessageBox.Show("报表公式导出失败 : " + exportError);
            }
            else
            {
                MessageBox.Show("报表公式导出成功");
            }
        }

        private void doExport(object obj)
        {
            string targetFile = obj.ToString();
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }

            Microsoft.Office.Interop.Excel.Application xlApp = null;
            try
            {
                xlApp = new Microsoft.Office.Interop.Excel.Application();
                xlApp.Visible = false;

                Microsoft.Office.Interop.Excel.Workbook eBook = xlApp.Workbooks.Add(true);
                Microsoft.Office.Interop.Excel.Worksheet worksheet = (Microsoft.Office.Interop.Excel.Worksheet)eBook.Sheets.get_Item(1);

                foreach (var cell in report.rptCellList)
                {
                    worksheet.Cells[cell.rowAt + 1, cell.colAt + 1] = cell.exp;
                }

                eBook.SaveAs(targetFile);
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);
            }
            catch (Exception e)
            {
                exportError = e.Message;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
                System.Threading.Thread.Sleep(300);
                WaitBox.Close();
            }
        }
        #endregion
    }
}
