﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using CQTLibrary.CqtZTFunc;
using CQTLibrary.PublicItem;
namespace MasterCom.RAMS.CQT
{
   public class CQTProblemSummaryAnalys3G : QueryBase
   {
        public CQTProblemSummaryAnalys3G(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override string Name
        {
            get { return "3G问题点统计汇总"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        #region 全局变量
        public static List<ProblemSummaryPont3G> problemPointSummarryList { get; set; } = new List<ProblemSummaryPont3G>();
        public static List<ProblemPont3G> problemPointList { get; set; } = new List<ProblemPont3G>();
        private bool pointNum = false;
        readonly Func_ProblemAna probAna = new Func_ProblemAna();
        Dictionary<AreaCityKey, AreaDetail> cqtDetailDic = new Dictionary<AreaCityKey, AreaDetail>();
        List<CQTLibrary.PublicItem.ProblemItem> result = new List<CQTLibrary.PublicItem.ProblemItem>();
        readonly CQTLibrary.RAMS.NET.MainModel cqtModel = new CQTLibrary.RAMS.NET.MainModel();
        SumarryDdataInfoTD sumarryInfo;
        int strcityid = 0;
        int iNo = 1;
        int newpoint = 0;
        bool isPartDb = false;
        #endregion
        protected override void query()
        {
            WaitBox.Show("准备获取CQT点...", seachProblemData);
            showData();
        }
        /// <summary>
        /// 准备查询数据的条件
        /// </summary>
        private void seachProblemData()
        {
            WaitBox.ProgressPercent = 20;
            problemPointSummarryList.Clear();
            problemPointList.Clear();
            iNo = 1;
            sumarryInfo = new SumarryDdataInfoTD();
            cqtModel.DistrictID = MainModel.DistrictID;
            cqtModel.UserName = MainModel.User.LoginName;
            cqtModel.UserPass = MainModel.User.Password;
            if (MainModel.Server.IP.Equals("*************") || MainModel.Server.IP.Equals("**************"))
            {
                isPartDb = true;
            }
            cqtModel.ServerIP = MainModel.Server.IP;
            cqtModel.ServerPort = MainModel.Server.Port;
            if (MainModel.User.DBID == -1)
            {
                string strCity = getStrCity();
                cqtDetailDic = probAna.getCqtDetailDic(cqtModel, strCity, isPartDb);
                WaitBox.ProgressPercent = 30;
                List<int> disid = getDisid();
                for (int chdistrid = 0; chdistrid < disid.Count; chdistrid++)
                {
                    cqtModel.DistrictID = disid[chdistrid];
                    strcityid = disid[chdistrid];
                    WaitBox.Text = "正在获取" + DistrictManager.GetInstance().getDistrictName(disid[chdistrid]) + "市的CQT点...";
                    WaitBox.ProgressPercent = WaitBox.ProgressPercent + 1;

                    dealWithDataXQ();
                    dealWithSummarryData();
                }
            }
            else
            {
                string strCity = condition.DistrictID.ToString();
                strcityid = condition.DistrictID;
                cqtDetailDic = probAna.getCqtDetailDic(cqtModel, strCity, isPartDb);
                dealWithDataXQ();
                dealWithSummarryData();
            }
            WaitBox.ProgressPercent = 80;
            if (problemPointSummarryList.Count != 0)
            {
                WaitBox.Text = "正在进行汇总处理...";
                CQTProblemSummaryAnalys3G.problemPointSummarryList.Add(ProblemSummaryPont3G.fillDataTotal(sumarryInfo));
            }
            WaitBox.Close();
        }

        private string getStrCity()
        {
            StringBuilder strCity = new StringBuilder();
            foreach (int DistrictID1 in condition.DistrictIDs)
            {
                if (strCity.Length == 0)
                    strCity.Append(DistrictID1.ToString());
                else
                    strCity.Append("," + DistrictID1.ToString());
            }

            return strCity.ToString();
        }

        private List<int> getDisid()
        {
            List<int> disid = new List<int>();
            //按特定顺序排列查询各是数据
            int[] zhidingID = { 1, 2, 3, 12, 6, 5, 4, 10, 13, 14, 16, 21, 17, 7, 8, 9, 11, 15, 18, 19, 20, 22, 23 };
            for (int idk = 0; idk < zhidingID.Length; idk++)
            {
                if (condition.DistrictIDs.Contains(zhidingID[idk]))
                {
                    disid.Add(zhidingID[idk]);
                }
            }

            return disid;
        }

        /// <summary>
        /// 处理数据
        /// </summary>
        private void dealWithSummarryData()
        {
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            StringBuilder project = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    project.Append(condition.Projects[i].ToString() + ",");
                else
                    project.Append(condition.Projects[i].ToString());
            }
            XQDataInfoTD xqInfo = new XQDataInfoTD();
            if (pointNum)
            {
                for (int id = problemPointList.Count - newpoint; id < problemPointList.Count; id++)
                {
                    addXQDataInfoCount(xqInfo, id);
                }
            }
            int iTestDicCount = probAna.getTdTestCqtNum(cqtModel, sDtime, eDtime, project.ToString(), isPartDb);
            ProblemSummaryPont3G lisSTem = ProblemSummaryPont3G.fillXQDataTD(xqInfo, iTestDicCount, strcityid);
            CQTProblemSummaryAnalysTD.problemPointSummarryList.Add(lisSTem);
            SumarryDdataInfoTD.fillSummarryData(ref sumarryInfo, xqInfo, iTestDicCount, strcityid);
        }

        private void addXQDataInfoCount(XQDataInfoTD xqInfo, int id)
        {
            string type = problemPointList[id].SMainType;
            switch (type)
            {
                case "弱覆盖":
                    xqInfo.iweakcoverage++;
                    break;
                case "未接通":
                    xqInfo.inotconnected++;
                    break;
                case "掉话":
                    xqInfo.idroppedcalls++;
                    break;
                case "深度覆盖不足":
                    xqInfo.ilessdowntdn++;
                    break;
                case "下载速率低":
                    xqInfo.idownlessother++;
                    break;
                case "单用户下载速率低":
                    xqInfo.isingleDownLess++;
                    break;
                case "多用户下载速率低":
                    xqInfo.imostDownLess++;
                    break;
                case "下载掉线":
                    xqInfo.idowndrop++;
                    break;
                case "下载超时":
                    xqInfo.idowntimeout++;
                    break;
                case "异系统切换失败":
                    xqInfo.ifallTDToGSM++;
                    break;
                case "TD深度覆盖不达标":
                    xqInfo.ilessthendeepTD++;
                    break;
                case "T劣":
                    xqInfo.itlessHG++;
                    break;
                case "竞对弱覆盖":
                    xqInfo.icompareWeak++;
                    break;
                case "竞对下载速率低":
                    xqInfo.icompareDownLess++;
                    break;
                case "全程呼叫成功率比竞争对手差":
                    xqInfo.icallSuccessLess++;
                    break;
            }
        }

        /// <summary>
        /// 对事前信息进行处理，以便对应原来的统计接口
        /// </summary>
        private void dealWithDataXQ()
        {
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            StringBuilder project = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    project.Append(condition.Projects[i].ToString() + ",");
                else
                    project.Append(condition.Projects[i].ToString());
            }
            result = probAna.getTdProblemInfo(cqtModel, sDtime, eDtime, project.ToString(), cqtDetailDic, isPartDb);
            if (result.Count > 0)
                pointNum = true;
            else
                pointNum = false;
            newpoint = 0;
            int mainProblemID = 0;
            for (int id = 0; id < result.Count; id++)
            {
                if (result[id].probFileList.Count != 0 && result[id].probFileList[0].ToString() != "")
                {
                    newpoint++;
                    ProblemPont3G lis1 = new ProblemPont3G();
                    mainProblemID = setProblemPont3GSMainType(mainProblemID, id, lis1);

                    setProblemPont3GSSecondType(mainProblemID, id, lis1);

                    setProblemPont3GInfo(id, lis1);

                    problemPointList.Add(lis1);
                }
            }
        }

        private int setProblemPont3GSMainType(int mainProblemID, int id, ProblemPont3G lis1)
        {
            if (result[id].StrMainProblem2 != "")
            {
                lis1.SMainType = result[id].StrMainProblem2;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 2);
                mainProblemID = 0;
            }
            else if (result[id].StrMainProblem3 != "")
            {
                lis1.SMainType = result[id].StrMainProblem3;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 3);
                mainProblemID = 1;
            }
            else if (result[id].StrMainProblem1 != "")
            {
                lis1.SMainType = result[id].StrMainProblem1;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 1);
                mainProblemID = 2;
            }
            else if (result[id].StrSecProblem1 != "")
            {
                lis1.SMainType = result[id].StrSecProblem1;
                mainProblemID = 3;
            }

            return mainProblemID;
        }

        private void setProblemPont3GSSecondType(int mainProblemID, int id, ProblemPont3G lis1)
        {
            if (mainProblemID == 0)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrMainProblem3, true);
                addSSecondType(lis1, result[id].StrMainProblem1, true);
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 1)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrMainProblem1, true);
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 2)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 3)
            {
                lis1.SSecondType = "";
            }
        }

        private void addSSecondType(ProblemPont3G lis1, string problem, bool addSplit)
        {
            if (problem != "")
            {
                lis1.SSecondType += problem;
            }
            if (addSplit)
            {
                lis1.SSecondType += ",";
            }
        }

        private void setProblemPont3GInfo(int id, ProblemPont3G lis1)
        {
            int ifile;
            int ilac = 0, ici = 0;
            lis1.IID = iNo++;
            lis1.SCity = DistrictManager.GetInstance().getDistrictName(result[id].Icity);
            lis1.STestTime = result[id].Dtime;
            lis1.STestPoint = result[id].Strcqtname;
            lis1.SSitePro = result[id].Strcqttype;
            lis1.SImportLeve = "-";
            StringBuilder s1 = new StringBuilder();
            for (ifile = 0; ifile < result[id].probFileList.Count; ifile++)
            {
                s1.Append(result[id].probFileList[ifile].ToString() + "；");
            }
            lis1.STestFilePosition = s1.ToString();
            StringBuilder s2 = new StringBuilder();
            for (ifile = 0; ifile < result[id].probCellInfoList.Count; ifile++)
            {
                ilac = result[id].probCellInfoList[ifile].Ilac;
                ici = result[id].probCellInfoList[ifile].Ici;
                if (result[id].probCellInfoList[ifile].Strcellname == "")
                    s2.Append("(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[ifile].Isampleid + "个,场强为" + result[id].probCellInfoList[ifile].Irxlev + "dBm" + "；");
                else
                    s2.Append(result[id].probCellInfoList[ifile].Strcellname + "(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[ifile].Isampleid + "个,场强为" + result[id].probCellInfoList[ifile].Irxlev + "dBm" + "；");
            }
            lis1.SReasonAna = s2.ToString();
            lis1.SReasonType = result[id].StrCauseType;
        }

        /// <summary>
        /// 显示处理结果窗体
        /// </summary>
        private void showData()
        {
            CQTProblemSummary3G cqtproblemShowForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(CQTProblemSummary3G).FullName);
            cqtproblemShowForm = obj == null ? null : obj as CQTProblemSummary3G;
            if (cqtproblemShowForm == null || cqtproblemShowForm.IsDisposed)
            {
                cqtproblemShowForm = new CQTProblemSummary3G(MainModel, "通报问题点");
            }
            cqtproblemShowForm.filldataSummary3G(0);

            cqtproblemShowForm.Show(MainModel.MainForm);
        }
   }
}
