﻿namespace MasterCom.RAMS.Stat
{
    partial class DIYQueryAbnormalDataGridSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.editGridRate = new DevExpress.XtraEditors.SpinEdit();
            this.editGridSampleNum = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.editSmax = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.editGridRate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.editGridSampleNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.editSmax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(322, 193);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 40;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(229, 193);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 39;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(338, 85);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 14);
            this.labelControl8.TabIndex = 49;
            this.labelControl8.Text = "%";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(338, 38);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 14);
            this.labelControl6.TabIndex = 48;
            this.labelControl6.Text = "个";
            // 
            // editGridRate
            // 
            this.editGridRate.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.editGridRate.Location = new System.Drawing.Point(214, 81);
            this.editGridRate.Name = "editGridRate";
            this.editGridRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editGridRate.Properties.IsFloatValue = false;
            this.editGridRate.Properties.Mask.EditMask = "N00";
            this.editGridRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.editGridRate.Size = new System.Drawing.Size(117, 21);
            this.editGridRate.TabIndex = 46;
            // 
            // editGridSampleNum
            // 
            this.editGridSampleNum.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.editGridSampleNum.Location = new System.Drawing.Point(214, 34);
            this.editGridSampleNum.Name = "editGridSampleNum";
            this.editGridSampleNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editGridSampleNum.Properties.IsFloatValue = false;
            this.editGridSampleNum.Properties.Mask.EditMask = "N00";
            this.editGridSampleNum.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.editGridSampleNum.Size = new System.Drawing.Size(117, 21);
            this.editGridSampleNum.TabIndex = 45;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(71, 88);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(125, 14);
            this.labelControl4.TabIndex = 43;
            this.labelControl4.Text = "异常文件栅格数比例 ≥ ";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(107, 37);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(89, 14);
            this.labelControl2.TabIndex = 42;
            this.labelControl2.Text = "栅格采样点数 ≥ ";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(107, 131);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(89, 14);
            this.labelControl1.TabIndex = 41;
            this.labelControl1.Text = "异常波动系数 ≥ ";
            // 
            // editSmax
            // 
            this.editSmax.EditValue = new decimal(new int[] {
            32,
            0,
            0,
            65536});
            this.editSmax.Location = new System.Drawing.Point(214, 128);
            this.editSmax.Name = "editSmax";
            this.editSmax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editSmax.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.editSmax.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.editSmax.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.editSmax.Size = new System.Drawing.Size(117, 21);
            this.editSmax.TabIndex = 50;
            // 
            // DIYQueryAbnormalDataGridSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(423, 246);
            this.Controls.Add(this.editSmax);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.editGridRate);
            this.Controls.Add(this.editGridSampleNum);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "DIYQueryAbnormalDataGridSettingForm";
            this.Text = "异常数据查询条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.editGridRate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.editGridSampleNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.editSmax.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit editGridRate;
        private DevExpress.XtraEditors.SpinEdit editGridSampleNum;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit editSmax;
    }
}