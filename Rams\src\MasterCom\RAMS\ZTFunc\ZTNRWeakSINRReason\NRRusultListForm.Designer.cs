﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRRusultListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.pageGeneral = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridSummary = new DevExpress.XtraGrid.GridControl();
            this.ctxSummary = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.viewSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colGrp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTotalCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWPCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colReason = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colReasonCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pageDetail = new DevExpress.XtraTab.XtraTabPage();
            this.gridDetail = new DevExpress.XtraGrid.GridControl();
            this.ctxDetail = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2XlsDetail = new System.Windows.Forms.ToolStripMenuItem();
            this.viewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).BeginInit();
            this.tabCtrl.SuspendLayout();
            this.pageGeneral.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).BeginInit();
            this.ctxSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).BeginInit();
            this.pageDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridDetail)).BeginInit();
            this.ctxDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedTabPage = this.pageGeneral;
            this.tabCtrl.Size = new System.Drawing.Size(1317, 691);
            this.tabCtrl.TabIndex = 0;
            this.tabCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageGeneral,
            this.pageDetail});
            // 
            // pageGeneral
            // 
            this.pageGeneral.Controls.Add(this.splitContainerControl1);
            this.pageGeneral.Name = "pageGeneral";
            this.pageGeneral.Size = new System.Drawing.Size(1310, 661);
            this.pageGeneral.Text = "概要";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Collapsed = true;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridSummary);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1310, 661);
            this.splitContainerControl1.SplitterPosition = 341;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridSummary
            // 
            this.gridSummary.ContextMenuStrip = this.ctxSummary;
            this.gridSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridSummary.Location = new System.Drawing.Point(0, 0);
            this.gridSummary.MainView = this.viewSummary;
            this.gridSummary.Name = "gridSummary";
            this.gridSummary.Size = new System.Drawing.Size(1310, 655);
            this.gridSummary.TabIndex = 0;
            this.gridSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewSummary});
            // 
            // ctxSummary
            // 
            this.ctxSummary.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.ctxSummary.Name = "ctxSummary";
            this.ctxSummary.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // viewSummary
            // 
            this.viewSummary.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colGrp,
            this.colTotalCnt,
            this.colWPCnt,
            this.colReason,
            this.colReasonCnt,
            this.colPer});
            this.viewSummary.GridControl = this.gridSummary;
            this.viewSummary.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "CellName", null, "{0}个质差点"),
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "FileName", null, "{0}个质差点")});
            this.viewSummary.Name = "viewSummary";
            this.viewSummary.OptionsBehavior.Editable = false;
            this.viewSummary.OptionsView.AllowCellMerge = true;
            this.viewSummary.OptionsView.ShowGroupPanel = false;
            // 
            // colGrp
            // 
            this.colGrp.Caption = "项";
            this.colGrp.FieldName = "Group";
            this.colGrp.Name = "colGrp";
            this.colGrp.Visible = true;
            this.colGrp.VisibleIndex = 0;
            // 
            // colTotalCnt
            // 
            this.colTotalCnt.Caption = "总采样点个数";
            this.colTotalCnt.FieldName = "TotalCnt";
            this.colTotalCnt.Name = "colTotalCnt";
            this.colTotalCnt.Visible = true;
            this.colTotalCnt.VisibleIndex = 1;
            // 
            // colWPCnt
            // 
            this.colWPCnt.Caption = "质差点个数";
            this.colWPCnt.FieldName = "WPCnt";
            this.colWPCnt.Name = "colWPCnt";
            this.colWPCnt.Visible = true;
            this.colWPCnt.VisibleIndex = 2;
            // 
            // colReason
            // 
            this.colReason.Caption = "原因";
            this.colReason.FieldName = "Reason";
            this.colReason.Name = "colReason";
            this.colReason.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colReason.Visible = true;
            this.colReason.VisibleIndex = 3;
            // 
            // colReasonCnt
            // 
            this.colReasonCnt.Caption = "个数";
            this.colReasonCnt.FieldName = "ReasonCnt";
            this.colReasonCnt.Name = "colReasonCnt";
            this.colReasonCnt.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colReasonCnt.Visible = true;
            this.colReasonCnt.VisibleIndex = 5;
            // 
            // colPer
            // 
            this.colPer.Caption = "原因占比(%)";
            this.colPer.FieldName = "Percentage";
            this.colPer.Name = "colPer";
            this.colPer.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colPer.Visible = true;
            this.colPer.VisibleIndex = 4;
            // 
            // pageDetail
            // 
            this.pageDetail.Controls.Add(this.gridDetail);
            this.pageDetail.Name = "pageDetail";
            this.pageDetail.Size = new System.Drawing.Size(1310, 661);
            this.pageDetail.Text = "详细";
            // 
            // gridDetail
            // 
            this.gridDetail.ContextMenuStrip = this.ctxDetail;
            this.gridDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridDetail.Location = new System.Drawing.Point(0, 0);
            this.gridDetail.MainView = this.viewDetail;
            this.gridDetail.Name = "gridDetail";
            this.gridDetail.Size = new System.Drawing.Size(1310, 661);
            this.gridDetail.TabIndex = 0;
            this.gridDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewDetail});
            // 
            // ctxDetail
            // 
            this.ctxDetail.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2XlsDetail});
            this.ctxDetail.Name = "ctxDetail";
            this.ctxDetail.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2XlsDetail
            // 
            this.miExport2XlsDetail.Name = "miExport2XlsDetail";
            this.miExport2XlsDetail.Size = new System.Drawing.Size(138, 22);
            this.miExport2XlsDetail.Text = "导出Excel...";
            this.miExport2XlsDetail.Click += new System.EventHandler(this.miExport2XlsDetail_Click);
            // 
            // viewDetail
            // 
            this.viewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn8,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13});
            this.viewDetail.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.viewDetail.GridControl = this.gridDetail;
            this.viewDetail.GroupCount = 1;
            this.viewDetail.Name = "viewDetail";
            this.viewDetail.OptionsBehavior.Editable = false;
            this.viewDetail.OptionsView.ShowGroupedColumns = true;
            this.viewDetail.SortInfo.AddRange(new DevExpress.XtraGrid.Columns.GridColumnSortInfo[] {
            new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn1, DevExpress.Data.ColumnSortOrder.Ascending)});
            this.viewDetail.DoubleClick += new System.EventHandler(this.viewDetail_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 94;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "EARFCN";
            this.gridColumn2.FieldName = "EARFCN";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 65;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "PCI";
            this.gridColumn3.FieldName = "PCI";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 43;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "原因";
            this.gridColumn4.FieldName = "ReasonName";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 8;
            this.gridColumn4.Width = 99;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "文件";
            this.gridColumn5.FieldName = "FileName";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 9;
            this.gridColumn5.Width = 99;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "距离(米)";
            this.gridColumn8.FieldName = "Distance";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 10;
            this.gridColumn8.Width = 85;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "采样点经度";
            this.gridColumn6.FieldName = "PntLng";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 11;
            this.gridColumn6.Width = 126;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "采样点纬度";
            this.gridColumn7.FieldName = "PntLat";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 12;
            this.gridColumn7.Width = 131;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "SINR";
            this.gridColumn9.FieldName = "SINR";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 6;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "RSRP";
            this.gridColumn10.FieldName = "RSRP";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 7;
            this.gridColumn10.Width = 64;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "问题点时间";
            this.gridColumn11.FieldName = "TestPoint.DateTimeStringWithMillisecond";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 1;
            this.gridColumn11.Width = 146;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "TAC";
            this.gridColumn12.FieldName = "TAC";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 4;
            this.gridColumn12.Width = 56;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "NCI";
            this.gridColumn13.FieldName = "NCI";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 5;
            this.gridColumn13.Width = 93;
            // 
            // NRRusultListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1317, 691);
            this.Controls.Add(this.tabCtrl);
            this.Name = "NRRusultListForm";
            this.Text = "SINR质差原因";
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).EndInit();
            this.tabCtrl.ResumeLayout(false);
            this.pageGeneral.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).EndInit();
            this.ctxSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).EndInit();
            this.pageDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridDetail)).EndInit();
            this.ctxDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabCtrl;
        private DevExpress.XtraTab.XtraTabPage pageGeneral;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView viewSummary;
        private DevExpress.XtraTab.XtraTabPage pageDetail;
        private DevExpress.XtraGrid.GridControl gridDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView viewDetail;
        private DevExpress.XtraGrid.Columns.GridColumn colGrp;
        private DevExpress.XtraGrid.Columns.GridColumn colTotalCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colWPCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colReason;
        private DevExpress.XtraGrid.Columns.GridColumn colReasonCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colPer;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private System.Windows.Forms.ContextMenuStrip ctxSummary;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private System.Windows.Forms.ContextMenuStrip ctxDetail;
        private System.Windows.Forms.ToolStripMenuItem miExport2XlsDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
    }
}