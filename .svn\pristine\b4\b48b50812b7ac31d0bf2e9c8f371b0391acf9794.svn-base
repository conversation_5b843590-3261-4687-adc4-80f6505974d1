﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    class DIYQueryFileInfoForOrderAnalyzer : DIYQueryFileInfoBase
    {
        public DIYQueryFileInfoForOrderAnalyzer(MainModel mainModel)
            : base(mainModel)
        {
        }


        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        private static DIYQueryFileInfoForOrderAnalyzer instance = null;

        public static DIYQueryFileInfoForOrderAnalyzer GetInstance()
        {
            if (instance == null)
            {
                instance = new DIYQueryFileInfoForOrderAnalyzer(MainModel.GetInstance());
            }
            return instance;
        }

        public override string Name
        {
            get { return "工单分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29010, this.Name);
        }
        public override string IconName
        {
            get { return ""; }
        }


        private DIYSQLForOrderAnalyzerTimer orderTimer = null;
        private DIYSQLForOrderAnalyzer GroupQuery = null;
        private OrderAnalyzerForm resultForm = null;
        private bool shouldShowForm = false;
        private string strWhere = "";
        protected override void query()
        {
            strWhere = "";
            if (conditionDlg == null)
            {
                conditionDlg = new GroupStatSetDlg();
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                conditionDlg.getCondition(out strWhere);
            }
            else
            {
                return;
            }
            
            WaitBox.Show("开始查询工单...", queryInThread);

            if (shouldShowForm)
            {
                fireShowForm();
            }
            shouldShowForm = false;

        }

        private void fireShowForm()
        {
            if (GroupQuery.GroupModels == null)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            OrderAnalyzer analyzer = new OrderAnalyzer();
            analyzer.Analyze(GroupQuery.GroupModels,orderTimer.Models);
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new OrderAnalyzerForm();
            }
            resultForm.FillData(analyzer.GetResults());
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        GroupStatSetDlg conditionDlg = null;
        private void queryInThread()
        {
            try
            {
                GroupQuery = new DIYSQLForOrderAnalyzer(MainModel, strWhere);
                GroupQuery.Query();
                orderTimer = new DIYSQLForOrderAnalyzerTimer(MainModel, strWhere);
                orderTimer.Query();
                shouldShowForm = true;


            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }

    class DIYSQLForOrderAnalyzer : DIYSQLBase
    {
        public DIYSQLForOrderAnalyzer(MainModel mainModel, string strWhere)
            : base(mainModel)
        {
            MainDB = false;
            this.strWhere = "where  " + strWhere;
            init();
        }

        protected string strWhere;//查询条件
        protected override string getSqlTextString()
        {

            return "select a.task_id,b._name,a.user_name,a.description,a.time,b.地市,b.区域名称, b.进度状态 "
            + "  from MTNOH_AAA_Platform.dbo.wf_task_process_info_9 a left join MTNOH_AAA_Platform.dbo.task_9 b on a.task_id = b._id   " + strWhere + " order by _id, a.time"; 
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[8];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            return rType;
        }

        private Dictionary<int, List<GroupStatModel>> groupModels;
        public Dictionary<int, List<GroupStatModel>> GroupModels
        {
            get { return groupModels; }
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            GroupStatModel groupModel = null;
            groupModels = new Dictionary<int, List<GroupStatModel>>();
            int status = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package, ref groupModel, ref status);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package, ref GroupStatModel groupModel, ref int status)
        {
            int taskID = package.Content.GetParamInt();
            string taskName = package.Content.GetParamString();
            string groupName = package.Content.GetParamString();
            string description = package.Content.GetParamString();
            string ordertime = package.Content.GetParamString();
            string district = package.Content.GetParamString();
            string areaName = package.Content.GetParamString();
            string timeStatus = package.Content.GetParamString();

            int groupindex = getGroupIndex(groupName);
            if (groupindex != -1)
            {
                groupModel = new GroupStatModel();
                groupModel.AreaName = areaName;
                groupModel.Description = description;
                groupModel.District = district;
                groupModel.GroupName = groupName;
                groupModel.OrderTime = ordertime;
                groupModel.TaskID = taskID;
                groupModel.TaskName = taskName;
                groupModel.TimeStatus = timeStatus;
                if (getGroupIndex(groupName) < 0)
                {
                    if (status == 0)
                    {
                        return;
                    }
                    if (groupModels.ContainsKey(taskID) && groupModels[taskID].Count > 0)
                    {
                        groupModels[taskID].RemoveAt(groupModels[taskID].Count - 1);
                        status = 0;
                    }
                    return;
                }
                status = 1;
                addGroupModels(groupModel, taskID);
            }
            else if (getGroupIndex(groupName) < 0 && status != 0 && groupModels.ContainsKey(taskID) && groupModels[taskID].Count > 0)
            {
                groupModels[taskID].RemoveAt(groupModels[taskID].Count - 1);
                status = 0;
            }
        }

        private void addGroupModels(GroupStatModel groupModel, int taskID)
        {
            if (groupModels.ContainsKey(taskID))
            {
                groupModels[taskID].Add(groupModel);
            }
            else
            {
                List<GroupStatModel> tempModels = new List<GroupStatModel>();
                tempModels.Add(groupModel);
                groupModels.Add(taskID, tempModels);
            }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        private void init()
        {
            initGroup();
            initDistrict();
        }

        Dictionary<string, int> groupDic;
        private void initGroup()
        {
            groupDic = new Dictionary<string, int>();
            groupDic.Add("T1", 0);
            groupDic.Add("测试数据管理", 0);
            groupDic.Add("T2_1", 1);
            groupDic.Add("片区优化组", 1);
            groupDic.Add("T2_2", 2);
            groupDic.Add("参数维护组", 2);
            groupDic.Add("T2_3", 3);
            groupDic.Add("室分维护组", 3);
            groupDic.Add("T2_4", 4);
            groupDic.Add("无线规划组", 4);
            groupDic.Add("T2_5", 5);
            groupDic.Add("干线优化组", 5);
            groupDic.Add("T2_6", 6);
            groupDic.Add("疑难问题处理组", 6);
            groupDic.Add("集中优化工单程序", 8);
        }

        Dictionary<string, int> districtDic;
        private void initDistrict()
        {
            districtDic = new Dictionary<string, int>();
            districtDic.Add("ezhou", 0);
            districtDic.Add("鄂州", 0);
            districtDic.Add("enshi", 1);
            districtDic.Add("恩施", 1);
            districtDic.Add("huangshi", 2);
            districtDic.Add("黄石", 2);
            districtDic.Add("huanggang", 3);
            districtDic.Add("黄冈", 3);
            districtDic.Add("jingmen", 4);
            districtDic.Add("荆门", 4);
            districtDic.Add("jingzhou", 5);
            districtDic.Add("荆州", 5);
            districtDic.Add("jianghan", 6);
            districtDic.Add("江汉", 6);
            districtDic.Add("tianmen", 7);
            districtDic.Add("天门", 7);
            districtDic.Add("qianjiang", 8);
            districtDic.Add("潜江", 8);
            districtDic.Add("shiyan", 9);
            districtDic.Add("十堰", 9);
            districtDic.Add("suizhou", 10);
            districtDic.Add("随州", 10);
            districtDic.Add("wuhan", 11);
            districtDic.Add("武汉", 11);
            districtDic.Add("xiangyang", 12);
            districtDic.Add("襄阳", 12);
            districtDic.Add("xianning", 13); 
            districtDic.Add("咸宁", 13);
            districtDic.Add("xiaogan", 14);
            districtDic.Add("孝感", 14);
            districtDic.Add("yichang", 15);
            districtDic.Add("宜昌", 15);
        }

        private int getGroupIndex(string str)
        {
            int index = -1;
            if (groupDic.ContainsKey(str))
            {
                index = groupDic[str];
            }
            else if (validDistrict(str) >= 0)
            {
                index = 7;
            }
            return index;
        }

        private int validDistrict(string str)
        {
            int index = -1;
            if (districtDic.ContainsKey(str))
            {
                index = districtDic[str];
            }
            return index;
        }
    }

    class DIYSQLForOrderAnalyzerTimer : DIYSQLBase
    {
        public DIYSQLForOrderAnalyzerTimer(MainModel mainModel, string strWhere)
            : base(mainModel)
        {
            MainDB = false;
            this.strWhere = "where  " + strWhere;
        }

        private Dictionary<int, List<TimerModel>> models = null;
        public Dictionary<int, List<TimerModel>> Models
        {
            get { return models; }
        }

        protected string strWhere;//查询条件
        protected override string getSqlTextString()
        {

            return "select a.task_id,a.工单当前状态,a.用户组别,a.地市,a.进度状态,a.状态类型,a.插入时间 "
            + "  from MTNOH_AAA_Platform.dbo.task_timer_9 a left join  MTNOH_AAA_Platform.dbo.task_9 b on a.task_id = b._id " + strWhere + "  order by task_id, 插入时间"; 
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            models = new Dictionary<int, List<TimerModel>>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            int taskID = package.Content.GetParamInt();
            string curStatus = package.Content.GetParamString();
            string groupName = package.Content.GetParamString();
            string district = package.Content.GetParamString();
            string timeStatus = package.Content.GetParamString();
            string statusType = package.Content.GetParamString();
            string inserTime = package.Content.GetParamString();

            TimerModel model = new TimerModel();
            model.TaskID = taskID;
            model.CurStatus = curStatus;
            model.CurUser = groupName;
            model.CityName = district;
            model.Status = timeStatus;
            model.StatusType = statusType;
            model.InsertTime = inserTime;

            if (models.ContainsKey(taskID))
            {
                models[taskID].Add(model);
            }
            else
            {
                List<TimerModel> modelTemp = new List<TimerModel>();
                modelTemp.Add(model);
                models[taskID] = modelTemp;
            }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

    }
}
