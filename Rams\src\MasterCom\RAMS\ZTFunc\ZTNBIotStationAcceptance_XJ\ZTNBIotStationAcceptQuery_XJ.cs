﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotStationAcceptQueryXJ : DIYReplayFileQuery
    {
        public NbIotStationAcceptQueryXJ(MainModel mainModel)
            : base(mainModel)
        {
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get
            {
                return "新疆NBIOT单站验收";
            }
        }

        protected Exception errEx;

        #region 回放字段
        protected virtual List<string> queryColumns
        {
            get
            {
                return new List<string>()
                        {
                             "isampleid",
                             "itime",
                             "ilongitude",
                             "ilatitude",
                             "lte_TAC",
                             "lte_ECI",
                             "lte_EARFCN",
                             "lte_PCI",
                             "lte_SCell_LAC",
                             "lte_SCell_CI",
                             "lte_RSRP",
                             "lte_SINR",
                             "lte_MAC_UL",
                             "lte_MAC_DL",
                        };
            }
        }
        #endregion

        /// <summary>
        /// 成功导出Excel文件
        /// </summary>
        protected string exportReportFiles = "";

        /// <summary>
        /// 选择的保存路径
        /// </summary>
        protected string saveFolder = "";

        /// <summary>
        /// 所有文件单验分析后的结果集(基站ENodeBID,CellID,结果标识,结果值)
        /// </summary>
        protected Dictionary<int, Dictionary<int, Dictionary<uint, object>>> btsResultInfo = new Dictionary<int, Dictionary<int, Dictionary<uint, object>>>();

        /// <summary>
        /// 基站信息合集(ENodeBID,对应单验的基站信息)
        /// </summary>
        protected Dictionary<int, NbIotBtsWorkParam> btsInfoIDic = new Dictionary<int, NbIotBtsWorkParam>();

        /// <summary>
        /// 获取回放指标
        /// </summary>
        /// <returns></returns>
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }
            option.EventInclude = true;
            option.MessageInclude = true;
            return option;
        }

        protected override bool isValidCondition()
        {
            saveFolder = string.Empty;
            System.Windows.Forms.FolderBrowserDialog fbd = new System.Windows.Forms.FolderBrowserDialog();
            if (fbd.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                saveFolder = fbd.SelectedPath;
            }
            else
            {
                return false;
            }

            errEx = null;
            btsResultInfo = new Dictionary<int, Dictionary<int, Dictionary<uint, object>>>();
            btsInfoIDic = new Dictionary<int, NbIotBtsWorkParam>();
            return true;
        }

        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            base.query();
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            this.analyzeFile(fileInfo);
            MainModel.DTDataManager.Clear();
        }

        /// <summary>
        /// 文件回放后,进行单站验收
        /// </summary>
        protected virtual void analyzeFile(FileInfo curAnaFileInfo)
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }

            //仅对包含NB的文件单验
            if (!curAnaFileInfo.Name.Contains("-NB"))
            {
                return;
            }

            NbIotStationAcceptManagerXJ manager = new NbIotStationAcceptManagerXJ();
            try
            {
                LTECell lteCell = manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
                if (lteCell != null)
                {
                    saveAnalyzeData(manager, lteCell);
                }
            }
            catch (Exception ex)
            {
                this.errEx = ex;
                throw;
            }
            MainModel.DTDataManager.Clear();
        }

        /// <summary>
        /// 保存文件分析的结果
        /// </summary>
        /// <param name="manager"></param>
        /// <param name="lteCell"></param>
        protected void saveAnalyzeData(NbIotStationAcceptManagerXJ manager, LTECell lteCell)
        {
            if (manager.NBIotAcceptFileInfo != null && manager.NBIotAcceptFileInfo.AcceptKpiDic.Count > 0)
            {
                int eNodeBID = lteCell.BelongBTS.BTSID;
                Dictionary<int, Dictionary<uint, object>> curBTSResult;
                if (!btsResultInfo.TryGetValue(eNodeBID, out curBTSResult))
                {
                    curBTSResult = new Dictionary<int, Dictionary<uint, object>>();
                    //添加基站
                    btsResultInfo.Add(eNodeBID, curBTSResult);
                }

                Dictionary<uint, object> curResult;
                if (!curBTSResult.TryGetValue(lteCell.CellID, out curResult))
                {
                    foreach (var curLTECell in lteCell.BelongBTS.Cells)
                    {
                        //由于存在FDD和NB共站的情况,故只添加单验文件对应的小区(需要工参和文件的小区ID一致)
                        if (curLTECell.CellID == lteCell.CellID)
                        {
                            //结果集添加所有小区
                            Dictionary<uint, object> curCellResult = new Dictionary<uint, object>();
                            curBTSResult.Add(curLTECell.CellID, curCellResult);

                            #region 基站结果信息添加所有小区
                            addBtsCellInfo(manager, eNodeBID, curLTECell);
                            #endregion
                            break;
                        }
                    }
                }
                
                curBTSResult.TryGetValue(lteCell.CellID, out curResult);
                foreach (var item in manager.NBIotAcceptFileInfo.AcceptKpiDic)
                {
                    //如果同一个指标存在多个文件会覆盖之前的结果,等于是保留最后一个文件的结果
                    curResult[item.Key] = item.Value;
                }
            }
        }

        private void addBtsCellInfo(NbIotStationAcceptManagerXJ manager, int eNodeBID, LTECell curLTECell)
        {
            NbIotCellWorkParam cellInfo = new NbIotCellWorkParam();
            cellInfo.CellName = curLTECell.Name;
            cellInfo.CellID = curLTECell.CellID;
            cellInfo.BtsName = GetBtsName(curLTECell, manager.NBIotAcceptFileInfo.FileName);
            cellInfo.Tac = curLTECell.TAC;
            cellInfo.Earfcn = curLTECell.EARFCN;
            cellInfo.Pci = curLTECell.PCI;
            cellInfo.DistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            cellInfo.CoverTypeDes = curLTECell.BelongBTS.TypeStringDesc;
            cellInfo.ENodeBID = eNodeBID;
            cellInfo.Longitude = curLTECell.Longitude;
            cellInfo.Latitude = curLTECell.Latitude;
            cellInfo.Direction = curLTECell.Direction;
            cellInfo.Downward = curLTECell.Downward;
            cellInfo.Altitude = curLTECell.Altitude;
            cellInfo.Eci = curLTECell.ECI;
            cellInfo.SectorID = curLTECell.SectorID;
            NbIotBtsWorkParam btsInfo;
            if (!btsInfoIDic.TryGetValue(cellInfo.ENodeBID, out btsInfo))
            {
                btsInfo = new NbIotBtsWorkParam(cellInfo);
                btsInfoIDic.Add(eNodeBID, btsInfo);
            }
            btsInfo.DateTimeDes = DateTime.Now.ToString();
            btsInfo.AddCellParamsInfo(cellInfo);
        }

        /// <summary>
        /// 由于存在共站导致工参中的基站名可能并非NB站名,故不能直接使用工参中的基站名
        /// 获取对应的NB基站名
        /// </summary>
        /// <param name="lteCell"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public string GetBtsName(LTECell lteCell, string fileName)
        {
            string btsName = lteCell.BelongBTS.Name;
            string btsPrefix = "";
            if (btsName.Contains("-NB"))
            {
                return btsName;
            }
            else if (btsName.Contains("-FDD"))
            {
                //由于存在FDD共站,假如站名为FDD站名时,替换为NB
                btsPrefix = btsName.Substring(0, btsName.LastIndexOf('-'));
            }
            else
            {
                return btsName + "-NB";
            }
            btsName = btsPrefix + "-NB";
            return btsName;
        }

        /// <summary>
        /// 分析完所有文件后调用
        /// </summary>
        protected override void doPostReplayAction()
        {
            WaitTextBox.Show("正在处理数据并导出Excel文件...", afterAnalyzeInThread);
        }

        protected virtual void afterAnalyzeInThread()
        {
            try
            {
                exportReportFiles = "";
                if (this.errEx == null)
                {
                    //按基站循环导出
                    foreach (var item in btsResultInfo)
                    {
                        if (btsInfoIDic[item.Key].IsOutDoorBts)
                        {
                            exportOutdoorBtsReport(item.Value, btsInfoIDic[item.Key]);
                        }
                        else
                        {
                            //室内站暂未处理
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                this.errEx = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        /// <summary>
        /// 导出宏站
        /// </summary>
        /// <param name="bgResultList"></param>
        /// <param name="btsWorkParamInfo"></param>
        protected virtual void exportOutdoorBtsReport(Dictionary<int, Dictionary<uint, object>> resultList, NbIotBtsWorkParam btsWorkParamInfo)
        {
            NbIotOutDoorBtsAcceptInfoXJ curBtsAcceptInfo = NbIotStaionAcceptResultHelperXJ.GetOutDoorBtsResultByData(
                resultList, btsWorkParamInfo);

            if (curBtsAcceptInfo != null)
            {
                bool hasExportReport = NbIotExportOutdoorBtsReportHelperXJ.ExportReports(curBtsAcceptInfo, btsWorkParamInfo, saveFolder);
                if(hasExportReport)
                {
                    exportReportFiles += btsWorkParamInfo.BtsName + ",";
                }
            }
        }
  
        protected override void fireShowResult()
        {
            if (errEx != null)
            {
                System.Windows.Forms.MessageBox.Show(errEx.Message + Environment.NewLine + errEx.StackTrace,
                    this.Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
            else if (string.IsNullOrEmpty(exportReportFiles))
            {
                System.Windows.Forms.MessageBox.Show("Excel导出失败,请检测是否包含单验文件的工参!", this.Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(string.Format("({0})站点的报告导出完成!", exportReportFiles.TrimEnd(',')), this.Name, System.Windows.Forms.MessageBoxButtons.OK);
            }
        }
    }
}
