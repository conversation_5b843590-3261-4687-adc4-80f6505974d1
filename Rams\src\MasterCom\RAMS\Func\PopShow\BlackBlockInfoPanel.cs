﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class BlackBlockInfoPanel : UserControl, PopShowPanelInterface
    {
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 

        private MainModel MainModel;
        public BlackBlockInfoPanel()
        {
            InitializeComponent();
            initShowInfo();
        }

        private void initShowInfo()
        {
            cbxTypeSel.Items.Add("GSM 2G黑点");
            cbxTypeSel.Items.Add("TD AMR黑点");
            cbxTypeSel.Items.Add("TD VP黑点");
            cbxTypeSel.SelectedIndex = 0;

            cbxShowType.Items.Add("最近一周");
            cbxShowType.Items.Add("最近一月");
            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.Items.Add("按天");
            cbxShowType.SelectedIndex = 0;
        }

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            List<BlockEntryItem> blockList = queryBlackBlockEntryFrom(worker, MainModel.User.DBID);
            Dictionary<string, List<BlockEntryItem>> blockTypeDic = new Dictionary<string, List<BlockEntryItem>>();
            blockTypeDic["GSM 2G黑点"] = new List<BlockEntryItem>();//gsm
            blockTypeDic["TD AMR黑点"] = new List<BlockEntryItem>();//td amr
            blockTypeDic["TD VP黑点"] = new List<BlockEntryItem>();//td vp
            foreach(BlockEntryItem block in blockList)
            {
                string key = "";
                switch(block.type)
                {
                    case 1:
                        key = "GSM 2G黑点";
                        break;
                    case 2:
                        key = "TD AMR黑点";
                        break;
                    case 3:
                        key = "TD VP黑点";
                        break;
                    default:
                        break;
                }
                List<BlockEntryItem> typeList = null;
                if(blockTypeDic.TryGetValue(key,out typeList))
                {
                    typeList.Add(block);
                }
            }
            task.retResultInfo = blockTypeDic;
        }

        private List<BlockEntryItem> queryBlackBlockEntryFrom(BackgroundWorker worker, int dbid)
        {
            List<BlockEntryItem> retList = new List<BlockEntryItem>();
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam("tb_popblackblock");
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        BlockEntryItem retItem = BlockEntryItem.ReadResultItemFrom(package.Content);
                        if (dbid == -1)//若为多地市的，需要将片区名修改为地市名
                        {
                            retItem.area_names = DistrictManager.GetInstance().getDistrictName(retItem.dbid);
                            retList.Add(retItem);
                        }
                        else
                        {
                            List<BlockEntryItem> retItemSplitList = splitByAreaName(retItem);
                            retList.AddRange(retItemSplitList);
                        }
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }
        }

        private List<BlockEntryItem> splitByAreaName(BlockEntryItem retItem)
        {
            List<BlockEntryItem> retList = new List<BlockEntryItem>();
            string[] names = retItem.area_names.Split('|');
            for (int i = 0; i < names.Length; i++)
            {
                if(names[i]!="" && !names[i].Contains("中心点"))
                {
                    BlockEntryItem splitItem = retItem.cloneInstance();
                    splitItem.area_names = names[i];
                    retList.Add(splitItem);
                }
            }
            return retList;
        }

        Dictionary<string, List<BlockEntryItem>> curRetDataDic = new Dictionary<string, List<BlockEntryItem>>();
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is Dictionary<string, List<BlockEntryItem>>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, List<BlockEntryItem>>;
            }
            cbxShowType.SelectedIndex = 0;
            refreshShowReport(true);
        }

        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        private void cbxTypeSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport(true);
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport(true);
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport(false);
            freshShowChart(4);
        }
        private void refreshShowReport(bool resetContent)
        {
            string seltype = cbxTypeSel.SelectedItem as string;
            if(seltype==null)
            {
                return;
            }
            List<BlockEntryItem> blockList = null;
            if(curRetDataDic.TryGetValue(seltype,out blockList))
            {
                string selShowType = cbxShowType.SelectedItem as string;
                if(selShowType!=null)
                {
                    List<BlockNumResult> blockShowResult = parseShowFromList(blockList,selShowType);
                    if (resetContent)
                    {
                        Dictionary<string, bool> namesDic = getNameList(blockShowResult);
                        cbxContentType.Items.Clear();
                        cbxContentType.Items.Add("(全部)");
                        foreach (string nm in namesDic.Keys)
                        {
                            cbxContentType.Items.Add(nm);
                        }
                        cbxContentType.SelectedIndex = 0;
                    }
                    showInGrid(blockShowResult);
                }
            }
        }

        private List<BlockNumResult> parseShowFromList(List<BlockEntryItem> blockList, string selShowType)
        {
            BlackBlockInfoHelper helper = new BlackBlockInfoHelper();
            List<BlockNumResult> retList = new List<BlockNumResult>();
            if (selShowType == "按天" || selShowType == "按月" || selShowType == "按周")
            {
                retList = helper.PrepareShowByPeriod(blockList,selShowType);
            }
            else if (selShowType == "最近一月" || selShowType == "最近一周")
            {
                retList = helper.PrepareShowByLastPeriod(blockList, selShowType);
            }
            return retList;
        }

        private Dictionary<string, bool> getNameList(List<BlockNumResult> blockShowResult)
        {
            Dictionary<string, bool> ret = new Dictionary<string, bool>();
            foreach (BlockNumResult numItem in blockShowResult)
            {
                ret[numItem.areaStr] = true;
            }
            return ret;
        }

        private void showInGrid(List<BlockNumResult> blockShowResult)
        {
            dataGridView.Rows.Clear();
            if (blockShowResult.Count > 0)
            {
                int indexRowAt = 0;
                for (int r = 0; r < blockShowResult.Count; r++)
                {
                    BlockNumResult numItem = blockShowResult[r];
                    if (((string)cbxContentType.SelectedItem) == "(全部)" || ((string)cbxContentType.SelectedItem) == numItem.areaStr)
                    {
                        dataGridView.Rows.Add(1);
                        dataGridView.Rows[indexRowAt].Cells[0].Value = numItem.timeStr;
                        dataGridView.Rows[indexRowAt].Cells[1].Value = numItem.areaStr;
                        dataGridView.Rows[indexRowAt].Cells[2].Value = numItem.createdNum;
                        dataGridView.Rows[indexRowAt].Cells[3].Value = numItem.closedNum;
                        dataGridView.Rows[indexRowAt].Cells[4].Value = numItem.remainNum;
                        dataGridView.Rows[indexRowAt].Cells[5].Value = string.Format("{0:f2}", numItem.closedNum * 100.0f / (numItem.closedNum+numItem.remainNum));
                        indexRowAt++;
                    }
                }
            }
        }
        private void freshShowChart(int colIndex)
        {
            string selShowType = cbxShowType.SelectedItem as string;
            if (selShowType == "最近一周" || selShowType == "最近一月")
            {
                showNearestChart(colIndex);
            }
            else if (selShowType == "按周" || selShowType == "按月" || selShowType == "按天")
            {
                string showCont = cbxContentType.SelectedItem as string;
                if (showCont == "(全部)")//显示多个序列
                {
                    showAllChart();
                }
                else
                {
                    showOtherChart(selShowType, showCont);
                }
            }
        }

        private void showNearestChart(int colIndex)
        {
            tChartBlock.Series.Clear();
            Steema.TeeChart.Styles.Bar newBar = new Steema.TeeChart.Styles.Bar();
            newBar.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;

            tChartBlock.Series.Add(newBar);
            tChartBlock.Header.Text = cbxTypeSel.SelectedText;
            tChartBlock.Axes.Bottom.Title.Text = "名称";
            tChartBlock.Axes.Bottom.Labels.Angle = 0;
            tChartBlock.Axes.Left.Title.Text = dataGridView.Columns[colIndex].HeaderText;
            double[] doubles;
            string[] labels;
            extractLabValues(colIndex, 1, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                tChartBlock.Series[0].Add(doubles[i], labels[i]);
            }
        }

        private void showAllChart()
        {
            tChartBlock.Chart.Aspect.View3D = false;
            tChartBlock.Chart.Aspect.Chart3DPercent = 10;
            tChartBlock.Series.Clear();
            Dictionary<string, Steema.TeeChart.Styles.Line> areaSeriesDic = new Dictionary<string, Steema.TeeChart.Styles.Line>();
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                string namelabel = dataGridView.Rows[r].Cells[1].Value.ToString();//名称列
                Steema.TeeChart.Styles.Line nmLine;
                if (!areaSeriesDic.TryGetValue(namelabel, out nmLine))
                {
                    nmLine = new Steema.TeeChart.Styles.Line();
                    nmLine.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;
                    //nmBar.Marks.Visible = false;
                    nmLine.Color = ColorSequenceSupplier.getColor(r);
                    nmLine.Title = namelabel;
                    nmLine.Depth = 2;
                    nmLine.LinePen.Width = 2;
                    tChartBlock.Series.Add(nmLine);
                    areaSeriesDic[namelabel] = nmLine;
                }
                double vdouble = 0;
                string labelStr = "";
                extractLabValue(r, 4, 0, out vdouble, out labelStr);
                nmLine.Add(vdouble, labelStr);
            }
            //tChartKPI.Series.Add()
        }

        private void showOtherChart(string selShowType, string showCont)
        {
            tChartBlock.Chart.Aspect.View3D = true;
            tChartBlock.Chart.Aspect.Chart3DPercent = 10;

            tChartBlock.Header.Text = cbxTypeSel.SelectedText;
            tChartBlock.Axes.Bottom.Title.Text = showCont + " " + selShowType + " 变化情况";
            tChartBlock.Axes.Bottom.Labels.Angle = 45;
            tChartBlock.Axes.Left.Title.Text = "黑点数量";

            tChartBlock.Series.Clear();
            //创建
            Steema.TeeChart.Styles.Bar barCreated = new Steema.TeeChart.Styles.Bar();
            barCreated.Color = Color.Red;
            barCreated.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;
            barCreated.Title = "创建数量";
            tChartBlock.Series.Add(barCreated);
            double[] doubles;
            string[] labels;
            extractLabValues(2, 0, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                barCreated.Add(doubles[i], labels[i]);
            }
            //关闭
            Steema.TeeChart.Styles.Bar barClosed = new Steema.TeeChart.Styles.Bar();
            barClosed.Color = Color.Blue;
            barClosed.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;
            barClosed.Title = "关闭数量";
            tChartBlock.Series.Add(barClosed);
            extractLabValues(3, 0, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                barClosed.Add(doubles[i], labels[i]);
            }
            //总数量
            Steema.TeeChart.Styles.Line lineCount = new Steema.TeeChart.Styles.Line();
            lineCount.Color = Color.Yellow;
            lineCount.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;
            lineCount.Title = "剩余数量";
            tChartBlock.Series.Add(lineCount);
            extractLabValues(4, 0, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                lineCount.Add(doubles[i], labels[i]);
            }
            if (labels.Length > 20)
            {
                barCreated.Marks.Visible = false;
                barClosed.Marks.Visible = false;
                lineCount.Marks.Visible = false;
            }
        }

        private void extractLabValue(int rowAt, int vColumn, int labelColumn, out double doublev, out string labelv)
        {
            doublev = 0;
            object v = dataGridView.Rows[rowAt].Cells[vColumn].Value;
            if (v is int)
            {
                doublev = (int)v;
            }
            else if (v is double)
            {
                doublev = (double)v;
            }
            else if (v is float)
            {
                doublev = (float)v;
            }
            string vlabel = dataGridView.Rows[rowAt].Cells[labelColumn].Value.ToString();
            labelv = vlabel;

        }
        private void extractLabValues(int vColumn, int labelColumn, out double[] doubles, out string[] labels)
        {
            doubles = new double[dataGridView.Rows.Count];
            labels = new string[dataGridView.Rows.Count];
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                object v = dataGridView.Rows[r].Cells[vColumn].Value;
                if (v is int)
                {
                    doubles[r] = (int)v;
                }
                else if (v is double)
                {
                    doubles[r] = (double)v;
                }
                else if (v is float)
                {
                    doubles[r] = (float)v;
                }
                string vlabel = dataGridView.Rows[r].Cells[labelColumn].Value.ToString();
                labels[r] = vlabel;
            }
        }

        private void dataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex >= 2 && e.ColumnIndex < dataGridView.Columns.Count)
            {
                freshShowChart(e.ColumnIndex);
            }
        }

        private void btnPopShow_Click(object sender, EventArgs e)
        {
            if (!splitMain.Panel2Collapsed)
            {
                ChartShowDialogBlackBlock dlg = new ChartShowDialogBlackBlock(this);
                splitMain.Panel2.Controls.Remove(tChartBlock);
                splitMain.Panel2Collapsed = true;
                dlg.AppendTeeChart(tChartBlock);
                dlg.Visible = true;
                dlg.Owner = (Form)this.Parent.Parent.Parent;
            }
        }

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMain.Panel2.Controls.Add(tchart);
            splitMain.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }

        private void btnGoBlackBlockQuery_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MapForm mapform = MainModel.GetInstance().MainForm.GetMapForm();
            if(mapform!=null)
            {
                WelcomForm welcomForm = this.Parent.Parent.Parent as WelcomForm;
                welcomForm.WindowState = FormWindowState.Minimized;
                CondSaverItem csi = new CondSaverItem();
                csi.autoRun = 1;
                csi.timeType = -1;
                csi.targetType = -1;
                csi.taskName = "问题黑点查询";
            }
        }

    }
    internal class BlockNumResult
    {
        public int _timeValue;
        public string timeStr;
        public string areaStr;
        public int createdNum;
        public int closedNum;
        public int remainNum;

        public static IComparer<BlockNumResult> GetCompareByTime()
        {
            if (comparerTime == null)
            {
                comparerTime = new ComparerByTime();
            }
            return comparerTime;
        }
        public class ComparerByTime : IComparer<BlockNumResult>
        {
            public int Compare(BlockNumResult x, BlockNumResult y)
            {
                return x._timeValue - y._timeValue;
            }
        }
        private static IComparer<BlockNumResult> comparerTime;
    };
    internal class BlockEntryItem
    {
        public int dbid;
        public int type;
        public int block_id;
        public int status;
        public int created_date;
        public int closed_date;
        public string area_names;
        public int evtnum;

        internal static BlockEntryItem ReadResultItemFrom(Content content)
        {
            BlockEntryItem blockRet = new BlockEntryItem();
            blockRet.dbid = content.GetParamInt();
            blockRet.type = content.GetParamInt();
            blockRet.block_id = content.GetParamInt();
            blockRet.status = content.GetParamInt();
            blockRet.created_date = content.GetParamInt();
            blockRet.closed_date = content.GetParamInt();
            blockRet.area_names = content.GetParamString();
            blockRet.evtnum = content.GetParamInt();
            return blockRet;
        }

        internal BlockEntryItem cloneInstance()
        {
            BlockEntryItem clone = new BlockEntryItem();
            clone.dbid = dbid;
            clone.type = type;
            clone.block_id = block_id;
            clone.status = status;
            clone.created_date = created_date;
            clone.closed_date = closed_date;
            clone.area_names = area_names;
            clone.evtnum = evtnum;
            return clone;
        }
    };
}
