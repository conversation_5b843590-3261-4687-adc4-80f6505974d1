﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakCellConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRSRP = new System.Windows.Forms.NumericUpDown();
            this.radAndOr = new DevExpress.XtraEditors.RadioGroup();
            this.label8 = new System.Windows.Forms.Label();
            this.numSINRMax = new System.Windows.Forms.NumericUpDown();
            this.chkSINR = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radAndOr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSINRMax)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(228, 134);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(309, 134);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(49, 18);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 83;
            this.label1.Text = "RSRP≤";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(182, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 82;
            this.label3.Text = "dBm";
            // 
            // numRSRP
            // 
            this.numRSRP.Location = new System.Drawing.Point(96, 12);
            this.numRSRP.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRSRP.Name = "numRSRP";
            this.numRSRP.Size = new System.Drawing.Size(80, 21);
            this.numRSRP.TabIndex = 81;
            this.numRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // radAndOr
            // 
            this.radAndOr.EditValue = true;
            this.radAndOr.Enabled = false;
            this.radAndOr.Location = new System.Drawing.Point(218, 16);
            this.radAndOr.Name = "radAndOr";
            this.radAndOr.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "且关系"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "或关系")});
            this.radAndOr.Size = new System.Drawing.Size(100, 53);
            this.radAndOr.TabIndex = 87;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(170, 36);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 85;
            this.label8.Text = "dB";
            // 
            // numSINRMax
            // 
            this.numSINRMax.Enabled = false;
            this.numSINRMax.Location = new System.Drawing.Point(84, 31);
            this.numSINRMax.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSINRMax.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numSINRMax.Name = "numSINRMax";
            this.numSINRMax.Size = new System.Drawing.Size(80, 21);
            this.numSINRMax.TabIndex = 84;
            this.numSINRMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSINRMax.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // chkSINR
            // 
            this.chkSINR.AutoSize = true;
            this.chkSINR.Location = new System.Drawing.Point(21, 35);
            this.chkSINR.Name = "chkSINR";
            this.chkSINR.Size = new System.Drawing.Size(60, 16);
            this.chkSINR.TabIndex = 86;
            this.chkSINR.Text = "SINR≤";
            this.chkSINR.UseVisualStyleBackColor = true;
            this.chkSINR.CheckedChanged += new System.EventHandler(this.chkSINR_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.numSINRMax);
            this.groupBox1.Controls.Add(this.radAndOr);
            this.groupBox1.Controls.Add(this.chkSINR);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Location = new System.Drawing.Point(12, 49);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(372, 77);
            this.groupBox1.TabIndex = 88;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Sinr设置";
            // 
            // WeakCellConditionDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(396, 167);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numRSRP);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "WeakCellConditionDlg";
            this.Text = "弱覆盖小区分析设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radAndOr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSINRMax)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRSRP;
        private DevExpress.XtraEditors.RadioGroup radAndOr;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numSINRMax;
        private System.Windows.Forms.CheckBox chkSINR;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}