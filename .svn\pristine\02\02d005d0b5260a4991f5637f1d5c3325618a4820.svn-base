﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanLTEMod3CellSettingForm : BaseDialog
    {
        public ScanLTEMod3CellSettingForm()
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
        }

        public ScanLTEMod3CellCondition GetCondition()
        {
            ScanLTEMod3CellCondition cond = new ScanLTEMod3CellCondition();
            cond.MinRsrp = (double)numMinRxlev.Value;
            cond.MaxSinr = (double)numMaxSinr.Value;
            cond.DiffRsrp = (double)numDiffRxlev.Value;
            cond.MinSampleCount = (int)numSampleCount.Value;
            return cond;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
