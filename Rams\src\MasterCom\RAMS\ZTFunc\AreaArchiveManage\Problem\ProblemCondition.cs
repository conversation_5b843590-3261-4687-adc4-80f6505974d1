﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem
{
    public class ProblemCondition
    {
        public ProblemCondition()
        {
            List<EventInfo> evts = new List<EventInfo>();
            evts.Add(EventInfoManager.GetInstance()[6]);
            evts.Add(EventInfoManager.GetInstance()[7]);
            evts.Add(EventInfoManager.GetInstance()[8]);
            evts.Add(EventInfoManager.GetInstance()[10]);
            evts.Add(EventInfoManager.GetInstance()[82]);
            evts.Add(EventInfoManager.GetInstance()[907]);
            evts.Add(EventInfoManager.GetInstance()[908]);
            Events = evts;

            this.PoorQualExp = @"{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/Mx_5A01050C}";
            this.PoorQualOperator = RelationalOperator.Less;
            this.PoorQualValue = 98;
            this.WeakCvrNameCM = "移动GSM90覆盖率（%）";
            this.WeakCvrNameCT = "电信CDMA90覆盖率（%）";
            this.WeakCvrNameCU = "联通GSM90覆盖率（%）";
            this.PoorQualName = "RxQual0-4级占比(%)";
            this.WeakCvrExpCM = @"{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}";
            this.WeakCvrExpCU = @"{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}";
            this.WeakCvrExpCT = @"{100*Cx_6E061F/Cx_6E0621}";
            this.WeakCvrValue = 98;
            this.WeakCvrOperator = RelationalOperator.Less;
        }

        private static readonly string cfgName = System.Windows.Forms.Application.StartupPath
            + "\\config\\AreaArchive\\ProbTemplate\\Condition.xml";
        public static bool LoadFromLocal(out  ProblemCondition cond)
        {
            cond = null;
            bool fileExist = false;
            if (fileExist == File.Exists(cfgName))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgName);
                Dictionary<string, object> dic = configFile.GetItemValue("Template", "Options") as Dictionary<string, object>;
                cond = new ProblemCondition();
                cond.Param = dic;
            }
            return fileExist;
        }

        public void Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            System.Xml.XmlElement cfg = configFile.AddConfig("Template");
            configFile.AddItem(cfg, "Options", this.Param);
            configFile.Save(cfgName);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["WeakCvrNameCM"] = WeakCvrNameCM;
                dic["WeakCvrExpCM"] = WeakCvrExpCM;
                dic["WeakCvrNameCU"] = WeakCvrNameCU;
                dic["WeakCvrExpCU"] = WeakCvrExpCU;
                dic["WeakCvrNameCT"] = WeakCvrNameCT;
                dic["WeakCvrExpCT"] = WeakCvrExpCT;
                dic["WeakCvrValue"] = WeakCvrValue;
                dic["WeakCvrOperator"] = WeakCvrOperator.ToString();

                dic["PoorQualName"] = PoorQualName;
                dic["PoorQualExp"] = PoorQualExp;
                dic["PoorQualValue"] = PoorQualValue;
                dic["PoorQualOperator"] = PoorQualOperator.ToString();
                StringBuilder idStr = new StringBuilder();
                foreach (EventInfo evt in Events)
                {
                    idStr.Append(evt.ID + ",");
                }
                dic["Events"] = idStr.ToString().TrimEnd(',');
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                WeakCvrNameCM = value["WeakCvrNameCM"] as string;
                WeakCvrNameCT = value["WeakCvrNameCT"] as string;
                WeakCvrNameCU = value["WeakCvrNameCU"] as string;

                WeakCvrExpCM = value["WeakCvrExpCM"] as string;
                WeakCvrExpCU = value["WeakCvrExpCU"] as string;
                WeakCvrExpCT = value["WeakCvrExpCT"] as string;
                WeakCvrValue = (double)value["WeakCvrValue"];
                WeakCvrOperator = (RelationalOperator)Enum.Parse(typeof(RelationalOperator), value["WeakCvrOperator"] as string);

                PoorQualName = value["PoorQualName"] as string;
                PoorQualExp = value["PoorQualExp"] as string;
                PoorQualValue = (double)value["PoorQualValue"];
                PoorQualOperator = (RelationalOperator)Enum.Parse(typeof(RelationalOperator), value["PoorQualOperator"] as string);

                EventIDDic = new Dictionary<int, bool>();
                events = new List<EventInfo>();
                string ids = value["Events"] as string;
                foreach (string idStr in ids.Split(new char[] { ',' }
                    , StringSplitOptions.RemoveEmptyEntries))
                {
                    int id = int.Parse(idStr);
                    if (EventIDDic.ContainsKey(id))
                    {
                        continue;
                    }
                    EventInfo evt = EventInfoManager.GetInstance()[id];
                    Events.Add(evt);
                    EventIDDic[id] = true;
                }
            }
        }

        private List<EventInfo> events = new List<EventInfo>();
        public List<EventInfo> Events
        {
            get
            { return events; }
            set
            {
                events.Clear();
                EventIDDic = new Dictionary<int, bool>();
                if (value == null)
                {
                    return;
                }
                events = value;
                foreach (EventInfo evt in value)
                {
                    EventIDDic[evt.ID] = true;
                }
            }
        }

        public string WeakCvrNameCM { get; set; }
        public string WeakCvrExpCM
        {
            get;
            set;
        }

        public string WeakCvrNameCU { get; set; }
        public string WeakCvrExpCU
        { get; set; }

        public string WeakCvrNameCT { get; set; }
        public string WeakCvrExpCT
        { get; set; }

        public double WeakCvrValue
        {
            get;
            set;
        }

        public RelationalOperator WeakCvrOperator
        {
            get;
            set;
        }

        public string PoorQualName { get; set; }
        public string PoorQualExp
        {
            get;
            set;
        }

        public double PoorQualValue
        {
            get;
            set;
        }

        public RelationalOperator PoorQualOperator
        {
            get;
            set;
        }

        public Dictionary<int, bool> EventIDDic
        {
            get;
            private set;
        }

        internal ProblemArea Evaluate(KPI_Statistics.KPIDataGroup areaGrp)
        {
            ProblemArea probArea = new ProblemArea(areaGrp.GroupInfo as AreaBase);
            bool isProb = false;
            foreach (EventInfo evtInfo in events)
            {
                string countKey = string.Format("evtIdCount[{0}]", evtInfo.ID - 1);
                double cnt = areaGrp.CalcFormula(CarrierType.ChinaMobile, -1, countKey);
                if (cnt > 0)
                {
                    isProb = true;
                    probArea.AddAbnormlEvent(evtInfo, cnt);
                    probArea.ProbType |= ProblemType.异常事件;
                }
            }

            double cvrRate = areaGrp.CalcFormula(CarrierType.ChinaMobile, -1, this.WeakCvrExpCM);
            double cvrRateCU = areaGrp.CalcFormula(CarrierType.ChinaUnicom, -1, this.WeakCvrExpCU);
            double cvrRateCT = areaGrp.CalcFormula(CarrierType.ChinaTelecom, -1, this.WeakCvrExpCT);
            bool isWorst = cvrRate < cvrRateCU || cvrRate < cvrRateCT;
            bool isWeakCvr = RelationalOperHelper.Evaluate(WeakCvrOperator, cvrRate, WeakCvrValue);
            probArea.CoverCM = cvrRate;
            probArea.CoverCU = cvrRateCU;
            probArea.CoverCT = cvrRateCT;

            double qual = areaGrp.CalcFormula(CarrierType.ChinaMobile, -1, this.PoorQualExp);
            probArea.PoorQual = qual;
            bool isPoorQual = RelationalOperHelper.Evaluate(PoorQualOperator, qual, PoorQualValue);
            if (isWeakCvr)
            {
                isProb = true;
                setProbType(probArea, isWorst, isPoorQual);
            }

            if (!isProb)
            {
                return null;
            }
            else
            {
                return probArea;
            }
        }

        private static void setProbType(ProblemArea probArea, bool isWorst, bool isPoorQual)
        {
            if (!isWorst && !isPoorQual)
            {
                probArea.ProbType |= ProblemType.仅弱覆盖;
            }
            else
            {
                if (isWorst)
                {
                    probArea.ProbType |= ProblemType.弱覆盖且差于竞争对手;
                }
                if (isPoorQual)
                {
                    probArea.ProbType |= ProblemType.弱覆盖且语音质差;
                }
            }
        }
    }
}
