﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRLTECollaborativeAnaByGrid
{
    public static class SerialGridHelper
    {
        public static List<SerialResult> GetSerialGrid(int serialCount
            , Dictionary<ZTNRLTECollaborativeAnaType, List<Result>> typeResultDic)
        {
            List<SerialResult> serialResultList = new List<SerialResult>();
            SerialGridFinder<Result> finder = new SerialGridFinder<Result>();

            foreach (var type in typeResultDic)
            {
                Dictionary<int, List<Result>> idItemsDic = finder.GetSerialGrid(type.Value, true);
                Dictionary<int, List<Result>> validRes = new Dictionary<int, List<Result>>();
                foreach (var item in idItemsDic)
                {
                    if (item.Value.Count >= serialCount)
                    {
                        SerialResult serialResult = new SerialResult();
                        serialResult.TypeDesc = item.Value[0].TypeDesc;
                        serialResult.Index = item.Key;
                        serialResult.ResultList = item.Value;
                        serialResultList.Add(serialResult);
                    }
                }
            }
            return serialResultList;
        }
    }
}
