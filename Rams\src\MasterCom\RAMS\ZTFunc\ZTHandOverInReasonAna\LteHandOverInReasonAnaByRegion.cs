﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverInReasonAnaByRegion : LteHandOverInReasonAnaBase
    {
        private static LteHandOverInReasonAnaByRegion instance = null;
        public static LteHandOverInReasonAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHandOverInReasonAnaByRegion(false);
                    }
                }
            }
            return instance;
        }
        public LteHandOverInReasonAnaByRegion(bool isVoLTE)
            : base()
        {
            this.isVoLTE = isVoLTE;
        }

        public override string Name
        {
            get { return "切换不合理分析(按区域)"; }
        }

    }

    public class LteHandOverInReasonAnaByRegion_FDD : LteHandOverInReasonAnaBase_FDD
    {
        public LteHandOverInReasonAnaByRegion_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "LTE_FDD切换不合理分析(按区域)"; }
        }
    }

    public class VoLteHandOverInReasonAnaByRegion_FDD : VoLteHandOverInReasonAnaBase_FDD
    {
        public VoLteHandOverInReasonAnaByRegion_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "VOLTE_FDD切换不合理分析(按区域)"; }
        }
    }
}
