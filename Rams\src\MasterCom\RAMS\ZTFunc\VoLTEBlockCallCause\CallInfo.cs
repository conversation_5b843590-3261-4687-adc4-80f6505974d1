﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    public class CallInfo
    {
        private CallInfo otherSideCall;
        public CallInfo OtherSideCall
        {
            get { return otherSideCall; }
            set
            {
                otherSideCall = value;
                if (otherSideCall != null)
                {
                    otherSideCall.otherSideCall = this;
                }
            }
        }


        public DateTime EndTime { get; set; }

        private readonly List<Event> events = new List<Event>();
        internal void AddEvent(Model.Event evt)
        {
            events.Add(evt);
        }

        public DateTime BeginTime { get; set; }

        public bool IsBlockCall { get; set; }

        public DateTime BlockTime { get; set; }

        private readonly List<TestPoint> tps = new List<TestPoint>();
        internal void AddTestPoint(Model.TestPoint tp)
        {
            tps.Add(tp);
        }

        private readonly List<Message> messages = new List<Message>();
        public List<Message> Messages
        {
            get { return messages; }
        }
        public void AddMsg(Message msg)
        {
            messages.Add(msg);
        }

        public string MoMtDesc { get; set; }

        public float? RsrpAvg { get; set; }

        public float? SinrAvg { get; set; }

        public float? MultiCvrValueAvg { get; set; }
        public float MultiCvrPer { get; set; }

        public bool IsMultiCvr
        { get; set; }

        public int HoNum { get; set; }

        internal void Evaluate(BlockCallCondition dropCallCond, bool both)
        {
            int rsrpNum = 0;
            float rsrpSum = 0;
            int sinrNum = 0;
            float sinrSum = 0;
            float multiNum = 0;
            float multiMatchNum = 0;
            float multiSum = 0;
            foreach (TestPoint tp in tps)
            {
                dealRsrp(dropCallCond, ref rsrpNum, ref rsrpSum, tp);
                dealSinr(dropCallCond, ref sinrNum, ref sinrSum, tp);
                if ((this.BlockTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds
                     <= dropCallCond.MultiSec)
                {
                    dealmulti(dropCallCond, ref multiNum, ref multiMatchNum, ref multiSum, tp);
                }
            }

            if (multiNum > 0)
            {
                MultiCvrValueAvg = (float)Math.Round(multiSum / multiNum, 2);
                MultiCvrPer = (float)Math.Round(multiMatchNum * 100.0 / multiNum, 2);
            }

            if (rsrpNum > 0)
            {
                this.RsrpAvg = (float)Math.Round(rsrpSum / rsrpNum, 2);
            }
            if (sinrNum > 0)
            {
                this.SinrAvg = (int)Math.Round(sinrSum / sinrNum, 2);
            }

            dealEvts(dropCallCond);

            setBlockCase(dropCallCond);

            if (both && this.otherSideCall != null)
            {
                this.otherSideCall.Evaluate(dropCallCond, false);
            }
        }

        private void dealEvts(BlockCallCondition dropCallCond)
        {
            foreach (Event evt in events)
            {
                if (evt.DateTime > this.BlockTime)
                {
                    break;
                }
                if (evt.ID == 850 || evt.ID == 898)
                {//LTE Intra Handover Request & LTE Inter Handover Request
                    double totalSeconds = (BlockTime - evt.DateTime).TotalSeconds;
                    if (totalSeconds <= dropCallCond.HoSec)
                    {
                        HoNum++;
                    }
                }
            }
        }

        private void dealRsrp(BlockCallCondition dropCallCond, ref int rsrpNum, ref float rsrpSum, TestPoint tp)
        {
            if ((this.BlockTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds
                <= dropCallCond.WeakRsrpSec)
            {
                float? rsrp = tp.GetRxlev();
                if (rsrp != null)
                {
                    rsrpSum += (float)rsrp;
                    rsrpNum++;
                }
            }
        }

        private void dealSinr(BlockCallCondition dropCallCond, ref int sinrNum, ref float sinrSum, TestPoint tp)
        {
            if ((this.BlockTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds
               <= dropCallCond.PoorSinrSec)
            {
                float? sinr;
                if (tp is LTEFddTestPoint)
                {
                    sinr = (float?)tp["lte_fdd_SINR"];
                }
                else
                {
                    sinr = (float?)tp["lte_SINR"];
                }
                if (sinr != null)
                {
                    sinrSum += (float)sinr;
                    sinrNum++;
                }
            }
        }

        private static void dealmulti(BlockCallCondition dropCallCond, ref float multiNum, ref float multiMatchNum, ref float multiSum, TestPoint tp)
        {
            float? rsrp = tp.GetRxlev();
            if (rsrp != null)
            {
                multiNum++;
                int num = 1;
                for (int i = 0; i < 10; i++)
                {
                    float? rsrpN = tp.GetNbRxlev(i);
                    if (rsrpN == null)
                    {
                        break;
                    }
                    if (Math.Abs((float)(rsrpN - rsrp)) <= dropCallCond.MultiBand)
                    {
                        num++;
                    }
                }
                multiSum += num;
                if (num >= dropCallCond.MultiValue)
                {
                    multiMatchNum++;
                }
            }
        }

        private void setBlockCase(BlockCallCondition dropCallCond)
        {
            bool netProb = false;
            foreach (BlockCallCauseBase cause in dropCallCond.CauseSet)
            {
                if (cause.IsSatisfy(this))
                {
                    netProb = true;
                }
            }

            if (!netProb)
            {
                if (this.RsrpAvg <= dropCallCond.WeakRsrp)
                {
                    BlockCause = BlockCallCause.弱覆盖;
                }
                else if (this.SinrAvg <= dropCallCond.PoorSinr)
                {
                    BlockCause = BlockCallCause.质差;
                }
                else if (this.MultiCvrPer >= dropCallCond.MultiPer)
                {
                    BlockCause = BlockCallCause.高重叠覆盖;
                    IsMultiCvr = true;
                }
                else if (this.HoNum >= dropCallCond.HoNum)
                {
                    BlockCause = BlockCallCause.频繁切换;
                }
            }
        }

        public BlockCallCause BlockCause { get; set; } = BlockCallCause.其它;

        public string Suggest
        { get; set; }


        public string FileName { get; set; }

        public Event BlockEvt { get; set; }

        public List<TestPoint> TestPoints { get { return tps; } }
        public List<Event> Events { get { return events; } }

        public Message ErrorMsg { get; set; }
    }

    public enum BlockCallCause
    {
        异常信令,
        VoiceHangup,
        UE_Cancel,
        弱覆盖,
        质差,
        高重叠覆盖,
        频繁切换,
        其它
    }

}
