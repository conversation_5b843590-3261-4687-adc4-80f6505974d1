﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Text.RegularExpressions;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.src.MasterCom.RAMS.Func
{
    public partial class FileCheckForm : MinCloseForm
    {
        public FileCheckForm():base()
        {
            InitializeComponent();
        }

        public bool IsArea { get; set; } = false;
        public bool IsAreaAgent { get; set; } = false;
        public bool IsGrid { get; set; } = false;
        public DevExpress.XtraGrid.Views.Grid.GridView gv { get; set; }
        public void FillData(DevExpress.XtraGrid.Views.Grid.GridView gridview)
        {
            this.gv = gridview;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            List<FileCheck> listGIS = new List<FileCheck>();
            ResvShapeFile areaFile = new ResvShapeFile();
            ResvShapeFile agentFile = new ResvShapeFile();
            ResvShapeFile gridFile = new ResvShapeFile();

            List<string> list = new List<string>();                //记录图层网格信息
            List<string> listName = new List<string>();            //记录文件名
            List<string> listNeed = new List<string>();            //记录未包含在文件中的网格信息
            List<string> listPort = new List<string>();            //记录文件端口信息
            Dictionary<string, string> dcTime = new Dictionary<string, string>();   //记录每一个文件的log时长
            List<string> listInFive = new List<string>();          //记录五环内的网格信息

            #region  校验网格信息
            if (chkArea.Checked)
            {
                areaFile = GISManager.GetInstance().AreaShapeFile;
                IsArea = true;
                if (areaFile == null)
                {
                    MessageBox.Show("请导入片区图层！");
                    return;
                }
            }
            if (chkAreaAgent.Checked)
            {
                agentFile = GISManager.GetInstance().AreaAgentShapeFile;
                IsAreaAgent = true;
                if (agentFile == null)
                {
                    MessageBox.Show("请导入代维分区图层！");
                    return;
                }
            }
            if (chkGrid.Checked)
            {
                gridFile = GISManager.GetInstance().GridShapeFile;
                IsGrid = true;
                if (gridFile == null)
                {
                    MessageBox.Show("请导入网格图层！");
                    return;
                }
            }
            for (int i = 0; i < this.gv.RowCount; i++)
            {
                string fileName = gv.GetRowCellValue(gv.GetRowHandle(i), "Name").ToString();
                string time = gv.GetRowCellValue(gv.GetRowHandle(i), "Duration").ToString();
                listName.Add(fileName);
                dcTime.Add(fileName, time);
            }
            addRegionList(IsArea, areaFile.regionList, list);
            addRegionList(IsAreaAgent, agentFile.regionList, list);
            addRegionList(IsGrid, gridFile.regionList, list);
            addListNeed(list, listName, listNeed);
            #endregion

            #region  校验不同文件是否存在同一网格，同一端口
            addListPort(listName, listPort);

            addIncludeGis(listGIS, list, listName);

            setGisBeginTime(listGIS);

            setGisIsRepeate(listGIS);

            foreach (FileCheck gis in listGIS)
            {
                if (!gis.IsRepeate)
                {
                    gis.BeginTime = "";
                }
            }

            #endregion

            #region  校验每个网格不同项目的不同端口的log时长是否相等
            DataTable dt = getDataTable(list, dcTime);

            setGisIsEquale(listGIS, dt);
            #endregion

            foreach (string net in list)
            {
                setFileCheckNumber(listGIS, net);
            }

            addListGIS(listGIS, list);

            #region  校验五环内的网格是否存在log时长小于3小时，五环外log时长小于2小时
            setListInFive(listGIS, list, listInFive, dt);
            #endregion

            listGIS.Sort();

            gridControl1.DataSource = listGIS;
        }

        private void addRegionList(bool type, List<ResvRegion> regionList, List<string> list)
        {
            if (type)
            {
                foreach (ResvRegion rs in regionList)
                {
                    if (!list.Contains(rs.ToString()))
                    {
                        list.Add(rs.ToString());
                    }
                }
            }
        }

        private void addListNeed(List<string> list, List<string> listName, List<string> listNeed)
        {
            foreach (string str in list)
            {
                for (int j = 0; j < listName.Count; j++)
                {
                    if (listName[j].Contains(str)) break;
                    if (j == listName.Count - 1 && !listNeed.Contains(str))
                    {
                        listNeed.Add(str);
                    }
                }
            }
        }

        private void addListPort(List<string> listName, List<string> listPort)
        {
            foreach (string port in listName)
            {
                string strPort = Regex.Match(port, @"MS\d+_", RegexOptions.IgnoreCase).Value;
                if (strPort == "")
                {
                    strPort = Regex.Match(port, @"\.rcu\(\d+\)", RegexOptions.IgnoreCase).Value;
                }
                listPort.Add(strPort);
            }
        }

        private void addIncludeGis(List<FileCheck> listGIS, List<string> list, List<string> listName)
        {
            foreach (string strName in listName)
            {
                foreach (string strNet in list)
                {
                    FileCheck gis = new FileCheck();
                    gis.Name = strNet;
                    if (strName.ToUpper().Contains(strNet.ToUpper() + ".") || strName.ToUpper().Contains(strNet.ToUpper() + "-") || strName.ToUpper().Contains(strNet.ToUpper() + "_"))
                    {
                        gis.IsInclude = true;
                        gis.Port = Regex.Match(strName, @"MS\d+", RegexOptions.IgnoreCase).Value;
                        if (gis.Port == "")
                        {
                            gis.Port = Regex.Match(strName, @"rcu\(\d+\)", RegexOptions.IgnoreCase).Value;
                        }
                        gis.FileName = strName;
                        gis.Number = 0;
                        listGIS.Add(gis);
                        break;
                    }
                    gis.IsInclude = false;
                }
            }
        }

        private void setGisBeginTime(List<FileCheck> listGIS)
        {
            foreach (FileCheck gis in listGIS)
            {
                for (int i = 0; i < gv.RowCount; i++)
                {
                    if (gis.FileName == gv.GetRowCellValue(gv.GetRowHandle(i), "Name").ToString())
                    {
                        gis.BeginTime = gv.GetRowCellValue(gv.GetRowHandle(i), "BeginTimeString").ToString();
                    }
                }
            }
        }

        private static void setGisIsRepeate(List<FileCheck> listGIS)
        {
            for (int i = 0; i < listGIS.Count - 1; i++)
            {
                for (int j = i + 1; j < listGIS.Count; j++)
                {
                    DateTime timei = Convert.ToDateTime(listGIS[i].BeginTime.ToString());
                    DateTime timej = Convert.ToDateTime(listGIS[j].BeginTime.ToString());
                    TimeSpan nd = timei - timej;
                    double hour = Math.Abs(nd.TotalHours);
                    if (listGIS[i].Name == listGIS[j].Name && listGIS[i].Port == listGIS[j].Port && hour >= 24)
                    {
                        listGIS[i].IsRepeate = true;
                        listGIS[j].IsRepeate = true;
                    }
                }
            }
        }

        private DataTable getDataTable(List<string> list, Dictionary<string, string> dcTime)
        {
            DataTable dt = new DataTable();
            DataColumn dc1 = new DataColumn("Port", Type.GetType("System.String"));
            DataColumn dc2 = new DataColumn("TimeSpan", Type.GetType("System.String"));
            DataColumn dc3 = new DataColumn("Name", Type.GetType("System.String"));
            DataColumn dc4 = new DataColumn("NetName", Type.GetType("System.String"));
            dt.Columns.Add(dc1);
            dt.Columns.Add(dc2);
            dt.Columns.Add(dc3);
            dt.Columns.Add(dc4);
            foreach (var dc in dcTime)
            {
                string port = Regex.Match(dc.Key, @"MS\d+", RegexOptions.IgnoreCase).Value;
                if (port == "")
                {
                    port = Regex.Match(dc.Key, @"rcu\(\d+\)", RegexOptions.IgnoreCase).Value;
                }
                DataRow dr = dt.NewRow();
                foreach (string strNet in list)
                {
                    if (dc.Key.Contains(strNet + ".") || dc.Key.Contains(strNet + "-") || dc.Key.Contains(strNet.ToUpper() + "_"))
                    {
                        dr[3] = strNet;
                    }
                }
                dr[0] = port;
                dr[1] = dc.Value;
                dr[2] = dc.Key;
                dt.Rows.Add(dr);
            }

            return dt;
        }

        private void setGisIsEquale(List<FileCheck> listGIS, DataTable dt)
        {
            string file1 = "";
            string file2 = "";
            int timeSpan = 0;
            if (!string.IsNullOrEmpty(numericUpDownTime.Text.Trim()))
            {
                timeSpan = Convert.ToInt32(numericUpDownTime.Text.Trim());
            }
            foreach (FileCheck gis in listGIS)
            {
                if (gis.FileName == file1 || gis.FileName == file2)
                {
                    gis.IsEquale = true;
                }
                else
                {
                    getFile(dt, ref file1, ref file2, timeSpan, gis);
                }
            }
        }

        private void getFile(DataTable dt, ref string file1, ref string file2, int timeSpan, FileCheck gis)
        {
            DataRow[] dr = dt.Select(string.Format(" NetName='{0}'", gis.Name));
            for (int i = 0; i < dr.Length - 1; i++)
            {
                for (int j = i + 1; j < dr.Length; j++)
                {
                    System.DateTime time1 = Convert.ToDateTime(dr[i]["TimeSpan"].ToString());
                    System.DateTime time2 = Convert.ToDateTime(dr[j]["TimeSpan"].ToString());
                    TimeSpan tp = time1 - time2;
                    int minute = Math.Abs(tp.Minutes);
                    if (minute <= timeSpan
                        && (dr[i]["Name"].ToString() != file1 || dr[j]["Name"].ToString() != file2))
                    {
                        file1 = dr[i]["Name"].ToString();
                        file2 = dr[j]["Name"].ToString();
                        gis.IsEquale = true;
                    }
                }
            }
        }

        private void addListGIS(List<FileCheck> listGIS, List<string> list)
        {
            foreach (string net in list)
            {
                for (int i = 0; i < listGIS.Count; i++)
                {
                    if (listGIS[i].Name == net)
                        break;
                    if (listGIS[i].Name != net && i == listGIS.Count - 1)
                    {
                        FileCheck gis = new FileCheck();
                        gis.Name = net;
                        listGIS.Add(gis);
                    }
                }
            }
        }

        private void setListInFive(List<FileCheck> listGIS, List<string> list, List<string> listInFive, DataTable dt)
        {
            for (int i = 1; i <= 93; i++)
            {
                string net = "网格" + i.ToString();
                if (list.Contains(net + ".") || list.Contains(net + "-") || list.Contains(net + "-"))
                {
                    listInFive.Add(net);
                }
            }
            foreach (DataRow dr in dt.Rows)
            {
                System.DateTime dtm = Convert.ToDateTime(dr["TimeSpan"].ToString());
                if (dtm.Hour < 3 && listInFive.Contains(dr["NetName"].ToString()))
                {
                    setInExit(listGIS, dr);
                }
                else if (dtm.Hour < 2 && !listInFive.Contains(dr["NetName"].ToString()))
                {
                    setInExit(listGIS, dr);
                }
            }
        }

        private void setInExit(List<FileCheck> listGIS, DataRow dr)
        {
            foreach (FileCheck gis in listGIS)
            {
                if (gis.FileName == dr["Name"].ToString())
                {
                    gis.IsInExit = true;
                }
            }
        }

        private void setFileCheckNumber(List<FileCheck> listGIS, string net)
        {
            int i = listGIS.FindAll(delegate (FileCheck n) { return n.Name == net; }).Count;
            if (i > 0)
            {
                foreach (FileCheck gis in listGIS)
                {
                    if (gis.Name == net)
                    {
                        gis.Number = i;
                    }
                }
            }
        }

        private void gridView1_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle >= 0)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView1);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }
    }
}
