﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using System.IO;
using MasterCom.MapSpaceManager.MapSpaceManager;

namespace MasterCom.MapSpaceManager
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            init();
        }

        TreeNode mapRootNode = null;
        private void init()
        {
            treeViewMapLayer.Nodes.Clear();
            mapRootNode = new TreeNode("地图");
            treeViewMapLayer.Nodes.Add(mapRootNode);
            layerInfoPanel.LayerPropertyChanged += new EventHandler(layerPropertyChanged);
        }

        void layerPropertyChanged(object sender,  EventArgs e)
        {
            if (e is VecArg)
            {
                VecArg vArg = e as VecArg;
                applyLayerShowStyle(vArg.vecItem);
            }
        }

        private void tsBtnAddLayer_Click(object sender, EventArgs e)
        {
            if (m_msLoader == null)
            {
                MessageBox.Show("请先创建一个地图空间文件!");
                return;
            }
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "Shape Files (*.shp)|*.shp";
            dlg.RestoreDirectory = true;
            dlg.Multiselect = true;
            if (dlg.ShowDialog(this) == DialogResult.OK)
            {
                List<VecLayerItem> vecList = m_msLoader.VLayerItems;
                string[] filenames = dlg.FileNames;
                for (int i = 0; i < filenames.Length; i++)
                {
                    string filename = filenames[i];
                    if (filename.IndexOf(m_mwgDirPath) != 0)
                    {
                        MessageBox.Show("所选图层文件所在路径必须跟地图空间文件所在路径一致，或在地图空间文件路径的子路径中！\r\n"+filename,"路径错误");
                        continue;
                    }
                    VecLayerItem vLayer = new VecLayerItem();
                    string filesubName = filename.Substring(m_mwgDirPath.Length);
                    string name = Path.GetFileNameWithoutExtension(filename);
                    vLayer.file_path_name = filesubName;
                    vLayer.name = name;
                    Shapefile sf = new Shapefile();
                    if (sf.Open(m_mwgDirPath + @"\" + vLayer.file_path_name, null))
                    {
                        sf.FastMode = true;
                        int hnd = mapControl.AddLayer(sf, true);
                        vLayer._sfile = sf;
                        vLayer._handle = hnd;
                        applyLayerShowStyle(vLayer);
                        vecList.Add(vLayer);
                    }
                    else
                    {
                        MessageBox.Show("未能打开文件" + m_mwgDirPath + @"\" + vLayer.file_path_name);
                        continue;
                    }
                    TreeNode node = new TreeNode(vLayer.name);
                    node.Tag = vLayer;
                    mapRootNode.Nodes.Insert(0, node);
                    treeViewMapLayer.ExpandAll();
                    treeViewMapLayer.SelectedNode = node;
                }
            }
        }


        private void treeViewMapLayer_AfterSelect(object sender, TreeViewEventArgs e)
        {
            TreeNode node = e.Node;
            if (node.Tag is VecLayerItem)
            {
                tsBtnRemoveLayer.Enabled = true;
                layerInfoPanel.Visible = true;

                miBtnUp.Enabled = node.Index > 0;
                miBtnDown.Enabled = node.Index < mapRootNode.Nodes.Count - 1;
                layerInfoPanel.showInfo(this.mapControl,node.Tag as VecLayerItem);
            }
            else
            {
                tsBtnRemoveLayer.Enabled = false;
                layerInfoPanel.Visible = false;
                miBtnDown.Enabled = false;
                miBtnUp.Enabled = false;
            }
        }

        private void tsBtnRemoveLayer_Click(object sender, EventArgs e)
        {
            if (treeViewMapLayer.SelectedNode != null)
            {
                VecLayerItem layer = treeViewMapLayer.SelectedNode.Tag as VecLayerItem;
                if (layer != null)
                {
                    mapControl.RemoveLayer(layer._handle);
                    m_msLoader.VLayerItems.Remove(layer);
                    treeViewMapLayer.SelectedNode.Remove();
                }
            }
        }

        private void miSaveMWS_Click(object sender, EventArgs e)
        {
            fireSaveIt();
            
        }

        private void fireSaveIt()
        {
            if (m_msLoader == null)
            {
                MessageBox.Show("没有待保存的地图空间，尚未创建！");
                return;
            }
            if (m_msLoader != null)
            {
                Extents bounds = mapControl.Extents as Extents;
                m_msLoader.curXMin = bounds.xMin;
                m_msLoader.curXMax = bounds.xMax;
                m_msLoader.curYMin = bounds.yMin;
                m_msLoader.curYMax = bounds.yMax;
            }
            m_msLoader.Save(m_mwgDirPathName);
        }
        private void miSaveAsMWS_Click(object sender, EventArgs e)
        {
            if (m_msLoader == null)
            {
                MessageBox.Show("没有待保存的地图空间，尚未创建！");
                return;
            }
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "名通地图空间 (*.mwg)|*.mwg";
            if (dlg.ShowDialog(this) == DialogResult.OK)
            {
                string pathNew = Path.GetDirectoryName(dlg.FileName);
                if (pathNew != m_mwgDirPath)
                {
                    MessageBox.Show("另存为地图空间，必须在当前地图空间目录上！");
                    return;
                }
                if (m_msLoader != null)
                {
                    Extents bounds = mapControl.Extents as Extents;
                    m_msLoader.curXMin = bounds.xMin;
                    m_msLoader.curXMax = bounds.xMax;
                    m_msLoader.curYMin = bounds.yMin;
                    m_msLoader.curYMax = bounds.yMax;
                }
                m_msLoader.Save(dlg.FileName);
                m_mwgDirPathName = dlg.FileName;
            }
        }

        private void miBtnUp_Click(object sender, EventArgs e)
        {
            if (treeViewMapLayer.SelectedNode != null)
            {
                TreeNode node = treeViewMapLayer.SelectedNode;
                if (mapRootNode.Nodes.Contains(node) && node.Index > 0)
                {
                    TreeNode preNode = node.PrevNode;
                    if (preNode != null)
                    {
                        int layerIdx = mapRootNode.Nodes.Count - (node.Index + 1);
                        node.Remove();
                        mapRootNode.Nodes.Insert(preNode.Index, node);
                        treeViewMapLayer.SelectedNode = node;
                        VecLayerItem layer = node.Tag as VecLayerItem;
                        mapControl.MoveLayerUp(layerIdx);
                        switchMoveLoaderVecLayer(layer, true);
                    }
                }
            }
        }

        private void miBtnDown_Click(object sender, EventArgs e)
        {
            if (treeViewMapLayer.SelectedNode != null)
            {
                TreeNode node = treeViewMapLayer.SelectedNode;
                if (mapRootNode.Nodes.Contains(node))
                {
                    TreeNode nextNode = node.NextNode;
                    if (nextNode != null)
                    {
                        int layerIdx = mapRootNode.Nodes.Count - (node.Index + 1);
                        node.Remove();
                        mapRootNode.Nodes.Insert(nextNode.Index + 1, node);
                        treeViewMapLayer.SelectedNode = node;
                        VecLayerItem layer = node.Tag as VecLayerItem;
                        mapControl.MoveLayerDown(layerIdx);
                        switchMoveLoaderVecLayer(layer,false);
                    }
                }
            }
        }

        private void switchMoveLoaderVecLayer(VecLayerItem layer, bool upward)
        {
            List<VecLayerItem> vecLayerList =  m_msLoader.VLayerItems;
            int oldAt = vecLayerList.IndexOf(layer);
            if (oldAt >= 0)
            {
                if (upward)
                {
                    if (oldAt < vecLayerList.Count - 1)
                    {
                        vecLayerList.Remove(layer);
                        vecLayerList.Insert(oldAt + 1, layer);
                    }
                }
                else
                {
                    if (oldAt > 0)
                    {
                        vecLayerList.Remove(layer);
                        vecLayerList.Insert(oldAt - 1, layer);
                    }
                }
            }
        }

        private void mapControl_OnDrawBackBuffer(object sender, AxMapWinGIS._DMapEvents_OnDrawBackBufferEvent e)
        {

        }

        private void mapControl_MouseMoveEvent(object sender, AxMapWinGIS._DMapEvents_MouseMoveEvent e)
        {

        }

        private void mapControl_ExtentsChanged(object sender, EventArgs e)
        {
            Console.WriteLine("mapControl_ExtentsChanged");
            showMapBoundInfo();
        }

        private void showMapBoundInfo()
        {
            Extents bounds=mapControl.Extents as Extents;
            txtCurMaxLat.Text = bounds.yMax.ToString("f8");
            txtCurMaxLong.Text = bounds.xMax.ToString("f8");
            txtCurMinLat.Text = bounds.yMin.ToString("f8");
            txtCurMinLong.Text = bounds.xMin.ToString("f8");
            
        }
        private String m_mwgDirPath = "";
        private String m_mwgDirPathName = "";
        private MapSpaceLoader m_msLoader = null;
        private void miOpenMWS_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Multiselect = false;
            dlg.Filter = "名通地图空间 (*.mwg)|*.mwg";
            if (dlg.ShowDialog(this) == DialogResult.OK)
            {
                mapControl.RemoveAllLayers();
                string fullname = dlg.FileName;
                m_mwgDirPath = Path.GetDirectoryName(fullname);

                try
                {
                    m_msLoader = new MapSpaceLoader();
                    m_msLoader.Load(fullname);
                    freshShowMapSpace();
                    this.Text = "名通GIS地图空间编辑器 [" + dlg.FileName + "]";
                    m_mwgDirPathName = dlg.FileName;
                }
                catch (Exception ex)
                {
                    MessageBox.Show("载入地图空间错误："+ex);
                }
            }
            
        }

        private void freshShowMapSpace()
        {
            List<VecLayerItem> vecList = m_msLoader.VLayerItems;
            mapRootNode.Nodes.Clear();
            GeoProjection proj = new GeoProjection();
            proj.ImportFromEPSG(4326);  // WGS84
            foreach (VecLayerItem vLayer in vecList)
            {
                if (vLayer._sfile == null)
                {
                    Shapefile sf = new Shapefile();
                    if (sf.Open(m_mwgDirPath + @"\" + vLayer.file_path_name, null))
                    {
                        sf.GeoProjection = proj;
                        sf.GenerateLabels(0, tkLabelPositioning.lpNone, true);
                        sf.Labels.VerticalPosition = tkVerticalPosition.vpAboveAllLayers;
                        sf.Labels.LineOrientation = tkLineLabelOrientation.lorHorizontal;
                        int hnd = mapControl.AddLayer(sf, true);
                        vLayer._sfile = sf;
                        vLayer._handle = hnd;
                        applyLayerShowStyle(vLayer);
                    }
                    else
                    {
                        MessageBox.Show("未能打开文件" + m_mwgDirPath + @"\" + vLayer.file_path_name);
                    }
                    
                }
                TreeNode node = new TreeNode(vLayer.name);
                node.Tag = vLayer;
                mapRootNode.Nodes.Insert(0, node);
            }
            treeViewMapLayer.ExpandAll();
            if (m_msLoader.curXMax > 10)
            {
                Extents extNew = new Extents();
                extNew.SetBounds(m_msLoader.curXMin, m_msLoader.curYMin, 0, m_msLoader.curXMax, m_msLoader.curYMax, 0);
                mapControl.Extents = extNew;
                showMapBoundInfo();
            }
            
        }
        private Dictionary<string, LinePatternItem> patternDic = makeLinePatternDic();

        private static Dictionary<string, LinePatternItem> makeLinePatternDic()
        {
            Dictionary<string, LinePatternItem> dic = new Dictionary<string, LinePatternItem>();
            List<LinePatternItem> linePatternList = LinePatternItem.InitCustomLineType();
            for (int i = 0; i < linePatternList.Count; i++)
            {
                LinePatternItem pattern = linePatternList[i];
                dic[pattern.name] = pattern;
            }
            return dic;
        }
        private void applyLayerShowStyle(VecLayerItem item)
        {
            int hnd = item._handle;
            mapControl.set_LayerVisible(hnd, item.visible==1);
            mapControl.set_ShapeLayerDrawFill(hnd, item.style_bg_fill == 1);
            item._sfile.Categories.Clear();
            if (item.style_bg_fill == 1)
            {
                if (item.style_bg_color_column != null && item.style_bg_color_column!="")
                {
                    //
                    int lbFldIdx = getColumnFieldIndex(item._sfile, item.style_bg_color_column);
                    if (lbFldIdx != -1)
                    {
                        Dictionary<uint, int> categoryByColor = new Dictionary<uint, int>();
                        for (int i = 0; i < item._sfile.NumShapes; i++)
                        {
                            string strv = item._sfile.get_CellValue(lbFldIdx, i) as string;
                            Color bgcolor = parseColorFrom(strv);
                            uint color = (uint)ColorTranslator.ToOle(bgcolor);// your custom function
                            if (!categoryByColor.ContainsKey(color))
                            {
                                string name = item._sfile.Categories.Count.ToString();
                                MapWinGIS.ShapefileCategory cat = item._sfile.Categories.Add(name);
                                if (cat != null)
                                {
                                    cat.DrawingOptions.FillColor = color;
                                    categoryByColor.Add(color, item._sfile.Categories.Count - 1);
                                }
                            }
                        }
                        for (int i = 0; i < item._sfile.NumShapes; i++)
                        {
                            string strv = item._sfile.get_CellValue(lbFldIdx, i) as string;
                            Color bgcolor = parseColorFrom(strv);
                            uint color = (uint)ColorTranslator.ToOle(bgcolor);// your custom function
                            int catIndex = categoryByColor[color];
                            item._sfile.set_ShapeCategory(i, catIndex);
                        }
                    }
                }
                else
                {
                    mapControl.set_ShapeLayerFillTransparency(hnd, item.style_bg_opaque);
                    mapControl.set_ShapeLayerFillColor(hnd, (uint)ColorTranslator.ToOle(item.style_bg_color));
                }

            }
            mapControl.set_ShapeLayerLineWidth(hnd, item.style_line_width);
            mapControl.set_ShapeLayerLineColor(hnd, (uint)ColorTranslator.ToOle(item.style_line_color));
            if (item.is_road_layer == 1)
            {
                item._sfile.SelectionAppearance = tkSelectionAppearance.saDrawingOptions;
                item._sfile.SelectionDrawingOptions.LineWidth = 3;
                item._sfile.SelectionDrawingOptions.LineColor = (uint)ColorTranslator.ToOle(Color.FromArgb(255, 0, 0));
                item._sfile.SelectionDrawingOptions.LineVisible = true;
                item._sfile.SelectionDrawingOptions.LineTransparency = 255;
                //===pattern
                if (item.style_line_patten != null)
                {
                    LinePatternItem linePatternItem = null;
                    if (patternDic.TryGetValue(item.style_line_patten, out linePatternItem))
                    {
                        if (linePatternItem.useLinePatten)
                        {
                            item._sfile.Categories.Clear();
                            ShapefileCategory ct = item._sfile.Categories.Add(item.style_line_patten);
                            ct.DrawingOptions.LinePattern = linePatternItem.linePattern;
                            ct.DrawingOptions.UseLinePattern = true;
                            for (int x = 0; x < item._sfile.NumShapes; x++)
                            {
                                item._sfile.set_ShapeCategory(x, 0);
                            }
                        }
                        else
                        {
                            item._sfile.Categories.Clear();
                            if (item.style_line_patten != "基本")
                            {
                                mapControl.set_ShapeLayerLineWidth(hnd, linePatternItem.lineWidth);
                                mapControl.set_ShapeLayerLineColor(hnd, (uint)ColorTranslator.ToOle(linePatternItem.lineColor));
                            }
                        }
                    }
                }
                
            }
            if (item.visible_range_enable == 1)
            {
                mapControl.set_LayerDynamicVisibility(hnd, true);
                mapControl.set_LayerMinVisibleScale(hnd, item.visible_range_min);
                mapControl.set_LayerMaxVisibleScale(hnd, item.visible_range_max);
            }
            else
            {
                mapControl.set_LayerDynamicVisibility(hnd, false);
            }
            item._sfile.Labels.Clear();
            item._sfile.Labels.ClearCategories();
            if (item.label_show == 1)
            {
                int lbFldIdx = getColumnFieldIndex(item._sfile, item.label_field);
                if (lbFldIdx != -1)
                {
                    LabelCategory lc = item._sfile.Labels.AddCategory("base");
                    for (int i = 0; i < item._sfile.NumShapes; i++)
                    {
                        string text = item._sfile.get_CellValue(lbFldIdx, i).ToString();
                        Shape shp = item._sfile.get_Shape(i);
                        if (item.is_road_layer != 1)
                        {
                            MapWinGIS.Point pnt = shp.Center;
                            item._sfile.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                        }
                        else
                        {
                            int pntCnt = shp.numPoints;
                            if (pntCnt < 10 || shp.Length < 0.05)//小于5公里
                            {
                                MapWinGIS.Point pnt = shp.get_Point((pntCnt + 1) / 2 - 1);
                                item._sfile.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                            }
                            else
                            {
                                int idxCnt = (int)Math.Floor((double)(pntCnt / 20));
                                if (idxCnt == 1)
                                {
                                    MapWinGIS.Point pnt = shp.get_Point((pntCnt + 1) / 2 - 1);
                                    item._sfile.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                                }
                                else
                                {
                                    MapWinGIS.Point lastLablePnt = null;
                                    for (int li = 0; li < idxCnt; li++)
                                    {
                                        MapWinGIS.Point pnt = shp.get_Point(li * 20);
                                        if (lastLablePnt != null)
                                        {
                                            if (Math.Abs(pnt.x - lastLablePnt.x) < 1.2 && Math.Abs(pnt.y - lastLablePnt.y) < 1.2)
                                            {
                                                continue;
                                            }
                                        }
                                        lastLablePnt = pnt;
                                        item._sfile.Labels.AddLabel(text, pnt.x, pnt.y, 0.0, 0);
                                    }
                                }
                            }
                        }
                    }
                    lc.FontColor = (uint)ColorTranslator.ToOle(item.label_font_color);
                    lc.FontSize = item.label_font_size;


                    //int count = 0;
                    //if (item.is_road_layer == 1)
                    //{
                    //    count = item._sfile.GenerateLabels(lbFldIdx, tkLabelPositioning.lpLongestSegement, true);
                    //}
                    //else
                    //{
                    //    count = item._sfile.GenerateLabels(lbFldIdx, tkLabelPositioning.lpCentroid, true);
                    //}
                    //if (count > 0)
                    //{
                    //    if (item._sfile.Labels.GenerateCategories(lbFldIdx, tkClassificationType.ctUniqueValues, 1))
                    //    {
                    //        item._sfile.Labels.ApplyCategories();
                    //        MapWinGIS.ColorScheme scheme = new ColorScheme();
                    //        uint clr = (uint)ColorTranslator.ToOle(item.label_font_color);
                    //        scheme.SetColors(clr, clr);
                    //        item._sfile.Labels.ApplyColorScheme2(tkColorSchemeType.ctSchemeGraduated, scheme, tkLabelElements.leFont);
                    //        for (int i = 0; i < item._sfile.Labels.NumCategories; i++)
                    //        {
                    //            item._sfile.Labels.get_Category(i).FontSize = item.label_font_size;
                    //        }
                    //    }
                    //}


                }
            }
        }

        private Color parseColorFrom(string rgb)
        {
            string[] colorArr = rgb.Split(',');
            if (colorArr.Length == 3)
            {
                int r = 255;
                int g = 255;
                int b = 255;
                int.TryParse(colorArr[0], out r);
                int.TryParse(colorArr[1], out g);
                int.TryParse(colorArr[2], out b);
                return Color.FromArgb(int.Parse(colorArr[0]), int.Parse(colorArr[1]), int.Parse(colorArr[2]));
            }
            return Color.White;
        }
        private int getColumnFieldIndex(Shapefile sfile, string column)
        {
            int fdIndex = -1;
            for (int fd = 0; fd < sfile.NumFields; fd++)
            {
                MapWinGIS.Field field = sfile.get_Field(fd);
                if (field.Name == column)
                {
                    fdIndex = fd;
                    break;
                }
            }
            return fdIndex;
        }

        private void btnFullExtent_Click(object sender, EventArgs e)
        {
            mapControl.ZoomToMaxExtents();
        }

        private void tsBtnNewSpace_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "名通地图空间 (*.mwg)|*.mwg";
            if (dlg.ShowDialog(this) == DialogResult.OK)
            {
                m_msLoader = new MapSpaceLoader();
                m_mwgDirPath = Path.GetDirectoryName(dlg.FileName);
                m_msLoader.Save(dlg.FileName);
                this.Text = "MTGis 地图空间管理 [" + dlg.FileName + "]";
                m_mwgDirPathName = dlg.FileName;
            }
        }

        private void btnSaveIt_Click(object sender, EventArgs e)
        {
            fireSaveIt();
        }

        

    }
}
