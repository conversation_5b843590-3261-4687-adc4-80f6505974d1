﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public class NRWrongCoverCause : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get { return "覆盖不符"; }
        }
        [NonSerialized]
        private ICell serverCell = null;
        public int Angle { get; set; } = 60;
        public override string Desc
        {
            get
            {
                return string.Format("采样点不在小区的正负{0}主瓣方向内", Angle);
            }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("核查小区{0}工参的方向角与实际覆盖方向", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                NRCell cell = pnt.GetMainCell_NR();
                if (cell == null)
                {
                    return;
                }
                if (!MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, pnt.Longitude, pnt.Latitude, cell.Direction, Angle))
                {
                    NRWrongCoverCause cln = this.Clone() as NRWrongCoverCause;
                    segItem.SetReason(new NRLowSpeedPointDetail(pnt, cln, nRCond));
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["angle"] = this.Angle;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Angle = (int)value["angle"];
            }
        }
    }
}
