﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class TrackAreaUpdateCause : CauseBase
    {
        public override string Name
        {
            get { return "位置更新"; }
        }

        public int Second { get; set; } = 10;
        public override string Desc
        {
            get { return string.Format("判断低速率发生前{0}秒是否出现Track Area Update Attempt或者Track Area Update Fail事件", Second); }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            //循环低速率采样点
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }

                int bTime = pnt.Time - Second;
                foreach (Event evt in evts)
                {
                    //Track Area Update Attempt或者Track Area Update Fail事件,事件发生时间在低速率的前n秒内
                    if ((evt.ID == 3171 || evt.ID == 3173) && bTime <= evt.Time && evt.Time <= pnt.Time)
                    {
                        TrackAreaUpdateCause cln = this.Clone() as TrackAreaUpdateCause;
                        segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                        break;
                    }
                }
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["second"] = this.Second;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["second"];
            }
        }
    }
}
