﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.Serialization;

using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    [Serializable()]
    public class MapFormJoinHandoverAnalysisLayer : CustomDrawLayer
    {
        static MapFormJoinHandoverAnalysisLayer()
        {
            
        }

        public MapFormJoinHandoverAnalysisLayer(MapOperation mp, string name)
            : base(mp, name)

        {
           
        }

        //List<JoinAwitchingAnalysis> analysisList = new List<JoinAwitchingAnalysis>();

        //public List<JoinAwitchingAnalysis> AnalysisList
        //{
        //    get { return analysisList; }
        //    set { analysisList = value; }
        //}
          
        public string IMon { get; set; }

        //Dictionary<Cell, List<Cell>> cellMap = new Dictionary<Cell, List<Cell>>();
        // ///<summary>
        // ///获取对应小区关系
        // ///</summary>
        //public void GetCell()
        //{
        //    cellMap.Clear();
        //    DateTime time = Convert.ToDateTime(iMon);
        //    //Dictionary<Cell, List<Cell>> cellMap = new Dictionary<Cell, List<Cell>>();
        //    foreach (JoinAwitchingAnalysis analysis in mainModel.SwitchingAnalysisList)
        //    {
        //        DateTime dateTime = Convert.ToDateTime("2012-08");
        //        Cell cell = mainModel.CellManager.GetCell(dateTime, (ushort)analysis.ILac, (ushort)analysis.ICi);
        //       // Cell cell = mainModel.CellManager.GetCell(dateTime, (ushort)analysis.ILac, (ushort)analysis.ICi);
        //        Cell TargetCell = mainModel.CellManager.GetCell(Convert.ToDateTime("2012-08"), (ushort)analysis.ITargetLac, (ushort)analysis.ITargetCi);
        //        if (cell == null)
        //        {
        //            continue;
        //        }
        //        if (!cellMap.ContainsKey(cell))
        //        {
        //            List<Cell> cellList = new List<Cell>();
        //            cellList.Add(TargetCell);
        //            cellMap.Add(cell, cellList);
        //        }
        //        else
        //        {
        //            cellMap[cell].Add(TargetCell);
        //        }
        //    }
        //   // return cellMap;
        //}

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (mainModel.HandoverAnalysisList.Count > 0)
            {
                System.Drawing.Drawing2D.AdjustableArrowCap lineCap = new System.Drawing.Drawing2D.AdjustableArrowCap(3, 5, true);

                foreach (JoinHandoverAnalysis analysis in mainModel.HandoverAnalysisList)
                {
                    string date = analysis.IMon.ToString().Substring(0, 4) + "-" + analysis.IMon.ToString().Substring(4, 2);
                    DateTime dateTime = Convert.ToDateTime(date);
                    Cell cell = mainModel.CellManager.GetCell(dateTime, (ushort)analysis.ILac, (ushort)analysis.ICi);
                    Cell targetCell = mainModel.CellManager.GetCell(dateTime, (ushort)analysis.ITargetLac, (ushort)analysis.ITargetCi);
                    if (cell != null && targetCell != null)
                    {
                        DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                        PointF point1;
                        Map.ToDisplay(dPoint, out point1);
                        DbPoint targetPoint = new DbPoint(targetCell.EndPointLongitude, targetCell.EndPointLatitude);
                        PointF point2;
                        Map.ToDisplay(targetPoint, out point2);
                        Pen pen = getPen(analysis);
                        pen.CustomEndCap = lineCap;
                        graphics.DrawLine(pen, point1, point2);
                    }

                    TDCell tdCell = mainModel.CellManager.GetTDCell(dateTime, analysis.ILac, analysis.ICi);
                    TDCell tdTargetCell = mainModel.CellManager.GetTDCell(dateTime, analysis.ITargetLac, analysis.ITargetCi);
                    if (tdCell != null && tdTargetCell != null)
                    {
                        DbPoint dPoint = new DbPoint(tdCell.EndPointLongitude, tdCell.EndPointLatitude);
                        PointF point1;
                        Map.ToDisplay(dPoint, out point1);
                        DbPoint targetPoint = new DbPoint(tdTargetCell.EndPointLongitude, tdTargetCell.EndPointLatitude);
                        PointF point2;
                        Map.ToDisplay(targetPoint, out point2);
                        Pen pen = getPen(analysis);
                        pen.CustomEndCap = lineCap;
                        graphics.DrawLine(pen, point1, point2);
                    }
                }
            }

            //GetCell();
            //if (cellMap.Count > 0)
            //{
            //    System.Drawing.Drawing2D.AdjustableArrowCap lineCap =
            //         new System.Drawing.Drawing2D.AdjustableArrowCap(4, 8, true);
            //    Pen pen = new Pen(Color.Red, 3);
            //    System.Drawing.Drawing2D.LineCap.=new 
            //    pen.CustomEndCap = lineCap;
            //     pen.EndCap = System.Drawing.Drawing2D.LineCap.ArrowAnchor;

            //    foreach (Cell cell in cellMap.Keys)
            //    {
            //        MapInfo.Geometry.DPoint dPoint = new MapInfo.Geometry.DPoint(cell.Longitude, cell.Latitude);
            //        PointF point1;
            //        Map.DisplayTransform.ToDisplay(dPoint, out point1);

            //        foreach (Cell targetCell in cellMap[cell])
            //        {
            //            if (targetCell != null)
            //            {
            //                MapInfo.Geometry.DPoint targetPoint = new MapInfo.Geometry.DPoint(targetCell.Longitude, targetCell.Latitude);
            //                PointF point2;
            //                Map.DisplayTransform.ToDisplay(targetPoint, out point2);
            //                graphics.DrawLine(pen, point1, point2);
            //                pen.Dispose();

            //            }
            //        }
            //    }

            //}

        }

        private static Pen getPen(JoinHandoverAnalysis analysis)
        {
            Color color = Color.Green;
            switch (analysis.ILevel)
            {
                case 1:
                    color = Color.MediumSeaGreen;
                    break;
                case 2:
                    color = Color.DodgerBlue;
                    break;
                case 3:
                    color = Color.DarkOrange;
                    break;
                case 4:
                    color = Color.Red;
                    break;
            }
            Pen pen = new Pen(color, 2);
            return pen;
        }
    }
}
