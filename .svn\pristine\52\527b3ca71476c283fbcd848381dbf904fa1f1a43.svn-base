﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CountryInvestigationPlanInfoForm : BaseForm
    {
        List<DistrictCountryInvestigation> disCIList;
        int allCount = 0;

        public CountryInvestigationPlanInfoForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<DistrictCountryInvestigation> disCIList)
        {
            disCIList.Sort();
            this.disCIList = disCIList;
            getCount();
            gridControl1.DataSource = disCIList;
            gridControl1.RefreshDataSource();
        }

        private void getCount()
        {
            foreach (DistrictCountryInvestigation districtCI in disCIList)
            {
                allCount += districtCI.DistrictCIList.Count;
            }
        }

        private void ToolStripMenuItemExportToTXT_Click(object sender, EventArgs e)
        {
            exportToTXT();
        }

        private void exportToTXT()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Text File (*.txt)|*.txt";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    WaitBox.Show("正在导出到Txt...", doExport, dlg.FileName);
                    MessageBox.Show("Txt导出完成！");
                }
                catch (System.Exception e)
                {
                    MessageBox.Show("导出到Txt出错：" + e.Message);
                }
            }
        }

        private void doExport(object nameObj)
        {
            string dir = System.IO.Path.GetDirectoryName(nameObj.ToString());
            string fileName = System.IO.Path.GetFileNameWithoutExtension(nameObj.ToString());
            DateTime dt = System.DateTime.Now;
            string path = System.IO.Path.Combine(dir,
                string.Format("{0}_{1}-{2}-{3}-{4}-{5}-{6}.txt", fileName, dt.Year, dt.Month, dt.Day, dt.Hour, dt.Minute, dt.Second));

            System.IO.FileStream fileStream = new System.IO.FileStream(path, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeContent(streamWriter);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
                WaitBox.Close();
            }
        }

        private void writeContent(System.IO.StreamWriter streamWriter)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("|");
            sb.Append("移动GSM|");
            sb.Append("移动TD|");
            sb.Append("联通GSM|");
            sb.Append("联通WCDMA|");
            sb.Append("电信");
            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);

            sb.Append("序号|");
            sb.Append("覆盖目标编号|");
            sb.Append("省|");
            sb.Append("地市|");
            sb.Append("区/县|");
            sb.Append("乡镇|");
            sb.Append("村庄|");
            sb.Append("经度|");
            sb.Append("纬度|");
            sb.Append("场景类型|");
            sb.Append("覆盖人口|");
            sb.Append("覆盖率(-90)|");
            sb.Append("覆盖率(-85)|");
            sb.Append("最大|");
            sb.Append("最小|");
            sb.Append("平均|");
            sb.Append("CGI-1|");
            sb.Append("占比|");
            sb.Append("CGI-2|");
            sb.Append("占比|");
            sb.Append("CGI-3|");
            sb.Append("占比|");
            sb.Append("覆盖率(-90)|");
            sb.Append("覆盖率(-85)|");
            sb.Append("最大|");
            sb.Append("最小|");
            sb.Append("平均|");
            sb.Append("CGI-1|");
            sb.Append("占比|");
            sb.Append("CGI-2|");
            sb.Append("占比|");
            sb.Append("CGI-3|");
            sb.Append("占比|");
            sb.Append("覆盖率(-90)|");
            sb.Append("覆盖率(-85)|");
            sb.Append("最大|");
            sb.Append("最小|");
            sb.Append("平均|");
            sb.Append("CGI-1|");
            sb.Append("占比|");
            sb.Append("CGI-2|");
            sb.Append("占比|");
            sb.Append("CGI-3|");
            sb.Append("占比|");
            sb.Append("覆盖率(-90)|");
            sb.Append("覆盖率(-85)|");
            sb.Append("最大|");
            sb.Append("最小|");
            sb.Append("平均|");
            sb.Append("CGI-1|");
            sb.Append("占比|");
            sb.Append("CGI-2|");
            sb.Append("占比|");
            sb.Append("CGI-3|");
            sb.Append("占比|");
            sb.Append("覆盖率(-90)|");
            sb.Append("覆盖率(-85)|");
            sb.Append("最大|");
            sb.Append("最小|");
            sb.Append("平均|");
            sb.Append("CGI-1|");
            sb.Append("占比|");
            sb.Append("CGI-2|");
            sb.Append("占比|");
            sb.Append("CGI-3|");
            sb.Append("占比");
            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 0;

            foreach (DistrictCountryInvestigation districtCI in disCIList)
            {
                foreach (CountryInvestigation ci in districtCI.DistrictCIList)
                {
                    sb.Append(ci.SN + "|");
                    sb.Append(ci.CoverNum + "|");
                    sb.Append(ci.Province + "|");
                    sb.Append(ci.City + "|");
                    sb.Append(ci.Country + "|");
                    sb.Append(ci.Town + "|");
                    sb.Append(ci.Village + "|");
                    sb.Append(ci.MidLong + "|");
                    sb.Append(ci.MidLat + "|");
                    sb.Append(ci.SceneType + "|");
                    sb.Append(ci.CoverPopulation + "|");

                    sb.Append(ci.GSMYD_90Coverage + "|");
                    sb.Append(ci.GSMYD_85Coverage + "|");
                    sb.Append(ci.GSMYD_RxlevMax + "|");
                    sb.Append(ci.GSMYD_RxlevMin + "|");
                    sb.Append(ci.GSMYD_RxlevMean + "|");
                    sb.Append(ci.GSMYD_CGI_1 + "|");
                    sb.Append(ci.TransDigit(ci.GSMYD_CGI_1_Rage) + "|");
                    sb.Append(ci.GSMYD_CGI_2 + "|");
                    sb.Append(ci.TransDigit(ci.GSMYD_CGI_2_Rage) + "|");
                    sb.Append(ci.GSMYD_CGI_3 + "|");
                    sb.Append(ci.TransDigit(ci.GSMYD_CGI_3_Rage) + "|");

                    sb.Append(ci.TD_90Coverage + "|");
                    sb.Append(ci.TD_85Coverage + "|");
                    sb.Append(ci.TD_RscpMax + "|");
                    sb.Append(ci.TD_RscpMin + "|");
                    sb.Append(ci.TD_RScpMean + "|");
                    sb.Append(ci.TDYD_CGI_1 + "|");
                    sb.Append(ci.TransDigit(ci.TDYD_CGI_1_Rage) + "|");
                    sb.Append(ci.TDYD_CGI_2 + "|");
                    sb.Append(ci.TransDigit(ci.TDYD_CGI_2_Rage) + "|");
                    sb.Append(ci.TDYD_CGI_3 + "|");
                    sb.Append(ci.TransDigit(ci.TDYD_CGI_3_Rage) + "|");

                    sb.Append(ci.GSMLT_90Coverage + "|");
                    sb.Append(ci.GSMLT_85Coverage + "|");
                    sb.Append(ci.GSMLT_RxlevMax + "|");
                    sb.Append(ci.GSMLT_RxlevMin + "|");
                    sb.Append(ci.GSMLT_RxlevMean + "|");
                    sb.Append(ci.GSMLT_CGI_1 + "|");
                    sb.Append(ci.TransDigit(ci.GSMLT_CGI_1_Rage) + "|");
                    sb.Append(ci.GSMLT_CGI_2 + "|");
                    sb.Append(ci.TransDigit(ci.GSMLT_CGI_2_Rage) + "|");
                    sb.Append(ci.GSMLT_CGI_3 + "|");
                    sb.Append(ci.TransDigit(ci.GSMLT_CGI_3_Rage) + "|");

                    sb.Append(ci.W_90Coverage + "|");
                    sb.Append(ci.W_85Coverage + "|");
                    sb.Append(ci.W_RscpMax + "|");
                    sb.Append(ci.W_RscpMin + "|");
                    sb.Append(ci.W_RscpMean + "|");
                    sb.Append(ci.WLT_CGI_1 + "|");
                    sb.Append(ci.TransDigit(ci.WLT_CGI_1_Rage) + "|");
                    sb.Append(ci.WLT_CGI_2 + "|");
                    sb.Append(ci.TransDigit(ci.WLT_CGI_2_Rage) + "|");
                    sb.Append(ci.WLT_CGI_3 + "|");
                    sb.Append(ci.TransDigit(ci.WLT_CGI_3_Rage) + "|");

                    sb.Append(ci.CDMA_90Coverage + "|");
                    sb.Append(ci.CDMA_85Coverage + "|");
                    sb.Append(ci.CDMA_RxAgc0Max + "|");
                    sb.Append(ci.CDMA_RxAgc0Min + "|");
                    sb.Append(ci.CDMA_RxAgc0Mean + "|");
                    sb.Append(ci.CDMADX_CGI_1 + "|");
                    sb.Append(ci.TransDigit(ci.CDMADX_CGI_1_Rage) + "|");
                    sb.Append(ci.CDMADX_CGI_2 + "|");
                    sb.Append(ci.TransDigit(ci.CDMADX_CGI_2_Rage) + "|");
                    sb.Append(ci.CDMADX_CGI_3 + "|");
                    sb.Append(ci.TransDigit(ci.CDMADX_CGI_3_Rage));

                    streamWriter.WriteLine(sb.ToString());
                    sb.Remove(0, sb.Length);

                    iLoop++;
                    WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / allCount);
                }
            }
        }
    }
}
