﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.NOP
{
    public class ProcRoutineManager2017 : ProcRoutineManager
    {
        private bool inited = false;
        protected ProcRoutineManager2017()
        {
        }


        private static ProcRoutineManager2017 instance = null;
        public new static ProcRoutineManager2017 Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ProcRoutineManager2017();
                }
                return instance;
            }
        }

        public override void Init()
        {
            if (inited)
            {
                return;
            }
            IdProcDic = new Dictionary<int, ProcRoutine>();
            IdProcRelationDic = new Dictionary<int, ProcRelation>();
            NameProcDic = new Dictionary<string, List<ProcRoutine>>();
            try
            {
                QueryProcNodeCfg qryProc = new QueryProcNodeCfg(true);
                qryProc.Query();

                QueryProcRelationCfg qryR = new QueryProcRelationCfg(true);
                qryR.Query();
                inited = true;
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

    }
}
