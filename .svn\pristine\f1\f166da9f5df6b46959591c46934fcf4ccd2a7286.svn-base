﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Utils;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Func
{
    public partial class LTETMAnaForm : MinCloseForm
    {
        public LTETMAnaForm()
        {
            InitializeComponent();
        }

        List<TMPointsSet> tmList1 = new List<TMPointsSet>();
        List<TMPointsSet> tmList2 = new List<TMPointsSet>();

        TMCondition tmCondition = new TMCondition();
        Dictionary<Range, Dictionary<int, TMPointsSpeed>> tmSpeedDic = new Dictionary<Range, Dictionary<int, TMPointsSpeed>>();
        public void FillData(TMCondition tmCondition, Dictionary<Range, Dictionary<int, TMPointsSpeed>> tmSpeedDic)
        {
            this.tmCondition = tmCondition;
            this.tmSpeedDic = tmSpeedDic;
            tmList1 = new List<TMPointsSet>();
            tmList2 = new List<TMPointsSet>();
            updateForm();
        }
        private void updateForm()
        {
            Series series1;
            chartControl1.Series.Clear();
            foreach (string strTm in tmCondition.TMList)
            {
                TMPointsSet tmp = new TMPointsSet(strTm);
                int index = 0;
                chartControl1.Series.Insert(index, new Series(strTm, new ViewType()));
                series1 = chartControl1.Series[index];
                series1.PointOptions.ValueNumericOptions.Format = NumericFormat.Percent;
                series1.PointOptions.ValueNumericOptions.Precision = 2;
                foreach (Range sinrRange in tmCondition.RangeValues.Values)
                {
                    addRangePointSet(series1, strTm, tmp, sinrRange);
                }
                tmp.GetPSetPer();
                tmList2.Add(tmp);
            }

            Series series2;
            chartControl2.Series.Clear();
            foreach (Range sinrRange in tmCondition.RangeValues.Values)
            {
                TMPointsSet tmp = new TMPointsSet(sinrRange.ToString());
                int index = 0;
                chartControl2.Series.Insert(index, new Series(sinrRange.ToString(), new ViewType()));
                series2 = chartControl2.Series[index];
                series2.PointOptions.ValueNumericOptions.Format = NumericFormat.Percent;
                series2.PointOptions.ValueNumericOptions.Precision = 2;
                foreach (string strTm in tmCondition.TMList)
                {
                    addTMPointSet(series2, sinrRange, tmp, strTm);
                }
                tmp.GetPSetPer();
                tmList1.Add(tmp);
            }
            gridControl1.DataSource = tmList1;
            gridControl1.RefreshDataSource();
            gridControl2.DataSource = tmList2;
            gridControl2.RefreshDataSource();
        }

        private void addRangePointSet(Series series1, string strTm, TMPointsSet tmp, Range sinrRange)
        {
            int tm = Convert.ToInt32(strTm.Remove(0, 2));
            if (tmSpeedDic.ContainsKey(sinrRange) && tmSpeedDic[sinrRange].ContainsKey(tm))
            {
                int sumCount = 0;
                foreach (var tmCountDic in tmSpeedDic[sinrRange])
                {
                    sumCount += tmCountDic.Value.SamplePointCount;
                }
                double per = Math.Round(((float)tmSpeedDic[sinrRange][tm].SamplePointCount / (float)sumCount), 4);
                series1.Points.Add(new SeriesPoint(sinrRange.ToString(), per));

                PointsSet pSet = new PointsSet(sinrRange.ToString(), tmSpeedDic[sinrRange][tm].SamplePointCount);
                TMPointsSpeed tmpSpeed = tmSpeedDic[sinrRange][tm];
                pSet.PointsSpeed = tmpSpeed;
                pSet.GetAverageSpeed();
                tmp.PointSet.Add(pSet);
            }
        }

        private void addTMPointSet(Series series2, Range sinrRange, TMPointsSet tmp, string strTm)
        {
            int tm = Convert.ToInt32(strTm.Remove(0, 2));
            if (tmSpeedDic.ContainsKey(sinrRange) && tmSpeedDic[sinrRange].ContainsKey(tm))
            {
                int sumCount = 0;

                foreach (var item in tmSpeedDic)
                {
                    if (item.Value.ContainsKey(tm))
                    {
                        sumCount += item.Value[tm].SamplePointCount;
                    }
                }
                double per = Math.Round(((float)tmSpeedDic[sinrRange][tm].SamplePointCount / (float)sumCount), 4);
                series2.Points.Add(new SeriesPoint(strTm, per));

                PointsSet pSet = new PointsSet(strTm, tmSpeedDic[sinrRange][tm].SamplePointCount);
                TMPointsSpeed tmpSpeed = tmSpeedDic[sinrRange][tm];
                pSet.PointsSpeed = tmpSpeed;
                pSet.GetAverageSpeed();
                tmp.PointSet.Add(pSet);
            }
        }

        List<NPOIRow> rows = new List<NPOIRow>();
        private void getNPOIRowsByGridView(List<TMPointsSet> tmList, GridView gv1, GridView gv2)
        {
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in gv1.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gv2.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < tmList.Count; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(tmList[i].TypeName);
                row.AddCellValue(tmList[i].SumCount);
                for (int j = 0; j < tmList[i].PointSet.Count; j++)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(tmList[i].PointSet[j].TypeName);
                    subRow.AddCellValue(tmList[i].PointSet[j].Percent);
                    subRow.AddCellValue(tmList[i].PointSet[j].Count);
                    subRow.AddCellValue(tmList[i].PointSet[j].AvgDownSpeed);
                    subRow.AddCellValue(tmList[i].PointSet[j].AvgUploadSpeed);
                }
            }
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            rows = new List<NPOIRow>();
            getNPOIRowsByGridView(tmList1, this.gridView1, this.gridView2);
            rows.Add(new NPOIRow());
            rows.Add(new NPOIRow());
            getNPOIRowsByGridView(tmList2, this.gridView3, this.gridView4);
            ExcelNPOIManager.ExportToExcel(rows);
        }
    }
}
