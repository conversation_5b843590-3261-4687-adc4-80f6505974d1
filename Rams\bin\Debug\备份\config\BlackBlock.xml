﻿<?xml version="1.0"?>
<Configs>
	<Config name="BlackBlockConfig">
		<Item name="BlackBlocks" typeName="IList">
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTE3</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">2007,2048,2062</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">13,36</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">30</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,5</Item>
					<Item typeName="String" key="ServiceType">35</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTE41</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">866,1271,1429,1548,1428,1550</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">1,7</Item>
					<Item typeName="Boolean" key="BlackBlock">True</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">3</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">3</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">3</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,19</Item>
					<Item typeName="String" key="ServiceType">33,34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTE5</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">866,1271,1429</Item>
					<Item typeName="String" key="ValidTestProjectIDs">53</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,63</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,19</Item>
					<Item typeName="String" key="ServiceType">33,34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTEVOLTE</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">866,877,1078,1079,1080,1147,1424</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">36,60</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">2</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">2</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">2</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,19</Item>
					<Item typeName="String" key="ServiceType">43,51</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
		  <Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">GSMSQ</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">6,7,10,42,43,400</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">3</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">3</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">3</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3</Item>
					<Item typeName="String" key="ServiceType">1,2,22</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">GSMXC</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">6,7,10,42,43,400</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,63</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3</Item>
					<Item typeName="String" key="ServiceType">1,2,22</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">GSMJQ</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">6,7,10,42,43,400</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,63,66,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3</Item>
					<Item typeName="String" key="ServiceType">1,2,22</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTEJQ</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">1542,1543</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,63,66,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,4,5,19,22</Item>
					<Item typeName="String" key="ServiceType">33,34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTEFG</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">1548,1428,1550</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,44,63</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,19</Item>
					<Item typeName="String" key="ServiceType">34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTEMOS</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">1401,1549,1550,1551</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,44,60,63</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,19</Item>
					<Item typeName="String" key="ServiceType">43,51</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTEDL</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">1644,1645,1646</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,19</Item>
					<Item typeName="String" key="ServiceType">34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">GSMDL</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">6,7,10,2129</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3</Item>
					<Item typeName="String" key="ServiceType">1,22</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">temp</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">1542,1543</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,63,66,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">4</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">15</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,19</Item>
					<Item typeName="String" key="ServiceType">33,34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">NBSCAN</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">7006,7007,7008</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs">80</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">9</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">0</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,4,5,6,19,22</Item>
					<Item typeName="String" key="ServiceType">55,56</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">LTETL</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">1644,1645,1646</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3,19</Item>
					<Item typeName="String" key="ServiceType">34,41,42,44</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			<Item typeName="BlackBlockAdapter">
				<Item name="Setting" typeName="IDictionary">
					<Item typeName="String" key="Name">GSMTL</Item>
					<Item typeName="String" key="BlackBlockAbnormalEventIDs">6,7,10,2129</Item>
					<Item typeName="String" key="ValidTestProjectIDs">-1</Item>
					<Item typeName="String" key="ProjIDs4Create">-999</Item>
					<Item typeName="String" key="ProjIDs">33,34,36,82,83,84</Item>
					<Item typeName="Boolean" key="BlackBlock">False</Item>
					<Item typeName="Int32" key="BlackBlockRadius">100</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestion">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEarseQuestionNoTest">90</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemDeno">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCreateProblemNum">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitCloseProblem">1</Item>
					<Item typeName="Int32" key="BlackBlockLimitEstablishProblemPersistDay">1</Item>
					<Item typeName="Int32" key="DevDwAreaTable">-1</Item>
					<Item typeName="Int32" key="CarrierID">1</Item>
					<Item typeName="Int32" key="MaxPerProcessFileCount">-1</Item>
					<Item typeName="Int32" key="BlackBlockLimitMaxFileForCloseOneDay">1</Item>
					<Item typeName="Int32" key="FileToBlockDistance">50</Item>
					<Item typeName="String" key="DealCityIDs">1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16</Item>
					<Item typeName="String" key="NotDealCityIDs">999</Item>
					<Item typeName="String" key="TestType">1,3</Item>
					<Item typeName="String" key="ServiceType">1,22</Item>
					<Item typeName="String" key="AreaTypeList">1,2,3,4,5,8,9,10,11</Item>
					<Item typeName="Boolean" key="MapLayerOpen">True</Item>
				</Item>
			</Item>
			
		</Item>
	</Config>
</Configs>
