﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Net
{
    public class DIYCellSetBriefDatabaseCompare : QueryBase
    {
        #region 全局变量
        Dictionary<CellKey, List<LongLat>> cellkeyDic { get; set; }
        int curDistricID { get; set; } = -1;
        int iCurCity { get; set; } = 0;
        int iCityNum { get; set; } = 0;
        float fCityPart { get; set; } = 0;
        string strCityName { get; set; } = "";

        DateTime compareNowFrom = DateTime.Now;
        DateTime compareNowTo = DateTime.Now;
        DateTime compareOldFrom = DateTime.Now;
        DateTime compareOldTo = DateTime.Now;
        public DateTime beginSetTime { get; set; } = DateTime.Now;
        public DateTime endSetTime { get; set; } = DateTime.Now;
        public bool isMaiCell { get; set; } = false;
        public bool isNearCell { get; set; } = false;

        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> tdcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> tdGsmcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> wcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> wGsmcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cdcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> evdocellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> evdocdma1xcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cd1xcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();

        List<CellSetDetailInfo> cellSetDetailInfoList { get; set; } = new List<CellSetDetailInfo>();
        List<CellSetDetailInfo> cellSetDetailInfoListOld { get; set; } = new List<CellSetDetailInfo>();

        Dictionary<string, CellSetStat> cellSetStatTheSame { get; set; } = new Dictionary<string, CellSetStat>();
        Dictionary<string, CellSetStat> cellSetStatTheOld { get; set; } = new Dictionary<string, CellSetStat>();
        Dictionary<string, CellSetStat> cellSetStatTheNew { get; set; } = new Dictionary<string, CellSetStat>();
        List<int> iSameID { get; set; } = new List<int>();

        List<CellSetSummaryInfo> cellSetSummaryInfoList { get; set; } = new List<CellSetSummaryInfo>();
        List<CellSetStat> cellSetStatList { get; set; } = new List<CellSetStat>();

        List<CellSetDetailInfo> cellSetSeverNullInfoListSame { get; set; } = new List<CellSetDetailInfo>();
        List<CellSetDetailInfo> cellSetSeverNullInfoListOld { get; set; } = new List<CellSetDetailInfo>();
        List<CellSetDetailInfo> cellSetSeverNullInfoListNew { get; set; } = new List<CellSetDetailInfo>();
        #endregion

        public DIYCellSetBriefDatabaseCompare(MainModel mainModel)
            : base(mainModel)
        { }
        public override string Name
        {
            get { return "小区集对比查询(数据库)"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12090, this.Name);
        }
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegion(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegion(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        #region getCondition
        /// <summary>
        /// 构造SQL的查询条件
        /// </summary>
        private void getConditionStr(ref string projIdStr, ref string servIdStr, ref string carrierTypeStr, ref string areaTypeIdStr, ref string agentIdStr)
        {
            projIdStr = getProjIdStr(projIdStr);

            servIdStr = getServIdStr(servIdStr);

            carrierTypeStr = getCarrierTypeStr(carrierTypeStr);

            areaTypeIdStr = getAreaTypeIdStr(areaTypeIdStr);

            agentIdStr = getAgentIdStr(agentIdStr);
        }

        private string getProjIdStr(string projIdStr)
        {
            for (int i = 0; i < Condition.Projects.Count; i++)
            {
                if (i == 0)
                    projIdStr = Condition.Projects[i].ToString();
                else
                {
                    projIdStr = string.Format(projIdStr + "," + Condition.Projects[i]);
                }
            }
            projIdStr = "(" + projIdStr + ")";
            return projIdStr;
        }

        private string getServIdStr(string servIdStr)
        {
            for (int i = 0; i < Condition.ServiceTypes.Count; i++)
            {
                if (i == 0)
                {
                    servIdStr = Condition.ServiceTypes[i].ToString();
                }
                else
                {
                    servIdStr = string.Format(servIdStr + "," + Condition.ServiceTypes[i]);
                }
            }
            servIdStr = "(" + servIdStr + ")";
            return servIdStr;
        }

        private string getCarrierTypeStr(string carrierTypeStr)
        {
            for (int i = 0; i < Condition.CarrierTypes.Count; i++)
            {
                if (i == 0)
                {
                    carrierTypeStr = Condition.CarrierTypes[i].ToString();
                }
                else
                {
                    carrierTypeStr = string.Format(carrierTypeStr + "," + Condition.CarrierTypes[i]);
                }
            }
            carrierTypeStr = "(" + carrierTypeStr + ")";
            return carrierTypeStr;
        }

        private string getAreaTypeIdStr(string areaTypeIdStr)
        {
            int iKey = 0;
            foreach (int id in Condition.Areas.Keys)
            {
                for (int j = 0; j < Condition.Areas[id].Count; j++)
                {
                    if (iKey == 0)
                    {
                        areaTypeIdStr = Condition.Areas[id][0].ToString();
                        iKey++;
                    }
                    else
                    {
                        areaTypeIdStr = string.Format(areaTypeIdStr + "," + Condition.Areas[id][j]);
                    }
                }
            }
            if (areaTypeIdStr != "")
                areaTypeIdStr = "(" + areaTypeIdStr + ")";
            return areaTypeIdStr;
        }

        private string getAgentIdStr(string agentIdStr)
        {
            for (int i = 0; i < Condition.AgentIds.Count; i++)
            {
                if (i == 0)
                {
                    agentIdStr = Condition.AgentIds[i].ToString();
                }
                else
                {
                    agentIdStr = string.Format(agentIdStr + "," + Condition.AgentIds[i]);
                }
            }
            agentIdStr = "(" + agentIdStr + ")";
            return agentIdStr;
        }
        #endregion

        protected override bool isValidCondition()
        {
            return true;
        }
        
        /// <summary>
        /// 变量初始化
        /// </summary>
        private void initDataMap()
        {
            cellStaterMap.Clear();
            tdcellStaterMap.Clear();
            tdGsmcellStaterMap.Clear();
            wcellStaterMap.Clear();
            wGsmcellStaterMap.Clear();
            cdcellStaterMap.Clear();
            evdocellStaterMap.Clear();
            evdocdma1xcellStaterMap.Clear();
            cd1xcellStaterMap.Clear();
            
            cellSetSummaryInfoList.Clear();
            cellSetStatList.Clear();

            cellSetSeverNullInfoListSame.Clear();
            cellSetSeverNullInfoListOld.Clear();
            cellSetSeverNullInfoListNew.Clear();
        }
        /// <summary>
        /// 查询条件准备
        /// </summary>
        protected override void query()
        {
            DIYCellSetBriefDataCompareTimeForm compareTimeForm = new DIYCellSetBriefDataCompareTimeForm();
            if (compareTimeForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool mainCell = false;
            bool nearCell = false;
            compareTimeForm.getSelect(ref compareNowFrom, ref compareNowTo, ref compareOldFrom, ref compareOldTo,
                ref mainCell, ref nearCell);
            isMaiCell = mainCell;
            isNearCell = nearCell;

            cellkeyDic = new Dictionary<CellKey, List<LongLat>>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            InitRegionMop2();
            cellSetDetailInfoList.Clear();
            cellSetDetailInfoListOld.Clear();
            for (int i = 0; i < 2; i++)
            {
                initDataMap();
                if (i == 1)
                {
                    beginSetTime = compareNowFrom;
                    endSetTime = compareNowTo;
                }
                else
                {
                    beginSetTime = compareOldFrom;
                    endSetTime = compareOldTo;
                }
                iCurCity = 0;
                iCityNum = condition.DistrictIDs.Count;
                if (iCityNum == 0)
                    iCityNum = 1;
                fCityPart = 90 / ((float)iCityNum);
                WaitBox.ProgressPercent = 0;

                curDistricID = MainModel.DistrictID;
                bool connected = quereyDistrict(i);
                if (!connected)
                {
                    return;
                }
                MainModel.DistrictID = curDistricID;

                MergeCellInfo(i);

            }

            ShowFormAfterQueryNew();
        }

        private bool quereyDistrict(int i)
        {
            ClientProxy clientProxy;
            foreach (int DistrictID in condition.DistrictIDs)
            {
                iCurCity++;
                strCityName = DistrictManager.GetInstance().getDistrictName(DistrictID);
                clientProxy = new ClientProxy();
                MainModel.DistrictID = DistrictID;
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    curDistricID = -1;
                    return false;
                }
                WaitBox.CanCancel = true;
                if (i == 0)
                    WaitBox.Show("正在获取对比月份小区...", queryInThread);
                else
                    WaitBox.Show("正在获取当前月份小区...", queryInThread);
            }

            return true;
        }

        /// <summary>
        /// 真正查询过程
        /// </summary>
        private void queryInThread()
        {
            try
            {
                string projIdStr = "";
                string servIdStr = "";
                string carrierTypeStr = "";
                string areaTypeIdStr = "";
                string agentIdStr = "";
                cellkeyDic = new Dictionary<CellKey, List<LongLat>>();
                getConditionStr(ref projIdStr, ref servIdStr, ref carrierTypeStr, ref areaTypeIdStr, ref agentIdStr);
                
                DateTime dBeginTime = beginSetTime;
                DateTime dEndTime = endSetTime;
                int beginTime = (int)(JavaDate.GetMilliseconds(beginSetTime) / 1000);
                int endTime = (int)(JavaDate.GetMilliseconds(endSetTime) / 1000);

                List<DateTime> dTimeList = new List<DateTime>();
                while (dBeginTime <= dEndTime)
                {
                    DateTime dTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-01 00:00:00", dBeginTime));
                    if (!dTimeList.Contains(dTime))
                        dTimeList.Add(dTime);
                    dBeginTime = dBeginTime.AddDays(1);
                }

                int iCurRound = 0;
                int iRoundNum = dTimeList.Count;
                float fRoundPart = fCityPart / ((float)iRoundNum);

                DiySqlQueryTbLogFile queryTbLogFile = new DiySqlQueryTbLogFile(MainModel, projIdStr, servIdStr, carrierTypeStr, areaTypeIdStr, agentIdStr);
                DiySqlQueryCellDatabase queryCell = new DiySqlQueryCellDatabase(MainModel, projIdStr, servIdStr, carrierTypeStr, areaTypeIdStr, agentIdStr);
                foreach (DateTime dTime in dTimeList)
                {
                    iCurRound++;
                    string logFileName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", dTime);

                    queryTbLogFile.FillData(logFileName, beginTime, endTime);
                    queryTbLogFile.Query();

                    int iCurTable = 0;
                    int iTableNum = queryTbLogFile.sampletbnameLst.Count;
                    if (iTableNum == 0)
                        iTableNum = 1;
                    float fTablePart = fRoundPart / iTableNum;
                    queryCell.FillData(Condition.FileName, logFileName, cellkeyDic, queryTbLogFile.sampletbnameLst, dEndTime);
                    foreach (string tbsample in queryTbLogFile.sampletbnameLst)
                    {
                        iCurTable++;
                        if (isMaiCell)
                        {
                            queryCell.FillData(tbsample, "sp_auto_stat_cellinfo_all");
                            queryCell.SetQueryCondition(Condition);
                            queryCell.Query();
                        }

                        //邻小区信息查询
                        if (isNearCell)
                        {
                            queryCell.FillData(tbsample, "sp_auto_stat_nbcellinfo");
                            queryCell.SetQueryCondition(Condition);
                            queryCell.Query();
                        }

                        WaitBox.Text = string.Format("当前城市{4},{5}/{6};月份:{0}/{1};数据表:{2}/{3}...", 
                            iCurRound, iRoundNum, iCurTable, iTableNum, strCityName, iCurCity, iCityNum);
                        WaitBox.ProgressPercent = (int)((iCurCity - 1) * fCityPart 
                            + (iCurRound - 1) * fRoundPart + (iCurTable - 1) * fTablePart);
                    }
                }

                WaitBox.Text = "正在获取小区名称...";
                GetCells();
               
            }
            finally
            {
                WaitBox.Close();
            }
        }
        /// <summary>
        /// 合并小区与基站数目信息
        /// </summary>
        private void MergeCellInfo(int ID)
        {
            if (ID == 1)
            {
                cellSetDetailInfoList.AddRange(cellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(tdcellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(tdGsmcellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(wcellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(wGsmcellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(cdcellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(evdocellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(evdocdma1xcellStaterMap.Keys);
                cellSetDetailInfoList.AddRange(cd1xcellStaterMap.Keys);

                cellSetDetailInfoList.Sort(CellSetDetailInfo.GetCompareByGridType());

                WaitBox.Text = "开始对比两个月小区间的共占情况，数据量大，请稍等.....";
                WaitBox.ProgressPercent = 5;
                compareNum(cellSetDetailInfoList, cellSetDetailInfoListOld);
            }
            else if (ID == 0)
            {
                cellSetDetailInfoListOld.AddRange(cellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(tdcellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(tdGsmcellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(wcellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(wGsmcellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(cdcellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(evdocellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(evdocdma1xcellStaterMap.Keys);
                cellSetDetailInfoListOld.AddRange(cd1xcellStaterMap.Keys);

                cellSetDetailInfoListOld.Sort(CellSetDetailInfo.GetCompareByGridType());
            }
        }
        /// <summary>
        /// 两个月小区对比过程
        /// </summary>
        private void compareNum(List<CellSetDetailInfo> cellSetDetailInfoList,
            List<CellSetDetailInfo> cellSetDetailInfoListOld)
        {
            iSameID.Clear();
            cellSetStatTheSame.Clear();
            cellSetStatTheOld.Clear();
            cellSetStatTheNew.Clear();
            cellSetSeverNullInfoListSame.Clear();
            cellSetSeverNullInfoListOld.Clear();
            cellSetSeverNullInfoListNew.Clear();
            int oldCount = cellSetDetailInfoListOld.Count;
            for (int iold = 0; iold < oldCount; iold++)
            {
                if (WaitBox.ProgressPercent >= 100)
                    WaitBox.ProgressPercent = 5;
                else
                    WaitBox.ProgressPercent += 1;
                string strkey = cellSetDetailInfoListOld[iold].strCity + "," + cellSetDetailInfoListOld[iold].strGridType + "," 
                    + cellSetDetailInfoListOld[iold].strGrid;
                if (cellSetDetailInfoList.Contains(cellSetDetailInfoListOld[iold]))
                {
                    addcellSetData(cellSetDetailInfoListOld, cellSetStatTheSame, iold, strkey, cellSetSeverNullInfoListSame);
                }
                else
                {
                    addcellSetData(cellSetDetailInfoListOld, cellSetStatTheOld, iold, strkey, cellSetSeverNullInfoListOld);
                }
            }

            newOwn();
        }
       
        /// <summary>
        /// 当前月独占小区数
        /// </summary>
        private void newOwn()
        {
            WaitBox.ProgressPercent = 75;
            int infoListCount = cellSetDetailInfoList.Count;
            for (int newOwnID = 0; newOwnID < infoListCount; newOwnID++)
            {
                if (iSameID.Contains(newOwnID))
                    continue;
                string strkey = cellSetDetailInfoList[newOwnID].strCity + "," + cellSetDetailInfoList[newOwnID].strGridType + ","
                    + cellSetDetailInfoList[newOwnID].strGrid;
                addcellSetData(cellSetDetailInfoList, cellSetStatTheNew, newOwnID, strkey, cellSetSeverNullInfoListNew);
            }
        }

        private void addcellSetData(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, List<CellSetDetailInfo> cellSetSeverNullInfoList)
        {
            bool isSame = isThisSame(cellSetDetailInfoList[id], 2);
            if (!isSame)
            {
                cellSetSeverNullInfoList.Add(cellSetDetailInfoList[id]);
            }
            if (!cellSetStat.ContainsKey(strkey))
            {
                CellSetStat cellS = new CellSetStat();
                cellS.strCity = cellSetDetailInfoList[id].strCity;
                cellS.strGridType = cellSetDetailInfoList[id].strGridType;
                cellS.strGrid = cellSetDetailInfoList[id].strGrid;
                cellSetStat.Add(strkey, cellS);
            }
            addcellSetStatNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
        }

        private void addcellSetStatNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (cellSetDetailInfoList[id].strCellType == "GSM")
            {
                addGSMNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "TD")
            {
                addTDNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "TD-GSM")
            {
                addTDGSMNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "WCDMA")
            {
                addWCDMANum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "WCDMA-GSM")
            {
                addWCDMAGSMNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "CDMA")
            {
                addCDMANum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "CDMA1x")
            {
                addCDMA1xNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "EVDO")
            {
                addEVDONum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
            else if (cellSetDetailInfoList[id].strCellType == "EVDO-CDMA1x")
            {
                addEVDOCDMA1xNum(cellSetDetailInfoList, cellSetStat, id, strkey, isSame);
            }
        }

        private void addGSMNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (cellSetDetailInfoList[id].strCarrier == "移动")
            {
                if (!isSame)
                    cellSetStat[strkey].iAllYDGsmCellNum += 1;
                if (cellSetDetailInfoList[id].strServiceType == "语音")
                    cellSetStat[strkey].iVGsmCellNum += 1;
                else if (cellSetDetailInfoList[id].strServiceType == "数据")
                    cellSetStat[strkey].iDGsmCellNum += 1;
                else if (cellSetDetailInfoList[id].strServiceType == "空闲")
                    cellSetStat[strkey].iFYDGsmCellNum += 1;
            }
            else
            {
                if (!isSame)
                    cellSetStat[strkey].iAllLTGsmCellNum += 1;
                if (cellSetDetailInfoList[id].strServiceType == "语音")
                    cellSetStat[strkey].iVLtGsmCellNum += 1;
                else if (cellSetDetailInfoList[id].strServiceType == "数据")
                    cellSetStat[strkey].iDLtGsmCellNum += 1;
                else if (cellSetDetailInfoList[id].strServiceType == "空闲")
                    cellSetStat[strkey].iFLTGsmCellNum += 1;
            }
        }

        private void addTDNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllTdCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "语音")
                cellSetStat[strkey].iVTdCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDTdCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "空闲")
                cellSetStat[strkey].iFTdCellNum += 1;
        }

        private void addTDGSMNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllYDGsmCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "语音")
                cellSetStat[strkey].iVTdGsmCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDTdGsmCellNum += 1;
        }

        private void addWCDMANum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllWCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "语音")
                cellSetStat[strkey].iVWCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDWCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "空闲")
                cellSetStat[strkey].iFWCellNum += 1;
        }

        private void addWCDMAGSMNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllLTGsmCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "语音")
                cellSetStat[strkey].iVWGsmCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDWGsmCellNum += 1;
        }

        private void addCDMANum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllCdmaCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "语音")
                cellSetStat[strkey].iVCdCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "空闲")
                cellSetStat[strkey].iFDXCdmaCellNum += 1;
        }

        private void addCDMA1xNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllCdmaCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDCdCellNum += 1;
        }

        private void addEVDONum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllEvodCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDEvdoCellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "语音")
                cellSetStat[strkey].iVCd2000CellNum += 1;
            else if (cellSetDetailInfoList[id].strServiceType == "空闲")
                cellSetStat[strkey].iFEvodoCellNum += 1;
        }

        private void addEVDOCDMA1xNum(List<CellSetDetailInfo> cellSetDetailInfoList, Dictionary<string, CellSetStat> cellSetStat, int id, string strkey, bool isSame)
        {
            if (!isSame)
                cellSetStat[strkey].iAllCdmaCellNum += 1;
            if (cellSetDetailInfoList[id].strServiceType == "数据")
                cellSetStat[strkey].iDEvdoCdma1xCellNum += 1;
        }

        /// <summary>
        /// 判断去重
        /// </summary>
        private bool isThisSame(CellSetDetailInfo cellSetInfo, int mothType)
        {
            bool isSame = false;
            if (mothType == 0)
            {
                isSame = judgeSame(cellSetInfo, isSame, cellSetSeverNullInfoListSame);
            }
            else if (mothType == 1)
            {
                isSame = judgeSame(cellSetInfo, isSame, cellSetSeverNullInfoListOld);
            }
            else if (mothType == 2)
            {
                isSame = judgeSame(cellSetInfo, isSame, cellSetSeverNullInfoListNew);
            }
            return isSame;
        }

        private bool judgeSame(CellSetDetailInfo cellSetInfo, bool isSame, List<CellSetDetailInfo> InfoList)
        {
            foreach (CellSetDetailInfo csif in InfoList)
            {
                if (csif.strCity.Equals(cellSetInfo.strCity) && csif.strCarrier.Equals(cellSetInfo.strCarrier)
                    && csif.strGridType.Equals(cellSetInfo.strGridType)
                    && csif.strGrid.Equals(cellSetInfo.strGrid) && csif.strCellName.Equals(cellSetInfo.strCellName)
                    && csif.strBtsName.Equals(cellSetInfo.strBtsName)
                    && csif.strBtsType.Equals(cellSetInfo.strBtsType))
                {
                    isSame = true;
                    break;
                }
            }

            return isSame;
        }

        /// <summary>
        /// 查询完毕后显示小区集
        /// </summary>
        private void ShowFormAfterQueryNew()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(DIYCellSetBriefDataCompareDataForm).FullName);
            DIYCellSetBriefDataCompareDataForm cellSetBriefForm = obj == null ? null : obj as DIYCellSetBriefDataCompareDataForm;
            if (cellSetBriefForm == null || cellSetBriefForm.IsDisposed)
            {
                cellSetBriefForm = new DIYCellSetBriefDataCompareDataForm(MainModel);
            }
            cellSetBriefForm.FillData(cellSetStatTheSame, cellSetStatTheOld, cellSetStatTheNew);
            if (!cellSetBriefForm.Visible)
            {
                cellSetBriefForm.Show(MainModel.MainForm);
            }
        }
        /// <summary>
        /// 通过LAC\CI\时间获取小区
        /// </summary>
        private void GetCells()
        {
            foreach (CellKey key in cellkeyDic.Keys)
            {
                List<LongLat> tmpList = cellkeyDic[key];
                if (key.iServicetype == 1 || key.iServicetype == 2 || key.iServicetype == 3 || key.iServicetype == 22)  //GSM
                {
                    doWithGSMData(key, tmpList[0]);
                }
                else if (key.iServicetype == 4 || key.iServicetype == 5 || key.iServicetype == 13 || key.iServicetype == 17 || key.iServicetype == 18 || key.iServicetype == 27)//TD
                {
                    doWithTDData(key, tmpList[0]);
                }
                else if (key.iServicetype == 10 || key.iServicetype == 11 || key.iServicetype == 14 || key.iServicetype == 15 || key.iServicetype == 25 || key.iServicetype == 28) //WCDMA
                {
                    doWithWCDMAData(key, tmpList[0]);
                }
                else if (key.iServicetype == 6 || key.iServicetype == 7 || key.iServicetype == 26)//CDMA
                {
                    doWithCDMAData(key, tmpList[0]);
                }
                else if (key.iServicetype == 8 || key.iServicetype == 9 || key.iServicetype == 40)//EVDO
                {
                    doWithEvdoData(key, tmpList[0]);
                }
            }
        }
        /// <summary>
        /// 定位所在网格
        /// </summary>
        private void isContainPoint(double x, double y, ref Dictionary<string, string> gridTypeGrid)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y) && !gridTypeGrid.ContainsKey(gridType))
                    {
                        gridTypeGrid.Add(gridType, grid);
                        break;
                    }
                }
            }
        }
        /// <summary>
        /// 获取业务类型
        /// </summary>
        private string getServiceType(int iServiceType)
        {
            if (iServiceType == 1 || iServiceType == 4 || iServiceType == 10 || iServiceType == 6 || iServiceType == 8)
            {
                return "语音";
            }
            else if (iServiceType == 22 || iServiceType == 17 || iServiceType == 25 || iServiceType == 26 || iServiceType == 40)
            {
                return "空闲";
            }
            else
            {
                return "数据";
            }
        }
        /// <summary>
        /// 按LAC\CI构造小区及基站
        /// </summary>
        private void getCellByLacCi(CellKey cKey, ref string strLacCi, ref string strBtsKey)
        {
            strLacCi = string.Format("{0}_{1}", cKey.iLac, cKey.iCi);
            string strSubCi = cKey.iCi.ToString().Length > 1 ? cKey.iCi.ToString().Substring(0, cKey.iCi.ToString().Length - 1) : cKey.iCi.ToString();
            strBtsKey = string.Format("{0}_{1}", cKey.iLac, strSubCi);
        }
        /// <summary>
        /// 处理GSM小区信息
        /// </summary>
        private void doWithGSMData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            Cell cell = CellManager.GetInstance().GetCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
            if (cKey.iCarriertype == 2)
                cell = null;
            foreach (string gridType in gridTypeGrid.Keys)
            {
                addGSMCell(cell, cKey, gridType, gridTypeGrid[gridType]);
            }
        }
        /// <summary>
        /// 分别组织移动、联通GSM
        /// </summary>
        private void addGSMCell(Cell cell, CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "GSM";
            stater.strCarrier = cKey.iCarriertype == 1 ? "移动" : "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == BTSType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!cellStaterMap.ContainsKey(stater))
            {
                cellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理TD小区信息
        /// </summary>
        private void doWithTDData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.inet == 2)
            {
                Cell cell = CellManager.GetInstance().GetCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addTDGSMCell(cell, cKey, gridType, gridTypeGrid[gridType]);
                }
            }
            else
            {
                TDCell tdcell = CellManager.GetInstance().GetTDCell(cKey.dTime, cKey.iLac, cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addTDCell(tdcell, cKey, gridType, gridTypeGrid[gridType]);
                }
            }
        }
        /// <summary>
        /// 组织移动TD
        /// </summary>
        private void addTDCell(TDCell cell, CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "TD";
            stater.strCarrier = "移动";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == TDNodeBType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!tdcellStaterMap.ContainsKey(stater))
            {
                tdcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动TD-GSM
        /// </summary>
        private void addTDGSMCell(Cell cell, CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "TD-GSM";
            stater.strCarrier = "移动";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == BTSType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!tdGsmcellStaterMap.ContainsKey(stater))
            {
                tdGsmcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理WCDMA小区信息
        /// </summary>
        private void doWithWCDMAData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.inet == 2)
            {
                Cell cell = CellManager.GetInstance().GetCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addWCDMAGSMCell(cell, cKey, gridType, gridTypeGrid[gridType]);
                }
            }
            else
            {
                WCell wcell = CellManager.GetInstance().GetWCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addWCDMACell(wcell, cKey, gridType, gridTypeGrid[gridType]);
                }
            }
        }
        /// <summary>
        /// 组织移动WCDMA
        /// </summary>
        private void addWCDMACell(WCell cell, CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "WCDMA";
            stater.strCarrier = "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == WNodeBType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongNodeBs[0].Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!wcellStaterMap.ContainsKey(stater))
            {
                wcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动WCDMA-GSM
        /// </summary>
        private void addWCDMAGSMCell(Cell cell, CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "WCDMA-GSM";
            stater.strCarrier = "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == BTSType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!wGsmcellStaterMap.ContainsKey(stater))
            {
                wGsmcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理CDMA小区信息
        /// </summary>
        private void doWithCDMAData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.iServicetype == 7)
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addCDMA1xCell(cKey, gridType, gridTypeGrid[gridType]);
                }
            }
            else
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addCDMACell(cKey, gridType, gridTypeGrid[gridType]);
                }
            }
        }
        /// <summary>
        /// 组织移动CDMA
        /// </summary>
        private void addCDMACell(CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "CDMA";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!cdcellStaterMap.ContainsKey(stater))
            {
                cdcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动CDMA1X
        /// </summary>
        private void addCDMA1xCell(CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "CDMA1x";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!cd1xcellStaterMap.ContainsKey(stater))
            {
                cd1xcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理CDMA小区信息
        /// </summary>
        private void doWithEvdoData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.inet == 3)
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addEvdoCell(cKey, gridType, gridTypeGrid[gridType]);
                }
            }
            else
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addEvdoCdma1XCell(cKey, gridType, gridTypeGrid[gridType]);
                }
            }
        }
        /// <summary>
        /// 组织移动CDMA
        /// </summary>
        private void addEvdoCell(CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "EVDO";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!evdocellStaterMap.ContainsKey(stater))
            {
                evdocellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动CDMA
        /// </summary>
        private void addEvdoCdma1XCell(CellKey cKey, string gridType, string gridname)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "EVDO-CDMA1x";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!evdocdma1xcellStaterMap.ContainsKey(stater))
            {
                evdocdma1xcellStaterMap.Add(stater, stater);
            }
        }        
    }
}
