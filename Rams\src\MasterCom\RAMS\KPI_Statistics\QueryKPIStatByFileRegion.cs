﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByFileRegion : QueryKPIStatByRegion
    {
        public QueryKPIStatByFileRegion() : base()
        { }

        public override string Name
        {
            get
            {
                return "区域文件KPI统计";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11054, this.Name);
        }

        protected Dictionary<int,FileInfo> iFileIDInfoDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            if (this.iFileIDInfoDic == null)
            {
                this.iFileIDInfoDic = new Dictionary<int, FileInfo>();
            }

            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            KpiDataManager.AddStatData(string.Empty, fi, fi, singleStatData, false);
            if (!this.iFileIDInfoDic.ContainsKey(fileID))
            {
                this.iFileIDInfoDic[fileID] = fi;
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            if (this.iFileIDInfoDic.ContainsKey(evt.FileID))
            {
                fi = this.iFileIDInfoDic[evt.FileID];
            }
            KpiDataManager.AddStatData(string.Empty, fi, fi, eventData, false);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            if (KpiDataManager != null)
            {
                KpiDataManager.FinalMtMoStatData();
            }
            this.iFileIDInfoDic = null;
        }
    }
}
