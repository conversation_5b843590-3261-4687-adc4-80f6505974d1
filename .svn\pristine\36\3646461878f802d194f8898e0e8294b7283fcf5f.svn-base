﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    /// <summary>
    /// 流程，下有各类NodeEntry（节点）
    /// </summary>
    public class ProcRoutine
    {
        public override string ToString()
        {
            return Name;
        }
        /// <summary>
        /// 加载时，用来记录重复的子节点信息
        /// </summary>
        public static Dictionary<int, NodeEntry> TempKeyInRoutineDic { get; set; } = new Dictionary<int, NodeEntry>();

        public NodeEntry RootNode { get; set; }

        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(ProcRoutine).Name))
            {
                TempKeyInRoutineDic.Clear();
                Dictionary<string, object> param = configFile.GetItemValue(item, "RootNode") as Dictionary<string, object>;
                NodeEntry node = new NodeEntry();
                node.Param = param;
                return node;
            }
            return null;
        }


        public int ID { get; set; }

        public string Name { get; set; }

        public int Version { get; set; }

        public string ModifyDateTime { get; set; }

        internal bool LoadFrom(string xml)
        {
            XmlConfigFile procFile = new XmlConfigFile();
            if (!procFile.LoadXml(xml))
            {
                return false;
            }

            XmlElement configRoutine = procFile.GetConfig("ProcRoutine");
            NodeEntry rootNode = procFile.GetItemValue(configRoutine, "EntryRoot", ProcRoutine.GetItemValue) as NodeEntry;
            if (rootNode != null)
            {
                this.RootNode = rootNode;
            }
            else
            {
                return false;
            }
            return true;
        }

        public NodeEntry HitTest(Point pnt)
        {
            return hitTestNode(RootNode, pnt);
        }

        private NodeEntry hitTestNode(NodeEntry node, Point pnt)
        {
            if (node==null)
            {
                return null;
            }
            GraphicsPath gp = new GraphicsPath();

            gp.AddRectangle(new Rectangle(node.XPos - node.Width / 2,
                node.YPos - node.Height / 2,
                node.Width,
                node.Height));

            if (gp.IsVisible(pnt))
            {
                return node;
            }
            else
            {
                if (node.YesNode != null)
                {
                    NodeEntry ret = hitTestNode(node.YesNode, pnt);
                    if (ret != null)
                    {
                        return ret;
                    }
                }
                if (node.NoNode != null)
                {
                    NodeEntry ret = hitTestNode(node.NoNode, pnt);
                    if (ret != null)
                    {
                        return ret;
                    }
                }
            }
            return null;
        }
    }
}
