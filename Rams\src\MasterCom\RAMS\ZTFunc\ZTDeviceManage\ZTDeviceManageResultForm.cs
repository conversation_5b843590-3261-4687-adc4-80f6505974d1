﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDeviceManageResultForm : MinCloseForm
    {
        public ZTDeviceManageResultForm()
        {
            InitializeComponent();
        }

        public void FillData(List<ZTDeviceManageInfo> deviceInfolist)
        {
            gridControl1.DataSource = deviceInfolist;
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rowList = getNPOIRow();
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        protected virtual List<NPOIRow> getNPOIRow()
        {
            List<ZTDeviceManageInfo> deviceInfolist = (List<ZTDeviceManageInfo>)gridControl1.DataSource;
            List<NPOIRow> rowList = new List<NPOIRow>(deviceInfolist.Count + 1);
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("BOXID");
            titleRow.AddCellValue("所属域");
            titleRow.AddCellValue("厂商");
            titleRow.AddCellValue("设备状态");
            titleRow.AddCellValue("使用频次");

            titleRow.AddCellValue("运营商");
            titleRow.AddCellValue("2G里程");
            titleRow.AddCellValue("2G时长");
            titleRow.AddCellValue("3G里程");
            titleRow.AddCellValue("3G时长");
            titleRow.AddCellValue("4G里程");
            titleRow.AddCellValue("4G时长");
            rowList.Add(titleRow);

            int index = 1;
            foreach (ZTDeviceManageInfo item in deviceInfolist)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, item, index);
                rowList.Add(row);
            }
            return rowList;
        }

        protected void fillRow(ref NPOIRow row, ZTDeviceManageInfo item, int index)
        {
            if (row == null || item == null)
                return;
            //添加一级数据
            row.AddCellValue(item.BoxID);
            row.AddCellValue(item.Area);
            row.AddCellValue(item.Vendor);
            row.AddCellValue(item.DeviceStatus);
            row.AddCellValue(item.Frequency);
            foreach (var carrier in item.ManageCarrierInfo)
            {
                //添加二级数据
                NPOIRow subRow = new NPOIRow();
                subRow.AddCellValue(carrier.CarrierStr);
                subRow.AddCellValue(carrier.Distance_2G);
                subRow.AddCellValue(carrier.Duration_2G);
                subRow.AddCellValue(carrier.Distance_3G);
                subRow.AddCellValue(carrier.Duration_3G);
                subRow.AddCellValue(carrier.Distance_4G);
                subRow.AddCellValue(carrier.Duration_4G);
                row.AddSubRow(subRow);
            }
        }

    }
}
