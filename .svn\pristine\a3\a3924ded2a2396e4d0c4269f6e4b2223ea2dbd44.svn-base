using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Threading;

namespace MasterCom.RAMS.Frame
{
    public partial class ExitDlg : BaseDialog
    {
        int nowCount = 15;
        public ExitDlg()
        {
            InitializeComponent();
            timerDelay.Enabled = true;
            timerDelay.Start(); 
            labelSecs.Text = nowCount.ToString();
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (timerDelay.Enabled)
            {
                timerDelay.Enabled = false;
                timerDelay.Dispose();
            }
            this.DialogResult = DialogResult.Cancel;
        }

        private void timerDelay_Tick(object sender, EventArgs e)
        {
            nowCount--;
            if (nowCount > 0)
            {
                labelSecs.Text = nowCount.ToString();
            }
            else
            {
                btnOK.PerformClick();
            }
        }
    }
}