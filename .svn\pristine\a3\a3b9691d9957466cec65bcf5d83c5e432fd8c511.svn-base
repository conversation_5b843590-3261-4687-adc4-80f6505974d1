﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class BlackBlockStatForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.SimpleDiagram simpleDiagram11 = new DevExpress.XtraCharts.SimpleDiagram();
            DevExpress.XtraCharts.Series series29 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel21 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions21 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions22 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView21 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel22 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView22 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram11 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series30 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel17 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions21 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel18 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SimpleDiagram simpleDiagram12 = new DevExpress.XtraCharts.SimpleDiagram();
            DevExpress.XtraCharts.Series series31 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel23 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions23 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions24 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView23 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.Series series32 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel19 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions22 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PointOptions pointOptions23 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel24 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView24 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram12 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series33 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel20 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions24 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel21 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SimpleDiagram simpleDiagram13 = new DevExpress.XtraCharts.SimpleDiagram();
            DevExpress.XtraCharts.Series series34 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel25 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions25 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions26 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView25 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.Series series35 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel22 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions25 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PointOptions pointOptions26 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel26 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView26 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.SimpleDiagram simpleDiagram14 = new DevExpress.XtraCharts.SimpleDiagram();
            DevExpress.XtraCharts.Series series36 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel27 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions27 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions28 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView27 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.Series series37 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel23 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions27 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PointOptions pointOptions28 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel28 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView28 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.SimpleDiagram simpleDiagram15 = new DevExpress.XtraCharts.SimpleDiagram();
            DevExpress.XtraCharts.Series series38 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel29 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions29 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions30 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView29 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.Series series39 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel24 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions29 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PointOptions pointOptions30 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel30 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView30 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram13 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series40 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.FullStackedBarSeriesLabel fullStackedBarSeriesLabel13 = new DevExpress.XtraCharts.FullStackedBarSeriesLabel();
            DevExpress.XtraCharts.FullStackedBarPointOptions fullStackedBarPointOptions13 = new DevExpress.XtraCharts.FullStackedBarPointOptions();
            DevExpress.XtraCharts.FullStackedBarPointOptions fullStackedBarPointOptions14 = new DevExpress.XtraCharts.FullStackedBarPointOptions();
            DevExpress.XtraCharts.FullStackedBarSeriesView fullStackedBarSeriesView13 = new DevExpress.XtraCharts.FullStackedBarSeriesView();
            DevExpress.XtraCharts.FullStackedBarSeriesLabel fullStackedBarSeriesLabel14 = new DevExpress.XtraCharts.FullStackedBarSeriesLabel();
            DevExpress.XtraCharts.FullStackedBarSeriesView fullStackedBarSeriesView14 = new DevExpress.XtraCharts.FullStackedBarSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram14 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series41 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.FullStackedBarSeriesLabel fullStackedBarSeriesLabel15 = new DevExpress.XtraCharts.FullStackedBarSeriesLabel();
            DevExpress.XtraCharts.FullStackedBarPointOptions fullStackedBarPointOptions15 = new DevExpress.XtraCharts.FullStackedBarPointOptions();
            DevExpress.XtraCharts.FullStackedBarPointOptions fullStackedBarPointOptions16 = new DevExpress.XtraCharts.FullStackedBarPointOptions();
            DevExpress.XtraCharts.FullStackedBarSeriesView fullStackedBarSeriesView15 = new DevExpress.XtraCharts.FullStackedBarSeriesView();
            DevExpress.XtraCharts.FullStackedBarSeriesLabel fullStackedBarSeriesLabel16 = new DevExpress.XtraCharts.FullStackedBarSeriesLabel();
            DevExpress.XtraCharts.FullStackedBarSeriesView fullStackedBarSeriesView16 = new DevExpress.XtraCharts.FullStackedBarSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram15 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series42 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.FullStackedBarSeriesLabel fullStackedBarSeriesLabel17 = new DevExpress.XtraCharts.FullStackedBarSeriesLabel();
            DevExpress.XtraCharts.FullStackedBarPointOptions fullStackedBarPointOptions17 = new DevExpress.XtraCharts.FullStackedBarPointOptions();
            DevExpress.XtraCharts.FullStackedBarPointOptions fullStackedBarPointOptions18 = new DevExpress.XtraCharts.FullStackedBarPointOptions();
            DevExpress.XtraCharts.FullStackedBarSeriesView fullStackedBarSeriesView17 = new DevExpress.XtraCharts.FullStackedBarSeriesView();
            DevExpress.XtraCharts.FullStackedBarSeriesLabel fullStackedBarSeriesLabel18 = new DevExpress.XtraCharts.FullStackedBarSeriesLabel();
            DevExpress.XtraCharts.FullStackedBarSeriesView fullStackedBarSeriesView18 = new DevExpress.XtraCharts.FullStackedBarSeriesView();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabRegion = new System.Windows.Forms.TabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridCtrlRegion = new DevExpress.XtraGrid.GridControl();
            this.gridViewRegion = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.chartRegionCreate = new DevExpress.XtraCharts.ChartControl();
            this.chartGridCreate = new DevExpress.XtraCharts.ChartControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.chartRegionClose = new DevExpress.XtraCharts.ChartControl();
            this.chartGridClose = new DevExpress.XtraCharts.ChartControl();
            this.tabCause = new System.Windows.Forms.TabPage();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlCause = new DevExpress.XtraGrid.GridControl();
            this.gridViewCaues = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl6 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabCtrlCausePie = new System.Windows.Forms.TabControl();
            this.tabMainCause = new System.Windows.Forms.TabPage();
            this.chartPieMainCause = new DevExpress.XtraCharts.ChartControl();
            this.tabSubCause = new System.Windows.Forms.TabPage();
            this.chartPieSubCause = new DevExpress.XtraCharts.ChartControl();
            this.tabDetailCause = new System.Windows.Forms.TabPage();
            this.chartPieDetailCause = new DevExpress.XtraCharts.ChartControl();
            this.tabCtrlBarCause = new System.Windows.Forms.TabControl();
            this.tabBarMainCause = new System.Windows.Forms.TabPage();
            this.chartBarMainCause = new DevExpress.XtraCharts.ChartControl();
            this.tabBarSubCause = new System.Windows.Forms.TabPage();
            this.chartBarSubCause = new DevExpress.XtraCharts.ChartControl();
            this.tabBarDetailCause = new System.Windows.Forms.TabPage();
            this.chartBarDetailCause = new DevExpress.XtraCharts.ChartControl();
            this.tabControl.SuspendLayout();
            this.ctxMenu.SuspendLayout();
            this.tabRegion.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlRegion)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartRegionCreate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartGridCreate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartRegionClose)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series31)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series32)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartGridClose)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series33)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel21)).BeginInit();
            this.tabCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCaues)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).BeginInit();
            this.splitContainerControl6.SuspendLayout();
            this.tabCtrlCausePie.SuspendLayout();
            this.tabMainCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartPieMainCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series34)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series35)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView26)).BeginInit();
            this.tabSubCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartPieSubCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series36)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series37)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel28)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView28)).BeginInit();
            this.tabDetailCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartPieDetailCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series38)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series39)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView30)).BeginInit();
            this.tabCtrlBarCause.SuspendLayout();
            this.tabBarMainCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartBarMainCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series40)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView14)).BeginInit();
            this.tabBarSubCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartBarSubCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView16)).BeginInit();
            this.tabBarDetailCause.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartBarDetailCause)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series42)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView18)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.ContextMenuStrip = this.ctxMenu;
            this.tabControl.Controls.Add(this.tabRegion);
            this.tabControl.Controls.Add(this.tabCause);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(1184, 680);
            this.tabControl.TabIndex = 0;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // tabRegion
            // 
            this.tabRegion.Controls.Add(this.splitContainerControl1);
            this.tabRegion.Location = new System.Drawing.Point(4, 23);
            this.tabRegion.Name = "tabRegion";
            this.tabRegion.Padding = new System.Windows.Forms.Padding(3);
            this.tabRegion.Size = new System.Drawing.Size(1176, 653);
            this.tabRegion.TabIndex = 0;
            this.tabRegion.Text = "区域统计";
            this.tabRegion.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl1.Appearance.Options.UseForeColor = true;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(3, 3);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridCtrlRegion);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1170, 647);
            this.splitContainerControl1.SplitterPosition = 215;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridCtrlRegion
            // 
            this.gridCtrlRegion.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlRegion.Location = new System.Drawing.Point(0, 0);
            this.gridCtrlRegion.MainView = this.gridViewRegion;
            this.gridCtrlRegion.Name = "gridCtrlRegion";
            this.gridCtrlRegion.Size = new System.Drawing.Size(1170, 215);
            this.gridCtrlRegion.TabIndex = 2;
            this.gridCtrlRegion.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRegion});
            // 
            // gridViewRegion
            // 
            this.gridViewRegion.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn6,
            this.gridColumn5});
            this.gridViewRegion.GridControl = this.gridCtrlRegion;
            this.gridViewRegion.Name = "gridViewRegion";
            this.gridViewRegion.OptionsBehavior.Editable = false;
            this.gridViewRegion.OptionsView.AllowCellMerge = true;
            this.gridViewRegion.OptionsView.RowAutoHeight = true;
            this.gridViewRegion.OptionsView.ShowDetailButtons = false;
            this.gridViewRegion.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域";
            this.gridColumn1.FieldName = "Branch";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 95;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "网格组";
            this.gridColumn2.FieldName = "Region";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 117;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "网格";
            this.gridColumn3.FieldName = "GridDescForDisplay";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 126;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "状态";
            this.gridColumn4.FieldName = "StatusDes";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 249;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "创建日期";
            this.gridColumn6.FieldName = "CreateDateString";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            this.gridColumn6.Width = 504;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "关闭日期";
            this.gridColumn5.FieldName = "ClosedDateString";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.groupControl2);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1170, 426);
            this.splitContainerControl3.SplitterPosition = 213;
            this.splitContainerControl3.TabIndex = 1;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.splitContainerControl4);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1170, 213);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "已创建黑点统计图";
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.Location = new System.Drawing.Point(2, 23);
            this.splitContainerControl4.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl4.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.chartRegionCreate);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.chartGridCreate);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(1166, 188);
            this.splitContainerControl4.SplitterPosition = 468;
            this.splitContainerControl4.TabIndex = 5;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // chartRegionCreate
            // 
            this.chartRegionCreate.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            simpleDiagram11.Dimension = 2;
            this.chartRegionCreate.Diagram = simpleDiagram11;
            this.chartRegionCreate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartRegionCreate.Location = new System.Drawing.Point(0, 0);
            this.chartRegionCreate.Name = "chartRegionCreate";
            this.chartRegionCreate.RuntimeSelection = true;
            this.chartRegionCreate.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel21.LineVisible = true;
            series29.Label = pieSeriesLabel21;
            piePointOptions21.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            piePointOptions21.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series29.LegendPointOptions = piePointOptions21;
            series29.Name = "mainRegion";
            piePointOptions22.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series29.PointOptions = piePointOptions22;
            series29.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series29.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series29.SynchronizePointOptions = false;
            pieSeriesView21.ExplodeMode = DevExpress.XtraCharts.PieExplodeMode.All;
            pieSeriesView21.Rotation = 100;
            pieSeriesView21.RuntimeExploding = false;
            series29.View = pieSeriesView21;
            this.chartRegionCreate.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series29};
            pieSeriesLabel22.LineVisible = true;
            this.chartRegionCreate.SeriesTemplate.Label = pieSeriesLabel22;
            pieSeriesView22.RuntimeExploding = false;
            this.chartRegionCreate.SeriesTemplate.View = pieSeriesView22;
            this.chartRegionCreate.Size = new System.Drawing.Size(468, 188);
            this.chartRegionCreate.TabIndex = 1;
            this.chartRegionCreate.ObjectSelected += new DevExpress.XtraCharts.HotTrackEventHandler(this.chartRegionCreate_ObjectSelected);
            // 
            // chartGridCreate
            // 
            this.chartGridCreate.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            xyDiagram11.AxisX.Label.Staggered = true;
            xyDiagram11.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram11.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram11.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram11.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram11.AxisY.NumericOptions.Precision = 0;
            xyDiagram11.AxisY.Range.Auto = false;
            xyDiagram11.AxisY.Range.MaxValueSerializable = "1";
            xyDiagram11.AxisY.Range.MinValueSerializable = "0";
            xyDiagram11.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram11.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram11.AxisY.VisibleInPanesSerializable = "-1";
            this.chartGridCreate.Diagram = xyDiagram11;
            this.chartGridCreate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartGridCreate.EmptyChartText.Text = "请点选左边区域图标项";
            this.chartGridCreate.EmptyChartText.TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.chartGridCreate.Legend.AlignmentHorizontal = DevExpress.XtraCharts.LegendAlignmentHorizontal.Right;
            this.chartGridCreate.Legend.Direction = DevExpress.XtraCharts.LegendDirection.RightToLeft;
            this.chartGridCreate.Legend.EquallySpacedItems = false;
            this.chartGridCreate.Legend.Visible = false;
            this.chartGridCreate.Location = new System.Drawing.Point(0, 0);
            this.chartGridCreate.Name = "chartGridCreate";
            this.chartGridCreate.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            sideBySideBarSeriesLabel17.LineVisible = true;
            series30.Label = sideBySideBarSeriesLabel17;
            series30.Name = "seriesGridCreate";
            pointOptions21.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions21.ValueNumericOptions.Precision = 0;
            series30.PointOptions = pointOptions21;
            series30.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series30.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series30.ShowInLegend = false;
            this.chartGridCreate.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series30};
            sideBySideBarSeriesLabel18.LineVisible = true;
            this.chartGridCreate.SeriesTemplate.Label = sideBySideBarSeriesLabel18;
            this.chartGridCreate.Size = new System.Drawing.Size(692, 188);
            this.chartGridCreate.TabIndex = 3;
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.splitContainerControl2);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1170, 207);
            this.groupControl3.TabIndex = 3;
            this.groupControl3.Text = "已关闭黑点统计图";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Location = new System.Drawing.Point(2, 23);
            this.splitContainerControl2.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl2.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.chartRegionClose);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.chartGridClose);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1166, 182);
            this.splitContainerControl2.SplitterPosition = 469;
            this.splitContainerControl2.TabIndex = 2;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // chartRegionClose
            // 
            this.chartRegionClose.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            simpleDiagram12.Dimension = 2;
            this.chartRegionClose.Diagram = simpleDiagram12;
            this.chartRegionClose.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartRegionClose.Location = new System.Drawing.Point(0, 0);
            this.chartRegionClose.Name = "chartRegionClose";
            this.chartRegionClose.RuntimeSelection = true;
            this.chartRegionClose.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel23.LineVisible = true;
            series31.Label = pieSeriesLabel23;
            piePointOptions23.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            piePointOptions23.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series31.LegendPointOptions = piePointOptions23;
            series31.Name = "mainRegion";
            piePointOptions24.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series31.PointOptions = piePointOptions24;
            series31.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series31.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series31.SynchronizePointOptions = false;
            pieSeriesView23.ExplodeMode = DevExpress.XtraCharts.PieExplodeMode.All;
            pieSeriesView23.Rotation = 100;
            pieSeriesView23.RuntimeExploding = false;
            series31.View = pieSeriesView23;
            sideBySideBarSeriesLabel19.LineVisible = true;
            series32.Label = sideBySideBarSeriesLabel19;
            pointOptions22.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            pointOptions22.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions22.ValueNumericOptions.Precision = 0;
            series32.LegendPointOptions = pointOptions22;
            series32.Name = "subRegion";
            pointOptions23.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions23.ValueNumericOptions.Precision = 0;
            series32.PointOptions = pointOptions23;
            series32.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series32.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series32.SynchronizePointOptions = false;
            series32.Visible = false;
            this.chartRegionClose.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series31,
        series32};
            pieSeriesLabel24.LineVisible = true;
            this.chartRegionClose.SeriesTemplate.Label = pieSeriesLabel24;
            pieSeriesView24.RuntimeExploding = false;
            this.chartRegionClose.SeriesTemplate.View = pieSeriesView24;
            this.chartRegionClose.Size = new System.Drawing.Size(469, 182);
            this.chartRegionClose.TabIndex = 1;
            this.chartRegionClose.ObjectSelected += new DevExpress.XtraCharts.HotTrackEventHandler(this.chartRegionClose_ObjectSelected);
            // 
            // chartGridClose
            // 
            this.chartGridClose.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            xyDiagram12.AxisX.Label.Staggered = true;
            xyDiagram12.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram12.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram12.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram12.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram12.AxisY.NumericOptions.Precision = 0;
            xyDiagram12.AxisY.Range.Auto = false;
            xyDiagram12.AxisY.Range.MaxValueSerializable = "1";
            xyDiagram12.AxisY.Range.MinValueSerializable = "0";
            xyDiagram12.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram12.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram12.AxisY.VisibleInPanesSerializable = "-1";
            this.chartGridClose.Diagram = xyDiagram12;
            this.chartGridClose.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartGridClose.EmptyChartText.Text = "请点选左边区域图标项";
            this.chartGridClose.EmptyChartText.TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.chartGridClose.Legend.AlignmentHorizontal = DevExpress.XtraCharts.LegendAlignmentHorizontal.Right;
            this.chartGridClose.Legend.Direction = DevExpress.XtraCharts.LegendDirection.RightToLeft;
            this.chartGridClose.Legend.EquallySpacedItems = false;
            this.chartGridClose.Legend.Visible = false;
            this.chartGridClose.Location = new System.Drawing.Point(0, 0);
            this.chartGridClose.Name = "chartGridClose";
            this.chartGridClose.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            sideBySideBarSeriesLabel20.LineVisible = true;
            series33.Label = sideBySideBarSeriesLabel20;
            series33.Name = "seriesGridClose";
            pointOptions24.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions24.ValueNumericOptions.Precision = 0;
            series33.PointOptions = pointOptions24;
            series33.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series33.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series33.ShowInLegend = false;
            this.chartGridClose.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series33};
            sideBySideBarSeriesLabel21.LineVisible = true;
            this.chartGridClose.SeriesTemplate.Label = sideBySideBarSeriesLabel21;
            this.chartGridClose.Size = new System.Drawing.Size(691, 182);
            this.chartGridClose.TabIndex = 4;
            // 
            // tabCause
            // 
            this.tabCause.Controls.Add(this.splitContainerControl5);
            this.tabCause.Location = new System.Drawing.Point(4, 23);
            this.tabCause.Name = "tabCause";
            this.tabCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabCause.Size = new System.Drawing.Size(1176, 653);
            this.tabCause.TabIndex = 1;
            this.tabCause.Text = "原因统计";
            this.tabCause.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Appearance.ForeColor = System.Drawing.Color.Cyan;
            this.splitContainerControl5.Appearance.Options.UseForeColor = true;
            this.splitContainerControl5.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.Horizontal = false;
            this.splitContainerControl5.Location = new System.Drawing.Point(3, 3);
            this.splitContainerControl5.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl5.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.splitContainerControl6);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.Size = new System.Drawing.Size(1170, 647);
            this.splitContainerControl5.SplitterPosition = 305;
            this.splitContainerControl5.TabIndex = 3;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridCtrlCause);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1170, 305);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "地点";
            // 
            // gridCtrlCause
            // 
            this.gridCtrlCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlCause.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlCause.MainView = this.gridViewCaues;
            this.gridCtrlCause.Name = "gridCtrlCause";
            this.gridCtrlCause.Size = new System.Drawing.Size(1166, 280);
            this.gridCtrlCause.TabIndex = 1;
            this.gridCtrlCause.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCaues});
            // 
            // gridViewCaues
            // 
            this.gridViewCaues.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn14,
            this.gridColumn13,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12});
            this.gridViewCaues.GridControl = this.gridCtrlCause;
            this.gridViewCaues.Name = "gridViewCaues";
            this.gridViewCaues.OptionsBehavior.Editable = false;
            this.gridViewCaues.OptionsView.AllowCellMerge = true;
            this.gridViewCaues.OptionsView.RowAutoHeight = true;
            this.gridViewCaues.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "主因";
            this.gridColumn7.FieldName = "主因";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "原因";
            this.gridColumn8.FieldName = "原因";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "原因分析";
            this.gridColumn9.FieldName = "原因分析";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 2;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "区域";
            this.gridColumn14.FieldName = "区域";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "ID";
            this.gridColumn13.FieldName = "ID";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "状态";
            this.gridColumn10.FieldName = "状态";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 5;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "创建日期";
            this.gridColumn11.FieldName = "创建日期";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 6;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "关闭日期";
            this.gridColumn12.FieldName = "关闭日期";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 7;
            // 
            // splitContainerControl6
            // 
            this.splitContainerControl6.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl6.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl6.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl6.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl6.Name = "splitContainerControl6";
            this.splitContainerControl6.Panel1.Controls.Add(this.tabCtrlCausePie);
            this.splitContainerControl6.Panel1.Text = "Panel1";
            this.splitContainerControl6.Panel2.Controls.Add(this.tabCtrlBarCause);
            this.splitContainerControl6.Panel2.Text = "Panel2";
            this.splitContainerControl6.Size = new System.Drawing.Size(1170, 336);
            this.splitContainerControl6.SplitterPosition = 431;
            this.splitContainerControl6.TabIndex = 1;
            this.splitContainerControl6.Text = "splitContainerControl6";
            // 
            // tabCtrlCausePie
            // 
            this.tabCtrlCausePie.Controls.Add(this.tabMainCause);
            this.tabCtrlCausePie.Controls.Add(this.tabSubCause);
            this.tabCtrlCausePie.Controls.Add(this.tabDetailCause);
            this.tabCtrlCausePie.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrlCausePie.Location = new System.Drawing.Point(0, 0);
            this.tabCtrlCausePie.Name = "tabCtrlCausePie";
            this.tabCtrlCausePie.SelectedIndex = 0;
            this.tabCtrlCausePie.Size = new System.Drawing.Size(431, 336);
            this.tabCtrlCausePie.TabIndex = 2;
            // 
            // tabMainCause
            // 
            this.tabMainCause.Controls.Add(this.chartPieMainCause);
            this.tabMainCause.Location = new System.Drawing.Point(4, 23);
            this.tabMainCause.Name = "tabMainCause";
            this.tabMainCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabMainCause.Size = new System.Drawing.Size(423, 309);
            this.tabMainCause.TabIndex = 0;
            this.tabMainCause.Text = "主因";
            this.tabMainCause.UseVisualStyleBackColor = true;
            // 
            // chartPieMainCause
            // 
            this.chartPieMainCause.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            simpleDiagram13.Dimension = 2;
            this.chartPieMainCause.Diagram = simpleDiagram13;
            this.chartPieMainCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartPieMainCause.Location = new System.Drawing.Point(3, 3);
            this.chartPieMainCause.Name = "chartPieMainCause";
            this.chartPieMainCause.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel25.LineVisible = true;
            series34.Label = pieSeriesLabel25;
            piePointOptions25.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            piePointOptions25.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series34.LegendPointOptions = piePointOptions25;
            series34.Name = "series";
            piePointOptions26.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            piePointOptions26.ValueNumericOptions.Precision = 0;
            series34.PointOptions = piePointOptions26;
            series34.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series34.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series34.SynchronizePointOptions = false;
            pieSeriesView25.ExplodeMode = DevExpress.XtraCharts.PieExplodeMode.All;
            pieSeriesView25.Rotation = 100;
            pieSeriesView25.RuntimeExploding = false;
            series34.View = pieSeriesView25;
            sideBySideBarSeriesLabel22.LineVisible = true;
            series35.Label = sideBySideBarSeriesLabel22;
            pointOptions25.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            pointOptions25.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series35.LegendPointOptions = pointOptions25;
            series35.Name = "subRegion";
            pointOptions26.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series35.PointOptions = pointOptions26;
            series35.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series35.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series35.SynchronizePointOptions = false;
            series35.Visible = false;
            this.chartPieMainCause.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series34,
        series35};
            pieSeriesLabel26.LineVisible = true;
            this.chartPieMainCause.SeriesTemplate.Label = pieSeriesLabel26;
            pieSeriesView26.RuntimeExploding = false;
            this.chartPieMainCause.SeriesTemplate.View = pieSeriesView26;
            this.chartPieMainCause.Size = new System.Drawing.Size(417, 303);
            this.chartPieMainCause.TabIndex = 1;
            // 
            // tabSubCause
            // 
            this.tabSubCause.Controls.Add(this.chartPieSubCause);
            this.tabSubCause.Location = new System.Drawing.Point(4, 23);
            this.tabSubCause.Name = "tabSubCause";
            this.tabSubCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabSubCause.Size = new System.Drawing.Size(423, 309);
            this.tabSubCause.TabIndex = 1;
            this.tabSubCause.Text = "原因";
            this.tabSubCause.UseVisualStyleBackColor = true;
            // 
            // chartPieSubCause
            // 
            this.chartPieSubCause.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            simpleDiagram14.Dimension = 2;
            this.chartPieSubCause.Diagram = simpleDiagram14;
            this.chartPieSubCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartPieSubCause.Location = new System.Drawing.Point(3, 3);
            this.chartPieSubCause.Name = "chartPieSubCause";
            this.chartPieSubCause.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel27.LineVisible = true;
            series36.Label = pieSeriesLabel27;
            piePointOptions27.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            piePointOptions27.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series36.LegendPointOptions = piePointOptions27;
            series36.Name = "series";
            piePointOptions28.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series36.PointOptions = piePointOptions28;
            series36.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series36.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series36.SynchronizePointOptions = false;
            pieSeriesView27.ExplodeMode = DevExpress.XtraCharts.PieExplodeMode.All;
            pieSeriesView27.Rotation = 100;
            pieSeriesView27.RuntimeExploding = false;
            series36.View = pieSeriesView27;
            sideBySideBarSeriesLabel23.LineVisible = true;
            series37.Label = sideBySideBarSeriesLabel23;
            pointOptions27.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            pointOptions27.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions27.ValueNumericOptions.Precision = 0;
            series37.LegendPointOptions = pointOptions27;
            series37.Name = "subRegion";
            pointOptions28.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions28.ValueNumericOptions.Precision = 0;
            series37.PointOptions = pointOptions28;
            series37.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series37.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series37.SynchronizePointOptions = false;
            series37.Visible = false;
            this.chartPieSubCause.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series36,
        series37};
            pieSeriesLabel28.LineVisible = true;
            this.chartPieSubCause.SeriesTemplate.Label = pieSeriesLabel28;
            pieSeriesView28.RuntimeExploding = false;
            this.chartPieSubCause.SeriesTemplate.View = pieSeriesView28;
            this.chartPieSubCause.Size = new System.Drawing.Size(417, 303);
            this.chartPieSubCause.TabIndex = 1;
            // 
            // tabDetailCause
            // 
            this.tabDetailCause.Controls.Add(this.chartPieDetailCause);
            this.tabDetailCause.Location = new System.Drawing.Point(4, 23);
            this.tabDetailCause.Name = "tabDetailCause";
            this.tabDetailCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabDetailCause.Size = new System.Drawing.Size(423, 309);
            this.tabDetailCause.TabIndex = 2;
            this.tabDetailCause.Text = "原因分析";
            this.tabDetailCause.UseVisualStyleBackColor = true;
            // 
            // chartPieDetailCause
            // 
            this.chartPieDetailCause.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            simpleDiagram15.Dimension = 2;
            this.chartPieDetailCause.Diagram = simpleDiagram15;
            this.chartPieDetailCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartPieDetailCause.Location = new System.Drawing.Point(3, 3);
            this.chartPieDetailCause.Name = "chartPieDetailCause";
            this.chartPieDetailCause.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel29.LineVisible = true;
            series38.Label = pieSeriesLabel29;
            piePointOptions29.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            piePointOptions29.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            piePointOptions29.ValueNumericOptions.Precision = 0;
            series38.LegendPointOptions = piePointOptions29;
            series38.Name = "series";
            piePointOptions30.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series38.PointOptions = piePointOptions30;
            series38.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series38.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series38.SynchronizePointOptions = false;
            pieSeriesView29.ExplodeMode = DevExpress.XtraCharts.PieExplodeMode.All;
            pieSeriesView29.Rotation = 100;
            pieSeriesView29.RuntimeExploding = false;
            series38.View = pieSeriesView29;
            sideBySideBarSeriesLabel24.LineVisible = true;
            series39.Label = sideBySideBarSeriesLabel24;
            pointOptions29.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            pointOptions29.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions29.ValueNumericOptions.Precision = 0;
            series39.LegendPointOptions = pointOptions29;
            series39.Name = "subRegion";
            pointOptions30.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions30.ValueNumericOptions.Precision = 0;
            series39.PointOptions = pointOptions30;
            series39.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series39.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series39.SynchronizePointOptions = false;
            series39.Visible = false;
            this.chartPieDetailCause.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series38,
        series39};
            pieSeriesLabel30.LineVisible = true;
            this.chartPieDetailCause.SeriesTemplate.Label = pieSeriesLabel30;
            pieSeriesView30.RuntimeExploding = false;
            this.chartPieDetailCause.SeriesTemplate.View = pieSeriesView30;
            this.chartPieDetailCause.Size = new System.Drawing.Size(417, 303);
            this.chartPieDetailCause.TabIndex = 2;
            // 
            // tabCtrlBarCause
            // 
            this.tabCtrlBarCause.Controls.Add(this.tabBarMainCause);
            this.tabCtrlBarCause.Controls.Add(this.tabBarSubCause);
            this.tabCtrlBarCause.Controls.Add(this.tabBarDetailCause);
            this.tabCtrlBarCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrlBarCause.Location = new System.Drawing.Point(0, 0);
            this.tabCtrlBarCause.Name = "tabCtrlBarCause";
            this.tabCtrlBarCause.SelectedIndex = 0;
            this.tabCtrlBarCause.Size = new System.Drawing.Size(733, 336);
            this.tabCtrlBarCause.TabIndex = 3;
            // 
            // tabBarMainCause
            // 
            this.tabBarMainCause.Controls.Add(this.chartBarMainCause);
            this.tabBarMainCause.Location = new System.Drawing.Point(4, 23);
            this.tabBarMainCause.Name = "tabBarMainCause";
            this.tabBarMainCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabBarMainCause.Size = new System.Drawing.Size(725, 309);
            this.tabBarMainCause.TabIndex = 0;
            this.tabBarMainCause.Text = "主因";
            this.tabBarMainCause.UseVisualStyleBackColor = true;
            // 
            // chartBarMainCause
            // 
            this.chartBarMainCause.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            xyDiagram13.AxisX.Label.Staggered = true;
            xyDiagram13.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram13.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram13.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram13.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram13.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram13.AxisY.VisibleInPanesSerializable = "-1";
            this.chartBarMainCause.Diagram = xyDiagram13;
            this.chartBarMainCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartBarMainCause.Legend.AlignmentVertical = DevExpress.XtraCharts.LegendAlignmentVertical.TopOutside;
            this.chartBarMainCause.Legend.EquallySpacedItems = false;
            this.chartBarMainCause.Location = new System.Drawing.Point(3, 3);
            this.chartBarMainCause.Name = "chartBarMainCause";
            this.chartBarMainCause.PaletteName = "Metro";
            this.chartBarMainCause.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            series40.Label = fullStackedBarSeriesLabel13;
            fullStackedBarPointOptions13.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            fullStackedBarPointOptions13.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series40.LegendPointOptions = fullStackedBarPointOptions13;
            series40.Name = "series";
            fullStackedBarPointOptions14.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series40.PointOptions = fullStackedBarPointOptions14;
            series40.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Ascending;
            series40.SynchronizePointOptions = false;
            series40.View = fullStackedBarSeriesView13;
            this.chartBarMainCause.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series40};
            this.chartBarMainCause.SeriesTemplate.Label = fullStackedBarSeriesLabel14;
            this.chartBarMainCause.SeriesTemplate.View = fullStackedBarSeriesView14;
            this.chartBarMainCause.Size = new System.Drawing.Size(719, 303);
            this.chartBarMainCause.TabIndex = 1;
            // 
            // tabBarSubCause
            // 
            this.tabBarSubCause.Controls.Add(this.chartBarSubCause);
            this.tabBarSubCause.Location = new System.Drawing.Point(4, 23);
            this.tabBarSubCause.Name = "tabBarSubCause";
            this.tabBarSubCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabBarSubCause.Size = new System.Drawing.Size(725, 309);
            this.tabBarSubCause.TabIndex = 1;
            this.tabBarSubCause.Text = "原因";
            this.tabBarSubCause.UseVisualStyleBackColor = true;
            // 
            // chartBarSubCause
            // 
            this.chartBarSubCause.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            xyDiagram14.AxisX.Label.Staggered = true;
            xyDiagram14.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram14.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram14.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram14.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram14.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram14.AxisY.VisibleInPanesSerializable = "-1";
            this.chartBarSubCause.Diagram = xyDiagram14;
            this.chartBarSubCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartBarSubCause.Legend.AlignmentVertical = DevExpress.XtraCharts.LegendAlignmentVertical.TopOutside;
            this.chartBarSubCause.Legend.EquallySpacedItems = false;
            this.chartBarSubCause.Location = new System.Drawing.Point(3, 3);
            this.chartBarSubCause.Name = "chartBarSubCause";
            this.chartBarSubCause.PaletteName = "Metro";
            this.chartBarSubCause.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            series41.Label = fullStackedBarSeriesLabel15;
            fullStackedBarPointOptions15.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            fullStackedBarPointOptions15.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series41.LegendPointOptions = fullStackedBarPointOptions15;
            series41.Name = "series";
            fullStackedBarPointOptions16.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series41.PointOptions = fullStackedBarPointOptions16;
            series41.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Ascending;
            series41.SynchronizePointOptions = false;
            series41.View = fullStackedBarSeriesView15;
            this.chartBarSubCause.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series41};
            this.chartBarSubCause.SeriesTemplate.Label = fullStackedBarSeriesLabel16;
            this.chartBarSubCause.SeriesTemplate.View = fullStackedBarSeriesView16;
            this.chartBarSubCause.Size = new System.Drawing.Size(719, 303);
            this.chartBarSubCause.TabIndex = 2;
            // 
            // tabBarDetailCause
            // 
            this.tabBarDetailCause.Controls.Add(this.chartBarDetailCause);
            this.tabBarDetailCause.Location = new System.Drawing.Point(4, 23);
            this.tabBarDetailCause.Name = "tabBarDetailCause";
            this.tabBarDetailCause.Padding = new System.Windows.Forms.Padding(3);
            this.tabBarDetailCause.Size = new System.Drawing.Size(725, 309);
            this.tabBarDetailCause.TabIndex = 2;
            this.tabBarDetailCause.Text = "原因分析";
            this.tabBarDetailCause.UseVisualStyleBackColor = true;
            // 
            // chartBarDetailCause
            // 
            this.chartBarDetailCause.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            xyDiagram15.AxisX.Label.Staggered = true;
            xyDiagram15.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram15.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram15.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram15.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram15.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram15.AxisY.VisibleInPanesSerializable = "-1";
            this.chartBarDetailCause.Diagram = xyDiagram15;
            this.chartBarDetailCause.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartBarDetailCause.Legend.AlignmentVertical = DevExpress.XtraCharts.LegendAlignmentVertical.TopOutside;
            this.chartBarDetailCause.Legend.EquallySpacedItems = false;
            this.chartBarDetailCause.Location = new System.Drawing.Point(3, 3);
            this.chartBarDetailCause.Name = "chartBarDetailCause";
            this.chartBarDetailCause.PaletteName = "Metro";
            this.chartBarDetailCause.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            series42.Label = fullStackedBarSeriesLabel17;
            fullStackedBarPointOptions17.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            fullStackedBarPointOptions17.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series42.LegendPointOptions = fullStackedBarPointOptions17;
            series42.Name = "series";
            fullStackedBarPointOptions18.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series42.PointOptions = fullStackedBarPointOptions18;
            series42.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Ascending;
            series42.SynchronizePointOptions = false;
            series42.View = fullStackedBarSeriesView17;
            this.chartBarDetailCause.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series42};
            this.chartBarDetailCause.SeriesTemplate.Label = fullStackedBarSeriesLabel18;
            this.chartBarDetailCause.SeriesTemplate.View = fullStackedBarSeriesView18;
            this.chartBarDetailCause.Size = new System.Drawing.Size(719, 303);
            this.chartBarDetailCause.TabIndex = 2;
            // 
            // BlackBlockStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 680);
            this.Controls.Add(this.tabControl);
            this.Name = "BlackBlockStatForm";
            this.Text = "黑点统计列表/图";
            this.tabControl.ResumeLayout(false);
            this.ctxMenu.ResumeLayout(false);
            this.tabRegion.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlRegion)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartRegionCreate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartGridCreate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series31)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series32)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartRegionClose)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series33)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartGridClose)).EndInit();
            this.tabCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlCause)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCaues)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).EndInit();
            this.splitContainerControl6.ResumeLayout(false);
            this.tabCtrlCausePie.ResumeLayout(false);
            this.tabMainCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series34)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series35)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartPieMainCause)).EndInit();
            this.tabSubCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series36)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series37)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartPieSubCause)).EndInit();
            this.tabDetailCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(simpleDiagram15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series38)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series39)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartPieDetailCause)).EndInit();
            this.tabCtrlBarCause.ResumeLayout(false);
            this.tabBarMainCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series40)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartBarMainCause)).EndInit();
            this.tabBarSubCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartBarSubCause)).EndInit();
            this.tabBarDetailCause.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series42)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesLabel18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(fullStackedBarSeriesView18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartBarDetailCause)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabRegion;
        private System.Windows.Forms.TabPage tabCause;
        private DevExpress.XtraCharts.ChartControl chartRegionCreate;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraCharts.ChartControl chartRegionClose;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraGrid.GridControl gridCtrlCause;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCaues;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl6;
        private DevExpress.XtraCharts.ChartControl chartPieMainCause;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraGrid.GridControl gridCtrlRegion;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRegion;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraCharts.ChartControl chartGridCreate;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraCharts.ChartControl chartGridClose;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private System.Windows.Forms.TabControl tabCtrlCausePie;
        private System.Windows.Forms.TabPage tabMainCause;
        private System.Windows.Forms.TabPage tabSubCause;
        private DevExpress.XtraCharts.ChartControl chartPieSubCause;
        private System.Windows.Forms.TabPage tabDetailCause;
        private DevExpress.XtraCharts.ChartControl chartPieDetailCause;
        private System.Windows.Forms.TabControl tabCtrlBarCause;
        private System.Windows.Forms.TabPage tabBarMainCause;
        private DevExpress.XtraCharts.ChartControl chartBarMainCause;
        private System.Windows.Forms.TabPage tabBarSubCause;
        private System.Windows.Forms.TabPage tabBarDetailCause;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraCharts.ChartControl chartBarSubCause;
        private DevExpress.XtraCharts.ChartControl chartBarDetailCause;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}