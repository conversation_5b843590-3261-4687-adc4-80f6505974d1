﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteAntParaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY1 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY2 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView1 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel1 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView1 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel2 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView2 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY3 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView2 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView3 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.RadarDiagram radarDiagram1 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel1 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint1 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint2 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint3 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint4 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint5 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView1 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel2 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView2 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel3 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView3 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram2 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel4 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint6 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint7 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint8 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint9 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint10 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView1 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series8 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel5 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView2 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel6 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView3 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram3 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series9 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel7 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint11 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint12 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint13 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint14 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint15 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView4 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series10 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel8 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView5 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel9 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView6 = new DevExpress.XtraCharts.RadarPointSeriesView();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.colIndex = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colvender = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column41 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcovertype = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colbeamwidth = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column62 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column63 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column64 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colgmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column56 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column57 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column58 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column59 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column60 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column61 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column27 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column33 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column35 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column36 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column37 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column38 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column40 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column42 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column43 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column46 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column44 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column47 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column48 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column49 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column50 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column51 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column52 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column53 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column54 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column55 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column23 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column25 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column34 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column39 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column45 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShwoChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.miShwoMRChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShwoMRGis = new System.Windows.Forms.ToolStripMenuItem();
            this.导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.cluCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cluTarget = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbbxSeries2 = new System.Windows.Forms.ComboBox();
            this.cbbxSeries1 = new System.Windows.Forms.ComboBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridViewPro = new System.Windows.Forms.DataGridView();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.chartControl5 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chartControl3 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartControl2 = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupCtrlMR = new DevExpress.XtraEditors.GroupControl();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.chartCtrlMRLine = new DevExpress.XtraCharts.ChartControl();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.chartCtrlMRSample = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPro)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView2)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupCtrlMR)).BeginInit();
            this.groupCtrlMR.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartCtrlMRLine)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView3)).BeginInit();
            this.groupBox15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartCtrlMRSample)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView6)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage4;
            this.xtraTabControl1.Size = new System.Drawing.Size(1184, 650);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage4,
            this.xtraTabPage1,
            this.xtraTabPage3,
            this.xtraTabPage5,
            this.xtraTabPage2});
            this.xtraTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.btnNextpage);
            this.xtraTabPage4.Controls.Add(this.btnPrevpage);
            this.xtraTabPage4.Controls.Add(this.label5);
            this.xtraTabPage4.Controls.Add(this.txtPage);
            this.xtraTabPage4.Controls.Add(this.labPage);
            this.xtraTabPage4.Controls.Add(this.label4);
            this.xtraTabPage4.Controls.Add(this.btnGo);
            this.xtraTabPage4.Controls.Add(this.labNum);
            this.xtraTabPage4.Controls.Add(this.label3);
            this.xtraTabPage4.Controls.Add(this.label2);
            this.xtraTabPage4.Controls.Add(this.btnSearch);
            this.xtraTabPage4.Controls.Add(this.txtCellName);
            this.xtraTabPage4.Controls.Add(this.label1);
            this.xtraTabPage4.Controls.Add(this.dataGridViewCell);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage4.Text = "小区权值设置及覆盖统计";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(860, 595);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 16;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(821, 595);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 15;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(763, 597);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 14;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(698, 594);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 13;
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(598, 599);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 12;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(555, 598);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 11;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(782, 594);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 9;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(510, 599);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 8;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(632, 598);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 7;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(434, 598);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 6;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1128, 594);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 5;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(966, 595);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 4;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(905, 599);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 3;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllowUserToAddRows = false;
            this.dataGridViewCell.AllowUserToDeleteRows = false;
            this.dataGridViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colIndex,
            this.Column11,
            this.Column1,
            this.Column14,
            this.colvender,
            this.Column41,
            this.Column2,
            this.colcgi,
            this.colcovertype,
            this.Column3,
            this.colbeamwidth,
            this.Column22,
            this.Column4,
            this.Column5,
            this.Column6,
            this.Column7,
            this.Column62,
            this.Column63,
            this.Column64,
            this.colgmax,
            this.col3db,
            this.col6db,
            this.Column8,
            this.Column9,
            this.Column10,
            this.Column12,
            this.Column13,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column18,
            this.Column19,
            this.Column20,
            this.Column56,
            this.Column57,
            this.Column58,
            this.Column59,
            this.Column60,
            this.Column61,
            this.Column21,
            this.Column26,
            this.Column24,
            this.Column27,
            this.Column28,
            this.Column30,
            this.Column31,
            this.Column32,
            this.Column33,
            this.Column35,
            this.Column36,
            this.Column37,
            this.Column38,
            this.Column40,
            this.Column42,
            this.Column43,
            this.Column46,
            this.Column44,
            this.Column47,
            this.Column48,
            this.Column49,
            this.Column50,
            this.Column51,
            this.Column52,
            this.Column53,
            this.Column54,
            this.Column55,
            this.Column23,
            this.Column25,
            this.Column29,
            this.Column34,
            this.Column39,
            this.Column45,
            this.colrange1,
            this.colrange2,
            this.colrange3,
            this.colrange4,
            this.colrange5,
            this.colrange6,
            this.colrange7,
            this.colrange8,
            this.colphase1,
            this.colphase2,
            this.colphase3,
            this.colphase4,
            this.colphase5,
            this.colphase6,
            this.colphase7,
            this.colphase8});
            this.dataGridViewCell.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewCell.Size = new System.Drawing.Size(1177, 590);
            this.dataGridViewCell.TabIndex = 2;
            // 
            // colIndex
            // 
            this.colIndex.HeaderText = "序号";
            this.colIndex.Name = "colIndex";
            // 
            // Column11
            // 
            this.Column11.HeaderText = "地市";
            this.Column11.Name = "Column11";
            // 
            // Column1
            // 
            this.Column1.HeaderText = "小区英文名";
            this.Column1.Name = "Column1";
            // 
            // Column14
            // 
            this.Column14.HeaderText = "小区名";
            this.Column14.Name = "Column14";
            // 
            // colvender
            // 
            this.colvender.HeaderText = "主设备厂家";
            this.colvender.Name = "colvender";
            // 
            // Column41
            // 
            this.Column41.HeaderText = "是否匹配工参";
            this.Column41.Name = "Column41";
            // 
            // Column2
            // 
            this.Column2.HeaderText = "天线权值";
            this.Column2.Name = "Column2";
            // 
            // colcgi
            // 
            this.colcgi.HeaderText = "CGI";
            this.colcgi.Name = "colcgi";
            // 
            // colcovertype
            // 
            this.colcovertype.HeaderText = "覆盖类型";
            this.colcovertype.Name = "colcovertype";
            // 
            // Column3
            // 
            this.Column3.HeaderText = "小区频段";
            this.Column3.Name = "Column3";
            // 
            // colbeamwidth
            // 
            this.colbeamwidth.HeaderText = "波束宽度";
            this.colbeamwidth.Name = "colbeamwidth";
            // 
            // Column22
            // 
            this.Column22.HeaderText = "方位角";
            this.Column22.Name = "Column22";
            // 
            // Column4
            // 
            this.Column4.HeaderText = "预置下倾角";
            this.Column4.Name = "Column4";
            // 
            // Column5
            // 
            this.Column5.HeaderText = "机械下倾角";
            this.Column5.Name = "Column5";
            // 
            // Column6
            // 
            this.Column6.HeaderText = "电调下倾角";
            this.Column6.Name = "Column6";
            // 
            // Column7
            // 
            this.Column7.HeaderText = "挂高";
            this.Column7.Name = "Column7";
            // 
            // Column62
            // 
            this.Column62.HeaderText = "分析结果";
            this.Column62.Name = "Column62";
            // 
            // Column63
            // 
            this.Column63.HeaderText = "告警状态";
            this.Column63.Name = "Column63";
            // 
            // Column64
            // 
            this.Column64.HeaderText = "评估结果";
            this.Column64.Name = "Column64";
            // 
            // colgmax
            // 
            this.colgmax.HeaderText = "Gmax(天线权值计算)";
            this.colgmax.Name = "colgmax";
            // 
            // col3db
            // 
            this.col3db.HeaderText = "3dB功率角";
            this.col3db.Name = "col3db";
            // 
            // col6db
            // 
            this.col6db.HeaderText = "6dB功率角";
            this.col6db.Name = "col6db";
            // 
            // Column8
            // 
            this.Column8.HeaderText = "上行吞吐量";
            this.Column8.Name = "Column8";
            // 
            // Column9
            // 
            this.Column9.HeaderText = "下行吞吐量";
            this.Column9.Name = "Column9";
            // 
            // Column10
            // 
            this.Column10.HeaderText = "无线接通率";
            this.Column10.Name = "Column10";
            // 
            // Column12
            // 
            this.Column12.HeaderText = "无线掉线率";
            this.Column12.Name = "Column12";
            // 
            // Column13
            // 
            this.Column13.HeaderText = "切换成功率";
            this.Column13.Name = "Column13";
            // 
            // Column15
            // 
            this.Column15.HeaderText = "ERAB建立成功率";
            this.Column15.Name = "Column15";
            // 
            // Column16
            // 
            this.Column16.HeaderText = "ERAB掉线率";
            this.Column16.Name = "Column16";
            // 
            // Column17
            // 
            this.Column17.HeaderText = "RSRP均值";
            this.Column17.Name = "Column17";
            // 
            // Column18
            // 
            this.Column18.HeaderText = "SINR均值";
            this.Column18.Name = "Column18";
            // 
            // Column19
            // 
            this.Column19.HeaderText = "95覆盖率";
            this.Column19.Name = "Column19";
            // 
            // Column20
            // 
            this.Column20.HeaderText = "110覆盖率";
            this.Column20.Name = "Column20";
            // 
            // Column56
            // 
            this.Column56.HeaderText = "MRO总采样点数";
            this.Column56.Name = "Column56";
            // 
            // Column57
            // 
            this.Column57.HeaderText = "重叠覆盖条件采样点数";
            this.Column57.Name = "Column57";
            // 
            // Column58
            // 
            this.Column58.HeaderText = "重叠覆盖指数";
            this.Column58.Name = "Column58";
            // 
            // Column59
            // 
            this.Column59.HeaderText = "过覆盖影响小区数";
            this.Column59.Name = "Column59";
            // 
            // Column60
            // 
            this.Column60.HeaderText = "高重叠覆盖小区";
            this.Column60.Name = "Column60";
            // 
            // Column61
            // 
            this.Column61.HeaderText = "过覆盖小区";
            this.Column61.Name = "Column61";
            // 
            // Column21
            // 
            this.Column21.HeaderText = "扫频采样点总数";
            this.Column21.Name = "Column21";
            // 
            // Column26
            // 
            this.Column26.HeaderText = "主瓣采样点比例";
            this.Column26.Name = "Column26";
            // 
            // Column24
            // 
            this.Column24.HeaderText = "主瓣最强信号强度";
            this.Column24.Name = "Column24";
            // 
            // Column27
            // 
            this.Column27.HeaderText = "疑似旁瓣数量";
            this.Column27.Name = "Column27";
            // 
            // Column28
            // 
            this.Column28.HeaderText = "旁瓣1辐射方向";
            this.Column28.Name = "Column28";
            // 
            // Column30
            // 
            this.Column30.HeaderText = "旁瓣1最强信号强度";
            this.Column30.Name = "Column30";
            // 
            // Column31
            // 
            this.Column31.HeaderText = "旁瓣1平均信号强度";
            this.Column31.Name = "Column31";
            // 
            // Column32
            // 
            this.Column32.HeaderText = "旁瓣1采样点比例";
            this.Column32.Name = "Column32";
            // 
            // Column33
            // 
            this.Column33.HeaderText = "旁瓣2辐射方向";
            this.Column33.Name = "Column33";
            // 
            // Column35
            // 
            this.Column35.HeaderText = "旁瓣2最强信号强度";
            this.Column35.Name = "Column35";
            // 
            // Column36
            // 
            this.Column36.HeaderText = "旁瓣2平均信号强度";
            this.Column36.Name = "Column36";
            // 
            // Column37
            // 
            this.Column37.HeaderText = "旁瓣2采样点比例";
            this.Column37.Name = "Column37";
            // 
            // Column38
            // 
            this.Column38.HeaderText = "旁瓣3辐射方向";
            this.Column38.Name = "Column38";
            // 
            // Column40
            // 
            this.Column40.HeaderText = "旁瓣3最强信号强度";
            this.Column40.Name = "Column40";
            // 
            // Column42
            // 
            this.Column42.HeaderText = "旁瓣3平均信号强度";
            this.Column42.Name = "Column42";
            // 
            // Column43
            // 
            this.Column43.HeaderText = "旁瓣3采样点比例";
            this.Column43.Name = "Column43";
            // 
            // Column46
            // 
            this.Column46.HeaderText = "背瓣采样点比例";
            this.Column46.Name = "Column46";
            // 
            // Column44
            // 
            this.Column44.HeaderText = "前后比";
            this.Column44.Name = "Column44";
            // 
            // Column47
            // 
            this.Column47.HeaderText = "路测采样点";
            this.Column47.Name = "Column47";
            // 
            // Column48
            // 
            this.Column48.HeaderText = "覆盖率(RSRP≥-110&SINR>=-3)";
            this.Column48.Name = "Column48";
            // 
            // Column49
            // 
            this.Column49.HeaderText = "LTE覆盖率(RSRP≥-110)";
            this.Column49.Name = "Column49";
            // 
            // Column50
            // 
            this.Column50.HeaderText = "LTE覆盖率(SINR>=-3)";
            this.Column50.Name = "Column50";
            // 
            // Column51
            // 
            this.Column51.HeaderText = "小区平均RSRP";
            this.Column51.Name = "Column51";
            // 
            // Column52
            // 
            this.Column52.HeaderText = "小区平均SINR";
            this.Column52.Name = "Column52";
            // 
            // Column53
            // 
            this.Column53.HeaderText = "小区过覆盖指数";
            this.Column53.Name = "Column53";
            // 
            // Column54
            // 
            this.Column54.HeaderText = "±(0,60°)范围内采样点比例";
            this.Column54.Name = "Column54";
            // 
            // Column55
            // 
            this.Column55.HeaderText = "±(0,60°)范围内小区平均RSRP";
            this.Column55.Name = "Column55";
            // 
            // Column23
            // 
            this.Column23.HeaderText = "±(0,60°)范围内覆盖率(RSRP≥-110&SINR>=-3)";
            this.Column23.Name = "Column23";
            // 
            // Column25
            // 
            this.Column25.HeaderText = "±(0,60°)范围内LTE覆盖率(RSRP≥-110)";
            this.Column25.Name = "Column25";
            // 
            // Column29
            // 
            this.Column29.HeaderText = "±(0,60°)范围内LTE覆盖率(SINR>=-3)";
            this.Column29.Name = "Column29";
            // 
            // Column34
            // 
            this.Column34.HeaderText = "±(60,150°)范围内采样点比例";
            this.Column34.Name = "Column34";
            // 
            // Column39
            // 
            this.Column39.HeaderText = "±(150,180°)范围内采样点比例";
            this.Column39.Name = "Column39";
            // 
            // Column45
            // 
            this.Column45.HeaderText = "路测前后比";
            this.Column45.Name = "Column45";
            // 
            // colrange1
            // 
            this.colrange1.HeaderText = "端口1幅度";
            this.colrange1.Name = "colrange1";
            // 
            // colrange2
            // 
            this.colrange2.HeaderText = "端口2幅度";
            this.colrange2.Name = "colrange2";
            // 
            // colrange3
            // 
            this.colrange3.HeaderText = "端口3幅度";
            this.colrange3.Name = "colrange3";
            // 
            // colrange4
            // 
            this.colrange4.HeaderText = "端口4幅度";
            this.colrange4.Name = "colrange4";
            // 
            // colrange5
            // 
            this.colrange5.HeaderText = "端口5幅度";
            this.colrange5.Name = "colrange5";
            // 
            // colrange6
            // 
            this.colrange6.HeaderText = "端口6幅度";
            this.colrange6.Name = "colrange6";
            // 
            // colrange7
            // 
            this.colrange7.HeaderText = "端口7幅度";
            this.colrange7.Name = "colrange7";
            // 
            // colrange8
            // 
            this.colrange8.HeaderText = "端口8幅度";
            this.colrange8.Name = "colrange8";
            // 
            // colphase1
            // 
            this.colphase1.HeaderText = "端口1相位";
            this.colphase1.Name = "colphase1";
            // 
            // colphase2
            // 
            this.colphase2.HeaderText = "端口2相位";
            this.colphase2.Name = "colphase2";
            // 
            // colphase3
            // 
            this.colphase3.HeaderText = "端口3相位";
            this.colphase3.Name = "colphase3";
            // 
            // colphase4
            // 
            this.colphase4.HeaderText = "端口4相位";
            this.colphase4.Name = "colphase4";
            // 
            // colphase5
            // 
            this.colphase5.HeaderText = "端口5相位";
            this.colphase5.Name = "colphase5";
            // 
            // colphase6
            // 
            this.colphase6.HeaderText = "端口6相位";
            this.colphase6.Name = "colphase6";
            // 
            // colphase7
            // 
            this.colphase7.HeaderText = "端口7相位";
            this.colphase7.Name = "colphase7";
            // 
            // colphase8
            // 
            this.colphase8.HeaderText = "端口8相位";
            this.colphase8.Name = "colphase8";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShwoChart,
            this.miShowSimulation,
            this.miShwoMRChart,
            this.miShwoMRGis,
            this.导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(197, 136);
            // 
            // miShwoChart
            // 
            this.miShwoChart.Name = "miShwoChart";
            this.miShwoChart.Size = new System.Drawing.Size(196, 22);
            this.miShwoChart.Text = "显示天线辐射波形重建";
            this.miShwoChart.Click += new System.EventHandler(this.miShwoChart_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(196, 22);
            this.miShowSimulation.Text = "显示天线GIS覆盖仿真";
            this.miShowSimulation.Click += new System.EventHandler(this.miShowSimulation_Click);
            // 
            // miShwoMRChart
            // 
            this.miShwoMRChart.Name = "miShwoMRChart";
            this.miShwoMRChart.Size = new System.Drawing.Size(196, 22);
            this.miShwoMRChart.Text = "显示MR数据图表";
            this.miShwoMRChart.Click += new System.EventHandler(this.miShwoMRChart_Click);
            // 
            // miShwoMRGis
            // 
            this.miShwoMRGis.Name = "miShwoMRGis";
            this.miShwoMRGis.Size = new System.Drawing.Size(196, 22);
            this.miShwoMRGis.Text = "显示MR覆盖仿真";
            this.miShwoMRGis.Click += new System.EventHandler(this.miShwoMRGis_Click);
            // 
            // 导出CSVToolStripMenuItem
            // 
            this.导出CSVToolStripMenuItem.Name = "导出CSVToolStripMenuItem";
            this.导出CSVToolStripMenuItem.Size = new System.Drawing.Size(196, 22);
            this.导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.导出CSVToolStripMenuItem.Click += new System.EventHandler(this.导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(196, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.groupControl1);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage1.Text = "天线角度级采样数据(路测与扫频)";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupControl2);
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1177, 620);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "图表";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGridViewAngle);
            this.groupControl2.Location = new System.Drawing.Point(10, 386);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1142, 218);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "天线角度";
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.AllowUserToAddRows = false;
            this.dataGridViewAngle.AllowUserToDeleteRows = false;
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAngle.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.cluCellName,
            this.cluTarget});
            this.dataGridViewAngle.Location = new System.Drawing.Point(12, 32);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1121, 181);
            this.dataGridViewAngle.TabIndex = 0;
            // 
            // cluCellName
            // 
            this.cluCellName.HeaderText = "小区名称";
            this.cluCellName.Name = "cluCellName";
            this.cluCellName.Width = 150;
            // 
            // cluTarget
            // 
            this.cluTarget.HeaderText = "指标项";
            this.cluTarget.Name = "cluTarget";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.panel1);
            this.groupBox2.Controls.Add(this.chartControl1);
            this.groupBox2.Location = new System.Drawing.Point(10, 75);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1142, 295);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Location = new System.Drawing.Point(1092, 21);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(29, 268);
            this.panel1.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram1.AxisX.MinorCount = 1;
            xyDiagram1.AxisX.Range.Auto = false;
            xyDiagram1.AxisX.Range.MaxValueInternal = 3.4999999999999991D;
            xyDiagram1.AxisX.Range.MinValueInternal = -0.5D;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Tickmarks.MinorLength = 1;
            xyDiagram1.AxisX.Title.Alignment = System.Drawing.StringAlignment.Near;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Color = System.Drawing.Color.DarkOrange;
            xyDiagram1.AxisY.Interlaced = true;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Thickness = 2;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.DefaultPane.BackColor = System.Drawing.Color.Transparent;
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            xyDiagram1.EnableAxisYScrolling = true;
            xyDiagram1.Margins.Bottom = 1;
            xyDiagram1.Margins.Left = 1;
            xyDiagram1.Margins.Right = 1;
            xyDiagram1.Margins.Top = 1;
            xyDiagram1.PaneDistance = 5;
            secondaryAxisY1.AxisID = 0;
            secondaryAxisY1.Color = System.Drawing.Color.Blue;
            secondaryAxisY1.GridLines.Color = System.Drawing.Color.White;
            secondaryAxisY1.Interlaced = true;
            secondaryAxisY1.Name = "Secondary AxisY 1";
            secondaryAxisY1.Range.Auto = false;
            secondaryAxisY1.Range.MaxValueSerializable = "1";
            secondaryAxisY1.Range.MinValueSerializable = "0";
            secondaryAxisY1.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY1.Range.SideMarginsEnabled = true;
            secondaryAxisY1.Thickness = 2;
            secondaryAxisY1.VisibleInPanesSerializable = "-1";
            secondaryAxisY2.AxisID = 1;
            secondaryAxisY2.Name = "Secondary AxisY 2";
            secondaryAxisY2.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY2.Range.SideMarginsEnabled = true;
            secondaryAxisY2.VisibleInPanesSerializable = "-1";
            xyDiagram1.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY1,
            secondaryAxisY2});
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Location = new System.Drawing.Point(9, 21);
            this.chartControl1.Name = "chartControl1";
            this.chartControl1.RefreshDataOnRepaint = false;
            this.chartControl1.RuntimeHitTesting = false;
            sideBySideBarSeriesLabel1.LineVisible = false;
            sideBySideBarSeriesLabel1.Visible = false;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 3";
            series1.ShowInLegend = false;
            sideBySideBarSeriesView1.AxisYName = "Secondary AxisY 2";
            sideBySideBarSeriesView1.BarWidth = 1D;
            sideBySideBarSeriesView1.Color = System.Drawing.Color.Red;
            series1.View = sideBySideBarSeriesView1;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControl1.SideBySideBarDistanceVariable = 0.01D;
            this.chartControl1.Size = new System.Drawing.Size(1112, 268);
            this.chartControl1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbbxSeries2);
            this.groupBox1.Controls.Add(this.cbbxSeries1);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Location = new System.Drawing.Point(10, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1142, 50);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例";
            // 
            // cbbxSeries2
            // 
            this.cbbxSeries2.FormattingEnabled = true;
            this.cbbxSeries2.Location = new System.Drawing.Point(365, 18);
            this.cbbxSeries2.Name = "cbbxSeries2";
            this.cbbxSeries2.Size = new System.Drawing.Size(112, 22);
            this.cbbxSeries2.TabIndex = 3;
            this.cbbxSeries2.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries2_SelectedIndexChanged);
            // 
            // cbbxSeries1
            // 
            this.cbbxSeries1.FormattingEnabled = true;
            this.cbbxSeries1.Location = new System.Drawing.Point(133, 18);
            this.cbbxSeries1.Name = "cbbxSeries1";
            this.cbbxSeries1.Size = new System.Drawing.Size(116, 22);
            this.cbbxSeries1.TabIndex = 2;
            this.cbbxSeries1.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries1_SelectedIndexChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(275, 21);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "路测折线图指标";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(43, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "扫频柱形图指标";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.dataGridViewPro);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage3.Text = "LET天线问题类型汇总";
            // 
            // dataGridViewPro
            // 
            this.dataGridViewPro.AllowUserToAddRows = false;
            this.dataGridViewPro.AllowUserToDeleteRows = false;
            this.dataGridViewPro.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewPro.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewPro.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewPro.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewPro.Location = new System.Drawing.Point(0, 15);
            this.dataGridViewPro.Name = "dataGridViewPro";
            this.dataGridViewPro.RowTemplate.Height = 23;
            this.dataGridViewPro.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewPro.Size = new System.Drawing.Size(1177, 590);
            this.dataGridViewPro.TabIndex = 3;
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.groupControl3);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage5.Text = "天线辐射波形重构";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.groupBox6);
            this.groupControl3.Controls.Add(this.groupBox4);
            this.groupControl3.Controls.Add(this.groupBox3);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1177, 620);
            this.groupControl3.TabIndex = 1;
            this.groupControl3.Text = "全向分析";
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Controls.Add(this.chartControl5);
            this.groupBox6.Location = new System.Drawing.Point(605, 26);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(565, 272);
            this.groupBox6.TabIndex = 2;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "权值理想波形图";
            // 
            // chartControl5
            // 
            this.chartControl5.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl5.Diagram = xyDiagram2;
            this.chartControl5.Legend.Visible = false;
            this.chartControl5.Location = new System.Drawing.Point(6, 21);
            this.chartControl5.Name = "chartControl5";
            series2.Label = stackedBarSeriesLabel1;
            series2.Name = "Series 1";
            series2.View = sideBySideStackedBarSeriesView1;
            this.chartControl5.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2};
            this.chartControl5.SeriesTemplate.Label = stackedBarSeriesLabel2;
            this.chartControl5.SeriesTemplate.View = sideBySideStackedBarSeriesView2;
            this.chartControl5.Size = new System.Drawing.Size(553, 245);
            this.chartControl5.TabIndex = 0;
            this.chartControl5.CustomDrawSeriesPoint += new DevExpress.XtraCharts.CustomDrawSeriesPointEventHandler(this.chartControl5_CustomDrawSeriesPoint);
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.chartControl3);
            this.groupBox4.Location = new System.Drawing.Point(599, 304);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(571, 309);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "权值配置";
            // 
            // chartControl3
            // 
            this.chartControl3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY3.AxisID = 0;
            secondaryAxisY3.Name = "Secondary AxisY 1";
            secondaryAxisY3.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY3.Range.SideMarginsEnabled = true;
            secondaryAxisY3.VisibleInPanesSerializable = "-1";
            xyDiagram3.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY3});
            this.chartControl3.Diagram = xyDiagram3;
            this.chartControl3.Location = new System.Drawing.Point(6, 21);
            this.chartControl3.Name = "chartControl3";
            sideBySideBarSeriesLabel3.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel3;
            series3.Name = "AntSeries";
            series3.ShowInLegend = false;
            sideBySideBarSeriesView2.Transparency = ((byte)(135));
            series3.View = sideBySideBarSeriesView2;
            sideBySideBarSeriesLabel4.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel4;
            series4.Name = "StandardSeries";
            series4.ShowInLegend = false;
            sideBySideBarSeriesView3.AxisYName = "Secondary AxisY 1";
            sideBySideBarSeriesView3.Transparency = ((byte)(135));
            series4.View = sideBySideBarSeriesView3;
            this.chartControl3.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3,
        series4};
            sideBySideBarSeriesLabel5.LineVisible = true;
            this.chartControl3.SeriesTemplate.Label = sideBySideBarSeriesLabel5;
            this.chartControl3.Size = new System.Drawing.Size(559, 282);
            this.chartControl3.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Controls.Add(this.chartControl2);
            this.groupBox3.Location = new System.Drawing.Point(10, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 587);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "俯视面辐射图";
            // 
            // chartControl2
            // 
            this.chartControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControl2.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControl2.Diagram = radarDiagram1;
            this.chartControl2.Location = new System.Drawing.Point(6, 21);
            this.chartControl2.Name = "chartControl2";
            radarPointSeriesLabel1.LineVisible = true;
            series5.Label = radarPointSeriesLabel1;
            series5.Name = "AntSeries";
            series5.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint1,
            seriesPoint2,
            seriesPoint3,
            seriesPoint4,
            seriesPoint5});
            series5.ShowInLegend = false;
            series5.View = radarLineSeriesView1;
            radarPointSeriesLabel2.LineVisible = true;
            series6.Label = radarPointSeriesLabel2;
            series6.Name = "StandardSeries";
            series6.ShowInLegend = false;
            series6.View = radarLineSeriesView2;
            this.chartControl2.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5,
        series6};
            radarPointSeriesLabel3.LineVisible = true;
            this.chartControl2.SeriesTemplate.Label = radarPointSeriesLabel3;
            this.chartControl2.SeriesTemplate.View = radarLineSeriesView3;
            this.chartControl2.Size = new System.Drawing.Size(571, 560);
            this.chartControl2.TabIndex = 0;
            this.chartControl2.SizeChanged += new System.EventHandler(this.chartControl2_SizeChanged);
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupCtrlMR);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage2.Text = "MR数据覆盖仿真";
            // 
            // groupCtrlMR
            // 
            this.groupCtrlMR.Controls.Add(this.groupBox5);
            this.groupCtrlMR.Controls.Add(this.groupBox15);
            this.groupCtrlMR.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupCtrlMR.Location = new System.Drawing.Point(0, 0);
            this.groupCtrlMR.Name = "groupCtrlMR";
            this.groupCtrlMR.Size = new System.Drawing.Size(1177, 620);
            this.groupCtrlMR.TabIndex = 3;
            this.groupCtrlMR.Text = "MR全向分析";
            // 
            // groupBox5
            // 
            this.groupBox5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox5.Controls.Add(this.chartCtrlMRLine);
            this.groupBox5.Location = new System.Drawing.Point(599, 26);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(583, 587);
            this.groupBox5.TabIndex = 1;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "MR覆盖模拟图";
            // 
            // chartCtrlMRLine
            // 
            this.chartCtrlMRLine.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartCtrlMRLine.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram2.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartCtrlMRLine.Diagram = radarDiagram2;
            this.chartCtrlMRLine.Location = new System.Drawing.Point(5, 21);
            this.chartCtrlMRLine.Name = "chartCtrlMRLine";
            radarPointSeriesLabel4.LineVisible = true;
            series7.Label = radarPointSeriesLabel4;
            series7.Name = "AntSeries";
            series7.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint6,
            seriesPoint7,
            seriesPoint8,
            seriesPoint9,
            seriesPoint10});
            series7.ShowInLegend = false;
            radarPointSeriesView1.PointMarkerOptions.Size = 10;
            series7.View = radarPointSeriesView1;
            radarPointSeriesLabel5.LineVisible = true;
            series8.Label = radarPointSeriesLabel5;
            series8.Name = "StandardSeries";
            series8.ShowInLegend = false;
            radarPointSeriesView2.PointMarkerOptions.Size = 10;
            series8.View = radarPointSeriesView2;
            this.chartCtrlMRLine.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series7,
        series8};
            radarPointSeriesLabel6.LineVisible = true;
            this.chartCtrlMRLine.SeriesTemplate.Label = radarPointSeriesLabel6;
            radarPointSeriesView3.PointMarkerOptions.Size = 10;
            this.chartCtrlMRLine.SeriesTemplate.View = radarPointSeriesView3;
            this.chartCtrlMRLine.Size = new System.Drawing.Size(565, 560);
            this.chartCtrlMRLine.TabIndex = 0;
            // 
            // groupBox15
            // 
            this.groupBox15.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox15.Controls.Add(this.chartCtrlMRSample);
            this.groupBox15.Location = new System.Drawing.Point(10, 26);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(583, 587);
            this.groupBox15.TabIndex = 0;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "MR采样点分布图";
            // 
            // chartCtrlMRSample
            // 
            this.chartCtrlMRSample.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartCtrlMRSample.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram3.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartCtrlMRSample.Diagram = radarDiagram3;
            this.chartCtrlMRSample.Location = new System.Drawing.Point(6, 21);
            this.chartCtrlMRSample.Name = "chartCtrlMRSample";
            radarPointSeriesLabel7.LineVisible = true;
            series9.Label = radarPointSeriesLabel7;
            series9.Name = "AntSeries";
            series9.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint11,
            seriesPoint12,
            seriesPoint13,
            seriesPoint14,
            seriesPoint15});
            series9.ShowInLegend = false;
            radarPointSeriesView4.PointMarkerOptions.Size = 10;
            series9.View = radarPointSeriesView4;
            radarPointSeriesLabel8.LineVisible = true;
            series10.Label = radarPointSeriesLabel8;
            series10.Name = "StandardSeries";
            series10.ShowInLegend = false;
            radarPointSeriesView5.PointMarkerOptions.Size = 10;
            series10.View = radarPointSeriesView5;
            this.chartCtrlMRSample.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series9,
        series10};
            radarPointSeriesLabel9.LineVisible = true;
            this.chartCtrlMRSample.SeriesTemplate.Label = radarPointSeriesLabel9;
            radarPointSeriesView6.PointMarkerOptions.Size = 10;
            this.chartCtrlMRSample.SeriesTemplate.View = radarPointSeriesView6;
            this.chartCtrlMRSample.Size = new System.Drawing.Size(571, 560);
            this.chartCtrlMRSample.TabIndex = 0;
            // 
            // LteAntParaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 650);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "LteAntParaForm";
            this.Text = "天线综合分析总表";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage4.ResumeLayout(false);
            this.xtraTabPage4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPro)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).EndInit();
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupCtrlMR)).EndInit();
            this.groupCtrlMR.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartCtrlMRLine)).EndInit();
            this.groupBox15.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartCtrlMRSample)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShwoChart;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraCharts.ChartControl chartControl5;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraCharts.ChartControl chartControl3;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartControl2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.ToolStripMenuItem 导出CSVToolStripMenuItem;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cbbxSeries2;
        private System.Windows.Forms.ComboBox cbbxSeries1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluTarget;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.GroupControl groupCtrlMR;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraCharts.ChartControl chartCtrlMRLine;
        private System.Windows.Forms.GroupBox groupBox15;
        private DevExpress.XtraCharts.ChartControl chartCtrlMRSample;
        private System.Windows.Forms.ToolStripMenuItem miShwoMRChart;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private System.Windows.Forms.DataGridView dataGridViewPro;
        private System.Windows.Forms.ToolStripMenuItem miShwoMRGis;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIndex;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn colvender;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column41;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcovertype;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colbeamwidth;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column4;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column5;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column7;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column62;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column63;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column64;
        private System.Windows.Forms.DataGridViewTextBoxColumn colgmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3db;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6db;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column8;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column13;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column18;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column56;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column57;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column58;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column59;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column60;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column61;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column27;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column30;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column31;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column32;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column33;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column35;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column36;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column37;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column38;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column40;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column42;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column43;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column46;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column44;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column47;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column48;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column49;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column50;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column51;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column52;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column53;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column54;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column55;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column23;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column25;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column29;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column34;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column39;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column45;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange8;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase8;
    }
}