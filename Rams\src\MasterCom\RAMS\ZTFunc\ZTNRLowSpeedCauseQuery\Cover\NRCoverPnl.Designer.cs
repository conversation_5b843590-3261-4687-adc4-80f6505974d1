﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRCoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.nrWeakCoverPnl1 = new MasterCom.RAMS.ZTFunc.NRWeakCoverPnl();
            this.nrWrongCoverPnl1 = new MasterCom.RAMS.ZTFunc.NRWrongCoverPnl();
            this.nrRepeatCoverPnl1 = new MasterCom.RAMS.ZTFunc.NRRepeatCoverPnl();
            this.nrOverCoverPnl1 = new MasterCom.RAMS.ZTFunc.NROverCoverPnl();
            this.SuspendLayout();
            // 
            // nrWeakCoverPnl1
            // 
            this.nrWeakCoverPnl1.Location = new System.Drawing.Point(3, 3);
            this.nrWeakCoverPnl1.Name = "nrWeakCoverPnl1";
            this.nrWeakCoverPnl1.Size = new System.Drawing.Size(348, 60);
            this.nrWeakCoverPnl1.TabIndex = 0;
            // 
            // nrWrongCoverPnl1
            // 
            this.nrWrongCoverPnl1.Location = new System.Drawing.Point(3, 69);
            this.nrWrongCoverPnl1.Name = "nrWrongCoverPnl1";
            this.nrWrongCoverPnl1.Size = new System.Drawing.Size(348, 60);
            this.nrWrongCoverPnl1.TabIndex = 1;
            // 
            // nrRepeatCoverPnl1
            // 
            this.nrRepeatCoverPnl1.Location = new System.Drawing.Point(3, 135);
            this.nrRepeatCoverPnl1.Name = "nrRepeatCoverPnl1";
            this.nrRepeatCoverPnl1.Size = new System.Drawing.Size(348, 93);
            this.nrRepeatCoverPnl1.TabIndex = 2;
            // 
            // nrOverCoverPnl1
            // 
            this.nrOverCoverPnl1.Location = new System.Drawing.Point(357, 3);
            this.nrOverCoverPnl1.Name = "nrOverCoverPnl1";
            this.nrOverCoverPnl1.Size = new System.Drawing.Size(348, 80);
            this.nrOverCoverPnl1.TabIndex = 3;
            // 
            // NRCoverPnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoScroll = true;
            this.Controls.Add(this.nrOverCoverPnl1);
            this.Controls.Add(this.nrRepeatCoverPnl1);
            this.Controls.Add(this.nrWrongCoverPnl1);
            this.Controls.Add(this.nrWeakCoverPnl1);
            this.Name = "NRCoverPnl";
            this.Size = new System.Drawing.Size(645, 270);
            this.ResumeLayout(false);

        }

        #endregion

        private NRWeakCoverPnl nrWeakCoverPnl1;
        private NRWrongCoverPnl nrWrongCoverPnl1;
        private NRRepeatCoverPnl nrRepeatCoverPnl1;
        private NROverCoverPnl nrOverCoverPnl1;
    }
}
