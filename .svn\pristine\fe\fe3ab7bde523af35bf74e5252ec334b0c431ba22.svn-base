﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTSINR.LastWeakRoad.GridCompare;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSinrRoadGridCompare : ZTWeakSINRRoadQuery
    {
        public WeakSinrRoadGridCompare()
        { }
        public WeakSinrRoadGridCompare(bool isVoLTE)
            : this()
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2 , 22000 , 22038 , this.Name);
        }
        protected override bool getCondition()
        {
            if(MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SettingDlg dlg = new SettingDlg();
            dlg.SetCondition(WeakCondition as WeakSinrRoadCompareCondition);
            if(dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCondition = dlg.GetCondition();
                WeakSinrRoadCompareCondition compCond = WeakCondition as WeakSinrRoadCompareCondition;
                this.condition.Periods=new List<TimePeriod>();
                this.condition.Periods.Add(compCond.Period1);
                this.condition.Periods.Add(compCond.Period2);
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            allSegGrids=new List<WeakSinrRoadGrid>();
            p1Grids = new List<WeakSinrRoadGrid>();
            p2Grids = new List<WeakSinrRoadGrid>();
            repeatGrids = new List<WeakSinrRoadGrid>();
        }

        protected override void doStatWithQuery()
        {
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            int fileID = MainModel.DTDataManager.FileDataManagers[0].GetFileInfo().ID;
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            WeakSinrRoadGrid weakSegGrid = null;
            TestPoint prePoint = null; //前一点
            for(int i = 0 ; i < testPointList.Count ; i++)
            {
                TestPoint testPoint = testPointList[i];
                if(isValidTestPoint(testPoint))
                {
                    weakSegGrid = addWeakSinrRoadGrid(fi, weakSegGrid, prePoint, testPoint);
                    prePoint = testPoint;
                }
                else //区域外的采样点
                {
                    saveWeakRoadSeg(ref weakSegGrid);
                }
            }
            saveWeakRoadSeg(ref weakSegGrid);
        }

        private WeakSinrRoadGrid addWeakSinrRoadGrid(FileInfo fi, WeakSinrRoadGrid weakSegGrid, TestPoint prePoint, TestPoint testPoint)
        {
            float? sinr = getSinr(testPoint);
            float? rsrp = getRsrp(testPoint);
            if (!WeakCondition.IsValidate(sinr)) //先作指标的判断，后作距离的判断
            { //不符合设置的弱覆盖条件
                saveWeakRoadSeg(ref weakSegGrid);
            }
            else //指标符合弱覆盖，还需要进行距离条件判断
            {
                if (weakSegGrid == null) //弱覆盖开始
                {
                    weakSegGrid = new WeakSinrRoadGrid(fi);
                    weakSegGrid.Add((float)sinr, rsrp, 0, testPoint);
                }
                else
                { //上一点为弱覆盖点
                    double dis = MathFuncs.GetDistance(prePoint.Longitude, prePoint.Latitude,
                        testPoint.Longitude, testPoint.Latitude);
                    if (WeakCondition.Match2TestpointsMaxDistance(dis))
                    { //符合两采样点之间的距离门限
                        weakSegGrid.Add((float)sinr, rsrp, dis, testPoint);
                    }
                    else
                    { //两采样点距离不符合，该点开始新的弱覆盖
                        saveWeakRoadSeg(ref weakSegGrid);
                        weakSegGrid = new WeakSinrRoadGrid(fi);
                        weakSegGrid.Add((float)sinr, rsrp, 0, testPoint);
                    }
                }
            }

            return weakSegGrid;
        }

        protected List<WeakSinrRoadGrid> allSegGrids = null;

        protected virtual void saveWeakRoadSeg(ref WeakSinrRoadGrid segGrid)
        {
            if(segGrid == null || !WeakCondition.MatchMinWeakCoverDistance(segGrid.Distance))
            {
                segGrid = null;
                return;
            }
            if(!allSegGrids.Contains(segGrid))
            {
                allSegGrids.Add(segGrid);
            }
            segGrid = null;
        }

        private List<WeakSinrRoadGrid> p1Grids = null;
        private List<WeakSinrRoadGrid> p2Grids = null;
        private List<WeakSinrRoadGrid> repeatGrids = null;
        protected override void getResultsAfterQuery()
        {
            WeakSinrRoadCompareCondition compareCondition = WeakCondition as WeakSinrRoadCompareCondition;
            Debug.Assert(compareCondition != null, "compareCondition != null");
            foreach(WeakSinrRoadGrid grid in allSegGrids)
            {
                if(grid.FileInfo.EndTime >= compareCondition.Period1.IBeginTime
                   && grid.FileInfo.BeginTime <= compareCondition.Period1.IEndTime)
                { //时间段1
                    grid.MakeGrid(compareCondition.GridSpanDegree);
                    p1Grids.Add(grid);
                    grid.SN = p1Grids.Count;
                }
                else if(grid.FileInfo.EndTime >= compareCondition.Period2.IBeginTime
                        && grid.FileInfo.BeginTime <= compareCondition.Period2.IEndTime)
                { //时间段2
                    grid.MakeGrid(compareCondition.GridSpanDegree);
                    p2Grids.Add(grid);
                    grid.SN = p2Grids.Count;
                }
            }

            foreach(WeakSinrRoadGrid grid2 in p2Grids)
            {
                foreach(WeakSinrRoadGrid grid1 in p1Grids)
                {
                    if(grid1.Intersect(grid2))
                    {
                        grid2.AddIntersectSeg(grid1);
                    }
                }
                if(grid2.IntersectSegNum > 0)
                {
                    repeatGrids.Add(grid2);
                }
            }

        }


        protected override void fireShowForm()
        {
            CompareResultForm frm = MainModel.GetObjectFromBlackboard(typeof(CompareResultForm)) as CompareResultForm;
            if (frm==null||frm.IsDisposed)
            {
                frm=new CompareResultForm();
            }
            frm.Owner = MainModel.MainForm;
            frm.FillData(p1Grids, p2Grids, repeatGrids);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class WeakSinrRoadGridCompare_FDD : ZTWeakSINRRoadQuery_LteFdd
    {
        public WeakSinrRoadGridCompare_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "SINR质差路段对比_LTEFDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26024, this.Name);//////
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SettingDlg dlg = new SettingDlg();
            dlg.SetCondition(WeakCondition as WeakSinrRoadCompareCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCondition = dlg.GetCondition();
                WeakSinrRoadCompareCondition compCond = WeakCondition as WeakSinrRoadCompareCondition;
                this.condition.Periods = new List<TimePeriod>();
                this.condition.Periods.Add(compCond.Period1);
                this.condition.Periods.Add(compCond.Period2);
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            allSegGrids = new List<WeakSinrRoadGrid>();
            p1Grids = new List<WeakSinrRoadGrid>();
            p2Grids = new List<WeakSinrRoadGrid>();
            repeatGrids = new List<WeakSinrRoadGrid>();
        }

        protected override void doStatWithQuery()
        {
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            int fileID = MainModel.DTDataManager.FileDataManagers[0].GetFileInfo().ID;
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            WeakSinrRoadGrid weakSegGrid = null;
            TestPoint prePoint = null; //前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    weakSegGrid = addWeakSinrRoadGrid(fi, weakSegGrid, prePoint, testPoint);
                    prePoint = testPoint;
                }
                else //区域外的采样点
                {
                    saveWeakRoadSeg(ref weakSegGrid);
                }
            }
            saveWeakRoadSeg(ref weakSegGrid);
        }

        private WeakSinrRoadGrid addWeakSinrRoadGrid(FileInfo fi, WeakSinrRoadGrid weakSegGrid, TestPoint prePoint, TestPoint testPoint)
        {
            float? sinr = getSinr(testPoint);
            float? rsrp = getRsrp(testPoint);
            if (!WeakCondition.IsValidate(sinr)) //先作指标的判断，后作距离的判断
            { //不符合设置的弱覆盖条件
                saveWeakRoadSeg(ref weakSegGrid);
            }
            else //指标符合弱覆盖，还需要进行距离条件判断
            {
                if (weakSegGrid == null) //弱覆盖开始
                {
                    weakSegGrid = new WeakSinrRoadGrid(fi);
                    weakSegGrid.Add((float)sinr, rsrp, 0, testPoint);
                }
                else
                { //上一点为弱覆盖点
                    double dis = MathFuncs.GetDistance(prePoint.Longitude, prePoint.Latitude,
                        testPoint.Longitude, testPoint.Latitude);
                    if (WeakCondition.Match2TestpointsMaxDistance(dis))
                    { //符合两采样点之间的距离门限
                        weakSegGrid.Add((float)sinr, rsrp, dis, testPoint);
                    }
                    else
                    { //两采样点距离不符合，该点开始新的弱覆盖
                        saveWeakRoadSeg(ref weakSegGrid);
                        weakSegGrid = new WeakSinrRoadGrid(fi);
                        weakSegGrid.Add((float)sinr, rsrp, 0, testPoint);
                    }
                }
            }

            return weakSegGrid;
        }

        protected List<WeakSinrRoadGrid> allSegGrids = null;

        protected virtual void saveWeakRoadSeg(ref WeakSinrRoadGrid segGrid)
        {
            if (segGrid == null || !WeakCondition.MatchMinWeakCoverDistance(segGrid.Distance))
            {
                segGrid = null;
                return;
            }
            if (!allSegGrids.Contains(segGrid))
            {
                allSegGrids.Add(segGrid);
            }
            segGrid = null;
        }

        private List<WeakSinrRoadGrid> p1Grids = null;
        private List<WeakSinrRoadGrid> p2Grids = null;
        private List<WeakSinrRoadGrid> repeatGrids = null;
        protected override void getResultsAfterQuery()
        {
            WeakSinrRoadCompareCondition compareCondition = WeakCondition as WeakSinrRoadCompareCondition;
            Debug.Assert(compareCondition != null, "compareCondition != null");
            foreach (WeakSinrRoadGrid grid in allSegGrids)
            {
                if (grid.FileInfo.EndTime >= compareCondition.Period1.IBeginTime
                   && grid.FileInfo.BeginTime <= compareCondition.Period1.IEndTime)
                { //时间段1
                    grid.MakeGrid(compareCondition.GridSpanDegree);
                    p1Grids.Add(grid);
                    grid.SN = p1Grids.Count;
                }
                else if (grid.FileInfo.EndTime >= compareCondition.Period2.IBeginTime
                        && grid.FileInfo.BeginTime <= compareCondition.Period2.IEndTime)
                { //时间段2
                    grid.MakeGrid(compareCondition.GridSpanDegree);
                    p2Grids.Add(grid);
                    grid.SN = p2Grids.Count;
                }
            }

            foreach (WeakSinrRoadGrid grid2 in p2Grids)
            {
                foreach (WeakSinrRoadGrid grid1 in p1Grids)
                {
                    if (grid1.Intersect(grid2))
                    {
                        grid2.AddIntersectSeg(grid1);
                    }
                }
                if (grid2.IntersectSegNum > 0)
                {
                    repeatGrids.Add(grid2);
                }
            }

        }

        protected override void fireShowForm()
        {
            CompareResultForm frm = MainModel.GetObjectFromBlackboard(typeof(CompareResultForm)) as CompareResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new CompareResultForm();
            }
            frm.Owner = MainModel.MainForm;
            frm.FillData(p1Grids, p2Grids, repeatGrids);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class WeakSinrRoadGridCompare_FDD_VOLTE : WeakSinrRoadGridCompare_FDD
    {
        public WeakSinrRoadGridCompare_FDD_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD SINR质差路段对比"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30013, this.Name);//////
        }
        
    }
}
