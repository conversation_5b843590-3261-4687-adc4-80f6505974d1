﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using Excel = Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Utils;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Func
{
    public partial class VolteMosAnalysisShow : MinCloseForm
    {
        float mosvalue = 2.8F;
        string compareType = ">=";
        double moskeymax = 0;
        int evenkeymax = 0;
        Dictionary<double, List<float>> dicMos = new Dictionary<double, List<float>>();
        Dictionary<int, List<float>> dicEvenCountMos = new Dictionary<int, List<float>>();
        Dictionary<string, List<float>> dicSinrCountMos = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicModulationModeMos = new Dictionary<string, List<float>>();
        Dictionary<int, List<float>> dicEvenCountPer = new Dictionary<int, List<float>>();
        Dictionary<string, List<float>> dicSinrCountPer = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicModulationModePer = new Dictionary<string, List<float>>();
        Dictionary<int, DataTable> dicDatas = new Dictionary<int, DataTable>();
        Dictionary<string, List<float>> dicRsrpCountMos = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicRsrpCountPer = new Dictionary<string, List<float>>();

        Dictionary<string, List<float>> dicDlQam16Mos = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicDlQam64Mos = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicDlQpskMos = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicDlQam16Per = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicDlQam64Per = new Dictionary<string, List<float>>();
        Dictionary<string, List<float>> dicDlQpskPer = new Dictionary<string, List<float>>();
        MosAnaCondition mosCondition = new MosAnaCondition();
        public VolteMosAnalysisShow()
        {
            InitializeComponent();
            gridView1.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView2.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView3.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView4.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView5.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView6.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView7.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView8.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView9.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView10.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView11.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView12.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView13.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
        }

        //该方法参数过多
        public void FillData(double moskeymax, int evenkeymax, VolteMosAnalysisInfo info, MosAnaCondition mosCondition)
        {
            this.moskeymax = moskeymax;
            this.evenkeymax = evenkeymax;
            this.dicMos = info.DicMos;
            this.dicEvenCountMos = info.DicEvenCountMos;
            this.dicSinrCountMos = info.DicSinrCountMos;
            this.dicModulationModeMos = info.DicModulationMos;
            this.dicEvenCountPer = info.DicEvenCountPer;
            this.dicSinrCountPer = info.DicSinrCountPer;
            this.dicModulationModePer = info.DicModulationPer;
            this.dicRsrpCountMos = info.DicRsrpCountMos;
            this.dicRsrpCountPer = info.DicRsrpCountPer;
            this.dicDlQam16Mos = info.DicDlQam16Mos;
            this.dicDlQam64Mos = info.DicDlQam64Mos;
            this.dicDlQpskMos = info.DicDlQpskMos;
            this.dicDlQam16Per = info.DicDlQam16Per;
            this.dicDlQam64Per = info.DicDlQam64Per;
            this.dicDlQpskPer = info.DicDlQpskPer;
            this.mosCondition = mosCondition;

            fresh();
        }

        private void VolteMosAnalysisShow_Load(object sender, EventArgs e)
        {
            fresh();
        }

        private void fresh()
        {
            labelControl1.Text = "注：MOS均值与MOS" + compareType + mosvalue + "比例是在固定调制方式比例（QAM16>50%）和固定Sinr（Sinr>-3）的情况下得出的.比例是在所有MOS周期内得出的.";
            labelControl2.Text = "注：MOS均值、MOS" + compareType + mosvalue + "比例是在固定调制方式比例（QAM16>50%）和固定切换次数（无切换）的情况下得出的.比例是在所有MOS周期内得出的";
            labelControl3.Text = "注：MOS均值、MOS" + compareType + mosvalue + "比例是在固定切换次数（切换次数为0）和固定Sinr（Sinr>-3）的情况下得出的.比例是在所有MOS周期内得出的";
            labelControl5.Text = "注：MOS均值、MOS" + compareType + mosvalue + "比例是在固定调制方式比例（QAM16>50%),固定切换次数（切换次数为0）和固定Sinr（Sinr>-3）的情况下得出的.比例是在所有MOS周期内得出的";
            chartControl3.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            chartControl6.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            chartControl9.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            chartControl12.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            cbxModel.SelectedIndex = 0;
            analytics();
            Show(dicDatas);
        }

        private void analytics()
        {
            try
            {
                dicDatas.Clear();
                // 计算总个数
                int moscount = 0;
                foreach (List<float> fmos in dicMos.Values)
                {
                    moscount += fmos.Count;
                }
                int evencount = 0;
                foreach (List<float> feven in dicEvenCountPer.Values)
                {
                    evencount += feven.Count;
                }
                int sinrcount = 0;
                foreach (List<float> fsinr in dicSinrCountPer.Values)
                {
                    sinrcount += fsinr.Count;
                }
                int speechcount = 0;
                foreach (List<float> fspeech in dicModulationModePer.Values)
                {
                    speechcount += fspeech.Count;
                }
                int rsrpcount = 0;
                foreach (List<float> frsrp in dicRsrpCountPer.Values)
                {
                    rsrpcount += frsrp.Count;
                }
                /////////////////MOS////////////////////
                DataTable dt1 = new DataTable();
                dt1.Columns.Add("MOS值", typeof(string));
                dt1.Columns.Add("比例", typeof(string));

                //////////MOS总体分布/////////
                for (int i = 10; i <= moskeymax * 10; i++)
                {
                    double d = (double)i / 10;
                    string percent = "0%";
                    if (dicMos.ContainsKey(d))
                    {
                        percent = ((double)dicMos[d].Count / (double)moscount).ToString("p");
                    }
                    dt1.Rows.Add(new object[] { "[" + d + "~" + (d + 0.1) + ")", percent });
                }
                dicDatas.Add(1, dt1);
                //////////切换次数与MOS/MOS均值/////////////
                DataTable dt2 = new DataTable();
                dt2.Columns.Add("切换次数", typeof(int));
                dt2.Columns.Add("MOS均值", typeof(float));
                dealEventMos(dt2);
                dicDatas.Add(2, dt2);

                //////////切换次数与MOS/MOS>=2.8比例/////////
                string fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt3 = new DataTable();
                dt3.Columns.Add("切换次数", typeof(int));
                dt3.Columns.Add(fieldName, typeof(float));
                dealEventMos3(dt3);
                dicDatas.Add(3, dt3);

                //////////切换次比例/////////
                DataTable dt4 = new DataTable();
                fieldName = "比例";
                dt4.Columns.Add("切换次数", typeof(int));
                dt4.Columns.Add(fieldName, typeof(float));
                dealEventPer(evencount, dt4);
                dicDatas.Add(4, dt4);

                //////////Sinr与MOS值关系/MOS均值/////////
                DataTable dt5 = new DataTable();
                dt5.Columns.Add("Sinr", typeof(string));
                dt5.Columns.Add("MOS均值", typeof(float));
                dealMos(dt5, dicSinrCountMos, mosCondition.SinrRangeSet);
                dicDatas.Add(5, dt5);

                //////////Sinr与MOS值关系/MOS>=2.8比例/////////
                fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt6 = new DataTable();
                dt6.Columns.Add("Sinr", typeof(string));
                dt6.Columns.Add(fieldName, typeof(float));
                dealMos3(dt6, dicSinrCountMos, mosCondition.SinrRangeSet);
                dicDatas.Add(6, dt6);

                //////////Sinr与MOS值关系/比例/////////
                fieldName = "比例";
                DataTable dt7 = new DataTable();
                dt7.Columns.Add("Sinr", typeof(string));
                dt7.Columns.Add(fieldName, typeof(float));
                dealPer(sinrcount, dt7, dicSinrCountPer, mosCondition.SinrRangeSet);
                dicDatas.Add(7, dt7);

                //////////调制方式与MOS值关系/MOS均值/////////
                DataTable dt8 = new DataTable();
                dt8.Columns.Add("调制方式上行QAM16占比", typeof(string));
                dt8.Columns.Add("MOS均值", typeof(float));
                dealMos(dt8, dicModulationModeMos, mosCondition.QamPerRangeSet);
                dicDatas.Add(8, dt8);

                //////////调制方式与MOS值关系/MOS>=2.8比例/////////
                fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt9 = new DataTable();
                dt9.Columns.Add("调制方式上行QAM16占比", typeof(string));
                dt9.Columns.Add(fieldName, typeof(float));
                dealMos3(dt9, dicModulationModeMos, mosCondition.QamPerRangeSet);
                dicDatas.Add(9, dt9);

                //////////调制方式与MOS值关系/比例/////////
                fieldName = "比例";
                DataTable dt10 = new DataTable();
                dt10.Columns.Add("调制方式上行QAM16占比", typeof(string));
                dt10.Columns.Add(fieldName, typeof(float));
                dealPer(speechcount, dt10, dicModulationModePer, mosCondition.QamPerRangeSet);
                dicDatas.Add(10, dt10);

                //////////Rsrp与MOS值关系/MOS均值/////////
                DataTable dt11 = new DataTable();
                dt11.Columns.Add("Rsrp", typeof(string));
                dt11.Columns.Add("MOS均值", typeof(float));
                dealMos(dt11, dicRsrpCountMos, mosCondition.RsrpRangeSet);
                dicDatas.Add(11, dt11);

                //////////Rsrp与MOS值关系/MOS>=2.8比例/////////
                fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt12 = new DataTable();
                dt12.Columns.Add("Rsrp", typeof(string));
                dt12.Columns.Add(fieldName, typeof(float));
                dealMos3(dt12, dicRsrpCountMos, mosCondition.RsrpRangeSet);
                dicDatas.Add(12, dt12);

                //////////Rsrp与MOS值关系/比例/////////
                fieldName = "比例";
                DataTable dt13 = new DataTable();
                dt13.Columns.Add("Rsrp", typeof(string));
                dt13.Columns.Add(fieldName, typeof(float));
                dealPer(rsrpcount, dt13, dicRsrpCountPer, mosCondition.RsrpRangeSet);
                dicDatas.Add(13, dt13);

                #region DlQam16

                //////////调制方式与MOS值关系/MOS均值/////////
                DataTable dt14 = new DataTable();
                dt14.Columns.Add("调制方式下行QAM16占比", typeof(string));
                dt14.Columns.Add("MOS均值", typeof(float));
                dealMos(dt14, dicDlQam16Mos, mosCondition.DlQam16PerRangeSet);
                dicDatas.Add(14, dt14);

                //////////调制方式与MOS值关系/MOS>=2.8比例/////////
                fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt15 = new DataTable();
                dt15.Columns.Add("调制方式下行QAM16占比", typeof(string));
                dt15.Columns.Add(fieldName, typeof(float));
                dealMos3(dt15, dicDlQam16Mos, mosCondition.DlQam16PerRangeSet);
                dicDatas.Add(15, dt15);


                //////////调制方式与MOS值关系/比例/////////
                fieldName = "比例";
                DataTable dt16 = new DataTable();
                dt16.Columns.Add("调制方式下行QAM16占比", typeof(string));
                dt16.Columns.Add(fieldName, typeof(float));
                dealPer(speechcount, dt16, dicDlQam16Per, mosCondition.DlQam16PerRangeSet);
                dicDatas.Add(16, dt16);

                #endregion

                #region DlQam64

                //////////调制方式与MOS值关系/MOS均值/////////
                DataTable dt17 = new DataTable();
                dt17.Columns.Add("调制方式下行QAM64占比", typeof(string));
                dt17.Columns.Add("MOS均值", typeof(float));
                dealMos(dt17, dicDlQam64Mos, mosCondition.DlQam64PerRangeSet);
                dicDatas.Add(17, dt17);

                //////////调制方式与MOS值关系/MOS>=2.8比例/////////
                fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt18 = new DataTable();
                dt18.Columns.Add("调制方式下行QAM64占比", typeof(string));
                dt18.Columns.Add(fieldName, typeof(float));
                dealMos3(dt18, dicDlQam64Mos, mosCondition.DlQam64PerRangeSet);
                dicDatas.Add(18, dt18);


                //////////调制方式与MOS值关系/比例/////////
                fieldName = "比例";
                DataTable dt19 = new DataTable();
                dt19.Columns.Add("调制方式下行QAM64占比", typeof(string));
                dt19.Columns.Add(fieldName, typeof(float));
                dealPer(speechcount, dt19, dicDlQam64Per, mosCondition.DlQam64PerRangeSet);
                dicDatas.Add(19, dt19);

                #endregion

                #region DlQpsk

                //////////调制方式与MOS值关系/MOS均值/////////
                DataTable dt20 = new DataTable();
                dt20.Columns.Add("调制方式下行QPSK占比", typeof(string));
                dt20.Columns.Add("MOS均值", typeof(float));
                dealMos(dt20, dicDlQpskMos, mosCondition.DlQpskPerRangeSet);
                dicDatas.Add(20, dt20);

                //////////调制方式与MOS值关系/MOS>=2.8比例/////////
                fieldName = "MOS" + compareType + mosvalue + "比例";
                DataTable dt21 = new DataTable();
                dt21.Columns.Add("调制方式下行QPSK占比", typeof(string));
                dt21.Columns.Add(fieldName, typeof(float));
                dealMos3(dt21, dicDlQpskMos, mosCondition.DlQpskPerRangeSet);
                dicDatas.Add(21, dt21);

                //////////调制方式与MOS值关系/比例/////////
                fieldName = "比例";
                DataTable dt22 = new DataTable();
                dt22.Columns.Add("调制方式下行QPSK占比", typeof(string));
                dt22.Columns.Add(fieldName, typeof(float));
                dealPer(speechcount, dt22, dicDlQpskPer, mosCondition.DlQpskPerRangeSet);
                dicDatas.Add(22, dt22);

                #endregion

            }
            catch
            {
                MessageBox.Show("MOS统计分析失败.\n");
            }
        }

        private void dealEventMos(DataTable dt2)
        {
            for (int even = 0; even <= evenkeymax; even++)
            {
                float summos = 0;
                float avgmos = 0;
                if (dicEvenCountMos.ContainsKey(even))
                {
                    foreach (float mos in dicEvenCountMos[even])
                    {
                        summos += mos;
                    }
                    avgmos = (float)Math.Round(summos / dicEvenCountMos[even].Count, 2);
                }
                dt2.Rows.Add(new object[] { even, avgmos });
            }
        }

        private void dealEventMos3(DataTable dt3)
        {
            for (int even = 0; even <= evenkeymax; even++)
            {
                int morethan3 = 0;
                float permos = 0;
                if (dicEvenCountMos.ContainsKey(even))
                {
                    foreach (float mos in dicEvenCountMos[even])
                    {
                        if (compareWithMOSThreshold(mos))
                        {
                            morethan3++;
                        }
                    }
                    if (morethan3 != 0)
                    {
                        permos = (float)Math.Round(((float)morethan3 / (float)dicEvenCountMos[even].Count), 4);
                    }
                }
                dt3.Rows.Add(new object[] { even, permos });
            }
        }

        private void dealEventPer(int evencount, DataTable dt4)
        {
            for (int even = 0; even <= evenkeymax; even++)
            {
                float permos = 0;
                if (dicEvenCountPer.ContainsKey(even))
                {
                    permos = (float)Math.Round((float)dicEvenCountPer[even].Count / (float)evencount, 4);
                }
                dt4.Rows.Add(new object[] { even, permos });
            }
        }

        private void dealMos(DataTable dt, Dictionary<string, List<float>> dicMos, RangeSet rangeSet)
        {
            foreach (Range range in rangeSet.Values)
            {
                string speechcode = range.ToString();
                float totalmos = 0;
                float avgmos = 0;
                if (dicMos.ContainsKey(speechcode))
                {
                    foreach (float mos in dicMos[speechcode])
                    {
                        totalmos += mos;
                    }
                    avgmos = (float)Math.Round(totalmos / dicMos[speechcode].Count, 2);
                }
                dt.Rows.Add(new object[] { speechcode, avgmos });
            }
        }

        private void dealMos3(DataTable dt, Dictionary<string, List<float>> dicMos, RangeSet rangeSet)
        {
            foreach (Range range in rangeSet.Values)
            {
                string speechcode = range.ToString();
                int morethan3 = 0;
                float permos = 0;
                if (dicMos.ContainsKey(speechcode))
                {
                    foreach (float mos in dicMos[speechcode])
                    {
                        if (compareWithMOSThreshold(mos))
                        {
                            morethan3++;
                        }
                    }
                    if (morethan3 != 0)
                    {
                        permos = (float)Math.Round((float)morethan3 / (float)dicMos[speechcode].Count, 4);
                    }
                }
                dt.Rows.Add(new object[] { speechcode, permos });
            }
        }

        private void dealPer(int speechcount, DataTable dt, Dictionary<string, List<float>> dicMos, RangeSet rangeSet)
        {
            foreach (Range range in rangeSet.Values)
            {
                string speechcode = range.ToString();
                float permos = 0;
                if (dicMos.ContainsKey(speechcode))
                {
                    permos = (float)Math.Round((float)dicMos[speechcode].Count / (float)speechcount, 4);
                }
                dt.Rows.Add(new object[] { speechcode, permos });
            }
        }

        private void Show(Dictionary<int, DataTable> dicdatas)
        {
            this.dicDatas = dicdatas;
            //////////////////////////////1///////////////////////////////////////
            gridControl1.DataSource = dicdatas[1];
            Series series1 = chartControl1.Series[0];
            series1.Points.Clear();
            for (int i = 0; i < dicdatas[1].Rows.Count; i++)
            {
                series1.Points.Add(new SeriesPoint(dicdatas[1].Rows[i][0], Math.Round(Decimal.Parse((dicdatas[1].Rows[i][1] as string).TrimEnd('%')) / 100, 2)));
            }
            /////////////////////////////2//////////////////////////////////////
            gridView2.Columns.Clear();
            gridControl2.DataSource = dicdatas[2];
            setColCondStyle(gridView2.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView2.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series2 = chartControl2.Series[0];
            series2.Points.Clear();
            for (int i = 0; i < dicdatas[2].Rows.Count; i++)
            {
                series2.Points.Add(new SeriesPoint(dicdatas[2].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[2].Rows[i][1]), 2)));
            }
            /////////////////////////////3//////////////////////////////////////
            gridView3.Columns.Clear();
            gridControl3.DataSource = dicdatas[3];
            setColDisplayFormat(gridView3.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView3.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series3 = chartControl3.Series[0];
            series3.Points.Clear();
            series3.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[3].Rows.Count; i++)
            {
                series3.Points.Add(new SeriesPoint(dicdatas[3].Rows[i][0], dicdatas[3].Rows[i][1]));
            }
            /////////////////////////////4//////////////////////////////////////
            gridControl4.DataSource = dicdatas[4];
            setColDisplayFormat(gridView4.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView4.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series4 = chartControl4.Series[0];
            series4.Points.Clear();
            series4.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[4].Rows.Count; i++)
            {
                series4.Points.Add(new SeriesPoint(dicdatas[4].Rows[i][0], dicdatas[4].Rows[i][1]));
            }
            /////////////////////////////5//////////////////////////////////////
            gridControl5.DataSource = dicdatas[5];
            setColCondStyle(gridView5.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView5.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series5 = chartControl5.Series[0];
            series5.Points.Clear();
            for (int i = 0; i < dicdatas[5].Rows.Count; i++)
            {
                series5.Points.Add(new SeriesPoint(dicdatas[5].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[5].Rows[i][1]), 2)));
            }
            /////////////////////////////6//////////////////////////////////////
            gridView6.Columns.Clear();
            gridControl6.DataSource = dicdatas[6];
            setColDisplayFormat(gridView6.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView6.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series6 = chartControl6.Series[0];
            series6.Points.Clear();
            series6.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[6].Rows.Count; i++)
            {
                series6.Points.Add(new SeriesPoint(dicdatas[6].Rows[i][0], dicdatas[6].Rows[i][1]));
            }
            /////////////////////////////7//////////////////////////////////////
            gridControl7.DataSource = dicdatas[7];
            setColDisplayFormat(gridView7.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView7.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series7 = chartControl7.Series[0];
            series7.Points.Clear();
            series7.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[7].Rows.Count; i++)
            {
                series7.Points.Add(new SeriesPoint(dicdatas[7].Rows[i][0], dicdatas[7].Rows[i][1]));
            }
            /////////////////////////////8//////////////////////////////////////
            gridView8.Columns.Clear();
            gridControl8.DataSource = dicdatas[8];
            setColCondStyle(gridView8.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView8.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series8 = chartControl8.Series[0];
            series8.Points.Clear();
            for (int i = 0; i < dicdatas[8].Rows.Count; i++)
            {
                series8.Points.Add(new SeriesPoint(dicdatas[8].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[8].Rows[i][1]), 2)));
            }
            /////////////////////////////9//////////////////////////////////////
            gridView9.Columns.Clear();
            gridControl9.DataSource = dicdatas[9];
            setColDisplayFormat(gridView9.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView9.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series9 = chartControl9.Series[0];
            series9.Points.Clear();
            for (int i = 0; i < dicdatas[9].Rows.Count; i++)
            {
                series9.Points.Add(new SeriesPoint(dicdatas[9].Rows[i][0], dicdatas[9].Rows[i][1]));
            }
            /////////////////////////////10//////////////////////////////////////
            gridControl10.DataSource = dicdatas[10];
            setColDisplayFormat(gridView10.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView10.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series10 = chartControl10.Series[0];
            series10.Points.Clear();
            series10.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[10].Rows.Count; i++)
            {
                series10.Points.Add(new SeriesPoint(dicdatas[10].Rows[i][0], dicdatas[10].Rows[i][1]));
            }

            /////////////////////////////11//////////////////////////////////////
            gridControl11.DataSource = dicdatas[11];
            setColCondStyle(gridView11.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView11.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series11 = chartControl11.Series[0];
            series11.Points.Clear();
            for (int i = 0; i < dicdatas[11].Rows.Count; i++)
            {
                series11.Points.Add(new SeriesPoint(dicdatas[11].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[11].Rows[i][1]), 2)));
            }
            /////////////////////////////12//////////////////////////////////////
            gridView12.Columns.Clear();
            gridControl12.DataSource = dicdatas[12];
            setColDisplayFormat(gridView12.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView12.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series12 = chartControl12.Series[0];
            series12.Points.Clear();
            series12.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[12].Rows.Count; i++)
            {
                series12.Points.Add(new SeriesPoint(dicdatas[12].Rows[i][0], dicdatas[12].Rows[i][1]));
            }
            /////////////////////////////13//////////////////////////////////////
            gridControl13.DataSource = dicdatas[13];
            setColDisplayFormat(gridView13.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView13.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series13 = chartControl13.Series[0];
            series13.Points.Clear();
            series13.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[13].Rows.Count; i++)
            {
                series13.Points.Add(new SeriesPoint(dicdatas[13].Rows[i][0], dicdatas[13].Rows[i][1]));
            }
        }
        private void setColCondStyle(GridColumn col, FormatConditionEnum cond, object value, Color cellBackClr)
        {
            StyleFormatCondition cn;
            cn = new StyleFormatCondition(cond, col, null, value);
            cn.Appearance.BackColor = cellBackClr;
            col.View.FormatConditions.Add(cn);
        }
        private void setColDisplayFormat(GridColumn col, FormatType displayFormatType, string formatStr)
        {
            col.DisplayFormat.FormatType = displayFormatType;
            col.DisplayFormat.FormatString = formatStr;
        }
        private bool compareWithMOSThreshold(float mos)
        {
            if (compareType.Equals(">="))
            {
                return mos >= mosvalue;
            }
            else
            {
                return mos <= mosvalue;
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView1);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void txtMos_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!(((e.KeyChar >= '0') && (e.KeyChar <= '9')) || e.KeyChar <= 31))
            {
                if (e.KeyChar == '.')
                {
                    if (txtMos.Text.Trim().IndexOf('.') > -1)
                        e.Handled = true;
                }
                else
                    e.Handled = true;
            }
            else
            {
                if (e.KeyChar <= 31)
                {
                    e.Handled = false;
                }
                else if (txtMos.Text.Trim().IndexOf('.') > -1
                    && txtMos.Text.Trim().Substring(txtMos.Text.Trim().IndexOf('.') + 1).Length >= 4)
                {
                    e.Handled = true;
                }
            }
        }

        private void btnMos_Click(object sender, EventArgs e)
        {
            mosvalue = float.Parse(txtMos.Text);
            compareType = cbxCompareType.Text;
            fresh();
        }

        private void btnExpore_Click(object sender, EventArgs e)
        {
            export();
        }
        private void export()
        {
            if (dicDatas != null)
            {
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel file (*.xls)|*.xls";
                saveFileDialog.RestoreDirectory = true;
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Excel.Application excel = new Excel.Application();
                    Excel.Workbook workbook = excel.Workbooks.Add(true);
                    Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;
                    excel.Visible = true;
                    worksheet.Name = "MOS统计分析";

                    int[] firstrow = new int[23];
                    try
                    {
                        setRowValue(worksheet, firstrow);

                        //创建图表
                        CreateChart(ref workbook, ref worksheet, firstrow);
                        //保存文件
                        excel.DisplayAlerts = false;
                        workbook.SaveAs(saveFileDialog.FileName, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Excel.XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                    }
                    catch
                    {
                        MessageBox.Show("文件保存失败!");
                    }
                    finally
                    {
                        //workbook = null;
                        //worksheet = null;
                        //excel.Quit();
                        //excel = null;
                        //GC.Collect();
                    }
                }
            }
        }

        private void setRowValue(Excel.Worksheet worksheet, int[] firstrow)
        {
            int excelrow = 1;
            for (int i = 1; i <= 22; i++)
            {
                firstrow[i] = excelrow;
                worksheet.Cells[excelrow, 1] = dicDatas[i].Columns[0].ColumnName;
                worksheet.Cells[excelrow, 2] = dicDatas[i].Columns[1].ColumnName;
                excelrow++;
                int rowAddCount = 15 - dicDatas[i].Rows.Count;
                //int rowAddCount = 1;

                for (int row = 0; row < dicDatas[i].Rows.Count; row++)
                {
                    worksheet.Cells[excelrow, 1] = dicDatas[i].Rows[row][0];
                    if (i == 1)
                    {
                        worksheet.Cells[excelrow, 2] = dicDatas[i].Rows[row][1];
                    }
                    else if (i == 2 || i == 5 || i == 8 || i == 11 || i == 14 || i == 17 || i == 20)
                    {
                        worksheet.Cells[excelrow, 2] = Math.Round((float)dicDatas[i].Rows[row][1], 2);
                    }
                    else
                    {
                        worksheet.Cells[excelrow, 2] = ((float)dicDatas[i].Rows[row][1]).ToString("p");
                    }
                    excelrow++;
                }
                excelrow = addRemark(worksheet, excelrow, i, rowAddCount);
                excelrow++;
            }
        }

        private int addRemark(Excel.Worksheet worksheet, int excelrow, int i, int rowAddCount)
        {
            if (i == 2 || i == 3)
            {
                excelrow = addRow(excelrow, i, rowAddCount);
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定调制方式比例（QAM16>50%)，固定Sinr（Sinr>-3）和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            else if (i == 5 || i == 6)
            {
                excelrow = addRow(excelrow, i, rowAddCount);
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定调制方式比例（QAM16>50%)，固定切换次数（切换次数为0）和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            else if (i == 8 || i == 9 || i == 14 || i == 15
                || i == 17 || i == 18 || i == 20 || i == 21)
            {
                excelrow += rowAddCount;
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定Sinr（Sinr>-3）和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            else if (i == 11 || i == 12)
            {
                excelrow += rowAddCount;
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定调制方式比例（QAM16>50%)，固定Sinr（Sinr>-3），固定切换次数（切换次数为0和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            else if (i == 4 || i == 7 || i == 10 || i == 13 || i == 16 || i == 19 || i == 22)
            {
                excelrow = addRow(excelrow, i, rowAddCount);
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在所有MOS周期内得出的.";
                excelrow += 2;
            }

            return excelrow;
        }

        private int addRow(int excelrow, int i, int rowAddCount)
        {
            if (dicDatas[i].Rows.Count < 15)
            {
                excelrow += rowAddCount;
            }

            return excelrow;
        }

        private void CreateChart(ref Excel.Workbook workbook, ref Excel.Worksheet worksheet, int[] firstrow)
        {
            int moscount = dicDatas[1].Rows.Count;
            int evencount = dicDatas[2].Rows.Count;
            int sinrcount = dicDatas[5].Rows.Count;
            int speechcount = dicDatas[8].Rows.Count;
            int rsrpcount = dicDatas[11].Rows.Count;
            int dlQam16Count = dicDatas[14].Rows.Count;
            int dlQam64Count = dicDatas[17].Rows.Count;
            int dlQpskCount = dicDatas[20].Rows.Count;

            #region MOS比例图表
            Excel.Chart chart1 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange1 = worksheet.get_Range("B1", "B" + (moscount + 1));

            chart1.ChartWizard(chartrange1, Excel.XlChartType.xlLine, Missing.Value, Excel.XlRowCol.xlColumns, 0, 1, false,
                Missing.Value, Missing.Value, Missing.Value, Missing.Value);

            chart1.HasTitle = false;
            Excel.Series exseries1 = (Excel.Series)chart1.SeriesCollection(1);
            exseries1.XValues = worksheet.get_Range("A2", "A" + (moscount + 1));
            exseries1.HasDataLabels = false;

            Excel.Axis yAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis1.MinimumScale = 0;
            yAxis1.HasMajorGridlines = true;
            yAxis1.MajorGridlines.Border.ColorIndex = 15;

            Excel.Axis xAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlCategory, Excel.XlAxisGroup.xlPrimary);
            xAxis1.TickLabels.Font.Size = 10;

            chart1.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(1).Top = 2;
            worksheet.Shapes.Item(1).Left = 162;
            #endregion

            #region 切换次数
            //切换次数与MOS均值
            Excel.Chart chart2 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange2 = worksheet.get_Range("B" + firstrow[2], "B" + (firstrow[2] + evencount));
            chart2.ChartWizard(chartrange2, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries2 = (Excel.Series)chart2.SeriesCollection(1);
            exseries2.BarShape = Excel.XlBarShape.xlCylinder;
            exseries2.XValues = worksheet.get_Range("A" + (firstrow[2] + 1), "A" + (firstrow[2] + evencount));
            exseries2.HasDataLabels = true;

            Excel.Axis yAxis2 = (Excel.Axis)chart2.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis2.MinimumScale = 0.0;
            yAxis2.MajorUnit = 1.0;
            yAxis2.HasMajorGridlines = true;
            yAxis2.MajorGridlines.Border.ColorIndex = 15;
            yAxis2.TickLabels.NumberFormat = "0.00";

            chart2.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            //worksheet.Shapes.Item(2).Top = 220;
            worksheet.Shapes.Item(2).Top = (float)(double)worksheet.get_Range("D" + firstrow[2], "J" + firstrow[2] + 16).Top;
            worksheet.Shapes.Item(2).Left = (float)(double)worksheet.get_Range("D" + firstrow[2], "J" + firstrow[2] + 16).Left;


            ////切换次数与MOS>=3
            Excel.Chart chart3 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange3 = worksheet.get_Range("B" + firstrow[3], "B" + (firstrow[3] + evencount));

            chart3.ChartWizard(chartrange3, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries3 = (Excel.Series)chart3.SeriesCollection(1);
            exseries3.BarShape = Excel.XlBarShape.xlCylinder;
            exseries3.XValues = worksheet.get_Range("A" + (firstrow[3] + 1), "A" + (firstrow[3] + evencount));
            exseries3.HasDataLabels = true;

            Excel.Axis yAxis3 = (Excel.Axis)chart3.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis3.MinimumScale = 0;
            yAxis3.HasMajorGridlines = true;
            yAxis3.MajorGridlines.Border.ColorIndex = 15;

            chart3.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(3).Top = (float)(double)worksheet.get_Range("D" + firstrow[3], "J" + firstrow[3] + 16).Top;
            worksheet.Shapes.Item(3).Left = (float)(double)worksheet.get_Range("D" + firstrow[3], "J" + firstrow[3] + 16).Left;


            ////切换次数与MOS比例
            Excel.Chart chart4 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange4 = worksheet.get_Range("B" + firstrow[4], "B" + (firstrow[4] + evencount));

            chart4.ChartWizard(chartrange4, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries4 = (Excel.Series)chart4.SeriesCollection(1);
            exseries4.BarShape = Excel.XlBarShape.xlCylinder;
            exseries4.XValues = worksheet.get_Range("A" + (firstrow[4] + 1), "A" + (firstrow[4] + evencount));
            exseries4.HasDataLabels = true;

            Excel.Axis yAxis4 = (Excel.Axis)chart4.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis4.MinimumScale = 0;
            yAxis4.HasMajorGridlines = true;
            yAxis4.MajorGridlines.Border.ColorIndex = 15;

            chart4.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(4).Top = (float)(double)worksheet.get_Range("D" + firstrow[4], "J" + firstrow[4] + 16).Top;
            worksheet.Shapes.Item(4).Left = (float)(double)worksheet.get_Range("D" + firstrow[4], "J" + firstrow[4] + 16).Left;
            #endregion

            #region Sinr
            /////////Sinr与MOS均值/////////////////////
            Excel.Chart chart5 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange5 = worksheet.get_Range("B" + firstrow[5], "B" + (firstrow[5] + sinrcount));
            chart5.ChartWizard(chartrange5, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries5 = (Excel.Series)chart5.SeriesCollection(1);
            exseries5.BarShape = Excel.XlBarShape.xlCylinder;
            exseries5.XValues = worksheet.get_Range("A" + (firstrow[5] + 1), "A" + (firstrow[5] + sinrcount));
            exseries5.HasDataLabels = true;

            Excel.Axis yAxis5 = (Excel.Axis)chart5.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis5.MinimumScale = 0;
            yAxis5.MajorUnit = 1.0;
            yAxis5.HasMajorGridlines = true;
            yAxis5.MajorGridlines.Border.ColorIndex = 15;
            yAxis5.TickLabels.NumberFormat = "0.00";

            chart5.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(5).Top = (float)(double)worksheet.get_Range("D" + firstrow[5], "J" + firstrow[5] + 16).Top;
            worksheet.Shapes.Item(5).Left = (float)(double)worksheet.get_Range("D" + firstrow[5], "J" + firstrow[5] + 16).Left;


            ////Sinr与MOS>=3
            Excel.Chart chart6 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange6 = worksheet.get_Range("B" + firstrow[6], "B" + (firstrow[6] + sinrcount));

            chart6.ChartWizard(chartrange6, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries6 = (Excel.Series)chart6.SeriesCollection(1);
            exseries6.BarShape = Excel.XlBarShape.xlCylinder;
            exseries6.XValues = worksheet.get_Range("A" + (firstrow[6] + 1), "A" + (firstrow[6] + sinrcount));
            exseries6.HasDataLabels = true;

            Excel.Axis yAxis6 = (Excel.Axis)chart6.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis6.MinimumScale = 0;
            yAxis6.HasMajorGridlines = true;
            yAxis6.MajorGridlines.Border.ColorIndex = 15;

            chart6.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(6).Top = (float)(double)worksheet.get_Range("D" + firstrow[6], "J" + firstrow[6] + 16).Top;
            worksheet.Shapes.Item(6).Left = (float)(double)worksheet.get_Range("D" + firstrow[6], "J" + firstrow[6] + 16).Left;


            ////Sinr与MOS比例
            Excel.Chart chart7 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange7 = worksheet.get_Range("B" + firstrow[7], "B" + (firstrow[7] + sinrcount));

            chart7.ChartWizard(chartrange7, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries7 = (Excel.Series)chart7.SeriesCollection(1);
            exseries7.BarShape = Excel.XlBarShape.xlCylinder;
            exseries7.XValues = worksheet.get_Range("A" + (firstrow[7] + 1), "A" + (firstrow[7] + sinrcount));
            exseries7.HasDataLabels = true;

            Excel.Axis yAxis7 = (Excel.Axis)chart7.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis7.MinimumScale = 0;
            yAxis7.HasMajorGridlines = true;
            yAxis7.MajorGridlines.Border.ColorIndex = 15;

            chart7.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(7).Top = (float)(double)worksheet.get_Range("D" + firstrow[7], "J" + firstrow[7] + 16).Top;
            worksheet.Shapes.Item(7).Left = (float)(double)worksheet.get_Range("D" + firstrow[7], "J" + firstrow[7] + 16).Left;
            #endregion

            #region 调制方式
            //调制方式与MOS均值
            Excel.Chart chart8 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange8 = worksheet.get_Range("B" + firstrow[8], "B" + (firstrow[8] + speechcount));
            chart8.ChartWizard(chartrange8, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries8 = (Excel.Series)chart8.SeriesCollection(1);
            exseries8.BarShape = Excel.XlBarShape.xlCylinder;
            exseries8.XValues = worksheet.get_Range("A" + (firstrow[8] + 1), "A" + (firstrow[8] + speechcount));
            exseries8.HasDataLabels = true;

            Excel.Axis yAxis8 = (Excel.Axis)chart8.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis8.MinimumScale = 0;
            yAxis8.MajorUnit = 1.0;
            yAxis8.HasMajorGridlines = true;
            yAxis8.MajorGridlines.Border.ColorIndex = 15;
            yAxis8.TickLabels.NumberFormat = "0.00";

            chart8.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(8).Top = (float)(double)worksheet.get_Range("D" + firstrow[8], "J" + firstrow[8] + 16).Top;
            worksheet.Shapes.Item(8).Left = (float)(double)worksheet.get_Range("D" + firstrow[8], "J" + firstrow[8] + 16).Left;

            ////调制方式与MOS>=3
            Excel.Chart chart9 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange9 = worksheet.get_Range("B" + firstrow[9], "B" + (firstrow[9] + speechcount));

            chart9.ChartWizard(chartrange9, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries9 = (Excel.Series)chart9.SeriesCollection(1);
            exseries9.BarShape = Excel.XlBarShape.xlCylinder;
            exseries9.XValues = worksheet.get_Range("A" + (firstrow[9] + 1), "A" + (firstrow[9] + speechcount));
            exseries9.HasDataLabels = true;

            Excel.Axis yAxis9 = (Excel.Axis)chart9.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis9.MinimumScale = 0;
            yAxis9.HasMajorGridlines = true;
            yAxis9.MajorGridlines.Border.ColorIndex = 15;

            chart9.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(9).Top = (float)(double)worksheet.get_Range("D" + firstrow[9], "J" + firstrow[9] + 16).Top;
            worksheet.Shapes.Item(9).Left = (float)(double)worksheet.get_Range("D" + firstrow[9], "J" + firstrow[9] + 16).Left;


            ////调制方式与MOS比例
            Excel.Chart chart10 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange10 = worksheet.get_Range("B" + firstrow[10], "B" + (firstrow[10] + speechcount));

            chart10.ChartWizard(chartrange10, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries10 = (Excel.Series)chart10.SeriesCollection(1);
            exseries10.BarShape = Excel.XlBarShape.xlCylinder;
            exseries10.XValues = worksheet.get_Range("A" + (firstrow[10] + 1), "A" + (firstrow[10] + speechcount));
            exseries10.HasDataLabels = true;

            Excel.Axis yAxis10 = (Excel.Axis)chart10.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis10.MinimumScale = 0;
            yAxis10.HasMajorGridlines = true;
            yAxis10.MajorGridlines.Border.ColorIndex = 15;

            chart10.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(10).Top = (float)(double)worksheet.get_Range("D" + firstrow[10], "J" + firstrow[10] + 16).Top;
            worksheet.Shapes.Item(10).Left = (float)(double)worksheet.get_Range("D" + firstrow[10], "J" + firstrow[10] + 16).Left;
            #endregion

            #region Rsrp
            /////////Rsrp与MOS均值/////////////////////
            Excel.Chart chart11 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange11 = worksheet.get_Range("B" + firstrow[11], "B" + (firstrow[11] + rsrpcount));
            chart11.ChartWizard(chartrange11, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries11 = (Excel.Series)chart11.SeriesCollection(1);
            exseries11.BarShape = Excel.XlBarShape.xlCylinder;
            exseries11.XValues = worksheet.get_Range("A" + (firstrow[11] + 1), "A" + (firstrow[11] + rsrpcount));
            exseries11.HasDataLabels = true;

            Excel.Axis yAxis11 = (Excel.Axis)chart11.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis11.MinimumScale = 0;
            yAxis11.MajorUnit = 1.0;
            yAxis11.HasMajorGridlines = true;
            yAxis11.MajorGridlines.Border.ColorIndex = 15;
            yAxis11.TickLabels.NumberFormat = "0.00";

            chart11.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(11).Top = (float)(double)worksheet.get_Range("D" + firstrow[11], "J" + firstrow[11] + 16).Top;
            worksheet.Shapes.Item(11).Left = (float)(double)worksheet.get_Range("D" + firstrow[11], "J" + firstrow[11] + 16).Left;


            ////Rsrp与MOS>=3
            Excel.Chart chart12 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange12 = worksheet.get_Range("B" + firstrow[12], "B" + (firstrow[12] + rsrpcount));

            chart12.ChartWizard(chartrange12, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries12 = (Excel.Series)chart12.SeriesCollection(1);
            exseries12.BarShape = Excel.XlBarShape.xlCylinder;
            exseries12.XValues = worksheet.get_Range("A" + (firstrow[12] + 1), "A" + (firstrow[12] + rsrpcount));
            exseries12.HasDataLabels = true;

            Excel.Axis yAxis12 = (Excel.Axis)chart12.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis12.MinimumScale = 0;
            yAxis12.HasMajorGridlines = true;
            yAxis12.MajorGridlines.Border.ColorIndex = 15;

            chart12.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(12).Top = (float)(double)worksheet.get_Range("D" + firstrow[12], "J" + firstrow[12] + 16).Top;
            worksheet.Shapes.Item(12).Left = (float)(double)worksheet.get_Range("D" + firstrow[12], "J" + firstrow[12] + 16).Left;


            ////Rsrp与MOS比例
            Excel.Chart chart13 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange13 = worksheet.get_Range("B" + firstrow[13], "B" + (firstrow[13] + rsrpcount));

            chart13.ChartWizard(chartrange13, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries13 = (Excel.Series)chart13.SeriesCollection(1);
            exseries13.BarShape = Excel.XlBarShape.xlCylinder;
            exseries13.XValues = worksheet.get_Range("A" + (firstrow[13] + 1), "A" + (firstrow[13] + rsrpcount));
            exseries13.HasDataLabels = true;

            Excel.Axis yAxis13 = (Excel.Axis)chart13.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis13.MinimumScale = 0;
            yAxis13.HasMajorGridlines = true;
            yAxis13.MajorGridlines.Border.ColorIndex = 15;

            chart13.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(13).Top = (float)(double)worksheet.get_Range("D" + firstrow[13], "J" + firstrow[13] + 16).Top;
            worksheet.Shapes.Item(13).Left = (float)(double)worksheet.get_Range("D" + firstrow[13], "J" + firstrow[13] + 16).Left;
            #endregion

            #region 下行Qam16
            Excel.Chart chart14 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange14 = worksheet.get_Range("B" + firstrow[14], "B" + (firstrow[14] + dlQam16Count));
            chart14.ChartWizard(chartrange14, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries14 = (Excel.Series)chart14.SeriesCollection(1);
            exseries14.BarShape = Excel.XlBarShape.xlCylinder;
            exseries14.XValues = worksheet.get_Range("A" + (firstrow[14] + 1), "A" + (firstrow[14] + dlQam16Count));
            exseries14.HasDataLabels = true;

            Excel.Axis yAxis14 = (Excel.Axis)chart14.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis14.MinimumScale = 0;
            yAxis14.MajorUnit = 1.0;
            yAxis14.HasMajorGridlines = true;
            yAxis14.MajorGridlines.Border.ColorIndex = 15;
            yAxis14.TickLabels.NumberFormat = "0.00";

            chart14.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(14).Top = (float)(double)worksheet.get_Range("D" + firstrow[14], "J" + firstrow[14] + 16).Top;
            worksheet.Shapes.Item(14).Left = (float)(double)worksheet.get_Range("D" + firstrow[14], "J" + firstrow[14] + 16).Left;

            ////调制方式与MOS>=3
            Excel.Chart chart15 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange15 = worksheet.get_Range("B" + firstrow[15], "B" + (firstrow[15] + dlQam16Count));

            chart15.ChartWizard(chartrange15, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries15 = (Excel.Series)chart15.SeriesCollection(1);
            exseries15.BarShape = Excel.XlBarShape.xlCylinder;
            exseries15.XValues = worksheet.get_Range("A" + (firstrow[15] + 1), "A" + (firstrow[15] + dlQam16Count));
            exseries15.HasDataLabels = true;

            Excel.Axis yAxis15 = (Excel.Axis)chart15.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis15.MinimumScale = 0;
            yAxis15.HasMajorGridlines = true;
            yAxis15.MajorGridlines.Border.ColorIndex = 15;

            chart15.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(15).Top = (float)(double)worksheet.get_Range("D" + firstrow[15], "J" + firstrow[15] + 16).Top;
            worksheet.Shapes.Item(15).Left = (float)(double)worksheet.get_Range("D" + firstrow[15], "J" + firstrow[15] + 16).Left;


            ////调制方式与MOS比例
            Excel.Chart chart16 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange16 = worksheet.get_Range("B" + firstrow[16], "B" + (firstrow[16] + dlQam16Count));

            chart16.ChartWizard(chartrange16, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries16 = (Excel.Series)chart16.SeriesCollection(1);
            exseries16.BarShape = Excel.XlBarShape.xlCylinder;
            exseries16.XValues = worksheet.get_Range("A" + (firstrow[16] + 1), "A" + (firstrow[16] + dlQam16Count));
            exseries16.HasDataLabels = true;

            Excel.Axis yAxis16 = (Excel.Axis)chart16.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis16.MinimumScale = 0;
            yAxis16.HasMajorGridlines = true;
            yAxis16.MajorGridlines.Border.ColorIndex = 15;

            chart16.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(16).Top = (float)(double)worksheet.get_Range("D" + firstrow[16], "J" + firstrow[16] + 16).Top;
            worksheet.Shapes.Item(16).Left = (float)(double)worksheet.get_Range("D" + firstrow[16], "J" + firstrow[16] + 16).Left;
            #endregion

            #region 下行Qam64
            //调制方式与MOS均值
            Excel.Chart chart17 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange17 = worksheet.get_Range("B" + firstrow[17], "B" + (firstrow[17] + dlQam64Count));
            chart17.ChartWizard(chartrange17, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries17 = (Excel.Series)chart17.SeriesCollection(1);
            exseries17.BarShape = Excel.XlBarShape.xlCylinder;
            exseries17.XValues = worksheet.get_Range("A" + (firstrow[17] + 1), "A" + (firstrow[17] + dlQam64Count));
            exseries17.HasDataLabels = true;

            Excel.Axis yAxis17 = (Excel.Axis)chart17.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis17.MinimumScale = 0;
            yAxis17.MajorUnit = 1.0;
            yAxis17.HasMajorGridlines = true;
            yAxis17.MajorGridlines.Border.ColorIndex = 15;
            yAxis17.TickLabels.NumberFormat = "0.00";

            chart17.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(17).Top = (float)(double)worksheet.get_Range("D" + firstrow[17], "J" + firstrow[17] + 16).Top;
            worksheet.Shapes.Item(17).Left = (float)(double)worksheet.get_Range("D" + firstrow[17], "J" + firstrow[17] + 16).Left;

            ////调制方式与MOS>=3
            Excel.Chart chart18 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange18 = worksheet.get_Range("B" + firstrow[18], "B" + (firstrow[18] + dlQam64Count));

            chart18.ChartWizard(chartrange18, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries18 = (Excel.Series)chart18.SeriesCollection(1);
            exseries18.BarShape = Excel.XlBarShape.xlCylinder;
            exseries18.XValues = worksheet.get_Range("A" + (firstrow[18] + 1), "A" + (firstrow[18] + dlQam64Count));
            exseries18.HasDataLabels = true;

            Excel.Axis yAxis18 = (Excel.Axis)chart18.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis18.MinimumScale = 0;
            yAxis18.HasMajorGridlines = true;
            yAxis18.MajorGridlines.Border.ColorIndex = 15;

            chart18.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(18).Top = (float)(double)worksheet.get_Range("D" + firstrow[18], "J" + firstrow[18] + 16).Top;
            worksheet.Shapes.Item(18).Left = (float)(double)worksheet.get_Range("D" + firstrow[18], "J" + firstrow[18] + 16).Left;


            ////调制方式与MOS比例
            Excel.Chart chart19 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange19 = worksheet.get_Range("B" + firstrow[19], "B" + (firstrow[19] + dlQam64Count));

            chart19.ChartWizard(chartrange19, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries19 = (Excel.Series)chart19.SeriesCollection(1);
            exseries19.BarShape = Excel.XlBarShape.xlCylinder;
            exseries19.XValues = worksheet.get_Range("A" + (firstrow[19] + 1), "A" + (firstrow[19] + dlQam64Count));
            exseries19.HasDataLabels = true;

            Excel.Axis yAxis19 = (Excel.Axis)chart19.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis19.MinimumScale = 0;
            yAxis19.HasMajorGridlines = true;
            yAxis19.MajorGridlines.Border.ColorIndex = 15;

            chart19.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(19).Top = (float)(double)worksheet.get_Range("D" + firstrow[19], "J" + firstrow[19] + 16).Top;
            worksheet.Shapes.Item(19).Left = (float)(double)worksheet.get_Range("D" + firstrow[19], "J" + firstrow[19] + 16).Left;
            #endregion

            #region 下行Qpsk
            //调制方式与MOS均值
            Excel.Chart chart20 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange20 = worksheet.get_Range("B" + firstrow[20], "B" + (firstrow[20] + dlQpskCount));
            chart20.ChartWizard(chartrange20, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries20 = (Excel.Series)chart20.SeriesCollection(1);
            exseries20.BarShape = Excel.XlBarShape.xlCylinder;
            exseries20.XValues = worksheet.get_Range("A" + (firstrow[20] + 1), "A" + (firstrow[20] + dlQpskCount));
            exseries20.HasDataLabels = true;

            Excel.Axis yAxis20 = (Excel.Axis)chart20.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis20.MinimumScale = 0;
            yAxis20.MajorUnit = 1.0;
            yAxis20.HasMajorGridlines = true;
            yAxis20.MajorGridlines.Border.ColorIndex = 15;
            yAxis20.TickLabels.NumberFormat = "0.00";

            chart20.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(20).Top = (float)(double)worksheet.get_Range("D" + firstrow[20], "J" + firstrow[20] + 16).Top;
            worksheet.Shapes.Item(20).Left = (float)(double)worksheet.get_Range("D" + firstrow[20], "J" + firstrow[20] + 16).Left;

            ////调制方式与MOS>=3
            Excel.Chart chart21 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange21 = worksheet.get_Range("B" + firstrow[21], "B" + (firstrow[21] + dlQpskCount));

            chart21.ChartWizard(chartrange21, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries21 = (Excel.Series)chart21.SeriesCollection(1);
            exseries21.BarShape = Excel.XlBarShape.xlCylinder;
            exseries21.XValues = worksheet.get_Range("A" + (firstrow[21] + 1), "A" + (firstrow[21] + dlQpskCount));
            exseries21.HasDataLabels = true;

            Excel.Axis yAxis21 = (Excel.Axis)chart21.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis21.MinimumScale = 0;
            yAxis21.HasMajorGridlines = true;
            yAxis21.MajorGridlines.Border.ColorIndex = 15;

            chart21.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(21).Top = (float)(double)worksheet.get_Range("D" + firstrow[21], "J" + firstrow[21] + 16).Top;
            worksheet.Shapes.Item(21).Left = (float)(double)worksheet.get_Range("D" + firstrow[21], "J" + firstrow[21] + 16).Left;


            ////调制方式与MOS比例
            Excel.Chart chart22 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange22 = worksheet.get_Range("B" + firstrow[22], "B" + (firstrow[22] + dlQpskCount));

            chart22.ChartWizard(chartrange22, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries22 = (Excel.Series)chart22.SeriesCollection(1);
            exseries22.BarShape = Excel.XlBarShape.xlCylinder;
            exseries22.XValues = worksheet.get_Range("A" + (firstrow[22] + 1), "A" + (firstrow[22] + dlQpskCount));
            exseries22.HasDataLabels = true;

            Excel.Axis yAxis22 = (Excel.Axis)chart22.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis22.MinimumScale = 0;
            yAxis22.HasMajorGridlines = true;
            yAxis22.MajorGridlines.Border.ColorIndex = 15;

            chart22.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(22).Top = (float)(double)worksheet.get_Range("D" + firstrow[22], "J" + firstrow[22] + 16).Top;
            worksheet.Shapes.Item(22).Left = (float)(double)worksheet.get_Range("D" + firstrow[22], "J" + firstrow[22] + 16).Left;
            #endregion
        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage == xtraTabPage1)
            {
                panel1.Visible = false;
            }
            else
            {
                panel1.Visible = true;
            }
        }

        private void cbxModel_SelectedIndexChanged(object sender, EventArgs e)
        {
            ShowMode(cbxModel.SelectedIndex);
        }

        private void ShowMode(int index)
        {
            int val8 = 8;
            int val9 = 9;
            int val10 = 10;
            string title = "调制方式上行QAM16比例";

            switch(index)
            {
                case 0 :
                    break;
                case 1:
                    val8 = 14;
                    val9 = 15;
                    val10 = 16;
                    title = "调制方式下行QAM16比例";
                    break;
                case 2:
                    val8 = 17;
                    val9 = 18;
                    val10 = 19;
                    title = "调制方式下行QAM64比例";
                    break;
                case 3:
                    val8 = 20;
                    val9 = 21;
                    val10 = 22;
                    title = "调制方式下行QPSK比例";
                    break;
            }

            if (dicDatas[val8] == null || dicDatas[val9] == null || dicDatas[val10] == null)
                return;

            /////////////////////////////8//////////////////////////////////////
            gridView8.Columns.Clear();
            gridControl8.DataSource = dicDatas[val8];
            setColCondStyle(gridView8.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView8.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series8 = chartControl8.Series[0];
            series8.Points.Clear();
            for (int i = 0; i < dicDatas[val8].Rows.Count; i++)
            {
                series8.Points.Add(new SeriesPoint(dicDatas[val8].Rows[i][0], Math.Round(Convert.ToDouble(dicDatas[val8].Rows[i][1]), 2)));
            }
            /////////////////////////////9//////////////////////////////////////
            gridView9.Columns.Clear();
            gridControl9.DataSource = dicDatas[val9];
            setColDisplayFormat(gridView9.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView9.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series9 = chartControl9.Series[0];
            series9.Points.Clear();
            for (int i = 0; i < dicDatas[val9].Rows.Count; i++)
            {
                series9.Points.Add(new SeriesPoint(dicDatas[val9].Rows[i][0], dicDatas[val9].Rows[i][1]));
            }
            /////////////////////////////10//////////////////////////////////////
            gridView10.Columns.Clear();
            gridControl10.DataSource = dicDatas[val10];
            setColDisplayFormat(gridView10.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView10.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series10 = chartControl10.Series[0];
            series10.Points.Clear();
            series10.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicDatas[val10].Rows.Count; i++)
            {
                series10.Points.Add(new SeriesPoint(dicDatas[val10].Rows[i][0], dicDatas[val10].Rows[i][1]));
            }
            chartControl10.Titles[0].Text = title;
        }

    }

    public class VolteMosAnalysisInfo
    {
        public Dictionary<double, List<float>> DicMos { get; set; }
        public Dictionary<int, List<float>> DicEvenCountMos { get; set; }
        public Dictionary<string, List<float>> DicSinrCountMos { get; set; }
        public Dictionary<string, List<float>> DicModulationMos { get; set; }
        public Dictionary<int, List<float>> DicEvenCountPer { get; set; }
        public Dictionary<string, List<float>> DicSinrCountPer { get; set; }
        public Dictionary<string, List<float>> DicModulationPer { get; set; }
        public Dictionary<string, List<float>> DicRsrpCountMos { get; set; }
        public Dictionary<string, List<float>> DicRsrpCountPer { get; set; }
        public Dictionary<string, List<float>> DicDlQam16Mos { get; set; }
        public Dictionary<string, List<float>> DicDlQam16Per { get; set; }
        public Dictionary<string, List<float>> DicDlQam64Mos { get; set; }
        public Dictionary<string, List<float>> DicDlQam64Per { get; set; }
        public Dictionary<string, List<float>> DicDlQpskMos { get; set; }
        public Dictionary<string, List<float>> DicDlQpskPer { get; set; }
    }
}
