<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ctxQuickSet.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="ctxQuickSet.Size" type="System.Drawing.Size, System.Drawing">
    <value>149, 26</value>
  </data>
  <data name="&gt;&gt;ctxQuickSet.Name" xml:space="preserve">
    <value>ctxQuickSet</value>
  </data>
  <data name="&gt;&gt;ctxQuickSet.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripMenuItem2.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 22</value>
  </data>
  <data name="toolStripMenuItem2.Text" xml:space="preserve">
    <value>栅格显示指标</value>
  </data>
  <data name="testToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 22</value>
  </data>
  <data name="testToolStripMenuItem.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnQuickSet.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnQuickSet.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btnQuickSet.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnQuickSet.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 6</value>
  </data>
  <data name="btnQuickSet.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 24</value>
  </data>
  <data name="btnQuickSet.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="btnQuickSet.Text" xml:space="preserve">
    <value>指标选择</value>
  </data>
  <data name="&gt;&gt;btnQuickSet.Name" xml:space="preserve">
    <value>btnQuickSet</value>
  </data>
  <data name="&gt;&gt;btnQuickSet.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnQuickSet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnQuickSet.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="comboBoxLegendType.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="comboBoxLegendType.Location" type="System.Drawing.Point, System.Drawing">
    <value>82, 8</value>
  </data>
  <data name="comboBoxLegendType.Size" type="System.Drawing.Size, System.Drawing">
    <value>302, 20</value>
  </data>
  <data name="comboBoxLegendType.TabIndex" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="&gt;&gt;comboBoxLegendType.Name" xml:space="preserve">
    <value>comboBoxLegendType</value>
  </data>
  <data name="&gt;&gt;comboBoxLegendType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxLegendType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxLegendType.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="listBoxLegend.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="listBoxLegend.IntegralHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="listBoxLegend.ItemHeight" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="listBoxLegend.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 36</value>
  </data>
  <data name="listBoxLegend.Size" type="System.Drawing.Size, System.Drawing">
    <value>379, 127</value>
  </data>
  <data name="listBoxLegend.TabIndex" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="&gt;&gt;listBoxLegend.Name" xml:space="preserve">
    <value>listBoxLegend</value>
  </data>
  <data name="&gt;&gt;listBoxLegend.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listBoxLegend.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;listBoxLegend.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="contextMenuStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>138, 17</value>
  </metadata>
  <data name="contextMenuStrip.Size" type="System.Drawing.Size, System.Drawing">
    <value>110, 26</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip.Name" xml:space="preserve">
    <value>contextMenuStrip</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="miSetting.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 22</value>
  </data>
  <data name="miSetting.Text" xml:space="preserve">
    <value>设置...</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>388, 169</value>
  </data>
  <data name="&gt;&gt;toolStripMenuItem2.Name" xml:space="preserve">
    <value>toolStripMenuItem2</value>
  </data>
  <data name="&gt;&gt;toolStripMenuItem2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;testToolStripMenuItem.Name" xml:space="preserve">
    <value>testToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;testToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;miSetting.Name" xml:space="preserve">
    <value>miSetting</value>
  </data>
  <data name="&gt;&gt;miSetting.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>LegendPanel</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>