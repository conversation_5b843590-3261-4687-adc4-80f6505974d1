﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteMgrsHighCoverageSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.numGridNum = new System.Windows.Forms.NumericUpDown();
            this.chkGridNum = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chkFBandType = new DevExpress.XtraEditors.CheckEdit();
            this.btnFile = new System.Windows.Forms.Button();
            this.txtCsvPath = new System.Windows.Forms.TextBox();
            this.checkEditSampleData = new DevExpress.XtraEditors.CheckEdit();
            this.chkTwoEarfcn = new DevExpress.XtraEditors.CheckEdit();
            this.cbxFreqType = new System.Windows.Forms.ComboBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numOptionalRsrp = new System.Windows.Forms.NumericUpDown();
            this.chkOptionalRsrp = new System.Windows.Forms.CheckBox();
            this.numGridCount = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numRsrpDiff = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numCoverage = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numRsrpMin = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridNum)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkFBandType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditSampleData.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkTwoEarfcn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.numGridNum);
            this.groupBox1.Controls.Add(this.chkGridNum);
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.numOptionalRsrp);
            this.groupBox1.Controls.Add(this.chkOptionalRsrp);
            this.groupBox1.Controls.Add(this.numGridCount);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numRsrpDiff);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.numCoverage);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numRsrpMin);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 359);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(391, 215);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(143, 12);
            this.label9.TabIndex = 41;
            this.label9.Text = "个(默认为邻近的8个栅格)";
            // 
            // numGridNum
            // 
            this.numGridNum.Enabled = false;
            this.numGridNum.Location = new System.Drawing.Point(265, 211);
            this.numGridNum.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numGridNum.Minimum = new decimal(new int[] {
            10000,
            0,
            0,
            -2147483648});
            this.numGridNum.Name = "numGridNum";
            this.numGridNum.Size = new System.Drawing.Size(120, 21);
            this.numGridNum.TabIndex = 40;
            this.numGridNum.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridNum.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // chkGridNum
            // 
            this.chkGridNum.AutoSize = true;
            this.chkGridNum.Location = new System.Drawing.Point(175, 214);
            this.chkGridNum.Name = "chkGridNum";
            this.chkGridNum.Size = new System.Drawing.Size(84, 16);
            this.chkGridNum.TabIndex = 39;
            this.chkGridNum.Text = "栅格间距≤";
            this.chkGridNum.UseVisualStyleBackColor = true;
            this.chkGridNum.CheckedChanged += new System.EventHandler(this.chkGridNum_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkFBandType);
            this.groupBox2.Controls.Add(this.btnFile);
            this.groupBox2.Controls.Add(this.txtCsvPath);
            this.groupBox2.Controls.Add(this.checkEditSampleData);
            this.groupBox2.Controls.Add(this.chkTwoEarfcn);
            this.groupBox2.Controls.Add(this.cbxFreqType);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Location = new System.Drawing.Point(119, 242);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(384, 111);
            this.groupBox2.TabIndex = 38;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "分析方式";
            // 
            // chkFBandType
            // 
            this.chkFBandType.Enabled = false;
            this.chkFBandType.Location = new System.Drawing.Point(13, 52);
            this.chkFBandType.Name = "chkFBandType";
            this.chkFBandType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFBandType.Properties.Appearance.Options.UseFont = true;
            this.chkFBandType.Properties.Caption = "F频F1/F2分频处理";
            this.chkFBandType.Size = new System.Drawing.Size(132, 19);
            this.chkFBandType.TabIndex = 45;
            // 
            // btnFile
            // 
            this.btnFile.Enabled = false;
            this.btnFile.Location = new System.Drawing.Point(341, 81);
            this.btnFile.Name = "btnFile";
            this.btnFile.Size = new System.Drawing.Size(37, 23);
            this.btnFile.TabIndex = 41;
            this.btnFile.Text = "浏览";
            this.btnFile.UseVisualStyleBackColor = true;
            this.btnFile.Click += new System.EventHandler(this.btnFile_Click);
            // 
            // txtCsvPath
            // 
            this.txtCsvPath.Enabled = false;
            this.txtCsvPath.Location = new System.Drawing.Point(90, 82);
            this.txtCsvPath.Name = "txtCsvPath";
            this.txtCsvPath.Size = new System.Drawing.Size(245, 21);
            this.txtCsvPath.TabIndex = 40;
            // 
            // checkEditSampleData
            // 
            this.checkEditSampleData.Location = new System.Drawing.Point(13, 85);
            this.checkEditSampleData.Name = "checkEditSampleData";
            this.checkEditSampleData.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEditSampleData.Properties.Appearance.Options.UseFont = true;
            this.checkEditSampleData.Properties.Caption = "导出数据";
            this.checkEditSampleData.Size = new System.Drawing.Size(78, 19);
            this.checkEditSampleData.TabIndex = 39;
            this.checkEditSampleData.CheckedChanged += new System.EventHandler(this.checkEditSampleData_CheckedChanged);
            // 
            // chkTwoEarfcn
            // 
            this.chkTwoEarfcn.Location = new System.Drawing.Point(13, 21);
            this.chkTwoEarfcn.Name = "chkTwoEarfcn";
            this.chkTwoEarfcn.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkTwoEarfcn.Properties.Appearance.Options.UseFont = true;
            this.chkTwoEarfcn.Properties.Caption = "多层网剔除异频";
            this.chkTwoEarfcn.Size = new System.Drawing.Size(115, 19);
            this.chkTwoEarfcn.TabIndex = 3;
            this.chkTwoEarfcn.CheckedChanged += new System.EventHandler(this.chkTwoEarfcn_CheckedChanged);
            // 
            // cbxFreqType
            // 
            this.cbxFreqType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxFreqType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxFreqType.FormattingEnabled = true;
            this.cbxFreqType.Location = new System.Drawing.Point(212, 20);
            this.cbxFreqType.Name = "cbxFreqType";
            this.cbxFreqType.Size = new System.Drawing.Size(143, 20);
            this.cbxFreqType.TabIndex = 18;
            // 
            // label7
            // 
            this.label7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(147, 24);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(59, 12);
            this.label7.TabIndex = 17;
            this.label7.Text = "频段设置:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(391, 104);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 21;
            this.label8.Text = "dBm";
            // 
            // numOptionalRsrp
            // 
            this.numOptionalRsrp.Enabled = false;
            this.numOptionalRsrp.Location = new System.Drawing.Point(265, 100);
            this.numOptionalRsrp.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOptionalRsrp.Minimum = new decimal(new int[] {
            10000,
            0,
            0,
            -2147483648});
            this.numOptionalRsrp.Name = "numOptionalRsrp";
            this.numOptionalRsrp.Size = new System.Drawing.Size(120, 21);
            this.numOptionalRsrp.TabIndex = 20;
            this.numOptionalRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOptionalRsrp.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // chkOptionalRsrp
            // 
            this.chkOptionalRsrp.AutoSize = true;
            this.chkOptionalRsrp.Location = new System.Drawing.Point(175, 103);
            this.chkOptionalRsrp.Name = "chkOptionalRsrp";
            this.chkOptionalRsrp.Size = new System.Drawing.Size(84, 16);
            this.chkOptionalRsrp.TabIndex = 19;
            this.chkOptionalRsrp.Text = "信号强度≥";
            this.chkOptionalRsrp.UseVisualStyleBackColor = true;
            // 
            // numGridCount
            // 
            this.numGridCount.Location = new System.Drawing.Point(265, 175);
            this.numGridCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numGridCount.Name = "numGridCount";
            this.numGridCount.Size = new System.Drawing.Size(120, 21);
            this.numGridCount.TabIndex = 16;
            this.numGridCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(170, 179);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 15;
            this.label6.Text = "连续栅格个数≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(391, 65);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 14;
            this.label5.Text = "dB";
            // 
            // numRsrpDiff
            // 
            this.numRsrpDiff.Location = new System.Drawing.Point(265, 63);
            this.numRsrpDiff.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRsrpDiff.Name = "numRsrpDiff";
            this.numRsrpDiff.Size = new System.Drawing.Size(120, 21);
            this.numRsrpDiff.TabIndex = 13;
            this.numRsrpDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(183, 66);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 12;
            this.label3.Text = "相对覆盖带≤";
            // 
            // numCoverage
            // 
            this.numCoverage.Location = new System.Drawing.Point(265, 135);
            this.numCoverage.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCoverage.Name = "numCoverage";
            this.numCoverage.Size = new System.Drawing.Size(120, 21);
            this.numCoverage.TabIndex = 11;
            this.numCoverage.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCoverage.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(207, 139);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 10;
            this.label4.Text = "覆盖度≥";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(391, 28);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 9;
            this.label2.Text = "dBm";
            // 
            // numRsrpMin
            // 
            this.numRsrpMin.Location = new System.Drawing.Point(265, 25);
            this.numRsrpMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpMin.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRsrpMin.Name = "numRsrpMin";
            this.numRsrpMin.Size = new System.Drawing.Size(120, 21);
            this.numRsrpMin.TabIndex = 8;
            this.numRsrpMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpMin.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(195, 28);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 7;
            this.label1.Text = "最强信号≥";
            // 
            // LteMgrsHighCoverageSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "LteMgrsHighCoverageSetting";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridNum)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkFBandType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditSampleData.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkTwoEarfcn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numCoverage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRsrpMin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numGridCount;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numRsrpDiff;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cbxFreqType;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox chkOptionalRsrp;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numOptionalRsrp;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.CheckEdit chkTwoEarfcn;
        private DevExpress.XtraEditors.CheckEdit checkEditSampleData;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numGridNum;
        private System.Windows.Forms.CheckBox chkGridNum;
        private System.Windows.Forms.Button btnFile;
        private System.Windows.Forms.TextBox txtCsvPath;
        private DevExpress.XtraEditors.CheckEdit chkFBandType;
    }
}
