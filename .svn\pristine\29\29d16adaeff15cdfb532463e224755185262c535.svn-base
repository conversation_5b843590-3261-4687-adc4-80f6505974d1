﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PesudoBaseStationInfoShowForm : MinCloseForm
    {
        private List<PesudoBaseStationInfo> pesuInfoList;

        public PesudoBaseStationInfoShowForm():base()
        {
            InitializeComponent();
        }

        public void FillData(List<PesudoBaseStationInfo> pesuInfoList)
        {
            this.pesuInfoList = pesuInfoList;
            gcPesudoBS.DataSource = pesuInfoList;
            gcPesudoBS.RefreshDataSource();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("名称");
                dataTable.Columns.Add("LAC");
                dataTable.Columns.Add("CI");
                dataTable.Columns.Add("经度");
                dataTable.Columns.Add("纬度");
                dataTable.Columns.Add("场强");
                dataTable.Columns.Add("道路名称");
                dataTable.Columns.Add("时间");
                foreach (PesudoBaseStationInfo pesuInfo in pesuInfoList)
                {
                    List<object> pesuOList = new List<object>();
                    pesuOList.Add(pesuInfo.Name);
                    pesuOList.Add(pesuInfo.LAC);
                    pesuOList.Add(pesuInfo.CI);
                    dataTable.Rows.Add(pesuOList.ToArray());
                    foreach (PesudoTestPoint psdTpInfo in pesuInfo.PsdTpList)
                    {
                        List<object> psdTpOList = new List<object>();
                        addDefaultValue(psdTpOList, 3);
                        psdTpOList.Add(psdTpInfo.Longitude);
                        psdTpOList.Add(psdTpInfo.Latitude);
                        psdTpOList.Add(psdTpInfo.Rxlev);
                        psdTpOList.Add(psdTpInfo.RoadName);
                        psdTpOList.Add(psdTpInfo.StrDateTime);
                        dataTable.Rows.Add(psdTpOList.ToArray());
                    }
                }
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(dataTable);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private static void addDefaultValue(List<object> psdTpOList, int count)
        {
            for (int i = 0; i < count; i++)
            {
                psdTpOList.Add("");
            }
        }

        private void gvPesudoBS_DoubleClick(object sender, EventArgs e)
        {
            MainModel.DTDataManager.Clear();
            int[] hRow = gvPesudoBS.GetSelectedRows();
            if (hRow.Length <= 0)
            {
                return;
            }
            object obj = gvPesudoBS.GetRow(hRow[0]);
            if (obj == null)
            {
                return;
            }
            PesudoBaseStationInfo info = obj as PesudoBaseStationInfo;
            foreach (PesudoTestPoint psdtp in info.PsdTpList)
            {
                MainModel.DTDataManager.Add(psdtp.tp);
            }
            MainModel.FireDTDataChanged(this);
            MainModel.MainForm.GetMapForm().GoToView(info.PsdTpList[0].Longitude, info.PsdTpList[0].Latitude, 500);
        }

        private void gvPsdTP_DoubleClick(object sender, EventArgs e)
        {
            MainModel.DTDataManager.Clear();
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            int[] hRow = gv.GetSelectedRows();
            if (hRow.Length <= 0)
            {
                return;
            }
            object obj = gv.GetRow(hRow[0]);
            if (obj == null)
            {
                return;
            }
            PesudoTestPoint info = obj as PesudoTestPoint;
            MainModel.DTDataManager.Add(info.tp);
            MainModel.MainForm.GetMapForm().GoToView(info.Longitude, info.Latitude, 500);
        }
    }
}
