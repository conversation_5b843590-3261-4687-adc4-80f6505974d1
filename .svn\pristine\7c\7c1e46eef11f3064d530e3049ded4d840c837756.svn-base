﻿using MasterCom.RAMS.NewBlackBlock;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public partial class CellFusionDataFormSettingBox : BaseDialog
    {
        List<string> alarmColumn_Fixed = new List<string> { "小区", "TAC", "ECI", "基站号", "基站名", "告警描述", "日期" };
        List<string> perfColumn_Fixed = new List<string> { "小区", "TAC", "ECI", "日期" };
        List<string> argColumn_Fixed = new List<string> { "小区", "TAC", "ECI", "日期" };

        public CellFusionDataFormSettingBox()
            : base()
        {
            InitializeComponent();
            initTreeView();
            TreeViewCheckHelper.AutoUpdateCheckState(treeViewFusion);
        }
        private void initTreeView()
        {
            treeViewFusion.Nodes.Clear();
            FusionColumnCfgManager cfg = FusionColumnCfgManager.Instance;
            foreach (FusionDataColumnCollection columnCollection in cfg.ColumnCollections.Values)
            {
                TreeNode node = new TreeNode();
                node.Text = columnCollection.Name;
                node.Tag = columnCollection;
                node.Checked = false;
                treeViewFusion.Nodes.Add(node);
                foreach (FusionDataColumn funsionColumn in columnCollection.FusionDataColumns)
                {
                    TreeNode childNode = new TreeNode();
                    childNode.Text = funsionColumn.ColumnName;
                    childNode.Checked = funsionColumn.IsCheck;
                    childNode.Tag = funsionColumn;
                    node.Nodes.Add(childNode);
                    if (childNode.Checked && !node.Checked)
                    {
                        node.Checked = true;
                    }
                }
            }
        }

        private void applySetting()
        {
            foreach (TreeNode node in treeViewFusion.Nodes)
            {
                foreach (TreeNode childNode in node.Nodes)
                {
                    FusionDataColumn funsionColumn = childNode.Tag as FusionDataColumn;
                    if (funsionColumn != null)
                    {
                        funsionColumn.IsCheck = childNode.Checked;
                    }
                }
            }
        }
        private void btnOk_Click(object sender, EventArgs e)
        {
            applySetting();
            FusionColumnCfgManager.Instance.Save();
            this.DialogResult = DialogResult.OK;
        }

        private void ToolStripMenuEditKpi_Click(object sender, EventArgs e)
        {
            if (treeViewFusion.SelectedNode != null && treeViewFusion.SelectedNode.Tag is FusionDataColumn)
            {
                FusionDataColumn funsionColumn = treeViewFusion.SelectedNode.Tag as FusionDataColumn;
                TreeNode parentNode = treeViewFusion.SelectedNode.Parent;
                if (parentNode != null && parentNode.Tag is FusionDataColumnCollection)
                {
                    FusionDataColumnCollection columnCollection = parentNode.Tag as FusionDataColumnCollection;

                    CellFusionKpiEditSettingBox addKpiSettingBox = new CellFusionKpiEditSettingBox();
                    addKpiSettingBox.SetCond(funsionColumn);
                    if (addKpiSettingBox.ShowDialog() == DialogResult.OK)
                    {
                        FusionDataColumn funsionColumn_New = addKpiSettingBox.GetCond();
                        if (funsionColumn_New.ColumnName == funsionColumn.ColumnName
                            || (isValidCol(funsionColumn_New, columnCollection)))
                        {
                            funsionColumn.Param = funsionColumn_New.Param;
                            initTreeView();
                        }
                    }
                }
            }
            else
            {
                MessageBox.Show("请选择一个子节点！");
            }
        }

        private void ToolStripMenuAddKpi_Click(object sender, EventArgs e)
        {
            if (treeViewFusion.SelectedNode != null)
            {
                if (treeViewFusion.SelectedNode.Tag is FusionDataColumnCollection)
                {
                    FusionDataColumnCollection columnCollection = treeViewFusion.SelectedNode.Tag as FusionDataColumnCollection;
                    showAddKpiSettingForm(columnCollection);
                }
                else if (treeViewFusion.SelectedNode.Tag is FusionDataColumn)
                {
                    TreeNode parentNode = treeViewFusion.SelectedNode.Parent;
                    if (parentNode != null && parentNode.Tag is FusionDataColumnCollection)
                    {
                        FusionDataColumnCollection columnCollection = parentNode.Tag as FusionDataColumnCollection;
                        showAddKpiSettingForm(columnCollection);
                    }
                }
                else
                {
                    MessageBox.Show("请先选择父节点！");
                }
            }
            else
            {
                MessageBox.Show("请先选择父节点！");
            }
        }

        private void showAddKpiSettingForm(FusionDataColumnCollection columnCollection)
        {
            CellFusionKpiEditSettingBox addKpiSettingBox = new CellFusionKpiEditSettingBox();
            if (addKpiSettingBox.ShowDialog() == DialogResult.OK)
            {
                FusionDataColumn fusionColumn_New = addKpiSettingBox.GetCond();
                if (isValidCol(fusionColumn_New, columnCollection))
                {
                    columnCollection.FusionDataColumns.Add(fusionColumn_New);
                    initTreeView();
                }
                else
                {
                    MessageBox.Show("已含有相同名称的列，请重新设置！");
                }
            }
        }

        private bool isValidCol(FusionDataColumn fusionColumn_New, FusionDataColumnCollection columnCollection)
        {
            bool hasSameName = false;
            if (columnCollection.Name == FusionDataType.Alarm)
            {
                hasSameName = alarmColumn_Fixed.Contains(fusionColumn_New.ColumnName);
            }
            else if (columnCollection.Name == FusionDataType.Perf)
            {
                hasSameName = perfColumn_Fixed.Contains(fusionColumn_New.ColumnName);
            }
            else if (columnCollection.Name == FusionDataType.Arg)
            {
                hasSameName = argColumn_Fixed.Contains(fusionColumn_New.ColumnName);
            }
            
            if (hasSameName)
            {
                return false;
            }

            foreach (FusionDataColumn fusionCol in columnCollection.FusionDataColumns)
            {
                if (fusionColumn_New.ColumnName == fusionCol.ColumnName)
                {
                    hasSameName = true;
                    break;
                }
            }
            return !hasSameName;
        }

        private void ToolStripMenuDelKpi_Click(object sender, EventArgs e)
        {
            if (treeViewFusion.SelectedNode != null && treeViewFusion.SelectedNode.Tag is FusionDataColumn)
            {
                FusionDataColumn funsionColumn = treeViewFusion.SelectedNode.Tag as FusionDataColumn;

                TreeNode parentNode = treeViewFusion.SelectedNode.Parent;
                if (parentNode != null && parentNode.Tag is FusionDataColumnCollection)
                {
                    FusionDataColumnCollection columnCollection = parentNode.Tag as FusionDataColumnCollection;
                    columnCollection.FusionDataColumns.Remove(funsionColumn);
                }

                treeViewFusion.SelectedNode.Remove();
            }
            else
            {
                MessageBox.Show("请选择一个子节点！");
            }
        }

        private void ToolStripMenuReset_Click(object sender, EventArgs e)
        {
            FusionColumnCfgManager.Instance.InitColumnCollections();
            initTreeView();
        }
    }
    public class FusionColumnCfgManager
    {
        private readonly string nopServerCfgName = Application.StartupPath + "\\config\\fusionColumnSetting.xml";
        private static FusionColumnCfgManager instance = null;
        public static FusionColumnCfgManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FusionColumnCfgManager();
                }
                return instance;
            }
        }

        private FusionColumnCfgManager()
        {
            load();
        }
        
        public Dictionary<string, FusionDataColumnCollection> ColumnCollections { get; set; } = new Dictionary<string, FusionDataColumnCollection>();

        public void InitColumnCollections()
        {
            ColumnCollections.Clear();

            FusionDataColumnCollection alarmCollection = new FusionDataColumnCollection(FusionDataType.Alarm);
            alarmCollection.AddFusionDataColumns(CellAlarmData.KpiKey_NameDic);
            ColumnCollections.Add(alarmCollection.Name, alarmCollection);

            FusionDataColumnCollection perfCollection = new FusionDataColumnCollection(FusionDataType.Perf);
            perfCollection.AddFusionDataColumns(CellPerfData.KpiKey_NameDic);
            ColumnCollections.Add(perfCollection.Name, perfCollection);

            FusionDataColumnCollection argCollection = new FusionDataColumnCollection(FusionDataType.Arg);
            argCollection.AddFusionDataColumns(CellArgData.KpiKey_NameDic);
            ColumnCollections.Add(argCollection.Name, argCollection);
        }

        private void load()
        {
            ColumnCollections.Clear();

            if (File.Exists(nopServerCfgName))
            {
                XmlConfigFile configFile = new XmlConfigFile(nopServerCfgName);
                Dictionary<string, Object> paramDic = configFile.GetItemValue("FusionColumns", "ColumnType") as Dictionary<string, Object>;
                if (paramDic != null)
                {
                    foreach (Object paramObj in paramDic.Values)
                    {
                        Dictionary<string, object> param = paramObj as Dictionary<string, object>;
                        if (param != null)
                        {
                            FusionDataColumnCollection colCollection = new FusionDataColumnCollection();
                            colCollection.Param = param;
                            ColumnCollections.Add(colCollection.Name, colCollection);
                        }
                    }
                }
            }
            if (ColumnCollections.Count <= 0)
            {
                InitColumnCollections();
            }
        }

        public void Save()
        {
            Dictionary<string, object> columnCollectionParamDic = new Dictionary<string, object>();
            foreach (FusionDataColumnCollection columnCollection in ColumnCollections.Values)
            {
                columnCollectionParamDic.Add(columnCollection.Name, columnCollection.Param);
            }

            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("FusionColumns");
            configFile.AddItem(config, "ColumnType", columnCollectionParamDic);
            configFile.Save(nopServerCfgName);
        }
    }

    public class FusionDataColumnCollection
    {
        public FusionDataColumnCollection()
        {
        }
        public FusionDataColumnCollection(string name)
            : this()
        {
            this.Name = name;
        }
        public string Name { get; set; }
        
        public List<FusionDataColumn> FusionDataColumns { get; set; } = new List<FusionDataColumn>();

        public void AddFusionDataColumns(Dictionary<int, string> kpiKey_NameDic)
        {
            if (kpiKey_NameDic == null)
            {
                return;
            }

            foreach (var key_nameVar in kpiKey_NameDic)
            {
                FusionDataColumn fusionColumn = new FusionDataColumn();
                fusionColumn.ColumnId = key_nameVar.Key;
                fusionColumn.ColumnName = key_nameVar.Value;
                FusionDataColumns.Add(fusionColumn);
            }
        }


        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> retDic = new Dictionary<string, object>();
                retDic["Name"] = Name;
                retDic["FusionDataColumns"] = getColumnsParams();
                return retDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                object obj = null;
                if (value.TryGetValue("Name", out obj) && obj != null)
                {
                    this.Name = obj.ToString();
                }
                if (value.TryGetValue("FusionDataColumns", out obj) && obj != null)
                {
                    FusionDataColumns.Clear();
                    Dictionary<string, object> param = obj as Dictionary<string, object>;
                    if (param != null)
                    {
                        foreach (object columnObj in param.Values)
                        {
                            Dictionary<string, object> columnParam = columnObj as Dictionary<string, object>;
                            if (columnParam != null)
                            {
                                FusionDataColumn column = new FusionDataColumn();
                                column.Param = columnParam;
                                FusionDataColumns.Add(column);
                            }
                        }
                    }
                }
            }
        }

        private Dictionary<string, object> getColumnsParams()
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            foreach (FusionDataColumn fsColumn in FusionDataColumns)
            {
                param[fsColumn.ColumnId.ToString()] = fsColumn.Param;
            }
            return param; 
        }
    }
    public class FusionDataColumn
    {
        public string ColumnName { get; set; }
        public int ColumnId { get; set; }
        public bool IsCheck { get; set; }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> retDic = new Dictionary<string, object>();
                retDic["ColumnName"] = ColumnName;
                retDic["ColumnId"] = ColumnId;
                retDic["IsCheck"] = IsCheck;
                return retDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                object obj = null;
                if (value.TryGetValue("ColumnName", out obj) && obj != null)
                {
                    this.ColumnName = obj.ToString();
                }
                if (value.TryGetValue("ColumnId", out obj) && obj != null)
                {
                    this.ColumnId = (int)obj;
                }
                if (value.TryGetValue("IsCheck", out obj) && obj != null)
                {
                    this.IsCheck = (bool)obj;
                }
            }
        }
    }

    public struct FusionDataType
    {
        /// <summary>
        /// 告警
        /// </summary>
        public static readonly string Alarm = "告警";

        /// <summary>
        /// 性能
        /// </summary>
        public static readonly string Perf = "性能";

        /// <summary>
        /// 参数
        /// </summary>
        public static readonly string Arg = "参数";
    }
}
