﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLogSearch
{
    public partial class SettingExportForm : MinCloseForm
    {
        private List<string> listFunc = new List<string>();
        public SettingExportForm(List<string> list)
        {
            InitializeComponent();

            this.checkedListBoxFunc.Items.Clear();
            foreach (string s in list)
            {
                this.checkedListBoxFunc.Items.Add(s);
            }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.listFunc = new List<string>();
            foreach (string str in this.checkedListBoxFunc.CheckedItems)
            {
                this.listFunc.Add(str);
            }
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
        public List<string> GetResult()
        {
            return this.listFunc;
        }

        private void ToolStripMenuSelectAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < checkedListBoxFunc.Items.Count; i++)
            {
                checkedListBoxFunc.SetItemChecked(i, true);
            }
        }
    }
}
