﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMainCellLastOccupyByRegion : ZTMainCellLastOccupyBase
    {

        public ZTMainCellLastOccupyByRegion(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "LTE主服占用时长(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22098, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTETestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }

    public class ZTMainCellLastOccupyByRegion_GSM : ZTMainCellLastOccupyByRegion
    {

        public ZTMainCellLastOccupyByRegion_GSM(MainModel mainModel)
            : base(mainModel)
        {

        }
        ZTMainCellLastOccupySetGSMConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetGSMConditionForm();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                resultList = new List<CellLastOccupyFileInfo>();
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12095, this.Name);
        }

        public override string Name
        {
            get
            {
                return "GSM主服占用时长(按区域)";
            }
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is TestPointDetail)
            {
                return true;
            }
            else
                return false;
        }
    }

    public class ZTMainCellLastOccupyByRegion_FDD : ZTMainCellLastOccupyBase_FDD
    {
        public ZTMainCellLastOccupyByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE_FDD主服占用时长(按区域)"; }
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                return true;
            }
            else
                return false;
        }
    }
}
