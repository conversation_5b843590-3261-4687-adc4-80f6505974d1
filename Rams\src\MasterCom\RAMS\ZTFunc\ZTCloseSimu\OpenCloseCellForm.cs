using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class OpenCloseCellForm : MasterCom.RAMS.Func.MinCloseForm
    {

        List<Cell> cells = null;

        List<Cell> foundCells = new List<Cell>();

        List<Cell> closedCells = new List<Cell>();

        public OpenCloseCellForm():
            base()
        {
            InitializeComponent();
            if (MapCellLayer.DrawCurrent)
            {
                cells = mModel.CellManager.GetCurrentCells();
            }
            else
            {
                cells = mModel.CellManager.GetCells(MapCellLayer.CurShowTimeAt);
            }
            cells.Sort(Cell.GetCompareByName());
            init();
        }

        public void init()
        {
            listBoxClosedCells.BeginUpdate();
            listBoxClosedCells.DataSource = null;
            listBoxClosedCells.DataSource = mModel.ClosedCells;
            listBoxClosedCells.DisplayMember = "Name";
            listBoxClosedCells.EndUpdate();
        }

        private void btnFind_Click(object sender, EventArgs e)
        {
            findCell();   
        }

        private void findCell()
        {
            foundCells.Clear();
            foreach (Cell cell in cells)
            {
                if (cell.Name.ToUpper().Contains(tbxCellName.Text.Trim().ToUpper()))
                {
                    foundCells.Add(cell);
                }
            }
            listBoxFoundCells.BeginUpdate();
            listBoxFoundCells.DataSource = null;
            listBoxFoundCells.DataSource = foundCells;
            listBoxFoundCells.DisplayMember = "Name";
            listBoxFoundCells.EndUpdate();
        }

        private void btnAddSel_Click(object sender, EventArgs e)
        {
            if (listBoxFoundCells.SelectedItems != null)
            {
                foreach (object cell in listBoxFoundCells.SelectedItems)
                {
                    if (!closedCells.Contains((Cell)cell))
                    {
                        closedCells.Add((Cell)cell);
                    }
                }
                listBoxClosedCells.BeginUpdate();
                listBoxClosedCells.DataSource = null;
                listBoxClosedCells.DataSource = closedCells;
                listBoxClosedCells.DisplayMember = "Name";
                listBoxClosedCells.EndUpdate();
            }
        }

        protected override string ShowImage
        {
            get
            {
                return "images\\dbmng.gif";
            }
        }

        private void btnDel_Click(object sender, EventArgs e)
        {
            if (listBoxClosedCells.SelectedItems != null && listBoxClosedCells.SelectedItems.Count > 0)
            {
                foreach (object cell in listBoxClosedCells.SelectedItems)
                {
                    closedCells.Remove((Cell)cell);
                }
                listBoxClosedCells.BeginUpdate();
                listBoxClosedCells.DataSource = null;
                listBoxClosedCells.DataSource = closedCells;
                listBoxClosedCells.DisplayMember = "Name";
                listBoxClosedCells.EndUpdate();
            }
        }

        public List<int> ClosedCellIDs
        {
            get
            {
                List<int> list = new List<int>();
                foreach (Cell cell in closedCells)
                {
                    list.Add(cell.ID);
                }
                return list;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            mModel.ClosedCellIDs = ClosedCellIDs;
            mModel.ClosedCells = closedCells;
        }

        public void clearData()
        {
            closedCells.Clear();
            ClosedCellIDs.Clear();
            listBoxClosedCells.BeginUpdate();
            listBoxClosedCells.DataSource = null;
            listBoxClosedCells.EndUpdate();
            this.Visible = false;
        }
    }
}