﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class AnaByRulesOtherSetDlg : BaseDialog
    {
        StationAcceptAutoSet funcSet;
        public AnaByRulesOtherSetDlg()
        {
            InitializeComponent();
            this.funcSet = StationAcceptAna_HB.GetInstance().FuncSet;
            txtExcelAutoPath.Text = funcSet.FileCountAutoULFolder;
            chkFileCountCheck.Checked = funcSet.FileCountAutoIsCheck;
            cmbOutAndIndoor.SelectedIndex = 0;
            numAutoDuration.Value = funcSet.AutoAnaDurationMinutes;
        }

        private void btnExcelAutoPath_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderDialog = new FolderBrowserDialog();
            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                funcSet.FileCountAutoULFolder = folderDialog.SelectedPath;
                txtExcelAutoPath.Text = folderDialog.SelectedPath;
            }
        }

        private void btnBeginUpLoad_Click(object sender, EventArgs e)
        {
            funcSet.FileCountAutoIsCheck = chkFileCountCheck.Checked;
            funcSet.FileCountAutoULFolder = txtExcelAutoPath.Text;
            if (string.IsNullOrEmpty(txtExcelAutoPath.Text))
            {
                XtraMessageBox.Show("请先选择文件夹！");
                return;
            }
            if (!StationFilesCountManager.AddBtsFileCountToDBAuto())
            {
                XtraMessageBox.Show("未找到相关文件！");
            }
        }

        private void chkFileCountCheck_CheckedChanged(object sender, EventArgs e)
        {
            panelAutoUL.Enabled = chkFileCountCheck.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            funcSet.AutoAnaDurationMinutes = (int)numAutoDuration.Value;
            funcSet.FileCountAutoIsCheck = chkFileCountCheck.Checked;
            funcSet.FileCountAutoULFolder = txtExcelAutoPath.Text;
            this.DialogResult = DialogResult.OK;
        }

        private void cmbOutAndIndoor_SelectedIndexChanged(object sender, EventArgs e)
        {
            CallKpiCheck kpiCheck = new CallKpiCheck();
            if (cmbOutAndIndoor.SelectedIndex == 0)
            {
                chkLeakOutRatioOrOtherSet.Text = "室外站RSRP弱覆盖、下载传低速率、SINR质差占比";
                chkInAndOutSrc.Visible = false;
                kpiCheck = funcSet.OutDoorCallCheck;
                chkLeakOutRatioOrOtherSet.Checked = kpiCheck.IsCheckOutDoorOtherSet;
            }
            else if (cmbOutAndIndoor.SelectedIndex == 1)
            {
                chkLeakOutRatioOrOtherSet.Text = "外泄受控比例";
                chkInAndOutSrc.Visible = true;
                kpiCheck = funcSet.InDoorCallCheck;
                chkLeakOutRatioOrOtherSet.Checked = kpiCheck.IsCheckLeakOutRatio;
            }
            chkHandOver.Checked = kpiCheck.IsCheckHandOver;
            chkCSFB.Checked = kpiCheck.IsCheckCsfb;
            chkVolteVoiceAllCall.Checked = kpiCheck.IsCheckVolteVoiceAllCall;
            chkVoLteVoiceMoCall.Checked = kpiCheck.IsCheckVolteVoiceMoCall;
            chkVolteVideoAllCall.Checked = kpiCheck.IsCheckVolteVideoAllCall;
            chkVoLteVideoMoCall.Checked = kpiCheck.IsCheckVolteVideoMoCall;
            chkSrvcc.Checked = kpiCheck.IsCheckSRVCCCall;
            chkInAndOutSrc.Checked = kpiCheck.IsCheckInAndOutSrc;
        }
        private void btnApply_Click(object sender, EventArgs e)
        {
            CallKpiCheck kpiCheck = new CallKpiCheck();
            if (cmbOutAndIndoor.SelectedIndex == 0)
            {
                kpiCheck = funcSet.OutDoorCallCheck;
                kpiCheck.IsCheckOutDoorOtherSet = chkLeakOutRatioOrOtherSet.Checked;
            }
            else if (cmbOutAndIndoor.SelectedIndex == 1)
            {
                kpiCheck = funcSet.InDoorCallCheck;
                kpiCheck.IsCheckLeakOutRatio = chkLeakOutRatioOrOtherSet.Checked;
            }
            kpiCheck.IsCheckHandOver = chkHandOver.Checked;
            kpiCheck.IsCheckCsfb = chkCSFB.Checked;
            kpiCheck.IsCheckVolteVoiceAllCall = chkVolteVoiceAllCall.Checked;
            kpiCheck.IsCheckVolteVoiceMoCall = chkVoLteVoiceMoCall.Checked;
            kpiCheck.IsCheckVolteVideoAllCall = chkVolteVideoAllCall.Checked;
            kpiCheck.IsCheckVolteVideoMoCall = chkVoLteVideoMoCall.Checked;
            kpiCheck.IsCheckSRVCCCall = chkSrvcc.Checked;
            kpiCheck.IsCheckInAndOutSrc = chkInAndOutSrc.Checked;
        }
    }
}
