﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.ExportTestPoint
{
    public class ColumnOptions
    {
        public string Caption { get; set; } = "未命名";

        public override string ToString()
        {
            return Caption;
        }
        public DTDisplayParameterInfo DisplayParam
        {
            get;
            set;
        }
        public int ParamArrayIndex
        {
            get;
            set;
        }
        
        public bool CheckValue { get; set; } = false;

        public bool IsIgnoreWhenNotInRange
        {
            get;
            set;
        }
        
        public double MinValue { get; set; }
        public double MaxValue { get; set; }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["Caption"] = this.Caption;
                paramDic["SysName"] = this.DisplayParam.System.Name;
                paramDic["ParamName"] = this.DisplayParam.Name;
                paramDic["ParamArrayIndex"] = this.ParamArrayIndex;
                paramDic["CheckValue"] = this.CheckValue;
                paramDic["MinValue"] = this.MinValue;
                paramDic["MaxValue"] = this.MaxValue;
                paramDic["IsIgnoreWhenNotInRange"] = this.IsIgnoreWhenNotInRange;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Caption = value["Caption"].ToString();
                string sysName = value["SysName"].ToString();
                string paramName = value["ParamName"].ToString();
                this.DisplayParam = DTDisplayParameterManager.GetInstance()[sysName][paramName];
                this.ParamArrayIndex = (int)value["ParamArrayIndex"];
                this.CheckValue = (bool)value["CheckValue"];
                this.MinValue = (double)value["MinValue"];
                this.MaxValue = (double)value["MaxValue"];
                if (value.ContainsKey("IsIgnoreWhenNotInRange"))
                {
                    this.IsIgnoreWhenNotInRange = (bool)value["IsIgnoreWhenNotInRange"];
                }
            }
        }

        public string ParamKey
        {
            get
            {
                return string.Format("{0}[{1}]", DisplayParam.ParamInfo.Name,
                 ParamArrayIndex);
            }
        }
    }
}
