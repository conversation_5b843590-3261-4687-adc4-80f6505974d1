﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MapWinGIS;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class GDDataPushDetailsQuery : DIYStatQuery
    {
        public GDDataPushDetailsQuery(MainModel mainModel)
            : base(mainModel)
        { 
        }

        public override string Name
        {
            get { return "数据推送跟踪"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22083, this.Name);
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }

        GDDataPushDetailsSetForm dataPushSetForm = null;
        List<GDDataPushDetailsInfo> gdDataPushDetailsInfoList = null;
        protected override void query()
        {
            if (dataPushSetForm == null)
            {
                dataPushSetForm = new GDDataPushDetailsSetForm();
            }
            if (dataPushSetForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            try
            {
                WaitBox.Show("正常查询数据推送信息，请稍后.....", getDataPustInfo);
                fireShowResult(gdDataPushDetailsInfoList);
            }
            catch
            {	
                //continue
            }
            finally
            {
                gdDataPushDetailsInfoList = null;
            }
        }

        private void getDataPustInfo()
        {
            WaitBox.ProgressPercent = 65;
            DIYQueryFilePush diyQueryFilePush = new DIYQueryFilePush(MainModel, dataPushSetForm.StrSQL
                , dataPushSetForm.isQueryDownload);
            diyQueryFilePush.SetQueryCondition(condition);
            diyQueryFilePush.Query();
            this.gdDataPushDetailsInfoList = diyQueryFilePush.gdDataPushDetailsInfoList;
            WaitBox.Close();
        }

        protected void fireShowResult(List<GDDataPushDetailsInfo> gdDataPushDetailsInfoList)
        {
            if (gdDataPushDetailsInfoList.Count == 0)
            {
                MessageBox.Show("该条件下并未结果，请重新设置查询！");
                return;
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(GDDataPushDetailsForm).FullName);
            GDDataPushDetailsForm form = obj == null ? null : obj as GDDataPushDetailsForm;
            if (form == null || form.IsDisposed)
            {
                form = new GDDataPushDetailsForm(MainModel);
            }
            form.FillData(gdDataPushDetailsInfoList);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
    public class DIYQueryFilePush : DIYSQLBase
    {
        public DIYQueryFilePush(MainModel mainModel,string strSql,bool query)
            : base(mainModel)
        {
            MainDB = true;
            this.strSQL = strSql;
            this.isQuery = query;
        }
        readonly string strSQL;
        readonly bool isQuery;
        public override string Name
        {
            get
            {
                return "查询数据推送跟踪表";
            }
        }

        protected override string getSqlTextString()
        {
            string sql = "select SourceId,pd.FileName,RemotePath,SourceFile,str(FileSize) AS filesizen,UploadTime,isnull(DeviceGuid,'') AS DeviceGuid,"
                       + "isnull(DeviceType,-1) AS DeviceType ,isnull(DeviceName,'') AS DeviceName,PushStartTime "
                       + ",PushDoneTime ,PushTry,[Status],InsertTime,Message,ServiceId ,";
            string strSQLTmp = sql + "'' as strDownloadStatStus from [DINGLISERVER].[Amount].[dbo].[push_datas_19] AS pd" + this.strSQL;
            if (this.isQuery)
            {
                strSQLTmp = sql + "isnull(dl.strDownloadStatStus,'未下载') as strDownloadStatStus  from [DINGLISERVER].[Amount].[dbo].[push_datas_19]  AS pd "
                       + " LEFT JOIN mtserver.dtasystem.dbo.tb_dinglifile_download_process_log AS dl "
                       + " ON pd.DeviceName + '_'+ pd.DeviceGuid + pd.FILENAME = dl.[filename] " + this.strSQL;
            }
            return strSQLTmp;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[17];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        public List<GDDataPushDetailsInfo> gdDataPushDetailsInfoList { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            gdDataPushDetailsInfoList = new List<GDDataPushDetailsInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    GDDataPushDetailsInfo gdDataPushDetailsInfo = new GDDataPushDetailsInfo();
                    gdDataPushDetailsInfo.序号 = gdDataPushDetailsInfoList.Count + 1;
                    gdDataPushDetailsInfo.数据编号 = package.Content.GetParamInt();
                    gdDataPushDetailsInfo.数据名称 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.数据FTP路径 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.数据监测平台路径 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.数据大小 = int.Parse(package.Content.GetParamString());
                    gdDataPushDetailsInfo.数据完整到达平台时间 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.设备ID = package.Content.GetParamString();
                    gdDataPushDetailsInfo.设备类型ID = package.Content.GetParamInt();
                    gdDataPushDetailsInfo.设备名称 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.开始推送时间 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.结束推送时间 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.推送次数 = package.Content.GetParamInt();
                    gdDataPushDetailsInfo.推送状态值 = package.Content.GetParamInt();
                    gdDataPushDetailsInfo.数据入记录表时间 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.失败错误信息 = package.Content.GetParamString();
                    gdDataPushDetailsInfo.I来源服务器ID = package.Content.GetParamInt();
                    gdDataPushDetailsInfo.下载状态 = package.Content.GetParamString();
                    gdDataPushDetailsInfoList.Add(gdDataPushDetailsInfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class GDDataPushDetailsInfo
    {
        public int 序号 { get; set; }
        public int 数据编号 { get; set; }
        public string 数据名称 { get; set; }
        public string 数据FTP路径 { get; set; }
        public string 数据监测平台路径 { get; set; }
        public int 数据大小 { get; set; }
        public int I来源服务器ID { get; set; }
        public string 数据来源服务器
        {
            get
            {
                string strService = "";
                switch (I来源服务器ID)
                {
                    case 1:
                        strService = "120.198.253.67";
                        break;
                    case 4:
                        strService = "120.198.253.19";
                        break;
                    case 39:
                        strService = "120.198.253.92";
                        break;
                    default:
                        return strService;
                }
                return strService;
            }
        }
        public string 设备ID { get; set; }
        public string 设备名称 { get; set; }
        public int 设备类型ID { get; set; }
        public string 设备类型
        {
            get
            {
                string strStatus = "";
                switch (设备类型ID)
                {
                    case 0:
                        strStatus = "RCU";
                        break;
                    case 1:
                        strStatus = "ATU";
                        break;
                    case 2:
                        strStatus = "Walktour";
                        break;
                    case 3:
                        strStatus = "Scout1.0";
                        break;
                    case 4:
                        strStatus = "PDA_Monitor";
                        break;
                    case 5:
                        strStatus = "Pioneer";
                        break;
                    case 6:
                        strStatus = "WalktourPack";
                        break;
                    case 7:
                        strStatus = "Indoor_ATU";
                        break;
                    case 8:
                        strStatus = "Scout2.0";
                        break;
                    case 9:
                        strStatus = "RCU_Light";
                        break;
                    case 10:
                        strStatus = "BTU";
                        break;
                    case 11:
                        strStatus = "CTU";
                        break;
                    default:
                        return strStatus;
                }
                return strStatus;
            }
        }
        public string 数据完整到达平台时间 { get; set; }
        public string 开始推送时间 { get; set; }
        public string 结束推送时间 { get; set; }
        public string 数据入记录表时间 { get; set; }
        public int 推送次数 { get; set; }
        public int 推送状态值 { get; set; }
        public string 推送状态
        {
            get
            {
                string strStatus = "";
                switch (推送状态值)
                {
                    case 1:
                        strStatus = "改名完成，等待上传";
                        break;
                    case 2:
                        strStatus = "上传中";
                        break;
                    case 3:
                        strStatus = "上传完成";
                        break;
                    case 4:
                        strStatus = "上传失败";
                        break;
                    default:
                        return strStatus;
                }
                return strStatus;
            }
        }
        public string 失败错误信息 { get; set; }
        public string 下载状态 { get; set; }
    }
}
