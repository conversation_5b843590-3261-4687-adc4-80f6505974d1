﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EleSelectFrm : DevExpress.XtraEditors.XtraForm
    {
        public string SelectedEleName { get; set; }
        private List<string> allEleNames;

        public List<string> AllEleNames
        {
            get
            {
                return allEleNames;
            }
            set
            {
                allEleNames = new List<string>(value);
            }
        }
        public EleSelectFrm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (comboBoxEdit1.Properties.Items.Contains(comboBoxEdit1.Text))
            {
                SelectedEleName = comboBoxEdit1.Text;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                XtraMessageBox.Show("指标项错误，请您重新选择！");
            }
        }

        private void EelSelectFrm_Load(object sender, EventArgs e)
        {
            if(allEleNames!=null)
            {
                if (allEleNames.Count != 0)
                {
                    comboBoxEdit1.Properties.Items.Clear();
                }
                for(int i = 0;i<allEleNames.Count;i++)
                {
                    comboBoxEdit1.Properties.Items.Add(allEleNames[i]);
                    if(i==0)
                    {
                        comboBoxEdit1.Text = allEleNames[0];
                    }
                }
            }
        }
    }
}