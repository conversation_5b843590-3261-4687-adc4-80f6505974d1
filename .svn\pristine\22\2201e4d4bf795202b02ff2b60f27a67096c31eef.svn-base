﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RoadKpiSetDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numMaxTPDistance = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chkMinDuration = new System.Windows.Forms.CheckBox();
            this.chkMinDistance = new System.Windows.Forms.CheckBox();
            this.label18 = new System.Windows.Forms.Label();
            this.numMinDuration = new System.Windows.Forms.NumericUpDown();
            this.numGoodSpeedGate = new System.Windows.Forms.NumericUpDown();
            this.label15 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.numGoodRsrpGate = new System.Windows.Forms.NumericUpDown();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numStopMaxSpeed = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.numSlowMinSpeed = new System.Windows.Forms.NumericUpDown();
            this.label19 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numFastMinSpeed = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numSlowMaxSpeed = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numCommonTpPercent = new System.Windows.Forms.NumericUpDown();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.chkBadDlSpeed = new System.Windows.Forms.CheckBox();
            this.label24 = new System.Windows.Forms.Label();
            this.cmbBadKpi = new System.Windows.Forms.ComboBox();
            this.label9 = new System.Windows.Forms.Label();
            this.numBadSinrGate = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numBadSpeedGate = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.numBadRsrpGate = new System.Windows.Forms.NumericUpDown();
            this.label22 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chkGoodDlSpeed = new System.Windows.Forms.CheckBox();
            this.label23 = new System.Windows.Forms.Label();
            this.cmbGoodKpi = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.numGoodSinrGate = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodSpeedGate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodRsrpGate)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numStopMaxSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSlowMinSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFastMinSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSlowMaxSpeed)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCommonTpPercent)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBadSinrGate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBadSpeedGate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBadRsrpGate)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodSinrGate)).BeginInit();
            this.SuspendLayout();
            // 
            // numMinDistance
            // 
            this.numMinDistance.DecimalPlaces = 1;
            this.numMinDistance.Location = new System.Drawing.Point(117, 68);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 1;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(440, 35);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 55;
            this.label7.Text = "米";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(249, 34);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 53;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(202, 75);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 48;
            this.label4.Text = "米";
            // 
            // numMaxTPDistance
            // 
            this.numMaxTPDistance.DecimalPlaces = 1;
            this.numMaxTPDistance.Location = new System.Drawing.Point(355, 29);
            this.numMaxTPDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxTPDistance.Name = "numMaxTPDistance";
            this.numMaxTPDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxTPDistance.TabIndex = 2;
            this.numMaxTPDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxTPDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(323, 452);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(417, 452);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 56;
            this.btnCancel.Text = "取消";
            // 
            // chkMinDuration
            // 
            this.chkMinDuration.AutoSize = true;
            this.chkMinDuration.Location = new System.Drawing.Point(290, 72);
            this.chkMinDuration.Name = "chkMinDuration";
            this.chkMinDuration.Size = new System.Drawing.Size(60, 16);
            this.chkMinDuration.TabIndex = 71;
            this.chkMinDuration.Text = "时长≥";
            this.chkMinDuration.UseVisualStyleBackColor = true;
            this.chkMinDuration.CheckedChanged += new System.EventHandler(this.chkMinDuration_CheckedChanged);
            // 
            // chkMinDistance
            // 
            this.chkMinDistance.AutoSize = true;
            this.chkMinDistance.Checked = true;
            this.chkMinDistance.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMinDistance.Location = new System.Drawing.Point(28, 72);
            this.chkMinDistance.Name = "chkMinDistance";
            this.chkMinDistance.Size = new System.Drawing.Size(84, 16);
            this.chkMinDistance.TabIndex = 70;
            this.chkMinDistance.Text = "持续距离≥";
            this.chkMinDistance.UseVisualStyleBackColor = true;
            this.chkMinDistance.CheckedChanged += new System.EventHandler(this.chkMinDistance_CheckedChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(442, 73);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 69;
            this.label18.Text = "秒";
            // 
            // numMinDuration
            // 
            this.numMinDuration.DecimalPlaces = 1;
            this.numMinDuration.Enabled = false;
            this.numMinDuration.Location = new System.Drawing.Point(355, 68);
            this.numMinDuration.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDuration.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numMinDuration.Name = "numMinDuration";
            this.numMinDuration.Size = new System.Drawing.Size(80, 21);
            this.numMinDuration.TabIndex = 68;
            this.numMinDuration.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDuration.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numGoodSpeedGate
            // 
            this.numGoodSpeedGate.Location = new System.Drawing.Point(104, 55);
            this.numGoodSpeedGate.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numGoodSpeedGate.Name = "numGoodSpeedGate";
            this.numGoodSpeedGate.Size = new System.Drawing.Size(80, 21);
            this.numGoodSpeedGate.TabIndex = 72;
            this.numGoodSpeedGate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGoodSpeedGate.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(189, 61);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(29, 12);
            this.label15.TabIndex = 74;
            this.label15.Text = "Mbps";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(57, 26);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(41, 12);
            this.label10.TabIndex = 53;
            this.label10.Text = "RSRP≥";
            // 
            // numGoodRsrpGate
            // 
            this.numGoodRsrpGate.Location = new System.Drawing.Point(104, 22);
            this.numGoodRsrpGate.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numGoodRsrpGate.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numGoodRsrpGate.Name = "numGoodRsrpGate";
            this.numGoodRsrpGate.Size = new System.Drawing.Size(80, 21);
            this.numGoodRsrpGate.TabIndex = 2;
            this.numGoodRsrpGate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGoodRsrpGate.Value = new decimal(new int[] {
            25,
            0,
            0,
            0});
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numStopMaxSpeed);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numSlowMinSpeed);
            this.groupBox1.Controls.Add(this.label19);
            this.groupBox1.Controls.Add(this.label17);
            this.groupBox1.Controls.Add(this.label16);
            this.groupBox1.Controls.Add(this.numFastMinSpeed);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numSlowMaxSpeed);
            this.groupBox1.Location = new System.Drawing.Point(12, 330);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(480, 116);
            this.groupBox1.TabIndex = 54;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "车速类型 (公里/小时)";
            // 
            // numStopMaxSpeed
            // 
            this.numStopMaxSpeed.Location = new System.Drawing.Point(151, 86);
            this.numStopMaxSpeed.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numStopMaxSpeed.Name = "numStopMaxSpeed";
            this.numStopMaxSpeed.Size = new System.Drawing.Size(80, 21);
            this.numStopMaxSpeed.TabIndex = 59;
            this.numStopMaxSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(172, 58);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(71, 12);
            this.label2.TabIndex = 58;
            this.label2.Text = "＜ Speed ≤";
            // 
            // numSlowMinSpeed
            // 
            this.numSlowMinSpeed.Location = new System.Drawing.Point(85, 52);
            this.numSlowMinSpeed.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numSlowMinSpeed.Name = "numSlowMinSpeed";
            this.numSlowMinSpeed.Size = new System.Drawing.Size(80, 21);
            this.numSlowMinSpeed.TabIndex = 57;
            this.numSlowMinSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(172, 25);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(53, 12);
            this.label19.TabIndex = 56;
            this.label19.Text = "＜ Speed";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(26, 90);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(119, 12);
            this.label17.TabIndex = 55;
            this.label17.Text = "停车：    Speed ≤ ";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(26, 58);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(53, 12);
            this.label16.TabIndex = 54;
            this.label16.Text = "车速慢：";
            // 
            // numFastMinSpeed
            // 
            this.numFastMinSpeed.Location = new System.Drawing.Point(85, 19);
            this.numFastMinSpeed.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numFastMinSpeed.Name = "numFastMinSpeed";
            this.numFastMinSpeed.Size = new System.Drawing.Size(80, 21);
            this.numFastMinSpeed.TabIndex = 0;
            this.numFastMinSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(26, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 43;
            this.label1.Text = "车速快：";
            // 
            // numSlowMaxSpeed
            // 
            this.numSlowMaxSpeed.Location = new System.Drawing.Point(249, 52);
            this.numSlowMaxSpeed.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numSlowMaxSpeed.Name = "numSlowMaxSpeed";
            this.numSlowMaxSpeed.Size = new System.Drawing.Size(80, 21);
            this.numSlowMaxSpeed.TabIndex = 48;
            this.numSlowMaxSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.numCommonTpPercent);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.numMinDistance);
            this.groupBox2.Controls.Add(this.numMaxTPDistance);
            this.groupBox2.Controls.Add(this.numMinDuration);
            this.groupBox2.Controls.Add(this.chkMinDistance);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.chkMinDuration);
            this.groupBox2.Location = new System.Drawing.Point(12, 224);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(480, 100);
            this.groupBox2.TabIndex = 54;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "指标好/差路段持续条件";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(206, 35);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(11, 12);
            this.label14.TabIndex = 78;
            this.label14.Text = "%";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(9, 34);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(101, 12);
            this.label3.TabIndex = 77;
            this.label3.Text = "该类采样点占比≥";
            // 
            // numCommonTpPercent
            // 
            this.numCommonTpPercent.DecimalPlaces = 1;
            this.numCommonTpPercent.Location = new System.Drawing.Point(117, 29);
            this.numCommonTpPercent.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numCommonTpPercent.Name = "numCommonTpPercent";
            this.numCommonTpPercent.Size = new System.Drawing.Size(80, 21);
            this.numCommonTpPercent.TabIndex = 72;
            this.numCommonTpPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCommonTpPercent.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.groupBox5);
            this.groupBox3.Controls.Add(this.groupBox4);
            this.groupBox3.Location = new System.Drawing.Point(12, 7);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(480, 211);
            this.groupBox3.TabIndex = 57;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "采样点指标类型";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.chkBadDlSpeed);
            this.groupBox5.Controls.Add(this.label24);
            this.groupBox5.Controls.Add(this.cmbBadKpi);
            this.groupBox5.Controls.Add(this.label9);
            this.groupBox5.Controls.Add(this.numBadSinrGate);
            this.groupBox5.Controls.Add(this.label12);
            this.groupBox5.Controls.Add(this.label13);
            this.groupBox5.Controls.Add(this.numBadSpeedGate);
            this.groupBox5.Controls.Add(this.label21);
            this.groupBox5.Controls.Add(this.numBadRsrpGate);
            this.groupBox5.Controls.Add(this.label22);
            this.groupBox5.Location = new System.Drawing.Point(13, 115);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(455, 90);
            this.groupBox5.TabIndex = 76;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "指标差点";
            // 
            // chkBadDlSpeed
            // 
            this.chkBadDlSpeed.AutoSize = true;
            this.chkBadDlSpeed.Location = new System.Drawing.Point(14, 61);
            this.chkBadDlSpeed.Name = "chkBadDlSpeed";
            this.chkBadDlSpeed.Size = new System.Drawing.Size(84, 16);
            this.chkBadDlSpeed.TabIndex = 81;
            this.chkBadDlSpeed.Text = "下载速率＜";
            this.chkBadDlSpeed.UseVisualStyleBackColor = true;
            this.chkBadDlSpeed.CheckedChanged += new System.EventHandler(this.chkBadDlSpeed_CheckedChanged);
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(260, 62);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(77, 12);
            this.label24.TabIndex = 80;
            this.label24.Text = "各指标关系：";
            // 
            // cmbBadKpi
            // 
            this.cmbBadKpi.FormattingEnabled = true;
            this.cmbBadKpi.Items.AddRange(new object[] {
            "且关系",
            "或关系"});
            this.cmbBadKpi.Location = new System.Drawing.Point(342, 55);
            this.cmbBadKpi.Name = "cmbBadKpi";
            this.cmbBadKpi.Size = new System.Drawing.Size(80, 22);
            this.cmbBadKpi.TabIndex = 79;
            this.cmbBadKpi.Text = "且关系";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(295, 28);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(41, 12);
            this.label9.TabIndex = 76;
            this.label9.Text = "SINR＜";
            // 
            // numBadSinrGate
            // 
            this.numBadSinrGate.Location = new System.Drawing.Point(342, 23);
            this.numBadSinrGate.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numBadSinrGate.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numBadSinrGate.Name = "numBadSinrGate";
            this.numBadSinrGate.Size = new System.Drawing.Size(80, 21);
            this.numBadSinrGate.TabIndex = 75;
            this.numBadSinrGate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numBadSinrGate.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(428, 28);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(17, 12);
            this.label12.TabIndex = 77;
            this.label12.Text = "db";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(57, 28);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(41, 12);
            this.label13.TabIndex = 53;
            this.label13.Text = "RSRP＜";
            // 
            // numBadSpeedGate
            // 
            this.numBadSpeedGate.Location = new System.Drawing.Point(104, 56);
            this.numBadSpeedGate.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numBadSpeedGate.Name = "numBadSpeedGate";
            this.numBadSpeedGate.Size = new System.Drawing.Size(80, 21);
            this.numBadSpeedGate.TabIndex = 72;
            this.numBadSpeedGate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numBadSpeedGate.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(189, 62);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(29, 12);
            this.label21.TabIndex = 74;
            this.label21.Text = "Mbps";
            // 
            // numBadRsrpGate
            // 
            this.numBadRsrpGate.Location = new System.Drawing.Point(104, 23);
            this.numBadRsrpGate.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numBadRsrpGate.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numBadRsrpGate.Name = "numBadRsrpGate";
            this.numBadRsrpGate.Size = new System.Drawing.Size(80, 21);
            this.numBadRsrpGate.TabIndex = 2;
            this.numBadRsrpGate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numBadRsrpGate.Value = new decimal(new int[] {
            25,
            0,
            0,
            0});
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(190, 28);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(23, 12);
            this.label22.TabIndex = 55;
            this.label22.Text = "dBm";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.chkGoodDlSpeed);
            this.groupBox4.Controls.Add(this.label23);
            this.groupBox4.Controls.Add(this.cmbGoodKpi);
            this.groupBox4.Controls.Add(this.label5);
            this.groupBox4.Controls.Add(this.numGoodSinrGate);
            this.groupBox4.Controls.Add(this.label8);
            this.groupBox4.Controls.Add(this.label10);
            this.groupBox4.Controls.Add(this.numGoodSpeedGate);
            this.groupBox4.Controls.Add(this.label15);
            this.groupBox4.Controls.Add(this.numGoodRsrpGate);
            this.groupBox4.Controls.Add(this.label11);
            this.groupBox4.Location = new System.Drawing.Point(13, 20);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(455, 90);
            this.groupBox4.TabIndex = 75;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "指标好点";
            // 
            // chkGoodDlSpeed
            // 
            this.chkGoodDlSpeed.AutoSize = true;
            this.chkGoodDlSpeed.Location = new System.Drawing.Point(14, 59);
            this.chkGoodDlSpeed.Name = "chkGoodDlSpeed";
            this.chkGoodDlSpeed.Size = new System.Drawing.Size(84, 16);
            this.chkGoodDlSpeed.TabIndex = 82;
            this.chkGoodDlSpeed.Text = "下载速率≥";
            this.chkGoodDlSpeed.UseVisualStyleBackColor = true;
            this.chkGoodDlSpeed.CheckedChanged += new System.EventHandler(this.chkGoodDlSpeed_CheckedChanged);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(260, 61);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(77, 12);
            this.label23.TabIndex = 79;
            this.label23.Text = "各指标关系：";
            // 
            // cmbGoodKpi
            // 
            this.cmbGoodKpi.FormattingEnabled = true;
            this.cmbGoodKpi.Items.AddRange(new object[] {
            "且关系",
            "或关系"});
            this.cmbGoodKpi.Location = new System.Drawing.Point(342, 55);
            this.cmbGoodKpi.Name = "cmbGoodKpi";
            this.cmbGoodKpi.Size = new System.Drawing.Size(80, 22);
            this.cmbGoodKpi.TabIndex = 78;
            this.cmbGoodKpi.Text = "且关系";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(295, 27);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 76;
            this.label5.Text = "SINR≥";
            // 
            // numGoodSinrGate
            // 
            this.numGoodSinrGate.Location = new System.Drawing.Point(342, 22);
            this.numGoodSinrGate.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numGoodSinrGate.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numGoodSinrGate.Name = "numGoodSinrGate";
            this.numGoodSinrGate.Size = new System.Drawing.Size(80, 21);
            this.numGoodSinrGate.TabIndex = 75;
            this.numGoodSinrGate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGoodSinrGate.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(428, 27);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 77;
            this.label8.Text = "db";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(190, 27);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(23, 12);
            this.label11.TabIndex = 55;
            this.label11.Text = "dBm";
            // 
            // RoadKpiSetDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(504, 482);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "RoadKpiSetDlg";
            this.Text = "指标与车速分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodSpeedGate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodRsrpGate)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numStopMaxSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSlowMinSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFastMinSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSlowMaxSpeed)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCommonTpPercent)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBadSinrGate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBadSpeedGate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBadRsrpGate)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodSinrGate)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.CheckBox chkMinDuration;
        private System.Windows.Forms.CheckBox chkMinDistance;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown numMinDuration;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown numGoodRsrpGate;
        private System.Windows.Forms.NumericUpDown numGoodSpeedGate;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numFastMinSpeed;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numSlowMaxSpeed;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numSlowMinSpeed;
        private System.Windows.Forms.NumericUpDown numStopMaxSpeed;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numGoodSinrGate;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numBadSinrGate;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown numBadSpeedGate;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.NumericUpDown numBadRsrpGate;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.ComboBox cmbGoodKpi;
        private System.Windows.Forms.ComboBox cmbBadKpi;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.CheckBox chkBadDlSpeed;
        private System.Windows.Forms.CheckBox chkGoodDlSpeed;
        private System.Windows.Forms.NumericUpDown numCommonTpPercent;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label14;
    }
}