﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedConvergeBlock
    {
        public int ID { get; set; }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public List<NRLowSpeedConvergeSample> Samplelist { get; set; } = new List<NRLowSpeedConvergeSample>();
        public int TestPointCount { get; set; }
        public double LongitudeMid { get; set; }
        public double LatitudeMid { get; set; }

        public DataInfo Speed { get; set; } = new DataInfo();
        public DataInfo Rsrp { get; set; }= new DataInfo();
        public DataInfo Sinr { get; set; }= new DataInfo();
        public DbRect Bounds { get; set; }
        public string RoadName { get; set; }

        public DataInfo Code0Qam64Rate { get; set; } = new DataInfo();
        public DataInfo Code1Qam64Rate { get; set; } = new DataInfo();
        public DataInfo Bler { get; set; } = new DataInfo();
        public DataInfo DLGrantCount { get; set; } = new DataInfo();

        int istime = int.MaxValue;
        int ietime = int.MinValue;

        public NRLowSpeedConvergeBlock(TestPoint item)
        {
            AddTestPoint(item);
        }

        public void AddTestPoint(TestPoint tp)
        {
            if (!TestPoints.Contains(tp))
            {
                TestPoints.Add(tp);
                if (istime > tp.Time)
                {
                    istime = tp.Time;
                }
                if (ietime < tp.Time)
                {
                    ietime = tp.Time;
                }
            }
        }

        public void Join(NRLowSpeedConvergeBlock tpBlock)
        {
            foreach (TestPoint tp in tpBlock.TestPoints)
            {
                AddTestPoint(tp);
            }
            Samplelist.AddRange(tpBlock.Samplelist);
        }

        public bool Intersect(double longitude, double latitude, int radius)
        {
            foreach (TestPoint tp in TestPoints)
            {
                if (MathFuncs.GetDistance(longitude, latitude, tp.Longitude, tp.Latitude) > radius)
                {
                    return false;
                }
            }
            return true;
        }

        public void GetResult()
        {
            TestPointCount = TestPoints.Count;

            foreach (NRLowSpeedConvergeSample badSample in Samplelist)
            {
                Speed.Add(badSample.Speed);
                Rsrp.Add(badSample.Rsrp);
                Sinr.Add(badSample.Sinr);
                Bler.Add(badSample.PDSCH_BLER);
                DLGrantCount.Add(badSample.PDCCH_DL_Grant_Count);

                Code0Qam64Rate.Add(badSample.Code0Qam64, (badSample.Code0Qam64 + badSample.Code0Qpsk + badSample.Code0Qam16));
                Code1Qam64Rate.Add(badSample.Code1Qam64, (badSample.Code1Qam64 + badSample.Code1Qpsk + badSample.Code1Qam16));
            }

            Speed.Calculate();
            Rsrp.Calculate();
            Sinr.Calculate();
            Bler.Calculate();
            DLGrantCount.Calculate();
            Code0Qam64Rate.Calculate();
            Code1Qam64Rate.Calculate();

            getCenterPoint();
            getRoad();

            //getCell();
            //this.getDlCode0Qam64Rate();
            //this.getDlCode1Qam64Rate();
            //this.getDoubleRankRate();
            //this.PDSCH_BLER_Avg = this[pdsch_bler, SummaryType.Avg];
            //this.PDCCH_DL_Grant_Count_Avg = this[pdcch_dl_grant_count, SummaryType.Avg];
        }

        private void getCenterPoint()
        {
            Bounds = new DbRect();
            bool first = true;
            foreach (TestPoint item in TestPoints)
            {
                if (first)
                {
                    Bounds.x1 = item.Longitude;
                    Bounds.x2 = item.Longitude;
                    Bounds.y1 = item.Latitude;
                    Bounds.y2 = item.Latitude;
                    first = false;
                    continue;
                }

                if (Bounds.x1 > item.Longitude)
                {
                    Bounds.x1 = item.Longitude;
                }
                if (Bounds.x2 < item.Longitude)
                {
                    Bounds.x2 = item.Longitude;
                }
                if (Bounds.y1 > item.Latitude)
                {
                    Bounds.y1 = item.Latitude;
                }
                if (Bounds.y2 < item.Latitude)
                {
                    Bounds.y2 = item.Latitude;
                }
            }
            LongitudeMid = Bounds.Center().x;
            LatitudeMid = Bounds.Center().y;
        }

        public class DataInfo
        {
           public double Sum { get; set; }
           public double Count { get; set; }
           public double Avg { get; set; }

            public void Add(double? data, double count)
            {
                if (data != null)
                {
                    Sum += (double)data;
                    Count += count;
                }
            }

            public void Add(double? data)
            {
                if (data != null)
                {
                    Sum += (double)data;
                    Count++;
                }
            }

            public void Calculate()
            {
                if (Count > 0)
                { 
                    Avg = Math.Round(Sum / Count, 2);
                }
            }
        }

        private void getRoad()
        {
            List<string> nameList = new List<string>();
            StringBuilder sb = new StringBuilder();
            foreach (TestPoint tp in TestPoints)
            {
                string name = GISManager.GetInstance().GetRoadPlaceDesc(tp.Longitude, tp.Latitude);
                if (!nameList.Contains(name))
                {
                    nameList.Add(name);
                    sb.Append(name + ",");
                }
            }
            RoadName = sb.Length > 0 ? sb.ToString().Substring(0, sb.Length - 1) : sb.ToString();
        }
    }

    public class NRLowSpeedConvergeSample
    {
        public int SN { get; set; }
        public TestPoint TestPoint { get; set; }
        public double? Speed { get; set; }
        public int? EARFCN { get; set; }
        public int? PCI { get; set; }
        public int? TAC { get; set; }
        public long? NCI { get; set; }
        public NRCell Cell { get; set; }
        public string CellName { get; set; } = "";
        public float? Rsrp { get; set; }
        public float? Sinr { get; set; }
        public string Distance { get; set; } = "";
        public string Time { get; set; }
        
        public double Code0Qam64 { get; set; }
        public double Code1Qam64 { get; set; }
        public double Code0Qpsk { get; set; }
        public double Code1Qpsk { get; set; }
        public double Code0Qam16 { get; set; }
        public double Code1Qam16 { get; set; }

        //PDSCH BLER
        public double PDSCH_BLER { get; set; }
        //PDCCH_DL_Grant_Count
        public double PDCCH_DL_Grant_Count { get; set; }

        public void SetSampleInfo(TestPoint tp)
        {
            TestPoint = tp;
            Speed = NRTpHelper.NrTpManager.GetAppSpeedMb(tp);
            Rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            Sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);

            Cell = tp.GetMainCell_NR();
            if (Cell != null)
            {
                EARFCN = Cell.SSBARFCN;
                PCI = Cell.PCI;
                TAC = Cell.TAC;
                NCI = Cell.NCI;
                CellName = Cell.Name;
                Distance = Math.Round(MathFuncs.GetDistance(Cell.Longitude, Cell.Latitude, tp.Longitude, tp.Latitude), 2).ToString();
            }
            else
            {
                EARFCN = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                PCI = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                TAC = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
                NCI = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            }

            Time = tp.DateTimeStringWithMillisecond;

            PDSCH_BLER = GetBler(tp);
            PDCCH_DL_Grant_Count = GetGrant(tp);

            Code0Qam64 = getValidData(tp["NR_64QAM_Count_DL_TB0"]);
            Code0Qpsk = getValidData(tp["NR_QPSK_Count_DL_TB0"]);
            Code0Qam16 = getValidData(tp["NR_16QAM_Count_DL_TB0"]);
            Code1Qam64 = getValidData(tp["NR_64QAM_Count_DL_TB1"]);
            Code1Qpsk = getValidData(tp["NR_QPSK_Count_DL_TB1"]);
            Code1Qam16 = getValidData(tp["NR_16QAM_Count_DL_TB1"]);
        }

        protected double getValidData(object obj)
        {
            double data = 0;
            if (obj != null)
            {
                double.TryParse(obj.ToString(), out data);
            }
            return data;
        }

        protected double GetBler(TestPoint tp)
        {
            return getValidData(tp["NR_PDSCH_BLER"]);
        }

        protected double GetGrant(TestPoint tp)
        {
            return getValidData(tp["NR_PDCCH_DL_Grant_Count"]);
        }
    }
}
