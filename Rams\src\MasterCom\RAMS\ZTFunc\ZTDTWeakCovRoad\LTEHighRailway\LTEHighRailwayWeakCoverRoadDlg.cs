﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEHighRailwayWeakCoverRoadDlg : BaseDialog
    {
        public LTEHighRailwayWeakCoverRoadDlg(WeakCoverRoadCondition_LTEHighRailWay condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(WeakCoverRoadCondition_LTEHighRailWay condition)
        {
            if (condition == null)
            {
                return;
            }
            numRSRP.Value = (decimal)condition.RSRP;
            numWeakTestPoints.Value = condition.WeakCoverRoadTestPoints;
            numTestPoints.Value = condition.NormalTestPoints;
        }

        public WeakCoverRoadCondition_LTEHighRailWay GetConditon()
        {
            WeakCoverRoadCondition_LTEHighRailWay condition = new WeakCoverRoadCondition_LTEHighRailWay();
            condition.RSRP = (float)numRSRP.Value;
            condition.WeakCoverRoadTestPoints = (int)numWeakTestPoints.Value;
            condition.NormalTestPoints = (int)numTestPoints.Value;
            
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }

    public class WeakCoverRoadCondition_LTEHighRailWay
    {
        public float RSRP { get; set; } = -110;
        public int WeakCoverRoadTestPoints { get; set; } = 3;
        public int NormalTestPoints { get; set; } = 3;
    }
}
