﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyQueryTaskOrderInfo : DIYSQLBase
    {
        public DiyQueryTaskOrderInfo(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "查询之前未解析的工单文件"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        #endregion     

        public Dictionary<string, TaskOrderManageInfo> orderFileDic { get; } = new Dictionary<string, TaskOrderManageInfo>();

        string districtName = "";
        public void SetQueryCondition(string districtName)
        {
            this.districtName = districtName;
        }

        #region 查询流程
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                string strsql = getSqlTextString();
                E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                package.Command = Command.DIYSearch;//枚举类型：DIY接口
                package.SubCommand = SubCommand.Request;//枚举类型：请求
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }
                package.Content.PrepareAddParam();
                package.Content.AddParam(strsql);
                StringBuilder sb = new StringBuilder();
                if (retArrDef != null)
                {
                    for (int i = 0; i < retArrDef.Length; i++)
                    {
                        sb.Append((int)retArrDef[i]);
                        sb.Append(",");
                    }
                }
                package.Content.AddParam(sb.ToString().TrimEnd(','));
                clientProxy.Send();
                receiveRetData(clientProxy);
            }
            catch (Exception ex)
            {
                log.Error(ex.ToString());
            }
        }

        protected override string getSqlTextString()
        {
            /* 由于存在部分工单未及时处理,现场手动填写数据关闭了工单,导致再上传对应工单数据时返回失败
               这种上传失败的工单就会累积,反复处理得不到解决,故去除-99上传失败的状态,不再重新处理上传
            */
            string strSQL = string.Format(@"select a.[地市],a.[工单号],a.[开始时间],a.[结束时间],a.[文件名],a.[导入时间],a.[备注] from tb_lowtask_file a left join tb_lowtask_result b on a.[工单号] = b.[工单号] where a.[地市] = '{0}' and (b.[上传状态] in (-100,-98) or b.[工单号] is null)", districtName);
            return strSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            TaskOrderManageInfo orderInfo;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TaskOrderFileInfo orderFile = new TaskOrderFileInfo();
                    orderFile.FillDataByDB(package);
                    if (!orderFileDic.TryGetValue(orderFile.OrderID, out orderInfo))
                    {
                        orderInfo = new TaskOrderManageInfo(orderFile.OrderID, orderFile.Area, orderFile.StartDateTime, orderFile.EndDateTime, orderFile.ImportDataTime);
                        orderFileDic.Add(orderFile.OrderID, orderInfo);
                    }
                    orderInfo.OrderFileList.Add(orderFile);
                    //暂时文件备注为文件解析异常信息
                    if (!string.IsNullOrEmpty(orderFile.Remarks))
                    {
                        orderInfo.OrderState = OrderStateType.AnalyseFail;
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        #endregion
    }
}
