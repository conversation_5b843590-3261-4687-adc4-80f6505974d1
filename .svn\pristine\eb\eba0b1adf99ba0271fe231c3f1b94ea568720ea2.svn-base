﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public enum LTEBTSType
    {
        Outdoor = 1,
        Indoor
    }

    public class LTEBTS : Snapshot<LTEBTS>,IComparable<LTEBTS> ,MasterCom.RAMS.Func.IVoronoi, ISite
    {
        public LTEBTS()
        {
            Value = this;
        }

        public string Name { get; set; }

        public double Longitude { get; set; }

        public double Latitude { get; set; }

        public LTEBTSType Type { get; set; }
        public string TypeStringDesc
        {
            get
            {
                if (Type == LTEBTSType.Indoor)
                {
                    return "室内";
                }
                else if (Type == LTEBTSType.Outdoor)
                {
                    return "室外";
                }
                return "";
            }
        }
        public string Description { get; set; }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append("Name:").Append(Name);
                info.Append("\r\nLongitude:").Append(Longitude);
                info.Append("\r\nLatitude:").Append(Latitude);
                info.Append("\r\nType:").Append(TypeStringDesc);
                info.Append("\r\nDescription:").Append(Description);
                info.Append("\r\n");
                return info.ToString();
            }
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public List<LTECell> Cells
        {
            get { return cells; }
        }

        public List<LTECell> LatestCells
        {
            get
            {
                List<LTECell> ret = new List<LTECell>();
                foreach (LTECell cell in cells)
                {
                    if (cell.ValidPeriod.IEndTime == int.MaxValue)
                    {
                        ret.Add(cell);
                    }
                }
                return ret;
            }
        }

        public void AddCell(LTECell cell)
        {
            cells.Add(cell);
            cell.BelongBTS.Add(this);
        }

        public void Fill(MasterCom.RAMS.Net.Content content, CellManager cellManager)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            content.GetParamString();//msc管理未实现
            Name = content.GetParamString();
            BTSID = content.GetParamInt();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            Type = parseType(content.GetParamString());
            Description = content.GetParamString();
        }

        private LTEBTSType parseType(string s)
        {
            if ("是".Equals(s) || "室内".Equals(s))
            {
                return LTEBTSType.Indoor;
            }
            else
            {
                return LTEBTSType.Outdoor;
            }

        }
        
        public int BTSID { get; set; }
        
        private readonly List<LTECell> cells = new List<LTECell>();
        public double VertexX { get { return Longitude; } }
        public double VertexY { get { return Latitude; } }

        #region IComparable<LTEBTS> 成员

        public int CompareTo(LTEBTS other)
        {
            if (other==null)
            {
                return 1;
            }
            return this.Name.CompareTo(other.Name);
        }

        #endregion
    }
}
