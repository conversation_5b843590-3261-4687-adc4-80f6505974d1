﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using static MasterCom.RAMS.ZTFunc.NROptimizationcCustersBase;

namespace MasterCom.RAMS.KPI_Statistics
{
    /// <summary>
    /// 按区域进行数据存储
    /// </summary>
    public class KPIDataManager
    {
        readonly private Dictionary<string, Dictionary<object, KPIDataGroup>> regionDataGroupDic = new Dictionary<string, Dictionary<object, KPIDataGroup>>();
        readonly private Dictionary<string, KPIDataGroup> regionSummaryDic = new Dictionary<string, KPIDataGroup>();
        readonly private KPIDataGroup summaryGroup = new KPIDataGroup(string.Empty, true);
        public List<KPIDataGroup> GetReportGroupDataSet()//获得报告组结果数据集
        {
            List<KPIDataGroup> groupSet = new List<KPIDataGroup>();
            List<KPIDataGroup> sumSet = new List<KPIDataGroup>();
            foreach (string regName in regionDataGroupDic.Keys)
            {
                foreach (KPIDataGroup group in regionDataGroupDic[regName].Values)
                {
                    groupSet.Add(group);
                }
                if (regionDataGroupDic[regName].Count > 1)
                {//区域下的数据组大于1组，需要加上汇总组
                    addRegionSummaryDic(regName);
                    KPIDataGroup g = regionSummaryDic[regName];
                    if (g.GroupInfo == null || g.GroupInfo.ToString() == "")
                    {
                        sumSet.Add(g);
                    }
                    else
                    {
                        groupSet.Add(g);
                    }
                }
            }
            groupSet.Sort();//元素排序。
            groupSet.AddRange(sumSet);
            if (regionDataGroupDic.Count > 1)
            {//区域组大于1组，需要加上区域汇总组
                mergeSummaryGroup();

                groupSet.Add(summaryGroup);
            }
            if (groupSet.Count == 0)
            {
                groupSet.Add(new KPIDataGroup(null));
            }
            return groupSet;
        }

        private void addRegionSummaryDic(string regName)
        {
            foreach (KPIDataGroup group in regionDataGroupDic[regName].Values)
            {
                KPIDataGroup sumGroup;
                if (!regionSummaryDic.TryGetValue(regName, out sumGroup))
                {
                    sumGroup = group.Clone();
                    sumGroup.SetGrpSummary(true);
                    sumGroup.GroupInfo = regName;
                    regionSummaryDic[regName] = sumGroup;
                }
                else
                {
                    sumGroup.Merge(group);//合并汇总
                }
            }
        }

        private void mergeSummaryGroup()
        {
            foreach (Dictionary<object, KPIDataGroup> regGroupDic in regionDataGroupDic.Values)
            {
                foreach (KPIDataGroup group in regGroupDic.Values)
                {
                    if (!group.IsSummaryGroup)
                    {
                        summaryGroup.Merge(group);
                    }
                }
            }
        }

        /// <summary>
        /// 增加一统计数据，按区域进行数据存储
        /// </summary>
        /// <param name="regionName">数据所属区域名称，未能关联到区域名称时，改值应该为String.Empty</param>
        /// <param name="groupInfo">统计数据的分组信息，可为：区域名称，FileInfo，ICell</param>
        /// <param name="fileInfo">统计数据的文件信息，用来区分运营商，主被叫等</param>
        /// <param name="data">统计数据</param>
        public void AddStatData(string regionName, object groupInfo
            , FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            AddStatData(regionName, groupInfo, false, fileInfo, data, saveAsGrid);
        }
        public void AddStatData(string regionName, object groupInfo, bool isSummary
            , FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            Dictionary<object, KPIDataGroup> groupDic = null;
            if (regionDataGroupDic.TryGetValue(regionName, out groupDic))
            {//区域分类下已有数据
                KPIDataGroup dataGrp = null;
                if (!groupDic.TryGetValue(groupInfo, out dataGrp))
                {
                    dataGrp = new KPIDataGroup(groupInfo, isSummary);
                    groupDic.Add(groupInfo, dataGrp);
                }
                dataGrp.AddStatData(fileInfo, data, saveAsGrid);
            }
            else
            {
                KPIDataGroup dataGrp = null;
                dataGrp = new KPIDataGroup(groupInfo, isSummary);
                dataGrp.AddStatData(fileInfo, data, saveAsGrid);
                groupDic = new Dictionary<object, KPIDataGroup>();
                groupDic.Add(groupInfo, dataGrp);
                regionDataGroupDic.Add(regionName, groupDic);
            }
        }
        public void FinalMtMoStatData()
        {
            foreach (Dictionary<object, KPIDataGroup> regionGroup in regionDataGroupDic.Values)
            {
                foreach (KPIDataGroup group in regionGroup.Values)
                {
                    group.FinalMtMoGroup();
                }
            }
        }
    }

    /// <summary>
    /// KPI统计的分组统计单元组
    /// 按运营商->按主被叫，进行数据存储
    /// </summary>
    public class KPIDataGroup : IComparable<KPIDataGroup>
    {
        /// <summary>
        /// 默认为非汇总组
        /// </summary>
        /// <param name="groupInfo"></param>
        public KPIDataGroup(object groupInfo)
        {
            this.GroupInfo = groupInfo;
        }
        public KPIDataGroup(object groupInfo, bool isSummaryGrp)
            : this(groupInfo)
        {
            this.isSummaryGrp = isSummaryGrp;
        }
        public override string ToString()
        {
            string info = string.Empty;
            if (GroupInfo != null)
            {
                if (MainModel.GetInstance().RootNodeGeometrys)
                {
                    if (GroupInfo is ResvRegion)
                    {
                        ResvRegion region = GroupInfo as ResvRegion;
                        info = region.RootNodeName + "--" + region.RegionName;
                    }
                    else if (GroupInfo is KeyValuePair<string, List<ResvRegion>>)
                    {
                        KeyValuePair<string, List<ResvRegion>> varPair = (KeyValuePair<string, List<ResvRegion>>)GroupInfo;
                        info = varPair.Key.ToString();
                    }
                    else
                    {
                        info = GroupInfo.ToString();
                    }
                }
                else
                {
                    info = GroupInfo.ToString();
                }
                if (isSummaryGrp)
                {
                    info += " 汇总";
                }
            }
            return info;
        }
        private bool isSummaryGrp = false;
        public bool IsSummaryGroup
        {
            get { return isSummaryGrp; }
        }
        public void SetGrpSummary(bool bSummary)
        {
            this.isSummaryGrp = bSummary;
        }
        /// <summary>
        /// 统计组信息，区分按区域，栅格，文件，小区，道路等。
        /// </summary>
        public object GroupInfo { get; set; }
        protected CarrierStatDataHub cmDataHub = null;
        protected CarrierStatDataHub cuDataHub = null;
        protected CarrierStatDataHub ctDataHub = null;

        public void Merge(KPIDataGroup other)
        {
            if (other.cmDataHub != null)
            {
                if (this.cmDataHub == null)
                {
                    this.cmDataHub = other.cmDataHub.Clone();
                }
                else
                {
                    this.cmDataHub.Merge(other.cmDataHub);
                }
            }
            if (other.ctDataHub != null)
            {
                if (this.ctDataHub == null)
                {
                    this.ctDataHub = other.ctDataHub.Clone();
                }
                else
                {
                    this.ctDataHub.Merge(other.ctDataHub);
                }
            }
            if (other.cuDataHub != null)
            {
                if (this.cuDataHub == null)
                {
                    this.cuDataHub = other.cuDataHub.Clone();
                }
                else
                {
                    this.cuDataHub.Merge(other.cuDataHub);
                }
            }
        }

        public KPIDataGroup Clone()
        {
            KPIDataGroup grp = new KPIDataGroup(this.GroupInfo, this.isSummaryGrp);
            if (this.cmDataHub != null)
            {
                grp.cmDataHub = this.cmDataHub.Clone();
            }
            if (this.ctDataHub != null)
            {
                grp.ctDataHub = this.ctDataHub.Clone();
            }
            if (this.cuDataHub != null)
            {
                grp.cuDataHub = this.cuDataHub.Clone();
            }
            return grp;
        }

        public virtual void AddStatData(FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            if (fileInfo == null || data == null)
            {
                return;
            }
            if (fileInfo.CarrierType == (int)CarrierType.ChinaMobile)
            {
                if (cmDataHub == null)
                {
                    cmDataHub = new CarrierStatDataHub(CarrierType.ChinaMobile);
                }
                cmDataHub.AddStatData(fileInfo, data, saveAsGrid);
            }
            else if (fileInfo.CarrierType == (int)CarrierType.ChinaUnicom)
            {
                if (cuDataHub == null)
                {
                    cuDataHub = new CarrierStatDataHub(CarrierType.ChinaUnicom);
                }
                cuDataHub.AddStatData(fileInfo, data, saveAsGrid);
            }
            else if (fileInfo.CarrierType == (int)CarrierType.ChinaTelecom)
            {
                if (ctDataHub == null)
                {
                    ctDataHub = new CarrierStatDataHub(CarrierType.ChinaTelecom);
                }
                ctDataHub.AddStatData(fileInfo, data, saveAsGrid);
            }
        }

        public void AddStatDataTotal(FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            if (fileInfo == null || data == null)
            {
                return;
            }
            if (fileInfo.CarrierType == (int)CarrierType.ChinaMobile)
            {
                if (cmDataHub == null)
                {
                    cmDataHub = new CarrierStatDataHub(CarrierType.ChinaMobile);
                }
                cmDataHub.AddToTotal(fileInfo, data, saveAsGrid);
            }
            else if (fileInfo.CarrierType == (int)CarrierType.ChinaUnicom)
            {
                if (cuDataHub == null)
                {
                    cuDataHub = new CarrierStatDataHub(CarrierType.ChinaUnicom);
                }
                cuDataHub.AddToTotal(fileInfo, data, saveAsGrid);
            }
            else if (fileInfo.CarrierType == (int)CarrierType.ChinaTelecom)
            {
                if (ctDataHub == null)
                {
                    ctDataHub = new CarrierStatDataHub(CarrierType.ChinaTelecom);
                }
                ctDataHub.AddToTotal(fileInfo, data, saveAsGrid);
            }
        }

        public void FinalMtMoGroup()
        {
            if (cmDataHub != null)
                cmDataHub.FinalMtMoGroup();
            if (cuDataHub != null)
                cuDataHub.FinalMtMoGroup();
            if (ctDataHub != null)
                ctDataHub.FinalMtMoGroup();
        }

        public double CalcFormula(CarrierType carrierType, int momtFlag, string formula, params object[] extraParams)
        {
            double v = double.NaN;
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return v;
            }
            v = hub.CalcFormula(momtFlag, formula, extraParams);
            return v;
        }
        public double CalcFormula(CarrierType carrierType, int momtFlag, string formula, int decPlace, params object[] extraParams)
        {
            double v = double.NaN;
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return v;
            }
            v = hub.CalcFormula(momtFlag, formula, decPlace, extraParams);
            return v;
        }
        private CarrierStatDataHub getDataHub(CarrierType carrierType)
        {
            CarrierStatDataHub hub = null;
            switch (carrierType)
            {
                case CarrierType.ChinaMobile:
                    hub = cmDataHub;
                    break;
                case CarrierType.ChinaUnicom:
                    hub = cuDataHub;
                    break;
                case CarrierType.ChinaTelecom:
                    hub = ctDataHub;
                    break;
                default:
                    break;
            }
            return hub;
        }

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// 簇优化统计
        /// </summary>
        public void statisficNROptimizationc(NROptimizationcIndicator nROptimizationcIndicator)
        {
            if (cmDataHub != null)
            {
                foreach (var item in cmDataHub.TokenStatDataDic)
                {
                    double switchdFZ = 0;
                    double switchFM = 0;
                    if (item.Key == "evt")
                    {
                        foreach (var fields in item.Value.fieldValueDic)
                        {
                            switch (fields.Key)
                            {
                                case "value3[9074]":
                                    nROptimizationcIndicator.addsucessFZ = fields.Value;
                                    break;
                                case "evtIdCount[9021]":
                                    nROptimizationcIndicator.addsucessFM = fields.Value;
                                    break;
                                case "evtIdCount[9332]":
                                    nROptimizationcIndicator.droppedFZ = fields.Value;
                                    break;
                                case "evtIdCount[9291]":
                                    nROptimizationcIndicator.droppedFM = fields.Value;
                                    break;
                                case "evtIdCount[9295]":
                                    switchdFZ = switchdFZ + fields.Value;
                                    break;
                                case "evtIdCount[9298]":
                                    switchdFZ = switchdFZ + fields.Value;
                                    break;
                                case "evtIdCount[9294]":
                                    nROptimizationcIndicator.switchFM = switchFM + fields.Value;
                                    break;
                                case "evtIdCount[9297]":
                                    nROptimizationcIndicator.switchFM = switchFM + fields.Value;
                                    break;
                            }
                        }

                        nROptimizationcIndicator.switchdFZ = switchdFZ;
                        nROptimizationcIndicator.switchFM = switchFM;
                        #region 公式
                        ////5G SCG添加成功率(value3[9074]/evtIdCount[9021])*100}
                        //nROptimizationcIndicator.addsucessFZ = item.Value.fieldValueDic["value3[9074]"];
                        //nROptimizationcIndicator.addsucessFM = item.Value.fieldValueDic["evtIdCount[9021]"];

                        ////NR掉线率(evtIdCount[9332]/evtIdCount[9291])*100}
                        //nROptimizationcIndicator.droppedFZ = item.Value.fieldValueDic["evtIdCount[9332]"];
                        //nROptimizationcIndicator.droppedFM = item.Value.fieldValueDic["evtIdCount[9291]"];

                        ////5G切换成功率{100*(evtIdCount[9295]+evtIdCount[9298])/(evtIdCount[9294]+evtIdCount[9297])}
                        //nROptimizationcIndicator.switchdFZ = (item.Value.fieldValueDic["evtIdCount[9295]"] + item.Value.fieldValueDic["evtIdCount[9298]"]);
                        //nROptimizationcIndicator.switchFM = (item.Value.fieldValueDic["evtIdCount[9294]"] + item.Value.fieldValueDic["evtIdCount[9297]"]);
                        #endregion
                    }
                    else
                    {
                        foreach (var fields in item.Value.fieldValueDic)
                        {
                            switch (fields.Key)
                            {
                                case "8004001F":
                                    nROptimizationcIndicator.occupyDuration = fields.Value;
                                    break;
                                case "80040005":
                                    nROptimizationcIndicator.connetionDurant = fields.Value;
                                    break;
                                case "BA040053":
                                    nROptimizationcIndicator.corecityFZ = fields.Value;
                                    break;
                                case "BA040001":
                                    nROptimizationcIndicator.corecityFM = fields.Value;
                                    break;
                                case "BA040103":
                                    nROptimizationcIndicator.downAvgFZ = fields.Value;
                                    break;
                                case "BA040205":
                                    nROptimizationcIndicator.downFZ = fields.Value;
                                    break;
                                case "BA0401A4":
                                    nROptimizationcIndicator.downFM = fields.Value;
                                    break;
                                case "BA0400FC":
                                    nROptimizationcIndicator.uploadAvgFZ = fields.Value;
                                    break;
                                case "BA040206":
                                    nROptimizationcIndicator.uploadAvgFZ = fields.Value;
                                    break;
                                case "BA0401A6":
                                    nROptimizationcIndicator.uploadAvgFZ = fields.Value;
                                    break;
                                case "BA0400DB":
                                    nROptimizationcIndicator.ltecoverFZ = fields.Value;
                                    break;
                                case "BA040050":
                                    nROptimizationcIndicator.ltecoverFM = fields.Value;
                                    break;
                            }
                        }
                        #region 公式
                        /*
                           //5G时长驻留比{Nr_8004001F/Nr_80040005*100}
                        nROptimizationcIndicator.occupyDuration = item.Value.fieldValueDic["8004001F"];
                        nROptimizationcIndicator.connetionDurant = item.Value.fieldValueDic["80040005"];

                        //5G道路测试覆盖率{Nr_BA040053/Nr_BA040052*100 }
                        nROptimizationcIndicator.corecityFZ = item.Value.fieldValueDic["BA040053"];
                        nROptimizationcIndicator.corecityFM = item.Value.fieldValueDic["BA040001"];

                        //5G下行平均吞吐率{Nr_BA040103/1000/1000}
                        nROptimizationcIndicator.downAvgFZ = item.Value.fieldValueDic["BA040103"];

                        //5G下行吞吐率高于100Mbps占比(FTP){Nr_BA040205 /Nr_BA0401A4*100 } 
                        nROptimizationcIndicator.downFZ = item.Value.fieldValueDic["BA040205"];
                        nROptimizationcIndicator.downFM = item.Value.fieldValueDic["BA0401A4"];

                        //5G上行平均吞吐率{Nr_BA0400FC/1000/1000}
                        nROptimizationcIndicator.uploadAvgFZ = item.Value.fieldValueDic["BA0400FC"];

                        //5G上行吞吐率高于2Mbps占比（FTP）{Nr_BA040206 /Nr_BA0401A6*100 }
                        nROptimizationcIndicator.uploadAvgFZ = item.Value.fieldValueDic["BA040206"];
                        nROptimizationcIndicator.uploadAvgFZ = item.Value.fieldValueDic["BA0401A6"];

                        //4G锚点覆盖率{100*Nr_BA0400DB/Nr_BA040050}
                        nROptimizationcIndicator.ltecoverFZ = item.Value.fieldValueDic["BA0400DB"];
                        nROptimizationcIndicator.ltecoverFM = item.Value.fieldValueDic["BA040050"];
                         */
                        #endregion
                    }
                }
            }
        }

        #region IComparable<KPIDataGroup> 成员

        public int CompareTo(KPIDataGroup other)
        {
            if (other == null)
            {
                return 1;
            }
            return this.ToString().CompareTo(other.ToString());
        }

        #endregion

        internal object GetReservedInfo(CarrierType carrierType, ReservedField field)
        {
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return "-";
            }
            return hub.GetReservedInfo(field);
        }
    }

    /// <summary>
    /// 按运营商归类的统计数据Hub，内部再按主被叫存储数据
    /// </summary>
    public class CarrierStatDataHub : StatDataHubBase
    {
        readonly CarrierType carrierType;
        public CarrierStatDataHub(CarrierType carrierType)
        {
            this.carrierType = carrierType;
        }
        public double CalcFormula(int momtFlag, string formula, params object[] extraParams)
        {
            StatDataHubBase hub = getDataHub(momtFlag);
            if (hub != null)
            {
                return hub.CalcValueByFormula(formula, extraParams);
            }
            return double.NaN;
        }
        public double CalcFormula(int momtFlag, string formula, int decPlace, params object[] extraParams)
        {
            StatDataHubBase hub = getDataHub(momtFlag);
            if (hub != null)
            {
                return hub.CalcValueByFormula(formula, decPlace, extraParams);
            }
            return double.NaN;
        }

        public new CarrierStatDataHub Clone()
        {
            CarrierStatDataHub clone = new CarrierStatDataHub(carrierType);
            StatDataHubBase baseClone = base.Clone();
            //clone.tokenStatDataDic = baseClone.TokenStatDataDic;
            foreach (string key in baseClone.TokenStatDataDic.Keys)
            {
                clone.tokenStatDataDic[key] = baseClone.TokenStatDataDic[key].Clone();
            }
            if (baseClone.GridMatrix != null)
            {
                clone.gridMatrix = new MasterCom.RAMS.Grid.GridMatrix<GridDataUnit>();
                foreach (GridDataUnit gu in baseClone.GridMatrix)
                {
                    clone.gridMatrix[gu.RowIdx, gu.ColIdx] = gu.Clone();
                }
            }
            //clone.gridMatrix = baseClone.GridMatrix;
            if (this.mtDataHub != null)
            {
                clone.mtDataHub = mtDataHub.Clone();
            }
            if (this.moDataHub != null)
            {
                clone.moDataHub = moDataHub.Clone();
            }
            return clone;
        }

        private StatDataHubBase getDataHub(int momtFlag)
        {
            StatDataHubBase hub = null;
            if (momtFlag == (int)MoMtFile.MoFlag)
            {
                hub = moDataHub;
            }
            else if (momtFlag == (int)MoMtFile.MtFlag)
            {
                hub = mtDataHub;
            }
            else
            {
                hub = this;
            }
            return hub;
        }
        private StatDataHubBase moDataHub = null;
        private StatDataHubBase mtDataHub = null;

        public override void Merge(StatDataHubBase otherHub)
        {
            base.Merge(otherHub);
            CarrierStatDataHub careierOther = otherHub as CarrierStatDataHub;
            if (careierOther == null)
            {
                return;
            }
            foreach (int id in careierOther.fileIDDic.Keys)
            {
                this.fileIDDic[id] = careierOther.fileIDDic[id];
            }
            if (careierOther.moDataHub != null)
            {
                if (moDataHub != null)
                {
                    moDataHub.Merge(careierOther.moDataHub);
                }
                else
                {
                    moDataHub = careierOther.moDataHub.Clone();
                }
            }
            if (careierOther.mtDataHub != null)
            {
                if (mtDataHub != null)
                {
                    mtDataHub.Merge(careierOther.mtDataHub);
                }
                else
                {
                    mtDataHub = careierOther.mtDataHub.Clone();
                }
            }
        }

        public void AddStatData(FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            fileIDDic[fileInfo.ID] = fileInfo;
            if (fileInfo.Momt == (int)MoMtFile.MoFlag)
            {
                if (moDataHub == null)
                {
                    moDataHub = new StatDataHubBase();
                }

                moDataHub.AddStatData(data, saveAsGrid);
            }
            else if (fileInfo.Momt == (int)MoMtFile.MtFlag)
            {
                if (mtDataHub == null)
                {
                    mtDataHub = new StatDataHubBase();
                }
                mtDataHub.AddStatData(data, saveAsGrid);
            }
            else
            {
                this.AddStatData(data, saveAsGrid);
            }
            //汇聚延迟  by zlr
            //AddStatData(data, saveAsGrid);
        }

        public void AddToTotal(FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            AddStatData(data, saveAsGrid);
        }

        public void FinalMtMoGroup()
        {
            if (moDataHub != null)
                Merge(moDataHub);
            if (mtDataHub != null)
                Merge(mtDataHub);
        }

        internal object GetReservedInfo(ReservedField field)
        {
            if (fileIDDic == null)
            {
                return null;
            }
            if (field == ReservedField.FileIDs)
            {

                StringBuilder id = new StringBuilder();
                foreach (int key in fileIDDic.Keys)
                {
                    id.Append(key + ";");
                }
                return id.ToString().TrimEnd(';');
            }
            else if (field == ReservedField.FileNames)
            {
                StringBuilder name = new StringBuilder();
                foreach (FileInfo fi in fileIDDic.Values)
                {
                    name.Append(fi.Name + ";");
                }
                return name.ToString().TrimEnd(';');
            }
            return null;
        }
    }

}
