﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRLastWeakMosAnaByTPDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.spinEditTPNum = new DevExpress.XtraEditors.SpinEdit();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.buttonOK = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.spinEditDistance = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.spinEditMOS = new DevExpress.XtraEditors.SpinEdit();
            this.checkEditACall = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTPNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMOS.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditACall.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // spinEditTPNum
            // 
            this.spinEditTPNum.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spinEditTPNum.Location = new System.Drawing.Point(101, 44);
            this.spinEditTPNum.Name = "spinEditTPNum";
            this.spinEditTPNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTPNum.Properties.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.spinEditTPNum.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditTPNum.Size = new System.Drawing.Size(136, 21);
            this.spinEditTPNum.TabIndex = 9;
            // 
            // buttonCancel
            // 
            this.buttonCancel.Location = new System.Drawing.Point(160, 148);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(87, 27);
            this.buttonCancel.TabIndex = 8;
            this.buttonCancel.Text = "取消";
            this.buttonCancel.UseVisualStyleBackColor = true;
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(17, 47);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(68, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "采样点数 ≥";
            // 
            // buttonOK
            // 
            this.buttonOK.Location = new System.Drawing.Point(65, 148);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 27);
            this.buttonOK.TabIndex = 6;
            this.buttonOK.Text = "确定";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(28, 17);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(57, 14);
            this.label1.TabIndex = 4;
            this.label1.Text = "MOS值 ≤";
            // 
            // spinEditDistance
            // 
            this.spinEditDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditDistance.Location = new System.Drawing.Point(101, 74);
            this.spinEditDistance.Name = "spinEditDistance";
            this.spinEditDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistance.Properties.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.spinEditDistance.Size = new System.Drawing.Size(136, 21);
            this.spinEditDistance.TabIndex = 11;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(17, 77);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(68, 14);
            this.label3.TabIndex = 10;
            this.label3.Text = "持续距离 ≥";
            // 
            // spinEditMOS
            // 
            this.spinEditMOS.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditMOS.Location = new System.Drawing.Point(101, 14);
            this.spinEditMOS.Name = "spinEditMOS";
            this.spinEditMOS.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMOS.Properties.MaxValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditMOS.Size = new System.Drawing.Size(136, 21);
            this.spinEditMOS.TabIndex = 12;
            // 
            // checkEditACall
            // 
            this.checkEditACall.Location = new System.Drawing.Point(79, 112);
            this.checkEditACall.Name = "checkEditACall";
            this.checkEditACall.Properties.Caption = "只统计同一次通话内结果";
            this.checkEditACall.Size = new System.Drawing.Size(158, 19);
            this.checkEditACall.TabIndex = 13;
            // 
            // NRLastWeakMosAnaByTPDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(267, 187);
            this.Controls.Add(this.checkEditACall);
            this.Controls.Add(this.spinEditMOS);
            this.Controls.Add(this.spinEditDistance);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.spinEditTPNum);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.label1);
            this.Name = "NRLastWeakMosAnaByTPDlg";
            this.Text = "持续弱MOS";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTPNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMOS.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditACall.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit spinEditTPNum;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button buttonOK;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit spinEditDistance;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit spinEditMOS;
        private DevExpress.XtraEditors.CheckEdit checkEditACall;
    }
}