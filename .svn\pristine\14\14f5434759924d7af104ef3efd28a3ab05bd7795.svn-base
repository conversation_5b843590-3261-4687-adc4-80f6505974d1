﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.MTGis;
using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc
{
    // 网格图层管理
    public class LtePlanningGridManager
    {
        private static LtePlanningGridManager instance;

        public static LtePlanningGridManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LtePlanningGridManager();
                }
                return instance;
            }
        }

        /// <summary>
        /// 获取指定字段的所有列值
        /// </summary>
        /// <param name="shpFileName"></param>
        /// <returns></returns>
        public List<string> GetFieldNamesFromFile(string shpFileName)
        {
            return ShapeHelper.GetFieldNamesFromFile(shpFileName);
        }

        /// <summary>
        /// 加载Shp文件，发生错误抛出异常
        /// </summary>
        /// <param name="shpFileName"></param>
        /// <param name="shpFieldName"></param>
        /// <returns>如果非空(不是null)则为警告信息</returns>
        public string LoadShapeFile(string shpFileName, string shpFieldName)
        {
            nameMopDic.Clear();
            List<string> warnings = new List<string>();

            Shapefile shp = new Shapefile();
            if (!shp.Open(shpFileName, null))
            {
                throw (new Exception(shp.get_ErrorMsg(shp.LastErrorCode)));
            }

            int fieldIndex = -1;
            fieldIndex = getFieldIndex(shpFieldName, shp, fieldIndex);
            if (fieldIndex == -1)
            {
                shp.Close();
                throw (new Exception(string.Format("Field Name [{0}] Not Found", shpFieldName)));
            }

            addNameMopDic(warnings, shp, fieldIndex);
            shp.Close();

            // result
            StringBuilder sb = new StringBuilder();
            foreach (string w in warnings)
            {
                sb.Append(w + Environment.NewLine);
            }
            if (sb.Length > 0)
            {
                sb.Insert(0, "发生以下错误，是否继续？" + Environment.NewLine);
            }
            return sb.ToString();
        }

        private void addNameMopDic(List<string> warnings, Shapefile shp, int fieldIndex)
        {
            bool foundUnnamed = false;
            bool foundSameName = false;
            for (int i = 0; i < shp.NumShapes; ++i)
            {
                string name = shp.get_CellValue(fieldIndex, i) as string;
                bool isValid = IsValidName(warnings, ref foundUnnamed, ref foundSameName, name);
                if (isValid)
                {
                    Shape shape = shp.get_Shape(i);
                    MapOperation2 mop2 = new MapOperation2();
                    mop2.FillPolygon(shape);
                    nameMopDic.Add(name, mop2);
                }
            }
        }

        private bool IsValidName(List<string> warnings, ref bool foundUnnamed, ref bool foundSameName, string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                if (!foundUnnamed)
                {
                    warnings.Add("存在未命名网格");
                    foundUnnamed = true;
                }
            }
            else
            {
                if (nameMopDic.ContainsKey(name))
                {
                    if (!foundSameName)
                    {
                        warnings.Add("存在同名网格");
                        foundSameName = true;
                    }
                }
                else
                {
                    return true;
                }
            }
            return false;
        }

        private static int getFieldIndex(string shpFieldName, Shapefile shp, int fieldIndex)
        {
            for (int i = 0; i < shp.NumFields; ++i)
            {
                Field field = shp.get_Field(i);
                if (field != null && field.Name == shpFieldName)
                {
                    fieldIndex = i;
                    break;
                }
            }

            return fieldIndex;
        }

        /// <summary>
        /// 获取经纬点所在网格名
        /// </summary>
        /// <param name="lng"></param>
        /// <param name="lat"></param>
        /// <returns></returns>
        public string GetLocation(double lng, double lat)
        {
            foreach (KeyValuePair<string, MapOperation2> kvp in nameMopDic)
            {
                if (kvp.Value.CheckPointInRegion(lng, lat))
                {
                    return kvp.Key;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取所有网格名
        /// </summary>
        /// <returns></returns>
        public List<string> GetRegionList()
        {
            return new List<string>(nameMopDic.Keys);
        }

        private LtePlanningGridManager()
        {
            nameMopDic = new Dictionary<string, MapOperation2>();
        }

        private readonly Dictionary<string, MapOperation2> nameMopDic;
    }
}
