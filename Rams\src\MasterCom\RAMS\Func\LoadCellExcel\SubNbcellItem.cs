using System;
using System.Collections.Generic;
using System.Text;

namespace  MasterCom.RAMS.Func.LoadCellExcel
{
    public class SubNbcellItem
    {
        public string strDestCgi { get; set; }
        public int iAdjacencyType { get; set; }


        public override bool Equals(object obj)
        {
            SubNbcellItem other = obj as SubNbcellItem;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strDestCgi.Equals(other.strDestCgi)
                    && this.iAdjacencyType.Equals(other.iAdjacencyType));
        }

        public override int GetHashCode()
        {
            return this.strDestCgi.GetHashCode();
        }
    }
}
