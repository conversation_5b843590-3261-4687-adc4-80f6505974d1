﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedCellDlg_TD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.chkSaveTestPoint = new DevExpress.XtraEditors.CheckEdit();
            this.numMinDPCH_C2I = new DevExpress.XtraEditors.SpinEdit();
            this.numMaxDPCH_C2I = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numMinSpeed = new DevExpress.XtraEditors.SpinEdit();
            this.numMinPCCPCH_C2I = new DevExpress.XtraEditors.SpinEdit();
            this.numMaxRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.numMaxPCCPCH_C2I = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxSpeed = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.btnMultiCover = new DevExpress.XtraEditors.SimpleButton();
            this.edtMultiCover = new DevExpress.XtraEditors.TextEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.btnWeakCover = new DevExpress.XtraEditors.SimpleButton();
            this.edtWeakCover = new DevExpress.XtraEditors.TextEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDPCH_C2I.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDPCH_C2I.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSpeed.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPCCPCH_C2I.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxPCCPCH_C2I.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSpeed.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtMultiCover.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtWeakCover.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(260, 282);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(72, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(345, 282);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(72, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            // 
            // groupControl1
            // 
            this.groupControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupControl1.Appearance.Options.UseFont = true;
            this.groupControl1.Controls.Add(this.labelControl10);
            this.groupControl1.Controls.Add(this.labelControl9);
            this.groupControl1.Controls.Add(this.chkSaveTestPoint);
            this.groupControl1.Controls.Add(this.numMinDPCH_C2I);
            this.groupControl1.Controls.Add(this.numMaxDPCH_C2I);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.numMinSpeed);
            this.groupControl1.Controls.Add(this.numMinPCCPCH_C2I);
            this.groupControl1.Controls.Add(this.numMaxRxLev);
            this.groupControl1.Controls.Add(this.numMaxPCCPCH_C2I);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Controls.Add(this.labelControl4);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.numMaxSpeed);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.numMinRxLev);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Location = new System.Drawing.Point(32, 12);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(385, 158);
            this.groupControl1.TabIndex = 10;
            this.groupControl1.Text = "基本条件设置";
            // 
            // chkSaveTestPoint
            // 
            this.chkSaveTestPoint.Location = new System.Drawing.Point(57, 134);
            this.chkSaveTestPoint.Name = "chkSaveTestPoint";
            this.chkSaveTestPoint.Properties.Caption = "保留采样点";
            this.chkSaveTestPoint.Size = new System.Drawing.Size(101, 19);
            this.chkSaveTestPoint.TabIndex = 32;
            // 
            // numMinDPCH_C2I
            // 
            this.numMinDPCH_C2I.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numMinDPCH_C2I.Location = new System.Drawing.Point(59, 80);
            this.numMinDPCH_C2I.Name = "numMinDPCH_C2I";
            this.numMinDPCH_C2I.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinDPCH_C2I.Properties.Appearance.Options.UseFont = true;
            this.numMinDPCH_C2I.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinDPCH_C2I.Properties.IsFloatValue = false;
            this.numMinDPCH_C2I.Properties.Mask.EditMask = "N00";
            this.numMinDPCH_C2I.Size = new System.Drawing.Size(63, 20);
            this.numMinDPCH_C2I.TabIndex = 31;
            // 
            // numMaxDPCH_C2I
            // 
            this.numMaxDPCH_C2I.EditValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numMaxDPCH_C2I.Location = new System.Drawing.Point(226, 80);
            this.numMaxDPCH_C2I.Name = "numMaxDPCH_C2I";
            this.numMaxDPCH_C2I.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxDPCH_C2I.Properties.Appearance.Options.UseFont = true;
            this.numMaxDPCH_C2I.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxDPCH_C2I.Properties.IsFloatValue = false;
            this.numMaxDPCH_C2I.Properties.Mask.EditMask = "N00";
            this.numMaxDPCH_C2I.Size = new System.Drawing.Size(63, 20);
            this.numMaxDPCH_C2I.TabIndex = 29;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(128, 83);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(90, 12);
            this.labelControl5.TabIndex = 30;
            this.labelControl5.Text = "≤ DPCH_C/I  ≤";
            // 
            // numMinSpeed
            // 
            this.numMinSpeed.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numMinSpeed.Location = new System.Drawing.Point(59, 107);
            this.numMinSpeed.Name = "numMinSpeed";
            this.numMinSpeed.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinSpeed.Properties.Appearance.Options.UseFont = true;
            this.numMinSpeed.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinSpeed.Properties.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMinSpeed.Properties.IsFloatValue = false;
            this.numMinSpeed.Properties.Mask.EditMask = "N00";
            this.numMinSpeed.Size = new System.Drawing.Size(63, 20);
            this.numMinSpeed.TabIndex = 28;
            // 
            // numMinPCCPCH_C2I
            // 
            this.numMinPCCPCH_C2I.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numMinPCCPCH_C2I.Location = new System.Drawing.Point(59, 53);
            this.numMinPCCPCH_C2I.Name = "numMinPCCPCH_C2I";
            this.numMinPCCPCH_C2I.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinPCCPCH_C2I.Properties.Appearance.Options.UseFont = true;
            this.numMinPCCPCH_C2I.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinPCCPCH_C2I.Properties.IsFloatValue = false;
            this.numMinPCCPCH_C2I.Properties.Mask.EditMask = "N00";
            this.numMinPCCPCH_C2I.Size = new System.Drawing.Size(63, 20);
            this.numMinPCCPCH_C2I.TabIndex = 27;
            // 
            // numMaxRxLev
            // 
            this.numMaxRxLev.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMaxRxLev.Location = new System.Drawing.Point(226, 26);
            this.numMaxRxLev.Name = "numMaxRxLev";
            this.numMaxRxLev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxRxLev.Properties.Appearance.Options.UseFont = true;
            this.numMaxRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRxLev.Properties.IsFloatValue = false;
            this.numMaxRxLev.Properties.Mask.EditMask = "N00";
            this.numMaxRxLev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMaxRxLev.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numMaxRxLev.Size = new System.Drawing.Size(63, 20);
            this.numMaxRxLev.TabIndex = 26;
            // 
            // numMaxPCCPCH_C2I
            // 
            this.numMaxPCCPCH_C2I.EditValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numMaxPCCPCH_C2I.Location = new System.Drawing.Point(226, 53);
            this.numMaxPCCPCH_C2I.Name = "numMaxPCCPCH_C2I";
            this.numMaxPCCPCH_C2I.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxPCCPCH_C2I.Properties.Appearance.Options.UseFont = true;
            this.numMaxPCCPCH_C2I.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxPCCPCH_C2I.Properties.IsFloatValue = false;
            this.numMaxPCCPCH_C2I.Properties.Mask.EditMask = "N00";
            this.numMaxPCCPCH_C2I.Size = new System.Drawing.Size(63, 20);
            this.numMaxPCCPCH_C2I.TabIndex = 20;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(128, 56);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(90, 12);
            this.labelControl6.TabIndex = 25;
            this.labelControl6.Text = "≤ PCCPCH_C/I≤";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(295, 110);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 12);
            this.labelControl4.TabIndex = 24;
            this.labelControl4.Text = "Kbps";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(295, 29);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 23;
            this.labelControl3.Text = "dBm";
            // 
            // numMaxSpeed
            // 
            this.numMaxSpeed.EditValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numMaxSpeed.Location = new System.Drawing.Point(226, 107);
            this.numMaxSpeed.Name = "numMaxSpeed";
            this.numMaxSpeed.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxSpeed.Properties.Appearance.Options.UseFont = true;
            this.numMaxSpeed.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSpeed.Properties.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSpeed.Properties.IsFloatValue = false;
            this.numMaxSpeed.Properties.Mask.EditMask = "N00";
            this.numMaxSpeed.Size = new System.Drawing.Size(63, 20);
            this.numMaxSpeed.TabIndex = 21;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(128, 110);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(90, 12);
            this.labelControl2.TabIndex = 22;
            this.labelControl2.Text = "≤    速率   ≤";
            // 
            // numMinRxLev
            // 
            this.numMinRxLev.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Location = new System.Drawing.Point(59, 26);
            this.numMinRxLev.Name = "numMinRxLev";
            this.numMinRxLev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinRxLev.Properties.Appearance.Options.UseFont = true;
            this.numMinRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxLev.Properties.IsFloatValue = false;
            this.numMinRxLev.Properties.Mask.EditMask = "N00";
            this.numMinRxLev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Size = new System.Drawing.Size(63, 20);
            this.numMinRxLev.TabIndex = 18;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(121, 29);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(96, 12);
            this.labelControl1.TabIndex = 19;
            this.labelControl1.Text = " ≤    场强   ≤";
            // 
            // groupControl2
            // 
            this.groupControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupControl2.Appearance.Options.UseFont = true;
            this.groupControl2.Controls.Add(this.btnMultiCover);
            this.groupControl2.Controls.Add(this.edtMultiCover);
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.btnWeakCover);
            this.groupControl2.Controls.Add(this.edtWeakCover);
            this.groupControl2.Controls.Add(this.labelControl7);
            this.groupControl2.Location = new System.Drawing.Point(32, 184);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(385, 80);
            this.groupControl2.TabIndex = 11;
            this.groupControl2.Text = "栅格设置(40*40米，含Longitude、Latitude两列)";
            // 
            // btnMultiCover
            // 
            this.btnMultiCover.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnMultiCover.Appearance.Options.UseFont = true;
            this.btnMultiCover.Location = new System.Drawing.Point(345, 52);
            this.btnMultiCover.Name = "btnMultiCover";
            this.btnMultiCover.Size = new System.Drawing.Size(27, 23);
            this.btnMultiCover.TabIndex = 5;
            this.btnMultiCover.Text = "...";
            this.btnMultiCover.Click += new System.EventHandler(this.btnMultiCover_Click);
            // 
            // edtMultiCover
            // 
            this.edtMultiCover.Location = new System.Drawing.Point(128, 53);
            this.edtMultiCover.Name = "edtMultiCover";
            this.edtMultiCover.Size = new System.Drawing.Size(205, 21);
            this.edtMultiCover.TabIndex = 4;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(14, 56);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(108, 12);
            this.labelControl8.TabIndex = 3;
            this.labelControl8.Text = "重叠覆盖栅格图层：";
            // 
            // btnWeakCover
            // 
            this.btnWeakCover.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnWeakCover.Appearance.Options.UseFont = true;
            this.btnWeakCover.Location = new System.Drawing.Point(345, 25);
            this.btnWeakCover.Name = "btnWeakCover";
            this.btnWeakCover.Size = new System.Drawing.Size(27, 23);
            this.btnWeakCover.TabIndex = 2;
            this.btnWeakCover.Text = "...";
            this.btnWeakCover.Click += new System.EventHandler(this.btnWeakCover_Click);
            // 
            // edtWeakCover
            // 
            this.edtWeakCover.Location = new System.Drawing.Point(128, 26);
            this.edtWeakCover.Name = "edtWeakCover";
            this.edtWeakCover.Size = new System.Drawing.Size(205, 21);
            this.edtWeakCover.TabIndex = 1;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(26, 29);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(96, 12);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "弱覆盖栅格图层：";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(295, 56);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(12, 12);
            this.labelControl9.TabIndex = 33;
            this.labelControl9.Text = "dB";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(295, 83);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(12, 12);
            this.labelControl10.TabIndex = 34;
            this.labelControl10.Text = "dB";
            // 
            // LowSpeedCellDlg_TD
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(449, 317);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "LowSpeedCellDlg_TD";
            this.Text = "低速率小区条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDPCH_C2I.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDPCH_C2I.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSpeed.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPCCPCH_C2I.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxPCCPCH_C2I.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSpeed.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtMultiCover.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtWeakCover.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.CheckEdit chkSaveTestPoint;
        private DevExpress.XtraEditors.SpinEdit numMinDPCH_C2I;
        private DevExpress.XtraEditors.SpinEdit numMaxDPCH_C2I;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numMinSpeed;
        private DevExpress.XtraEditors.SpinEdit numMinPCCPCH_C2I;
        private DevExpress.XtraEditors.SpinEdit numMaxRxLev;
        private DevExpress.XtraEditors.SpinEdit numMaxPCCPCH_C2I;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numMaxSpeed;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numMinRxLev;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnWeakCover;
        private DevExpress.XtraEditors.TextEdit edtWeakCover;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SimpleButton btnMultiCover;
        private DevExpress.XtraEditors.TextEdit edtMultiCover;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl9;
    }
}