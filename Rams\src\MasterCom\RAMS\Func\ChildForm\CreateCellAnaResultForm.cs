﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.Func
{
    public class CreateCellAnaResultForm : CreateChildForm
    {
        public CreateCellAnaResultForm(MainModel mm)
            : base(mm)
        {
        }
        public override string Description
        {
            get
            {
                return "创建小区综合分析窗口 CellAnaResultForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20051, this.Name);           
        }
        public override string Name
        {
            get
            {
                return "创建小区综合分析窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.CellAnaResultForm";
            actionParam["Text"] = "小区综合分析（正在加载）";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
