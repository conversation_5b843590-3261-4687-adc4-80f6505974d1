﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTMainCellLastOccupySetGSMConditionForm : BaseDialog
    {
        public ZTMainCellLastOccupySetGSMConditionForm()
        {
            InitializeComponent();
        }

        public ZTMainCellLastOccupySetCondition GetCondition()
        {
            ZTMainCellLastOccupySetCondition condition = new ZTMainCellLastOccupySetCondition();
            condition.RxLev = (short)numSiteRxLev.Value;
            condition.RxQul = (byte)numSiteRxQul.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
