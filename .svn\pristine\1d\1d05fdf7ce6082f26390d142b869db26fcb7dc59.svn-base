﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Model.Interface
{
    /// <summary>
    /// Written by <PERSON><PERSON><PERSON><PERSON><PERSON> 2012.7.31
    /// </summary>
    public class ZTDIYCellOccupationAnaByDTAndScanByRegion : QueryBase
    {
        public ZTDIYCellOccupationAnaByDTAndScanByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "小区占用对比分析(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12069, this.Name);
        }

        public MapFormItemSelection ItemSelection { get; set; }
        protected override void query()
        {
            SetQueryForm setQueryForm = new SetQueryForm(MainModel, ItemSelection, Condition);
            setQueryForm.Show(MainModel.MainForm);
        }
    }

}
