﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Func.PopShow
{
    
    public partial class KPIInfoPanel : UserControl, PopShowPanelInterface
    {
        //第一命令字：config
        public const byte REQTYPE_CONFIG_KPI_TABLE = 0x2b;  //REQUEST
        public const byte RESTYPE_CONFIG_KPI_TABLE = 0x2b;

        //第一命令字：查询    启动预读
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 
        private MainModel MainModel;
        public Dictionary<string, Object> colorRangeDic { get; set; }
        public Dictionary<string, AlarmCfgItem> alarmCfgDic { get; set; }

        public KPIInfoPanel()
        {
            InitializeComponent();
#if PopShow_KPI_Color
            btnColor.Visible = true;
#endif
            initShowInfo();

            curRetDataDic = new Dictionary<string, KPIPopTable>();
        }

        private void initShowInfo()
        {
            cbxShowType.Items.Add("最近一周");
            cbxShowType.Items.Add("最近一月");
            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.Items.Add("按天");
            cbxShowType.SelectedIndex = 0;
        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            Dictionary<string, KPIPopTable> entryHeaderDic = queryPopEntry(worker);
            foreach (string tbkey in entryHeaderDic.Keys)
            {
                KPIPopTable hdUnit = entryHeaderDic[tbkey];
                hdUnit.initFinderDic();
                List<KPIResultInfo> resultList = queryResultFromHeader(worker, hdUnit, MainModel.User.DBID);
                hdUnit.dataResult = resultList;
            }
            task.retResultInfo = entryHeaderDic;
           
        }

        private List<KPIResultInfo> queryResultFromHeader(BackgroundWorker worker, KPIPopTable hdUnit,int dbid)
        {
            List<KPIResultInfo> retList = new List<KPIResultInfo>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam(hdUnit.tablename);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        KPIResultInfo retItem = hdUnit.ReadResultItemFrom(package.Content);
                        if(dbid ==-1)//若为多地市的，需要将片区名修改为地市名
                        {
                            retItem.strname = DistrictManager.GetInstance().getDistrictName(retItem.dbid);
                        }
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }

        }
        private Dictionary<string, KPIPopTable> queryPopEntry(BackgroundWorker worker)
        {
            DiySqlPopKpiColor kpiColorQeruyTask = new DiySqlPopKpiColor(MainModel);
            kpiColorQeruyTask.Query();
            colorRangeDic = kpiColorQeruyTask.colorRangeDic;

            DiySqlPopKpiAlarmCfg kpiAlarmCfgrQeruyTask = new DiySqlPopKpiAlarmCfg(MainModel);
            kpiAlarmCfgrQeruyTask.Query();
            alarmCfgDic = kpiAlarmCfgrQeruyTask.alarmCfgDic;

            Dictionary<string, KPIPopTable> entryDicList = new Dictionary<string, KPIPopTable>();
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99,"连接服务器端出错！");
                return entryDicList;
            }
            try
            {
                Package package = clientProxy.Package;
                // get cellDefine;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_CONFIG_KPI_TABLE;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_CONFIG_KPI_TABLE)
                    {
                        package.Content.PrepareGetParam();
                        PopKPIEntryItem entry = new PopKPIEntryItem();
                        entry.Fill(package.Content);
                        addEntryDicList(kpiColorQeruyTask, entryDicList, entry);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return entryDicList;
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void addEntryDicList(DiySqlPopKpiColor kpiColorQeruyTask, Dictionary<string, KPIPopTable> entryDicList, PopKPIEntryItem entry)
        {
            if (entry.strtablename == "tb_popblackblock"
                || entry.strtablename == "tb_pop_esinsight"
                || entry.strtablename == "tb_pop_compbench_status"
                || entry.strtablename == "tb_pop_compbench_unit")//特殊的供其它程序使用的，不在KPI显示处显示
            {
                //
            }
            else
            {
                KPIPopTable headerUnit = null;
                if (!entryDicList.TryGetValue(entry.strtablename, out headerUnit))
                {
                    headerUnit = new KPIPopTable();
                    headerUnit.tablename = entry.strtablename;
                    entryDicList[headerUnit.tablename] = headerUnit;
                }
                string key = entry.strtablename + entry.strcolname;
                if (kpiColorQeruyTask.colorRangeDic.ContainsKey(key))
                {
                    entry.colorLst = kpiColorQeruyTask.colorRangeDic[key] as List<DTParameterRangeColor>;
                }
                headerUnit.entryList.Add(entry);
            }
        }
        #endregion

        #region PopShowPanelInterface 成员

        public Dictionary<string, KPIPopTable> curRetDataDic { get; set; }
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is Dictionary<string, KPIPopTable>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, KPIPopTable>;
            }
            cbxReportSel.Items.Clear();
            foreach(KPIPopTable popTable in curRetDataDic.Values)
            {
                cbxReportSel.Items.Add(popTable);
            }
            if(cbxReportSel.Items.Count>0)
            {
                cbxReportSel.SelectedIndex = 0;
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion

        private void cbxReportSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport(true);
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selShowType = cbxShowType.SelectedItem as string;
            if(selShowType==null)
            {
                return;
            }
            refreshShowReport(false);
        }
        private void refreshShowReport(bool resetContent)
        {
            dataGridView.Columns.Clear();
            KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
            string selShowType = cbxShowType.SelectedItem as string;
            if(kpiPopTable!=null && selShowType!=null)
            {
                FinalShowResult showRet = parseShowFromTable(kpiPopTable,selShowType);
                if(resetContent)
                {
                    Dictionary<string, bool> namesDic = getNameList(showRet);
                    cbxContentType.Items.Clear();
                    cbxContentType.Items.Add("(全部)");
                    foreach (string nm in namesDic.Keys)
                    {
                        cbxContentType.Items.Add(nm);
                    }
                    cbxContentType.SelectedIndex = 0;
                }
                showInGrid(showRet);
            }
        }

        private Dictionary<string, bool> getNameList(FinalShowResult showRet)
        {
            Dictionary<string, bool> ret = new Dictionary<string, bool>();
            foreach (List<object> vList in showRet.dataRows)
            {
                ret[vList[1] as string] = true;
            }
            return ret;
        }

        private FinalShowResult parseShowFromTable(KPIPopTable kpiPopTable, string selShowType)
        {
            FinalShowResult sRet = new FinalShowResult();
            if (selShowType == "按天")
            {
                sRet = prepareShowByDay(kpiPopTable);
            }
            else if (selShowType == "按月")
            {
                sRet = prepareShowByMonth(kpiPopTable);
            }
            else if (selShowType == "按周")
            {
                sRet = prepareShowByWeek(kpiPopTable);
            }
            else if(selShowType == "最近一月")
            {
                sRet = prepareShowByLastMonth(kpiPopTable);

            }
            else if (selShowType == "最近一周")
            {
                sRet = prepareShowByLastWeek(kpiPopTable);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByDay(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            foreach (KPIResultInfo retInfo in kpiPopTable.dataResult)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(retInfo.stime * 1000L);
                objList.Add(stimeDate.ToString("yyyy.MM.dd"));
                objList.Add(retInfo.strname);
                for (int i = 0; i < retInfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = retInfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult setResultBase(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }

            return sRet;
        }

        private FinalShowResult prepareShowByMonth(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                objList.Add(stimeDate.ToString("yyyy-MM"));
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByLastMonth(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getMonthBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                    objList.Add(stimeDate.ToString("yyyy-MM"));
                    objList.Add(ginfo.strname);
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                }
            }
            return sRet;
        }

        private FinalShowResult prepareShowByWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByLastWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = setResultBase(kpiPopTable);
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getWeekBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if(ginfo.stime== maxstime)
                {
                    List<object> objList = new List<object>();
                    objList.Add(getWeekStr(ginfo.stime));
                    objList.Add(ginfo.strname);
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                }
            }
            return sRet;
        }

        private int getWeekBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            long seconds = JavaDate.GetMilliseconds(dtbegin)/1000;
            return (int)seconds;
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch(dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy =0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case  DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }
        private int getMonthBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            return (int)(JavaDate.GetMilliseconds(dtbegin)/1000);
        }

        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private void showInGrid(FinalShowResult showRet)
        {
            dataGridView.Rows.Clear();
            dataGridView.Columns.Clear();
            for (int i = 0; i < showRet.columnNames.Count; i++)
            {
                string columnname = showRet.columnNames[i];
                dataGridView.Columns.Add("Column" + i, columnname);
            }
            dataGridView.Columns[0].SortMode = DataGridViewColumnSortMode.Programmatic;
            if(showRet.dataRows.Count>0)
            {
                int indexRowAt = 0;
                for (int r = 0; r < showRet.dataRows.Count; r++)
                {
                    List<object> dataRow = showRet.dataRows[r];
                    if (((string)cbxContentType.SelectedItem) == "(全部)" || ((string)cbxContentType.SelectedItem)== (string)dataRow[1])
                    {
                        dataGridView.Rows.Add(1);
                        setDataGridViewValue(indexRowAt, dataRow);
                        indexRowAt++;
                    }
                }
            }
            dataGridView.Sort(dataGridView.Columns[0], ListSortDirection.Ascending);
            dataGridView.Columns[0].Frozen = true;
            dataGridView.Columns[1].Frozen = true;
        }

        private void setDataGridViewValue(int indexRowAt, List<object> dataRow)
        {
            for (int c = 0; c < dataRow.Count; c++)
            {
                object dv = dataRow[c];
                if (dv is DateTime)
                {
                    dataGridView.Rows[indexRowAt].Cells[c].Value = ((DateTime)dv).ToString("yyyy-MM-dd");
                }
                else
                {
                    dataGridView.Rows[indexRowAt].Cells[c].Value = dataRow[c];
#if PopShow_KPI_Color
                    string selShowType = cbxShowType.SelectedItem as string;
                    if (selShowType.Contains("周"))
                    {
                        KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                        string key = kpiPopTable.tablename;
                        foreach (PopKPIEntryItem item in kpiPopTable.entryList)
                        {
                            if (item.strcoldesc == showRet.columnNames[c])
                            {
                                key += item.strcolname;
                                break;
                            }
                        }
                        if (colorRangeDic!=null && colorRangeDic.ContainsKey(key))
                        {
                            List<DTParameterRangeColor> colorRangeLst = colorRangeDic[key] as List<DTParameterRangeColor>;
                            foreach (DTParameterRangeColor colorRange in colorRangeLst)
                            {
                                if (colorRange.Within(float.Parse(dataRow[c].ToString())))
                                {
                                    dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorRange.Value;
                                    dataGridView.Rows[indexRowAt].Cells[c].ToolTipText = colorRange.DesInfo;

                                    break;
                                }
                            }
                        }
                    }
#endif
                }
            }
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport(false);
            freshShowChart(2);
        }
        private void dataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex >= 2 && e.ColumnIndex<dataGridView.Columns.Count) 
            {
                freshShowChart(e.ColumnIndex);
            }
        }
        private void freshShowChart(int colIndex)
        {
            bar.Clear();
            string selShowType = cbxShowType.SelectedItem as string;
            if (selShowType == "最近一周" || selShowType == "最近一月")
            {
                string text = "名称";
                setChartInfo(colIndex, text, 0, 1);
            }
            else if (selShowType == "按周" || selShowType == "按月" || selShowType == "按天")
            {
                string showCont = cbxContentType.SelectedItem as string;
                if(showCont=="(全部)")//显示多个序列
                {
                    setAllChartInfo(colIndex);
                }
                else
                {
                    string text = showCont + " " + selShowType + " 变化情况";
                    setChartInfo(colIndex, text, 45, 0);
                }
            }
        }

        private void setAllChartInfo(int colIndex)
        {
            tChartKPI.Series.Clear();
            Dictionary<string, Steema.TeeChart.Styles.Bar> areaSeriesDic = new Dictionary<string, Steema.TeeChart.Styles.Bar>();
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                string namelabel = dataGridView.Rows[r].Cells[1].Value.ToString();//名称列
                Steema.TeeChart.Styles.Bar nmBar;
                if (!areaSeriesDic.TryGetValue(namelabel, out nmBar))
                {
                    nmBar = new Steema.TeeChart.Styles.Bar();
                    nmBar.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;
                    //nmBar.Marks.Visible = false;
                    nmBar.Title = namelabel;
                    nmBar.Depth = 5;
                    tChartKPI.Series.Add(nmBar);
                    areaSeriesDic[namelabel] = nmBar;
                }
                double vdouble = 0;
                string labelStr = "";
                extractLabValue(r, colIndex, 0, out vdouble, out labelStr);
                nmBar.Add(vdouble, labelStr);
            }
        }

        private void setChartInfo(int colIndex, string text, int angle, int lableCol)
        {
            tChartKPI.Series.Clear();
            Steema.TeeChart.Styles.Bar newBar = new Steema.TeeChart.Styles.Bar();
            newBar.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Value;
            tChartKPI.Series.Add(newBar);
            tChartKPI.Header.Text = cbxReportSel.SelectedText;
            tChartKPI.Axes.Bottom.Title.Text = text;
            tChartKPI.Axes.Bottom.Labels.Angle = angle;
            tChartKPI.Axes.Left.Title.Text = dataGridView.Columns[colIndex].HeaderText;
            double[] doubles;
            string[] labels;
            extractLabValues(colIndex, lableCol, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                tChartKPI.Series[0].Add(doubles[i], labels[i]);
            }
        }

        private void extractLabValue(int rowAt,int vColumn, int labelColumn, out double doublev, out string labelv)
        {
            doublev = 0;
            object v = dataGridView.Rows[rowAt].Cells[vColumn].Value;
            if (v is int)
            {
                doublev = (int)v;
            }
            else if (v is double)
            {
                doublev = (double)v;
            }
            else if (v is float)
            {
                doublev = (float)v;
            }
            string vlabel = dataGridView.Rows[rowAt].Cells[labelColumn].Value.ToString();
            labelv = vlabel;
            
        }
        private void extractLabValues(int vColumn, int labelColumn,out double[] doubles, out string[] labels)
        {
            doubles = new double[dataGridView.Rows.Count];
            labels = new string[dataGridView.Rows.Count];
            for(int r = 0;r<dataGridView.Rows.Count;r++)
            {
                object v = dataGridView.Rows[r].Cells[vColumn].Value;
                if(v is int)
                {
                    doubles[r] = (int)v;
                }
                else if (v is double)
                {
                    doubles[r] = (double)v;
                }
                else if (v is float)
                {
                    doubles[r] = (float)v;
                }
                string vlabel = dataGridView.Rows[r].Cells[labelColumn].Value.ToString();
                labels[r] = vlabel;
            }
        }

        private void btnPopShow_Click(object sender, EventArgs e)
        {
            if(!splitMain.Panel2Collapsed)
            {
                ChartShowDialogKPI dlg = new ChartShowDialogKPI(this);
                splitMain.Panel2.Controls.Remove(tChartKPI);
                splitMain.Panel2Collapsed = true;
                dlg.AppendTeeChart(tChartKPI);
                dlg.Visible = true;
                dlg.Owner = (Form)this.Parent.Parent.Parent;
            }
        }

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMain.Panel2.Controls.Add(tchart);
            splitMain.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }
    }

    public class PopKPIEntryItem
    {
        public int iidx { get; set; }
        public string strcolname { get; set; }
        public string strcoldesc { get; set; }
        public int icoltype { get; set; }
        public string strtablename { get; set; }
        public string strrptname { get; set; }
        public List<DTParameterRangeColor> colorLst { get; set; }

        internal void Fill(Content content)
        {
            iidx = content.GetParamInt();
            strcolname = content.GetParamString();
            strcoldesc = content.GetParamString();
            icoltype = content.GetParamInt();
            strtablename = content.GetParamString();
            strrptname = content.GetParamString();
        }
        public override string ToString()
        {
            return iidx + "," + strcolname + "," + strcoldesc + "," + icoltype + "," + strtablename +","+ strrptname;
        }
    }

    public class KPIPopTable
    {
        public KPIPopTable()
        {
            keyIndexDic = new Dictionary<string, int>();
            entryList = new List<PopKPIEntryItem>();
            dataResult = new List<KPIResultInfo>();
            cityDataResult = new Dictionary<string, List<KPIResultInfo>>();
            icoltype5NeedDivide1000 = true;
        }

        public string tablename { get; set; }
        public Dictionary<string, int> keyIndexDic { get; set; }
        public List<PopKPIEntryItem> entryList { get; set; }//列头描述
        public List<KPIResultInfo> dataResult { get; set; }//old for single city
        public Dictionary<string, List<KPIResultInfo>> cityDataResult { get; set; }

        public bool icoltype5NeedDivide1000 { get; set; }  //icoltype等于5的类型的字段，处理输出时，是否需要除以1000

        internal int SortA(PopKPIEntryItem a1, PopKPIEntryItem a2)
        {
            return a1.iidx.CompareTo(a2.iidx);
        }

        internal void Sort()
        {
            entryList.Sort(SortA);
        }
        
        internal KPIResultInfo ReadResultItemFrom(Content content)
        {
            KPIResultInfo kpiRet = new KPIResultInfo();
            kpiRet.dbid = content.GetParamInt();
            kpiRet.stime = content.GetParamInt();
            kpiRet.etime = content.GetParamInt();
            kpiRet.strname = content.GetParamString();
            for(int i=4;i<entryList.Count;i++)
            {
                PopKPIEntryItem entry = entryList[i];
                if(entry.icoltype==5)
                {
                    if(icoltype5NeedDivide1000)
                        kpiRet.valueList.Add(content.GetParamFloat()/1000);
                    else
                        kpiRet.valueList.Add(content.GetParamFloat());
                }
                else if(entry.icoltype ==8)
                {
                    kpiRet.valueList.Add(content.GetParamInt());
                }
                else if(entry.icoltype ==6)
                {
                    kpiRet.valueList.Add(content.GetParamString());
                }
            }
            return kpiRet;
        }
        public override string ToString()
        {
            if(entryList.Count>0)
            {
                return entryList[0].strrptname;
            }
            return "";
        }

        internal void initFinderDic()
        {
            for (int i=4;i<entryList.Count;i++)
            {
                keyIndexDic[entryList[i].strcolname] = i-4;
            }
        }

        internal void reloadDataResultByLevel(bool isProvUser, string cityName, string selShowType)
        {
            reloadDataResultByLevel(isProvUser, cityName, selShowType, false);
        }

        internal void reloadDataResult(bool isProvUser, string cityName, string selShowType)
        {
            dataResult.Clear();
            if (isProvUser)
            {
                addProvUserRes(cityName);
            }
            else
            {
                addOtherUserRes(selShowType);
            }
        }

        private void addProvUserRes(string cityName)
        {
            if (cityName == "")//全部，需要汇聚地市
            {
                foreach (string cname in cityDataResult.Keys)
                {
                    List<KPIResultInfo> listOfCity = cityDataResult[cname];
                    foreach (KPIResultInfo crInfo in listOfCity)
                    {
                        dataResult.Add(crInfo);
                    }
                }
            }
            else
            {
                List<KPIResultInfo> list;
                if (cityDataResult.TryGetValue(cityName, out list))
                {
                    foreach (KPIResultInfo kinfo in list)
                    {
                        dataResult.Add(kinfo);
                    }
                }
            }
        }

        private void addOtherUserRes(string selShowType)
        {
            foreach (List<KPIResultInfo> list in cityDataResult.Values)
            {
                if (selShowType == "")
                {
                    dataResult.AddRange(list);//only one
                    break;
                }
                else
                {
                    foreach (KPIResultInfo kinfo in list)
                    {
                        dataResult.Add(kinfo);
                    }
                }
            }
        }

        internal void reloadDataResultByLevel(bool isProvUser, string cityName, string selShowType, bool gatherByRegion)
        {
            dataResult.Clear();
            if (isProvUser)
            {
                dealMaster(cityName, selShowType, gatherByRegion);
            }
            else
            {
                dealGeneral(selShowType);
            }
        }

        private void dealMaster(string cityName, string selShowType, bool gatherByRegion)
        {
            if (cityName == "")//全部，需要汇聚地市
            {
                converageCityRes(selShowType, gatherByRegion);
            }
            else
            {
                List<KPIResultInfo> list;
                if (cityDataResult.TryGetValue(cityName, out list))
                {
                    foreach (KPIResultInfo kinfo in list)
                    {
                        if (kinfo.isValidSegment(selShowType))
                        {
                            dataResult.Add(kinfo);
                        }
                    }
                }
            }
        }

        private void converageCityRes(string selShowType, bool gatherByRegion)
        {
            foreach (string cname in cityDataResult.Keys)
            {
                List<KPIResultInfo> listOfCity = cityDataResult[cname];
                Dictionary<string, KPIResultInfo> combinedCityDic = new Dictionary<string, KPIResultInfo>();//string(stime+etime) -> Ret
                foreach (KPIResultInfo crInfo in listOfCity)
                {
                    if (crInfo.isValidSegment(selShowType))
                    {
                        addCombinedCity(gatherByRegion, cname, combinedCityDic, crInfo);
                    }

                }
                foreach (KPIResultInfo info in combinedCityDic.Values)
                {
                    dataResult.Add(info);
                }
            }
        }

        private void addCombinedCity(bool gatherByRegion, string cname, Dictionary<string, KPIResultInfo> combinedCityDic, KPIResultInfo crInfo)
        {
            string timeStr = "";
            if (gatherByRegion)
            {
                timeStr = crInfo.stime + ":" + crInfo.etime + ":" + crInfo.strname;
            }
            else
            {
                timeStr = crInfo.stime + ":" + crInfo.etime;
            }
            KPIResultInfo tarCombineInfo = null;
            if (!combinedCityDic.TryGetValue(timeStr, out tarCombineInfo))
            {
                tarCombineInfo = crInfo.copyInstance();
                if (gatherByRegion)
                {
                    tarCombineInfo.strname = crInfo.strname;
                }
                else
                {
                    tarCombineInfo.strname = cname;
                }
                combinedCityDic[timeStr] = tarCombineInfo;
            }
            else
            {
                tarCombineInfo.Gather(crInfo, this);
            }
        }

        private void dealGeneral(string selShowType)
        {
            foreach (List<KPIResultInfo> list in cityDataResult.Values)
            {
                if (selShowType == "")
                {
                    dataResult.AddRange(list);//only one
                    break;
                }
                else
                {
                    foreach (KPIResultInfo kinfo in list)
                    {
                        if (kinfo.isValidSegment(selShowType))
                        {
                            dataResult.Add(kinfo);
                        }
                    }
                }
            }
        }
    }

    public class KPIResultInfo
    {
        public int dbid { get; set; }
        public int stime { get; set; }
        public int etime { get; set; }
        public string strname { get; set; }
        public List<object> valueList { get; set; }

        public KPIResultInfo()
        {
            valueList = new List<object>();
        }

        internal KPIResultInfo copyInstance()
        {
            KPIResultInfo ret = new KPIResultInfo();
            ret.dbid = dbid;
            ret.stime = stime;
            ret.etime = etime;
            ret.strname = strname;
            for (int i = 0; i < valueList.Count; i++)
            {
                ret.valueList.Add(valueList[i]);
            }
            return ret;
        }

        internal void Gather(KPIResultInfo rinfo,KPIPopTable popTable)
        {
            try
            {
                List<PopKPIEntryItem> entryList = popTable.entryList;
                Dictionary<string, int> entryFinderDic = popTable.keyIndexDic;
                for (int i = 0; i < valueList.Count; i++)
                {
                    PopKPIEntryItem entry = entryList[i + 4];
                    if (entry.strcolname.EndsWith("pct"))
                    {
                        int colAt = 0;
                        if (entryFinderDic.TryGetValue(entry.strcolname + "_base", out colAt))//找到所需基数
                        {
                            setValue(rinfo, i, colAt);
                        }
                    }
                    else
                    {
                        if (valueList[i] is int)
                        {
                            valueList[i] = ((int)valueList[i] + (int)rinfo.valueList[i]);
                        }
                        else if (valueList[i] is float)
                        {
                            valueList[i] = ((float)valueList[i] + (float)rinfo.valueList[i]);
                        }
                        else if (valueList[i] is double)
                        {
                            valueList[i] = ((double)valueList[i] + (double)rinfo.valueList[i]);
                        }
                        else if (valueList[i] is long)
                        {
                            valueList[i] = ((long)valueList[i] + (long)rinfo.valueList[i]);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void setValue(KPIResultInfo rinfo, int i, int colAt)
        {
            double pctv = 0;
            double xxpctv = 0;
            long baseV = 0;
            long xxbaseV = 0;
            if (valueList[i] is int)
            {
                pctv = (int)valueList[i];
                xxpctv = (int)rinfo.valueList[i];
            }
            else if (valueList[i] is float)
            {
                pctv = (float)valueList[i];
                xxpctv = (float)rinfo.valueList[i];
            }
            else if (valueList[i] is double)
            {
                pctv = (double)valueList[i];
                xxpctv = (double)rinfo.valueList[i];
            }
            else if (valueList[i] is long)
            {
                pctv = (long)valueList[i];
                xxpctv = (long)rinfo.valueList[i];
            }
            //base
            if (valueList[colAt] is int)
            {
                baseV = (int)valueList[colAt];
                xxbaseV = (int)rinfo.valueList[colAt];
            }
            else if (valueList[colAt] is long)
            {
                baseV = (long)valueList[colAt];
                xxbaseV = (long)rinfo.valueList[colAt];
            }
            if (baseV + xxbaseV > 0)
            {
                double finalV = Math.Round((pctv * baseV + xxpctv * xxbaseV) / (baseV + xxbaseV), 2, MidpointRounding.ToEven);
                if (valueList[i] is int)
                {
                    valueList[i] = (int)finalV;
                }
                else if (valueList[i] is float)
                {
                    valueList[i] = (float)finalV;
                }
                else if (valueList[i] is double)
                {
                    valueList[i] = finalV;
                }
                else if (valueList[i] is long)
                {
                    valueList[i] = (long)finalV;
                }
            }
        }

        internal bool isValidSegment(string selShowType)
        {
            if(selShowType=="")
            {
                return true;
            }
            if(etime==-1)
            {
                DateTime dtime = JavaDate.GetDateTimeFromMilliseconds(1000L * stime);
                string dStr = dtime.Year + "_" + dtime.Month +"_"+dtime.Day;
                string dCur = DateTime.Now.Year + "_" + DateTime.Now.Month+"_1";

                return selShowType == "本月初至今" && dStr == dCur;
            }
            int span = etime - stime;
            int daysSpan = span / (3600 * 24);
            if(daysSpan==7)
            {
                return selShowType == "按周" || selShowType == "最近一周";
            }
            else if(daysSpan>25)
            {
                return selShowType == "按月" || selShowType == "最近一月"; 
            }
            return false;
        }
    };
    public class FinalShowResult
    {
        public FinalShowResult()
        {
            columnNames = new List<string>();
            dataRows = new List<List<object>>();
        }

        public List<string> columnNames { get; set; }
        public List<List<object>> dataRows { get; set; }
        public void Clear()
        {
            columnNames.Clear();
            dataRows.Clear();
        }

        /// <summary>
        /// 按相同地市名排列
        /// </summary>
        /// <param name="showRet"></param>
        /// <returns></returns>
        public void SortByTimeAndName(FinalShowResult showRet)
        {
            List<List<object>> refList = new List<List<object>>();
            foreach (List<object> row in dataRows)
            {
                foreach (List<object> rowTmp in showRet.dataRows)
                {
                    if (row[0].ToString().Equals(rowTmp[0].ToString()) && row[1].ToString().Equals(rowTmp[1].ToString()))
                    {
                        refList.Add(rowTmp);
                        break;
                    }
                }
            }
            showRet.dataRows = refList;
        }
    };

    public class DiySqlPopKpiColor : DIYSQLBase
    {
        public Dictionary<string, object> colorRangeDic { get; set; }

        public DiySqlPopKpiColor(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
            colorRangeDic = new Dictionary<string, object>();
        }

        protected override string getSqlTextString()
        {
            return "SELECT * FROM tb_popkpi_color";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[10];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            string strColName = package.Content.GetParamString();
            string strTableName = package.Content.GetParamString();

            float minval = package.Content.GetParamFloat();
            float maxval = package.Content.GetParamFloat();
            int mininc = package.Content.GetParamInt();
            int maxinc = package.Content.GetParamInt();
            int red = package.Content.GetParamInt();
            int green = package.Content.GetParamInt();
            int blue = package.Content.GetParamInt();
            string strDesc = package.Content.GetParamString();

            DTParameterRangeColor colorRanger = new DTParameterRangeColor();
            colorRanger.Max = maxval;
            colorRanger.Min = minval;
            colorRanger.MaxIncluded = (maxinc != 0);
            colorRanger.MinIncluded = (mininc != 0);
            colorRanger.Value = Color.FromArgb(red, green, blue);
            colorRanger.DesInfo = strDesc;
            string key = strTableName + strColName;
            if (!colorRangeDic.ContainsKey(key))
            {
                List<DTParameterRangeColor> colorRangeLst = new List<DTParameterRangeColor>();
                colorRangeDic[key] = colorRangeLst;
            }
            List<DTParameterRangeColor> colorRangeLst2 = colorRangeDic[key] as List<DTParameterRangeColor>;
            colorRangeLst2.Add(colorRanger);
        }

        public override string Name
        {
            get { return "DiySqlPopKpiColor"; }
        }
    };

    public class AlarmCfgItem
    {
        public string strcolname { get; set; }
        public string strcoldesc { get; set; }
        public string strtablename { get; set; }
        public int check_period_type { get; set; }
        public int check_history_days { get; set; }
        public int check_method { get; set; }
        public float abs_threshhold { get; set; }
        public float cmp_threshhold { get; set; }
        public int cmp_method { get; set; }
        public int is_alarm { get; set; } 
    }

    public class DiySqlPopKpiAlarmCfg : DIYSQLBase
    {
        public Dictionary<string, AlarmCfgItem> alarmCfgDic { get; set; }

        public DiySqlPopKpiAlarmCfg(MainModel mainModel)
            : base(mainModel)
        {
            alarmCfgDic = new Dictionary<string, AlarmCfgItem>();
        }

        protected override string getSqlTextString()
        {
            return "SELECT * FROM tb_popkpi_alarm_cfg";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[10];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AlarmCfgItem alarmCfgItem = new AlarmCfgItem();
                    alarmCfgItem.strcolname = package.Content.GetParamString();
                    alarmCfgItem.strcoldesc = package.Content.GetParamString();
                    alarmCfgItem.strtablename = package.Content.GetParamString();

                    alarmCfgItem.check_period_type = package.Content.GetParamInt();
                    alarmCfgItem.check_history_days = package.Content.GetParamInt();
                    alarmCfgItem.check_method = package.Content.GetParamInt();
                    alarmCfgItem.abs_threshhold = package.Content.GetParamInt();
                    alarmCfgItem.cmp_threshhold = package.Content.GetParamInt();
                    alarmCfgItem.cmp_method = package.Content.GetParamInt();
                    alarmCfgItem.is_alarm = package.Content.GetParamInt();

                    string key = alarmCfgItem.strtablename + alarmCfgItem.strcolname;
                    alarmCfgDic[key] = alarmCfgItem;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DiySqlPopKpiAlarmCfg"; }
        }
    };
}
