﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRRepeatCoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numCellRSRP = new DevExpress.XtraEditors.SpinEdit();
            this.numLowSpeedSec = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCellRSRP.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLowSpeedSec.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numCellRSRP);
            this.groupBox1.Controls.Add(this.numLowSpeedSec);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(343, 105);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "重叠覆盖";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(247, 57);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "dB";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(31, 57);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(149, 12);
            this.label3.TabIndex = 4;
            this.label3.Text = "主服小区以及邻区电平值≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(179, 28);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "秒内(含等于)开始";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "低速率发生前";
            // 
            // numCellRSRP
            // 
            this.numCellRSRP.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numCellRSRP.Location = new System.Drawing.Point(182, 53);
            this.numCellRSRP.Name = "numCellRSRP";
            this.numCellRSRP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numCellRSRP.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numCellRSRP.Size = new System.Drawing.Size(60, 21);
            this.numCellRSRP.TabIndex = 1;
            // 
            // numLowSpeedSec
            // 
            this.numLowSpeedSec.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numLowSpeedSec.Location = new System.Drawing.Point(114, 24);
            this.numLowSpeedSec.Name = "numLowSpeedSec";
            this.numLowSpeedSec.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLowSpeedSec.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numLowSpeedSec.Size = new System.Drawing.Size(60, 21);
            this.numLowSpeedSec.TabIndex = 0;
            // 
            // NRRepeatCoverPnl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Name = "NRRepeatCoverPnl";
            this.Size = new System.Drawing.Size(343, 105);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCellRSRP.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLowSpeedSec.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numCellRSRP;
        private DevExpress.XtraEditors.SpinEdit numLowSpeedSec;
    }
}
