﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanHighCoverateRoadCond
    {
        //相对
        public bool IsRelativeCheck { get; set; }
        public int RsrpMaxDiff { get; set; } = 6;
        public int RelCoverate { get; set; } = 4;
        //绝对
        public bool IsAbsCheck { get; set; }
        public int AbsValue { get; set; } = -110;
        public int AbsCoverate { get; set; } = 11;
        //持续性
        public int RoadDistance { get; set; } = 50;
        public double RoadMinPercent { get; set; } = 100;
        public int SampleDistance { get; set; } = 50;

        public int RsrpMin { get; set; } = -95;

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["AbsValue"] = AbsValue;
                param["IsAbsCheck"] = IsAbsCheck;
                param["IsRelativeCheck"] = IsRelativeCheck;
                param["AbsCoverate"] = AbsCoverate;
                param["RsrpMaxDiff"] = RsrpMaxDiff;
                param["RsrpMin"] = RsrpMin;
                param["RelCoverate"] = RelCoverate;
                param["RoadDistance"] = RoadDistance;
                param["RoadMinPercent"] = RoadMinPercent;
                param["SampleDistance"] = SampleDistance;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                AbsValue = setParam(param, "AbsValue", AbsValue);
                IsAbsCheck = setParam(param, "IsAbsCheck", IsAbsCheck);
                IsRelativeCheck = setParam(param, "IsRelativeCheck", IsRelativeCheck);
                AbsCoverate = setParam(param, "AbsCoverate", AbsCoverate);
                RsrpMaxDiff = setParam(param, "RsrpMaxDiff", RsrpMaxDiff);
                RsrpMin = setParam(param, "RsrpMin", RsrpMin);
                RelCoverate = setParam(param, "RelCoverate", RelCoverate);
                RoadDistance = setParam(param, "RoadDistance", RoadDistance);
                RoadMinPercent = setParam(param, "RoadMinPercent", RoadMinPercent);
                SampleDistance = setParam(param, "SampleDistance", SampleDistance);
            }
        }

        protected T setParam<T>(Dictionary<string, object> param, string name, T defaultValue)
        {
            T res = defaultValue;
            if (param.ContainsKey(name))
            {
                res = (T)param[name];
                if (res == null)
                {
                    res = defaultValue;
                }
            }

            return res;
        }
    }
}
