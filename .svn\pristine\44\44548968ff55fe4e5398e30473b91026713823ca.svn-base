﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    public class BlockCallInfo
    {
         public int SN
        { private set; get; }

         public BlockCallInfo(int sn, CallInfo call)
        {
            this.SN = sn;
            this.MoMtCalls = new List<CallInfo>();
            if (call.MoMtDesc == "主叫")
            {
                MoMtCalls.Add(call);
                if (call.OtherSideCall != null)
                {
                    MoMtCalls.Add(call.OtherSideCall);
                }
            }
            else
            {
                if (call.OtherSideCall != null)
                {
                    MoMtCalls.Add(call.OtherSideCall);
                }
                MoMtCalls.Add(call);
            }
        }

        public List<CallInfo> MoMtCalls
        {
            get;
            set;
        }

    }
}
