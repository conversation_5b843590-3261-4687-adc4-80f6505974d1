using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class PilotFrequencyPolluteBlock
    {
        public PilotFrequencyPolluteBlock()
        {

        }
        public PilotFrequencyPolluteBlock(int idx)
        {
            ID = idx;
        }

        public int ID { get; set; }
        
        public Dictionary<string, CellOfPilotFrequencyPolluteBlock> CellDic { get; set; } = new Dictionary<string, CellOfPilotFrequencyPolluteBlock>();

        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        public int TestPointCount { get; set; }

        public int GoodTestPointCount { get; set; }

        public int TotalTestPointCount
        {
            get { return TestPointCount + GoodTestPointCount; }
        }

        public double BadSampleScale
        {
            get
            {
                return Math.Round(100.0 * TestPointCount / (TotalTestPointCount), 2);
            }
        }

        private int istime;
        private int ietime;
        public double LongitudeMid { get; set;}
        public double LatitudeMid { get; set; }

        internal void Join(PilotFrequencyPolluteBlock tpBlock)
        {
            foreach (TestPoint tp in tpBlock.TestPoints)
            {
                AddTestPoint(tp);
            }
            foreach (CellOfPilotFrequencyPolluteBlock item in tpBlock.CellDic.Values)
            {
                AddCellOfPilotFrequencyPolluteBlock(item);
            }
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            if (testPoints.Count == 0)
            {
                if (LongitudeMid >= x1 && LongitudeMid <= x2 && LatitudeMid >= y1 || LatitudeMid <= y2)
                {
                    return true;
                }
            }
            else
            {
                foreach (TestPoint tp in testPoints)
                {
                    if (tp.Longitude >= x1 && tp.Longitude <= x2 && tp.Latitude >= y1 || tp.Latitude <= y2)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void AddTestPoint(TestPoint tp)
        {
            if (!testPoints.Contains(tp))
            {
                testPoints.Add(tp);
                if (istime > tp.Time)
                {
                    istime = tp.Time;
                }
                if (ietime < tp.Time)
                {
                    ietime = tp.Time;
                }
            }
        }

        public void AddCellOfPilotFrequencyPolluteBlock(CellOfPilotFrequencyPolluteBlock cellOfBlock)
        {
            if (cellOfBlock is LTECellOfPilotFrequencyPolluteBlock)
            {
                LTECellOfPilotFrequencyPolluteBlock lteBlock = cellOfBlock as LTECellOfPilotFrequencyPolluteBlock;
                if (CellDic.ContainsKey(lteBlock.LTECell.Name))
                {
                    CellDic[lteBlock.LTECell.Name].Join(cellOfBlock);
                }
                else
                {
                    CellDic[lteBlock.LTECell.Name] = cellOfBlock;
                }
            }
            else if (cellOfBlock is WCellOfPilotFrequencyPolluteBlock)
            {
                WCellOfPilotFrequencyPolluteBlock wBlock = cellOfBlock as WCellOfPilotFrequencyPolluteBlock;
                if (CellDic.ContainsKey(wBlock.Cell.Name))
                {
                    CellDic[wBlock.Cell.Name].Join(cellOfBlock);
                }
                else
                {
                    CellDic[wBlock.Cell.Name] = cellOfBlock;
                }
            }
            else
            {
                if (CellDic.ContainsKey(cellOfBlock.Cell.Name))
                {
                    CellDic[cellOfBlock.Cell.Name].Join(cellOfBlock);
                }
                else
                {
                    CellDic[cellOfBlock.Cell.Name] = cellOfBlock;
                }
            }
        }

        //LTE
        public void AddCellOfPilotFrequencyPolluteBlock(LTECellOfPilotFrequencyPolluteBlock cellOfBlock)
        {
            if (CellDic.ContainsKey(cellOfBlock.LTECell.Name))
            {
                CellDic[cellOfBlock.LTECell.Name].Join(cellOfBlock);
            }
            else
            {
                CellDic[cellOfBlock.LTECell.Name] = cellOfBlock;
            }
        }

        public void AddTestPoint(TestPoint tp, List<CellOfPilotFrequencyPolluteBlock> cellList)
        {
            AddTestPoint(tp);
            foreach (CellOfPilotFrequencyPolluteBlock cellOfBlock in cellList)
            {
                AddCellOfPilotFrequencyPolluteBlock(cellOfBlock);
            }
        }

        //WCDMA
        public void AddTestPoint(TestPoint tp, List<WCellOfPilotFrequencyPolluteBlock> cellList)
        {
            AddTestPoint(tp);
            foreach (WCellOfPilotFrequencyPolluteBlock cellOfBlock in cellList)
            {
                AddCellOfPilotFrequencyPolluteBlock(cellOfBlock);
            }
        }

        //LTE
        public void AddTestPoint(TestPoint tp, List<LTECellOfPilotFrequencyPolluteBlock> cellList)
        {
            AddTestPoint(tp);
            foreach (LTECellOfPilotFrequencyPolluteBlock cellOfBlock in cellList)
            {
                AddCellOfPilotFrequencyPolluteBlock(cellOfBlock);
            }
        }

        public bool Intersect(double longitude, double latitude, int radius)
        {
            foreach (TestPoint tp in testPoints)
            {
                if (MathFuncs.GetDistance(longitude, latitude, tp.Longitude, tp.Latitude) <= radius)
                {
                    return true;
                }
            }
            return false;
        }

        private void getCenterPoint()
        {
            DbRect bounds = new DbRect();
            bool first = true;
            foreach (TestPoint item in TestPoints)
            {
                first = setBounds(bounds, first, item);
            }
            LongitudeMid = bounds.Center().x;
            LatitudeMid = bounds.Center().y;
        }

        private bool setBounds(DbRect bounds, bool first, TestPoint item)
        {
            if (first)
            {
                bounds.x1 = item.Longitude;
                bounds.x2 = item.Longitude;
                bounds.y1 = item.Latitude;
                bounds.y2 = item.Latitude;
                first = false;
            }
            else
            {
                if (bounds.x1 > item.Longitude)
                {
                    bounds.x1 = item.Longitude;
                }
                if (bounds.x2 < item.Longitude)
                {
                    bounds.x2 = item.Longitude;
                }
                if (bounds.y1 > item.Latitude)
                {
                    bounds.y1 = item.Latitude;
                }
                if (bounds.y2 < item.Latitude)
                {
                    bounds.y2 = item.Latitude;
                }
            }

            return first;
        }

        public string RoadPlaceDesc { get; set; } = "";

        private string areaPlaceDesc = "";
        public string AreaPlaceDesc
        {
            get { return areaPlaceDesc; }
        }

        private string gridDesc = "";
        public string GridDesc
        {
            get { return gridDesc; }
        }

        private string areaAgentPlaceDesc = "";
        public string AreaAgentPlaceDesc
        {
            get { return areaAgentPlaceDesc; }
        }

        public void GetResult()
        {
            getCenterPoint();
            TestPointCount = testPoints.Count;
            RoadPlaceDesc = GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
            //areaPlaceDesc = GISManager.GetInstance().GetAreaPlaceDesc(LongitudeMid, LatitudeMid);
            //gridDesc = GISManager.GetInstance().GetGridDesc(LongitudeMid, LatitudeMid);
            //areaAgentPlaceDesc = GISManager.GetInstance().GetAreaAgentDesc(LongitudeMid, LatitudeMid);

            string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(LongitudeMid, LatitudeMid);
            areaPlaceDesc = setDesc(strAreaName, areaPlaceDesc);

            string strGridName = GISManager.GetInstance().GetGridDesc(LongitudeMid, LatitudeMid);
            gridDesc = setDesc(strGridName, gridDesc);

            string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(LongitudeMid, LatitudeMid);
            areaAgentPlaceDesc = setDesc(strAreaAgentName, areaAgentPlaceDesc);
        }

        private string setDesc(string strName, string strDesc)
        {
            if (strName != null)
            {
                if (strDesc == null || strDesc == "")
                {
                    strDesc = strName;
                }
                else
                {
                    if (!strDesc.Contains(strName) && strName != "")
                    {
                        strDesc += "," + strName;
                    }
                }
            }
            return strDesc;
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.ISTime = istime;
            bgResult.IETime = ietime;
            bgResult.LongitudeMid = LongitudeMid;
            bgResult.LatitudeMid = LatitudeMid;
            bgResult.RoadDesc = RoadPlaceDesc;
            bgResult.AreaDesc = areaPlaceDesc;
            bgResult.GridDesc = gridDesc;
            bgResult.AreaAgentDesc = areaAgentPlaceDesc;
            bgResult.AddImageValue(TestPointCount);
            bgResult.AddImageValue(GoodTestPointCount);
            return bgResult;
        }

        public List<BackgroundResult> ConvertToBackgroundResultList(PilotFrequencyPolluteBlock block)
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            try
            {
                foreach (string strKey in block.CellDic.Keys)
                {
                    BackgroundResult bgResult = new BackgroundResult();
                    bgResult.ISTime = istime;
                    bgResult.IETime = ietime;
                    bgResult.LongitudeMid = block.LongitudeMid;
                    bgResult.LatitudeMid = block.LatitudeMid;
                    bgResult.RoadDesc = block.RoadPlaceDesc;
                    bgResult.AreaDesc = block.areaPlaceDesc;
                    bgResult.GridDesc = block.gridDesc;
                    bgResult.AreaAgentDesc = block.areaAgentPlaceDesc;
                    bgResult.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                    bgResult.SampleCount = TotalTestPointCount;

                    bgResult.AddImageValue(TestPointCount);
                    bgResult.AddImageValue(GoodTestPointCount);
                    if (block.CellDic[strKey] is LTECellOfPilotFrequencyPolluteBlock)
                    {
                        LTECellOfPilotFrequencyPolluteBlock blockCell = block.CellDic[strKey] as LTECellOfPilotFrequencyPolluteBlock;
                        bgResult.AddImageValue(blockCell.LTECell.Name);
                        bgResult.AddImageValue(blockCell.LTECell.Code.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.TAC.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.ECI.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.EARFCN.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.PCI.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.Altitude.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.Downward.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.Direction.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.Longitude.ToString());
                        bgResult.AddImageValue(blockCell.LTECell.Latitude.ToString());
                    }
                    else
                    {
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Name);
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Code.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.LAC.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.CI.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.FREQ.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.CPI.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Altitude.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Downword.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Direction.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Longitude.ToString());
                        bgResult.AddImageValue(block.CellDic[strKey].Cell.Latitude.ToString());
                    }
                    bgResult.AddImageValue(block.CellDic[strKey].PCCPCH_RSCPAvg.ToString());
                    bgResult.AddImageValue(block.CellDic[strKey].SampleCount.ToString());
                    bgResult.AddImageValue(block.CellDic[strKey].PCCPCH_C2IString);
                    bgResult.AddImageValue(block.CellDic[strKey].DPCH_C2IString);
                    bgResult.AddImageValue(bgResult.ProjectString);

                    bgResultList.Add(bgResult);
                }              
            }
            catch
            { 
                //continue
            }
            return bgResultList;
        }
    }

    public class CellOfPilotFrequencyPolluteBlock
    {
        public CellOfPilotFrequencyPolluteBlock()
        {

        }

        public CellOfPilotFrequencyPolluteBlock(int sn, TDCell cell, double pccpch_rscp)
        {
            SN = sn;
            Cell = cell;
            PCCPCH_RSCP = pccpch_rscp;
            sampleCount = 1;
        }

        public CellOfPilotFrequencyPolluteBlock(int sn, TDCell cell, double pccpch_rscp, double pccpch_c2i, double dpch_c2i)
            : this(sn, cell, pccpch_rscp)
        {
            if (pccpch_c2i != 255)
            {
                this.pccpch_c2iSum = pccpch_c2i;
                pccpch_c2iCount = 1;
            }
            if (dpch_c2i != 255)
            {
                this.dpch_c2iSum = dpch_c2i;
                dpch_c2iCount = 1;
            }
        }
        
        public int SN { get; set; }

        public TDCell Cell { get; set; }

        public double PCCPCH_RSCP { get; set; }

        public double PCCPCH_RSCPAvg
        {
            get { return Math.Round(PCCPCH_RSCP / sampleCount, 2); }
        }

        protected int sampleCount;
        public int SampleCount
        {
            get { return sampleCount; }
        }

        protected int pccpch_c2iCount = 0;
        protected double pccpch_c2iSum = 0;
        public string PCCPCH_C2IString
        {
            get { return pccpch_c2iCount == 0 ? "-" : Math.Round(pccpch_c2iSum / pccpch_c2iCount, 2).ToString(); }
        }

        protected int dpch_c2iCount = 0;
        protected double dpch_c2iSum = 0;
        public string DPCH_C2IString
        {
            get { return dpch_c2iCount == 0 ? "-" : Math.Round(dpch_c2iSum / dpch_c2iCount, 2).ToString(); }
        }

        public void Join(CellOfPilotFrequencyPolluteBlock otherBlock)
        {
            if (Cell != otherBlock.Cell)
            {
                return;
            }
            PCCPCH_RSCP += otherBlock.PCCPCH_RSCP;
            sampleCount += otherBlock.SampleCount;
            pccpch_c2iSum += otherBlock.pccpch_c2iSum;
            pccpch_c2iCount += otherBlock.pccpch_c2iCount;
            dpch_c2iSum += otherBlock.dpch_c2iSum;
            dpch_c2iCount += otherBlock.dpch_c2iCount;
        }
    }

    public class WCellOfPilotFrequencyPolluteBlock : CellOfPilotFrequencyPolluteBlock
    {
        public WCellOfPilotFrequencyPolluteBlock()
        {

        }

        public WCellOfPilotFrequencyPolluteBlock(int sn, WCell cell, double pccpch_rscp)
        {
            SN = sn;
            Cell = cell;
            PCCPCH_RSCP = pccpch_rscp;
            sampleCount = 1;
        }

        public WCellOfPilotFrequencyPolluteBlock(int sn, WCell cell, double pccpch_rscp, double pccpch_c2i, double dpch_c2i)
            : this(sn, cell, pccpch_rscp)
        {
            if (pccpch_c2i != 255)
            {
                this.pccpch_c2iSum = pccpch_c2i;
                pccpch_c2iCount = 1;
            }
            if (dpch_c2i != 255)
            {
                this.dpch_c2iSum = dpch_c2i;
                dpch_c2iCount = 1;
            }
        }
        
        public new WCell Cell { get; set; }
    }

    public class LTECellOfPilotFrequencyPolluteBlock : CellOfPilotFrequencyPolluteBlock
    {
        public LTECell LTECell { get; set; }

        public int sinrCnt { get; set; } = 0;
        private double totalSinr = 0;
        public LTECellOfPilotFrequencyPolluteBlock(int sn, LTECell cell, double rsrp, float? sinr)
        {
            SN = sn;
            LTECell = cell;
            PCCPCH_RSCP = rsrp;
            sampleCount = 1;
            if (sinr != null && -50 <= sinr && sinr <= 50)
            {
                sinrCnt++;
                totalSinr += (float)sinr;
            }
        }

        public void Join(LTECellOfPilotFrequencyPolluteBlock otherBlock)
        {
            if (LTECell != otherBlock.LTECell)
            {
                return;
            }
            PCCPCH_RSCP += otherBlock.PCCPCH_RSCP;
            sampleCount += otherBlock.SampleCount;
            sinrCnt += otherBlock.sinrCnt;
            totalSinr += otherBlock.totalSinr;
        }

        public object SINRAvg
        {
            get
            {
                string ret = "-";
                if (sinrCnt != 0)
                {
                    ret = Math.Round(totalSinr / sinrCnt, 2).ToString();
                }
                return ret;
            }
        }
    }


}
