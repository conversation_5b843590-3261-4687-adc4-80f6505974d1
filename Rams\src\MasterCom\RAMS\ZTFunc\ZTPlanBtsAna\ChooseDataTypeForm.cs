﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ChooseDataTypeForm : BaseForm
    {
        public ChooseDataTypeForm()
        {
            InitializeComponent();
            initCity();
            initDataType();
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void initCity()
        {
            int count = DistrictManager.GetInstance().getDistrictCount();
            for (int i = 0; i < count; i++)
            {
                cbxCity.Properties.Items.Add(DistrictManager.GetInstance().getDistrictName(i + 1));
            }
            if (count > 0)
                cbxCity.SelectedIndex = 0;
        }

        private void initDataType()
        {
            cbxDataType.Properties.Items.Add("MR数据良好覆盖率");
            cbxDataType.Properties.Items.Add("MR数据弱覆盖率");
            cbxDataType.Properties.Items.Add("倒流量情况");
            cbxDataType.Properties.Items.Add("栅格竞争对比（移动绝对值）");
            cbxDataType.Properties.Items.Add("栅格竞争对比（移动与联通相对值）");
            cbxDataType.Properties.Items.Add("用户投诉量");
            cbxDataType.Properties.Items.Add("高流量小区");
            cbxDataType.Properties.Items.Add("驻留率情况");
            cbxDataType.Properties.Items.Add("持续差道路");
            cbxDataType.Properties.Items.Add("规划站评估");
            cbxDataType.SelectedIndex = 0;
        }

        public string Data
        {
            get { return cbxDataType.SelectedItem.ToString(); }
        }

        public string City
        {
            get { return cbxCity.SelectedItem.ToString(); }
        }

        public int Radius
        {
            get 
            {
                if (numUDRadius.Enabled)
                {
                    return (int)numUDRadius.Value;
                }
                else
                {
                    return -1;
                }
            }
        }

        private void cbxDataType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxDataType.SelectedItem.ToString().Equals("栅格竞争对比（移动绝对值）")
                || cbxDataType.SelectedItem.ToString().Equals("栅格竞争对比（移动与联通相对值）")
                || cbxDataType.SelectedItem.ToString().Equals("用户投诉量")
                || cbxDataType.SelectedItem.ToString().Equals("持续差道路")
                || cbxDataType.SelectedItem.ToString().Equals("规划站评估"))
            {
                this.numUDRadius.Enabled = true;//非小区类的统计，可设置覆盖半径大小
            }
            else
            {
                this.numUDRadius.Enabled = false;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
