﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTNRLTECollaborativeAnaBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        List<TestPoint> allValidTPs;
        List<ZTNRLTECollaborativeAnaInfo> resList;
        public ZTNRLTECollaborativeAnaCondition CurCondition { get; set; }

        protected static readonly object lockObj = new object();
        protected ZTNRLTECollaborativeAnaBase()
            : base(MainModel.GetInstance())
        {
            IncludeEvent = false;
            CurCondition = new ZTNRLTECollaborativeAnaCondition();
            init();
        }

        protected void init()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            //var serviceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE);
            //ServiceTypes.AddRange(serviceTypes);
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public override string Name
        {
            get { return "4/5G协同分析按采样点"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35028, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            ZTNRLTECollaborativeAnaDlg dlg = new ZTNRLTECollaborativeAnaDlg(CurCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                CurCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            allValidTPs = new List<TestPoint>();
            resList = new List<ZTNRLTECollaborativeAnaInfo>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                ZTNRLTECollaborativeAnaInfo info = new ZTNRLTECollaborativeAnaInfo();
                TestPoint prePoint = null;//前一点
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (isValidTestPoint(tp))
                    {
                        info = getZTNRLTECollaborativeAnaInfo(tp, info, ref prePoint);
                    }
                    else
                    {
                        saveZTNRLTECollaborativeAnaInfo(info);
                        info = new ZTNRLTECollaborativeAnaInfo();//重置
                        prePoint = null;
                    }
                }
                //保存最后一个点
                if (info.Type != ZTNRLTECollaborativeAnaType.Unknown)
                {
                    saveZTNRLTECollaborativeAnaInfo(info);
                }
            }
        }

        private float getValidData(float? data)
        {
            if (data == null)
            {
                return -999;
            }
            return (float)data;
        }

        protected ZTNRLTECollaborativeAnaInfo getZTNRLTECollaborativeAnaInfo(TestPoint tp, ZTNRLTECollaborativeAnaInfo info, ref TestPoint prePoint)
        {
            float? nrRsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            if (nrRsrp == null || lteRsrp == null)
            {
                prePoint = null;
                return new ZTNRLTECollaborativeAnaInfo();
            }

            float? nrSinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            ZTNRLTECollaborativeAnaInfo.TPInfo nrInfo = new ZTNRLTECollaborativeAnaInfo.TPInfo();
            ZTNRLTECollaborativeAnaInfo.TPInfo lteInfo = new ZTNRLTECollaborativeAnaInfo.TPInfo();

            nrInfo.Rsrp = (float)nrRsrp;
            lteInfo.Rsrp = (float)lteRsrp;
            nrInfo.Sinr = getValidData(nrSinr);
            lteInfo.Sinr = getValidData(lteSinr);

            if (nrRsrp < CurCondition.Num5GWeakCover && lteRsrp < CurCondition.Num4GWeakCover)
            {
                //覆盖同差
                info = dealNRLTECollaborativeAnaInfo(tp, info, prePoint, nrInfo, lteInfo, ZTNRLTECollaborativeAnaType.WeakCover4G5G);
                prePoint = tp;
            }
            else if (lteRsrp >= CurCondition.Num4GWeakCover && lteRsrp - nrRsrp >= CurCondition.Num4GBetterThan5G)
            {
                //4G优于5G
                info = dealNRLTECollaborativeAnaInfo(tp, info, prePoint, nrInfo, lteInfo, ZTNRLTECollaborativeAnaType.Better4G);
                prePoint = tp;
            }
            else if (nrRsrp >= CurCondition.Num5GWeakCover && nrRsrp - lteRsrp >= CurCondition.Num5GBetterThan4G)
            {
                //5G优于4G
                info = dealNRLTECollaborativeAnaInfo(tp, info, prePoint, nrInfo, lteInfo, ZTNRLTECollaborativeAnaType.Better5G);
                prePoint = tp;
            }
            else
            {
                saveZTNRLTECollaborativeAnaInfo(info);
                info = new ZTNRLTECollaborativeAnaInfo();
                prePoint = null;
            }

            return info;
        }

        private ZTNRLTECollaborativeAnaInfo dealNRLTECollaborativeAnaInfo(TestPoint tp, ZTNRLTECollaborativeAnaInfo info,
            TestPoint prePoint, ZTNRLTECollaborativeAnaInfo.TPInfo nrInfo, ZTNRLTECollaborativeAnaInfo.TPInfo lteInfo, ZTNRLTECollaborativeAnaType type)
        {
            if (info.Type == ZTNRLTECollaborativeAnaType.Unknown)
            {
                info.Type = type;
                addZTNRLTECollaborativeAnaInfo(info, tp, prePoint, nrInfo, lteInfo);
            }
            else if (info.Type == type)
            {
                addZTNRLTECollaborativeAnaInfo(info, tp, prePoint, nrInfo, lteInfo);
            }
            else
            {
                saveZTNRLTECollaborativeAnaInfo(info);
                info = new ZTNRLTECollaborativeAnaInfo();
                info.Type = type;
                addZTNRLTECollaborativeAnaInfo(info, tp, prePoint, nrInfo, lteInfo);
            }

            return info;
        }

        private void addZTNRLTECollaborativeAnaInfo(ZTNRLTECollaborativeAnaInfo info, TestPoint tp, TestPoint prePoint,
            ZTNRLTECollaborativeAnaInfo.TPInfo nrInfo, ZTNRLTECollaborativeAnaInfo.TPInfo lteInfo)
        {
            double dis = 0;
            if (prePoint != null)
            {
                dis = prePoint.Distance2(tp.Longitude, tp.Latitude);
            }
            info.AddTestPoint(tp, nrInfo, lteInfo, dis);
        }


        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys == null || Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

        private void saveZTNRLTECollaborativeAnaInfo(ZTNRLTECollaborativeAnaInfo info)
        {
            if (info == null
                || !CurCondition.CheckStayDistance(info.StayDistance)
                || !CurCondition.CheckStayTime(info.StaySecond))
            {
                return;
            }

            foreach (var tp in info.TestPoints)
            {
                allValidTPs.Add(tp);
            }
            info.SN = resList.Count + 1;
            info.Calculate();
            resList.Add(info);
        }

        protected override void fireShowForm()
        {
            if (resList.Count <= 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ZTNRLTECollaborativeAnaForm frm = MainModel.CreateResultForm(typeof(ZTNRLTECollaborativeAnaForm)) as ZTNRLTECollaborativeAnaForm;
            frm.FillData(resList, allValidTPs);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class ZTNRLTECollaborativeAnaByRegion : ZTNRLTECollaborativeAnaBase
    {
        private ZTNRLTECollaborativeAnaByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static ZTNRLTECollaborativeAnaByRegion instance = null;
        public static ZTNRLTECollaborativeAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTNRLTECollaborativeAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "4/5G协同分析按采样点(按区域)"; }
        }
    }

    public class ZTNRLTECollaborativeAnaByFile : ZTNRLTECollaborativeAnaBase
    {
        private ZTNRLTECollaborativeAnaByFile()
            : base()
        {
        }

        private static ZTNRLTECollaborativeAnaByFile instance = null;
        public static ZTNRLTECollaborativeAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTNRLTECollaborativeAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "4/5G协同分析按采样点(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.GetEnumerator().MoveNext())
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
