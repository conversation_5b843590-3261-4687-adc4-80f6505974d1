﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NoCoverRoadInfo_NR
    {
        public int SN { get; set; }
        public List<TestPoint> TestPointList { get; set; } = new List<TestPoint>();

        private double duration;
        public double Duration
        {
            get
            {
                return Math.Round(duration, 2);
            }
            set
            {
                duration = value;
            }
        }

        private double distance;
        public double Distance
        {
            get
            {
                return Math.Round(distance, 2);
            }
            set
            {
                distance = value;
            }
        }
        public int TpCount { get { return TestPointList.Count; } }
        public int TpCount_NoCover { get; set; }
        public double TpRate_NoCover { get; set; }

        public string FileName { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public double? RsrpMax { get; set; }
        public double? RsrpMin { get; set; }
        public double? RsrpAvg { get; set; }

        public double? SinrMax { get; set; }
        public double? SinrMin { get; set; }
        public double? SinrAvg { get; set; }

        public double? LteRsrpMax { get; set; }
        public double? LteRsrpMin { get; set; }
        public double? LteRsrpAvg { get; set; }

        public double? LteSinrMax { get; set; }
        public double? LteSinrMin { get; set; }
        public double? LteSinrAvg { get; set; }

        public string RoadDesc { get; set; }
        public string AreaName { get; set; }
        public string GridName { get; set; }

    }
}
