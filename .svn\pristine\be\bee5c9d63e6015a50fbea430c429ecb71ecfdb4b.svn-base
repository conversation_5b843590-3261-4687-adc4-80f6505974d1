﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VolteWeakMosResultForm : MinCloseForm
    {
        List<WeakMosItem> weakMosTpList = new List<WeakMosItem>();
        public VolteWeakMosResultForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;

        }

        public void FillData(List<WeakMosItem> weakMosTpList)
        {
            gridControl1.DataSource = weakMosTpList;
            gridControl1.RefreshDataSource();
            this.weakMosTpList = weakMosTpList;
            MainModel.DTDataManager.Clear();
            lines.Clear();
            foreach (WeakMosItem item in weakMosTpList)
            {
                draw(item);
            }
            TempLayer.Instance.Draw(DrawLine);
            MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:PESQMos", "LTE_TDD:POLQA_Score_SWB");
            MainModel.FireDTDataChanged(this);
        }

        public void GridView_DoubleClick(object sender, EventArgs e)
        {
            lines.Clear();
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is WeakMosItem)
            {
                WeakMosItem item = gv.GetRow(gv.GetSelectedRows()[0]) as WeakMosItem;
                MainModel.DTDataManager.Clear();
                draw(item);
                TempLayer.Instance.Draw(DrawLine);
                MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:PESQMos", "LTE_TDD:POLQA_Score_SWB");
                MainModel.FireDTDataChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(item.WeakMosTpList[0].Longitude, item.WeakMosTpList[0].Latitude);
            }
        }

        private void draw(WeakMosItem item)
        {
            for (int i = 0; i < item.WeakMosTpList.Count; i++)
            {
                WeakMosTPInfo info = item.WeakMosTpList[i];
                MainModel.DTDataManager.Add(info.TP);

                if (i >= 1)
                {
                    Line line = new Line();
                    line.P1 = new DbPoint(item.WeakMosTpList[i].Longitude, item.WeakMosTpList[i].Latitude);
                    line.P2 = new DbPoint(item.WeakMosTpList[i - 1].Longitude, item.WeakMosTpList[i - 1].Latitude);
                    line.LineColor = Color.Red;
                    lines.Add(line);
                }
            }
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void DrawLine(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (lines == null || lines.Count == 0)
            {
                return;
            }

            foreach (Line line in lines)
            {
                PointF p1, p2;
                mop.ToDisplay(line.P1, out p1);
                mop.ToDisplay(line.P2, out p2);
                Pen pen = new Pen(line.LineColor, 1);
                pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                graphics.DrawLine(pen, p1, p2);
            }
        }
        private List<Line> lines = new List<Line>();

        private class Line
        {
            public DbPoint P1;
            public DbPoint P2;
            public Color LineColor;
        }

        private void miShowMosPeriodInfo_Click(object sender, EventArgs e)
        {
            lines.Clear();
            if (gridView1.GetRow(gridView1.GetSelectedRows()[0]) is WeakMosItem)
            {
                WeakMosItem item = gridView1.GetRow(gridView1.GetSelectedRows()[0]) as WeakMosItem;
                MainModel.DTDataManager.Clear();
                draw(item);
                TempLayer.Instance.Draw(DrawLine);
                foreach (WeakMosTPInfo info in item.WeakMosTpList)
                {
                    foreach (TestPoint tp in info.PointList)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    foreach (Event evt in info.EvtList)
                    {
                        MainModel.DTDataManager.Add(evt);
                    }
                }
                MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:PESQMos", "LTE_TDD:POLQA_Score_SWB", "TD_LTE_RSRP");
                MainModel.FireDTDataChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(item.WeakMosTpList[0].Longitude, item.WeakMosTpList[0].Latitude);
            }
        }
    }
}
