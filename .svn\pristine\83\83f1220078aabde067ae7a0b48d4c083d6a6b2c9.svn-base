﻿namespace MasterCom.RAMS.Func
{
    partial class OverlapCoverForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridViewCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLac = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellCoverGridNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellReCoverGridNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellReCoverRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCoverGridNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellRedunGridNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellRedunGridRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.advBandedGridViewStreet = new DevExpress.XtraGrid.Views.BandedGrid.AdvBandedGridView();
            this.gridColumnGridName = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridViewGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnGridReCoverNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridReCoverNum900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridReCoverNum1800 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridReCoverRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridStructIndexNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridStructIndexNum900 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridStructIndexNum1800 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStructIndexRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.GridColumnRelative9001 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative9002 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative9003 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative9004 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute9001 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute9002 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute9003 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute9004 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative18001 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative18002 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative18003 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnRelative18004 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute18001 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute18002 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute18003 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.GridColumnAbsolute18004 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBandRegionInfo = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBandRelativeCanUsed900 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBandAbsoluteCanUsed900 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBandRelativeCanUsed1800 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBandAbsoluteCanUsed1800 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.advBandedGridViewStreet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.advBandedGridViewStreet;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(946, 442);
            this.gridControl1.TabIndex = 14;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCell,
            this.advBandedGridViewStreet,
            this.gridViewGrid});
            // 
            // gridViewCell
            // 
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCellName,
            this.gridColumnLac,
            this.gridColumnCI,
            this.gridColumnCellType,
            this.gridColumnCellCoverGridNum,
            this.gridColumnCellReCoverGridNum,
            this.gridColumnCellReCoverRate,
            this.gridColumnCoverGridNum,
            this.gridColumnCellRedunGridNum,
            this.gridColumnCellRedunGridRate});
            this.gridViewCell.GridControl = this.gridControl1;
            this.gridViewCell.GroupPanelText = "请拖拉需要聚合的列到这里...";
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsDetail.AllowZoomDetail = false;
            this.gridViewCell.OptionsView.ColumnAutoWidth = false;
            this.gridViewCell.ViewCaption = "小区信息列表";
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 0;
            // 
            // gridColumnLac
            // 
            this.gridColumnLac.Caption = "位置区码(LAC)";
            this.gridColumnLac.FieldName = "Lac";
            this.gridColumnLac.Name = "gridColumnLac";
            this.gridColumnLac.Visible = true;
            this.gridColumnLac.VisibleIndex = 1;
            this.gridColumnLac.Width = 108;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "小区码（CI）";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 2;
            this.gridColumnCI.Width = 112;
            // 
            // gridColumnCellType
            // 
            this.gridColumnCellType.Caption = "小区类型";
            this.gridColumnCellType.FieldName = "BandType";
            this.gridColumnCellType.Name = "gridColumnCellType";
            this.gridColumnCellType.Visible = true;
            this.gridColumnCellType.VisibleIndex = 3;
            this.gridColumnCellType.Width = 101;
            // 
            // gridColumnCellCoverGridNum
            // 
            this.gridColumnCellCoverGridNum.Caption = "覆盖栅格总数";
            this.gridColumnCellCoverGridNum.FieldName = "StreetCellReCoverGridNumAll";
            this.gridColumnCellCoverGridNum.Name = "gridColumnCellCoverGridNum";
            this.gridColumnCellCoverGridNum.Visible = true;
            this.gridColumnCellCoverGridNum.VisibleIndex = 4;
            this.gridColumnCellCoverGridNum.Width = 130;
            // 
            // gridColumnCellReCoverGridNum
            // 
            this.gridColumnCellReCoverGridNum.Caption = "重叠覆盖栅格个数";
            this.gridColumnCellReCoverGridNum.FieldName = "StreetCellReCoverGridNum";
            this.gridColumnCellReCoverGridNum.Name = "gridColumnCellReCoverGridNum";
            this.gridColumnCellReCoverGridNum.Visible = true;
            this.gridColumnCellReCoverGridNum.VisibleIndex = 5;
            this.gridColumnCellReCoverGridNum.Width = 123;
            // 
            // gridColumnCellReCoverRate
            // 
            this.gridColumnCellReCoverRate.Caption = "重叠覆盖栅格比率";
            this.gridColumnCellReCoverRate.FieldName = "StreetCellReCoverRate";
            this.gridColumnCellReCoverRate.Name = "gridColumnCellReCoverRate";
            this.gridColumnCellReCoverRate.Visible = true;
            this.gridColumnCellReCoverRate.VisibleIndex = 6;
            this.gridColumnCellReCoverRate.Width = 127;
            // 
            // gridColumnCoverGridNum
            // 
            this.gridColumnCoverGridNum.Caption = "进入覆盖带栅格个数";
            this.gridColumnCoverGridNum.FieldName = "StreetCellRedunCoverGridNum";
            this.gridColumnCoverGridNum.Name = "gridColumnCoverGridNum";
            this.gridColumnCoverGridNum.Visible = true;
            this.gridColumnCoverGridNum.VisibleIndex = 7;
            this.gridColumnCoverGridNum.Width = 135;
            // 
            // gridColumnCellRedunGridNum
            // 
            this.gridColumnCellRedunGridNum.Caption = "冗余覆盖栅格个数";
            this.gridColumnCellRedunGridNum.FieldName = "StreetCellRedunCoverGridNum";
            this.gridColumnCellRedunGridNum.Name = "gridColumnCellRedunGridNum";
            this.gridColumnCellRedunGridNum.Visible = true;
            this.gridColumnCellRedunGridNum.VisibleIndex = 8;
            this.gridColumnCellRedunGridNum.Width = 111;
            // 
            // gridColumnCellRedunGridRate
            // 
            this.gridColumnCellRedunGridRate.Caption = "冗余覆盖栅格比率";
            this.gridColumnCellRedunGridRate.FieldName = "StreetCellRedunCoverRate";
            this.gridColumnCellRedunGridRate.Name = "gridColumnCellRedunGridRate";
            this.gridColumnCellRedunGridRate.Visible = true;
            this.gridColumnCellRedunGridRate.VisibleIndex = 9;
            this.gridColumnCellRedunGridRate.Width = 108;
            // 
            // advBandedGridViewStreet
            // 
            this.advBandedGridViewStreet.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBandRegionInfo,
            this.gridBandRelativeCanUsed900,
            this.gridBandAbsoluteCanUsed900,
            this.gridBandRelativeCanUsed1800,
            this.gridBandAbsoluteCanUsed1800});
            this.advBandedGridViewStreet.ChildGridLevelName = "ScanEstimateCellInfoList";
            this.advBandedGridViewStreet.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumnGridName,
            this.GridColumnRelative9001,
            this.GridColumnRelative9002,
            this.GridColumnRelative9003,
            this.GridColumnRelative9004,
            this.GridColumnAbsolute9001,
            this.GridColumnAbsolute9002,
            this.GridColumnAbsolute9003,
            this.GridColumnAbsolute9004,
            this.GridColumnRelative18001,
            this.GridColumnRelative18002,
            this.GridColumnRelative18003,
            this.GridColumnRelative18004,
            this.GridColumnAbsolute18001,
            this.GridColumnAbsolute18002,
            this.GridColumnAbsolute18003,
            this.GridColumnAbsolute18004});
            this.advBandedGridViewStreet.GridControl = this.gridControl1;
            this.advBandedGridViewStreet.GroupPanelText = "请拖拉需要聚合的列到这里...";
            this.advBandedGridViewStreet.Name = "advBandedGridViewStreet";
            this.advBandedGridViewStreet.OptionsCustomization.AllowBandMoving = false;
            this.advBandedGridViewStreet.OptionsDetail.AllowZoomDetail = false;
            this.advBandedGridViewStreet.OptionsPrint.AutoWidth = false;
            this.advBandedGridViewStreet.OptionsPrint.ExpandAllDetails = true;
            this.advBandedGridViewStreet.OptionsPrint.PrintDetails = true;
            this.advBandedGridViewStreet.OptionsPrint.PrintPreview = true;
            // 
            // gridColumnGridName
            // 
            this.gridColumnGridName.Caption = "区域名称";
            this.gridColumnGridName.FieldName = "regionName";
            this.gridColumnGridName.Name = "gridColumnGridName";
            this.gridColumnGridName.Visible = true;
            this.gridColumnGridName.Width = 84;
            // 
            // gridViewGrid
            // 
            this.gridViewGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnGridReCoverNum,
            this.gridColumnGridReCoverNum900,
            this.gridColumnGridReCoverNum1800,
            this.gridColumnGridReCoverRate,
            this.gridColumnGridStructIndexNum,
            this.gridColumnGridStructIndexNum900,
            this.gridColumnGridStructIndexNum1800,
            this.gridColumnStructIndexRate});
            this.gridViewGrid.GridControl = this.gridControl1;
            this.gridViewGrid.GroupPanelText = "请拖拉需要聚合的列到这里...";
            this.gridViewGrid.Name = "gridViewGrid";
            this.gridViewGrid.OptionsDetail.AllowZoomDetail = false;
            this.gridViewGrid.OptionsView.ColumnAutoWidth = false;
            this.gridViewGrid.PaintStyleName = "WindowsXP";
            this.gridViewGrid.ViewCaption = "栅格信息列表";
            // 
            // gridColumnGridReCoverNum
            // 
            this.gridColumnGridReCoverNum.Caption = "重叠覆盖栅格个数";
            this.gridColumnGridReCoverNum.FieldName = "ReCoverGridRate";
            this.gridColumnGridReCoverNum.Name = "gridColumnGridReCoverNum";
            this.gridColumnGridReCoverNum.Visible = true;
            this.gridColumnGridReCoverNum.VisibleIndex = 0;
            // 
            // gridColumnGridReCoverNum900
            // 
            this.gridColumnGridReCoverNum900.Caption = "重叠覆盖栅格个数900";
            this.gridColumnGridReCoverNum900.FieldName = "ReCoverGridNum_900";
            this.gridColumnGridReCoverNum900.Name = "gridColumnGridReCoverNum900";
            this.gridColumnGridReCoverNum900.Visible = true;
            this.gridColumnGridReCoverNum900.VisibleIndex = 1;
            // 
            // gridColumnGridReCoverNum1800
            // 
            this.gridColumnGridReCoverNum1800.Caption = "重叠覆盖栅格个数1800";
            this.gridColumnGridReCoverNum1800.FieldName = "ReCoverGridNum_1800";
            this.gridColumnGridReCoverNum1800.Name = "gridColumnGridReCoverNum1800";
            this.gridColumnGridReCoverNum1800.Visible = true;
            this.gridColumnGridReCoverNum1800.VisibleIndex = 2;
            // 
            // gridColumnGridReCoverRate
            // 
            this.gridColumnGridReCoverRate.Caption = "道路重叠覆盖率";
            this.gridColumnGridReCoverRate.FieldName = "ReCoverGridNum";
            this.gridColumnGridReCoverRate.Name = "gridColumnGridReCoverRate";
            this.gridColumnGridReCoverRate.Visible = true;
            this.gridColumnGridReCoverRate.VisibleIndex = 3;
            // 
            // gridColumnGridStructIndexNum
            // 
            this.gridColumnGridStructIndexNum.Caption = "结构指数不合格栅格个数";
            this.gridColumnGridStructIndexNum.FieldName = "StructIndexGridNum";
            this.gridColumnGridStructIndexNum.Name = "gridColumnGridStructIndexNum";
            this.gridColumnGridStructIndexNum.Visible = true;
            this.gridColumnGridStructIndexNum.VisibleIndex = 4;
            // 
            // gridColumnGridStructIndexNum900
            // 
            this.gridColumnGridStructIndexNum900.Caption = "结构指数不合格栅格个数900";
            this.gridColumnGridStructIndexNum900.FieldName = "StructIndexGridNum_900";
            this.gridColumnGridStructIndexNum900.Name = "gridColumnGridStructIndexNum900";
            this.gridColumnGridStructIndexNum900.Visible = true;
            this.gridColumnGridStructIndexNum900.VisibleIndex = 5;
            // 
            // gridColumnGridStructIndexNum1800
            // 
            this.gridColumnGridStructIndexNum1800.Caption = "结构指数不合格栅格个数1800";
            this.gridColumnGridStructIndexNum1800.FieldName = "StructIndexGridNum_1800";
            this.gridColumnGridStructIndexNum1800.Name = "gridColumnGridStructIndexNum1800";
            this.gridColumnGridStructIndexNum1800.Visible = true;
            this.gridColumnGridStructIndexNum1800.VisibleIndex = 6;
            // 
            // gridColumnStructIndexRate
            // 
            this.gridColumnStructIndexRate.Caption = "道路结构指数栅格不合格率";
            this.gridColumnStructIndexRate.FieldName = "StructIndexGridRate";
            this.gridColumnStructIndexRate.Name = "gridColumnStructIndexRate";
            this.gridColumnStructIndexRate.Visible = true;
            this.gridColumnStructIndexRate.VisibleIndex = 7;
            // 
            // GridColumnRelative9001
            // 
            this.GridColumnRelative9001.Caption = "0≤X＜4";
            this.GridColumnRelative9001.FieldName = "Relative9001";
            this.GridColumnRelative9001.Name = "GridColumnRelative9001";
            this.GridColumnRelative9001.Visible = true;
            this.GridColumnRelative9001.Width = 76;
            // 
            // GridColumnRelative9002
            // 
            this.GridColumnRelative9002.Caption = "4≤X＜7";
            this.GridColumnRelative9002.FieldName = "Relative9002";
            this.GridColumnRelative9002.Name = "GridColumnRelative9002";
            this.GridColumnRelative9002.Visible = true;
            this.GridColumnRelative9002.Width = 76;
            // 
            // GridColumnRelative9003
            // 
            this.GridColumnRelative9003.Caption = "7≤X＜10";
            this.GridColumnRelative9003.FieldName = "Relative9003";
            this.GridColumnRelative9003.Name = "GridColumnRelative9003";
            this.GridColumnRelative9003.Visible = true;
            this.GridColumnRelative9003.Width = 76;
            // 
            // GridColumnRelative9004
            // 
            this.GridColumnRelative9004.Caption = "10≤X＜+∞";
            this.GridColumnRelative9004.FieldName = "Relative9004";
            this.GridColumnRelative9004.Name = "GridColumnRelative9004";
            this.GridColumnRelative9004.Visible = true;
            this.GridColumnRelative9004.Width = 78;
            // 
            // GridColumnAbsolute9001
            // 
            this.GridColumnAbsolute9001.Caption = "0≤X＜4";
            this.GridColumnAbsolute9001.FieldName = "Absolute9001";
            this.GridColumnAbsolute9001.Name = "GridColumnAbsolute9001";
            this.GridColumnAbsolute9001.Visible = true;
            this.GridColumnAbsolute9001.Width = 76;
            // 
            // GridColumnAbsolute9002
            // 
            this.GridColumnAbsolute9002.Caption = "4≤X＜7";
            this.GridColumnAbsolute9002.FieldName = "Absolute9002";
            this.GridColumnAbsolute9002.Name = "GridColumnAbsolute9002";
            this.GridColumnAbsolute9002.Visible = true;
            this.GridColumnAbsolute9002.Width = 76;
            // 
            // GridColumnAbsolute9003
            // 
            this.GridColumnAbsolute9003.Caption = "7≤X＜10";
            this.GridColumnAbsolute9003.FieldName = "Absolute9003";
            this.GridColumnAbsolute9003.Name = "GridColumnAbsolute9003";
            this.GridColumnAbsolute9003.Visible = true;
            this.GridColumnAbsolute9003.Width = 76;
            // 
            // GridColumnAbsolute9004
            // 
            this.GridColumnAbsolute9004.Caption = "10≤X＜+∞";
            this.GridColumnAbsolute9004.FieldName = "Absolute9004";
            this.GridColumnAbsolute9004.Name = "GridColumnAbsolute9004";
            this.GridColumnAbsolute9004.Visible = true;
            this.GridColumnAbsolute9004.Width = 78;
            // 
            // GridColumnRelative18001
            // 
            this.GridColumnRelative18001.Caption = "0≤X＜4";
            this.GridColumnRelative18001.FieldName = "Relative18001";
            this.GridColumnRelative18001.Name = "GridColumnRelative18001";
            this.GridColumnRelative18001.Visible = true;
            this.GridColumnRelative18001.Width = 77;
            // 
            // GridColumnRelative18002
            // 
            this.GridColumnRelative18002.Caption = "4≤X＜7";
            this.GridColumnRelative18002.FieldName = "Relative18002";
            this.GridColumnRelative18002.Name = "GridColumnRelative18002";
            this.GridColumnRelative18002.Visible = true;
            this.GridColumnRelative18002.Width = 77;
            // 
            // GridColumnRelative18003
            // 
            this.GridColumnRelative18003.Caption = "7≤X＜10";
            this.GridColumnRelative18003.FieldName = "Relative18003";
            this.GridColumnRelative18003.Name = "GridColumnRelative18003";
            this.GridColumnRelative18003.Visible = true;
            this.GridColumnRelative18003.Width = 77;
            // 
            // GridColumnRelative18004
            // 
            this.GridColumnRelative18004.Caption = "10≤X＜+∞";
            this.GridColumnRelative18004.FieldName = "Relative18004";
            this.GridColumnRelative18004.Name = "GridColumnRelative18004";
            this.GridColumnRelative18004.Visible = true;
            this.GridColumnRelative18004.Width = 79;
            // 
            // GridColumnAbsolute18001
            // 
            this.GridColumnAbsolute18001.Caption = "0≤X＜4";
            this.GridColumnAbsolute18001.FieldName = "Absolute18001";
            this.GridColumnAbsolute18001.Name = "GridColumnAbsolute18001";
            this.GridColumnAbsolute18001.Visible = true;
            this.GridColumnAbsolute18001.Width = 77;
            // 
            // GridColumnAbsolute18002
            // 
            this.GridColumnAbsolute18002.Caption = "4≤X＜7";
            this.GridColumnAbsolute18002.FieldName = "Absolute18002";
            this.GridColumnAbsolute18002.Name = "GridColumnAbsolute18002";
            this.GridColumnAbsolute18002.Visible = true;
            this.GridColumnAbsolute18002.Width = 77;
            // 
            // GridColumnAbsolute18003
            // 
            this.GridColumnAbsolute18003.Caption = "7≤X＜10";
            this.GridColumnAbsolute18003.FieldName = "Absolute18003";
            this.GridColumnAbsolute18003.Name = "GridColumnAbsolute18003";
            this.GridColumnAbsolute18003.Visible = true;
            this.GridColumnAbsolute18003.Width = 77;
            // 
            // GridColumnAbsolute18004
            // 
            this.GridColumnAbsolute18004.Caption = "10≤X＜+∞";
            this.GridColumnAbsolute18004.FieldName = "Absolute18004";
            this.GridColumnAbsolute18004.Name = "GridColumnAbsolute18004";
            this.GridColumnAbsolute18004.Visible = true;
            this.GridColumnAbsolute18004.Width = 80;
            // 
            // gridBandRegionInfo
            // 
            this.gridBandRegionInfo.Caption = "区域信息";
            this.gridBandRegionInfo.Columns.Add(this.gridColumnGridName);
            this.gridBandRegionInfo.Name = "gridBandRegionInfo";
            this.gridBandRegionInfo.Width = 84;
            // 
            // gridBandRelativeCanUsed900
            // 
            this.gridBandRelativeCanUsed900.Caption = "900M相对可用信号数统计";
            this.gridBandRelativeCanUsed900.Columns.Add(this.GridColumnRelative9001);
            this.gridBandRelativeCanUsed900.Columns.Add(this.GridColumnRelative9002);
            this.gridBandRelativeCanUsed900.Columns.Add(this.GridColumnRelative9003);
            this.gridBandRelativeCanUsed900.Columns.Add(this.GridColumnRelative9004);
            this.gridBandRelativeCanUsed900.Name = "gridBandRelativeCanUsed900";
            this.gridBandRelativeCanUsed900.Width = 306;
            // 
            // gridBandAbsoluteCanUsed900
            // 
            this.gridBandAbsoluteCanUsed900.Caption = "900M绝对可用信号数统计";
            this.gridBandAbsoluteCanUsed900.Columns.Add(this.GridColumnAbsolute9001);
            this.gridBandAbsoluteCanUsed900.Columns.Add(this.GridColumnAbsolute9002);
            this.gridBandAbsoluteCanUsed900.Columns.Add(this.GridColumnAbsolute9003);
            this.gridBandAbsoluteCanUsed900.Columns.Add(this.GridColumnAbsolute9004);
            this.gridBandAbsoluteCanUsed900.Name = "gridBandAbsoluteCanUsed900";
            this.gridBandAbsoluteCanUsed900.Width = 306;
            // 
            // gridBandRelativeCanUsed1800
            // 
            this.gridBandRelativeCanUsed1800.Caption = "1800M相对可用信号数统计";
            this.gridBandRelativeCanUsed1800.Columns.Add(this.GridColumnRelative18001);
            this.gridBandRelativeCanUsed1800.Columns.Add(this.GridColumnRelative18002);
            this.gridBandRelativeCanUsed1800.Columns.Add(this.GridColumnRelative18003);
            this.gridBandRelativeCanUsed1800.Columns.Add(this.GridColumnRelative18004);
            this.gridBandRelativeCanUsed1800.Name = "gridBandRelativeCanUsed1800";
            this.gridBandRelativeCanUsed1800.Width = 310;
            // 
            // gridBandAbsoluteCanUsed1800
            // 
            this.gridBandAbsoluteCanUsed1800.Caption = "900M绝对可用信号数统计";
            this.gridBandAbsoluteCanUsed1800.Columns.Add(this.GridColumnAbsolute18001);
            this.gridBandAbsoluteCanUsed1800.Columns.Add(this.GridColumnAbsolute18002);
            this.gridBandAbsoluteCanUsed1800.Columns.Add(this.GridColumnAbsolute18003);
            this.gridBandAbsoluteCanUsed1800.Columns.Add(this.GridColumnAbsolute18004);
            this.gridBandAbsoluteCanUsed1800.Name = "gridBandAbsoluteCanUsed1800";
            this.gridBandAbsoluteCanUsed1800.Width = 311;
            // 
            // OverlapCoverForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(946, 442);
            this.Controls.Add(this.gridControl1);
            this.Name = "OverlapCoverForm";
            this.Text = "OverlapCoverForm";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.advBandedGridViewStreet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.BandedGrid.AdvBandedGridView advBandedGridViewStreet;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnGridName;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative9001;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative9002;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative9003;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative9004;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute9001;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute9002;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute9003;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute9004;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative18001;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative18002;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCell;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLac;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellCoverGridNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellReCoverGridNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellReCoverRate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCoverGridNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellRedunGridNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellRedunGridRate;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridReCoverNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridReCoverNum900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridReCoverNum1800;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridReCoverRate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridStructIndexNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridStructIndexNum900;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridStructIndexNum1800;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStructIndexRate;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative18003;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnRelative18004;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBandRegionInfo;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBandRelativeCanUsed900;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBandAbsoluteCanUsed900;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBandRelativeCanUsed1800;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBandAbsoluteCanUsed1800;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute18001;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute18002;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute18003;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn GridColumnAbsolute18004;
    }
}