﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;

namespace MasterCom.Util
{
    public class BcpHelper<T>
    {
        public delegate void DealData(BCPStore bcp, T data);

        public static string Bcp(SqlConnectionStringBuilder sb,string tableName, DealData func, T data)
        {
            string errMsg = "";
            using (SqlConnection sqlConn = new SqlConnection(sb.ToString()))
            {
                BCPStore bcp = new BCPStore();
                bcp.Init(sqlConn, tableName);

                func(bcp, data);

                try
                {
                    bcp.Flush(sqlConn, true);
                }
                catch (Exception ee)
                {
                    errMsg = ee.Message + ee.Source + ee.StackTrace;
                }
            }

            return errMsg;
        }
    }
}
