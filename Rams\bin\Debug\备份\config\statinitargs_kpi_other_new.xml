<?xml version="1.0"?>
<Configs>
  <Config name="StatParamCfg">
    <Item name="configs" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">其他统计信息</Item>
        <Item typeName="String" key="FDesc" />
        <Item typeName="String" key="FName" />
        <Item typeName="IList" key="children">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">CDMA参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">基本参数</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Fileid</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">TestType</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">MS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0803</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FileTime</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0804</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0805</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0806</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0807</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">TLLongitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0808</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">TLLatitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0809</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">BRLongitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_080A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">BRLatitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_080B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LAC1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_080C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">RAC1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_080D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CI1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_080E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LAC1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_080F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">RAC1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0810</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CI1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0811</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LAC2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0812</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">RAC2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0813</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CI2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0814</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LAC3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0815</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">RAC3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0816</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CI3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0817</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LAC4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0818</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">RAC4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0819</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CI4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_081A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_DL_CDMA1X(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_081D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_DL_CDMA1X(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0821</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA_VOICE(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_082A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA_VOICE(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0829</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA1X(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_086C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA1X(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_086D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA2000_EVDO(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_086E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA2000_EVDO(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_086F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_DL_CDMA2000_EVDO</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0839</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_DL_CDMA2000_EVDO</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_083A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA2000_VIDEO(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0854</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA2000_VIDEO(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0855</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA_DEDICATE(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0870</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA_DEDICATE(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0871</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA_IDLE(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0872</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA_IDLE(米)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_0873</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_084C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Cx_084D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">CDMA2000参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_CDMA2000_EVDO(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_086E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_CDMA2000_EVDO(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_086F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_CDMA2000_EVDO(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0839</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_CDMA2000_EVDO(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_083A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_CDMA1X</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_081D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_CDMA1X</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_0821</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_084C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ex_084D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">GSM参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_080C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_080D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_080E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0860</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0861</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GPRS(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0866</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GPRS(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0867</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_EDGE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0868</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_EDGE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0869</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_GPRS(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_081B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_GPRS(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_081F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_EDGE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_081C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_EDGE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0820</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_MODE_GSM_VOICE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0823</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_MODE_GSM_VOICE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0824</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_IDLE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0835</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE_IDLE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0836</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_DEDICATED(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0837</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE_DEDICATED(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_0838</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_084C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Mx_084D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">GSM MTR参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_080C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_080D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_080E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GPRS(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_081B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GPRS(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_081F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_EDGE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_081C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_EDGE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0820</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0823</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0824</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_IDLE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0835</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE_IDLE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0836</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_DEDICATED(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0837</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE_DEDICATED(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_0838</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_084C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Ux_084D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">LTE参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_LTE_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0874</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_LTE_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Lte_0875</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">GSM扫频参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_SCAN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0856</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_SCAN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0857</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BCCH</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0846</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BSIC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0847</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CellName</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Gc_0848</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">TDSCDMA扫频参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_SCAN_TD</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0858</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_SCAN_TD</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0859</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_080C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_080D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_080E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC1</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_080F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC1</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0810</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI1</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0811</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC2</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0812</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC2</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0813</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI2</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0814</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC3</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0815</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC3</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0816</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI3</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0817</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LAC4</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0818</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RAC4</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0819</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CI4</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_081A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GRPS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_081B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_EDGE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_081C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_CDMA1X</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_081D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_WCDMA_DATA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_081E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GPRS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_081F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_EDGE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0820</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_CDMA1X</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0821</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_WCDMA_DATA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0822</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0823</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_GSM_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0824</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TD_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0825</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TD_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0826</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_WCDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0827</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_WCDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0828</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_CDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0829</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_CDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_082A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_WCDMA_VIDEO</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_082B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_WCDMA_VIDEO</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_082C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TDSCDMA_PS64</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_082D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TDSCDMA_PS64</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_082E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TDSCDMA_PS128</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_082F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TDSCDMA_PS128</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0830</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TDSCDMA_PS384</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0831</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TDSCDMA_PS384</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0832</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TDSCDMA_HSPA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0833</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TDSCDMA_HSPA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0834</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_IDLE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0835</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_IDLE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0836</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_DEDICATED</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0837</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_GSM_VOICE_DEDICATED</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0838</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_CDMA2000_EVDO</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0839</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_CDMA2000_EVDO</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_083A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum_GSM_V</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_083C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum_TD_V</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_083D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum_WCDMA_V</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_083E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum_CDMA_V</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_083F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BCCH</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0846</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BSIC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0847</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CellName</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0848</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CellID</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_0849</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Channel</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_084A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CPI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tc_084B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">WCDMA扫频参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_SCAN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0856</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_SCAN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0857</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BCCH</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0846</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BSIC</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0847</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CellName</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wc_0848</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">TDSCDMA参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TD_VOICE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0864</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TD_VOICE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0865</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_MODE_TD_VOICE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0825</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_MODE_TD_VOICE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0826</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_MODE_GSM(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0823</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_MODE_GSM(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0824</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TD_VIDEO(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0850</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TD_VIDEO(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0851</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_TD_DATA(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_084E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_TD_DATA(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_084F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_GPRS(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_081B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_GPRS(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_081F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_EDGE(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_081C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_EDGE(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0820</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_TD_PS64(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_082D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_TD_PS64(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_082E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_TD_PS128(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_082F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_TD_PS128(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0830</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_TD_PS384(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0831</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_TD_PS384(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0832</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_TD_HSPA(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0833</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_TD_HSPA(米)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_0834</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_084C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Tx_084D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">WCDMA参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_WCDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0862</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_MODE_GSM</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0824</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_MODE_WCDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0827</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_MODE_WCDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0828</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_WCDMA_VOICE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0863</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_MODE_GSM</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0823</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_MODE_WCDMA_VIDEO</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_082B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_MODE_WCDMA_VIDEO</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_082C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_WCDMA_DATA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_086A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_WCDMA_DATA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_086B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_WCDMA_DATA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_081E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_WCDMA_DATA</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0822</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_GPRS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_081B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_GPRS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_081F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DL_EDGE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_081C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DL_EDGE</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_0820</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_084C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wx_084D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">WLAN参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_WLAN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_085E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_WLAN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_085F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0808</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TLLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0809</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLongitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_080A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">BRLatitude</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_080B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>