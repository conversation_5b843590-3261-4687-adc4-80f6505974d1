﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    public partial class RepeatDetailResultForm : MinCloseForm
    {
        public RepeatDetailResultForm(MainModel mModel) : base(mModel)
        {
            InitializeComponent();
        }

        public void FillData(object result)
        {
            gridControl1.DataSource = result;
            gridControl1.RefreshDataSource();
        }
    }
}
