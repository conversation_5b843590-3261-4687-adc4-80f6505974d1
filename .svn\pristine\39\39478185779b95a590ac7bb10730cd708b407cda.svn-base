﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FastFadingDlg_GSCAN : BaseDialog
    {
        public FastFadingDlg_GSCAN()
        {
            InitializeComponent();
            cbxBandType.SelectedIndex = 0;
        }

        public void GetFilterCondition(out int bandType, out int rxLevDValue, out int secondLast, out int secondFading, out int rxLevDValueFading)
        {
            bandType = cbxBandType.SelectedIndex;
            rxLevDValue = (int)numRxLevDValue.Value;
            secondLast = (int)numSecondLast.Value;
            secondFading = (int)numSecondFading.Value;
            rxLevDValueFading = (int)numRxLevDValueFading.Value;
        }
    }
}
