﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Mobius.Utility;
using System.Windows.Forms;
using System.Drawing;

namespace MasterCom.RAMS.Stat
{
    public abstract class ReportStatQueryBase : QueryKPIStatBase
    {
        public bool IsShowWaitBox { get; set; } = false;
        protected ReporterTemplate rptTemplate = null;
        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.area;
        }
        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            queryDistrictData(condition.DistrictID);
            afterRecieveAllData();
            if (IsShowResultForm)
            {
                fireShowResult();
            }
        }
        protected override void queryDistrictData(int districtID)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                string imgTriadIDSet = getStatImgNeededTriadID();
                if (string.IsNullOrEmpty(imgTriadIDSet))
                {
                    imgTriadIDSet = "1,1,1";
                }
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, imgTriadIDSet);
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.evtIDFileNameDic = new Dictionary<int, Dictionary<string, bool>>();
            this.imgCodeFileNameDic = new Dictionary<string, Dictionary<string, bool>>();
            Dictionary<string, bool> triadIDDic = new Dictionary<string, bool>();
            if (rptTemplate == null)
            {
                return "";
            }
            foreach (ColumnSet col in this.rptTemplate.Columns)
            {
                Dictionary<string, bool> tempDic = extractTriadID(col.Exp
                    , col.ServiceIDSet, ref this.imgCodeSvrIDDic, ref this.evtIDSvrIDDic
                    , col.FileNameKeyValueList, ref this.imgCodeFileNameDic, ref this.evtIDFileNameDic);
                if (tempDic != null)
                {
                    foreach (string id in tempDic.Keys)
                    {
                        triadIDDic[id] = true;
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string triadID in triadIDDic.Keys)
            {
                sb.Append(triadID);
                sb.Append(",");
            }
            if (sb.Length > 0)
            {//remove last ","
                sb = sb.Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }
        protected override void preparePackageCondition(Package package, TimePeriod period, params object[] reservedParams)
        {
            base.preparePackageCondition(package, period, reservedParams);

            List<int> evtIdList = getNeedEventIdList();
            if (isQueringEvent && evtIdList.Count > 0)
            {
                StringBuilder sbEventIds = new StringBuilder();
                foreach (int evtId in evtIdList)
                {
                    sbEventIds.Append(evtId);
                    sbEventIds.Append(",");
                }
                if (sbEventIds.Length > 0)
                {
                    sbEventIds = sbEventIds.Remove(sbEventIds.Length - 1, 1);
                }
                string str = string.Format("iEventID in ({0})", sbEventIds.ToString());
                package.Content.AddParam((byte)OpOptionDef.DIYSql);
                package.Content.AddParam(str);
            }
        }

        protected virtual List<int> getNeedEventIdList()
        {
            List<int> evtIdList = new List<int>();
            if (evtIDSvrIDDic != null && evtIDSvrIDDic.Count > 0)
            {
                evtIdList = new List<int>(evtIDSvrIDDic.Keys);
            }
            return evtIdList;
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            //DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                bool isStop = judgeNeedsStop(ref index, ref progress);
                if (isStop)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                KPIStatDataBase singleStatData = null;
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    recieveAndHandleSpecificStatData(package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END ||
                    package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    break;
                }
                else
                {
                    System.Diagnostics.Debug.Assert(false, package.Content.Type.ToString());
                    break;
                }
                #endregion
            }
        }

        private bool judgeNeedsStop(ref int index, ref int progress)
        {
            if (IsShowWaitBox)
            {
                if (WaitBox.CancelRequest)
                {
                    return true;
                }
                setProgressPercent(ref index, ref progress);
            }
            if (mainModel.BackgroundStopRequest)
            {
                return true;
            }
            return false;
        }

        protected override void recieveInfo_Event(ClientProxy clientProxy, params object[] reservedParams)
        {
            //DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> colDefSet = new List<ColumnDefItem>();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                bool isStop = judgeNeedsStop(ref index, ref progress);
                if (isStop)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, colDefSet);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isColDefContent(package, colDefSet))
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT
                   || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000
                || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE
                || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE_FDD
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_NR)
                {
                    NREventHelper.ReSetIntCI(colDefSet);
                    dealEvt(colDefSet, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT_NR)
                {
                    NREventHelper.SetLongCI(colDefSet);
                    dealEvt(colDefSet, package);
                }
                else if (package.Content.Type == ResponseType.END ||
                    package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    break;
                }
                else
                {
                    System.Diagnostics.Debug.Assert(false, package.Content.Type.ToString());
                    break;
                }
                #endregion
            }
        }

        private void dealEvt(List<ColumnDefItem> colDefSet, Package package)
        {
            Event evt = Event.Create(package.Content, colDefSet);
            if (isQueryAllParams || this.evtIDSvrIDDic.ContainsKey(evt.ID))
            {
                handleStatEvent(evt);
            }
        }

        protected bool IsKeyIdColumn(string exp, out string keyFieldRet)
        {
            try
            {
                string expReal = exp.Substring(1, exp.Length - 2);
                string real = expReal.Trim();
                if (real[0] == 'k')
                {
                    keyFieldRet = real;
                    return true;
                }
            }
            catch
            {
                //continue
            }
            keyFieldRet = "";
            return false;
        }

        protected string GetKeyUnionString(ReporterTemplate tpl, StatInfoBase statInfo)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < tpl.KeyCount; i++)
            {
                ColumnSet cs = tpl.Columns[i];
                string keyFieldRet = "";
                string val = "";
                if (IsKeyIdColumn(cs.Exp, out keyFieldRet))
                {
                    val = GetKeyValue(keyFieldRet, tpl, statInfo);
                }
                else
                {
                    val = statInfo.KPIData.CalcFormula((CarrierType)cs.carrierId, cs.momt, cs.Exp, cs.decPlace
                        , cs.Title, cs.ServiceIDSet, cs.FileNameKeyValueList).ToString();
                }
                sb.Append(val);
                sb.Append("&");
            }
            return sb.ToString();
        }

        protected abstract string GetKeyValue(string keyFieldRet, ReporterTemplate tpl, StatInfoBase statInfo);

        protected static string GetKeyValueBase(string keyFieldRet, ReporterTemplate tpl
            , int areaTypeId, int areaId, int districtId, StatInfoBase statInfo)
        {
            DTDataHeader fileHeader = statInfo.FileHeader;
            string val = "";
            if (keyFieldRet == KeyName.OrderFlag
              || keyFieldRet == KeyName.OrderCheckRound
              || keyFieldRet == KeyName.OrderCityName
              || keyFieldRet == KeyName.OrderAreaTypeName
              || keyFieldRet == KeyName.OrderAreaName)
            {
                if (statInfo.ReservedDic != null)
                {
                    return statInfo.ReservedDic[keyFieldRet].ToString();
                }
                else
                {
                    return "-";
                }
            }
            else if (keyFieldRet == "kAreaId")
            {
                Dictionary<int, IDNamePair> areaIDNameDic = null;

                if (!CommonNoGisStatForm.usingProvince)
                {
                    if (CommonNoGisStatForm.AllAreaNameDic.TryGetValue(areaTypeId, out areaIDNameDic))
                    {
                        IDNamePair pair = null;
                        if (areaIDNameDic.TryGetValue(areaId, out pair))
                        {
                            return pair.Name;
                        }
                    }
                }
                else
                {
                    if (CommonNoGisStatForm.DistrictAllAreaNameDic.ContainsKey(districtId))
                    {
                        Dictionary<int, Dictionary<int, IDNamePair>> AllAreaNameDic = CommonNoGisStatForm.DistrictAllAreaNameDic[districtId];
                        if (AllAreaNameDic.TryGetValue(areaTypeId, out areaIDNameDic))
                        {
                            IDNamePair pair = null;
                            if (areaIDNameDic.TryGetValue(areaId, out pair))
                            {
                                return pair.Name;
                            }
                        }
                    }
                }
                val = areaId.ToString();
            }
            else if (keyFieldRet == "kAreaTypeId" || keyFieldRet == "kAreaTypeIdInt")
            {
                val = areaTypeId.ToString();
            }
            else if (keyFieldRet == "kAreaIdInt")
            {
                val = areaId.ToString();
            }
            else if (keyFieldRet == "kDistrictId")
            {
                val = districtId.ToString();
            }
            else if (keyFieldRet == "kDbValue")
            {
                if (fileHeader != null)
                {
                    val = fileHeader.DBValue.ToString();
                }
                else
                {
                    val = districtId.ToString();
                }
            }
            else if (keyFieldRet == "kAttribute")
            {
                val = "";
                if (CommonNoGisStatForm.cqtCityNameDic.ContainsKey(districtId)
                    && CommonNoGisStatForm.cqtCityNameDic[districtId].ContainsKey(areaId))
                    val = "" + CommonNoGisStatForm.cqtCityNameDic[districtId][areaId].Strcomment;
            }
            else if (keyFieldRet == "kRangeType")
            {
                val = "";
                if (CommonNoGisStatForm.cqtCityNameMultiDimensionDic.ContainsKey(districtId)
                    && CommonNoGisStatForm.cqtCityNameMultiDimensionDic[districtId].ContainsKey(areaId))
                    val = "" + CommonNoGisStatForm.cqtCityNameMultiDimensionDic[districtId][areaId].Strareaname;
            }
            else if (keyFieldRet == "kGridType")
            {
                val = "";
                if (CommonNoGisStatForm.cqtCityNameMultiDimensionDic.ContainsKey(districtId)
                    && CommonNoGisStatForm.cqtCityNameMultiDimensionDic[districtId].ContainsKey(areaId))
                    val = "" + CommonNoGisStatForm.cqtCityNameMultiDimensionDic[districtId][areaId].Strcomment;
            }
            else if (keyFieldRet == "kStrCoverType")
            {
                val = "";
                if (CommonNoGisStatForm.cqtCityNameMultiDimensionDic.ContainsKey(districtId)
                    && CommonNoGisStatForm.cqtCityNameMultiDimensionDic[districtId].ContainsKey(areaId))
                    val = "" + CommonNoGisStatForm.cqtCityNameMultiDimensionDic[districtId][areaId].Strareatypename;
                if (val == "")
                    val = "非室分";
            }
            else if (fileHeader != null)
            {
                if (keyFieldRet == "kDwId")
                {
                    val = "" + fileHeader.CompanyID;
                }
                else if (keyFieldRet == "kProjId")
                {
                    val = "" + fileHeader.ProjectID;
                }
                else if (keyFieldRet == "kTimeValue")
                {
                    val = tpl.timeFormat.Int2DateString(fileHeader.BeginTime);
                }
                else if (keyFieldRet == "kEqpId")
                {
                    val = fileHeader.DeviceType.ToString();
                }
                else if (keyFieldRet == "kFileId")
                {
                    val = fileHeader.ID.ToString();
                }
                else if (keyFieldRet == "kFileName")
                {
                    val = fileHeader.Name;
                }
                else if (keyFieldRet == "kSvTypeId")
                {
                    val = fileHeader.ServiceType.ToString();
                }
                else if (keyFieldRet == "kCarrierId")
                {
                    val = fileHeader.CarrierType.ToString();
                }
                else if (keyFieldRet == "kFileTypeId")
                {
                    val = fileHeader.FileType.ToString();
                }
                else if (keyFieldRet == "kRound")
                {
                    val = fileHeader.Batch.ToString();
                }
                else if (keyFieldRet == "kSubTestType")
                {
                    val = "无子地点";
                    if (fileHeader.Name.Split('@').Length > 1)
                        val = "" + fileHeader.Name.Split('@')[1].Split('_')[0];
                }
                else if (keyFieldRet == "kLogAreaTypeName")
                {
                    string areaType = AreaManager.GetInstance().GetAreaTypeDesc(fileHeader.AreaTypeID);
                    if (!string.IsNullOrEmpty(areaType))
                    {
                        return areaType;
                    }
                }
                else if (keyFieldRet == "kLogAreaName")
                {
                    string areaName = AreaManager.GetInstance().GetAreaDesc(fileHeader.AreaTypeID, fileHeader.AreaID);
                    if (!string.IsNullOrEmpty(areaName))
                    {
                        return areaName;
                    }
                }
                else if (keyFieldRet == "kAreaComment")
                {
                    CategoryEnumItem item = AreaManager.GetInstance()[fileHeader.AreaTypeID, fileHeader.AreaID];
                    if (item == null)
                    {
                        return "-";
                    }
                    else if (!string.IsNullOrEmpty(item.Description))
                    {
                        return item.Description;
                    }
                    else
                    {
                        return string.Empty;
                    }
                }
                else if (keyFieldRet == "kLogAreaTypeId")
                {
                    return fileHeader.AreaTypeID.ToString();
                }
                else if (keyFieldRet == "kLogAreaId")
                {
                    return fileHeader.AreaID.ToString();
                }
                else if (keyFieldRet.Contains("kLogNameSubString|"))
                {
                    int begin = "kLogNameSubString|".Length;
                    StringBuilder token = new StringBuilder();
                    while (keyFieldRet.Length > begin)
                    {
                        char x = keyFieldRet[begin];
                        if (x == '|')
                        {
                            begin++;
                            break;
                        }
                        token.Append(x);
                        begin++;
                    }
                    StringBuilder idxStr = new StringBuilder();
                    begin++;
                    while (keyFieldRet.Length > begin)
                    {
                        char x = keyFieldRet[begin];
                        if (x == ']')
                        {
                            break;
                        }
                        idxStr.Append(x);
                        begin++;
                    }
                    int idx = -1;
                    if (int.TryParse(idxStr.ToString(), out idx))
                    {
                        string[] arr = fileHeader.Name.Split(
                     new string[] { token.ToString() }, StringSplitOptions.RemoveEmptyEntries);
                        if (arr.Length > idx)
                        {
                            return arr[idx];
                        }
                    }
                }
            }
            return val;
        }

        public virtual void ShowReport<T>(ReporterTemplate tpl, List<T> retdatas, SortedDataGridView dataGrid)
            where T : StatInfoBase 
        {
            dataGrid.Columns.Clear();
            dataGrid.Rows.Clear();
            //初始化列头
            for (int i = 0; i < tpl.Columns.Count; i++)
            {
                ColumnSet column = tpl.Columns[i];
                dataGrid.Columns.Add("Column" + i, column.Title);
                if (i < tpl.KeyCount)
                {
                    dataGrid.Columns[i].Frozen = true;
                }
            }

            if (retdatas == null || retdatas.Count == 0)
            {
                return;
            }
            retdatas.Sort();
            dataGrid.Rows.Add(retdatas.Count);
            for (int r = 0; r < retdatas.Count; r++)
            {
                StatInfoBase statInfo = retdatas[r];
                showReportRow(tpl, dataGrid.Rows[r], statInfo);
            }
        }
        private void showReportRow(ReporterTemplate tpl, DataGridViewRow dataRow, StatInfoBase statInfo)
        {
            for (int i = 0; i < tpl.Columns.Count; i++)
            {
                DataGridViewCell dataCell = dataRow.Cells[i];

                double? hilightValue = null;
                dataCell.Value = getColumnSetValue(statInfo, tpl, i, ref hilightValue, false);

                #region 颜色设置

                ColumnSet column = tpl.Columns[i];
                if (hilightValue != null && column.hilight && column.HiList.Count > 0)//是否设置了按取值高亮Hilight
                {
                    float fv = (float)hilightValue;
                    fv = (float)Math.Round(fv);
                    setCellColor(dataCell, column, fv);
                }
                #endregion
            }
        }

        private static void setCellColor(DataGridViewCell dataCell, ColumnSet column, float fv)
        {
            for (int hi = 0; hi < column.HiList.Count; hi++)
            {
                HiLighter lighter = column.HiList[hi];
                if (fv >= lighter.MinV && fv <= lighter.MaxV)
                {
                    if (lighter.bgColor != Color.Empty)//设置了背景色
                    {
                        dataCell.Style.BackColor = lighter.bgColor;
                    }
                    if (lighter.foreColor != Color.Empty)//设置了前景色
                    {
                        dataCell.Style.ForeColor = lighter.foreColor;
                    }
                    break;
                }
            }
        }

        protected object getColumnSetValue(StatInfoBase statInfo, ReporterTemplate tpl
           , int curColumnIndex, bool showAllKeyIdColumn = true)
        {
            double? hilightValue = null;
            return getColumnSetValue(statInfo, tpl, curColumnIndex, ref hilightValue, showAllKeyIdColumn);
        }

        protected object getColumnSetValue(StatInfoBase statInfo, ReporterTemplate tpl
            , int curColumnIndex, ref double? hilightValue, bool showAllKeyIdColumn)
        {
            object value = "";
            ColumnSet column = tpl.Columns[curColumnIndex];

            #region 汇总数据的纬度列显示
            getTotalValue(statInfo, tpl, curColumnIndex, ref value, column);
            if (value.ToString() != "")
            {
                return value;
            }
            #endregion

            string keyRet;
            if (string.IsNullOrEmpty(column.Exp))
            {
                return "-";
            }
            else if (column.Exp.IndexOf('{') == -1)
            {
                return column.Exp;
            }
            else if (IsKeyIdColumn(column.Exp, out keyRet))
            {
                #region KeyIdColumn
                if (!showAllKeyIdColumn && curColumnIndex >= tpl.KeyCount)
                {
                    return "-";
                }

                value = getKeyIdColumn(statInfo, tpl, keyRet);
                #endregion
            }
            else
            {
                if (statInfo == null)
                {
                    return "-";
                }

                #region 文件名或ID集合
                ReservedField field;
                if (statInfo.FileIDDic != null && StatReserved.IsContainsReserved(column.Exp, out field))
                {
                    return getFileIDOrName(statInfo, ref value, field);
                }
                #endregion

                #region KPI指标信息
                value = getKPIValue(statInfo, ref hilightValue, column);
                #endregion
            }
            if (value.ToString() == "")
            {
                value = "-";
            }
            return value;
        }

        private static void getTotalValue(StatInfoBase statInfo, ReporterTemplate tpl, int curColumnIndex, ref object value, ColumnSet column)
        {
            if (statInfo != null && statInfo.IsSum && curColumnIndex < tpl.KeyCount)
            {
                if (statInfo is AreaStatInfo && !string.IsNullOrEmpty(column.Exp) && column.Exp.Contains("kAreaId"))//区域统计汇总特殊处理
                {
                    //
                }
                else
                {
                    if (curColumnIndex == 0)
                    {
                        value = "汇总";
                    }
                    else
                    {
                        value = "-";
                    }
                }
            }
        }

        private object getKeyIdColumn(StatInfoBase statInfo, ReporterTemplate tpl, string keyRet)
        {
            object value;
            long lVal;
            string val = GetKeyValue(keyRet, tpl, statInfo);
            if (long.TryParse(val, out lVal))
            {
                if (keyRet == "kAreaId")//已在GetKeyValue方法中取到kAreaId的值
                {
                    value = lVal;
                }
                else
                {
                    value = QueryFuncHelper.ParseKeyNameFromId(MainModel.GetInstance(), keyRet, lVal, 1, tpl.timeFormat);
                }
            }
            else
            {
                value = val;
            }

            return value;
        }

        private static object getFileIDOrName(StatInfoBase statInfo, ref object value, ReservedField field)
        {
            StringBuilder v = new StringBuilder();
            if (field == ReservedField.FileIDs)
            {
                foreach (int id in statInfo.FileIDDic.Keys)
                {
                    v.Append(id + ";");
                }
                value = v.ToString().TrimEnd(';');
            }
            else if (field == ReservedField.FileNames)
            {
                foreach (FileInfo fi in statInfo.FileIDDic.Values)
                {
                    v.Append(fi.Name + ";");
                }
                value = v.ToString().TrimEnd(';');
            }

            return value;
        }

        private static object getKPIValue(StatInfoBase statInfo, ref double? hilightValue, ColumnSet column)
        {
            object value;
            double dVal = statInfo.KPIData.CalcFormula((CarrierType)column.carrierId, column.momt, column.Exp
    , column.decPlace, column.Title, column.ServiceIDSet, column.FileNameKeyValueList);
            if (double.IsNaN(dVal) || double.IsInfinity(dVal))
            {
                value = "-";
            }
            else
            {
                try
                {
                    int beginIdx = column.Exp.IndexOf("{");
                    int endIdx = column.Exp.IndexOf("}");
                    if (beginIdx > 0 || endIdx != column.Exp.Length - 1)
                    {
                        value = column.Exp.Substring(0, beginIdx) + dVal + column.Exp.Substring(endIdx + 1);
                    }
                    else
                    {
                        value = dVal;//如果没有添加其他值（如%），则取原double值类型方便排序
                    }
                }
                catch
                {
                    value = dVal;
                }
                hilightValue = dVal;
            }

            return value;
        }
    }

    public class StatInfoBase : IComparable<StatInfoBase>
    {
        public Dictionary<string, object> ReservedDic { get; set; }
        public StatInfoBase(bool isMerge)
        {
            this.IsSum = isMerge;
            this.KPIData = new KPIDataGroup(null);
        }
        public StatInfoBase()
        {
            this.KPIData = new KPIDataGroup(null);
        }

        public KPIDataGroup KPIData
        {
            get;
            set;
        }

        public Dictionary<int, DTDataHeader> FileIDDic
        {
            get;
            private set;
        }

        public void AddFileInfo(DTDataHeader file)
        {
            if (file == null)
            {
                return;
            }
            if (FileIDDic==null)
            {
                FileIDDic = new Dictionary<int, DTDataHeader>();
            }
            FileIDDic[file.ID] = file;
        }

        public DTDataHeader FileHeader
        {
            get;
            set;
        }

        public bool IsSum { get; set; }
        public int DistrictID { get; set; } = 1;
        #region IComparable<StatInfoBase> 成员

        public int CompareTo(StatInfoBase other)
        {
            if (this.IsSum != other.IsSum)
            {
                return this.IsSum.CompareTo(other.IsSum);
            }
            else
            {
                return this.DistrictID.CompareTo(other.DistrictID);
            }
        }

        #endregion
    }
}
