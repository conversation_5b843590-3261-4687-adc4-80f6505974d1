﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.CQT
{
    public class CQTKPIFinePoint
    {
        public int ID { get; set; }
        public int AreatypeID { get; set; }
        public int AreaID { get; set; }
        public string Name { get; set; }
        public string AddrAt { get; set; }
        public string NetType { get; set; }
        public double LTLongitude { get; set; }
        public double LTLatitude { get; set; }
        public double BRLongitude { get; set; }
        public double BRLatitude { get; set; }
        public double Longitude
        {
            get
            {
                return (BRLongitude + LTLongitude) / 2;
            }
        }
        public double Latitude
        {
            get
            {
                return (BRLatitude + LTLatitude) / 2;
            }
        }
    }
}
