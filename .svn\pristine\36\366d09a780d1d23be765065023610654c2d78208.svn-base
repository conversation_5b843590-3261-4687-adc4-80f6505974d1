﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 同时呈现NR和LTE采样点指标,LTE采样点往右下偏移
    /// </summary>
    public class NRLTECollaborativeAnaLayer : LayerBase
    {
        public NRLTECollaborativeAnaLayer()
           : base("4/5G协同分析图层")
        {
            ResList = new List<ZTNRLTECollaborativeAnaInfo>();
            //TPList = new List<TestPoint>();
            TypeDic = new Dictionary<ZTNRLTECollaborativeAnaType, int>() {
                { ZTNRLTECollaborativeAnaType.WeakCover4G5G, 1 },
                { ZTNRLTECollaborativeAnaType.Better4G, 2 },
                { ZTNRLTECollaborativeAnaType.Better5G, 3 }
            };
        }

        //public List<TestPoint> TPList { get; set; }
        public Dictionary<ZTNRLTECollaborativeAnaType, int> TypeDic { get; set; }
        public List<ZTNRLTECollaborativeAnaInfo> ResList { get; set; }

        public int PointSize { get; set; } = 20;
        public Pen PenSelected { get; set; } = new Pen(Color.Red, 3);

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (ResList.Count == 0)
            {
                return;
            }

            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);
            updateRect.Inflate((int)(64 * ratio), (int)(64 * ratio));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);

            foreach (var res in ResList)
            {
                //if (!TypeDic.TryGetValue(res.Type, out _))
                //{
                //    continue;
                //}

                var tps = res.TestPoints;
                foreach (var tp in tps)
                {
                    if (tp.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                    {
                        drawSerialTP(graphics, ratio, tp);
                    }
                }

                if (mainModel.DrawFlyLines)
                {
                    flyCellColorIdxDic.Clear();
                    drawFlyLines(tps, graphics, DTLayerSerialManager.Instance.FlyLineSerial);
                }
            }

            //foreach (var tp in TPList)
            //{
            //    if (tp.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            //    {
            //        drawSerialTP(graphics, ratio, tp);
            //    }
            //}
        }

        #region DrawFlyLines
        private void drawFlyLines(List<TestPoint> sampledTestPoints, Graphics graphics, MapSerialInfo serialInfo)
        {
            foreach (TestPoint testPoint in sampledTestPoints)
            {
                setDataByNRTestPoint(graphics, serialInfo, testPoint);
            }
        }

        private void setDataByNRTestPoint(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint)
        {
            DbPoint antennaDPoint;
            int cellID;
            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();
            if (mapForm.GetLTECellLayer().IsVisible)
            {
                LTECell lteCell = testPoint.GetMainCell_LTE();
                if (lteCell != null)
                {
                    antennaDPoint = GetLTEAntennaEndPoint(lteCell, testPoint.Longitude, testPoint.Latitude);
                    cellID = lteCell.ID;
                    drawValidDistaceFlyLine(graphics, serialInfo, testPoint, antennaDPoint, cellID, 20, 20);
                }
            }

            if (mapForm.GetNRCellLayer().IsVisible)
            {
                NRCell nrCell = testPoint.GetMainCell_NR();
                if (nrCell != null)
                {
                    antennaDPoint = GetNRAntennaEndPoint(nrCell, testPoint.Longitude, testPoint.Latitude);
                    cellID = nrCell.ID;
                    drawValidDistaceFlyLine(graphics, serialInfo, testPoint, antennaDPoint, cellID, 0, 0);
                }
            }
        }

        public DbPoint GetLTEAntennaEndPoint(LTECell cell, double x, double y)
        {
            if (cell.Antennas.Count == 0 || x <= 0 || y <= 0 || cell.DirectionType == LTEAntennaDirectionType.Omni || cell.Type == LTEBTSType.Indoor)
            {
                return new DbPoint(cell.Longitude, cell.Latitude);
            }
            else
            {
                int index = 0;
                double dDistance = double.MaxValue;
                getAntennasEndPointDistance(cell, x, y, ref index, ref dDistance);
                if (dDistance > 50000)
                {
                    return new DbPoint(cell.Longitude, cell.Latitude);
                }
                else
                {
                    return new DbPoint(cell.Antennas[index].EndPointLongitude, cell.Antennas[index].EndPointLatitude);
                }
            }
        }

        private void getAntennasEndPointDistance(LTECell cell, double x, double y, ref int index, ref double dDistance)
        {
            for (int i = 0; i < cell.Antennas.Count; i++)
            {
                if (cell.Antennas[i].EndPointLongitude > 60 && cell.Antennas[i].EndPointLatitude > 10)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(cell.Antennas[i].EndPointLongitude, cell.Antennas[i].EndPointLatitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                        index = i;
                    }
                }
            }
        }

        public DbPoint GetNRAntennaEndPoint(NRCell cell, double x, double y)
        {
            if (cell.Antennas.Count == 0 || x <= 0 || y <= 0 || cell.Antennas[0].DirectionType == NRAntennaDirectionType.Omni || cell.Type == NRBTSType.Indoor)
            {
                return new DbPoint(cell.Longitude, cell.Latitude);
            }
            else
            {
                int index = 0;
                double dDistance = double.MaxValue;
                getAntennasEndPointDistance(cell, x, y, ref index, ref dDistance);
                if (dDistance > 50000)
                {
                    return new DbPoint(cell.Longitude, cell.Latitude);
                }
                else
                {
                    return new DbPoint(cell.Antennas[index].EndPointLongitude, cell.Antennas[index].EndPointLatitude);
                }

            }
        }

        private void getAntennasEndPointDistance(NRCell cell, double x, double y, ref int index, ref double dDistance)
        {
            for (int i = 0; i < cell.Antennas.Count; i++)
            {
                if (cell.Antennas[i].EndPointLongitude > 60 && cell.Antennas[i].EndPointLatitude > 10)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(cell.Antennas[i].EndPointLongitude, cell.Antennas[i].EndPointLatitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                        index = i;
                    }
                }
            }
        }

        private void drawValidDistaceFlyLine(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, DbPoint antennaDPoint, int cellID, int curOffsetX, int curOffsetY)
        {
            if (mainModel.FlyLineDistLimit > 0)
            {
                double distance = MathFuncs.GetDistance(antennaDPoint.x, antennaDPoint.y, testPoint.Longitude, testPoint.Latitude);
                if (distance <= mainModel.FlyLineDistLimit)
                {
                    drawCommonFlyLines(antennaDPoint, testPoint, cellID, serialInfo, graphics, curOffsetX, curOffsetY);
                }
            }
        }

        public int Opacity { get; set; } = 255;
        private readonly Dictionary<int, int> flyCellColorIdxDic = new Dictionary<int, int>();
        private void drawCommonFlyLines(DbPoint antennaDPoint, TestPoint testPoint, int cellID, MapSerialInfo serialInfo, Graphics graphics, int curOffsetX, int curOffsetY)
        {
            if (antennaDPoint != null)
            {
                if (antennaDPoint.x < 60 || antennaDPoint.y < 10)
                {
                    return;
                }
                PointF antennaPointF;
                gisAdapter.ToDisplay(antennaDPoint, out antennaPointF);

                Color antennaColor = getAntennaColor(testPoint, cellID, serialInfo);

                DbPoint testDPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                PointF testPointF;
                gisAdapter.ToDisplay(testDPoint, out testPointF);
                testPointF += new Size(curOffsetX, curOffsetY);
                graphics.DrawLine(new Pen(antennaColor, 1), testPointF, antennaPointF);
            }
        }

        private Color getAntennaColor(TestPoint testPoint, int cellID, MapSerialInfo serialInfo)
        {
            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();
            MapDTLayer dtLayer = mapForm.GetDTLayer();
            Color antennaColor = Color.Empty;
            if (dtLayer.IsBySerials)
            {
                Color? color;
                int? size;
                int? symbol;
                if (serialInfo != null && serialInfo.getStyle(dtLayer, testPoint, out color, out size, out symbol) && color != null)
                {
                    antennaColor = (Color)color;
                    antennaColor = Color.FromArgb(Opacity, antennaColor);
                }
            }
            else
            {
                if (dtLayer.FlyColorFromOrig)
                {
                    int clrIdx = 0;
                    if (!flyCellColorIdxDic.TryGetValue(cellID, out clrIdx))
                    {
                        flyCellColorIdxDic[cellID] = flyCellColorIdxDic.Count;
                    }
                    antennaColor = ColorSequenceSupplier.getColor(clrIdx);
                    antennaColor = Color.FromArgb(Opacity, antennaColor);
                }
                else
                {
                    antennaColor = ColorSequenceSupplier.getColor(cellID);
                    antennaColor = Color.FromArgb(Opacity, antennaColor);
                }
            }

            return antennaColor;
        }
        #endregion

        private void drawSerialTP(Graphics graphics, float ratio, TestPoint tp)
        {
            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                Color? curColor;
                int? curSymbol = 0;
                bool isValid = getStyle(serialInfo, tp, out curColor);
                if (isValid)
                {
                    float curOffsetX = 0;
                    float curOffsetY = 0;
                    if (serialInfo.ColorParamName.Contains("lte"))
                    {
                        //lte采样点偏移
                        curOffsetX = 250 * ratio;
                        curOffsetY = 250 * ratio;
                        curSymbol = 4;
                    }

                    DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                    PointF point;
                    GisAdapter.ToDisplay(dPoint, out point);
                    point += new SizeF(curOffsetX, curOffsetY);
                    drawPoint(tp, ratio, point, graphics, curColor, curSymbol);
                }
            }
        }

        public bool getStyle(MapSerialInfo serialInfo, TestPoint testPoint, out Color? color)
        {
            color = null;
            bool isValid = setColor(serialInfo, testPoint, ref color);
            if (!isValid)
            {
                return false;
            }
            if (color == null)
            {
                return false;
            }
            return true;
        }

        private bool setColor(MapSerialInfo serialInfo, TestPoint testPoint, ref Color? color)
        {
            if (serialInfo.ColorParamEnabled)
            {
                if (serialInfo.ColorDisplayParam == null)
                {
                    color = serialInfo.Color;
                    return false;
                }
                color = serialInfo.ColorDisplayParam.Info.GetColor(getValue(testPoint, serialInfo.ColorDisplayParam));
            }
            else
            {
                color = serialInfo.Color;
            }

            return true;
        }

        private float? getValue(TestPoint testPoint, DTDisplayParameter displayParam)
        {
            if (displayParam == null)
            {
                return null;
            }
            if (DTParameterManager.GetInstance().CanConvertToFloat(testPoint[displayParam.Info.ParamInfo.Name, displayParam.ArrayIndex], displayParam.Info.ParamInfo.ValueType))
            {
                return DTParameterManager.GetInstance().ConvertToFloat(testPoint[displayParam.Info.ParamInfo.Name, displayParam.ArrayIndex], displayParam.Info.ParamInfo.ValueType);
            }
            return null;
        }

        private void drawPoint(TestPoint testPoint, float ratio, PointF point, Graphics graphics, Color? color, int? symbol)
        {
            //画采样点
            Brush brush = Alpha == 255 ? new SolidBrush((Color)color) : new SolidBrush(Color.FromArgb(Alpha, (Color)color));
            float radius = ratio * PointSize;
            graphics.TranslateTransform(point.X, point.Y);
            graphics.ScaleTransform(radius, radius);
            //获取点形状
            GraphicsPath path = SymbolManager.GetInstance().Paths[(int)symbol];
            graphics.FillPath(brush, path);

            //对当前选中点，加外框
            if (testPoint.Selected)
            {
                graphics.DrawPath(PenSelected, path);
            }

            graphics.ResetTransform();
        }

        #region Select
        public List<TestPoint> SelectedTPs { get; set; } = new List<TestPoint>();
        public event EventHandler SelectedPointsChanged;

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            Select(((MapForm.MapEventArgs)e).MapOp2);
        }

        public void Select(MapOperation2 mop2)
        {
            if (!IsVisible || ResList.Count <= 0)
            {
                return;
            }

            foreach (var tp in SelectedTPs)
            {
                tp.Selected = false;
            }
            SelectedTPs.Clear();

            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);
            foreach (var res in ResList)
            {
                if (SelectedTPs.Count == 1)
                {
                    break;
                }
                var tps = res.TestPoints;
                foreach (var data in tps)
                {
                    if (SelectedTPs.Count == 1)
                    {
                        break;
                    }
                    DbPoint dPoint = new DbPoint(data.Longitude, data.Latitude);
                    PointF point;
                    gisAdapter.ToDisplay(dPoint, out point);
                    //缩放比例
                    float ratioSize = (ratio * PointSize);
                    float radius = PointSize / 2f;
                    RectangleF rect = new RectangleF(point.X - ratioSize * radius, point.Y - ratioSize * radius, ratioSize * PointSize, ratioSize * PointSize);
                    DbRect dRect;
                    gisAdapter.FromDisplay(rect, out dRect);

                    if (mop2.CheckCenterInDRect(dRect))
                    {
                        data.Selected = true;
                        SelectedTPs.Add(data);
                    }
                }
            }
            if (SelectedPointsChanged != null)
            {
                SelectedPointsChanged(this, EventArgs.Empty);
            }
        }
        #endregion
    }
}
