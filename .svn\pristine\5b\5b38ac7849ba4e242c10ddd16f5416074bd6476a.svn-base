﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

using MapWinGIS;
using AxMapWinGIS;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ChkMgrsGridForm : MinCloseForm
    {
        private ChkMgrsGridSet gridSet;

        public ChkMgrsGridForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            gridSet = new ChkMgrsGridSet();
            RefreshStyle();

            btnClose.Click += BtnClose_Click;
            btnSelect.Click += BtnSelect_Click;
            btnFind.Click += BtnFind_Click;

            chkCmVisible.CheckedChanged += StyleControl_CheckedChange;
            chkMcVisible.CheckedChanged += StyleControl_CheckedChange;
            chkShareVisible.CheckedChanged += StyleControl_CheckedChange;
            panelCmColor.BackColorChanged += StyleControl_CheckedChange;
            panelMcColor.BackColorChanged += StyleControl_CheckedChange;
            panelShareColor.BackColorChanged += StyleControl_CheckedChange;
            numCmAlpha.ValueChanged += StyleControl_CheckedChange;
            numMcAlpha.ValueChanged += StyleControl_CheckedChange;
            numShareAlpha.ValueChanged += StyleControl_CheckedChange;

            MainModel.MainForm.GetMapForm().ToolInfoClickEvent += ToolInfo_Click;
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            gridSet.Clear();
            MainModel.GetInstance().MainForm.GetMapForm().updateMap();
            MainModel.MainForm.GetMapForm().ToolInfoClickEvent -= ToolInfo_Click;
            base.MinCloseForm_FormClosing(sender, e);
        }

        private void ToolInfo_Click(double lng, double lat, MapOperation2 mop2, ref List<string> titles, ref List<string> infos)
        {
            gridSet.GetSelectedInfo(lng, lat, ref titles, ref infos);
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFile.Text = dlg.FileName;
            gridSet.ReadFrom(dlg.FileName);
            Refresh();
        }

        private void BtnFind_Click(object sender, EventArgs e)
        {
            ChkMgrsGridItem grid = gridSet.Find(txtFind.Text);
            if (grid == null)
            {
                return;
            }
            MainModel.MainForm.GetMapForm().GoToView(grid.DbRect);
        }

        private void StyleControl_CheckedChange(object sender, EventArgs e)
        {
            RefreshStyle();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void RefreshStyle()
        {
            gridSet.MasterStyle.FillColor = Color.FromArgb((int)numMcAlpha.Value, panelMcColor.BackColor);
            gridSet.MasterStyle.Visible = chkMcVisible.Checked;

            gridSet.MobileStyle.FillColor = Color.FromArgb((int)numCmAlpha.Value, panelCmColor.BackColor);
            gridSet.MobileStyle.Visible = chkCmVisible.Checked;

            gridSet.ShareStyle.FillColor = Color.FromArgb((int)numShareAlpha.Value, panelShareColor.BackColor);
            gridSet.ShareStyle.Visible = chkShareVisible.Checked;

            gridSet.Draw();
            MainModel.GetInstance().MainForm.GetMapForm().updateMap();
        }
    }
}
