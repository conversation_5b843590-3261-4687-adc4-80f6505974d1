﻿namespace MasterCom.RAMS.Frame
{
    partial class LegendPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LegendPanel));
            this.ctxQuickSet = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.testToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.btnQuickSet = new System.Windows.Forms.Button();
            this.comboBoxLegendType = new System.Windows.Forms.ComboBox();
            this.listBoxLegend = new System.Windows.Forms.ListBox();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miSetting = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxQuickSet.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // ctxQuickSet
            // 
            this.ctxQuickSet.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2});
            this.ctxQuickSet.Name = "ctxQuickSet";
            resources.ApplyResources(this.ctxQuickSet, "ctxQuickSet");
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.testToolStripMenuItem});
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            resources.ApplyResources(this.toolStripMenuItem2, "toolStripMenuItem2");
            // 
            // testToolStripMenuItem
            // 
            this.testToolStripMenuItem.Name = "testToolStripMenuItem";
            resources.ApplyResources(this.testToolStripMenuItem, "testToolStripMenuItem");
            // 
            // btnQuickSet
            // 
            resources.ApplyResources(this.btnQuickSet, "btnQuickSet");
            this.btnQuickSet.Name = "btnQuickSet";
            this.btnQuickSet.UseVisualStyleBackColor = true;
            this.btnQuickSet.Click += new System.EventHandler(this.btnQuickSet_Click);
            // 
            // comboBoxLegendType
            // 
            resources.ApplyResources(this.comboBoxLegendType, "comboBoxLegendType");
            this.comboBoxLegendType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxLegendType.FormattingEnabled = true;
            this.comboBoxLegendType.Name = "comboBoxLegendType";
            this.comboBoxLegendType.SelectedIndexChanged += new System.EventHandler(this.comboBoxLegendType_SelectedIndexChanged);
            // 
            // listBoxLegend
            // 
            resources.ApplyResources(this.listBoxLegend, "listBoxLegend");
            this.listBoxLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listBoxLegend.FormattingEnabled = true;
            this.listBoxLegend.Name = "listBoxLegend";
            this.listBoxLegend.MouseUp += new System.Windows.Forms.MouseEventHandler(this.listBoxLegend_MouseUp);
            this.listBoxLegend.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listBoxLegend_DrawItem);
            this.listBoxLegend.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listBoxLegend_MouseClick);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miSetting});
            this.contextMenuStrip.Name = "contextMenuStrip";
            resources.ApplyResources(this.contextMenuStrip, "contextMenuStrip");
            // 
            // miSetting
            // 
            this.miSetting.Name = "miSetting";
            resources.ApplyResources(this.miSetting, "miSetting");
            this.miSetting.Click += new System.EventHandler(this.miSetting_Click);
            // 
            // LegendPanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.btnQuickSet);
            this.Controls.Add(this.comboBoxLegendType);
            this.Controls.Add(this.listBoxLegend);
            this.Name = "LegendPanel";
            resources.ApplyResources(this, "$this");
            this.ctxQuickSet.ResumeLayout(false);
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxQuickSet;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem testToolStripMenuItem;
        private System.Windows.Forms.Button btnQuickSet;
        private System.Windows.Forms.ComboBox comboBoxLegendType;
        private System.Windows.Forms.ListBox listBoxLegend;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miSetting;


    }
}
