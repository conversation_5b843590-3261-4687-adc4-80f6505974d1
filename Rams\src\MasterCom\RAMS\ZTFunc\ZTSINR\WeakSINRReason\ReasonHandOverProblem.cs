﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonHandOverProblem : ReasonBase
    {
        //public double distance = 250;
        //public int hoCount = 4;
        public ReasonHandOverProblem()
        {
            this.Name = "切换不合理";
        }
        //protected int timeLimitMax = 3;
        //public int TimeLimitMax
        //{
        //    get { return timeLimitMax; }
        //    set { timeLimitMax = value; }
        //}

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            if (ZTWeakSINRReason.isHandOverProblem)
            {
                return true;
            }
            return false;
        }
    }
}
