﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellWrongDirItem_NRScan
    {
        public CellWrongDirItem_NRScan(ICell cell)
        {
            this.Cell = cell;
            WrongTestPoints = new List<TestPoint>();
        }

        public ICell Cell { get; private set; }

        public int CellID { get; private set; }
        public int TAC { get; private set; }
        public long NCI { get; private set; }
        public int ARFCN { get; private set; }
        public int PCI { get; private set; }
        public short DirectionCfg { get; private set; }
        public string CellName { get; private set; }

        private double maxLongitude = 0;
        private double minLongitude = 99999;
        public double LongitudeMid { get; private set; }
        private double maxLatitude = 0;
        private double minLatitude = 99999;
        public double LatitudeMid { get; private set; }

        private readonly List<string> fileNameList = new List<string>();
        public string FileName { get; private set; } = "";

        public List<TestPoint> WrongTestPoints { get; private set; }
        public int WrongPntCnt { get; set; }
        public int TotalPntCnt { get; private set; }
        public int GoodPntCnt { get; private set; }
        public int WrongMCellCnt { get; private set; }
        public int WrongNoneMCellCnt { get; private set; }

        public float WrongRxLevMin { get; private set; } = float.MaxValue;
        public float WrongRxLevMax { get; private set; } = float.MinValue;
        private float totalRxLev = 0;
        public float WrongRxLevAvg { get; private set; }
        public double WrongRate { get; private set; }

        public int WrongDirMean { get; private set; }
        public int WrongDirMax { get; private set; }
        public int WrongDirMin { get; private set; }
        public int DirDiff { get; private set; }

        public void CalcWrongDir()
        {
            CellName = Cell.Name;
            NRCell nrCell = Cell as NRCell;
            CellID = nrCell.CellID;
            TAC = nrCell.TAC;
            NCI = nrCell.NCI;
            ARFCN = nrCell.SSBARFCN;
            PCI = nrCell.PCI;
            DirectionCfg = nrCell.Direction;

            getMaxAndMinWrongDir();
            getMeanAndDiffWrongDir();

            calculate();
        }

        protected void getMaxAndMinWrongDir()
        {
            WrongDirMax = -1;
            WrongDirMin = 361;

            foreach (TestPoint tp in WrongTestPoints)
            {
                int dir = MathFuncs.getAngleFromPointToPoint(this.Cell.Longitude, this.Cell.Latitude
                    , tp.Longitude, tp.Latitude);
                WrongDirMin = Math.Min(dir, WrongDirMin);
                WrongDirMax = Math.Max(dir, WrongDirMax);

                if (maxLongitude < tp.Longitude)
                {
                    maxLongitude = tp.Longitude;
                }
                if (minLongitude > tp.Longitude && tp.Longitude != 0)
                {
                    minLongitude = tp.Longitude;
                }
                if (maxLatitude < tp.Latitude)
                {
                    maxLatitude = tp.Latitude;
                }
                if (minLatitude > tp.Latitude && tp.Longitude != 0)
                {
                    minLatitude = tp.Latitude;
                }
            }
        }

        protected void getMeanAndDiffWrongDir()
        {
            int diffDir = WrongDirMax - WrongDirMin;
            if (diffDir > 180)
            {
                double meanDir = (360 - diffDir) / 2.0;
                if (meanDir > WrongDirMin)
                {
                    WrongDirMean = (int)(360 - (meanDir - WrongDirMin));
                }
                else
                {
                    WrongDirMean = (int)(WrongDirMin - meanDir);
                }
            }
            else
            {
                double halfDir = diffDir / 2.0;
                WrongDirMean = (short)(WrongDirMin + halfDir);
            }

            DirDiff = Math.Abs(WrongDirMean - this.DirectionCfg);
            DirDiff = DirDiff > 180 ? 360 - DirDiff : DirDiff;
        }

        protected void calculate()
        {
            if (maxLongitude != 0 && minLongitude != 99999)
            {
                LongitudeMid = Math.Round((maxLongitude + minLongitude) / 2, 7);
            }

            if (maxLatitude != 0 && minLatitude != 99999)
            {
                LatitudeMid = Math.Round((maxLatitude + minLatitude) / 2, 7);
            }

            if (fileNameList != null)
            {
                StringBuilder strb = new StringBuilder();
                foreach (string str in fileNameList)
                {
                    strb.Append(str + ",");
                }
                if (strb.Length > 1)
                {
                    FileName = strb.Remove(strb.Length - 1, 1).ToString();
                }
            }

            GoodPntCnt = TotalPntCnt - WrongPntCnt;
            WrongNoneMCellCnt = WrongPntCnt - WrongMCellCnt;

            WrongRxLevAvg = (float)Math.Round(1.0 * totalRxLev / WrongPntCnt, 2);
            WrongRate = Math.Round(100.0 * WrongPntCnt / TotalPntCnt, 2);
        }

        /// <summary>
        /// 添加采样点信息。如果是正常覆盖的采样点，只记录个数。
        /// </summary>
        /// <param name="tp">采样点</param>
        /// <param name="rxlev">场强</param>
        /// <param name="wrongPnt">是否为异常覆盖点</param>
        /// <param name="cellIdx">是否为作为该点的主强小区</param>
        public void AddPoint(TestPoint tp, float rxLev, bool isWrongPnt, bool isMainCell)
        {
            TotalPntCnt++;

            if (!isWrongPnt)
            {
                return;
            }
            WrongTestPoints.Add(tp);
            WrongPntCnt++;
            totalRxLev += rxLev;
            WrongRxLevMin = Math.Min(WrongRxLevMin, rxLev);
            WrongRxLevMax = Math.Max(WrongRxLevMax, rxLev);
            if (isMainCell)
            {
                WrongMCellCnt++;
            }
        }
    }
}
