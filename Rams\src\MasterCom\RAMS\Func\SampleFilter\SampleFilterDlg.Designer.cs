﻿namespace MasterCom.RAMS.Func.SampleFilter
{
    partial class SampleFilterDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.treeView = new System.Windows.Forms.TreeView();
            this.cbxSampleFilter = new System.Windows.Forms.ComboBox();
            this.btnFresh = new System.Windows.Forms.Button();
            this.btnApply = new System.Windows.Forms.Button();
            this.lbCurSel = new System.Windows.Forms.Label();
            this.cbxUseNeib = new System.Windows.Forms.CheckBox();
            this.SuspendLayout();
            // 
            // treeView
            // 
            this.treeView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.treeView.CheckBoxes = true;
            this.treeView.Location = new System.Drawing.Point(8, 37);
            this.treeView.Name = "treeView";
            this.treeView.Size = new System.Drawing.Size(333, 296);
            this.treeView.TabIndex = 0;
            this.treeView.AfterCheck += new System.Windows.Forms.TreeViewEventHandler(this.treeView_AfterCheck);
            // 
            // cbxSampleFilter
            // 
            this.cbxSampleFilter.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxSampleFilter.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxSampleFilter.FormattingEnabled = true;
            this.cbxSampleFilter.Location = new System.Drawing.Point(8, 6);
            this.cbxSampleFilter.Name = "cbxSampleFilter";
            this.cbxSampleFilter.Size = new System.Drawing.Size(250, 20);
            this.cbxSampleFilter.TabIndex = 1;
            // 
            // btnFresh
            // 
            this.btnFresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFresh.Location = new System.Drawing.Point(266, 4);
            this.btnFresh.Name = "btnFresh";
            this.btnFresh.Size = new System.Drawing.Size(75, 23);
            this.btnFresh.TabIndex = 2;
            this.btnFresh.Text = "刷新";
            this.btnFresh.UseVisualStyleBackColor = true;
            this.btnFresh.Click += new System.EventHandler(this.btnFresh_Click);
            // 
            // btnApply
            // 
            this.btnApply.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnApply.Location = new System.Drawing.Point(266, 339);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(75, 23);
            this.btnApply.TabIndex = 2;
            this.btnApply.Text = "应用";
            this.btnApply.UseVisualStyleBackColor = true;
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // lbCurSel
            // 
            this.lbCurSel.AutoSize = true;
            this.lbCurSel.Location = new System.Drawing.Point(12, 327);
            this.lbCurSel.Name = "lbCurSel";
            this.lbCurSel.Size = new System.Drawing.Size(0, 12);
            this.lbCurSel.TabIndex = 3;
            // 
            // cbxUseNeib
            // 
            this.cbxUseNeib.AutoSize = true;
            this.cbxUseNeib.Checked = true;
            this.cbxUseNeib.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxUseNeib.Location = new System.Drawing.Point(12, 344);
            this.cbxUseNeib.Name = "cbxUseNeib";
            this.cbxUseNeib.Size = new System.Drawing.Size(72, 16);
            this.cbxUseNeib.TabIndex = 4;
            this.cbxUseNeib.Text = "包含邻区";
            this.cbxUseNeib.UseVisualStyleBackColor = true;
            // 
            // SampleFilterDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(353, 374);
            this.Controls.Add(this.cbxUseNeib);
            this.Controls.Add(this.lbCurSel);
            this.Controls.Add(this.btnApply);
            this.Controls.Add(this.btnFresh);
            this.Controls.Add(this.cbxSampleFilter);
            this.Controls.Add(this.treeView);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SampleFilterDlg";
            this.Text = "采样点显示过滤";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.SampleFilterDlg_FormClosing);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TreeView treeView;
        private System.Windows.Forms.ComboBox cbxSampleFilter;
        private System.Windows.Forms.Button btnFresh;
        private System.Windows.Forms.Button btnApply;
        private System.Windows.Forms.Label lbCurSel;
        private System.Windows.Forms.CheckBox cbxUseNeib;
    }
}