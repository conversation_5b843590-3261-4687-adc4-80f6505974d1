﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using System.IO;
using MasterCom.RAMS.Grid;
using MasterCom.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RoadQualAnaDlg : BaseDialog
    {
        public RoadQualAnaDlg()
        {
            InitializeComponent();
            mainModel = MainModel.GetInstance();
            init();
        }

        private void init()
        {
            RoadQualAnaCond cond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;
            chk2G.Checked = cond.IsCheck2G;
            chk4G.Checked = cond.IsCheck4G;
            cmbKpiTimeType.Text = cond.KpiTimeTypeDes;
            timeKpiBegin.DateTime = cond.TimePeriod_Kpi.BeginTime;
            timeKpiEnd.DateTime = cond.TimePeriod_Kpi.EndTime;
            timeExitCellBegin.DateTime = cond.TimePeriod_ExitCell.BeginTime;
            timeExitCellEnd.DateTime = cond.TimePeriod_ExitCell.EndTime;
            numWeakRsrpGate.Value = (decimal)cond.WeakRsrpGate;
            numWeakRxlevGate.Value = (decimal)cond.WeakRxlevGate;
            numAbnormalGate.Value = (decimal)cond.RoadAbnormalGate;

            cmbGridColorMode.Properties.Items.Clear();
            foreach (RoadQualAnaColorModeItem item in cond.GridColorModeList)
            {
                cmbGridColorMode.Properties.Items.Add(item);
            }
            cmbGridColorMode.SelectedItem = cond.CurUsingColorMode;
        }
        private void btnEditColor_Click(object sender, EventArgs e)
        {
            RoadQualAnaCond cond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;
            if (cond.CurUsingColorMode != null)
            {
                ColorRangeMngDlg dlg = new ColorRangeMngDlg();
                dlg.FillColorRanges(cond.CurUsingColorMode.ColorRanges);
                dlg.FixMinMax(cond.CurUsingColorMode.MinR, cond.CurUsingColorMode.MaxR);
                dlg.MakeRangeModeOnly();
                if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    cond.CurUsingColorMode.ColorRanges = dlg.ColorRanges;
                }
            }
        }
        private void cmbGridColorMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            RoadQualAnaCond cond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;
            cond.CurUsingColorModeIndex = cmbGridColorMode.SelectedIndex;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!chk2G.Checked && !chk4G.Checked)
            {
                MessageBox.Show("需至少选择一种网络类型！");
                return;
            }
            RoadQualAnaCond cond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;
            cond.IsCheck2G = chk2G.Checked;
            cond.IsCheck4G = chk4G.Checked;
            cond.KpiTimeTypeDes = cmbKpiTimeType.Text;
            cond.TimePeriod_Kpi = new TimePeriod(timeKpiBegin.DateTime, timeKpiEnd.DateTime);
            cond.TimePeriod_ExitCell = new TimePeriod(timeExitCellBegin.DateTime, timeExitCellEnd.DateTime);
            cond.WeakRsrpGate = (double)numWeakRsrpGate.Value;
            cond.WeakRxlevGate = (double)numWeakRxlevGate.Value;
            cond.RoadAbnormalGate = (double)numAbnormalGate.Value;

            cond.CurUsingColorModeIndex = cmbGridColorMode.SelectedIndex;
            RoadQualAnaCfgManager.GetInstance().Save();

            DialogResult = DialogResult.OK;
        }

        private void chk2G_CheckedChanged(object sender, EventArgs e)
        {
            numWeakRxlevGate.Enabled = chk2G.Checked;
        }

        private void chk4G_CheckedChanged(object sender, EventArgs e)
        {
            numWeakRsrpGate.Enabled = chk4G.Checked;
        }

        private void cmbKpiTimeType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string kpiTimeTypeDes = cmbKpiTimeType.Text;
            if (kpiTimeTypeDes == "最新")
            {
                timeKpiBegin.Enabled = timeKpiEnd.Enabled = false;
            }
            else if (kpiTimeTypeDes == "月")
            {
                timeKpiBegin.Enabled = timeKpiEnd.Enabled = true;
                DateTime time = DateTime.Now.Date.AddMonths(-1);
                timeKpiBegin.DateTime = new DateTime(time.Year, time.Month, 1);
                timeKpiEnd.DateTime = timeKpiBegin.DateTime.AddMonths(1).AddSeconds(-1);
            }
            else
            {
                timeKpiBegin.Enabled = timeKpiEnd.Enabled = true;
                DateTime dateNow = DateTime.Now.Date;
                timeKpiBegin.DateTime = dateNow.AddMonths(-1);
                timeKpiEnd.DateTime = dateNow.AddDays(1).AddSeconds(-1);
            }
        }
    }

    public class RoadQualAnaCond
    {
        public RoadQualAnaCond()
        {
            DateTime timeNow = DateTime.Now;
            TimePeriod_Kpi = new TimePeriod(timeNow.Date.AddMonths(-1), timeNow.Date);
            TimePeriod_ExitCell = new TimePeriod(timeNow.AddHours(-1), timeNow);
        }
        public bool IsCheck2G { get; set; }
        public bool IsCheck4G { get; set; }
        
        public string KpiTimeTypeDes { get; set; } = "最新";
        public string KpiTimeType
        {
            get 
            {
                string kpiTimeType = "";
                if (KpiTimeTypeDes == "最新")
                {
                    kpiTimeType = "recent";
                }
                else if (KpiTimeTypeDes == "月")
                {
                    kpiTimeType = "mm";
                }
                else if (KpiTimeTypeDes == "天")
                {
                    kpiTimeType = "dd";
                }

                return kpiTimeType;
            }
        }
        
        public TimePeriod TimePeriod_Kpi { get; set; }
        public TimePeriod TimePeriod_ExitCell { get; set; }
        public double WeakRsrpGate { get; set; } = -100;
        public double WeakRxlevGate { get; set; } = -90;
        public double RoadAbnormalGate { get; set; } = 10;
        public int CurUsingColorModeIndex { get; set; }
        public RoadQualAnaColorModeItem CurUsingColorMode 
        { 
            get 
            {
                if (GridColorModeList.Count > CurUsingColorModeIndex)
                {
                    return GridColorModeList[CurUsingColorModeIndex];
                }
                return null;
            } 
        }
        
        public List<RoadQualAnaColorModeItem> GridColorModeList { get; set; } = new List<RoadQualAnaColorModeItem>();
        public bool IsAbnormal(double? abnormalRate)
        {
            if (abnormalRate == null)
            {
                return false;
            }
            return abnormalRate >= RoadAbnormalGate;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["IsCheck2G"] = this.IsCheck2G;
                param["IsCheck4G"] = this.IsCheck4G;
                param["WeakRsrpGate"] = this.WeakRsrpGate;
                param["WeakRxlevGate"] = this.WeakRxlevGate;
                param["RoadAbnormalGate"] = this.RoadAbnormalGate;
                param["CurUsingColorModeIndex"] = this.CurUsingColorModeIndex;

                List<object> gridColorObjList = new List<object>();
                foreach (RoadQualAnaColorModeItem colorItem in GridColorModeList)
                {
                    gridColorObjList.Add(colorItem.Param);
                }
                param["GridColorModeList"] = gridColorObjList;

                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;

                this.IsCheck2G = (bool)param["IsCheck2G"];
                this.IsCheck4G = (bool)param["IsCheck4G"];
                this.WeakRsrpGate = (double)param["WeakRsrpGate"];
                this.WeakRxlevGate = (double)param["WeakRxlevGate"];
                this.RoadAbnormalGate = (double)param["RoadAbnormalGate"];
                this.CurUsingColorModeIndex = (int)param["CurUsingColorModeIndex"];

                if (param.ContainsKey("GridColorModeList"))
                {
                    GridColorModeList.Clear();
                    List<object> tpParams = (List<object>)value["GridColorModeList"];
                    foreach (object o in tpParams)
                    {
                        Dictionary<string, object> cfgParam = (Dictionary<string, object>)o;
                        RoadQualAnaColorModeItem item = new RoadQualAnaColorModeItem();
                        item.Param = cfgParam;
                        this.GridColorModeList.Add(item);
                    }
                }
            }
        }
    }
}
