﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellAssociationKPISettingForm : BaseDialog
    {
        public CellAssociationKPISettingForm()
        {
            InitializeComponent();
            this.toolStrip1.BackColor = this.BackColor;
            this.btnAdd.Enabled = false;
            this.btnDel.Enabled = false;
            this.btnUp.Enabled = false;
            this.btnDown.Enabled = false;
            this.tsBtnDel.Enabled = false;
            this.tsBtnSave.Enabled = false;

            InitCbxTimeType();
            InitCbxNetType();
            RefreshCbxReport();

            this.Load += Form_Load;
            cbxTimeType.SelectedIndexChanged += KpiComboBox_SelectedChanged;
            cbxNetType.SelectedIndexChanged += KpiComboBox_SelectedChanged;
            txtFilter.TextChanged += KpiComboBox_SelectedChanged;
            cbxCurReport.SelectedIndexChanged += CbxCurReport_SelectedChanged;
            listBox.SelectedIndexChanged += ListBox_SelectedChanged;
            listBox.DoubleClick += ListBox_DoubleClick;
            treeView.MouseDown += TreeView_MouseDown;
            treeView.AfterSelect += TreeView_AfterSelect;
            treeView.DoubleClick += TreeView_DoubleClick;
            btnUpdate.Click += BtnUpdate_Click;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnAdd.Click += BtnAddField_Click;
            btnDel.Click += BtnDelField_Click;
            btnUp.Click += BtnUpField_Click;
            btnDown.Click += BtnDownField_Click;
            tsBtnNew.Click += TsBtnNew_Click;
            tsBtnSave.Click += TsBtnSave_Click;
            tsBtnDel.Click += TsBtnDel_Click;
        }

        public CellAssociationKPIReport SelectedReport
        {
            get { return cbxCurReport.SelectedItem as CellAssociationKPIReport; }
        }

        private void Form_Load(object sender, EventArgs e)
        {
            KpiComboBox_SelectedChanged(this, new EventArgs());
            CbxCurReport_SelectedChanged(this, new EventArgs());
        }

        private void KpiComboBox_SelectedChanged(object sender, EventArgs e)
        {
            List<CellAssociationKPIItem> kpiList = CellAssociationKPIManager.Instance.KPIList;
            Dictionary<string, TreeNode> kpiSetDic = new Dictionary<string, TreeNode>();
            ECellAssociationKPITimeType curTimeType = (ECellAssociationKPITimeType)EnumDescriptionAttribute.Parse(typeof(ECellAssociationKPITimeType), cbxTimeType.SelectedItem.ToString());
            ECellAssociationKPINetType curNetType = (ECellAssociationKPINetType)EnumDescriptionAttribute.Parse(typeof(ECellAssociationKPINetType), cbxNetType.SelectedItem.ToString());
            string filterString = txtFilter.Text;

            treeView.BeginUpdate();
            treeView.Nodes.Clear();
            foreach (CellAssociationKPIItem kpiItem in kpiList)
            {
                if (kpiItem.TimeType != curTimeType || kpiItem.NetType != curNetType)
                {
                    continue;
                }
                else if (!string.IsNullOrEmpty(filterString) && kpiItem.Name.IndexOf(filterString, StringComparison.CurrentCultureIgnoreCase) == -1)
                {
                    continue;
                }

                TreeNode grpNode = null;
                if (!kpiSetDic.TryGetValue(kpiItem.KPISetName, out grpNode))
                {
                    grpNode = new TreeNode(kpiItem.KPISetName);
                    kpiSetDic.Add(kpiItem.KPISetName, grpNode);
                    treeView.Nodes.Add(grpNode);
                }

                TreeNode kpiNode = new TreeNode(kpiItem.Name);
                kpiNode.Tag = kpiItem;
                grpNode.Nodes.Add(kpiNode);
            }
            if (treeView.Nodes.Count > 0)
            {
                treeView.SelectedNode = treeView.Nodes[0];
            }
            treeView.EndUpdate();
        }

        private void CbxCurReport_SelectedChanged(object sender, EventArgs e)
        {
            tsBtnDel.Enabled = tsBtnSave.Enabled = cbxCurReport.SelectedItem != null;
            TreeNode curNode = treeView.SelectedNode;
            btnAdd.Enabled = curNode != null && curNode.Level > 0 && cbxCurReport.SelectedItem != null;
            RefreshListBox();
        }

        private void TreeView_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button != MouseButtons.Right)
            {
                return;
            }

            TreeNode node = treeView.GetNodeAt(e.X, e.Y);
            if (node == null)
            {
                return;
            }
            treeView.SelectedNode = node;
        }

        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            TreeNode curNode = treeView.SelectedNode;
            btnAdd.Enabled = curNode != null && curNode.Level > 0 && cbxCurReport.SelectedItem != null;
        }

        private void TreeView_DoubleClick(object sender, EventArgs e)
        {
            TreeNode curNode = treeView.SelectedNode;
            if (curNode == null || curNode.Level == 0)
            {
                return;
            }

            CellAssociationKPIItem kpiItem = curNode.Tag as CellAssociationKPIItem;
            if (kpiItem == null)
            {
                return;
            }

            MasterCom.Grid.ColorRangeMngDlg mngDlg = new MasterCom.Grid.ColorRangeMngDlg();
            mngDlg.FixMinMax(CellAssociationKPIItem.SMinRange, CellAssociationKPIItem.SMaxRange);
            mngDlg.MakeRangeModeOnly();
            mngDlg.FillColorRanges(kpiItem.ColorRanges);
            if (DialogResult.OK != mngDlg.ShowDialog(this))
            {
                return;
            }
            kpiItem.ColorRanges.Clear();
            kpiItem.ColorRanges.AddRange(mngDlg.ColorRanges);
            CellAssociationKPIManager.Instance.Save();
        }

        private void ListBox_SelectedChanged(object sender, EventArgs e)
        {
            btnDel.Enabled = listBox.Items.Count > 0 && listBox.SelectedItem != null;
            btnUp.Enabled = listBox.SelectedIndex > 0;
            btnDown.Enabled = listBox.SelectedIndex < listBox.Items.Count - 1;
        }

        private void ListBox_DoubleClick(object sender, EventArgs e)
        {
            if (listBox.SelectedItem == null)
            {
                return;
            }

            CellAssociationKPIField field = listBox.SelectedItem as CellAssociationKPIField;
            TextInputBox inputBox = new TextInputBox("指标别名设置", "输入指标别名", field.ShowName == null? "" : field.ShowName);
            if (inputBox.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            if (string.IsNullOrEmpty(inputBox.TextInput))
            {
                field.ShowName = null;
            }
            else
            {
                field.ShowName = inputBox.TextInput;
            }
            RefreshListBox();
            listBox.SelectedItem = field;
        }

        private void BtnUpdate_Click(object sender, EventArgs e)
        {
            CellAssociationKPIManager.Instance.Download();
            MessageBox.Show("指标集更新成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            KpiComboBox_SelectedChanged(this, new EventArgs());
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnAddField_Click(object sender, EventArgs e)
        {
            TreeNode curNode = treeView.SelectedNode;
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;
            string errMsg = null;
            CellAssociationKPIField field = curReport.AddField(curNode.Tag as CellAssociationKPIItem, out errMsg);
            if (field == null)
            {
                MessageBox.Show(errMsg, "添加指标失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            RefreshListBox();
            listBox.SelectedItem = field;
        }

        private void BtnDelField_Click(object sender, EventArgs e)
        {
            CellAssociationKPIField field = listBox.SelectedItem as CellAssociationKPIField;
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;
            curReport.DelField(field);
            RefreshListBox();
        }

        private void BtnUpField_Click(object sender, EventArgs e)
        {
            CellAssociationKPIField field = listBox.SelectedItem as CellAssociationKPIField;
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;
            curReport.UpField(field);
            RefreshListBox();
            listBox.SelectedItem = field;
        }

        private void BtnDownField_Click(object sender, EventArgs e)
        {
            CellAssociationKPIField field = listBox.SelectedItem as CellAssociationKPIField;
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;
            curReport.DownField(field);
            RefreshListBox();
            listBox.SelectedItem = field;
        }

        private void TsBtnNew_Click(object sender, EventArgs e)
        {
            TextInputBox inputBox = new TextInputBox("新建关联报表", "报表名称", "");
            if (inputBox.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            string reportName = inputBox.TextInput;
            CellAssociationKPIReport curReport = CellAssociationKPIReportManager.Instance.New(reportName);
            curReport.TimeType = (ECellAssociationKPITimeType)EnumDescriptionAttribute.Parse(typeof(ECellAssociationKPITimeType), cbxTimeType.SelectedItem.ToString());
            curReport.NetType = (ECellAssociationKPINetType)EnumDescriptionAttribute.Parse(typeof(ECellAssociationKPINetType), cbxNetType.SelectedItem.ToString());
            RefreshCbxReport();
            cbxCurReport.SelectedItem = curReport;
        }

        private void TsBtnSave_Click(object sender, EventArgs e)
        {
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;

            if (curReport == null)
            {
                return;
            }
            if (curReport.FieldList.Count == 0)
            {
                MessageBox.Show("报表需要包含至少一个指标", "保存失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            CellAssociationKPIReportManager.Instance.Save(curReport);
            MessageBox.Show("报表保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void TsBtnDel_Click(object sender, EventArgs e)
        {
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;

            if (curReport == null)
            {
                return;
            }

            if (curReport.FieldList.Count > 0)
            {
                DialogResult res = MessageBox.Show(string.Format("报表'{0}'包含指标，是否确定删除?", curReport.ReportName), 
                    "删除报表", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                if (res == DialogResult.Cancel)
                {
                    return;
                }
            }

            CellAssociationKPIReportManager.Instance.Delete(curReport);
            RefreshCbxReport();
        }

        private void InitCbxTimeType()
        {
            cbxTimeType.Items.Clear();
            cbxTimeType.Items.Add(EnumDescriptionAttribute.GetText(ECellAssociationKPITimeType.ByDay));
            cbxTimeType.Items.Add(EnumDescriptionAttribute.GetText(ECellAssociationKPITimeType.ByHour));
            cbxTimeType.SelectedIndex = 0;
        }

        private void InitCbxNetType()
        {
            cbxNetType.Items.Clear();
            cbxNetType.Items.Add(EnumDescriptionAttribute.GetText(ECellAssociationKPINetType.GSM));
            cbxNetType.Items.Add(EnumDescriptionAttribute.GetText(ECellAssociationKPINetType.TD));
            cbxNetType.Items.Add(EnumDescriptionAttribute.GetText(ECellAssociationKPINetType.LTE));
            cbxNetType.SelectedIndex = 0;
        }

        private void RefreshCbxReport()
        {
            cbxCurReport.Items.Clear();
            List<CellAssociationKPIReport> reportList = CellAssociationKPIReportManager.Instance.ReportList;
            foreach (CellAssociationKPIReport report in reportList)
            {
                cbxCurReport.Items.Add(report);
            }
            if (cbxCurReport.Items.Count > 0)
            {
                cbxCurReport.SelectedIndex = 0;
            }
            else
            {
                CbxCurReport_SelectedChanged(cbxCurReport, new EventArgs());
            }
        }

        private void RefreshListBox()
        {
            listBox.Items.Clear();
            CellAssociationKPIReport curReport = cbxCurReport.SelectedItem as CellAssociationKPIReport;
            if (curReport != null)
            {
                foreach (CellAssociationKPIField field in curReport.FieldList)
                {
                    listBox.Items.Add(field);
                }
            }

            if (listBox.Items.Count > 0)
            {
                listBox.SelectedIndex = 0;
            }
            else
            {
                ListBox_SelectedChanged(listBox, new EventArgs());
            }
        }
    }
}
