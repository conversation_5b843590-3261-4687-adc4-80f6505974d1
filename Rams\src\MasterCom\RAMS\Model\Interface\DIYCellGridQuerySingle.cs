﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.Net
{
    public class DIYCellGridQuerySingle : DIYCellGridQueryBase
    {
        public DIYCellGridQuerySingle(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "栅格查询(按小区)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11016, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Cell;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null && searchGeometrys.SelectedTDCell == null && searchGeometrys.SelectedWCell == null)
            {
                return false;
            }
            return true;
        }

    }
}
