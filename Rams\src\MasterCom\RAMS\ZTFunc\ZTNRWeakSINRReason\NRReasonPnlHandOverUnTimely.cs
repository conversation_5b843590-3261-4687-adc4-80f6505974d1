﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlHandOverUnTimely : NRReasonPanelBase
    {
        public NRReasonPnlHandOverUnTimely()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numLastTime.ValueChanged -= timeLimit_ValueChanged;
            numLastTime.Value = (decimal)NRWeakSINRReason.stayTime;
            numLastTime.ValueChanged += timeLimit_ValueChanged;
            rsrpDiffer.ValueChanged -= rsrpDiffer_ValueChanged;
            rsrpDiffer.Value = (decimal)NRWeakSINRReason.rsrpDiffer;
            rsrpDiffer.ValueChanged += rsrpDiffer_ValueChanged;
            numBeforeTime.ValueChanged -= numBeforeTime_ValueChanged;
            numBeforeTime.Value = (decimal)NRWeakSINRReason.beforeTime;
            numBeforeTime.ValueChanged += numBeforeTime_ValueChanged;
        }

        void timeLimit_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.stayTime = (int)numLastTime.Value;
        }
        void rsrpDiffer_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.rsrpDiffer = (int)rsrpDiffer.Value;
        }
        void numBeforeTime_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.beforeTime = (int)numBeforeTime.Value;
        }
    }
}
