﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    class NRAcpGoodPointFtpDownload : NRStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("好点");
        }

        protected override void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            curCellServiceInfo.GoodSampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.GoodSampleDL.Calculate();
            dealValidGoodThroughput(curCellServiceInfo.GoodSampleDL, standard.DownThroughput);
        }
    }

    class NRAcpMiddlePointFtpDownload : NRStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("中点");
        }

        protected override void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            curCellServiceInfo.MiddleSampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.MiddleSampleDL.Calculate();
            dealValidMiddleThroughput(curCellServiceInfo.MiddleSampleDL, standard.DownThroughput);
        }
    }

    class NRAcpBadPointFtpDownload : NRStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("差点");
        }

        protected override void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            curCellServiceInfo.BadSampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.BadSampleDL.Calculate();
            dealValidBadThroughput(curCellServiceInfo.BadSampleDL, standard.DownThroughput);
        }
    }

    class NRAcpGoodPointFtpUpload : NRStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("好点");
        }

        protected override void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            curCellServiceInfo.GoodSampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.GoodSampleUL.Calculate();
            dealValidGoodThroughput(curCellServiceInfo.GoodSampleUL, standard.UpThroughput);
        }
    }

    class NRAcpMiddlePointFtpUpload : NRStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("中点");
        }

        protected override void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            curCellServiceInfo.MiddleSampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.MiddleSampleUL.Calculate();
            dealValidMiddleThroughput(curCellServiceInfo.MiddleSampleUL, standard.UpThroughput);
        }
    }

    class NRAcpBadPointFtpUpload : NRStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("差点");
        }

        protected override void addData(TestPoint tp, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            curCellServiceInfo.BadSampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NRCellInfo cell, NRStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.BadSampleUL.Calculate();
            dealValidBadThroughput(curCellServiceInfo.BadSampleUL, standard.UpThroughput);
        }
    }

    class NRAcpAccRate : NRStationAcceptEvent
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("接入") || fileInfo.Name.Contains("附着");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(NRCellInfo cell, NRStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.SA)
            {
                evtRequList = new List<int> { (int)NREventManager.NR_Registration_Request };
                evtSuccList = new List<int> { (int)NREventManager.NR_Registration_Accept };
            }
            else if (condition.NRServiceType == NRServiceName.NSA)
            {
                evtRequList = new List<int> { (int)NREventManager.Attach_Request };
                evtSuccList = new List<int> { (int)NREventManager.Attach_Accept };
            }

            return curCellServiceInfo.AccessInfo;
        }

        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            //1.测试10次
            //2.成功率100%
            curCellServiceInfo.AccessInfo.Calculate();
            curCellServiceInfo.AccessInfo.Judge(10, 1);
        }
    }

    class NRAcpEPSFBRate : NRStationAcceptEvent
    {
        public NRAcpEPSFBRate()
        {
            evtRequList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Attempt, (int)NREventManager.EPSFB_Audio_MT_Call_Attempt };
            evtSuccList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Setup, (int)NREventManager.EPSFB_Audio_MT_Call_Setup };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("EPS语音");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(NRCellInfo cell, NRStationAcceptCondition condition)
        {
            return curCellServiceInfo.EPSFBInfo;
        }

        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            //1.测试10次
            //2.成功率100%
            curCellServiceInfo.EPSFBInfo.Calculate();
            curCellServiceInfo.EPSFBInfo.Judge(10, 1);
        }
    }

    class NRVONRRate : NRStationAcceptEvent
    {
        public NRVONRRate()
        {
            evtRequList = new List<int> { (int)NREventManager.VoNR_Audio_MO_Call_Attempt, (int)NREventManager.VoNR_Audio_MT_Call_Attempt };
            evtSuccList = new List<int> { (int)NREventManager.VoNR_Audio_MT_Call_Established, (int)NREventManager.VoNR_Audio_MO_Call_End };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("VONR");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(NRCellInfo cell, NRStationAcceptCondition condition)
        {
            return curCellServiceInfo.VONRInfo;
        }

        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            //1.测试10次
            //2.成功率100%
            curCellServiceInfo.VONRInfo.Calculate();
            curCellServiceInfo.VONRInfo.Judge(10, 1);
        }
    }

    class NRAcpEPSFBDelay : NRStationAcceptBase
    {
        protected List<int> evtSuccList;
        public NRAcpEPSFBDelay()
        {
            evtSuccList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Setup, (int)NREventManager.EPSFB_Audio_MT_Call_Setup };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("EPS语音");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            foreach (Event evt in fileManager.Events)
            {
                dealEvtData(curCellServiceInfo, evt);
            }
        }

        protected void dealEvtData(NRCellServiceInfo cellTypeInfo, Event evt)
        {
            if (evtSuccList.Contains(evt.ID))
            {
                int delay = int.Parse(evt["Value1"].ToString());
                cellTypeInfo?.EPSFBDelay.Add(delay / 1000d);
            }
        }

        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            //1.时延<=5s
            curCellServiceInfo.EPSFBDelay.Calculate();
            if (curCellServiceInfo.EPSFBDelay.Data <= 5)
            {
                curCellServiceInfo.EPSFBDelay.IsValid = true;
            }
        }
    }

    class NRAcpPingDelay : NRStationAcceptBase
    {
        protected List<int> evtSuccList;
        public NRAcpPingDelay()
        {
            evtSuccList = new List<int> { (int)NREventManager.Ping_Success };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("PING");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            Event preEvt = null;
            foreach (Event evt in fileManager.Events)
            {
                dealEvtData(fileInfo.Name, curCellServiceInfo, evt, ref preEvt);
            }
        }

        protected void dealEvtData(string fileInfoName, NRCellServiceInfo cellTypeInfo, Event evt, ref Event preEvt)
        {
            if (evtSuccList.Contains(evt.ID))
            {
                if (preEvt == null || evt.DateTime.Subtract(preEvt.DateTime).TotalMilliseconds >= 2000)
                {
                    int delay = int.Parse(evt["Value1"].ToString());
                    if (fileInfoName.Contains("大包"))
                    {
                        cellTypeInfo?.BigPackageDelay.Add(delay);
                    }
                    else if (fileInfoName.Contains("小包"))
                    {
                        cellTypeInfo?.SmallPackageDelay.Add(delay);
                    }
                }
                preEvt = evt;
            }
        }

        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            //1.ping事件相隔2s
            //2.PING事件次数>=20次
            //3.小包<=30,大包<=35ms
            curCellServiceInfo.SmallPackageDelay.Calculate();
            if (curCellServiceInfo.SmallPackageDelay.Divisor >= 20 && curCellServiceInfo.SmallPackageDelay.Data <= 30)
            {
                curCellServiceInfo.SmallPackageDelay.IsValid = true;
            }

            curCellServiceInfo.BigPackageDelay.Calculate();
            if (curCellServiceInfo.BigPackageDelay.Divisor >= 20 && curCellServiceInfo.BigPackageDelay.Data <= 35)
            {
                curCellServiceInfo.BigPackageDelay.IsValid = true;
            }
        }
    }

    class NRAcpHandoverRate : NRStationAcceptBase
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;
        protected List<int> lteEvtSuccList;
        protected List<int> lteEvtRequList;

        protected void resetEvtList(NRServiceName type)
        {
            if (type == NRServiceName.NSA)
            {
                evtRequList = new List<int> { (int)NREventManager.SameLTEDiffNRHandoverRequest };
                evtSuccList = new List<int> { (int)NREventManager.SameLTEDiffNRHandoverSuccess };
                lteEvtRequList = new List<int> {
                  (int)NREventManager.LTEInterHandoverRequest,
                  (int)NREventManager.LTEIntraHandoverRequest };
                lteEvtSuccList = new List<int> {
                  (int)NREventManager.LTEInterHandoverSuccess,
                  (int)NREventManager.LTEIntraHandoverSuccess  };
            }
            else if (type == NRServiceName.SA)
            {
                evtRequList = new List<int> {
                  (int)NREventManager.NRInterHandoverRequest,
                  (int)NREventManager.NRIntraHandoverRequest };
                evtSuccList = new List<int> {
                  (int)NREventManager.NRInterHandoverSuccess,
                  (int)NREventManager.NRIntraHandoverSuccess  };
            }
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.NSA)
            {
                if (!bts.NSABtsInfo.FileBtsHandOverInfoDic.TryGetValue(bts.CurFileBtsName, out BtsHandoverInfo info))
                {
                    info = new BtsHandoverInfo(bts.CurFileBtsName);
                    bts.NSABtsInfo.FileBtsHandOverInfoDic.Add(bts.CurFileBtsName, info);
                }

                resetEvtList(condition.NRServiceType);
                //优先统计Same LTE Diff NR Handover Request
                //如果没有Same LTE Diff NR Handover Request再统计LTE Intra Handover Request
                dealEvtCount(fileManager, info.HandoverRate, evtSuccList, evtRequList);
                if (info.HandoverRate.SucceedCnt == 0 && info.HandoverRate.RequestCnt == 0)
                {
                    dealEvtCount(fileManager, info.HandoverRate, lteEvtSuccList, lteEvtRequList);
                }

                info.HandoverRate.CalculateFailEvt();
            }
            else if (condition.NRServiceType == NRServiceName.SA)
            {
                if (!bts.SABtsInfo.FileBtsHandOverInfoDic.TryGetValue(bts.CurFileBtsName, out BtsHandoverInfo info))
                {
                    info = new BtsHandoverInfo(bts.CurFileBtsName);
                    bts.SABtsInfo.FileBtsHandOverInfoDic.Add(bts.CurFileBtsName, info);
                }

                resetEvtList(condition.NRServiceType);
                dealEvtCount(fileManager, info.HandoverRate, evtSuccList, evtRequList);

                info.HandoverRate.CalculateFailEvt();
            }
        }

        private void dealEvtCount(DTFileDataManager fileManager, SuccessRateKpiInfo handOverInfo
            , List<int> evtSuccList, List<int> evtRequList)
        {
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    handOverInfo.RequestCnt++;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    handOverInfo.SucceedCnt++;
                }
            }
        }

        protected override void verifyfileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellInfo cell, NRStationAcceptCondition condition)
        {
            //1.成功率100%
            foreach (var handOverInfo in curBtsServiceInfo.FileBtsHandOverInfoDic.Values)
            {
                handOverInfo.Calculate();
                handOverInfo.HandoverRate.Judge(0, 1);
            }
        }
    }

    class NRAcpCoverPic : NRStationAcceptCoverPic
    {
        public NRAcpCoverPic(string picPath)
            : base(picPath)
        {
            reSetMapView("NR_SS_RSRP", new Func(getRsrpRanges), "");
            reSetMapView("NR_SS_SINR", new Func(getSinrRanges), "");
        }

        protected List<RangeInfo> getRsrpRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
            ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
            ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
            ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
            ranges.Add(new RangeInfo(true, false, -70, -30, Color.Blue));
            return ranges;
        }

        protected List<RangeInfo> getSinrRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            ranges.Add(new RangeInfo(false, false, -20, 3, Color.Red));
            ranges.Add(new RangeInfo(true, false, 3, 10, Color.Orange));
            ranges.Add(new RangeInfo(true, false, 10, 16, Color.Yellow));
            ranges.Add(new RangeInfo(true, false, 16, 25, Color.Green));
            ranges.Add(new RangeInfo(true, false, 25, 50, Color.Blue));
            return ranges;
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT上传") || fileInfo.Name.Contains("DT下载");
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, NRBtsInfo bts, NRCellServiceInfo cellTypeInfo, NRStationAcceptCondition condition)
        {
            MTGis.DbRect bounds = getCoverBounds(fileManager, cellTypeInfo.Cell.Longitude, cellTypeInfo.Cell.Latitude);
            string paramName;
            string path;
            string band = getBandWidth(fileInfo.Name);

            MapForm mf = initMap(bounds);

            string serviceType = "";
            if (condition.NRServiceType == NRServiceName.NSA)
            {
                serviceType = "NSA_";
            }
            else if (condition.NRServiceType == NRServiceName.SA)
            {
                serviceType = "SA_";
            }
            string cellName = serviceType + cellTypeInfo.Cell.Name;

            if (fileInfo.Name.Contains("DT下载") || string.IsNullOrEmpty(cellTypeInfo.NRRsrpPic.PicPath))
            {
                paramName = "NR_SS_RSRP";
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRRsrpPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                paramName = "NR_lte_RSRP";
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.LteRsrpPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                paramName = "NR_SS_SINR";
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRSinrPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                paramName = "NR_lte_SINR";
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.LteSinrPic.PicPath = fireMapAndTakePic(mf, paramName, path);
            }

            if (fileInfo.Name.Contains("DT上传"))
            {
                paramName = "NR_Throughput_MAC_UL_Mb";
                reSetMapView(paramName, new Func(getULRanges), band);
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRULPic.PicPath = fireMapAndTakePic(mf, paramName, path);
            }
            else
            {
                paramName = "NR_Throughput_MAC_DL_Mb";
                reSetMapView(paramName, new Func(getDLRanges), band);
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRDLPic.PicPath = fireMapAndTakePic(mf, paramName, path);
            }
        }

        protected List<RangeInfo> getDLRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            switch (band)
            {
                //case "60M":
                //case "80M":
                //case "100M":
                //    break;
                default:
                    ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                    ranges.Add(new RangeInfo(true, false, 10, 100, Color.Orange));
                    ranges.Add(new RangeInfo(true, false, 100, 400, Color.Yellow));
                    ranges.Add(new RangeInfo(true, false, 400, 700, Color.Green));
                    ranges.Add(new RangeInfo(true, false, 700, 10000, Color.Blue));
                    break;
            }
            return ranges;
        }

        protected List<RangeInfo> getULRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            switch (band)
            {
                //case "60M":
                //case "80M":
                //case "100M":
                //    break;
                default:
                    ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                    ranges.Add(new RangeInfo(true, false, 10, 25, Color.Orange));
                    ranges.Add(new RangeInfo(true, false, 25, 75, Color.Yellow));
                    ranges.Add(new RangeInfo(true, false, 75, 100, Color.Green));
                    ranges.Add(new RangeInfo(true, false, 100, 10000, Color.Blue));
                    break;
            }
            return ranges;
        }
    }

    class NRAcpHandoverPic : StationAcceptHanOverPic
    {
        public NRAcpHandoverPic(string picPath)
            : base(picPath)
        {

        }

        private const string pciParamName = "NR_PCI";

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRBtsInfo nrBts = bts as NRBtsInfo;
            NRStationAcceptCondition nrCondition = condition as NRStationAcceptCondition;
            reSetPCIMapView(nrBts.Bts, nrBts.CurFileBtsName, pciParamName);

            MTGis.DbRect bounds;
            if (nrBts.CurFileBtsName == nrBts.BtsName)
            {
                bounds = getCoverBounds(fileManager, nrBts.Bts.Longitude, nrBts.Bts.Latitude);
            }
            else
            {
                bounds = getCoverBounds(fileManager);
            }

            MapForm mf = initMap(bounds);
            MainModel.GetInstance().DrawDifferentServerColor = true;
            if (nrCondition.NRServiceType == NRServiceName.NSA)
            {
                if (!nrBts.NSABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.CurFileBtsName, out BtsHandoverInfo info))
                {
                    info = new BtsHandoverInfo(nrBts.CurFileBtsName);
                    nrBts.NSABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.CurFileBtsName, info);
                }

                string path = getCoverPicPath(nrBts.BtsName, "NSA_" + nrBts.CurFileBtsName, pciParamName);
                info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
            }
            else if (nrCondition.NRServiceType == NRServiceName.SA)
            {
                if (!nrBts.SABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.CurFileBtsName, out BtsHandoverInfo info))
                {
                    info = new BtsHandoverInfo(nrBts.CurFileBtsName);
                    nrBts.SABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.CurFileBtsName, info);
                }

                string path = getCoverPicPath(nrBts.BtsName, "SA_" + nrBts.CurFileBtsName, pciParamName);
                info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
            }

            MainModel.GetInstance().DrawDifferentServerColor = false;
        }

        protected override string fireMapAndTakePic(MapForm mf, string paramName, string filePath)
        {
            //要让小区显示在采样点之上
            var nrLayer = mf.GetNRCellLayer();
            mf.MakeSureCustomLayerVisible(nrLayer, true);

            return base.fireMapAndTakePic(mf, paramName, filePath);
        }

        protected override List<ICell> getValidCells(ISite bts, string btsNamePostfix)
        {
            List<ICell> cellList = new List<ICell>();
            if (bts is NRBTS)
            {
                NRBTS nrBts = bts as NRBTS;
                foreach (NRCell cell in nrBts.Cells)
                {
                    if (cell.Name.Contains(btsNamePostfix))
                    {
                        cellList.Add(cell);
                    }
                }
            }
            return cellList;
        }

        protected override List<int> getPCIList(List<ICell> cellList)
        {
            List<int> pciList = new List<int>();
            foreach (ICell cell in cellList)
            {
                NRCell nrCell = cell as NRCell;
                pciList.Add(nrCell.PCI);
            }
            return pciList;
        }

        protected override void setServerCellColorByPCI(List<ICell> cellList, List<RangeInfo> ranges)
        {
            foreach (ICell cell in cellList)
            {
                NRCell nrCell = cell as NRCell;
                foreach (RangeInfo range in ranges)
                {
                    if (nrCell.PCI >= range.Min && nrCell.PCI < range.Max)
                    {
                        nrCell.ServerCellColor = range.RangeColor;
                        break;
                    }
                }
            }
        }
    }

    class NRAcpBtsOutdoorScenePic : NRStationAcceptBase
    {
        protected List<string> picNameList;
        protected List<string> pathList = new List<string>();

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            if (!nrBtsInfo.OutdoorScenePicInfoDic.TryGetValue(nrBtsInfo.CurFileBtsName, out NRBtsOutdoorScenePicInfo info))
            {
                info = new NRBtsOutdoorScenePicInfo(nrBtsInfo.CurFileBtsName);
                nrBtsInfo.OutdoorScenePicInfoDic.Add(nrBtsInfo.CurFileBtsName, info);
            }
            else
            {
                return;
            }

            picNameList = new List<string>()
            {
                "建筑物全景照.jpg",
                "站点入口图.jpg",
                "屋顶天面全景图.jpg",
                "小区1.jpg",
                "小区2.jpg",
                "小区3.jpg",
                //"小区4.jpg",
                //"小区5.jpg",
                //"小区6.jpg",
                //"小区7.jpg",
                //"小区8.jpg",
                //"小区9.jpg",
                "方向0.jpg",
                "方向45.jpg",
                "方向90.jpg",
                "方向135.jpg",
                "方向180.jpg",
                "方向225.jpg",
                "方向270.jpg",
                "方向315.jpg",
                "其他1.jpg",
                "其他2.jpg"
            };

            StationAcceptDownloadPicHelper.DownloadPicFile(Singleton<NRStationAcceptConfigHelper>.Instance, nrBtsInfo.CurFileBtsName, picNameList);
            string curBtsPicPath = Singleton<NRStationAcceptConfigHelper>.Instance.ConfigInfo.BtsLocalCoverPicPath;
            pathList.Add(curBtsPicPath);
            info.PanoramicPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "建筑物全景照.jpg");
            info.EntrancePicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "站点入口图.jpg");
            info.RoofPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "屋顶天面全景图.jpg");

            info.Cell1PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区1.jpg");
            info.Cell2PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区2.jpg");
            info.Cell3PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区3.jpg");
            //info.Cell4PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区4.jpg");
            //info.Cell5PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区5.jpg");
            //info.Cell6PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区6.jpg");
            //info.Cell7PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区7.jpg");
            //info.Cell8PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区8.jpg");
            //info.Cell9PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "小区9.jpg");

            info.Dir0PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向0.jpg");
            info.Dir45PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向45.jpg");
            info.Dir90PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向90.jpg");
            info.Dir135PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向135.jpg");
            info.Dir180PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向180.jpg");
            info.Dir225PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向225.jpg");
            info.Dir270PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向270.jpg");
            info.Dir315PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "方向315.jpg");
            info.Other1PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "其他1.jpg");
            info.Other2PicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, "其他2.jpg");
        }

        public override void Clear()
        {
            foreach (var path in pathList)
            {
                StationAcceptDownloadPicHelper.Clear(path);
            }
        }
    }

    class NRAcpBtsAlarm : NRStationAcceptBase
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            if (nrBtsInfo.BtsAlarmList == null)
            {
                nrBtsInfo.BtsAlarmList = new List<NRAlarmInfo>();
            }
            else
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("NR单验告警", out setting))
            {
                return;
            }

            DIYQueryNRAlarm query = new DIYQueryNRAlarm(nrBtsInfo.BtsName, setting);
            query.Query();
            if (query.DataList.Count > 0)
            {
                nrBtsInfo.BtsAlarmList = query.DataList;
            }
        }

    }

    class NRAcpBtsBaseConfig : NRStationAcceptBase
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            if (nrBtsInfo.BtsBaseInfo != null)
            {
                return;
            }
            NRBtsParameters btsParameters = new NRBtsParameters();
            nrBtsInfo.BtsBaseInfo = btsParameters;

            btsParameters.BtsName = nrBtsInfo.BtsName;

            addAntennaPlatform(btsParameters);

            addNetworkConfig(btsParameters);

            addAuditData(btsParameters);

            addWireless(btsParameters);

            verifyfileResult(nrBtsInfo);
        }

        private void addAntennaPlatform(NRBtsParameters btsParameters)
        {
            DIYQueryNRAntennaPlatform antQuery = new DIYQueryNRAntennaPlatform();
            antQuery.SetCondition(btsParameters.BtsName);
            antQuery.Query();

            foreach (var info in antQuery.NRAntennaPlatformDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.BtsLongutide.Real = info.BtsLongitude;
                btsParameters.BtsLatitude.Real = info.BtsLatitude;
                cellInfo.Longitude.Real = info.Longitude;
                cellInfo.Latitude.Real = info.Latitude;
                cellInfo.Altitude.Real = info.Altitude;
                cellInfo.Direction.Real = info.Direction;
                cellInfo.MechanicalTilt.Real = info.MechanicalTilt;
            }
        }

        private void addNetworkConfig(NRBtsParameters btsParameters)
        {
            DIYQueryNRNetworkConfig netQuery = new DIYQueryNRNetworkConfig();
            netQuery.SetCondition(btsParameters.BtsName);
            netQuery.Query();

            foreach (var info in netQuery.NRNetworkConfigDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.ENodeBID = info.NodeBID;
                btsParameters.CellCount.Real = info.CellCount;
                btsParameters.TAC.Real = info.TAC;
                cellInfo.CellID.Real = info.CellID;
                cellInfo.PCI.Real = info.PCI;
                cellInfo.PRACH.Real = info.PRACH;
                //cellInfo.Downward.Real = cellInfo.MechanicalTilt.Real;// + info.Downtilt;
            }
        }

        private void addAuditData(NRBtsParameters btsParameters)
        {
            DIYQueryNRStationAuditData dataQuery = new DIYQueryNRStationAuditData();
            dataQuery.SetCondition(btsParameters.BtsName);
            dataQuery.Query();

            foreach (var info in dataQuery.NRStationAuditDataDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.Country = info.Country;
                btsParameters.BtsLongutide.Planing = info.BtsLongitude;
                btsParameters.BtsLatitude.Planing = info.BtsLatitude;
                cellInfo.Longitude.Planing = info.Longitude;
                cellInfo.Latitude.Planing = info.Latitude;
                cellInfo.Altitude.Planing = info.Altitude;
                cellInfo.Direction.Planing = info.Direction;
                cellInfo.Downtilt.Planing = info.Downtilt;
                cellInfo.MechanicalTilt.Planing = info.MechanicalTilt;
                cellInfo.Downward.Planing = info.Downward;
            }
        }

        private void addWireless(NRBtsParameters btsParameters)
        {
            DIYQueryNRWirelessPlanningTable queryTable = new DIYQueryNRWirelessPlanningTable();
            queryTable.Query();
            if (string.IsNullOrEmpty(queryTable.TableName))
            {
                return;
            }

            DIYQueryNRWirelessPlanning wirelessQuery = new DIYQueryNRWirelessPlanning();
            wirelessQuery.SetCondition(btsParameters.BtsName);
            wirelessQuery.TableName = queryTable.TableName;
            wirelessQuery.Query();

            foreach (var info in wirelessQuery.NRWirelessPlanningDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }
                btsParameters.TAC.Planing = info.TAC;
                cellInfo.CellID.Planing = info.CellID;
                cellInfo.PCI.Planing = info.PCI;
                cellInfo.FreqBand.Planing = info.FreqBand;
                cellInfo.Freq.Planing = info.Freq;
                cellInfo.SSBFreq.Planing = info.SSBFreq;
                cellInfo.Bandwidth.Planing = info.Bandwidth;
                cellInfo.PRACH.Planing = info.PRACH;
                cellInfo.SubFrameRatio.Planing = info.SubFrameRatio;
                cellInfo.CoreMode.Planing = info.Mode;
            }
        }

        protected void verifyfileResult(NRBtsInfo bts)
        {
            //各个参数的判断条件不同,针对每个参数单独判断
            NRBtsParameters btsParameters = bts.BtsBaseInfo as NRBtsParameters;
            btsParameters.Caluculate();
        }
    }
}
