﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.LastWeakRoad.GridCompare
{
    public class WeakSinrRoadGrid : WeakSINRRoad
    {
        public FileInfo FileInfo { get; private set; }
        public WeakSinrRoadGrid(FileInfo fi)
        {
            this.FileInfo = fi;
        }

        private List<DbRect> grids = null;
        public List<DbRect> Grids
        {
            get { return grids; }
        }

        public virtual void MakeGrid(double gridSpanDegree)
        {
            grids = new List<DbRect>();
            foreach(TestPoint tp in TestPoints)
            {
                DbRect rect = GridHelper.GetCustomSizeBounds(tp.Longitude , tp.Latitude , gridSpanDegree);
                int idx = grids.FindIndex(x =>
                {
                    return x.x1 == rect.x1 && x.y1 == rect.y1;
                });
                if (idx == -1)
                {
                    grids.Add(rect);
                }
            }
            FindRoadName();
        }

        internal bool Intersect(WeakSinrRoadGrid grid2)
        {
            foreach(DbRect rect in grid2.Grids)
            {
                foreach(DbRect grid in grids)
                {
                    if(grid.IsPointInThisRect(rect.Center()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public List<WeakSinrRoadGrid> IntersectSegs { get;private set; }

        public string IntersectSn
        {
            get
            {
                StringBuilder sns = new StringBuilder();
                foreach(WeakSinrRoadGrid other in IntersectSegs)
                {
                    sns.Append(other.SN + "；");
                }
                return sns.ToString().TrimEnd('；');
            }
        }

        internal void AddIntersectSeg(WeakSinrRoadGrid grid1)
        {
            if(IntersectSegs == null)
            {
                IntersectSegs = new List<WeakSinrRoadGrid>();
            }
            IntersectSegs.Add(grid1);
        }

        public int IntersectSegNum
        {
            get
            {
                return IntersectSegs==null ? 0 : IntersectSegs.Count;
            }
        }
    }
}
