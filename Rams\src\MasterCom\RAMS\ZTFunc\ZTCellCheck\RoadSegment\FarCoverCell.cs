﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck
{
    public class FarCoverCell : IProblemData
    {
        public override string ToString()
        {
            return "超远覆盖";
        }
        public FarCoverCell(LTECell cell)
        {
            this.Cell = cell;
        }
        int totalPntCnt = 0;
        private readonly List<TestPoint> farCoverPts = new List<TestPoint>();
        public int FarCoverPointNum
        {
            get { return farCoverPts.Count; }
        }
        public void AddTestPoint(TestPoint tp, bool farCover)
        {
            totalPntCnt++;
            if (farCover)
            {
                farCoverPts.Add(tp);
            }
        }

        public LTECell Cell
        {
            get;
            private set;
        }

        #region IRelatedData 成员

        public List<string> Cells
        {
            get
            {
                List<string> ret = new List<string>();
                ret.Add(Cell.Name);
                return ret;
            }
        }

        public List<Model.TestPoint> TestPoints
        {
            get { return farCoverPts; }
        }

        public List<Model.Event> Events
        {
            get { return new List<Event>(); }
        }

        private string roadDesc = null;
        public string RoadDesc
        {
            get
            {
                getRoadDesc();
                return roadDesc;
            }
        }


        public void MakeSummary()
        {
            getRoadDesc();
        }

        private void getRoadDesc()
        {
            if (roadDesc != null)
            {
                return;
            }
            List<double> lng = new List<double>();
            List<double> lat = new List<double>();
            dealTPs(lng, lat);
            dealEvts(lng, lat);
            roadDesc = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
        }

        private void dealTPs(List<double> lng, List<double> lat)
        {
            if (TestPoints != null)
            {
                float rsrpMin = float.MaxValue;
                float rsrpMax = float.MinValue;
                float rsrpSum = 0;
                int rsrpNum = 0;
                float sinrMin = float.MaxValue;
                float sinrMax = float.MinValue;
                float sinrSum = 0;
                int sinrNum = 0;
                foreach (TestPoint tp in TestPoints)
                {
                    lng.Add(tp.Longitude);
                    lat.Add(tp.Latitude);
                    dealRsrp(ref rsrpMin, ref rsrpMax, ref rsrpSum, ref rsrpNum, tp);
                    dealSinr(ref sinrMin, ref sinrMax, ref sinrSum, ref sinrNum, tp);
                }
                caculateRsrpRes(rsrpMin, rsrpMax, rsrpSum, rsrpNum);
                caculateSinrRes(sinrMin, sinrMax, sinrSum, sinrNum);
            }
        }

        private static void dealRsrp(ref float rsrpMin, ref float rsrpMax, ref float rsrpSum, ref int rsrpNum, TestPoint tp)
        {
            float? rsrpObj = null;
            if (tp is LTEFddTestPoint)
            {
                rsrpObj = (float?)tp["lte_fdd_RSRP"];
            }
            else
            {
                rsrpObj = (float?)tp["lte_RSRP"];
            }
            if (rsrpObj != null && -141 <= rsrpObj && rsrpObj <= 25)
            {
                rsrpMin = Math.Min(rsrpMin, (float)rsrpObj);
                rsrpMax = Math.Max(rsrpMax, (float)rsrpObj);
                rsrpSum += (float)rsrpObj;
                rsrpNum++;
            }
        }

        private static void dealSinr(ref float sinrMin, ref float sinrMax, ref float sinrSum, ref int sinrNum, TestPoint tp)
        {
            float? sinrObj = null;
            if (tp is LTEFddTestPoint)
            {
                sinrObj = (float?)tp["lte_fdd_SINR"];
            }
            else
            {
                sinrObj = (float?)tp["lte_SINR"];
            }
            if (sinrObj != null && -50 <= sinrObj && sinrObj <= 50)
            {
                sinrMin = Math.Min(sinrMin, (float)sinrObj);
                sinrMax = Math.Max(sinrMax, (float)sinrObj);
                sinrSum += (float)sinrObj;
                sinrNum++;
            }
        }

        private void caculateRsrpRes(float rsrpMin, float rsrpMax, float rsrpSum, int rsrpNum)
        {
            if (float.MinValue != rsrpMax)
            {
                this.RSRPMax = rsrpMax;
            }
            if (float.MaxValue != rsrpMin)
            {
                this.RSRPMin = rsrpMin;
            }
            if (rsrpNum != 0)
            {
                this.RSRPAvg = (float)Math.Round(1.0 * rsrpSum / rsrpNum, 2);
            }
        }

        private void caculateSinrRes(float sinrMin, float sinrMax, float sinrSum, int sinrNum)
        {
            if (float.MinValue != sinrMax)
            {
                this.SINRMax = sinrMax;
            }
            if (float.MaxValue != sinrMin)
            {
                this.SINRMin = sinrMin;
            }
            if (sinrNum != 0)
            {
                this.SINRAvg = (float)Math.Round(1.0 * sinrSum / sinrNum, 2);
            }
        }

        private void dealEvts(List<double> lng, List<double> lat)
        {
            if (Events != null)
            {
                foreach (Event evt in Events)
                {
                    lng.Add(evt.Longitude);
                    lat.Add(evt.Latitude);
                }
            }
        }
        #endregion

        #region IProblemData 成员


        public float? RSRPMin
        {
            get;
            private set;
        }

        public float? RSRPMax
        {
            get;
            private set;
        }

        public float? RSRPAvg
        {
            get;
            private set;
        }

        public float? SINRMin
        {
            get;
            private set;
        }

        public float? SINRMax
        {
            get;
            private set;
        }

        public float? SINRAvg
        {
            get;
            private set;
        }

        #endregion
    }
}
