﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class ZTAreaListForm : MinCloseForm
    {
        public ZTAreaListForm()
            : base()
        {
            InitializeComponent();
        }

        private List<AreaBase> allAreas = null;
        public ZTAreaListForm(ZTAreaManager areaMng)
            : this()
        {
            this.areaMng = areaMng;
            allAreas = areaMng.AllAreas;
            areaListPnl.FillData(areaMng);
            cbxRank.Items.Clear();
            foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
            {
                cbxRank.Items.Add(rank);
            }
            if (cbxRank.Items.Count > 0)
            {
                cbxRank.SelectedIndex = cbxRank.Items.Count - 1;
            }

            checkEditFilter.CheckedChanged += new EventHandler(checkEditFilter_CheckedChanged);
            checkEditFilter_CheckedChanged(null, null);
        }

        private void checkEditFilter_CheckedChanged(object sender, EventArgs e)
        {
            numSquareMin.Enabled = numSquareMax.Enabled = numXYRateMin.Enabled = numXYRateMax.Enabled = checkEditFilter.Checked;
        }

        public void InitData()
        {
            btnFilter_Click(null, null);
        }

        private ZTAreaManager areaMng;

        private void btnFilter_Click(object sender, EventArgs e)
        {
            List<AreaBase> areas = filterArea();
            gridCtrl.DataSource = areas;
            gridCtrl.RefreshDataSource();
            Dictionary<AreaBase, bool> dic = new Dictionary<AreaBase, bool>();
            foreach (AreaBase area in areas)
            {
                foreach (AreaBase subArea in area.GetLeafs())
                {
                    dic[subArea] = true;
                }
            }
            List<AreaBase> layerAreas = new List<AreaBase>(dic.Keys);
            getLayer().Areas = layerAreas;
            layer.Invalidate();
        }

        private List<AreaBase> filterArea()
        {
            AreaRank rank = cbxRank.SelectedItem as AreaRank;
            double minArea = (double)numSquareMin.Value;
            double maxArea = (double)numSquareMax.Value;
            double minXYRate = (double)numXYRateMin.Value;
            //double maxXYRate = (double)numXYRateMax.Value;
            string name = txtAreaName.Text;
            bool filterByName = name.Length > 0;
            List<AreaBase> areas = new List<AreaBase>();
            foreach (AreaBase area in allAreas)
            {
                if (area.Rank == rank && (!filterByName || (area.Name.Contains(name))) &&
                    (!checkEditFilter.Checked || 
                    ((minArea <= area.SquareKM && area.SquareKM <= maxArea) && (minXYRate <= area.XYRate && area.XYRate <= maxArea))))
                {
                    areas.Add(area);
                }
            }
            return areas;
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<AreaBase> data = gridCtrl.DataSource as List<AreaBase>;
            if (data == null || data.Count == 0)
            {
                MessageBox.Show("当前列表为空");
                return;
            }
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            AreaBase area = gv.GetRow(info.RowHandle) as AreaBase;

            Dictionary<AreaBase, bool> dic = new Dictionary<AreaBase, bool>();
            foreach (AreaBase subArea in area.GetLeafs())
            {
                dic[subArea] = true;
            }
            List<AreaBase> layerAreas = new List<AreaBase>(dic.Keys);
            getLayer().SelectedAreas = layerAreas;
            MainModel.MainForm.GetMapForm().GoToView(area.Bounds);
        }

        ZTAreaArchiveLayer layer;
        private ZTAreaArchiveLayer getLayer()
        {
            layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
            if (layer == null)
            {
                layer = new ZTAreaArchiveLayer();
                MainModel.MainForm.GetMapForm().AddLayerBase(layer);
            }
            return layer;
        }

    }
}
