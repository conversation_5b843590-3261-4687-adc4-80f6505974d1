﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        List<CellCheckItem> items = null;
        public void FillData(List<CellCheckItem> list)
        {
            this.items = list;
            filter();
            MainModel.FireSetDefaultMapSerialTheme("LTE:RSRP","LTE_FDD:RSRP");
        }

        private void miExport_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            CellCheckItem item = gridView.GetRow(info.RowHandle) as CellCheckItem;
            if (item == null)
            {
                return;
            }
            LTECell cell = CellManager.GetInstance().GetLTECellLatest(item.CellName);
            MainModel.SelectedLTECell = cell;
            MainModel.ClearDTData();
            MainModel.DrawHandoverSeq = false;
            MainModel.HandOverSeqEvents = new List<Event>();
            if (item.Data != null)
            {
                if (item.Data.Events!=null)
                {
                    MainModel.DrawHandoverSeq = true;
                    MainModel.HandOverSeqEvents = item.Data.Events;
                    foreach (Event evt in item.Data.Events)
                    {
                        MainModel.DTDataManager.Add(evt);
                    }
                }
                if (item.Data.TestPoints!=null)
                {
                    foreach (TestPoint tp in item.Data.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private Dictionary<string, List<string>> getSiteFilter()
        {
            Dictionary<string, List<string>> filterDic = new Dictionary<string, List<string>>();

            List<string> chkList = getChkDic(grpFar);
            filterDic[grpFar.Text] = chkList;
            chkList = getChkDic(grpHigh);
            filterDic[grpHigh.Text] = chkList;
            chkList = getChkDic(grpNear);
            filterDic[grpNear.Text] = chkList;
            return filterDic;
        }

        private List<string> getChkDic(GroupBox grp)
        {
            List<string> chkList = new List<string>();
            foreach (Control ctrl in grp.Controls)
            {
                if (ctrl is CheckBox)
                {
                    CheckBox chk = ctrl as CheckBox;
                    if (chk.Checked)
                    {
                        chkList.Add(chk.Text);
                    }
                }
            }
            return chkList;
        }

        private void filter()
        {
            Dictionary<string, List<string>> filterDic = getSiteFilter();
            List<CellCheckItem> filteredList = new List<CellCheckItem>();
            foreach (CellCheckItem item in items)
            {
                bool filter = false;
                if (!string.IsNullOrEmpty(item.UltraType))
                {
                    string[] types = item.UltraType.Split('；');
                    foreach (string type in types)
                    {
                        if (filterDic[type].Contains(item.Prob))
                        {
                            filter = true;
                            break;
                        }
                    }
                }
                if (!filter)
                {
                    item.SN = filteredList.Count + 1;
                    filteredList.Add(item);
                }
            }
            gridControl.DataSource = filteredList;
            gridControl.RefreshDataSource();
        }

        private void btnFilter_Click(object sender, EventArgs e)
        {
            filter();
        }



    }
}
