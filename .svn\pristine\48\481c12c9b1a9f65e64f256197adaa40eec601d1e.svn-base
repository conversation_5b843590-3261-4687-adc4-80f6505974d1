﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class IndexOfRoadStructureForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.btnColorSetting = new DevExpress.XtraEditors.SimpleButton();
            this.edtSampleStructuralIndex = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.edtGridStructuralIndex = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbxBandType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlGrid = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToSHP = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTXT = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnGridStructuralIndex = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlSample = new DevExpress.XtraGrid.GridControl();
            this.gridViewSample = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleStructuralIndex = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlCell = new DevExpress.XtraGrid.GridControl();
            this.gridViewCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTCHCount = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtSampleStructuralIndex.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtGridStructuralIndex.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxBandType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGrid)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSample)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSample)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.btnColorSetting);
            this.panelControl1.Controls.Add(this.edtSampleStructuralIndex);
            this.panelControl1.Controls.Add(this.labelControl3);
            this.panelControl1.Controls.Add(this.edtGridStructuralIndex);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.cbxBandType);
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(843, 43);
            this.panelControl1.TabIndex = 0;
            // 
            // btnColorSetting
            // 
            this.btnColorSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorSetting.Location = new System.Drawing.Point(745, 11);
            this.btnColorSetting.Name = "btnColorSetting";
            this.btnColorSetting.Size = new System.Drawing.Size(75, 23);
            this.btnColorSetting.TabIndex = 9;
            this.btnColorSetting.Text = "着色设置";
            this.btnColorSetting.Click += new System.EventHandler(this.btnColorSetting_Click);
            // 
            // edtSampleStructuralIndex
            // 
            this.edtSampleStructuralIndex.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edtSampleStructuralIndex.Location = new System.Drawing.Point(438, 12);
            this.edtSampleStructuralIndex.Name = "edtSampleStructuralIndex";
            this.edtSampleStructuralIndex.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtSampleStructuralIndex.Properties.DisplayFormat.FormatString = "0.00";
            this.edtSampleStructuralIndex.Properties.EditFormat.FormatString = "0.00";
            this.edtSampleStructuralIndex.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.edtSampleStructuralIndex.Properties.Mask.EditMask = "0.00";
            this.edtSampleStructuralIndex.Size = new System.Drawing.Size(54, 21);
            this.edtSampleStructuralIndex.TabIndex = 5;
            this.edtSampleStructuralIndex.ValueChanged += new System.EventHandler(this.edtSampleStructuralIndex_ValueChanged);
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(339, 15);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(93, 14);
            this.labelControl3.TabIndex = 4;
            this.labelControl3.Text = "经纬度结构指数≥";
            // 
            // edtGridStructuralIndex
            // 
            this.edtGridStructuralIndex.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edtGridStructuralIndex.Location = new System.Drawing.Point(242, 12);
            this.edtGridStructuralIndex.Name = "edtGridStructuralIndex";
            this.edtGridStructuralIndex.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtGridStructuralIndex.Properties.DisplayFormat.FormatString = "0.00";
            this.edtGridStructuralIndex.Properties.EditFormat.FormatString = "0.00";
            this.edtGridStructuralIndex.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.edtGridStructuralIndex.Properties.Mask.EditMask = "0.00";
            this.edtGridStructuralIndex.Size = new System.Drawing.Size(55, 21);
            this.edtGridStructuralIndex.TabIndex = 3;
            this.edtGridStructuralIndex.ValueChanged += new System.EventHandler(this.edtGridStructuralIndex_ValueChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(155, 15);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(81, 14);
            this.labelControl2.TabIndex = 2;
            this.labelControl2.Text = "栅格结构指数≥";
            // 
            // cbxBandType
            // 
            this.cbxBandType.EditValue = "900";
            this.cbxBandType.Location = new System.Drawing.Point(65, 12);
            this.cbxBandType.Name = "cbxBandType";
            this.cbxBandType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxBandType.Properties.Items.AddRange(new object[] {
            "900",
            "1800"});
            this.cbxBandType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxBandType.Size = new System.Drawing.Size(62, 21);
            this.cbxBandType.TabIndex = 1;
            this.cbxBandType.SelectedIndexChanged += new System.EventHandler(this.cbxBandType_SelectedIndexChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(23, 15);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "频段：";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 43);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(843, 451);
            this.splitContainerControl1.SplitterPosition = 164;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(843, 281);
            this.splitContainerControl2.SplitterPosition = 334;
            this.splitContainerControl2.TabIndex = 0;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridControlGrid);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(334, 281);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "栅格信息";
            // 
            // gridControlGrid
            // 
            this.gridControlGrid.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGrid.Location = new System.Drawing.Point(2, 23);
            this.gridControlGrid.MainView = this.gridViewGrid;
            this.gridControlGrid.Name = "gridControlGrid";
            this.gridControlGrid.Size = new System.Drawing.Size(330, 256);
            this.gridControlGrid.TabIndex = 1;
            this.gridControlGrid.UseEmbeddedNavigator = true;
            this.gridControlGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGrid});
            this.gridControlGrid.DoubleClick += new System.EventHandler(this.gridControlGrid_DoubleClick);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel,
            this.miExportToSHP,
            this.miExportTXT});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 92);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportToExcel.Text = "导出到Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // miExportToTAB
            // 
            this.miExportToSHP.Name = "miExportToTAB";
            this.miExportToSHP.Size = new System.Drawing.Size(152, 22);
            this.miExportToSHP.Text = "导出到shp...";
            this.miExportToSHP.Click += new System.EventHandler(this.miExportToSHP_Click);
            // 
            // miExportTXT
            // 
            this.miExportTXT.Name = "miExportTXT";
            this.miExportTXT.Size = new System.Drawing.Size(152, 22);
            this.miExportTXT.Text = "导出到TXT...";
            this.miExportTXT.Click += new System.EventHandler(this.miExportTXT_Click);
            // 
            // gridViewGrid
            // 
            this.gridViewGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnGridStructuralIndex,
            this.gridColumnLongitude,
            this.gridColumnLatitude});
            this.gridViewGrid.GridControl = this.gridControlGrid;
            this.gridViewGrid.Name = "gridViewGrid";
            this.gridViewGrid.OptionsBehavior.Editable = false;
            this.gridViewGrid.OptionsView.ColumnAutoWidth = false;
            this.gridViewGrid.OptionsView.ShowDetailButtons = false;
            this.gridViewGrid.OptionsView.ShowGroupPanel = false;
            this.gridViewGrid.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewGrid_FocusedRowChanged);
            // 
            // gridColumnGridStructuralIndex
            // 
            this.gridColumnGridStructuralIndex.Caption = "结构指数";
            this.gridColumnGridStructuralIndex.FieldName = "IndexOfStructure";
            this.gridColumnGridStructuralIndex.Name = "gridColumnGridStructuralIndex";
            this.gridColumnGridStructuralIndex.Visible = true;
            this.gridColumnGridStructuralIndex.VisibleIndex = 0;
            this.gridColumnGridStructuralIndex.Width = 100;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "MidLongitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 1;
            this.gridColumnLongitude.Width = 93;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "MidLatitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 2;
            this.gridColumnLatitude.Width = 94;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.gridControlSample);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(503, 281);
            this.groupControl2.TabIndex = 0;
            this.groupControl2.Text = "经纬度信息";
            // 
            // gridControlSample
            // 
            this.gridControlSample.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlSample.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlSample.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlSample.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlSample.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlSample.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlSample.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlSample.Location = new System.Drawing.Point(2, 23);
            this.gridControlSample.MainView = this.gridViewSample;
            this.gridControlSample.Name = "gridControlSample";
            this.gridControlSample.Size = new System.Drawing.Size(499, 256);
            this.gridControlSample.TabIndex = 2;
            this.gridControlSample.UseEmbeddedNavigator = true;
            this.gridControlSample.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSample});
            // 
            // gridViewSample
            // 
            this.gridViewSample.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnSampleLongitude,
            this.gridColumnSampleLatitude,
            this.gridColumnSampleStructuralIndex});
            this.gridViewSample.GridControl = this.gridControlSample;
            this.gridViewSample.Name = "gridViewSample";
            this.gridViewSample.OptionsBehavior.Editable = false;
            this.gridViewSample.OptionsView.ColumnAutoWidth = false;
            this.gridViewSample.OptionsView.ShowDetailButtons = false;
            this.gridViewSample.OptionsView.ShowGroupPanel = false;
            this.gridViewSample.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewSample_FocusedRowChanged);
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 185;
            // 
            // gridColumnSampleLongitude
            // 
            this.gridColumnSampleLongitude.Caption = "经度";
            this.gridColumnSampleLongitude.FieldName = "Longitude";
            this.gridColumnSampleLongitude.Name = "gridColumnSampleLongitude";
            this.gridColumnSampleLongitude.Visible = true;
            this.gridColumnSampleLongitude.VisibleIndex = 1;
            this.gridColumnSampleLongitude.Width = 91;
            // 
            // gridColumnSampleLatitude
            // 
            this.gridColumnSampleLatitude.Caption = "纬度";
            this.gridColumnSampleLatitude.FieldName = "Latitude";
            this.gridColumnSampleLatitude.Name = "gridColumnSampleLatitude";
            this.gridColumnSampleLatitude.Visible = true;
            this.gridColumnSampleLatitude.VisibleIndex = 2;
            this.gridColumnSampleLatitude.Width = 88;
            // 
            // gridColumnSampleStructuralIndex
            // 
            this.gridColumnSampleStructuralIndex.Caption = "结构指数";
            this.gridColumnSampleStructuralIndex.FieldName = "IndexOfStructure";
            this.gridColumnSampleStructuralIndex.Name = "gridColumnSampleStructuralIndex";
            this.gridColumnSampleStructuralIndex.Visible = true;
            this.gridColumnSampleStructuralIndex.VisibleIndex = 3;
            this.gridColumnSampleStructuralIndex.Width = 93;
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.gridControlCell);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(843, 164);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "小区信息";
            // 
            // gridControlCell
            // 
            this.gridControlCell.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCell.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlCell.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlCell.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlCell.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlCell.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlCell.Location = new System.Drawing.Point(2, 23);
            this.gridControlCell.MainView = this.gridViewCell;
            this.gridControlCell.Name = "gridControlCell";
            this.gridControlCell.Size = new System.Drawing.Size(839, 139);
            this.gridControlCell.TabIndex = 2;
            this.gridControlCell.UseEmbeddedNavigator = true;
            this.gridControlCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCell});
            // 
            // gridViewCell
            // 
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCellName,
            this.gridColumnBCCH,
            this.gridColumnBSIC,
            this.gridColumnRxLev,
            this.gridColumnTCHCount});
            this.gridViewCell.GridControl = this.gridControlCell;
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsBehavior.Editable = false;
            this.gridViewCell.OptionsView.ColumnAutoWidth = false;
            this.gridViewCell.OptionsView.ShowDetailButtons = false;
            this.gridViewCell.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 0;
            this.gridColumnCellName.Width = 125;
            // 
            // gridColumnBCCH
            // 
            this.gridColumnBCCH.Caption = "BCCH";
            this.gridColumnBCCH.FieldName = "BCCH";
            this.gridColumnBCCH.Name = "gridColumnBCCH";
            this.gridColumnBCCH.Visible = true;
            this.gridColumnBCCH.VisibleIndex = 1;
            // 
            // gridColumnBSIC
            // 
            this.gridColumnBSIC.Caption = "BSIC";
            this.gridColumnBSIC.FieldName = "BSIC";
            this.gridColumnBSIC.Name = "gridColumnBSIC";
            this.gridColumnBSIC.Visible = true;
            this.gridColumnBSIC.VisibleIndex = 2;
            // 
            // gridColumnRxLev
            // 
            this.gridColumnRxLev.Caption = "场强";
            this.gridColumnRxLev.FieldName = "RxLev";
            this.gridColumnRxLev.Name = "gridColumnRxLev";
            this.gridColumnRxLev.Visible = true;
            this.gridColumnRxLev.VisibleIndex = 3;
            // 
            // gridColumnTCHCount
            // 
            this.gridColumnTCHCount.Caption = "TCH数";
            this.gridColumnTCHCount.FieldName = "TCHCount";
            this.gridColumnTCHCount.Name = "gridColumnTCHCount";
            this.gridColumnTCHCount.Visible = true;
            this.gridColumnTCHCount.VisibleIndex = 4;
            // 
            // IndexOfRoadStructureForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(843, 494);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.panelControl1);
            this.Name = "IndexOfRoadStructureForm";
            this.Text = "道路结构指数";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtSampleStructuralIndex.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtGridStructuralIndex.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxBandType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGrid)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSample)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSample)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxBandType;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.SpinEdit edtGridStructuralIndex;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit edtSampleStructuralIndex;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraGrid.GridControl gridControlGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridStructuralIndex;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraGrid.GridControl gridControlSample;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSample;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraGrid.GridControl gridControlCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCell;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleStructuralIndex;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRxLev;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraEditors.SimpleButton btnColorSetting;
        private System.Windows.Forms.ToolStripMenuItem miExportToSHP;
        private System.Windows.Forms.ToolStripMenuItem miExportTXT;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTCHCount;
    }
}