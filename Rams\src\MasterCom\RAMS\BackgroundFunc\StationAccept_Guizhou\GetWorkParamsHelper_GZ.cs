﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class GetWorkParamsHelper_GZ
    {
        protected GetWorkParamsHelper_GZ()
        {

        }

        public static Dictionary<string, Dictionary<int, BtsWorkParam_GZ>> GetWorkParamsInfo(
            StationAcceptAutoSet_GZ funcSet)
        {
            upLoadWorkParams(funcSet.CellParamFolderPath);
            return queryWorkParams(funcSet);
        }
        protected static void upLoadWorkParams(string paramFolderPath)
        {   
            try
            {
                reportInfo("开始读取待评估对象数据...");

                List<string> fileNameList = new List<string>();
                if (System.IO.Directory.Exists(paramFolderPath))
                {
                    System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(paramFolderPath);
                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*.csv", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        fileNameList.Add(file.FullName);
                    }

                    if (fileNameList.Count > 0)
                    {
                        //Dictionary<小区TAC_ECI，CellWorkParam_GZ>
                        Dictionary<string, CellWorkParam_GZ> btsParamInfoDic = new Dictionary<string, CellWorkParam_GZ>();
                        foreach (string fileName in fileNameList)//逐个读取待上传工参文件
                        {
                            getWorkParamFormCsv(fileName, ref btsParamInfoDic);
                        }

                        UpLoadWorkParams_GZ upLoadParams = new UpLoadWorkParams_GZ(btsParamInfoDic);
                        upLoadParams.Query();//上传工参

                        GetWorkParamsHelper_QH.BcpFiles(paramFolderPath, fileNameList);
                    }
                    else
                    {
                        reportInfo("未找到指定目录下的待上传工参文件");
                    }
                }
                else
                {
                    reportInfo("未找到指定目录下");
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
        }
        protected static Dictionary<string, Dictionary<int, BtsWorkParam_GZ>> queryWorkParams(
            StationAcceptAutoSet_GZ funcSet)
        {
            // Dictionary<地市, Dictionary<enodebid, BtsWorkParam_GZ>> 
            Dictionary<string, Dictionary<int, BtsWorkParam_GZ>> workParamSumDic = new Dictionary<string, Dictionary<int, BtsWorkParam_GZ>>();
            try
            {
                WorkParamsQuery_GZ queryParams = new WorkParamsQuery_GZ(funcSet);
                queryParams.Query();//从服务端查询需要自动导出报告的站点工参
                workParamSumDic = queryParams.workParamSumDic;
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
            reportInfo(string.Format("查询到{0}个地市的待评估工参数据", workParamSumDic.Count));
            return workParamSumDic;
        }
        
        private static void getWorkParamFormCsv(string fileName
            , ref Dictionary<string, CellWorkParam_GZ> cellParamInfoDic)
        {
            try
            {
                reportInfo(string.Format("正在读取文件 {0} 信息...", fileName));
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(10000);
                    reReadCount++;
                }
                string fileName_Upper = fileName.ToUpper();
                if (fileName_Upper.EndsWith(".CSV"))
                {
                    CSVReader reader = new CSVReader(fileName);
                    List<string[]> listData = reader.GetAllData();
                    if (listData != null && listData.Count > 0)
                    {
                        string[] cols = listData[0];
                        listData.RemoveAt(0);
                        DataTable dtnew = getTableValue(listData, cols);

                        getBtsInfoFromDataTable(fileName, dtnew, ref cellParamInfoDic);
                    }
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
        }

        private static DataTable getTableValue(List<string[]> listData, string[] cols)
        {
            DataTable dtnew = new DataTable();
            foreach (String c in cols)
            {
                DataColumn dcol = new DataColumn(c, typeof(String));
                dtnew.Columns.Add(dcol);
            }
            foreach (String[] rowDataVec in listData)
            {
                DataRow drow = dtnew.NewRow();
                for (int colAt = 0; colAt < rowDataVec.Length; colAt++)
                {
                    drow[colAt] = rowDataVec[colAt].ToString().Trim();
                }
                dtnew.Rows.Add(drow);
            }

            return dtnew;
        }

        private static void getBtsInfoFromDataTable(string fileName, System.Data.DataTable tb
            , ref Dictionary<string, CellWorkParam_GZ> cellParamInfoDic)
        {
            if (tb == null || tb.Rows.Count <= 0)
            {
                return;
            }

            StringBuilder strbErrorInfo = new StringBuilder();
            int index = 0;
            foreach (DataRow row in tb.Rows)
            {
                index++;
                try
                {
                    CellWorkParam_GZ cellInfo = new CellWorkParam_GZ();
                    cellInfo.DistrictName = getStringValue(row, "地市").Replace("市", "");
                    cellInfo.BtsName = getStringValue(row, "基站名称");
                    cellInfo.CellName = getStringValue(row, "小区名称");
                    cellInfo.CoverTypeDes = getStringValue(row, "覆盖类型");
                    cellInfo.ENodeBID = Convert.ToInt32(row["ENBID"]);
                    cellInfo.Tac = Convert.ToInt32(row["TAC"]);
                    cellInfo.CellID = Convert.ToInt32(row["CELLID"]);
                    cellInfo.Longitude = Convert.ToDouble(row["经度"]);
                    cellInfo.Latitude = Convert.ToDouble(row["纬度"]);
                    if (row["方向角"] != null)
                    {
                        cellInfo.Direction = string.IsNullOrEmpty(row["方向角"].ToString()) ? 0 : Convert.ToInt32(row["方向角"]);
                    }
                    cellInfo.EARFCN = string.IsNullOrEmpty(row["ARFCN"].ToString()) ? 0 : Convert.ToInt32(row["ARFCN"]);
                    cellInfo.PCI = string.IsNullOrEmpty(row["PCI"].ToString()) ? 0 : Convert.ToInt32(row["PCI"]);

                    cellInfo.ECI = (cellInfo.ENodeBID * 256) + cellInfo.CellID;

                    cellParamInfoDic[cellInfo.Token] = cellInfo;
                }
                catch
                {
                    strbErrorInfo.AppendLine("第" + index + "行工参信息配置错误!   " + System.DateTime.Now.ToString());
                }
            }
            if (strbErrorInfo.Length > 0)
            {
                reportInfo(string.Format("{0}文件错误信息:{1}", fileName, strbErrorInfo.ToString()));
            }
        }

        private static string getStringValue(DataRow row, string colName)
        {
            string strValue = "";
            object objValue = row[colName];
            if (objValue != null)
            {
                return objValue.ToString().Trim();
            }
            return strValue;
        }

        private static void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        private static void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }

    public class BtsWorkParam_GZ
    {
        public BtsWorkParam_GZ()
        { 
        }
        public BtsWorkParam_GZ(CellWorkParam_GZ cellInfo)
        {
            if (cellInfo != null)
            {
                this.BtsName = cellInfo.BtsName;
                this.ENodeBID = cellInfo.ENodeBID;
                this.DistrictName = cellInfo.DistrictName;
                this.IsOutDoor = cellInfo.IsOutDoor;
            }
        }

        private string btsName = "";
        public string BtsName
        {
            get { return string.IsNullOrEmpty(btsName) ? "" : btsName.Trim(); }
            set { btsName = value; }
        }
        public int ENodeBID { get; set; }
        public string DistrictName { get; set; }
        public bool IsOutDoor { get; set; }

        public Dictionary<int, CellWorkParam_GZ> CellWorkParamDic { get; set; } = new Dictionary<int, CellWorkParam_GZ>();
        public void AddCellParamsInfo(CellWorkParam_GZ info)
        {
            CellWorkParamDic[info.CellID] = info;
        }
    }
    public class CellWorkParam_GZ
    {
        public string CellName { get; set; }
        public int CellID { get; set; }
        public string DistrictName { get; set; }

        private string btsName = "";
        public string BtsName
        {
            get { return string.IsNullOrEmpty(btsName) ? "" : btsName.Trim(); }
            set { btsName = value; }
        }
        public int ENodeBID { get; set; }
        public int Tac { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }

        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public int Direction { get; set; }
        public int ECI{ get; set; }
        public bool IsOutDoor
        {
            get
            {
                return CoverTypeDes == "室外";
            }
        }
        public string CoverTypeDes { get; set; }
        public string Token
        {
            get { return string.Format("{0}_{1}", Tac, ECI); }
        }
    }

    public class UpLoadWorkParams_GZ : DiySqlMultiNonQuery
    {
        readonly Dictionary<string, CellWorkParam_GZ> cellParamInfoDic;
        public UpLoadWorkParams_GZ(Dictionary<string, CellWorkParam_GZ> cellParamInfoDic)
            : base()
        {
            MainDB = true;
            this.cellParamInfoDic = cellParamInfoDic;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (CellWorkParam_GZ cellInfo in cellParamInfoDic.Values)
            {
                strb.Append(string.Format("delete from tb_btscheck_guizhou_cfg_cell where eci = '{0}';"
                   , cellInfo.ECI));
                strb.Append(string.Format(@"insert into [tb_btscheck_guizhou_cfg_cell]([districtName],[btsName]
,[cellName],[coverType],[enodebid],[tac],[cellid],[eci],[direction],[ilongitude],[ilatitude],earfcn,pci) values 
      ('{0}','{1}', '{2}', '{3}', {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11}, {12});"
                    , cellInfo.DistrictName, cellInfo.BtsName, cellInfo.CellName, cellInfo.CoverTypeDes
                    , cellInfo.ENodeBID, cellInfo.Tac, cellInfo.CellID, cellInfo.ECI, cellInfo.Direction
                    , cellInfo.Longitude * 10000000L, cellInfo.Latitude * 10000000L, cellInfo.EARFCN, cellInfo.PCI));
            }
            return strb.ToString();
        }
    }

    public class WorkParamsQuery_GZ : DIYSQLBase
    {
        readonly StationAcceptAutoSet_GZ funcSet;
        
        public Dictionary<string, Dictionary<int, BtsWorkParam_GZ>> workParamSumDic { get; set; } = new Dictionary<string, Dictionary<int, BtsWorkParam_GZ>>();
        public WorkParamsQuery_GZ(StationAcceptAutoSet_GZ funcSet)
        {
            MainDB = true;
            this.funcSet = funcSet;
        }
        protected override string getSqlTextString()
        {
            if (funcSet.IsAnaSpecifyBts)
            {
                return string.Format(@"select [districtName],[btsName],[cellName],[coverType],[enodebid],[tac],[eci]
,[cellid],[direction],[ilongitude],[ilatitude],earfcn,pci FROM [tb_btscheck_guizhou_cfg_cell]
where enodebid in ({0})", funcSet.SpecifyBtsEnodeBids);
            }
            else
            {
                return string.Format(@"select [districtName],[btsName],[cellName],[coverType],[enodebid],[tac],[eci]
,[cellid],[direction],[ilongitude],[ilatitude],earfcn,pci FROM [tb_btscheck_guizhou_cfg_cell]
where updateTime between '{0}' and '{1}'", funcSet.WorkParamTime_Begin.ToString(), funcSet.WorkParamTime_End.ToString());
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[13];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_Int;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            workParamSumDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellWorkParam_GZ cellInfo = new CellWorkParam_GZ();
                    cellInfo.DistrictName = package.Content.GetParamString();
                    cellInfo.BtsName = package.Content.GetParamString();
                    cellInfo.CellName = package.Content.GetParamString();
                    cellInfo.CoverTypeDes = package.Content.GetParamString();
                    cellInfo.ENodeBID = package.Content.GetParamInt();
                    cellInfo.Tac = package.Content.GetParamInt();
                    cellInfo.ECI = package.Content.GetParamInt();
                    cellInfo.CellID = package.Content.GetParamInt();
                    cellInfo.Direction = package.Content.GetParamInt();

                    int iLongitude = package.Content.GetParamInt();
                    int iLatitude = package.Content.GetParamInt();
                    cellInfo.Longitude = (double)iLongitude / 10000000;
                    cellInfo.Latitude = (double)iLatitude / 10000000;

                    cellInfo.EARFCN = package.Content.GetParamInt();
                    cellInfo.PCI = package.Content.GetParamInt();

                    Dictionary<int, BtsWorkParam_GZ> btsInfoIDic;
                    if (!workParamSumDic.TryGetValue(cellInfo.DistrictName, out btsInfoIDic))
                    {
                        btsInfoIDic = new Dictionary<int, BtsWorkParam_GZ>();
                        workParamSumDic.Add(cellInfo.DistrictName, btsInfoIDic);
                    }
                    BtsWorkParam_GZ btsInfo;
                    if (!btsInfoIDic.TryGetValue(cellInfo.ENodeBID, out btsInfo))
                    {
                        btsInfo = new BtsWorkParam_GZ(cellInfo);
                        btsInfoIDic.Add(cellInfo.ENodeBID, btsInfo);
                    }
                    btsInfo.AddCellParamsInfo(cellInfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
