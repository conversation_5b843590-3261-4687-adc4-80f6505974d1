﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakSINRRoadNRSettingDlg : BaseForm
    {
        public WeakSINRRoadNRSettingDlg(WeakSINRRoadCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(WeakSINRRoadCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numMaxValue.Value = (decimal)condition.MaxSINR;
            numMinValue.Value = (decimal)condition.MinRSRP;
            chkRSRP.Checked = condition.CheckRSRP;
            numMinDistance.Value = (decimal)condition.MinCoverRoadDistance;
            numMaxTPDistance.Value = (decimal)condition.Max2TPDistance;
            numWeakSINRPercent.Value = (decimal)condition.WeakSINRPercent;
            chkMinDistance.Checked = condition.CheckMinDistance;
            chkMinDuration.Checked = condition.CheckMinDuration;
            numMinDuration.Value = (decimal)condition.MinDuration;
        }

        /// <summary>
        /// 获得查询条件
        /// </summary>
        /// <returns></returns>
        public WeakSINRRoadCondition GetCondition()
        {
            WeakSINRRoadCondition condition = new WeakSINRRoadCondition();
            condition.MaxSINR = (float)numMaxValue.Value;
            condition.MinRSRP = (float)numMinValue.Value;
            condition.CheckRSRP = chkRSRP.Checked;
            condition.MinCoverRoadDistance = (double)numMinDistance.Value;
            condition.Max2TPDistance = (double)numMaxTPDistance.Value;
            condition.WeakSINRPercent = (double)numWeakSINRPercent.Value;
            condition.CheckMinDistance = chkMinDistance.Checked;
            condition.CheckMinDuration = chkMinDuration.Checked;
            condition.MinDuration = (double)numMinDuration.Value;
            return condition;
        }

        private void chkMinDuration_CheckedChanged(object sender, EventArgs e)
        {
            numMinDuration.Enabled = chkMinDuration.Checked;
            if (!chkMinDuration.Checked)
            {
                chkMinDistance.Checked = true;
            }
        }

        private void chkMinDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMinDistance.Enabled = chkMinDistance.Checked;
            if (!chkMinDistance.Checked)
            {
                chkMinDuration.Checked = true;
            }
        }
    }
}
