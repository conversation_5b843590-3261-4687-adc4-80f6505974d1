﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellWrongDirQuery_LTE : ZTDIYCellWrongDirQueryBase
   {
        public string themeName { get; set; } = "";

        private static ZTCellWrongDirQuery_LTE instance = null;
        public static ZTCellWrongDirQuery_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellWrongDirQuery_LTE();
                    }
                }
            }
            return instance;
        }
        protected ZTCellWrongDirQuery_LTE()
            : base(MainModel.GetInstance())
        {
            init();
            cellWrongCond.SetThreShold(-120, 100, 50, 50);
        }

        public ZTCellWrongDirQuery_LTE(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }


        protected void init()
        {
            themeName = "TD_LTE_RSRP";
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "天馈分析_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22012, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (cellDic.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的数据！");
                return;
            }
            CellWrongDirForm_TD frm =MainModel.CreateResultForm(typeof(CellWrongDirForm_TD)) as CellWrongDirForm_TD;
            frm.FillData(new List<TDCellWrongDir>(cellDic.Values), cellWrongCond.IsTwiceBatch);
            frm.Visible = true;
            frm.BringToFront();
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected override void doWithDTData(TestPoint tp)
        {
            float? rsrp = getRsrp(tp);
            if (rsrp != null && rsrp >= -141 && rsrp <= 25 && rsrp >= cellWrongCond.RxLevMin)
            {
                LTECell cell = tp.GetMainCell_LTE();//
                if (cell == null)
                    return;
                double dis = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (dis >= cellWrongCond.DistanceMin)
                {
                    TDCellWrongDir cellWrong;
                    if (!cellDic.TryGetValue(cell, out cellWrong))
                    {
                        cellWrong = new LTECellWrongDir(cell);
                        cellDic.Add(cell, cellWrong);
                    }
                    cellWrong.AddTestPoint(tp, cellWrongCond.AngleMin, cellWrongBatch);
                }
            }
        }

        protected override void getResultAfterQuery()
        {
            List<object> cells = new List<object>();
            foreach (ICell cell in cellDic.Keys)
            {
                if (cellDic[cell].WrongPercentage < cellWrongCond.WrongRateMin)
                {
                    cells.Add(cell);
                }
            }
            foreach (ICell cell in cells)
            {
                cellDic.Remove(cell);
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RSRPMin"] = cellWrongCond.RxLevMin;
                param["DisMin"] = cellWrongCond.DistanceMin;
                param["AngleMin"] = cellWrongCond.AngleMin;
                param["WrongPerMin"] = cellWrongCond.WrongRateMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RSRPMin"))
                {
                    cellWrongCond.RxLevMin = float.Parse(param["RSRPMin"].ToString());
                }
                if (param.ContainsKey("DisMin"))
                {
                    cellWrongCond.DistanceMin = double.Parse(param["DisMin"].ToString());
                }
                if (param.ContainsKey("AngleMin"))
                {
                    cellWrongCond.AngleMin = int.Parse(param["AngleMin"].ToString());
                }
                if (param.ContainsKey("WrongPerMin"))
                {
                    cellWrongCond.WrongRateMin = double.Parse(param["WrongPerMin"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (TDCellWrongDir block in cellDic.Values)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            cellDic.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int goodSampleCount = bgResult.GetImageValueInt();
                float wrongPercent = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("正常采样点数：");
                sb.Append(goodSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点占比：");
                sb.Append(wrongPercent);
                sb.Append("%");
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }
    public class CellWrongDirQueryByRegion_LTE : CellWrongDirQueryByFileBase
    {
        public string themeName { get; set; } = "";

        private static CellWrongDirQueryByRegion_LTE instance = null;
        public static CellWrongDirQueryByRegion_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new CellWrongDirQueryByRegion_LTE();
                    }
                }
            }
            return instance;
        }
        protected CellWrongDirQueryByRegion_LTE()
            : base(MainModel.GetInstance())
        {
            init();
            cellWrongCond.SetThreShold(-120, 100, 50, 50);
        }

        public CellWrongDirQueryByRegion_LTE(bool isVoLTE)
            : this()
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }
        protected void init()
        {
            themeName = "TD_LTE_RSRP";
            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "天馈分析_LTE"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22012, this.Name);
        }
        protected override void fireShowForm()
        {
            if (cellDic.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的数据！");
                return;
            }
            CellWrongDirForm_TD frm = MainModel.CreateResultForm(typeof(CellWrongDirForm_TD)) as CellWrongDirForm_TD;
            frm.FillData(new List<TDCellWrongDir>(cellDic.Values), cellWrongCond.IsTwiceBatch);
            frm.Visible = true;
            frm.BringToFront();
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected override void doWithDTData(TestPoint tp)
        {
            float? rsrp = getRsrp(tp);
            if (rsrp != null && rsrp >= -141 && rsrp <= 25 && rsrp >= cellWrongCond.RxLevMin)
            {
                LTECell cell = tp.GetMainCell_LTE();//
                if (cell == null)
                    return;
                double dis = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (dis >= cellWrongCond.DistanceMin)
                {
                    TDCellWrongDir cellWrong;
                    if (!cellDic.TryGetValue(cell, out cellWrong))
                    {
                        cellWrong = new LTECellWrongDir(cell);
                        cellDic.Add(cell, cellWrong);
                    }
                    cellWrong.AddTestPoint(tp, cellWrongCond.AngleMin, cellWrongBatch);
                }
            }
        }

        protected override void doSomethingAfterAnalyseFiles()
        {
            if (mainModel.IsBackground)
            {
                return;
            }

            List<object> cells = new List<object>();
            foreach (ICell cell in cellDic.Keys)
            {
                if (cellDic[cell].WrongPercentage < cellWrongCond.WrongRateMin)
                {
                    cells.Add(cell);
                }
            }
            foreach (ICell cell in cells)
            {
                cellDic.Remove(cell);
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RSRPMin"] = cellWrongCond.RxLevMin;
                param["DisMin"] = cellWrongCond.DistanceMin;
                param["AngleMin"] = cellWrongCond.AngleMin;
                param["WrongPerMin"] = cellWrongCond.WrongRateMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RSRPMin"))
                {
                    cellWrongCond.RxLevMin = float.Parse(param["RSRPMin"].ToString());
                }
                if (param.ContainsKey("DisMin"))
                {
                    cellWrongCond.DistanceMin = double.Parse(param["DisMin"].ToString());
                }
                if (param.ContainsKey("AngleMin"))
                {
                    cellWrongCond.AngleMin = int.Parse(param["AngleMin"].ToString());
                }
                if (param.ContainsKey("WrongPerMin"))
                {
                    cellWrongCond.WrongRateMin = double.Parse(param["WrongPerMin"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CellWrongProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (TDCellWrongDir block in cellDic.Values)
            {
                LTECellWrongDir lteItem = block as LTECellWrongDir;
                if (lteItem != null)
                {
                    BackgroundResult result = lteItem.ConvertToBackgroundResult(curAnaFileInfo);
                    result.SubFuncID = GetSubFuncID();
                    bgResultList.Add(result);
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            cellDic.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            BackgroundResultList.Sort(BackgroundResult.ComparerByFileIdAndISTimeAsc);
            Dictionary<string, BackgroundResult> bgResultDic = new Dictionary<string, BackgroundResult>();

            foreach (BackgroundResult curBgResult in BackgroundResultList)
            {
                int goodTestPointCount = curBgResult.GetImageValueInt();
                int direction = curBgResult.GetImageValueInt();
                int wrongDirMax = curBgResult.GetImageValueInt();
                int wrongDirMin = curBgResult.GetImageValueInt();

                BackgroundResult cellBgResult;
                Dictionary<string, int> imageDic;
                string cellKey = curBgResult.LAC + "_" + curBgResult.CI;
                if (bgResultDic.TryGetValue(cellKey, out cellBgResult))
                {
                    imageDic = cellBgResult.ImageResultObj as Dictionary<string, int>;
                    cellBgResult.SampleCount += curBgResult.SampleCount;
                    imageDic["角度正常采样点数"] += goodTestPointCount;
                    imageDic["最大异常角"] = Math.Max(imageDic["最大异常角"], wrongDirMax);
                    imageDic["最小异常角"] = Math.Min(imageDic["最小异常角"], wrongDirMin);
                }
                else
                {
                    imageDic = new Dictionary<string, int>();
                    imageDic["角度正常采样点数"] = goodTestPointCount;
                    imageDic["方向角"] = direction;
                    imageDic["最大异常角"] = wrongDirMax;
                    imageDic["最小异常角"] = wrongDirMin;

                    curBgResult.ImageResultObj = imageDic;

                    cellBgResult = curBgResult;
                    bgResultDic[cellKey] = cellBgResult;
                }
            }
            bgResultToNPOIRow(bgResultDic);
        }
        private void bgResultToNPOIRow(Dictionary<string, BackgroundResult> bgResultDic)
        {
            List<NPOIRow> lteNPOIRowList = new List<NPOIRow>();
            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic[""] = lteNPOIRowList;

            NPOIRow rowTitle = new NPOIRow();
            lteNPOIRowList.Add(rowTitle);
            rowTitle.AddCellValue("小区");
            rowTitle.AddCellValue("TAC");
            rowTitle.AddCellValue("ECI");
            rowTitle.AddCellValue("经度");
            rowTitle.AddCellValue("纬度");
            rowTitle.AddCellValue("方向角");
            rowTitle.AddCellValue("建议方向角");
            rowTitle.AddCellValue("角度异常采样点个数");
            rowTitle.AddCellValue("角度正常采样点个数");
            rowTitle.AddCellValue("异常百分比(%)");

            foreach (BackgroundResult bgResult in bgResultDic.Values)
            {
                Dictionary<string, int> imageDic = bgResult.ImageResultObj as Dictionary<string, int>;
                int dirSug = ResultCellWrong.CalcSuggestDir(imageDic["最大异常角"], imageDic["最小异常角"]);
                int goodTestPointCount = imageDic["角度正常采样点数"];
                int totalCount = goodTestPointCount + bgResult.SampleCount;
                double wrongPer;
                if (totalCount > 0)
                {
                    wrongPer = Math.Round(100.0 * bgResult.SampleCount / totalCount, 2);
                    if (wrongPer < cellWrongCond.WrongRateMin)
                    {
                        continue;
                    }
                }
                else
                {
                    continue;
                }
                NPOIRow row = new NPOIRow();
                row.AddCellValue(bgResult.CellName);
                row.AddCellValue(bgResult.LAC);
                row.AddCellValue(bgResult.CI);
                row.AddCellValue(bgResult.LongitudeMid);
                row.AddCellValue(bgResult.LatitudeMid);
                row.AddCellValue(imageDic["方向角"]);
                row.AddCellValue(dirSug);
                row.AddCellValue(bgResult.SampleCount);
                row.AddCellValue(goodTestPointCount);
                row.AddCellValue(wrongPer);

                lteNPOIRowList.Add(row);
                imageDic.Clear();
            }
        }
        #endregion
    }

    //覆盖不符**************************************************************
    public class ZTCellWrongDirQuery_LteFdd : ZTCellWrongDirQuery_LTE
    {
        private static ZTCellWrongDirQuery_LteFdd instance = null;
        public new static ZTCellWrongDirQuery_LteFdd GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellWrongDirQuery_LteFdd();
                    }
                }
            }
            return instance;
        }
        protected ZTCellWrongDirQuery_LteFdd()
            : base()
        {
            init();
        }

        protected new void init()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get { return "覆盖方向异常_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26006, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }

        protected override void doWithDTData(TestPoint tp)
        {
            float? rsrp = getRsrp(tp);
            if (rsrp != null && rsrp >= -141 && rsrp <= 25 && rsrp >= cellWrongCond.RxLevMin)
            {
                LTECell cell = tp.GetMainCell_LTE_FDD();//
                if (cell == null)
                    return;
                double dis = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (dis >= cellWrongCond.DistanceMin)
                {
                    if (cellDic.ContainsKey(cell))
                    {
                        cellDic[cell].AddTestPoint(tp, cellWrongCond.AngleMin, cellWrongBatch);
                    }
                    else
                    {
                        LTECellWrongDir cellWrong = new LTECellWrongDir(cell);
                        cellWrong.AddTestPoint(tp, cellWrongCond.AngleMin, cellWrongBatch);
                        cellDic.Add(cell, cellWrong);
                    }
                }
            }
        }
    }

    public class ZTCellWrongDirQuery_LteFdd_VOLTE : ZTCellWrongDirQuery_LteFdd
    {
        private static ZTCellWrongDirQuery_LteFdd_VOLTE instance = null;
        public static new ZTCellWrongDirQuery_LteFdd_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellWrongDirQuery_LteFdd_VOLTE();
                    }
                }
            }
            return instance;
        }
        public ZTCellWrongDirQuery_LteFdd_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD覆盖方向异常"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30008, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }
}
