﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTENCellLevelHigherForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControlCell = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.columnHeaderSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderCellType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderHostRSRP = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderNCellRSPR = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderDiff = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderLng = new DevExpress.XtraGrid.Columns.GridColumn();
            this.columnHeaderLat = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlCell
            // 
            this.gridControlCell.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCell.Location = new System.Drawing.Point(0, 0);
            this.gridControlCell.MainView = this.gridViewCell;
            this.gridControlCell.Name = "gridControlCell";
            this.gridControlCell.Size = new System.Drawing.Size(952, 526);
            this.gridControlCell.TabIndex = 0;
            this.gridControlCell.UseEmbeddedNavigator = true;
            this.gridControlCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCell});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridViewCell
            // 
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.columnHeaderSN,
            this.colFileName,
            this.columnHeaderName,
            this.columnHeaderCellType,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeaderHostRSRP,
            this.columnHeaderNCellRSPR,
            this.columnHeaderDiff,
            this.columnHeaderLng,
            this.columnHeaderLat});
            this.gridViewCell.GridControl = this.gridControlCell;
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsBehavior.Editable = false;
            this.gridViewCell.OptionsView.ShowGroupPanel = false;
            this.gridViewCell.VertScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Always;
            this.gridViewCell.DoubleClick += new System.EventHandler(this.gridViewCell_DoubleClick);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Caption = "序号";
            this.columnHeaderSN.FieldName = "SN";
            this.columnHeaderSN.Name = "columnHeaderSN";
            this.columnHeaderSN.Visible = true;
            this.columnHeaderSN.VisibleIndex = 0;
            // 
            // colFileName
            // 
            this.colFileName.Caption = "文件名";
            this.colFileName.FieldName = "FileName";
            this.colFileName.Name = "colFileName";
            this.colFileName.Visible = true;
            this.colFileName.VisibleIndex = 1;
            this.colFileName.Width = 200;
            // 
            // columnHeaderName
            // 
            this.columnHeaderName.Caption = "小区名称";
            this.columnHeaderName.FieldName = "CellName";
            this.columnHeaderName.Name = "columnHeaderName";
            this.columnHeaderName.Visible = true;
            this.columnHeaderName.VisibleIndex = 2;
            // 
            // columnHeaderCellType
            // 
            this.columnHeaderCellType.Caption = "小区类型";
            this.columnHeaderCellType.FieldName = "CellType";
            this.columnHeaderCellType.Name = "columnHeaderCellType";
            this.columnHeaderCellType.Visible = true;
            this.columnHeaderCellType.VisibleIndex = 3;
            // 
            // columnHeaderLAC
            // 
            this.columnHeaderLAC.Caption = "TAC";
            this.columnHeaderLAC.FieldName = "TAC";
            this.columnHeaderLAC.Name = "columnHeaderLAC";
            this.columnHeaderLAC.Visible = true;
            this.columnHeaderLAC.VisibleIndex = 4;
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Caption = "ECI";
            this.columnHeaderCI.FieldName = "ECI";
            this.columnHeaderCI.Name = "columnHeaderCI";
            this.columnHeaderCI.Visible = true;
            this.columnHeaderCI.VisibleIndex = 5;
            // 
            // columnHeaderHostRSRP
            // 
            this.columnHeaderHostRSRP.Caption = "主服电平";
            this.columnHeaderHostRSRP.FieldName = "RSRP";
            this.columnHeaderHostRSRP.Name = "columnHeaderHostRSRP";
            this.columnHeaderHostRSRP.Visible = true;
            this.columnHeaderHostRSRP.VisibleIndex = 6;
            // 
            // columnHeaderNCellRSPR
            // 
            this.columnHeaderNCellRSPR.Caption = "最强邻服电平";
            this.columnHeaderNCellRSPR.FieldName = "NRSRP";
            this.columnHeaderNCellRSPR.Name = "columnHeaderNCellRSPR";
            this.columnHeaderNCellRSPR.Visible = true;
            this.columnHeaderNCellRSPR.VisibleIndex = 7;
            // 
            // columnHeaderDiff
            // 
            this.columnHeaderDiff.Caption = "电平差";
            this.columnHeaderDiff.DisplayFormat.FormatString = "n2";
            this.columnHeaderDiff.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.columnHeaderDiff.FieldName = "RSRPDiff";
            this.columnHeaderDiff.Name = "columnHeaderDiff";
            this.columnHeaderDiff.Visible = true;
            this.columnHeaderDiff.VisibleIndex = 8;
            // 
            // columnHeaderLng
            // 
            this.columnHeaderLng.Caption = "经度";
            this.columnHeaderLng.FieldName = "Longitude";
            this.columnHeaderLng.Name = "columnHeaderLng";
            this.columnHeaderLng.Visible = true;
            this.columnHeaderLng.VisibleIndex = 9;
            // 
            // columnHeaderLat
            // 
            this.columnHeaderLat.Caption = "纬度";
            this.columnHeaderLat.FieldName = "Latitude";
            this.columnHeaderLat.Name = "columnHeaderLat";
            this.columnHeaderLat.Visible = true;
            this.columnHeaderLat.VisibleIndex = 10;
            // 
            // ZTLTENCellLevelHigherForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(952, 526);
            this.Controls.Add(this.gridControlCell);
            this.Name = "ZTLTENCellLevelHigherForm";
            this.Text = "小区集";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlCell;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCell;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderSN;
        private DevExpress.XtraGrid.Columns.GridColumn colFileName;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderName;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderCellType;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderLAC;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderCI;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderHostRSRP;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderNCellRSPR;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderDiff;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderLng;
        private DevExpress.XtraGrid.Columns.GridColumn columnHeaderLat;
    }
}