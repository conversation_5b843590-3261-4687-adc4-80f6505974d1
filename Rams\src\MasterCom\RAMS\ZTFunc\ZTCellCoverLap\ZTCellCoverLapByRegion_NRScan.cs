﻿using MasterCom.ES.Data;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_NRScan : ZTCellCoverLapByRegion
    {
        private static ZTCellCoverLapByRegion_NRScan intance = null;
        public new static ZTCellCoverLapByRegion_NRScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverLapByRegion_NRScan();
                    }
                }
            }
            return intance;
        }

        protected ZTCellCoverLapByRegion_NRScan()
            : base()
        {
            this.isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "过覆盖分析_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36003, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return NRTpHelper.InitNrScanParamSample(NRTpHelper.NrScanTpManager.RsrpThemeName);
        }

        ZTCellCoverLapSetDlg_NR fDlg = null;
        CellCoverLapCondition_NR curCondition = null;
        protected override bool getConditionBeforeQuery()
        {
            cellDic = new Dictionary<ICell, NrOverCoverCell>();
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (fDlg == null)
            {
                curCondition = new CellCoverLapCondition_NR();
                fDlg = new ZTCellCoverLapSetDlg_NR();
            }
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            fDlg.GetSettingFilterRet(curCondition);
            curFilterRxlev = curCondition.CurFilterRxlev;
            curMinSampleCount = curCondition.CurMinSampleCount;
            curMinPercent = curCondition.CurMinPercent;
            curMinDistance = curCondition.CurMinDistance;
            curMaxDistance = curCondition.CurMaxDistance;
            nearestCellCount = curCondition.NearestCellCount;
            disFactor = curCondition.DisFactor;
            return true;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
        }

        protected override void doWithDTData(TestPoint tp)
        {
            juedeOverCover(tp);
        }

        Dictionary<ICell, NrOverCoverCell> cellDic = null;

        /// <summary>
        /// 判断TestPoint是否有过覆盖小区
        /// </summary>
        /// <param name="tp"></param>
        /// <returns>true:过覆盖;false:无过覆盖</returns>
        protected bool juedeOverCover(TestPoint tp)
        {
            bool isValid = false;
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var index in groupDic.Values)
            {
                float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, index);
                if (rsrp != null && rsrp > curFilterRxlev)
                {
                    NRCell cell = tp.GetCell_NRScan(index);
                    if (cell == null)
                    {
                        continue;
                    }

                    double distance = tp.Distance2(cell.Longitude, cell.Latitude);
                    if (!cellDic.TryGetValue(cell, out var overCell))
                    {
                        double radiusOfCell = CfgDataProvider.CalculateRadius(cell, nearestCellCount);
                        if (radiusOfCell == 0)
                        {
                            continue;
                        }
                        string bts = CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                        overCell = new NrOverCoverCell(cell, radiusOfCell, disFactor, bts);
                        cellDic[cell] = overCell;
                    }
                    overCell.AddTestPoint(tp, index, (float)rsrp, distance);
                }
            }
            return isValid;
        }

        List<OverCoverCellBase> overCells = null;
        protected override void FilterCellCoverLap()
        {
            overCells = new List<OverCoverCellBase>();
            foreach (var cell in cellDic.Values)
            {
                cell.MakeSummary();
                if (cell.OverCell.Count > 0
                      && cell.OverCell.TotalCount >= curMinSampleCount
                      && cell.OverCell.Rate >= curMinPercent * 100
                      && cell.Distance.Avg >= curMinDistance
                      && cell.Distance.Avg <= curMaxDistance)
                {
                    overCells.Add(cell);
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (overCells == null || overCells.Count == 0)
            {
                MessageBox.Show("无符合条件的过覆盖小区！");
                return;
            }
            OverCoverListForm_NRScan form = MainModel.GetObjectFromBlackboard(typeof(OverCoverListForm_NRScan)) as OverCoverListForm_NRScan;
            if (form == null || form.IsDisposed)
            {
                form = new OverCoverListForm_NRScan();
            }
            form.FillData(overCells);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();
            cellDic = null;
            overCells = null;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }
        #endregion
    }
}
