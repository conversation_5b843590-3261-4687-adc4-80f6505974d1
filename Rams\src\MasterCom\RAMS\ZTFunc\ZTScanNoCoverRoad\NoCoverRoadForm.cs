using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Collections;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoCoverRoadForm : MinCloseForm
    {
        List<NoCoverRoad> noCoverRoadList;
        public NoCoverRoadForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            edtDistanceMin.Value = 100;
            edtDistanceMax.Value = 10000;
            edtTimeMin.Value = 0;
            edtTimeMax.Value = 10000;
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        public void FillData(List<NoCoverRoad> noCoverRoadList, int distanceMin)
        {
            this.noCoverRoadList = noCoverRoadList;
            edtDistanceMin.Value = distanceMin;
            if (edtDistanceMax.Value <= edtDistanceMin.Value)
            {
                edtDistanceMax.Value = 10000;
            }
            filterData();
        }

        private void filterData()
        {
            List<NoCoverRoad> showList = new List<NoCoverRoad>();
            MainModel.ClearDTData();
            foreach (NoCoverRoad item in noCoverRoadList)
            {
                if (item.Distance > (double)edtDistanceMin.Value && item.Distance < (double)edtDistanceMax.Value &&
                    item.Second > edtTimeMin.Value && item.Second < edtTimeMax.Value)
                {
                    foreach (TestPoint tp in item.testPointList)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    showList.Add(item);
                }
            }
            BindingSource source = new BindingSource();
            source.DataSource = showList;
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
            MainModel.FireDTDataChanged(this);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                NoCoverRoad info = gridView.GetRow(gridView.GetSelectedRows()[0]) as NoCoverRoad;
                MainModel.ClearDTData();
                foreach (TestPoint tp in info.testPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);

                string serialInfoName = string.Empty;

                if (info.testPointList[0] is ScanTestPoint_G)
                {
                    serialInfoName = "GSM_SCAN_RxLev";
                }
                else if (info.testPointList[0] is ScanTestPoint_TD)
                {
                    serialInfoName = "TDSCAN_PCCPCH_RSCP";
                }
                else if (info.testPointList[0] is ScanTestPoint_LTE)
                {
                    serialInfoName = "LTESCAN_TopN_PSS_RP";
                }

                foreach (MapSerialInfo serialInfo in mModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    if (serialInfo.Name.Equals(serialInfoName))
                    {
                        mModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                    }
                }
                mModel.DrawFlyLines = true;

                int index = info.testPointList.Count / 2;
                mapForm.GoToView(info.testPointList[index].Longitude, info.testPointList[index].Latitude, 2000);
                mapForm.GetDTLayer().Invalidate();
            }
        } 

        private void btnFilter_Click(object sender, EventArgs e)
        {
            filterData();
        }

        private void miDisplayAll_Click(object sender, EventArgs e)
        {
            mModel.DTDataManager.Clear();

            foreach (NoCoverRoad info in noCoverRoadList)
            {
                foreach (TestPoint tp in info.testPointList)
                {
                    mModel.DTDataManager.Add(tp);
                }
            }

            mModel.FireDTDataChanged(this);
        }
    }
}