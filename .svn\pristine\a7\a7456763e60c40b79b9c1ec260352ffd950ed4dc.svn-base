﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class FartherCoverInfo
    {
        public int SN { get; set; }

        public virtual string CellName { get { return TheCell.Name; } }
        public virtual int TAC { get { return TheCell.TAC; } }
        public virtual int ECI { get { return TheCell.ECI; } }

        public int SampleNum { get { return TestPointVec.Count; } }

        protected float rsrpMax;
        public float RsrpMax { get { return (float)Math.Round(rsrpMax, 2); } set { rsrpMax = value; } }

        protected float rsrpMin;
        public float RsrpMin { get { return (float)Math.Round(rsrpMin, 2); } set { rsrpMin = value; } }

        protected float rsrpMean;
        public float RsrpMean
        {
            get
            {
                if (SampleNum > 0)
                {
                    return (float)Math.Round(rsrpMean * 1.0 / SampleNum, 2);
                }
                return 0.0f;
            }
        }

        protected double distanceMin;
        public double DistanceMin { get { return Math.Round(distanceMin, 2); } set { distanceMin = value; } }

        protected double distanceMax;
        public double DistanceMax { get { return Math.Round(distanceMax, 2); } set { distanceMax = value; } }

        protected double distanceSum;
        public double DistanceMean
        {
            get
            {
                if (SampleNum > 0)
                {
                    return Math.Round(distanceSum / SampleNum, 2);
                }
                return 0;
            }
        }
        
        public List<double> DistanceVec { get; set; } = new List<double>();
        public LTECell TheCell { get; set; }
        public List<TestPoint> TestPointVec { get; set; } = new List<TestPoint>();

        public virtual double Longitude { get { return TheCell.Longitude; } }
        public virtual double Latitude { get { return TheCell.Latitude; } }

        public FartherCoverInfo(LTECell cell)
            : this()
        {
            this.TheCell = cell;
        }

        public FartherCoverInfo()
        {
            this.rsrpMean = 0;
            this.distanceSum = 0;
            this.rsrpMax = float.MinValue;
            this.rsrpMin = float.MaxValue;
            this.distanceMax = double.MinValue;
            this.distanceMin = double.MaxValue;
        }

        public void DealTestPoint(TestPoint tp, float rsrp, double distance)
        {
            TestPointVec.Add(tp);
            DistanceVec.Add(distance);
            rsrpMean += rsrp;
            rsrpMax = rsrp > rsrpMax ? rsrp : rsrpMax;
            rsrpMin = rsrp < rsrpMin ? rsrp : rsrpMin;
            distanceSum += distance;
            distanceMax = distance > distanceMax ? distance : distanceMax;
            distanceMin = distance < distanceMin ? distance : distanceMin;
            addOtherTPInfo(tp);
        }

        protected virtual void addOtherTPInfo(TestPoint testPoint)
        { 
        
        }

        public BackgroundResult ConvertToBackgroundResult(FileInfo file)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.LTE;
            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
                bgResult.ISTime = file.BeginTime;
                bgResult.IETime = file.EndTime;
            }
            if (TheCell != null)
            {
                bgResult.LAC = TheCell.TAC;
                bgResult.CI = TheCell.ECI;
                bgResult.BCCH = TheCell.EARFCN;
                bgResult.BSIC = TheCell.PCI;
                bgResult.LongitudeMid = TheCell.Longitude;
                bgResult.LatitudeMid = TheCell.Latitude;
            }
            if (TestPointVec.Count > 0)
            {
                bgResult.LongitudeStart = TestPointVec[0].Longitude;
                bgResult.LatitudeStart = TestPointVec[0].Latitude;
                bgResult.LongitudeEnd = TestPointVec[TestPointVec.Count - 1].Longitude;
                bgResult.LatitudeEnd = TestPointVec[TestPointVec.Count - 1].Latitude;
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(TestPointVec[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(TestPointVec[TestPointVec.Count - 1].DateTime) / 1000);
                bgResult.SampleCount = TestPointVec.Count;
            }
            bgResult.RxLevMean = RsrpMean;
            bgResult.RxLevMin = RsrpMin;
            bgResult.RxLevMax = RsrpMax;

            bgResult.AddImageValue(CellName);
            bgResult.AddImageValue((float)DistanceMax);
            bgResult.AddImageValue((float)DistanceMin);
            bgResult.AddImageValue((float)DistanceMean);

            return bgResult;
        }
    }
}
