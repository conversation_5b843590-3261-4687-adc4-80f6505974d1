﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsResultControlBase : UserControl
    {
        protected MainModel MainModel;
        protected MapForm mf = null;

        public NbIotMgrsResultControlBase()
        {
            InitializeComponent();
            this.MainModel = MainModel.GetInstance();
        }

        public virtual string Desc
        {
            get { return "结果标题"; }
        }

        public virtual void DrawOnLayer()
        {

        }

        public virtual void Clear()
        {

        }

        public virtual void LayerDataClear()
        {
        
        }

        protected void SetNormalMapScale()
        {
            MapOperation mop = MainModel.MainForm.GetMapForm().GetMapOperation();
            if (mop.Scale < 15000)
            {
                mop.Scale = 15000;
            }
        }
        
        protected void MiExportExcelAll_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            NbIotMgrsResultForm resultForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NbIotMgrsResultForm).FullName) as NbIotMgrsResultForm;
            List<NbIotMgrsResultControlBase> bindingControls = resultForm.BindingControls;
            foreach (NbIotMgrsResultControlBase ctrl in bindingControls)
            {
                ctrl.ExportAllExcel(dlg.SelectedPath);
            }
            MessageBox.Show("导出全部Excel完成!", "导出完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected virtual void ExportAllExcel(string savePath)
        {

        }

        protected void MiExportShpAll_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            NbIotMgrsResultForm resultForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NbIotMgrsResultForm).FullName) as NbIotMgrsResultForm;
            List<NbIotMgrsResultControlBase> bindingControls = resultForm.BindingControls;
            foreach (NbIotMgrsResultControlBase ctrl in bindingControls)
            {
                ctrl.ExportAllShp(dlg.SelectedPath);
            }
            MessageBox.Show("导出全部图层完成!", "导出完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected virtual void ExportAllShp(string savePath)
        {

        }
    }
}
