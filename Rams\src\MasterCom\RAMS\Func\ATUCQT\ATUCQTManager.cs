﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public class ATUCQTManager
    {
        private readonly Dictionary<long, string> atuCQTTimeFPNameDic = new Dictionary<long, string>();
        private readonly Dictionary<string, ATUCQTImageInfo> atuCQTImagesDic = new Dictionary<string, ATUCQTImageInfo>();
        private readonly List<long> times = new List<long>();
        private readonly MainModel mainModel = MainModel.GetInstance();
        private bool bInited = false;
        private static ATUCQTManager manager = null;
        public static ATUCQTManager GetInstance()
        {
            if (manager == null)
            {
                manager = new ATUCQTManager();
            }
            return manager;
        }

        public void Init()
        {
            if (!bInited)
            {
                mainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
                mainModel.SelectedEventsChanged += selectedEventsChanged;
                mainModel.SelectedMessageChanged += selectedMessageChanged;
                bInited = true;
            }
            atuCQTTimeFPNameDic.Clear();
            atuCQTImagesDic.Clear();
            times.Clear();
        }

        public void AddFPInfo(ATUCQTFPInfo fpInfo)
        {
            long time = fpInfo.time * 1000L + fpInfo.wTimeMS;
            times.Add(time);
            times.Sort();
            atuCQTTimeFPNameDic[time] = fpInfo.fpName;
        }

        public void AddFPInfo(List<ATUCQTFPInfo> fpInfos)
        {
            foreach (ATUCQTFPInfo fpInfo in fpInfos)
            {
                AddFPInfo(fpInfo);
            }
        }

        public void AddImageInfo(ATUCQTImageInfo imageInfo)
        {
            atuCQTImagesDic[imageInfo.name] = imageInfo;
        }

        public void SetFirstImage()
        {
            if (times.Count > 0)
            {
                bool isContian = atuCQTTimeFPNameDic.ContainsKey(times[0]) && atuCQTImagesDic.ContainsKey(atuCQTTimeFPNameDic[times[0]]);
                if (isContian)
                {
                    setShowImage(atuCQTImagesDic[atuCQTTimeFPNameDic[times[0]]]);
                }
            }
        }

        public void SetATUCQTImage(long time)
        {
            ATUCQTImageInfo imageInfo = getImageInfo(time);
            setShowImage(imageInfo);
        }

        private void setShowImage(ATUCQTImageInfo imageInfo)
        {
            if (imageInfo == null)
            {
                return;
            }
            mainModel.CQTPlanImg = imageInfo.image;
            mainModel.CQTPlanImgLTPos = imageInfo.ltPos;
            mainModel.CQTPlanImgBRPos = imageInfo.brPos;
            mainModel.CQTPlanIMGScale = imageInfo.scale;
        }

        private ATUCQTImageInfo getImageInfo(long time)
        {
            for (int i = 0; i < times.Count; i++)
            {
                long curTime = times[0];
                if (time < curTime)
                {
                    string fpName = atuCQTTimeFPNameDic[curTime];
                    if (atuCQTImagesDic.ContainsKey(fpName))
                    {
                        return atuCQTImagesDic[fpName];
                    }
                    else
                    {
                        return null;
                    }
                }
            }
            return null;
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            if (times.Count == 0)
            {
                return;
            }
            if (mainModel.SelectedTestPoints.Count > 0)
            {
                TestPoint tp = mainModel.SelectedTestPoints[0];
                long time = tp.Time * 1000L + tp.Millisecond;
                SetATUCQTImage(time);
            }
        }

        private void selectedEventsChanged(object sender, EventArgs e)
        {
            if (times.Count == 0)
            {
                return;
            }
            if (mainModel.SelectedEvents.Count > 0)
            {
                DTData displayDTData = mainModel.SelectedEvents[0];
                DTFileDataManager fdm = mainModel.DTDataManager.FileDataManagers[0];
                if (fdm.FileID != displayDTData.FileID)
                {
                    int time = displayDTData.Time;
                    displayDTData = fdm.DTDatas.Find(delegate(DTData d) { return (d.Time == time) && !(d is Model.Message); });
                }

                DTData data = null;
                for (int idx = fdm.DTDatas.IndexOf(displayDTData); idx >= 0; idx--)
                {
                    data = fdm.DTDatas[idx];
                    if (data is TestPoint)
                    {
                        long time = data.Time * 1000L + data.Millisecond;
                        SetATUCQTImage(time);
                    }
                }
            }
        }

        private void selectedMessageChanged(object sender, EventArgs e)
        {
            if (times.Count == 0)
            {
                return;
            }
            if (mainModel.SelectedMessage != null)
            {
                if (mainModel.SelectedTestPoints.Count == 0)
                {
                    mainModel.SelectTestPointByMessage();
                    if (mainModel.SelectedTestPoints.Count == 0) return;
                }
                DTData displayDTData = mainModel.SelectedTestPoints[0];
                long time = displayDTData.Time * 1000L + displayDTData.Millisecond;
                SetATUCQTImage(time);
            }
        }
    }

    public class ATUCQTFPInfo
    {
        public ATUCQTFPInfo(int time, int wTimeMS, string fpName)
        {
            this.time = time;
            this.wTimeMS = wTimeMS;
            this.fpName = fpName;
        }

        public int time { get; set; }
        public int wTimeMS { get; set; }
        public string fpName { get; set; }
    }

    public class ATUCQTImageInfo
    {
        public ATUCQTImageInfo(string name, Image image, DbPoint ltPos, DbPoint brPos, double scale)
        {
            this.name = name;
            this.image = image;
            this.ltPos = ltPos;
            this.brPos = brPos;
            this.scale = scale;
        }

        public string name { get; set; }
        public Image image { get; set; }
        public DbPoint ltPos { get; set; }
        public DbPoint brPos { get; set; }
        public double scale { get; set; }
    }
}
