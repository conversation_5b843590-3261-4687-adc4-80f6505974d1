﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLastWeakMosAna : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> handOverEventIds = initHandOverEventIds();

        private static List<int> initHandOverEventIds()
        {
            List<int> evtList = NREventHelper.HandoverHelper.GetHandoverSuccessEvt(false);
            evtList.Add((int)NREventManager.VoLTE_eSRVCC_HandOver_Success);
            return evtList;
        }

        protected List<int> weakMosEventIds;

        private readonly NRLastWeakMosCondition curCondtion = new NRLastWeakMosCondition();
        private List<NRLastWeakMosResult> listResult = null;
        private NRLastWeakMosAnaForm resultForm = null;

        public NRLastWeakMosAna()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);
            Columns.Add("NR_PESQScore");
            Columns.Add("NR_PESQLQ");
            Columns.Add("NR_PESQMos");
            Columns.Add("NR_POLQA_Score_SWB");
            Columns.Add("NR_VONR_RTP_Packets_Num");
            Columns.Add("NR_VONR_RTP_Packets_Lost_Num");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRVoice);
        }

        public override string Name
        {
            get { return "持续弱MOS事件"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35037, this.Name);
        }

        protected override bool getCondition()
        {
            NRLastWeakMosAnaDlg setForm = new NRLastWeakMosAnaDlg();
            setForm.SetValue(curCondtion);
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            setForm.GetResult(curCondtion);
            weakMosEventIds = curCondtion.GetEvtList();

            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            this.listResult = new List<NRLastWeakMosResult>();
        }

        protected virtual string eventName
        {
            get { return "NR_弱MOS"; }
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event ev in file.Events)
                {
                    dealEvt(file, ev);
                }
            }
        }

        private void dealEvt(DTFileDataManager file, Event ev)
        {
            if (weakMosEventIds.Contains(ev.ID))
            {
                int testpointNum = (int)(long)ev["Value3"];
                addRes(file, ev, testpointNum);
            }
        }

        private void addRes(DTFileDataManager file, Event ev, int testpointNum)
        {
            if (testpointNum >= curCondtion.TpNum)
            {
                NRLastWeakMosInfo info = new NRLastWeakMosInfo();

                DateTime bT = JavaDate.GetDateTimeFromMilliseconds((long)ev["Value1"]);
                DateTime eT = JavaDate.GetDateTimeFromMilliseconds((long)ev["Value2"]);
                dealTP(bT, eT, file, info);
                dealHandoverEvt(bT, eT, file, info);
                NRLastWeakMosResult re = new NRLastWeakMosResult();
                re.SN = listResult.Count + 1;
                re.EventName = eventName;
                re.FileName = file.FileName;
                re.DateTime = ev.DateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                re.TpNum = testpointNum;
                re.SetResData(info);
                re.Ev = ev;
                re.Longitude = ev.Longitude;
                re.Latitude = ev.Latitude;
                listResult.Add(re);
            }
        }

        private void dealTP(DateTime bT, DateTime eT, DTFileDataManager file, NRLastWeakMosInfo mv)
        {
            int fstPackage = -1;
            int lastPackage = -1;
            int fstLossPackage = -1;
            int lastLossPackage = -1;

            double distance = 0;
            TestPoint lastTP = null;
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp.DateTime < bT)
                {
                    continue;
                }
                else if (tp.DateTime > eT)
                {
                    break;
                }
                getValue(mv, tp);

                if (lastTP != null)
                {
                    distance += tp.Distance2(lastTP);
                }
                lastTP = tp;

                getPackage(ref fstPackage, ref lastPackage, tp, "NR_VONR_RTP_Packets_Num");
                getPackage(ref fstLossPackage, ref lastLossPackage, tp, "NR_VONR_RTP_Packets_Lost_Num");
            }

            mv.Distance = distance;
            mv.LossRateInfo.Count = lastPackage - fstPackage;
            mv.LossRateInfo.Sum = lastLossPackage - fstLossPackage;
        }

        private void getPackage(ref int fstPackage, ref int lastPackage, TestPoint tp, string Name)
        {
            if (fstPackage == -1)
            {
                int? package = (int?)tp[Name];
                if (package != null)
                {
                    fstPackage = (int)package;
                    lastPackage = fstPackage;
                }
            }
            else
            {
                int? package = (int?)tp[Name];
                if (package != null)
                {
                    lastPackage = (int)package;
                }
            }
        }

        private void dealHandoverEvt(DateTime bT, DateTime eT, DTFileDataManager file, NRLastWeakMosInfo mv)
        {
            foreach (Event eve in file.Events)
            {
                if (eve.DateTime < bT)
                {
                    continue;
                }
                if (eve.DateTime > eT)
                {
                    break;
                }
                if (handOverEventIds.Contains(eve.ID))
                {
                    mv.HandOverInfo.Count++;
                }
            }
        }

        protected virtual void getValue(NRLastWeakMosInfo mv, TestPoint tp)
        {
            setEarfcnPci(mv, tp);

            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            if (rsrp != null)
            {
                mv.NRInfo.RsrpInfo.Sum += (float)rsrp;
                mv.NRInfo.RsrpInfo.Count++;
            }
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            if (sinr != null)
            {
                mv.NRInfo.SinrInfo.Sum += (float)sinr;
                mv.NRInfo.SinrInfo.Count++;
            }

            float? rsrpLte = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp, true);
            if (rsrpLte != null)
            {
                mv.LTEInfo.RsrpInfo.Sum += (float)rsrpLte;
                mv.LTEInfo.RsrpInfo.Count++;
            }
            float? sinrLte = NRTpHelper.NrLteTpManager.GetSCellSinr(tp, true);
            if (sinrLte != null)
            {
                mv.LTEInfo.SinrInfo.Sum += (float)sinrLte;
                mv.LTEInfo.SinrInfo.Count++;
            }

            float? mos = NRTpHelper.NrTpManager.GetMosInfo(tp);
            if (mos != null)
            {
                mv.MOSInfo.Sum += (float)mos;
                mv.MOSInfo.Count++;
            }
        }

        private static void setEarfcnPci(NRLastWeakMosInfo mv, TestPoint tp)
        {
            int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            if (mv.NRInfo.Earfcn == 0 && earfcn != null)
            {
                mv.NRInfo.Earfcn = (int)earfcn;
            }
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            if (mv.NRInfo.PCI == 0 && pci != null)
            {
                mv.NRInfo.PCI = (int)pci;
            }
            int? earfcnLte = (int?)NRTpHelper.NrLteTpManager.GetEARFCN(tp);
            if (mv.LTEInfo.Earfcn == 0 && earfcnLte != null)
            {
                mv.LTEInfo.Earfcn = (int)earfcnLte;
            }
            int? pciLte = (int?)NRTpHelper.NrLteTpManager.GetPCI(tp);
            if (mv.LTEInfo.PCI == 0 && pciLte != null)
            {
                mv.LTEInfo.PCI = (int)pciLte;
            }
        }

        protected override void fireShowForm()
        {
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new NRLastWeakMosAnaForm();
            }
            resultForm.FillData(listResult);
            resultForm.Owner = MainModel.MainForm;
            resultForm.Visible = false;
            resultForm.Show();
            resultForm.Focus();
        }
    }
}
