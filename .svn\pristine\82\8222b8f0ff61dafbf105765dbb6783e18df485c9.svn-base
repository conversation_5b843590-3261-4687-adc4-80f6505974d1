﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CDMAWeakCovRoadSetForm : BaseForm
    {
        public CDMAWeakCovRoadSetForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void GetSetCondition(out CDMAWeakCovRoadCondition cond)
        {
            cond = new CDMAWeakCovRoadCondition();
            cond.RxPower = (int)this.numRxPower.Value;
            cond.TxPower = (int)this.numTxPower.Value;
            cond.C2I = (int)this.numEclo.Value;
            cond.RoadDistance = (int)this.numRoadDis.Value;
            cond.SampleDistance = (int)this.numSampleDis.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
