﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTCellCoverLapByRegion_NB : ZTCellCoverLapByRegion_LTE
    {
        public ZTCellCoverLapByRegion_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }


        public override string Name
        {
            get
            {
                return "过覆盖分析_NB";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34002, this.Name);
        }
    }
}
