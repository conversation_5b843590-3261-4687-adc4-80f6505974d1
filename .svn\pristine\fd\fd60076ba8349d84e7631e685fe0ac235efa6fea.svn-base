﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyNrCellSetQueryBase : DIYAnalyseFilesOneByOneByRegion
    {
        public ZTDiyNrCellSetQueryBase()
            : base(MainModel.GetInstance())
        {
            init();
        }

        protected void init()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_SS_RSRQ");

            addOtherInitData();
        }

        protected virtual void addOtherInitData()
        {
            IncludeEvent = false;

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "NR小区集"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithDTData(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        protected virtual void doWithDTData(TestPoint tp)
        {
            throw (new Exception("需要处理路测数据"));
        }

        protected float? getValidData(TestPoint tp, string name, float min, float max)
        {
            float? data = (float?)tp[name];
            if (data != null && data >= min && data <= max)
            {
                return data;
            }
            return null;
        }

        protected virtual void showResultForm()
        {
            throw (new Exception("需要实现界面显示"));
        }

        protected float getMaxValue(float infoValue, float tpValue)
        {
            if (infoValue < tpValue)
            {
                infoValue = tpValue;
            }
            return infoValue;
        }

        protected float getMinValue(float infoValue, float tpValue)
        {
            if (infoValue > tpValue)
            {
                infoValue = tpValue;
            }
            return infoValue;
        }
    }

    public class ZTDiyNrCellSetQueryByRegion : ZTDiyNrCellSetQueryBase
    {
        List<NrCellInfo> resInfoLst;
        Dictionary<NRCell, NrCellInfo> nrCellInfoDic;
        Dictionary<string, NrCellInfo> notFindCellInfoDic;

        public override string Name
        {
            get { return "NR小区集(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35015, this.Name);
        }

        protected override void getReadyBeforeQuery()
        {
            nrCellInfoDic = new Dictionary<NRCell, NrCellInfo>();
            notFindCellInfoDic = new Dictionary<string, NrCellInfo>();
            resInfoLst = new List<NrCellInfo>();
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            if (!isValidTestPoint(tp))
            {
                return;
            }

            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            float? rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tp, true);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            if (arfcn == null || pci == null || rsrp == null || rsrq == null || sinr == null)
            {
                return;
            }

            NRCell cell = tp.GetMainCell_NR();
            if (cell == null)
            {
                addNotFindCellInfo(tp, (float)rsrp, (float)rsrq, (float)sinr);
                return;
            }

            addCellInfo(tp, rsrp, rsrq, sinr, cell);
        }

        private void addCellInfo(TestPoint tp, float? rsrp, float? rsrq, float? sinr, NRCell cell)
        {
            NrCellInfo lcInfo;
            if (!nrCellInfoDic.TryGetValue(cell, out lcInfo))
            {
                lcInfo = new NrCellInfo();
                lcInfo.Cell = cell;
                lcInfo.CellName = cell.Name;
                lcInfo.CellCode = cell.Code;
                lcInfo.Tac = cell.TAC;
                lcInfo.Nci = cell.NCI;
                lcInfo.Arfcn = cell.SSBARFCN;
                lcInfo.Pci = cell.PCI;
                lcInfo.CellID = cell.CellID;
                lcInfo.IndoorOrOutdoor = cell.Type == NRBTSType.Indoor ? "室内" : "室外";
                lcInfo.Longitude = cell.Longitude;
                lcInfo.Latitude = cell.Latitude;
                lcInfo.MinRsrp = lcInfo.MaxRsrp = (float)rsrp;
                lcInfo.MinRsrq = lcInfo.MaxRsrq = (float)rsrq;
                lcInfo.MinDistance = lcInfo.MaxDistance = Math.Round(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude), 2);
                lcInfo.MinSinr = lcInfo.MaxSinr = (float)sinr;
                //lcInfo.Grid = getGrid(cell.Longitude, cell.Latitude);

                nrCellInfoDic.Add(cell, lcInfo);
            }
            lcInfo.AddCellInfo(cell, tp, (float)rsrp, (float)rsrq, (float)sinr);
        }

        private void addNotFindCellInfo(TestPoint tp, float rsrp, float rsrq, float sinr)
        {
            int? iTAC = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
            long? iNci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            //string strTAC_NCI = "" + iTAC + "_" + iNci;
            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            string strArfcn_Pci = "" + arfcn + "_" + pci;

            NrCellInfo lcInfo;
            if (!notFindCellInfoDic.TryGetValue(strArfcn_Pci, out lcInfo))
            {
                lcInfo = new NrCellInfo();
                lcInfo.CellName = strArfcn_Pci;
                lcInfo.Tac = iTAC;
                lcInfo.Nci = iNci;
                lcInfo.Arfcn = (int)arfcn;
                lcInfo.Pci = (int)pci;
                lcInfo.MinRsrp = lcInfo.MaxRsrp = rsrp;
                lcInfo.MinRsrq = lcInfo.MaxRsrq = rsrq;
                lcInfo.MinSinr = lcInfo.MaxSinr = sinr;

                notFindCellInfoDic.Add(strArfcn_Pci, lcInfo);
            }
            lcInfo.AddNoCellInfo(rsrp, rsrq, sinr);
        }

        /// <summary>
        /// 定位所在网格
        /// </summary>
        /// <param name="iType"></param>
        /// <returns>返回区域名称</returns>
        //private string getGrid(double longitude, double latitude)
        //{
        //    foreach (string strKey in regionMopDic.Keys)
        //    {
        //        if (regionMopDic[strKey].CheckPointInRegion(longitude, latitude))
        //        {
        //            return strKey;
        //        }
        //    }
        //    return null;
        //}

        protected override void getResultsAfterQuery()
        {
            int sn = 1;
            foreach (NrCellInfo info in nrCellInfoDic.Values)
            {
                info.SN = sn++;
                info.Caculate();
                resInfoLst.Add(info);
            }
            foreach (NrCellInfo info in notFindCellInfoDic.Values)
            {
                info.SN = sn++;
                info.Caculate();
                resInfoLst.Add(info);
            }
        }

        protected override void fireShowForm()
        {
            NRCellSetForm nrCellSetForm = MainModel.CreateResultForm(typeof(NRCellSetForm)) as NRCellSetForm;
            nrCellSetForm.FillData(resInfoLst);
            nrCellSetForm.Visible = true;
            nrCellSetForm.BringToFront();
        }
    }

    public class ZTDiyNrCellSetQueryByFile : ZTDiyNrCellSetQueryBase
    {
        List<NrCellInfo> resInfoLst;
        protected Dictionary<string, Dictionary<NRCell, NrCellInfo>> fileNrCellInfoDic;
        protected Dictionary<string, Dictionary<string, NrCellInfo>> fileNotFindCellInfoDic;
        protected List<string> fileNames = new List<string>();
        protected Dictionary<string, long> fileSampleCount = new Dictionary<string, long>();

        public override string Name
        {
            get { return "NR小区集（按文件）"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35014, this.Name);
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            fileNrCellInfoDic = new Dictionary<string, Dictionary<NRCell, NrCellInfo>>();
            fileNotFindCellInfoDic = new Dictionary<string, Dictionary<string, NrCellInfo>>();
            resInfoLst = new List<NrCellInfo>();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            if (!isValidTestPoint(tp))
            {
                return;
            }

            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            float? rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tp, true);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            if (arfcn == null || pci == null || rsrp == null || rsrq == null || sinr == null)
            {
                return;
            }

            string fileName = tp.FileName;
            if (!fileNames.Contains(fileName))
            {
                fileNames.Add(fileName);
            }

            NRCell cell = tp.GetMainCell_NR();
            if (cell == null)
            {
                addNotFindCellInfo(tp, (float)rsrp, (float)rsrq, (float)sinr);
                return;
            }

            addCellInfo(fileName, rsrp, rsrq, sinr, cell);
        }

        private void addCellInfo(string fileName, float? rsrp, float? rsrq, float? sinr, NRCell cell)
        {
            Dictionary<NRCell, NrCellInfo> nrCellInfoDic = null;
            if (!fileNrCellInfoDic.TryGetValue(fileName, out nrCellInfoDic))
            {
                nrCellInfoDic = new Dictionary<NRCell, NrCellInfo>();
                fileNrCellInfoDic.Add(fileName, nrCellInfoDic);
            }

            NrCellInfo lcInfo;
            if (!nrCellInfoDic.TryGetValue(cell, out lcInfo))
            {
                lcInfo = new NrCellInfo();
                lcInfo.Cell = cell;
                lcInfo.FileName = fileName;
                lcInfo.CellName = cell.Name;
                lcInfo.CellCode = cell.Code;
                lcInfo.Tac = cell.TAC;
                lcInfo.Nci = cell.NCI;
                lcInfo.Arfcn = cell.SSBARFCN;
                lcInfo.Pci = cell.PCI;
                lcInfo.MinRsrp = lcInfo.MaxRsrp = (float)rsrp;
                lcInfo.MinRsrq = lcInfo.MaxRsrq = (float)rsrq;
                lcInfo.MinSinr = lcInfo.MaxSinr = (float)sinr;

                nrCellInfoDic.Add(cell, lcInfo);
            }
            
            lcInfo.AddCellInfo(fileName, (float)rsrp, (float)rsrq, (float)sinr);
            //lcInfo.SampleCount++;
            if (fileSampleCount.ContainsKey(fileName))
            {
                fileSampleCount[fileName]++;
            }
            else
            {
                fileSampleCount[fileName] = 1;
            }
        }

        private void addNotFindCellInfo(TestPoint tp, float rsrp, float rsrq, float sinr)
        {
            int? iTAC = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
            long? iNci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            //string strTAC_NCI = "" + iTAC + "_" + iNci;
            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            string strArfcn_Pci = arfcn + "_" + pci;
            string fileName = tp.FileName;//文件名

            Dictionary<string, NrCellInfo> notFindCellInfoDic = null;
            if (!fileNotFindCellInfoDic.TryGetValue(fileName, out notFindCellInfoDic))
            {
                notFindCellInfoDic = new Dictionary<string, NrCellInfo>();
                fileNotFindCellInfoDic.Add(fileName, notFindCellInfoDic);
            }

            NrCellInfo lcInfo;
            if (!notFindCellInfoDic.TryGetValue(strArfcn_Pci, out lcInfo))
            {
                lcInfo = new NrCellInfo();
                lcInfo.FileName = fileName;
                lcInfo.CellName = strArfcn_Pci;
                lcInfo.Tac = iTAC;
                lcInfo.Nci = iNci;
                lcInfo.Arfcn = (int)arfcn;
                lcInfo.Pci = (int)pci;
                lcInfo.MinRsrp = lcInfo.MaxRsrp = rsrp;
                lcInfo.MinRsrq = lcInfo.MaxRsrq = rsrq;
                lcInfo.MinSinr = lcInfo.MaxSinr = sinr;

                notFindCellInfoDic.Add(strArfcn_Pci, lcInfo);
            }
            lcInfo.AddNoCellInfo(rsrp, rsrq, sinr);

            if (fileSampleCount.ContainsKey(fileName))
            {
                fileSampleCount[fileName]++;
            }
            else
            {
                fileSampleCount[fileName] = 1;
            }
        }

        protected override void getResultsAfterQuery()
        {
            statCell();
            fileNames.Clear();
            fileSampleCount.Clear();

            int sn = 1;
            foreach (Dictionary<NRCell, NrCellInfo> dic in fileNrCellInfoDic.Values)
            {
                foreach (NrCellInfo info in dic.Values)
                {
                    info.SN = sn++;
                    info.Caculate();
                    resInfoLst.Add(info);
                }
            }
            foreach (Dictionary<string, NrCellInfo> dic in fileNotFindCellInfoDic.Values)
            {
                foreach (NrCellInfo info in dic.Values)
                {
                    info.SN = sn++;
                    //info.CellName = "";//匹配不上的小区名称显示为空
                    info.Caculate();
                    resInfoLst.Add(info);
                }
            }
        }

        private void statCell()
        {
            foreach (string fileName in fileNames)
            {
                long totalCount = 0;
                fileSampleCount.TryGetValue(fileName, out totalCount);

                if (fileNrCellInfoDic.Count > 0 && fileNrCellInfoDic.ContainsKey(fileName))
                {
                    foreach (NrCellInfo lcInfo in fileNrCellInfoDic[fileName].Values)
                    {
                        setCellInfo(fileName, totalCount, lcInfo);
                    }
                }

                if (fileNotFindCellInfoDic.Count > 0 && fileNotFindCellInfoDic.ContainsKey(fileName))
                {
                    foreach (NrCellInfo lcInfo in fileNotFindCellInfoDic[fileName].Values)
                    {
                        setCellInfo(fileName, totalCount, lcInfo);
                    }
                }
            }
        }

        private void setCellInfo(string fileName, long totalCount, NrCellInfo lcInfo)
        {
            if (lcInfo.FileName == fileName && totalCount != 0)
            {
                lcInfo.SampleTotalCount = totalCount;
                lcInfo.SampleCountRatio = float.Parse((lcInfo.SampleCount * 100.0 / totalCount).ToString("0.00"));
            }
        }

        protected override void fireShowForm()
        {
            NRCellSetByFileForm NRCellSetForm = MainModel.CreateResultForm(typeof(NRCellSetByFileForm)) as NRCellSetByFileForm;
            NRCellSetForm.FillData(resInfoLst);
            NRCellSetForm.Visible = true;
            NRCellSetForm.BringToFront();
        }
    }
}
