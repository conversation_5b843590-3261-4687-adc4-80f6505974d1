﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS
{
    public partial class CQTAdressPropertyAssessForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTNetType NetTypeSelected { get; set; }
        public CQTCellVillage CellVillageSelected { get; set; }
        public CQTCompareTime CompareTimeSelected { get; set; }
        public CQTAnalysisModel AnalysisModelSelected { get; set; }
        public QueryCondition condtion { get; set; }
        public CQTAdressPropertyAssessForm(QueryCondition condtion)
        {
            InitializeComponent();
            this.condtion = condtion;
            dTimePickerStime.Value = condtion.Periods[0].BeginTime;
            compareDTimePickerEtime.Value = condtion.Periods[0].EndTime.AddMinutes(-1);
            TimeSpan sp = compareDTimePickerEtime.Value.Subtract(dTimePickerStime.Value);
            int daySpan = sp.Days / 2;
            dTimePickerEtime.Value = dTimePickerStime.Value.AddDays(daySpan);
            compareDTimePickerStime.Value = dTimePickerEtime.Value.AddDays(1);
            
        }

        private void smplBtnQuery_Click(object sender, EventArgs e)
        {
            if (compareDTimePickerEtime.Value > dTimePickerStime.Value && dTimePickerEtime.Value > dTimePickerStime.Value && compareDTimePickerEtime.Value > compareDTimePickerStime.Value)
            {
                NetTypeSelected = new CQTNetType();
                CellVillageSelected = new CQTCellVillage();
                CompareTimeSelected = new CQTCompareTime();
                AnalysisModelSelected = new CQTAnalysisModel();

                NetTypeSelected.GSMCheckBoxRes = rdBtnGSM.Checked;
                NetTypeSelected.TDCheckBoxRes = rdBtnTD.Checked;

                CellVillageSelected.AllCellChaeckBoxRes = rdBtnAll.Checked;
                CellVillageSelected.MainCellCheckBoxRes = rdBtnMain.Checked;
                CellVillageSelected.CoverCellChaeckBoxRes = rdBtnCover.Checked;

                CompareTimeSelected.dTimePickerSt = dTimePickerStime.Value;
                CompareTimeSelected.dTimePickerEt = dTimePickerEtime.Value.AddDays(1).AddMinutes(-1);
                CompareTimeSelected.compareDTimePickerSt = compareDTimePickerStime.Value;
                CompareTimeSelected.compareDTimePickerEt = compareDTimePickerEtime.Value;

                AnalysisModelSelected.AverageModelCheckBoxRes = rdBtnAverage.Checked;
                AnalysisModelSelected.MaxModelCheckBoxRes = rdBtnMax.Checked;
                AnalysisModelSelected.MinModelCheckBoxRes = rdBtnMin.Checked;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                errorLabel.Text = "设置的时间不正确，请检查并重新设置！";
            }
        }
    }
}