﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Core;
using MasterCom.ES.Data;
using System.Runtime.InteropServices;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class ExpFomularEditForm : BaseFormStyle
    {
        [DllImport("user32.dll")]
        private static extern bool GetCaretPos(out Point ppt);

        private static ExpFomularEditForm expEditForm = null;
        public static ExpFomularEditForm GetInstance()
        {
            if(expEditForm==null)
            {
                expEditForm = new ExpFomularEditForm();
                expEditForm.InitInfo();
            }
            return expEditForm;
        }

        private void InitInfo()
        {
            DataGatherProxy proxy = DataGatherProxy.GetInstance();
            Dictionary<string, DataProvider> dic = proxy.DataProviderDic;
            foreach(DataProvider prov in dic.Values)
            {
                cbxDataSrc.Items.Add(prov);
            }
            if(cbxDataSrc.Items.Count>0)
            {
                cbxDataSrc.SelectedIndex = 0;
            }
        }
        public void FreshInProcModule()
        {
            cbxInProcModule.Items.Clear();

            foreach(string key in ESEngine.GetInstance().inProcModuleDic.Keys)
            {
                cbxInProcModule.Items.Add(key);
            }
        }
        public void FreshResvTreeShow(ProcRoutine curSelProc)
        {
            DataGatherProxy proxy = DataGatherProxy.GetInstance();
            treeViewResvValue.Nodes.Clear();
            TreeNode pubNode = new TreeNode();
            treeViewResvValue.Nodes.Add(pubNode);
            pubNode.Tag = proxy.PublicResvStore;
            pubNode.Text = "流程内预存值";
            fillRsvItemsToNode(curSelProc.ReservStore, pubNode);

            pubNode = new TreeNode();
            treeViewResvValue.Nodes.Add(pubNode);
            pubNode.Tag = proxy.PublicResvStore;
            pubNode.Text = "公共预存值";
            fillRsvItemsToNode(proxy.PublicResvStore, pubNode);
            foreach (string inProcKey in ESEngine.GetInstance().inProcModuleDic.Keys)
            {
                ResvStore store = ESEngine.GetInstance().inProcModuleDic[inProcKey].ReservStore;
                TreeNode provNode = new TreeNode();
                provNode.Tag = store;
                provNode.Text = inProcKey;
                treeViewResvValue.Nodes.Add(provNode);
                fillRsvItemsToNode(store, provNode);
            }
        }
        private void fillRsvItemsToNode(ResvStore store, TreeNode tnode)
        {
            foreach (string str in store.ResvValueDic.Keys)
            {
                TreeNode nd = new TreeNode();
                nd.Tag = str;
                nd.Text = str;
                tnode.Nodes.Add(nd);
            }
            foreach (string str in store.ResvStringDic.Keys)
            {
                TreeNode nd = new TreeNode();
                nd.Tag = str;
                nd.Text = str + "(字符型)";
                tnode.Nodes.Add(nd);
            }
        }
        public ExpFomularEditForm()
        {
            InitializeComponent();
        }
        public string InputFormula
        {
            get
            {
                string exp = rboxFormula.Text.Replace(" ","");
                return exp;
            }
        }

        private void ExpFomularEditForm_Load(object sender, EventArgs e)
        {
            //
        }

        private void cbxDataSrc_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxMethod.Items.Clear();
            resetParamDescription(null);
            DataProvider prov = cbxDataSrc.SelectedItem as DataProvider;
            if(prov!=null)
            {
                if(prov.VFuncGroup.Count>0)
                {
                    cbxVirtual.Enabled = true;
                    cbxVirtual.Items.Clear();
                    foreach(VirtualFuncGroup vg in prov.VFuncGroup.Values)
                    {
                        cbxVirtual.Items.Add(vg);
                    }
                    cbxVirtual.SelectedIndex = 0;
                }
                else
                {
                    cbxVirtual.Items.Clear();
                    cbxVirtual.Items.Add("全部");
                    cbxVirtual.SelectedIndex = 0;
                    cbxVirtual.Enabled = false;
                    foreach (FuncInfo fi in prov.FuncInfoDic.Values)
                    {
                        cbxMethod.Items.Add(fi);
                    }
                }
                if(cbxMethod.Items.Count>0)
                {
                    cbxMethod.SelectedIndex = 0;
                }
            }
        }
        private void cbxVirtual_SelectedIndexChanged(object sender, EventArgs e)
        {
            VirtualFuncGroup vg = cbxVirtual.SelectedItem as VirtualFuncGroup;
            if(vg !=null)
            {
                cbxMethod.Items.Clear();
                foreach (FuncInfo fi in vg.Funcs)
                {
                    cbxMethod.Items.Add(fi);
                }
                if (cbxMethod.Items.Count > 0)
                {
                    cbxMethod.SelectedIndex = 0;
                }
            }
        }
        private void cbxMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(cbxMethod.SelectedItem==null)
            {
                lbFuncDesc.Text = "";
            }
            FuncInfo fi = cbxMethod.SelectedItem as FuncInfo;
            resetParamDescription(fi);
            updateFuncExpString();
        }
        private void resetParamDescription(FuncInfo fi)
        {
            tbxParam1.Enabled = false;
            tbxParam2.Enabled = false;
            tbxParam3.Enabled = false;
            tbxParam4.Enabled = false;
            tbxParam5.Enabled = false;
            tbxParam6.Enabled = false;
            lbParamTxt1.Text = "";
            lbParamTxt2.Text = "";
            lbParamTxt3.Text = "";
            lbParamTxt4.Text = "";
            lbParamTxt5.Text = "";
            lbParamTxt6.Text = "";
            if(fi==null)
            {
                lbFuncDesc.Text = "";
            }
            else
            {
                lbFuncDesc.Text = fi.Desc;
                List<ParamInfo> paramsList = fi.ParamDescList;
                for(int i=0;i<paramsList.Count;i++)
                {
                    if(i==0)
                    {
                        tbxParam1.Enabled = true;
                        lbParamTxt1.Text = paramsList[i].desc;
                    }
                    else if (i == 1)
                    {
                        tbxParam2.Enabled = true;
                        lbParamTxt2.Text = paramsList[i].desc;
                    }
                    else if (i == 2)
                    {
                        tbxParam3.Enabled = true;
                        lbParamTxt3.Text = paramsList[i].desc;
                    }
                    else if (i == 3)
                    {
                        tbxParam4.Enabled = true;
                        lbParamTxt4.Text = paramsList[i].desc;
                    }
                    else if (i == 4)
                    {
                        tbxParam5.Enabled = true;
                        lbParamTxt5.Text = paramsList[i].desc;
                    }
                    else if (i == 5)
                    {
                        tbxParam6.Enabled = true;
                        lbParamTxt6.Text = paramsList[i].desc;
                    }
                }
            }
        }
        private void updateFuncExpString()
        {
            DataProvider prov = cbxDataSrc.SelectedItem as DataProvider;
            FuncInfo fi = cbxMethod.SelectedItem as FuncInfo;
            if(prov!=null && fi!=null)
            {
                StringBuilder sbExp = new StringBuilder();
                sbExp.Append("{");
                sbExp.Append(prov.Name);
                sbExp.Append(":");
                sbExp.Append(fi.Name);
                if(fi.ParamDescList.Count>0)
                {
                    sbExp.Append("[");
                    addParam(fi, sbExp);
                    sbExp.Append("]");
                }
                sbExp.Append("}");
                tbxFuncExp.Text = sbExp.ToString();
            }
            else
            {
                tbxFuncExp.Text = "";
            }
        }

        private void addParam(FuncInfo fi, StringBuilder sbExp)
        {
            for (int i = 0; i < fi.ParamDescList.Count; i++)
            {
                if (i == 0)
                {
                    sbExp.Append(tbxParam1.Text);
                }
                else if (i == 1)
                {
                    sbExp.Append(tbxParam2.Text);
                }
                else if (i == 2)
                {
                    sbExp.Append(tbxParam3.Text);
                }
                else if (i == 3)
                {
                    sbExp.Append(tbxParam4.Text);
                }
                else if (i == 4)
                {
                    sbExp.Append(tbxParam5.Text);
                }
                else if (i == 5)
                {
                    sbExp.Append(tbxParam6.Text);
                }
                if (i < fi.ParamDescList.Count - 1)
                {
                    sbExp.Append(",");
                }
            }
        }

        private void tbxParam1_TextChanged(object sender, EventArgs e)
        {
            updateFuncExpString();
        }

        private void tbxParam2_TextChanged(object sender, EventArgs e)
        {
            updateFuncExpString();
        }

        private void tbxParam3_TextChanged(object sender, EventArgs e)
        {
            updateFuncExpString();
        }

        private void tbxParam4_TextChanged(object sender, EventArgs e)
        {
            updateFuncExpString();
        }

        private void tbxParam5_TextChanged(object sender, EventArgs e)
        {
            updateFuncExpString();
        }
        private void tbxParam6_TextChanged(object sender, EventArgs e)
        {
            updateFuncExpString();
        }
        
        private void inputToken(string s)
        {
            if (rboxFormula.TextLength > 0 && rboxFormula.Text[rboxFormula.TextLength - 1] != ' ')
            {
                rboxFormula.AppendText(" ");
            }
            Point p;
            GetCaretPos(out p);
            int i = rboxFormula.GetCharIndexFromPosition(p);
            rboxFormula.Text = rboxFormula.Text.Insert(i, s);
            rboxFormula.Focus();
            if (s.Equals("()"))
            {
                rboxFormula.Select(i + 1, 0);
            }
            else
            {
                rboxFormula.Select(i + s.Length, 0);
            }

        }
        private void backSpace()
        {
            if (rboxFormula.TextLength > 0 && rboxFormula.Text[rboxFormula.TextLength - 1] != ' ')
            {
                rboxFormula.AppendText(" ");
            }
            Point p;
            GetCaretPos(out p);
            int i = rboxFormula.GetCharIndexFromPosition(p) - 1;
            if (i >= 0)
            {
                rboxFormula.Text = rboxFormula.Text.Remove(i, 1);
                rboxFormula.Focus();
                rboxFormula.Select(i, 0);
            }
        }
        private void btnInsertFuncExp_Click(object sender, EventArgs e)
        {
            inputToken(tbxFuncExp.Text);
        }

        private void btn0_Click(object sender, EventArgs e)
        {
            inputToken("0");
        }

        private void btn1_Click(object sender, EventArgs e)
        {
            inputToken("1");
        }

        private void btn2_Click(object sender, EventArgs e)
        {
            inputToken("2");
        }

        private void btn3_Click(object sender, EventArgs e)
        {
            inputToken("3");
        }

        private void btn4_Click(object sender, EventArgs e)
        {
            inputToken("4");
        }

        private void btn5_Click(object sender, EventArgs e)
        {
            inputToken("5");
        }

        private void btn6_Click(object sender, EventArgs e)
        {
            inputToken("6");
        }

        private void btn7_Click(object sender, EventArgs e)
        {
            inputToken("7");
        }

        private void btn8_Click(object sender, EventArgs e)
        {
            inputToken("8");
        }

        private void btn9_Click(object sender, EventArgs e)
        {
            inputToken("9");
        }

        private void btnKuohao_Click(object sender, EventArgs e)
        {
            inputToken("()");
        }

        private void btnDot_Click(object sender, EventArgs e)
        {
            inputToken(".");
        }

        private void btnChu_Click(object sender, EventArgs e)
        {
            inputToken("/");
        }

        private void btnCheng_Click(object sender, EventArgs e)
        {
            inputToken("*");
        }

        private void btnJian_Click(object sender, EventArgs e)
        {
            inputToken("-");
        }

        private void btnJia_Click(object sender, EventArgs e)
        {
            inputToken("+");
        }

        private void btnSmallThan_Click(object sender, EventArgs e)
        {
            inputToken("<");
        }

        private void btnBigThan_Click(object sender, EventArgs e)
        {
            inputToken(">");
        }

        private void btnNotEqual_Click(object sender, EventArgs e)
        {
            inputToken("#");
        }

        private void btnEqual_Click(object sender, EventArgs e)
        {
            inputToken("=");
        }
        private void btnOpIn_Click(object sender, EventArgs e)
        {
            inputToken("@");
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            rboxFormula.Text = "";
        }

        private void btnBackspace_Click(object sender, EventArgs e)
        {
            backSpace();
        }

        internal void FillCurrentInfo(string p)
        {
            rboxFormula.Text = p;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void treeViewResvValue_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (treeViewResvValue.SelectedNode!=null && treeViewResvValue.SelectedNode.Level == 1)
            {
                string parentNodeName = treeViewResvValue.SelectedNode.Parent.Text;
                if (parentNodeName.Equals("流程内预存值"))
                {
                    string key = treeViewResvValue.SelectedNode.Tag as string;
                    tbxResvExp.Text = "R[" + key + "]";
                }
                else
                {
                    string key = treeViewResvValue.SelectedNode.Tag as string;
                    tbxResvExp.Text = "R[" + parentNodeName + "," + key + "]";
                }
            }
        }

        private void btnInputRsvExp_Click(object sender, EventArgs e)
        {
            inputToken(tbxResvExp.Text);
        }

        private void btnCheckExp_Click(object sender, EventArgs e)
        {
            checkExpression(rboxFormula.Text.Trim());
        }
        public int checkExpression(String formula)
        {
            return 0;
        }
        private ParamSelectDlg paramSelDlg = null;
        private ParamSelectDlg getParamDlg()
        {
            if(paramSelDlg==null)
            {
                paramSelDlg = new ParamSelectDlg();
            }
            return paramSelDlg;
        }
        private ReservSelectDlg resvSelDlg = null;
        private ReservSelectDlg getResvDlg()
        {
            if(resvSelDlg==null)
            {
                resvSelDlg = new ReservSelectDlg();
            }
            return resvSelDlg;
        }
        private void miResvValue_Click(object sender, EventArgs e)
        {
            ReservSelectDlg dlg = getResvDlg();
            dlg.FreshResvTreeShow(ESProcGraphForm.GetInstance().CurSelRoutine);
            if(DialogResult.OK == dlg.ShowDialog(this))
            {
                string rsvstr = dlg.GetResultResvString();
                if (_curFireTextBox != null && rsvstr != null)
                {
                    _curFireTextBox.Text = rsvstr;
                }
            }
        }

        private void miParamValue_Click(object sender, EventArgs e)
        {
            ParamSelectDlg dlg = getParamDlg();
            dlg.FillParams(ESEngine.GetInstance().dtParamDic);
            if (DialogResult.OK == dlg.ShowDialog(this))
            {
                DTParameter selParam = dlg.GetSelectedParam();
                if (_curFireTextBox != null && selParam != null)
                {
                    _curFireTextBox.Text = selParam.paramKey;
                }
            }
        }
        private TextBox _curFireTextBox;
        private void btnFunc1_Click(object sender, EventArgs e)
        {
            if(tbxParam1.Enabled)
            {
                _curFireTextBox = tbxParam1;
                ctxInputFromMenu.Show(btnFunc1, 0, 0);
            }
        }

        private void btnFunc2_Click(object sender, EventArgs e)
        {
            if (tbxParam2.Enabled)
            {
                _curFireTextBox = tbxParam2;
                ctxInputFromMenu.Show(btnFunc2, 0, 0);
            }
        }

        private void btnFunc3_Click(object sender, EventArgs e)
        {
            if (tbxParam3.Enabled)
            {
                _curFireTextBox = tbxParam3;
                ctxInputFromMenu.Show(btnFunc3, 0, 0);
            }
        }

        private void btnFunc4_Click(object sender, EventArgs e)
        {
            if (tbxParam4.Enabled)
            {
                _curFireTextBox = tbxParam4;
                ctxInputFromMenu.Show(btnFunc4, 0, 0);
            }
        }

        private void btnFunc5_Click(object sender, EventArgs e)
        {
            if (tbxParam5.Enabled)
            {
                _curFireTextBox = tbxParam5;
                ctxInputFromMenu.Show(btnFunc5, 0, 0);
            }
        }
        private void btnFunc6_Click(object sender, EventArgs e)
        {
            if (tbxParam6.Enabled)
            {
                _curFireTextBox = tbxParam6;
                ctxInputFromMenu.Show(btnFunc6, 0, 0);
            }
        }

        private MsgIDDlg msgDlg = null;
        private MsgIDDlg getMsgDlg()
        {
            if (msgDlg == null)
            {
                msgDlg = new MsgIDDlg();
            }
            return msgDlg;
        }
        private void miMsgId_Click(object sender, EventArgs e)
        {
            MsgIDDlg dlg = getMsgDlg();
            if (DialogResult.OK == dlg.ShowDialog(this))
            {
                int msgid = dlg.GetSelectedMsgID();
                if (_curFireTextBox != null)
                {
                    _curFireTextBox.Text = ""+msgid;
                }
            }
        }
        private EventIDDlg evtDlg = null;
        private EventIDDlg getEventDlg()
        {
            if (evtDlg == null)
            {
                evtDlg = new EventIDDlg();
            }
            return evtDlg;
        }
        private void miEventId_Click(object sender, EventArgs e)
        {
            EventIDDlg dlg = getEventDlg();
            if (DialogResult.OK == dlg.ShowDialog(this))
            {
                int evtid = dlg.GetSelectedEventID();
                if (_curFireTextBox != null)
                {
                    _curFireTextBox.Text = "" + evtid;
                }
            }
        }

        private void cbxInProcModule_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if(cbxInProcModule.SelectedItem!=null)
            {
                rboxFormula.Text = cbxInProcModule.SelectedItem.ToString();
            }
        }

        private void miMSCName_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "MSC名称";
            }
        }

        private void miBSCName_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "BSC名称";
            }
        }

        private void miCellType_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "小区类型";
            }
        }

        private void miBandType_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "BANDTYPE";
            }
        }

        private void miCellDesc_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "小区描述";
            }
        }

        private void miCellCode_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "小区编码";
            }
        }

        private void miHop_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "HOP";
            }
        }

        private void miTCH_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "TCH";
            }
        }

        private void miLAC_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "LAC";
            }
        }

        private void miCI_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "CI";
            }
        }

        private void miBCCH_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "BCCH";
            }
        }

        private void miBSIC_Click(object sender, EventArgs e)
        {
            if (_curFireTextBox != null)
            {
                _curFireTextBox.Text = "BSIC";
            }
        }

        private void miOwnFunc_Click(object sender, EventArgs e)
        {
            SelectOwnFuncDlg dlg = new SelectOwnFuncDlg();
            dlg.FillCommandersDic(DTDataProvider.GetDTProviderInstance().CommanderDic);
            if (DialogResult.OK == dlg.ShowDialog(this))
            {
                ESOwnFuncCommander cmd = dlg.GetSelectedOwnFuncCmd();
                if (cmd != null && _curFireTextBox != null)
                {
                    _curFireTextBox.Text = cmd.desc;
                }
            }
        }
    }
}