﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraPrinting;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class RusultListForm : MinCloseForm
    {
        private List<WeakSINRPoint> details = new List<WeakSINRPoint>();
        public RusultListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        public void FillData(List<WeakSINRPointGroup> groupSet)
        {
            fillSummaryTable(groupSet);
        }

        private void fillSummaryTable(List<WeakSINRPointGroup> groupSet)
        {
            DataTable tb = new DataTable();
            tb.Columns.Add("Group", typeof(object));
            tb.Columns.Add("TotalCnt", typeof(int));
            tb.Columns.Add("WPCnt", typeof(int));
            tb.Columns.Add("Reason", typeof(ReasonBase));
            tb.Columns.Add("ReasonCnt", typeof(int));
            tb.Columns.Add("Percentage", typeof(double));

            List<WeakSINRPoint> pnts = new List<WeakSINRPoint>();
            foreach (WeakSINRPointGroup item in groupSet)
            {
                pnts.AddRange(item.Points);
                foreach (ReasonBase reason in item.ReasonPntDic.Keys)
                {
                    DataRow row = tb.NewRow();
                    row["Group"] = item;
                    row["TotalCnt"] = item.TotalPntCnt;
                    row["WPCnt"] = item.WeakPointCnt;
                    row["Reason"] = reason;
                    row["ReasonCnt"] = item.ReasonPntDic[reason].Count;
                    row["Percentage"] = Math.Round(100.0
                        * item.ReasonPntDic[reason].Count / item.WeakPointCnt, 2);
                    tb.Rows.Add(row);
                }
            }
            this.gridSummary.DataSource = tb;
            this.gridSummary.RefreshDataSource();

            this.details = pnts;
            this.gridDetail.DataSource = pnts;
            this.gridDetail.RefreshDataSource();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExportResultSecurityHelper.ExportToExcel(exportSum2XlsThread, ExportResultSecurityHelper.ObjFileName, true);
        }
        private void exportSum2XlsThread(object obj)
        {
            string fileName = obj as string;
            XlsxExportOptions options = new XlsxExportOptions(TextExportMode.Text);
            this.gridSummary.ExportToXlsx(fileName, options);
            WaitBox.Close();
        }

        private void miExport2XlsDetail_Click(object sender, EventArgs e)
        {
            Dictionary<string, List<WeakSINRPoint>> cellDataDic = CoverageDataByCell();
            List<NPOIRow> rowList = getNPOIRow(cellDataDic);
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        private Dictionary<string, List<WeakSINRPoint>> CoverageDataByCell()
        {
            Dictionary<string, List<WeakSINRPoint>> cellDataDic = new Dictionary<string, List<WeakSINRPoint>>();
            List<WeakSINRPoint> cellResultList;
            foreach (var cellData in details)
            {
                string cellName = cellData.CellName;
                if (!cellDataDic.TryGetValue(cellName, out cellResultList))
                {
                    cellResultList = new List<WeakSINRPoint>();
                    cellDataDic.Add(cellName, cellResultList);
                }
                cellResultList.Add(cellData);
            }
            return cellDataDic;
        }

        private List<NPOIRow> getNPOIRow(Dictionary<string, List<WeakSINRPoint>> cellDataDic)
        {
            List<NPOIRow> rowList = new List<NPOIRow>(details.Count + 1);
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("小区");

            titleRow.AddCellValue("TAC");
            titleRow.AddCellValue("ECI");
            titleRow.AddCellValue("CellID");
            titleRow.AddCellValue("SINR");
            titleRow.AddCellValue("RSRP");
            titleRow.AddCellValue("原因");
            titleRow.AddCellValue("文件");
            titleRow.AddCellValue("距离(米)");
            titleRow.AddCellValue("采样点经度");
            titleRow.AddCellValue("采样点纬度");
            rowList.Add(titleRow);
            
            foreach (var cellData in cellDataDic)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, cellData.Value, cellData.Key);
                rowList.Add(row);
            }
            return rowList;
        }

        protected void fillRow(ref NPOIRow row, List<WeakSINRPoint> cellData, string cellName)
        {
            if (row == null || cellData == null)
                return;
            //添加一级数据
            row.AddCellValue(cellName);
            foreach (var cell in cellData)
            {
                //添加二级数据
                NPOIRow subRow = new NPOIRow();
                subRow.AddCellValue(cell.TAC);
                subRow.AddCellValue(cell.ECI);
                subRow.AddCellValue(cell.CellID);
                subRow.AddCellValue(cell.SINR);
                subRow.AddCellValue(cell.RSRP);
                subRow.AddCellValue(cell.ReasonName);
                subRow.AddCellValue(cell.FileName);
                subRow.AddCellValue(cell.Distance);
                subRow.AddCellValue(cell.PntLng);
                subRow.AddCellValue(cell.PntLat);
                row.AddSubRow(subRow);
            }
        }


        private void viewDetail_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = viewDetail.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            List<WeakSINRPoint> selPnts = new List<WeakSINRPoint>();
            if (viewDetail.IsGroupRow(info.RowHandle))
            {//组头行
                getChildRows(viewDetail, info.RowHandle, selPnts);
            }
            else
            {//普通行
                WeakSINRPoint row = viewDetail.GetRow(info.RowHandle) as WeakSINRPoint;
                if (row != null)
                {
                    selPnts.Add(row);
                }
            }
            showInGIS(selPnts);
        }

        private void showInGIS(List<WeakSINRPoint> pnts)
        {
            mModel.ClearDTData();
            foreach (WeakSINRPoint pnt in pnts)
            {
                mModel.DTDataManager.Add(pnt.TestPoint);
            }
            mModel.FireDTDataChanged(this);
        }

        public void getChildRows(GridView view, int groupRowHandle, List<WeakSINRPoint> childRows)
        {
            if (!view.IsGroupRow(groupRowHandle))
            {
                return;
            }
            int childCount = view.GetChildRowCount(groupRowHandle);
            for (int i = 0; i < childCount; i++)
            {
                int childHandle = view.GetChildRowHandle(groupRowHandle, i);
                if (view.IsGroupRow(childHandle))
                    getChildRows(view, childHandle, childRows);
                else
                {
                    WeakSINRPoint row = view.GetRow(childHandle) as WeakSINRPoint;
                    if (row != null && !childRows.Contains(row))
                    {
                        childRows.Add(row);
                    }
                }
            }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
        }

    }
}
