﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLTENCellLevelHigherForm : MinCloseForm
    {
        MapForm mapForm;
        private  List<LteRsrpInfo> listCellInfo = null;

        public ZTLTENCellLevelHigherForm() : base(MainModel.GetInstance())
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
            DisposeWhenClose = true;
        }

        /// <summary>
        /// 绑定数据
        /// </summary>
        /// <param name="CelllteInfos"></param>
        public void FillData(List<LteRsrpInfo> CelllteInfos)
        {
            listCellInfo = new List<LteRsrpInfo>();
            int sn = 0;
            foreach (LteRsrpInfo lcInfo in CelllteInfos)
            {
                lcInfo.SN = ++sn;
                lcInfo.CellType = "Lte";//小区类型
                MainModel.DTDataManager.Add(lcInfo.TestPoint);
                listCellInfo.Add(lcInfo);
            }
            //绑定数据源
            gridControlCell.DataSource = listCellInfo;
            this.gridViewCell.BestFitColumns();//单元格自适应宽度
            gridControlCell.RefreshDataSource();
            MainModel.FireDTDataChanged(this);
        }

        /// <summary>
        /// 导出Excel文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void miExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridViewCell);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        /// <summary>
        /// 双击在地图上显示小区
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridViewCell_DoubleClick(object sender, EventArgs e)
        {
            if (gridViewCell.SelectedRowsCount> 0 )
            {
                MainModel.SelectedCells.Clear();
                MainModel.SelectedTestPoints.Clear();
                int i= gridViewCell.GetSelectedRows()[0];
                //得到选中的第0行数据
                LteRsrpInfo info = listCellInfo[i];
                mModel.DrawFlyLines = true;
                MainModel.SelectedTestPoints.Add(info.TestPoint);
                MainModel.FireSelectedTestPointsChanged(this);
                double Longitude = info.Longitude;
                double Latitude = info.Latitude;
                mapForm.GoToView(Longitude, Latitude);
            }
        }
    }
}
