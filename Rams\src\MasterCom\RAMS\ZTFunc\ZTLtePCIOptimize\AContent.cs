﻿using System;
using System.Collections.Generic;
//using System.Linq;
using System.Text;
using System.Net;
using System.IO;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class AContent : ICloneable
    {
        public byte[] Buff
        {
            get { return paramsBuffer; }
        }
        public virtual ushort Length
        {
            get
            {
                return (ushort)paramsBufferSize;
            }
        }
        public int CurOffset { get; set; }

        public void PrepareAddParam()
        {
            this.paramsBufferSize = 0;
            PrepareParamsBuffer(0);
        }

        public AContent()
        {
            init();
            initNoOrder();
        }

        Dictionary<Type, object> typeDic;
        private void init()
        {
            typeDic = new Dictionary<Type, object>();
            typeDic.Add(typeof(int), GetParamInt());
            typeDic.Add(typeof(double), GetParamDouble());
            typeDic.Add(typeof(String), GetParamString());
            typeDic.Add(typeof(float), GetParamFloat());
            typeDic.Add(typeof(Single), GetParamFloat());
            typeDic.Add(typeof(Numeric), GetParamNumeric());
            typeDic.Add(typeof(short), GetParamShort());
            typeDic.Add(typeof(long), GetParamLong());
            typeDic.Add(typeof(byte), GetParamByte());
            typeDic.Add(typeof(bool), GetParamBool());
            typeDic.Add(typeof(UInt16), GetParamUShort());
            typeDic.Add(typeof(UInt32), GetParamUInt());
            typeDic.Add(typeof(UInt64), GetParamULong());
            typeDic.Add(typeof(DateTime), GetParameDateTimeByInt());
            typeDic.Add(typeof(byte[]), GetParamBytes());
            typeDic.Add(typeof(Char), GetParamChar());
        }

        Dictionary<Type, object> typeNoOrderDic;
        private void initNoOrder()
        {
            typeNoOrderDic = new Dictionary<Type, object>();
            typeNoOrderDic.Add(typeof(int), GetNoOrderParamInt());
            typeNoOrderDic.Add(typeof(double), GetNoOrderParamDouble());
            typeNoOrderDic.Add(typeof(String), GetNoOrderParamString());
            typeNoOrderDic.Add(typeof(float), GetNoOrderParamFloat());
            typeNoOrderDic.Add(typeof(Single), GetNoOrderParamFloat());
            typeNoOrderDic.Add(typeof(Numeric), GetNoOrderParamNumeric());
            typeNoOrderDic.Add(typeof(short), GetNoOrderParamShort());
            typeNoOrderDic.Add(typeof(long), GetNoOrderParamLong());
            typeNoOrderDic.Add(typeof(byte), GetParamByte());
            typeNoOrderDic.Add(typeof(bool), GetParamBool());
            typeNoOrderDic.Add(typeof(UInt16), GetParamUShort());
            typeNoOrderDic.Add(typeof(UInt32), GetParamUInt());
            typeNoOrderDic.Add(typeof(UInt64), GetParamULong());
            typeNoOrderDic.Add(typeof(DateTime), GetNoOrderParameDateTimeByInt());
            typeNoOrderDic.Add(typeof(byte[]), GetParamBytes());
            typeNoOrderDic.Add(typeof(Char), GetParamChar());
        }

        public object GetParame(Type type)
        {
            foreach (var item in typeDic)
            {
                if (type.Equals(item.Key))
                {
                    return item.Value;
                }
            }
            return null;
        }

        public object GetNoOrderParame(Type type)
        {
            foreach (var item in typeNoOrderDic)
            {
                if (type.Equals(item.Key))
                {
                    return item.Value;
                }
            }
            return null;
        }

        protected void addParam<T>(object obj, T type)
        {
            AddParam((T)obj);
        }

        public void AddParam(object obj)
        {
            if (obj is int || obj is double || obj is string || obj is short || obj is float || obj is long 
                || obj is UInt16 || obj is UInt32 || obj is UInt64 || obj is byte || obj is Char)
            {
                Type type = obj.GetType();
                addParam(obj, type);
            }
            else if (obj is bool) AddParam((bool)obj ? (byte)1 : (byte)0);
            else if (obj is DateTime) AddParamDateTime4((DateTime)obj);
            else if (obj is byte[]) AddParam((byte[])obj, ((byte[])obj).Length);
            else AddParam(obj.ToString()); //包含Numeric类型
        }

        protected void addNoOrderParam<T>(object obj, T type)
        {
            AddNoOrderParam((T)obj);
        }

        public void AddNoOrderParam(object obj)
        {
            if (obj is int || obj is double || obj is string || obj is short || obj is float || obj is long
               || obj is UInt16 || obj is UInt32 || obj is UInt64 || obj is DateTime)
            {
                Type type = obj.GetType();
                addNoOrderParam(obj, type);
            }
            else if(obj is byte || obj is Char)
            {
                Type type = obj.GetType();
                addParam(obj, type);
            }
            else if (obj is bool) AddParam((bool)obj ? (byte)1 : (byte)0);
            else if (obj is byte[]) AddParam((byte[])obj, ((byte[])obj).Length);
            else AddNoOrderParam(obj.ToString());//包含Numeric类型
        }

        public void AddParam(byte param)
        {
            this.PrepareParamsBuffer(1);
            this.paramsBuffer[this.paramsBufferSize] = param;
            this.paramsBufferSize++;
        }
        public void AddParam(short param)
        {
            this.PrepareParamsBuffer(2);
            BitConverter.GetBytes(IPAddress.HostToNetworkOrder(param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 2;
        }
        public void AddParam(int param)
        {
            this.PrepareParamsBuffer(4);
            BitConverter.GetBytes(IPAddress.HostToNetworkOrder(param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 4;
        }
        public void AddParam(long param)
        {
            this.PrepareParamsBuffer(8);
            BitConverter.GetBytes(IPAddress.HostToNetworkOrder(param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 8;
        }
        public void AddParam(ushort param)
        {
            this.PrepareParamsBuffer(2);
            BitConverter.GetBytes((ushort)IPAddress.HostToNetworkOrder((short)param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 2;
        }
        public void AddParam(uint param)
        {
            this.PrepareParamsBuffer(4);
            BitConverter.GetBytes((uint)IPAddress.HostToNetworkOrder((int)param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 4;
        }
        public void AddParam(ulong param)
        {
            this.PrepareParamsBuffer(8);
            BitConverter.GetBytes((ulong)IPAddress.HostToNetworkOrder((long)param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 8;
        }
        public void AddParam(float param)
        {
            AddParam((int)(param * 1000));
        }
        public void AddParam(double param)
        {
            AddParam((int)(param * 10000000));
        }
        /// <summary>
        /// 固定长度的字符串传输，若太长就截断
        /// </summary>
        /// <param name="param"></param>
        /// <param name="fixLen"></param>
        public void AddParam(String param, int fixLen)
        {
            byte[] buffFix = new byte[fixLen];
            byte[] buffer = Encoding.Default.GetBytes(param);
            Array.Copy(buffer, buffFix, Math.Min(buffer.Length, fixLen));
            buffFix.CopyTo(paramsBuffer, paramsBufferSize);
            paramsBufferSize += buffFix.Length;
        }
        public void AddParam(String param)
        {
            if (param == null)
            {
                AddParam((short)0);
            }
            else
            {
                byte[] buffer = Encoding.Default.GetBytes(param);
                AddParam((short)buffer.Length);
                PrepareParamsBuffer(buffer.Length);
                buffer.CopyTo(paramsBuffer, paramsBufferSize);
                paramsBufferSize += buffer.Length;
            }
        }
        public void AddParam(DateTime dt)
        {
            long time = JavaDate.GetMilliseconds(dt);
            AddParam((int)(time / 1000L));
            AddParam((int)(time % 1000L));
        }
        public void AddParamDateTime8(DateTime dt)
        {
            AddParam(dt);
        }
        public void AddParamDateTime4(DateTime dt)
        {
            long time = JavaDate.GetMilliseconds(dt);
            AddParam((int)(time / 1000L));
        }
        //public void AddNoOrderParam(byte param)
        //{
        //    this.PrepareParamsBuffer(1);
        //    this.paramsBuffer[this.paramsBufferSize] = param;
        //    this.paramsBufferSize++;
        //}
        public void AddNoOrderParam(short param)
        {
            this.PrepareParamsBuffer(2);
            BitConverter.GetBytes(param).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 2;
        }
        public void AddNoOrderParam(int param)
        {
            this.PrepareParamsBuffer(4);
            BitConverter.GetBytes(param).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 4;
        }
        public void AddNoOrderParam(long param)
        {
            this.PrepareParamsBuffer(8);
            BitConverter.GetBytes(param).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 8;
        }
        public void AddNoOrderParam(ushort param)
        {
            this.PrepareParamsBuffer(2);
            BitConverter.GetBytes(param).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 2;
        }
        public void AddNoOrderParam(uint param)
        {
            this.PrepareParamsBuffer(4);
            BitConverter.GetBytes(param).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 4;
        }
        public void AddNoOrderParam(ulong param)
        {
            this.PrepareParamsBuffer(8);
            BitConverter.GetBytes(param).CopyTo(this.paramsBuffer, this.paramsBufferSize);
            this.paramsBufferSize += 8;
        }
        public void AddNoOrderParam(float param)
        {
            AddNoOrderParam((int)(param * 1000));
        }
        public void AddNoOrderParam(double param)
        {
            AddNoOrderParam((int)(param * 10000000));
        }
        public void AddNoOrderParam(String param)
        {
            if (param == null)
            {
                AddNoOrderParam((short)0);
            }
            else
            {
                byte[] buffer = Encoding.Default.GetBytes(param);
                AddNoOrderParam((short)buffer.Length);
                PrepareParamsBuffer(buffer.Length);
                buffer.CopyTo(paramsBuffer, paramsBufferSize);
                paramsBufferSize += buffer.Length;
            }
        }
        public void AddParam(byte[] buffer, int length)
        {
            PrepareParamsBuffer(buffer.Length);
            if (length == buffer.Length)
            {
                buffer.CopyTo(paramsBuffer, paramsBufferSize);
            }
            else
            {
                for (int i = 0; i < length; i++)
                {
                    paramsBuffer[paramsBufferSize + i] = buffer[i];
                }
            }
            paramsBufferSize += length;
        }

        public void AddNoOrderParam(DateTime dt)
        {
            long time = JavaDate.GetMilliseconds(dt);
            AddNoOrderParam((int)(time / 1000L));
            AddNoOrderParam((int)(time % 1000L));
        }

        public void AddNoOrderParamDateTime8(DateTime dt)
        {
            AddNoOrderParam(dt);
        }

        public void AddNoOrderParamDateTime4(DateTime dt)
        {
            long time = JavaDate.GetMilliseconds(dt);
            AddNoOrderParam((int)(time / 1000L));
        }

        public void PrepareGetParam()
        {
            this.CurOffset = 0;
        }

        public bool HasNextParam()
        {
            return this.CurOffset < paramsBufferSize - 1;
        }

        public byte GetParamByte()
        {
            return this.paramsBuffer[this.CurOffset++];
        }

        public char GetParamChar()
        {
            return (Char)GetParamByte();
        }

        public bool GetParamBool()
        {
            return this.paramsBuffer[this.CurOffset++] == 0x01;
        }

        public byte[] GetParamBytes()
        {
            short length = GetParamShort();
            byte[] retBytes = new byte[length];
            if (length > 0)
            {
                GetParamBytesNoConvert(retBytes, 0, length);
            }
            return retBytes;
        }

        public byte[] GetParamBytes(int length)
        {
            byte[] tmp = new byte[length];
            Array.Copy(this.paramsBuffer, this.CurOffset, tmp, 0, length);
            this.CurOffset += length;
            return tmp;
        }

        public void GetParamBytesNoConvert(byte[] dest, int offset, int length)
        {
            Array.Copy(this.paramsBuffer, this.CurOffset, dest, offset, length);
            this.CurOffset += length;
        }

        public short GetParamShort()
        {
            short param = BitConverter.ToInt16(this.paramsBuffer, this.CurOffset);
            this.CurOffset += 2;
            return IPAddress.NetworkToHostOrder(param);
        }

        public int GetParamInt()
        {
            int param = BitConverter.ToInt32(this.paramsBuffer, this.CurOffset);
            this.CurOffset += 4;
            return IPAddress.NetworkToHostOrder(param);
        }
        //public int GetParamIntNoConvert()
        //{
        //    int param = BitConverter.ToInt32(this.paramsBuffer, this.CurOffset);
        //    this.CurOffset += 4;
        //    return param;
        //}

        public long GetParamLong()
        {
            long param = BitConverter.ToInt64(this.paramsBuffer, this.CurOffset);
            this.CurOffset += 8;
            return IPAddress.NetworkToHostOrder(param);
        }

        public ushort GetParamUShort()
        {
            return (ushort)GetParamShort();
        }

        public uint GetParamUInt()
        {
            return (uint)GetParamInt();
        }

        public ulong GetParamULong()
        {
            return (ulong)GetParamLong();
        }

        public float GetParamFloat()
        {
            return GetParamInt() / 1000.0f;
        }

        public double GetParamDouble()
        {
            return GetParamInt() / 10000000.0d;
        }

        public DateTime GetParameDateTimeByLong()
        {
            return JavaDate.GetDateTimeFromMilliseconds(GetParamInt() * 1000L + GetParamInt());
        }

        public DateTime GetParameDateTimeByInt()
        {
            return JavaDate.GetDateTimeFromMilliseconds(GetParamInt() * 1000L);
        }
        public float GetParamKFloat()
        {
            return GetParamInt() / 1024.0f;
        }

        //public string GetParamString(int length)
        //{
        //    string param = Encoding.Default.GetString(this.paramsBuffer, this.CurOffset, length);
        //    this.CurOffset += length;
        //    return param;
        //}

        public string GetParamString()
        {
            short length = this.GetParamShort();
            string param = Encoding.Default.GetString(this.paramsBuffer, this.CurOffset, length);
            this.CurOffset += length;
            return param;
        }

        public Numeric GetParamNumeric()
        {
            return new Numeric(GetParamString());
        }

        public short GetNoOrderParamShort()
        {
            short param = BitConverter.ToInt16(this.paramsBuffer, this.CurOffset);
            this.CurOffset += 2;
            return param;
        }

        public int GetNoOrderParamInt()
        {
            int param = BitConverter.ToInt32(this.paramsBuffer, this.CurOffset);
            this.CurOffset += 4;
            return param;
        }

        public long GetNoOrderParamLong()
        {
            long param = BitConverter.ToInt64(this.paramsBuffer, this.CurOffset);
            this.CurOffset += 8;
            return param;
        }

        public ushort GetNoOrderParamUShort()
        {
            return (ushort)GetNoOrderParamShort();
        }

        public uint GetNoOrderParamUInt()
        {
            return (uint)GetNoOrderParamInt();
        }

        public ulong GetNoOrderParamULong()
        {
            return (ulong)GetNoOrderParamLong();
        }

        public float GetNoOrderParamFloat()
        {
            return GetNoOrderParamInt() / 1000.0f;
        }

        public double GetNoOrderParamDouble()
        {
            return GetNoOrderParamInt() / 10000000.0d;
        }

        public string GetNoOrderParamString(int length)
        {
            string param = Encoding.Default.GetString(this.paramsBuffer, this.CurOffset, length);
            this.CurOffset += length;
            return param;
        }

        public string GetNoOrderParamString()
        {
            short length = this.GetNoOrderParamShort();
            string param = Encoding.Default.GetString(this.paramsBuffer, this.CurOffset, length);
            this.CurOffset += length;
            return param;
        }

        public Numeric GetNoOrderParamNumeric()
        {
            return new Numeric(GetNoOrderParamString());
        }

        public DateTime GetNoOrderParameDateTimeByLong()
        {
            return JavaDate.GetDateTimeFromMilliseconds(GetNoOrderParamInt() * 1000L + GetNoOrderParamInt());
        }

        public DateTime GetNoOrderParameDateTimeByInt()
        {
            return JavaDate.GetDateTimeFromMilliseconds(GetNoOrderParamInt() * 1000L);
        }

        Boolean isValidHexNum(byte ch1)
        {
            if ((ch1 >= '0' && ch1 <= '9') ||
               (ch1 >= 'a' && ch1 <= 'f') ||
               (ch1 >= 'A' && ch1 <= 'F')
              )
            {
                return true;
            }
            return false;
        }

        byte toHex(byte ch1)
        {
            if (ch1 >= '0' && ch1 <= '9')
            {
                return (byte)(ch1 - '0');
            }
            else if (ch1 >= 'a' && ch1 <= 'f')
            {
                return (byte)(ch1 - 'a' + 10);
            }
            else if (ch1 >= 'A' && ch1 <= 'F')
            {
                return (byte)(ch1 - 'A' + 10);
            }
            else
            {
                return 0;
            }
        }

        void atox(byte[] toHexChar, byte[] fromString)
        {
            int nIndex = 0;
            for (int i = 0; i < fromString.Length; i++)
            {
                byte ch1 = 0, ch2 = 0;
                ch1 = fromString[i];
                while (!isValidHexNum(ch1) && (i < fromString.Length))
                {
                    if (i == (fromString.Length - 1))
                    {
                        return;
                    }

                    i++;
                    ch1 = fromString[i];
                }

                i++;
                if (i == (fromString.Length))
                {
                    return;
                }

                ch2 = fromString[i];
                while (!isValidHexNum(ch2) && (i < fromString.Length))
                {
                    if (i == (fromString.Length - 1))
                    {
                        return;
                    }

                    i++;
                    ch2 = fromString[i];
                }
                toHexChar[nIndex++] = (byte)(toHex(ch1) * 16 + toHex(ch2));
            }
        }

        public void GetParamBytes(byte[] dest, int offset, int length)
        {
            byte[] tmp = new byte[length];
            Array.Copy(this.paramsBuffer, this.CurOffset, tmp, offset, length);
            atox(dest, tmp);
            this.CurOffset += length;
        }

        public virtual void Write(BinaryWriter writer)
        {
            if (paramsBufferSize > 0)
            {
                writer.Write(this.paramsBuffer, 0, this.paramsBufferSize);
            }
        }

        public byte[] GetAllBytes()
        {
            byte[] tmp = new byte[this.Length];
            Array.Copy(this.paramsBuffer, 0, tmp, 0, this.Length);
            return tmp;
        }

        public virtual void Read(BinaryReader reader, int length)
        {
            if (length > 0)
            {
                this.paramsBuffer = new byte[length];
                for (int i = 0; i < length; i++)
                {
                    paramsBuffer[i] = reader.ReadByte();
                }
                this.paramsBufferSize = this.paramsBuffer.Length;
            }
            CurOffset = 0;
        }

        protected void PrepareParamsBuffer(int addLength)
        {
            if (this.paramsBuffer == null)
            {
                this.paramsBuffer = new byte[(addLength / bufferAddSize + 1) * bufferAddSize];
            }
            else if (this.paramsBuffer.Length - this.paramsBufferSize < addLength)
            {
                byte[] tempBuffer = new byte[((this.paramsBufferSize + addLength) / bufferAddSize + 1) * bufferAddSize];
                this.paramsBuffer.CopyTo(tempBuffer, 0);
                this.paramsBuffer = tempBuffer;
            }
        }

        protected const int bufferAddSize = 1024;

        protected byte[] paramsBuffer;//内容

        protected int paramsBufferSize = 0;

        #region ICloneable 成员
        public virtual object Clone()
        {
            AContent co = new AContent();
            co.PrepareAddParam();
            co.AddParam(this.Buff);
            return co;
        }
        #endregion
    }
}
