﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsAcceptInfo_GX : BtsAcceptInfo_SX<LTECell, string>
    {
        public BtsAcceptInfo_GX(bool isOutdoor)
            : base(isOutdoor)
        {
        }
        protected override List<NPOIRow> getOutDoorBtsKpiSumRows()
        {
            List<NPOIRow> sumRows = new List<NPOIRow>();
            NPOIRow row = null;
            bool hasAddTitle = false;
            foreach (var varPair in this.CellAcceptInfoDic)
            {
                CellAcceptInfoBase_SX cellAcceptInfo = varPair.Value;

                addTitle(sumRows, ref row, ref hasAddTitle, cellAcceptInfo);

                row = new NPOIRow();
                row.AddCellValue(cellAcceptInfo.BtsName);
                row.AddCellValue(cellAcceptInfo.CellName);
                foreach (var kpiPair in cellAcceptInfo.KpiInfoDic)
                {
                    row.AddCellValue(kpiPair.Value.Rsrp.Avg);
                }
                foreach (var kpiPair in cellAcceptInfo.KpiInfoDic)
                {
                    row.AddCellValue(kpiPair.Value.Sinr.Avg);
                }
                foreach (var kpiPair in cellAcceptInfo.KpiInfoDic)
                {
                    row.AddCellValue(kpiPair.Value.Speed.Avg);
                }
                sumRows.Add(row);
            }
            return sumRows;
        }

        private static void addTitle(List<NPOIRow> sumRows, ref NPOIRow row, ref bool hasAddTitle, CellAcceptInfoBase_SX cellAcceptInfo)
        {
            if (!hasAddTitle)
            {
                row = new NPOIRow();
                row.AddCellValue("基站名称");
                row.AddCellValue("小区名");
                foreach (var kpiPair in cellAcceptInfo.KpiInfoDic)
                {
                    row.AddCellValue(kpiPair.Key + "RSRP");
                }
                foreach (var kpiPair in cellAcceptInfo.KpiInfoDic)
                {
                    row.AddCellValue(kpiPair.Key + "SINR");
                }
                foreach (var kpiPair in cellAcceptInfo.KpiInfoDic)
                {
                    row.AddCellValue(kpiPair.Key + "速率");
                }
                sumRows.Add(row);
                hasAddTitle = true;
            }
        }

        protected override List<NPOIRow> getInDoorBtsKpiSumRows()
        {
            List<NPOIRow> sumRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("基站名");
            row.AddCellValue("小区名称");
            row.AddCellValue("下行RSRP");
            row.AddCellValue("下行吞吐率（Mbps）");
            row.AddCellValue("下行SINR");

            row.AddCellValue("上行RSRP");
            row.AddCellValue("上行吞吐率（Mbps）");
            row.AddCellValue("上行SINR");
            sumRows.Add(row);

            foreach (var varPair in this.CellAcceptInfoDic)
            {
                CellAcceptInfoBase_SX cellAcceptInfo = varPair.Value;
                row = new NPOIRow();
                row.AddCellValue(cellAcceptInfo.BtsName);
                row.AddCellValue(cellAcceptInfo.CellName);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下行"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下行"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下行"].Sinr.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上行"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上行"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上行"].Sinr.Avg);
                sumRows.Add(row);
            }
            return sumRows;
        }
    }
    public class OutDoorCellAcceptInfo_GX : OutDoorCellAcceptInfo_SX
    {
        public OutDoorCellAcceptInfo_GX()
        {
            KpiInfoDic = new Dictionary<string, AcceptKpiInfo_SX>();
            KpiInfoDic["下载好点"] = new AcceptKpiInfo_SX();
            KpiInfoDic["下载中点"] = new AcceptKpiInfo_SX();
            KpiInfoDic["下载差点"] = new AcceptKpiInfo_SX();
            KpiInfoDic["下载任意点"] = new AcceptKpiInfo_SX();
            //KpiInfoDic["下载切换"] = new AcceptKpiInfo_SX();

            KpiInfoDic["上传好点"] = new AcceptKpiInfo_SX();
            KpiInfoDic["上传中点"] = new AcceptKpiInfo_SX();
            KpiInfoDic["上传差点"] = new AcceptKpiInfo_SX();
            KpiInfoDic["上传任意点"] = new AcceptKpiInfo_SX();
            //KpiInfoDic["上传切换"] = new AcceptKpiInfo_SX();
        }
    }
    public class IndoorCellAcceptInfo_GX : IndoorCellAcceptInfo_SX
    {
        public IndoorCellAcceptInfo_GX()
            : base()
        {
        }
    }
}
