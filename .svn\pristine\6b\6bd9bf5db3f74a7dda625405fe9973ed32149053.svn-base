﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public enum RelationalOperator
    {
        Less = 0,
        LessEqual,
        Greater,
        GreaterEqual,
        Equal
    }

    public static class RelationalOperHelper
    {
        public static bool Evaluate(RelationalOperator oper, double hostValue, double guestValue)
        {
            switch (oper)
            {
                case RelationalOperator.Less:
                    return hostValue < guestValue;
                case RelationalOperator.LessEqual:
                    return hostValue <= guestValue;
                case RelationalOperator.Greater:
                    return hostValue > guestValue;
                case RelationalOperator.GreaterEqual:
                    return hostValue >= guestValue;
                case RelationalOperator.Equal:
                    return hostValue == guestValue;
                default:
                    return false;
            }
        }
    }
}
