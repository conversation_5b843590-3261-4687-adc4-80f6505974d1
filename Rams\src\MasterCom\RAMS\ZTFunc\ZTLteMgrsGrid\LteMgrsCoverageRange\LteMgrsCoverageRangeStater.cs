﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.MTGis;
using MasterCom.Util;

using DevExpress.XtraCharts;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsCoverageRangeStater : LteMgrsStaterBase
    {
        LteMgrsFuncItem tmpFuncItem = null;

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            tmpFuncItem = curFuncItem;

#if FollowGrid
            followGrid();
#endif
        }
#if FollowGrid
        private void followGrid()
        {
            if (tmpFuncItem.BaseQueryCitys == null)
                return;

            foreach (LteMgrsCity cityHistory in tmpFuncItem.BaseQueryCitys)
            {
                LteMgrsCity cityCur = getCurCity(cityHistory.CityName);

                if (cityCur == null)
                    continue;

                foreach (LteMgrsRegion regionHistory in cityHistory.MgrsRegionMap.Values)
                {
                    LteMgrsRegion regionCur;
                    if(!cityCur.RegionDic.TryGetValue(regionHistory.RegionName, out regionCur))
                        continue;

                    foreach (LteMgrsGrid mgrsGrid in regionHistory.GridDic.Values)
                    {
                        regionCur.AddGrid(mgrsGrid);
                    }

                }
            }
        }

        private LteMgrsCity getCurCity(string cityName)
        {
            foreach (LteMgrsCity cityCur in tmpFuncItem.CurQueryCitys)
            {
                if (cityCur.CityName == cityName)
                    return cityCur;
            }

            return null;
        }
#endif
        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsCoverageRangeResult resultControl = new LteMgrsCoverageRangeResult();
            resultControl.FillData(tmpFuncItem);
            return new List<LteMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            tmpFuncItem = null;
        }

        public DataTable GetTable(LteMgrsCity city, LteMgrsCoverageCondition cond, ref List<LteMgrsDrawItem> drawList)
        {
            DataTable table = new DataTable(city.CityName + EnumDescriptionAttribute.GetText(cond.FreqType) + "重叠覆盖度分布");
            table.Columns.AddRange(BuildColumns());
            //栅格小区详情
            List<NPOIRow> cellSampleDataList = new List<NPOIRow>();
            NPOIRow nrCol = LteMgrsCellData.getSampleColName();
            cellSampleDataList.Add(nrCol);
            //栅格指标统计
            List<NPOIRow> cellStatDataList = new List<NPOIRow>();
            NPOIRow nr2Col = LteMgrsGridDetail.getSampleColName();
            cellStatDataList.Add(nr2Col);
            //重叠覆盖度统计
            List<NPOIRow> resultDataList = new List<NPOIRow>();
            NPOIRow nr3Col = BuildNpoiColumn();
            resultDataList.Add(nr3Col);

            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                int invalidCnt = 0;
                float rsrpSum = 0;
                int[] rangesCnt = new int[Ranges.ColorRanges.Count];
                List<LteMgrsGrid> gridList = new List<LteMgrsGrid>(region.GridDic.Values);
                foreach (LteMgrsGrid grid in gridList)
                {
                    List<LteMgrsCellData> cellDataList = new List<LteMgrsCellData>();
                    float value = LteMgrsGridHelper.GetRelativeCoverageJT(grid, cond, ref cellDataList);
                    cellDataList.Sort(new Comparison<LteMgrsCellData>(delegate(LteMgrsCellData x, LteMgrsCellData y) { return y.lteCell.AvgRsrp.CompareTo(x.lteCell.AvgRsrp); }));
                    foreach (LteMgrsCellData cellData in cellDataList)//小区详情输出
                    {
                        cellSampleDataList.Add(cellData.getSampleColValue(grid, region.RegionName, cellDataList.Count));
                    }
                    cellStatDataList.Add(LteMgrsGridDetail.getSampleColValue(grid, region.RegionName, cellDataList, value));

                    //用于地图绘制
                    LteMgrsDrawItem item = new LteMgrsDrawItem(new DbPoint(grid.TLLng, grid.BRLat), new DbPoint(grid.BRLng, grid.TLLat));
                    item.FillColor = Ranges.GetColor(value);
                    item.ToolInfoTitle = grid.MgrsString;
                    item.ToolInfoDetail = grid.DetailInfo;
                    drawList.Add(item);

                    if (value == 0)
                    {
                        ++invalidCnt;
                        continue;
                    }
                    int idx = Ranges.GetIndex(value);
                    ++rangesCnt[idx];
                    rsrpSum += value;
                }

                // add to table
                List<object> dataRow = new List<object>();
                dataRow.Add(region.RegionName);
                foreach (int cnt in rangesCnt)
                {
                    dataRow.Add(cnt);
                }
                dataRow.Add(invalidCnt);
                dataRow.Add(gridList.Count);
                table.Rows.Add(dataRow.ToArray());
                NPOIRow nrData = new NPOIRow();
                nrData.cellValues = dataRow;
                resultDataList.Add(nrData);
            }

            if (cond.EnableOutputSample)
            {
                List<string> sheetList = new List<string>();
                sheetList.Add(string.Format("{0}_栅格小区详细_{2}_{1:yyyyMMddHHmmss}.csv", city.CityName, DateTime.Now, EnumDescriptionAttribute.GetText(cond.FreqType)));
                sheetList.Add(string.Format("{0}_栅格指标统计_{2}_{1:yyyyMMddHHmmss}.csv", city.CityName, DateTime.Now, EnumDescriptionAttribute.GetText(cond.FreqType)));
                sheetList.Add(string.Format("{0}_重叠覆盖度统计_{2}_{1:yyyyMMddHHmmss}.csv", city.CityName, DateTime.Now, EnumDescriptionAttribute.GetText(cond.FreqType)));

                List<List<NPOIRow>> allDataList =new List<List<NPOIRow>>();
                allDataList.Add(cellSampleDataList);
                allDataList.Add(cellStatDataList);
                allDataList.Add(resultDataList);
                OutputCsvFile(allDataList, sheetList, cond.strCsvPath);
            }

            // summary
            AddSummary(table);
            return table;
        }

        /// <summary>
        /// 批量导出CSV文件
        /// </summary>
        public static void OutputCsvFile(List<List<NPOIRow>> nrDatasList, List<string> sheetNames,string filePath)
        {
            if (!System.IO.Directory.Exists(filePath))
            {
                filePath = Application.StartupPath + "\\userData\\SampleData";
                if (!System.IO.Directory.Exists(filePath))
                    System.IO.Directory.CreateDirectory(filePath);
            }

            try
            {
                int iSheetNum = nrDatasList.Count;
                for (int i = 0; i < iSheetNum; i++)
                {
                    List<NPOIRow> npoiList = nrDatasList[i];
                    if (sheetNames.Count < i + 1)
                        break;

                    string strFileSubName = string.Format(filePath + "\\" + sheetNames[i]);
                    if (System.IO.File.Exists(strFileSubName))
                        System.IO.File.Delete(strFileSubName);

                    addRowData(npoiList, strFileSubName);
                }
            }
            catch
            {
                //continue
            }
        }

        private static void addRowData(List<NPOIRow> npoiList, string strFileSubName)
        {
            System.IO.FileStream fileStream = new System.IO.FileStream(strFileSubName, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            foreach (NPOIRow nrow in npoiList)
            {
                int iCellNum = nrow.cellValues.Count;
                StringBuilder sb = new StringBuilder();
                for (int j = 0; j < iCellNum; j++)
                {
                    object cellValue = nrow.cellValues[j];
                    if (cellValue == null)
                        cellValue = "";
                    if (cellValue.ToString().IndexOf(",") >= 0)
                    {
                        cellValue = cellValue.ToString().Replace(",", "，");
                    }

                    sb.Append(cellValue + ",");
                }
                streamWriter.WriteLine(sb.ToString());
            }
            streamWriter.Close();
            fileStream.Close();
        }

        public ChartControl GetChart(DataTable dt)
        {
            ChartControl chart = new ChartControl();

            Series[] series = new Series[Ranges.ColorRanges.Count + 1];
            for (int i = 0; i < Ranges.ColorRanges.Count + 1; ++i) // BuildColumns
            {
                Series sis = null;
                if (i == Ranges.ColorRanges.Count)
                {
                    sis = new Series(Ranges.InvalidDesc, ViewType.FullStackedBar);
                    ((FullStackedBarSeriesView)sis.View).Color = Ranges.InvalidColor;
                }
                else
                {
                    sis = new Series(Ranges.ColorRanges[i].desInfo, ViewType.FullStackedBar);
                    ((FullStackedBarSeriesView)sis.View).Color = Ranges.ColorRanges[i].color;
                }
                ((FullStackedBarSeriesView)sis.View).FillStyle.FillMode = FillMode.Solid;
                ((FullStackedBarSeriesLabel)sis.Label).Visible = false;
                ((StackedBarSeriesView)sis.View).BarWidth = 0.4;
                series[i] = sis;
            }

            for (int i = 0; i < dt.Rows.Count; ++i)
            {
                object[] row = dt.Rows[i].ItemArray;

                string argument = string.Format("{0}", row[0].ToString());
                int total = Convert.ToInt32(row[row.Length - 1]);
                for (int j = 1, k = 0; j < dt.Columns.Count - 1 && k < series.Length; ++j, ++k)
                {
                    series[k].Points.Add(new SeriesPoint(argument, total == 0 ? 0 : 1d * Convert.ToInt32(row[j]) / total));
                }
            }

            ChartTitle title = new ChartTitle();
            title.Text = dt.TableName;
            chart.Titles.Add(title);
            chart.Series.Clear();
            chart.Series.AddRange(series);
            ((XYDiagram)chart.Diagram).Rotated = true;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Format = NumericFormat.Percent;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Precision = 0;
            ((XYDiagram)chart.Diagram).AxisX.Reverse = true;
            return chart;
        }

        public LteMgrsLegendGroup GetLegend()
        {
            return Ranges.GetLegend();
        }

        private DataColumn[] BuildColumns()
        {
            List<DataColumn> columns = new List<DataColumn>();
            columns.Add(new DataColumn("网格", typeof(string)));
            for (int i = 0; i < Ranges.ColorRanges.Count; ++i)
            {
                columns.Add(new DataColumn(Ranges.ColorRanges[i].desInfo, typeof(string)));
            }
            columns.Add(new DataColumn("无效栅格", typeof(string))); // 无效栅格定义：设定频段没有频点或者频点的最强信号少于设定条件
            columns.Add(new DataColumn("栅格总数", typeof(string))); // 栅格总数定义：区域内所有栅格，包括无效栅格
            return columns.ToArray();
        }

        private NPOIRow BuildNpoiColumn()
        {
            NPOIRow nr = new NPOIRow();
            List<object> columns = new List<object>();
            columns.Add("网格");
            for (int i = 0; i < Ranges.ColorRanges.Count; ++i)
            {
                columns.Add(Ranges.ColorRanges[i].desInfo);
            }
            columns.Add("无效栅格"); // 无效栅格定义：设定频段没有频点或者频点的最强信号少于设定条件
            columns.Add("栅格总数"); // 栅格总数定义：区域内所有栅格，包括无效栅格
            nr.cellValues = columns;
            return nr;
        }

        private void AddSummary(DataTable table)
        {
            DataRow summary = table.NewRow();
            for (int i = 0; i < table.Columns.Count; ++i)
            {
                if (i == 0)
                {
                    summary[i] = sSummaryColumnName;
                    continue;
                }

                int sum = 0;
                for (int j = 1; j < table.Rows.Count; ++j )
                {
                    sum += Convert.ToInt32(table.Rows[j][i].ToString());
                }
                summary[i] = sum;
            }
            table.Rows.Add(summary);
        }

        public static LteMgrsColorRange Ranges { get; set; } = new LteMgrsCoverageColorRange();
        private static string sSummaryColumnName = "汇总(网格内)";
    }

    public class LteMgrsCoverageCondition
    {
        public double MinRsrp { get; set; }
        public double DiffRsrp { get; set; }
        public bool EnableDiff { get; set; } = true; // reserved
        public double OptionalRsrp { get; set; }
        public bool EnableOptional { get; set; }
        public bool EnableOutputSample { get; set; } = false;
        public LteMgrsCoverageBandType FreqType { get; set; }
        public string strCsvPath { get; set; } = "";
        public bool CheckTwoEarfcn { get; set; } = false;
        public bool EnableFBandType { get; set; } = false;

        public bool FilterF2 { get; set; }
    }

    public class LteMgrsCellData : IComparable<LteMgrsCellData>
    {
        public LteMgrsCell lteCell { get; set; }
        public string strVaild
        {
            get 
            {
                return FilteredByMultiBand ? "剔除" : "保留";
            }
        }
        public bool FilteredByMultiBand
        {
            get;
            private set;
        }
        public LteMgrsCellData(LteMgrsCell lteCell, bool filteredByMultiBand)
        {
            this.lteCell = lteCell;
            FilteredByMultiBand = filteredByMultiBand;
        }

        public LteMgrsCellData(LteMgrsCell lteCell)
        {
            this.lteCell = lteCell;
            FilteredByMultiBand = false;
        }

        public int CompareTo(LteMgrsCellData other)
        {
            return this.lteCell.AvgRsrp.CompareTo(other.lteCell.AvgRsrp);
        }

        /// <summary>
        /// 获取列名称
        /// </summary>
        public static NPOIRow getSampleColName()
        {
            NPOIRow nr = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("栅格编号");
            cols.Add("网格");
            cols.Add("样本数");
            cols.Add("小区距离");
            cols.Add("Earfcn");
            cols.Add("PCI");
            cols.Add("RSRP");
            cols.Add("频段");
            cols.Add("小区名称");
            cols.Add("ECI");
            cols.Add("剔除情况");
            cols.Add("中心经度");
            cols.Add("中心纬度");
            nr.cellValues = cols;
            return nr;
        }

        /// <summary>
        /// 获取数据值
        /// </summary>
        public NPOIRow getSampleColValue(LteMgrsGrid mgrsGrid, string strRegion, int iNum)
        {
            double dDist = 0;
            if (lteCell.Longitude == null || lteCell.Latitude == null || lteCell.Longitude == 0 || lteCell.Latitude == 0
                || mgrsGrid.CentLng == 0 || mgrsGrid.CentLat == 0)
                dDist = -1;
            else
            {
                dDist = MathFuncs.GetDistance((double)lteCell.Longitude, (double)lteCell.Latitude, mgrsGrid.CentLng, mgrsGrid.CentLat);
            }

            NPOIRow nr = new NPOIRow();
            List<object> values = new List<object>();
            values.Add(mgrsGrid.MgrsString);
            values.Add(strRegion);
            values.Add(iNum);
            values.Add(dDist);
            values.Add(lteCell.Earfcn);
            values.Add(lteCell.Pci);
            values.Add(lteCell.AvgRsrp);
            values.Add(lteCell.BandJT.ToString());
            values.Add(lteCell.CellName);
            values.Add(lteCell.Eci == null ? 0 : lteCell.Eci);
            values.Add(strVaild);
            values.Add(mgrsGrid.CentLng);
            values.Add(mgrsGrid.CentLat);
            nr.cellValues = values;
            return nr;
        }
    }

    public static class LteMgrsGridDetail
    {
        /// <summary>
        /// 获取列名称
        /// </summary>
        public static NPOIRow getSampleColName()
        {
            NPOIRow nr = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("栅格编号");
            cols.Add("网格");
            cols.Add("网络最强信号电平");
            cols.Add("网络与最强电平相关6dB小区数");
            cols.Add("网络重叠覆盖度");
            cols.Add("中心经度");
            cols.Add("中心纬度");
            nr.cellValues = cols;
            return nr;
        }

        /// <summary>
        /// 获取数据值
        /// </summary>
        public static NPOIRow getSampleColValue(LteMgrsGrid mgrsGrid, string strRegion, List<LteMgrsCellData> cellDataList, float retValue)
        {
            NPOIRow nr = new NPOIRow();
            List<object> values = new List<object>();
            values.Add(mgrsGrid.MgrsString);
            values.Add(strRegion);
            values.Add(cellDataList.Count > 0 ? cellDataList[0].lteCell.AvgRsrp : -105);
            values.Add(cellDataList.Count);
            values.Add(retValue);
            values.Add(mgrsGrid.CentLng);
            values.Add(mgrsGrid.CentLat);
            nr.cellValues = values;
            return nr;
        }
    }
}
