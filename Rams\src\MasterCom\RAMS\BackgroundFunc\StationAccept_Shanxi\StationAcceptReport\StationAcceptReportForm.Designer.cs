﻿namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    partial class StationAcceptReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvCell4G = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc4G = new DevExpress.XtraGrid.GridControl();
            this.gv4G = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemComboBox1 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemComboBox2 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.tabControl = new DevExpress.XtraTab.XtraTabControl();
            this.tabPage4G = new DevExpress.XtraTab.XtraTabPage();
            this.tabPage5G = new DevExpress.XtraTab.XtraTabPage();
            this.gc5G = new DevExpress.XtraGrid.GridControl();
            this.gvCell5G = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gv5G = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemComboBox3 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemComboBox4 = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.repositoryItemCheckEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell4G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gc4G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv4G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).BeginInit();
            this.tabControl.SuspendLayout();
            this.tabPage4G.SuspendLayout();
            this.tabPage5G.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gc5G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell5G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv5G)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit2)).BeginInit();
            this.SuspendLayout();
            // 
            // gvCell4G
            // 
            this.gvCell4G.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn5});
            this.gvCell4G.GridControl = this.gc4G;
            this.gvCell4G.Name = "gvCell4G";
            this.gvCell4G.OptionsBehavior.Editable = false;
            this.gvCell4G.OptionsDetail.ShowDetailTabs = false;
            this.gvCell4G.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区名";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "小区ID";
            this.gridColumn2.FieldName = "CellID";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "小区是否通过验收";
            this.gridColumn3.FieldName = "IsCellPassAcceptDesc";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "小区错误信息";
            this.gridColumn5.FieldName = "CellAcceptErrorInfo";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gc4G
            // 
            this.gc4G.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gc4G.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gc4G.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gc4G.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gc4G.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gc4G.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gvCell4G;
            gridLevelNode1.RelationName = "CellRecordInfoList";
            this.gc4G.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gc4G.Location = new System.Drawing.Point(0, 0);
            this.gc4G.MainView = this.gv4G;
            this.gc4G.Name = "gc4G";
            this.gc4G.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemComboBox1,
            this.repositoryItemComboBox2,
            this.repositoryItemCheckEdit1});
            this.gc4G.Size = new System.Drawing.Size(1006, 518);
            this.gc4G.TabIndex = 115;
            this.gc4G.UseEmbeddedNavigator = true;
            this.gc4G.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv4G,
            this.gvCell4G});
            // 
            // gv4G
            // 
            this.gv4G.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn4,
            this.gridColumn7,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn16,
            this.gridColumn19,
            this.gridColumn22,
            this.gridColumn11});
            this.gv4G.GridControl = this.gc4G;
            this.gv4G.Name = "gv4G";
            this.gv4G.OptionsBehavior.Editable = false;
            this.gv4G.OptionsDetail.ShowDetailTabs = false;
            this.gv4G.OptionsView.ColumnAutoWidth = false;
            this.gv4G.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "地市";
            this.gridColumn4.FieldName = "DistrictName";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 0;
            this.gridColumn4.Width = 61;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "基站名";
            this.gridColumn7.FieldName = "BtsName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 1;
            this.gridColumn7.Width = 166;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "基站号";
            this.gridColumn9.FieldName = "ENodeBID";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 2;
            this.gridColumn9.Width = 81;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "覆盖类型";
            this.gridColumn10.FieldName = "CoverTypeDes";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 3;
            this.gridColumn10.Width = 78;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "基站是否通过验收";
            this.gridColumn16.FieldName = "IsBtsPassAcceptDesc";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 4;
            this.gridColumn16.Width = 120;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "基站错误信息";
            this.gridColumn19.FieldName = "BtsAcceptErrorInfo";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 5;
            this.gridColumn19.Width = 159;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "Excel保存路径";
            this.gridColumn22.FieldName = "ExcelPath";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 6;
            this.gridColumn22.Width = 105;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "单验完成时间";
            this.gridColumn11.FieldName = "UpdateTime";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 7;
            this.gridColumn11.Width = 101;
            // 
            // repositoryItemComboBox1
            // 
            this.repositoryItemComboBox1.AutoHeight = false;
            this.repositoryItemComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox1.Items.AddRange(new object[] {
            "还未进行单验",
            "存在问题未导出报告",
            "已成功导出报告"});
            this.repositoryItemComboBox1.Name = "repositoryItemComboBox1";
            this.repositoryItemComboBox1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemComboBox2
            // 
            this.repositoryItemComboBox2.AutoHeight = false;
            this.repositoryItemComboBox2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox2.Items.AddRange(new object[] {
            "室内",
            "室外"});
            this.repositoryItemComboBox2.Name = "repositoryItemComboBox2";
            this.repositoryItemComboBox2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemCheckEdit1
            // 
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            // 
            // tabControl
            // 
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedTabPage = this.tabPage4G;
            this.tabControl.Size = new System.Drawing.Size(1013, 548);
            this.tabControl.TabIndex = 116;
            this.tabControl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabPage4G,
            this.tabPage5G});
            // 
            // tabPage4G
            // 
            this.tabPage4G.Controls.Add(this.gc4G);
            this.tabPage4G.Name = "tabPage4G";
            this.tabPage4G.Size = new System.Drawing.Size(1006, 518);
            this.tabPage4G.Text = "4G";
            // 
            // tabPage5G
            // 
            this.tabPage5G.Controls.Add(this.gc5G);
            this.tabPage5G.Name = "tabPage5G";
            this.tabPage5G.Size = new System.Drawing.Size(1006, 518);
            this.tabPage5G.Text = "5G";
            // 
            // gc5G
            // 
            this.gc5G.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gc5G.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gc5G.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gc5G.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gc5G.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gc5G.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode2.LevelTemplate = this.gvCell5G;
            gridLevelNode2.RelationName = "CellRecordInfoList";
            this.gc5G.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gc5G.Location = new System.Drawing.Point(0, 0);
            this.gc5G.MainView = this.gv5G;
            this.gc5G.Name = "gc5G";
            this.gc5G.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemComboBox3,
            this.repositoryItemComboBox4,
            this.repositoryItemCheckEdit2});
            this.gc5G.Size = new System.Drawing.Size(1006, 518);
            this.gc5G.TabIndex = 116;
            this.gc5G.UseEmbeddedNavigator = true;
            this.gc5G.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvCell5G,
            this.gv5G});
            // 
            // gvCell5G
            // 
            this.gvCell5G.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn6,
            this.gridColumn8,
            this.gridColumn12,
            this.gridColumn13});
            this.gvCell5G.GridControl = this.gc5G;
            this.gvCell5G.Name = "gvCell5G";
            this.gvCell5G.OptionsBehavior.Editable = false;
            this.gvCell5G.OptionsDetail.ShowDetailTabs = false;
            this.gvCell5G.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "小区名";
            this.gridColumn6.FieldName = "CellName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 0;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "小区ID";
            this.gridColumn8.FieldName = "CellID";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "小区是否通过验收";
            this.gridColumn12.FieldName = "IsCellPassAcceptDesc";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 2;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "小区错误信息";
            this.gridColumn13.FieldName = "CellAcceptErrorInfo";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 3;
            // 
            // gv5G
            // 
            this.gv5G.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn23,
            this.gridColumn24});
            this.gv5G.GridControl = this.gc5G;
            this.gv5G.Name = "gv5G";
            this.gv5G.OptionsBehavior.Editable = false;
            this.gv5G.OptionsDetail.ShowDetailTabs = false;
            this.gv5G.OptionsView.ColumnAutoWidth = false;
            this.gv5G.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "地市";
            this.gridColumn14.FieldName = "DistrictName";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 0;
            this.gridColumn14.Width = 61;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "基站名";
            this.gridColumn15.FieldName = "BtsName";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 1;
            this.gridColumn15.Width = 166;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "基站号";
            this.gridColumn17.FieldName = "ENodeBID";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 2;
            this.gridColumn17.Width = 81;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "覆盖类型";
            this.gridColumn18.FieldName = "CoverTypeDes";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 3;
            this.gridColumn18.Width = 78;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "基站是否通过验收";
            this.gridColumn20.FieldName = "IsBtsPassAcceptDesc";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 4;
            this.gridColumn20.Width = 120;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "基站错误信息";
            this.gridColumn21.FieldName = "BtsAcceptErrorInfo";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 5;
            this.gridColumn21.Width = 159;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "Excel保存路径";
            this.gridColumn23.FieldName = "ExcelPath";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 6;
            this.gridColumn23.Width = 105;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "单验完成时间";
            this.gridColumn24.FieldName = "UpdateTime";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 7;
            this.gridColumn24.Width = 101;
            // 
            // repositoryItemComboBox3
            // 
            this.repositoryItemComboBox3.AutoHeight = false;
            this.repositoryItemComboBox3.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox3.Items.AddRange(new object[] {
            "还未进行单验",
            "存在问题未导出报告",
            "已成功导出报告"});
            this.repositoryItemComboBox3.Name = "repositoryItemComboBox3";
            this.repositoryItemComboBox3.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemComboBox4
            // 
            this.repositoryItemComboBox4.AutoHeight = false;
            this.repositoryItemComboBox4.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBox4.Items.AddRange(new object[] {
            "室内",
            "室外"});
            this.repositoryItemComboBox4.Name = "repositoryItemComboBox4";
            this.repositoryItemComboBox4.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // repositoryItemCheckEdit2
            // 
            this.repositoryItemCheckEdit2.AutoHeight = false;
            this.repositoryItemCheckEdit2.Name = "repositoryItemCheckEdit2";
            // 
            // StationAcceptReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1013, 548);
            this.Controls.Add(this.tabControl);
            this.Name = "StationAcceptReportForm";
            this.Text = "单验报告查询结果";
            ((System.ComponentModel.ISupportInitialize)(this.gvCell4G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gc4G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv4G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).EndInit();
            this.tabControl.ResumeLayout(false);
            this.tabPage4G.ResumeLayout(false);
            this.tabPage5G.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gc5G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCell5G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv5G)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gc4G;
        private DevExpress.XtraGrid.Views.Grid.GridView gv4G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox2;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCell4G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraTab.XtraTabControl tabControl;
        private DevExpress.XtraTab.XtraTabPage tabPage4G;
        private DevExpress.XtraTab.XtraTabPage tabPage5G;
        private DevExpress.XtraGrid.GridControl gc5G;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCell5G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.Grid.GridView gv5G;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox3;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox repositoryItemComboBox4;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit2;
    }
}