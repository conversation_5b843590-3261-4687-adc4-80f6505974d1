﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// NR扫频模干扰查询条件
    /// </summary>
    public class NRScanModRoadCondition : ModRoadConditionBase
    {
        public double DiffRxlev { get; set; }
        public NRModInterfereCondition FilterCond { get; set; } = new NRModInterfereCondition();
    }

    /// <summary>
    /// NR扫频模干扰道路
    /// </summary>
    public class NRScanModRoadQueryExtent : ModRoadItemBase
    {
        public List<NRCell> Cells { get; private set; }
        public Dictionary<NRCell, List<NRCell>> ScanCellsDic { get; private set; }
        public int RelLevel { get; private set; }
        public int InvalidSampleCount { get; private set; }

        public NRScanModRoadQueryExtent(TestPoint firstPoint, string fileName, NRScanModRoadCondition cond)
            : base(firstPoint, fileName, cond)
        {
            cellPairCountDic = new Dictionary<string, int>();
            idCellDic = new Dictionary<int, NRCell>();
        }

        public int GetScanCellPairTime(NRCell tarCell, NRCell srcCell)
        {
            string key = string.Format("{0}_{1}", tarCell.ID, srcCell.ID);
            return cellPairCountDic.ContainsKey(key) ? cellPairCountDic[key] : 0;
        }

        protected override void ProcessInfo()
        {
            foreach (TestPoint tp in TestPoints)
            {
                ParsePoint(tp);
            }
            Cells = new List<NRCell>(idCellDic.Values);
            RelLevel = TestPoints.Count - InvalidSampleCount == 0 ? 0 : relLevelSum / (TestPoints.Count - InvalidSampleCount);

            ScanCellsDic = new Dictionary<NRCell, List<NRCell>>();
            foreach (int tarId in idCellDic.Keys)
            {
                NRCell key = idCellDic[tarId];
                List<NRCell> value = new List<NRCell>();
                foreach (int srcId in idCellDic.Keys)
                {
                    if (tarId == srcId)
                    {
                        continue;
                    }

                    string tmpKey = string.Format("{0}_{1}", tarId, srcId);
                    if (!cellPairCountDic.ContainsKey(tmpKey))
                    {
                        continue;
                    }

                    value.Add(idCellDic[srcId]);
                }
                ScanCellsDic.Add(key, value);
            }
        }

        private void ParsePoint(TestPoint tp)
        {
            float? maxRxlev = null;
            NRCell maxCell = null;

            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var i in groupDic.Values)
            {
                int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(tp, i);
                int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(tp, i);

                // 信号强度判断
                float? rxlev = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, i);
                bool flag = rxlev == null || rxlev < cond.MaxRxlev;// 无效频点
                bool isValid = judgeValidSample(i, flag);
                if (!isValid)
                {
                    break;
                }

                // 相对覆盖度处理
                isValid = judgeValidRxlev(ref maxRxlev, rxlev);
                if (!isValid)
                {
                    break;
                }

                // 小区匹配
                NRCell cell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);
                flag = cell == null;
                isValid = judgeValidSample(i, flag);
                if (!isValid)
                {
                    break;
                }

                // 小区对出现情况收集
                maxCell = getMaxCellInfo(maxCell, cell);

                // 所有小区集合
                if (!idCellDic.ContainsKey(cell.ID))
                {
                    idCellDic.Add(cell.ID, cell);
                }
            }
        }

        private bool judgeValidSample(int i, bool flag)
        {
            if (flag)
            {
                if (i == 0)
                {
                    InvalidSampleCount += 1;
                }
                return false;
            }
            return true;
        }

        private bool judgeValidRxlev(ref float? maxRxlev, float? rxlev)
        {
            if (maxRxlev == null)                       // 第一强
            {
                maxRxlev = rxlev;
                relLevelSum += 1;                       // 相对覆盖度包括第一强
            }
            else if (maxRxlev - rxlev > (cond as NRScanModRoadCondition).DiffRxlev) // 不满足相对覆盖条件，后面小区不再考虑
            {
                return false;
            }
            else
            {
                relLevelSum += 1;
            }

            return true;
        }

        private NRCell getMaxCellInfo(NRCell maxCell, NRCell cell)
        {
            if (maxCell == null)
            {
                maxCell = cell;
            }
            else
            {
                string key = string.Format("{0}_{1}", maxCell.ID, cell.ID);
                if (!cellPairCountDic.ContainsKey(key))
                {
                    cellPairCountDic.Add(key, 0);
                }
                ++cellPairCountDic[key];
            }

            return maxCell;
        }

        private readonly Dictionary<string, int> cellPairCountDic;
        private readonly Dictionary<int, NRCell> idCellDic;
        private int relLevelSum;
    }

    /// <summary>
    /// NR扫频模干扰结果分析基类
    /// 与NRScanModRoadQueryExtent关系密切
    /// </summary>
    public class NRScanModRoadQueryStater : ModRoadStaterBase
    {
        public NRScanModRoadQueryStater(MainModel mm, NRScanModRoadCondition cond) : base(mm, cond)
        {
        }

        public override object GetStatResult(object param)
        {
            NRModInterfereCondition interCond = param as NRModInterfereCondition;
            List<NRScanModRoadQueryInfo> retList = new List<NRScanModRoadQueryInfo>();
            foreach (ModRoadItemBase road in this.roadList)
            {
                NRScanModRoadQueryExtent exRoad = road as NRScanModRoadQueryExtent;
                NRScanModRoadQueryInfo roadInfo = new NRScanModRoadQueryInfo();
                roadInfo.TestPoints = exRoad.TestPoints;
                roadInfo.FileName = exRoad.FileName;
                roadInfo.Length = exRoad.Length;
                roadInfo.RelLevel = exRoad.RelLevel;
                roadInfo.CellsCount = exRoad.Cells.Count;
                roadInfo.InvalidSampleCount = exRoad.InvalidSampleCount;
                roadInfo.RoadDesc = exRoad.RoadDesc;
                roadInfo.TotalSampleCount = exRoad.TestPoints.Count;
                roadInfo.ModCells = getCellInfo(exRoad, interCond);
                roadInfo.ModCellsCount = roadInfo.ModCells.Count;
                if (roadInfo.ModCellsCount != 0)
                {
                    roadInfo.SN = retList.Count + 1;
                    retList.Add(roadInfo);
                }
            }
            return retList;
        }

        private List<ScanNRModCellInfo> getCellInfo(NRScanModRoadQueryExtent exRoad, NRModInterfereCondition interCond)
        {
            List<ScanNRModCellInfo> retList = new List<ScanNRModCellInfo>();
            Dictionary<NRCell, List<NRCell>> scanCellDic = exRoad.ScanCellsDic;
            foreach (NRCell tarCell in scanCellDic.Keys)
            {
                List<NRModInterfereCell> interCells = NRModInterferer.Instance.Stat(tarCell, scanCellDic[tarCell], interCond);
                if (interCells.Count == 0)
                {
                    continue;
                }

                ScanNRModCellInfo tarInfo = new ScanNRModCellInfo(tarCell, interCond.ModX);
                tarInfo.SrcCells = new List<ScanNRModCellInfo>();
                foreach (NRModInterfereCell srcInterCell in interCells)
                {
                    ScanNRModCellInfo srcInfo = new ScanNRModCellInfo(srcInterCell.Cell, interCond.ModX);
                    srcInfo.TarDistance = srcInterCell.Distance;
                    srcInfo.TarInterfereCount = exRoad.GetScanCellPairTime(tarCell, srcInterCell.Cell);
                    tarInfo.SrcCells.Add(srcInfo);
                }
                tarInfo.SrcCellsCount = tarInfo.SrcCells.Count;
                retList.Add(tarInfo);
            }
            return retList;
        }
    }

    /// <summary>
    /// 结果窗口显示的道路信息
    /// </summary>
    public class NRScanModRoadQueryInfo
    {
        public List<TestPoint> TestPoints { get; set; }
        public int SN { get; set; }
        public string FileName { get; set; }
        public string RoadDesc { get; set; }
        public double Length { get; set; }
        public int TotalSampleCount { get; set; }
        public int InvalidSampleCount { get; set; }
        public int CellsCount { get; set; }
        public int ModCellsCount { get; set; }
        public int RelLevel { get; set; }
        public List<ScanNRModCellInfo> ModCells { get; set; }
    }

    /// <summary>
    /// 结果窗口显示的小区信息
    /// </summary>
    public class ScanNRModCellInfo
    {
        public NRCell NRCell { get; set; }
        public string CellName { get; set; }
        public int CellID { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public int Direction { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public int SID { get; set; }
        public double TarDistance { get; set; }
        public int TarInterfereCount { get; set; }
        public List<ScanNRModCellInfo> SrcCells { get; set; }
        public int SrcCellsCount { get; set; }

        public ScanNRModCellInfo(NRCell cell, int modX)
        {
            NRCell = cell;
            CellName = cell.Name;
            CellID = cell.ID;
            Longitude = cell.Longitude;
            Latitude = cell.Latitude;
            Direction = cell.Direction;
            EARFCN = cell.SSBARFCN;
            PCI = cell.PCI;
            SID = cell.PCI % modX;
        }
    }
}
