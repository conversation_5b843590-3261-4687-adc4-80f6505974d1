﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using DevExpress.XtraTab;
using MasterCom.RAMS.Net;
using System.IO;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPIFineReportForm : MinCloseForm
    {
        public CQTKPIFineReportForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            helper = new TreeListSearchHelper(treeList);
        }
        CQTPointMapLayer layer = null;
        TimePeriod curStatPeriod = null;
        CQTKPIFineDataManager dataManager = null;
        CQTKPIReport curReport = null;
        Dictionary<string, CQTKPIFinePoint> cqtPointStatDic = null;
        public void FillData(TimePeriod statPeriod, CQTKPIFineDataManager dataMng
            , CQTKPIReport report, Dictionary<string, CQTKPIFinePoint> cqtPointStatDic)
        {
            curStatPeriod = statPeriod;
            dataManager = dataMng;
            curReport = report;
            this.cqtPointStatDic = cqtPointStatDic;
            refreshReport();
        }

        private void refreshReport()
        {
            dataManager.MakeReportData(treeList, curStatPeriod, curReport, cqtPointStatDic);
            if (treeList.VisibleColumns.Count > 0)
            {
                helper.FieldName = treeList.Columns[0].FieldName;
            }
            refreshLayer();
            if (DIYQueryCQTKPIFine.isStatCylindricity)
            {
                fillCombBoxReport(curReport);
                cbeCurColumn_SelectedValueChanged(this, EventArgs.Empty);
            }
        }

        private void refreshLayer()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            mModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CQTPointMapLayer));
            if (cLayer == null)
            {
                layer = new CQTPointMapLayer(mf.GetMapOperation(), "CQT");
                mf.AddTempCustomLayer(layer);
            }
            else
            {
                layer = cLayer as CQTPointMapLayer;
            }
            layer.CQTPoints2Show = CQTPointManager.GetInstance().CQTPoints;
            layer.IsVisible = true;
            layer.Invalidate();
        }

        private void fillCombBoxReport(CQTKPIReport report)
        {
            cbeReport.Properties.Items.Clear();
            foreach (CQTKPIReport rpt in CQTKPIReportCfgManager.GetInstance().Reports)
            {
                cbeReport.Properties.Items.Add(rpt);
            }
            cbeReport.SelectedItem = report;
        }

        private void cbeReport_SelectedValueChanged(object sender, EventArgs e)
        {
            cbeCurColumn.Properties.Items.Clear();
            if (cbeReport.SelectedItem == null)
            {
                return;
            }
            curReport = cbeReport.SelectedItem as CQTKPIReport;
            if (curReport == null)
            {
                return;
            }
            treeList.NodeCellStyle -= this.treeListCQTPointNavi_NodeCellStyle;
            dataManager.MakeReportData(treeList, curStatPeriod, curReport, cqtPointStatDic);
            foreach (CQTKPIReportColumn col in curReport.Columns)
            {
                cbeCurColumn.Properties.Items.Add(col);
            }
            foreach (CQTKPISummaryColumn sCol in curReport.SummaryColumns)
            {
                cbeCurColumn.Properties.Items.Add(sCol);
            }
            if (cbeCurColumn.Properties.Items.Count > 0)
            {
                cbeCurColumn.SelectedIndex = 0;
            }
            treeList.NodeCellStyle += this.treeListCQTPointNavi_NodeCellStyle;
        }

        private void cbeCurColumn_SelectedValueChanged(object sender, EventArgs e)
        {
            MasterCom.RAMS.CQT.CQTKPIFineDataManager.ColumnShowEventArgs eA = new MasterCom.RAMS.CQT.CQTKPIFineDataManager.ColumnShowEventArgs();
            eA.column = cbeCurColumn.SelectedItem;
            dataManager.FireGisShowColumnChange(this, eA);
            treeList.Invalidate();
            if (treeList.FocusedNode != null && treeList.FocusedNode.Tag is CQTMainPointKPIFine)
            {
                CQTMainPointKPIFine mainPoint = (CQTMainPointKPIFine)treeList.FocusedNode.Tag;
                fillTabCharts(mainPoint);
                fillFloorLengend(mainPoint);
                selTabPageByColumn(treeList.FocusedColumn.Tag);
            }
            refreshLayer();
        }


        private void treeListCQTPointNavi_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node==null)
            {
                return;
            }
            if (e.Node.Tag is CQTMainPointKPIFine)
            {
                CQTMainPointKPIFine mainPoint = e.Node.Tag as CQTMainPointKPIFine;
                fillTabCharts(mainPoint);
                fillFloorLengend(mainPoint);
                selTabPageByColumn(treeList.FocusedColumn.Tag);
            }
        }

        private void fillTabCharts(CQTMainPointKPIFine mainPoint)
        {
            grpPointInfo.Text = mainPoint.Name;
            tabControlChart.TabPages.Clear();
            foreach (CQTKPIReportColumn col in curReport.Columns)
            {
                XtraTabPage page = tabControlChart.TabPages.Add(col.Name);
                page.Tag = col;
                ChartControl chart = createChart(mainPoint, col);
                chart.Dock = DockStyle.Fill;
                page.Controls.Add(chart);
            }
            foreach (CQTKPISummaryColumn sCol in curReport.SummaryColumns)
            {
                XtraTabPage page = tabControlChart.TabPages.Add(sCol.Name);
                page.Tag = sCol;
                ChartControl chart = createChart(mainPoint, sCol);
                chart.Dock = DockStyle.Fill;
                page.Controls.Add(chart);
            }
        }

        private ChartControl createChart(CQTMainPointKPIFine mainPoint, object col)
        {
            ChartControl chart = new ChartControl();
            chart.Legend.Visible = false;
            chart.Titles.Clear();
            chart.Series.Clear();
            ChartTitle title = new ChartTitle();
            Series series = null;
            if (col is CQTKPIReportColumn)
            {
                title.Text = (col as CQTKPIReportColumn).Name;
                series = new Series(title.Text, ViewType.Bar);
                foreach (CQTSubPointKPIFine subPoint in mainPoint.SubPointNameDataDic.Values)
                {
                    double value = subPoint.ColumnKPIFineResultDic[col as CQTKPIReportColumn].KPIValue;
                    if (!double.IsNaN(value))
                    {
                        series.Points.Add(new SeriesPoint(subPoint.Name, value));
                    }
                }
            }
            else if (col is CQTKPISummaryColumn)
            {
                title.Text = (col as CQTKPISummaryColumn).Name;
                series = new Series(title.Text, ViewType.Bar);
                foreach (CQTSubPointKPIFine subPoint in mainPoint.SubPointNameDataDic.Values)
                {
                    double value = subPoint.SummaryResultDic[col as CQTKPISummaryColumn].Score;
                    if (!double.IsNaN(value))
                    {
                        series.Points.Add(new SeriesPoint(subPoint.Name, value));
                    }
                }
            }
            chart.Titles.Add(title);
            chart.Series.Add(series);
            XYDiagram diagram = chart.Diagram as XYDiagram;
            diagram.EnableAxisXScrolling = true;
            diagram.EnableAxisXZooming = true;
            diagram.AxisX.Label.Angle = mainPoint.SubPointNameDataDic.Count > 10 ? 90 : 0;
            if (series != null)
            {
                series.View.Color = Color.DodgerBlue;
                ((SideBySideBarSeriesView)series.View).FillStyle.FillMode = FillMode.Solid;
            }
            return chart;
        }

        TreeListSearchHelper helper;
        private void PerformSearch(bool forward)
        {
            helper.PerformSearch(forward);
            treeList.FocusedNode = helper.CurrentNode;
        }

        private void buttonEditSearch_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (buttonEditSearch.Text.Trim().Length > 0)
            {
                PerformSearch(e.Button.Index == 1);
            }
        }

        private void buttonEditSearch_EditValueChanged(object sender, EventArgs e)
        {
            helper.Text = buttonEditSearch.Text;
        }

        //导出信息整理
        private void treeListCQTPointNavi_NodeCellStyle(object sender, DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs e)
        {
            if (e.Node == null || e.Node.Tag == null)
            {
                return;
            }
            try
            {
                if (e.Column.Tag is CQTKPIReportColumn)
                {
                    CQTKPIReportColumn col = e.Column.Tag as CQTKPIReportColumn;
                    if (e.Node.Tag is CQTMainPointKPIFine)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTMainPointKPIFine).ColumnKPIFineResultDic[col].Color;
                    }
                    else if (e.Node.Tag is CQTSubPointKPIFine)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTSubPointKPIFine).ColumnKPIFineResultDic[col].Color;
                    }
                    else if (e.Node.Tag is CQTFileKPIFineData)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTFileKPIFineData).ColumnKPIFineResultDic[col].Color;
                    }
                    e.Appearance.BorderColor = Color.Red;
                }
                else if (e.Column.Tag is CQTKPISummaryColumn)
                {
                    CQTKPISummaryColumn col = e.Column.Tag as CQTKPISummaryColumn;
                    if (e.Node.Tag is CQTMainPointKPIFine)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTMainPointKPIFine).SummaryResultDic[col].Color;
                    }
                    else if (e.Node.Tag is CQTSubPointKPIFine)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTSubPointKPIFine).SummaryResultDic[col].Color;
                    }
                    else if (e.Node.Tag is CQTFileKPIFineData)
                    {
                        e.Appearance.BackColor = (e.Node.Tag as CQTFileKPIFineData).SummaryResultDic[col].Color;
                    }
                    e.Appearance.BorderColor = Color.Red;
                }
            }
            catch
            {
                //continue
            }
        }



        private void fillFloorLengend(CQTMainPointKPIFine mainPoint)
        {
            splitContainerControl2.Panel2.Controls.Clear();
            splitContainerControl2.Panel2.AutoScroll = true;

            string serialInfoName = "GSM RxLevSub";
            MapSerialInfo serial = DTLayerSerialManager.Instance.GetSerialByName(serialInfoName);

            List<DTFileDataManager> files = new List<DTFileDataManager>();
            foreach (MasterCom.RAMS.Model.FileInfo file in mainPoint.GetFileInfo(this.curReport.CarreerID))
            {
                foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
                {
                    if (file.ID==fileMng.FileID)
                    {
                        files.Add(fileMng);
                        break;
                    }
                }
            }

            floorImgPanel.ShowImages(files, serial);
            floorImgPanel.Parent = splitContainerControl2.Panel2;
        }

        private void treeList_FocusedColumnChanged(object sender, DevExpress.XtraTreeList.FocusedColumnChangedEventArgs e)
        {
            if (e.Column==null)
            {
                return;
            }
            selTabPageByColumn(e.Column.Tag);
        }

        private void selTabPageByColumn(object col)
        {
            if (col==null)
            {
                return;
            }
            foreach (XtraTabPage page in tabControlChart.TabPages)
            {
                if (page.Tag == col)
                {
                    tabControlChart.SelectedTabPage = page;
                    return;
                }
            }
        }

        private void buttonEditSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                PerformSearch(true);
            }
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            TreeListNode fNode = treeList.FocusedNode;
            if (fNode != null)
            {
                if (fNode.Tag is CQTMainPointKPIFine)
                {
                    CQTMainPointKPIFine mainPoint = fNode.Tag as CQTMainPointKPIFine;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    QueryCondition codition = new QueryCondition();
                    codition.DistrictID = MainModel.DistrictID;
                    codition.FileInfos = mainPoint.GetFileInfo((cbeReport.SelectedItem as CQTKPIReport).CarreerID);
                    qry.SetQueryCondition(codition);
                    qry.Query();
                }
                else if (fNode.Tag is CQTSubPointKPIFine)
                {
                    CQTSubPointKPIFine subPoint = fNode.Tag as CQTSubPointKPIFine;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    QueryCondition codition = new QueryCondition();
                    codition.DistrictID = MainModel.DistrictID;
                    codition.FileInfos = subPoint.GetFileInfo((cbeReport.SelectedItem as CQTKPIReport).CarreerID);
                    qry.SetQueryCondition(codition);
                    qry.Query();
                }
                else if (fNode.Tag is CQTFileKPIFineData)
                {
                    CQTFileKPIFineData fileData = fNode.Tag as CQTFileKPIFineData;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    QueryCondition codition = new QueryCondition();
                    codition.DistrictID = MainModel.DistrictID;
                    codition.FileInfos.Add(fileData.DataHeader);
                    qry.SetQueryCondition(codition);
                    qry.Query();
                }
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            treeList.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            treeList.CollapseAll();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            Export2Xls("");
        }

        /// <summary>
        /// 导出EXCEL
        /// </summary>
        public void Export2Xls(string exportPath)
        {
            List<CQTMainPointKPIFine> pointKPIList = new List<CQTMainPointKPIFine>();
            for (int j = 0; j < treeList.Nodes.Count; j++)
            {
                pointKPIList.Add((CQTMainPointKPIFine)treeList.Nodes[j].Tag);
            }
            List<NPOIRow> datas = new List<NPOIRow>();
            List<NPOIRow> datas2 = new List<NPOIRow>();
            List<NPOIRow> datas3 = new List<NPOIRow>();

            #region EXCEL-SHEET列表构造
            NPOIRow nrpub1 = new NPOIRow();
            NPOIRow nrpub2 = new NPOIRow();
            NPOIRow nrpub3 = new NPOIRow();
            List<object> cols1 = new List<object>();
            List<object> cols2 = new List<object>();
            List<object> cols3 = new List<object>();
            cols1.Add("主地点名称");
            cols2.Add("主地点名称");
            cols2.Add("子地点名称");
            cols3.Add("主地点名称");
            cols3.Add("子地点名称");
            cols3.Add("测试日期");
            cols3.Add("文件名称");
            for (int i = 1; i < treeList.Columns.Count; i++)
            {
                cols1.Add(treeList.Columns[i].FieldName);
                cols2.Add(treeList.Columns[i].FieldName);
                cols3.Add(treeList.Columns[i].FieldName);
            }
            if (DIYQueryCQTKPIFine.isStatByCell)
            {
                cols1.RemoveRange(1, 5);//去除无用列Lac Ci 频点 扰码 覆盖类型，地址，文件名
                cols1.RemoveRange(3, 5);   
                cols2.RemoveRange(2, 12);
            }       
            nrpub2.cellValues = cols2;
            nrpub3.cellValues = cols3;
            nrpub1.cellValues = cols1;
            datas.Add(nrpub1);
            datas2.Add(nrpub2);
            datas3.Add(nrpub3);
            
            #endregion

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();

            foreach (CQTMainPointKPIFine cellSetDetail in pointKPIList)
            {
                dealCQTMainPointKPIFine(datas, datas2, datas3, cols3, cellSetDetail);
            }

            nrDatasList.Add(datas);
            nrDatasList.Add(datas2);
            nrDatasList.Add(datas3);

            sheetNames.Add("主地点指标");
            sheetNames.Add("子地点指标");
            sheetNames.Add("文件、小区指标");
            if (exportPath != "")
            {
                if (!File.Exists(exportPath))
                {
                    Directory.CreateDirectory(exportPath);
                }
                exportPath += "\\楼层建模_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";
                ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames, exportPath);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
            }

        }

        private void dealCQTMainPointKPIFine(List<NPOIRow> datas, List<NPOIRow> datas2, List<NPOIRow> datas3, List<object> cols3, CQTMainPointKPIFine cellSetDetail)
        {
            NPOIRow nr = new NPOIRow();
            List<object> objs = new List<object>();
            objs.Add(cellSetDetail.Name.ToString());
            if (DIYQueryCQTKPIFine.isStatByCell)
            {
                objs.Add(cellSetDetail.CQTPoint.Longitude);
                objs.Add(cellSetDetail.CQTPoint.Latitude);
            }
            foreach (CQTKPIFineStatResult cqtResult in cellSetDetail.ColumnKPIFineResultDic.Values)//主地点
            {
                objs.Add(CheckData(cqtResult.KPIValue));
            }
            nr.cellValues = objs;
            datas.Add(nr);
            string pointShow = "";
            foreach (string subKPIKey in cellSetDetail.SubPointNameDataDic.Keys)//子地点
            {
                NPOIRow nr2 = new NPOIRow();
                List<object> objs2 = new List<object>();
                string pointNameShow = "";
                if (pointShow != cellSetDetail.Name.ToString())
                {
                    pointNameShow = pointShow = cellSetDetail.Name.ToString();
                }
                objs2.Add(pointNameShow);
                objs2.Add(subKPIKey);
                foreach (CQTKPIFineStatResult cqt2 in cellSetDetail.SubPointNameDataDic[subKPIKey].ColumnKPIFineResultDic.Values)
                {
                    objs2.Add(CheckData(cqt2.KPIValue));
                }
                nr2.cellValues = objs2;
                datas2.Add(nr2);
                foreach (string subKPIKey3 in cellSetDetail.SubPointNameDataDic[subKPIKey].SubPointNameDataDic.Keys)//日期
                {
                    if (cols3[4].ToString() == "LAC")
                    {
                        dealCQTStatCellKPIInfo(datas3, cols3, cellSetDetail, subKPIKey, pointNameShow, subKPIKey3);
                    }
                    else
                    {
                        dealCQTFileKPIFineData(datas3, cellSetDetail, subKPIKey, pointNameShow, subKPIKey3);
                    }
                }
            }
        }

        private void dealCQTStatCellKPIInfo(List<NPOIRow> datas3, List<object> cols3, CQTMainPointKPIFine cellSetDetail, string subKPIKey, string pointNameShow, string subKPIKey3)
        {
            string subPointShow = "";
            cols3[3] = "小区名称";
            List<CQTStatCellKPIInfo> cellList = cellSetDetail.SubPointNameDataDic[subKPIKey].SubPointNameDataDic[subKPIKey3].CarreerCellDataList;
            foreach (CQTStatCellKPIInfo cqtCellInfo in cellList)
            {
                string subPointNameShow = "";
                if (subPointShow != subKPIKey)
                {
                    subPointNameShow = subPointShow = subKPIKey;
                }
                NPOIRow nr3 = new NPOIRow();
                List<object> objs3 = new List<object>();
                objs3.Add(pointNameShow);
                objs3.Add(subPointNameShow);
                objs3.Add(subKPIKey3);
                objs3.Add(cqtCellInfo.StrCellName);
                objs3.Add(cqtCellInfo.StrLac);
                objs3.Add(cqtCellInfo.StrCi);
                objs3.Add(cqtCellInfo.Str频点);
                objs3.Add(cqtCellInfo.Str扰码);
                objs3.Add(cqtCellInfo.StrCGI);
                objs3.Add(cqtCellInfo.CellLng);
                objs3.Add(cqtCellInfo.CellLat);
                objs3.Add(cqtCellInfo.Distance);
                objs3.Add(cqtCellInfo.NetType);
                objs3.Add(cqtCellInfo.CellCoverType);
                objs3.Add(cqtCellInfo.CellAddress);
                objs3.Add(cqtCellInfo.CellFileNames);
                foreach (CQTKPIFineStatResult cqt3 in cqtCellInfo.ColumnKPIFineResultDic.Values)
                {
                    objs3.Add(CheckData(cqt3.KPIValue));
                }
                nr3.cellValues = objs3;
                datas3.Add(nr3);
            }
        }

        private void dealCQTFileKPIFineData(List<NPOIRow> datas3, CQTMainPointKPIFine cellSetDetail, string subKPIKey, string pointNameShow, string subKPIKey3)
        {
            string subPointShow = "";
            Dictionary<int, List<CQTFileKPIFineData>> dicList = cellSetDetail.SubPointNameDataDic[subKPIKey].SubPointNameDataDic[subKPIKey3].CarreerFileDataDic;
            foreach (int key4 in dicList.Keys)
            {
                foreach (CQTFileKPIFineData cqtfile in dicList[key4])
                {
                    string subPointNameShow = "";
                    if (subPointShow != subKPIKey)
                    {
                        subPointNameShow = subPointShow = subKPIKey;
                    }
                    NPOIRow nr3 = new NPOIRow();
                    List<object> objs3 = new List<object>();
                    objs3.Add(pointNameShow);
                    objs3.Add(subPointNameShow);
                    objs3.Add(subKPIKey3);
                    objs3.Add(cqtfile.DataHeader.Name);
                    foreach (CQTKPIFineStatResult cqt3 in cqtfile.ColumnKPIFineResultDic.Values)
                    {
                        objs3.Add(CheckData(cqt3.KPIValue));
                    }
                    if (cqtfile.ColumnKPIFineResultDic.Count > 0)
                    {
                        nr3.cellValues = objs3;
                        datas3.Add(nr3);
                    }
                }
            }
        }

        private object CheckData(double p)
        {
            if (double.IsNaN(p) || p == -999999 || p == 999999)
                return "-";
            return p;
        }

        

        private void btnEditReport_Click(object sender, EventArgs e)
        {
            CQTKPIReportEditForm editFrm = new CQTKPIReportEditForm();
            editFrm.FillData(CQTKPIReportCfgManager.GetInstance(), curReport);
            editFrm.ShowDialog();
            if (!CQTKPIReportCfgManager.GetInstance().Reports.Contains(curReport))
            {
                curReport=CQTKPIReportCfgManager.GetInstance().Reports[0];
            }
            treeList.NodeCellStyle -= treeListCQTPointNavi_NodeCellStyle;
            refreshReport();
            treeList.NodeCellStyle += treeListCQTPointNavi_NodeCellStyle;
        }
        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            TreeListNode fNode = treeList.FocusedNode;
            MainModel.SelCQTPoint = null;
            if (fNode != null && fNode.Tag is CQTMainPointKPIFine)
            {
                CQTMainPointKPIFine mainPoint = fNode.Tag as CQTMainPointKPIFine;
                MainModel.MainForm.GetMapForm().GoToView(mainPoint.CQTPoint.Longitude, mainPoint.CQTPoint.Latitude, 5000);
            }
        }
    }
}
