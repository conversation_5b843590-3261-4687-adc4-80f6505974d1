﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ZTNBCellMissByRegion_LTE2LTE : ZTNBCellMissByRegion
    {
        public ZTNBCellMissByRegion_LTE2LTE(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTE2LTE邻区配置核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23004, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            cellGridDic.Clear();
            CellManager.GetInstance().GetLTENBCellInfo();
            return true;
        }

        protected override void fireShowResult()
        {
            ZTNBCellMissForm frm = MainModel.CreateResultForm(typeof(ZTNBCellMissForm)) as ZTNBCellMissForm;
            frm.FillDatas(cellGridDic, InspectType.LTE2LTE);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add(DataScan_LTE.PSSRPMeanValue);
            formulaSet.Add(DataScan_LTE.PSSRPMaxValue);
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void getGridCells()
        {
            foreach (GridDataUnit grid in CurScanGridUnitMatrix)
            {
                StatDataSCAN_LTE dataScan = grid.GetStatData(typeof(StatDataSCAN_LTE)) as StatDataSCAN_LTE;
                if (dataScan == null)
                {
                    continue;
                }
                GridItem gi = new GridItem(grid.LTLng, grid.LTLat);
                foreach (int cellID in dataScan.CellInicatorDic.Keys)
                {
                    setCellGridDic(dataScan, gi, cellID);
                }
            }
        }

        private void setCellGridDic(StatDataSCAN_LTE dataScan, GridItem gi, int cellID)
        {
            if (!cellGridDic.ContainsKey(gi))
            {
                Dictionary<string, Dictionary<int, GSMCellRxLev>> serviceCellsInfo = new Dictionary<string, Dictionary<int, GSMCellRxLev>>();
                serviceCellsInfo["LTE"] = new Dictionary<int, GSMCellRxLev>();
                cellGridDic[gi] = serviceCellsInfo;
            }
            if (!cellGridDic[gi].ContainsKey("LTE"))
            {
                cellGridDic[gi]["LTE"] = new Dictionary<int, GSMCellRxLev>();
            }
            if (!cellGridDic[gi]["LTE"].ContainsKey(cellID))
            {
                cellGridDic[gi]["LTE"][cellID] = new GSMCellRxLev();
            }
            cellGridDic[gi]["LTE"][cellID].cellID = cellID;
            cellGridDic[gi]["LTE"][cellID].rxlevAvg = dataScan[cellID, DataScan_LTE.PSSRPMeanValue];
            cellGridDic[gi]["LTE"][cellID].rxlevMax = dataScan[cellID, DataScan_LTE.PSSRPMaxValue];
            double num = dataScan[cellID, DataScan_LTE.PSSRPSampleNum];
            if (!double.IsNaN(num))
            {
                cellGridDic[gi]["LTE"][cellID].sampleNum = (int)num;
            }
        }
    }
}
