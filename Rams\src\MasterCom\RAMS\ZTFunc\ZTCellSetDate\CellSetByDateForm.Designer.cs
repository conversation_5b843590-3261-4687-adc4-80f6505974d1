﻿namespace MasterCom.RAMS.Func
{
    partial class CellSetByDateForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeaderSN = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderCellType = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderLAC = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderCI = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEndTestPointCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEndTestPointTotalCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEndFileTestPointRatio = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderEndDate = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderCompareDate = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderCompareTestPointCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderCompareTestPointTotalCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderIsExist = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTestPointCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderTestPointTotalCount = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderFileTestPointRatio = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderChangeRate = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView
            // 
            this.listView.AllowColumnReorder = true;
            this.listView.AutoArrange = false;
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderName,
            this.columnHeaderCellType,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeaderEndTestPointCount,
            this.columnHeaderEndTestPointTotalCount,
            this.columnHeaderEndFileTestPointRatio,
            this.columnHeaderEndDate,
            this.columnHeaderCompareDate,
            this.columnHeaderCompareTestPointCount,
            this.columnHeaderCompareTestPointTotalCount,
            this.columnHeaderIsExist,
            this.columnHeaderTestPointCount,
            this.columnHeaderTestPointTotalCount,
            this.columnHeaderFileTestPointRatio,
            this.columnHeaderChangeRate});
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.HideSelection = false;
            this.listView.LabelWrap = false;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(1100, 510);
            this.listView.TabIndex = 3;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            this.columnHeaderSN.Width = 41;
            // 
            // columnHeaderName
            // 
            this.columnHeaderName.Text = "小区名称";
            this.columnHeaderName.Width = 119;
            // 
            // columnHeaderCellType
            // 
            this.columnHeaderCellType.Text = "小区类型";
            // 
            // columnHeaderLAC
            // 
            this.columnHeaderLAC.Text = "LAC";
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Text = "CI";
            // 
            // columnHeaderEndTestPointCount
            // 
            this.columnHeaderEndTestPointCount.Text = "底层占用采样点数";
            this.columnHeaderEndTestPointCount.Width = 89;
            // 
            // columnHeaderEndTestPointTotalCount
            // 
            this.columnHeaderEndTestPointTotalCount.Text = "底层采样点总数";
            this.columnHeaderEndTestPointTotalCount.Width = 74;
            // 
            // columnHeaderEndFileTestPointRatio
            // 
            this.columnHeaderEndFileTestPointRatio.Text = "采样点比(%)";
            this.columnHeaderEndFileTestPointRatio.Width = 84;
            // 
            // columnHeaderEndDate
            // 
            this.columnHeaderEndDate.Text = "底层时段";
            this.columnHeaderEndDate.Width = 120;
            // 
            // columnHeaderCompareDate
            // 
            this.columnHeaderCompareDate.Text = "对比时段";
            this.columnHeaderCompareDate.Width = 120;
            // 
            // columnHeaderCompareTestPointCount
            // 
            this.columnHeaderCompareTestPointCount.Text = "对比时段占用采样点数";
            // 
            // columnHeaderCompareTestPointTotalCount
            // 
            this.columnHeaderCompareTestPointTotalCount.Text = "对比采样点总数";
            // 
            // columnHeaderIsExist
            // 
            this.columnHeaderIsExist.Text = "是否在该小区集";
            // 
            // columnHeaderTestPointCount
            // 
            this.columnHeaderTestPointCount.Text = "占用采样点总数";
            // 
            // columnHeaderTestPointTotalCount
            // 
            this.columnHeaderTestPointTotalCount.Text = "采样点总数";
            // 
            // columnHeaderFileTestPointRatio
            // 
            this.columnHeaderFileTestPointRatio.Text = "占用采样点占比";
            // 
            // columnHeaderChangeRate
            // 
            this.columnHeaderChangeRate.Text = "变化趋势";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // CellSetByDateForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1100, 510);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.listView);
            this.Name = "CellSetByDateForm";
            this.Text = "时段对比小区集";
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderName;
        private System.Windows.Forms.ColumnHeader columnHeaderCellType;
        private System.Windows.Forms.ColumnHeader columnHeaderLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderCI;
        private System.Windows.Forms.ColumnHeader columnHeaderEndTestPointCount;
        private System.Windows.Forms.ColumnHeader columnHeaderEndTestPointTotalCount;
        private System.Windows.Forms.ColumnHeader columnHeaderEndFileTestPointRatio;
        private System.Windows.Forms.ColumnHeader columnHeaderEndDate;
        private System.Windows.Forms.ColumnHeader columnHeaderCompareDate;
        private System.Windows.Forms.ColumnHeader columnHeaderCompareTestPointCount;
        private System.Windows.Forms.ColumnHeader columnHeaderCompareTestPointTotalCount;
        private System.Windows.Forms.ColumnHeader columnHeaderIsExist;
        private System.Windows.Forms.ColumnHeader columnHeaderTestPointCount;
        private System.Windows.Forms.ColumnHeader columnHeaderTestPointTotalCount;
        private System.Windows.Forms.ColumnHeader columnHeaderFileTestPointRatio;
        private System.Windows.Forms.ColumnHeader columnHeaderChangeRate;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}