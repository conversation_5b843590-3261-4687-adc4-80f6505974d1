﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDWeakCoverRoadCompareForm : MinCloseForm
    {
        public TDWeakCoverRoadCompareForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }

        ZTWeakCoverRoadLayer layer = null;
        public void FillData(List<ZTWeakCoverGrid> period1Info, List<ZTWeakCoverGrid> period2RepeatGrid, List<ZTWeakCoverGrid> period2NewGrid)
        {
            gridControlPeriod1Grid.DataSource = period1Info;
            gridControlPeriod1Grid.RefreshDataSource();
            gridControlRepeatGrid.DataSource = period2RepeatGrid;
            gridControlRepeatGrid.RefreshDataSource();
            gridControlNewGrid.DataSource = period2NewGrid;
            gridControlNewGrid.RefreshDataSource();

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            MainModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(ZTWeakCoverRoadLayer));
            if (cLayer == null)
            {
                layer = new ZTWeakCoverRoadLayer(mf.GetMapOperation(), "弱覆盖路段栅格");
                mf.AddTempCustomLayer(layer);
            }
            else
            {
                layer = cLayer as ZTWeakCoverRoadLayer;
            }
            layer.Peroid1WeakGrids = period1Info;
            layer.Peroid2RepeatWeakGrids = period2RepeatGrid;
            layer.Peroid2NewWeakGrids = period2NewGrid;
            layer.Invalidate();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            if (gv==null)
            {
                return;
            }
            int[] rs = gv.GetSelectedRows();
            if (rs.Length > 0)
            {
                MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
                if (mf != null)
                {
                    TDWeakCovRoadInfo grid = gv.GetRow(rs[0]) as TDWeakCovRoadInfo;
                    if (grid != null)
                    {
                        MainModel.GetInstance().ClearDTData();
                        foreach (TestPoint tp in grid.SampleList)
                        {
                            MainModel.GetInstance().DTDataManager.Add(tp);
                        }
                        MainModel.GetInstance().FireDTDataChanged(this);
                        MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
                    }
                }
            }
        }

        private void checkPeriod1_CheckedChanged(object sender, EventArgs e)
        {
            if (layer!=null)
            {
                layer.ShowPeriod1Grid = checkPeriod1.Checked;
                layer.Invalidate();
            }
        }

        private void checkNew_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2NewGrid = checkNew.Checked;
                layer.Invalidate();
            }
        }

        private void checkRepeat_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2RepeatGrid = checkRepeat.Checked;
                layer.Invalidate();
            }
        }

        private void colorPeriod1_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period1GridColor = colorPeriod1.Color;
                layer.Invalidate();
            }
        }

        private void colorNew_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2NewGridColor = colorNew.Color;
                layer.Invalidate();
            }
        }

        private void colorRepeat_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2RepeatGridColor = colorRepeat.Color;
                layer.Invalidate();
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            exp2Xls(gridControlPeriod1Grid);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            exp2Xls(gridControlRepeatGrid);
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            exp2Xls(gridControlNewGrid);
        }

        private void exp2Xls(GridControl gridControl)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Excel2007 (*.xlsx)|*.xlsx";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    gridControl.ExportToXlsx(dlg.FileName);
                    if (XtraMessageBox.Show("导出完毕，是否打开文件？", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(dlg.FileName);
                    }
                }
                catch
                {
                    XtraMessageBox.Show("导出失败！");
                }
            }
        }
    }
}
