using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTWeakCoverConditionDlg : Form
    {
        public ZTWeakCoverConditionDlg()
        {
            InitializeComponent();
        }

        public static ZTWeakCoverConditionDlg GetDlg()
        {
            return dlg;
        }
        private static ZTWeakCoverConditionDlg dlg = new ZTWeakCoverConditionDlg();

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public int GSMRxlevSub
        {
            get { return int.Parse(numGSMRxlevSub.Value.ToString()); }
        }

        public int RscpThreshold
        {
            get { return int.Parse(numPccpchRscpThreshold.Value.ToString()); }
        }

        public int PccPchC_I
        {
            get { return int.Parse(numC_I.Value.ToString()); }
        }

        public int MinDistance
        {
            get { return int.Parse(numDistance.Value.ToString()); }
        }

        public int MaxDistance
        {
            get { return int.Parse(numMaxDistance.Value.ToString()); }
        }
    }
}