﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMultiCoverQuery_NR : ZTDiyRoadMultiCoverageQueryByRegion_TDScan
    {
        public ZTMultiCoverQuery_NR()
           : base(MainModel.GetInstance())
        {
            themeName = "NR:SS_RSRP";

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "重叠覆盖分析_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35003, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            rsrpParamInfo = DTDisplayParameterManager.GetInstance()["NR", "SS_RSRP"];
            List<object> columnsDef = NRTpHelper.InitBaseReplayParamSample(false, true);
            NRTpHelper.AddParam(columnsDef, "NR_lte_RSRP", 0);
            NRTpHelper.AddParam(columnsDef, "NR_lte_SINR", 0);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            if (xtraSetRoadfrom == null)
            {
                xtraSetRoadfrom = new XtraSetRoadMultiForm("NR");
            }
            if (xtraSetRoadfrom.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            xtraSetRoadfrom.GetSettingFilterRet(out setRxlev, out setRxlevDiff, out coFreq, out interferenceType,
                out saveTestPoint, out invalidPointRxLev);
            InitRegionMop2();
            return true;
        }

        protected override void doWithDTData(TestPoint testPoint)
        {
            string strRegionName = isContainPoint(new DbPoint(testPoint.Longitude, testPoint.Latitude));
            if (strRegionName == null) return;

            int relativeLevel = 0;
            List<string> relCovCellsName = new List<string>();
            int absoluteLevel = 0;
            List<string> absCovCellsName = new List<string>();

            int relANDabsLevel = 0;
            List<string> relANDabsCovCellsName = new List<string>();
            float maxRxlev = 0;
            string mainCellName = "";
            NRCell mainCell = null;
            Dictionary<string, NRCellCoverData> cellRscpDic = getCellList(testPoint, ref maxRxlev, ref mainCellName, ref mainCell);
            if (cellRscpDic.Count == 0)
            {
                return;
            }
            foreach (var data in cellRscpDic)
            {
                NRCell curCell = data.Value.NrCell;
                float rscp = data.Value.Rsrp;
                bool isCoFreq = IsCoFreq(mainCell, mainCellName, curCell, data.Value.Earfcn);
                if (rscp > setRxlev)//绝对覆盖度
                {
                    addCovData(ref absoluteLevel, absCovCellsName, data, isCoFreq);
                }
                if (rscp - maxRxlev > -setRxlevDiff)//相对覆盖度
                {
                    addCovData(ref relativeLevel, relCovCellsName, data, isCoFreq);
                }
                if ((rscp > setRxlev) && (rscp - maxRxlev > -setRxlevDiff))
                {
                    addCovData(ref relANDabsLevel, relANDabsCovCellsName, data, isCoFreq);
                }
            }

            bool invalidate = maxRxlev < invalidPointRxLev;
            RoadMultiCoverageInfo_TD info = new RoadMultiCoverageInfo_TD(testPoint, maxRxlev, mainCellName, relativeLevel, relCovCellsName, absoluteLevel, absCovCellsName,
            relANDabsLevel, relANDabsCovCellsName, saveTestPoint, invalidate);
            MainModel.RegionRoadMultiCoveragePoints_TD[strRegionName].Add(info);
            MainModel.RegionRoadMultiCoveragePoints_TD["全部汇总"].Add(info);
        }

        private void addCovData(ref int level, List<string> covCellsName, KeyValuePair<string, NRCellCoverData> data, bool isCoFreq)
        {
            if (!coFreq || isCoFreq)
            {
                level++;
                covCellsName.Add(data.Key);
            }
        }

        private DTDisplayParameterInfo rsrpParamInfo = null;

        /// <summary>
        /// 获取信号列表
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        private Dictionary<string, NRCellCoverData> getCellList(TestPoint testPoint, ref float maxRxlev, ref string mainCellName, ref NRCell mainCell)
        {
            Dictionary<string, NRCellCoverData> cellRscpList = new Dictionary<string, NRCellCoverData>();
            maxRxlev = float.MinValue;

            float? rxlev = NRTpHelper.NrTpManager.GetSCellRsrp(testPoint);
             mainCellName = string.Empty;
            if (rxlev != null && rxlev >= rsrpParamInfo.ValueMin && rxlev <= rsrpParamInfo.ValueMax)
            {
                int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(testPoint);
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(testPoint);
                NRCellCoverData data = getCellData(testPoint, ref maxRxlev, ref mainCellName, ref mainCell, rxlev, earfcn, pci);
                if (data != null)
                {
                    cellRscpList[data.CellName] = data;
                }
            }

            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(testPoint, i);
                if (type != NRTpHelper.NRNCellType.NCELL)
                {
                    continue;
                }

                rxlev = NRTpHelper.NrTpManager.GetNCellRsrp(testPoint, i);
                if (rxlev != null && rxlev <= rsrpParamInfo.ValueMax && rxlev >= rsrpParamInfo.ValueMin)
                {
                    int? nEarfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(testPoint, i);
                    int? nPci = (int?)NRTpHelper.NrTpManager.GetNPCI(testPoint, i);

                    NRCellCoverData data = getCellData(testPoint, ref maxRxlev, ref mainCellName, ref mainCell, rxlev, nEarfcn, nPci);
                    if (data != null)
                    {
                        cellRscpList[data.CellName] = data;
                    }
                }

            }

            return cellRscpList;
        }

        private NRCellCoverData getCellData(TestPoint testPoint, ref float maxRxlev, ref string mainCellName, ref NRCell mainCell, 
            float? rxlev, int? earfcn, int? pci)
        {
            if (earfcn != null && pci != null)
            {
                NRCellCoverData data = new NRCellCoverData();
                data.CellName = earfcn.ToString() + "_" + pci.ToString();
                data.Rsrp = (float)rxlev;
                NRCell nrCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(testPoint.DateTime, earfcn, pci, testPoint.Longitude, testPoint.Latitude);
                data.NrCell = nrCell;
                data.Earfcn = (int)earfcn;

                if (rxlev > maxRxlev)
                {
                    maxRxlev = (float)rxlev;
                    mainCellName = data.CellName;
                    mainCell = nrCell;
                }
                return data;
            }
            return null;
        }

        protected bool IsCoFreq(NRCell mainCell, string mainCellName, NRCell curCell, int earfcn)
        {
            if (mainCell != null && curCell != null && mainCell.SSBARFCN == earfcn)
            {
                return true;
            }
            return false;
        }
    }

    class NRCellCoverData
    {
        public NRCell NrCell { get; set; }
        public string CellName { get; set; }
        public float Rsrp { get; set; }
        public int Earfcn { get; set; }
    }
}
