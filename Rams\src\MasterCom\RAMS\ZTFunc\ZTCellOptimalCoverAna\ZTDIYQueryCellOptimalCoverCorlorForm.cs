﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryCellOptimalCoverCorlorForm : BaseDialog
    {
        public ZTDIYQueryCellOptimalCoverCorlorForm()
        {
            InitializeComponent();
            setColorVar();
        }
        private GridColorFixed gridColorFixed = null;
        public GridColorFixed GridColorFixed
        {
            get { return gridColorFixed; }
        }
        private void setColorVar()
        {
            lstSetColor.Items.Add("最优场强:[-90,-80);颜色三色值:(255,204,0)");
            lstSetColor.Items.Add("最优场强:[-80,-70);颜色三色值:(0,0,255)");
            lstSetColor.Items.Add("最优场强:[-70,-60);颜色三色值:(101,255,0)");
            lstSetColor.Items.Add("最优场强:[-60,-9);颜色三色值:(0,255,255)");

            gridColorFixed = new GridColorFixed();
            gridColorFixed.items = new List<GridColorFixedItem>();
            gridColorFixed.theme = "栅格2/3G最优覆盖场强均值";
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = "-90 <= 最优场强均值 < -80";
            item.color = Color.FromArgb(255, Color.FromArgb(255, 204, 0));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-80 <= 最优场强均值 < -70";
            item.color = Color.FromArgb(255, Color.FromArgb(0, 0, 255));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-70 <= 最优场强均值 < -60";
            item.color = Color.FromArgb(255, Color.FromArgb(101, 255, 0));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-60 <= 最优场强均值 < -9";
            item.color = Color.FromArgb(255, Color.FromArgb(0, 255, 255));
            gridColorFixed.items.Add(item);
        }
        /// <summary>
        /// 添加渲染颜色
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            int iNumL = 0;
            int iNumR = 0;

            if (!int.TryParse(txtLeft.Text.ToString(), out iNumL) || !int.TryParse(txtRight.Text.ToString(), out iNumR))
            {
                MessageBox.Show("设置的区间有误，请检查！");
                return;
            }
            string strColor = "最优场强:[" + txtLeft.Text.ToString() + "," + txtRight.Text.ToString()
                + ");颜色三色值:(" + colEdit.Color.R + "," + colEdit.Color.G + "," + colEdit.Color.B + ")";
            lstSetColor.Items.Add(strColor);
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = txtLeft.Text.ToString() +" <= 最优场强均值 < " + txtRight.Text.ToString();
            item.color = Color.FromArgb(255, colEdit.Color);
            gridColorFixed.items.Add(item);
        }
        /// <summary>
        /// 移除渲染颜色
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            string strSelColor = lstSetColor.SelectedItem.ToString().Split(';')[0].Split(':')[1].Replace("[","").Replace(")","");
            lstSetColor.Items.Remove(lstSetColor.SelectedItem);

            foreach (GridColorFixedItem item in gridColorFixed.items)
            {
                string strItem = item.desc.Replace(" <= 最优场强均值 < ", ",");
                if (strSelColor.Contains(strItem.Split(',')[0])
                    && strSelColor.Contains(strItem.Split(',')[1]))
                {
                    gridColorFixed.items.Remove(item);
                    break;
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }
}
