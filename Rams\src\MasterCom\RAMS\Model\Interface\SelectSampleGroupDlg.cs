﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class SelectSampleGroupDlg : BaseForm
    {
        public SelectSampleGroupDlg()
        {
            InitializeComponent();
        }

        internal DIYSampleGroup GetSelectedSampleGroup()
        {
            return cbxSampleParaGroupSel.SelectedItem as DIYSampleGroup;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (GetSelectedSampleGroup() != null)
            {
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show(this, "请选择指标内容模板！", "选择");
            }
        }

        internal void InitLoadInfo()
        {
            loadCondSettings();
            if(cbxSampleParaGroupSel.Items.Count>0)
            {
                cbxSampleParaGroupSel.SelectedIndex = 0;
            }
        }

        private void loadCondSettings()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/diysamplegroup.xml"));
                List<Object> list = configFile.GetItemValue("DIYSampleGroups", "groups") as List<Object>;
                if (list != null)
                {
                    cbxSampleParaGroupSel.Items.Clear();
                    foreach (object value in list)
                    {
                        DIYSampleGroup group = new DIYSampleGroup();
                        group.Param = value as Dictionary<string, object>;
                        cbxSampleParaGroupSel.Items.Add(group);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void btnEditGroup_Click(object sender, EventArgs e)
        {
            List<DIYSampleGroup> curGroups = new List<DIYSampleGroup>();
            foreach(DIYSampleGroup group in cbxSampleParaGroupSel.Items)
            {
                curGroups.Add(group);
            }
            EditSampleParamGroupDlg sampleEditDlg = new EditSampleParamGroupDlg();
            sampleEditDlg.FillGroups(curGroups, cbxSampleParaGroupSel.SelectedIndex);
            sampleEditDlg.ShowDialog();
            List<DIYSampleGroup> newGroups = sampleEditDlg.GetDIYSampleGroupResult();
            cbxSampleParaGroupSel.Items.Clear();
            foreach(DIYSampleGroup group in newGroups)
            {
                cbxSampleParaGroupSel.Items.Add(group);
            }
            cbxSampleParaGroupSel.SelectedItem = sampleEditDlg.CurSelGroupItem;
        }
    }
}