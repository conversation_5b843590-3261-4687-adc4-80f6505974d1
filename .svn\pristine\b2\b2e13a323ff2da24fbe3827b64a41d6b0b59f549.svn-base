﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc.ZTCluster
{
    public class ClusterManager
    {
        public int Iclusterid { get; set; }
        public string Strbandtype { get; set; }
        public List<ClusterInfoMR> ClusterinfoMR { get; set; } = new List<ClusterInfoMR>();
        public Color Color { get; set; }
        /// <summary>
        /// 小区出现次数
        /// </summary>
        public int Num { get; set; }
        //本簇中最大频率小区 -3
        public List<ClusterInfoMR> MaxCell { get; set; } = new List<ClusterInfoMR>();
    }

    public class ClusterInfoMR
    {
        /// <summary>
        /// 小区出现次数
        /// </summary>
        public int Num { get; set; }
        public int Iroundid { get; set; }
        public int Iclusterid { get; set; }
        public int Icellid { get; set; }
        public string Strcellname { get; set; }
        public string Strcellcode { get; set; }
        public int Ibcch { get; set; }
        public int Ibsic { get; set; }
        public int ItchNum { get; set; }
        public double Flongitude { get; set; }
        public double Flatitude { get; set; }
        public int Idir { get; set; }
        public string Strbandtype { get; set; }
    }

    public class ClusterKey
    {
        public int IRoundId { get; set; }
        public string StrCell { get; set; }
        public int IBcch { get; set; }
        public int IBsic { get; set; }
        public string StrNbCell { get; set; }
        public int INbBcch { get; set; }
        public int INbBsic { get; set; }
    }


    public class ClusterRound
    {
        override public string ToString()
        {
            string str = String.Empty;
            str = Dtdesc;
            str += " ("+ Dstime.ToString("yyyy-MM-dd HH:mm:ss");
            str += " - " + Detime.ToString("yyyy-MM-dd HH:mm:ss")+")";
            return str;
        }
       
        public int Iroundid { get; set; }
        public DateTime Dstime { get; set; }
        public DateTime Detime { get; set; }
        public DateTime Dimporttime { get; set; }
        public string Dtdesc { get; set; }
    }

    public class ClusterShow
    {
        public int GroupId { get; set; }
        public int Iclusterid { get; set; }
        public int CellNum { get; set; }
        public int Tchnum { get; set; }
        public double Avgtchnum { get; set; }
        public double Junhengdu { get; set; }
        public string Strcells { get; set; }
    }

    public class ClusterAna
    {
        public int Iroundid { get; set; }
        public int Ibandtype { get; set; }
        public string Strcellname { get; set; }
        public string Strcellcode { get; set; }
        public int Ibcch { get; set; }
        public int Ibsic { get; set; }
        public int Itchnum { get; set; }
        public double Radiorate { get; set; }
        public int Inum { get; set; }
        public string Strtype { get; set; }
        public string Cells { get; set; }
        public string Strldea { get; set; }
    }

    public class ClusterInterference
    {
        public int Iroundid { get; set; }
        public string Strcell { get; set; }
        public int Ibcch { get; set; }
        public int Ibsic { get; set; }
        public string Strtype { get; set; }
        public string Cells { get; set; }
        public string Iscluster { get; set; }
    }
}
