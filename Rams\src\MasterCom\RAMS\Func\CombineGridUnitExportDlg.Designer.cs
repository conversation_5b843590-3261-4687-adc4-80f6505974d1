﻿namespace MasterCom.RAMS.Func
{
    partial class CombineGridUnitExportDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.edtFile = new DevExpress.XtraEditors.ButtonEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.edtPS = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.edtCoverRadius = new DevExpress.XtraEditors.SpinEdit();
            this.edtCoverRate = new DevExpress.XtraEditors.SpinEdit();
            this.lblCoverRate1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.lblCoverRate2 = new DevExpress.XtraEditors.LabelControl();
            this.chkSetColor = new DevExpress.XtraEditors.CheckEdit();
            this.cbxColorSelect = new DevExpress.XtraEditors.ColorEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.edtFile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtPS.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtCoverRadius.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtCoverRate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSetColor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxColorSelect.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(16, 91);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(88, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "站点信息Excel：";
            // 
            // edtFile
            // 
            this.edtFile.Location = new System.Drawing.Point(106, 88);
            this.edtFile.Name = "edtFile";
            this.edtFile.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtFile.Size = new System.Drawing.Size(223, 21);
            this.edtFile.TabIndex = 1;
            this.edtFile.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtFile_ButtonClick);
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(173, 184);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(254, 184);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            // 
            // edtPS
            // 
            this.edtPS.EditValue = "";
            this.edtPS.Location = new System.Drawing.Point(16, 115);
            this.edtPS.Name = "edtPS";
            this.edtPS.Properties.ReadOnly = true;
            this.edtPS.Size = new System.Drawing.Size(313, 59);
            this.edtPS.TabIndex = 4;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(20, 10);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 5;
            this.labelControl2.Text = "站点覆盖半径：";
            // 
            // edtCoverRadius
            // 
            this.edtCoverRadius.EditValue = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.edtCoverRadius.Location = new System.Drawing.Point(106, 7);
            this.edtCoverRadius.Name = "edtCoverRadius";
            this.edtCoverRadius.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtCoverRadius.Properties.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.edtCoverRadius.Properties.IsFloatValue = false;
            this.edtCoverRadius.Properties.Mask.EditMask = "N00";
            this.edtCoverRadius.Size = new System.Drawing.Size(100, 21);
            this.edtCoverRadius.TabIndex = 6;
            // 
            // edtCoverRate
            // 
            this.edtCoverRate.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            0});
            this.edtCoverRate.Location = new System.Drawing.Point(106, 34);
            this.edtCoverRate.Name = "edtCoverRate";
            this.edtCoverRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtCoverRate.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.edtCoverRate.Properties.IsFloatValue = false;
            this.edtCoverRate.Properties.Mask.EditMask = "N00";
            this.edtCoverRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.edtCoverRate.Size = new System.Drawing.Size(100, 21);
            this.edtCoverRate.TabIndex = 8;
            // 
            // lblCoverRate1
            // 
            this.lblCoverRate1.Location = new System.Drawing.Point(20, 37);
            this.lblCoverRate1.Name = "lblCoverRate1";
            this.lblCoverRate1.Size = new System.Drawing.Size(84, 14);
            this.lblCoverRate1.TabIndex = 7;
            this.lblCoverRate1.Text = "覆盖栅格比例：";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(212, 10);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(12, 14);
            this.labelControl4.TabIndex = 9;
            this.labelControl4.Text = "米";
            // 
            // lblCoverRate2
            // 
            this.lblCoverRate2.Location = new System.Drawing.Point(212, 37);
            this.lblCoverRate2.Name = "lblCoverRate2";
            this.lblCoverRate2.Size = new System.Drawing.Size(12, 14);
            this.lblCoverRate2.TabIndex = 10;
            this.lblCoverRate2.Text = "%";
            // 
            // chkSetColor
            // 
            this.chkSetColor.Location = new System.Drawing.Point(23, 62);
            this.chkSetColor.Name = "chkSetColor";
            this.chkSetColor.Properties.Caption = "颜色定制：";
            this.chkSetColor.Size = new System.Drawing.Size(75, 19);
            this.chkSetColor.TabIndex = 11;
            this.chkSetColor.CheckedChanged += new System.EventHandler(this.chkSetColor_CheckedChanged);
            // 
            // cbxColorSelect
            // 
            this.cbxColorSelect.EditValue = System.Drawing.Color.Empty;
            this.cbxColorSelect.Location = new System.Drawing.Point(106, 61);
            this.cbxColorSelect.Name = "cbxColorSelect";
            this.cbxColorSelect.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxColorSelect.Size = new System.Drawing.Size(100, 21);
            this.cbxColorSelect.TabIndex = 12;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(212, 64);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(118, 14);
            this.labelControl3.TabIndex = 13;
            this.labelControl3.Text = "(不定制则按栅格着色)";
            // 
            // CombineGridUnitExportDlg
            // 
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(344, 215);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.cbxColorSelect);
            this.Controls.Add(this.chkSetColor);
            this.Controls.Add(this.lblCoverRate2);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.edtCoverRate);
            this.Controls.Add(this.lblCoverRate1);
            this.Controls.Add(this.edtCoverRadius);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.edtPS);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.edtFile);
            this.Controls.Add(this.labelControl1);
            this.Name = "CombineGridUnitExportDlg";
            this.Text = "栅格汇聚站点比对设置";
            ((System.ComponentModel.ISupportInitialize)(this.edtFile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtPS.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtCoverRadius.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtCoverRate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSetColor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxColorSelect.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.ButtonEdit edtFile;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.MemoEdit edtPS;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit edtCoverRadius;
        private DevExpress.XtraEditors.SpinEdit edtCoverRate;
        private DevExpress.XtraEditors.LabelControl lblCoverRate1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl lblCoverRate2;
        private DevExpress.XtraEditors.CheckEdit chkSetColor;
        private DevExpress.XtraEditors.ColorEdit cbxColorSelect;
        private DevExpress.XtraEditors.LabelControl labelControl3;
    }
}