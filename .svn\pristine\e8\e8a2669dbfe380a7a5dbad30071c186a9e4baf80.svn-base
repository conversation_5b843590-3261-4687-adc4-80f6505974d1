﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.NOP
{
    class RepeatStatQuery : QueryBase
    {
        public RepeatStatQuery()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "重复工单统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29006, this.Name);
        }
        protected override bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new RepeatStatSettingForm();
            }
            return setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在查询分析工单...", DoStatInThread);
            FireShowResult();
        }

        private void DoStatInThread()
        {
            try
            {
                RepeatStater stater = new RepeatStater();
                result = stater.Stat(setForm.GetCondition());
                this.error = null;
            }
            catch (Exception ex)
            {
                this.error = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private void FireShowResult()
        {
            if (error != null)
            {
                System.Windows.Forms.MessageBox.Show(error.Message + Environment.NewLine + error.StackTrace,
                    this.Name, System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            else
            {
                RepeatSummaryResultForm resultForm = MainModel.GetObjectFromBlackboard(
                    typeof(RepeatSummaryResultForm).FullName) as RepeatSummaryResultForm;
                if (resultForm == null || resultForm.IsDisposed)
                {
                    resultForm = new RepeatSummaryResultForm(MainModel);
                }
                resultForm.FillData(result);
                if (!resultForm.Visible)
                {
                    resultForm.Show(MainModel.MainForm);
                }
            }

            error = null;
            result = null;
        }

        Exception error = null;
        object result = null;
        RepeatStatSettingForm setForm = null;
    }
}
