﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEModXCellStater
    {
        private readonly MainModel mainModel;
        private readonly LTEModXCondition cond;

        public LTEModXCellStater(MainModel mm, LTEModXCondition cond)
        {
            this.mainModel = mm;
            this.cond = cond;
        }

        public List<LTEModXCell> GetModXCells()
        {
            List<LTEModXCell> retList = new List<LTEModXCell>();

            List<LTECell> allCells = GetAllCells();
            List<LTECell> regionCells = GetCellsInRegion(allCells);
            List<LTECell>[] mxCells = ClassifyByModX(regionCells, cond.ModX);

            foreach (int m in cond.SIDs)
            {
                List<LTEModXCell> tmpList = FindInterfere(mxCells[m % cond.ModX], cond);
                retList.AddRange(tmpList);
            }

            return retList;
        }

        private List<LTECell> GetAllCells()
        {
            List<LTECell> cellList = null;
            if (MapCellLayer.DrawCurrent)
            {
                cellList = MainModel.GetInstance().CellManager.GetCurrentLTECells();
            }
            else
            {
                cellList = MainModel.GetInstance().CellManager.GetLTECells(MapCellLayer.CurShowTimeAt);
            }

            List<LTECell> retList = new List<LTECell>();
            foreach (LTECell cell in cellList)
            {
                if (cell.Type == LTEBTSType.Outdoor)
                {
                    retList.Add(cell);
                }
            }
            return retList;
        }

        private List<LTECell> GetCellsInRegion(List<LTECell> cellList)
        {
            if (!mainModel.SearchGeometrys.IsSelectRegion())
            {
                return new List<LTECell>(cellList);
            }

            List<LTECell> retList = new List<LTECell>();
            foreach (LTECell cell in cellList)
            {
                if (mainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    retList.Add(cell);
                }
            }
            return retList;
        }

        private List<LTECell>[] ClassifyByModX(List<LTECell> cellList, int modX)
        {
            List<LTECell>[] retArray = new List<LTECell>[modX];
            for (int i = 0; i < retArray.Length; ++i)
            {
                retArray[i] = new List<LTECell>();
            }

            foreach (LTECell cell in cellList)
            {
                int mod = cell.PCI % modX;
                retArray[mod].Add(cell);
            }

            return retArray;
        }

        private List<LTEModXCell> FindInterfere(List<LTECell> cellList, LTEModXCondition cond)
        {
            List<LTEModXCell> retList = new List<LTEModXCell>();

            for (int i = 0; i < cellList.Count; ++i)
            {
                LTEModXCell mxCell = new LTEModXCell(cellList[i], cond.ModX);
                for (int j = 0; j < cellList.Count; ++j)
                {
                    addModXCells(cellList, cond, i, mxCell, j);
                }
                if (mxCell.ModXCells.Count > 0)
                {
                    retList.Add(mxCell);
                }
            }
            return retList;
        }

        private void addModXCells(List<LTECell> cellList, LTEModXCondition cond, int i, LTEModXCell mxCell, int j)
        {
            if (i != j && cellList[i].EARFCN == cellList[j].EARFCN)
            {
                double distance = GetDistance(cellList[i], cellList[j]);
                if (distance <= cond.Distance)
                {
                    bool inRange = JudgeAngle(cellList[i], cellList[j], cond.Angle);
                    if (inRange)
                    {
                        mxCell.ModXCells.Add(new LTEModXCell(cellList[j], cond.ModX, distance));
                    }
                }
            }
        }

        private double GetDistance(LTECell mainCell, LTECell otherCell)
        {
            return MathFuncs.GetDistance(mainCell.Longitude, mainCell.Latitude, otherCell.Longitude, otherCell.Latitude);
        }

        private bool JudgeAngle(LTECell mainCell, LTECell otherCell, double angleRange)
        {
            return MathFuncs.JudgePoint(mainCell.Longitude, mainCell.Latitude, otherCell.Longitude, otherCell.Latitude, mainCell.Direction, (int)angleRange);
        }

    }

    public class LTEModXCondition
    {
        public List<int> SIDs { get; set; }
        public double Distance { get; set; }
        public double Angle { get; set; }
        public int ModX { get; set; }
        public LTEModXCondition()
        {
            ModX = 3;
            SIDs = new List<int>();
        }
    }

    public class LTEModXCell
    {
        public LTECell Cell
        {
            get { return cell; }
        }

        public string CellName
        {
            get { return cell.Name; }
        }

        public int CellID
        {
            get { return cell.ID; }
        }

        public double Longitude
        {
            get { return cell.Longitude; }
        }

        public double Latitude
        {
            get { return cell.Latitude; }
        }

        public int Direction
        {
            get { return cell.Direction; }
        }

        public int PCI
        {
            get { return cell.PCI; }
        }

        public int SID
        {
            get { return cell.PCI % modX; }
        }

        public int EARFCN
        {
            get { return cell.EARFCN; }
        }

        public int MXCellCount
        {
            get { return ModXCells == null ? 0 : ModXCells.Count; }
        }

        public double Distance
        {
            get;
            private set;
        }

        public List<LTEModXCell> ModXCells
        {
            get;
            private set;
        }

        public LTEModXCell(LTECell cell, int modX)
        {
            this.cell = cell;
            this.modX = modX;
            ModXCells = new List<LTEModXCell>();
        }

        public LTEModXCell(LTECell cell, int modX, double distance)
        {
            this.cell = cell;
            this.modX = modX;
            Distance = distance;
            ModXCells = null;
        }

        private readonly LTECell cell;
        private readonly int modX;
    }
}
