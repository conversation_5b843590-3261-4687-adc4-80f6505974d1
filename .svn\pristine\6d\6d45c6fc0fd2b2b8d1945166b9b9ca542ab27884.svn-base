﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;

using MasterCom.RAMS.Model;

using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class GsmIndoorStationAcceptBase : GsmStationAcceptBase
    {
        /**
        protected void SetCellDeviceIDMap(string fileName, BTS bts)
        {
            if (bts == null)
            {
                cellIDMap = new Dictionary<int, int>();
                cellIDMap[0] = 0;
            }
            else
            {
                string deviceID = "";
                int deviceIDStart = fileName.LastIndexOf('-') + 1;
                int deviceIDLength = fileName.LastIndexOf('_') - deviceIDStart;
                if (deviceIDLength > 0)
                {
                    deviceID = fileName.Substring(deviceIDStart, deviceIDLength);
                }

                cellIDMap = new Dictionary<int, int>();
                for (int i = 0; i < bts.Cells.Count; i++)
                {
                    int cellDeviceID;
                    if (int.TryParse(bts.Cells[i].ID.ToString() + deviceID, out cellDeviceID))
                    {
                        if (!cellIDMap.ContainsKey(cellDeviceID))
                        {
                            cellIDMap[cellDeviceID] = i;
                        }
                    }
                }
            }
        }

        protected override void getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            SetCellDeviceIDMap(fileName, isSetCellIDMap ? gsmBTS : null);
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(gsmBTS.Name, result);
            }
        }
        */
    }

    /// <summary>
    /// 首页(站点验收记录单中的基础数据)
    /// </summary>
    class GsmIndoorAcpHomePage : GsmAcpHomePage
    {
        public GsmIndoorAcpHomePage()
            : base()
        {
        }
    }

    /// <summary>
    /// 首页部分的小区和对应天线信息
    /// </summary>
    class GsmIndoorAcpCellParameter : GsmAcpCellParameter
    {
        public GsmIndoorAcpCellParameter()
        {
            resultGrid = new string[3, 5, 1] {
                { 
                    { "h19" },
                    { "h20" },
                    { "h22" },
                    { "h23" },
                    { "h24" },
                },
                { 
                    { "o19" },
                    { "o20" },
                    { "o22" },
                    { "o23" },
                    { "o24" },
                },
                {  
                    { "w19" },
                    { "w20" },
                    { "w22" },
                    { "w23" },
                    { "w24" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            Cell cell = targetCell as Cell;
            GsmStationAcceptResult result;
            getResult(fileInfo.Name, cell, out result, true);
            foreach (Cell icell in cell.BelongBTS.Cells)
            {
                result.SetValue(icell.ID, 0, 0, icell.BCCH);
                result.SetValue(icell.ID, 1, 0, icell.BSIC);
                result.SetValue(icell.ID, 2, 0, icell.Altitude);
                result.SetValue(icell.ID, 3, 0, icell.Direction);
                result.SetValue(icell.ID, 4, 0, icell.Downword);//Downward拼写错误
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 1);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }
    }

    /// <summary>
    /// 语音业务(接通率,语音质量,掉话率)
    /// </summary>
    class GsmIndoorAcpVoice : GsmAcpVoice
    {
        public GsmIndoorAcpVoice()
        {
            resultGrid = new string[18, 3, 1];
            int startRow = 14;
            int offsetRow = 10;
            for (int i = 0; i < resultGrid.GetLength(0); i++)
            {
                resultGrid[i, 0, 0] = "y" + (startRow + i * offsetRow + 0).ToString();
                resultGrid[i, 1, 0] = "y" + (startRow + i * offsetRow + 1).ToString();
                resultGrid[i, 2, 0] = "y" + (startRow + i * offsetRow + 2).ToString();
            }
        }

        protected override void setCellInfo(Cell cell, FileInfo fileInfo, out VoiceInfo voiceInfo)
        {
            int id = GetCellDeviceID(cell, fileInfo.Name);
            if (!cellInfo.TryGetValue(id, out voiceInfo))
            {
                voiceInfo = new VoiceInfo();
                cellInfo.Add(id, voiceInfo);
            }
        }


        protected override int getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(gsmBTS.Name, result);
            }
            SetCellDeviceIDMap(result, fileName, isSetCellIDMap ? gsmBTS : null);
            return GetCellDeviceID(cell, fileName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("语音");
        }
    }

    /// <summary>
    /// GPRS附着成功率
    /// </summary>
    class GsmIndoorAcpGPRSAttachRate : GsmAcpGPRSAttachRate
    {
        public GsmIndoorAcpGPRSAttachRate()
        {
            resultGrid = new string[18, 1, 2];
            int startRow = 19;
            int offsetRow = 10;
            for (int i = 0; i < resultGrid.GetLength(0); i++)
            {
                resultGrid[i, 0, 0] = "y" + (startRow + i * offsetRow).ToString();
                resultGrid[i, 0, 1] = "ab" + (startRow + i * offsetRow).ToString();
            }
        }

        protected override int getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(gsmBTS.Name, result);
            }
            SetCellDeviceIDMap(result, fileName, isSetCellIDMap ? gsmBTS : null);
            return GetCellDeviceID(cell, fileName);
        }
    }

    /// <summary>
    /// PING成功率
    /// </summary>
    class GsmIndoorAcpPingRate : GsmAcpPingRate
    {
        public GsmIndoorAcpPingRate()
        {
            resultGrid = new string[18, 1, 2];
            int startRow = 20;
            int offsetRow = 10;
            for (int i = 0; i < resultGrid.GetLength(0); i++)
            {
                resultGrid[i, 0, 0] = "y" + (startRow + i * offsetRow).ToString();
                resultGrid[i, 0, 1] = "ab" + (startRow + i * offsetRow).ToString();
            }
        }

        protected override int getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(gsmBTS.Name, result);
            }
            SetCellDeviceIDMap(result, fileName, isSetCellIDMap ? gsmBTS : null);
            return GetCellDeviceID(cell, fileName);
        }
    }

    /// <summary>
    /// FTP下载
    /// </summary>
    class GsmIndoorAcpFtpDownload : GsmAcpFtpDownload
    {
        public GsmIndoorAcpFtpDownload()
        {
            resultGrid = new string[18, 1, 1];
            int startRow = 21;
            int offsetRow = 10;
            for (int i = 0; i < resultGrid.GetLength(0); i++)
            {
                resultGrid[i, 0, 0] = "y" + (startRow + i * offsetRow).ToString();
            }
        }

        protected override int getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(gsmBTS.Name, result);
            }
            SetCellDeviceIDMap(result, fileName, isSetCellIDMap ? gsmBTS : null);
            return GetCellDeviceID(cell, fileName);
        }
    }

    class GsmIndoorAcpWithInstationHandOver : GsmAcpWithInstationHandOver
    {
        public GsmIndoorAcpWithInstationHandOver()
        {
            resultGrid = new string[1, 1, 1] {
                { 
                    { "y5"},
                },
            };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站间切换") && fileInfo.Momt == (int)MoMtFile.MoFlag;
        }
    }

    class GsmIndoorAcpBetweenstationHandOver : GsmIndoorAcpWithInstationHandOver
    {
        public GsmIndoorAcpBetweenstationHandOver()
        {
            resultGrid = new string[6, 1, 1] {
                { 
                    { "y6"},
                },
                {
                     { "y7"},
                },
                {
                     { "y8"},
                },
                {
                     { "y9"},
                },
                {
                     { "y10"},
                },
                {
                     { "y11"},
                },
            };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站内切换") && fileInfo.Momt == (int)MoMtFile.MoFlag;
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            //切换成功次数
            int handoverSuccessTimes = 0;
            //切换次数
            int handoverTimes = 0;
            Cell cell = targetCell as Cell;
            //是否包含站内切换
            bool containsBetweenstationHandOver = false;
            int id = 0;
            foreach (Event evt in fileManager.Events)
            {
                if (evt.ID == 16)
                {
                    handoverTimes++;
                }
                else if (evt.ID == 17)
                {
                    handoverSuccessTimes++;
                    if (!containsBetweenstationHandOver)
                    {
                        containsBetweenstationHandOver = checkVaildCell(evt, cell.BelongBTS, ref id);
                    }
                }
            }

            if (!containsBetweenstationHandOver)
            {
                return;
            }

            //切换成功率
            string handoverSuccessRate = handoverTimes == 0 ? "" :
                Math.Round(handoverSuccessTimes * 100.0 / handoverTimes, 2).ToString() + "%";

            GsmStationAcceptResult result;
            getResult(fileInfo.Name, cell, out result, true);
            result.SetValue(id, 0, 0, handoverSuccessRate);
        }

        protected override int getResult(string fileName, Cell cell, out GsmStationAcceptResult result, bool isSetCellIDMap)
        {
            BTS gsmBTS = cell.BelongBTS;
            result = null;
            if (!btsResultDic.TryGetValue(gsmBTS.Name, out result))
            {
                result = new GsmStationAcceptResult(gsmBTS.ID, gsmBTS.Name, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(gsmBTS.Name, result);
            }
            SetCellIDMap(result, isSetCellIDMap ? gsmBTS : null);
            return 0;
        }

        protected bool checkVaildCell(Event evt, BTS bts, ref int id)
        {
            Cell beforeHandoverCell = null;
            Cell afterHandoverCell = null;
            beforeHandoverCell = (Cell)evt.GetSrcCell();
            afterHandoverCell = (Cell)evt.GetTargetCell();
            if (beforeHandoverCell != null && afterHandoverCell != null &&
                bts.Cells.Exists(x => x.ID == beforeHandoverCell.ID) && bts.Cells.Exists(x => x.ID == afterHandoverCell.ID))
            {
                id = Convert.ToInt32(beforeHandoverCell.ID.ToString() + afterHandoverCell.ID.ToString());
                return true;
            }
            else
            {
                return false;
            }
        }

        protected override void SetCellIDMap(GsmStationAcceptResult result, BTS bts)
        {
            if (bts == null)
            {
                result.CellIDMap = new Dictionary<int, int>();
                result.CellIDMap[0] = 0;
            }
            else
            {
                result.CellIDMap = new Dictionary<int, int>();
                int index = 0;
                for (int i = 0; i < bts.Cells.Count; i++)
                {
                    for (int j = 0; j < bts.Cells.Count; j++)
                    {
                        if (i == j)
                        {
                            continue;
                        }
                        int id = Convert.ToInt32(bts.Cells[i].ID.ToString() + bts.Cells[j].ID.ToString());
                        if (!result.CellIDMap.ContainsKey(id))
                        {
                            result.CellIDMap[id] = index++;
                        }
                    }
                }
            }
        }
    }


}
