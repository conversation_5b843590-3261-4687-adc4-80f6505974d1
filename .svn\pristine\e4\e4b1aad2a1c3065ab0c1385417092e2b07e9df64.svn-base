﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverTooSlowAnaByRegion : LteHandOverTooSlowAnaBase
    {
        private static LteHandOverTooSlowAnaByRegion instance = null;
        public static LteHandOverTooSlowAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHandOverTooSlowAnaByRegion(false);
                    }
                }
            }
            return instance;
        }
        public LteHandOverTooSlowAnaByRegion(bool isVoLTE)
            : base()
        {
            this.isVoLTE = isVoLTE;
        }

        public override string Name
        {
            get { return "切换过慢分析(按区域)"; }
        }

    }

    public class LteHandOverTooSlowAnaByRegion_FDD : LteHandOverTooSlowAnaBase_FDD
    {
        public LteHandOverTooSlowAnaByRegion_FDD()
            : base()
        {

        }

        public override string Name
        {
            get { return "LTE_FDD切换过慢分析(按区域)"; }
        }
    }

    public class VoLteHandOverTooSlowAnaByRegion_FDD : VoLteHandOverTooSlowAnaBase_FDD
    {
        public VoLteHandOverTooSlowAnaByRegion_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "VOLTE_FDD切换过慢分析(按区域)"; }
        }
    }
}
