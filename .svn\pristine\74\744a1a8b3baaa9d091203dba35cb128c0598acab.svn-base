﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public static class StationAcceptHelper_SX
    {
        public static QueryCondition GetQueryCondition(BackgroundFuncConfigManager backgroundConfigManager)
        {
            QueryCondition queryCond = new QueryCondition();
            DateTime startTime = backgroundConfigManager.StartTime;
            DateTime endTime = backgroundConfigManager.EndTime;
            queryCond.Periods.Add(new TimePeriod(startTime, endTime));
            queryCond.Projects = backgroundConfigManager.ProjectTypeList;
            return queryCond;
        }

        public static void BackToBackgroundWorkSheet()
        {
            MainModel model = MainModel.GetInstance();
            WorkSheet curWs = model.MainForm.getSelectedWorkSheet();
            foreach (ChildFormConfig cfc in curWs.ChildFormConfigs)
            {
                if (cfc.Text == "后台专题运行信息")
                {
                    return;
                }
            }

            foreach (WorkSheet ws in model.WorkSpace.WorkSheets)
            {
                foreach (ChildFormConfig cfc in ws.ChildFormConfigs)
                {
                    if (cfc.Text == "后台专题运行信息")
                    {
                        model.MainForm.activeChildForm(ws, cfc);
                        return;
                    }
                }
            }
        }

        //相同文件名的文件只取最新的
        public static void GetNewestFile(MainModel mainModel)
        {
            Dictionary<string, FileInfo> fileDic = new Dictionary<string, FileInfo>();
            for (int i = mainModel.FileInfos.Count - 1; i >= 0; i--)
            {
                FileInfo file = mainModel.FileInfos[i];
                if (fileDic.ContainsKey(file.Name))
                {
                    if (fileDic[file.Name].BeginTime > file.BeginTime)
                    {
                        mainModel.FileInfos.Remove(file);
                    }
                    else
                    {
                        mainModel.FileInfos.Remove(fileDic[file.Name]);
                        fileDic[file.Name] = file;
                    }
                }
                else
                {
                    fileDic.Add(file.Name, file);
                }
            }
        }

        public static void GetCellCircleTestTPs<T, U>(List<string> columns, BtsAcceptInfo_SX<T, U> btsAcceptInfo, QueryCondition queryCond, NetType type) where T : ICell
        {
            string strFilter = FileNameRuleHelper_SX.GetFileFilterStr(btsAcceptInfo.BtsName);
            queryCond.NameFilterType = FileFilterType.ByFileName;
            int orNum = 1;
            queryCond.FileName = QueryCondition.MakeFileFilterString(strFilter, ref orNum).Replace("[_]", "_");
            queryCond.FileNameOrNum = orNum;

            QueryAndReplayCirCleFileInfo tpQuery = new QueryAndReplayCirCleFileInfo(type);
            tpQuery.SetQueryCondition(queryCond);
            tpQuery.Columns = columns;
            tpQuery.Query();
            if (tpQuery.DTFiles != null)
            {
                string strTip = string.Format("读取{0}站点的环测文件共{1}个...", btsAcceptInfo.BtsName, tpQuery.DTFiles.Count);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strTip);
                foreach (DTFileDataManager dtFile in tpQuery.DTFiles)
                {
                    FileNameKey key = FileNameRuleHelper_SX.GetFileNameKey(dtFile.FileName);
                    if (key == FileNameKey.Download)
                    {
                        btsAcceptInfo.TestPointList_Dl.AddRange(dtFile.TestPoints);
                    }
                    else if (key == FileNameKey.Upload)
                    {
                        btsAcceptInfo.TestPointList_Ul.AddRange(dtFile.TestPoints);
                    }
                }
            }
        }

        public static ErrorFileType JudgeError<T>(StationAutoAcceptManager_SX<T> manager) where T : ICell
        {
            if (!manager.HasFoundSrcCell)
            {
                return ErrorFileType.NoCellFound;
            }
            else if (manager.AcceptFileInfo == null)
            {
                return ErrorFileType.NoDataAnalysed;
            }
            else if (string.IsNullOrEmpty(manager.AcceptFileInfo.FileNameKey))
            {
                return ErrorFileType.FileNameWrong;
            }
            else
            {
                return ErrorFileType.NULL;
            }
        }
    }

    public enum NetType
    {
        LTE,
        NR
    }

    public enum ErrorFileType
    {
        NULL,
        [EnumDescription("小区未找到")]//工参和文件实际测试比匹配
        NoCellFound,
        [EnumDescription("没有分析出有效的数据")]//似乎不会出现这种情况
        NoDataAnalysed,
        [EnumDescription("文件名不包含关键字")]//文件包含小区或基站名,但是没有单验相关的关键字
        FileNameWrong,
    }

    public enum AnalyseType
    {
        [EnumDescription("还未进行单验")]//单验工参已入库,但还未进行单验
        NeedAnalyse = -1,
        [EnumDescription("存在问题未导出报告")]//已经单验,但是单验出现错误,并未生产报告
        FailedAnalyse = 0,
        [EnumDescription("已成功导出报告")]//成功导出报告
        SuccessAnalyse = 1,
        [EnumDescription("已删除")]//工参导入界面删除工参
        Delete = -999
    }

    public enum StationAcceptType
    {
        [EnumDescription("仅对未单验的工参进行单验")]
        NotAccept = 0,
        [EnumDescription("仅对单验失败的工参进行单验")]
        FailedAccept = 1,
        [EnumDescription("对未单验和单验失败的工参进行单验")]
        NotAndFailedAccept = 2
    }

    /// <summary>
    /// 单验条件
    /// </summary>
    public class StationAcceptCondition_SX : StationAcceptCondition
    {
        public bool IsAnalysedNearest { get; set; }
        public int NearestDay { get; set; }
        public StationAcceptType AcceptType { get; set; }
        public string FileNameFilters { get; set; }
        public override Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["ExcelPath"] = ExcelPath,
                    ["FilePath"] = FilePath,
                    ["AcceptType"] = (int)AcceptType,
                    ["FileNameFilters"] = FileNameFilters,
                    ["IsAnalysedNearest"] = IsAnalysedNearest,
                    ["NearestDay"] = NearestDay
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        protected override void setParam(Dictionary<string, object> param)
        {
            ExcelPath = getValidValue(param, "ExcelPath", "");
            FilePath = getValidValue(param, "FilePath", "");
            AcceptType = (StationAcceptType)getValidValue(param, "AcceptType", 0);
            FileNameFilters = getValidValue(param, "FileNameFilters", "");
            IsAnalysedNearest = getValidValue(param, "IsAnalysedNearest", false);
            NearestDay = getValidValue(param, "NearestDay", 30);
        }
    }

    public class StationAcceptCondition_SX_NR : StationAcceptCondition_SX
    {
        public SqlConnectionStringBuilder SqlConnect { get; set; } = new SqlConnectionStringBuilder();

        public override Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["ExcelPath"] = ExcelPath,
                    ["FilePath"] = FilePath,
                    ["AcceptType"] = (int)AcceptType,
                    ["FileNameFilters"] = FileNameFilters,
                    ["IsAnalysedNearest"] = IsAnalysedNearest,
                    ["NearestDay"] = NearestDay,
                    ["DataSource"] = SqlConnect.DataSource,
                    ["InitialCatalog"] = SqlConnect.InitialCatalog,
                    ["User"] = SqlConnect.UserID,
                    ["PW"] = SqlConnect.Password
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        protected override void setParam(Dictionary<string, object> param)
        {
            base.setParam(param);
            SqlConnect.DataSource = getValidValue(param, "DataSource", "");
            SqlConnect.InitialCatalog = getValidValue(param, "InitialCatalog", "");
            SqlConnect.UserID = getValidValue(param, "User", "");
            SqlConnect.Password = getValidValue(param, "PW", "");
        }

        public bool JudgeConnectValid()
        {
            if (string.IsNullOrEmpty(SqlConnect.DataSource) ||
                string.IsNullOrEmpty(SqlConnect.InitialCatalog) ||
                string.IsNullOrEmpty(SqlConnect.UserID) ||
                string.IsNullOrEmpty(SqlConnect.Password))
            {
                return false;
            }
            return true;
        }
    }


    public static class ParamsHelper
    {
        public static string GetWorkParamsTableName(NetType netType)
        {
            if (netType == NetType.LTE)
            {
                return "tb_btscheck_SX_cfg_cell";
            }
            else if (netType == NetType.NR)
            {
                return "tb_btscheck_SX_cfg_cell_NR";
            }
            else
            {
                return "";
            }
        }

        public static string GetRecordTableName(NetType netType)
        {
            if (netType == NetType.LTE)
            {
                return "tb_btscheck_SX_cfg_record";
            }
            else if (netType == NetType.NR)
            {
                return "tb_btscheck_SX_cfg_record_NR";
            }
            else
            {
                return "";
            }
        }

    }
}
