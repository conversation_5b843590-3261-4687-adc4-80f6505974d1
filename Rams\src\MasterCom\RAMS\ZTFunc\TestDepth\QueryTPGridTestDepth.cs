﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.TestDepth;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryTPGridTestDepth : DIYSampleByRegion
    {
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18036, this.Name);
        }
        public QueryTPGridTestDepth()
            : base(MainModel.GetInstance())
        {

        }
        GridCondition funcCondition = null;
        protected override bool getConditionBeforeQuery()
        {
            GridPercentageSettingDlg dlg = new GridPercentageSettingDlg(funcCondition);
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                funcCondition = dlg.GetCondition();
                condition.Periods.Clear();
                condition.Periods.Add(funcCondition.HistoryPeriod);
                condition.Periods.Add(funcCondition.EstimatePeriod);
                return true;
            }
            return false;
        }
        protected override Model.Interface.DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return new Model.Interface.DIYSampleGroup();
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            historyIdxGridDic = new Dictionary<string, TestDepthTpGrid>();
            estimateIdxGridDic = new Dictionary<string, TestDepthTpGrid>();
            details = new List<TestDepthDetail>();
            summary = null;
        }

        protected override void fillContentNeeded_Sample(Package package)
        {//只查询采样点的 isampleid
            package.Content.AddParam("0,1,29,0,1,33,0,1,22,0,1,25,0,1,28,0,1,34,0,1,21,0,1,26,0,1,30,0,1,32,0,1,20,0,1,24,0,1,27,0,1,31,0,1,19,0,1,23,0,2,46,0,1,52,0,1,53,");
        }

        private bool historyTestpoint = true;

        protected override void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byround)
        {
            historyTestpoint = funcCondition.HistoryPeriod.Equals(period);
            WaitBox.Text = string.Format("正在查询{0}时间段数据...", historyTestpoint ? "基准" : "待评估");
            base.queryPeriodInfo(clientProxy, package, period, byround);
        }

        private Dictionary<string, TestDepthTpGrid> historyIdxGridDic = new Dictionary<string, TestDepthTpGrid>();
        private Dictionary<string, TestDepthTpGrid> estimateIdxGridDic = new Dictionary<string, TestDepthTpGrid>();
        protected override bool recieveAndSaveTestPoint(Package package, List<Model.Interface.ColumnDefItem> curSampleColumnDef)
        {
            TestPoint tp = getSpecificTestPoint(package);
            if (tp==null)
            {
                return false;
            }
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            package.Content.GetParamInt();//fileID
            package.Content.GetParamInt();//time
            int row, col;
            TestDepthTpGrid.GetIdx(funcCondition.GridPrecision, lng, lat, out row, out col);
            string pos = string.Format("{0}_{1}", row, col);
            TestDepthTpGrid grid = null;
            if (historyTestpoint)
            {
                if (!historyIdxGridDic.TryGetValue(pos, out grid))
                {
                    grid = new TestDepthTpGrid(funcCondition.GridPrecision, row, col);
                    historyIdxGridDic.Add(pos, grid);
                }
                grid.TestpointCount++;
            }
            else
            {
                if (!estimateIdxGridDic.TryGetValue(pos, out grid))
                {
                    grid = new TestDepthTpGrid(funcCondition.GridPrecision, row, col);
                    estimateIdxGridDic.Add(pos, grid);
                }
                grid.TestpointCount++;
            }
            return true;
        }

        private List<TestDepthDetail> details = new List<TestDepthDetail>();
        private TestDepthDetail summary = null;
        protected override void getResultAfterQuery()
        {
            details.Clear();
            WaitBox.Text = "正在分析数据...";
            if (condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)
            {//预存区域
                Dictionary<ResvRegion, List<TestDepthTpGrid>> regionHisGridDic = new Dictionary<ResvRegion, List<TestDepthTpGrid>>();
                Dictionary<ResvRegion, List<TestDepthTpGrid>> regionEstimateGridDic = new Dictionary<ResvRegion, List<TestDepthTpGrid>>();

                addRegionGridDic(historyIdxGridDic, regionHisGridDic);
                addRegionGridDic(estimateIdxGridDic, regionEstimateGridDic);

                addHisGrid(regionHisGridDic, regionEstimateGridDic);
                addEstimateGrid(regionHisGridDic, regionEstimateGridDic);
            }
            else
            {
                List<TestDepthTpGrid> hisGrids = new List<TestDepthTpGrid>();
                List<TestDepthTpGrid> estimateGrids = new List<TestDepthTpGrid>();
                addTestDepthTpGrid(hisGrids, historyIdxGridDic);
                addTestDepthTpGrid(estimateGrids, estimateIdxGridDic);
                TestDepthDetail detail = new TestDepthDetail("当前选择区域", hisGrids, estimateGrids);
                detail.MakeSummary();
                details.Add(detail);
            }

            if (details.Count > 1)
            {
                List<TestDepthTpGrid> hisGrids = new List<TestDepthTpGrid>();
                List<TestDepthTpGrid> estimateGrids = new List<TestDepthTpGrid>();
                foreach (TestDepthDetail item in details)
                {
                    hisGrids.AddRange(item.HistoryGrids);
                    estimateGrids.AddRange(item.EstimateGrids);
                }
                summary = new TestDepthDetail("汇总", hisGrids, estimateGrids);
                summary.MakeSummary();
            }
        }

        private void addHisGrid(Dictionary<ResvRegion, List<TestDepthTpGrid>> regionHisGridDic, Dictionary<ResvRegion, List<TestDepthTpGrid>> regionEstimateGridDic)
        {
            foreach (ResvRegion region in regionHisGridDic.Keys)
            {
                List<TestDepthTpGrid> hisGrids = regionHisGridDic[region];
                List<TestDepthTpGrid> estimateGrids = null;
                if (!regionEstimateGridDic.TryGetValue(region, out estimateGrids))
                {//只有历史栅格，没有待评估数据
                    estimateGrids = new List<TestDepthTpGrid>();
                }
                TestDepthDetail detail = new TestDepthDetail(region.RegionName, hisGrids, estimateGrids);
                detail.MakeSummary();
                details.Add(detail);
            }
        }

        private void addEstimateGrid(Dictionary<ResvRegion, List<TestDepthTpGrid>> regionHisGridDic, Dictionary<ResvRegion, List<TestDepthTpGrid>> regionEstimateGridDic)
        {
            //分析可能存在的：待评估栅格，没有历史栅格
            foreach (ResvRegion region in regionEstimateGridDic.Keys)
            {
                if (!regionHisGridDic.ContainsKey(region))
                {//没有历史栅格
                    List<TestDepthTpGrid> hisGrids = new List<TestDepthTpGrid>();
                    List<TestDepthTpGrid> estimateGrids = regionEstimateGridDic[region];
                    TestDepthDetail detail = new TestDepthDetail(region.RegionName, hisGrids, estimateGrids);
                    detail.MakeSummary();
                    details.Add(detail);
                }
            }
        }

        private void addRegionGridDic(Dictionary<string, TestDepthTpGrid> gridDic, Dictionary<ResvRegion, List<TestDepthTpGrid>> regionGridDic)
        {
            foreach (TestDepthTpGrid grid in gridDic.Values)
            {
                foreach (ResvRegion resvRegion in condition.Geometorys.SelectedResvRegions)
                {
                    if (resvRegion.GeoOp.CheckRectCenterInRegion(grid.Bounds))
                    {
                        List<TestDepthTpGrid> grids = null;
                        if (!regionGridDic.TryGetValue(resvRegion, out grids))
                        {
                            grids = new List<TestDepthTpGrid>();
                            regionGridDic.Add(resvRegion, grids);
                        }
                        grids.Add(grid);
                        break;//栅格只归为一个区域
                    }
                }
            }
        }

        private void addTestDepthTpGrid(List<TestDepthTpGrid> grids, Dictionary<string, TestDepthTpGrid> gridDic)
        {
            foreach (TestDepthTpGrid grid in gridDic.Values)
            {
                if (condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds))
                {
                    grids.Add(grid);
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            TestDepthResultForm frm = MainModel.GetObjectFromBlackboard(typeof(TestDepthResultForm).FullName) as TestDepthResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new TestDepthResultForm();
            }
            frm.FillData(details, summary);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            historyIdxGridDic = null;
            estimateIdxGridDic = null;
            details = null;
            summary = null;
        }

        public class GridCondition
        {
            readonly int gridSize;
            /// <summary>
            /// 栅格大小(米)
            /// </summary>
            public int GridSize
            {
                get { return gridSize; }
            }
            /// <summary>
            /// 
            /// </summary>
            /// <param name="gridSize">栅格大小(米)</param>
            /// <param name="historyPeriod">基准时间段</param>
            /// <param name="estimatePeriod">待评估时间段</param>
            public GridCondition(int gridSize,TimePeriod historyPeriod,TimePeriod estimatePeriod)
            {
                this.gridSize = gridSize;
                GridPrecision = gridSize / 100000.0;//*0.00001
                HistoryPeriod = historyPeriod;
                EstimatePeriod = estimatePeriod;
            }
            
            /// <summary>
            /// 栅格精度，如50米大小的栅格，精度为0.0005
            /// </summary>
            public double GridPrecision { get; set; }
            public TimePeriod HistoryPeriod
            {
                get;
                set;
            }

            public TimePeriod EstimatePeriod
            {
                get;
                set;
            }
        }
    }

    public class TestDepthTpGrid
    {
        public static void GetIdx(double gridPrecision, double lng, double lat, out string position)
        {
            int row, col;
            GetIdx(gridPrecision, lng, lat, out row, out col);
            position = string.Format("{0}:{1}", row, col);
        }
        public static void GetIdx(double gridPrecision, double lng, double lat, out int rowIdx, out int colIdx)
        {
            colIdx = (int)(((int)(lng * 10000)) / (gridPrecision * 10000));
            rowIdx = (int)(((int)(lat * 10000)) / (gridPrecision * 10000));
        }
        readonly double gridPrecision = 0;
        readonly private int rowIdx;
        readonly private int colIdx;
        private double ltLng = double.NaN;
        public double LTLng
        {
            get
            {
                if (double.IsNaN(ltLng) && gridPrecision != 0)
                {
                    ltLng = gridPrecision * colIdx;
                }
                return ltLng;
            }
        }
        private double ltLat = double.NaN;
        public double LTLat
        {
            get
            {
                if (double.IsNaN(ltLat) && gridPrecision != 0)
                {
                    ltLat = gridPrecision * (rowIdx + 1);
                }
                return ltLat;
            }
        }
        private double brLng = double.NaN;
        public double BRLng
        {
            get
            {
                if (double.IsNaN(brLng) && gridPrecision != 0)
                {
                    brLng = gridPrecision * (colIdx + 1);
                }
                return brLng;
            }
        }
        private double brLat = double.NaN;
        public double BRLat
        {
            get
            {
                if (double.IsNaN(brLat) && gridPrecision != 0)
                {
                    brLat = gridPrecision * rowIdx;
                }
                return brLat;
            }
        }
        public TestDepthTpGrid(double sizeFactor, int rowIdx, int colIdx)
        {
            this.gridPrecision = sizeFactor;
            this.rowIdx = rowIdx;
            this.colIdx = colIdx;
        }
        
        public int TestpointCount { get; set; }

        public bool IsInSamePosition(TestDepthTpGrid other)
        {
            return other.gridPrecision == gridPrecision && other.colIdx == colIdx && other.rowIdx == rowIdx;
        }


        public MTGis.DbRect Bounds
        {
            get
            {
                return new MTGis.DbRect(LTLng, BRLat, BRLng, LTLat);
            }
        }
    }

}
