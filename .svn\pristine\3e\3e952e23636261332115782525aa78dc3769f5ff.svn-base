﻿using System;
using System.Collections.Generic;
using System.Drawing;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;
using System.Xml;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class LteFddStationAccept : LteStationAcceptBase
    {
        public override int MaxCellCount { get { return LteFddStationAcceptManager.FddMaxCellCount; } }
        public int CurBtsCellCount { get; protected set; }

        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
        }

        protected override void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteStationAcceptResult result = btsResultDic[btsName];
            List<int> cellIDs = result.CellIDs;
            for (int i = 0; i < cellIDs.Count; ++i)
            {
                int cellId = cellIDs[i];
                if (!cellIDMap.ContainsKey(cellId))
                {
                    continue;
                }

                int index = cellIDMap[cellId];
                //避免超出长度报错
                if (index >= resultGrid.GetLength(0))
                {
                    break;
                }
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(cellId, row, col);
                        InsertExcelValue(eBook, sheetIndex, resultGrid[index, row, col], value);
                    }
                }
            }
        }

        protected virtual int setCellIDMap(ICell cell, string btsName)
        {
            //获取基站名后缀-Fdd,-Fdd900,-Fdd1800
            //string btsNameSuffix = btsName.Substring(btsName.LastIndexOf('-'));

            LTEBTS bts = (cell as LTECell).BelongBTS;
            cellIDMap = new Dictionary<int, int>();
            int index = 0;
            for (int i = 0; i < bts.Cells.Count; i++)
            {
                //考虑到共站问题,仅添加当前单验文件对应的Fdd小区
                if (bts.Cells[i].Name.Contains(btsName))
                {
                    cellIDMap[bts.Cells[i].CellID] = index++;
                }
            }
            return cellIDMap.Count;
        }
    }

    class FddAcpFtpDownload : LteFddStationAccept
    {
        public FddAcpFtpDownload()
        {
            resultGrid = new string[MaxCellCount, 3, 1];
            int idx = 15;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, targetCell, btsName);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            kpiCell.CalcResult();

            int colIndex = GetColumnIndex(fileInfo);
            if (colIndex == -1)
            {
                log.Info(string.Format("文件{0}未发现极好点关键字", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, colIndex, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("点下载");
        }

        protected virtual double GetSpeed(CellKpi kpiCell)
        {
            return kpiCell.AvgDLSpeed;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int colIndex, string btsName)
        {
            int cellID = kpiCell.LteCell.CellID;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            SetValue(result, cellID, 0, colIndex, kpiCell.AvgRsrp);
            SetValue(result, cellID, 1, colIndex, kpiCell.AvgSinr);
            SetValue(result, cellID, 2, colIndex, GetSpeed(kpiCell));
        }

        protected CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            CellKpi targetKpiCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                ICell iCell = tp.GetMainCell();
                if (iCell != null && iCell == targetCell)
                {
                    if (targetKpiCell == null)
                    {
                        targetKpiCell = new CellKpi((LTECell)targetCell);
                    }
                    targetKpiCell.AddPoint(tp);
                }
            }
            return targetKpiCell;
        }

        protected int GetColumnIndex(FileInfo fileInfo)
        {
            if (fileInfo.Name.Contains("极好点"))
            {
                return 0;
            }
            return -1;
        }

        protected void SetValue(LteStationAcceptResult result, int cellID, int rowIdx, int colIdx, double value)
        {
            // 直接覆盖原有的值
            result.SetValue(cellID, rowIdx, colIdx, value);
        }

        protected class CellKpi
        {
            public CellKpi(LTECell lteCell)
            {
                this.LteCell = lteCell;
            }

            public LTECell LteCell
            {
                get;
                private set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public double AvgULSpeed
            {
                get;
                private set;
            }

            public double AvgDLSpeed
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? rsrp;
                float? sinr;
                double? ulSpeed;
                double? dlSpeed;

                rsrp = (float?)tp["lte_RSRP"];
                sinr = (float?)tp["lte_SINR"];
                ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];

                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }
                //空闲态数据
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;
                }
                //空闲态数据
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                }
            }

            public void CalcResult()
            {
                AvgRsrp = getDivisionResult(sumRsrp, cntRsrp);
                AvgSinr = getDivisionResult(sumSinr, cntSinr);
                AvgULSpeed = getDivisionResult(sumULSpeed, cntULSpeed);
                AvgDLSpeed = getDivisionResult(sumDLSpeed, cntDLSpeed);
            }

            private double getDivisionResult(double value, int count)
            {
                if (count == 0)
                {
                    return double.MinValue;
                }
                double res = Math.Round(value / count, 2);
                return res;
            }

            private double sumRsrp;
            private int cntRsrp;

            private double sumSinr;
            private int cntSinr;

            private double sumULSpeed;
            private int cntULSpeed;

            private double sumDLSpeed;
            private int cntDLSpeed;
        }
    }

    class FddAcpFtpUpload : FddAcpFtpDownload
    {
        public FddAcpFtpUpload()
        {
            resultGrid = new string[MaxCellCount, 3, 1];
            int idx = 18;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("点上传");
        }

        protected override double GetSpeed(CellKpi kpiCell)
        {
            return kpiCell.AvgULSpeed;
        }
    }

    class FddAcpInnerHandover : LteFddStationAccept
    {
        public FddAcpInnerHandover()
        {
            evtRequList = new List<int> { 850, 898 };
            evtSuccList = new List<int> { 851, 899 };

            resultGrid = new string[1, 1, 2];
        }

        public virtual void ReSetResultGridValue(int cellCount)
        {
            int valueRowIndex;
            if (cellCount <= 3)
            {
                valueRowIndex = 58;
            }
            else if (cellCount <= 6)
            {
                valueRowIndex = 112;
            }
            else
            {
                valueRowIndex = 166;
            }

            resultGrid = new string[1, 1, 2] {
                    {
                        { "p" + valueRowIndex, "w" + valueRowIndex },
                    },
                };
        }

        protected List<int> evtSuccList;
        protected List<int> evtRequList;
        protected BtsKpi kpiCell = null;

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            cellIDMap = new Dictionary<int, int>();
            cellIDMap[0] = 0;

            kpiCell = AnaFile(fileInfo, fileManager, targetCell, btsName);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, 0, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected virtual BtsKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTECell lteCell = targetCell as LTECell;
            if (kpiCell == null)
            {
                kpiCell = new BtsKpi(lteCell.BelongBTS);
            }

            //拉远站可能存在多个切换文件,不同文件的切换次数累加
            if (lteCell.BelongBTS != kpiCell.LteBts || kpiCell.LteCellList.Contains(lteCell))
            {
                return kpiCell;
            }

            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected virtual void SaveResult(FileInfo fileInfo, BtsKpi kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteBts;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);
        }

        protected class BtsKpi
        {
            public List<LTECell> LteCellList
            {
                get;
                protected set;
            } = new List<LTECell>();

            public int RequestCnt
            {
                get;
                set;
            }

            public int SucceedCnt
            {
                get;
                set;
            }

            public int FailedCnt
            {
                get;
                set;
            }

            public LTEBTS LteBts
            {
                get;
                protected set;
            }

            public BtsKpi(LTEBTS bts)
            {
                this.LteBts = bts;
            }
        }
    }

    /// <summary>
    /// 切换图
    /// </summary>
    class FddAcpInnerHandoverPic : FddAcpCoverPicture
    {
        public FddAcpInnerHandoverPic()
        {
            resultGrid = new string[MaxCellCount, 1, 1];
            int idx = 11;
            int step = 2;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "r" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected override void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTECell lteCell = targetCell as LTECell;

            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteCell.BelongBTS.ID, btsName,
                    resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            //根据基站小区PCI重置PCI对应的图例
            string btsNameSuffix = btsName.Substring(btsName.LastIndexOf('-'));
            reSetPCIMapView(lteCell.BelongBTS, btsNameSuffix);

            AcpAutoCoverPicture picAnaFunc = AcpAutoCoverPicture.Instance;
            foreach (LTECell btsLteCell in lteCell.BelongBTS.Cells)
            {
                if (btsLteCell.Name.Contains(btsNameSuffix))
                {
                    MasterCom.MTGis.DbRect bounds = picAnaFunc.GetCoverBounds(fileManager, btsLteCell);
                    string picPath = picAnaFunc.FireMapAndTakePicByFunc("Handover", "PCI", bounds, btsLteCell.BTSName, btsLteCell.Name);

                    int cellID = btsLteCell.CellID;
                    result.SetValue(cellID, 0, 0, picPath);
                }
            }
            MainModel.GetInstance().DrawDifferentServerColor = false;
        }

        /// <summary>
        /// 重置PCI图例
        /// </summary>
        /// <param name="bts"></param>
        protected virtual void reSetPCIMapView(LTEBTS bts, string btsNameSuffix)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_PCI");
            if (msi == null)
            {
                return;
            }

            List<int> pciList;
            pciList = getPCIList(bts, btsNameSuffix);

            List<RangeInfo> ranges;
            ranges = getRangesList(pciList);

            changePCISerial(msi, ranges);

            setServerCellColorByPCI(bts, btsNameSuffix, ranges);
        }

        /// <summary>
        /// 重设pci图例,由于每个小区的PCI不同,图例需要根据pci进行变化
        /// </summary>
        /// <param name="msi"></param>
        /// <param name="ranges"></param>
        private void changePCISerial(MapSerialInfo msi, List<RangeInfo> ranges)
        {
            msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
            foreach (RangeInfo range in ranges)
            {
                DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                paramColor.MaxIncluded = range.InculdeMax;
                paramColor.MinIncluded = range.InculdeMin;
                msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
            }
        }

        private void setServerCellColorByPCI(LTEBTS bts, string btsNameSuffix, List<RangeInfo> ranges)
        {
            foreach (LTECell btsLteCell in bts.Cells)
            {
                foreach (RangeInfo range in ranges)
                {
                    if (btsLteCell.PCI >= range.Min && btsLteCell.PCI < range.Max && btsLteCell.Name.Contains(btsNameSuffix))
                    {
                        btsLteCell.ServerCellColor = range.RangeColor;
                        break;
                    }
                }
            }
        }

        private List<int> getPCIList(LTEBTS bts, string btsNameSuffix)
        {
            List<int> pciList = new List<int>();
            foreach (LTECell btsLteCell in bts.Cells)
            {
                if (btsLteCell.Name.Contains(btsNameSuffix))
                {
                    pciList.Add(btsLteCell.PCI);
                }
            }
            return pciList;
        }

        private List<RangeInfo> getRangesList(List<int> pciList)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            List<Color> colorList = new List<Color> { Color.Aqua, Color.Green, Color.Blue, Color.Yellow, Color.Fuchsia, Color.Bisque, Color.DarkOrange, Color.Silver, Color.LightGreen };
            for (int i = 0; i < pciList.Count; i++)
            {
                ranges.Add(new RangeInfo(true, false, pciList[i], pciList[i] + 1, colorList[i]));
            }
            return ranges;
        }
    }

    class FddAcpCsfbRate : FddAcpRrcRate
    {
        public FddAcpCsfbRate()
        {
            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 10;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return (fileInfo.Name.ToUpper().Contains("CSFB"));
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            AcpAutoCsfbRate.GetCsfbEventIdsByDevice(fileInfo.DeviceType, out evtRequList, out evtSuccList);

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, targetCell, btsName);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            kpiCell.FailedCnt = kpiCell.RequestCnt - kpiCell.SucceedCnt;
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }
    }

    class FddAcpRrcRate : LteFddStationAccept
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;
        protected CellKpi kpiCell = null;

        public FddAcpRrcRate()
        {
            evtRequList = new List<int>() { 855 };
            evtSuccList = new List<int>() { 856 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 5;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return (fileInfo.Name.Contains("接入") || fileInfo.Name.Contains("附着"));
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            kpiCell = AnaFile(fileInfo, fileManager, targetCell, btsName);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }

        protected virtual CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (kpiCell == null || kpiCell.LteCell != (targetCell as LTECell))
            {
                kpiCell = new CellKpi(targetCell as LTECell);
            }

            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);
        }

        protected class CellKpi
        {
            public LTECell LteCell
            {
                get;
                protected set;
            }

            public int RequestCnt
            {
                get;
                set;
            }

            public int SucceedCnt
            {
                get;
                set;
            }

            public int FailedCnt
            {
                get;
                set;
            }

            public CellKpi(LTECell lteCell)
            {
                this.LteCell = lteCell;
            }
        }
    }

    class FddAcpErabRate : FddAcpRrcRate
    {
        public FddAcpErabRate()
        {
            evtRequList = new List<int>() { 858 };
            evtSuccList = new List<int>() { 859 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 6;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }
    }

    class FddAcpAccRate : FddAcpRrcRate
    {
        public FddAcpAccRate()
        {
            evtRequList = new List<int>() { 22 };
            evtSuccList = new List<int>() { 23 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 7;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }
    }

    class FddAcp24ReselectRate : FddAcpRrcRate
    {
        public FddAcp24ReselectRate()
        {
            evtRequList = new List<int>() { 852 };
            evtSuccList = new List<int>() { 853 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 8;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("重选");
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            SwitchEventIDs(fileInfo.DeviceType);

            CellKpi kpiCell = AnaFile(fileInfo, fileManager, targetCell, btsName);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }

        protected virtual void SwitchEventIDs(int deviceType)
        {
            if (deviceType == 21 || deviceType == 8) // 中兴
            {
                evtRequList = new List<int>() { 852 };
                evtSuccList = new List<int>() { 853 };
            }
            else
            {
                evtRequList = new List<int>() { 1306 };
                evtSuccList = new List<int>() { 1306 };
            }
        }
    }

    class FddAcpCoverPicture : LteFddStationAccept
    {
        public FddAcpCoverPicture()
        {
            Init();

            resultGrid = new string[MaxCellCount, 1, 4];
            int idx = 11;
            int step = 2;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "b" + row.ToString();
                resultGrid[i, 0, 1] = "f" + row.ToString();
                resultGrid[i, 0, 2] = "j" + row.ToString();
                resultGrid[i, 0, 3] = "n" + row.ToString();
            }
        }

        protected void Init()
        {
            reSetRsrpMapView();
            reSetSinrMapView();
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            AnaFile(fileInfo, fileManager, targetCell, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("DT上传") || fileInfo.Name.ToUpper().Contains("DT下载");
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteStationAcceptResult result = btsResultDic[btsName];

            foreach (var item in cellIDMap)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    string picPath = result.GetValue(item.Key, 0, col) as string;
                    if (picPath != null)
                    {
                        AcpAutoCoverPicture.InsertExcelFDDPicture(eBook, resultGrid[item.Value, 0, col], picPath, 3, 10.79, 5.81);
                    }
                }
            }
        }

        protected virtual void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTECell lteCell = targetCell as LTECell;
            int cellID = lteCell.CellID;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteCell.BelongBTS.ID, btsName,
                    resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            AcpAutoCoverPicture picAnaFunc = AcpAutoCoverPicture.Instance;
            MasterCom.MTGis.DbRect bounds = picAnaFunc.GetCoverBounds(fileManager, lteCell);
            double nearestDistance;
            TestPoint nearestTp = picAnaFunc.GetNearestTp(fileManager.TestPoints, lteCell, out nearestDistance);

            string picPath = null;
            string type = GetBandWidth(fileInfo.Name);
            if (fileInfo.Name.ToUpper().Contains("DT上传"))
            {
                reSetULMapView(type);
                picPath = picAnaFunc.FireMapAndTakePic("PDCP_UL_Mb", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 0, 3, picPath);
            }
            else
            {
                picPath = picAnaFunc.FireMapAndTakePic("RSRP", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 0, 0, picPath);

                picPath = picAnaFunc.FireMapAndTakePic("SINR", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 0, 1, picPath);

                reSetDLMapView(type);
                picPath = picAnaFunc.FireMapAndTakePic("PDCP_DL_Mb", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 0, 2, picPath);
            }
        }

        /// <summary>
        /// 根据文件名命名规则,获取Fdd中带宽
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public string GetBandWidth(string fileName)
        {
            string bandWidth = "";
            string[] name = fileName.Split('_');
            if (name.Length >= 2)
            {
                string[] tmp = name[name.Length - 1].Split('-');
                if (tmp.Length >= 2)
                {
                    bandWidth = tmp[0];
                }
            }
            return bandWidth;
        }

        #region 重置图例
        protected virtual void reSetRsrpMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_RSRP");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
                ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
                ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
                ranges.Add(new RangeInfo(true, false, -70, -40, Color.Blue));

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        protected virtual void reSetSinrMapView()
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_SINR");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -20, 0, Color.Red));
                ranges.Add(new RangeInfo(true, false, 0, 6, Color.Orange));
                ranges.Add(new RangeInfo(true, false, 6, 12, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, 12, 18, Color.Green));
                ranges.Add(new RangeInfo(true, false, 18, 50, Color.Blue));

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        protected virtual void reSetDLMapView(string type)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_PDCP_DL_Mb");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (type)
                {
                    case "5M":
                        ranges.Add(new RangeInfo(false, false, 0, 1, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 1, 5, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 5, 10, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 10, 21, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 21, 10000, Color.Blue));
                        break;
                    case "10M":
                        ranges.Add(new RangeInfo(false, false, 0, 5, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 5, 10, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 10, 20, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 20, 42, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 42, 10000, Color.Blue));
                        break;
                    case "15M":
                        ranges.Add(new RangeInfo(false, false, 0, 5, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 5, 20, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 20, 40, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 40, 60, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 60, 10000, Color.Blue));
                        break;
                    case "20M":
                        ranges.Add(new RangeInfo(false, false, 0, 5, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 5, 20, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 20, 40, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 40, 80, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 80, 10000, Color.Blue));
                        break;
                    default:
                        break;
                }

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        protected virtual void reSetULMapView(string type)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_PDCP_UL_Mb");
            if (msi != null)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (type)
                {
                    case "5M":
                        ranges.Add(new RangeInfo(false, false, 0, 1, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 1, 2, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 2, 4, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 4, 7.5f, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 7.5f, 10000, Color.Blue));
                        break;
                    case "10M":
                        ranges.Add(new RangeInfo(false, false, 0, 1, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 1, 5, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 5, 10, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 10, 15, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 15, 10000, Color.Blue));
                        break;
                    case "15M":
                        ranges.Add(new RangeInfo(false, false, 0, 1, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 1, 5, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 5, 15, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 15, 25, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 25, 10000, Color.Blue));
                        break;
                    case "20M":
                        ranges.Add(new RangeInfo(false, false, 0, 1, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 1, 10, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 10, 20, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 20, 35, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 35, 10000, Color.Blue));
                        break;
                    default:
                        break;
                }

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        protected class RangeInfo
        {
            public RangeInfo(bool inculdeMin, bool inculdeMax, float min, float max, Color rangeColor)
            {
                Min = min;
                InculdeMin = inculdeMin;
                Max = max;
                InculdeMax = inculdeMax;
                RangeColor = rangeColor;
            }

            public float Min
            {
                get;
                private set;
            }
            public bool InculdeMin
            {
                get;
                private set;
            }
            public float Max
            {
                get;
                private set;
            }
            public bool InculdeMax
            {
                get;
                private set;
            }

            public Color RangeColor
            {
                get;
                private set;
            }
        }
        #endregion

        public override void Clear()
        {
            foreach (LteStationAcceptResult result in btsResultDic.Values)
            {
                string folderPath = AcpAutoCoverPicture.Instance.GetBtsPicFolder(result.BtsName);
                if (System.IO.Directory.Exists(folderPath))
                {
                    System.IO.Directory.Delete(folderPath, true);
                }
            }
            btsResultDic.Clear();
        }
    }

    #region 关联推送图片和数据
    /// <summary>
    /// 天资 一个站只执行一次
    /// </summary>
    class FddAcpAntennaInfo : LteFddStationAccept
    {
        private string localDirectoryName;

        public FddAcpAntennaInfo()
        {
            resultGrid = new string[1, 1, 14] {
                { 
                    { "a26","c26","a28",
                      "a31","c31","a33",
                      "a36","c36","a38","c38",
                      "a40","c40","a42","c42" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTEBTS lteBts = (targetCell as LTECell).BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            if (ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .FddStationAcceptConfigHelper>.Instance.LoadConfig())
            {
                return;
            }

            string serverPicPath = ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .FddStationAcceptConfigHelper>.Instance.ConfigInfo.ServerCoverPicPath;
            string localPicPath = ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .FddStationAcceptConfigHelper>.Instance.ConfigInfo.LocalCoverPicPath;

            string serverDirectoryName = serverPicPath + System.IO.Path.DirectorySeparatorChar + btsName;
            List<string> picPathList = initPicPath(serverDirectoryName);

            //1.从服务端下载图片到本地
            localDirectoryName = localPicPath + System.IO.Path.DirectorySeparatorChar + btsName;
            if (!System.IO.Directory.Exists(localDirectoryName))
            {
                System.IO.Directory.CreateDirectory(localDirectoryName);
            }
            DownloadStationAcceptPic query = new DownloadStationAcceptPic(picPathList, localDirectoryName);
            query.Query();

            setCellIDMap(null, btsName);
            if (!btsResultDic.TryGetValue(btsName, out var result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            else
            {
                return;
            }

            List<string> localPicPathList = initPicPath(localDirectoryName);
            setFilePath(localPicPathList, result);
        }

        private List<string> initPicPath(string directoryName)
        {
            List<string> picPath = new List<string>();
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "建筑物全景照.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "站点入口图.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "屋顶天面全景图.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "小区1.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "小区2.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "小区3.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "0.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "45.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "90.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "135.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "180.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "225.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "270.jpg");
            picPath.Add(directoryName + System.IO.Path.DirectorySeparatorChar + "315.jpg");
            return picPath;
        }

        protected void setFilePath(List<string> picPathList, LteStationAcceptResult result)
        {
            int col = 0;
            foreach (var picPath in picPathList)
            {
                if (System.IO.File.Exists(picPath))
                {
                    result.SetValue(0, 0, col, picPath);
                }
                col++;
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteStationAcceptResult result = btsResultDic[btsName];

            try
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    string picPath = result.GetValue(0, 0, col) as string;
                    if (picPath != null)
                    {
                        AcpAutoCoverPicture.InsertExcelFDDPicture(eBook, resultGrid[0, 0, col], picPath, 4, 8.85, 6.0);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override int setCellIDMap(ICell cell, string btsName)
        {
            cellIDMap = new Dictionary<int, int>();
            cellIDMap[0] = 0;
            return 1;
        }

        public override void Clear()
        {
            base.Clear();
            //删除本地图片文件,不加时延可能会导致图片加载到Excel之前被删除
            System.Threading.Thread.Sleep(1000);
            if (System.IO.Directory.Exists(localDirectoryName))
            {
                System.IO.Directory.Delete(localDirectoryName, true);
            }
        }
    }

    /// <summary>
    /// 查询其他平台推送的规划数据 一个站只执行一次
    /// </summary>
    class FddAcpCellPlanParameter : LteFddStationAccept
    {
        protected string getCharByIndex(int idx)
        {
            string res = "";
            //int id = 8;
            //int step = 8;
            //1-A, 8-H, 16-P, 27-AA, 32-AF

            int divisor = idx / 26;
            int remainder = idx % 26;

            if (divisor > 1)
            {
                res += getCharByIndex(divisor);
            }
            else
            {
                char c = (char)(96 + remainder);
                res = c.ToString();
            }

            return res;
        }

        public FddAcpCellPlanParameter()
        {
            resultGrid = new string[MaxCellCount, 20, 1];
            int colIdx = 8;
            int step = 8;
            for (int i = 0; i < MaxCellCount; i++)
            {
                resultGrid[i, 0, 0] = "e3";
                resultGrid[i, 1, 0] = "e5";
                resultGrid[i, 2, 0] = "h13";
                resultGrid[i, 3, 0] = "h14";
                resultGrid[i, 4, 0] = "h15";
                resultGrid[i, 5, 0] = "h16";

                string col = ExcelHelper.GetColNameByIndex(colIdx + step * i);
                int row = 19;
                for (int j = 6; j < 13; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                row = 31;
                for (int j = 13; j < 18; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }

                resultGrid[i, 18, 0] = col + 43;
                resultGrid[i, 19, 0] = col + 44;
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTEBTS lteBts = (targetCell as LTECell).BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            //从数据库读取规划信息
            DiyQueryFddPlanData query = new DiyQueryFddPlanData();
            query.SetCondition(lteBts.BTSID, btsName, lteBts.Type);
            query.Query();
            List<FddPlanBtsCellData> btsCellData = query.PlanBtsCellInfo;

            SaveResult(btsName, lteBts, btsCellData);
        }

        private void SaveResult(string btsName, LTEBTS lteBts, List<FddPlanBtsCellData> btsCellData)
        {
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            foreach (FddPlanBtsCellData cellData in btsCellData)
            {
                int cellID = cellData.CellID;
                //获取的规划站信息中的小区不在工参对应站的小区中则不保存结果
                if (cellIDMap.ContainsKey(cellID))
                {
                    result.SetValue(cellID, 0, 0, cellData.BtsName);
                    result.SetValue(cellID, 1, 0, cellData.ENodeBID);
                    result.SetValue(cellID, 2, 0, cellData.Longitude);
                    result.SetValue(cellID, 3, 0, cellData.Latitude);
                    result.SetValue(cellID, 4, 0, cellData.TAC);
                    result.SetValue(cellID, 5, 0, cellData.ENodeBID);

                    result.SetValue(cellID, 6, 0, cellData.CarrierInfo);
                    result.SetValue(cellID, 7, 0, cellID);
                    result.SetValue(cellID, 8, 0, cellData.PCI);
                    result.SetValue(cellID, 9, 0, cellData.FrequencyBand);
                    result.SetValue(cellID, 10, 0, cellData.Earfcn);
                    result.SetValue(cellID, 11, 0, cellData.CellBroadBand);
                    result.SetValue(cellID, 12, 0, cellData.RootSN);
                    result.SetValue(cellID, 13, 0, cellData.Altitude);
                    result.SetValue(cellID, 14, 0, cellData.Direction);
                    result.SetValue(cellID, 15, 0, cellData.Downward);
                    result.SetValue(cellID, 16, 0, cellData.Downtilt);
                    result.SetValue(cellID, 17, 0, cellData.MechanicalTilt);
                    result.SetValue(cellID, 18, 0, cellData.AntennaType);
                    result.SetValue(cellID, 19, 0, cellData.AntennaGain);
                }
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 1);
        }
    }

    /// <summary>
    /// 查询其他平台推送的实测数据 一个站只执行一次
    /// </summary>
    class FddAcpCellActualParameter : LteFddStationAccept
    {
        public FddAcpCellActualParameter()
        {
            resultGrid = new string[MaxCellCount, 18, 1];
            int colIdx = 11;
            int step = 8;
            for (int i = 0; i < MaxCellCount; i++)
            {
                resultGrid[i, 0, 0] = "n15";
                resultGrid[i, 1, 0] = "n16";
                resultGrid[i, 2, 0] = "n17";

                string col = ExcelHelper.GetColNameByIndex(colIdx + step * i);
                int row = 19;
                for (int j = 3; j < 10; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                resultGrid[i, 10, 0] = col + 29;
                resultGrid[i, 11, 0] = col + 32;
                resultGrid[i, 12, 0] = col + 35;
                row = 36;
                for (int j = 13; j < 18; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTEBTS lteBts = (targetCell as LTECell).BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                //每个站只匹配一次对应实测数据
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            //从数据库读取实测信息
            DiyQueryFddActualData query = new DiyQueryFddActualData();
            query.SetCondition(lteBts.BTSID, btsName, lteBts.Type);
            query.Query();
            List<FddActualBtsCellData> btsCellData = query.ActualBtsCellInfo;

            SaveResult(btsName, lteBts, btsCellData);
        }

        protected virtual void SaveResult(string btsName, LTEBTS lteBts, List<FddActualBtsCellData> btsCellData)
        {
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            foreach (FddActualBtsCellData cellData in btsCellData)
            {
                int cellID = cellData.CellID;
                if (!cellIDMap.ContainsKey(cellID))
                {
                    result.SetValue(cellID, 0, 0, cellData.TAC);
                    result.SetValue(cellID, 1, 0, cellData.ENodeBID);
                    result.SetValue(cellID, 2, 0, cellData.WideBand);

                    result.SetValue(cellID, 3, 0, cellData.CarrierInfo);
                    result.SetValue(cellID, 4, 0, cellID);
                    result.SetValue(cellID, 5, 0, cellData.PCI);
                    result.SetValue(cellID, 6, 0, cellData.FrequencyBand);
                    result.SetValue(cellID, 7, 0, cellData.Earfcn);
                    result.SetValue(cellID, 8, 0, cellData.CellBroadBand);
                    result.SetValue(cellID, 9, 0, cellData.RootSN);
                    result.SetValue(cellID, 10, 0, cellData.RsPower);
                    result.SetValue(cellID, 11, 0, cellData.Altitude);
                    result.SetValue(cellID, 12, 0, cellData.Downtilt);
                    result.SetValue(cellID, 13, 0, cellData.PDCCH);
                    result.SetValue(cellID, 14, 0, cellData.VOLTE);
                    result.SetValue(cellID, 15, 0, cellData.SRVCC);
                    result.SetValue(cellID, 16, 0, cellData.A2Threshold);
                    result.SetValue(cellID, 17, 0, cellData.A4Threshold);
                }
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 1);
        }
    }

    /// <summary>
    /// 查询其他平台推送的性能数据 一个站只执行一次
    /// </summary>
    class FddAcpCellPerformanceParameter : LteFddStationAccept
    {
        public FddAcpCellPerformanceParameter()
        {
            #region 性能数据
            resultGrid = new string[3, 1, 13] {
                { 
                    { "a4","b4","c4","d4","e4","f4","g4","h4","i4","j4","k4","l4","m4" },
                },
                {
                    { "a5","b5","c5","d5","e5","f5","g5","h5","i5","j5","k5","l5","m5" },
                },
                {
                    { "a6","b6","c6","d6","e6","f6","g6","h6","i6","j6","k6","l6","m6" },
                },
            };
            #endregion
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTECell lteCell = targetCell as LTECell;
            LTEBTS lteBts = lteCell.BelongBTS;
            //同一个站相同小区的ID不进行分析
            if (btsResultDic.ContainsKey(btsName))//&& btsResultDic[btsName].CellDic.ContainsKey(lteCell.CellID)
            {
                return;
            }

            string[] fileName = fileInfo.Name.Split('_');
            //log文件测试日期
            DateTime testDate = new DateTime();
            if (fileName.Length > 1 && !DateTime.TryParseExact(fileName[1], "yyyyMMdd",
                new System.Globalization.CultureInfo("zh-CN", true), System.Globalization.DateTimeStyles.None, out testDate))
            {
                return;
            }

            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            Dictionary<LTECell, FddFusionBtsCellDataResult> cellResDic = new Dictionary<LTECell, FddFusionBtsCellDataResult>();
            List<LTECell> fddCells = getFddCells(lteCell, lteBts);

            //同一个小区3天的数据取平均              
            foreach (var cell in fddCells)
            {
                //从数据库读取实测信息,查询前2天和今天的推送数据
                FddFusionBtsCellDataResult cellData = new FddFusionBtsCellDataResult();
                //Dictionary<int, List<FddFusionBtsCellData>> fusionBtsCellInfo = new Dictionary<int, List<FddFusionBtsCellData>>();
                for (int i = -2; i < 1; i++)
                {
                    string testDateStr = testDate.AddDays(i).ToString("yyyyMMdd");
                    DiyQueryFddFusionData query = new DiyQueryFddFusionData();
                    query.SetCondition(lteBts.BTSID, lteCell.CellID, testDateStr);
                    query.Query();
                    //fusionBtsCellInfo.Add(i + 1, query.FusionBtsCellInfo);
                    if (query.FusionBtsCellInfo.Count > 0)
                    {
                        cellData.Add(query.FusionBtsCellInfo[0]);
                    }
                }
                cellResDic.Add(cell, cellData);
            }

            SaveResult(btsName, lteBts, testDate, cellResDic);
        }

        protected List<LTECell> getFddCells(LTECell lteCell, LTEBTS lteBts)
        {
            //由于共站,判断小区后缀是哪种类型,只验证相同类型的小区
            string type;
            if (lteCell.Name.Contains("-FDD900-"))
            {
                type = "-FDD900-";
            }
            else if (lteCell.Name.Contains("-FDD1800-"))
            {
                type = "-FDD1800-";
            }
            else
            {
                type = "-FDD-";
            }

            List<LTECell> fddCells = new List<LTECell>();
            foreach (var cell in lteBts.Cells)
            {
                if (cell.Name.Contains(type))
                {
                    fddCells.Add(cell);
                }
            }

            return fddCells;
        }

        private void SaveResult(string btsName, LTEBTS lteBts, DateTime testDate, Dictionary<LTECell, FddFusionBtsCellDataResult> cellResDic)
        {
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            foreach (var res in cellResDic)
            {
                LTECell cell = res.Key;
                FddFusionBtsCellDataResult cellData = res.Value;

                int cellID = cell.CellID;
                result.SetValue(cellID, 0, 0, testDate.AddDays(-2).ToShortDateString() + "-" + testDate.ToShortDateString());
                result.SetValue(cellID, 0, 1, testDate.ToShortDateString());
                result.SetValue(cellID, 0, 2, cellID);
                if (cellData.DataList.Count > 0)
                {
                    result.SetValue(cellID, 0, 3, cellData.AvgRrcConnectCount);
                    result.SetValue(cellID, 0, 4, cellData.AvgRrcSuccessRate);
                    result.SetValue(cellID, 0, 5, cellData.AvgERABConnectCount);
                    result.SetValue(cellID, 0, 6, cellData.AvgERABSuccessRate);
                    result.SetValue(cellID, 0, 7, cellData.AvgWirelessRate);
                    result.SetValue(cellID, 0, 8, cellData.AvgWirelessDropRate);
                    result.SetValue(cellID, 0, 9, cellData.AvgERABDropRate);
                    result.SetValue(cellID, 0, 10, cellData.AvgHandOverSuccessRate);
                    result.SetValue(cellID, 0, 11, cellData.AvgULPDCPThroughput);
                    result.SetValue(cellID, 0, 12, cellData.AvgDLPDCPThroughput);
                }
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 5);
        }
    }

    /// <summary>
    /// 关联告警数据
    /// </summary>
    class FddAcpCellAlarm : FddAcpCellPerformanceParameter
    {
        protected Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        public FddAcpCellAlarm()
        {
            resultGrid = new string[1, 1, 3] {
                {
                    { "a39", "c39", "e39" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTECell lteCell = targetCell as LTECell;
            LTEBTS lteBts = lteCell.BelongBTS;
           
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验告警", out setting))
            {
                return;
            }

            //查询当月告警信息
            string[] fileName = fileInfo.Name.Split('_');
            //log文件测试日期
            DateTime testMonth = new DateTime();
            if (fileName.Length > 1 && !DateTime.TryParseExact(fileName[1], "yyyyMMdd",
                new System.Globalization.CultureInfo("zh-CN", true), System.Globalization.DateTimeStyles.None, out testMonth))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);
            List<LTECell> fddCells = getFddCells(lteCell, lteBts);

            Dictionary<LTECell, FddAlarmInfo> btsCellDataDic = new Dictionary<LTECell, FddAlarmInfo>();
            string testMonthStr = testMonth.ToString("yyMM");
            foreach (var cell in fddCells)
            {
                //从数据库读取告警信息
                DiyQueryFddDBAlarm query = new DiyQueryFddDBAlarm(cell, testMonthStr, setting);
                query.Query();
                if (query.DataList.Count > 0)
                {
                    btsCellDataDic.Add(cell, query.DataList[0]);
                }
            }

            InitResultGrid(btsCellDataDic.Count);

            SaveResult(btsName, lteBts, btsCellDataDic);
        }

        private void SaveResult(string btsName, LTEBTS lteBts, Dictionary<LTECell, FddAlarmInfo> btsCellDataDic)
        {
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            foreach (var res in btsCellDataDic)
            {
                LTECell cell = res.Key;
                FddAlarmInfo cellData = res.Value;
                int cellID = cell.CellID;

                result.SetValue(cellID, 0, 0, cellData.Date);
                result.SetValue(cellID, 0, 1, cellData.CellName);
                result.SetValue(cellID, 0, 2, cellData.AlarmType);
            }
        }

        private void InitResultGrid(int count)
        {
            for (int i = 0; i < count; i++)
            {
                int index = i + 39;
                resultDic[i] = new string[3] { "a" + index, "c" + index, "e" + index  };
            }
        }

        protected override void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteStationAcceptResult result1 = btsResultDic[btsName];
            List<int> cellIDs = result1.CellIDs;
            for (int sid = 0; sid < resultDic.Count; ++sid)
            {
                int cellId = cellIDs[sid];
                if (!cellIDMap.ContainsKey(cellId))
                {
                    continue;
                }
                
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result1.GetValue(cellId, 0, col);
                    InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], value);
                }
            }
        }
    }
    #endregion

    class FddAcpVolteVoiceMo : FddAcpRrcRate
    {
        public FddAcpVolteVoiceMo()
        {
            evtRequList = new List<int>() { 1070 };
            evtSuccList = new List<int>() { 1072 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 11;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != 1)
            {
                return false;
            }
            return (fileInfo.Name.ToUpper().Contains("语音VOLTE"));
        }

        protected override CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (kpiCell == null || kpiCell.LteCell != (targetCell as LTECell))
            {
                kpiCell = new CellKpi(targetCell as LTECell);
            }

            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);
        }
    }

    class FddAcpVolteVoiceMt : FddAcpRrcRate
    {
        public FddAcpVolteVoiceMt()
        {
            evtRequList = new List<int>() { 1071 };
            evtSuccList = new List<int>() { 1073 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 13;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != 2)
            {
                return false;
            }
            return (fileInfo.Name.ToUpper().Contains("语音VOLTE"));
        }

        protected override CellKpi AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            if (kpiCell == null || kpiCell.LteCell != (targetCell as LTECell))
            {
                kpiCell = new CellKpi(targetCell as LTECell);
            }

            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);
        }
    }

    class FddAcpCellName : LteFddStationAccept
    {
        public FddAcpCellName()
        {
            resultGrid = new string[MaxCellCount, 1, 1];
            int idx = 4;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "a" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName)
        {
            LTEBTS lteBts = (targetCell as LTECell).BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }
            CurBtsCellCount = setCellIDMap(targetCell, btsName);

            if (!btsResultDic.TryGetValue(btsName, out var result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            object value = result.GetValue(targetCell.ID, 0, 0);
            if (value != null)
            {
                return;
            }

            result.SetValue(targetCell.ID, 0, 0, targetCell.Name);
        }
    }
}
