﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Collections.ObjectModel;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.MControls;

namespace MasterCom.RAMS.Func
{
    public partial class ScanGridCompareColorSettingForm : BaseFormStyle
    {
        public ScanGridCompareColorSettingForm(List<ColorRange> colorRange)
        {
            InitializeComponent();
            this.colorRange = colorRange;
            colorDlg = new ColorDialog();
            colorDlg.AllowFullOpen = false;
            colorDlg.ShowHelp = false;

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            panelSame.Click += PanelSame_Click;
            panelOne.Click += PanelOne_Click;
            panelTwo.Click += PanelTwo_Click;

            panelSame.BackColor = colorRange[0].color;
            panelOne.BackColor = colorRange[1].color;
            panelTwo.BackColor = colorRange[2].color;
        }

        public List<ColorRange> ColorRanges
        {
            get
            {
                return colorRange;
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            colorRange[0].color = panelSame.BackColor;
            colorRange[1].color = panelOne.BackColor;
            colorRange[2].color = panelTwo.BackColor;
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void PanelOne_Click(object sender, EventArgs e)
        {
            if (colorDlg.ShowDialog() == DialogResult.OK)
            {
                panelOne.BackColor = colorDlg.Color;
            }
        }

        private void PanelTwo_Click(object sender, EventArgs e)
        {
            if (colorDlg.ShowDialog() == DialogResult.OK)
            {
                panelTwo.BackColor = colorDlg.Color;
            }
        }

        private void PanelSame_Click(object sender, EventArgs e)
        {
            if (colorDlg.ShowDialog() == DialogResult.OK)
            {
                panelSame.BackColor = colorDlg.Color;
            }
        }

        private ColorDialog colorDlg;
        private List<ColorRange> colorRange;
    }
}
