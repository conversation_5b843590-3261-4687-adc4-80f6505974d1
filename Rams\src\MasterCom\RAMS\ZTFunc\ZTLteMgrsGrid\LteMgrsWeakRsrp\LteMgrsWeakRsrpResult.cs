﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsWeakRsrpResult : LteMgrsResultControlBase
    {
        LteMgrsFuncItem funcItem = null;

        public LteMgrsWeakRsrpResult()
        {
            InitializeComponent();
            InitCbxFreqType();

            cbxFreqType.SelectedIndexChanged += CbxFreqType_SelectedChanged;
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            miExportDetailExcel.Click += MiExportDetailExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += GridView_DoubleClick;

            miExportWord.Click += base.MiExportWord_Click;
            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportList.Click += MiExportGridList_Click;
        }

        public override string Desc
        {
            get { return "弱覆盖区域"; }
        }

        public void FillData(LteMgrsFuncItem funcItem)
        {
            this.funcItem = funcItem;
            object[] values = funcItem.FuncCondtion as object[];
            cbxFreqType.SelectedItem = EnumDescriptionAttribute.GetText((LteMgrsRsrpBandType)values[2]);
            RefreshResult();
        }

        protected override void ExportAllExcel(string savePath)
        {
            for (int i = 0; i < cbxFreqType.Items.Count; ++i)
            {
                cbxFreqType.SelectedIndex = i;
                string sheetName = cbxFreqType.SelectedItem.ToString() + Desc;
                string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
                ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
            }
        }

        private void CbxFreqType_SelectedChanged(object sender, EventArgs e)
        {
            RefreshResult();
            DrawOnLayer();
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleF));
            cbxFreqType.SelectedIndex = 0;
        }

        private void RefreshResult()
        {
            object[] values = this.funcItem.FuncCondtion as object[];
            LteMgrsWeakRsrpStater stater = this.funcItem.Stater as LteMgrsWeakRsrpStater;
            stater.FreqType = (LteMgrsRsrpBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsRsrpBandType), cbxFreqType.SelectedItem as string);
            stater.GridCount = (int)values[1];
            stater.RsrpMax = (double)values[0];
            gridControl1.DataSource = stater.GetViews(funcItem.CurQueryCitys[funcItem.SelectedCityIndex]);
            gridControl1.RefreshDataSource();
        }

        private void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void MiExportGridList_Click(object sender, EventArgs e)
        {
            List<LteMgrsWeakRsrpView> viewList = gridControl1.DataSource as List<LteMgrsWeakRsrpView>;
            if (viewList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("栅格序号");
            title.Add("所属网格");
            title.Add("栅格编号");
            title.Add("采样点数");
            title.Add("最大场强");
            title.Add("平均场强");
            title.Add("中心经度");
            title.Add("中心纬度");
            content.Add(title);

            int idIndex = 0;
            foreach (LteMgrsWeakRsrpView view in viewList)
            {
                foreach (LteMgrsWeakRsrpGrid grid in view.GridViews)
                {
                    List<object> row = new List<object>();
                    row.Add(++idIndex);
                    row.Add(view.RegionName);
                    row.Add(grid.MgrsString);
                    row.Add(grid.SampleCount);
                    row.Add(grid.TopRsrp);
                    row.Add(grid.AvgRsrp);
                    row.Add(grid.CentLng);
                    row.Add(grid.CentLat);
                    content.Add(row);
                }
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void MiExportDetailExcel_Click(object sender, EventArgs e)
        {
            List<LteMgrsWeakRsrpView> viewList = gridControl1.DataSource as List<LteMgrsWeakRsrpView>;
            if (viewList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("区域序号");
            title.Add("网格");
            title.Add("连续栅格数");
            title.Add("连续栅格集");
            title.Add("最强电平");
            title.Add("最小电平");
            title.Add("平均电平");
            title.Add("道路名称");
            title.Add("栅格编号");
            title.Add("采样点数");
            title.Add("平均场强");
            title.Add("频段第一强");
            title.Add("中心经度");
            title.Add("中心纬度");
            title.Add("小区名");
            title.Add("TAC");
            title.Add("ECI");
            title.Add("EARFCN");
            title.Add("PCI");
            title.Add("小区采样点数");
            title.Add("小区平均场强");
            title.Add("经度");
            title.Add("纬度");
            content.Add(title);

            foreach (LteMgrsWeakRsrpView view in viewList)
            {
                List<object> levelOne = new List<object>();
                levelOne.Add(view.SN);
                levelOne.Add(view.RegionName);
                levelOne.Add(view.GridCount);
                levelOne.Add(view.GridSetDesc);
                levelOne.Add(view.MaxRsrp);
                levelOne.Add(view.MinRsrp);
                levelOne.Add(view.AvgRsrp);
                levelOne.Add(view.RoadName);
                foreach (LteMgrsWeakRsrpGrid grid in view.GridViews)
                {
                    List<object> levelTwo = new List<object>(levelOne);
                    levelTwo.Add(grid.MgrsString);
                    levelTwo.Add(grid.SampleCount);
                    levelTwo.Add(grid.AvgRsrp);
                    levelTwo.Add(grid.TopRsrp);
                    levelTwo.Add(grid.CentLng);
                    levelTwo.Add(grid.CentLat);
                    foreach (LteMgrsCell cell in grid.Cells)
                    {
                        List<object> levelThree = new List<object>(levelTwo);
                        levelThree.Add(cell.CellName);
                        levelThree.Add(cell.Tac);
                        levelThree.Add(cell.Eci);
                        levelThree.Add(cell.Earfcn);
                        levelThree.Add(cell.Pci);
                        levelThree.Add(cell.SampleCount);
                        levelThree.Add(cell.AvgRsrp);
                        levelThree.Add(cell.Longitude);
                        levelThree.Add(cell.Latitude);
                        content.Add(levelThree);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(content);
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is LteMgrsCell)
            {
                LteMgrsCell cell = row as LteMgrsCell;
                if (cell.Cell != null)
                {
                    MainModel.SelectedLTECell = cell.Cell as LTECell;
                    LteMgrsLayer.SelGrid = gv.SourceRow as LteMgrsWeakRsrpGrid;
                    DbRect rect = new DbRect(cell.Cell.Longitude,
                        cell.Cell.Latitude
                        , LteMgrsLayer.SelGrid.CentLng
                        , LteMgrsLayer.SelGrid.CentLat);
                    MainModel.MainForm.GetMapForm().GoToView(rect.Expend(1.2));
                }
            }
            else if (row is LteMgrsWeakRsrpGrid)
            {
                LteMgrsWeakRsrpGrid grid = row as LteMgrsWeakRsrpGrid;
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
                LteMgrsLayer.SelGrid = grid;
            }
            else if (row is LteMgrsWeakRsrpView)
            {
                LteMgrsWeakRsrpView view = row as LteMgrsWeakRsrpView;
                LteMgrsWeakRsrpGrid grid = view.GridViews[view.GridViews.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
            }
        }

        public override void DrawOnLayer()
        {
            List<LteMgrsWeakRsrpView> viewList = gridControl1.DataSource as List<LteMgrsWeakRsrpView>;
            if (viewList == null || viewList.Count == 0)
            {
                return;
            }

            LteMgrsWeakRsrpStater stater = funcItem.Stater as LteMgrsWeakRsrpStater;
            List<LteMgrsDrawItem> drawList = stater.GetDrawList(viewList);
            LteMgrsLayer.DrawList = drawList;
            DbRect rect = LteMgrsLayer.GetDrawBound(drawList);
            if (rect != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(rect);
                SetNormalMapScale();
            }
            LteMgrsLayer.LegendGroup = stater.GetLegend();
            MainModel.RefreshLegend();
        }

        protected override bool ExportWord(WordControl word, string title)
        {
            if (title != "连续弱覆盖")
            {
                return false;
            }

            WaitTextBox.Text = "正在导出 " + title + "...";
            cbxFreqType.SelectedIndex = 0;

            if (gridView1.RowCount == 0)
            {
                word.InsertText("未发现连续弱覆盖路段。", "正文");
                word.NewLine();
                return true;
            }

            word.InsertText("连续弱覆盖整体渲染如下：", "正文");
            string fileName = "LTE连续弱覆盖";
            this.DrawOnLayer();
            word.InsertPicture(LteMgrsScreenShoter.GisScreenshot(fileName));
            word.NewLine();

            return true;
        }
    }
}
