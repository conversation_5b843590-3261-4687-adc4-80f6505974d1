using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLowSpeedConvergeSettingDlg : BaseDialog
    {
        public ZTLowSpeedConvergeSettingDlg()
        {
            InitializeComponent();
        }

        public void GetSettingFilterRet(out int rxQualShold, out int rxQualRadius, out int sampleCountLimit)
        {
            rxQualShold = (int)spinEditRxQual.Value;
            rxQualRadius = (int)spinEditRadius.Value;
            sampleCountLimit = (int)spinEditSampleNum.Value;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}