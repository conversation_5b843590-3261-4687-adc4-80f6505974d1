﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class MapLTECellLayerNBIOTPropertis : MTLayerPropUserControl
    {
        private List<BandRange> rangeValues = new List<BandRange>();

        public MapLTECellLayerNBIOTPropertis()
        {
            InitializeComponent();
        }

        private MapLTECellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapLTECellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "NBIOT频段";

            checkBoxSeparated.Checked = layer.NBIOTSetting.IsSeparated;
            checkBoxSeparated.CheckedChanged += new EventHandler(checkBoxSeparated_CheckedChanged);

            colorNBIot.Color = layer.NBIOTSetting.ColorCellNBIOT;
            colorNBIot.ColorChanged += new EventHandler(colorNBIot_ColorChanged);

            foreach (BandRange rangeValue in layer.NBIOTSetting.BandRangeList)
            {
                rangeValues.Add(rangeValue);
            }
            rowCountChanged();
        }

        private void rowCountChanged()
        {
            dataGridView.RowCount = rangeValues.Count;
            dataGridView.Invalidate();
        }

        void colorNBIot_ColorChanged(object sender, EventArgs e)
        {
            layer.NBIOTSetting.ColorCellNBIOT = Color.FromArgb(255, colorNBIot.Color);
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        {
            BandRangeValueSettingBox box = new BandRangeValueSettingBox(null);
            if (box.ShowDialog() == DialogResult.OK)
            {
                rangeValues.Add(box.RangeValue);
                rowCountChanged();
                layer.NBIOTSetting.BandRangeList = rangeValues;
            }
        }

        private void buttonModify_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                BandRange rangeValue = rangeValues[dataGridView.SelectedRows[0].Index];
                BandRangeValueSettingBox box = new BandRangeValueSettingBox(rangeValue);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    rangeValues.Remove(rangeValue);
                    rangeValues.Add(box.RangeValue);
                    rowCountChanged();
                    layer.NBIOTSetting.BandRangeList = rangeValues;
                }
            }
        }

        private void buttonRemove_Click(object sender, EventArgs e)
        {
            rangeValues.RemoveAt(dataGridView.SelectedRows[0].Index);
            rowCountChanged();
            layer.NBIOTSetting.BandRangeList = rangeValues;
        }

        private void checkBoxSeparated_CheckedChanged(object sender, EventArgs e)
        {
            layer.NBIOTSetting.IsSeparated = checkBoxSeparated.Checked;
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex >= rangeValues.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = rangeValues[e.RowIndex].RangeDescription;
            }
        }

        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }

        private void checkButtonState()
        {
            buttonModify.Enabled = dataGridView.SelectedRows.Count == 1;
            buttonRemove.Enabled = dataGridView.SelectedRows.Count == 1;
        }
    }

    public class NbIotSetting
    {
        public List<BandRange> BandRangeList { get; set; } = new List<BandRange>();

        public bool IsSeparated { get; set; }

        public SolidBrush BrushCell
        {
            get { return new SolidBrush(penEarfcn_NBIOT_Cell.Color); }
        }

        private Pen penEarfcn_NBIOT_Cell = new Pen(Color.Orange, 2);
        public Pen PenEarfcn_NBIOT_Cell
        {
            get { return penEarfcn_NBIOT_Cell; }
        }
        /// <summary>
        /// NBIOT频段颜色
        /// </summary>
        public Color ColorCellNBIOT
        {
            get { return penEarfcn_NBIOT_Cell.Color; }
            set { penEarfcn_NBIOT_Cell.Color = value; }
        }

        /// <summary>
        /// 根据频点判断是否是NBIOT
        /// </summary>
        /// <param name="earfcn">频点</param>
        /// <returns></returns>
        public bool IsValidNBIOT(int earfcn)
        {
            bool result = false;
            foreach (var range in BandRangeList)
            {
                result = range.IsValidBand(earfcn);
                if (result)
                {
                    break;
                }
            }
            return result;
        }

        bool btsContainLTECell;
        bool btsContainNBCell;
        public int GetBTSCellType(LTEBTS bts)
        {
            int cellType = -1;
            btsContainLTECell = false;
            btsContainNBCell = false;
            foreach (var cell in bts.Cells)
            {
                bool isCurNBCell = false;
                foreach (var range in BandRangeList)
                {
                    if (range.IsValidBand(cell.EARFCN))
                    {
                        isCurNBCell = true;
                        btsContainNBCell = true;
                        break;
                    }
                }
                if (!isCurNBCell)
                {
                    btsContainLTECell = true;
                }
            }

            if (btsContainLTECell && btsContainNBCell)
            {
                //同时有LTE和NB小区的站
                cellType = 3;
            }
            else if (btsContainNBCell)
            {
                //只有NB小区的站
                cellType = 1;
            }
            else if (btsContainLTECell)
            {
                //只有LTE小区的站
                cellType = 2;
            }
            return cellType;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> rangeParams = new List<object>();
                param["colorCellNBIOT"] = ColorCellNBIOT.ToArgb();
                param["IsSeparated"] = IsSeparated;
                param["BandRange"] = rangeParams;
                foreach (BandRange range in BandRangeList)
                {
                    rangeParams.Add(range.Param);
                }
                return param;
            }
            set
            {
                BandRangeList.Clear();
                if (value.ContainsKey("colorCellNBIOT"))
                {
                    penEarfcn_NBIOT_Cell = new Pen(Color.FromArgb((int)(value["colorCellNBIOT"])), 2);
                }
                IsSeparated = (bool)value["IsSeparated"];
                List<object> rangeParams = (List<object>)value["BandRange"];
                foreach (object o in rangeParams)
                {
                    Dictionary<string, object> rangeParam = (Dictionary<string, object>)o;
                    BandRange range = new BandRange();
                    range.Param = rangeParam;
                    BandRangeList.Add(range);
                }
            }
        }
    }
}
