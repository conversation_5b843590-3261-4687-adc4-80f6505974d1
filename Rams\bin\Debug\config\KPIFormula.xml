﻿<?xml version="1.0"?>
<Configs>
  <Config name="KPIFormulaCfg">
    <Item name="KPIFormulaManager" typeName="KPIFormulaManager">
      <Item name="KPIFamilyList" typeName="IList">
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">2014年集团考核指标</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">TDD-LTE</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE网络下载速率</Item>
                  <Item name="Formula" typeName="String">{(value10[57])*(1000*8)/((value4[57])*(1024*1024))}</Item>
                  <Item name="Descrition" typeName="String">应用层下载流量（MB）/下载时间（s）</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">TD</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD网络下载速率 </Item>
                  <Item name="Formula" typeName="String">{(value1[57])*(1000*8)/((value4[57])*(1024*1024))}</Item>
                  <Item name="Descrition" typeName="String">应用层下载流量（MB）/下载时间（s）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD手机用户下载速率大于500kbps的采样点占比</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_050564020113+Tx_051264020113)/(Tx_050564020103+Tx_051264020103) }</Item>
                  <Item name="Descrition" typeName="String">【下载速率大于500kbps的采样点数/测试采样点总数】*100%</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD手机用户下载速率大于等于500kbps的采样点占比</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_050564020114+Tx_051264020114)/(Tx_050564020103+Tx_051264020103) }</Item>
                  <Item name="Descrition" typeName="String">【下载速率大于等于500kbps的采样点数/测试采样点总数】*100%</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD全程呼叫成功率 </Item>
                  <Item name="Formula" typeName="String">{100*(1-(evtIdCount[105]+evtIdCount[111]+evtIdCount[117]+evtIdCount[123]+evtIdCount[198]+evtIdCount[199]+value9[105]+value9[111]+value9[117]+value9[123]+value9[198]+value9[199] )/((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106]+evtIdCount[112]+evtIdCount[118]+value9[112]+value9[118])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+evtIdCount[200]+value9[104]+value9[110]+value9[188]+value9[200]+evtIdCount[116]+evtIdCount[122]+evtIdCount[189]+evtIdCount[201]+value9[116]+value9[122]+value9[189]+value9[201])))*((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+evtIdCount[200]+value9[104]+value9[110]+value9[188]+value9[200]))/(evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])}%</Item>
                  <Item name="Descrition" typeName="String">TD接通率*（1-TD掉话率）</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">GSM</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxQuality0-4占比</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/Mx_5A01050C}%</Item>
                  <Item name="Descrition" typeName="String">【RxQuality0-4级样本点数/总样本点数】*100%</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM全程呼叫成功率 </Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))))*(((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0]))}%</Item>
                  <Item name="Descrition" typeName="String">GSM接通率*（1-GSM掉话率）</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">LTE_TDD</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">基本指标及覆盖</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">测试里程（Km）</Item>
                  <Item name="Formula" typeName="String">{Lte_0806 / 1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">测试总时长（分钟）</Item>
                  <Item name="Formula" typeName="String">{Lte_0805/(60*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均车速（km/h）</Item>
                  <Item name="Formula" typeName="String">{Lte_0875 * 3600/Lte_0874 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均RSRP</Item>
                  <Item name="Formula" typeName="String">{Lte_61210309}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段A_平均RSRP</Item>
                  <Item name="Formula" typeName="String">{Lte_61212309}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段D_平均RSRP</Item>
                  <Item name="Formula" typeName="String">{Lte_61212409}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段E_平均RSRP</Item>
                  <Item name="Formula" typeName="String">{Lte_61212509}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段F_平均RSRP</Item>
                  <Item name="Formula" typeName="String">{Lte_61212609}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">边缘RSRP</Item>
                  <Item name="Formula" typeName="String">{Lte_61211204}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均SINR</Item>
                  <Item name="Formula" typeName="String">{Lte_61210403}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段A_平均SINR</Item>
                  <Item name="Formula" typeName="String">{Lte_61211F03}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段D_平均SINR</Item>
                  <Item name="Formula" typeName="String">{Lte_61212003}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段E_平均SINR</Item>
                  <Item name="Formula" typeName="String">{Lte_61212103}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段F_平均SINR</Item>
                  <Item name="Formula" typeName="String">{Lte_61212203}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">边缘SINR</Item>
                  <Item name="Formula" typeName="String">{Lte_61211201}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均RSRQ</Item>
                  <Item name="Formula" typeName="String">{Lte_61210702}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE覆盖率1（RSRP&gt;-113且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210104/Lte_61210101) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE覆盖率2（RSRP&gt;-110且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210105/Lte_61210101) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_6121010D/Lte_61210101) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段A_LTE覆盖率1（RSRP&gt;-113且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212704/Lte_61212701) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段A_LTE覆盖率2（RSRP&gt;-110且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212705/Lte_61212701) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段A_LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_6121270D/Lte_61212701) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段D_LTE覆盖率1（RSRP&gt;-113且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212804/Lte_61212801) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段D_LTE覆盖率2（RSRP&gt;-110且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212805/Lte_61212801) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段D_LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_6121280D/Lte_61212801) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段E_LTE覆盖率1（RSRP&gt;-113且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212904/Lte_61212901) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段E_LTE覆盖率2（RSRP&gt;-110且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212905/Lte_61212901) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段E_LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_6121290D/Lte_61212901) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段F_LTE覆盖率1（RSRP&gt;-113且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212A04/Lte_61212A01) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段F_LTE覆盖率2（RSRP&gt;-110且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212A05/Lte_61212A01) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">频段F_LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lte_61212A0D/Lte_61212A01) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RSRP连续弱覆盖里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[870] / 1000) / Lte_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RSRP连续无覆盖里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[873] / 1000) / Lte_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">连续UE高发射功率里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[872] / 1000) / Lte_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">连续SINR质差里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[871] / 1000) / Lte_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">重叠覆盖度大于等于3比例</Item>
                  <Item name="Formula" typeName="String">{Lte_61210F02*100/Lte_61210F01}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">重叠覆盖里程占比</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210F03/Lte_0806) *100}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">其它业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP下载尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+evtIdCount[58]+value9[57]+value9[58]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+value9[57]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[58])*100/(evtIdCount[57]+evtIdCount[58])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_物理层平均下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lte_61211312/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_Mac层平均下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lte_61211512/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[57]+value10[58]+value10[91])*(1000*8)/((value4[57]+value4[58]+value4[91])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均下载速率（不含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[57])*(1000*8)/((value4[57])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率_适用于栅格(kbps)</Item>
                  <Item name="Formula" typeName="String">{(Lte_052164020101)*(1000*8)/((Lte_052164020102)*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率小于2M占比（包括LTE、TD、GSM部分）(%)</Item>
                  <Item name="Formula" typeName="String">{100*( Lte_052164020105+Lte_052164020106+Lte_052164020107+Lte_05216402010205+Lte_05216402010206+Lte_05216402010207 )/(Lte_052164020103+Lte_05216402010203)}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率小于2M占比（LTE部分）(%)</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_052109020105+Lte_052109020106+Lte_052109020107+Lte_05210902010205+Lte_05210902010206+Lte_05210902010207)/(Lte_052109020103+Lte_05210902010203)  }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率大于等于10M占比（包括LTE、TD、GSM部分）(%)</Item>
                  <Item name="Formula" typeName="String">{((Lte_05216402010D+Lte_05216402010E+Lte_05216402010F+Lte_052164020110+Lte_0521640201020D+Lte_0521640201020E+Lte_0521640201020F+Lte_052164020102CE+Lte_052164020102CF )/(Lte_052164020103+Lte_05216402010203)) *100}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率大于等于10M占比（LTE部分）(%)</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_05210902010D+Lte_05210902010E+Lte_05210902010F+Lte_0521090201CE+Lte_0521090201CF+Lte_0521090201020D+Lte_0521090201020E+Lte_0521090201020F+Lte_052109020102CE+Lte_052109020102CF )/(Lte_052109020103+Lte_05210902010203) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率大于等于5M占比（包括LTE、TD、GSM部分）(%)</Item>
                  <Item name="Formula" typeName="String">{((Lte_05216402010A+Lte_0521640201020A)/(Lte_052164020103+Lte_05216402010203)) *100}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE下载速率大于等于5M占比（包括LTE部分）(%)</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_05210902010A+Lte_0521090201020A)/(Lte_052109020103+Lte_05210902010203) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE上行速率3.5M以上占比(%)</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_0521640302F904+Lte_052164030204+Lte_052164030202F904+Lte_05216403020204)/(Lte_05216403020203+Lte_052164030203)}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE上行速率512K以下占比</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_0521640302D1+Lte_0521640302D2+Lte_052164030202D2+Lte_052164030202D1 )/(Lte_052164030203+Lte_05216403020203) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_边缘PDCP下行吞吐量（含掉线）（kbps）</Item>
                  <Item name="Formula" typeName="String">{Lte_61211202/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_边缘PDCP上行吞吐量（kbps）</Item>
                  <Item name="Formula" typeName="String">{Lte_61211203/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_下行低吞吐量占比</Item>
                  <Item name="Formula" typeName="String">{((Lte_61210502+Lte_61210503+Lte_61210504+Lte_61210505+Lte_61210506+Lte_61210507+Lte_61210508+Lte_61210509+Lte_6121050A+Lte_6121050B+Lte_6121050C+Lte_6121050D+Lte_6121050E+Lte_6121050F+Lte_61210510)/Lte_61210501) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_物理层平均上传载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lte_61211412/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_Mac层平均上传速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lte_61211612/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均上传速率（不含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[60])*(1000*8)/((value2[60])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均上传速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[60]+value10[61])*(1000*8)/((value2[60]+value2[61])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_PDSCH BLER平均值</Item>
                  <Item name="Formula" typeName="String">{Lte_61210802}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行初始HARQ重传比率</Item>
                  <Item name="Formula" typeName="String">{((Lte_61210908/(Lte_61210908+Lte_61210906)) + (Lte_6121090C/(Lte_6121090A+Lte_6121090C))) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行子帧调度率</Item>
                  <Item name="Formula" typeName="String">{value3[57]*100/(value4[57])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行平均每时隙调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value2[57]*1000/(2*value3[57])}</Item>
                  <Item name="Descrition" typeName="String">下行业务调度PRB个数总和/（已调度给UE的子帧数*2）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行平均每秒调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value2[57]*1000000/(value4[57])}</Item>
                  <Item name="Descrition" typeName="String">下载过程中调度PRB总个数/总下载时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行子帧调度率(个/秒)</Item>
                  <Item name="Formula" typeName="String">{((value2[57]*1000000/(value4[57])) / (value2[57]*1000/(2*value3[57]))) / 2}</Item>
                  <Item name="Descrition" typeName="String">调度给UE的子帧数总和 / 下行业务时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行每RB平均下载量（含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[57]+value10[58]+value10[91])*8)/((value2[57]+value2[58]+value2[91])*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据下载量（含掉线）/下载时间内调度RB数总数（含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行每RB平均下载量（不含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[57])*8)/(value2[57]*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据下载量（不含掉线）/下载时间内调度RB数总数（不含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行平均每时隙调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value1[60]*1000/(2*value4[60])}</Item>
                  <Item name="Descrition" typeName="String">上行业务调度PRB个数总和/（已调度给UE的子帧数*2）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行平均每秒调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value1[60]*1000000/(value2[60])}</Item>
                  <Item name="Descrition" typeName="String">上传过程中调度PRB总个数/总上传时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行子帧调度率(个/秒)</Item>
                  <Item name="Formula" typeName="String">{((value1[60]*1000000/(value2[60])) / (value1[60]*1000/(2*value4[60]))) / 2}</Item>
                  <Item name="Descrition" typeName="String">调度给UE的子帧数总和 / 上行业务时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行每RB平均上传量（含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[60]+value10[61]+value10[36])*8)/((value1[60]+value1[61]+value1[36])*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据上传量（含掉线）/上传时间内调度RB数总数（含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行每RB平均上传量（不含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[60])*8)/((value1[60])*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据上传量（不含掉线）/上传时间内调度RB数总数（不含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0MCS平均值</Item>
                  <Item name="Formula" typeName="String">{Lte_61210A02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1MCS平均值</Item>
                  <Item name="Formula" typeName="String">{Lte_61210B02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行MCS平均值</Item>
                  <Item name="Formula" typeName="String">{Lte_61210C02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_码字0CQI平均值</Item>
                  <Item name="Formula" typeName="String">{Lte_61210D02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_码字1CQI平均值</Item>
                  <Item name="Formula" typeName="String">{Lte_61210D04}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0 64QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210E04/(Lte_61210E02+Lte_61210E03+Lte_61210E04)) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0 16QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210E03/(Lte_61210E02+Lte_61210E03+Lte_61210E04)) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0 QPSK比例</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210E02/(Lte_61210E02+Lte_61210E03+Lte_61210E04)) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1 64QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210E08/(Lte_61210E06+Lte_61210E07+Lte_61210E08)) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1 16QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210E07/(Lte_61210E06+Lte_61210E07+Lte_61210E08)) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1 QPSK比例</Item>
                  <Item name="Formula" typeName="String">{(Lte_61210E06/(Lte_61210E06+Lte_61210E07+Lte_61210E08)) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上报RANK1采样占比</Item>
                  <Item name="Formula" typeName="String">{Lte_61211003 * 100/Lte_61211001 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上报RANK2采样占比</Item>
                  <Item name="Formula" typeName="String">{Lte_61211005 * 100/Lte_61211001 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_单流时长占比</Item>
                  <Item name="Formula" typeName="String">{(Lte_61211004/Lte_61211002) * 100 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_双流时长占比</Item>
                  <Item name="Formula" typeName="String">{(Lte_61211006/Lte_61211002) * 100 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=2）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lte_61211103*100/Lte_61211101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=3）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lte_61211104*100/Lte_61211101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=7）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lte_61211108*100/Lte_61211101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=8）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lte_61211109*100/Lte_61211101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换尝试总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[849]+evtIdCount[897]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换成功总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[850]+evtIdCount[898]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换失败总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[869]+evtIdCount[1099]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[850]+evtIdCount[898])*100/(evtIdCount[849]+evtIdCount[897])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[850]+value1[898])/((evtIdCount[850]+evtIdCount[898])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换尝试总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[849]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换成功总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[850]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换失败总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[869]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[850])*100/(evtIdCount[849])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[850])/((evtIdCount[850])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换尝试总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[897]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换成功总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[898]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换失败总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1099]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[898])*100/(evtIdCount[897])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[898])/((evtIdCount[898])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[851]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[852]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[852]*100/evtIdCount[851]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新时延（s）</Item>
                  <Item name="Formula" typeName="String">{value1[852]/(evtIdCount[852]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_RRC连接成功率(%)</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[855]+evtIdCount[891])*100/(evtIdCount[854]+evtIdCount[890])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_RRC连接平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[855]+value1[891])/((evtIdCount[855]+evtIdCount[891])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_ATTACH请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[21]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_ATTACH成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[22]*100/evtIdCount[21]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_ATTACH平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{value1[22]/(evtIdCount[22]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_SERVICE成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[875]*100)/(evtIdCount[875]+evtIdCount[1259]+value9[1259])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_SERVICE平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{value1[875]/(evtIdCount[875]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1200] + evtIdCount[1201]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1200]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1201]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1200]/(evtIdCount[1200]+evtIdCount[1201])) * 100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[1200] / evtIdCount[1200])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1205]+evtIdCount[1206]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1205]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1206]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1205]/(evtIdCount[1205]+evtIdCount[1206]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[1205] / evtIdCount[1205])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆尝试次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1211]+evtIdCount[1212])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆成功次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1211])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1211]/(evtIdCount[1211]+evtIdCount[1212]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value2[1211]/evtIdCount[1211])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP完全加载次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1213])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP浏览成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1213]/(evtIdCount[1211]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP浏览时长(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[1213]/evtIdCount[1213])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP浏览速率(kbps)</Item>
                  <Item name="Formula" typeName="String">{((value2[1213] * 8000) / value1[1213])/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1216]+evtIdCount[1217]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1216]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1216]/(evtIdCount[1216]+evtIdCount[1217]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1217]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载掉线率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1217]/(evtIdCount[1216]+evtIdCount[1217]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP应用层下载速率（不含掉线）(kbps）</Item>
                  <Item name="Formula" typeName="String">{((value2[1216]) / (value1[1216])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP应用层下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{((value2[1216]+value2[1217]) / (value1[1216]+value1[1217])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1219]+evtIdCount[1220]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1219]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1219] / (evtIdCount[1219]+evtIdCount[1220]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[1219]/evtIdCount[1219])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送速率（kbps）</Item>
                  <Item name="Formula" typeName="String">{((value2[1219]) / (value1[1219])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体业务发起次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1221] + evtIdCount[1230]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体业务成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1228] + evtIdCount[1235]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体业务成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[1228]+evtIdCount[1235])/(evtIdCount[1221]+evtIdCount[1230]))*100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体加载时延(s)</Item>
                  <Item name="Formula" typeName="String">{((value1[1225]+value1[1237])/(evtIdCount[1225]+evtIdCount[1237]))/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体时长（s）</Item>
                  <Item name="Formula" typeName="String">{((value1[1222]+value1[1231])/(evtIdCount[1222]+evtIdCount[1231]))/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体缓冲时长（s）</Item>
                  <Item name="Formula" typeName="String">{((value2[1228] + value4[1234]) / (evtIdCount[1228] + evtIdCount[1234]))/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体播放总时长（s）</Item>
                  <Item name="Formula" typeName="String">{((value3[1228] + value5[1234]) / (evtIdCount[1228] + evtIdCount[1234])) /1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体缓冲次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1226]+evtIdCount[1232]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体应用层下载速率（kbps）</Item>
                  <Item name="Formula" typeName="String">{((value2[1223]+value2[1234]) / (value1[1223]+value1[1234])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">语音业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/((evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]+evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021]+evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041])-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]+evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]+evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">全称呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/((evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]+evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021]+evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041])-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]+evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]+evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))))*(((evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[1011]+value1[1031]+value3[1051])/(1000.0*(evtIdCount[1011]+evtIdCount[1031]+evtIdCount[1051]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">呼叫时延&gt;9s占比</Item>
                  <Item name="Formula" typeName="String">{100*(value5[1011]+value5[1031]+value5[1051])/(evtIdCount[1011]+evtIdCount[1031]+evtIdCount[1051])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB返回TDL比例</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[883]+value9[883])/(evtIdCount[882]+value9[882])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB(GSM)返回TDL比例</Item>
                  <Item name="Formula" typeName="String">{100*(value5[883])/(value5[882])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB(TD)返回TDL比例</Item>
                  <Item name="Formula" typeName="String">{100*(value4[883])/(value4[882])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB返回TDL时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[883]/(1000.0*evtIdCount[883])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到TD/GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[879]+value9[879])/(evtIdCount[876]+value9[876])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value5[879]+value9[879])/(evtIdCount[876]+value9[876] - value4[879])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到TD成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value4[879]+value9[879])/(evtIdCount[876]+value9[876] - value5[879])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到TD/GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[887]+value9[887])/(evtIdCount[884]+value9[884])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value5[887]+value9[887])/(evtIdCount[884]+value9[884] - value4[887])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到TD成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value4[887]+value9[887])/(evtIdCount[884]+value9[884] - value5[887])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到TD/GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[879]/(1000.0*evtIdCount[879])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value3[879]/(1000.0*value5[879])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到TD平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value2[879]/(1000.0*value4[879])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到TD/GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[887]/(1000.0*evtIdCount[887])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value3[887]/(1000.0*value5[887])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到TD平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value2[887]/(1000.0*value4[887])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1000]+value9[1000]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1000]+value9[1000])-(evtIdCount[1007]+value9[1007])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB接通率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[1000]+value9[1000]-(evtIdCount[1007]+value9[1007]))/(evtIdCount[1000]+value9[1000])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016])/(evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB全程呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016])/(evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]))))*((evtIdCount[1000]+value9[1000]-(evtIdCount[1007]+value9[1007]))/(evtIdCount[1000]+value9[1000]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[1011]/(1000.0*evtIdCount[1011])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1040]+value9[1040]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1040]+value9[1040])-(evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD接通率</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[1040]+value9[1040])-(evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1040]+value9[1040])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/(evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041]-(evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD全程呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/(evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041]-(evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))))*(((evtIdCount[1040]+value9[1040])-(evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1040]+value9[1040]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{value3[1051]/(1000.0*evtIdCount[1051])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1020]+value9[1020]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[1020]+value9[1020])-(evtIdCount[1027]+value9[1027])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM接通率</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[1020]+value9[1020])-(evtIdCount[1027]+value9[1027]))/(evtIdCount[1020]+value9[1020])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036])/((evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021])-(evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM全程呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036])/((evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021])-(evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]))))*(((evtIdCount[1020]+value9[1020])-(evtIdCount[1025]+value9[1025]))/(evtIdCount[1020]+value9[1020]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[1031]/(1000.0*evtIdCount[1031])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS均值</Item>
                  <Item name="Formula" typeName="String">{(Lte_6D2109*Lte_6D2108 +Lte_61216209*Lte_61216208)/(Lte_6D2108+Lte_61216208) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;2.8占比</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_6D2104+Lte_6D2105+Lte_6D2106+Lte_6D2107+Lte_6D210C+Lte_61216204+Lte_61216205+Lte_61216206+Lte_61216207+Lte_6121620C)/(Lte_6D2108 +Lte_61216208) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;3.5占比</Item>
                  <Item name="Formula" typeName="String">{100*(Lte_6D2106+Lte_6D2107+Lte_6D210C+Lte_61216206+Lte_61216207+Lte_6121620C)/(Lte_6D2108 +Lte_61216208) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">LTE_FDD</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">基本指标及覆盖</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">测试里程（Km）</Item>
                  <Item name="Formula" typeName="String">{Lf_0806 / 1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">测试总时长（分钟）</Item>
                  <Item name="Formula" typeName="String">{Lf_0805/(60*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均车速（km/h）</Item>
                  <Item name="Formula" typeName="String">{Lf_0875 * 3600/Lf_0874 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均RSRP</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0309}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">边缘RSRP</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1204}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均SINR</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0403}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">边缘SINR</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1201}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均RSRQ</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0702}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE覆盖率1（RSRP&gt;-113且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0104/Lf_612D0101) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE覆盖率2（RSRP&gt;-110且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0105/Lf_612D0101) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">LTE覆盖率3（RSRP&gt;-103且SINR&gt;=-3）</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D010D/Lf_612D0101) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RSRP连续弱覆盖里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[3188] / 1000) / Lf_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RSRP连续无覆盖里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[3191] / 1000) / Lf_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">连续UE高发射功率里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[3190] / 1000) / Lf_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">连续SINR质差里程占比</Item>
                  <Item name="Formula" typeName="String">{((value4[3189] / 1000) / Lf_0806) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">重叠覆盖度大于等于3比例</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0F02*100/Lf_612D0F01}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">重叠覆盖里程占比</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0F03/Lf_0806) *100}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">其它业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP下载尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3557]+evtIdCount[3558]+value9[3557]+value9[3558]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3557]+value9[3557]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3558]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_FTP掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3558])*100/(evtIdCount[3557]+evtIdCount[3558])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_物理层平均下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1312/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_Mac层平均下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1512/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[3557]+value10[3558]+value10[3578])*(1000*8)/((value4[3557]+value4[3558]+value4[3578])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均下载速率（不含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[3557])*(1000*8)/((value4[3557])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_边缘PDCP下行吞吐量（含掉线）（kbps）</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1202/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_边缘PDCP上行吞吐量（kbps）</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1203/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_下行低吞吐量占比</Item>
                  <Item name="Formula" typeName="String">{((Lf_612D0502+Lf_612D0503+Lf_612D0504+Lf_612D0505+Lf_612D0506+Lf_612D0507+Lf_612D0508+Lf_612D0509+Lf_612D050A+Lf_612D050B+Lf_612D050C+Lf_612D050D+Lf_612D050E+Lf_612D050F+Lf_612D0510)/Lf_612D0501) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_物理层平均上传载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1412/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_Mac层平均上传速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1612/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均上传速率（不含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[3560])*(1000*8)/((value2[3560])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">业务类_应用层平均上传速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{(value10[3560]+value10[3561])*(1000*8)/((value2[3560]+value2[3561])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_PDSCH BLER平均值</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0802}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行初始HARQ重传比率</Item>
                  <Item name="Formula" typeName="String">{((Lf_612D0908/(Lf_612D0908+Lf_612D0906)) + (Lf_612D090C/(Lf_612D090A+Lf_612D090C))) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行子帧调度率</Item>
                  <Item name="Formula" typeName="String">{value3[3557]*100/(value4[3557])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行平均每时隙调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value2[3557]*1000/(2*value3[3557])}</Item>
                  <Item name="Descrition" typeName="String">下行业务调度PRB个数总和/（已调度给UE的子帧数*2）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行平均每秒调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value2[3557]*1000000/(value4[3557])}</Item>
                  <Item name="Descrition" typeName="String">下载过程中调度PRB总个数/总下载时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行子帧调度率(个/秒)</Item>
                  <Item name="Formula" typeName="String">{((value2[3557]*1000000/(value4[3557])) / (value2[3557]*1000/(2*value3[3557]))) / 2}</Item>
                  <Item name="Descrition" typeName="String">调度给UE的子帧数总和 / 下行业务时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行RB总数</Item>
                  <Item name="Formula" typeName="String">{value1[3560]*1000}</Item>
                  <Item name="Descrition" typeName="String"></Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行RB总数</Item>
                  <Item name="Formula" typeName="String">{value2[3557]*1000}</Item>
                  <Item name="Descrition" typeName="String"></Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_PDCCH BLER</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0808}</Item>
                  <Item name="Descrition" typeName="String"></Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_RBCount</Item>
                  <Item name="Formula" typeName="String">{value1[3560]*1000 + value2[3557]*1000}</Item>
                  <Item name="Descrition" typeName="String"></Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行每RB平均下载量（含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[3557]+value10[3558]+value10[3591])*8)/((value2[3557]+value2[3558]+value2[3591])*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据下载量（含掉线）/下载时间内调度RB数总数（含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行每RB平均下载量（不含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[3557])*8)/(value2[3557]*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据下载量（不含掉线）/下载时间内调度RB数总数（不含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行平均每时隙调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value1[3560]*1000/(2*value4[3560])}</Item>
                  <Item name="Descrition" typeName="String">上行业务调度PRB个数总和/（已调度给UE的子帧数*2）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行平均每秒调度PRB个数</Item>
                  <Item name="Formula" typeName="String">{value1[3560]*1000000/(value2[3560])}</Item>
                  <Item name="Descrition" typeName="String">上传过程中调度PRB总个数/总上传时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行子帧调度率(个/秒)</Item>
                  <Item name="Formula" typeName="String">{((value1[3560]*1000000/(value2[3560])) / (value1[3560]*1000/(2*value4[3560]))) / 2}</Item>
                  <Item name="Descrition" typeName="String">调度给UE的子帧数总和 / 上行业务时长</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行每RB平均上传量（含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[3560]+value10[3561]+value10[3536])*8)/((value1[3560]+value1[3561]+value1[3536])*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据上传量（含掉线）/上传时间内调度RB数总数（含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行每RB平均上传量（不含掉线）(bit/RB)</Item>
                  <Item name="Formula" typeName="String">{((value10[3560])*8)/((value1[3560])*1000)}</Item>
                  <Item name="Descrition" typeName="String">应用层数据上传量（不含掉线）/上传时间内调度RB数总数（不含掉线）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0MCS平均值</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0A02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1MCS平均值</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0B02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上行MCS平均值</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0C02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_码字0CQI平均值</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0D02}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_码字1CQI平均值</Item>
                  <Item name="Formula" typeName="String">{Lf_612D0D04}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0 64QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0E04/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04)) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0 16QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0E03/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04)) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字0 QPSK比例</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0E02/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04)) * 100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1 64QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0E08/(Lf_612D0E06+Lf_612D0E07+Lf_612D0E08)) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1 16QAM比例</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0E07/(Lf_612D0E06+Lf_612D0E07+Lf_612D0E08)) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_下行码字1 QPSK比例</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D0E06/(Lf_612D0E06+Lf_612D0E07+Lf_612D0E08)) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上报RANK1采样占比</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1003 * 100/Lf_612D1001 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_上报RANK2采样占比</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1005 * 100/Lf_612D1001 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_单流时长占比</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D1004/Lf_612D1002) * 100 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_双流时长占比</Item>
                  <Item name="Formula" typeName="String">{(Lf_612D1006/Lf_612D1002) * 100 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=2）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1103*100/Lf_612D1101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=3）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1104*100/Lf_612D1101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=7）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1108*100/Lf_612D1101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">调度类_传输模式（TM=8）时长占比（ms）</Item>
                  <Item name="Formula" typeName="String">{Lf_612D1109*100/Lf_612D1101 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换尝试总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3154]+evtIdCount[3157]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换成功总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3155]+evtIdCount[3158]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换失败总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3156]+evtIdCount[3159]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3155]+evtIdCount[3158])*100/(evtIdCount[3154]+evtIdCount[3157])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_网内切换平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[3155]+value1[3158])/((evtIdCount[3155]+evtIdCount[3158])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换尝试总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3154]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换成功总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3155]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换失败总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3156]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3155])*100/(evtIdCount[3154])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_同频切换平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[3155])/((evtIdCount[3155])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换尝试总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3157]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换成功总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3158]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换失败总次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3159]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3158])*100/(evtIdCount[3157])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_异频切换平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[3158])/((evtIdCount[3158])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3170]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3171]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3171]*100/evtIdCount[3170]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_TA更新时延（s）</Item>
                  <Item name="Formula" typeName="String">{value1[3171]/(evtIdCount[3171]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_RRC连接成功率(%)</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3164]+evtIdCount[3160])*100/(evtIdCount[3163]+evtIdCount[3161])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">移动类_RRC连接平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[3164]+value1[3160])/((evtIdCount[3164]+evtIdCount[3160])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_ATTACH请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3521]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_ATTACH成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3522]*100/evtIdCount[3521]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_ATTACH平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{value1[3522]/(evtIdCount[3522]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_SERVICE成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3186]*100)/(evtIdCount[3186]+evtIdCount[3187]+value9[3187])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接入类_SERVICE平均时延（s）</Item>
                  <Item name="Formula" typeName="String">{value1[3186]/(evtIdCount[3186]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3200] + evtIdCount[3201]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3200]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3201]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3200]/(evtIdCount[3200]+evtIdCount[3201])) * 100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">短信类_短信发送时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[3200] / evtIdCount[3200])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3205]+evtIdCount[3206]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3205]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3206]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3205]/(evtIdCount[3205]+evtIdCount[3206]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">彩信类_彩信发送时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[3205] / evtIdCount[3205])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆尝试次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3211]+evtIdCount[3212])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆成功次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3211])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3211]/(evtIdCount[3211]+evtIdCount[3212]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP登陆时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value2[3211]/evtIdCount[3211])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP完全加载次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3213])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP浏览成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3213]/(evtIdCount[3211]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP浏览时长(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[3213]/evtIdCount[3213])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP浏览类_HTTP浏览速率(kbps)</Item>
                  <Item name="Formula" typeName="String">{((value2[3213] * 8000) / value1[3213])/1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3216]+evtIdCount[3217]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3216]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3216]/(evtIdCount[3216]+evtIdCount[3217]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3217]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP下载掉线率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3217]/(evtIdCount[3216]+evtIdCount[3217]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP应用层下载速率（不含掉线）(kbps）</Item>
                  <Item name="Formula" typeName="String">{((value2[3216]) / (value1[3216])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP下载类_HTTP应用层下载速率（含掉线）(kbps)</Item>
                  <Item name="Formula" typeName="String">{((value2[3216]+value2[3217]) / (value1[3216]+value1[3217])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3219]+evtIdCount[3220]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3219]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3219] / (evtIdCount[3219]+evtIdCount[3220]))*100)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送时延（s）</Item>
                  <Item name="Formula" typeName="String">{(value1[3219]/evtIdCount[3219])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邮件类_邮件发送速率（kbps）</Item>
                  <Item name="Formula" typeName="String">{((value2[3219]) / (value1[3219])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体业务发起次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3221] + evtIdCount[3230]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体业务成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3228] + evtIdCount[3235]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体业务成功率</Item>
                  <Item name="Formula" typeName="String">{((evtIdCount[3228]+evtIdCount[3235])/(evtIdCount[3221]+evtIdCount[3230]))*100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体加载时延(s)</Item>
                  <Item name="Formula" typeName="String">{((value1[3225]+value1[3237])/(evtIdCount[3225]+evtIdCount[3237]))/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体时长（s）</Item>
                  <Item name="Formula" typeName="String">{((value1[3222]+value1[3231])/(evtIdCount[3222]+evtIdCount[3231]))/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体缓冲时长（s）</Item>
                  <Item name="Formula" typeName="String">{((value2[3228] + value4[3234]) / (evtIdCount[3228] + evtIdCount[3234]))/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体播放总时长（s）</Item>
                  <Item name="Formula" typeName="String">{((value3[3228] + value5[3234]) / (evtIdCount[3228] + evtIdCount[3234])) /1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体缓冲次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3226]+evtIdCount[3232]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">流媒体类_流媒体应用层下载速率（kbps）</Item>
                  <Item name="Formula" typeName="String">{((value2[3223]+value2[3234]) / (value1[3223]+value1[3234])) * 8000 / 1024}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">语音业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;2.8占比</Item>
                  <Item name="Formula" typeName="String">{((Lte_6D2103+Lte_6D2104+Lte_6D2105+Lte_6D2106+Lte_6D2107+Lte_6D210C) /Lte_6D2108) *100}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3055]+value9[3056])/((evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]+evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021]+evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041])-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]+evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]+evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">全称呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/((evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]+evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021]+evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041])-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]+evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]+evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))))*(((evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{(value1[3011]+value1[3031]+value3[3051])/(1000.0*(evtIdCount[3011]+evtIdCount[3031]+evtIdCount[3051]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">呼叫时延&gt;9s占比</Item>
                  <Item name="Formula" typeName="String">{100*(value5[3011]+value5[3031]+value5[3051])/(evtIdCount[3011]+evtIdCount[3031]+evtIdCount[3051])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO CSFB返回FDD比例</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3083]+value9[3083])/(evtIdCount[3081]+value9[3081])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT CSFB返回FDD比例</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3084]+value9[3084])/(evtIdCount[3082]+value9[3082])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO CSFB(GSM)返回FDD比例</Item>
                  <Item name="Formula" typeName="String">{100*(value5[3083])/(value5[3081])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT CSFB(GSM)返回FDD比例</Item>
                  <Item name="Formula" typeName="String">{100*(value5[3084])/(value5[3082])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO CSFB(WCDMA)返回FDD比例</Item>
                  <Item name="Formula" typeName="String">{100*(value4[3083])/(value4[3081])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT CSFB(WCDMA)返回FDD比例</Item>
                  <Item name="Formula" typeName="String">{100*(value4[3084])/(value4[3082])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO CSFB返回FDD时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[3083]/(1000.0*evtIdCount[3083])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT CSFB返回FDD时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[3084]/(1000.0*evtIdCount[3084])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到WCDMA/GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3075]+value9[3075])/(evtIdCount[3069]+value9[3069])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value5[3075]+value9[3075])/(evtIdCount[3069]+value9[3069] - value4[3075])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到WCDMA成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value5[3075]+value9[3075])/(evtIdCount[3069]+value9[3069] - value5[3075])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到WCDMA/GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3076]+value9[3076])/(evtIdCount[3070]+value9[3070])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到GSM成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value5[3076]+value9[3076])/(evtIdCount[3070]+value9[3070] - value4[3076])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到WCDMA成功率%</Item>
                  <Item name="Formula" typeName="String">{100*(value4[3076]+value9[3076])/(evtIdCount[3070]+value9[3070] - value5[3076])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到WCDMA/GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[3075]/(1000.0*evtIdCount[3075])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到WCDMA平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value2[3075]/(1000.0*value4[3075])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫CSFB回落到GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value3[3075]/(1000.0*value5[3075])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到WCDMA/GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[3076]/(1000.0*evtIdCount[3076])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到WCDMA平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value2[3076]/(1000.0*value4[3076])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫CSFB回落到GSM平均时延(s)</Item>
                  <Item name="Formula" typeName="String">{value3[3076]/(1000.0*value5[3076])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3000]+value9[3000]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3000]+value9[3000])-(evtIdCount[3007]+value9[3007])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB接通率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB全程呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))))*((evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[3011]/(1000.0*evtIdCount[3011])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3040]+value9[3040]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA接通率</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3040]+value9[3040])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/(evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041]-(evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA全程呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/(evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041]-(evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))))*(((evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3040]+value9[3040]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{value3[3051]/(1000.0*evtIdCount[3051])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3020]+value9[3020]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[3020]+value9[3020])-(evtIdCount[3027]+value9[3027])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM接通率</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[3020]+value9[3020])-(evtIdCount[3027]+value9[3027]))/(evtIdCount[3020]+value9[3020])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM掉话率</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036])/((evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021])-(evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM全程呼叫完好率</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036])/((evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021])-(evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]))))*(((evtIdCount[3020]+value9[3020])-(evtIdCount[3025]+value9[3025]))/(evtIdCount[3020]+value9[3020]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM呼叫时延(s)</Item>
                  <Item name="Formula" typeName="String">{value1[3031]/(1000.0*evtIdCount[3031])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CSFB语音接通时延&lt;6.2s占比</Item>
                  <Item name="Formula" typeName="String">{100*(value8[3011]+value8[3012])/(evtIdCount[3011]+evtIdCount[3012])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS话音业务掉话率（MOS≤3）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3678]+evtIdCount[3679]+evtIdCount[3680]+evtIdCount[3681]+evtIdCount[3682]+evtIdCount[3683])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS话音业务掉话率（MOS≤2.5）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[3684]+evtIdCount[3685]+evtIdCount[3686]+evtIdCount[3687]+evtIdCount[3688]+evtIdCount[3689])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">GSM</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">GSM语音业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Rxlev90覆盖率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}%</Item>
                  <Item name="Descrition" typeName="String">Rxlev在通话时取RxlevSub，在空闲时取BCCHRxlev,针对鼎利数据，请选择Rxlev</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Rxlev90覆盖里程（公里）</Item>
                  <Item name="Formula" typeName="String">{(Mx_640119+Mx_640116+Mx_640115+Mx_640114)/1000}公里</Item>
                  <Item name="Descrition" typeName="String">90覆盖里程</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxlevSub90覆盖率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_5A010217+Mx_5A01020A+Mx_5A010209+Mx_5A010208)/Mx_5A010201}%</Item>
                  <Item name="Descrition" typeName="String">针对ATU数据，请选择RxlevSub</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxlevSub90覆盖里程（公里）</Item>
                  <Item name="Formula" typeName="String">{(Mx_5A010219+Mx_5A010216+Mx_5A010215+Mx_5A010214)/1000}公里</Item>
                  <Item name="Descrition" typeName="String">RxlevSub90覆盖里程</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxlevBcch90覆盖率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_5A010317+Mx_5A01030A+Mx_5A010309+Mx_5A010308)/Mx_5A010301}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxQuality0-5百分比（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[0]+value9[0]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]+value9[87]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_主叫接通率（%）</Item>
                  <Item name="Formula" typeName="String">{100.0*((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0])}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[5]+value9[5]+evtIdCount[906]+value9[906]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[6]+value9[6]+evtIdCount[907]+value9[907]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[5]+evtIdCount[6]+value9[5]+value9[6]+evtIdCount[906]+evtIdCount[907]+value9[906]+value9[907]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主被叫同时掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[41]+value9[41]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">语音呼叫全程成功率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))))*(((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均呼叫建立时延（秒）</Item>
                  <Item name="Formula" typeName="String">{value1[13]/(1000.0*evtIdCount[13]) }秒</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;=2.8比例（%）</Item>
                  <Item name="Formula" typeName="String">{100.0*(Mx_5A010B58+Mx_5A010B59+Mx_5A010B5A+Mx_5A010B5B) /Mx_5A010B5C }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS均值</Item>
                  <Item name="Formula" typeName="String">{Mx_5A010B53}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM语音质量2012（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_5A011401/Mx_5A011402)* ((Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C)}%</Item>
                  <Item name="Descrition" typeName="String">
                      【RxQuality0-5级样本点数/RxQuality总样本点数】*【在RxQuality0-5级占比范围内的MOS大于等于2.8样本点数/RxQuality对应的MOS总样本点数】
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GSM语音质量2013（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/Mx_5A01050C}%</Item>
                  <Item name="Descrition" typeName="String">
                      RxQuality0-4占比
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxLevSub平均值</Item>
                  <Item name="Formula" typeName="String">{Mx_5A010202}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">AMR-FR比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A01080A/(Mx_5A010806+Mx_5A010807+Mx_5A010808+Mx_5A010809+Mx_5A01080A) }%</Item>
                  <Item name="Descrition" typeName="String">
                      时长比例
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">AMR-HR比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A010809/(Mx_5A010806+Mx_5A010807+Mx_5A010808+Mx_5A010809+Mx_5A01080A) }%</Item>
                  <Item name="Descrition" typeName="String">时长比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EFR比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A010808/(Mx_5A010806+Mx_5A010807+Mx_5A010808+Mx_5A010809+Mx_5A01080A) }%</Item>
                  <Item name="Descrition" typeName="String">时长比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FR比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A010806/(Mx_5A010806+Mx_5A010807+Mx_5A010808+Mx_5A010809+Mx_5A01080A) }%</Item>
                  <Item name="Descrition" typeName="String">时长比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HR比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A010807/(Mx_5A010806+Mx_5A010807+Mx_5A010808+Mx_5A010809+Mx_5A01080A) }%</Item>
                  <Item name="Descrition" typeName="String">时长比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">900时长比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A011207/(Mx_5A011207+Mx_5A011209+Mx_5A01120B) }%</Item>
                  <Item name="Descrition" typeName="String">通话状态下</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">1800时长比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Mx_5A011209/(Mx_5A011207+Mx_5A011209+Mx_5A01120B) }%</Item>
                  <Item name="Descrition" typeName="String">通话状态下</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">弱覆盖占总测试里程比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_640115+Mx_640114+Mx_640113+Mx_64011F+Mx_640112+Mx_640111) /Mx_0806 }%</Item>
                  <Item name="Descrition" typeName="String">低于-80的覆盖里程占总里程的比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深度覆盖不足占总测试里程比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Mx_64011F+Mx_640112+Mx_640111) /Mx_0806 }%</Item>
                  <Item name="Descrition" typeName="String">低于-94的覆盖里程占总里程的比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均时速（公里/小时）</Item>
                  <Item name="Formula" typeName="String">{(Mx_0861/1000)/(Mx_0860/3600000) }</Item>
                  <Item name="Descrition" typeName="String">GSM语音业务平均时速</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">里程掉话比（公里/每掉话）</Item>
                  <Item name="Formula" typeName="String">{((Mx_0836+Mx_0838)/2000)/(evtIdCount[5]+value9[5]+evtIdCount[906]+value9[906]+evtIdCount[6]+value9[6]+evtIdCount[907]+value9[907])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">每通话平均切换次数（切换次数/每呼叫）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[16]/(evtIdCount[3]+evtIdCount[4]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">GSM数据业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+evtIdCount[58]+value9[58]}</Item>
                  <Item name="Descrition" typeName="String">下载成功+下载失败</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功率</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[57])*100/((evtIdCount[57]+evtIdCount[58]+value9[58]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]+value9[58] }</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线率</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[58]+value9[58])*100/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String">
                      有过滤
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载数据掉线比（KByte/每掉线）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])/(1024*(evtIdCount[58]+value9[58])) }</Item>
                  <Item name="Descrition" typeName="String">
                      下载数据量/掉线次数，该值越大越好
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载总数据量_不含掉线（KByte）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])/1024 }</Item>
                  <Item name="Descrition" typeName="String">不含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP总下载时长_不含掉线（秒）</Item>
                  <Item name="Formula" typeName="String">{(value4[57])/1000}</Item>
                  <Item name="Descrition" typeName="String">不含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_不含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])*(1000*8)/((value4[57])*1024) }</Item>
                  <Item name="Descrition" typeName="String">不含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载总数据量_含掉线（KByte）</Item>
                  <Item name="Formula" typeName="String">{(value1[58]+value1[57]+value1[91])/1024}</Item>
                  <Item name="Descrition" typeName="String">含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP总下载时长_含掉线（秒）</Item>
                  <Item name="Formula" typeName="String">{(value4[57]+value4[58]+value4[91])/1000}</Item>
                  <Item name="Descrition" typeName="String">含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57]+value1[58]+value1[91])*(1000*8)/((value4[57]+value4[58]+value4[91])*1024) }</Item>
                  <Item name="Descrition" typeName="String">含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS/EDGE平均时隙使用数量</Item>
                  <Item name="Formula" typeName="String">{(Mx_06020401+(Mx_06020402*2)+(Mx_06020403*3)+(Mx_06020404*4)+Mx_06020801+(Mx_06020802*2)+(Mx_06020803*3)+(Mx_06020804*4))/ (Mx_06020401+Mx_06020402+Mx_06020403+Mx_06020404+Mx_06020801+Mx_06020802+Mx_06020803+Mx_06020804)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE平均MCS编码使用率（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020801+Mx_07020802*2+Mx_07020803*3+Mx_07020804*4+Mx_07020805*5+Mx_07020806*6+Mx_07020807*7 + Mx_07020808*8+Mx_07020809*9+Mx_07020401+Mx_07020402*2+Mx_07020403*3+Mx_07020404*4+Mx_07020405*5+Mx_07020406*6+Mx_07020407*7 + Mx_07020408*8+Mx_07020409*9)/(Mx_0702080B+Mx_0702040B) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE 高编码使用比例_MCS7-9（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020807 + Mx_07020808+Mx_07020809+Mx_07020407+Mx_07020408+Mx_07020409 )*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String">
                      MCS7-9占的比例
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS1编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020801+Mx_07020401)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS2编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020802+Mx_07020402)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS3编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020803+Mx_07020403)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS4编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020804+Mx_07020404)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS5编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020805+Mx_07020405)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS6编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020806+Mx_07020406)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS7编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020807+Mx_07020407)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS8编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020808+Mx_07020408)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE MCS9编码使用比例（%）</Item>
                  <Item name="Formula" typeName="String">{(Mx_07020809+Mx_07020409)*100/(Mx_0702080B+Mx_0702040B) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP登录请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[954]+value9[954]+evtIdCount[947]+value9[947]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP登录成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[947]+value9[947]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP登录时延（秒）</Item>
                  <Item name="Formula" typeName="String">{(value1[947]+value1[44])/1000*(evtIdCount[947]+value9[947]+evtIdCount[44]+value9[44]) }秒</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP页面刷新请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]+evtIdCount[47]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP页面刷新成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP页面刷新时延（秒）</Item>
                  <Item name="Formula" typeName="String">{value1[46]/(1000*evtIdCount[46])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP页面刷新成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]*100/(evtIdCount[46]+evtIdCount[47])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP图铃下载测试尝试次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]+evtIdCount[49]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP图铃下载测试成次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP图铃下载平均速率（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[48]*8000)/((value2[48])*1024) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP图铃下载成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]*100/(evtIdCount[48]+evtIdCount[49])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均BLER_GPRS</Item>
                  <Item name="Formula" typeName="String">{Mx_04020402/(Mx_04020401) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均BLER_EDGE</Item>
                  <Item name="Formula" typeName="String">{Mx_04020802/(Mx_04020801) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均BLER_全部</Item>
                  <Item name="Formula" typeName="String">{(Mx_04020802+Mx_04020402)/(Mx_04020801+Mx_04020401) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[26]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[27]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU成功率（%）</Item>
                  <Item name="Formula" typeName="String">{100*evtIdCount[27]/evtIdCount[26] }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU平均间隔_时间（分钟/每RAU）</Item>
                  <Item name="Formula" typeName="String">{Mx_0805/evtIdCount[27]*60000 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU平均间隔_距离（公里/每RAU）</Item>
                  <Item name="Formula" typeName="String">{Mx_0806/evtIdCount[27]*1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU平均时延（秒）</Item>
                  <Item name="Formula" typeName="String">{value1[27]/evtIdCount[27]*10000 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRSAttach请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[21]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRSAttach成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[22]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRSAttach失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[23]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRSAttach成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[22]*100/evtIdCount[21]}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRSAttach时延（秒）</Item>
                  <Item name="Formula" typeName="String">{value1[22]/(1000*evtIdCount[22])}秒</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Dettach请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[24]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Dettach成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[25]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 激活请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[29]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 激活成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[30]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 激活失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[31]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 激活成功率（%）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[30]*100/evtIdCount[29]}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 激活平均时延（秒）</Item>
                  <Item name="Formula" typeName="String">{value1[30]/(1000*evtIdCount[30])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 去激活请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[34]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PDP 去激活成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[35]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_适用于栅格（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(Mx_050264020101)*(1000*8)/((Mx_050264020102 )*1024) }</Item>
                  <Item name="Descrition" typeName="String">建议用于GIS上的栅格呈现</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">GSM事件</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call Attempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[0]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：1 | 业务类型：语音 | 事件说明：信令中出现Channel Request或CM Service Request来确定试呼开始</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call Attempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[1]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：2 | 业务类型：语音 | 事件说明：信令中出现PagingResponse出现来确定被叫试呼开始</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Call Attempt Retry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[2]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：3 | 业务类型：语音 | 事件说明：主叫信令出现连续的Channel Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[3]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：4 | 业务类型：语音 | 事件说明：信令出现Connect或Connect Acknowledge</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[4]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：5 | 业务类型：语音 | 事件说明：信令出现Connect或Connect Acknowledge</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Drop Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[5]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：6 | 业务类型：语音 | 事件说明：在一次通话中如出现Disconnect或Channel Release中任意一条，就计为一次呼叫正常释放。只有当两条消息都未出现而由专用模式转为空闲模式时，才计为一次掉话</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Drop Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[6]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：7 | 业务类型：语音 | 事件说明：在一次通话中如出现Disconnect或Channel Release中任意一条，就计为一次呼叫正常释放。只有当两条消息都未出现而由专用模式转为空闲模式时，才计为一次掉话</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[7]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：8 | 业务类型：语音 | 事件说明：起呼之后，有Alterting，但是没有Connect， 并且非正常释放</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[8]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：9 | 业务类型：语音 | 事件说明：起呼之后，有Alterting，但是没有Connect， 并且非正常释放</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Block Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[9]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：10 | 业务类型：语音 | 事件说明：起呼之后，没有Alterting</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Block Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[10]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：11 | 业务类型：语音 | 事件说明：起呼之后，没有Alterting</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call End</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[11]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：12 | 业务类型：语音 | 事件说明：信令出现Disconnect</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call End</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[12]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：13 | 业务类型：语音 | 事件说明：信令出现Disconnect</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call Alerting</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[13]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：14 | 业务类型：语音 | 事件说明：信令出现Alerting</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call Alerting</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[14]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：15 | 业务类型：语音 | 事件说明：信令出现Alerting</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Command</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[15]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：16 | 业务类型：语音 | 事件说明：信令出现Handover Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[16]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：17 | 业务类型：语音 | 事件说明：信令出现Handover Complete，与最近的handover Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[17]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：18 | 业务类型：语音 | 事件说明：信令出现Handover Failure，与最近的handover Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Location Area Update Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[18]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：19 | 业务类型：通用 | 事件说明：信令出现Location Update Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Location Area Update Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[19]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：20 | 业务类型：通用 | 事件说明：信令出现Location Update Accept，与最近的Location Update Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Location Area Update Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[20]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：21 | 业务类型：通用 | 事件说明：信令出现Location Update Reject，与最近的Location Update Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[21]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：22 | 业务类型：数据 | 事件说明：信令出现Attach Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[22]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：23 | 业务类型：数据 | 事件说明：信令出现Attach Accept，与最近的Attach Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[23]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：24 | 业务类型：数据 | 事件说明：超过30s没有出现Attach Accept，判断为Failure</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Detach Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[24]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：25 | 业务类型：数据 | 事件说明：信令出现Detach Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Detach Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[25]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：26 | 业务类型：数据 | 事件说明：信令出现Detach Accept，与最近的Detach Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Routing Area Update Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[26]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：27 | 业务类型：数据 | 事件说明：信令出现Route Area Update Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Routing Area Update Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[27]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：28 | 业务类型：数据 | 事件说明：信令出现Route Area Update Complete，与最近的Route Area Update Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Routing Area Update Reject</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[28]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：29 | 业务类型：数据 | 事件说明：信令出现Route Area Update Reject，与最近的Route Area Update Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[29]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：30 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[30]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：31 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Reject</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[31]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：32 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Deactive PDP Context Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[34]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：35 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Deactive PDP Context Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[35]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：36 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Cell Reselection</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[39]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：40 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Weak Coverage</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[40]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：41 | 业务类型：通用 | 事件说明：信号强度小于-94dBm持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主被叫同时掉话[智能预判处理]</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[41]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：42 | 业务类型：语音 | 事件说明：主被叫同时掉话</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Weak Quality</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[42]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：43 | 业务类型：语音 | 事件说明：RxQuality大于=6持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[44]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：45 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[45]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：46 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：47 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[47]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：48 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：49 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[49]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：50 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[50]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：51 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[51]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：52 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[52]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：53 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[53]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：54 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[54]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：55 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[55]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：56 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[56]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：57 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：58 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：59 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[59]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：60 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[60]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：61 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[61]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：62 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[62]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：63 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[63]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：64 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Push</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[64]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：65 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[65]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：66 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[66]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：67 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[67]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：68 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[68]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：69 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Out</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[69]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：70 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[70]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：71 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ftp Download Fail Atu</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[71]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：72 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[72]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：73 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Cell Reselection Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[73]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：74 | 业务类型：通用 | 事件说明：主服C1小于0持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Cell Reselection TooSlow</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[74]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：75 | 业务类型：通用 | 事件说明：主服C2+5小于邻区C2持续7秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[75]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：76 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Received Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[76]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：77 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon wap reply</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[77]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：78 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail三分钟无数据</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[78]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：79 | 业务类型：数据 | 事件说明：开始下载后3分钟内无数据</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PingPanHandover</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[79]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：80 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Abnormal Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[80]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：81 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO_NoConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[81]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：82 | 业务类型：语音 | 事件说明：起呼之后，有Alterting，但是没有Connect， 并且正常释放，在有些地方，这种情况不算未接通</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT_NoConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[82]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：83 | 业务类型：语音 | 事件说明：起呼之后，有Alterting，但是没有Connect， 并且正常释放，在有些地方，这种情况不算未接通</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">场强快衰落</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[83]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：84 | 业务类型：通用 | 事件说明：信号强度从较好状态（持续6秒超过-85）进入较差状态（持续5秒低于-85）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">质量快衰落</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[84]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：85 | 业务类型：语音 | 事件说明：RxQuality从较好状态（持续6秒小于=3）进入较差状态（持续5秒大于3）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">DeviceLost</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[85]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：86 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">强信号弱质量</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[86]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：87 | 业务类型：语音 | 事件说明：信号强度大于-70且Rxquality大于=5持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">疑似MOBlockCall</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[87]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：88 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">信令缺失</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[88]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：89 | 业务类型：通用 | 事件说明：超过10秒无层三信令</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[89]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：90 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP gateway connected</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[90]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：91 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail_超时十分钟</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[91]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：92 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail_路由区更新拒绝</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[92]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：93 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail网络去活PDP</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[97]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：98 | 业务类型：数据 | 事件说明：网络测发起的去活PDP</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail主动去活PDP</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[98]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：99 | 业务类型：数据 | 事件说明：下载尚未结束，主动发去活PDP</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西移动_弱覆盖路段_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[399]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：400 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">云南移动_路由区更新过频繁</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[400]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：401 | 业务类型：通用 | 事件说明：30秒内路由更新超过1次</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">云南移动_TBF_CLOSE</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[401]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：402 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">云南移动_小区重选频繁状</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[402]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：403 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_30S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[403]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：404 | 业务类型：数据 | 事件说明：下载速率=0持续30秒以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深圳移动_突然高质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[404]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：405 | 业务类型：语音 | 事件说明：RxQuality从较好状态(小于=3)-大于不好状态(大于=6超过6秒)-大于好转(小于=3超过5秒)</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深圳移动_位置更新突发质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[405]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：406 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Http Page Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[406]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：407 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[407]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：408 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP Page Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[408]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：409 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Mo Call Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[409]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：410 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">分组资源请求过慢</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[410]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：411 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音信令掉话差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[411]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：412 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音业务通话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[412]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：413 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[413]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：414 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音业务掉话差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[414]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：415 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音连续未接通差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[415]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：416 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">上海移动弱MOS_持续质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[416]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：417 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">上海移动弱MOS_质量毛刺</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[417]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：418 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">上海移动弱MOS_半速率占用</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[418]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：419 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">上海移动_持续弱MOS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[419]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：420 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续质差567算法2</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[420]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：421 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续质差567</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[421]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：422 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">弱质量567</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[422]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：423 | 业务类型：语音 | 事件说明：RxQuality大于=5持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_连续质差567</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[423]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：424 | 业务类型：语音 | 事件说明：持续15秒以上 Rxquality大于=5</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换不及时</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[424]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：425 | 业务类型：语音 | 事件说明：主服小区比同频段第一邻区场强低10DB，持续10秒以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">湖北移动_弱覆盖90_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[425]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：426 | 业务类型：语音 | 事件说明：GSM弱覆盖：主服务小区场强小于90dBm持续50米及以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">湖北移动_弱质量567_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[426]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：427 | 业务类型：语音 | 事件说明：GSM弱质量：RxQual大于=5持续50米及以上 </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换频繁</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[427]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：428 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东_弱覆盖起呼</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[428]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：429 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆移动_4秒质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[666]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：667 | 业务类型：语音 | 事件说明：连续4秒 rxqualsub 大于= 5</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续质差算法2</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[899]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：900 | 业务类型：语音 | 事件说明：持续15秒以上70%时间Rxquality大于=6</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[900]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：901 | 业务类型：语音 | 事件说明：持续15秒以上70%采样点Rxquality大于=6</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">上海_弱覆盖事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[901]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：902 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Drop Call_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[902]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：903 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Drop Call_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[903]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：904 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call CM_ReEstablishment</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[904]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：905 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call CM_ReEstablishment</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[905]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：906 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Drop Call_ReEstablish</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[906]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：907 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Drop Call_ReEstablish</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[907]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：908 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call CM_ReEstablishment2</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[908]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：909 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Call CM_ReEstablishment3</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[909]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：910 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call CM_ReEstablishment2</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[910]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：911 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MT Call CM_ReEstablishment3</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[911]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：912 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Cell Reselection_nonormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[912]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：913 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续弱MOS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[913]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：914 | 业务类型：语音 | 事件说明：连续4个或4个以上MOS值低于2.8</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_强信号质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[914]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：915 | 业务类型：语音 | 事件说明：Rxlev大于-80 and RxQual大于=5 持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_弱信号质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[915]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：916 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_一般质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[916]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：917 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_连续质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[917]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：918 | 业务类型：语音 | 事件说明：持续15秒以上 Rxquality大于=6</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_弱覆盖路段</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[918]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：919 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深圳移动_小区重选参数异常</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[919]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：920 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">苏州移动_伪小区事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[920]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：921 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">弱MOS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[921]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：922 | 业务类型：语音 | 事件说明：MOS值低于2.8</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深圳移动_重选不及时</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[922]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：923 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深圳移动_场强快衰落</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[923]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：924 | 业务类型：通用 | 事件说明：场强大于-80db,持续5秒以上,在5秒时间内信号下降15dBm以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MO Drop Call_ReEstablis_notnormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[924]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：925 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MO业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[925]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：926 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MT业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[926]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：927 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MO通话质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[927]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：928 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MT通话质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[928]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：929 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">信令缺失_IDLE</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[929]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：930 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖_08县城</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[940]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：941 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖_10县城</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[941]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：942 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖_08密集城区</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[942]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：943 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖_10密集城区</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[943]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：944 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广州移动_内切事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[944]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：945 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">位置更新未接通[智能预判生成]</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[945]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：946 | 业务类型：语音 | 事件说明：被叫位置更新引起的未接通</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Login请求_鼎利</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[946]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：947 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Login成功_鼎利</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[947]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：948 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">EDGE 覆盖采样事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[948]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：949 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download连接失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[949]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：950 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">邻区丢失事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[950]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：951 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广州弱覆盖事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[951]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：952 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广州弱覆盖事件_加衰减</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[952]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：953 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">连续弱覆盖路段事件（集团弱覆盖事件）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[953]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：954 | 业务类型：语音 | 事件说明：连续10秒RxLev小于-90</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Login失败_鼎利</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[954]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：955 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">云南移动_质差路段</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[955]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：956 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">贵州移动_弱覆盖路段（150米）</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[956]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：957 | 业务类型：语音 | 事件说明：信号强度大于-94持续50米</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">停车测试</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[957]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：958 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西移动_3秒质差</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[958]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：959 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Weak Quality_3秒</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[959]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：960 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[960]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：961 | 业务类型：数据 | 事件说明：下载速率小于90Kbps持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_有重选</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[961]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：962 | 业务类型：数据 | 事件说明：下载速率=0持续2秒以上，且期间发生重选</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_无重选</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[962]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：963 | 业务类型：数据 | 事件说明：下载速率=0持续2秒以上，且期间未发生重选</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_切换不合理事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[963]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：964 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_切换不合理质差事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[964]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：965 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_起呼不合理事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[965]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：966 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_起呼不合理质差事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[966]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：967 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖路段</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[967]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：968 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖路段(200米)</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[968]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：969 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低_100米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[969]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：970 | 业务类型：数据 | 事件说明：下载速率小于30Kbps持续100米</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低_200米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[970]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：971 | 业务类型：数据 | 事件说明：下载速率小于30Kbps持续200米</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通BcchLevel_B90DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[971]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：972 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通C2I_B6DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[972]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：973 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通C2I_U12DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[973]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：974 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通MOS值连续5次大于4</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[974]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：975 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通MOS值连续5次小于2</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[975]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：976 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通RXQUAL_U5_5S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[976]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：977 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通BcchLevel_75DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[977]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：978 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Command900_1800</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[978]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：979 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Command1800_900</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[979]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：980 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Success900_1800</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[980]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：981 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Success1800_900</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[981]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：982 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Failure900_1800</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[982]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：983 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Handover Failure1800_900</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[983]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：984 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换过频繁[由智能预判产生]</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[984]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：985 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">高误块率</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[985]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：986 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">强电平高误块率</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[986]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：987 | 业务类型：数据 | 事件说明：信号强度大于-70且BLER大于25%持续5秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP下载速率低</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[987]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：988 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">数据业务小区重选事件</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[988]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：989 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_FTP零下载速率</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[989]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：990 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_FTP下载速率低</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[990]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：991 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">山西_路由区更新过频繁</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[991]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：992 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低于10K</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[992]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：993 | 业务类型：数据 | 事件说明：下载速率小于10Kbps持续2秒及以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖_BM90_5S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[993]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：994 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖_BM85_5S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[994]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：995 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖路段_40米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[995]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：996 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖路段_400米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[996]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：997 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">广东移动_弱覆盖路段_BM94</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[997]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：998 | 业务类型：语音 | 事件说明：</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">TD</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">TD语音业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PCCPCH_RSCP95覆盖率_PCCPCH_C2I大于-3（%）</Item>
                  <Item name="Formula" typeName="String">{100*Tx_5C040311/Tx_5C04030A }%</Item>
                  <Item name="Descrition" typeName="String">PCCPCH_RSCP大于-95且PCCPCH_C2I大于-3</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PCCPCH_RSCP80覆盖率_PCCPCH_C2I大于-3（%）</Item>
                  <Item name="Formula" typeName="String">{100*Tx_5C04031B/Tx_5C04030A }%</Item>
                  <Item name="Descrition" typeName="String">PCCPCH_RSCP大于-80且PCCPCH_C2I大于-3</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PCCPCH_RSCP90覆盖率_PCCPCH_C2I大于-3（%）</Item>
                  <Item name="Formula" typeName="String">{100*Tx_5C04030E/Tx_5C04030A }%</Item>
                  <Item name="Descrition" typeName="String">PCCPCH_RSCP大于-90且PCCPCH_C2I大于-3</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫试呼次数_TD</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[100]+value9[100]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数_TD</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[104]+evtIdCount[188]+value9[104]+value9[188])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫接通次数_TD</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[100]+value9[100])-(evtIdCount[104]+evtIdCount[188]+value9[104]+value9[188])}</Item>
                  <Item name="Descrition" typeName="String">
                      有过滤
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_TD</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[100]+value9[100])-(evtIdCount[104]+evtIdCount[188]+value9[104]+value9[188]))/(evtIdCount[100]+value9[100])}%</Item>
                  <Item name="Descrition" typeName="String">
                      有过滤
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫_TD</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[105]+value9[105]+evtIdCount[117]+value9[117]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率_TD（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[105]+evtIdCount[117]+value9[105]+value9[117])/((evtIdCount[100]+value9[100]+evtIdCount[112]+value9[112])-(evtIdCount[104]+evtIdCount[188]+value9[104]+value9[188]+evtIdCount[116]+evtIdCount[189]+value9[116]+value9[189]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫试呼次数_GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[106]+value9[106]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数_GSM</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[110]+evtIdCount[200]+value9[110]+value9[200])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫接通次数_GSM</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[106]+value9[106])-(evtIdCount[110]+evtIdCount[200]+value9[110]+value9[200])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_GSM</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[106]+value9[106])-(evtIdCount[110]+evtIdCount[200]+value9[110]+value9[200]))/(evtIdCount[106]+value9[106])}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫_GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[111]+evtIdCount[198]+value9[111]+value9[198]+evtIdCount[123]+evtIdCount[199]+value9[123]+value9[199]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率_GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[111]+evtIdCount[123]+evtIdCount[198]+evtIdCount[199]+value9[111]+value9[123]+value9[198]+value9[199] )/((evtIdCount[106]+value9[106]+evtIdCount[118]+value9[118])-(evtIdCount[110]+evtIdCount[200]+value9[110]+value9[200]+evtIdCount[122]+evtIdCount[201]+value9[122]+value9[201]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数_TD+GSM</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[104]+evtIdCount[188]+value9[104]+value9[188]+evtIdCount[110]+evtIdCount[200]+value9[110]+value9[200])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_TD+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+evtIdCount[200]+value9[104]+value9[110]+value9[188]+value9[200]))/(evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫_TD+GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[105]+value9[105]+evtIdCount[117]+value9[117]+evtIdCount[111]+evtIdCount[198]+value9[111]+value9[198]+evtIdCount[123]+evtIdCount[199]+value9[123]+value9[199]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率_TD+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[105]+evtIdCount[111]+evtIdCount[117]+evtIdCount[123]+evtIdCount[198]+evtIdCount[199]+value9[105]+value9[111]+value9[117]+value9[123]+value9[198]+value9[199] )/((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106]+evtIdCount[112]+evtIdCount[118]+value9[112]+value9[118])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+evtIdCount[200]+value9[104]+value9[110]+value9[188]+value9[200]+evtIdCount[116]+evtIdCount[122]+evtIdCount[189]+evtIdCount[201]+value9[116]+value9[122]+value9[189]+value9[201]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">全程呼叫成功率_TD+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(1-(evtIdCount[105]+evtIdCount[111]+evtIdCount[117]+evtIdCount[123]+evtIdCount[198]+evtIdCount[199]+value9[105]+value9[111]+value9[117]+value9[123]+value9[198]+value9[199] )/((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106]+evtIdCount[112]+evtIdCount[118]+value9[112]+value9[118])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+evtIdCount[200]+value9[104]+value9[110]+value9[188]+value9[200]+evtIdCount[116]+evtIdCount[122]+evtIdCount[189]+evtIdCount[201]+value9[116]+value9[122]+value9[189]+value9[201])))*((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+evtIdCount[200]+value9[104]+value9[110]+value9[188]+value9[200]))/(evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均接入时延_TD（秒）</Item>
                  <Item name="Formula" typeName="String">{value3[102]/(1000*evtIdCount[102]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均接入时延_GSM（秒）</Item>
                  <Item name="Formula" typeName="String">{value3[108]/(1000*evtIdCount[108]) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均接入时延_TD+GSM（秒）</Item>
                  <Item name="Formula" typeName="String">{(value3[108]+value3[102])/(1000*(evtIdCount[108]+evtIdCount[102])) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS均值</Item>
                  <Item name="Formula" typeName="String">{Tx_6D0409}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;=2.8比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_6D0404+Tx_6D0405+Tx_6D0406+Tx_6D0407+Tx_6D040C) /Tx_6D0408 }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换请求次数_TD+GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]+evtIdCount[142]+evtIdCount[145]+evtIdCount[148]+evtIdCount[151]  }</Item>
                  <Item name="Descrition" typeName="String">全部</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功次数_TD+GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]}</Item>
                  <Item name="Descrition" typeName="String">全部</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功率_TD+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150])/(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]+evtIdCount[142]+evtIdCount[145]+evtIdCount[148]+evtIdCount[151])}%</Item>
                  <Item name="Descrition" typeName="String">全部</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换请求次数_TD切向GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[141]+evtIdCount[142] }</Item>
                  <Item name="Descrition" typeName="String">T-&gt;G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功次数_TD切向GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[141]}</Item>
                  <Item name="Descrition" typeName="String">T-&gt;G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换请求次数_GSM切向GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[150]+evtIdCount[151]  }</Item>
                  <Item name="Descrition" typeName="String">G-&gt;G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功次数_GSM切向GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[150]}</Item>
                  <Item name="Descrition" typeName="String">G-&gt;G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换请求次数_TD切向TD_含接力切换</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[144]+evtIdCount[147]+evtIdCount[145]+evtIdCount[148]}</Item>
                  <Item name="Descrition" typeName="String">T-&gt;T（含接力切换）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功次数_TD切向TD_含接力切换</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[144]+evtIdCount[147]}</Item>
                  <Item name="Descrition" typeName="String">T-&gt;T（含接力切换）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">时长占比_TD（%）</Item>
                  <Item name="Formula" typeName="String">{100*Tx_0825/(Tx_0825+Tx_0823)}% </Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">时长占比_GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*Tx_0823/(Tx_0825+Tx_0823)}% </Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">弱覆盖占总测试里程比例_95_TD（%）</Item>
                  <Item name="Formula" typeName="String">{100*(1-Tx_5C040323/Tx_0865)}</Item>
                  <Item name="Descrition" typeName="String">低于-95的覆盖里程占总里程的比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深度覆盖不足占总测试里程比例_80_TD（%）</Item>
                  <Item name="Formula" typeName="String">{100*(1-Tx_5C040321/Tx_0865)}</Item>
                  <Item name="Descrition" typeName="String">低于-80的覆盖里程占总里程的比例</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">呼叫切换比</Item>
                  <Item name="Formula" typeName="String">{(((evtIdCount[100]+value9[100])-(evtIdCount[104]+evtIdCount[188]+value9[104]+value9[188]))+((evtIdCount[112]+value9[112])-(evtIdCount[116]+evtIdCount[189]+value9[116]+value9[189])))/evtIdCount[141]}</Item>
                  <Item name="Descrition" typeName="String">有过滤，呼叫切换比=（在3G下起呼建立通话的主叫次数+被叫次数）/（主叫从3G切到2G+被叫从3G切到2G次数之和）。建议参考目标值：12，该值越大证明TD网络占用情况越好</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">PCCPCH_C/I低于-3比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_5C04031A-Tx_5C040317)/Tx_5C04031A }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">里程掉话比_TD+GSM（公里/每掉话）</Item>
                  <Item name="Formula" typeName="String">{(Tx_0806/2000)/(evtIdCount[105]+evtIdCount[111]+evtIdCount[117]+evtIdCount[123]+evtIdCount[198]+evtIdCount[199]+value9[105]+value9[111]+value9[117]+value9[123]+value9[198]+value9[199] )}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">连续覆盖率_TD</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_5C040301+Tx_5C040302+Tx_5C040303+Tx_5C040304) /Tx_5C04030A }%</Item>
                  <Item name="Descrition" typeName="String">【目标覆盖区域内导频信号电平大于等于-85dBm采样点数/总样本点数】*100%</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">深度覆盖率_TD</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_5C040301+Tx_5C040302+Tx_5C040303) /Tx_5C04030A }%</Item>
                  <Item name="Descrition" typeName="String">【目标覆盖区域内导频信号电平大于等于-80dBm采样点数/总样本点数】*100%</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">小区重选成功率_大唐（%）</Item>
                  <Item name="Formula" typeName="String">{100*evtIdCount[235]/(evtIdCount[235]+evtIdCount[236])}%</Item>
                  <Item name="Descrition" typeName="String">大唐手机芯片独有</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">DPCH_C/I大于等于-3的比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*Tx_5C040417/Tx_5C04041A }%</Item>
                  <Item name="Descrition" typeName="String">采样点（C/I &gt;=-3dB）/总采样点×100％</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RxLevSub90覆盖率_GSM</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_640417+Tx_64040A+Tx_640409+Tx_640408)/Tx_640401 }%</Item>
                  <Item name="Descrition" typeName="String">TD下GSM90覆盖率</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">TD数据业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+evtIdCount[58]+value9[58]}</Item>
                  <Item name="Descrition" typeName="String">下载成功+下载失败</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[57])*100/((evtIdCount[57]+evtIdCount[58]+value9[58]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]+value9[58] }</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[58]+value9[58])*100/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String">
                      有过滤
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载数据掉线比（KByte/每掉线）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])/(1024*(evtIdCount[58]+value9[58])) }</Item>
                  <Item name="Descrition" typeName="String">
                      下载数据量/掉线次数，该值越大越好
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载总数据量_不含掉线（KByte）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])/1024 }</Item>
                  <Item name="Descrition" typeName="String">不含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP总下载时长_不含掉线（秒）</Item>
                  <Item name="Formula" typeName="String">{(value4[57])/1000}</Item>
                  <Item name="Descrition" typeName="String">不含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_不含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])*(1000*8)/((value4[57])*1024) }</Item>
                  <Item name="Descrition" typeName="String">不含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载总数据量_含掉线（KByte）</Item>
                  <Item name="Formula" typeName="String">{(value1[58]+value1[57]+value1[91])/1024}</Item>
                  <Item name="Descrition" typeName="String">含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP总下载时长_含掉线（秒）</Item>
                  <Item name="Formula" typeName="String">{(value4[57]+value4[58]+value4[91])/1000}</Item>
                  <Item name="Descrition" typeName="String">含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57]+value1[58]+value1[91])*(1000*8)/((value4[57]+value4[58]+value4[91])*1024) }</Item>
                  <Item name="Descrition" typeName="String">含掉线</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载大于等于500Kbps比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_05126402010B+Tx_05126402010C+Tx_05126402010D+Tx_05126402010E+Tx_05126402010F+Tx_051264020110+Tx_051264020111+Tx_051264020112+Tx_05056402010B+Tx_05056402010C+Tx_05056402010D+Tx_05056402010E+Tx_05056402010F+Tx_050564020110+Tx_050564020111+Tx_050564020112)/(Tx_051264020105+Tx_051264020106+Tx_051264020107+Tx_051264020108+Tx_051264020109+Tx_05126402010A+Tx_05126402010B+Tx_05126402010C+Tx_05126402010D+Tx_05126402010E+Tx_05126402010F+Tx_051264020110+Tx_051264020111+Tx_051264020112+Tx_050564020105+Tx_050564020106+Tx_050564020107+Tx_050564020108+Tx_050564020109+Tx_05056402010A+Tx_05056402010B+Tx_05056402010C+Tx_05056402010D+Tx_05056402010E+Tx_05056402010F+Tx_050564020110+Tx_050564020111+Tx_050564020112) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流30秒次数</Item>
                  <Item name="Formula" typeName="String">{value5[215]+value5[216]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流60秒次数</Item>
                  <Item name="Formula" typeName="String">{value6[215]+value6[216]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流120秒次数</Item>
                  <Item name="Formula" typeName="String">{value7[215]+value7[216]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流180秒次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[78]+value9[78]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP_30秒断流_掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value5[215]+value5[216]+evtIdCount[97]+value9[97]+evtIdCount[92]+value9[92])/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String">
                      有过滤
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长_含小区重选（小时）</Item>
                  <Item name="Formula" typeName="String">{(value4[215]+value4[216])/3600000 }小时</Item>
                  <Item name="Descrition" typeName="String">含小区重选</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长_不含小区重选（小时）</Item>
                  <Item name="Formula" typeName="String">{value4[216]/3600000 }小时</Item>
                  <Item name="Descrition" typeName="String">不含小区重选</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_低于0Kbps_含小区重选（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value4[215]+value4[216])/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String">断流时长/总下载时长（含小区重选，低于0Kbps为断流）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_不含小区重选（%）</Item>
                  <Item name="Formula" typeName="String">{100*value4[216]/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String">断流时长/总下载时长（不含小区重选，低于0Kbps为断流）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_低于10Kbps（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value4[992])/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String">断流时长/总下载时长（低于10Kbps为断流）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉线次数_RAU更新失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[92]+value9[92]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉线次数_PDP去激活</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[97]+value9[97]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均RAU时延（秒）</Item>
                  <Item name="Formula" typeName="String">{(value1[131]+value1[134])/((evtIdCount[131]+evtIdCount[134])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU更新成功率（%）</Item>
                  <Item name="Formula" typeName="String">{100*evtIdCount[131]/(evtIdCount[131]+evtIdCount[132])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">小区重选次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[138]+evtIdCount[136]+evtIdCount[178]+evtIdCount[180]+evtIdCount[144]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">小区重选间隔（秒）</Item>
                  <Item name="Formula" typeName="String">{(Tx_0805/1000)/(evtIdCount[138]+evtIdCount[136]+evtIdCount[178]+evtIdCount[180]+evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150] )}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">编码方式_QPSK比例（%）</Item>
                  <Item name="Formula" typeName="String">{100-((Tx_5C050706*Tx_5C050709)+(Tx_5C120706*Tx_5C120709))/(Tx_5C050706+Tx_5C120706) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">编码方式_16QAM比例（%）</Item>
                  <Item name="Formula" typeName="String">{((Tx_5C050706*Tx_5C050709)+(Tx_5C120706*Tx_5C120709))/(Tx_5C050706+Tx_5C120706) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长_GPRS/EDGE（秒）</Item>
                  <Item name="Formula" typeName="String">{(value2[212]+value4[212])/1000 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长_R4（秒）</Item>
                  <Item name="Formula" typeName="String">{(value2[57])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长_HSDPA（秒）</Item>
                  <Item name="Formula" typeName="String">{(value6[57])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长_GPRS/EDGE+R4+HSDPA（秒）</Item>
                  <Item name="Formula" typeName="String">{(value2[212]+value4[212]+value2[57]+value6[57])/1000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长比例_GPRS/EDGE（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value2[212]+value4[212])/(value2[212]+value4[212]+value2[57]+value6[57])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长比例_R4（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value2[57])/(value2[212]+value4[212]+value2[57]+value6[57])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载时长比例_HSDPA（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value6[57])/(value2[212]+value4[212]+value2[57]+value6[57])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_适用于栅（(Kbps）</Item>
                  <Item name="Formula" typeName="String">{(Tx_050564020101+Tx_051264020101)*(1000*8)/((Tx_050564020102+Tx_051264020102)*1024) } </Item>
                  <Item name="Descrition" typeName="String">建议用于gis上的栅格呈现 </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载速率小于300Kbps里程比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Tx_050564020116+Tx_051264020116)/Tx_084F }%</Item>
                  <Item name="Descrition" typeName="String">下载速率小于300kbps的里程占比</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">下载速率小于300Kbps采样点比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*((Tx_050564020103+Tx_051264020103)-(Tx_050564020115+Tx_051264020115))/(Tx_050564020103+Tx_051264020103) }% </Item>
                  <Item name="Descrition" typeName="String">采样点占比</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载数据掉线比（KByte/每掉线）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])/(1024*(evtIdCount[58]+value9[58])) }%</Item>
                  <Item name="Descrition" typeName="String">下载数据总量/掉线次数</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">TD事件</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[21]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：22 | 业务类型：数据 | 事件说明：信令出现Attach Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[22]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：23 | 业务类型：数据 | 事件说明：信令出现Attach Accept，与最近的Attach Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[23]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：24 | 业务类型：数据 | 事件说明：超过30s没有出现Attach Accept，判断为Failure</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Detach Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[24]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：25 | 业务类型：数据 | 事件说明：信令出现Detach Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Detach Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[25]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：26 | 业务类型：数据 | 事件说明：信令出现Detach Accept，与最近的Detach Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[29]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：30 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[30]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：31 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Reject</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[31]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：32 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Deactive PDP Context Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[34]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：35 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Deactive PDP Context Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[35]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：36 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[44]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：45 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[45]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：46 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：47 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[47]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：48 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：49 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[49]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：50 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[50]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：51 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[51]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：52 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[52]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：53 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[53]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：54 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[54]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：55 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[55]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：56 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[56]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：57 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：58 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：59 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[59]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：60 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[60]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：61 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[61]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：62 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[62]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：63 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[63]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：64 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Push</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[64]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：65 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[65]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：66 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[66]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：67 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[67]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：68 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[68]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：69 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Out</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[69]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：70 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[70]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：71 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Receieved</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[71]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：72 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[72]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：73 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[75]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：76 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Received Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[76]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：77 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon wap reply</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[77]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：78 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail三分钟无数据</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[78]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：79 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">信令缺失</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[88]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：89 | 业务类型：通用 | 事件说明：超过10秒无层三信令</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[89]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：90 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP gateway connected</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[90]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：91 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail_下载超时十分钟</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[91]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：92 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail_路由区更新拒绝</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[92]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：93 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail网络去活PDP</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[97]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：98 | 业务类型：数据 | 事件说明：网络测发起的去活PDP</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail主动去活PDP</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[98]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：99 | 业务类型：数据 | 事件说明：下载尚未结束，主动发去活PDP</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[100]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：101 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[101]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：102 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[102]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：103 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[103]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：104 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[104]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：105 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[105]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：106 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MO_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[106]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：107 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MO_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[107]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：108 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MO_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[108]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：109 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MO_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[109]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：110 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MO_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[110]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：111 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MO_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[111]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：112 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[112]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：113 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[113]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：114 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[114]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：115 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[115]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：116 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[116]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：117 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[117]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：118 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MT_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[118]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：119 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MT_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[119]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：120 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MT_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[120]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：121 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MT_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[121]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：122 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MT_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[122]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：123 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_MT_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[123]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：124 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_LocationUpdate_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[124]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：125 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_LocationUpdate_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[125]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：126 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_LocationUpdate_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[126]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：127 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_LocationUpdate_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[127]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：128 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_LocationUpdate_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[128]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：129 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_LocationUpdate_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[129]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：130 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_RAU_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[130]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：131 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_RAU_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[131]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：132 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_RAU_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[132]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：133 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_RAU_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[133]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：134 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_RAU_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[134]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：135 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GSM_RAU_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[135]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：136 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_T2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[136]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：137 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_Fail_T2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[137]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：138 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_G2T</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[138]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：139 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_Fail_G2T</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[139]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：140 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverRequest_T2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[140]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：141 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverSuccess_T2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[141]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：142 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverFail_T2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[142]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：143 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverRequest_IntraT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[143]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：144 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverSuccess_IntraT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[144]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：145 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverFail_IntraT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[145]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：146 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverRequest_Baton</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[146]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：147 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverSuccess_Baton</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[147]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：148 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverFail_Baton</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[148]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：149 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverRequest_IntraG</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[149]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：150 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverSuccess_IntraG</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[150]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：151 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverFail_IntraG</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[151]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：152 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_R4_DataDrop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[152]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：153 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_GPRSEDGE_DataDrop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[153]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：154 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[154]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：155 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_Cir_Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[155]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：156 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_VP_Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[156]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：157 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_Disonnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[157]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：158 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[158]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：159 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[159]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：160 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_CirConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[160]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：161 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_VP_Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[161]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：162 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_Disonnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[162]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：163 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[163]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：164 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_PPP_Dial_Start</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[164]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：165 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_PPP_Dial_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[165]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：166 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_FTP_Download_Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[166]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：167 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_FTP_Send_RETR</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[167]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：168 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_FTP_Download_First_Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[168]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：169 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_FTP_Download_Last_Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[169]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：170 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_FTP_Download_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[170]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：171 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_FTP_Download_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[171]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：172 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RRC Connection Completed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[172]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：173 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAB Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[173]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：174 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAB Setup Completed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[174]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：175 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAB Setup Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[175]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：176 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellUPdate</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[176]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：177 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellUPdateConfirm</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[177]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：178 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_T2T</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[178]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：179 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_Fail_T2T</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[179]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：180 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_G2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[180]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：181 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_CellReselection_Fail_G2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[181]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：182 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[182]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：183 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_Failed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[183]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：184 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[184]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：185 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_Failed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[185]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：186 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TDWeakCoverage</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[186]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：187 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TDLowRate</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[187]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：188 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[188]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：189 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[189]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：190 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[190]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：191 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[191]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：192 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_弱覆盖掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[192]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：193 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_弱覆盖掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[193]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：194 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MO_弱覆盖呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[194]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：195 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_VP_MT_弱覆盖呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[195]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：196 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD-2G主叫接通</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[196]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：197 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD-2G被叫接通</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[197]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：198 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD-2G主叫掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[198]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：199 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD-2G被叫掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[199]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：200 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD-2G主叫呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[200]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：201 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD-2G被叫呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[201]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：202 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_CallAttemptRetry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[202]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：203 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_CallAttemptRetry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[203]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：204 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MO_CallAttemptTo2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[204]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：205 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_MT_CallAttemptTo2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[205]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：206 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RRC Connection Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[206]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：207 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RRC Connect Reject</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[207]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：208 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD弱覆盖路段_100M_M80</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[208]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：209 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD弱覆盖路段(100米)</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[209]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：210 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低_300米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[210]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：211 | 业务类型：数据 | 事件说明：下载速率小于500Kbps持续300米</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低_500米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[211]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：212 | 业务类型：数据 | 事件说明：下载速率小于500Kbps持续500米</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success_GSMSTAT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[212]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：213 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD弱覆盖路段_BM95</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[213]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：214 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD弱覆盖路段_BM80</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[214]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：215 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_有重选</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[215]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：216 | 业务类型：数据 | 事件说明：下载速率=0持续2秒以上，且中间出现小区重选(value2记录GSM断流时长，value3记录TD断流时长，value4记录总断流时长，value5=1标识为30秒断流事)</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_无重选</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[216]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：217 | 业务类型：数据 | 事件说明：下载速率=0持续2秒以上，且中间未出现小区重选(value2记录GSM断流时长，value3记录TD断流时长，value4记录总断流时长，value5=1标识为30秒断流事件)</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续弱C2I</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[217]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：218 | 业务类型：通用 | 事件说明：C2I大于-3比例低于30%持续15秒</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD弱C2I路段_100米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[218]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：219 | 业务类型：通用 | 事件说明：C/I小于-3持续100米</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD场强快衰落</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[219]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：220 | 业务类型：通用 | 事件说明：信号强度从较好状态（持续6秒超过-85）进入较差状态（持续5秒低于-85）</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">湖北移动_PCCPCH_C2I差路段_M85</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[220]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：221 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">湖北移动_PCCPCH_C2I差路段_M95</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[221]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：222 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">云南移动_TD弱覆盖路段_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[222]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：223 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">云南移动_TD深度覆盖不足路段_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[223]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：224 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">持续弱MOS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[224]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：225 | 业务类型：语音 | 事件说明：连续4个或4个以上MOS值低于2.8</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">弱MOS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[225]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：226 | 业务类型：语音 | 事件说明：MOS值低于2.8</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">弱覆盖8秒负85</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[226]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：227 | 业务类型：通用 | 事件说明：PCCPCH_RSCP小于-85dBm，持续8秒。在第8秒生成事件</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">湖北移动_弱覆盖85_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[227]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：228 | 业务类型：语音 | 事件说明：PCCPCH Rscp小于=-85dBm持续50米及以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">湖北移动_高BLER5_50米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[228]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：229 | 业务类型：语音 | 事件说明：TD高BLER路段：BLER大于等于5%持续50米及以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆_持续高BLER</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[229]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：230 | 业务类型：语音 | 事件说明：BLER持续3秒大于等于5</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_RBReconfigRequest</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[230]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：231 | 业务类型：通用 | 事件说明：当有radioBearerReconfiguration信令生成时，就生成请求事件：TD_RBReconfigRequest</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_HandoverSuccess_RBReconfigT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[231]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：232 | 业务类型：通用 | 事件说明：当有radioBearerReconfigurationComplete信令生成后，如主服务小区有变化（即RBReconfig前后LAC-CI有变化）就生成切换事件：TD_HandoverSuccess_RBReconfigT</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_liftingSpeed_RBReconfigT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[232]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：233 | 业务类型：通用 | 事件说明：当有radioBearerReconfigurationComplete信令生成后，如主服务小区没变化（即RBReconfig前后LAC-CI没变化）就生成升降速事件：TD_liftingSpeed_RBReconfigT</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">TD_RBReconfig_Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[233]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：234 | 业务类型：通用 | 事件说明：当有radioBearerReconfigurationfailure信令生成时，就生成失败事件：TD_RBReconfig_Failure</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">cell_reselection_start_in_TDD_Mode</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[234]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：235 | 业务类型：通用 | 事件说明：大唐芯片独有</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Cell_reselection_Success_in_TDD_Mode</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[235]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：236 | 业务类型：通用 | 事件说明：大唐芯片独有</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Cell_reselection_Failure_in_TDD_Mode</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[236]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：237 | 业务类型：通用 | 事件说明：大唐芯片独有</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">上海_BLER质差路段</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[237]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：238 | 业务类型：语音 | 事件说明：10秒内BLER大于等于5%的采样点比例大于90%的路段。</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">覆盖拐角效应</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[238]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：239 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换不及时</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[239]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：240 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">孤岛效应</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[240]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：241 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_30S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[403]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：404 | 业务类型：数据 | 事件说明：下载速率=0持续30秒以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">陕西_FTP下载速率低_300k20s</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[429]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：430 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低于10K</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[992]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：993 | 业务类型：数据 | 事件说明：下载速率小于10Kbps持续2秒及以上</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">CDMA</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">CDMA语音业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">90覆盖率</Item>
                  <Item name="Formula" typeName="String">{100*Cx_6E061F/Cx_6E0621 }%</Item>
                  <Item name="Descrition" typeName="String">TxPower &lt;= 15 and Rxagc &gt;= -90 and ec/io &gt;=  -12</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">94覆盖率</Item>
                  <Item name="Formula" typeName="String">{100*Cx_6E0623/Cx_6E0621 }%</Item>
                  <Item name="Descrition" typeName="String">TxPower &lt;= 20 and Rxagc &gt;= -94 and ec/io &gt;=  -12</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫试呼次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[300]+value9[300]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫接通次数</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[300]+value9[300])-(evtIdCount[307]+evtIdCount[309]+value9[307]+value9[309])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫接通次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[313]+value9[313]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[307]+value9[307]+evtIdCount[309]+value9[309]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_主叫接通率（%）</Item>
                  <Item name="Formula" typeName="String">{100.0*((evtIdCount[300]+value9[300])-(evtIdCount[307]+evtIdCount[309]+value9[307]+value9[309]))/(evtIdCount[300]+value9[300]) }%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[305]+value9[305]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[306]+value9[306]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[305]+value9[305]+evtIdCount[306]+value9[306]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率（%）</Item>
                  <Item name="Formula" typeName="String">{100.0*(evtIdCount[305]+value9[305]+evtIdCount[306]+value9[306])/(evtIdCount[303]+value9[303]+evtIdCount[304]+value9[304])}%</Item>
                  <Item name="Descrition" typeName="String">100*主被叫掉话次数/主被叫接通次数</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主被叫同时掉话次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[327]+value9[327]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">全程呼叫成功率（%）</Item>
                  <Item name="Formula" typeName="String">{(100.0*((evtIdCount[300]+value9[300])-(evtIdCount[307]+evtIdCount[309]+value9[307]+value9[309]))/(evtIdCount[300]+value9[300]))*(1-(evtIdCount[305]+value9[305]+evtIdCount[306]+value9[306])/(evtIdCount[303]+value9[303]+evtIdCount[313]+value9[313]))}%</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均呼叫时延（秒）</Item>
                  <Item name="Formula" typeName="String">{value1[314]/(1000*(evtIdCount[314]+value9[314]))}</Item>
                  <Item name="Descrition" typeName="String">已经剔除建立时廷异常的统计</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;=2.8比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Cx_5A060B58+Cx_5A060B59+Cx_5A060B5A+Cx_5A060B5B)/Cx_5A060B5C }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;=3比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Cx_5A060B59+Cx_5A060B5A+Cx_5A060B5B)/Cx_5A060B5C }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS平均值</Item>
                  <Item name="Formula" typeName="String">{Cx_5A060B53}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">话音质量_FER（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Cx_730601+Cx_730602+Cx_730603+0.7*(Cx_730604+Cx_730605+Cx_730606+Cx_730607+Cx_730608+Cx_730609+Cx_73060A))/(Cx_73060F) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">电平平均值</Item>
                  <Item name="Formula" typeName="String">{Cx_5B060526}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[315]}</Item>
                  <Item name="Descrition" typeName="String">无过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换失败次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[317]}</Item>
                  <Item name="Descrition" typeName="String">无过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[315]-evtIdCount[317])/evtIdCount[315] }%</Item>
                  <Item name="Descrition" typeName="String">无过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">每通话平均切换次数（切换次数/每通话）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[315]-evtIdCount[317])/(evtIdCount[303]+value9[303])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均时速（公里/小时）</Item>
                  <Item name="Formula" typeName="String">{((Cx_0829)/1000)/((Cx_082A)/3600000) }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">里程掉话比（公里/每掉话）</Item>
                  <Item name="Formula" typeName="String">{((Cx_0829)/2000)/(evtIdCount[305]+value9[305]+evtIdCount[306] +value9[306])}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">CDMA数据业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_不含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])*(1000*8)/((value4[57])*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      不含掉线
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57]+value1[58])*(1000*8)/((value4[57]+value4[58])*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      含掉线
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+evtIdCount[58]+value9[58]}</Item>
                  <Item name="Descrition" typeName="String">
                      FTP下载成功+掉线次数
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]+value9[58] }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[58]+value9[58])*100/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_适用于栅格（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(Cx_050764020101*(1000*8))/(Cx_050764020102*1024) }</Item>
                  <Item name="Descrition" typeName="String">建议用于GIS上的栅格呈现</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">CDMA事件</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[44]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：45 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[45]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：46 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：47 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[47]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：48 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：49 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[49]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：50 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[50]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：51 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[51]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：52 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[52]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：53 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[53]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：54 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[54]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：55 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[55]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：56 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[56]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：57 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：58 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：59 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[59]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：60 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[60]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：61 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[61]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：62 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[62]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：63 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[63]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：64 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Push</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[64]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：65 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[65]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：66 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[66]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：67 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[67]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：68 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[68]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：69 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Out</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[69]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：70 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[70]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：71 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ftp Download Fail Atu</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[71]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：72 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[72]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：73 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[75]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：76 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Received Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[76]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：77 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon wap reply</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[77]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：78 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">信令缺失</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[88]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：89 | 业务类型：通用 | 事件说明：超过10秒无层三信令</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[89]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：90 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP gateway connected</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[90]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：91 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success GPRS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[91]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：92 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MO Call Attempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[300]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：301 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Call Attempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[301]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：302 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Call Attempt Retry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[302]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：303 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MO Call ServiceConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[303]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：304 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Call ServiceConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[304]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：305 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MO Drop Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[305]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：306 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Drop Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[306]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：307 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MO Call Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[307]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：308 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Call Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[308]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：309 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MO Block Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[309]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：310 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Block Call</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[310]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：311 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MO Call End</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[311]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：312 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Call End</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[312]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：313 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Call Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[313]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：314 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MT Call Alerting</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[314]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：315 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA SoftHandover command</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[315]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：316 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA SoftHandover Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[316]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：317 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA SoftHandover Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[317]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：318 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA CellReselectTion</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[318]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：319 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Weak Coverage</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[319]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：320 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Weak Quality</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[320]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：321 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Handover Command</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[321]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：322 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Handover Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[322]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：323 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Handover Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[323]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：324 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Registration Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[324]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：325 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Registration Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[325]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：326 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA Registration Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[326]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：327 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">CDMA MOMT DROP[智能预判处理]</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[327]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：328 | 业务类型：语音 | 事件说明：cdma主被叫同时掉话</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[328]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：329 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_30S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[403]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：404 | 业务类型：数据 | 事件说明：下载速率=0持续30秒以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低于10K</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[992]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：993 | 业务类型：数据 | 事件说明：下载速率小于10Kbps持续2秒及以上</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">WCDMA</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">WCDMA语音业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">90覆盖率</Item>
                  <Item name="Formula" typeName="String">{100*Wx_6E0A01/Wx_6E0A03}%</Item>
                  <Item name="Descrition" typeName="String">PCCPCH_RSCP大于-90且PCCPCH_C2I大于-12</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫试呼次数_WCDMA</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[500]+value9[500]}</Item>
                  <Item name="Descrition" typeName="String">WCDMA试呼次数</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫试呼次数_GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[506]+value9[506]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫接通次数_WCDMA</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[500]+value9[500])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫接通次数_WCDMA</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[512]+value9[512])-(evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫接通次数_GSM</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[506]+value9[506])-(evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">被叫接通次数_GSM</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[518]+value9[518])-(evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601])}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数_WCDMA</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">主叫未接通次数_GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]}</Item>
                  <Item name="Descrition" typeName="String">有过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_WCDMA（%）</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[500]+value9[500])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]))/(evtIdCount[500]+value9[500]) }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[506]+value9[506])-(evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]))/(evtIdCount[506]+value9[506])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">接通率_WCDMA+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]))/(evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506])}%</Item>
                  <Item name="Descrition" typeName="String">无过滤</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫_WCDMA</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话次数_主叫+被叫_GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[511]+evtIdCount[523]+evtIdCount[598]+evtIdCount[599]+value9[511]+value9[523]+value9[598]+value9[599]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率_WCDMA（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517])/((evtIdCount[500]+value9[500]+evtIdCount[512]+value9[512])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589]))}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率_GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[511]+evtIdCount[523]+evtIdCount[598]+evtIdCount[599]+value9[511]+value9[523]+value9[598]+value9[599])/((evtIdCount[506]+value9[506]+evtIdCount[518]+value9[518])-(evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]+evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601]))}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">掉话率_WCDMA+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]+evtIdCount[511]+evtIdCount[523]+evtIdCount[598]+evtIdCount[599]+value9[511]+value9[523]+value9[598]+value9[599])/((evtIdCount[500]+value9[500]+evtIdCount[512]+value9[512]+evtIdCount[506]+value9[506]+evtIdCount[518]+value9[518])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]+evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601]))}%</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">全程呼叫成功率_WCDMA+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*(1-((evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]+evtIdCount[511]+evtIdCount[523]+evtIdCount[598]+evtIdCount[599]+value9[511]+value9[523]+value9[598]+value9[599])/((evtIdCount[500]+value9[500]+evtIdCount[512]+value9[512]+evtIdCount[506]+value9[506]+evtIdCount[518]+value9[518])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]+evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601]))))*(((evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]))/(evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506]))}%</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS均值</Item>
                  <Item name="Formula" typeName="String">{Wx_6D0A09}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS&gt;=2.8比例（%）</Item>
                  <Item name="Formula" typeName="String">{100*(Wx_6D0A04+Wx_6D0A05+Wx_6D0A06+Wx_6D0A07+Wx_6D0A0C)/Wx_6D0A08 }%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均呼叫时延_WCDMA（秒）</Item>
                  <Item name="Formula" typeName="String">{value3[502] /(evtIdCount[502]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均呼叫时延_GSM（秒）</Item>
                  <Item name="Formula" typeName="String">{value3[508] /(evtIdCount[508]*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">平均呼叫时延_WCDMA+GSM（秒）</Item>
                  <Item name="Formula" typeName="String">{(value3[508]+value3[502])/((evtIdCount[508]+evtIdCount[502])*1000)}</Item>
                  <Item name="Descrition" typeName="String">
                      
                      包括W网和G网的建立时延
                      
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换请求次数_WCDMA+GSM</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[540]+evtIdCount[543]+evtIdCount[546]+evtIdCount[549] }</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功次数_WCDMA+GSM</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[540]+evtIdCount[543]+evtIdCount[546]+evtIdCount[549])-(evtIdCount[542]+evtIdCount[545]+evtIdCount[548]+evtIdCount[551])}</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">切换成功率_WCDMA+GSM（%）</Item>
                  <Item name="Formula" typeName="String">{100*((evtIdCount[540]+evtIdCount[543]+evtIdCount[546]+evtIdCount[549])-(evtIdCount[542]+evtIdCount[545]+evtIdCount[548]+evtIdCount[551]))/(evtIdCount[540]+evtIdCount[543]+evtIdCount[546]+evtIdCount[549])}%</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS话音业务掉话率（MOS≤3）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[993]+evtIdCount[994]+evtIdCount[995]+evtIdCount[996])/((evtIdCount[500]+value9[500]+evtIdCount[512]+value9[512]+evtIdCount[506]+value9[506]+evtIdCount[518]+value9[518])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]+evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601]))}</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MOS话音业务掉话率（MOS≤2.5）</Item>
                  <Item name="Formula" typeName="String">{100*(evtIdCount[997]+evtIdCount[998]+evtIdCount[999]+evtIdCount[1000])/((evtIdCount[500]+value9[500]+evtIdCount[512]+value9[512]+evtIdCount[506]+value9[506]+evtIdCount[518]+value9[518])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]+evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601]))}</Item>
                  <Item name="Descrition" typeName="String">W+G</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">WCDMA数据业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_不含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])*(1000*8)/((value4[57])*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      不含掉线
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57]+value1[58])*(1000*8)/((value4[57]+value4[58])*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      含掉线
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+evtIdCount[58]+value9[58]}</Item>
                  <Item name="Descrition" typeName="String">
                      FTP下载成功+掉线次数
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]+value9[58] }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[58]+value9[58])*100/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流30秒次数</Item>
                  <Item name="Formula" typeName="String">{value5[961]+value5[962]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流60秒次数</Item>
                  <Item name="Formula" typeName="String">{value6[961]+value6[962]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流120秒次数</Item>
                  <Item name="Formula" typeName="String">{value7[961]+value7[962]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流180秒次数</Item>
                  <Item name="Formula" typeName="String">{value8[961]+value8[962]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP_30秒断流_掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value5[961]+value5[962]+evtIdCount[92]+value9[92]+evtIdCount[97]+value9[97])/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长_含小区重选（小时）</Item>
                  <Item name="Formula" typeName="String">{(value4[961]+value4[962])/3600000 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长_不含小区重选（小时）</Item>
                  <Item name="Formula" typeName="String">{value4[962]/3600000 }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_不含小区重选（%）</Item>
                  <Item name="Formula" typeName="String">{100*value4[962]/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_低于0Kbps_含小区重选（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value4[961]+value4[962])/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_低于10Kbps（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value4[992])/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAU平均时延（秒）</Item>
                  <Item name="Formula" typeName="String">{(value1[531]+value1[534])/((evtIdCount[531]+evtIdCount[534])*1000)}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_适用于栅格（Kbps）</Item>
                  <Item name="Formula" typeName="String">{((Wx_050B01640201+Wx_050F01640201)*(1000*8))/((Wx_050B01640202+Wx_050F01640202)*1024) }</Item>
                  <Item name="Descrition" typeName="String">建议用于gis上的栅格呈现</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">WCDMA事件</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[21]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：22 | 业务类型：数据 | 事件说明：信令出现Attach Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[22]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：23 | 业务类型：数据 | 事件说明：信令出现Attach Accept，与最近的Attach Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Attach Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[23]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：24 | 业务类型：数据 | 事件说明：超过30s没有出现Attach Accept，判断为Failure</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Detach Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[24]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：25 | 业务类型：数据 | 事件说明：信令出现Detach Request</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">GPRS Detach Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[25]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：26 | 业务类型：数据 | 事件说明：信令出现Detach Accept，与最近的Detach Request匹配</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[29]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：30 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[30]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：31 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Active PDP Reject</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[31]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：32 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Deactive PDP Context Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[34]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：35 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Deactive PDP Context Accept</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[35]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：36 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[44]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：45 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[45]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：46 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[46]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：47 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Page Refresh Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[47]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：48 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[48]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：49 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[49]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：50 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[50]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：51 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Upload Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[51]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：52 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[52]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：53 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Kjava Download Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[53]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：54 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[54]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：55 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ping fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[55]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：56 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[56]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：57 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：58 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：59 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Began</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[59]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：60 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[60]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：61 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[61]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：62 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[62]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：63 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Send Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[63]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：64 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Push</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[64]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：65 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[65]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：66 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS Retrieve Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[66]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：67 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[67]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：68 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">MMS P2P Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[68]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：69 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Out</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[69]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：70 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Send Failure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[70]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：71 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Ftp Download Fail Atu</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[71]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：72 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[72]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：73 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS P2P Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[75]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：76 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">SMS Received Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[76]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：77 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP Logon wap reply</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[77]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：78 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">信令缺失</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[88]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：89 | 业务类型：通用 | 事件说明：超过10秒无层三信令</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Upload First Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[89]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：90 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WAP gateway connected</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[90]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：91 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success GPRS</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[91]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：92 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail_路由区更新拒绝</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[92]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：93 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success PS64</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[93]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：94 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success PS128</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[94]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：95 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success PS384</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[95]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：96 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success HSDPA</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[96]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：97 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail网络去活PDP</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[97]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：98 | 业务类型：数据 | 事件说明：网络测发起的去活PDP</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Fail主动去活PDP</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[98]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：99 | 业务类型：数据 | 事件说明：下载尚未结束，主动发去活PDP</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP Download Success_GSMSTAT</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[212]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：213 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_30S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[403]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：404 | 业务类型：数据 | 事件说明：下载速率=0持续30秒以上</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">Http Page Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[406]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：407 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[407]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：408 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">HTTP Page Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[408]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：409 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[500]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：501 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[501]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：502 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[502]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：503 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[503]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：504 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[504]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：505 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[505]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：506 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[506]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：507 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[507]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：508 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[508]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：509 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[509]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：510 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[510]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：511 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[511]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：512 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[512]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：513 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[513]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：514 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[514]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：515 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[515]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：516 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[516]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：517 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[517]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：518 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[518]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：519 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[519]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：520 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[520]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：521 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[521]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：522 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_CallFail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[522]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：523 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[523]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：524 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_LocationUpdate_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[524]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：525 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_LocationUpdate_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[525]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：526 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_LocationUpdate_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[526]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：527 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_LocationUpdate_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[527]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：528 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_LocationUpdate_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[528]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：529 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_LocationUpdate_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[529]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：530 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_RAU_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[530]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：531 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_RAU_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[531]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：532 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_RAU_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[532]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：533 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_RAU_Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[533]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：534 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_RAU_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[534]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：535 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_RAU_Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[535]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：536 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_W2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[536]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：537 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_Fail_W2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[537]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：538 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_G2W</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[538]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：539 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_Fail_G2W</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[539]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：540 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverRequest_W2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[540]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：541 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverSuccess_W2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[541]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：542 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverFail_W2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[542]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：543 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverRequest_IntraW</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[543]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：544 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverSuccess_IntraW</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[544]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：545 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverFail_IntraW</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[545]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：546 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverRequest_Soft</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[546]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：547 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverSuccess_Soft</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[547]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：548 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverFail_Soft</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[548]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：549 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverRequest_IntraG</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[549]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：550 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverSuccess_IntraG</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[550]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：551 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_HandoverFail_IntraG</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[551]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：552 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_R4_DataDrop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[552]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：553 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GPRSEDGE_DataDrop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[553]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：554 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[554]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：555 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_Cir_Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[555]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：556 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_Disonnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[557]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：558 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[558]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：559 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_CallAttempt</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[559]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：560 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_CirConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[560]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：561 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_Disonnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[562]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：563 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[563]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：564 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_PPP_Dial_Start</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[564]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：565 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_PPP_Dial_Success</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[565]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：566 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_FTP_Download_Connect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[566]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：567 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_FTP_Send_RETR</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[567]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：568 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_FTP_Download_First_Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[568]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：569 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_FTP_Download_Last_Data</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[569]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：570 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_FTP_Download_Disconnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[570]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：571 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_FTP_Download_Drop</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[571]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：572 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RRC Connection Completed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[572]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：573 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAB Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[573]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：574 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAB Setup Completed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[574]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：575 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RAB Setup Fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[575]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：576 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellUPdate</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[576]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：577 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellUPdateConfirm</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[577]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：578 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_W2W</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[578]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：579 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_Fail_W2W</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[579]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：580 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_G2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[580]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：581 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_CellReselection_Fail_G2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[581]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：582 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[582]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：583 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_Failed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[583]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：584 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_Established</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[584]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：585 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_Failed</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[585]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：586 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMAWeakCoverage</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[586]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：587 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMALowRate</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[587]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：588 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[588]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：589 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[589]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：590 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[590]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：591 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_NOConnect</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[591]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：592 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_弱覆盖掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[592]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：593 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_弱覆盖掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[593]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：594 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MO_弱覆盖呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[594]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：595 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_VP_MT_弱覆盖呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[595]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：596 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA-2G主叫接通</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[596]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：597 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA-2G被叫接通</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[597]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：598 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA-2G主叫掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[598]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：599 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA-2G被叫掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[599]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：600 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA-2G主叫呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[600]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：601 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA-2G被叫呼叫失败</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[601]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：602 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_CallAttemptRetry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[602]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：603 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_CallAttemptRetry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[603]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：604 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_CallAttemptW2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[604]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：605 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_CallAttemptW2G</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[605]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：606 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_weakQuality</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[606]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：607 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_VP_CallAttemptRetry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[607]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：608 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_VP_CallAttemptRetry</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[608]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：609 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GPRS_HandoverRequest</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[609]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：610 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GPRS_HandoverSuccess</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[610]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：611 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GPRS_HandoverFailure</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[611]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：612 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_Drop_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[612]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：613 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_Drop_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[613]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：614 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_VP_Drop_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[614]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：615 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MT_VP_Drop_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[615]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：616 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_Drop_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[616]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：617 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MT_Drop_NotNormal</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[617]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：618 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RRC Connection Request</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[618]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：619 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">RRC Connection fail</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[619]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：620 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA弱覆盖_08密集城区</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[620]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：621 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA弱覆盖_08县城</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[621]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：622 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA弱覆盖_08一般区域</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[622]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：623 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA弱覆盖_10密集城区</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[623]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：624 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA弱覆盖_10县城</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[624]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：625 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA弱覆盖_10一般区域</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[625]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：626 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖路段</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[626]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：627 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通MOS值连续5次大于4</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[627]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：628 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通MOS值连续5次小于2</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[628]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：629 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通BLER_B1PCT_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[629]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：630 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通BLER_U3PCT_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[630]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：631 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通EcIo_BM14DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[631]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：632 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通EcIo_UM8DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[632]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：633 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通RSCP_BM95DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[633]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：634 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通RSCP_UM90DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[634]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：635 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通RxPower_BM90DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[635]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：636 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通RxPower_UM75DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[636]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：637 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通SIR_U12DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[637]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：638 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通SIR_B6DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[638]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：639 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通TxPower_BM15DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[639]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：640 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通TxPower_U20DB_10S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[640]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：641 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">压缩模式启动</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[641]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：642 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">压缩模式终止</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[642]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：643 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖_BM90_5S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[643]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：644 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_市区弱覆盖_BM85_20S</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[644]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：645 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖路段_40米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[645]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：646 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">新疆联通_弱覆盖路段_400米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[646]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：647 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MO业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[647]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：648 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MT业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[648]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：649 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MO通话质差(MOS)</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[649]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：650 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MT通话质差(MOS)</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[650]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：651 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_GSM_MO_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[651]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：652 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">WCDMA_MO_Setup</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[652]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：653 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">分组业务连续建立失败差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[653]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：654 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载业务掉线</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[654]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：655 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP上传业务掉线</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[655]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：656 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音信令掉话差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[656]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：657 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音业务通话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[657]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：658 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[658]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：659 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音业务掉话差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[659]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：660 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">联通话音连续未接通差点</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[660]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：661 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MO业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[925]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：926 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MT业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[926]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：927 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MO通话质差(MOS)</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[927]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：928 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_MT通话质差(MOS)</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[928]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：929 | 业务类型：语音 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_HSUPA业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[930]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：931 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_FTP成功收到第1包数据</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[931]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：932 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_FTP成功发送第1包数据</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[932]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：933 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_HSDPA业务掉话</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[933]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：934 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">浙江联通_弱覆盖路段_200米</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[934]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：935 | 业务类型：通用 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_有重选</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[961]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：962 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP零下载速率_无重选</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[962]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：963 | 业务类型：数据 | 事件说明：</Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率低于10K</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[992]}</Item>
                  <Item name="Descrition" typeName="String">事件ID：993 | 业务类型：数据 | 事件说明：下载速率小于10Kbps持续2秒及以上</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="KPIFamily">
          <Item name="Name" typeName="String">EVDO</Item>
          <Item name="SubType" typeName="IList">
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">EVDO数据业务</Item>
              <Item name="KPIFormulas" typeName="IList">
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_不含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57])*(1000*8)/((value4[57])*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      不含掉线
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_含掉线（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(value1[57]+value1[58])*(1000*8)/((value4[57]+value4[58])*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      含掉线
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载请求次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]+evtIdCount[58]+value9[58]}</Item>
                  <Item name="Descrition" typeName="String">
                      FTP下载成功+掉线次数
                  </Item>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载成功次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[57]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线次数</Item>
                  <Item name="Formula" typeName="String">{evtIdCount[58]+value9[58] }</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{(evtIdCount[58]+value9[58])*100/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流30秒次数</Item>
                  <Item name="Formula" typeName="String">{value5[328]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流60秒次数</Item>
                  <Item name="Formula" typeName="String">{value6[328]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流120秒次数</Item>
                  <Item name="Formula" typeName="String">{value7[328]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流180秒次数</Item>
                  <Item name="Formula" typeName="String">{value8[328]}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP_30秒断流_掉线率（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value5[328]+evtIdCount[92]+value9[92]+evtIdCount[97]+value9[97])/(evtIdCount[57]+evtIdCount[58]+value9[58])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长（小时）</Item>
                  <Item name="Formula" typeName="String">{value4[328]/3600000}</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_低于0Kbps（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value4[328])/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">断流时长比例_低于10Kbps（%）</Item>
                  <Item name="Formula" typeName="String">{100*(value4[992])/(value4[57]+value4[58]+value4[91]+value4[98])}%</Item>
                  <Item name="Descrition" typeName="String"/>
                </Item>
                <Item typeName="KPIFormula">
                  <Item name="Name" typeName="String">FTP下载速率_适用于栅格（Kbps）</Item>
                  <Item name="Formula" typeName="String">{(Ex_050964020101*(1000*8))/(Ex_050964020102*1024) }</Item>
                  <Item name="Descrition" typeName="String">
                      建议用于gis上的栅格呈现
                  </Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="KPIFamilySubType">
              <Item name="Name" typeName="String">EVDO事件</Item>
              <Item name="KPIFormulas" typeName="IList"/>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>