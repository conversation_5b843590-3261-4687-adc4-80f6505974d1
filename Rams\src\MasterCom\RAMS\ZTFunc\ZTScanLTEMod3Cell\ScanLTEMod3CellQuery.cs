﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanLTEMod3CellQueryByFile : DIYReplayFileQuery
    {
        public ScanLTEMod3CellQueryByFile(MainModel mm)
            : base(mm)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            queryer = ScanLTEMod3CellQuery.Instance;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryer.QueryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            return option;
        }

        protected override void doPostReplayAction()
        {
            queryer.GetResultAfterQuery();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowResult();
            queryer.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            queryer.DoWithTestPoint(tp);
        }

        private readonly ScanLTEMod3CellQuery queryer;
    }

    public class ScanLTEMod3CellQueryByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public ScanLTEMod3CellQueryByRegion(MainModel mm)
            : base(mm)
        {
            IncludeEvent = false;
            IncludeMessage = false;
            FilterEventByRegion = false;
            FilterSampleByRegion = true;
            queryer = ScanLTEMod3CellQuery.Instance;
            Columns = new List<string>(queryer.QueryColumns);
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override void fireShowForm()
        {
            queryer.FireShowResult();
            queryer.Clear();
        }

        protected override void getResultsAfterQuery()
        {
            queryer.GetResultAfterQuery();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                foreach (TestPoint tp in testPointList)
                {
                    queryer.DoWithTestPoint(tp);
                }
            }
        }

        protected ScanLTEMod3CellQuery queryer;
    }

    public class ScanLTEMod3CellQuery
    {
        public static ScanLTEMod3CellQuery Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ScanLTEMod3CellQuery();
                }
                return instance;
            }
        }

        public virtual string Name
        {
            get { return "模三小区"; }
        }

        public virtual MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23015, "模三小区");
        }

        public virtual List<string> QueryColumns
        {
            get { return queryColumns; }
        }

        public virtual bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new ScanLTEMod3CellSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            queryCond = setForm.GetCondition();
            return true;
        }

        protected Dictionary<LTECell, ScanLTEMod3TargetCell> tarCellDic = new Dictionary<LTECell, ScanLTEMod3TargetCell>();
        public void DoWithTestPoint(TestPoint tp)
        {
            float? maxRsrp = null;
            float? maxSinr = null;
            ScanLTEMod3TargetCell targetCell = null;

            bool isInterfered = false;
            for (int i = 0; i < 50; ++i)
            {
                bool hasGet = setTargetCell(tp, ref maxRsrp, ref maxSinr, ref targetCell, ref isInterfered, i);
                if (!hasGet)
                {
                    break;
                }
            }

            if (targetCell != null)
            {
                targetCell.AddTestPoint(tp, (double)maxRsrp, (double)maxSinr, isInterfered);
            }
        }

        private bool setTargetCell(TestPoint tp, ref float? maxRsrp, ref float? maxSinr, ref ScanLTEMod3TargetCell targetCell, ref bool isInterfered, int i)
        {
            // 小区匹配
            float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
            float? sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];
            int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
            int? pci = (short?)tp["LTESCAN_TopN_PCI", i];

            if (rsrp == null || sinr == null || earfcn == null || pci == null)
            {
                return false;
            }
            LTECell lteCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);
            if (lteCell == null)
            {
                return false;
            }

            // 第一强作为目标小区
            if (i == 0)
            {
                maxRsrp = rsrp;
                maxSinr = sinr;
                if (!tarCellDic.TryGetValue(lteCell, out targetCell))
                {
                    targetCell = new ScanLTEMod3TargetCell(lteCell, (int)earfcn, (int)pci);
                    tarCellDic.Add(lteCell, targetCell);
                }

                if (rsrp < queryCond.MinRsrp || sinr > queryCond.MaxSinr)
                {
                    return false; // rsrp和sinr不满足条件，不需要判断干扰
                }
            }
            else
            {
                bool isValid = judgeContainsMod3(maxRsrp, targetCell, rsrp, pci);
                if (isValid)
                {
                    // 对目标小区产生干扰的源小区
                    isInterfered = true;
                    addSourceCell(tp, targetCell, rsrp, sinr, earfcn, pci, lteCell);
                }
            }
            return true;
        }

        private bool judgeContainsMod3(float? maxRsrp, ScanLTEMod3TargetCell targetCell, float? rsrp, int? pci)
        {
            // 非第一强频点，但不存在模三干扰
            if (maxRsrp - rsrp <= queryCond.DiffRsrp && targetCell.Pci % 3 == (int)pci % 3)
            {
                return true;
            }
            return false;
        }


        private void addSourceCell(TestPoint tp, ScanLTEMod3TargetCell targetCell, float? rsrp, float? sinr, int? earfcn, int? pci, LTECell lteCell)
        {
            ScanLTEMod3SourceCell sourceCell = null;
            if (!targetCell.SrcCellDic.TryGetValue(lteCell, out sourceCell))
            {
                sourceCell = new ScanLTEMod3SourceCell(lteCell, (int)earfcn, (int)pci);
                targetCell.SrcCellDic.Add(lteCell, sourceCell);
            }
            sourceCell.AddTestPoint(tp, (double)rsrp, (double)sinr);
        }

        protected List<ScanLTEMod3TargetCell> targetCellList = new List<ScanLTEMod3TargetCell>();
        public virtual void GetResultAfterQuery()
        {
            foreach (ScanLTEMod3TargetCell tarCell in tarCellDic.Values)
            {
                tarCell.GetResult();
                if (tarCell.SampleCount < queryCond.MinSampleCount || tarCell.SourceCells.Count == 0)
                {
                    continue;
                }
                targetCellList.Add(tarCell);
            }
        }

        public virtual void FireShowResult()
        {
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            ScanLTEMod3CellResultForm resultForm = MainModel.GetObjectFromBlackboard(typeof(ScanLTEMod3CellResultForm).FullName) as ScanLTEMod3CellResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ScanLTEMod3CellResultForm(MainModel);
            }
            resultForm.FillData(new List<ScanLTEMod3TargetCell>(targetCellList));
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        public virtual void Clear()
        {
            tarCellDic.Clear();
            targetCellList.Clear();
        }

        private ScanLTEMod3CellQuery()
        {
            this.MainModel = MainModel.GetInstance();
        }

        protected MainModel MainModel;
        protected ScanLTEMod3CellCondition queryCond;
        protected ScanLTEMod3CellSettingForm setForm;


        private readonly List<string> queryColumns = new List<string>()
        {
            "isampleid",
            "itime",
            "ilongitude",
            "ilatitude",
            "LTESCAN_TopN_PSS_RP", 
            "LTESCAN_TopN_EARFCN",
            "LTESCAN_TopN_PCI",
            "LTESCAN_TopN_CELL_Specific_RSRP",
            "LTESCAN_TopN_CELL_Specific_RSSINR",
        };

        private static ScanLTEMod3CellQuery instance;
    }

    public class ScanLTEMod3CellCondition
    {
        public double MinRsrp { get; set; }
        public double MaxSinr { get; set; }
        public double DiffRsrp { get; set; }
        public int MinSampleCount { get; set; }
    }

    public class ScanLTEMod3CellItem
    {
        public LTECell LteCell
        {
            get;
            protected set;
        }

        public string CellName
        {
            get { return LteCell == null ? string.Format("{0}_{1}", Earfcn, Pci) : LteCell.Name; }
        }

        public virtual double AvgRsrp
        {
            get { return SampleCount == 0 ? 0 : rsrpSum / SampleCount; }
        }

        public virtual double AvgSinr
        {
            get { return SampleCount == 0 ? 0 : sinrSum / SampleCount; }
        }

        public string Tac
        {
            get { return LteCell == null ? "" : LteCell.TAC.ToString(); }
        }

        public string Eci
        {
            get { return LteCell == null ? "" : LteCell.ECI.ToString(); }
        }

        public int Earfcn
        {
            get;
            protected set;
        }

        public int Pci
        {
            get;
            protected set;
        }

        public int SampleCount
        {
            get;
            protected set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            protected set;
        }

        public void AddTestPoint(TestPoint tp, double rsrp, double sinr)
        {
            TestPoints.Add(tp);
            rsrpSum += rsrp;
            sinrSum += sinr;
            SampleCount += 1;
        }

        public ScanLTEMod3CellItem(LTECell cell, int earfcn, int pci)
        {
            LteCell = cell;
            Earfcn = earfcn;
            Pci = pci;
            TestPoints = new List<TestPoint>();
        }

        protected double rsrpSum;
        protected double sinrSum;
    }

    public class ScanLTEMod3SourceCell : ScanLTEMod3CellItem
    {
        public double InterferTargetRate
        {
            get;
            private set;
        }

        public double InterfereTargetTotalRate
        {
            get;
            private set;
        }

        public ScanLTEMod3TargetCell TargetCell
        {
            get;
            private set;
        }

        public void GetResult(ScanLTEMod3TargetCell tarCell)
        {
            InterferTargetRate = tarCell.InterferedSampleCount == 0 ? 0 : 1d * SampleCount / tarCell.InterferedSampleCount;
            InterfereTargetTotalRate = tarCell.SampleCount == 0 ? 0 : 1d * SampleCount / tarCell.SampleCount;
            TargetCell = tarCell;
        }

        public ScanLTEMod3SourceCell(LTECell cell, int earfcn, int pci) : base(cell, earfcn, pci)
        {
        }
    }

    public class ScanLTEMod3TargetCell : ScanLTEMod3CellItem
    {
        public override double AvgRsrp
        {
            get { return InterferedSampleCount == 0 ? 0 : 1d * base.rsrpSum / InterferedSampleCount; }
        }

        public override double AvgSinr
        {
            get { return InterferedSampleCount == 0 ? 0 : 1d * base.sinrSum / InterferedSampleCount; }
        }

        public int InterferedSampleCount //  受干扰点数
        {
            get;
            private set;
        }

        public List<ScanLTEMod3SourceCell> SourceCells
        {
            get;
            private set;
        }

        public Dictionary<LTECell, ScanLTEMod3SourceCell> SrcCellDic
        {
            get { return srcCellDic; }
        }

        public void AddTestPoint(TestPoint tp, double rsrp, double sinr, bool isInterfered)
        {
            if (isInterfered)
            {
                base.AddTestPoint(tp, rsrp, sinr);
                ++InterferedSampleCount;
            }
            else
            {
                ++SampleCount; // 只对总点数加1
                TestPoints.Add(tp);
            }
        }

        public void GetResult()
        {
            SourceCells = new List<ScanLTEMod3SourceCell>(srcCellDic.Values);
            foreach (ScanLTEMod3SourceCell srcCell in SourceCells)
            {
                srcCell.GetResult(this);
            }
        }

        public ScanLTEMod3TargetCell(LTECell cell, int earfcn, int pci) : base(cell, earfcn, pci)
        {
        }

        private readonly Dictionary<LTECell, ScanLTEMod3SourceCell> srcCellDic = new Dictionary<LTECell, ScanLTEMod3SourceCell>();
    }
}
