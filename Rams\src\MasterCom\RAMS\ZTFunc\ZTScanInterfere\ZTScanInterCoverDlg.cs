using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanInterCoverDlg : Form
    {
        MainModel mainModel = null;

        public ZTScanInterCoverDlg(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }

        private void rbtnShowBoth_CheckedChanged(object sender, EventArgs e)
        {
            if (rbtnShowBoth.Checked)
            {
                mainModel.DTDataManager = DTDataOperation.GetWholeDTDataManager(mainModel.OrgDTDataManager, mainModel.JamDTDataManager);
                mainModel.FireDTDataChanged(this);
            }
        }

        private void rbtnShowOrg_CheckedChanged(object sender, EventArgs e)
        {
            if (rbtnShowOrg.Checked)
            {
                mainModel.DTDataManager = mainModel.OrgDTDataManager;
                mainModel.FireDTDataChanged(this);
            }
        }

        private void rbtnShowJam_CheckedChanged(object sender, EventArgs e)
        {
            if (rbtnShowJam.Checked)
            {
                mainModel.DTDataManager = mainModel.JamDTDataManager;
                mainModel.FireDTDataChanged(this);
            }
        }

        private void rbtnShowSame_CheckedChanged(object sender, EventArgs e)
        {
            if (rbtnShowSame.Checked)
            {
                mainModel.DTDataManager = DTDataOperation.GetSameDTDataManager(mainModel.OrgDTDataManager, mainModel.JamDTDataManager,20);
                mainModel.FireDTDataChanged(this);
            }
        }
    }
}