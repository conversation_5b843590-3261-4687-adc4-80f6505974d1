<?xml version="1.0"?>
<Configs>
  <Config name="Main">
    <Item name="WorkSpace" typeName="WorkSpace">
      <Item name="WorkSheets" typeName="IList">
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">文件列表</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.FileInfoForm</Item>
              <Item name="ImageFilePath" typeName="String" />
              <Item name="Text" typeName="String">文件列表</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">0</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">826</Item>
              <Item name="SizeHeight" typeName="Int32">199</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">-1</Item>
              <Item name="BeforeLocationY" typeName="Int32">-1</Item>
              <Item name="Param" typeName="IDictionary" />
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">欢迎页</Item>
          <Item name="ChildFormConfigs" typeName="IList" />
          <Item name="ActiveChildFormIndex" typeName="Int32">-1</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">指标列表</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.TestPointGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">指标列表</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">-41</Item>
              <Item name="LocationY" typeName="Int32">13</Item>
              <Item name="SizeWidth" typeName="Int32">1251</Item>
              <Item name="SizeHeight" typeName="Int32">584</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="IList" key="Columns">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">Common Param</Item>
                    <Item typeName="String" key="ParamName">FileName</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">FileName</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">Common Param</Item>
                    <Item typeName="String" key="ParamName">Time</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">Time</Item>
                    <Item typeName="Int32" key="Width">163</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">Common Param</Item>
                    <Item typeName="String" key="ParamName">Longitude</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">Longitude</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">Common Param</Item>
                    <Item typeName="String" key="ParamName">Latitude</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">Latitude</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">SCell_LAC</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">SCell_LAC</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">SCell_CI</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">SCell_CI</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">SCell_RAC</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">SCell_RAC</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">SCell_RNC</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">SCell_RNC</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_WorkUARFCN</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_WorkUARFCN</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_PDSCH_RSCP</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_PDSCH_RSCP</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_PDSCH_CI</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_PDSCH_CI</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_PDSCH_AverageSize</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_PDSCH_AverageSize</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_SCCH_RSCP</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_SCCH_RSCP</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_SCCH_CI</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_SCCH_CI</Item>
                    <Item typeName="Int32" key="Width">114</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_ScchScheduled_Count</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_ScchScheduled_Count</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_ScchScheduled_Rate</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_ScchScheduled_Rate</Item>
                    <Item typeName="Int32" key="Width">112</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_16QAM_Rate</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_16QAM_Rate</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_QPSK_Rate</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_QPSK_Rate</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_CQI_Max</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_CQI_Max</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_CQI_Mean</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_CQI_Mean</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_CQI_Min</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_CQI_Min</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_DSCH_ACKRate</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_DSCH_ACKRate</Item>
                    <Item typeName="Int32" key="Width">124</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_HS_DSCH_NACKRate</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_HS_DSCH_NACKRate</Item>
                    <Item typeName="Int32" key="Width">104</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">HSDPA_MAC_HSPDU_RecvRate</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">HSDPA_MAC_HSPDU_RecvRate</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">APP_Speed</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">APP_Speed</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">AppThroughputDL_kb</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">AppThroughputDL_kb</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="SystemName">TD-SCDMA</Item>
                    <Item typeName="String" key="ParamName">APP_TransferedSize</Item>
                    <Item typeName="Int32" key="ArrayIndex">0</Item>
                    <Item typeName="String" key="HeaderText">APP_TransferedSize</Item>
                    <Item typeName="Int32" key="Width">100</Item>
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">KPI统计</Item>
        </Item>
      </Item>
      <Item name="SelectedIndex" typeName="Int32">3</Item>
    </Item>
  </Config>
</Configs>