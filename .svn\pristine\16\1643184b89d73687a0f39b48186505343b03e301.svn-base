﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc.ProblemGridQuery
{
    public partial class SettingDlg : BaseDialog
    {
        public SettingDlg()
        {
            InitializeComponent();
            chkAll.Checked = true;
            chkAll.CheckedChanged += chkAll_CheckedChanged;
            tvCity.Nodes.Clear();
            foreach (IDNamePair item in mainModel.User.GetAvailableCitys())
            {
                TreeNode node = new TreeNode(item.Name);
                node.Checked = true;
                node.Tag = item;
                tvCity.Nodes.Add(node);
            }
            dtFrom.Value = DateTime.Now.AddMonths(-1).Date;
            dtTo.Value = dtFrom.Value.AddMonths(1).AddDays(1).AddMilliseconds(-1);
        }

        private void chkAll_CheckedChanged(object sender, EventArgs e)
        {
            foreach (TreeNode node in tvCity.Nodes)
            {
                node.Checked = chkAll.Checked;
            }
        }

        List<int> ids = new List<int>();
        public List<int> CityIDSet
        {
            get { return ids; }
            set
            {
                if (value==null)
                {
                    return;
                }
                ids.Clear();
                ids.AddRange(value);
                foreach (TreeNode node in tvCity.Nodes)
                {
                    IDNamePair item = node.Tag as IDNamePair;
                    node.Checked = ids.Contains(item.id);
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dtTo.Value < dtFrom.Value)
            {
                MessageBox.Show("结束时间不能小于开始时间！");
                return;
            }
            ids.Clear();
            foreach (TreeNode node in tvCity.Nodes)
            {
                if (node.Checked)
                {
                    IDNamePair item = node.Tag as IDNamePair;
                    ids.Add(item.id);
                }
            }
            if (ids.Count == 0)
            {
                MessageBox.Show("请至少选择一个地市!");
                return;
            }
            DialogResult = DialogResult.OK;
        }


        public DateTime DateFrom
        {
            get { return dtFrom.Value; }
            set { dtFrom.Value = value; }
        }

        public DateTime DateTo
        {
            get { return dtTo.Value; }
            set { dtTo.Value = value; }
        }
    }
}
