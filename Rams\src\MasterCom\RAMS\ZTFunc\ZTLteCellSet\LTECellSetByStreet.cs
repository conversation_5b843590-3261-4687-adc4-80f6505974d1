﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Net
{
    public class LTECellSetByStreet : ZTDIYCellSetByStreet
    {
        public LTECellSetByStreet(MainModel mainModel)
            : base(mainModel)
        {
        }

        public LTECellSetByStreet(bool isVoLTE)
            : this(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }

        public override string Name
        {
            get { return "LTE道路小区集分析"; }
        }
        public override string IconName
        {
            get { return "Images/streetq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22065, this.Name);
        }
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SetConditionServerType();
            rxQualMin = -40;
            rxQualMax = 0;
            ZTCellSetSettingDlg dlg = new ZTCellSetSettingDlg();
            dlg.Fill(rxQualMin, rxQualMax);
            dlg.SetLableTxt("   ≤  RSRQ  ≤");
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            rxQualMin = dlg.RxqualSubMin;
            rxQualMax = dlg.RxqualSubMax;
            return true;
        }
    }

    public class LTECellSetByStreet_FDD : LTECellSetByStreet
    {
        public LTECellSetByStreet_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE道路小区集分析LTE_FDD"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26055, this.Name);
        }
    }
    public class LTECellSetByStreet_FDD_VOLTE : LTECellSetByStreet_FDD
    {
        public LTECellSetByStreet_FDD_VOLTE(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD道路小区集分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30004, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }
}
