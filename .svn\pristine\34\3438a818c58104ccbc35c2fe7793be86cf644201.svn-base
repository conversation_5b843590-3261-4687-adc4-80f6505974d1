﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTdCellEmulateCoverAna: DIYSampleByRegion
    {
        public ZTTdCellEmulateCoverAna(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "TD小区覆盖仿真"; }
        }

        //protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        //{
        //    return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12060, "查询");
        //}

        MapOperation2 mapOp2 = null;
        List<List<LongLat>> cellSimulationPointsList = null;
        List<TDCell> cellList = null;
        protected override void query()
        {
            cellSimulationPointsList = new List<List<LongLat>>();
            mapOp2 = new MapOperation2();
            cellList = new List<TDCell>();

            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
                return;

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";

                MainModel.ClearDTData();

                MapWinGIS.Shape curRegion = Condition.Geometorys.Region;
                mapOp2.FillPolygon(curRegion);
                WaitBox.Show("开始绘制小区仿真点...", queryInThread, clientProxy);

                MainModel.CellEmulationPointsList = cellSimulationPointsList;
                MainModel.FireCellDrawInfoChanged(this);

            }
            catch
            {
                clientProxy.Close();
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup group = new DIYSampleGroup();
            group.ThemeName = "---";
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("TD_SCell_LAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                group.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TD_SCell_CI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                group.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TD_SCell_UARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                group.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TD_SCell_CPI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                group.ColumnsDefSet.Add(pDef);
            }

            return group;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude)
                    && tp is TestPointDetail)//进行天线分析算法运算
                {
                    TDCell mainCell = tp.GetMainCell_TD_TDCell();
                    if (mainCell == null)
                    {
                        return;
                    }
                    if (mainCell.Direction > 360)
                    {
                        return;
                    }
                    double cellDistance = mainCell.GetDistance((tp.Longitude), (tp.Latitude));

                    //屏蔽超远小区
                    if (cellDistance > CD.MAX_COV_DISTANCE_TD)
                    {
                        return;
                    }
                    if (cellList.Contains(mainCell))
                    {
                        return;
                    }
                    else
                    {
                        cellList.Add(mainCell);
                    }

                    List<LongLat> longLatList = new List<LongLat>();
                    LongLat ll = new LongLat();
                    ll.fLongitude = (float)(mainCell.Longitude);
                    ll.fLatitude = (float)(mainCell.Latitude);
                    longLatList.Add(ll);

                    longLatList.AddRange(getCellEmulateCover(ll, (int)(mainCell.Direction), mainCell.Altitude, (int)(mainCell.Downword)));


                    cellSimulationPointsList.Add(longLatList);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        /// <summary>
        /// 小区覆盖仿真采样点
        /// </summary>
        private List<LongLat> getCellEmulateCover(LongLat btsLongLat, int iangle_dir, int ialtitude, int iangle_ob)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();
            float coverDistance = 0;
            if (iangle_ob > 0 && iangle_ob < 90)
            {
                coverDistance = (float)(ialtitude / Math.Tan(iangle_ob * Math.PI / 180));
            }
            if (coverDistance < 300)
                coverDistance = 300;
            else if (coverDistance > 1000)
                coverDistance = 1000;

            int sDir = iangle_dir - 60;
            int eDir = iangle_dir + 60;
            for (int i = sDir; i <= eDir; i += 5)
            {
                LongLat tLongLat = calcPointX(i, coverDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }

        /// <summary>
        /// 粗略定位另一点信息
        /// </summary>
        /// <param name="iangle">夹角</param>
        public LongLat calcPointX(int iangle, float idis, LongLat s)
        {
            LongLat e = new LongLat();
            double a = Math.Cos((90 - iangle) * 2 * Math.PI / 360);
            double b = Math.Sin((90 - iangle) * 2 * Math.PI / 360);
            e.fLongitude = s.fLongitude + (float)(a * (idis / 40075360) * 360);//32507969.15
            e.fLatitude = s.fLatitude + (float)(b * (idis / 39940670) * 360); //40172187.93
            return e;
        }
    }
}
