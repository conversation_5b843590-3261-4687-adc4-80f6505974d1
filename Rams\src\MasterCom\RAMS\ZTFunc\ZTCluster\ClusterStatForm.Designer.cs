﻿namespace MasterCom.RAMS.ZTFunc.ZTCluster
{
    partial class ClusterStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.tabPageClique = new System.Windows.Forms.TabPage();
            this.tabCtrlEstimate = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.listViewCliqueEstimate = new System.Windows.Forms.ListView();
            this.columnHeader16 = new System.Windows.Forms.ColumnHeader();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.listViewMeanSquareError = new System.Windows.Forms.ListView();
            this.columnHeader17 = new System.Windows.Forms.ColumnHeader();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.listViewMeanRate = new System.Windows.Forms.ListView();
            this.columnHeader18 = new System.Windows.Forms.ColumnHeader();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.listViewOverCover = new System.Windows.Forms.ListView();
            this.columnHeader19 = new System.Windows.Forms.ColumnHeader();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.listViewInjectRate = new System.Windows.Forms.ListView();
            this.columnHeader24 = new System.Windows.Forms.ColumnHeader();
            this.listViewClique = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCellCount = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFreqNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderMeanFreqNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderMeanVariance = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderClique = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderMeanQuality = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderArea = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderOverCoverNum = new System.Windows.Forms.ColumnHeader();
            this.ctxClique = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowCliqueInMap = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowRelativeCluster = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Excel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageCluster = new System.Windows.Forms.TabPage();
            this.listViewCluster = new System.Windows.Forms.ListView();
            this.columnHeaderNO = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCellNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCluster = new System.Windows.Forms.ColumnHeader();
            this.columnHeader15 = new System.Windows.Forms.ColumnHeader();
            this.ctxCluster = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowClusterInMap = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowCliquesOfCluster = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageClusterInfo = new System.Windows.Forms.TabPage();
            this.listViewCellOfCluster = new System.Windows.Forms.ListView();
            this.columnHeader8 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader9 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader10 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader13 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader20 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader25 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader21 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader22 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader23 = new System.Windows.Forms.ColumnHeader();
            this.ctxClusterCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowCellClique = new System.Windows.Forms.ToolStripMenuItem();
            this.miCheckClusterCellFreq = new System.Windows.Forms.ToolStripMenuItem();
            this.miCheckCellInterference = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowClusterCellGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.miExp2Excel = new System.Windows.Forms.ToolStripMenuItem();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.listViewCliquesOfCluster = new System.Windows.Forms.ListView();
            this.columnHeader4 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader5 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader6 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader11 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader7 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader12 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader14 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader30 = new System.Windows.Forms.ColumnHeader();
            this.ctxCliqueOfCluster = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miHilightCliqueOfCluaster = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportCofC = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageDisCoNum = new System.Windows.Forms.TabPage();
            this.listViewDisCoStat = new System.Windows.Forms.ListView();
            this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader2 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader3 = new System.Windows.Forms.ColumnHeader();
            this.ctxDisCoStat = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowDisCoStatInMap = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowDetails = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.tabPageDetails = new System.Windows.Forms.TabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.rbtnByBCCH = new System.Windows.Forms.RadioButton();
            this.rbtnByDCH = new System.Windows.Forms.RadioButton();
            this.listViewDetails = new System.Windows.Forms.ListView();
            this.columnHeaderNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderSource = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTarget = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCoNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderAdjNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderSouDCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTarDCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFreqDes = new System.Windows.Forms.ColumnHeader();
            this.tabPageInterference = new System.Windows.Forms.TabPage();
            this.listViewCellInterference = new System.Windows.Forms.ListView();
            this.columnHeader26 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader27 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader28 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader29 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader31 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader32 = new System.Windows.Forms.ColumnHeader();
            this.tabPageExport = new System.Windows.Forms.TabPage();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.btnExportXls = new System.Windows.Forms.Button();
            this.btnBrowse = new System.Windows.Forms.Button();
            this.tbxExportPath = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.ctxClusterGrid = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowClusterGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.miFindOverCoverCellCluster = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxDetails = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.cbxShowClique = new System.Windows.Forms.CheckBox();
            this.cbxShowCluster = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.cbxShowSelectedCell = new System.Windows.Forms.CheckBox();
            this.cbxShowDSC1800 = new System.Windows.Forms.CheckBox();
            this.cbxShowGSM900 = new System.Windows.Forms.CheckBox();
            this.tabControl.SuspendLayout();
            this.tabPageClique.SuspendLayout();
            this.tabCtrlEstimate.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.ctxClique.SuspendLayout();
            this.tabPageCluster.SuspendLayout();
            this.ctxCluster.SuspendLayout();
            this.tabPageClusterInfo.SuspendLayout();
            this.ctxClusterCell.SuspendLayout();
            this.ctxCliqueOfCluster.SuspendLayout();
            this.tabPageDisCoNum.SuspendLayout();
            this.ctxDisCoStat.SuspendLayout();
            this.tabPageDetails.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPageInterference.SuspendLayout();
            this.tabPageExport.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.ctxClusterGrid.SuspendLayout();
            this.ctxDetails.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.Controls.Add(this.tabPageClique);
            this.tabControl.Controls.Add(this.tabPageCluster);
            this.tabControl.Controls.Add(this.tabPageClusterInfo);
            this.tabControl.Controls.Add(this.tabPageDisCoNum);
            this.tabControl.Controls.Add(this.tabPageDetails);
            this.tabControl.Controls.Add(this.tabPageInterference);
            this.tabControl.Controls.Add(this.tabPageExport);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(862, 511);
            this.tabControl.TabIndex = 0;
            // 
            // tabPageClique
            // 
            this.tabPageClique.Controls.Add(this.tabCtrlEstimate);
            this.tabPageClique.Controls.Add(this.listViewClique);
            this.tabPageClique.Location = new System.Drawing.Point(4, 21);
            this.tabPageClique.Name = "tabPageClique";
            this.tabPageClique.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageClique.Size = new System.Drawing.Size(854, 486);
            this.tabPageClique.TabIndex = 0;
            this.tabPageClique.Text = "全部Clique";
            this.tabPageClique.UseVisualStyleBackColor = true;
            // 
            // tabCtrlEstimate
            // 
            this.tabCtrlEstimate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.tabCtrlEstimate.Appearance = System.Windows.Forms.TabAppearance.FlatButtons;
            this.tabCtrlEstimate.Controls.Add(this.tabPage1);
            this.tabCtrlEstimate.Controls.Add(this.tabPage2);
            this.tabCtrlEstimate.Controls.Add(this.tabPage3);
            this.tabCtrlEstimate.Controls.Add(this.tabPage4);
            this.tabCtrlEstimate.Controls.Add(this.tabPage5);
            this.tabCtrlEstimate.Location = new System.Drawing.Point(3, 247);
            this.tabCtrlEstimate.Name = "tabCtrlEstimate";
            this.tabCtrlEstimate.SelectedIndex = 0;
            this.tabCtrlEstimate.Size = new System.Drawing.Size(848, 236);
            this.tabCtrlEstimate.TabIndex = 1;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.listViewCliqueEstimate);
            this.tabPage1.Location = new System.Drawing.Point(4, 24);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(840, 208);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "clique评估";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // listViewCliqueEstimate
            // 
            this.listViewCliqueEstimate.AllowColumnReorder = true;
            this.listViewCliqueEstimate.AutoArrange = false;
            this.listViewCliqueEstimate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader16});
            this.listViewCliqueEstimate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCliqueEstimate.FullRowSelect = true;
            this.listViewCliqueEstimate.GridLines = true;
            this.listViewCliqueEstimate.HideSelection = false;
            this.listViewCliqueEstimate.LabelWrap = false;
            this.listViewCliqueEstimate.Location = new System.Drawing.Point(3, 3);
            this.listViewCliqueEstimate.Name = "listViewCliqueEstimate";
            this.listViewCliqueEstimate.ShowGroups = false;
            this.listViewCliqueEstimate.Size = new System.Drawing.Size(834, 202);
            this.listViewCliqueEstimate.TabIndex = 0;
            this.listViewCliqueEstimate.UseCompatibleStateImageBehavior = false;
            this.listViewCliqueEstimate.View = System.Windows.Forms.View.Details;
            this.listViewCliqueEstimate.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewCliqueEstimate_MouseClick);
            // 
            // columnHeader16
            // 
            this.columnHeader16.Text = "片区名/时间";
            this.columnHeader16.Width = 90;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.listViewMeanSquareError);
            this.tabPage2.Location = new System.Drawing.Point(4, 24);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(840, 208);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "全网小区载波数标准差";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // listViewMeanSquareError
            // 
            this.listViewMeanSquareError.AllowColumnReorder = true;
            this.listViewMeanSquareError.AutoArrange = false;
            this.listViewMeanSquareError.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader17});
            this.listViewMeanSquareError.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMeanSquareError.FullRowSelect = true;
            this.listViewMeanSquareError.GridLines = true;
            this.listViewMeanSquareError.HideSelection = false;
            this.listViewMeanSquareError.LabelWrap = false;
            this.listViewMeanSquareError.Location = new System.Drawing.Point(3, 3);
            this.listViewMeanSquareError.Name = "listViewMeanSquareError";
            this.listViewMeanSquareError.ShowGroups = false;
            this.listViewMeanSquareError.Size = new System.Drawing.Size(834, 202);
            this.listViewMeanSquareError.TabIndex = 0;
            this.listViewMeanSquareError.UseCompatibleStateImageBehavior = false;
            this.listViewMeanSquareError.View = System.Windows.Forms.View.Details;
            this.listViewMeanSquareError.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewMeanSquareError_MouseClick);
            // 
            // columnHeader17
            // 
            this.columnHeader17.Text = "片区名/时间";
            this.columnHeader17.Width = 91;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.listViewMeanRate);
            this.tabPage3.Location = new System.Drawing.Point(4, 24);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(840, 208);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "平均载波利用率";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // listViewMeanRate
            // 
            this.listViewMeanRate.AllowColumnReorder = true;
            this.listViewMeanRate.AutoArrange = false;
            this.listViewMeanRate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader18});
            this.listViewMeanRate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMeanRate.FullRowSelect = true;
            this.listViewMeanRate.GridLines = true;
            this.listViewMeanRate.HideSelection = false;
            this.listViewMeanRate.LabelWrap = false;
            this.listViewMeanRate.Location = new System.Drawing.Point(0, 0);
            this.listViewMeanRate.Name = "listViewMeanRate";
            this.listViewMeanRate.ShowGroups = false;
            this.listViewMeanRate.Size = new System.Drawing.Size(840, 208);
            this.listViewMeanRate.TabIndex = 0;
            this.listViewMeanRate.UseCompatibleStateImageBehavior = false;
            this.listViewMeanRate.View = System.Windows.Forms.View.Details;
            this.listViewMeanRate.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewMeanRate_MouseClick);
            // 
            // columnHeader18
            // 
            this.columnHeader18.Text = "片区名/时间";
            this.columnHeader18.Width = 83;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.listViewOverCover);
            this.tabPage4.Location = new System.Drawing.Point(4, 24);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(840, 208);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "过覆盖评估";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // listViewOverCover
            // 
            this.listViewOverCover.AllowColumnReorder = true;
            this.listViewOverCover.AutoArrange = false;
            this.listViewOverCover.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader19});
            this.listViewOverCover.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewOverCover.FullRowSelect = true;
            this.listViewOverCover.GridLines = true;
            this.listViewOverCover.HideSelection = false;
            this.listViewOverCover.LabelWrap = false;
            this.listViewOverCover.Location = new System.Drawing.Point(0, 0);
            this.listViewOverCover.Name = "listViewOverCover";
            this.listViewOverCover.ShowGroups = false;
            this.listViewOverCover.Size = new System.Drawing.Size(840, 208);
            this.listViewOverCover.TabIndex = 0;
            this.listViewOverCover.UseCompatibleStateImageBehavior = false;
            this.listViewOverCover.View = System.Windows.Forms.View.Details;
            this.listViewOverCover.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewOverCover_MouseClick);
            // 
            // columnHeader19
            // 
            this.columnHeader19.Text = "片区名/时间";
            this.columnHeader19.Width = 82;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.listViewInjectRate);
            this.tabPage5.Location = new System.Drawing.Point(4, 24);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Size = new System.Drawing.Size(840, 208);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "渗透率";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // listViewInjectRate
            // 
            this.listViewInjectRate.AllowColumnReorder = true;
            this.listViewInjectRate.AutoArrange = false;
            this.listViewInjectRate.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader24});
            this.listViewInjectRate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewInjectRate.FullRowSelect = true;
            this.listViewInjectRate.GridLines = true;
            this.listViewInjectRate.HideSelection = false;
            this.listViewInjectRate.LabelWrap = false;
            this.listViewInjectRate.Location = new System.Drawing.Point(0, 0);
            this.listViewInjectRate.Name = "listViewInjectRate";
            this.listViewInjectRate.ShowGroups = false;
            this.listViewInjectRate.Size = new System.Drawing.Size(840, 208);
            this.listViewInjectRate.TabIndex = 1;
            this.listViewInjectRate.UseCompatibleStateImageBehavior = false;
            this.listViewInjectRate.View = System.Windows.Forms.View.Details;
            this.listViewInjectRate.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewInjectRate_MouseClick);
            // 
            // columnHeader24
            // 
            this.columnHeader24.Text = "片区名/时间";
            this.columnHeader24.Width = 82;
            // 
            // listViewClique
            // 
            this.listViewClique.AllowColumnReorder = true;
            this.listViewClique.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewClique.AutoArrange = false;
            this.listViewClique.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderCellCount,
            this.columnHeaderFreqNum,
            this.columnHeaderMeanFreqNum,
            this.columnHeaderMeanVariance,
            this.columnHeaderClique,
            this.columnHeaderMeanQuality,
            this.columnHeaderArea,
            this.columnHeaderOverCoverNum});
            this.listViewClique.ContextMenuStrip = this.ctxClique;
            this.listViewClique.FullRowSelect = true;
            this.listViewClique.GridLines = true;
            this.listViewClique.HideSelection = false;
            this.listViewClique.LabelWrap = false;
            this.listViewClique.Location = new System.Drawing.Point(3, 3);
            this.listViewClique.Name = "listViewClique";
            this.listViewClique.ShowGroups = false;
            this.listViewClique.Size = new System.Drawing.Size(848, 238);
            this.listViewClique.TabIndex = 0;
            this.listViewClique.UseCompatibleStateImageBehavior = false;
            this.listViewClique.View = System.Windows.Forms.View.Details;
            this.listViewClique.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewClique_MouseDoubleClick);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            this.columnHeaderSN.Width = 52;
            // 
            // columnHeaderCellCount
            // 
            this.columnHeaderCellCount.Text = "小区数";
            this.columnHeaderCellCount.Width = 61;
            // 
            // columnHeaderFreqNum
            // 
            this.columnHeaderFreqNum.Text = "设计频点数";
            this.columnHeaderFreqNum.Width = 80;
            // 
            // columnHeaderMeanFreqNum
            // 
            this.columnHeaderMeanFreqNum.Text = "每小区平均载频数";
            this.columnHeaderMeanFreqNum.Width = 111;
            // 
            // columnHeaderMeanVariance
            // 
            this.columnHeaderMeanVariance.Text = "载波均衡度系数";
            this.columnHeaderMeanVariance.Width = 107;
            // 
            // columnHeaderClique
            // 
            this.columnHeaderClique.Text = "Clique内小区列表";
            this.columnHeaderClique.Width = 263;
            // 
            // columnHeaderMeanQuality
            // 
            this.columnHeaderMeanQuality.Text = "平均质量值";
            this.columnHeaderMeanQuality.Width = 84;
            // 
            // columnHeaderArea
            // 
            this.columnHeaderArea.Text = "所属片区";
            this.columnHeaderArea.Width = 81;
            // 
            // columnHeaderOverCoverNum
            // 
            this.columnHeaderOverCoverNum.Text = "过覆盖小区个数";
            this.columnHeaderOverCoverNum.Width = 106;
            // 
            // ctxClique
            // 
            this.ctxClique.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowCliqueInMap,
            this.miShowRelativeCluster,
            this.toolStripSeparator1,
            this.miExport2Excel});
            this.ctxClique.Name = "ctxClique";
            this.ctxClique.Size = new System.Drawing.Size(191, 76);
            // 
            // miShowCliqueInMap
            // 
            this.miShowCliqueInMap.Name = "miShowCliqueInMap";
            this.miShowCliqueInMap.Size = new System.Drawing.Size(190, 22);
            this.miShowCliqueInMap.Text = "高亮Clique包含的小区";
            this.miShowCliqueInMap.Click += new System.EventHandler(this.miShowCliqueInMap_Click);
            // 
            // miShowRelativeCluster
            // 
            this.miShowRelativeCluster.Name = "miShowRelativeCluster";
            this.miShowRelativeCluster.Size = new System.Drawing.Size(190, 22);
            this.miShowRelativeCluster.Text = "转到所属簇";
            this.miShowRelativeCluster.Click += new System.EventHandler(this.miShowRelativeCluster_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(187, 6);
            // 
            // miExport2Excel
            // 
            this.miExport2Excel.Name = "miExport2Excel";
            this.miExport2Excel.Size = new System.Drawing.Size(190, 22);
            this.miExport2Excel.Text = "导出Excel...";
            this.miExport2Excel.Click += new System.EventHandler(this.miExport2Excel_Click);
            // 
            // tabPageCluster
            // 
            this.tabPageCluster.Controls.Add(this.listViewCluster);
            this.tabPageCluster.Location = new System.Drawing.Point(4, 21);
            this.tabPageCluster.Name = "tabPageCluster";
            this.tabPageCluster.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCluster.Size = new System.Drawing.Size(854, 486);
            this.tabPageCluster.TabIndex = 1;
            this.tabPageCluster.Text = "全部簇";
            this.tabPageCluster.UseVisualStyleBackColor = true;
            // 
            // listViewCluster
            // 
            this.listViewCluster.AllowColumnReorder = true;
            this.listViewCluster.AutoArrange = false;
            this.listViewCluster.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderNO,
            this.columnHeaderCellNum,
            this.columnHeaderCluster,
            this.columnHeader15});
            this.listViewCluster.ContextMenuStrip = this.ctxCluster;
            this.listViewCluster.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCluster.FullRowSelect = true;
            this.listViewCluster.GridLines = true;
            this.listViewCluster.HideSelection = false;
            this.listViewCluster.LabelWrap = false;
            this.listViewCluster.Location = new System.Drawing.Point(3, 3);
            this.listViewCluster.Name = "listViewCluster";
            this.listViewCluster.ShowGroups = false;
            this.listViewCluster.Size = new System.Drawing.Size(848, 480);
            this.listViewCluster.TabIndex = 1;
            this.listViewCluster.UseCompatibleStateImageBehavior = false;
            this.listViewCluster.View = System.Windows.Forms.View.Details;
            this.listViewCluster.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewCluster_MouseDoubleClick);
            // 
            // columnHeaderNO
            // 
            this.columnHeaderNO.Text = "序号";
            // 
            // columnHeaderCellNum
            // 
            this.columnHeaderCellNum.Text = "小区个数";
            this.columnHeaderCellNum.Width = 136;
            // 
            // columnHeaderCluster
            // 
            this.columnHeaderCluster.Text = "簇内小区列表";
            this.columnHeaderCluster.Width = 487;
            // 
            // columnHeader15
            // 
            this.columnHeader15.Text = "所属片区";
            this.columnHeader15.Width = 152;
            // 
            // ctxCluster
            // 
            this.ctxCluster.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowClusterInMap,
            this.miShowCliquesOfCluster,
            this.toolStripSeparator2,
            this.miExportExcel});
            this.ctxCluster.Name = "ctxClique";
            this.ctxCluster.Size = new System.Drawing.Size(179, 76);
            // 
            // miShowClusterInMap
            // 
            this.miShowClusterInMap.Name = "miShowClusterInMap";
            this.miShowClusterInMap.Size = new System.Drawing.Size(178, 22);
            this.miShowClusterInMap.Text = "高亮簇包含的小区";
            this.miShowClusterInMap.Click += new System.EventHandler(this.miShowClusterInMap_Click);
            // 
            // miShowCliquesOfCluster
            // 
            this.miShowCliquesOfCluster.Name = "miShowCliquesOfCluster";
            this.miShowCliquesOfCluster.Size = new System.Drawing.Size(178, 22);
            this.miShowCliquesOfCluster.Text = "显示簇的成员Clique";
            this.miShowCliquesOfCluster.Click += new System.EventHandler(this.miShowCliquesOfCluster_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(175, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(178, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // tabPageClusterInfo
            // 
            this.tabPageClusterInfo.Controls.Add(this.listViewCellOfCluster);
            this.tabPageClusterInfo.Controls.Add(this.label12);
            this.tabPageClusterInfo.Controls.Add(this.label11);
            this.tabPageClusterInfo.Controls.Add(this.listViewCliquesOfCluster);
            this.tabPageClusterInfo.Location = new System.Drawing.Point(4, 21);
            this.tabPageClusterInfo.Name = "tabPageClusterInfo";
            this.tabPageClusterInfo.Size = new System.Drawing.Size(854, 486);
            this.tabPageClusterInfo.TabIndex = 4;
            this.tabPageClusterInfo.Text = "所选簇信息";
            this.tabPageClusterInfo.UseVisualStyleBackColor = true;
            // 
            // listViewCellOfCluster
            // 
            this.listViewCellOfCluster.AllowColumnReorder = true;
            this.listViewCellOfCluster.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewCellOfCluster.AutoArrange = false;
            this.listViewCellOfCluster.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader8,
            this.columnHeader9,
            this.columnHeader10,
            this.columnHeader13,
            this.columnHeader20,
            this.columnHeader25,
            this.columnHeader21,
            this.columnHeader22,
            this.columnHeader23});
            this.listViewCellOfCluster.ContextMenuStrip = this.ctxClusterCell;
            this.listViewCellOfCluster.FullRowSelect = true;
            this.listViewCellOfCluster.GridLines = true;
            this.listViewCellOfCluster.HideSelection = false;
            this.listViewCellOfCluster.LabelWrap = false;
            this.listViewCellOfCluster.Location = new System.Drawing.Point(0, 255);
            this.listViewCellOfCluster.Name = "listViewCellOfCluster";
            this.listViewCellOfCluster.ShowGroups = false;
            this.listViewCellOfCluster.Size = new System.Drawing.Size(851, 228);
            this.listViewCellOfCluster.TabIndex = 4;
            this.listViewCellOfCluster.UseCompatibleStateImageBehavior = false;
            this.listViewCellOfCluster.View = System.Windows.Forms.View.Details;
            this.listViewCellOfCluster.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewCellOfCluster_MouseDoubleClick);
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "序号";
            this.columnHeader8.Width = 52;
            // 
            // columnHeader9
            // 
            this.columnHeader9.Text = "小区名称";
            this.columnHeader9.Width = 132;
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "干扰因子";
            this.columnHeader10.Width = 94;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "无线利用率";
            this.columnHeader13.Width = 102;
            // 
            // columnHeader20
            // 
            this.columnHeader20.Text = "是否过覆盖";
            this.columnHeader20.Width = 79;
            // 
            // columnHeader25
            // 
            this.columnHeader25.Text = "干扰的小区个数";
            this.columnHeader25.Width = 99;
            // 
            // columnHeader21
            // 
            this.columnHeader21.Text = "载波数";
            this.columnHeader21.Width = 67;
            // 
            // columnHeader22
            // 
            this.columnHeader22.Text = "所属小区片区";
            this.columnHeader22.Width = 96;
            // 
            // columnHeader23
            // 
            this.columnHeader23.Text = "小区调整建议";
            this.columnHeader23.Width = 135;
            // 
            // ctxClusterCell
            // 
            this.ctxClusterCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowCellClique,
            this.miCheckClusterCellFreq,
            this.miCheckCellInterference,
            this.miShowClusterCellGrid,
            this.toolStripSeparator5,
            this.miExp2Excel});
            this.ctxClusterCell.Name = "ctxClusterCell";
            this.ctxClusterCell.Size = new System.Drawing.Size(191, 120);
            // 
            // miShowCellClique
            // 
            this.miShowCellClique.Name = "miShowCellClique";
            this.miShowCellClique.Size = new System.Drawing.Size(190, 22);
            this.miShowCellClique.Text = "显示小区相关的Clique";
            this.miShowCellClique.Click += new System.EventHandler(this.miShowCellClique_Click);
            // 
            // miCheckClusterCellFreq
            // 
            this.miCheckClusterCellFreq.Name = "miCheckClusterCellFreq";
            this.miCheckClusterCellFreq.Size = new System.Drawing.Size(190, 22);
            this.miCheckClusterCellFreq.Text = "显示小区频率核查";
            this.miCheckClusterCellFreq.Click += new System.EventHandler(this.miCheckClusterCellFreq_Click);
            // 
            // miCheckCellInterference
            // 
            this.miCheckCellInterference.Name = "miCheckCellInterference";
            this.miCheckCellInterference.Size = new System.Drawing.Size(190, 22);
            this.miCheckCellInterference.Text = "显示小区干扰核查";
            this.miCheckCellInterference.Click += new System.EventHandler(this.miCheckCellInterference_Click);
            // 
            // miShowClusterCellGrid
            // 
            this.miShowClusterCellGrid.Name = "miShowClusterCellGrid";
            this.miShowClusterCellGrid.Size = new System.Drawing.Size(190, 22);
            this.miShowClusterCellGrid.Text = "呈现覆盖栅格";
            this.miShowClusterCellGrid.Click += new System.EventHandler(this.miShowClusterCellGrid_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(187, 6);
            // 
            // miExp2Excel
            // 
            this.miExp2Excel.Name = "miExp2Excel";
            this.miExp2Excel.Size = new System.Drawing.Size(190, 22);
            this.miExp2Excel.Text = "导出到Excel...";
            this.miExp2Excel.Click += new System.EventHandler(this.miExp2Excel_Click);
            // 
            // label12
            // 
            this.label12.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(3, 240);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(83, 12);
            this.label12.TabIndex = 3;
            this.label12.Text = "簇内小区信息:";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(8, 6);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(107, 12);
            this.label11.TabIndex = 2;
            this.label11.Text = "簇包含Clique信息:";
            // 
            // listViewCliquesOfCluster
            // 
            this.listViewCliquesOfCluster.AllowColumnReorder = true;
            this.listViewCliquesOfCluster.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewCliquesOfCluster.AutoArrange = false;
            this.listViewCliquesOfCluster.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader11,
            this.columnHeader7,
            this.columnHeader12,
            this.columnHeader14,
            this.columnHeader30});
            this.listViewCliquesOfCluster.ContextMenuStrip = this.ctxCliqueOfCluster;
            this.listViewCliquesOfCluster.FullRowSelect = true;
            this.listViewCliquesOfCluster.GridLines = true;
            this.listViewCliquesOfCluster.HideSelection = false;
            this.listViewCliquesOfCluster.LabelWrap = false;
            this.listViewCliquesOfCluster.Location = new System.Drawing.Point(0, 21);
            this.listViewCliquesOfCluster.Name = "listViewCliquesOfCluster";
            this.listViewCliquesOfCluster.ShowGroups = false;
            this.listViewCliquesOfCluster.Size = new System.Drawing.Size(851, 216);
            this.listViewCliquesOfCluster.TabIndex = 1;
            this.listViewCliquesOfCluster.UseCompatibleStateImageBehavior = false;
            this.listViewCliquesOfCluster.View = System.Windows.Forms.View.Details;
            this.listViewCliquesOfCluster.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewCliquesOfCluster_MouseDoubleClick);
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "序号";
            this.columnHeader4.Width = 52;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "小区数";
            this.columnHeader5.Width = 66;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "设计频点数";
            this.columnHeader6.Width = 86;
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "每小区平均载频数";
            this.columnHeader11.Width = 115;
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "Clique";
            this.columnHeader7.Width = 366;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "平均质量值";
            this.columnHeader12.Width = 75;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "所属片区";
            this.columnHeader14.Width = 68;
            // 
            // columnHeader30
            // 
            this.columnHeader30.Text = "过覆盖小区个数";
            this.columnHeader30.Width = 103;
            // 
            // ctxCliqueOfCluster
            // 
            this.ctxCliqueOfCluster.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miHilightCliqueOfCluaster,
            this.toolStripSeparator4,
            this.miExportCofC});
            this.ctxCliqueOfCluster.Name = "ctxClique";
            this.ctxCliqueOfCluster.Size = new System.Drawing.Size(191, 54);
            // 
            // miHilightCliqueOfCluaster
            // 
            this.miHilightCliqueOfCluaster.Name = "miHilightCliqueOfCluaster";
            this.miHilightCliqueOfCluaster.Size = new System.Drawing.Size(190, 22);
            this.miHilightCliqueOfCluaster.Text = "高亮Clique包含的小区";
            this.miHilightCliqueOfCluaster.Click += new System.EventHandler(this.miHilightCliqueOfCluaster_Click);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(187, 6);
            // 
            // miExportCofC
            // 
            this.miExportCofC.Name = "miExportCofC";
            this.miExportCofC.Size = new System.Drawing.Size(190, 22);
            this.miExportCofC.Text = "导出Excel...";
            this.miExportCofC.Click += new System.EventHandler(this.miExportCofC_Click);
            // 
            // tabPageDisCoNum
            // 
            this.tabPageDisCoNum.Controls.Add(this.listViewDisCoStat);
            this.tabPageDisCoNum.Location = new System.Drawing.Point(4, 21);
            this.tabPageDisCoNum.Name = "tabPageDisCoNum";
            this.tabPageDisCoNum.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageDisCoNum.Size = new System.Drawing.Size(854, 486);
            this.tabPageDisCoNum.TabIndex = 2;
            this.tabPageDisCoNum.Text = "所有不能同频小区";
            this.tabPageDisCoNum.UseVisualStyleBackColor = true;
            // 
            // listViewDisCoStat
            // 
            this.listViewDisCoStat.AllowColumnReorder = true;
            this.listViewDisCoStat.AutoArrange = false;
            this.listViewDisCoStat.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3});
            this.listViewDisCoStat.ContextMenuStrip = this.ctxDisCoStat;
            this.listViewDisCoStat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewDisCoStat.FullRowSelect = true;
            this.listViewDisCoStat.GridLines = true;
            this.listViewDisCoStat.HideSelection = false;
            this.listViewDisCoStat.LabelWrap = false;
            this.listViewDisCoStat.Location = new System.Drawing.Point(3, 3);
            this.listViewDisCoStat.Name = "listViewDisCoStat";
            this.listViewDisCoStat.ShowGroups = false;
            this.listViewDisCoStat.Size = new System.Drawing.Size(848, 480);
            this.listViewDisCoStat.TabIndex = 2;
            this.listViewDisCoStat.UseCompatibleStateImageBehavior = false;
            this.listViewDisCoStat.View = System.Windows.Forms.View.Details;
            this.listViewDisCoStat.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewDisCoStat_MouseDoubleClick);
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "序号";
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "小区名称";
            this.columnHeader2.Width = 136;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "不能同频小区个数";
            this.columnHeader3.Width = 366;
            // 
            // ctxDisCoStat
            // 
            this.ctxDisCoStat.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowDisCoStatInMap,
            this.miShowDetails,
            this.toolStripSeparator3,
            this.miExport2Xls});
            this.ctxDisCoStat.Name = "ctxDisCoStat";
            this.ctxDisCoStat.Size = new System.Drawing.Size(275, 76);
            // 
            // miShowDisCoStatInMap
            // 
            this.miShowDisCoStatInMap.Name = "miShowDisCoStatInMap";
            this.miShowDisCoStatInMap.Size = new System.Drawing.Size(274, 22);
            this.miShowDisCoStatInMap.Text = "在地图上显示与选中小区不能同频信息";
            this.miShowDisCoStatInMap.Click += new System.EventHandler(this.miShowDisCoStatInMap_Click);
            // 
            // miShowDetails
            // 
            this.miShowDetails.Name = "miShowDetails";
            this.miShowDetails.Size = new System.Drawing.Size(274, 22);
            this.miShowDetails.Text = "小区频点核查";
            this.miShowDetails.Click += new System.EventHandler(this.miShowDetails_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(271, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(274, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // tabPageDetails
            // 
            this.tabPageDetails.Controls.Add(this.groupBox1);
            this.tabPageDetails.Controls.Add(this.listViewDetails);
            this.tabPageDetails.Location = new System.Drawing.Point(4, 21);
            this.tabPageDetails.Name = "tabPageDetails";
            this.tabPageDetails.Size = new System.Drawing.Size(854, 486);
            this.tabPageDetails.TabIndex = 3;
            this.tabPageDetails.Text = "所选小区频率核查";
            this.tabPageDetails.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.rbtnByBCCH);
            this.groupBox1.Controls.Add(this.rbtnByDCH);
            this.groupBox1.Location = new System.Drawing.Point(4, 419);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(847, 61);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "同邻频着色选项";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.BackColor = System.Drawing.Color.Yellow;
            this.label4.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.label4.Location = new System.Drawing.Point(791, 27);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(21, 15);
            this.label4.TabIndex = 4;
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.BackColor = System.Drawing.Color.Red;
            this.label3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.label3.Location = new System.Drawing.Point(695, 27);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(21, 15);
            this.label3.TabIndex = 0;
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(744, 30);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "邻频：";
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(648, 30);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "同频：";
            // 
            // rbtnByBCCH
            // 
            this.rbtnByBCCH.AutoSize = true;
            this.rbtnByBCCH.Location = new System.Drawing.Point(198, 28);
            this.rbtnByBCCH.Name = "rbtnByBCCH";
            this.rbtnByBCCH.Size = new System.Drawing.Size(65, 16);
            this.rbtnByBCCH.TabIndex = 1;
            this.rbtnByBCCH.TabStop = true;
            this.rbtnByBCCH.Text = "不含TCH";
            this.rbtnByBCCH.UseVisualStyleBackColor = true;
            this.rbtnByBCCH.CheckedChanged += new System.EventHandler(this.rbtnByBCCH_CheckedChanged);
            // 
            // rbtnByDCH
            // 
            this.rbtnByDCH.AutoSize = true;
            this.rbtnByDCH.Checked = true;
            this.rbtnByDCH.Location = new System.Drawing.Point(83, 27);
            this.rbtnByDCH.Name = "rbtnByDCH";
            this.rbtnByDCH.Size = new System.Drawing.Size(53, 16);
            this.rbtnByDCH.TabIndex = 0;
            this.rbtnByDCH.TabStop = true;
            this.rbtnByDCH.Text = "含TCH";
            this.rbtnByDCH.UseVisualStyleBackColor = true;
            this.rbtnByDCH.CheckedChanged += new System.EventHandler(this.rbtnByDCH_CheckedChanged);
            // 
            // listViewDetails
            // 
            this.listViewDetails.AllowColumnReorder = true;
            this.listViewDetails.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewDetails.AutoArrange = false;
            this.listViewDetails.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderNum,
            this.columnHeaderSource,
            this.columnHeaderTarget,
            this.columnHeaderCoNum,
            this.columnHeaderAdjNum,
            this.columnHeaderSouDCH,
            this.columnHeaderTarDCH,
            this.columnHeaderFreqDes});
            this.listViewDetails.FullRowSelect = true;
            this.listViewDetails.GridLines = true;
            this.listViewDetails.HideSelection = false;
            this.listViewDetails.LabelWrap = false;
            this.listViewDetails.Location = new System.Drawing.Point(3, 3);
            this.listViewDetails.Name = "listViewDetails";
            this.listViewDetails.ShowGroups = false;
            this.listViewDetails.Size = new System.Drawing.Size(848, 410);
            this.listViewDetails.TabIndex = 0;
            this.listViewDetails.UseCompatibleStateImageBehavior = false;
            this.listViewDetails.View = System.Windows.Forms.View.Details;
            this.listViewDetails.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewDetails_MouseDoubleClick);
            this.listViewDetails.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewDetails_MouseClick);
            // 
            // columnHeaderNum
            // 
            this.columnHeaderNum.Text = "序号";
            this.columnHeaderNum.Width = 52;
            // 
            // columnHeaderSource
            // 
            this.columnHeaderSource.Text = "源小区名称";
            this.columnHeaderSource.Width = 88;
            // 
            // columnHeaderTarget
            // 
            this.columnHeaderTarget.Text = "目标小区名称";
            this.columnHeaderTarget.Width = 97;
            // 
            // columnHeaderCoNum
            // 
            this.columnHeaderCoNum.Text = "同频个数";
            this.columnHeaderCoNum.Width = 79;
            // 
            // columnHeaderAdjNum
            // 
            this.columnHeaderAdjNum.Text = "邻频个数";
            this.columnHeaderAdjNum.Width = 79;
            // 
            // columnHeaderSouDCH
            // 
            this.columnHeaderSouDCH.Text = "源载波个数";
            this.columnHeaderSouDCH.Width = 85;
            // 
            // columnHeaderTarDCH
            // 
            this.columnHeaderTarDCH.Text = "目标载波个数";
            this.columnHeaderTarDCH.Width = 100;
            // 
            // columnHeaderFreqDes
            // 
            this.columnHeaderFreqDes.Text = "描述";
            this.columnHeaderFreqDes.Width = 216;
            // 
            // tabPageInterference
            // 
            this.tabPageInterference.Controls.Add(this.listViewCellInterference);
            this.tabPageInterference.Location = new System.Drawing.Point(4, 21);
            this.tabPageInterference.Name = "tabPageInterference";
            this.tabPageInterference.Size = new System.Drawing.Size(854, 486);
            this.tabPageInterference.TabIndex = 5;
            this.tabPageInterference.Text = "所选小区干扰核查";
            this.tabPageInterference.UseVisualStyleBackColor = true;
            // 
            // listViewCellInterference
            // 
            this.listViewCellInterference.AllowColumnReorder = true;
            this.listViewCellInterference.AutoArrange = false;
            this.listViewCellInterference.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader26,
            this.columnHeader27,
            this.columnHeader28,
            this.columnHeader29,
            this.columnHeader31,
            this.columnHeader32});
            this.listViewCellInterference.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCellInterference.FullRowSelect = true;
            this.listViewCellInterference.GridLines = true;
            this.listViewCellInterference.HideSelection = false;
            this.listViewCellInterference.LabelWrap = false;
            this.listViewCellInterference.Location = new System.Drawing.Point(0, 0);
            this.listViewCellInterference.Name = "listViewCellInterference";
            this.listViewCellInterference.ShowGroups = false;
            this.listViewCellInterference.Size = new System.Drawing.Size(854, 486);
            this.listViewCellInterference.TabIndex = 0;
            this.listViewCellInterference.UseCompatibleStateImageBehavior = false;
            this.listViewCellInterference.View = System.Windows.Forms.View.Details;
            this.listViewCellInterference.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewCellInterference_MouseDoubleClick);
            this.listViewCellInterference.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listViewCellInterference_MouseClick);
            // 
            // columnHeader26
            // 
            this.columnHeader26.Text = "序号";
            this.columnHeader26.Width = 53;
            // 
            // columnHeader27
            // 
            this.columnHeader27.Text = "过覆盖小区名";
            this.columnHeader27.Width = 100;
            // 
            // columnHeader28
            // 
            this.columnHeader28.Text = "干扰小区个数";
            this.columnHeader28.Width = 95;
            // 
            // columnHeader29
            // 
            this.columnHeader29.Text = "被干扰小区名";
            this.columnHeader29.Width = 372;
            // 
            // columnHeader31
            // 
            this.columnHeader31.Text = "干扰小区所属片区";
            this.columnHeader31.Width = 130;
            // 
            // columnHeader32
            // 
            this.columnHeader32.Text = "是否属于簇小区";
            this.columnHeader32.Width = 99;
            // 
            // tabPageExport
            // 
            this.tabPageExport.Controls.Add(this.groupBox2);
            this.tabPageExport.Location = new System.Drawing.Point(4, 21);
            this.tabPageExport.Name = "tabPageExport";
            this.tabPageExport.Size = new System.Drawing.Size(854, 486);
            this.tabPageExport.TabIndex = 6;
            this.tabPageExport.Text = "导出簇小区信息";
            this.tabPageExport.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.btnExportXls);
            this.groupBox2.Controls.Add(this.btnBrowse);
            this.groupBox2.Controls.Add(this.tbxExportPath);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Location = new System.Drawing.Point(8, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(838, 85);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "导出Excel";
            // 
            // btnExportXls
            // 
            this.btnExportXls.Location = new System.Drawing.Point(623, 34);
            this.btnExportXls.Name = "btnExportXls";
            this.btnExportXls.Size = new System.Drawing.Size(75, 23);
            this.btnExportXls.TabIndex = 3;
            this.btnExportXls.Text = "导出";
            this.btnExportXls.UseVisualStyleBackColor = true;
            this.btnExportXls.Click += new System.EventHandler(this.btnExportXls_Click);
            // 
            // btnBrowse
            // 
            this.btnBrowse.Location = new System.Drawing.Point(542, 34);
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Size = new System.Drawing.Size(75, 23);
            this.btnBrowse.TabIndex = 2;
            this.btnBrowse.Text = "浏览...";
            this.btnBrowse.UseVisualStyleBackColor = true;
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // tbxExportPath
            // 
            this.tbxExportPath.BackColor = System.Drawing.SystemColors.Window;
            this.tbxExportPath.Location = new System.Drawing.Point(212, 36);
            this.tbxExportPath.Name = "tbxExportPath";
            this.tbxExportPath.ReadOnly = true;
            this.tbxExportPath.Size = new System.Drawing.Size(324, 21);
            this.tbxExportPath.TabIndex = 1;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(141, 39);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "导出路径：";
            // 
            // ctxClusterGrid
            // 
            this.ctxClusterGrid.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowClusterGrid,
            this.miFindOverCoverCellCluster,
            this.toolStripSeparator6,
            this.miExport});
            this.ctxClusterGrid.Name = "ctxClusterGrid";
            this.ctxClusterGrid.Size = new System.Drawing.Size(203, 76);
            // 
            // miShowClusterGrid
            // 
            this.miShowClusterGrid.Name = "miShowClusterGrid";
            this.miShowClusterGrid.Size = new System.Drawing.Size(202, 22);
            this.miShowClusterGrid.Text = "显示栅格";
            this.miShowClusterGrid.Click += new System.EventHandler(this.miShowClusterGrid_Click);
            // 
            // miFindOverCoverCellCluster
            // 
            this.miFindOverCoverCellCluster.Name = "miFindOverCoverCellCluster";
            this.miFindOverCoverCellCluster.Size = new System.Drawing.Size(202, 22);
            this.miFindOverCoverCellCluster.Text = "显示过覆盖小区的簇信息";
            this.miFindOverCoverCellCluster.Click += new System.EventHandler(this.miFindOverCoverCellCluster_Click);
            // 
            // toolStripSeparator6
            // 
            this.toolStripSeparator6.Name = "toolStripSeparator6";
            this.toolStripSeparator6.Size = new System.Drawing.Size(199, 6);
            // 
            // miExport
            // 
            this.miExport.Name = "miExport";
            this.miExport.Size = new System.Drawing.Size(202, 22);
            this.miExport.Text = "导出Excel...";
            this.miExport.Click += new System.EventHandler(this.miExport_Click);
            // 
            // ctxDetails
            // 
            this.ctxDetails.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.ctxDetails.Name = "ctxDetails";
            this.ctxDetails.Size = new System.Drawing.Size(143, 26);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(142, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // cbxShowClique
            // 
            this.cbxShowClique.AutoSize = true;
            this.cbxShowClique.Checked = true;
            this.cbxShowClique.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxShowClique.Location = new System.Drawing.Point(56, 20);
            this.cbxShowClique.Name = "cbxShowClique";
            this.cbxShowClique.Size = new System.Drawing.Size(108, 16);
            this.cbxShowClique.TabIndex = 4;
            this.cbxShowClique.Text = "显示Clique轮廓";
            this.cbxShowClique.UseVisualStyleBackColor = true;
            this.cbxShowClique.CheckedChanged += new System.EventHandler(this.cbxShowClique_CheckedChanged);
            // 
            // cbxShowCluster
            // 
            this.cbxShowCluster.AutoSize = true;
            this.cbxShowCluster.Checked = true;
            this.cbxShowCluster.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxShowCluster.Location = new System.Drawing.Point(189, 20);
            this.cbxShowCluster.Name = "cbxShowCluster";
            this.cbxShowCluster.Size = new System.Drawing.Size(84, 16);
            this.cbxShowCluster.TabIndex = 4;
            this.cbxShowCluster.Text = "显示簇轮廓";
            this.cbxShowCluster.UseVisualStyleBackColor = true;
            this.cbxShowCluster.CheckedChanged += new System.EventHandler(this.cbxShowCluster_CheckedChanged);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.cbxShowSelectedCell);
            this.groupBox3.Controls.Add(this.cbxShowDSC1800);
            this.groupBox3.Controls.Add(this.cbxShowGSM900);
            this.groupBox3.Controls.Add(this.cbxShowClique);
            this.groupBox3.Controls.Add(this.cbxShowCluster);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.groupBox3.Location = new System.Drawing.Point(0, 511);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(862, 48);
            this.groupBox3.TabIndex = 5;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "显示选项";
            // 
            // cbxShowSelectedCell
            // 
            this.cbxShowSelectedCell.AutoSize = true;
            this.cbxShowSelectedCell.Location = new System.Drawing.Point(522, 20);
            this.cbxShowSelectedCell.Name = "cbxShowSelectedCell";
            this.cbxShowSelectedCell.Size = new System.Drawing.Size(156, 16);
            this.cbxShowSelectedCell.TabIndex = 7;
            this.cbxShowSelectedCell.Text = "显示选中小区Clique信息";
            this.cbxShowSelectedCell.UseVisualStyleBackColor = true;
            this.cbxShowSelectedCell.CheckedChanged += new System.EventHandler(this.cbxShowSelectedCell_CheckedChanged);
            // 
            // cbxShowDSC1800
            // 
            this.cbxShowDSC1800.AutoSize = true;
            this.cbxShowDSC1800.Checked = true;
            this.cbxShowDSC1800.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxShowDSC1800.Location = new System.Drawing.Point(407, 20);
            this.cbxShowDSC1800.Name = "cbxShowDSC1800";
            this.cbxShowDSC1800.Size = new System.Drawing.Size(90, 16);
            this.cbxShowDSC1800.TabIndex = 6;
            this.cbxShowDSC1800.Text = "显示DSC1800";
            this.cbxShowDSC1800.UseVisualStyleBackColor = true;
            this.cbxShowDSC1800.CheckedChanged += new System.EventHandler(this.cbxShowDSC1800_CheckedChanged);
            // 
            // cbxShowGSM900
            // 
            this.cbxShowGSM900.AutoSize = true;
            this.cbxShowGSM900.Checked = true;
            this.cbxShowGSM900.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxShowGSM900.Location = new System.Drawing.Point(298, 20);
            this.cbxShowGSM900.Name = "cbxShowGSM900";
            this.cbxShowGSM900.Size = new System.Drawing.Size(84, 16);
            this.cbxShowGSM900.TabIndex = 5;
            this.cbxShowGSM900.Text = "显示GSM900";
            this.cbxShowGSM900.UseVisualStyleBackColor = true;
            this.cbxShowGSM900.CheckedChanged += new System.EventHandler(this.cbxShowGSM900_CheckedChanged);
            // 
            // ClusterStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(862, 559);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.groupBox3);
            this.KeyPreview = true;
            this.Name = "ClusterStatForm";
            this.Text = "簇优化分析";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ClusterStatForm_FormClosing);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.ClusterStatForm_KeyDown);
            this.tabControl.ResumeLayout(false);
            this.tabPageClique.ResumeLayout(false);
            this.tabCtrlEstimate.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tabPage5.ResumeLayout(false);
            this.ctxClique.ResumeLayout(false);
            this.tabPageCluster.ResumeLayout(false);
            this.ctxCluster.ResumeLayout(false);
            this.tabPageClusterInfo.ResumeLayout(false);
            this.tabPageClusterInfo.PerformLayout();
            this.ctxClusterCell.ResumeLayout(false);
            this.ctxCliqueOfCluster.ResumeLayout(false);
            this.tabPageDisCoNum.ResumeLayout(false);
            this.ctxDisCoStat.ResumeLayout(false);
            this.tabPageDetails.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPageInterference.ResumeLayout(false);
            this.tabPageExport.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ctxClusterGrid.ResumeLayout(false);
            this.ctxDetails.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabPageClique;
        private System.Windows.Forms.TabPage tabPageCluster;
        private System.Windows.Forms.ListView listViewClique;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ContextMenuStrip ctxClique;
        private System.Windows.Forms.ToolStripMenuItem miShowCliqueInMap;
        private System.Windows.Forms.ListView listViewCluster;
        private System.Windows.Forms.ColumnHeader columnHeaderNO;
        private System.Windows.Forms.ColumnHeader columnHeaderCellNum;
        private System.Windows.Forms.ColumnHeader columnHeaderCluster;
        private System.Windows.Forms.TabPage tabPageDisCoNum;
        private System.Windows.Forms.ContextMenuStrip ctxCluster;
        private System.Windows.Forms.ToolStripMenuItem miShowClusterInMap;
        private System.Windows.Forms.ListView listViewDisCoStat;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ContextMenuStrip ctxDisCoStat;
        private System.Windows.Forms.ToolStripMenuItem miShowDisCoStatInMap;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Excel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private System.Windows.Forms.TabPage tabPageDetails;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ListView listViewDetails;
        private System.Windows.Forms.RadioButton rbtnByBCCH;
        private System.Windows.Forms.RadioButton rbtnByDCH;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ColumnHeader columnHeaderNum;
        private System.Windows.Forms.ColumnHeader columnHeaderSource;
        private System.Windows.Forms.ColumnHeader columnHeaderTarget;
        private System.Windows.Forms.ColumnHeader columnHeaderCoNum;
        private System.Windows.Forms.ColumnHeader columnHeaderSouDCH;
        private System.Windows.Forms.ColumnHeader columnHeaderTarDCH;
        private System.Windows.Forms.ToolStripMenuItem miShowDetails;
        private System.Windows.Forms.ContextMenuStrip ctxDetails;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ColumnHeader columnHeaderCellCount;
        private System.Windows.Forms.ColumnHeader columnHeaderFreqNum;
        private System.Windows.Forms.ColumnHeader columnHeaderClique;
        private System.Windows.Forms.TabPage tabPageClusterInfo;
        private System.Windows.Forms.ListView listViewCliquesOfCluster;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ColumnHeader columnHeader7;
        private System.Windows.Forms.ToolStripMenuItem miShowCliquesOfCluster;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.ListView listViewCellOfCluster;
        private System.Windows.Forms.ColumnHeader columnHeader8;
        private System.Windows.Forms.ColumnHeader columnHeader9;
        private System.Windows.Forms.ColumnHeader columnHeader10;
        private System.Windows.Forms.ColumnHeader columnHeader13;
        private System.Windows.Forms.ColumnHeader columnHeader11;
        private System.Windows.Forms.ColumnHeader columnHeader12;
        private System.Windows.Forms.ColumnHeader columnHeaderMeanFreqNum;
        private System.Windows.Forms.ColumnHeader columnHeaderMeanQuality;
        private System.Windows.Forms.ToolStripMenuItem miShowRelativeCluster;
        private System.Windows.Forms.CheckBox cbxShowClique;
        private System.Windows.Forms.CheckBox cbxShowCluster;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.ContextMenuStrip ctxCliqueOfCluster;
        private System.Windows.Forms.ToolStripMenuItem miHilightCliqueOfCluaster;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem miExportCofC;
        private System.Windows.Forms.ColumnHeader columnHeaderFreqDes;
        private System.Windows.Forms.ColumnHeader columnHeaderAdjNum;
        private System.Windows.Forms.ColumnHeader columnHeaderMeanVariance;
        private System.Windows.Forms.CheckBox cbxShowDSC1800;
        private System.Windows.Forms.CheckBox cbxShowGSM900;
        private System.Windows.Forms.CheckBox cbxShowSelectedCell;
        private System.Windows.Forms.ColumnHeader columnHeaderArea;
        private System.Windows.Forms.ColumnHeader columnHeader14;
        private System.Windows.Forms.ContextMenuStrip ctxClusterCell;
        private System.Windows.Forms.ToolStripMenuItem miShowCellClique;
        private System.Windows.Forms.ToolStripMenuItem miCheckClusterCellFreq;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripMenuItem miExp2Excel;
        private System.Windows.Forms.ColumnHeader columnHeader15;
        private System.Windows.Forms.TabControl tabCtrlEstimate;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.ListView listViewCliqueEstimate;
        private System.Windows.Forms.ListView listViewMeanSquareError;
        private System.Windows.Forms.ListView listViewMeanRate;
        private System.Windows.Forms.ListView listViewOverCover;
        private System.Windows.Forms.ColumnHeader columnHeader16;
        private System.Windows.Forms.ColumnHeader columnHeader17;
        private System.Windows.Forms.ColumnHeader columnHeader18;
        private System.Windows.Forms.ColumnHeader columnHeader19;
        private System.Windows.Forms.ColumnHeader columnHeader20;
        private System.Windows.Forms.ColumnHeader columnHeader21;
        private System.Windows.Forms.ColumnHeader columnHeader22;
        private System.Windows.Forms.ColumnHeader columnHeader23;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.ListView listViewInjectRate;
        private System.Windows.Forms.ColumnHeader columnHeader24;
        private System.Windows.Forms.ColumnHeader columnHeader25;
        private System.Windows.Forms.TabPage tabPageInterference;
        private System.Windows.Forms.ListView listViewCellInterference;
        private System.Windows.Forms.ColumnHeader columnHeader26;
        private System.Windows.Forms.ColumnHeader columnHeader27;
        private System.Windows.Forms.ColumnHeader columnHeader28;
        private System.Windows.Forms.ColumnHeader columnHeader29;
        private System.Windows.Forms.ToolStripMenuItem miCheckCellInterference;
        private System.Windows.Forms.ColumnHeader columnHeaderOverCoverNum;
        private System.Windows.Forms.ColumnHeader columnHeader30;
        private System.Windows.Forms.ToolStripMenuItem miShowClusterCellGrid;
        private System.Windows.Forms.ContextMenuStrip ctxClusterGrid;
        private System.Windows.Forms.ToolStripMenuItem miShowClusterGrid;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator6;
        private System.Windows.Forms.ToolStripMenuItem miExport;
        private System.Windows.Forms.ColumnHeader columnHeader31;
        private System.Windows.Forms.ColumnHeader columnHeader32;
        private System.Windows.Forms.ToolStripMenuItem miFindOverCoverCellCluster;
        private System.Windows.Forms.TabPage tabPageExport;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button btnExportXls;
        private System.Windows.Forms.Button btnBrowse;
        private System.Windows.Forms.TextBox tbxExportPath;
        private System.Windows.Forms.Label label5;
    }
}