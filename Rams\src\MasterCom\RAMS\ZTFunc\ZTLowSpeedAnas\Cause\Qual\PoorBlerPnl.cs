﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class PoorSINRPnl : UserControl
    {
        public PoorSINRPnl()
        {
            InitializeComponent();
        }

        PoorSINRCause mainReason = null;
        PoorQualMod3Interf mod3 = null;
        PoorQualTDSInterfFFreqBand fFreq = null;
        PoorQualWeakCover wc = null;
        public void LinkCondition(PoorSINRCause reason)
        {
            this.mainReason = reason;
            foreach (CauseBase item in reason.SubCauses)
            {
                if (item is PoorQualMod3Interf)
                {
                    mod3 = item as PoorQualMod3Interf;
                }
                else if (item is PoorQualTDSInterfFFreqBand)
                {
                    fFreq = item as PoorQualTDSInterfFFreqBand;
                }
                else if (item is PoorQualWeakCover)
                {
                    wc = item as PoorQualWeakCover;
                }
            }

            numSINRMax.Value = (decimal)mainReason.SINRMax;
            numSINRMax.ValueChanged += numSINRMax_ValueChanged;

            numRSRPDiff.Value = (decimal)mod3.RSRPDiffMax;
            numRSRPDiff.ValueChanged += numRSRPDiff_ValueChanged;

            numRadius.Value = (decimal)fFreq.Distance;
            numRadius.ValueChanged += numRadius_ValueChanged;

            numWCRSRPMax.Value = (decimal)wc.RSRPMax;
            numWCRSRPMax.ValueChanged += numWCRSRPMax_ValueChanged;
        }

        void numWCRSRPMax_ValueChanged(object sender, EventArgs e)
        {
            wc.RSRPMax = (float)numWCRSRPMax.Value;
        }

        void numRadius_ValueChanged(object sender, EventArgs e)
        {
            fFreq.Distance = (double)numRadius.Value;
        }

        void numRSRPDiff_ValueChanged(object sender, EventArgs e)
        {
            mod3.RSRPDiffMax = (float)numRSRPDiff.Value;
        }

        void numSINRMax_ValueChanged(object sender, EventArgs e)
        {
            mainReason.SINRMax = (float)numSINRMax.Value;
        }


    }
}
