﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Util;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class WelcomForm : ChildForm
    {
        private List<TaskInfo> popTasks = new List<TaskInfo>();
        private Dictionary<int, int> typeShowPlaceDic = new Dictionary<int, int>();
        private bool isSecondWelcomForm = false;
        public WelcomForm(MainModel model, bool isSecondWelcomForm) //参数isWxwljc：此面板是否专门为提供无线网络监测使用
        {
            this.mainModel = model;
            this.isSecondWelcomForm = isSecondWelcomForm;
            InitializeComponent();
            this.panelMain.MouseWheel += new MouseEventHandler(panelMain_MouseWheel);
#if Guangdong
            this.Text = "欢迎使用网优平台-测试数据分析模块";
#endif
            initTaskInfo();
            prepareForShow();
        }
        public void reloadAllData()
        {
            if(!bgWorker.IsBusy)
            {
                initShowPanels();
                bgWorker.RunWorkerAsync();
            }
        }
        private void prepareForShow()
        {
            for(int i=0;i<popTasks.Count;i++)
            {
                typeShowPlaceDic[popTasks[i].type] = i;
            }
        }

        private void initTaskInfo()
        {
            popTasks.Clear();
            int nIndex = 1;
            if (!isSecondWelcomForm)
            {
#if PopShow_KPI_Total
                popTasks.Add(new TaskInfo(nIndex++, "KPI指标", typeof(KPIInfoPanel_ng_total), this));
#elif PopShow_KPI_GIS
                popTasks.Add(new TaskInfo(nIndex++, "KPI指标", typeof(KPIInfoPanel_ng_GIS), this));
#elif PopShow_KPI_None
                //do nothing
#else
                popTasks.Add(new TaskInfo(nIndex++, "KPI指标", typeof(KPIInfoPanel_ng), this));
#endif

#if PopShow_BlackBlock_Spec
                popTasks.Add(new TaskInfo(nIndex++, "异常事件情况", typeof(NewBlackBlockInfoPanel_ng), this));
#else
                popTasks.Add(new TaskInfo(nIndex++, "异常事件情况", typeof(BlackBlockInfoPanel_ng), this));
#endif
#if PopShow_Problem
                popTasks.Add(new TaskInfo(nIndex++, "问题点情况", typeof(ProblemBlockInfoPanel), this));
#endif

#if PopShow_Agent_None
                //do nothing
#else 
                popTasks.Add(new TaskInfo(nIndex++, "代维工作量", typeof(AgentWorkPanel_ng), this));
#endif

#if PopShow_ES_None
                //do nothing
#else
                popTasks.Add(new TaskInfo(nIndex++, "智能预判情况", typeof(ESInfoPanel), this));
#endif

#if PopShow_CQT
                popTasks.Add(new TaskInfo(nIndex++, "CQT统计报表", typeof(CQTInfoPanel), this));
#endif
#if PopShow_KPI_Color
                popTasks.Add(new TaskInfo(nIndex++, "指标波动告警", typeof(KpiAlarmPanel), this));
#endif
            }
            else
            {
                //popTasks.Add(new TaskInfo(nIndex++, "RCU测试完成率", typeof(WirelessNetworkMonitoringInfoPanel), this));
                //popTasks.Add(new TaskInfo(nIndex++, "手机测试完成率", typeof(WirelessNetworkMonitoringInfoPanel), this));
                popTasks.Add(new TaskInfo(nIndex++, "RCU测试指标2G", typeof(WirelessNetworkMonitoringInfoPanel), this));
                popTasks.Add(new TaskInfo(nIndex++, "RCU测试指标3G", typeof(WirelessNetworkMonitoringInfoPanel), this));
                //popTasks.Add(new TaskInfo(nIndex++, "手机测试指标2G", typeof(WirelessNetworkMonitoringInfoPanel), this));
                popTasks.Add(new TaskInfo(nIndex++, "手机测试指标", typeof(WirelessNetworkMonitoringInfoPanel), this));
            }
#if DEBUG
            Console.Write(nIndex);
#endif
        }
        
        private void panelMain_MouseWheel(object sender, MouseEventArgs e)
        {
            int mVSValue = this.panelMain.VerticalScroll.Value;
            int pScrollValueDelta = e.Delta;
            if ((mVSValue - pScrollValueDelta) <= this.panelMain.VerticalScroll.Minimum)
            {
                this.panelMain.VerticalScroll.Value = this.panelMain.VerticalScroll.Minimum;
            }
            else if ((mVSValue - pScrollValueDelta) >= this.panelMain.VerticalScroll.Maximum)
            {
                this.panelMain.VerticalScroll.Value = this.panelMain.VerticalScroll.Maximum;
            }
            else
            {
                this.panelMain.VerticalScroll.Value -= pScrollValueDelta;
            }

            if (this.panelMain.VerticalScroll.Value != mVSValue)
            {
                return;
            }

            this.panelMain.Refresh();
            this.panelMain.Invalidate();
            this.panelMain.Update();

        }
        private void initShowPanels()
        {
            this.tableLayoutMain.Controls.Clear();
            this.tableLayoutMain.RowCount = popTasks.Count;
            int totalHeight = 0;
            for (int i = 0; i < popTasks.Count;i++ )
            {
                TaskInfo task = popTasks[i];
                task.GetInstancePanel(mainModel);
                RunningTaskPanel rpanel = new RunningTaskPanel(task.taskName);
                this.tableLayoutMain.Controls.Add(rpanel, 0, i);
                totalHeight += rpanel.Height+10;
            }
            this.tableLayoutMain.Height = totalHeight;

#if Guangdong
            BlackBlockInfoPanel_ng blackBlockInfoPanel_ng=null;
            KPIInfoPanel_ng_total kpiInfoPanel_ng_total=null;
            foreach (TaskInfo task in popTasks)
            {
                if (task.GetInstancePanel(mainModel) is BlackBlockInfoPanel_ng)
                {
                    blackBlockInfoPanel_ng = task.GetInstancePanel(mainModel) as BlackBlockInfoPanel_ng;
                }
                if (task.GetInstancePanel(mainModel) is KPIInfoPanel_ng_total)
                {
                    kpiInfoPanel_ng_total = task.GetInstancePanel(mainModel) as KPIInfoPanel_ng_total;
                }
            }
            if (blackBlockInfoPanel_ng != null && kpiInfoPanel_ng_total != null)
            {
                kpiInfoPanel_ng_total.setBlackBlockInfoPanel(blackBlockInfoPanel_ng);
            }
#endif
        }

        private void panelMain_MouseClick(object sender, MouseEventArgs e)
        {
            this.panelMain.Focus();
        }

        private void WelcomForm_Shown(object sender, EventArgs e)
        {
            initShowPanels();
            bgWorker.RunWorkerAsync();
        }

        private void bgWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            System.Threading.Thread.Sleep(1000);
            foreach (TaskInfo task in popTasks)
            {
                task.GetInstancePanel(mainModel).RunQuery(bgWorker, task);
                bgWorker.ReportProgress(1, task);
            }
        }

        int countWNMIPanel = 1;
        private void bgWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is TaskInfo)
            {
                this.panelMain.Focus();
                TaskInfo taskInfo = e.UserState as TaskInfo;
                int rowPos = typeShowPlaceDic[taskInfo.type];
                PopShowPanelInterface panelFace = taskInfo.GetInstancePanel(mainModel);
                if (panelFace is WirelessNetworkMonitoringInfoPanel)  //无线网络监测欢迎页
                {
                    //if (countWNMIPanel == 1)  //RCU测试完成率表面板
                    //{
                    //    buildWNMITablePanel(panelFace, taskInfo, rowPos, "tb_popkpi_wxwljc_rcu_cmpstat");
                    //    countWNMIPanel++;
                    //}
                    //else if (countWNMIPanel == 2)  //手机测试完成率表面板
                    //{
                    //    buildWNMITablePanel(panelFace, taskInfo, rowPos, "tb_popkpi_wxwljc_phone_wk_cmpstat");
                    //    countWNMIPanel++;
                    //}
                    if (countWNMIPanel==1)  //RCU测试指标2G表面板
                    {
                        buildWNMITablePanel(panelFace, taskInfo, rowPos, "tb_popkpi_wxwljc_rcugsm_stat");
                        countWNMIPanel++;
                    }
                    else if (countWNMIPanel == 2)  //RCU测试指标3G表面板
                    {
                        buildWNMITablePanel(panelFace, taskInfo, rowPos, "tb_popkpi_wxwljc_rcutd_stat");
                        countWNMIPanel++;
                    }
                    //else if (countWNMIPanel == 5)  //手机测试指标2G表面板
                    //{
                    //    buildWNMITablePanel(panelFace, taskInfo, rowPos, "tb_popkpi_wxwljc_phonegsm_stat");
                    //    countWNMIPanel++;
                    //}
                    else if (countWNMIPanel == 3)  //手机测试指标3G表面板
                    {
                        buildWNMITablePanel(panelFace, taskInfo, rowPos, "tb_popkpi_wxwljc_phonetd_stat");
                        countWNMIPanel++;
                    }
                }
                else
                {
                    panelFace.FireFreshShowData(taskInfo);

                    Control ctrl = this.tableLayoutMain.GetControlFromPosition(0, rowPos);
                    this.tableLayoutMain.Controls.Remove(ctrl);
                    this.tableLayoutMain.Controls.Add((UserControl)panelFace, 0, rowPos);
                    DockStyle myDockStyle = DockStyle.Fill;

                    ((UserControl)panelFace).Dock = myDockStyle;
                    this.tableLayoutMain.Height += (((UserControl)panelFace).Height - ctrl.Height);
                }
            }
        }

        private void buildWNMITablePanel(PopShowPanelInterface panelFace, TaskInfo taskInfo, int rowPos, string panelName)
        {
            ((WirelessNetworkMonitoringInfoPanel)panelFace).panelName = panelName;
            panelFace.FireFreshShowData(taskInfo);
            Control ctrl2 = this.tableLayoutMain.GetControlFromPosition(0, rowPos);
            this.tableLayoutMain.Controls.Remove(ctrl2);
            this.tableLayoutMain.Controls.Add((UserControl)panelFace, 0, rowPos);
            ((UserControl)panelFace).Dock = DockStyle.Fill;
            this.tableLayoutMain.Height += (((UserControl)panelFace).Height - ctrl2.Height);
        }

        private void WelcomForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if(e.CloseReason == CloseReason.UserClosing)
            {
                this.Visible = false;
                e.Cancel = true;
            }
        }

        private void ExpAllToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExpAll2Xls();
        }
        public void ExpAll2Xls()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Title = "选择要保存文档的路径";
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    //KPIInfoPanel_ng kpiPn = tableLayoutMain.Controls[0] as KPIInfoPanel_ng;
                    KPIInfoPanel_ng_total kpiPn = tableLayoutMain.Controls[0] as KPIInfoPanel_ng_total;
                    kpiPn.ExportInfo(excel);
                    BlackBlockInfoPanel_ng bbPn = tableLayoutMain.Controls[1] as BlackBlockInfoPanel_ng;
                    bbPn.ExportAllTabInfo(excel, dlg.FileName);
#if Guangdong
                    ProblemBlockInfoPanel pbInfoPanel = tableLayoutMain.Controls[2] as ProblemBlockInfoPanel;
                    if (pbInfoPanel != null)
                    {
                        excel.CreateSheet();
                        pbInfoPanel.ExportInfo(excel);
                    }
#endif
                    excel.SaveFile(dlg.FileName);
                   
                }
                catch (Exception)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("导出数据出错！");
                    return;
                }
                finally
                {
                    excel.CloseExcel();
                    WaitBox.Close();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(dlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + dlg.FileName);
                    }
                }
            }
        }

        public void ExpWNMonitorAll()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Title = "选择要保存文档的路径";
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    foreach (WirelessNetworkMonitoringInfoPanel panel in tableLayoutMain.Controls)
                    {
                        if (tableLayoutMain.Controls.IndexOf(panel) != 0)
                        {
                            excel.CreateSheet();
                            panel.ExportInfo(excel);
                        }
                        else
                            panel.ExportInfo(excel);
                    }
                    excel.SaveFile(dlg.FileName);

                }
                catch (Exception)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("导出数据出错！");
                    return;
                }
                finally
                {
                    excel.CloseExcel();
                    WaitBox.Close();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(dlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + dlg.FileName);
                    }
                }
            }
        }

    }
    public class TaskInfo
    {
        public int type { get; set; }
        public string taskName { get; set; }
        public Type theClass { get; set; }
        public object retResultInfo { get; set; }
        private PopShowPanelInterface theInstancePanel = null;
        private readonly WelcomForm wf;
        public PopShowPanelInterface GetInstancePanel(MainModel mm)
        {
            if(theInstancePanel==null)
            {
                theInstancePanel = (PopShowPanelInterface)theClass.Assembly.CreateInstance(theClass.ToString());
                theInstancePanel.SetMainModal(mm,wf);
            }
            return theInstancePanel;
        }
        public TaskInfo(int type,string taskName,Type classType,WelcomForm wf)
        {
            this.type = type;
            this.taskName = taskName;
            this.theClass = classType;
            this.wf = wf;
        }
    };
}