﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateNeighborCellBcchTchAnaForm : CreateChildForm
    {
        public CreateNeighborCellBcchTchAnaForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建同邻频窗口 NeighborCellBcchTchAnaForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20022, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建同邻频窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.NeighborCellBcchTchAnaForm";
            actionParam["Text"] = "同邻频";
            actionParam["ImageFilePath"] = @"images\statfolder.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
