﻿using MasterCom.RAMS.Model;
using System;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYLtePoorRxQualityRoadQueryByRegion : ZTDIYGSMPoorRxQualityRoadQueryByRegion
    {
        private static ZTDIYLtePoorRxQualityRoadQueryByRegion instance_LTE = null;
        protected static readonly object lockObj_LTE = new object();
        protected ZTDIYLtePoorRxQualityRoadQueryByRegion()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
        }
        public override string Name
        {
            get { return "质差路段_LTE"; }
        }
        protected override GSMPoorRxQualityRoadInfo getInfoInstance()
        {
            return new LTEPoorRxQualityRoadInfo();
        }
        public static new ZTDIYLtePoorRxQualityRoadQueryByRegion GetInstance()
        {
            if (instance_LTE == null)
            {
                lock (lockObj_LTE)
                {
                    if (instance_LTE == null)
                    {
                        instance_LTE = new ZTDIYLtePoorRxQualityRoadQueryByRegion();
                    }
                }
            }
            return instance_LTE;
        }
        protected override void getParam(TestPoint testPoint, out int? rxQual, out short? rxlev, out short? C2I)
        {
            rxQual = (int?)(byte?)testPoint["lte_gsm_DM_RxQualSub"];
            rxlev = (short?)testPoint["lte_gsm_DM_RxLevSub"];
            C2I = null;
        }
    }

    public class ZTDIYLtePoorRxQualityRoadQueryByRegion_FDD : ZTDIYLtePoorRxQualityRoadQueryByRegion
    {
        private static ZTDIYLtePoorRxQualityRoadQueryByRegion_FDD instance = null;
        public static new ZTDIYLtePoorRxQualityRoadQueryByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYLtePoorRxQualityRoadQueryByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected ZTDIYLtePoorRxQualityRoadQueryByRegion_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
        }
        protected override GSMPoorRxQualityRoadInfo getInfoInstance()
        {
            return new LTEPoorRxQualityRoadInfo_FDD();
        }
        protected void getParamFdd(TestPoint testPoint, out float? rxQual, out float? rxlev, out short? C2I)
        {
            rxQual = (float?)testPoint["lte_fdd_wcdma_TotalEc_Io"];
            rxlev = (float?)testPoint["lte_fdd_wcdma_TotalRSCP"];
            C2I = null;
        }

        protected override void doWithDTData(ref GSMPoorRxQualityRoadInfo info, TestPoint testPoint, double distance)
        {
            float? rxQual, rxlev;
            short? C2I;
            this.getParamFdd(testPoint, out rxQual, out rxlev, out C2I);

            if ((rxQual != null) && (rxQual >= 0) && (rxQual <= 7))
            {
                info = getGSMPoorRxQualityRoadInfo(info, testPoint, distance, rxQual, rxlev, C2I);
            }
        }

        private GSMPoorRxQualityRoadInfo getGSMPoorRxQualityRoadInfo(GSMPoorRxQualityRoadInfo info, TestPoint testPoint, double distance, float? rxQual, float? rxlev, short? C2I)
        {
            if (!isChkPercent)
            {
                if (rxQual < rxQualThreshold)   //低于门限，不是差Rxquality
                {
                    getValidResult(info);
                    info = this.getInfoInstance();    //重置
                }
                else
                {
                    addInfoValue(info, testPoint, distance, rxQual, rxlev, C2I);
                }
            }
            else
            {
                double dCurPercent = info.sampleLst.Count == 0 ? 0 : Math.Round(100.0 * info.RxQualThresholdNum / info.sampleLst.Count, 2);
                if (rxQual < rxQualThreshold && dCurPercent <= dPercent)   //低于门限，不是差Rxquality
                {
                    getValidResult(info);
                    info = this.getInfoInstance();    //重置
                }
                else
                {
                    if (rxQual >= rxQualThreshold)
                    {
                        info.RxQualThresholdNum++;
                    }
                    addInfoValue(info, testPoint, distance, rxQual, rxlev, C2I);
                }
            }

            return info;
        }

        private static void addInfoValue(GSMPoorRxQualityRoadInfo info, TestPoint testPoint, double distance, float? rxQual, float? rxlev, short? C2I)
        {
            info.distance += distance;

            info.SetQuality((float)rxQual);
            if ((rxlev != null) && (rxlev >= -140) && (rxlev <= -10))
            {
                info.SetRxlev((float)rxlev);
            }
            if ((C2I != null) && (C2I >= -20) && (C2I <= 25))
            {
                info.SetC2I((float)C2I);
            }
            info.sampleLst.Add(testPoint);
        }

        public override string Name
        {
            get { return "质差路段_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26069, this.Name);//////
        }
    }

    public class LTEPoorRxQualityRoadInfo : GSMPoorRxQualityRoadInfo
    {
        protected override ICell getMainCell(TestPoint tp)
        {
            return tp.GetMainCell_LTE_GSM();
        }

        protected override bool testLacCi(TestPoint tp)
        {
            return tp["lte_gsm_SC_LAC"] != null && tp["lte_gsm_SC_CI"] != null;
        }
        protected override string getCellKey(TestPoint tp)
        {
            return tp["lte_gsm_SC_LAC"].ToString() + "_" + tp["lte_gsm_SC_CI"].ToString();
        }
    }
    public class LTEPoorRxQualityRoadInfo_FDD : LTEPoorRxQualityRoadInfo
    {
        protected override ICell getMainCell(TestPoint tp)
        {
            return tp.GetMainCell_LTE_FDD_W();
            //return tp.GetMainCell_LTE_GSM();
        }

        protected override bool testLacCi(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_SysLAI"] != null && tp["lte_fdd_wcdma_SysCellID"] != null;
        }
        protected override string getCellKey(TestPoint tp)
        {
            return tp["lte_fdd_wcdma_SysLAI"].ToString() + "_" + tp["lte_fdd_wcdma_SysCellID"].ToString();
        }
    }
}
