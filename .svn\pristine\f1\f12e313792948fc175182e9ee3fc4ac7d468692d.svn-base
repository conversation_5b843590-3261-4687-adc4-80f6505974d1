﻿using System;
using System.Collections.Generic;
using System.Text;
using MapWinGIS;
using System.Drawing;

namespace MasterCom.MapSpaceManager.MapSpaceManager
{
    public class LinePatternItem
    {
        public String name;
        public bool useLinePatten = false;
        public float lineWidth = 2.0f;
        public Color lineColor = Color.FromArgb(230,220,220);
        public LinePattern linePattern;
        public override string ToString()
        {
            return name;
        }
        private static Utils utils = new Utils();
        public static  List<LinePatternItem> InitCustomLineType()
        {
            List<LinePatternItem> retlist = new List<LinePatternItem>();
            LinePatternItem item = new LinePatternItem();
            item.useLinePatten = false;
            item.name = "基本";
            retlist.Add(item);
            item = new LinePatternItem();
            item.name = "铁路";
            item.useLinePatten = true;
            item.linePattern = new LinePattern();
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.Black), 4.0f, tkDashStyle.dsSolid);
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.White), 3.0f, tkDashStyle.dsDot);
            retlist.Add(item);
            item = new LinePatternItem();
            item.name = "高速";
            item.useLinePatten = true;
            item.linePattern = new LinePattern();
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 5.0f, tkDashStyle.dsSolid);
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.Yellow), 4.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.name = "国道";
            item.useLinePatten = true;
            item.linePattern = new LinePattern();
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 4.0f, tkDashStyle.dsSolid);
            item.linePattern.AddLine((uint)ColorTranslator.ToOle(Color.FromArgb(255, 0, 255)), 3.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.name = "省道";
            item.useLinePatten = true;
            item.linePattern = new LinePattern();
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.Gray), 3.0f, tkDashStyle.dsSolid);
            item.linePattern.AddLine(utils.ColorByName(tkMapColor.Orange), 2.0f, tkDashStyle.dsSolid);
            retlist.Add(item);
            item = new LinePatternItem();
            item.name = "县级路";
            item.useLinePatten = false;
            item.lineWidth = 2;
            item.lineColor = Color.FromArgb(0, 255, 0);
            retlist.Add(item);
            item = new LinePatternItem();
            item.name = "乡村路";
            item.useLinePatten = false;
            item.lineWidth = 2;
            item.lineColor = Color.FromArgb(240, 150, 150);
            retlist.Add(item);

            return retlist;
        }
    }
}
