﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model.CellParam
{
    public class CellParamProblemPointRuleMng
    {
        private static CellParamProblemPointRuleMng instance = null;
        private CellParamProblemPointRuleMng()
        {
            init();
        }
        public static CellParamProblemPointRuleMng GetInstance()
        {
            if (instance == null)
            {
                instance = new CellParamProblemPointRuleMng();
            }
            return instance;
        }
        private Dictionary<int, ProblemPointRuleDetail> ruleIDDic = null;
        public ProblemPointRuleDetail GetRule(int ruleID)
        {
            ProblemPointRuleDetail rule = null;
            ruleIDDic.TryGetValue(ruleID, out rule);
            return rule;
        }
        private void init()
        {
            ruleIDDic = new Dictionary<int, ProblemPointRuleDetail>();
            string sqlTxt = @"select a.规则ID,a.规则名称,a.参数规则描述,b.检查操作,b.检查值,c.参数名,c.取值范围,c.缺省取值 from  
 [MTNOH_APP_PARAMS].[dbo].[TB_参数问题点_规则定义] a
,[MTNOH_APP_PARAMS].[dbo].[TB_参数问题点_规则定义_检查项] b
,[MTNOH_APP_PARAMS].[dbo].[TB_参数体系_参数定义]c
where a.规则ID=b.规则ID and b.参数ID=c.参数ID";
            using (SqlConnection conn = new SqlConnection(CellParamCfgManager.GetInstance().DBConnectionStr))
            {
                SqlCommand command = new SqlCommand(sqlTxt, conn);
                conn.Open();
                SqlDataReader reader = command.ExecuteReader();
                while (reader.Read())
                {
                    try
                    {
                        ProblemPointRuleDetail rule = new ProblemPointRuleDetail();
                        rule.ID = (int)reader["规则ID"];
                        rule.Name = reader["规则名称"] as string;
                        rule.Description = reader["参数规则描述"] as string;
                        rule.CheckDescription = reader["检查操作"] as string;
                        rule.CheckValue = reader["检查值"] as string;
                        rule.Range = reader["取值范围"] as string;
                        rule.DefaultValue = reader["缺省取值"] as string;
                        rule.ParamName = reader["参数名"] as string;
                        if (ruleIDDic.ContainsKey(rule.ID))
                        {
                            MessageBox.Show("存在相同ID的规则,ID:" + rule.ID.ToString());
                        }
                        ruleIDDic[rule.ID] = rule;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                    }
                }
            }
        }
    }

    public class ProblemPointRuleDetail
    {
        public int ID
        {
            get;
            set;
        }
        public string Name
        {
            get;
            set;
        }
        public string Description
        {
            get;
            set;
        }
        public string CheckDescription
        {
            get;
            set;
        }
        public string CheckValue
        {
            get;
            set;
        }
        public string Range
        {
            get;
            set;
        }
        public string DefaultValue
        {
            get;
            set;
        }
        public string ParamName
        {
            get;
            set;
        }
        //public List<ProblemPointRuleCheckItem> checkItem = new List<ProblemPointRuleCheckItem>();
    }

    public class ProblemPointRuleCheckItem
    {
 
    }

    public class ProblemPointParamItem
    {
 
    }

}
