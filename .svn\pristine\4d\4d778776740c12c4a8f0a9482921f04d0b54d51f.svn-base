using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class DTDisplayParameter
    {
        public DTDisplayParameter(DTDisplayParameterInfo info, int arrayIndex)
        {
            Info = info;
            ArrayIndex = arrayIndex;
        }

        public DTDisplayParameterInfo Info { get; set; }

        public int ArrayIndex { get; set; }

        public DTParameter Parameter
        {
            get { return Info.ParamInfo[ArrayIndex]; }
        }
    }
}
