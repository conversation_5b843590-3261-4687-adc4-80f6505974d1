﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellForm_W : MinCloseForm
    {
        public LowSpeedCellForm_W(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }
        public void FillData(List<LowSpeedCell_W> LowSpeedCell_WList)
        {
            initGridColumns();
            gridControl.DataSource = LowSpeedCell_WList;
            gridControl.RefreshDataSource();
        }
        private void initGridColumns()
        {
            gridView.Columns.Clear();
            List<GridColumn> gridColumnList = new List<GridColumn>();
            GridColumn gridColumn = new GridColumn();
            gridColumn.Caption = "小区名";
            gridColumn.FieldName = "CellName";
            gridColumn.Name = "gridColumnCellName";
            gridColumn.Width = 102;
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "LAC";
            gridColumn.FieldName = "LAC";
            gridColumn.Name = "gridColumnLAC";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "CI";
            gridColumn.FieldName = "CI";
            gridColumn.Name = "gridColumnCI";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均下载速率(Kbps)";
            gridColumn.FieldName = "AvgSpeedDl";
            gridColumn.Name = "gridColumnDlSpeedAvg";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均上传速率(Kbps)";
            gridColumn.FieldName = "AvgSpeedUl";
            gridColumn.Name = "gridColumnUlSpeedAvg";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均速率(Kbps)";
            gridColumn.FieldName = "AvgSpeed";
            gridColumn.Name = "gridColumnSpeedAvg";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "平均RSCP";
            gridColumn.FieldName = "AvgRsrp";
            gridColumn.Name = "gridColumnRxLevAvg";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "低速率采样点数";
            gridColumn.FieldName = "LowSpeedTpCount";
            gridColumn.Name = "gridColumnSpeedSample";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "小区总采样点数";
            gridColumn.FieldName = "TotalSampleCount";
            gridColumn.Name = "gridColumnAllSampleCount";
            gridColumnList.Add(gridColumn);

            gridColumn = new GridColumn();
            gridColumn.Caption = "低速率采样占比(%)";
            gridColumn.FieldName = "LowSpeedTpPercent";
            gridColumn.Name = "gridColumnSamplePercent";
            gridColumnList.Add(gridColumn);

            for (int i = 0; i < gridColumnList.Count; i++)
            {
                gridColumnList[i].VisibleIndex = i;
            }
            gridView.Columns.AddRange(gridColumnList.ToArray());
        }
        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }
        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                LowSpeedCell_W item = gridView.GetRow(gridView.GetSelectedRows()[0]) as LowSpeedCell_W;
                if (item.TestPointList.Count == 0)
                {
                    return;
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in item.TestPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
