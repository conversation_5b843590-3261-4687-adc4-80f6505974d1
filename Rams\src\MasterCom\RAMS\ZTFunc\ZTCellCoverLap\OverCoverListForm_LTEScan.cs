﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class OverCoverListForm_LTEScan : MinCloseForm
    {
        public OverCoverListForm_LTEScan()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        internal void FillData(List<OverCoverCellBase> overCells)
        {
            gridCtrlAll.DataSource = overCells;
            gridCtrlAll.RefreshDataSource();

            gridCtrlRemoved.DataSource = new List<OverCoverCellBase>();
            gridCtrlRemoved.RefreshDataSource();
        }

        private void gridCtrlAll_DragEnter(object sender, DragEventArgs e)
        {
            GridControl owner = getOwner(e.Data);
            if (owner == null || owner == gridCtrlAll)
            {
                return;
            }
            e.Effect = DragDropEffects.Move;
        }

        private void gridCtrlAll_DragDrop(object sender, DragEventArgs e)
        {
            dragDrop(getOwner(e.Data), (GridControl)sender);
        }

        private void dragDrop(GridControl fromCtrl, GridControl toCtrl)
        {
            List<OverCoverCellBase> fromDataSrc = fromCtrl.DataSource as List<OverCoverCellBase>;
            List<OverCoverCellBase> toDataSrc = toCtrl.DataSource as List<OverCoverCellBase>;
            GridView fromView = ((GridView)(fromCtrl.MainView));
            int[] rows = fromView.GetSelectedRows();
            List<OverCoverCellBase> toChangeCells = new List<OverCoverCellBase>();
            foreach (int row in rows)
            {
                OverCoverCellBase cell = fromView.GetRow(row) as OverCoverCellBase;
                if (cell != null)
                {
                    toChangeCells.Add(cell);
                }
            }
            foreach (OverCoverCellBase cell in toChangeCells)
            {
                fromDataSrc.Remove(cell);
                toDataSrc.Add(cell);
            }
            fromCtrl.RefreshDataSource();
            toCtrl.RefreshDataSource();
            refreashGis(fromDataSrc);
        }

        private void refreashGis(List<OverCoverCellBase> cells)
        {
            if (cells.Count > 0)
            {
                switch (cells[0])
                {
                    case LteOverCoverCell _:
                        LteOverCoverCell.RefreashGis(MainModel, this, cells);
                        break;
                    case NrOverCoverCell _:
                        NrOverCoverCell.RefreashGis(MainModel, this, cells);
                        break;
                }
            }

            //MainModel.ClearDTData();
            //MainModel.SelectedLTECells.Clear();
            //List<TestPoint> points = new List<TestPoint>();
            //foreach (OverCoverCellBase cell in cells)
            //{
            //    MainModel.SelectedLTECells.Add(cell.Cell);
            //    foreach (TestPoint tp in cell.OverCoverPoints)
            //    {
            //        if (!points.Contains(tp))
            //        {
            //            points.Add(tp);
            //        }
            //    }
            //}
            //foreach (TestPoint tp in points)
            //{
            //    MainModel.DTDataManager.Add(tp);
            //}
            //MainModel.FireDTDataChanged(this);
            //MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }

        private GridControl getOwner(IDataObject data)
        {
            GridControl ctrl = data.GetData(typeof(GridControl)) as GridControl;
            return ctrl;
        }

        private void gridCtrlRemoved_DragEnter(object sender, DragEventArgs e)
        {
            GridControl owner = getOwner(e.Data);
            if (owner==null||owner==gridCtrlRemoved)
            {
                return;
            }
            e.Effect = DragDropEffects.Move;
        }

        private void gridCtrlRemoved_DragDrop(object sender, DragEventArgs e)
        {
            dragDrop(getOwner(e.Data), (GridControl)sender);
        }

        GridHitInfo hitInfoAll = null;
        private void gridCtrlAll_MouseDown(object sender, MouseEventArgs e)
        {
            hitInfoAll = viewAll.CalcHitInfo(new Point(e.X, e.Y));
            if (hitInfoAll.RowHandle < 0 || !hitInfoAll.InRow)
                hitInfoAll = null;
        }

        private void gridCtrlAll_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button != MouseButtons.Left)
                return;
            if (hitInfoAll == null)
                return;
            Rectangle dragRect = new Rectangle(new Point(
                hitInfoAll.HitPoint.X - SystemInformation.DragSize.Width / 2,
                hitInfoAll.HitPoint.Y - SystemInformation.DragSize.Height / 2), SystemInformation.DragSize);
            if (!dragRect.Contains(new Point(e.X, e.Y)))
            {
                gridCtrlAll.DoDragDrop(gridCtrlAll, DragDropEffects.Move);
            }
        }

        GridHitInfo hitInfoRemoved = null;
        private void gridCtrlRemoved_MouseDown(object sender, MouseEventArgs e)
        {
            hitInfoRemoved = viewRemoved.CalcHitInfo(new Point(e.X, e.Y));
            if (hitInfoRemoved.RowHandle < 0 || !hitInfoRemoved.InRow)
                hitInfoRemoved = null;
        }

        private void gridCtrlRemoved_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button != MouseButtons.Left)
                return;
            if (hitInfoRemoved == null)
                return;
            Rectangle dragRect = new Rectangle(new Point(
                hitInfoRemoved.HitPoint.X - SystemInformation.DragSize.Width / 2,
                hitInfoRemoved.HitPoint.Y - SystemInformation.DragSize.Height / 2), SystemInformation.DragSize);
            if (!dragRect.Contains(new Point(e.X, e.Y)))
            {
                gridCtrlRemoved.DoDragDrop(gridCtrlRemoved, DragDropEffects.Move);
            }
        }

        private void gridCtrlRemoved_DragOver(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Move;
        }

        private void view_DoubleClick(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = view.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int[] rows = view.GetSelectedRows();
            List<OverCoverCellBase> cells = new List<OverCoverCellBase>();
            foreach (int row in rows)
            {
                OverCoverCellBase cell = view.GetRow(row) as OverCoverCellBase;
                if (cell!=null)
                {
                    cells.Add(cell);
                }
            }
            refreashGis(cells);
        }

        private void miMove2GridRemoved_Click(object sender, EventArgs e)
        {
            dragDrop(gridCtrlAll, gridCtrlRemoved);
        }

        private void miExp2XlsAll_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(viewAll);
        }

        private void miMove2AllGrid_Click(object sender, EventArgs e)
        {
            dragDrop(gridCtrlRemoved, gridCtrlAll);
        }

        private void miExp2XlsRemoved_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(viewRemoved);
        }

        private void miShowRemovedInGis_Click(object sender, EventArgs e)
        {
            List<OverCoverCellBase> cells = gridCtrlRemoved.DataSource as List<OverCoverCellBase>;
            refreashGis(cells);
        }

        private void miShowAllInGis_Click(object sender, EventArgs e)
        {
            List<OverCoverCellBase> cells = gridCtrlAll.DataSource as List<OverCoverCellBase>;
            refreashGis(cells);
        }
    }
}
