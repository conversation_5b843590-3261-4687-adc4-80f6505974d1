﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LtePlanningCellResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.tabSummary = new System.Windows.Forms.TabPage();
            this.gcSummary = new DevExpress.XtraGrid.GridControl();
            this.gvSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabNotOpen = new System.Windows.Forms.TabPage();
            this.gcNotOpened = new DevExpress.XtraGrid.GridControl();
            this.gvNotOpened = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabSampleLess = new System.Windows.Forms.TabPage();
            this.gcSampleLess = new DevExpress.XtraGrid.GridControl();
            this.gvSampleLess = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabOverCoverage = new System.Windows.Forms.TabPage();
            this.gcOverCoverage = new DevExpress.XtraGrid.GridControl();
            this.gvOverCoverage = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabWeakCoverage = new System.Windows.Forms.TabPage();
            this.gcWeakCoverage = new DevExpress.XtraGrid.GridControl();
            this.gvWeakCoverage = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn90 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn91 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn92 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn94 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn95 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn96 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn97 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn98 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn99 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn100 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn101 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn102 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn103 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn104 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn105 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabWeakQuality = new System.Windows.Forms.TabPage();
            this.gcWeakQuality = new DevExpress.XtraGrid.GridControl();
            this.gvWeakQuality = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn107 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn108 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn109 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn110 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn111 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn112 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn113 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn114 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn115 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn116 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn118 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn119 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn120 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn121 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn122 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn123 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn124 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn125 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn126 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn127 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn128 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn129 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportCurXls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAllXls = new System.Windows.Forms.ToolStripMenuItem();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabControl.SuspendLayout();
            this.tabSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.tabNotOpen.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcNotOpened)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvNotOpened)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.tabSampleLess.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcSampleLess)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSampleLess)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.tabOverCoverage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcOverCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvOverCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            this.tabWeakCoverage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcWeakCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakCoverage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.tabWeakQuality.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcWeakQuality)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakQuality)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.Controls.Add(this.tabSummary);
            this.tabControl.Controls.Add(this.tabNotOpen);
            this.tabControl.Controls.Add(this.tabSampleLess);
            this.tabControl.Controls.Add(this.tabOverCoverage);
            this.tabControl.Controls.Add(this.tabWeakCoverage);
            this.tabControl.Controls.Add(this.tabWeakQuality);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(1022, 505);
            this.tabControl.TabIndex = 0;
            // 
            // tabSummary
            // 
            this.tabSummary.Controls.Add(this.gcSummary);
            this.tabSummary.Location = new System.Drawing.Point(4, 23);
            this.tabSummary.Name = "tabSummary";
            this.tabSummary.Size = new System.Drawing.Size(1014, 478);
            this.tabSummary.TabIndex = 2;
            this.tabSummary.Text = "汇总统计";
            this.tabSummary.UseVisualStyleBackColor = true;
            // 
            // gcSummary
            // 
            this.gcSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSummary.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcSummary.Location = new System.Drawing.Point(0, 0);
            this.gcSummary.MainView = this.gvSummary;
            this.gcSummary.Name = "gcSummary";
            this.gcSummary.ShowOnlyPredefinedDetails = true;
            this.gcSummary.Size = new System.Drawing.Size(1014, 478);
            this.gcSummary.TabIndex = 4;
            this.gcSummary.UseEmbeddedNavigator = true;
            this.gcSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSummary,
            this.gridView4});
            // 
            // gvSummary
            // 
            this.gvSummary.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn58,
            this.gridColumn82,
            this.gridColumn1,
            this.gridColumn25,
            this.gridColumn31,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn51,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57});
            this.gvSummary.GridControl = this.gcSummary;
            this.gvSummary.Name = "gvSummary";
            this.gvSummary.OptionsBehavior.Editable = false;
            this.gvSummary.OptionsDetail.ShowDetailTabs = false;
            this.gvSummary.OptionsView.ShowGroupPanel = false;
            this.gvSummary.OptionsView.ShowIndicator = false;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "基站个数";
            this.gridColumn32.FieldName = "BtsCount";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 0;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "小区个数";
            this.gridColumn33.FieldName = "CellCount";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 1;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "主服个数";
            this.gridColumn58.FieldName = "MainCellCount";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 2;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "邻区个数";
            this.gridColumn82.FieldName = "NbCellCount";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 3;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "总采样点";
            this.gridColumn1.FieldName = "SampleCount";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 4;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "主服采样点";
            this.gridColumn25.FieldName = "MainSampleCount";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "邻区采样点";
            this.gridColumn31.FieldName = "NbSampleCount";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 6;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "未开通基站数";
            this.gridColumn34.FieldName = "NotOpenedCount";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 7;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "未开通基站占比";
            this.gridColumn35.DisplayFormat.FormatString = "P2";
            this.gridColumn35.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn35.FieldName = "NotOpenedRate";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 8;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "空口小区数";
            this.gridColumn36.FieldName = "SampleLessCount";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 9;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "空口小区占比";
            this.gridColumn51.DisplayFormat.FormatString = "P2";
            this.gridColumn51.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn51.FieldName = "SampleLessRate";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 10;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "过覆盖小区数";
            this.gridColumn52.FieldName = "OverCoverageCount";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 11;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "过覆盖小区占比";
            this.gridColumn53.DisplayFormat.FormatString = "P2";
            this.gridColumn53.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn53.FieldName = "OverCoverageRate";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 12;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "弱覆盖小区数";
            this.gridColumn54.FieldName = "WeakCoverageCount";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 13;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "弱覆盖小区占比";
            this.gridColumn55.DisplayFormat.FormatString = "P2";
            this.gridColumn55.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn55.FieldName = "WeakCoverageRate";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 14;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "质差小区数";
            this.gridColumn56.FieldName = "WeakQualityCount";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 15;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "质差小区占比";
            this.gridColumn57.DisplayFormat.FormatString = "P2";
            this.gridColumn57.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn57.FieldName = "WeakQualityRate";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 16;
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn49,
            this.gridColumn50});
            this.gridView4.GridControl = this.gcSummary;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsBehavior.Editable = false;
            this.gridView4.OptionsDetail.ShowDetailTabs = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "小区名";
            this.gridColumn37.FieldName = "CellName";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 0;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "小区ID";
            this.gridColumn38.FieldName = "CellID";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 1;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "EARFCN";
            this.gridColumn39.FieldName = "EARFCN";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 2;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "PCI";
            this.gridColumn40.FieldName = "PCI";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 3;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "经度";
            this.gridColumn41.FieldName = "Longitude";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 4;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "纬度";
            this.gridColumn49.FieldName = "Latitude";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 5;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "距离";
            this.gridColumn50.DisplayFormat.FormatString = "F2";
            this.gridColumn50.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn50.FieldName = "Distance";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 6;
            // 
            // tabNotOpen
            // 
            this.tabNotOpen.Controls.Add(this.gcNotOpened);
            this.tabNotOpen.Location = new System.Drawing.Point(4, 23);
            this.tabNotOpen.Name = "tabNotOpen";
            this.tabNotOpen.Size = new System.Drawing.Size(1014, 478);
            this.tabNotOpen.TabIndex = 1;
            this.tabNotOpen.Text = "未开通基站";
            this.tabNotOpen.UseVisualStyleBackColor = true;
            // 
            // gcNotOpened
            // 
            this.gcNotOpened.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcNotOpened.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcNotOpened.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcNotOpened.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcNotOpened.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcNotOpened.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcNotOpened.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcNotOpened.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcNotOpened.Location = new System.Drawing.Point(0, 0);
            this.gcNotOpened.MainView = this.gvNotOpened;
            this.gcNotOpened.Name = "gcNotOpened";
            this.gcNotOpened.ShowOnlyPredefinedDetails = true;
            this.gcNotOpened.Size = new System.Drawing.Size(1014, 478);
            this.gcNotOpened.TabIndex = 3;
            this.gcNotOpened.UseEmbeddedNavigator = true;
            this.gcNotOpened.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvNotOpened,
            this.gridView3});
            // 
            // gvNotOpened
            // 
            this.gvNotOpened.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30});
            this.gvNotOpened.GridControl = this.gcNotOpened;
            this.gvNotOpened.Name = "gvNotOpened";
            this.gvNotOpened.OptionsBehavior.Editable = false;
            this.gvNotOpened.OptionsDetail.ShowDetailTabs = false;
            this.gvNotOpened.OptionsView.ShowGroupPanel = false;
            this.gvNotOpened.OptionsView.ShowIndicator = false;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "基站名";
            this.gridColumn26.FieldName = "BtsName";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 0;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "eNodeBID";
            this.gridColumn27.FieldName = "ENodeBID";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 1;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "经度";
            this.gridColumn28.FieldName = "Longitude";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 2;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "纬度";
            this.gridColumn29.FieldName = "Latitude";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 3;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "地址";
            this.gridColumn30.FieldName = "Address";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 4;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48});
            this.gridView3.GridControl = this.gcNotOpened;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsDetail.ShowDetailTabs = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "小区名";
            this.gridColumn42.FieldName = "CellName";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 0;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "小区ID";
            this.gridColumn43.FieldName = "CellID";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 1;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "EARFCN";
            this.gridColumn44.FieldName = "EARFCN";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 2;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "PCI";
            this.gridColumn45.FieldName = "PCI";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 3;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "经度";
            this.gridColumn46.FieldName = "Longitude";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 4;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "纬度";
            this.gridColumn47.FieldName = "Latitude";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 5;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "距离";
            this.gridColumn48.DisplayFormat.FormatString = "F2";
            this.gridColumn48.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn48.FieldName = "Distance";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 6;
            // 
            // tabSampleLess
            // 
            this.tabSampleLess.Controls.Add(this.gcSampleLess);
            this.tabSampleLess.Location = new System.Drawing.Point(4, 23);
            this.tabSampleLess.Name = "tabSampleLess";
            this.tabSampleLess.Size = new System.Drawing.Size(1014, 478);
            this.tabSampleLess.TabIndex = 0;
            this.tabSampleLess.Text = "空口问题小区";
            this.tabSampleLess.UseVisualStyleBackColor = true;
            // 
            // gcSampleLess
            // 
            this.gcSampleLess.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSampleLess.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcSampleLess.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcSampleLess.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcSampleLess.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcSampleLess.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcSampleLess.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcSampleLess.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcSampleLess.Location = new System.Drawing.Point(0, 0);
            this.gcSampleLess.MainView = this.gvSampleLess;
            this.gcSampleLess.Name = "gcSampleLess";
            this.gcSampleLess.ShowOnlyPredefinedDetails = true;
            this.gcSampleLess.Size = new System.Drawing.Size(1014, 478);
            this.gcSampleLess.TabIndex = 2;
            this.gcSampleLess.UseEmbeddedNavigator = true;
            this.gcSampleLess.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSampleLess,
            this.gridView2});
            // 
            // gvSampleLess
            // 
            this.gvSampleLess.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn15,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24});
            this.gvSampleLess.GridControl = this.gcSampleLess;
            this.gvSampleLess.Name = "gvSampleLess";
            this.gvSampleLess.OptionsBehavior.Editable = false;
            this.gvSampleLess.OptionsDetail.ShowDetailTabs = false;
            this.gvSampleLess.OptionsView.ShowGroupPanel = false;
            this.gvSampleLess.OptionsView.ShowIndicator = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "基站名";
            this.gridColumn2.FieldName = "BtsName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "eNodeBID";
            this.gridColumn3.FieldName = "ENodeBID";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "经度";
            this.gridColumn4.FieldName = "Longitude";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "纬度";
            this.gridColumn5.FieldName = "Latitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "小区名";
            this.gridColumn6.FieldName = "CellName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "总采样点";
            this.gridColumn7.FieldName = "SampleCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "主服采样点";
            this.gridColumn8.FieldName = "MainSampleCount";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "邻区采样点";
            this.gridColumn15.FieldName = "NbSampleCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 7;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "CellID";
            this.gridColumn17.FieldName = "CellID";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 8;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "TAC";
            this.gridColumn18.FieldName = "Tac";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 9;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "EARFCN";
            this.gridColumn20.FieldName = "Earfcn";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 10;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "PCI";
            this.gridColumn21.FieldName = "Pci";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 11;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "Direction";
            this.gridColumn22.FieldName = "Direction";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 12;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "Downward";
            this.gridColumn23.FieldName = "Downward";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 13;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "Altitude";
            this.gridColumn24.FieldName = "Altitude";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 14;
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn16,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14});
            this.gridView2.GridControl = this.gcSampleLess;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsDetail.ShowDetailTabs = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "小区名";
            this.gridColumn9.FieldName = "CellName";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "小区ID";
            this.gridColumn10.FieldName = "CellID";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "EARFCN";
            this.gridColumn16.FieldName = "EARFCN";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 2;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "PCI";
            this.gridColumn11.FieldName = "PCI";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 3;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "经度";
            this.gridColumn12.FieldName = "Longitude";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 4;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "纬度";
            this.gridColumn13.FieldName = "Latitude";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 5;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "距离";
            this.gridColumn14.DisplayFormat.FormatString = "F2";
            this.gridColumn14.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn14.FieldName = "Distance";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 6;
            // 
            // tabOverCoverage
            // 
            this.tabOverCoverage.Controls.Add(this.gcOverCoverage);
            this.tabOverCoverage.Location = new System.Drawing.Point(4, 23);
            this.tabOverCoverage.Name = "tabOverCoverage";
            this.tabOverCoverage.Size = new System.Drawing.Size(1014, 478);
            this.tabOverCoverage.TabIndex = 3;
            this.tabOverCoverage.Text = "过覆盖小区";
            this.tabOverCoverage.UseVisualStyleBackColor = true;
            // 
            // gcOverCoverage
            // 
            this.gcOverCoverage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcOverCoverage.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcOverCoverage.Location = new System.Drawing.Point(0, 0);
            this.gcOverCoverage.MainView = this.gvOverCoverage;
            this.gcOverCoverage.Name = "gcOverCoverage";
            this.gcOverCoverage.ShowOnlyPredefinedDetails = true;
            this.gcOverCoverage.Size = new System.Drawing.Size(1014, 478);
            this.gcOverCoverage.TabIndex = 3;
            this.gcOverCoverage.UseEmbeddedNavigator = true;
            this.gcOverCoverage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvOverCoverage,
            this.gridView5});
            // 
            // gvOverCoverage
            // 
            this.gvOverCoverage.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn59,
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn19,
            this.gridColumn64,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn74});
            this.gvOverCoverage.GridControl = this.gcOverCoverage;
            this.gvOverCoverage.Name = "gvOverCoverage";
            this.gvOverCoverage.OptionsBehavior.Editable = false;
            this.gvOverCoverage.OptionsDetail.ShowDetailTabs = false;
            this.gvOverCoverage.OptionsView.ShowGroupPanel = false;
            this.gvOverCoverage.OptionsView.ShowIndicator = false;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "基站名";
            this.gridColumn59.FieldName = "BtsName";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 0;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "eNodeBID";
            this.gridColumn60.FieldName = "ENodeBID";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 1;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "经度";
            this.gridColumn61.FieldName = "Longitude";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 2;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "纬度";
            this.gridColumn62.FieldName = "Latitude";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 3;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "小区名";
            this.gridColumn63.FieldName = "CellName";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 4;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "总采样点";
            this.gridColumn64.FieldName = "SampleCount";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 6;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "过覆盖采样点";
            this.gridColumn65.FieldName = "OverCoverSampleCount";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 7;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "过覆盖占比";
            this.gridColumn66.DisplayFormat.FormatString = "P2";
            this.gridColumn66.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn66.FieldName = "OverCoverSampleRate";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 8;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "CellID";
            this.gridColumn67.FieldName = "CellID";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 9;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "TAC";
            this.gridColumn68.FieldName = "Tac";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 10;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "EARFCN";
            this.gridColumn70.FieldName = "Earfcn";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 11;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "PCI";
            this.gridColumn71.FieldName = "Pci";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 12;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "Direction";
            this.gridColumn72.FieldName = "Direction";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 13;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "Downward";
            this.gridColumn73.FieldName = "Downward";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 14;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "Altitude";
            this.gridColumn74.FieldName = "Altitude";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 15;
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn75,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn78,
            this.gridColumn79,
            this.gridColumn80,
            this.gridColumn81});
            this.gridView5.GridControl = this.gcOverCoverage;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.Editable = false;
            this.gridView5.OptionsDetail.ShowDetailTabs = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.OptionsView.ShowIndicator = false;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "小区名";
            this.gridColumn75.FieldName = "CellName";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 0;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "小区ID";
            this.gridColumn76.FieldName = "CellID";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 1;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "EARFCN";
            this.gridColumn77.FieldName = "EARFCN";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 2;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "PCI";
            this.gridColumn78.FieldName = "PCI";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 3;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "经度";
            this.gridColumn79.FieldName = "Longitude";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 4;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "纬度";
            this.gridColumn80.FieldName = "Latitude";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 5;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "距离";
            this.gridColumn81.DisplayFormat.FormatString = "F2";
            this.gridColumn81.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn81.FieldName = "Distance";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 6;
            // 
            // tabWeakCoverage
            // 
            this.tabWeakCoverage.Controls.Add(this.gcWeakCoverage);
            this.tabWeakCoverage.Location = new System.Drawing.Point(4, 23);
            this.tabWeakCoverage.Name = "tabWeakCoverage";
            this.tabWeakCoverage.Size = new System.Drawing.Size(1014, 478);
            this.tabWeakCoverage.TabIndex = 4;
            this.tabWeakCoverage.Text = "弱覆盖小区";
            this.tabWeakCoverage.UseVisualStyleBackColor = true;
            // 
            // gcWeakCoverage
            // 
            this.gcWeakCoverage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcWeakCoverage.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcWeakCoverage.Location = new System.Drawing.Point(0, 0);
            this.gcWeakCoverage.MainView = this.gvWeakCoverage;
            this.gcWeakCoverage.Name = "gcWeakCoverage";
            this.gcWeakCoverage.ShowOnlyPredefinedDetails = true;
            this.gcWeakCoverage.Size = new System.Drawing.Size(1014, 478);
            this.gcWeakCoverage.TabIndex = 4;
            this.gcWeakCoverage.UseEmbeddedNavigator = true;
            this.gcWeakCoverage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvWeakCoverage,
            this.gridView6});
            // 
            // gvWeakCoverage
            // 
            this.gvWeakCoverage.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn85,
            this.gridColumn86,
            this.gridColumn87,
            this.gridColumn88,
            this.gridColumn89,
            this.gridColumn90,
            this.gridColumn91,
            this.gridColumn92,
            this.gridColumn94,
            this.gridColumn95,
            this.gridColumn96,
            this.gridColumn97,
            this.gridColumn98});
            this.gvWeakCoverage.GridControl = this.gcWeakCoverage;
            this.gvWeakCoverage.Name = "gvWeakCoverage";
            this.gvWeakCoverage.OptionsBehavior.Editable = false;
            this.gvWeakCoverage.OptionsDetail.ShowDetailTabs = false;
            this.gvWeakCoverage.OptionsView.ShowGroupPanel = false;
            this.gvWeakCoverage.OptionsView.ShowIndicator = false;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "基站名";
            this.gridColumn83.FieldName = "BtsName";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 0;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "eNodeBID";
            this.gridColumn84.FieldName = "ENodeBID";
            this.gridColumn84.Name = "gridColumn84";
            this.gridColumn84.Visible = true;
            this.gridColumn84.VisibleIndex = 1;
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "经度";
            this.gridColumn85.FieldName = "Longitude";
            this.gridColumn85.Name = "gridColumn85";
            this.gridColumn85.Visible = true;
            this.gridColumn85.VisibleIndex = 2;
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "纬度";
            this.gridColumn86.FieldName = "Latitude";
            this.gridColumn86.Name = "gridColumn86";
            this.gridColumn86.Visible = true;
            this.gridColumn86.VisibleIndex = 3;
            // 
            // gridColumn87
            // 
            this.gridColumn87.Caption = "小区名";
            this.gridColumn87.FieldName = "CellName";
            this.gridColumn87.Name = "gridColumn87";
            this.gridColumn87.Visible = true;
            this.gridColumn87.VisibleIndex = 4;
            // 
            // gridColumn88
            // 
            this.gridColumn88.Caption = "总采样点";
            this.gridColumn88.FieldName = "SampleCount";
            this.gridColumn88.Name = "gridColumn88";
            this.gridColumn88.Visible = true;
            this.gridColumn88.VisibleIndex = 5;
            // 
            // gridColumn89
            // 
            this.gridColumn89.Caption = "弱覆盖采样点";
            this.gridColumn89.FieldName = "WeakCoverSampleCount";
            this.gridColumn89.Name = "gridColumn89";
            this.gridColumn89.Visible = true;
            this.gridColumn89.VisibleIndex = 6;
            // 
            // gridColumn90
            // 
            this.gridColumn90.Caption = "弱覆盖占比";
            this.gridColumn90.DisplayFormat.FormatString = "P2";
            this.gridColumn90.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn90.FieldName = "WeakCoverSampleRate";
            this.gridColumn90.Name = "gridColumn90";
            this.gridColumn90.Visible = true;
            this.gridColumn90.VisibleIndex = 7;
            // 
            // gridColumn91
            // 
            this.gridColumn91.Caption = "CellID";
            this.gridColumn91.FieldName = "CellID";
            this.gridColumn91.Name = "gridColumn91";
            this.gridColumn91.Visible = true;
            this.gridColumn91.VisibleIndex = 8;
            // 
            // gridColumn92
            // 
            this.gridColumn92.Caption = "TAC";
            this.gridColumn92.FieldName = "Tac";
            this.gridColumn92.Name = "gridColumn92";
            this.gridColumn92.Visible = true;
            this.gridColumn92.VisibleIndex = 9;
            // 
            // gridColumn94
            // 
            this.gridColumn94.Caption = "EARFCN";
            this.gridColumn94.FieldName = "Earfcn";
            this.gridColumn94.Name = "gridColumn94";
            this.gridColumn94.Visible = true;
            this.gridColumn94.VisibleIndex = 10;
            // 
            // gridColumn95
            // 
            this.gridColumn95.Caption = "PCI";
            this.gridColumn95.FieldName = "Pci";
            this.gridColumn95.Name = "gridColumn95";
            this.gridColumn95.Visible = true;
            this.gridColumn95.VisibleIndex = 11;
            // 
            // gridColumn96
            // 
            this.gridColumn96.Caption = "Direction";
            this.gridColumn96.FieldName = "Direction";
            this.gridColumn96.Name = "gridColumn96";
            this.gridColumn96.Visible = true;
            this.gridColumn96.VisibleIndex = 12;
            // 
            // gridColumn97
            // 
            this.gridColumn97.Caption = "Downward";
            this.gridColumn97.FieldName = "Downward";
            this.gridColumn97.Name = "gridColumn97";
            this.gridColumn97.Visible = true;
            this.gridColumn97.VisibleIndex = 13;
            // 
            // gridColumn98
            // 
            this.gridColumn98.Caption = "Altitude";
            this.gridColumn98.FieldName = "Altitude";
            this.gridColumn98.Name = "gridColumn98";
            this.gridColumn98.Visible = true;
            this.gridColumn98.VisibleIndex = 14;
            // 
            // gridView6
            // 
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn99,
            this.gridColumn100,
            this.gridColumn101,
            this.gridColumn102,
            this.gridColumn103,
            this.gridColumn104,
            this.gridColumn105});
            this.gridView6.GridControl = this.gcWeakCoverage;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsBehavior.Editable = false;
            this.gridView6.OptionsDetail.ShowDetailTabs = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            this.gridView6.OptionsView.ShowIndicator = false;
            // 
            // gridColumn99
            // 
            this.gridColumn99.Caption = "小区名";
            this.gridColumn99.FieldName = "CellName";
            this.gridColumn99.Name = "gridColumn99";
            this.gridColumn99.Visible = true;
            this.gridColumn99.VisibleIndex = 0;
            // 
            // gridColumn100
            // 
            this.gridColumn100.Caption = "小区ID";
            this.gridColumn100.FieldName = "CellID";
            this.gridColumn100.Name = "gridColumn100";
            this.gridColumn100.Visible = true;
            this.gridColumn100.VisibleIndex = 1;
            // 
            // gridColumn101
            // 
            this.gridColumn101.Caption = "EARFCN";
            this.gridColumn101.FieldName = "EARFCN";
            this.gridColumn101.Name = "gridColumn101";
            this.gridColumn101.Visible = true;
            this.gridColumn101.VisibleIndex = 2;
            // 
            // gridColumn102
            // 
            this.gridColumn102.Caption = "PCI";
            this.gridColumn102.FieldName = "PCI";
            this.gridColumn102.Name = "gridColumn102";
            this.gridColumn102.Visible = true;
            this.gridColumn102.VisibleIndex = 3;
            // 
            // gridColumn103
            // 
            this.gridColumn103.Caption = "经度";
            this.gridColumn103.FieldName = "Longitude";
            this.gridColumn103.Name = "gridColumn103";
            this.gridColumn103.Visible = true;
            this.gridColumn103.VisibleIndex = 4;
            // 
            // gridColumn104
            // 
            this.gridColumn104.Caption = "纬度";
            this.gridColumn104.FieldName = "Latitude";
            this.gridColumn104.Name = "gridColumn104";
            this.gridColumn104.Visible = true;
            this.gridColumn104.VisibleIndex = 5;
            // 
            // gridColumn105
            // 
            this.gridColumn105.Caption = "距离";
            this.gridColumn105.DisplayFormat.FormatString = "F2";
            this.gridColumn105.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn105.FieldName = "Distance";
            this.gridColumn105.Name = "gridColumn105";
            this.gridColumn105.Visible = true;
            this.gridColumn105.VisibleIndex = 6;
            // 
            // tabWeakQuality
            // 
            this.tabWeakQuality.Controls.Add(this.gcWeakQuality);
            this.tabWeakQuality.Location = new System.Drawing.Point(4, 23);
            this.tabWeakQuality.Name = "tabWeakQuality";
            this.tabWeakQuality.Size = new System.Drawing.Size(1014, 478);
            this.tabWeakQuality.TabIndex = 5;
            this.tabWeakQuality.Text = "质差小区";
            this.tabWeakQuality.UseVisualStyleBackColor = true;
            // 
            // gcWeakQuality
            // 
            this.gcWeakQuality.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcWeakQuality.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcWeakQuality.Location = new System.Drawing.Point(0, 0);
            this.gcWeakQuality.MainView = this.gvWeakQuality;
            this.gcWeakQuality.Name = "gcWeakQuality";
            this.gcWeakQuality.ShowOnlyPredefinedDetails = true;
            this.gcWeakQuality.Size = new System.Drawing.Size(1014, 478);
            this.gcWeakQuality.TabIndex = 5;
            this.gcWeakQuality.UseEmbeddedNavigator = true;
            this.gcWeakQuality.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvWeakQuality,
            this.gridView7});
            // 
            // gvWeakQuality
            // 
            this.gvWeakQuality.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn107,
            this.gridColumn108,
            this.gridColumn109,
            this.gridColumn110,
            this.gridColumn111,
            this.gridColumn112,
            this.gridColumn113,
            this.gridColumn114,
            this.gridColumn115,
            this.gridColumn116,
            this.gridColumn118,
            this.gridColumn119,
            this.gridColumn120,
            this.gridColumn121,
            this.gridColumn122});
            this.gvWeakQuality.GridControl = this.gcWeakQuality;
            this.gvWeakQuality.Name = "gvWeakQuality";
            this.gvWeakQuality.OptionsBehavior.Editable = false;
            this.gvWeakQuality.OptionsDetail.ShowDetailTabs = false;
            this.gvWeakQuality.OptionsView.ShowGroupPanel = false;
            this.gvWeakQuality.OptionsView.ShowIndicator = false;
            // 
            // gridColumn107
            // 
            this.gridColumn107.Caption = "基站名";
            this.gridColumn107.FieldName = "BtsName";
            this.gridColumn107.Name = "gridColumn107";
            this.gridColumn107.Visible = true;
            this.gridColumn107.VisibleIndex = 0;
            // 
            // gridColumn108
            // 
            this.gridColumn108.Caption = "eNodeBID";
            this.gridColumn108.FieldName = "ENodeBID";
            this.gridColumn108.Name = "gridColumn108";
            this.gridColumn108.Visible = true;
            this.gridColumn108.VisibleIndex = 1;
            // 
            // gridColumn109
            // 
            this.gridColumn109.Caption = "经度";
            this.gridColumn109.FieldName = "Longitude";
            this.gridColumn109.Name = "gridColumn109";
            this.gridColumn109.Visible = true;
            this.gridColumn109.VisibleIndex = 2;
            // 
            // gridColumn110
            // 
            this.gridColumn110.Caption = "纬度";
            this.gridColumn110.FieldName = "Latitude";
            this.gridColumn110.Name = "gridColumn110";
            this.gridColumn110.Visible = true;
            this.gridColumn110.VisibleIndex = 3;
            // 
            // gridColumn111
            // 
            this.gridColumn111.Caption = "小区名";
            this.gridColumn111.FieldName = "CellName";
            this.gridColumn111.Name = "gridColumn111";
            this.gridColumn111.Visible = true;
            this.gridColumn111.VisibleIndex = 4;
            // 
            // gridColumn112
            // 
            this.gridColumn112.Caption = "总采样点";
            this.gridColumn112.FieldName = "SampleCount";
            this.gridColumn112.Name = "gridColumn112";
            this.gridColumn112.Visible = true;
            this.gridColumn112.VisibleIndex = 5;
            // 
            // gridColumn113
            // 
            this.gridColumn113.Caption = "质差采样点";
            this.gridColumn113.FieldName = "WeakQualSampleCount";
            this.gridColumn113.Name = "gridColumn113";
            this.gridColumn113.Visible = true;
            this.gridColumn113.VisibleIndex = 6;
            // 
            // gridColumn114
            // 
            this.gridColumn114.Caption = "质差占比";
            this.gridColumn114.DisplayFormat.FormatString = "P2";
            this.gridColumn114.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn114.FieldName = "WeakQualSampleRate";
            this.gridColumn114.Name = "gridColumn114";
            this.gridColumn114.Visible = true;
            this.gridColumn114.VisibleIndex = 7;
            // 
            // gridColumn115
            // 
            this.gridColumn115.Caption = "CellID";
            this.gridColumn115.FieldName = "CellID";
            this.gridColumn115.Name = "gridColumn115";
            this.gridColumn115.Visible = true;
            this.gridColumn115.VisibleIndex = 8;
            // 
            // gridColumn116
            // 
            this.gridColumn116.Caption = "TAC";
            this.gridColumn116.FieldName = "Tac";
            this.gridColumn116.Name = "gridColumn116";
            this.gridColumn116.Visible = true;
            this.gridColumn116.VisibleIndex = 9;
            // 
            // gridColumn118
            // 
            this.gridColumn118.Caption = "EARFCN";
            this.gridColumn118.FieldName = "Earfcn";
            this.gridColumn118.Name = "gridColumn118";
            this.gridColumn118.Visible = true;
            this.gridColumn118.VisibleIndex = 10;
            // 
            // gridColumn119
            // 
            this.gridColumn119.Caption = "PCI";
            this.gridColumn119.FieldName = "Pci";
            this.gridColumn119.Name = "gridColumn119";
            this.gridColumn119.Visible = true;
            this.gridColumn119.VisibleIndex = 11;
            // 
            // gridColumn120
            // 
            this.gridColumn120.Caption = "Direction";
            this.gridColumn120.FieldName = "Direction";
            this.gridColumn120.Name = "gridColumn120";
            this.gridColumn120.Visible = true;
            this.gridColumn120.VisibleIndex = 12;
            // 
            // gridColumn121
            // 
            this.gridColumn121.Caption = "Downward";
            this.gridColumn121.FieldName = "Downward";
            this.gridColumn121.Name = "gridColumn121";
            this.gridColumn121.Visible = true;
            this.gridColumn121.VisibleIndex = 13;
            // 
            // gridColumn122
            // 
            this.gridColumn122.Caption = "Altitude";
            this.gridColumn122.FieldName = "Altitude";
            this.gridColumn122.Name = "gridColumn122";
            this.gridColumn122.Visible = true;
            this.gridColumn122.VisibleIndex = 14;
            // 
            // gridView7
            // 
            this.gridView7.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn123,
            this.gridColumn124,
            this.gridColumn125,
            this.gridColumn126,
            this.gridColumn127,
            this.gridColumn128,
            this.gridColumn129});
            this.gridView7.GridControl = this.gcWeakQuality;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.Editable = false;
            this.gridView7.OptionsDetail.ShowDetailTabs = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            this.gridView7.OptionsView.ShowIndicator = false;
            // 
            // gridColumn123
            // 
            this.gridColumn123.Caption = "小区名";
            this.gridColumn123.FieldName = "CellName";
            this.gridColumn123.Name = "gridColumn123";
            this.gridColumn123.Visible = true;
            this.gridColumn123.VisibleIndex = 0;
            // 
            // gridColumn124
            // 
            this.gridColumn124.Caption = "小区ID";
            this.gridColumn124.FieldName = "CellID";
            this.gridColumn124.Name = "gridColumn124";
            this.gridColumn124.Visible = true;
            this.gridColumn124.VisibleIndex = 1;
            // 
            // gridColumn125
            // 
            this.gridColumn125.Caption = "EARFCN";
            this.gridColumn125.FieldName = "EARFCN";
            this.gridColumn125.Name = "gridColumn125";
            this.gridColumn125.Visible = true;
            this.gridColumn125.VisibleIndex = 2;
            // 
            // gridColumn126
            // 
            this.gridColumn126.Caption = "PCI";
            this.gridColumn126.FieldName = "PCI";
            this.gridColumn126.Name = "gridColumn126";
            this.gridColumn126.Visible = true;
            this.gridColumn126.VisibleIndex = 3;
            // 
            // gridColumn127
            // 
            this.gridColumn127.Caption = "经度";
            this.gridColumn127.FieldName = "Longitude";
            this.gridColumn127.Name = "gridColumn127";
            this.gridColumn127.Visible = true;
            this.gridColumn127.VisibleIndex = 4;
            // 
            // gridColumn128
            // 
            this.gridColumn128.Caption = "纬度";
            this.gridColumn128.FieldName = "Latitude";
            this.gridColumn128.Name = "gridColumn128";
            this.gridColumn128.Visible = true;
            this.gridColumn128.VisibleIndex = 5;
            // 
            // gridColumn129
            // 
            this.gridColumn129.Caption = "距离";
            this.gridColumn129.DisplayFormat.FormatString = "F2";
            this.gridColumn129.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn129.FieldName = "Distance";
            this.gridColumn129.Name = "gridColumn129";
            this.gridColumn129.Visible = true;
            this.gridColumn129.VisibleIndex = 6;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportCurXls,
            this.miExportAllXls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(154, 48);
            // 
            // miExportCurXls
            // 
            this.miExportCurXls.Name = "miExportCurXls";
            this.miExportCurXls.Size = new System.Drawing.Size(153, 22);
            this.miExportCurXls.Text = "导出当前Excel";
            // 
            // miExportAllXls
            // 
            this.miExportAllXls.Name = "miExportAllXls";
            this.miExportAllXls.Size = new System.Drawing.Size(153, 22);
            this.miExportAllXls.Text = "导出全部Excel";
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "理想覆盖半径(米)";
            this.gridColumn19.DisplayFormat.FormatString = "F2";
            this.gridColumn19.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn19.FieldName = "OverCoverCellRadius";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 5;
            // 
            // LtePlanningCellResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1022, 505);
            this.ContextMenuStrip = this.contextMenuStrip1;
            this.Controls.Add(this.tabControl);
            this.Name = "LtePlanningCellResultForm";
            this.Text = "LTE规划小区类检查结果";
            this.tabControl.ResumeLayout(false);
            this.tabSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.tabNotOpen.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcNotOpened)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvNotOpened)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.tabSampleLess.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcSampleLess)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSampleLess)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.tabOverCoverage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcOverCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvOverCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            this.tabWeakCoverage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcWeakCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakCoverage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.tabWeakQuality.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcWeakQuality)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakQuality)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabSampleLess;
        private System.Windows.Forms.TabPage tabNotOpen;
        private DevExpress.XtraGrid.GridControl gcSampleLess;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSampleLess;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.GridControl gcNotOpened;
        private DevExpress.XtraGrid.Views.Grid.GridView gvNotOpened;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private System.Windows.Forms.TabPage tabSummary;
        private DevExpress.XtraGrid.GridControl gcSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSummary;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private System.Windows.Forms.TabPage tabOverCoverage;
        private DevExpress.XtraGrid.GridControl gcOverCoverage;
        private DevExpress.XtraGrid.Views.Grid.GridView gvOverCoverage;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private System.Windows.Forms.TabPage tabWeakCoverage;
        private System.Windows.Forms.TabPage tabWeakQuality;
        private DevExpress.XtraGrid.GridControl gcWeakCoverage;
        private DevExpress.XtraGrid.Views.Grid.GridView gvWeakCoverage;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn90;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn91;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn92;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn94;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn95;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn96;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn97;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn98;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn99;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn100;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn101;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn102;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn103;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn104;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn105;
        private DevExpress.XtraGrid.GridControl gcWeakQuality;
        private DevExpress.XtraGrid.Views.Grid.GridView gvWeakQuality;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn107;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn108;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn109;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn110;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn111;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn112;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn113;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn114;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn115;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn116;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn118;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn119;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn120;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn121;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn122;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn123;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn124;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn125;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn126;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn127;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn128;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn129;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportCurXls;
        private System.Windows.Forms.ToolStripMenuItem miExportAllXls;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
    }
}