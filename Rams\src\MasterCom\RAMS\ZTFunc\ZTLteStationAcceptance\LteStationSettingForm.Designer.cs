﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteStationSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.radioByDT = new System.Windows.Forms.RadioButton();
            this.radioByFile = new System.Windows.Forms.RadioButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.btnFolder = new System.Windows.Forms.Button();
            this.txtFolder = new System.Windows.Forms.TextBox();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label18 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.txtCSFBSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.txtSwitchSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label19 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.txtSwitchCount = new System.Windows.Forms.NumericUpDown();
            this.label17 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.txtFTPUpSpeed = new System.Windows.Forms.NumericUpDown();
            this.label15 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.txtFTPDownSpeed = new System.Windows.Forms.NumericUpDown();
            this.label13 = new System.Windows.Forms.Label();
            this.txtVolteSuccessRate = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.txtVolteTestCount = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.txtCSFBTestCount = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.txtAccessTestCount = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.txtAccessRate = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBTestCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessRate)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radioByDT);
            this.groupBox1.Controls.Add(this.radioByFile);
            this.groupBox1.Location = new System.Drawing.Point(17, 19);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox1.Size = new System.Drawing.Size(862, 91);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "统计方式";
            // 
            // radioByDT
            // 
            this.radioByDT.AutoSize = true;
            this.radioByDT.Location = new System.Drawing.Point(369, 33);
            this.radioByDT.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.radioByDT.Name = "radioByDT";
            this.radioByDT.Size = new System.Drawing.Size(213, 22);
            this.radioByDT.TabIndex = 1;
            this.radioByDT.TabStop = true;
            this.radioByDT.Text = "使用DT上传下载的文件";
            this.radioByDT.UseVisualStyleBackColor = true;
            // 
            // radioByFile
            // 
            this.radioByFile.AutoSize = true;
            this.radioByFile.Checked = true;
            this.radioByFile.Location = new System.Drawing.Point(73, 33);
            this.radioByFile.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.radioByFile.Name = "radioByFile";
            this.radioByFile.Size = new System.Drawing.Size(213, 22);
            this.radioByFile.TabIndex = 0;
            this.radioByFile.TabStop = true;
            this.radioByFile.Text = "使用极好点分类的文件";
            this.radioByFile.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.btnFolder);
            this.groupBox2.Controls.Add(this.txtFolder);
            this.groupBox2.Location = new System.Drawing.Point(17, 119);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox2.Size = new System.Drawing.Size(862, 112);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "导出目录";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Red;
            this.label1.Location = new System.Drawing.Point(91, 0);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(161, 18);
            this.label1.TabIndex = 2;
            this.label1.Text = "同名文件将被覆盖!";
            // 
            // btnFolder
            // 
            this.btnFolder.Location = new System.Drawing.Point(523, 38);
            this.btnFolder.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnFolder.Name = "btnFolder";
            this.btnFolder.Size = new System.Drawing.Size(107, 36);
            this.btnFolder.TabIndex = 1;
            this.btnFolder.Text = "选择";
            this.btnFolder.UseVisualStyleBackColor = true;
            // 
            // txtFolder
            // 
            this.txtFolder.Location = new System.Drawing.Point(16, 41);
            this.txtFolder.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFolder.Name = "txtFolder";
            this.txtFolder.ReadOnly = true;
            this.txtFolder.Size = new System.Drawing.Size(497, 28);
            this.txtFolder.TabIndex = 0;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(761, 607);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(107, 36);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(615, 607);
            this.btnOK.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(107, 36);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.label20);
            this.groupBox3.Controls.Add(this.txtCSFBSuccessRate);
            this.groupBox3.Controls.Add(this.label21);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.txtSwitchSuccessRate);
            this.groupBox3.Controls.Add(this.label19);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.txtSwitchCount);
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.txtFTPUpSpeed);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.txtFTPDownSpeed);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.txtVolteSuccessRate);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.txtVolteTestCount);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.txtCSFBTestCount);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.txtAccessTestCount);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.txtAccessRate);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Location = new System.Drawing.Point(17, 236);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupBox3.Size = new System.Drawing.Size(862, 360);
            this.groupBox3.TabIndex = 4;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "4G宏站单验门限设置";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(810, 311);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 18);
            this.label18.TabIndex = 156;
            this.label18.Text = "%";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(809, 114);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 18);
            this.label20.TabIndex = 155;
            this.label20.Text = "%";
            // 
            // txtCSFBSuccessRate
            // 
            this.txtCSFBSuccessRate.Location = new System.Drawing.Point(688, 109);
            this.txtCSFBSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCSFBSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtCSFBSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtCSFBSuccessRate.Name = "txtCSFBSuccessRate";
            this.txtCSFBSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtCSFBSuccessRate.TabIndex = 154;
            this.txtCSFBSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtCSFBSuccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(555, 116);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(116, 18);
            this.label21.TabIndex = 153;
            this.label21.Text = "CSFB成功率≥";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(808, 182);
            this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 18);
            this.label9.TabIndex = 152;
            this.label9.Text = "%";
            // 
            // txtSwitchSuccessRate
            // 
            this.txtSwitchSuccessRate.Location = new System.Drawing.Point(688, 307);
            this.txtSwitchSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSwitchSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtSwitchSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtSwitchSuccessRate.Name = "txtSwitchSuccessRate";
            this.txtSwitchSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtSwitchSuccessRate.TabIndex = 150;
            this.txtSwitchSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSwitchSuccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(555, 311);
            this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(116, 18);
            this.label19.TabIndex = 149;
            this.label19.Text = "切换成功率≥";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(352, 311);
            this.label16.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(26, 18);
            this.label16.TabIndex = 148;
            this.label16.Text = "次";
            // 
            // txtSwitchCount
            // 
            this.txtSwitchCount.Location = new System.Drawing.Point(228, 307);
            this.txtSwitchCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSwitchCount.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtSwitchCount.Name = "txtSwitchCount";
            this.txtSwitchCount.Size = new System.Drawing.Size(114, 28);
            this.txtSwitchCount.TabIndex = 147;
            this.txtSwitchCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSwitchCount.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(121, 314);
            this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(98, 18);
            this.label17.TabIndex = 146;
            this.label17.Text = "切换次数≥";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(812, 252);
            this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 18);
            this.label14.TabIndex = 145;
            this.label14.Text = "M";
            // 
            // txtFTPUpSpeed
            // 
            this.txtFTPUpSpeed.Location = new System.Drawing.Point(688, 248);
            this.txtFTPUpSpeed.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPUpSpeed.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPUpSpeed.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPUpSpeed.Name = "txtFTPUpSpeed";
            this.txtFTPUpSpeed.Size = new System.Drawing.Size(114, 28);
            this.txtFTPUpSpeed.TabIndex = 144;
            this.txtFTPUpSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPUpSpeed.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(555, 255);
            this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(125, 18);
            this.label15.TabIndex = 143;
            this.label15.Text = "FTP上传速率≥";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(350, 252);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(17, 18);
            this.label12.TabIndex = 142;
            this.label12.Text = "M";
            // 
            // txtFTPDownSpeed
            // 
            this.txtFTPDownSpeed.Location = new System.Drawing.Point(226, 248);
            this.txtFTPDownSpeed.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPDownSpeed.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPDownSpeed.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPDownSpeed.Name = "txtFTPDownSpeed";
            this.txtFTPDownSpeed.Size = new System.Drawing.Size(114, 28);
            this.txtFTPDownSpeed.TabIndex = 141;
            this.txtFTPDownSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPDownSpeed.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(94, 253);
            this.label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(125, 18);
            this.label13.TabIndex = 140;
            this.label13.Text = "FTP下载速率≥";
            // 
            // txtVolteSuccessRate
            // 
            this.txtVolteSuccessRate.Location = new System.Drawing.Point(687, 177);
            this.txtVolteSuccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteSuccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtVolteSuccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtVolteSuccessRate.Name = "txtVolteSuccessRate";
            this.txtVolteSuccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtVolteSuccessRate.TabIndex = 138;
            this.txtVolteSuccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteSuccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(554, 184);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(125, 18);
            this.label11.TabIndex = 137;
            this.label11.Text = "VOLTE成功率≥";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(353, 185);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(26, 18);
            this.label7.TabIndex = 136;
            this.label7.Text = "次";
            // 
            // txtVolteTestCount
            // 
            this.txtVolteTestCount.Location = new System.Drawing.Point(229, 181);
            this.txtVolteTestCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteTestCount.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtVolteTestCount.Name = "txtVolteTestCount";
            this.txtVolteTestCount.Size = new System.Drawing.Size(114, 28);
            this.txtVolteTestCount.TabIndex = 135;
            this.txtVolteTestCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteTestCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(79, 188);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(143, 18);
            this.label8.TabIndex = 134;
            this.label8.Text = "VOLTE测试次数≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(350, 107);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(26, 18);
            this.label5.TabIndex = 133;
            this.label5.Text = "次";
            // 
            // txtCSFBTestCount
            // 
            this.txtCSFBTestCount.Location = new System.Drawing.Point(226, 103);
            this.txtCSFBTestCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtCSFBTestCount.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtCSFBTestCount.Name = "txtCSFBTestCount";
            this.txtCSFBTestCount.Size = new System.Drawing.Size(114, 28);
            this.txtCSFBTestCount.TabIndex = 132;
            this.txtCSFBTestCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtCSFBTestCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(85, 109);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(134, 18);
            this.label6.TabIndex = 131;
            this.label6.Text = "CSFB测试次数≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(812, 48);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(26, 18);
            this.label4.TabIndex = 130;
            this.label4.Text = "次";
            // 
            // txtAccessTestCount
            // 
            this.txtAccessTestCount.Location = new System.Drawing.Point(688, 42);
            this.txtAccessTestCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessTestCount.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAccessTestCount.Name = "txtAccessTestCount";
            this.txtAccessTestCount.Size = new System.Drawing.Size(114, 28);
            this.txtAccessTestCount.TabIndex = 129;
            this.txtAccessTestCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessTestCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(545, 52);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(134, 18);
            this.label2.TabIndex = 128;
            this.label2.Text = "接入测试次数≥";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(352, 52);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 18);
            this.label3.TabIndex = 127;
            this.label3.Text = "%";
            // 
            // txtAccessRate
            // 
            this.txtAccessRate.Location = new System.Drawing.Point(230, 46);
            this.txtAccessRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessRate.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtAccessRate.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtAccessRate.Name = "txtAccessRate";
            this.txtAccessRate.Size = new System.Drawing.Size(114, 28);
            this.txtAccessRate.TabIndex = 126;
            this.txtAccessRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(25, 52);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(197, 18);
            this.label10.TabIndex = 125;
            this.label10.Text = "Access Success Rate≥";
            // 
            // LteStationSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 22F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(892, 659);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Margin = new System.Windows.Forms.Padding(10, 14, 10, 14);
            this.Name = "LteStationSettingForm";
            this.Text = " ";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSwitchCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCSFBTestCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessRate)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton radioByDT;
        private System.Windows.Forms.RadioButton radioByFile;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button btnFolder;
        private System.Windows.Forms.TextBox txtFolder;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown txtAccessRate;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown txtAccessTestCount;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown txtFTPDownSpeed;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown txtVolteSuccessRate;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown txtVolteTestCount;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown txtCSFBTestCount;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown txtSwitchSuccessRate;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown txtSwitchCount;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.NumericUpDown txtFTPUpSpeed;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.NumericUpDown txtCSFBSuccessRate;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label18;
    }
}