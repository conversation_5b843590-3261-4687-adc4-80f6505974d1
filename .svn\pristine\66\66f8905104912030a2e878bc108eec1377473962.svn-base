﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsHighCoverageStater : LteMgrsStaterBase
    {
        public LteMgrsHighCoverageStater()
        {
        }

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsHighCoverageResult resultControl = new LteMgrsHighCoverageResult();
            resultControl.FillData(tmpFuncItem);
            return new List<LteMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }

        public List<LteMgrsHighCoverageView> GetViews(LteMgrsCity city)
        {
            List<LteMgrsHighCoverageView> retList = new List<LteMgrsHighCoverageView>();
            LteMgrsHighCoverageCondition cond = this.tmpFuncItem.FuncCondtion as LteMgrsHighCoverageCondition;
            
            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                // find high coverage grids
                List<LteMgrsGrid> gridList = new List<LteMgrsGrid>(region.GridDic.Values);
                gridList.Sort((a, b) => { return a.MgrsString.CompareTo(b.MgrsString); });
                Dictionary<LteMgrsGrid, List<LteMgrsCellData>> gridCoverageDic = GetGridCoverageDic(gridList, cond);

                // find block
                LteMgrsBlockFinder<LteMgrsGrid> blockFinder;
                if (!cond.EnableGridNumDist)
                    blockFinder = new LteMgrsBlockFinder<LteMgrsGrid>(LteMgrsBaseSettingManager.Instance.GridSize, cond.GridCount);
                else
                    blockFinder = new LteMgrsBlockFinder<LteMgrsGrid>(LteMgrsBaseSettingManager.Instance.GridSize, cond.GridCount, cond.GridNumDist);

                List<LteMgrsGrid[]> blocks = blockFinder.FindBlocks(new List<LteMgrsGrid>(gridCoverageDic.Keys));

                // result view
                addResult(city, retList, region, gridCoverageDic, blocks);
            }
            return retList;
        }

        private void addResult(LteMgrsCity city, List<LteMgrsHighCoverageView> retList, LteMgrsRegion region, Dictionary<LteMgrsGrid, List<LteMgrsCellData>> gridCoverageDic, List<LteMgrsGrid[]> blocks)
        {
            foreach (LteMgrsGrid[] grids in blocks)
            {
                float[] values = new float[grids.Length];

                for (int i = 0; i < grids.Length; ++i)
                {
                    values[i] = gridCoverageDic[grids[i]].Count;
                }
                LteMgrsHighCoverageView view = new LteMgrsHighCoverageView(city.CityName, region.RegionName, grids, values);

                for (int j = 0; j < grids.Length; ++j)
                {
                    List<LteMgrsCell> hCells = new List<LteMgrsCell>();
                    foreach (LteMgrsCellData cd in gridCoverageDic[grids[j]])
                    {
                        hCells.Add(cd.lteCell);
                    }
                    view.GridViews[j].Cells = hCells;
                }

                view.SN = retList.Count;
                retList.Add(view);
            }
        }

        public List<LteMgrsDrawItem> GetDrawList(List<LteMgrsHighCoverageView> viewList)
        {
            List<LteMgrsDrawItem> retList = new List<LteMgrsDrawItem>();
            foreach (LteMgrsHighCoverageView curView in viewList)
            {
                LteMgrsGrid[] grids = curView.Grids;
                float[] values = curView.Rsrps;
                for (int i = 0; i < curView.GridViews.Count; i++)
                {
                    LteMgrsGrid grid = grids[i];
                    float coverage = values[i];
                    LteMgrsDrawItem item = new LteMgrsDrawItem(new DbPoint(grid.TLLng, grid.BRLat), new DbPoint(grid.BRLng, grid.TLLat));
                    item.FillColor = Ranges.GetColor(coverage);
                    item.ToolInfoDetail = grid.DetailInfo;
                    item.ToolInfoTitle = grid.MgrsString;
                    item.GridCellInfo = curView.GridViews[i];
                    retList.Add(item);
                }
            }
            return retList;
        }

        public LteMgrsLegendGroup GetLegend()
        {
            return Ranges.GetLegend();
        }

        private Dictionary<LteMgrsGrid, List<LteMgrsCellData>> GetGridCoverageDic(List<LteMgrsGrid> gridList, LteMgrsHighCoverageCondition cond)
        {
            Dictionary<LteMgrsGrid, List<LteMgrsCellData>> retDic = new Dictionary<LteMgrsGrid, List<LteMgrsCellData>>();
            foreach (LteMgrsGrid grid in gridList)
            {
                List<LteMgrsCellData> tmpList = new List<LteMgrsCellData>();
                float value = LteMgrsGridHelper.GetRelativeCoverageJT(grid
                    , this.tmpFuncItem.FuncCondtion as LteMgrsHighCoverageCondition
                    , ref tmpList);
                if (value == 0 || value < cond.Coverage)
                {
                    continue;
                }
                for (int i = 0; i < tmpList.Count; i++)
                {
                    if (tmpList[i].FilteredByMultiBand)
                    {
                        tmpList.RemoveAt(i);
                        i--;
                    }
                }
                retDic.Add(grid, tmpList);
            }
            return retDic;
        }

        private LteMgrsFuncItem tmpFuncItem;

        public static LteMgrsColorRange Ranges { get; set; } = LteMgrsCoverageRangeStater.Ranges;
    }

    public class LteMgrsHighCoverageView : LteMgrsWeakRsrpView
    {
        public LteMgrsHighCoverageView(string cityName, string regionName, LteMgrsGrid[] grids, float[] values)
            : base(cityName, regionName, grids, values)
        {
        }
    }
}
