﻿namespace MasterCom.RAMS.CQT
{
    partial class NoMainCellSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.numSampleCountLimit = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numCellCountLimit = new System.Windows.Forms.NumericUpDown();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.numRxLevMax = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label13 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label12 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(108, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 10;
            this.label1.Text = "≤信号强度≤";
            // 
            // numRxlevMin
            // 
            this.numRxlevMin.Location = new System.Drawing.Point(27, 20);
            this.numRxlevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Name = "numRxlevMin";
            this.numRxlevMin.Size = new System.Drawing.Size(75, 21);
            this.numRxlevMin.TabIndex = 3;
            this.numRxlevMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxlevMin.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(246, 236);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(165, 236);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(25, 57);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 12);
            this.label4.TabIndex = 14;
            this.label4.Text = "无主导采样点数≥";
            // 
            // numSampleCountLimit
            // 
            this.numSampleCountLimit.Location = new System.Drawing.Point(132, 52);
            this.numSampleCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCountLimit.Name = "numSampleCountLimit";
            this.numSampleCountLimit.Size = new System.Drawing.Size(75, 21);
            this.numSampleCountLimit.TabIndex = 7;
            this.numSampleCountLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCountLimit.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(55, 64);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(77, 12);
            this.label7.TabIndex = 16;
            this.label7.Text = "包含小区数≥";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(25, 31);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(107, 12);
            this.label8.TabIndex = 17;
            this.label8.Text = "最大最小Rxlev差≤";
            // 
            // numCellCountLimit
            // 
            this.numCellCountLimit.Location = new System.Drawing.Point(137, 60);
            this.numCellCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCellCountLimit.Name = "numCellCountLimit";
            this.numCellCountLimit.Size = new System.Drawing.Size(75, 21);
            this.numCellCountLimit.TabIndex = 4;
            this.numCellCountLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCellCountLimit.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Location = new System.Drawing.Point(137, 26);
            this.numRxLevDValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(75, 21);
            this.numRxLevDValue.TabIndex = 5;
            this.numRxLevDValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevDValue.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // numRxLevMax
            // 
            this.numRxLevMax.Location = new System.Drawing.Point(191, 20);
            this.numRxLevMax.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Name = "numRxLevMax";
            this.numRxLevMax.Size = new System.Drawing.Size(75, 21);
            this.numRxLevMax.TabIndex = 18;
            this.numRxLevMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevMax.Value = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(217, 64);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(17, 12);
            this.label11.TabIndex = 22;
            this.label11.Text = "个";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.numRxlevMin);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRxLevMax);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.numSampleCountLimit);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(309, 91);
            this.groupBox1.TabIndex = 23;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "采样点条件设置";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(216, 57);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 24;
            this.label13.Text = "个";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.numCellCountLimit);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.numRxLevDValue);
            this.groupBox2.Location = new System.Drawing.Point(12, 118);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(309, 98);
            this.groupBox2.TabIndex = 24;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "无主导条件设置";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(217, 31);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(17, 12);
            this.label12.TabIndex = 25;
            this.label12.Text = "dB";
            // 
            // NoMainCellSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(334, 276);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "NoMainCellSetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "无主导小区分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numRxlevMin;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numSampleCountLimit;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numCellCountLimit;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.NumericUpDown numRxLevMax;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label12;
    }
}