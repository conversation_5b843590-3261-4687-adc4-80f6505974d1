﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public class GisPos
    {
        private double wgLat;
        private double wgLon;

        public override String ToString()
        {
            return wgLat + "," + wgLon;
        }

        public GisPos(double wgLon, double wgLat)
        {
            setWgLat(wgLat);
            setWgLon(wgLon);
        }

        public double getWgLat()
        {
            return wgLat;
        }

        public void setWgLat(double wgLat)
        {
            this.wgLat = wgLat;
        }

        public double getWgLon()
        {
            return wgLon;
        }

        public void setWgLon(double wgLon)
        {
            this.wgLon = wgLon;
        }
    }
}
