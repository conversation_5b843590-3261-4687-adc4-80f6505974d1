<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStrip2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnShowChart.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABESURBVDhPY2AY8eA/tUOANgYCTf0Pw5S6GOxCwgbC7SPo
        o6Fq4H9gKMAgOFAp9fKogQMfhpTmDmT9oIRNDqamG4aaWQA9kLBQdV/DPQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="cascadeToolStripButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEuSURBVDhP7ZJPSwJRFEfnU0VkhEYkpalJ6OQ2WriJFm0M
        3ATRRkuhhX8W7UcJ+ggt2kVtMpAIRcnCHGgsagbfvNMEZW5UENp1eTze4t1zL4efcnxyQaqokcicEUuc
        E9w+xbuhMafmcak5ZmMFXIFdlGF1lNfYL/fYzOpE9hoEkzUWd+5wb1WZjt8wFa/i8UWHAw6LJaSU4Jyf
        +npazvViCQxbjgak8qV+s/MX03IaXwUtXXD/ZFPTGQ1IF7T+5Il8ZHLl/vYT+UgXyt8b2EzkI3mQJRQK
        s7q2zr8PRfkzH1YPuu+SjiFodAS3TYvK45iEDubjzZToXWg+C6otwVVdcN0Ym9DffHzYoJtQNySVts3l
        gwNojwEM+lgJqyyFVLwBlQV/FPdyhHm/yozHxycTozvcdqQV5wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="tileVerticalToolStripButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHDSURBVDhPrZPvS1NRHIf9w2yvtHobadba24RKp0IMX0gR
        SK9ChqBQrTkQ3fyZLsVdibk3pqwlFSut9Dq13GBjO8PY0Ovd09kVmve4d3bg8+q5n+d7Dufchob/sbwh
        wUBQ8Hgkj3s4hetZCodHp7E7icpu96dofLRN29M4/2ZXPzoqg38FBhZN+kYEzsGcJXgdLtpYr1/Q8jxz
        UXCpHQQin1DX70wWxy03U7GvF9iHz5u03ffUjqAKjFOIJ+sLysewEFMFcxu1KRU4OTFZ/6jjaO1kSkva
        WKlkEF5OKDt4EwNZrMiYJpT/mLxLSEFLhxSs2VixaBDSFIE/FLOKhrwJIZM9Mlle1bkiBRPhNRtLFwzG
        3yoCbzBqFQslyBcgkzWZXZHv4OZDfPPvbWw/beCbVQWBOQqymJNJ5Sv8/GUwHa0KHuCb1Gxsc++YlzN1
        BDlZ3E2b6IcGWzuCQORM8GpC4zz78kPI11lHsFMtHggS24L171KwdCZ4EdI4z1a/SUGwjiAS162p1WJg
        XmdwdMMSDI0torL+4aj9Gn2TS1x39XDN1V3L3S5u3OvF65/mqtNtpdnZSfOdDppk2j1Pai/xMn/0X/kY
        c4QGrjx+AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="tileHorizontalToolStripButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEySURBVDhP7ZPPK4NxHMf3Tzm4yElxcJQzuUiKw1a0JGtk
        JTU/WhImFCkHDhwdHEgRGoa17dmPZ8bY9n2ePc/z3V5s7LDmIi7Kp97Hz+v1+fbua7P9xng2U1TjXk8x
        sqJiX0jSN5OgaypO53iM9lGFlmGFJnuUxsEINd7ycgnwH8LcvmR6z8KzY+LaMnBuFHD4NQYWBb0+Qc9s
        7mtAGeJaUxlaUumf/zB3uGO0OaM0f1rL5mpqLvAuH1QusCToRpG8JsnkJKmMifJoEEro3ER0Lh4Ep7d5
        Glq7a58w6dtFFksYZhGhS17zkvSLRTxtEE4WCCoaVyHBWVBwHMjVA8a825hWCe3dnhWSp6xF8tkkohrc
        xXQCYY3ze8HJdY6jyy8AjonVCvU7qWvhv8Y/X+NPfvQb5HYtfyxLn8gAAAAASUVORK5CYII=
</value>
  </data>
  <data name="StatReportDockForm.Appearance.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP///yZLgqGwxqfH6leX4EN4ukyFyv39/aW0y1iW4FaU3VeT3Pr6+uj1
        +Jmvyuz2+pywye32+leS2Pz8/JGsytzw+tzw+5Ksydzv+gAAAFeR2FeQ1tnr/Nnv/GeNvXup3gAAAFiR
        1liQ1I2qyo6qytXr+9Xs+wAAAFiP1FiP09Dq+4upyoypytLq+3mo3nio3liO0wAAAAAAAAAAAAAAAFeM
        0FuJwGCNwpK34szm+wAAAFiNz1GAvvX6/meUy8rl+1eMzvz+/2GQyFSFxU98t058twAAAKi20AAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwYLGFi4cMABhggK
        DDSQoKLFiwkWDGSAsSODgQ0cgBhJEoZJBw0GUqhwoSUGlxouOOAwsEOFkjBC6AxRYcRAEglOCB16AgWK
        BCoGrkgQo6lTpwlkDLyRYIfVq1cF9Bjo44fJr08FCCHoYwgRAWjToi1S0MgRI3Djwk2CsK5dgwEBADs=
</value>
  </data>
</root>