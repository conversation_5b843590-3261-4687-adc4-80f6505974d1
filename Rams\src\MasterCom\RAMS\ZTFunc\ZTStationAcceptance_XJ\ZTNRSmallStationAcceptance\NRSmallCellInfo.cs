﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRSmallCellInfo : CellInfoBase
    {
        //public NRIndoorCellServiceInfo SAInfo { get; private set; }
        //public NRIndoorCellServiceInfo NSAInfo { get; private set; }

        public NRSmallCellInfo(ICell cell)
            : base(cell)
        {

        }

        public void Init(ICell cell, NRServiceName serviceType)
        {
            //if (SAInfo == null && serviceType == NRServiceName.SA)
            //{
            //    SAInfo = new NRIndoorCellServiceInfo(cell as NRCell);
            //}
            //else if (NSAInfo == null && serviceType == NRServiceName.NSA)
            //{
            //    NSAInfo = new NRIndoorCellServiceInfo(cell as NRCell);
            //}
        }

        //public override void Calculate()
        //{
        //    SAInfo?.Calculate();
        //    NSAInfo?.Calculate();
        //}
    }
}
