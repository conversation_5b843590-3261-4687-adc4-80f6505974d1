﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCellSet;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellServiceConditionQuery : DIYAnalyseByFileBackgroundBase
    {
        public Dictionary<string, Dictionary<string, CellServiceCondition>> regionCellServiceConditionsDic { get; set; } = new Dictionary<string, Dictionary<string, CellServiceCondition>>();
        public List<string> regionNames { get; set; } = new List<string>();
        List<RegionCellServiceCondition> RegionCellServiceConditions = null;

        protected static readonly object lockObj = new object();
        private static ZTDIYCellServiceConditionQuery instance = null;
        public static ZTDIYCellServiceConditionQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYCellServiceConditionQuery();
                    }
                }
            }
            return instance;
        }

        protected ZTDIYCellServiceConditionQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("isampleid");
            Columns.Add("itime");
            Columns.Add("FileName");
            Columns.Add("ilongitude");
            Columns.Add("ilatitude");
            Columns.Add("LAC");
            Columns.Add("CI");
            Columns.Add("BCCH");
            Columns.Add("BSIC");
            Columns.Add("RxLevSub");
        }


        public override string Name
        {
            get
            {
                return "小区占用情况分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12011, this.Name);
        }

        protected int maxDelaySec = 30;

        protected override bool getCondition()
        {
            CellServiceConditionDlg dlg = new CellServiceConditionDlg();
            dlg.SetCondition(maxDelaySec);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out maxDelaySec);
            regionCellServiceConditionsDic = new Dictionary<string, Dictionary<string, CellServiceCondition>>();

            RegionCellServiceConditions = new List<RegionCellServiceCondition>();
            return RegionCellServiceConditions != null;
        }
        protected override void getReadyBeforeQuery()
        {
            regionCellServiceConditionsDic.Clear();
            regionNames.Clear();
        }
        protected override void fireShowForm()
        {
            WaitBox.Show("获取B、C类小区...", getCellServiceCondition);
            if (regionCellServiceConditionsDic == null || regionCellServiceConditionsDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            CellServiceConditionForm frm = MainModel.GetObjectFromBlackboard(typeof(CellServiceConditionForm)) as CellServiceConditionForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new CellServiceConditionForm(MainModel);
            }
            frm.FillData(RegionCellServiceConditions);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            regionCellServiceConditionsDic = null;
        } 

        /// <summary>
        /// 数据处理
        /// </summary>
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                ICell lastCell = null;
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (isValidTestPoint(tp))
                    {
                        ICell cell = null;
                        if (tp is LTETestPointDetail)
                        {
                            lastCell = new LTECell();
                            cell = tp.GetMainCell_LTE();
                        }
                        else if (tp is TestPointDetail)
                        {
                            lastCell = new Cell();
                            cell = tp.GetMainCell_GSM();
                        }
                        if (cell != null)
                        {
                            judgeCell(cell, lastCell, tp);
                            lastCell = cell;
                        }
                    }
                }
            }
         
        }

        protected virtual void judgeCell(ICell cell, ICell lastCell, TestPoint testPoint)
        {
            string regionName = getRegionName(testPoint.Longitude, testPoint.Latitude);

            if (regionCellServiceConditionsDic.ContainsKey(regionName))
            {
                if (regionCellServiceConditionsDic[regionName].ContainsKey(cell.Name))
                {
                    regionCellServiceConditionsDic[regionName][cell.Name].AddTestPoint(testPoint);
                }
                else
                {
                    CellServiceCondition cellServiceCondition = new CellServiceCondition(cell as Cell, testPoint);
                    regionCellServiceConditionsDic[regionName][cell.Name] = cellServiceCondition;
                }
            }
            else
            {
                CellServiceCondition cellServiceCondition = new CellServiceCondition(cell as Cell, testPoint);
                Dictionary<string, CellServiceCondition> cellDic = new Dictionary<string, CellServiceCondition>();
                cellDic[cell.Name] = cellServiceCondition;
                regionCellServiceConditionsDic[regionName] = cellDic;
                regionNames.Add(regionName);
            }
            if (cell!=lastCell)
            {
                regionCellServiceConditionsDic[regionName][cell.Name].serviceTimes++;
            }
        }

        public string getRegionName(double x, double y)
        {
            if ((MainModel.MultiGeometrys && Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count > 0)
                || (Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count == 1))
            {
                return getValidRegionName(x, y);
            }
            else if (Condition.Geometorys.SelectedResvRegions != null && Condition.Geometorys.SelectedResvRegions.Count == 1)
            {
                return getValidRegionName(x, y);
            }
            return "区域";
        }

        private string getValidRegionName(double x, double y)
        {
            foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
            {
                if (resvRegion.GeoOp.CheckPointInRegion(x, y))
                {
                    return resvRegion.RegionName;
                }
            }
            return "区域";
        }

        protected virtual void getCellServiceCondition()
        {
            try
            {
                List<Cell> cells = MainModel.CellManager.GetCurrentCells();
                int iLoop = 0;
                foreach (string regionName in regionNames)
                {
                    Dictionary<string, CellServiceCondition> cellServiceConditionsDic = regionCellServiceConditionsDic[regionName];
                    List<string> cellNames = new List<string>();
                    List<string> cellCodes = new List<string>();
                    addCellNames(cellServiceConditionsDic, cellNames, cellCodes);
                    foreach (Cell cell in cells)
                    {
                        iLoop++;
                        if (char.IsLetter(cell.Name, cell.Name.Length - 1))
                        {
                            continue;
                        }
                        if (cellServiceConditionsDic.ContainsKey(cell.Name))
                        {
                            continue;
                        }
                        addCellTestPoint(regionName, cellServiceConditionsDic, cellNames, cellCodes, cell);
                        WaitBox.ProgressPercent = (int)(100.8 * iLoop / cells.Count);
                    }
                }
                foreach (KeyValuePair<string, Dictionary<string, CellServiceCondition>> keyValue in regionCellServiceConditionsDic)
                {
                    List<CellServiceCondition> cellServiceCondtionList = new List<CellServiceCondition>();
                    foreach (KeyValuePair<string, CellServiceCondition> cellResult in keyValue.Value)
                    {
                        cellServiceCondtionList.Add(cellResult.Value);
                    }
                    RegionCellServiceCondition regionCellServiceCondition = new RegionCellServiceCondition(keyValue.Key, cellServiceCondtionList);
                    RegionCellServiceConditions.Add(regionCellServiceCondition);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private static void addCellNames(Dictionary<string, CellServiceCondition> cellServiceConditionsDic, List<string> cellNames, List<string> cellCodes)
        {
            foreach (string cellName in cellServiceConditionsDic.Keys)
            {
                if (cellServiceConditionsDic[cellName].category == 0)
                {
                    cellNames.Add(cellName);
                    cellCodes.Add(cellServiceConditionsDic[cellName].cell.Code);
                }
            }
        }

        private void addCellTestPoint(string regionName, Dictionary<string, CellServiceCondition> cellServiceConditionsDic, List<string> cellNames, List<string> cellCodes, Cell cell)
        {
            for (int i = 0; i < cellNames.Count; i++)
            {
                string cellName = cellNames[i];
                string cellCode = cellCodes[i];
                if (getComparePart(cell.Code).Equals(getComparePart(cellCode)))
                {
                    if (Math.Abs(cellServiceConditionsDic[cellName].cell.Direction - cell.Direction) <= maxDelaySec)
                    {
                        addTestPoint(cell, null, 1, regionName);
                    }
                    else
                    {
                        addTestPoint(cell, null, 2, regionName);
                    }
                }
            }
        }

        protected string getComparePart(string cellCode)
        {
            return cellCode.Substring(1, cellCode.Length - 2);
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is TestPointDetail || testPoint is LTETestPointDetail)
                {
                    if (condition.Geometorys != null && condition.Geometorys.Region != null 
                        && !condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
                    {
                        return false;
                    }
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }


        public virtual void addTestPoint(Cell cell, TestPoint testPoint, int category, string regionName)
        {
            CellServiceCondition cellServiceCondition = new CellServiceCondition(cell, testPoint);
            cellServiceCondition.category = category;
            regionCellServiceConditionsDic[regionName][cell.Name] = cellServiceCondition;
        }
    }
}
