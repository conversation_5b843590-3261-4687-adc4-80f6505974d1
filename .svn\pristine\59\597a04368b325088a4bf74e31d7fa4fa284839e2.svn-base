﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FastFadingDlg_GSM : BaseDialog
    {
        public FastFadingDlg_GSM()
        {
            InitializeComponent();
        }

        public void GetFilterCondition(out int rxLevMin, out int secondLast, out int secondFading, out int rxLevDValueFading)
        {
            rxLevMin = (int)numRxLevMin.Value;
            secondLast = (int)numSecondLast.Value;
            secondFading = (int)numSecondFading.Value;
            rxLevDValueFading = (int)numRxLevDValueFading.Value;
        }
    }
}
