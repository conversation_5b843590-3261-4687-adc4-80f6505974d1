﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Frame;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func.NebulaForm;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public partial class HandoverNebulaForm : ChildForm
    {
        public List<DataXY> Dataxy { get; set; }
        public Dictionary<int, List<JoinHandoverAnalysis>> HandoverAnalysisDic { get; set; }

        private bool IsTDAnalysis = false;
        public HandoverNebulaForm(NebulaDetailForm detailform,
            JoinHandoverAnalysisFrm handoverAnalysisFrm, bool IsTDAnalysis)
        {
            InitializeComponent();
            this.IsTDAnalysis = IsTDAnalysis;
            this.panel.BackColor = Color.White;
            this.detailform = detailform;
            this.handoverAnalysisFrm = handoverAnalysisFrm;

            Dataxy = new List<DataXY>();
            HandoverAnalysisDic = new Dictionary<int, List<JoinHandoverAnalysis>>();
        }
        NebulaDetailForm detailform;
        JoinHandoverAnalysisFrm handoverAnalysisFrm;

        public override void Init()
        {
            base.Init();
            MainModel.GetInstance().DistrictChanged += districtChanged;
            MainModel.GetInstance().DTDataChanged += dtDataChanged;
            MainModel.GetInstance().SelectedMessageChanged += selectedMessageChanged;
            Disposed += disposed;
            dtDataChanged(null, null);
        }
        private void disposed(object sender, EventArgs e)
        {
            MainModel.GetInstance().DistrictChanged -= districtChanged;
            MainModel.GetInstance().DTDataChanged -= dtDataChanged;
            MainModel.GetInstance().SelectedMessageChanged -= selectedMessageChanged;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["SystemName"] = XYData.Sys;
                param["Xtarget"] = XYData.TargetX;
                param["Ytarget"] = XYData.TargetY;
                param["Xsmall"] = XYData.Xsmall;
                param["Xlarge"] = XYData.Xlarge;
                param["XcomboboxIndex"] = XYData.Xcomboboxindex;
                param["Ysmall"] = XYData.Ysmall;
                param["Ylarge"] = XYData.Ylarge;
                param["YcomboboxIndex"] = XYData.Ycomboboxindex;
                param["Xcenter"] = XYData.Xcenter;
                param["Ycenter"] = XYData.Ycenter;
                param["Xcheckbox"] = XYData.Xcheckbox;
                param["Ycheckbox"] = XYData.Ycheckbox;
                param["Pointcheckbox"] = XYData.Pointcheckbox;
                param["FirstColorR"] = (int)XYData.ColorFirst.R;
                param["FirstColorG"] = (int)XYData.ColorFirst.G;
                param["FirstColorB"] = (int)XYData.ColorFirst.B;
                param["SecondColorR"] = (int)XYData.ColorSecond.R;
                param["SecondColorG"] = (int)XYData.ColorSecond.G;
                param["SecondColorB"] = (int)XYData.ColorSecond.B;
                param["ThirdColorR"] = (int)XYData.ColorThird.R;
                param["ThirdColorG"] = (int)XYData.ColorThird.G;
                param["ThirdColorB"] = (int)XYData.ColorThird.B;
                param["FourthColorR"] = (int)XYData.ColorFourth.R;
                param["FourthColorG"] = (int)XYData.ColorFourth.G;
                param["FourthColorB"] = (int)XYData.ColorFourth.B;
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                XYData.Sys = (string)value["SystemName"];
                XYData.TargetX = (string)value["Xtarget"];
                XYData.TargetY = (string)value["Ytarget"];
                XYData.Xsmall = (float)value["Xsmall"];
                XYData.Xlarge = (float)value["Xlarge"];
                XYData.Xcomboboxindex = (int)value["XcomboboxIndex"];
                XYData.Ysmall = (float)value["Ysmall"];
                XYData.Ylarge = (float)value["Ylarge"];
                XYData.Ycomboboxindex = (int)value["YcomboboxIndex"];
                XYData.Xcheckbox = (bool)value["Xcheckbox"];
                XYData.Ycheckbox = (bool)value["Ycheckbox"];
                XYData.Pointcheckbox = (bool)value["Pointcheckbox"];
                XYData.Xcenter = (float)value["Xcenter"];
                XYData.Ycenter = (float)value["Ycenter"];
                XYData.ColorFirst = Color.FromArgb(255, (int)value["FirstColorR"], (int)value["FirstColorG"], (int)value["FirstColorB"]);
                XYData.ColorSecond = Color.FromArgb(255, (int)value["SecondColorR"], (int)value["SecondColorG"], (int)value["SecondColorB"]);
                XYData.ColorThird = Color.FromArgb(255, (int)value["ThirdColorR"], (int)value["ThirdColorG"], (int)value["ThirdColorB"]);
                XYData.ColorFourth = Color.FromArgb(255, (int)value["FourthColorR"], (int)value["FourthColorG"], (int)value["FourthColorB"]);
            }
        }

        private void districtChanged(object sender, EventArgs e)
        {
            dtDataChanged(null, null);
        }

        private void dtDataChanged(object sender, EventArgs e)
        {

            if (MainModel.GetInstance().DTDataManager.FileDataManagers.Count > 0)
            {
                DTFileDataManager fdm = MainModel.GetInstance().DTDataManager.FileDataManagers[IsCompareForm ? 1 : 0];
                if (fdm.DTDatas.Count > 0)
                {
                    int ms = fdm.DTDatas[0].MS;
                    for (int i = 0; i < chartInfoManager.ChartCount; i++)
                    {
                        chartInfoManager[i].ChangeMS(ms);
                    }
                }
            }
            freshPixelCount();
            charInfoChanged();
        }

        private void freshPixelCount()
        {
            pixels.Clear();
            pixelDataIndexMap.Clear();
            pixelCount = 0;
            for (int i = 0; i < MainModel.GetInstance().DTDataManager.FileDataManagers.Count; i++)  //计算总共有多少个元素点
            {
                if (MainModel.GetInstance().IsFileReplayByCompareMode && i != (IsCompareForm ? 1 : 0))
                {
                    continue;
                }
                DTFileDataManager fdm = MainModel.GetInstance().DTDataManager.FileDataManagers[i];//实例化第i个文件
                addPixelCount(fdm);
                if (fdm.TestPoints.Count + fdm.Events.Count > 0)
                {
                    pixelCount += graphRatio;
                }
            }
        }

        private void addPixelCount(DTFileDataManager fdm)
        {
            DTData preData = null;
            int index = 0;
            foreach (DTData data in fdm.DTDatas) //通过循环拿到文件中的所有数据
            {
                if (data is TestPoint || data is Event)
                {
                    if (preData == null || data.Time - preData.Time < 2)
                    {
                        pixelCount += graphRatio;
                    }
                    else if (data.Time - preData.Time < 20)
                    {
                        pixelCount += (data.Time - preData.Time) * graphRatio;
                    }
                    else
                    {
                        pixelCount += 20 * graphRatio;
                    }
                    preData = data;
                    pixels.Add(pixelCount - 1);
                    pixelDataIndexMap[pixelCount - 1] = new DTDataIndex(fdm.FileID, DTDataType.DTData, index);
                }
                index++;
            }
        }

        private void charInfoChanged()
        {
            GetPoint();
            updateGraphDraw();
        }

        private void GetPoint()
        {
            ChartSerialInfo serialInfo = new ChartSerialInfo();
            serialInfo.DisplayParam = DTDisplayParameterManager.GetInstance()[XYData.Sys, XYData.TargetX, 0];  //x轴信息
            ChartSerialInfo seeialInfoy = new ChartSerialInfo();
            seeialInfoy.DisplayParam = DTDisplayParameterManager.GetInstance()[XYData.Sys, XYData.TargetY, 0];  //y轴信息

            float unitX = 0;
            float unitY = 0;
            if (XYData.Xlarge - XYData.Xsmall > 0)
            {
                unitX = (panel.Width - 125) / (XYData.Xlarge - XYData.Xsmall);
            }
            if (XYData.Ylarge - XYData.Ysmall > 0)
            {
                unitY = (panel.Height - 80) / (XYData.Ylarge - XYData.Ysmall);
            }
            foreach (int i in pixels)
            {
                DTData dtData = MainModel.GetInstance().DTDataManager[pixelDataIndexMap[i]];
                TestPoint tp = dtData as TestPoint;
                if (tp != null)
                {
                    addDataxy(serialInfo, seeialInfoy, unitX, unitY, tp);
                }
            }
        }

        private void addDataxy(ChartSerialInfo serialInfo, ChartSerialInfo seeialInfoy, float unitX, float unitY, TestPoint tp)
        {
            Random random = new Random();
            float? value = serialInfo.GetValue(tp);
            float? valuey = seeialInfoy.GetValue(tp);
            int randomX = random.Next(0, (int)unitX);
            int randomY = random.Next(0, (int)unitY);
            if (value != null && valuey != null)
            {
                DataXY dtxy = new DataXY();
                dtxy.Xvalue = (float)value;
                dtxy.Yvalue = (float)valuey;
                //x轴的随机数
                if (dtxy.Xvalue <= XYData.Xsmall + 0.5)
                {
                    dtxy.Xrandom = (float)randomX;
                }
                else if (dtxy.Xvalue >= XYData.Xlarge - 0.5)
                {
                    dtxy.Xrandom = -(float)randomX;
                }
                else
                {
                    dtxy.Xrandom = (float)(randomX - (int)unitX / 2);
                }
                //y轴的随机数
                if (dtxy.Yvalue <= XYData.Ysmall + 0.5)
                {
                    dtxy.Yrandom = (float)randomY;
                }
                else if (dtxy.Yvalue >= XYData.Ylarge - 0.5)
                {
                    dtxy.Yrandom = -(float)randomY;
                }
                else
                {
                    dtxy.Yrandom = (float)(randomY - (int)unitY / 2);
                }
                Dataxy.Add(dtxy);
            }
        }

        public class DataXY
        {
            public float Xvalue { get; set; }
            public float Yvalue { get; set; }
            public float Xrandom { get; set; }
            public float Yrandom { get; set; }
        }

        private void panel_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                this.ContextMenuStrip = this.contextMenuStrip1;
            }
        }

        private List<int> pixels = new List<int>();

        private static int chartCount = 1;

        private ChartInfoManager chartInfoManager = new ChartInfoManager(chartCount);

        private int pixelCount;

        private int selectedPixel = -1;

        private int graphRatio = 2;

        private Dictionary<int, DTDataIndex> pixelDataIndexMap = new Dictionary<int, DTDataIndex>();

        public void ChangeMSAs(int ms)
        {
            int count = chartInfoManager.ChartCount;
            for (int i = 0; i < count; i++)
            {
                ChartInfo chartInfo = chartInfoManager[i];
                chartInfo.ChangeMS(ms);
            }
            freshPixelCount();
            charInfoChanged();
        }


        private void panel_SizeChanged(object sender, EventArgs e)
        {
            this.Invalidate();
        }

        private void selectedMessageChanged(object sender, EventArgs e)
        {
            if (sender != this)
            {
                selectedPixel = -1;
                if (MainModel.SelectedMessage != null)
                {
                    if (MainModel.SelectedTestPoints.Count == 0)
                    {
                        MainModel.SelectTestPointByMessage();
                        if (MainModel.SelectedTestPoints.Count == 0) return;
                    }
                    TestPoint stp;
                    DTDataManager dtDataManager;
                    getSelectedTestPoint(out stp, out dtDataManager);
                    setSelectedPixel(stp, dtDataManager);
                }
            }
        }

        private void getSelectedTestPoint(out TestPoint stp, out DTDataManager dtDataManager)
        {
            stp = MainModel.SelectedTestPoints[0];
            dtDataManager = MainModel.DTDataManager;
            if (MainModel.IsFileReplayByCompareMode && stp.FileID != dtDataManager.FileDataManagers[IsCompareForm ? 1 : 0].FileID)
            {
                int time = stp.Time;
                stp = dtDataManager.FileDataManagers[IsCompareForm ? 1 : 0].TestPoints.Find(delegate (TestPoint p) { return p.Time == time; });
            }
        }

        private void setSelectedPixel(TestPoint stp, DTDataManager dtDataManager)
        {
            foreach (int i in pixels)
            {
                if (dtDataManager[pixelDataIndexMap[i]] == stp)
                {
                    selectedPixel = i;
                    break;
                }
            }
        }

        Image image;
        private void updateGraphDraw()
        {

            Bitmap bitmap = new Bitmap(panel.Width, panel.Height);
            Graphics dc = Graphics.FromImage(bitmap);
            dc.Clear(Color.White);
            Pen pen = new Pen(Color.Black, 2);
            Pen penR = new Pen(Color.Black, 1);
            dc.DrawString("性能侧切换成功率" + "  VS  " + "路测切换次数", new Font("Arial", 10, FontStyle.Bold), new SolidBrush(Color.Red), new PointF(25, 4));
            dc.DrawString("性能侧切换成功率", new Font("Arial", 10), new SolidBrush(Color.Red), new PointF(25 + (panel.Width - 125) / 2 - 3 * XYData.TargetX.Length, panel.Height - 25));
            dc.DrawString("路测切换次数", new Font("Arial", 10), new SolidBrush(Color.Red), new PointF(panel.Width - 25, 25 + (panel.Height - 80) / 2 - 3 * XYData.TargetY.Length), new StringFormat(StringFormatFlags.DirectionVertical));
            dc.DrawString("采样点个数：" + Dataxy.Count.ToString("N"), new Font("Arial", 8), new SolidBrush(Color.Red), new PointF(panel.Width - 70 - 7 * Dataxy.Count.ToString("N").Length, panel.Height - 14));
            float x, y;

            if (XYData.Xcheckbox)  //x轴居中
            {
                y = (panel.Height - 80) / 2 + 25;
            }
            else  //x轴不居中
            {
                if ((XYData.Ylarge - XYData.Ysmall) != 0)
                {
                    y = 25 + (panel.Height - 80) * (XYData.Ycenter - XYData.Ysmall) / (XYData.Ylarge - XYData.Ysmall);
                }
                else
                    y = 25;
            }

            if (XYData.Ycheckbox)
            {
                x = 25 + (panel.Width - 125) / 2;
            }
            else
            {
                if ((XYData.Xlarge - XYData.Xsmall) != 0)
                {
                    x = 25 + (panel.Width - 125) * (XYData.Xcenter - XYData.Xsmall) / (XYData.Xlarge - XYData.Xsmall);
                }
                else
                    x = 25;
            }

            if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 0)  //x轴从右向左递增,y轴从上向下
            {
                //画X轴
                dc.DrawLine(pen, new PointF(25, y), new PointF(this.panel.Width - 100, y));
                dc.DrawLine(pen, new PointF(25, y), new PointF(30, y - 4));
                dc.DrawLine(pen, new PointF(25, y), new PointF(30, y + 4));

                //画Y轴
                dc.DrawLine(pen, new PointF(panel.Width - x - 75, 25), new PointF(panel.Width - x - 75, panel.Height - 55));
                dc.DrawLine(pen, new PointF(panel.Width - x - 75 - 4, panel.Height - 55 - 8), new PointF(panel.Width - x - 75, panel.Height - 55));
                dc.DrawLine(pen, new PointF(panel.Width - x - 75 + 4, panel.Height - 55 - 8), new PointF(panel.Width - x - 75, panel.Height - 55));

                //坐标线
                dc.DrawLine(penR, new Point(25, panel.Height - 55), new Point(panel.Width - 100, panel.Height - 55));
                dc.DrawString(XYData.Xsmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xlarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(25, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xcenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - x - 75, panel.Height - 55 + 7));

                dc.DrawLine(penR, new Point(panel.Width - 100, 25), new Point(panel.Width - 100, panel.Height - 55));
                dc.DrawString(XYData.Ysmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, 25));
                dc.DrawString(XYData.Ylarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, panel.Height - 55 - 15));
                dc.DrawString(XYData.Ycenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, y));

                float XlenL = 0.00f;
                float YlenL = 0.00f;
                if (XYData.Xcheckbox)  //x轴居中
                {
                    for (int j = 0; j < 5; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 10f;
                    }
                }

                if (XYData.Ycheckbox)  //y轴居中
                {
                    for (int i = 0; i < 5; i++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 10f;
                    }
                }
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 0) //x轴从左向右递增，y轴从上向下
            {
                //画x轴
                dc.DrawLine(pen, new PointF(25, y), new PointF(this.panel.Width - 100, y));
                dc.DrawLine(pen, new PointF(this.panel.Width - 100 - 8, y - 4), new PointF(this.panel.Width - 100, y));
                dc.DrawLine(pen, new PointF(this.panel.Width - 100 - 8, y + 4), new PointF(this.panel.Width - 100, y));
                dc.DrawString(XYData.Xsmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(25, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xlarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xcenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(x, panel.Height - 55 + 7));
                //画y轴
                dc.DrawLine(pen, new PointF(x, 25), new PointF(x, panel.Height - 55));
                dc.DrawLine(pen, new PointF(x - 4, panel.Height - 55 - 8), new PointF(x, panel.Height - 55));
                dc.DrawLine(pen, new PointF(x + 4, panel.Height - 55 - 8), new PointF(x, panel.Height - 55));
                dc.DrawString(XYData.Ysmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, 25));
                dc.DrawString(XYData.Ylarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, panel.Height - 55 - 15));
                dc.DrawString(XYData.Ycenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, y));

                dc.DrawLine(penR, new Point(25, panel.Height - 55), new Point(panel.Width - 100, panel.Height - 55));
                dc.DrawLine(penR, new Point(panel.Width - 100, 25), new Point(panel.Width - 100, panel.Height - 55));
                float XlenL = 0.00f;
                float YlenL = 0.00f;
                if (XYData.Xcheckbox)  //x轴居中
                {
                    for (int j = 0; j < 5; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 10f;
                    }
                }

                if (XYData.Ycheckbox)  //y轴居中
                {
                    for (int i = 0; i < 5; i++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 10f;
                    }
                }
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 1) //x轴从左向右递增，y轴从下向上
            {
                //画x轴
                dc.DrawLine(pen, new PointF(25, panel.Height - y - 30), new PointF(this.panel.Width - 100, panel.Height - y - 30));
                dc.DrawLine(pen, new PointF(this.panel.Width - 100 - 8, panel.Height - y - 30 - 4), new PointF(this.panel.Width - 100, panel.Height - y - 30));
                dc.DrawLine(pen, new PointF(this.panel.Width - 100 - 8, panel.Height - y - 30 + 4), new PointF(this.panel.Width - 100, panel.Height - y - 30));
                dc.DrawString(XYData.Xsmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(25, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xlarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xcenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(x, panel.Height - 55 + 7));
                //画y轴
                dc.DrawLine(pen, new PointF(x, 25), new PointF(x, panel.Height - 55));
                dc.DrawLine(pen, new PointF(x - 4, 35), new PointF(x, 25));
                dc.DrawLine(pen, new PointF(x + 4, 35), new PointF(x, 25));
                dc.DrawString(XYData.Ysmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, panel.Height - 55 - 15));
                dc.DrawString(XYData.Ylarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, 25));
                dc.DrawString(XYData.Ycenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, panel.Height - y - 30));

                dc.DrawLine(penR, new Point(25, panel.Height - 55), new Point(panel.Width - 100, panel.Height - 55));
                dc.DrawLine(penR, new Point(panel.Width - 100, 25), new Point(panel.Width - 100, panel.Height - 55));
                float XlenL = 0.00f;
                float YlenL = 0.00f;
                if (XYData.Xcheckbox)  //x轴居中
                {

                    for (int j = 0; j < 5; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 10f;
                    }
                }

                if (XYData.Ycheckbox)  //y轴居中
                {
                    for (int i = 0; i < 5; i++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 10f;
                    }
                }
            }
            else if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 1)//x轴从右向左递增，y轴从下向上
            {
                //画x轴
                dc.DrawLine(pen, new PointF(25, panel.Height - y - 30), new PointF(this.panel.Width - 100, panel.Height - y - 30));
                dc.DrawLine(pen, new PointF(25, panel.Height - y - 30), new PointF(35, panel.Height - y - 34));
                dc.DrawLine(pen, new PointF(25, panel.Height - y - 30), new PointF(35, panel.Height - y - 26));
                dc.DrawString(XYData.Xsmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xlarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(25, panel.Height - 55 + 7));
                dc.DrawString(XYData.Xcenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - x - 75, panel.Height - 55 + 7));
                //画y轴
                dc.DrawLine(pen, new PointF(panel.Width - x - 75, 25), new PointF(panel.Width - x - 75, panel.Height - 55));
                dc.DrawLine(pen, new PointF(panel.Width - x - 75 - 4, 30), new PointF(panel.Width - x - 75, 25));
                dc.DrawLine(pen, new PointF(panel.Width - x - 75 + 4, 30), new PointF(panel.Width - x - 75, 25));
                dc.DrawString(XYData.Ysmall.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, panel.Height - 55 - 15));
                dc.DrawString(XYData.Ylarge.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, 25));
                dc.DrawString(XYData.Ycenter.ToString("N"), new Font("Arial", 10), new SolidBrush(Color.Black), new PointF(panel.Width - 100 + 7, panel.Height - y - 30));

                dc.DrawLine(penR, new Point(25, panel.Height - 55), new Point(panel.Width - 100, panel.Height - 55));
                dc.DrawLine(penR, new Point(panel.Width - 100, 25), new Point(panel.Width - 100, panel.Height - 55));
                float XlenL = 0.00f;
                float YlenL = 0.00f;
                if (XYData.Xcheckbox)  //x轴居中
                {
                    for (int j = 0; j < 5; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(panel.Width - 100, 25 + YlenL), new PointF(panel.Width - 95, 25 + YlenL));
                        YlenL += (panel.Height - 80) / 10f;
                    }
                }

                if (XYData.Ycheckbox)  //y轴居中
                {
                    for (int i = 0; i < 5; i++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 4f;
                    }
                }
                else
                {
                    for (int j = 0; j < 11; j++)
                    {
                        dc.DrawLine(penR, new PointF(25 + XlenL, panel.Height - 55), new PointF(25 + XlenL, panel.Height - 50));
                        XlenL += (panel.Width - 125) / 10f;
                    }
                }
            }

            //画点
            XYData.FistCount = 0;
            XYData.SecondCount = 0;
            XYData.ThirdCount = 0;
            XYData.FourthCount = 0;
            foreach (DataXY dtxy in Dataxy)
            {
                DrawPoint dp = new DrawPoint(dtxy.Xvalue, dtxy.Yvalue);
                if (XYData.Pointcheckbox)
                {
                    if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 0)
                    {
                        dc.FillEllipse(dp.getBrush(this.panel), dp.getX(this.panel) - 2 + dtxy.Xrandom, dp.getY(this.panel) - 2 + dtxy.Yrandom, 4, 4);
                    }
                    else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 1)
                    {
                        dc.FillEllipse(dp.getBrush(this.panel), dp.getX(this.panel) - 2 + dtxy.Xrandom, dp.getY(this.panel) - 2 - dtxy.Yrandom, 4, 4);
                    }
                    else if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 0)
                    {
                        dc.FillEllipse(dp.getBrush(this.panel), dp.getX(this.panel) - 2 - dtxy.Xrandom, dp.getY(this.panel) - 2 + dtxy.Yrandom, 4, 4);
                    }
                    else if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 1)
                    {
                        dc.FillEllipse(dp.getBrush(this.panel), dp.getX(this.panel) - 2 - dtxy.Xrandom, dp.getY(this.panel) - 2 - dtxy.Yrandom, 4, 4);
                    }
                }
                else
                    dc.FillEllipse(dp.getBrush(this.panel), dp.getX(this.panel) - 2, dp.getY(this.panel) - 2, 4, 4);
            }

            //计算百分比
            double firstcount = 100 * (float)XYData.FistCount / (float)Dataxy.Count;
            double secondcount = 100 * (float)XYData.SecondCount / (float)Dataxy.Count;
            double thirdcount = 100 * (float)XYData.ThirdCount / (float)Dataxy.Count;
            double fourthcount = 100 - Math.Round(firstcount, 2) - Math.Round(secondcount, 2) - Math.Round(thirdcount, 2);
            GetRadioOfForeLevel(ref firstcount, ref secondcount, ref thirdcount, ref fourthcount);
            if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 0)
            {
                dc.DrawString(firstcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF((panel.Width - x - 75) / 2, y / 2));
                dc.DrawString(secondcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(panel.Width - x / 2 - 75, y / 2));
                dc.DrawString(thirdcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(panel.Width - x / 2 - 75, y + (panel.Height - y - 55) / 2));
                dc.DrawString(fourthcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF((panel.Width - x - 75) / 2, y + (panel.Height - y - 55) / 2));
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 0)
            {
                dc.DrawString("冗余小区：" + firstcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF((panel.Width - x - 100) / 2 + x - 70, y / 2));
                dc.DrawString("规避小区：" + secondcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x / 2 - 70, y / 2));
                dc.DrawString("低质小区：" + thirdcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x / 2 - 70, y + (panel.Height - y - 55) / 2));
                dc.DrawString("正常小区：" + fourthcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x + (panel.Width - x - 100) / 2 - 70, y + (panel.Height - y - 55) / 2));
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 1)
            {
                dc.DrawString(firstcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF((panel.Width - x - 100) / 2 + x, panel.Height - y / 2 - 30));
                dc.DrawString(secondcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x / 2, panel.Height - y / 2 - 30));
                dc.DrawString(thirdcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x / 2, (panel.Height - y) / 2));
                dc.DrawString(fourthcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(x + (panel.Width - x - 100) / 2, (panel.Height - y - 30) / 2));
            }
            else
            {
                dc.DrawString(firstcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF((panel.Width - x - 75) / 2, panel.Height - y / 2 - 30));
                dc.DrawString(secondcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(panel.Width - x / 2 - 75, panel.Height - y / 2 - 30));
                dc.DrawString(thirdcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF(panel.Width - x / 2 - 75, (panel.Height - y - 30) / 2));
                dc.DrawString(fourthcount.ToString("f2") + "%", new Font("Arial", 13, FontStyle.Bold), new SolidBrush(Color.Black), new PointF((panel.Width - x - 75) / 2, (panel.Height - y - 30) / 2));
            }
            image = bitmap;
        }
        private void GetRadioOfForeLevel(ref double firstRadio, ref double secondRadio,
            ref double thirdRadio, ref double forthRadio)
        {
            int firstCount = 0;
            int secondCount = 0;
            int thirdCount = 0;
            int forthCount = 0;
            foreach (int keyInf in HandoverAnalysisDic.Keys)
            {
                if (keyInf == 1)
                {
                    firstCount = HandoverAnalysisDic[keyInf].Count;
                }
                if (keyInf == 2)
                {
                    secondCount = HandoverAnalysisDic[keyInf].Count;
                }
                if (keyInf == 3)
                {
                    thirdCount = HandoverAnalysisDic[keyInf].Count;
                }
                if (keyInf == 4)
                {
                    forthCount = HandoverAnalysisDic[keyInf].Count;
                }
            }
            int sumCount = firstCount + secondCount + thirdCount + forthCount;
            //冗余
            firstRadio = 100 * (double)secondCount / (double)sumCount;
            //规避小区
            secondRadio = 100 * (double)forthCount / (double)sumCount;
            //低质小区
            thirdRadio = 100 * (double)thirdCount / (double)sumCount;
            //正常小区
            forthRadio = 100 * (double)firstCount / (double)sumCount;
        }
        private void panel_Paint(object sender, PaintEventArgs e)
        {
            if (XYData.XsmallChanged && !(XYData.TarXchanged || XYData.TarYchanged))
            {
                updateGraphDraw();
                XYData.XsmallChanged = false;
            }
            else if (XYData.TarXchanged || XYData.TarYchanged)
            {
                dtDataChanged(null, null);
                XYData.TarXchanged = false;
                XYData.TarYchanged = false;
                XYData.XsmallChanged = false;
            }
            Graphics g = this.panel.CreateGraphics();
            if (image != null)
            {
                g.DrawImage(image, new Rectangle(new Point(0, 0), new Size(panel.Width, panel.Height)));
            }
        }

        //打开设置界面
        private void ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            HandoveSettingForm box = new HandoveSettingForm();
            box.ShowDialog();
        }

        /// <summary>
        /// 双击显示象限所对应的数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void panel_DoubleClick(object sender, EventArgs e)
        {
            if (screenX > this.panel.Width / 2 && screenY > this.panel.Height / 2)
            {
                //第四象限
                detailform.SetDataSource(HandoverAnalysisDic[1]);

                handoverAnalysisFrm.ChangeTabControl();

            }
            if (screenX < this.panel.Width / 2 && screenY < this.panel.Height / 2)
            {
                //第二象限
                detailform.SetDataSource(HandoverAnalysisDic[4]);

                handoverAnalysisFrm.ChangeTabControl();
            }
            if (screenX > this.panel.Width / 2 && screenY < this.panel.Height / 2)
            {
                //第一象限
                detailform.SetDataSource(HandoverAnalysisDic[2]);
                handoverAnalysisFrm.ChangeTabControl();
            }
            if (screenX < this.panel.Width / 2 && screenY > this.panel.Height / 2)
            {
                ////第三象限
                detailform.SetDataSource(HandoverAnalysisDic[3]);
                handoverAnalysisFrm.ChangeTabControl();
            }
            detailform.Show();
        }

        float screenX = 0;
        float screenY = 0;
        private void panel_MouseMove(object sender, MouseEventArgs e)
        {
            screenX = e.X;
            screenY = e.Y;
        }
    }

    public class DrawPoint
    {
        private readonly float x;
        private readonly float y;
        public DrawPoint(float x, float y)
        {
            this.x = x;
            this.y = y;
        }
        public Brush getBrush(Panel panel)
        {
            SolidBrush brush;
            if (XYData.Xcheckbox && XYData.Ycheckbox)   //x轴，y轴居中
            {
                return centerXYBrush(panel, out brush);
            }
            else if (XYData.Xcheckbox && !XYData.Ycheckbox)  //x轴居中
            {
                return centerXBrush(panel, out brush);
            }
            else if (!XYData.Xcheckbox && XYData.Ycheckbox)  //y轴居中
            {
                return centerYBrush(panel, out brush);
            }
            else
            {
                return notCenterBrush(out brush);
            }
        }

        private Brush centerXYBrush(Panel panel, out SolidBrush brush)
        {
            if (getX(panel) <= 25 + (panel.Width - 125) / 2 && getY(panel) < 25 + (panel.Height - 80) / 2)     //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;
            }
            else if (getX(panel) > 25 + (panel.Width - 125) / 2 && getY(panel) <= 25 + (panel.Height - 80) / 2) //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;
            }
            else if (getX(panel) < 25 + (panel.Width - 125) / 2 && getY(panel) >= 25 + (panel.Height - 80) / 2) //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else   //第4象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        private Brush centerXBrush(Panel panel, out SolidBrush brush)
        {
            if (x <= XYData.Xcenter && getY(panel) < 25 + (panel.Height - 80) / 2)  //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;
            }
            else if (x > XYData.Xcenter && getY(panel) <= 25 + (panel.Height - 80) / 2)  //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;
            }
            else if (x < XYData.Xcenter && getY(panel) >= 25 + (panel.Height - 80) / 2)  //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else        //第1象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        private Brush centerYBrush(Panel panel, out SolidBrush brush)
        {
            if (getX(panel) <= 25 + (panel.Width - 125) / 2 && y < XYData.Ycenter)  //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;

            }
            else if (getX(panel) > 25 + (panel.Width - 125) / 2 && y <= XYData.Ycenter)  //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;

            }
            else if (getX(panel) < 25 + (panel.Width - 125) / 2 && y >= XYData.Ycenter)  //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else     //第1象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        private Brush notCenterBrush(out SolidBrush brush)
        {
            if (x <= XYData.Xcenter && y < XYData.Ycenter)  //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;
            }
            else if (x > XYData.Xcenter && y <= XYData.Ycenter)  //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;
            }
            else if (x < XYData.Xcenter && y >= XYData.Ycenter)  //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else   //第1象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        public float getX(Panel panel)
        {
            float X;
            if (XYData.Ycheckbox)   //y轴居中
            {
                if (XYData.Xcomboboxindex == 1)  //箭头向左
                {
                    X = setCenterYLeft(panel);
                }
                else                         //箭头向右
                {
                    X = setCenterYRight(panel);
                }
            }
            else                         //y轴就是中心线位置
            {
                if (XYData.Xcomboboxindex == 1)   //箭头向左
                {
                    X = setNotCenterYLeft(panel);
                }
                else                          //箭头向右
                {
                    X = setNotCenterYRight(panel);
                }
            }
            return X;
        }

        private float setCenterYLeft(Panel panel)
        {
            float X;
            if (x > XYData.Xcenter)    //y轴左边
            {
                if ((XYData.Xlarge - XYData.Xcenter) != 0)
                {
                    X = 25 + ((panel.Width - 125) / 2) * (XYData.Xlarge - x) / (XYData.Xlarge - XYData.Xcenter);
                }
                else
                    X = 0;
            }
            else
            {
                if ((XYData.Xcenter - XYData.Xsmall) != 0)
                {
                    X = 25 + ((panel.Width - 125) / 2) + ((panel.Width - 125) / 2 * (XYData.Xcenter - x) / (XYData.Xcenter - XYData.Xsmall));
                }
                else
                    X = 0;
            }

            return X;
        }

        private float setCenterYRight(Panel panel)
        {
            float X;
            if (x < XYData.Xcenter)
            {
                if ((XYData.Xcenter - XYData.Xsmall) != 0)
                {
                    X = 25 + (panel.Width - 125) / 2 * (x - XYData.Xsmall) / (XYData.Xcenter - XYData.Xsmall);
                }
                else
                    X = 0;
            }
            else
            {
                if ((XYData.Xlarge - XYData.Xcenter) != 0)
                {
                    X = 25 + (panel.Width - 125) / 2 + (panel.Width - 125) / 2 * (x - XYData.Xcenter) / (XYData.Xlarge - XYData.Xcenter);
                }
                else
                    X = 0;
            }

            return X;
        }

        private float setNotCenterYLeft(Panel panel)
        {
            float X;
            if ((XYData.Xlarge - XYData.Xsmall) != 0)
            {
                X = 25 + (panel.Width - 125) * (XYData.Xlarge - x) / (XYData.Xlarge - XYData.Xsmall);
            }
            else
                X = 0;
            return X;
        }

        private float setNotCenterYRight(Panel panel)
        {
            float X;
            if ((XYData.Xlarge - XYData.Xsmall) != 0)
            {
                X = 25 + (panel.Width - 125) * (x - XYData.Xsmall) / (XYData.Xlarge - XYData.Xsmall);
            }
            else
                X = 0;
            return X;
        }

        public float getY(Panel panel)
        {
            float Y;
            if (XYData.Xcheckbox)            //x轴居中
            {
                if (XYData.Ycomboboxindex == 1)      //Y轴箭头向上
                {
                    Y = setCenterXLeft(panel);
                }
                else                             //箭头向右
                {
                    Y = setCenterXRight(panel);
                }
            }
            else                                 //y轴居中
            {
                if (XYData.Ycomboboxindex == 1)  //箭头向左
                {
                    Y = setNotCenterXLeft(panel);
                }
                else
                {
                    Y = setNotCenterXRight(panel);
                }
            }
            return Y;
        }

        private float setCenterXLeft(Panel panel)
        {
            float Y;
            if (y > XYData.Ycenter)
            {
                if ((XYData.Ylarge - XYData.Ycenter) != 0)
                {
                    Y = 25 + (panel.Height - 80) / 2 * (XYData.Ylarge - y) / (XYData.Ylarge - XYData.Ycenter);
                }
                else
                    Y = 0;
            }
            else
            {
                if ((XYData.Ycenter - XYData.Ysmall) != 0)
                {
                    Y = 25 + (panel.Height - 80) / 2 + (panel.Height - 80) / 2 * (XYData.Ycenter - y) / (XYData.Ycenter - XYData.Ysmall);
                }
                else
                    Y = 0;
            }

            return Y;
        }

        private float setCenterXRight(Panel panel)
        {
            float Y;
            if (y < XYData.Ycenter)
            {
                if ((XYData.Ycenter - XYData.Ysmall) != 0)
                {
                    Y = 25 + (panel.Height - 80) / 2 * (y - XYData.Ysmall) / (XYData.Ycenter - XYData.Ysmall);
                }
                else
                    Y = 0;
            }
            else
            {
                if ((XYData.Ylarge - XYData.Ycenter) != 0)
                {
                    Y = 25 + (panel.Height - 80) / 2 + (panel.Height - 80) / 2 * (y - XYData.Ycenter) / (XYData.Ylarge - XYData.Ycenter);
                }
                else
                    Y = 0;
            }

            return Y;
        }

        private float setNotCenterXLeft(Panel panel)
        {
            float Y;
            if ((XYData.Ylarge - XYData.Ysmall) != 0)
            {
                Y = 25 + (panel.Height - 80) * (XYData.Ylarge - y) / (XYData.Ylarge - XYData.Ysmall);
            }
            else
                Y = 0;
            return Y;
        }

        private float setNotCenterXRight(Panel panel)
        {
            float Y;
            if ((XYData.Ylarge - XYData.Ysmall) != 0)
            {
                Y = 25 + (panel.Height - 80) * (y - XYData.Ysmall) / (XYData.Ylarge - XYData.Ysmall);
            }
            else
                Y = 0;
            return Y;
        }
    }

    public class ChartInfoManager : ICloneable
    {
        public ChartInfoManager(int chartCount)
        {
            chartInfos = new List<ChartInfo>(chartCount);
            for (int i = 0; i < chartCount; i++)
            {
                chartInfos.Add(new ChartInfo());
            }
        }

        public int ChartCount
        {
            get { return chartInfos.Count; }
        }

        public ChartInfo this[int index]
        {
            get { return chartInfos[index]; }
            set { chartInfos[index] = value; }
        }

        public object Clone()
        {
            int count = chartInfos.Count;
            ChartInfoManager clone = new ChartInfoManager(count);
            for (int i = 0; i < count; i++)
            {
                clone[i] = this[i];
            }
            return clone;
        }

        private readonly List<ChartInfo> chartInfos;
    }

    public class ChartInfo : ICloneable
    {
        public string Name { get; set; }

        public List<ChartSerialInfo> SerialInfos
        {
            get { return serialInfos; }
        }

        public void ChangeMS(int ms)
        {
            for (int i = 0; i < SerialInfos.Count; i++)
            {
                SerialInfos[i].MS = ms;
            }
        }

        public object Clone()
        {
            ChartInfo clone = new ChartInfo();
            clone.Name = Name;
            for (int i = 0; i < SerialInfos.Count; i++)
            {
                clone.SerialInfos.Add((ChartSerialInfo)SerialInfos[i].Clone());
            }
            return clone;
        }

        private readonly List<ChartSerialInfo> serialInfos = new List<ChartSerialInfo>();
    }

    public class ChartSerialInfo : ICloneable
    {
        public ChartSerialInfo()
        {
            MS = 1;
        }

        public float? GetValue(TestPoint testPoint)
        {
            object objectValue = testPoint[displayParam.Parameter];
            if (objectValue != null && DTParameterManager.GetInstance().CanConvertToFloat(objectValue, displayParam.Parameter.Info.ValueType))
            {
                float value = DTParameterManager.GetInstance().ConvertToFloat(objectValue, displayParam.Parameter.Info.ValueType);
                if (value >= displayParam.Info.ValueMin && value <= displayParam.Info.ValueMax)
                {
                    return value;
                }
            }
            return null;
        }

        public string Name
        {
            get
            {
                string text = displayParam.Info.Name;
                if (displayParam.Info.ArrayBounds > 1)
                {
                    text += "[" + displayParam.ArrayIndex + "]";
                }
                if (MS > 0)
                {
                    text += "(MS" + MS + ")";
                }
                return text;
            }
        }

        public DTDisplayParameter DisplayParam
        {
            get { return displayParam; }
            set
            {
                displayParam = value;
                if (displayParam != null && displayParam.Info != null)
                {
                    Min = displayParam.Info.ValueMin;
                    Max = displayParam.Info.ValueMax;
                }
            }
        }

        public int MS { get; set; }
        public float Max { get; set; }
        public float Min { get; set; }

        public object Clone()
        {
            ChartSerialInfo clone = new ChartSerialInfo();
            clone.DisplayParam = DisplayParam;
            clone.MS = MS;
            clone.Max = Max;
            clone.Min = Min;
            return clone;
        }

        private DTDisplayParameter displayParam;
    }
}

