﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYInsertNRAlarm : DiySqlMultiNonQuery
    {
        readonly List<NRAlarmInfo> dataList = null;
        public DIYInsertNRAlarm(List<NRAlarmInfo> dataList)
        {
            MainDB = true;
            this.dataList = dataList;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            var sql = @"IF NOT EXISTS(SELECT 1 FROM sysobjects WHERE type = 'U' AND name = 'tb_xinjiang_cfg_alarm')
                BEGIN
                    CREATE TABLE tb_xinjiang_cfg_alarm (
                        [告警名称]     VARCHAR(255)
                    );
                    CREATE UNIQUE NONCLUSTERED INDEX idx_tb_xinjiang_cfg_alarm ON tb_xinjiang_cfg_alarm
                    (
                        [告警名称] ASC
                    );
                END;";
            strb.Append(sql);
            strb.Append("Truncate table tb_xinjiang_cfg_alarm;");
            foreach (NRAlarmInfo info in dataList)
            {
                strb.AppendFormat($"insert into [tb_xinjiang_cfg_alarm] values ('{info.AlarmName}');");
            }

            return strb.ToString();
        }
    }
}
