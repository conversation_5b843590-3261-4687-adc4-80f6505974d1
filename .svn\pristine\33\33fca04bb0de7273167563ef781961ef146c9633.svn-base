﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class PhoneInfoAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static PhoneInfoAnaByRegion instance = null;

        protected List<PhoneInfoItem> phoneInfoList = new List<PhoneInfoItem>();

        public static PhoneInfoAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new PhoneInfoAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected PhoneInfoAnaByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }
        public override string Name
        {
            get
            {
                return "号码信息(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22062, this.Name);
        }

        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<FileInfo, bool> fileAdded = new Dictionary<FileInfo, bool>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == (int)MoMtFile.MoFlag)
                {//主叫关联被叫
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    moMtPair[fileInfo] = mtFile;
                    if (mtFile != null)
                    {
                        fileAdded[mtFile] = true;
                    }
                }
            }
            
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                //被叫关联主叫,且没之前没有添加过
                if (fileInfo.Momt == (int)MoMtFile.MtFlag && !fileAdded.ContainsKey(fileInfo))
                {
                    moMtPair[fileInfo] = null;
                }
            }

            return moMtPair;
        }

        protected override void fireShowForm()
        {
            if (phoneInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            PhoneInfoListForm frm = MainModel.CreateResultForm(typeof(PhoneInfoListForm)) as PhoneInfoListForm;
            frm.FillData(phoneInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            phoneInfoList = new List<PhoneInfoItem>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    string strPairFileName = getPairFileName(file);

                    PhoneInfoItem phoneItem = new PhoneInfoItem(file.FileName, strPairFileName);

                    dowithData(phoneItem, file);

                    if (!string.IsNullOrEmpty(phoneItem.PairPhoneNumber) && !phoneInfoList.Contains(phoneItem))
                    {
                        phoneItem.SN = phoneInfoList.Count + 1;
                        phoneInfoList.Add(phoneItem);
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private string getPairFileName(DTFileDataManager file)
        {
            string strPairFileName = "";
            FileInfo pairFile = MainModel.FileInfos.Find(delegate (FileInfo x) { return x.EventCount == file.FileID; });
            if (pairFile != null)
            {
                strPairFileName = pairFile.Name;
            }

            return strPairFileName;
        }

        protected virtual void dowithData(PhoneInfoItem phoneItem, DTFileDataManager file)
        {
            foreach (TestPoint testPoint in file.TestPoints)
            {
                phoneItem.TestPoints.Add(testPoint);
            }
            foreach (Event evt in file.Events)
            {
                phoneItem.Events.Add(evt);
            }

            foreach (DTData data in file.DTDatas)
            {
                if (WaitBox.CancelRequest)
                {
                    return;
                }
                if (data is MessageWithSource)
                {
                    MessageWithSource msg = data as MessageWithSource;
                    phoneItem.Messages.Add(msg);
                    if ((msg.ID == (int)EnumMsg.Setup2G || msg.ID == (int)EnumMsg.Setup3G)
                        && !string.IsNullOrEmpty(phoneItem.PairPhoneNumber)&& !string.IsNullOrEmpty(phoneItem.PairIMSI)
                        && !string.IsNullOrEmpty(phoneItem.PairTMSI))
                    {
                        phoneItem.SN = phoneInfoList.Count + 1;
                        phoneInfoList.Add(phoneItem);
                        break;
                    }
                    else
                    {
                        setPhoneInfoItem(phoneItem, msg);
                    }
                }
            }
        }

        private void setPhoneInfoItem(PhoneInfoItem phoneItem, MessageWithSource msg)
        {
            if (msg.ID == (int)EnumMsg.Setup2G || msg.ID == (int)EnumMsg.Setup3G)
            {
                string phoneNumber = GetCsfbPhoneNumber(msg);
                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    phoneItem.PairPhoneNumber = phoneNumber;
                }
            }
            else if (msg.ID == (int)EnumMsg.AttachRequest2G || msg.ID == (int)EnumMsg.AttachRequest3G
                || msg.ID == (int)EnumMsg.AttachRequest4G || msg.ID == (int)EnumMsg.AttachAccept2G
                || msg.ID == (int)EnumMsg.AttachAccept3G || msg.ID == (int)EnumMsg.AttachAccept4G
                || msg.ID == (int)EnumMsg.LocationUpdatingAccept2G || msg.ID == (int)EnumMsg.LocationUpdatingAccept3G
                || msg.ID == (int)EnumMsg.LocationUpdatingRequest2G || msg.ID == (int)EnumMsg.LocationUpdatingRequest3G)
            {
                if (string.IsNullOrEmpty(phoneItem.PairIMSI))
                {
                    string imsi = "";
                    MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Length, msg.ID);
                    MessageDecodeHelper.GetSingleString("gsm_a.imsi", ref imsi);
                    phoneItem.PairIMSI = imsi.Replace("\0", "").Trim();
                }
                if (string.IsNullOrEmpty(phoneItem.PairTMSI))
                {
                    uint tmsi = 0;
                    MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Length, msg.ID);
                    MessageDecodeHelper.GetSingleUInt("gsm_a.tmsi", ref tmsi);
                    phoneItem.PairTMSI = "0x" + tmsi.ToString("x");
                }
            }
        }

        public static string GetCsfbPhoneNumber(MessageWithSource msg)
        {
            string paramKey;
            if (msg.MoMtFlag == (int)MoMtFile.MoFlag)
            {
                paramKey = "gsm_a.cld_party_bcd_num";
            }
            else if (msg.MoMtFlag == (int)MoMtFile.MtFlag)
            {
                paramKey = "gsm_a.clg_party_bcd_num";
            }
            else
            {
                return "";
            }

            string phoneNumber = MessageDecodeHelper.GetMsgSingleString(msg, paramKey).Replace("\0", "").Trim();

            int idx = phoneNumber.IndexOf(":");//新版解码带有“Called Party BCD Number:”等前缀
            if (idx > 0)
            {
                phoneNumber = phoneNumber.Substring(idx + 1, phoneNumber.Length - idx - 1);
            }

            return phoneNumber;
        }

        protected enum EnumMsg
        {
            AttachRequest2G = 2049,
            AttachRequest3G = 1899628545,
            AttachRequest4G = 1097533249,

            AttachAccept2G = 2050,
            AttachAccept3G = 1899628546,
            AttachAccept4G = 1097533250,

            LocationUpdatingAccept2G = 1282,
            LocationUpdatingAccept3G = 1899627778,

            LocationUpdatingRequest2G = 1288,
            LocationUpdatingRequest3G = 1899627784,

            Setup2G = 773,
            Setup3G = 1899627269,
        }

    }

    public class PhoneInfoAnaByRegion_FDD : PhoneInfoAnaByRegion
    {
        private static PhoneInfoAnaByRegion_FDD instance = null;
        public static new PhoneInfoAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new PhoneInfoAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected PhoneInfoAnaByRegion_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "LTE_FDD号码信息(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26073, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                return true;
            }
            return false;
        }
    }
}
