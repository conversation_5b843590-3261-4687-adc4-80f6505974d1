using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanInterfereCellFilterForm : BaseDialog
    {
        public ZTScanInterfereCellFilterForm()
        {
            InitializeComponent();
            rdbCaleStrongestCell.Checked = true;
            rdbCaleOtherCell_CheckedChanged(null, null);
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void GetSettingCondition(ref int minRxLev, ref int sameFrequency, ref int adjacentFrequency, 
            ref bool bCaleOtherCell, ref int rxLevDiffOtherCell)
        {
            minRxLev = (int)numMainRxLevMinValue.Value;
            sameFrequency = (int)numSameValue.Value;
            adjacentFrequency = (int)numNbhValue.Value;
            bCaleOtherCell = rdbCaleOtherCell.Checked;
            rxLevDiffOtherCell = (int)numRxLevDiff.Value;
        }

        private void rdbCaleOtherCell_CheckedChanged(object sender, EventArgs e)
        {
            numRxLevDiff.Enabled = rdbCaleOtherCell.Checked;
        }
    }
}