﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;
using DBDataViewer;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class BackgroundFuncQueryManager
    {
        private BackgroundFuncQueryManager()
        {

        }

        private static BackgroundFuncQueryManager instance = null;
        public static BackgroundFuncQueryManager GetInstance()
        {
            if (instance == null)
            {
                instance = new BackgroundFuncQueryManager();
            }
            return instance;
        }

        readonly MainModel mainModel = MainModel.GetInstance();

        readonly BackgroundFuncConfigManager configManager = BackgroundFuncConfigManager.GetInstance();
        readonly DIYSQLBackground_Road_GetFile roadGetFileQuery = DIYSQLBackground_Road_GetFile.GetInstanceRoadGetFile();
        readonly DIYSQLBackground_Road_GetFile_New roadGetFileQuery_New = DIYSQLBackground_Road_GetFile_New.GetInstanceRoadGetFileNew();
        readonly DIYSQLBackground_Road_GetResult roadGetResultQuery = DIYSQLBackground_Road_GetResult.GetInstanceRoadGetResult();

        readonly DIYSQLBackground_Region_GetResult regionGetResultQuery = DIYSQLBackground_Region_GetResult.GetInstanceRegionGetResult();
        readonly DIYSQLBackground_Region_GetProcessedLog getProcessedLogQuery = DIYSQLBackground_Region_GetProcessedLog.GetInstanceRegionGetProcessedLog();

        readonly DIYSQLBackground_Cell_GetResult cellGetResultQuery = DIYSQLBackground_Cell_GetResult.GetInstanceCellGetResult();
        readonly DIYSQLBackground_Cell_GetFile cellGetFileQuery = DIYSQLBackground_Cell_GetFile.GetInstanceCellGetFile();

        public List<BackgroundResult> GetResult_Road(int istime, int ietime, int iSubFuncID, string subFuncName,
            BackgroundStatType statType, string strProject)
        {
            roadGetResultQuery.SetCondition(istime, ietime, iSubFuncID, strProject);
            roadGetResultQuery.Query();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (BackgroundResult_Road item in roadGetResultQuery.ResultList)
            {
                item.SubFuncName = subFuncName;
                item.StatType = statType;
                bgResultList.Add((BackgroundResult)item);
            }
            return bgResultList;
        }

        public List<BackgroundResult> GetResult_Road(DateTime sDateTime, DateTime eDateTime, int iSubFuncID, string subFuncName,
            BackgroundStatType statType, string strProject)
        {
            int istime = (int)(JavaDate.GetMilliseconds(sDateTime) / 1000);
            int ietime = (int)(JavaDate.GetMilliseconds(eDateTime) / 1000);
            return GetResult_Road(istime, ietime, iSubFuncID, subFuncName, statType, strProject);
        }

        public List<BackgroundResult> GetResult_Region(int istime, int ietime, int iSubFuncID, string subFuncName,
            BackgroundStatType statType, string strProject)
        {
            regionGetResultQuery.SetCondition(istime, ietime, iSubFuncID, strProject);
            regionGetResultQuery.Query();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (BackgroundResult_Region item in regionGetResultQuery.ResultList)
            {
                item.SubFuncName = subFuncName;
                item.StatType = statType;
                bgResultList.Add((BackgroundResult)item);
            }
            return bgResultList;
        }

        public List<BackgroundResult> GetResult_Region(DateTime sDateTime, DateTime eDateTime, int iSubFuncID, string subFuncName,
            BackgroundStatType statType, string strProject)
        {
            int istime = (int)(JavaDate.GetMilliseconds(sDateTime) / 1000);
            int ietime = (int)(JavaDate.GetMilliseconds(eDateTime) / 1000);
            return GetResult_Region(istime, ietime, iSubFuncID, subFuncName, statType, strProject);
        }

        public List<BackgroundResult> GetResult_Cell(int istime, int ietime, int iSubFuncID, string subFuncName,
            BackgroundStatType statType, string strProject)
        {
            cellGetResultQuery.SetCondition(istime, ietime, iSubFuncID, strProject);
            cellGetResultQuery.Query();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (BackgroundResult_Cell item in cellGetResultQuery.ResultList)
            {
                item.SubFuncName = subFuncName;
                item.StatType = statType;
                bgResultList.Add((BackgroundResult)item);
            }
            return bgResultList;
        }

        public List<BackgroundResult> GetResult_Cell(DateTime sDateTime, DateTime eDateTime, int iSubFuncID, string subFuncName,
            BackgroundStatType statType, string strProject)
        {
            int istime = (int)(JavaDate.GetMilliseconds(sDateTime) / 1000);
            int ietime = (int)(JavaDate.GetMilliseconds(eDateTime) / 1000);
            return GetResult_Cell(istime, ietime, iSubFuncID, subFuncName, statType, strProject);
        }

        /// <summary>
        /// 获取按路段分析，需要分析的文件
        /// </summary>
        /// <param name="istime">起始时间</param>
        /// <param name="ietime">结束时间</param>
        /// <param name="iSubFuncID">功能点ID</param>
        /// <param name="projectType">项目ID，字符串，如：1,2,3</param>
        /// <param name="serviceType">业务ID，字符串，如：1,2,3</param>
        public void GetFile_Road(int iSubFuncID, string serviceType, string carrierType)
        {
            roadGetFileQuery.SetCondition(configManager.ISTime, configManager.IETime, iSubFuncID, configManager.ProjectType, serviceType, carrierType);
            roadGetFileQuery.Query();
        }
        public void GetFile_RoadNew(int iSubFuncID, string serviceType, string carrierType)
        {
            roadGetFileQuery_New.SetCondition(configManager.ISTime, configManager.IETime, iSubFuncID, configManager.ProjectType, serviceType, carrierType);
            roadGetFileQuery_New.Query();
        }

        /// <summary>
        /// 获取按小区分析，需要分析的文件
        /// </summary>
        /// <param name="istime">起始时间</param>
        /// <param name="ietime">结束时间</param>
        /// <param name="iSubFuncID">功能点ID</param>
        /// <param name="projectType">项目ID，字符串，如：1,2,3</param>
        /// <param name="serviceType">业务ID，字符串，如：1,2,3</param>
        public void GetFile_Cell(int iSubFuncID, string serviceType, string carrierType)
        {
            GetFile_Cell(iSubFuncID, serviceType, carrierType, configManager.ISTime, configManager.IETime);
        }

        /// <summary>
        /// 获取按小区分析，需要分析的文件
        /// </summary>
        /// <param name="istime">起始时间</param>
        /// <param name="ietime">结束时间</param>
        /// <param name="iSubFuncID">功能点ID</param>
        /// <param name="projectType">项目ID，字符串，如：1,2,3</param>
        /// <param name="serviceType">业务ID，字符串，如：1,2,3</param>
        public void GetFile_Cell(int iSubFuncID, string serviceType, string carrierType, int istime, int ietime)
        {
            cellGetFileQuery.SetCondition(istime, ietime, iSubFuncID, configManager.ProjectType, serviceType, carrierType);
            cellGetFileQuery.Query();
        }

        #region 单站验收过滤获取分析文件、过滤查询分析结果(陕西、新疆)

        readonly DIYSQLCellAccept_GetFilterFile cellAcceptGetFileQuery = DIYSQLCellAccept_GetFilterFile.GetInstanceGetFilterFile();
        readonly DIYSQLCellAccept_GetFilterResult cellAcceptGetResultQuery = DIYSQLCellAccept_GetFilterResult.GetInstanceGetFilterResult();

        /// <summary>
        /// 过滤查询预处理结果
        /// </summary>
        /// <param name="iSubFuncID"></param>
        /// <param name="serviceType"></param>
        /// <param name="carrierType"></param>
        /// <param name="sqlColName">tb_probchk_cell_result年月表中要过滤查询的列名</param>
        /// <param name="fileNameKeyText">tb_probchk_cell_result年月表中要过滤查询列的关键字</param>
        public void GetFilterFile_CellAccept(int iSubFuncID, string serviceType, string carrierType
            , string sqlColName, string fileNameKeyText)
        {
            string fileNameSplitSqlStr = getFilterSqlString(sqlColName, fileNameKeyText);
            cellAcceptGetFileQuery.SetCondition(configManager.ISTime, configManager.IETime, iSubFuncID
                , configManager.ProjectType, serviceType, carrierType, fileNameSplitSqlStr);
            cellAcceptGetFileQuery.Query();
        }

        /// <summary>
        /// 过滤查询待分析的文件
        /// </summary>
        /// <param name="istime"></param>
        /// <param name="ietime"></param>
        /// <param name="iSubFuncID"></param>
        /// <param name="subFuncName"></param>
        /// <param name="statType"></param>
        /// <param name="strProject"></param>
        /// <param name="sqlColName">tb_log_file_年月表中要过滤查询的列名</param>
        /// <param name="fileNameKeyText">tb_log_file_年月表中要过滤查询列的关键字</param>
        /// <returns></returns>
        public List<BackgroundResult> GetFilterResult_CellAccept(CellAcceptCondition cond, string subFuncName,
            BackgroundStatType statType)
        {
            //先检查tb_probchk_cell_result表中已分析的文件，项目类型是否不再属于单站验收
            string sql = string.Format("exec sp_probchk_cellResultProject_ReCheck {0},{1},{2},'{3}'"
                , cond.Stime, cond.Etime, cond.SubFuncID, cond.Projects);
            DiySqlNonQuery queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();

            string fileNameSplitSqlStr = getFilterSqlString(cond.ColName, cond.FileNameKeyText);
            cellAcceptGetResultQuery.SetCondition(cond.Stime, cond.Etime, cond.SubFuncID, cond.Projects, fileNameSplitSqlStr);
            cellAcceptGetResultQuery.Query();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (BackgroundResult_Cell item in cellAcceptGetResultQuery.ResultList)
            {
                item.SubFuncName = subFuncName;
                item.StatType = statType;
                bgResultList.Add((BackgroundResult)item);
            }
            return bgResultList;
        }

        public class CellAcceptCondition
        {
            public CellAcceptCondition(int istime, int ietime, int iSubFuncID, string strProject
                , string sqlColName, string fileNameKeyText)
            {
                Stime = istime;
                Etime = ietime;
                SubFuncID = iSubFuncID;
                Projects = strProject;
                ColName = sqlColName;
                FileNameKeyText = fileNameKeyText;
            }

            public int Stime { get; set; }
            public int Etime { get; set; }
            public int SubFuncID { get; set; }
            public string Projects { get; set; }
            public string ColName { get; set; }
            public string FileNameKeyText { get; set; }
        }

        private string getFilterSqlString(string sqlColName, string fileNameKeyText)
        {
            if (string.IsNullOrEmpty(sqlColName) || string.IsNullOrEmpty(fileNameKeyText))
            {
                return "";
            }

            int orNum = 1;
            string strFileNamekeys = QueryCondition.MakeFileFilterString(fileNameKeyText, ref orNum);
            string[] split = new string[] { "{,}" };
            split = strFileNamekeys.Split(split, StringSplitOptions.RemoveEmptyEntries);

            StringBuilder strb = new StringBuilder();
            foreach (string strKey in split)
            {
                strb.Append(string.Format("{0} like ''%{1}%'' or ", sqlColName, strKey));
            }
            if (strb.Length > 3)
            {
                strb.Remove(strb.Length - 3, 3);
            }
            strFileNamekeys = strb.ToString();
            return strFileNamekeys;
        }

        private class DIYSQLCellAccept_GetFilterFile : DIYSQLBackground_Road_GetFile
        {
            string fileNameSplitSqlStr = "";
            private static DIYSQLCellAccept_GetFilterFile instanceGetFilterFile = null;
            public static DIYSQLCellAccept_GetFilterFile GetInstanceGetFilterFile()
            {
                if (instanceGetFilterFile == null)
                {
                    instanceGetFilterFile = new DIYSQLCellAccept_GetFilterFile();
                }
                return instanceGetFilterFile;
            }
            private DIYSQLCellAccept_GetFilterFile()
                : base()
            {
            }
            public void SetCondition(int istime, int ietime, int iSubFuncID, string projectType
                , string serviceType, string carrierType, string fileNameSplitSqlStr)
            {
                this.istime = istime;
                this.ietime = ietime;
                this.iSubFuncID = iSubFuncID;
                this.projectType = projectType;
                this.serviceType = serviceType;
                this.carrierType = carrierType;
                this.fileNameSplitSqlStr = fileNameSplitSqlStr;
            }
            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_cellAccept_file_filterGet " + istime + "," + ietime + "," + iSubFuncID + ",'"
                    + projectType + "','" + serviceType + "','" + carrierType + "','" + fileNameSplitSqlStr + "'";
                return sql;
            }

            public override string Name
            {
                get { return "DIYSQLCellAccept_GetFilterFile"; }
            }
        }

        private class DIYSQLCellAccept_GetFilterResult : DIYSQLBackground_Cell_GetResult
        {
            string fileNameSplitSqlStr = "";
            private static DIYSQLCellAccept_GetFilterResult instanceGetFilterResult = null;
            public static DIYSQLCellAccept_GetFilterResult GetInstanceGetFilterResult()
            {
                if (instanceGetFilterResult == null)
                {
                    instanceGetFilterResult = new DIYSQLCellAccept_GetFilterResult();
                }
                return instanceGetFilterResult;
            }
            private DIYSQLCellAccept_GetFilterResult()
                : base()
            {
            }
            public void SetCondition(int istime, int ietime, int iSubFuncID, string strProject
                , string fileNameSplitSqlStr)
            {
                this.istime = istime;
                this.ietime = ietime;
                this.iSubFuncID = iSubFuncID;
                this.strProject = strProject;
                this.fileNameSplitSqlStr = fileNameSplitSqlStr;
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_cellAccept_result_filterGet " + istime + "," + ietime
                    + "," + iSubFuncID + ",'" + strProject + "','" + fileNameSplitSqlStr + "'";
                return sql;
            }
            public override string Name
            {
                get { return "DIYSQLCellAccept_GetFilterResult"; }
            }
        }

        #endregion

        #region 湖北单站验收获取分析文件、保存分析结果

        readonly DIYSQLBackground_SingleStation_GetFile stationGetFileQuery = DIYSQLBackground_SingleStation_GetFile.GetInstanceGetFile();

        public void GetFile_SingleStation(int iSubFuncID, string serviceType, string eNodeBID, int CellID, string carrierType, int recentDays)
        {
            stationGetFileQuery.SetCondition(configManager.ISTime, configManager.IETime, iSubFuncID, configManager.ProjectType, serviceType, carrierType);
            stationGetFileQuery.ENodeBID = eNodeBID;
            stationGetFileQuery.CellId = CellID;
            stationGetFileQuery.RecentDays = recentDays;
            stationGetFileQuery.Query();
        }

        public void SaveResult_SingleStation(CellAcceptInfo_HB info, bool hasFoundCircleFile, bool isBtsPassAccept
            , string StrErrorInfo, string StrNote)
        {
            if (StrErrorInfo!=null && StrErrorInfo.Length > 3500)
            {
                StrErrorInfo = StrErrorInfo.Substring(0, 3500) + "...";
            }
            if (StrNote != null && StrNote.Length > 3500)
            {
                StrNote = StrNote.Substring(0, 3500) + "...";
            }
            string sql = string.Format(@"exec sp_probchk_singleStation_result_save '{0}','{1}',{2},{3},'{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}'" 
                ,info.CellParamInfo.DistrictName, info.CellParamInfo.BtsName, info.CellParamInfo.ENodeBID, info.CellParamInfo.CellId
                , info.CellParamInfo.CoverType, info.IsPassAccept, info.HasFoundFile, isBtsPassAccept, hasFoundCircleFile
                , info.CellParamInfo.AcceptDateTime, StrErrorInfo, StrNote);
            
            DiySqlNonQuery queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }


        private class DIYSQLBackground_SingleStation_GetFile : DIYSQLBackground_Road_GetFile
        {
            public int RecentDays = 0;
            public string ENodeBID = "";
            public int CellId = 0;
            private static DIYSQLBackground_SingleStation_GetFile instanceGetFile = null;
            public static DIYSQLBackground_SingleStation_GetFile GetInstanceGetFile()
            {
                if (instanceGetFile == null)
                {
                    instanceGetFile = new DIYSQLBackground_SingleStation_GetFile();
                }
                return instanceGetFile;
            }
            private DIYSQLBackground_SingleStation_GetFile()
                : base()
            {
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_singleStation_file_get " + RecentDays + "," + istime + "," + ietime + ",'"
                    + ENodeBID + "'," + CellId + ",'" + projectType + "','" + serviceType + "','" + carrierType + "'";
                return sql;
            }

            public override string Name
            {
                get { return "DIYSQLBackground_SingleStation_GetFile"; }
            }
        }

        #endregion

        /// <summary>
        /// 保存按路段分析的结果
        /// </summary>
        /// <param name="subFuncID"></param>
        /// <param name="fileID"></param>
        /// <param name="resultList"></param>
        public void SaveResult_Road(int subFuncID, FileInfo file, List<BackgroundResult> resultList)
        {
            DiySqlNonQuery queryNon = null;
            string sql = "";
            int fileID = file.ID;
            if (resultList.Count > 0)
            {
                DateTime beginDateTime = JavaDate.GetDateTimeFromMilliseconds(file.BeginTime * 1000L);
                string year = beginDateTime.Year.ToString();
                string month = beginDateTime.Month.ToString();
                if (month.Length == 1)
                {
                    month = "0" + month;
                }
                string yearMonth = year + month;
                int maxID_Road = getMaxID_Road(yearMonth);
                string tableName = "tb_probchk_road_result_" + yearMonth;
                checkTableExists(yearMonth, "tb_probchk_road_result");

                sql = "delete from " + tableName + " where iSubFuncID = " + subFuncID + " and iFileID = " + fileID + " and strProject = '" + configManager.ProjectType + "'";
                queryNon = new DiySqlNonQuery(mainModel, sql);
                queryNon.Query();

                BackgroundFuncResultWriter.Instance.WriteRoad(tableName, resultList, maxID_Road);
            }

            sql = "insert into tb_probchk_road_processed_log values(" + subFuncID + "," + fileID + ",'" + configManager.ProjectType + "')";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        private void checkTableExists(string yearMonth, string tableNameSrc)
        {
            string tableName = tableNameSrc + "_" + yearMonth;
            string sql = "exec sp_probchk_table_time_insert '" + tableNameSrc + "','" + yearMonth + "','" + configManager.ProjectType + "'";
            DiySqlNonQuery queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();

            sql = "exec sp_adapter_addtable '" + tableName + "', '" + tableNameSrc + "'";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        /// <summary>
        /// 保存按汇聚分析的结果
        /// </summary>
        /// <param name="subFuncID"></param>
        /// <param name="fileID"></param>
        /// <param name="resultList"></param>
        public void SaveResult_Region(int subFuncID, int istime, int ietime, List<BackgroundResult> resultList)
        {
            DiySqlNonQuery queryNon = null;
            string sql = "";
            if (resultList.Count > 0)
            {
                DateTime beginDateTime = JavaDate.GetDateTimeFromMilliseconds(istime * 1000L);
                string year = beginDateTime.Year.ToString();
                string month = beginDateTime.Month.ToString();
                if (month.Length == 1)
                {
                    month = "0" + month;
                }
                string yearMonth = year + month;
                int maxID_Region = getMaxID_Region(yearMonth);

                string tableName = "tb_probchk_region_result_" + yearMonth;
                checkTableExists(yearMonth, "tb_probchk_region_result");

                sql = "delete from " + tableName + " where iSubFuncID = " + subFuncID + " and istime >= " + istime +
                    " and ietime <= " + ietime + " and strProject = '" + configManager.ProjectType + "'";
                queryNon = new DiySqlNonQuery(mainModel, sql);
                queryNon.Query();

                BackgroundFuncResultWriter.Instance.WriteRegion(tableName, resultList, maxID_Region);
            }

            sql = "delete from tb_probchk_region_processed_log where iSubFuncID = " + subFuncID + " and istime >= " + istime +
                    " and ietime <= " + ietime + " and strProject = '" + configManager.ProjectType + "'";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();

            sql = "insert into tb_probchk_region_processed_log values(" + subFuncID + "," + istime + "," + ietime + ",'" + configManager.ProjectType + "')";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        /// <summary>
        /// 保存按小区分析的结果
        /// </summary>
        /// <param name="subFuncID"></param>
        /// <param name="fileID"></param>
        /// <param name="resultList"></param>
        public void SaveResult_Cell(int subFuncID, FileInfo file, List<BackgroundResult> resultList)
        {
            if (file == null)
            {
                return;
            }

            int fileID = file.ID;
            DiySqlNonQuery queryNon = null;
            string sql = "";
            if (resultList.Count > 0)
            {
                DateTime beginDateTime = JavaDate.GetDateTimeFromMilliseconds(file.BeginTime * 1000L);
                string year = beginDateTime.Year.ToString();
                string month = beginDateTime.Month.ToString();
                if (month.Length == 1)
                {
                    month = "0" + month;
                }
                string yearMonth = year + month;
                int maxID_Cell = getMaxID_Cell(yearMonth);
                string tableName = "tb_probchk_cell_result_" + yearMonth;
                checkTableExists(yearMonth, "tb_probchk_cell_result");

                sql = "delete from " + tableName + " where iSubFuncID = " + subFuncID + " and iFileID = " + fileID + " and strProject = '" + configManager.ProjectType + "'"; 
                queryNon = new DiySqlNonQuery(mainModel, sql);
                queryNon.Query();

                BackgroundFuncResultWriter.Instance.WriteCell(tableName, resultList, maxID_Cell);
            }

            sql = "insert into tb_probchk_cell_processed_log_ByFile values(" + subFuncID + "," + fileID + ",'" + configManager.ProjectType + "')";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        /// <summary>
        /// 保存按小区汇聚分析的结果
        /// </summary>
        /// <param name="subFuncID"></param>
        /// <param name="fileID"></param>
        /// <param name="resultList"></param>
        public void SaveResult_Cell(int subFuncID, int istime, int ietime, List<BackgroundResult> resultList)
        {
            DiySqlNonQuery queryNon = null;
            string sql = "";
            if (resultList.Count > 0)
            {
                DateTime beginDateTime = JavaDate.GetDateTimeFromMilliseconds(istime * 1000L);
                string year = beginDateTime.Year.ToString();
                string month = beginDateTime.Month.ToString();
                if (month.Length == 1)
                {
                    month = "0" + month;
                }
                string yearMonth = year + month;
                int maxID_Cell = getMaxID_Cell(yearMonth);

                string tableName = "tb_probchk_cell_result_" + yearMonth;
                checkTableExists(yearMonth, "tb_probchk_cell_result");

                sql = "delete from " + tableName + " where iSubFuncID = " + subFuncID + " and istime >= " + istime +
                    " and ietime <= " + ietime + " and strProject = '" + configManager.ProjectType + "'";
                queryNon = new DiySqlNonQuery(mainModel, sql);
                queryNon.Query();

                BackgroundFuncResultWriter.Instance.WriteCell(tableName, resultList, maxID_Cell);
            }

            sql = "delete from tb_probchk_cell_processed_log_ByRegion where iSubFuncID = " + subFuncID + " and istime >= " + istime +
                " and ietime <= " + ietime + " and strProject = '" + configManager.ProjectType + "'";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();

            sql = "insert into tb_probchk_cell_processed_log_ByRegion values(" + subFuncID + "," + istime + "," + ietime + ",'" + configManager.ProjectType + "')";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        public List<TimePeriod> GetStatedTimePeriod_Region(int subFuncID, string strProject)
        {
            string sql = "select isubfuncid, istime, ietime from tb_probchk_region_processed_log where iSubFuncID = " + subFuncID
                + " and strProject = '" + strProject + "'";
            getProcessedLogQuery.SetSql(sql);
            getProcessedLogQuery.Query();
            return getProcessedLogQuery.StatedTimes;
        }

        public List<TimePeriod> GetStatedTimePeriod_Cell(int subFuncID, string strProject)
        {
            string sql = "select isubfuncid, istime, ietime from tb_probchk_cell_processed_log_ByRegion where iSubFuncID = " + subFuncID
                 + " and strProject = '" + strProject + "'";
            getProcessedLogQuery.SetSql(sql);
            getProcessedLogQuery.Query();
            return getProcessedLogQuery.StatedTimes;
        }

        public List<int> GetStatedFileID_Cell(int subFuncID)
        {
            DIYSQLBackground_Cell_GetProcessedLog.GetInstanceCellGetProcessedLog().SetCondition(subFuncID,configManager.ProjectType);
            DIYSQLBackground_Cell_GetProcessedLog.GetInstanceCellGetProcessedLog().Query();
            return new List<int>(DIYSQLBackground_Cell_GetProcessedLog.GetInstanceCellGetProcessedLog().fileIdList);
        }

        /// <summary>
        /// 获取按路段分析结果表中最大ID
        /// </summary>
        private int getMaxID_Road(string yearMonth)
        {
            string sql = "select isnull(max(iid),0) from tb_probchk_road_result_" + yearMonth;
            DiySqlOneIntValueOnly queryMaxID = new DiySqlOneIntValueOnly(mainModel, sql);
            queryMaxID.Query();
            int maxID = queryMaxID.IntValue;
            return maxID < 0 ? 0 : maxID;
        }

        /// <summary>
        /// 获取按汇聚分析结果表中最大ID
        /// </summary>
        private int getMaxID_Region(string yearMonth)
        {
            string sql = "select isnull(max(iid),0) from tb_probchk_region_result_" + yearMonth;
            DiySqlOneIntValueOnly queryMaxID = new DiySqlOneIntValueOnly(mainModel, sql);
            queryMaxID.Query();
            int maxID = queryMaxID.IntValue;
            return maxID < 0 ? 0 : maxID;
        }

        /// <summary>
        /// 获取按小区汇聚分析结果表中最大ID
        /// </summary>
        private int getMaxID_Cell(string yearMonth)
        {
            string sql = "select isnull(max(iid),0) from tb_probchk_cell_result_" + yearMonth;
            DiySqlOneIntValueOnly queryMaxID = new DiySqlOneIntValueOnly(mainModel, sql);
            queryMaxID.Query();
            int maxID = queryMaxID.IntValue;
            return maxID < 0 ? 0 : maxID;
        }

        public class DIYSQLBackground_Road_GetFile : DIYSQLBase
        {
            private static DIYSQLBackground_Road_GetFile instanceRoadGetFile = null;
            public static DIYSQLBackground_Road_GetFile GetInstanceRoadGetFile()
            {
                if (instanceRoadGetFile == null)
                {
                    instanceRoadGetFile = new DIYSQLBackground_Road_GetFile();
                }
                return instanceRoadGetFile;
            }
            protected DIYSQLBackground_Road_GetFile()
                : base(MainModel.GetInstance())
            {
            }

            protected int istime;
            protected int ietime;
            protected int iSubFuncID;
            protected string projectType;
            protected string serviceType;
            protected string carrierType;

            public void SetCondition(int istime, int ietime, int iSubFuncID, string projectType, string serviceType, string carrierType)
            {
                this.istime = istime;
                this.ietime = ietime;
                this.iSubFuncID = iSubFuncID;
                this.projectType = projectType;
                this.serviceType = serviceType;
                this.carrierType = carrierType;
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_road_file_get " + istime + "," + ietime + "," + iSubFuncID + ",'" + projectType + "','" +
                    serviceType + "','" + carrierType + "'";
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[4];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_String;
                rType[3] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                MainModel.FileInfos.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        int iFileID = package.Content.GetParamInt();
                        int beginTime = package.Content.GetParamInt();
                        string strFileName = package.Content.GetParamString();
                        string logTableName = package.Content.GetParamString();
                        FileInfo fileInfo = new FileInfo();
                        fileInfo.ID = iFileID;
                        fileInfo.BeginTime = beginTime;
                        fileInfo.Name = strFileName;
                        fileInfo.LogTable = logTableName;
                        fileInfo.DistrictID = clientProxy.DbID;
                        MainModel.FileInfos.Add(fileInfo);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Road_GetFile"; }
            }

            protected override UserMng.LogInfoItem getRecLogItem()
            {
                throw new NotImplementedException();
            }
        }
        public class DIYSQLBackground_Road_GetFile_New : DIYSQLBackground_Road_GetFile
        {//比DIYSQLBackground_Road_GetFile多查询出若干列
            private static DIYSQLBackground_Road_GetFile_New instanceRoadGetFileNew = null;
            public static DIYSQLBackground_Road_GetFile_New GetInstanceRoadGetFileNew()
            {
                if (instanceRoadGetFileNew == null)
                {
                    instanceRoadGetFileNew = new DIYSQLBackground_Road_GetFile_New();
                }
                return instanceRoadGetFileNew;
            }
            protected DIYSQLBackground_Road_GetFile_New()
                : base()
            {
            }
            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_road_file_get_new " + istime + "," + ietime + "," + iSubFuncID + ",'" + projectType + "','" +
                    serviceType + "','" + carrierType + "'";
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[12];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_String;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                MainModel.FileInfos.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        FileInfo fileInfo = new FileInfo();
                        fileInfo.ID = package.Content.GetParamInt();
                        fileInfo.Name = package.Content.GetParamString();
                        fileInfo.BeginTime = package.Content.GetParamInt();
                        fileInfo.EndTime = package.Content.GetParamInt();
                        fileInfo.EventCount = package.Content.GetParamInt();
                        fileInfo.TestPointCount = package.Content.GetParamInt();
                        fileInfo.ProjectID = package.Content.GetParamInt();
                        fileInfo.ServiceType = package.Content.GetParamInt();
                        fileInfo.AreaTypeID = package.Content.GetParamInt();
                        fileInfo.AreaID = package.Content.GetParamInt();
                        fileInfo.Momt = package.Content.GetParamInt();
                        fileInfo.LogTable = package.Content.GetParamString();
                        fileInfo.DistrictID = clientProxy.DbID;

                        MainModel.FileInfos.Add(fileInfo);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Road_GetFile_New"; }
            }
        }
        private class DIYSQLBackground_Cell_GetFile : DIYSQLBackground_Road_GetFile
        {
            private static DIYSQLBackground_Cell_GetFile instanceCellGetFile = null;
            public static DIYSQLBackground_Cell_GetFile GetInstanceCellGetFile()
            {
                if (instanceCellGetFile == null)
                {
                    instanceCellGetFile = new DIYSQLBackground_Cell_GetFile();
                }
                return instanceCellGetFile;
            }
            private DIYSQLBackground_Cell_GetFile()
                : base()
            {
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_cell_file_get " + istime + "," + ietime + "," + iSubFuncID + ",'" + projectType + "','" +
                    serviceType + "','" + carrierType + "'";
                return sql;
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Cell_GetFile"; }
            }
        }

        private class DIYSQLBackground_Cell_GetProcessedLog : DIYSQLBase
        {
            private static DIYSQLBackground_Cell_GetProcessedLog instanceCellGetProcessedLog = null;
            public static DIYSQLBackground_Cell_GetProcessedLog GetInstanceCellGetProcessedLog()
            {
                if (instanceCellGetProcessedLog == null)
                {
                    instanceCellGetProcessedLog = new DIYSQLBackground_Cell_GetProcessedLog ();
                }
                return instanceCellGetProcessedLog;
            }
            private DIYSQLBackground_Cell_GetProcessedLog()
                : base(MainModel.GetInstance())
            {
            }

            int iSubFuncID;
            string strProject = "";
            public List<int> fileIdList = new List<int>();

            public void SetCondition(int iSubFuncID,string strProject)
            {
                this.iSubFuncID = iSubFuncID;
                this.strProject = strProject;
            }

            protected override string getSqlTextString()
            {
                string sql = string.Format("select iFileID from tb_probchk_cell_processed_log_ByFile where iSubFuncID = {0} and strProject ='{1}'"
                    , iSubFuncID, strProject);
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[1];
                rType[0] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                fileIdList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fileIdList.Add(package.Content.GetParamInt());
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Cell_GetProcessedLog"; }
            }
        }

        public class DIYSQLBackground_Road_GetResult : DIYSQLBase
        {
            private static DIYSQLBackground_Road_GetResult instanceRoadGetResult = null;
            public static DIYSQLBackground_Road_GetResult GetInstanceRoadGetResult()
            {
                if (instanceRoadGetResult == null)
                {
                    instanceRoadGetResult = new DIYSQLBackground_Road_GetResult();
                }
                return instanceRoadGetResult;
            }
            private DIYSQLBackground_Road_GetResult()
                : base(MainModel.GetInstance())
            {
            }

            int istime;
            int ietime;
            int iSubFuncID;
            string strProject = "";
            public List<BackgroundResult_Road> ResultList { get; set; } = new List<BackgroundResult_Road>();

            public void SetCondition(int istime, int ietime, int iSubFuncID, string strProject)
            {
                this.istime = istime;
                this.ietime = ietime;
                this.iSubFuncID = iSubFuncID;
                this.strProject = strProject;
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_road_result_get " + istime + "," + ietime + "," + iSubFuncID + ",'" + strProject + "'";
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[26];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_String;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_Float;
                rType[12] = E_VType.E_Int;
                rType[13] = E_VType.E_Float;
                rType[14] = E_VType.E_Float;
                rType[15] = E_VType.E_Float;
                rType[16] = E_VType.E_Float;
                rType[17] = E_VType.E_Float;
                rType[18] = E_VType.E_Float;
                rType[19] = E_VType.E_String;
                rType[20] = E_VType.E_String;
                rType[21] = E_VType.E_String;
                rType[22] = E_VType.E_String;
                rType[23] = E_VType.E_String;
                rType[24] = E_VType.E_String;
                rType[25] = E_VType.E_VARYBIN;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                ResultList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        BackgroundResult_Road result = new BackgroundResult_Road();
                        result.FillFrom(package.Content);
                        ResultList.Add(result);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Road_GetResult"; }
            }

            protected override UserMng.LogInfoItem getRecLogItem()
            {
                throw new NotImplementedException();
            }
        }

        private class DIYSQLBackground_Region_GetResult : DIYSQLBase
        {
            private static DIYSQLBackground_Region_GetResult instanceRegionGetResult = null;
            public static DIYSQLBackground_Region_GetResult GetInstanceRegionGetResult()
            {
                if (instanceRegionGetResult == null)
                {
                    instanceRegionGetResult = new DIYSQLBackground_Region_GetResult();
                }
                return instanceRegionGetResult;
            }
            private DIYSQLBackground_Region_GetResult()
                : base(MainModel.GetInstance())
            {
            }

            int istime;
            int ietime;
            int iSubFuncID;
            string strProject = "";
            public List<BackgroundResult_Region> ResultList = new List<BackgroundResult_Region>();

            public void SetCondition(int istime, int ietime, int iSubFuncID,string strProject)
            {
                this.istime = istime;
                this.ietime = ietime;
                this.iSubFuncID = iSubFuncID;
                this.strProject = strProject;
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_region_result_get " + istime + "," + ietime + "," + iSubFuncID + "," + strProject;
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[19];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Float;
                rType[7] = E_VType.E_Float;
                rType[8] = E_VType.E_Float;
                rType[9] = E_VType.E_Float;
                rType[10] = E_VType.E_Float;
                rType[11] = E_VType.E_Float;
                rType[12] = E_VType.E_String;
                rType[13] = E_VType.E_String;
                rType[14] = E_VType.E_String;
                rType[15] = E_VType.E_String;
                rType[16] = E_VType.E_String;
                rType[17] = E_VType.E_String;
                rType[18] = E_VType.E_VARYBIN;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                ResultList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        BackgroundResult_Region result = new BackgroundResult_Region();
                        result.FillFrom(package.Content);
                        ResultList.Add(result);
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Region_GetResult"; }
            }
        }

        private class DIYSQLBackground_Region_GetProcessedLog : DIYSQLBase
        {
            public List<TimePeriod> StatedTimes = new List<TimePeriod>();
            private static DIYSQLBackground_Region_GetProcessedLog instanceRegionGetProcessedLog = null;
            public static DIYSQLBackground_Region_GetProcessedLog GetInstanceRegionGetProcessedLog()
            {
                if (instanceRegionGetProcessedLog == null)
                {
                    instanceRegionGetProcessedLog = new DIYSQLBackground_Region_GetProcessedLog();
                }
                return instanceRegionGetProcessedLog;
            }
            private DIYSQLBackground_Region_GetProcessedLog()
                : base(MainModel.GetInstance())
            {
            }

            string sql = "";

            public void SetSql(string sql)
            {
                this.sql = sql;
            }

            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[3];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                StatedTimes.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(package);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            private void fillData(Package package)
            {
                package.Content.GetParamInt();//iFileID
                int istime = package.Content.GetParamInt();
                int ietime = package.Content.GetParamInt();
                DateTime beginDate = JavaDate.GetDateTimeFromMilliseconds(istime * 1000L);
                DateTime endDate = JavaDate.GetDateTimeFromMilliseconds(ietime * 1000L);
                TimePeriod tp = new TimePeriod(beginDate, endDate);
                if (!StatedTimes.Contains(tp))
                {
                    StatedTimes.Add(tp);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Region_GetProcessedLog"; }
            }
        }

        private class DIYSQLBackground_Cell_GetResult : DIYSQLBase
        {
            private static DIYSQLBackground_Cell_GetResult instanceCellGetResult = null;
            public static DIYSQLBackground_Cell_GetResult GetInstanceCellGetResult()
            {
                if (instanceCellGetResult == null)
                {
                    instanceCellGetResult = new DIYSQLBackground_Cell_GetResult();
                }
                return instanceCellGetResult;
            }
            protected DIYSQLBackground_Cell_GetResult()
                : base(MainModel.GetInstance())
            {
            }

            protected int istime;
            protected int ietime;
            protected int iSubFuncID;
            protected string strProject;
            public List<BackgroundResult_Cell> ResultList = new List<BackgroundResult_Cell>();

            public void SetCondition(int istime, int ietime, int iSubFuncID, string strProject)
            {
                this.istime = istime;
                this.ietime = ietime;
                this.iSubFuncID = iSubFuncID;
                this.strProject = strProject;
            }

            protected override string getSqlTextString()
            {
                string sql = "exec sp_probchk_cell_result_get " + istime + "," + ietime + "," + iSubFuncID + ",'" + strProject + "'";
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[31];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_String;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_Int;
                rType[12] = E_VType.E_Int;
                rType[13] = E_VType.E_Int;
                rType[14] = E_VType.E_Int;
                rType[15] = E_VType.E_Int;
                rType[16] = E_VType.E_Float;
                rType[17] = E_VType.E_Int;
                rType[18] = E_VType.E_Float;
                rType[19] = E_VType.E_Float;
                rType[20] = E_VType.E_Float;
                rType[21] = E_VType.E_Float;
                rType[22] = E_VType.E_Float;
                rType[23] = E_VType.E_Float;
                rType[24] = E_VType.E_String;
                rType[25] = E_VType.E_String;
                rType[26] = E_VType.E_String;
                rType[27] = E_VType.E_String;
                rType[28] = E_VType.E_String;
                rType[29] = E_VType.E_String;
                rType[30] = E_VType.E_VARYBIN;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                ResultList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        BackgroundResult_Cell result = new BackgroundResult_Cell();
                        result.FillFrom(package.Content);
                        ResultList.Add(result);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    setProgressPercent(ref index, ref progress);
                }
            }

            public override string Name
            {
                get { return "DIYSQLBackground_Cell_GetResult"; }
            }
        }

    }

    public class BackgroundResult
    {
        public int SubFuncID { get; set; }
        public string SubFuncName { get; set; } = "";
        public int SN { get; set; }
        public string ProjectString { get; set; } = "";
        public int FileID { get; set; }
        public string FileName { get; set; } = "";
        public string CellName
        {
            get
            {
                string cellName = "";
                switch (CellType)
                {
                    case BackgroundCellType.GSM:
                        Cell cell = CellManager.GetInstance().GetNearestCell(JavaDate.GetDateTimeFromMilliseconds(ISTime * 1000L), (ushort?)LAC, (ushort?)CI,
                            (short?)BCCH, (byte?)BSIC, LongitudeMid, LatitudeMid);
                        if (cell != null)
                        {
                            cellName = cell.Name;
                        }
                        break;
                    case BackgroundCellType.TD:
                        TDCell tdCell = CellManager.GetInstance().GetNearestTDCell(JavaDate.GetDateTimeFromMilliseconds(ISTime * 1000L), (int?)LAC, (int?)CI,
                            (int?)BCCH, (int?)BSIC, LongitudeMid, LatitudeMid);
                        if (tdCell != null)
                        {
                            cellName = tdCell.Name;
                        }
                        break;
                    case BackgroundCellType.WCDMA:
                        WCell wCell = CellManager.GetInstance().GetNearestWCell(JavaDate.GetDateTimeFromMilliseconds(ISTime * 1000L), (int?)LAC, (int?)CI,
                            (int?)BCCH, (int?)BSIC, LongitudeMid, LatitudeMid);
                        if (wCell != null)
                        {
                            cellName = wCell.Name;
                        }
                        break;
                    case BackgroundCellType.CDMA:
                        CDCell cdCell = CellManager.GetInstance().GetNearestCDCell(JavaDate.GetDateTimeFromMilliseconds(ISTime * 1000L), (int?)LAC, (int?)CI,
                            (int?)BCCH, (int?)BSIC, LongitudeMid, LatitudeMid);
                        if (cdCell != null)
                        {
                            cellName = cdCell.Name;
                        }
                        break;
                    case BackgroundCellType.LTE:
                        LTECell lteCell = CellManager.GetInstance().GetNearestLTECell(JavaDate.GetDateTimeFromMilliseconds(ISTime * 1000L), (int?)LAC, (int?)CI,
                            (int?)BCCH, (int?)BSIC, LongitudeMid, LatitudeMid);
                        if (lteCell != null)
                        {
                            cellName = lteCell.Name;
                        }
                        break;
                    default:
                        break;
                }
                return cellName;
            }
        }
        public BackgroundCellType CellType { get; set; } = BackgroundCellType.GSM;
        /// <summary>
        /// 统计类型：按区域、按路段、按小区等，结果列表显示不同的列
        /// </summary>
        public BackgroundStatType StatType { get; set; } = BackgroundStatType.None;
        public int LAC { get; set; }
        public int CI { get; set; }
        public int BCCH { get; set; }
        public int BSIC { get; set; }
        public double LongitudeStart { get; set; }
        public double LatitudeStart { get; set; }
        public double LongitudeMid { get; set; }
        public double LatitudeMid { get; set; }
        public double LongitudeEnd { get; set; }
        public double LatitudeEnd { get; set; }
        public int iLongitudeStart
        {
            get { return (int)(10000000 * LongitudeStart); }
        }
        public int iLatitudeStart
        {
            get { return (int)(10000000 * LatitudeStart); }
        }
        public int iLongitudeMid
        {
            get { return (int)(10000000 * LongitudeMid); }
        }
        public int iLatitudeMid
        {
            get { return (int)(10000000 * LatitudeMid); }
        }
        public int iLongitudeEnd
        {
            get { return (int)(10000000 * LongitudeEnd); }
        }
        public int iLatitudeEnd
        {
            get { return (int)(10000000 * LatitudeEnd); }
        }
        public int ISTime { get; set; }
        public string DateTimeBeginString
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(ISTime * 1000L).ToString("yyyy-MM-dd HH:mm:ss"); }
        }
        public int IETime { get; set; }
        public string DateTimeEndString
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(IETime * 1000L).ToString("yyyy-MM-dd HH:mm:ss"); }
        }
        public int TimeLast
        {
            get { return IETime - ISTime; }
        }
        public double DistanceLast { get; set; }
        public int SampleCount { get; set; }
        public float RxLevMean { get; set; } = 255;
        public float RxLevMin { get; set; } = 255;
        public float RxLevMax { get; set; } = 255;
        public float RxQualMean { get; set; } = 255;
        public float RxQualMin { get; set; } = 255;
        public float RxQualMax { get; set; } = 255;
        public string RxLevMeanString
        {
            get { return RxLevMean == 255 ? "-" : RxLevMean.ToString(); }
        }
        public string RxLevMinString
        {
            get { return RxLevMin == 255 ? "-" : RxLevMin.ToString(); }
        }
        public string RxLevMaxString
        {
            get { return RxLevMax == 255 ? "-" : RxLevMax.ToString(); }
        }
        public string RxQualMeanString
        {
            get { return  RxQualMean == 255 ? "-" :  RxQualMean.ToString(); }
        }
        public string RxQualMinString
        {
            get { return  RxQualMin == 255 ? "-" :  RxQualMin.ToString(); }
        }
        public string RxQualMaxString
        {
            get { return  RxQualMax == 255 ? "-" :  RxQualMax.ToString(); }
        }
        public string DistrictDesc { get; set; } = "";
        public string RoadDesc { get; set; } = "";
        public string AreaDesc { get; set; } = "";
        public string AreaAgentDesc { get; set; } = "";
        public string GridDesc { get; set; } = "";
        public string CellIDDesc { get; set; } = "";
        public string StrDesc { get; set; } = "";
        public string ImageDesc { get; set; } = "";
        public void AddImageValue(byte[] obj)
        {
            otherParamList.Add(obj);
        }
        public void AddImageValue(int obj)
        {
            otherParamList.Add(obj);
        }
        public void AddImageValue(float obj)
        {
            otherParamList.Add(obj);
        }
        public void AddImageValue(string obj)
        {
            if (obj == null)
            {
                otherParamList.Add("");
            }
            else
            {
                otherParamList.Add(obj);
            }
        }
        public byte[] GetImageValueBytes()
        {
            return con.GetParamBytes();
        }

        public int GetImageValueInt()
        {
            return con.GetParamInt();
        }

        public float GetImageValueFloat()
        {
            return con.GetParamFloat();
        }

        public string GetImageValueString()
        {
            return con.GetParamString();
        }

        public string GetImageString()
        {
            return getImageString();
        }

        protected string getImageString()
        {
            con = new Content();
            con.PrepareAddParam();
            foreach (object otherParam in otherParamList)
            {
                if (otherParam is int)
                {
                    con.AddParam((int)otherParam);
                }
                else if (otherParam is float)
                {
                    con.AddParam((float)otherParam);
                }
                else if (otherParam is string)
                {
                    con.AddParam(otherParam as string);
                }
                else if (otherParam is byte[])
                {
                    byte[] bytes = otherParam as byte[];
                    con.AddParam((short)bytes.Length);
                    con.AddParam(bytes, bytes.Length);
                }
            }
            return "0x" + ByteOpHelper.ByteToOXStr(con.Buff);
        }

        public string SQLRoad
        {
            get
            {
                return SubFuncID + "," + FileID + ",'" + FileName + "'," + iLongitudeStart + "," + iLatitudeStart + "," +
                    iLongitudeMid + "," + iLatitudeMid + "," + iLongitudeEnd + "," + iLatitudeEnd + "," + ISTime + "," + IETime + "," +
                    DistanceLast + "," + SampleCount + "," + RxLevMean + "," + RxLevMin + "," + RxLevMax + "," +  RxQualMean + "," +
                     RxQualMin + "," +  RxQualMax + ",'" + RoadDesc + "','" + AreaDesc + "','" + AreaAgentDesc + "','" + GridDesc + "','" +
                    CellIDDesc + "','" + StrDesc + "'," + getImageString() + ",'" + ProjectString + "'";
            }
        }

        public string SQLRegion
        {
            get
            {
                return SubFuncID + "," + ISTime + "," + IETime + "," + iLongitudeMid + "," + iLatitudeMid + "," +
                    SampleCount + "," + RxLevMean + "," + RxLevMin + "," + RxLevMax + "," +  RxQualMean + "," +
                     RxQualMin + "," +  RxQualMax + ",'" + RoadDesc + "','" + AreaDesc + "','" + AreaAgentDesc + "','" + GridDesc + "','" +
                    CellIDDesc + "','" + StrDesc + "'," + getImageString() + ",'" + ProjectString + "'";
            }
        }

        public string SQLCell
        {
            get
            {
                return SubFuncID + "," + FileID + ",'" + FileName + "'," + (int)CellType + "," + LAC + "," + CI + "," + BCCH + "," + BSIC + "," +
                    ISTime + "," + IETime + "," + iLongitudeStart + "," + iLatitudeStart + "," + iLongitudeMid + "," + iLatitudeMid + "," +
                    iLongitudeEnd + "," + iLatitudeEnd + "," + DistanceLast + "," + SampleCount + "," + RxLevMean + "," + RxLevMin + "," +
                    RxLevMax + "," +  RxQualMean + "," +  RxQualMin + "," +  RxQualMax + ",'" + RoadDesc + "','" + AreaDesc + "','" +
                    AreaAgentDesc + "','" + GridDesc + "','" + CellIDDesc + "','" + StrDesc + "'," + getImageString() + ",'" + ProjectString + "'";
            }
        }

        public virtual void FillFrom(Content content)
        {
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            if (StatType == BackgroundStatType.Cell_Region || StatType == BackgroundStatType.Cell_Road)
            {
                return false;
            }
            if (LongitudeMid >= x1 && LongitudeMid <= x2 && LatitudeMid >= y1 && LatitudeMid <= y2)
            {
                return true;
            }
            if (StatType == BackgroundStatType.Road)
            {
                if (LongitudeStart >= x1 && LongitudeStart <= x2 && LatitudeStart >= y1 && LatitudeStart <= y2)
                {
                    return true;
                }
                if (LongitudeEnd >= x1 && LongitudeEnd <= x2 && LatitudeEnd >= y1 && LatitudeEnd <= y2)
                {
                    return true;
                }
            }
            return false;
        }

        public string Desc
        {
            get
            {
                return getDesc();
            }
        }

        private string getDesc()
        {
            StringBuilder sb = new StringBuilder();
            if (StatType != BackgroundStatType.None)
            {
                if (StatType == BackgroundStatType.Road || StatType == BackgroundStatType.Cell_Road)
                {
                    sb.Append("文件名：");
                    sb.Append(FileName);
                    sb.Append("\r\n");
                }
                getCellInfo(sb);
                if (StatType == BackgroundStatType.Road || StatType == BackgroundStatType.Cell_Road)
                {
                    sb.Append("起始经度：");
                    sb.Append(LongitudeStart);
                    sb.Append("\r\n");

                    sb.Append("起始纬度：");
                    sb.Append(LatitudeStart);
                    sb.Append("\r\n");
                }
                sb.Append("中心经度：");
                sb.Append(LongitudeMid);
                sb.Append("\r\n");

                sb.Append("中心纬度：");
                sb.Append(LatitudeMid);
                sb.Append("\r\n");
                if (StatType == BackgroundStatType.Road || StatType == BackgroundStatType.Cell_Road)
                {
                    sb.Append("结束经度：");
                    sb.Append(LongitudeEnd);
                    sb.Append("\r\n");

                    sb.Append("结束纬度：");
                    sb.Append(LatitudeEnd);
                    sb.Append("\r\n");
                }
                sb.Append("起始时间：");
                sb.Append(DateTimeBeginString);
                sb.Append("\r\n");

                sb.Append("结束时间：");
                sb.Append(DateTimeEndString);
                sb.Append("\r\n");
                if (StatType == BackgroundStatType.Road || StatType == BackgroundStatType.Cell_Road)
                {
                    sb.Append("持续时长：");
                    sb.Append(TimeLast);
                    sb.Append("\r\n");

                    sb.Append("持续距离：");
                    sb.Append(DistanceLast);
                    sb.Append("\r\n");
                }
                sb.Append("采样点数：");
                sb.Append(SampleCount);
                sb.Append("\r\n");

                sb.Append("平均场强：");
                sb.Append(RxLevMeanString);
                sb.Append("\r\n");

                sb.Append("最小场强：");
                sb.Append(RxLevMinString);
                sb.Append("\r\n");

                sb.Append("最大场强：");
                sb.Append(RxLevMaxString);
                sb.Append("\r\n");

                sb.Append("平均质量：");
                sb.Append(RxQualMeanString);
                sb.Append("\r\n");

                sb.Append("最小质量：");
                sb.Append(RxQualMinString);
                sb.Append("\r\n");

                sb.Append("最大质量：");
                sb.Append(RxQualMaxString);
                sb.Append("\r\n");

                sb.Append("道路：");
                sb.Append(RoadDesc);
                sb.Append("\r\n");

                sb.Append("片区：");
                sb.Append(AreaDesc);
                sb.Append("\r\n");

                sb.Append("网格：");
                sb.Append(GridDesc);
                sb.Append("\r\n");

                sb.Append("代维片区：");
                sb.Append(AreaAgentDesc);
                sb.Append("\r\n");

                sb.Append("涉及小区：");
                sb.Append(CellIDDesc);
                sb.Append("\r\n");

                sb.Append("备注：");
                sb.Append(StrDesc);
                sb.Append("\r\n");
            }
            sb.Append("专题功能附加信息：");
            sb.Append(ImageDesc);
            sb.Append("\r\n");

            return sb.ToString();
        }

        private void getCellInfo(StringBuilder sb)
        {
            if (StatType == BackgroundStatType.Cell_Road || StatType == BackgroundStatType.Cell_Region)
            {
                sb.Append("小区名：");
                sb.Append(CellName);
                sb.Append("\r\n");

                sb.Append("LAC：");
                sb.Append(LAC);
                sb.Append("\r\n");

                sb.Append("CI：");
                sb.Append(CI);
                sb.Append("\r\n");

                sb.Append("频点：");
                sb.Append(BCCH);
                sb.Append("\r\n");

                sb.Append("色码(扰码)：");
                sb.Append(BSIC);
                sb.Append("\r\n");
            }
        }

        public object ImageResultObj { get; set; } = new object();      
        List<object> otherParamList { get; set; } = new List<object>();
        protected Content con = new Content();

        /// <summary>
        /// 按文件ID和开始时间升序排列
        /// </summary>
        public static ComparerFileIdAndISTime ComparerByFileIdAndISTimeAsc { get; set; } = new ComparerFileIdAndISTime();
        public class ComparerFileIdAndISTime : IComparer<BackgroundResult>
        {
            public int Compare(BackgroundResult x, BackgroundResult y)
            {
                if (x.FileID == y.FileID)
                {
                    return x.ISTime - y.ISTime;
                }
                return x.FileID - y.FileID;
            }
        }

        /// <summary>
        /// 按开始时间降叙排列
        /// </summary>
        public static ComparerISTime ComparerByISTimeDesc { get; set; } = new ComparerISTime();
        public class ComparerISTime : IComparer<BackgroundResult>
        {
            public int Compare(BackgroundResult x, BackgroundResult y)
            {
                return y.ISTime - x.ISTime;
            }
        }
    }

    public class BackgroundResult_Road : BackgroundResult
    {
        public override void FillFrom(Content content)
        {
            SubFuncID = content.GetParamInt();
            FileID = content.GetParamInt();
            FileName = content.GetParamString();
            LongitudeStart = 0.0000001 * content.GetParamInt();
            LatitudeStart = 0.0000001 * content.GetParamInt();
            LongitudeMid = 0.0000001 * content.GetParamInt();
            LatitudeMid = 0.0000001 * content.GetParamInt();
            LongitudeEnd = 0.0000001 * content.GetParamInt();
            LatitudeEnd = 0.0000001 * content.GetParamInt();
            ISTime = content.GetParamInt();
            IETime = content.GetParamInt();
            DistanceLast = content.GetParamFloat();
            SampleCount = content.GetParamInt();
            RxLevMean = content.GetParamFloat();
            RxLevMin = content.GetParamFloat();
            RxLevMax = content.GetParamFloat();
            RxQualMean = content.GetParamFloat();
            RxQualMin = content.GetParamFloat();
            RxQualMax = content.GetParamFloat();
            RoadDesc = content.GetParamString();
            AreaDesc = content.GetParamString();
            AreaAgentDesc = content.GetParamString();
            GridDesc = content.GetParamString();
            CellIDDesc = content.GetParamString();
            StrDesc = content.GetParamString();
            byte[] cc = content.GetParamBytes();
            con = new Content();
            con.CopyBuffFrom(cc);
            con.PrepareGetParam();
        }
    }

    public class BackgroundResult_Region : BackgroundResult
    {
        public override void FillFrom(Content content)
        {
            SubFuncID = content.GetParamInt();
            ISTime = content.GetParamInt();
            IETime = content.GetParamInt();
            LongitudeMid = 0.0000001 * content.GetParamInt();
            LatitudeMid = 0.0000001 * content.GetParamInt();
            SampleCount = content.GetParamInt();
            RxLevMean = content.GetParamFloat();
            RxLevMin = content.GetParamFloat();
            RxLevMax = content.GetParamFloat();
            RxQualMean = content.GetParamFloat();
            RxQualMin = content.GetParamFloat();
            RxQualMax = content.GetParamFloat();
            RoadDesc = content.GetParamString();
            AreaDesc = content.GetParamString();
            AreaAgentDesc = content.GetParamString();
            GridDesc = content.GetParamString();
            CellIDDesc = content.GetParamString();
            StrDesc = content.GetParamString();
            byte[] cc = content.GetParamBytes();
            con = new Content();
            con.CopyBuffFrom(cc);
            con.PrepareGetParam();
        }
    }

    public class BackgroundResult_Cell : BackgroundResult
    {
        public override void FillFrom(Content content)
        {
            SubFuncID = content.GetParamInt();
            FileID = content.GetParamInt();
            FileName = content.GetParamString();
            CellType = (BackgroundCellType)content.GetParamInt();
            LAC = content.GetParamInt();
            CI = content.GetParamInt();
            BCCH = content.GetParamInt();
            BSIC = content.GetParamInt();
            ISTime = content.GetParamInt();
            IETime = content.GetParamInt();
            LongitudeStart = 0.0000001 * content.GetParamInt();
            LatitudeStart = 0.0000001 * content.GetParamInt();
            LongitudeMid = 0.0000001 * content.GetParamInt();
            LatitudeMid = 0.0000001 * content.GetParamInt();
            LongitudeEnd = 0.0000001 * content.GetParamInt();
            LatitudeEnd = 0.0000001 * content.GetParamInt();
            DistanceLast = content.GetParamFloat();
            SampleCount = content.GetParamInt();
            RxLevMean = content.GetParamFloat();
            RxLevMin = content.GetParamFloat();
            RxLevMax = content.GetParamFloat();
            RxQualMean = content.GetParamFloat();
            RxQualMin = content.GetParamFloat();
            RxQualMax = content.GetParamFloat();
            RoadDesc = content.GetParamString();
            AreaDesc = content.GetParamString();
            AreaAgentDesc = content.GetParamString();
            GridDesc = content.GetParamString();
            CellIDDesc = content.GetParamString();
            StrDesc = content.GetParamString();
            byte[] cc = content.GetParamBytes();
            con = new Content();
            con.CopyBuffFrom(cc);
            con.PrepareGetParam();
        }
    }
    
    /// <summary>
    /// 专题类型，按业务分
    /// </summary>
    public enum BackgroundFuncType
    {
        GSM业务专题,
        TD业务专题,
        GSM扫频,
        WCDMA扫频,
        TD扫频,
        LTE业务专题,
        LTE扫频专题,
        LTE感知,
        WCDMA业务专题,
        NBIot业务专题,
        NR业务专题,
        None = 100
    }

    /// <summary>
    /// 专题子类型，按问题类型分
    /// </summary>
    public enum BackgroundSubFuncType
    {
        覆盖,
        频点,
        干扰,
        质量,
        切换,
        其他,
        小区集,
        单站验收
    }

    /// <summary>
    /// 小区类型，按小区统计的结果获取不同类型小区
    /// </summary>
    public enum BackgroundCellType
    {
        GSM = 1,
        TD,
        WCDMA,
        CDMA,
        LTE,
        NR,
        None = 100,
    }

    /// <summary>
    /// 统计类型：按区域、按路段、按小区等，结果列表显示不同的列
    /// </summary>
    public enum BackgroundStatType
    {
        Road,
        Region,
        Cell_Road,
        Cell_Region,
        Cell_Simple,

        None = 100
    }
}
