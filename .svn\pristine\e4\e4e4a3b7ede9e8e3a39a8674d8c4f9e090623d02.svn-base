﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model.CellParam;
using MasterCom.RAMS.Model.CellParam.QueryByRegion;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class QueryCellSignParamByRegion : QueryBase
    {
        public QueryCellSignParamByRegion(MainModel mm):base(mm)
        {
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get { return "按区域查询GSM小区参数"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20029, this.Name);
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            getCellSigns();
            if (cellSignCellDic.Count == 0)
            {
                MessageBox.Show("该区域未能关联到小区！");
                return;
            }
            List<CellParamInfo> results = queryParam(cellSignCellDic);
            if (results.Count == 0)
            {
                MessageBox.Show("所选时间段未能关联到小区参数！");
            }
            else
            {
                showResultForm(results);
            }
        }
        protected virtual void showResultForm(List<CellParamInfo> cellParamResults)
        {
            CellSignParamForm frm = MainModel.GetObjectFromBlackboard(typeof(CellSignParamForm).FullName) as CellSignParamForm;
            if (frm==null)
            {
                frm = new CellSignParamForm(MainModel);
            }
            frm.FillData(cellParamResults,tableColsDic);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm); 
            }
            frm.BringToFront();
            cellParamResults.Clear();
        }
        Dictionary<CellParamTable, List<CellParamColumn>> tableColsDic;
        protected List<CellParamInfo> queryParam(Dictionary<CellSign, object> cellSignCellDic)
        {
            StringBuilder cellSb = new StringBuilder();
            Dictionary<int, CellSign> cellDic = new Dictionary<int, CellSign>();
            foreach (CellSign cellSign in cellSignCellDic.Keys)
            {
                if (!cellDic.ContainsKey(cellSign.SignID))
                {
                    cellDic.Add(cellSign.SignID, cellSign);
                }
                cellSb.Append(cellSign.SignID);
                cellSb.Append(',');
            }
            cellSb.Remove(cellSb.Length - 1, 1);
            tableColsDic = getTableColsDic(paramCols);
            Dictionary<int, CellParamInfo> idParamDic = new Dictionary<int, CellParamInfo>();
            foreach (KeyValuePair<CellParamTable, List<CellParamColumn>> pair in tableColsDic)
            {
                CellParamTable table = pair.Key;
                StringBuilder sb = new StringBuilder("select ");
                sb.Append(table.CellIDForeignKey);
                sb.Append(",");
                sb.Append(table.CheckDateFieldName);
                foreach (CellParamColumn col in pair.Value)
                {
                    sb.Append(",");
                    sb.Append(col.Name);
                }
                sb.Append(" from ");
                sb.Append(table.FullName);
                sb.Append(" where ");
                sb.Append(table.CheckDateFieldName);
                sb.Append(" between '");
                sb.Append(dtBegin.ToString("yyyy-MM-dd HH:mm:ss"));
                sb.Append("' and '");
                sb.Append(dtEnd.ToString("yyyy-MM-dd HH:mm:ss"));
                sb.Append("' and ");
                sb.Append(table.CellIDForeignKey);
                sb.Append(" in(");
                sb.Append(cellSb.ToString());
                sb.Append(");");
                using (SqlConnection conn = new SqlConnection(CellParamCfgManager.GetInstance().DBConnectionStr))
                {
                    SqlCommand command = new SqlCommand(sb.ToString(), conn);
                    conn.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        Dictionary<CellParamColumn, object> paramValueDic = new Dictionary<CellParamColumn, object>();
                        foreach (CellParamColumn col in pair.Value)
                        {
                            object objValue = reader[col.Name];
                            paramValueDic.Add(col, objValue);
                        }
                        DateTime date = DateTime.Parse(reader[table.CheckDateFieldName].ToString());
                        int id = int.Parse(reader[table.CellIDForeignKey].ToString());
                        CellParamInfo cellParam = null;
                        if (!idParamDic.TryGetValue(id, out cellParam))
                        {
                            CellSign curCell = cellDic[id];
                            cellParam = new CellParamInfo(cellSignCellDic[curCell], curCell);
                            idParamDic.Add(id, cellParam);
                        }
                        cellParam.AddRecord(date, table, paramValueDic);
                    }
                }
            }
            List<CellParamInfo> results = new List<CellParamInfo>(idParamDic.Values);
            return results;
        }

        private Dictionary<CellParamTable, List<CellParamColumn>> getTableColsDic(List<CellParamColumn> cols)
        {
            Dictionary<CellParamTable, List<CellParamColumn>> dic = new Dictionary<CellParamTable, List<CellParamColumn>>();
            foreach (CellParamColumn col in cols)
            {
                if (dic.ContainsKey(col.Table))
                {
                    dic[col.Table].Add(col);
                }
                else
                {
                    List<CellParamColumn> list = new List<CellParamColumn>();
                    list.Add(col);
                    dic.Add(col.Table, list);
                }
            }
            return dic;
        }

        protected Dictionary<CellSign, object> cellSignCellDic = new Dictionary<CellSign, object>();
        protected virtual void getCellSigns()
        {
            cellSignCellDic.Clear();
            List<Cell> cells = MainModel.MainForm.GetMapForm().GetCellLayer().CellsInCurrentView;
            if (cells == null || cells.Count==0)
            {
                cells = MainModel.MainForm.GetMapForm().GetCellLayer().GetCellsByRegion(MainModel.SearchGeometrys.GeoOp);
            }
            else
            {
                List<Cell> regionCells = new List<Cell>();
                foreach (Cell cell in cells)
                {
                    if (MainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude,cell.Latitude))
                    {
                        regionCells.Add(cell);
                    }
                }
                cells = regionCells;
            }
            if (cells != null)
            {
                addCellSignCellDic(cells);
            }
        }

        private void addCellSignCellDic(List<Cell> cells)
        {
            TimePeriod period = new TimePeriod(dtBegin, dtEnd);
            foreach (Cell cell in cells)
            {
                List<GSMCellSign> signs = CellSignManager.GetInstance().GetCellSign(period, cell);
                if (signs != null && signs.Count > 0)
                {
                    foreach (CellSign sign in signs)
                    {
                        cellSignCellDic.Add(sign, cell);
                    }
                }
            }
        }

        protected DateTime dtBegin = DateTime.Now.AddMonths(-1);
        protected DateTime dtEnd = DateTime.Now;
        private List<CellParamColumn> paramCols = new List<CellParamColumn>();
        private bool getCondition()
        {
            CellSignParamConditionDlg dlg = new CellSignParamConditionDlg(dtBegin, dtEnd);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dtBegin = dlg.BeginTime;
                dtEnd = dlg.EndTime;
            }
            else
            {
                return false;
            }
            CellParamSettingDlg paramDlg = new CellParamSettingDlg(paramCols);
            if (paramDlg.ShowDialog() == DialogResult.OK)
            {
                paramCols = paramDlg.DisplayCellParamCols;
            }
            else
            {
                return false;
            }
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return searchGeometrys.IsSelectRegion();
        }
    }
}
