﻿<?xml version="1.0" encoding="UTF-8"?>
<!-- 编辑使用 XMLSpy v2006 U (http://www.altova.com) 由 any (any) -->
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">基础信息</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试时间 kTimeValue</Item>
						<Item typeName="String" key="FName">kTimeValue</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">设备类型 kEqpId</Item>
						<Item typeName="String" key="FName">kEqpId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">文件类型 kFileTypeId</Item>
						<Item typeName="String" key="FName">kFileTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">业务类型 kSvTypeId</Item>
						<Item typeName="String" key="FName">kSvTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">运营商类型 kCarrierId</Item>
						<Item typeName="String" key="FName">kCarrierId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">项目类型 kProjId</Item>
						<Item typeName="String" key="FName">kProjId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">所属轮次 kRound</Item>
						<Item typeName="String" key="FName">kRound</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">所属地域类型 kAreaTypeId</Item>
						<Item typeName="String" key="FName">kAreaTypeId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">所属地域ID kAreaId</Item>
						<Item typeName="String" key="FName">kAreaId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">所属代维公司 kDwId</Item>
						<Item typeName="String" key="FName">kDwId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">衰减值 kDbValue</Item>
						<Item typeName="String" key="FName">kDbValue</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">LAC kLAC</Item>
						<Item typeName="String" key="FName">kLAC</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">CI kCI</Item>
						<Item typeName="String" key="FName">kCI</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">GSM小区名 kCellName</Item>
						<Item typeName="String" key="FName">kCellName</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">TD小区名 kTDCellName</Item>
						<Item typeName="String" key="FName">kTDCellName</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">W小区名 kWCellName</Item>
						<Item typeName="String" key="FName">kWCellName</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">LTE小区名 kLTECellName</Item>
						<Item typeName="String" key="FName">kLTECellName</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">地市 kDistrictId</Item>
						<Item typeName="String" key="FName">kDistrictId</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
