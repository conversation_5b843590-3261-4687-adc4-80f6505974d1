﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.CQT
{
    public class ZTDIYUserDownLoadAna : DIYEventQuery
    {
        public ZTDIYUserDownLoadAna(MainModel mainModel)
            :base(mainModel)
        {
            isAddEventToDTDataManager = false;
        }
        public Dictionary<int, Dictionary<int, CQTPointTem>> cqtCityNameMultiDimensionDic { get; set; }
        Dictionary<DownloadEventKey, DownloadEevetInfo> downloadEvtDic = new Dictionary<DownloadEventKey, DownloadEevetInfo>();
        public override string Name
        {
            get { return "TD数据按用户速率统计(按区域)"; }
        }

        public override string IconName
        {
            get { throw new NotImplementedException("The method or operation is not implemented."); }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13033, this.Name);
        }

        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            //selectedEventIDs.Add(57);//下载开始
            selectedEventIDs.Add(58);//下载成功
            selectedEventIDs.Add(59);//下载失败
            selectedEventIDs.Add(213);//GSM下载成功
            Condition.EventIDs = selectedEventIDs;
            return true;
        }

        protected override bool isValidPoint(double jd, double wd)
        {
            try
            {
                if (MainModel.SearchGeometrys.Region == null) //不选区域按全区查询
                {
                    return true;
                }
                else//选区域按区域查询
                {
                    return Condition.Geometorys == null || Condition.Geometorys.GeoOp.Contains(jd, wd);
                }
              
            }
            catch
            {
                return false;
            }
        }

        protected override void doWithDTData(Event evt)
        {
            DownloadEventKey evtKey = new DownloadEventKey();
            evtKey.StrCity = DistrictManager.GetInstance().getDistrictName(evt.DistrictID);
            evtKey.StrTestAddr = getTestAddr(evt.FileName);
            evtKey.StrTestSite = getTestSite(evt.FileName);
            evtKey.StrUserType = getUserType(evt.FileName);
            evtKey.DTime = Convert.ToDateTime(string.Format("{0:yyyy-MM-dd} 00:00:00", evt.DateTime));
            evtKey.StrCoverType = getCoverType(evt.DistrictID, evt.AreaID);
            string strTestPort = getTestPort(evt.FileName);

            if (evtKey.StrCity != "" && evtKey.StrTestAddr != "" && evtKey.StrTestSite != "" && evtKey.StrUserType != "" && strTestPort != "" &&
               (evt.ServiceType == 2 || evt.ServiceType == 3 || evt.ServiceType == 5 || evt.ServiceType == 18 || evt.ServiceType == 7 || evt.ServiceType == 9 || evt.ServiceType == 11 || evt.ServiceType == 15))
            {
                DownloadEevetInfo evtInfo = new DownloadEevetInfo();
                if (downloadEvtDic.ContainsKey(evtKey))
                    evtInfo = downloadEvtDic[evtKey];

                evtInfo.StrCity = evtKey.StrCity;
                evtInfo.StrTestAddr = evtKey.StrTestAddr;
                evtInfo.StrTestSite = evtKey.StrTestSite;
                evtInfo.StrUserType = evtKey.StrUserType;
                evtInfo.DTime = evtKey.DTime;
                evtInfo.StrCoverType = evtKey.StrCoverType;

                dealWitData(evt, evtKey, strTestPort, evtInfo);

                downloadEvtDic[evtKey] = evtInfo;
            }
        }

        private static void dealWitData(Event evt, DownloadEventKey evtKey, string strTestPort, DownloadEevetInfo evtInfo)
        {
            if (judgePort2(evt, evtKey, strTestPort))
            {
                if (evt.ID == 58)
                {
                    evtInfo.ISuccNum += 1 + int.Parse(evt["Value9"].ToString());

                    evtInfo.FDownSize += (float)int.Parse(evt["Value1"].ToString()) * 8 / 1024;
                    evtInfo.FDownTime += (float)int.Parse(evt["Value4"].ToString()) / 1000;
                    evtInfo.FHDowmTime += (float)int.Parse(evt["Value6"].ToString()) / 1000;
                    evtInfo.FRDownTime += (float)int.Parse(evt["Value2"].ToString()) / 1000;
                }
                else if (evt.ID == 59)
                {
                    evtInfo.IFailNum += 1 + int.Parse(evt["Value9"].ToString());

                    evtInfo.FDownSize += (float)int.Parse(evt["Value1"].ToString()) * 8 / 1024;
                    evtInfo.FDownTime += (float)int.Parse(evt["Value4"].ToString()) / 1000;
                    evtInfo.FHDowmTime += (float)int.Parse(evt["Value6"].ToString()) / 1000;
                    evtInfo.FRDownTime += (float)int.Parse(evt["Value2"].ToString()) / 1000;
                }
                else if (evt.ID == 213)
                {
                    evtInfo.FGDownTime += ((float)int.Parse(evt["Value2"].ToString()) + (float)int.Parse(evt["Value4"].ToString())) / 1000;
                }
            }
            else if (judgePort3(evt, evtKey, strTestPort))
            {
                if (evt.ID == 58 || evt.ID == 59)
                {
                    evtInfo.FP3Size += (float)int.Parse(evt["Value1"].ToString()) * 8 / 1024;
                    evtInfo.FP3Time += (float)int.Parse(evt["Value4"].ToString()) / 1000;
                }
            }
            else if (judgePort4(evt, evtKey, strTestPort))
            {
                if (evt.ID == 58 || evt.ID == 59)
                {
                    evtInfo.FP4Size += (float)int.Parse(evt["Value1"].ToString()) * 8 / 1024;
                    evtInfo.FP4Time += (float)int.Parse(evt["Value4"].ToString()) / 1000;
                }
            }
            else
            { 
                //
            }
        }

        private static bool judgePort2(Event evt, DownloadEventKey evtKey, string strTestPort)
        {
            return (evtKey.StrUserType == "单用户" && ((evt.CarrierType == 1 && (evt.ServiceType == 2 || evt.ServiceType == 3)) || evt.ServiceType == 5 || evt.ServiceType == 18)) ||
                           (evtKey.StrUserType == "多用户" && strTestPort == "Port2") ||
                           (evtKey.StrUserType == "三网对比" && ((evt.CarrierType == 1 && (evt.ServiceType == 2 || evt.ServiceType == 3)) || evt.ServiceType == 5 || evt.ServiceType == 18));
        }

        private static bool judgePort3(Event evt, DownloadEventKey evtKey, string strTestPort)
        {
            return (evtKey.StrUserType == "多用户" && strTestPort == "Port3") ||
                                (evtKey.StrUserType == "三网对比" && (evt.ServiceType == 7 || evt.ServiceType == 9));
        }

        private static bool judgePort4(Event evt, DownloadEventKey evtKey, string strTestPort)
        {
            return ((evtKey.StrUserType == "多用户" && strTestPort == "Port4")
                                || (evtKey.StrUserType == "三网对比"
                                    && ((evt.CarrierType == 2
                                        && (evt.ServiceType == 2 || evt.ServiceType == 3)) || evt.ServiceType == 11 || evt.ServiceType == 15)));
        }

        protected override void query()
        {
            downloadEvtDic = new Dictionary<DownloadEventKey, DownloadEevetInfo>();
            if (!prepareAskWhatEvent())
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = null;
            loadCQTPointInfo();

            if (MainModel.User.DBID == -1) //省用户执行
            {
                foreach (int DistrictID in condition.DistrictIDs)
                {
                    MainModel.DTDataManager.Clear();
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedEvents.Clear();
                    MainModel.IsDrawEventResult = false;
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                    MainModel.SelectedMessage = null;

                    clientProxy = new ClientProxy();
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    WaitBox.Show("开始统计[" + DistrictManager.GetInstance().getDistrictName(DistrictID) + "]数据...", queryInThread, clientProxy);
                    clientProxy.Close();
                }
            }
            else
            {
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.SelectedMessage = null;

                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                clientProxy.Close();

            }

            dealDownladEvent();
        }

        private void dealDownladEvent()
        {
            List<DownloadEevetInfo> multiDeList = new List<DownloadEevetInfo>();
            List<DownloadEevetInfo> onlyAndCompDeList = new List<DownloadEevetInfo>();

            foreach (DownloadEventKey deKey in downloadEvtDic.Keys)
            {
                if (deKey.StrUserType == "多用户")
                    multiDeList.Add(downloadEvtDic[deKey]);
                else
                    onlyAndCompDeList.Add(downloadEvtDic[deKey]);
            }

            Dictionary<string, List<DownloadEevetInfo>> cityCompDic = getCityCompDic(onlyAndCompDeList);

            setOnlyAndCompDeListData(onlyAndCompDeList, cityCompDic);

            List<List<NPOIRow>> nrDatasList = getNrDatasList(multiDeList, onlyAndCompDeList);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("TD加载");
            sheetNames.Add("三网对比测试(单用户)");

            if (DialogResult.Yes == XtraMessageBox.Show("是否不显示结果窗口，直接导出Excel？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information))
            {
                ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
            }
            else
            {
                object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(UserDownLoadForm).FullName);
                UserDownLoadForm userDownLoadForm = obj == null ? null : obj as UserDownLoadForm;
                if (userDownLoadForm == null || userDownLoadForm.IsDisposed)
                {
                    userDownLoadForm = new UserDownLoadForm(MainModel);
                }
                userDownLoadForm.FillData(multiDeList, onlyAndCompDeList, nrDatasList, sheetNames);
                if (!userDownLoadForm.Visible)
                {
                    userDownLoadForm.Show(MainModel.MainForm);
                }
            }
        }

        private static Dictionary<string, List<DownloadEevetInfo>> getCityCompDic(List<DownloadEevetInfo> onlyAndCompDeList)
        {
            Dictionary<string, List<DownloadEevetInfo>> cityCompDic = new Dictionary<string, List<DownloadEevetInfo>>();
            foreach (DownloadEevetInfo dei in onlyAndCompDeList)
            {
                if (cityCompDic.ContainsKey(dei.StrCity))
                    cityCompDic[dei.StrCity].Add(dei);
                else
                {
                    List<DownloadEevetInfo> deiList = new List<DownloadEevetInfo>();
                    deiList.Add(dei);
                    cityCompDic.Add(dei.StrCity, deiList);
                }
            }

            return cityCompDic;
        }

        private static void setOnlyAndCompDeListData(List<DownloadEevetInfo> onlyAndCompDeList, Dictionary<string, List<DownloadEevetInfo>> cityCompDic)
        {
            onlyAndCompDeList.Clear();//先清空

            foreach (string strCity in cityCompDic.Keys)
            {
                List<DownloadEevetInfo> cityList = cityCompDic[strCity];
                cityList.Sort(DownloadEevetInfo.CompareByTestAddr);

                Dictionary<string, List<DownloadEevetInfo>> userDic = new Dictionary<string, List<DownloadEevetInfo>>();
                foreach (DownloadEevetInfo dei2 in cityList)
                {
                    if (userDic.ContainsKey(dei2.StrTestAddr))
                        userDic[dei2.StrTestAddr].Add(dei2);
                    else
                    {
                        List<DownloadEevetInfo> dei2List = new List<DownloadEevetInfo>();
                        dei2List.Add(dei2);
                        userDic.Add(dei2.StrTestAddr, dei2List);
                    }
                }

                foreach (string strUserType in userDic.Keys)
                {
                    List<DownloadEevetInfo> dei3List = userDic[strUserType];
                    dei3List.Sort(DownloadEevetInfo.CompareByUserType);

                    foreach (DownloadEevetInfo dei3 in dei3List)
                    {
                        onlyAndCompDeList.Add(dei3);//后添加
                    }
                }
            }
        }

        private List<List<NPOIRow>> getNrDatasList(List<DownloadEevetInfo> multiDeList, List<DownloadEevetInfo> onlyAndCompDeList)
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            List<NPOIRow> data2s = new List<NPOIRow>();
            List<object> col2s = new List<object>();
            NPOIRow nr2 = new NPOIRow();

            #region EXCEL-SHEET1列表构造
            cols.Add("序号");
            cols.Add("日期");
            cols.Add("城市");
            cols.Add("测试地点");
            cols.Add("测试位置");
            cols.Add("覆盖属性");
            cols.Add("FTP下载尝试次数");
            cols.Add("FTP下载成功次数");
            cols.Add("掉线次数");
            cols.Add("总下载数据量(KBytes)");
            cols.Add("掉线率(%)");
            cols.Add("总下载时间(s)");
            cols.Add("应用层平均速率(kbps)");
            cols.Add("HSDPA占用时长(s)");
            cols.Add("R4占用时长(s)");
            cols.Add("GPRS/EDGE占用时长(s)");
            cols.Add("端口3总下载量(KBytes)");
            cols.Add("端口3总下载时间(s)");
            cols.Add("端口3平均速率");
            cols.Add("端口4总下载量(KBytes)");
            cols.Add("端口4总下载时间(s)");
            cols.Add("端口4平均速率");

            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            #region EXCEL-SHEET2列表构造
            col2s.Add("序号");
            col2s.Add("日期");
            col2s.Add("城市");
            col2s.Add("测试地点");
            col2s.Add("测试位置");
            col2s.Add("覆盖属性");
            col2s.Add("FTP下载尝试次数");
            col2s.Add("FTP下载成功次数");
            col2s.Add("掉线次数");
            col2s.Add("总下载数据量(KBytes)");
            col2s.Add("掉线率(%)");
            col2s.Add("总下载时间(s)");
            col2s.Add("应用层平均速率(kbps)");
            col2s.Add("HSDPA占用时长(s)");
            col2s.Add("R4占用时长(s)");
            col2s.Add("GPRS/EDGE占用时长(s)");
            col2s.Add("电信总下载量(KBytes)");
            col2s.Add("电信总下载时间(s)");
            col2s.Add("电信平均速率");
            col2s.Add("联通总下载量(KBytes)");
            col2s.Add("联通总下载时间(s)");
            col2s.Add("联通平均速率");

            nr2.cellValues = col2s;
            data2s.Add(nr2);
            #endregion

            try
            {
                addDownloadEevetInfoRow(multiDeList, datas);
                addDownloadEevetInfoRow(onlyAndCompDeList, data2s);
            }
            catch
            {
                //continue
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(data2s);
            return nrDatasList;
        }

        private void addDownloadEevetInfoRow(List<DownloadEevetInfo> list, List<NPOIRow> datas)
        {
            int idx = 1;
            foreach (DownloadEevetInfo deInfo in list)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                objs.Add(idx.ToString());
                deInfo.Sn = idx;
                objs.Add(string.Format("{0:yyyy-MM-dd}", deInfo.DTime));
                objs.Add(deInfo.StrCity);
                objs.Add(deInfo.StrTestAddr);
                objs.Add(deInfo.StrTestSite);
                objs.Add(deInfo.StrCoverType);
                objs.Add(deInfo.IAttNum.ToString());
                objs.Add(deInfo.ISuccNum.ToString());
                objs.Add(deInfo.IFailNum.ToString());
                objs.Add(deInfo.FDownSize.ToString());
                objs.Add(deInfo.FDropRate);
                objs.Add(deInfo.FDownTime.ToString());
                objs.Add(deInfo.FAppRate.ToString());
                objs.Add(deInfo.FHDowmTime.ToString());
                objs.Add(deInfo.FRDownTime.ToString());
                objs.Add(deInfo.FGDownTime.ToString());
                objs.Add(deInfo.FP3Size.ToString("0.00"));
                objs.Add(deInfo.FP3Time.ToString("0.00"));
                objs.Add(deInfo.FP3AppRate.ToString());
                objs.Add(deInfo.FP4Size.ToString("0.00"));
                objs.Add(deInfo.FP4Time.ToString("0.00"));
                objs.Add(deInfo.FP4AppRate.ToString());

                nr.cellValues = objs;
                datas.Add(nr);

                idx++;
            }
        }

        protected override void queryInThread(object o) 
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                if(condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = 20;
                        queryPeriodInfo(clientProxy, package, period,false);
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 测试地点
        /// </summary>
        private string getTestAddr(string strfilename)
        {
            string[] filePart = strfilename.Split('_');
            if (filePart.Length >= 3)
                return filePart[2];
            else
                return "";
        }

        /// <summary>
        /// 测试位置
        /// </summary>
        private string getTestSite(string strfilename)
        {
            string[] filePart = strfilename.Split('_');
            if (filePart.Length >= 4)
                return filePart[3].Replace("@", "");
            else
                return "";
        }

        /// <summary>
        /// 获取端口号
        /// </summary>
        private string getTestPort(string strfilename)
        {
            if (strfilename.Length > 10)
            {
                string strSub = strfilename.Substring(strfilename.Length - 4, 4);
                if (strSub == "(02)")
                    return "Port2";
                else if (strSub == "(03)")
                    return "Port3";
                else if (strSub == "(04)")
                    return "Port4";
                else
                    return "";
            }
            else
                return "";
        }

        /// <summary>
        /// 获取测试类型
        /// </summary>
        private string getUserType(string strfilename)
        {
            if (strfilename.IndexOf("多用户") >= 0)
                return "多用户";
            else if (strfilename.IndexOf("单用户") >= 0)
                return "单用户";
            else if (strfilename.IndexOf("三网") >= 0)
                return "三网对比";
            else
                return "其他";
        }

        /// <summary>
        /// 获取室分类型
        /// </summary>
        private string getCoverType(int iCity ,int iareaId)
        {
            string strCover = "非室分";
            if (cqtCityNameMultiDimensionDic.ContainsKey(iCity)
                && cqtCityNameMultiDimensionDic[iCity].ContainsKey(iareaId))
            {
                strCover = cqtCityNameMultiDimensionDic[iCity][iareaId].Strareatypename;
            }
            return strCover;
        }

        /// <summary>
        /// 加载选中地市CQT测试点的覆盖属性列表
        /// </summary>
        private void loadCQTPointInfo()
        {
            cqtCityNameMultiDimensionDic = new Dictionary<int, Dictionary<int, CQTPointTem>>();

            string strSql = "select iareatypeid,strcover,iareaid,strcomment2,strcomment3 from tb_cfg_static_areainfo where iareatypeid in ";
            if (MainModel.User.DBID == -1)
            {
                int iCityIDTem = MainModel.DistrictID;
                for (int i = 0; i < condition.DistrictIDs.Count; i++)
                {
                    MainModel.DistrictID = condition.DistrictIDs[i];
                    MasterCom.RAMS.Stat.DIYQueryCQTPointInfo dqcpi = new MasterCom.RAMS.Stat.DIYQueryCQTPointInfo(MainModel, 25, true, strSql);
                    dqcpi.Query();
                    if (!cqtCityNameMultiDimensionDic.ContainsKey(condition.DistrictIDs[i]))
                    {
                        cqtCityNameMultiDimensionDic.Add(condition.DistrictIDs[i], dqcpi.CQTPointDic);
                    }
                }
                MainModel.DistrictID = iCityIDTem;
            }
            else
            {
                int cityID = MainModel.DistrictID;
                MasterCom.RAMS.Stat.DIYQueryCQTPointInfo dqcpi = new MasterCom.RAMS.Stat.DIYQueryCQTPointInfo(MainModel, 25, true, strSql);
                dqcpi.Query();
                if (!cqtCityNameMultiDimensionDic.ContainsKey(cityID))
                {
                    cqtCityNameMultiDimensionDic.Add(cityID, dqcpi.CQTPointDic);
                }
            }
        }

        public class DownloadEventKey
        {
            /// <summary>
            /// 时间
            /// </summary>
            public DateTime DTime { get; set; }
            /// <summary>
            /// 城市
            /// </summary>
            public string StrCity { get; set; } = "";
            /// <summary>
            /// 测试地点
            /// </summary>
            public string StrTestAddr { get; set; } = "";
            /// <summary>
            /// 测试位置
            /// </summary>
            public string StrTestSite { get; set; } = "";
            /// <summary>
            /// 测试用户类型
            /// </summary>
            public string StrUserType { get; set; } = "";
            /// <summary>
            /// 室分类型
            /// </summary>
            public string StrCoverType { get; set; } = "";

            public override bool Equals(object obj)
            {
                DownloadEventKey other = obj as DownloadEventKey;
                if (other == null)
                    return false;

                if (!base.GetType().Equals(obj.GetType()))
                    return false;

                return (this.DTime.Equals(other.DTime) &&
                        this.StrCity.Equals(other.StrCity) &&
                        this.StrTestAddr.Equals(other.StrTestAddr) &&
                        this.StrTestSite.Equals(other.StrTestSite) &&
                        this.StrUserType.Equals(other.StrUserType)) &&
                        this.StrCoverType.Equals(other.StrCoverType);
            }

            public override int GetHashCode()
            {
                return this.StrTestAddr.GetHashCode();
            }
        }

        public class DownloadEevetInfo
        {
            private double _fDownSize;
            private double _fDownTime;
            private double _fHDowmTime;
            private double _fRDownTime;
            private double _fGDownTime;

            #region 字体封装
            /// <summary>
            /// 序号
            /// </summary>
            public int Sn { get; set; }
            /// <summary>
            /// 时间
            /// </summary>
            public DateTime DTime { get; set; }
            /// <summary>
            /// 城市
            /// </summary>
            public string StrCity { get; set; } = "";
            /// <summary>
            /// 测试地点
            /// </summary>
            public string StrTestAddr { get; set; } = "";
            /// <summary>
            /// 测试位置
            /// </summary>
            public string StrTestSite { get; set; } = "";
            /// <summary>
            /// 室分类型
            /// </summary>
            public string StrCoverType { get; set; } = "";
            /// <summary>
            /// 下载成功次数
            /// </summary>
            public int ISuccNum { get; set; }
            /// <summary>
            /// 下载失败次数
            /// </summary>
            public int IFailNum { get; set; }
            /// <summary>
            /// 下载请求次数
            /// </summary>
            public int IAttNum
            {
                get { return ISuccNum + IFailNum; }
            }
            /// <summary>
            /// 掉线率
            /// </summary>
            public string FDropRate
            {
                get
                {
                    if (IAttNum == 0)
                        return "0";
                    else
                        return Math.Round((double)100 * IFailNum / IAttNum, 2) + "%";
                }
            }
            /// <summary>
            /// 总下载数据量(KBytes)
            /// </summary>
            public double FDownSize
            {
                get { return Math.Round(_fDownSize, 2); }
                set { _fDownSize = value; }
            }
            /// <summary>
            /// 总下载时间(s)
            /// </summary>
            public double FDownTime
            {
                get { return Math.Round(_fDownTime, 2); }
                set { _fDownTime = value; }
            }
            /// <summary>
            /// 应用层平均速率(kbps)
            /// </summary>
            public double FAppRate
            {
                get
                {
                    if (_fDownTime == 0)
                        return 0;
                    else
                        return Math.Round(_fDownSize / _fDownTime, 2);
                }
            }
            /// <summary>
            /// HSDPA下载时间(s)
            /// </summary>
            public double FHDowmTime
            {
                get { return Math.Round(_fHDowmTime, 2); }
                set { _fHDowmTime = value; }
            }
            /// <summary>
            /// R4下载时间(s)
            /// </summary>
            public double FRDownTime
            {
                get { return Math.Round(_fRDownTime, 2); }
                set { _fRDownTime = value; }
            }
            /// <summary>
            /// EDGE/GPRS下载时间(s)
            /// </summary>
            public double FGDownTime
            {
                get { return Math.Round(_fGDownTime, 2); }
                set { _fGDownTime = value; }
            }
            /// <summary>
            /// 平均下载速率3端口
            /// </summary>
            public double FP3AppRate
            {
                get
                {
                    if (FP3Time == 0)
                        return 0;
                    else
                        return Math.Round(FP3Size / FP3Time, 2);
                }
            }
            /// <summary>
            /// 平均下载速率4端口
            /// </summary>
            public double FP4AppRate
            {
                get
                {
                    if (FP4Time == 0)
                        return 0;
                    else
                        return Math.Round(FP4Size / FP4Time, 2);
                }
            }

            /// <summary>
            /// 测试用户类型
            /// </summary>
            public string StrUserType { get; set; } = "";

            public double FP3Size { get; set; }
            public double FP4Size { get; set; }
            public double FP3Time { get; set; }
            public double FP4Time { get; set; }
            #endregion

            public static int CompareByTestAddr(DownloadEevetInfo a, DownloadEevetInfo b)
            {
                return a.StrTestAddr.CompareTo(b.StrTestAddr);
            }
            public static int CompareByUserType(DownloadEevetInfo a, DownloadEevetInfo b)
            {
                return b.StrUserType.CompareTo(a.StrUserType);
            }
        }
    }

}
