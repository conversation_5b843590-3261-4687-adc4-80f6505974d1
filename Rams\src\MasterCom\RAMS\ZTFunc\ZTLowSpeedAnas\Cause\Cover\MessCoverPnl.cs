﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class MessCoverPnl : UserControl
    {
        public MessCoverPnl()
        {
            InitializeComponent();
        }

        MessCoverCause mainReason = null;
        MessCoverSCellOverCover scR=null;
        MessCoverNCellOverCover ncR=null;
        MessCoverUnreasonableCover unR=null;
        public void LinkCondition(MessCoverCause reason)
        {
            if (reason == null)
            {
                return;
            }
            this.mainReason = reason;
            foreach (CauseBase item in reason.SubCauses)
            {
                if (item is MessCoverSCellOverCover)
                {
                    scR = item as MessCoverSCellOverCover;
                }
                else if (item is MessCoverNCellOverCover)
                {
                    ncR = item as MessCoverNCellOverCover;
                }
                else if (item is MessCoverUnreasonableCover)
                {
                    unR = item as MessCoverUnreasonableCover;
                }
            }

            numRsrpSpan.Value = (decimal)mainReason.RSRP;
            numCellCnt.Value = (decimal)mainReason.CellCountMin;
            numRsrpSpan.ValueChanged += numRsrpSpan_ValueChanged;
            numCellCnt.ValueChanged += numCellCnt_ValueChanged;

            if (scR != null)
            {
                numSCellRadio.Value = (decimal)scR.OverRatio;
                numSCellRadio.ValueChanged += numSCellRadio_ValueChanged;
            }

            if (ncR != null)
            {
                numNCellRadio.Value = (decimal)ncR.OverRatio;
                numNCellRadio.ValueChanged += numNCellRadio_ValueChanged;
            }

            if (unR != null)
            {
                numDistance.Value = (decimal)unR.DistanceMin;
                numDistance.ValueChanged += numDistance_ValueChanged;
            }
        }

        void numDistance_ValueChanged(object sender, EventArgs e)
        {
            unR.DistanceMin = (double)numDistance.Value;
        }

        void numNCellRadio_ValueChanged(object sender, EventArgs e)
        {
            ncR.OverRatio = (float)numNCellRadio.Value;
        }

        void numSCellRadio_ValueChanged(object sender, EventArgs e)
        {
            scR.OverRatio = (float)numSCellRadio.Value;
        }

        void numCellCnt_ValueChanged(object sender, EventArgs e)
        {
            mainReason.CellCountMin = (int)numCellCnt.Value;
        }

        void numRsrpSpan_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRP = (float)numRsrpSpan.Value;
        }


    }
}
