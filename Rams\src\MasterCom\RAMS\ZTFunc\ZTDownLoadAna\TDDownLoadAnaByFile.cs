﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDDownLoadAnaByFile : TDDownLoadQueryAnaBase
    {
        public TDDownLoadAnaByFile(MainModel mm)
            : base(mm)
        {
            NeedJudgeTestPointByRegion = false;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override string Name
        {
            get { return "按文件下载速率分析"; }
        }
    }
}
