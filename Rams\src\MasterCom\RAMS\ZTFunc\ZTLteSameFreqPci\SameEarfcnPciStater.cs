﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class SameEarfcnPciStater
    {
        public SameEarfcnPciStater(MainModel mainModel)
        {
            this.mainModel = mainModel;
            this.latPerMeter = DistanceTranslator.LatitudePerMeter();
        }

        public List<SameEarfcnPciCell> DoStat(SameEarfcnPciCond cond)
        {
            curCond = cond;
            List<SameEarfcnPciCell> resultList = new List<SameEarfcnPciCell>();
            WaitBox.Show("正在查找同频同PCI小区...", DoStatInThread, resultList);
            curCond = null;
            return resultList;
        }

        private void DoStatInThread(object o)
        {
            List<SameEarfcnPciCell> resultList = o as List<SameEarfcnPciCell>;
            List<LTECell> cellList = GetCellsInRegion(GetAllCells());

            if (curCond.IsRadioNew)
            {
                doStatNew(resultList, cellList);
            }
            else
            {
                doStatOld(resultList, cellList);
            }
        }

        private void doStatNew(List<SameEarfcnPciCell> resultList, List<LTECell> cellList)
        {
            ExcelNPOIReader reader = new ExcelNPOIReader(curCond.xlsFileName);
            ExcelNPOITable table = reader.GetTable();

            try
            {
                foreach (object[] cellValue in table.CellValues)
                {
                    LTECell srcCell = new LTECell();
                    srcCell.Name = cellValue[0].ToString();
                    srcCell.CellID = Convert.ToInt32(cellValue[1]);
                    srcCell.SectorID = Convert.ToInt32(cellValue[2]);
                    srcCell.LongitudeTemp = (double)cellValue[3];
                    srcCell.LatitudeTemp = (double)cellValue[4];
                    srcCell.DirectionTemp = Convert.ToInt16(cellValue[5]);
                    srcCell.EARFCN = Convert.ToInt32(cellValue[6]);
                    srcCell.PCI = Convert.ToInt32(cellValue[7]);

                    int j = 0;
                    WaitBox.ProgressPercent = (++j) * 100 / table.CellValues.Count;
                    for (int i = 0; i < cellList.Count; ++i)
                    {
                        SameEarfcnPciCell srcView = null;
                        if (curCond.IsRejectIndoorCell && cellList[i].Type == LTEBTSType.Indoor)
                        {
                            continue;
                        }

                        if (curCond.IsDisAngle)
                        {
                            GetDisAngleCell(cellList[i], ref srcView, srcCell);
                        }
                        //if (curCond.IsFirstNBCell)
                        //{
                        //    GetFirstNBCell(ref srcView, cellList[i]);
                        //}
                        //if (curCond.IsSecondNBCell)
                        //{
                        //    GetSecondNBCell(ref srcView, cellList[i]);
                        //}

                        addResult(resultList, srcView);
                    }
                }
            }
            catch
            {
                MessageBox.Show("Excel文档格式有误，请核查！");
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
            }
        }

        private void addResult(List<SameEarfcnPciCell> resultList, SameEarfcnPciCell srcView)
        {
            if (srcView != null && srcView.IsValid())
            {
                List<string> keys2Remove = new List<string>();
                foreach (string key in srcView.cellsTypeDic.Keys)
                {
                    if (srcView.cellsTypeDic[key].cellItems.Count == 0)
                    {
                        keys2Remove.Add(key);
                    }
                }
                foreach (string key in keys2Remove)
                {
                    srcView.cellsTypeDic.Remove(key);
                }
                resultList.Add(srcView);
            }
        }

        private void doStatOld(List<SameEarfcnPciCell> resultList, List<LTECell> cellList)
        {
            try
            {
                for (int i = 0; i < cellList.Count; ++i)
                {
                    WaitBox.ProgressPercent = (i + 1) * 100 / cellList.Count;
                    SameEarfcnPciCell srcView = null;
                    if (curCond.IsRejectIndoorCell && cellList[i].Type == LTEBTSType.Indoor)
                    {
                        continue;
                    }

                    if (curCond.IsDisAngle)
                    {
                        GetDisAngleCell(cellList, ref srcView, i);
                    }
                    if (curCond.IsFirstNBCell)
                    {
                        GetFirstNBCell(ref srcView, cellList[i]);
                    }
                    if (curCond.IsSecondNBCell)
                    {
                        GetSecondNBCell(ref srcView, cellList[i]);
                    }

                    addResult(resultList, srcView);
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
            }
        }
        private void GetDisAngleCell(LTECell tarCell, ref SameEarfcnPciCell srcView, LTECell srcCell)
        {
            double maxLngLat = latPerMeter * curCond.MaxDistance * 1.2; // 0.2 作为余量
            if (curCond.IsRejectIndoorCell && tarCell.Type == LTEBTSType.Indoor)
            {
                return;
            }
            if (srcCell.Name == tarCell.Name)
            {
                return;
            }

            if (srcCell.EARFCN != tarCell.EARFCN || srcCell.PCI != tarCell.PCI)
            {
                return;
            }

            if (Math.Abs(srcCell.LongitudeTemp - tarCell.Longitude) > maxLngLat
                || Math.Abs(srcCell.LatitudeTemp - tarCell.Latitude) > maxLngLat)
            {
                return;
            }

            double distance = MathFuncs.GetDistance(srcCell.LongitudeTemp, srcCell.LatitudeTemp, tarCell.Longitude, tarCell.Latitude);
            if (distance > curCond.MaxDistance)
            {
                return;
            }

            int interAngle = -999;

            if (srcCell.DirectionTemp >= 0 && srcCell.DirectionTemp <= 360 && tarCell.Direction >= 0 && tarCell.Direction <= 360)
            {
                interAngle = Math.Abs(srcCell.DirectionTemp - tarCell.Direction);
                if (interAngle > 180)
                {
                    interAngle = 360 - interAngle;
                }
            }

            if (interAngle > curCond.maxAngle || interAngle < curCond.minAngle)
            {
                return;
            }

            // found it
            if (srcView == null)
            {
                srcView = new SameEarfcnPciCell(srcCell, curCond.IsDisAngle, curCond.IsFirstNBCell, curCond.IsSecondNBCell);
            }

            srcView.cellsTypeDic["距离角度"].cellItems.Add(new SameEarfcnPciCellItem(tarCell, distance, interAngle));


        }
        private void GetDisAngleCell(List<LTECell> cellList, ref SameEarfcnPciCell srcView, int i)
        {
            LTECell srcCell = cellList[i];
            double maxLngLat = latPerMeter * curCond.MaxDistance * 1.2; // 0.2 作为余量
            for (int j = 0; j < cellList.Count; ++j)
            {
                LTECell tarCell = cellList[j];
                if ((curCond.IsRejectIndoorCell && tarCell.Type == LTEBTSType.Indoor)
                    || i == j || srcCell.Name == tarCell.Name || srcCell.EARFCN != tarCell.EARFCN || srcCell.PCI != tarCell.PCI
                    || Math.Abs(srcCell.Longitude - tarCell.Longitude) > maxLngLat
                    || Math.Abs(srcCell.Latitude - tarCell.Latitude) > maxLngLat)
                {
                    continue;
                }

                double distance = MathFuncs.GetDistance(srcCell.Longitude, srcCell.Latitude, tarCell.Longitude, tarCell.Latitude);
                if (distance > curCond.MaxDistance)
                {
                    continue;
                }

                int interAngle = getInterAngle(srcCell, tarCell);

                if (interAngle > curCond.maxAngle || interAngle < curCond.minAngle)
                {
                    continue;
                }

                // found it
                if (srcView == null)
                {
                    srcView = new SameEarfcnPciCell(srcCell, curCond.IsDisAngle, curCond.IsFirstNBCell, curCond.IsSecondNBCell);
                }

                srcView.cellsTypeDic["距离角度"].cellItems.Add(new SameEarfcnPciCellItem(tarCell, distance, interAngle));

            }
        }

        private void GetFirstNBCell(ref SameEarfcnPciCell srcView, LTECell lteCell)
        {
            for (int i = 0; i < lteCell.NeighbourCells.Count; i++)
            {
                LTECell tarCell = lteCell.NeighbourCells[i];
                if (curCond.IsRejectIndoorCell && tarCell.Type == LTEBTSType.Indoor)
                {
                    continue;
                }
                if (lteCell.EARFCN != tarCell.EARFCN || lteCell.PCI != tarCell.PCI)
                {
                    continue;
                }

                double distance = MathFuncs.GetDistance(lteCell.Longitude, lteCell.Latitude, tarCell.Longitude, tarCell.Latitude);

                int interAngle = getInterAngle(lteCell, tarCell);
                // found it
                if (srcView == null)
                {
                    srcView = new SameEarfcnPciCell(lteCell, curCond.IsDisAngle, curCond.IsFirstNBCell, curCond.IsSecondNBCell);
                }

                srcView.cellsTypeDic["一层邻区"].cellItems.Add(new SameEarfcnPciCellItem(tarCell, distance, interAngle));

            }
        }

        private int getInterAngle(LTECell lteCell, LTECell tarCell)
        {
            int interAngle = -999;

            if (lteCell.Direction >= 0 && lteCell.Direction <= 360 && tarCell.Direction >= 0 && tarCell.Direction <= 360)
            {
                interAngle = Math.Abs(lteCell.Direction - tarCell.Direction);
                if (interAngle > 180)
                {
                    interAngle = 360 - interAngle;
                }
            }

            return interAngle;
        }

        private void GetSecondNBCell(ref SameEarfcnPciCell srcView, LTECell lteCell)
        {
            List<LTECell> lteCells = new List<LTECell>();
            for (int i = 0; i < lteCell.NeighbourCells.Count; i++)
            {
                LTECell tempCell = lteCell.NeighbourCells[i];
                for (int j = 0; j < tempCell.NeighbourCells.Count; j++)
                {
                    LTECell tarCell = tempCell.NeighbourCells[j];
                    if ((curCond.IsRejectIndoorCell && tarCell.Type == LTEBTSType.Indoor) 
                        || (lteCells.Count > 0 && lteCells.Contains(tarCell))
                        || lteCell.SCellID == tarCell.SCellID || lteCell.EARFCN != tarCell.EARFCN || lteCell.PCI != tarCell.PCI)
                    {
                        continue;
                    }

                    lteCells.Add(tarCell);

                    double distance = MathFuncs.GetDistance(lteCell.Longitude, lteCell.Latitude, tarCell.Longitude, tarCell.Latitude);
                    int interAngle = getInterAngle(lteCell, tarCell);
                    // found it
                    if (srcView == null)
                    {
                        srcView = new SameEarfcnPciCell(lteCell, curCond.IsDisAngle, curCond.IsFirstNBCell, curCond.IsSecondNBCell);
                    }
                    srcView.cellsTypeDic["二层邻区"].cellItems.Add(new SameEarfcnPciCellItem(tarCell, distance, interAngle));

                }
            }
        }

        private List<LTECell> GetAllCells()
        {
            List<LTECell> cellList = null;
            if (MapCellLayer.DrawCurrent)
            {
                cellList = mainModel.CellManager.GetCurrentLTECells();
            }
            else
            {
                cellList = mainModel.CellManager.GetLTECells(MapCellLayer.CurShowTimeAt);
            }
            return cellList;
        }

        private List<LTECell> GetCellsInRegion(List<LTECell> cellList)
        {
            if (!mainModel.SearchGeometrys.IsSelectRegion())
            {
                return new List<LTECell>(cellList);
            }

            List<LTECell> retList = new List<LTECell>();
            foreach (LTECell cell in cellList)
            {
                if (mainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    retList.Add(cell);
                }
            }
            return retList;
        }

        private readonly MainModel mainModel;
        private SameEarfcnPciCond curCond;
        private readonly double latPerMeter;
    }

    public class SameEarfcnPciCond
    {
        public double MaxDistance { get; set; } 
        public int maxAngle { get; set; } 
        public int minAngle { get; set; } 
        public bool IsRejectIndoorCell { get; set; } 
        public bool IsDisAngle { get; set; } 
        public bool IsFirstNBCell { get; set; } 
        public bool IsSecondNBCell { get; set; } 

        public bool IsRadioNew { get; set; } 
        public string xlsFileName { get; set; } 
    }

    public class SameEarfcnPciCellItem
    {
        public SameEarfcnPciCellItem(LTECell lteCell, double distance, int interAngle)
        {
            this.lteCell = lteCell;
            this.Distance = distance;
            this.InterAngle = interAngle;
        }

        public LTECell Cell
        {
            get { return lteCell; }
        }

        public int CellID
        {
            get { return lteCell.SCellID; }
        }

        public string CellName
        {
            get { return lteCell.Name; }
        }

        public int Earfcn
        {
            get { return lteCell.EARFCN; }
        }

        public int Pci
        {
            get { return lteCell.PCI; }
        }

        public double Longitude
        {
            get { return lteCell.Longitude; }
        }

        public double Latitude
        {
            get { return lteCell.Latitude; }
        }

        public double Distance
        {
            get;
            set;
        }

        public int InterAngle
        {
            get;
            set;
        }

        public SameEarfcnPciCellItem Parent
        {
            get;
            set;
        }

        private readonly LTECell lteCell;
        public LTECell LteCell
        {
            get { return lteCell; }
        }
    }

    public class SameEarfcnPciCell
    {
        public SameEarfcnPciCell(LTECell lteCell, bool isDisAngle, bool isFirstNBCell, bool isSecondNBCell)
        {
            this.selectedCell = lteCell;
            cellsTypeDic = new Dictionary<string, SameEarfcnPciCellType>();

            if (isDisAngle)
            {
                SameEarfcnPciCellType type1 = new SameEarfcnPciCellType("距离角度");
                cellsTypeDic.Add("距离角度", type1);
            }
            if (isFirstNBCell)
            {
                SameEarfcnPciCellType type2 = new SameEarfcnPciCellType("一层邻区");
                cellsTypeDic.Add("一层邻区", type2);
            }
            if (isSecondNBCell)
            {
                SameEarfcnPciCellType type3 = new SameEarfcnPciCellType("二层邻区");
                cellsTypeDic.Add("二层邻区", type3);
            }
        }
        public LTECell selectedCell { get; set; }
        public Dictionary<string, SameEarfcnPciCellType> cellsTypeDic { get; set; }

        public bool IsValid()
        {
            foreach (string type in cellsTypeDic.Keys)
            {
                if (cellsTypeDic[type].cellItems.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }
    }

    public class SameEarfcnPciCellType
    {
        public SameEarfcnPciCellType(string typeName)
        {
            this.typeName = typeName;
        }
        public string typeName { get; set; }
        public List<SameEarfcnPciCellItem> cellItems { get; set; } = new List<SameEarfcnPciCellItem>();
    }
}
