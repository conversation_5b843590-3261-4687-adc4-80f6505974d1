﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.ComponentModel;
using System.IO;

using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.AssistLayer;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System.Collections.ObjectModel;

namespace MasterCom.RAMS.Func
{
    public delegate void FavoriteSerialsChangedHandler();    

    /// <summary>
    /// 接管DTLayer的MapSerialInfo
    /// </summary>
    public class DTLayerSerialManager
    {
        public static DTLayerSerialManager Instance
        {
            get { return instance; }
        }

        /// <summary>
        /// 根据DTDisplayParameterManager的指标建立Serials
        /// 由DTDisplayParameterManager.Load和DTDisplayParameterManager.Save调用
        /// </summary>
        public void FireDisplayParamsChanged()
        {
            // 指标集合改变了
            serialByParams.Clear();
            List<DTDisplayParameterSystem> systems = DTDisplayParameterManager.GetInstance().Systems;
            foreach (DTDisplayParameterSystem sys in systems)
            {
                foreach (DTDisplayParameterInfo info in sys.DisplayParamInfos)
                {
                    MapSerialInfo serial = CreateSerial(sys, info, 0);
                    serialByParams.Add(serial);
                }
            }

            // 根据新的指标集合重设索引字典
            serialByParamsDic.Clear();
            foreach (MapSerialInfo msi in serialByParams)
            {
                string key = msi.ColorSystemName + ":" + msi.ColorParamName;
                serialByParamsDic[key] = msi;
            }

            // 更新已选中的指标集合
            for (int i = 0; i < selectedSerials.Count; ++i)
            {
                MapSerialInfo msi = selectedSerials[i];
                if (msi.OwnColorFuncDesc != null)
                {
                    continue;
                }

                string key = msi.ColorSystemName + ":" + msi.ColorParamName;
                if (serialByParamsDic.ContainsKey(key))
                {
                    serialByParamsDic[key].Visible = true;
                    selectedSerials.Add(serialByParamsDic[key]);
                }
                selectedSerials.Remove(msi);
            }

            // 飞线
            if (FlyLineSerial != null && FlyLineSerial.OwnColorFuncDesc == null)
            {
                string key = FlyLineSerial.ColorSystemName + ":" + FlyLineSerial.ColorParamName;
                if (serialByParamsDic.ContainsKey(key))
                {
                    FlyLineSerial = serialByParamsDic[key];
                    FlyLineSerial.Visible = true;
                }
                else
                {
                    FlyLineSerial = null;
                }
            }

            this.AddSerialFinished(0);
        }

        public void ChangeMS(int ms)
        {
            this.ms = ms;
            foreach (MapSerialInfo msi in this.AllSerials)
            {
                msi.MS = ms;
            }
        }

        /// <summary>
        /// 开启一个Serial
        /// </summary>
        /// <param name="serial"></param>
        public void AddSelectedSerial(MapSerialInfo serial)
        {
            if (!selectedSerials.Contains(serial))
            {
                serial.Visible = true;
                selectedSerials.Add(serial);
            }
        }

        /// <summary>
        /// 开启一堆Serials
        /// </summary>
        /// <param name="serials"></param>
        public void AddSelectedSerials(List<MapSerialInfo> serials)
        {
            ClearSelectedSerials();
            foreach (MapSerialInfo msi in serials)
            {
                AddSelectedSerial(msi);
            }
        }

        /// <summary>
        /// 清空已选中的Serials
        /// </summary>
        public void ClearSelectedSerials()
        {
            foreach (MapSerialInfo msi in selectedSerials)
            {
                msi.Visible = false;
            }
            selectedSerials.Clear();
        }

        /// <summary>
        /// 添加一个自定义Serial，由MapFormDTLayer.set_Param调用
        /// </summary>
        /// <param name="serial"></param>
        public void AddCustomSerial(MapSerialInfo serial)
        {
            serialByCustom.Add(serial);
        }

        public void RemoveCustomSerial(MapSerialInfo serial)
        {
            if (serialByCustom.Contains(serial))
            {
                serialByCustom.Remove(serial);
            }
        }

        /// <summary>
        /// 指标载入完毕
        /// </summary>
        /// <param name="type">0为参数指标，1为自定义指标</param>
        public void AddSerialFinished(int type)
        {
            if (type == 0)
            {
                paramsFinishFlag = true;
            }
            else if (type == 1)
            {
                customFinishFlag = true;
            }

            if (paramsFinishFlag && customFinishFlag) // 都加载完毕后，加载常用指标配置
            {
                FavoriteSerialConfig.Instance.Load();
            }
        }

        /// <summary>
        /// 通过指标方式获取Serial
        /// </summary>
        /// <param name="sysName"></param>
        /// <param name="paramName"></param>
        /// <returns></returns>
        public MapSerialInfo GetParamsSerial(string sysName, string paramName)
        {
            string key = sysName + ":" + paramName;
            MapSerialInfo msi = null;
            serialByParamsDic.TryGetValue(key, out msi);
            return msi;
        }

        public string GetNameBySerial(MapSerialInfo msi)
        {
            if (msi == null)
            {
                return "";
            }
            else if (msi.OwnColorFuncDesc != null)
            {
                return msi.Name;
            }
            else if (!string.IsNullOrEmpty(msi.ColorSystemName) && !string.IsNullOrEmpty(msi.ColorParamName))
            {
                return msi.ColorSystemName + ":" + msi.ColorParamName;
            }
            return "";
        }

        /// <summary>
        /// 通过原本的固定名称获取Serial，用于旧式兼容
        /// 固定名称有三种可能情况，需分别处理：
        /// SystemName:ParamName
        /// OldSerialName
        /// DiySerialName
        /// 再加一种情况：
        /// 指标的唯一名称，如：LTE_RSRP   (取自DTDisplayParameterInfo.ParamInfo.Name)
        /// </summary>
        /// <param name="paramName"></param>
        /// <returns></returns>
        public MapSerialInfo GetSerialByName(string paramName)
        {
            if (paramName.IndexOf(':') != -1)
            {
                MapSerialInfo msi = null;
                serialByParamsDic.TryGetValue(paramName, out msi);
                return msi;
            }
            foreach (MapSerialInfo m in this.serialByParamsDic.Values)
            {
                if (m.IDName.Equals(paramName))
                {
                    return m;
                }
            }
            if (sCompatibleDic.ContainsKey(paramName))
            {
                paramName = sCompatibleDic[paramName];
                if (serialByParamsDic.ContainsKey(paramName))
                {
                    return serialByParamsDic[paramName];
                }
            }
            else
            {
                foreach (MapSerialInfo diyMsi in serialByCustom)
                {
                    if (diyMsi.Name == paramName)
                    {
                        return diyMsi;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 使用原本的固定名称选中一个Serial
        /// 用于旧式兼容
        /// </summary>
        /// <param name="paramName"></param>
        public void SetDefaultSerial(string paramName)
        {
            ClearSelectedSerials();
            MapSerialInfo msi = GetSerialByName(paramName);
            if (msi != null)
            {
                AddSelectedSerial(msi);
            }
        }
        public void SetDefaultSerials(params string[] paramNames)
        {
            ClearSelectedSerials();
            foreach (string paramName in paramNames)
            {
                MapSerialInfo msi = GetSerialByName(paramName);
                if (msi != null)
                {
                    AddSelectedSerial(msi);
                }
            }
        }
        /// <summary>
        /// 通过指标方式选中一个Serial
        /// </summary>
        /// <param name="sysName"></param>
        /// <param name="paramName"></param>
        public void SetDefaultSerial(string sysName, string paramName)
        {
            ClearSelectedSerials();
            MapSerialInfo msi = GetParamsSerial(sysName, paramName);
            if (msi == null)
            {
                return;
            }
            AddSelectedSerial(msi);
        }

        public ReadOnlyCollection<MapSerialInfo> AllSerials
        {
            get
            {
                List<MapSerialInfo> serials = new List<MapSerialInfo>();
                serials.AddRange(serialByParams);
                serials.AddRange(serialByCustom);
                return serials.AsReadOnly();
            }
        }

        /// <summary>
        /// 这些Serials可能会被重新实例化
        /// </summary>
        public ReadOnlyCollection<MapSerialInfo> ParamsSerials
        {
            get { return serialByParams.AsReadOnly(); }
        }

        /// <summary>
        /// 自定义的Serial指挥实例化一次
        /// </summary>
        public ReadOnlyCollection<MapSerialInfo> CustomSerials
        {
            get { return serialByCustom.AsReadOnly(); }
        }

        public ReadOnlyCollection<MapSerialInfo> SelectedSerials
        {
            get { return selectedSerials.AsReadOnly(); }
        }

        public MapSerialInfo FlyLineSerial { get; set; }

        private MapSerialInfo CreateSerial(DTDisplayParameterSystem sys, DTDisplayParameterInfo info, int arrayIndex)
        {
            DTDisplayParameter parameter = info[arrayIndex];
            MapSerialInfo serial = new MapSerialInfo();

            serial.Name = parameter.Info.Name;
            serial.IDName = info.ParamInfo.Name;
            serial.Type = parameter.Info.SubGroupName;
            serial.LineWidth = this.lineWidth;
            serial.Visible = this.visible;
            serial.MS = this.ms;
            serial.DisplayLine = this.displayLine;
            serial.OffsetX = this.offsetX;
            serial.OffsetY = this.offsetY;

            serial.SizeParamEnabled = false;
            serial.Size = 20;
            serial.SymbolParamEnabled = false;
            serial.Symbol = 0;
            serial.ColorSystemName = sys.Name;
            serial.ColorParamName = info.Name;
            serial.ColorArrayIndex = arrayIndex;
            serial.ColorParamEnabled = true;
            return serial;
        }

        private readonly List<MapSerialInfo> serialByParams;
        private readonly List<MapSerialInfo> serialByCustom;
        private readonly List<MapSerialInfo> selectedSerials;
        private readonly Dictionary<string, MapSerialInfo> serialByParamsDic;

        // 加载完毕标志
        private bool customFinishFlag = false;
        private bool paramsFinishFlag = false;

        // 原本Serial的可选项，现在默认固定了
        private readonly float lineWidth = 2.0F;
        private readonly bool visible = false;
        private readonly int offsetX = 0;
        private readonly int offsetY = 0;
        private int ms = 0;
        private readonly bool displayLine = true;

        private static DTLayerSerialManager instance = new DTLayerSerialManager();
        private DTLayerSerialManager()
        {
            serialByParams = new List<MapSerialInfo>();
            serialByCustom = new List<MapSerialInfo>();
            selectedSerials = new List<MapSerialInfo>();
            serialByParamsDic = new Dictionary<string, MapSerialInfo>();
        }

        private static Dictionary<string, string> sCompatibleDic = init();

        private static Dictionary<string, string> init()
        {
            Dictionary<string, string> sCompatibleDicTmp = new Dictionary<string, string>();
            sCompatibleDicTmp.Add("GSM RxLevSub", "GSM:RxLevSub");
            sCompatibleDicTmp.Add("GSM RxQual", "GSM:RxQualSub");
            sCompatibleDicTmp.Add("GSM_PESQ", "GSM:PESQ");
            sCompatibleDicTmp.Add("GSM_SCAN_RxLev", "GSM_SCAN:RxLev");
            sCompatibleDicTmp.Add("TD_PCCPCH_RSCP", "TDSCDMA:PCCPCH_RSCP");
            sCompatibleDicTmp.Add("TD_DPCH_RSCP", "TDSCDMA:DPCH_RSCP");
            sCompatibleDicTmp.Add("TD_TxPower", "TDSCDMA:TxPower");
            sCompatibleDicTmp.Add("TD_GSM_RxlevSub", "TDSCDMA:GSM_RxlevSub");
            sCompatibleDicTmp.Add("TD_PCCPCH_C2I", "TDSCDMA:PCCPCH_C2I");
            sCompatibleDicTmp.Add("TD_DPCH_C2I", "TDSCDMA:DPCH_C2I");
            sCompatibleDicTmp.Add("TD_GSM_RxqualSub", "TDSCDMA:GSM_RxqualSub");
            sCompatibleDicTmp.Add("CDMA_Ec/Io(total)", "CDMA:TotalEcIo");
            sCompatibleDicTmp.Add("CDMA_RxPower", "CDMA:RX_Power");
            sCompatibleDicTmp.Add("CDMA_TxPower", "CDMA:TX Power");
            sCompatibleDicTmp.Add("CDMA_FFER", "CDMA:FFER");
            sCompatibleDicTmp.Add("CDMA_TxAdj", "CDMA:TX_Adj");
            sCompatibleDicTmp.Add("WCDMA_Ec/Io(Total)", "WCDMA:TotalEc_Io");
            sCompatibleDicTmp.Add("WCDMA_RSCP", "WCDMA:TotalRSCP");
            sCompatibleDicTmp.Add("WCDMA_TxPower", "WCDMA:TxPower");
            sCompatibleDicTmp.Add("WCDMA_RxPower", "WCDMA:RxPower");
            sCompatibleDicTmp.Add("WCDMA_BLER", "WCDMA:BLER");
            sCompatibleDicTmp.Add("TDSCAN_PCCPCH_RSCP", "TDSCDMA_SCAN:PCCPCH_RSCP");
            sCompatibleDicTmp.Add("TDSCAN_PCCPCH_C/I", "TDSCDMA_SCAN:PCCPCH_C_I");
            sCompatibleDicTmp.Add("WSCAN_CPICHRSCP(Total)", "WCDMA_SCAN:CPICHTotalRSCP");
            sCompatibleDicTmp.Add("WSCAN_CPICHEcIo(Total)", "WCDMA_SCAN:CPICHTotalEcIo");
            sCompatibleDicTmp.Add("WLAN_APP_Average_Speed", "WLAN:APP_Average_Speed");
            sCompatibleDicTmp.Add("WLAN_APP_DataStatus_DL", "WLAN:APP_DataStatus_DL");
            sCompatibleDicTmp.Add("APP_Speed", "GSM:APP_Speed");
            sCompatibleDicTmp.Add("TD_APP_Speed", "TD-SCDMA:APP_Speed");
            sCompatibleDicTmp.Add("TD_BLER", "TD-SCDMA:BLER");
            sCompatibleDicTmp.Add("LTESCANCW_Pwr[0]", "SCAN_parameter:SCAN_CW_Pwr");
            sCompatibleDicTmp.Add("SCAN_FreqSp_Pwr", "SCAN_parameter:SCAN_FreqSp_Pwr");
            sCompatibleDicTmp.Add("LTESCAN_TopN_PSS_RSSI", "LTE_SCAN:TopN_PSS_RSSI");
            sCompatibleDicTmp.Add("LTESCAN_TopN_PSS_RP", "LTE_SCAN:TopN_PSS_RP");
            sCompatibleDicTmp.Add("TD_LTE_RSRP", "LTE_TDD:RSRP");
            sCompatibleDicTmp.Add("TD_LTE_SINR", "LTE_TDD:SINR");
            sCompatibleDicTmp.Add("lte_APP_Speed_kb", "LTE:APP_Speed_kb");
            return sCompatibleDicTmp;
        }
    }

    /// <summary>
    /// 常用指标配置
    /// </summary>
    public class FavoriteSerialConfig
    {
        public static FavoriteSerialConfig Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FavoriteSerialConfig();
                }
                return instance;
            }
        }

        public ReadOnlyCollection<FavoriteSerial> FavoriteSerials
        {
            get { return favoriteSerials.AsReadOnly(); }
        }

        public ReadOnlyCollection<MapSerialInfo> FavoriteSerialInfos
        {
            get
            {
                List<MapSerialInfo> infos = new List<MapSerialInfo>();
                foreach (FavoriteSerial fs in favoriteSerials)
                {
                    MapSerialInfo msi = null;
                    if (fs.IsDiy)
                    {
                        msi = DTLayerSerialManager.Instance.GetSerialByName(fs.Name);
                    }
                    else
                    {
                        msi = DTLayerSerialManager.Instance.GetParamsSerial(fs.Group, fs.Name);
                    }
                    if (msi == null)
                    {
                        continue;
                    }
                    infos.Add(msi);
                }
                return infos.AsReadOnly();
            }
        }

        public event FavoriteSerialsChangedHandler FavoriteSerialsChanged;
        
        /// <summary>
        /// 将原本mapsetting.cs的Serial用作常用指标
        /// 如果常用指标已经存在配置文件，则忽略这些指标
        /// </summary>
        /// <param name="oldSerials"></param>
        public void AddOldSerials(List<MapSerialInfo> oldSerials)
        {
            if (favoriteSerials.Count != 0)
            {
                return;
            }

            foreach (MapSerialInfo msi in oldSerials)
            {
                if (IndexOf(msi) != -1)
                {
                    continue;
                }
                FavoriteSerial fs = new FavoriteSerial(msi);
                favoriteSerials.Add(fs);
            }
            Save();
        }

        public void AddFavorite(MapSerialInfo msi)
        {
            if (IndexOf(msi) != -1)
            {
                return;
            }

            FavoriteSerial fs = new FavoriteSerial(msi);
            favoriteSerials.Add(fs);
            Save();
        }

        public void RemoveFavorite(MapSerialInfo msi)
        {
            int pos = IndexOf(msi);
            if (pos == -1)
            {
                return;
            }

            favoriteSerials.RemoveAt(pos);
            Save();
        }

        private int IndexOf(MapSerialInfo msi)
        {
            string key = "";
            
            if (msi.OwnColorFuncDesc != null)
            {
                key = FavoriteSerial.DiyGroupName + ":" + msi.Name;
            }
            else 
            {
                key = msi.ColorSystemName + ":" + msi.ColorParamName;
            }

            for (int i = 0; i < favoriteSerials.Count; ++i)
            {
                FavoriteSerial fs = favoriteSerials[i];
                if (fs.Key == key)
                {
                    return i;
                }
            }
            return -1;
        }

        public void Load()
        {
            if (!File.Exists(cfgFileName))
            {
                return;
            }

            favoriteSerials.Clear();
            XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
            List<object> fsLst = configFile.GetItemValue("Config", "CommonUseSerials") as List<object>;
            foreach (object o in fsLst)
            {
                FavoriteSerial fs = new FavoriteSerial();
                fs.Param = o as Dictionary<string, object>;
                favoriteSerials.Add(fs);
            }

            FireSerialsChanged();
        }

        private void Save()
        {
            List<object> objs = new List<object>();
            foreach (FavoriteSerial fs in favoriteSerials)
            {
                objs.Add(fs.Param);
            }

            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("Config");
            configFile.AddItem(config, "CommonUseSerials", objs);
            configFile.Save(cfgFileName);

            FireSerialsChanged();
        }

        private void FireSerialsChanged()
        {
            if (FavoriteSerialsChanged != null)
            {
                FavoriteSerialsChanged();
            }
        }

        private FavoriteSerialConfig()
        {
            cfgFileName = System.Windows.Forms.Application.StartupPath + @"\config\CommonUseSerials.xml";
            favoriteSerials = new List<FavoriteSerial>();
        }

        private readonly string cfgFileName;
        private readonly List<FavoriteSerial> favoriteSerials;
        private static FavoriteSerialConfig instance;
    }

    /// <summary>
    /// 常用指标配置文件项
    /// </summary>
    public class FavoriteSerial
    {
        public static string DiyGroupName { get; set; } = "自定义指标";
        public string Name { get; set; }
        public string Group { get; set; }  // Group = IsDiy == true ? DiyGroupName : MapSerialInfo.ColorSystemName
        public bool IsDiy { get; set; }

        public FavoriteSerial()
        {

        }

        public FavoriteSerial(MapSerialInfo msi)
        {
            Name = msi.Name;
            IsDiy = msi.OwnColorFuncDesc != null;
            Group = IsDiy ? DiyGroupName : msi.ColorSystemName;
        }

        public string Key
        {
            get { return string.Format("{0}:{1}", Group, Name);}
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string,object>();
                param.Add("Name", Name);
                param.Add("Group", Group);
                param.Add("IsDiy", IsDiy);
                return param;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                if (value.ContainsKey("Name"))
                {
                    Name = value["Name"] as string;
                }
                if (value.ContainsKey("Group"))
                {
                    Group = value["Group"] as string;
                }
                if (value.ContainsKey("IsDiy"))
                {
                    IsDiy = (bool)value["IsDiy"];
                }
            }
        }
    }
}
