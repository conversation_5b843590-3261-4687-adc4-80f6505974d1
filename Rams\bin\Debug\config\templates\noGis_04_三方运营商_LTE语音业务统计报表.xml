<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">04_三方运营商_LTE语音业务统计报表</Item>
          <Item typeName="Int32" key="KeyCount">4</Item>
          <Item typeName="Int32" key="TimeShowType">0</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">地市</Item>
              <Item typeName="String" key="Exp">{kDistrictId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">区域</Item>
              <Item typeName="String" key="Exp">{kAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">所属项目</Item>
              <Item typeName="String" key="Exp">{kProjId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">移动运营商</Item>
              <Item typeName="String" key="Exp">移动</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]) }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/((evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]+evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021]+evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041])-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]+evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]+evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">全称呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/((evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]+evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021]+evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041])-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]+evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]+evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))))*(((evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{(value1[1011]+value1[1031]+value3[1051])/(1000.0*(evtIdCount[1011]+evtIdCount[1031]+evtIdCount[1051]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延&gt;9s占比</Item>
              <Item typeName="String" key="Exp">{100*(value5[1011]+value5[1031]+value5[1051])/(evtIdCount[1011]+evtIdCount[1031]+evtIdCount[1051])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB返回TDL比例</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[883]+value9[883])/(evtIdCount[882]+value9[882])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB返回TDL时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[883]/(1000.0*evtIdCount[883])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到TD/GSM成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[879]+value9[879])/(evtIdCount[876]+value9[876])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到TD/GSM成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[887]+value9[887])/(evtIdCount[884]+value9[884])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[879]+value9[879]+evtIdCount[887]+value9[887])/(evtIdCount[876]+value9[876]+evtIdCount[884]+value9[884])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到TD/GSM平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[879]/(1000.0*evtIdCount[879])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到TD/GSM平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[887]/(1000.0*evtIdCount[887])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">Rxquality0-4级占比</Item>
              <Item typeName="String" key="Exp">{100*(Lte_652101+Lte_652102+Lte_652103+Lte_652104+Lte_652105)/Lte_65210C }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1000]+value9[1000]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[1007]+value9[1007])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB接通率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[1000]+value9[1000]-(evtIdCount[1007]+value9[1007]))/(evtIdCount[1000]+value9[1000])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016])/(evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016])/(evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]))))*((evtIdCount[1000]+value9[1000]-(evtIdCount[1007]+value9[1007]))/(evtIdCount[1000]+value9[1000]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[1011]/(1000.0*evtIdCount[1011])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1040]+value9[1040]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[1040]+value9[1040])-(evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1040]+value9[1040])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/(evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041]-(evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056])/(evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041]-(evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]))))*(((evtIdCount[1040]+value9[1040])-(evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]))/(evtIdCount[1040]+value9[1040]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value3[1051]/(1000.0*evtIdCount[1051])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1020]+value9[1020]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[1027]+value9[1027])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[1020]+value9[1020])-(evtIdCount[1027]+value9[1027]))/(evtIdCount[1020]+value9[1020])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036])/((evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021])-(evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036])/((evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021])-(evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]))))*(((evtIdCount[1020]+value9[1020])-(evtIdCount[1027]+value9[1027]))/(evtIdCount[1020]+value9[1020]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[1031]/(1000.0*evtIdCount[1031])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">联通运营商</Item>
              <Item typeName="String" key="Exp">联通</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]) }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/((evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]+evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021]+evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041])-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]+evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]+evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">全称呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/((evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]+evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021]+evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041])-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]+evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]+evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))))*(((evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{(value1[3011]+value1[3031]+value3[3051])/(1000.0*(evtIdCount[3011]+evtIdCount[3031]+evtIdCount[3051]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延&gt;9s占比</Item>
              <Item typeName="String" key="Exp">{100*(value5[3011]+value5[3031]+value5[3051])/(evtIdCount[3011]+evtIdCount[3031]+evtIdCount[3051])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB返回FDD比例</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3083]+value9[3083])/(evtIdCount[3081]+value9[3081])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB返回FDD时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3083]/(1000.0*evtIdCount[3083])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到WCDMA/GSM成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075])/(evtIdCount[3069]+value9[3069])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到WCDMA/GSM成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3076]+value9[3076])/(evtIdCount[3070]+value9[3070])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075]+evtIdCount[3076]+value9[3076])/(evtIdCount[3069]+value9[3069]+evtIdCount[3070]+value9[3070])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到WCDMA/GSM平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3075]/(1000.0*evtIdCount[3075])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到WCDMA/GSM平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3076]/(1000.0*evtIdCount[3076])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">Rxquality0-4级占比</Item>
              <Item typeName="String" key="Exp">{100*(Lte_652101+Lte_652102+Lte_652103+Lte_652104+Lte_652105)/Lte_65210C }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3000]+value9[3000]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3007]+value9[3007])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB接通率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))))*((evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3011]/(1000.0*evtIdCount[3011])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3040]+value9[3040]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3040]+value9[3040])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/(evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041]-(evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/(evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041]-(evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))))*(((evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3040]+value9[3040]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">WCDMA呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value3[3051]/(1000.0*evtIdCount[3051])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3020]+value9[3020]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3027]+value9[3027])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3020]+value9[3020])-(evtIdCount[3027]+value9[3027]))/(evtIdCount[3020]+value9[3020])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036])/((evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021])-(evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036])/((evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021])-(evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]))))*(((evtIdCount[3020]+value9[3020])-(evtIdCount[3027]+value9[3027]))/(evtIdCount[3020]+value9[3020]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3031]/(1000.0*evtIdCount[3031])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">电信运营商</Item>
              <Item typeName="String" key="Exp">电信</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]) }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/((evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]+evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021]+evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041])-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]+evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]+evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">全称呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]+evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]+evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/((evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]+evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021]+evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041])-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]+evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]+evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))))*(((evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040])-(evtIdCount[3007]+value9[3007]+evtIdCount[3027]+value9[3027]+evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3000]+value9[3000]+evtIdCount[3020]+value9[3020]+evtIdCount[3040]+value9[3040]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{(value1[3011]+value1[3031]+value3[3051])/(1000.0*(evtIdCount[3011]+evtIdCount[3031]+evtIdCount[3051]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫时延&gt;9s占比</Item>
              <Item typeName="String" key="Exp">{100*(value5[3011]+value5[3031]+value5[3051])/(evtIdCount[3011]+evtIdCount[3031]+evtIdCount[3051])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB返回FDD比例</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3083]+value9[3083])/(evtIdCount[3081]+value9[3081])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB返回FDD时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3083]/(1000.0*evtIdCount[3083])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到23G成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075])/(evtIdCount[3069]+value9[3069])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到23G成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3076]+value9[3076])/(evtIdCount[3070]+value9[3070])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075]+evtIdCount[3076]+value9[3076])/(evtIdCount[3069]+value9[3069]+evtIdCount[3070]+value9[3070])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">主叫CSFB回落到23G平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3075]/(1000.0*evtIdCount[3075])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">被叫CSFB回落到23G平均时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3076]/(1000.0*evtIdCount[3076])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">Rxquality0-4级占比</Item>
              <Item typeName="String" key="Exp">{100*(Lte_652101+Lte_652102+Lte_652103+Lte_652104+Lte_652105)/Lte_65210C }%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3000]+value9[3000]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3007]+value9[3007])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB接通率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3005]+value9[3005]+evtIdCount[3006]+value9[3006]+evtIdCount[3015]+value9[3015]+evtIdCount[3016]+value9[3016])/(evtIdCount[3000]+value9[3000]+evtIdCount[3001]+value9[3001]-(evtIdCount[3007]+value9[3007]+evtIdCount[3008]+value9[3008]))))*((evtIdCount[3000]+value9[3000]-(evtIdCount[3007]+value9[3007]))/(evtIdCount[3000]+value9[3000]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">CSFB呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3011]/(1000.0*evtIdCount[3011])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3040]+value9[3040]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3040]+value9[3040])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/(evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041]-(evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3045]+value9[3045]+evtIdCount[3046]+value9[3046]+evtIdCount[3055]+value9[3055]+evtIdCount[3056]+value9[3056])/(evtIdCount[3040]+value9[3040]+evtIdCount[3041]+value9[3041]-(evtIdCount[3047]+value9[3047]+evtIdCount[3048]+value9[3048]+evtIdCount[3057]+value9[3057]+evtIdCount[3058]+value9[3058]))))*(((evtIdCount[3040]+value9[3040])-(evtIdCount[3047]+value9[3047]+evtIdCount[3057]+value9[3057]))/(evtIdCount[3040]+value9[3040]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">3G呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value3[3051]/(1000.0*evtIdCount[3051])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3020]+value9[3020]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G未接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[3027]+value9[3027])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[3020]+value9[3020])-(evtIdCount[3027]+value9[3027]))/(evtIdCount[3020]+value9[3020])}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036])/((evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021])-(evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G全程呼叫完好率</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[3025]+value9[3025]+evtIdCount[3026]+value9[3026]+evtIdCount[3035]+value9[3035]+evtIdCount[3036]+value9[3036])/((evtIdCount[3020]+value9[3020]+evtIdCount[3021]+value9[3021])-(evtIdCount[3027]+value9[3027]+evtIdCount[3028]+value9[3028]))))*(((evtIdCount[3020]+value9[3020])-(evtIdCount[3027]+value9[3027]))/(evtIdCount[3020]+value9[3020]))}%</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">2G呼叫时延(s)</Item>
              <Item typeName="String" key="Exp">{value1[3031]/(1000.0*evtIdCount[3031])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">3</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>