﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45G700MHighReverseFlowCoverage
{
    public static class CellNeighborHelper
    {
        public static void DealCellParams(Dictionary<long, List<CellNeighbor>> cellInfosDic
          , Dictionary<long, CellParamInfo> nrCellInfos
          , Dictionary<long, CellParamInfo> lteCellInfos
          , out List<CellResult> nrCellResults
          , out List<CellResult> lteCellResults)
        {
            //以4G为基准
            Dictionary<long, string> nrCellRelations = new Dictionary<long, string>();
            Dictionary<long, string> lteCellRelations = new Dictionary<long, string>();
            //匹配邻区对信息
            foreach (var cell in cellInfosDic)
            {
                if (!lteCellInfos.TryGetValue(cell.Key, out var cellInfo))
                {
                    //HighReverseFlowCoverageConfig.Instance.WriteLog($"没有{cell.Key}小区工参", "Error");
                    continue;
                }

                CellRelation relation = getRelation(nrCellInfos, cell.Value, cellInfo);
                if (!string.IsNullOrEmpty(relation.SerialNumber))
                {
                    if (!nrCellRelations.TryGetValue(relation.NRCellInfo.CI, out var _))
                    {
                        nrCellRelations.Add(relation.NRCellInfo.CI, relation.SerialNumber);
                    }

                    if (!lteCellRelations.TryGetValue(relation.LTECellInfo.CI, out var _))
                    {
                        lteCellRelations.Add(relation.LTECellInfo.CI, relation.SerialNumber);
                    }
                }
            }

            //添加全量小区结果
            //Dictionary<long, CellResult> nrCellResultDic = new Dictionary<long, CellResult>();
            //Dictionary<long, CellResult> lteCellResultDic = new Dictionary<long, CellResult>();

            addCellResults(lteCellInfos, out lteCellResults, lteCellRelations);
            addCellResults(nrCellInfos, out nrCellResults, nrCellRelations);

            //nrCellResults = new List<CellResult>(nrCellResultDic.Values);
            //lteCellResults = new List<CellResult>(lteCellResultDic.Values);
        }

        private static CellRelation getRelation(Dictionary<long, CellParamInfo> nrCellInfos
            , List<CellNeighbor> neighborCells, CellParamInfo lteCellInfo)
        {
            var relation = new CellRelation();
            relation.LTECellInfo = lteCellInfo;
            double minDistance = 10000;
            foreach (var neighborCell in neighborCells)
            {
                if (nrCellInfos.TryGetValue(neighborCell.Nci, out var neighborCellInfo))
                {
                    double distance = lteCellInfo.Cell.GetDistance(neighborCellInfo.Cell);
                    if (distance < minDistance)
                    {
                        relation.NRCellInfo = neighborCellInfo;
                        relation.CellNeighborInfo = neighborCell;
                        relation.SerialNumber = neighborCellInfo.Enodebid.ToString();

                        minDistance = distance;
                    }
                }
            }

            return relation;
        }

        private static void addCellResults(Dictionary<long, CellParamInfo> cellInfos
            , out List<CellResult> cellResults, Dictionary<long, string> cellRelations)
        {
            cellResults = new List<CellResult>();
            foreach (var cell in cellInfos)
            {
                var ci = cell.Key;
                var cellRes = new CellResult(cell.Value, "");
                cellResults.Add(cellRes);
                //if (!cellResultDic.TryGetValue(ci, out var cellRes))
                //{
                //    cellRes = new CellResult(cell.Value, "");
                //    cellResultDic.Add(ci, cellRes);
                //}

                if (cellRelations.TryGetValue(ci, out var serialNumber))
                {
                    cellRes.SerialNumber = serialNumber;
                }
            }
        }
    }
}
