﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HandoverBehindTimeSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numSvrPccpch = new System.Windows.Forms.NumericUpDown();
            this.numMaxNCellPccpch = new System.Windows.Forms.NumericUpDown();
            this.numPccpchDiff = new System.Windows.Forms.NumericUpDown();
            this.numStaySecond = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.chkType = new System.Windows.Forms.CheckBox();
            this.chkSameBand = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numSvrPccpch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNCellPccpch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numStaySecond)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(108, 19);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "主服电平≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(84, 49);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(89, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "最强邻区电平≥";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(30, 80);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(143, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "最强邻区电平-主服电平≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(108, 111);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "持续时间≥";
            // 
            // numSvrPccpch
            // 
            this.numSvrPccpch.Location = new System.Drawing.Point(181, 14);
            this.numSvrPccpch.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numSvrPccpch.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numSvrPccpch.Name = "numSvrPccpch";
            this.numSvrPccpch.Size = new System.Drawing.Size(82, 21);
            this.numSvrPccpch.TabIndex = 1;
            this.numSvrPccpch.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSvrPccpch.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // numMaxNCellPccpch
            // 
            this.numMaxNCellPccpch.Location = new System.Drawing.Point(181, 44);
            this.numMaxNCellPccpch.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numMaxNCellPccpch.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMaxNCellPccpch.Name = "numMaxNCellPccpch";
            this.numMaxNCellPccpch.Size = new System.Drawing.Size(82, 21);
            this.numMaxNCellPccpch.TabIndex = 1;
            this.numMaxNCellPccpch.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxNCellPccpch.Value = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            // 
            // numPccpchDiff
            // 
            this.numPccpchDiff.Location = new System.Drawing.Point(181, 75);
            this.numPccpchDiff.Maximum = new decimal(new int[] {
            130,
            0,
            0,
            0});
            this.numPccpchDiff.Minimum = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numPccpchDiff.Name = "numPccpchDiff";
            this.numPccpchDiff.Size = new System.Drawing.Size(82, 21);
            this.numPccpchDiff.TabIndex = 1;
            this.numPccpchDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchDiff.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numStaySecond
            // 
            this.numStaySecond.Location = new System.Drawing.Point(181, 106);
            this.numStaySecond.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numStaySecond.Name = "numStaySecond";
            this.numStaySecond.Size = new System.Drawing.Size(82, 21);
            this.numStaySecond.TabIndex = 1;
            this.numStaySecond.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numStaySecond.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(123, 201);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(218, 201);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(270, 19);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(23, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "dBm";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(270, 49);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(23, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "dBm";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(270, 80);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "dB";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(270, 111);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "秒";
            // 
            // chkType
            // 
            this.chkType.AutoSize = true;
            this.chkType.Checked = true;
            this.chkType.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkType.Location = new System.Drawing.Point(108, 141);
            this.chkType.Name = "chkType";
            this.chkType.Size = new System.Drawing.Size(108, 16);
            this.chkType.TabIndex = 3;
            this.chkType.Text = "采样点为连接态";
            this.chkType.UseVisualStyleBackColor = true;
            // 
            // chkSameBand
            // 
            this.chkSameBand.AutoSize = true;
            this.chkSameBand.Checked = true;
            this.chkSameBand.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSameBand.Location = new System.Drawing.Point(108, 166);
            this.chkSameBand.Name = "chkSameBand";
            this.chkSameBand.Size = new System.Drawing.Size(108, 16);
            this.chkSameBand.TabIndex = 4;
            this.chkSameBand.Text = "只判断同频小区";
            this.chkSameBand.UseVisualStyleBackColor = true;
            // 
            // HandoverBehindTimeSettingForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(323, 239);
            this.Controls.Add(this.chkSameBand);
            this.Controls.Add(this.chkType);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numStaySecond);
            this.Controls.Add(this.numPccpchDiff);
            this.Controls.Add(this.numMaxNCellPccpch);
            this.Controls.Add(this.numSvrPccpch);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "HandoverBehindTimeSettingForm";
            this.Text = "切换不及时条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numSvrPccpch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNCellPccpch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numStaySecond)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numSvrPccpch;
        private System.Windows.Forms.NumericUpDown numMaxNCellPccpch;
        private System.Windows.Forms.NumericUpDown numPccpchDiff;
        private System.Windows.Forms.NumericUpDown numStaySecond;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.CheckBox chkType;
        private System.Windows.Forms.CheckBox chkSameBand;
    }
}