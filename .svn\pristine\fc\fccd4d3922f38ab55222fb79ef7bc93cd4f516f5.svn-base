﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTScanNBIOTLowSINRRoadQuery : ZTScanLTELowSINRRoadQuery
    {
        public ZTScanNBIOTLowSINRRoadQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "SINR质差路段_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33016, this.Name);
        }
    }
}
