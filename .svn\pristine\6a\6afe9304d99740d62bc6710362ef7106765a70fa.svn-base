using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class EventDateLabelSettingBox : BaseDialog
    {
        public EventDateLabelSettingBox()
        {
            InitializeComponent();
            cbxDateFormat.SelectedIndex = cbxDateFormat.Items.Count - 1;
        }
        public string StrFormat
        {
            get { return cbxDateFormat.SelectedItem.ToString(); }
        }
        public bool IsShow
        {
            get { return checkBoxShow.Checked; }
        }
        private void checkBoxShow_CheckedChanged(object sender, EventArgs e)
        {
            cbxDateFormat.Enabled = checkBoxShow.Checked;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}