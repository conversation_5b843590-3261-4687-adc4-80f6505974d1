﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class NearWeakCoverCell : IComparable<NearWeakCoverCell>
    {
        public NearWeakCoverCell(LTECell cell)
        {
            this.Cell = cell;
            TestPoints = new List<TestPoint>();
            RsrpMin = float.MaxValue;
            RsrpMax = float.MinValue;
        }

        public int Sn
        {
            get;
            set;
        }

        public LTECell Cell
        {
            get;
            private set;
        }

        public string CellName
        {
            get { return this.Cell.Name; }
        }

        public int CellID
        {
            get { return this.Cell.SCellID; }
        }

        public List<TestPoint> TestPoints
        { get; private set; }

        public float RsrpMin
        {
            get;
            private set;
        }

        public float RsrpMax
        {
            get;
            private set;
        }

        public float RsrpAvg
        {
            get;
            private set;
        }

        public float? SinrMin
        {
            get;
            private set;
        }

        public float? SinrMax
        {
            get;
            private set;
        }

        public float? SinrAvg
        {
            get;
            private set;
        }

        public int TestPointNum
        {
            get { return TestPoints.Count; }
        }

        public int MainCellPointNum
        {
            get;
            private set;
        }

        public int NbCellPointNum
        {
            get { return TestPoints.Count - MainCellPointNum; }
        }

        public string RoadDesc
        {
            get;
            private set;
        }

        private float rsrpTotal = 0;
        private float sinrTotal = 0;
        private int sinrNum = 0;
        private double distanceTotal = 0;
        public double DistanceAvg
        {
            get;
            private set;
        }

        internal void AddPoint(TestPoint tp, double distance, float rsrp, bool isMainCell)
        {
            distanceTotal += distance;
            TestPoints.Add(tp);
            if (isMainCell)
            {
                MainCellPointNum++;
            }
            rsrpTotal += rsrp;
            RsrpMin = Math.Min(RsrpMin, rsrp);
            RsrpMax = Math.Max(RsrpMax, rsrp);

            object value = tp["LTESCAN_TopN_CELL_Specific_RSSINR"];
            if (value != null)
            {
                float sinr = float.Parse(value.ToString());
                if (sinr >= -50 && sinr <= 50)
                {
                    sinrNum++;
                    sinrTotal += sinr;
                    SinrMin = SinrMin == null ? sinr : Math.Min((float)SinrMin, sinr);
                    SinrMax = SinrMax == null ? sinr : Math.Max((float)SinrMax, sinr);
                }
            }
        }

        internal void MakeSummary()
        {
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(TestPoints[0].Longitude);
            lats.Add(TestPoints[0].Latitude);
            lngs.Add(TestPoints[TestPoints.Count / 2].Longitude);
            lats.Add(TestPoints[TestPoints.Count / 2].Latitude);
            lngs.Add(TestPoints[TestPoints.Count - 1].Longitude);
            lats.Add(TestPoints[TestPoints.Count - 1].Latitude);
            this.RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            RsrpAvg = (float)Math.Round(rsrpTotal / TestPointNum, 2);
            if (sinrNum!=0)
            {
                SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);
            }
            DistanceAvg = Math.Round(distanceTotal / TestPointNum, 2);
        }

        #region IComparable<NearWeakCoverCell> 成员

        public int CompareTo(NearWeakCoverCell other)
        {
            return other.TestPointNum.CompareTo(this.TestPointNum);
        }

        #endregion
    }
}
