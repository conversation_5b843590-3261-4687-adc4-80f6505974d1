﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakSINRPoint
    {
        public ICell Cell { get; private set; }
        public string CellName { get; private set; }
        public int? EARFCN { get; private set; }
        public int? PCI { get; private set; }
        public int? TAC { get; private set; }
        public long? NCI { get; private set; }

        public double PntLng
        {
            get { return tp.Longitude; }
        }
        public double PntLat
        {
            get { return tp.Latitude; }
        }
        public string Distance
        {
            get
            {
                if (Cell is UnknowCell)
                {
                    return "-";
                }
                return tp.Distance2(this.Cell.Longitude, this.Cell.Latitude).ToString();
            }
        }
        protected TestPoint tp = null;
        public TestPoint TestPoint
        {
            get { return tp; }
        }
        private readonly NRReasonBase reason;
        public NRReasonBase Reason
        {
            get { return reason; }
        }
        public string ReasonName
        {
            get { return reason.Name; }
        }
        private readonly FileInfo fi = null;
        public string FileName
        {
            get { return fi.Name; }
        }
        public float SINR { get; private set; }
        public float RSRP { get; private set; }
        readonly bool isAnaLte;

        public NRWeakSINRPoint(FileInfo fi, TestPoint tp, NRReasonBase reason, bool isAnaLte)
        {
            this.fi = fi;
            this.tp = tp;
            this.reason = reason;
            this.isAnaLte = isAnaLte;
        }

        public bool Calculate()
        {
            Cell = tp.GetMainCell_NR();
            if (Cell == null)
            {
                bool isSet = setUnknowCell(NRTpHelper.NrTpManager);

                if (isAnaLte && !isSet)
                {
                    Cell = tp.GetMainCell_LTE();
                    if (Cell == null)
                    {
                        isSet = setUnknowCell(NRTpHelper.NrLteTpManager);
                    }
                }
                if (!isSet)
                {
                    return false;
                }
            }

            CellName = Cell.Name;
            setCellInfo();

            RSRP = GetRSRP(tp);
            SINR = GetSINR(tp);
            return true;
        }

        private void setCellInfo()
        {
            if (Cell is UnknowCell)
            {
                UnknowCell cell = Cell as UnknowCell;
                string earfcn = cell.Token.Substring(0, cell.Token.IndexOf('_'));
                if (!string.IsNullOrEmpty(earfcn))
                {
                    EARFCN = int.Parse(earfcn);
                }
                int idx = cell.Token.IndexOf("_");
                if (idx < cell.Token.Length - 1)
                {
                    PCI = int.Parse(cell.Token.Substring(idx + 1));
                }
            }
            else if (Cell is LTECell)
            {
                LTECell lteCell = Cell as LTECell;
                EARFCN = lteCell.EARFCN;
                PCI = lteCell.PCI;
                TAC = lteCell.TAC;
                NCI = lteCell.ECI;
            }
            else
            {
                NRCell nrCell = Cell as NRCell;
                EARFCN = nrCell.SSBARFCN;
                PCI = nrCell.PCI;
                TAC = nrCell.TAC;
                NCI = nrCell.NCI;
            }
        }

        private bool setUnknowCell(NRTpManagerBase cond)
        {
            int? earfcn = (int?)cond.GetEARFCN(tp);
            int? pci = (int?)cond.GetPCI(tp);
            if (earfcn != null && pci != null)
            {
                string token = earfcn.ToString() + "_" + pci.ToString();
                Cell = new UnknowCell(token);
                return true;
            }
            return false;
        }

        protected float GetRSRP(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (isAnaLte && rsrp == null)
            {
                rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            }

            if (rsrp == null)
            {
                return -1000000;
            }
            return float.Parse(rsrp.ToString());
        }

        protected float GetSINR(TestPoint tp)
        {
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            if (isAnaLte && sinr == null)
            {
                sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            }
            if (sinr == null)
            {
                return -1000000;
            }
            return float.Parse(sinr.ToString());
        }
    }
}
