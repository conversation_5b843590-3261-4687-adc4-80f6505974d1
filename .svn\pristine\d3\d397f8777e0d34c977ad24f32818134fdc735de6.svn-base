﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc.ZTLteScanGoodRsrpPoorSinr;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GoodRsrpPoorSinrSettingDlg_LteScan : BaseDialog
    {
        public GoodRsrpPoorSinrSettingDlg_LteScan()
        {
            InitializeComponent();
        }

        public FuncCondition Condition
        {
            get
            {
                FuncCondition cond = new FuncCondition();
                cond.Rsrp = (float)numRsrp.Value;
                cond.Sinr = (float)numSinr.Value;
                cond.CheckBand = chkBand.Checked;
                cond.CoverBand = (int)numBand.Value;
                return cond;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                numRsrp.Value = (decimal)value.Rsrp;
                numSinr.Value = (decimal)value.Sinr;
                chkBand.Checked = value.CheckBand;
                numBand.Value = (decimal)value.CoverBand;
            }
        }

    }
}
