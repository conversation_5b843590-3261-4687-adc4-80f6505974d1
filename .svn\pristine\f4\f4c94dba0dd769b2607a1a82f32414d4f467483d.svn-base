﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTReportEventStatQueryByFileListForm_GZ
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTReportEventStatQueryByFileListForm_GZ));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView19 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.gridView21 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl2 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl3 = new DevExpress.XtraGrid.GridControl();
            this.gridView22 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl3 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl4 = new DevExpress.XtraGrid.GridControl();
            this.gridView23 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn90 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn91 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn92 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn93 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn94 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn95 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn96 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl4 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl5 = new DevExpress.XtraGrid.GridControl();
            this.gridView24 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn97 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn98 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn99 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn100 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn101 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn102 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn103 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn104 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn105 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn106 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn107 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn108 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn109 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn110 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn111 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn112 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl5 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl6 = new DevExpress.XtraGrid.GridControl();
            this.gridView25 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn113 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn114 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn115 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn116 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn117 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn118 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn119 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn120 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn121 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn122 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn123 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn124 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn125 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn126 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn127 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn128 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl6 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl7 = new DevExpress.XtraGrid.GridControl();
            this.gridView26 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn135 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn136 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn137 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn138 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn139 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn140 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn141 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn142 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn143 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn144 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn193 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn194 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn195 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn196 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn197 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn198 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl7 = new DevExpress.XtraTab.XtraTabControl();
            this.gridControl11 = new DevExpress.XtraGrid.GridControl();
            this.gridView27 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn199 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn200 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn201 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn202 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn203 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn204 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn205 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn206 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn207 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn208 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn209 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn210 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn211 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn212 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn213 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn214 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl11 = new DevExpress.XtraTab.XtraTabControl();
            this.gridView15 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView16 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabControlChartTDProfile = new DevExpress.XtraTab.XtraTabControl();
            this.gridControlTDProfile = new DevExpress.XtraGrid.GridControl();
            this.gridView28 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridViewTDProfile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView18 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl8 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlGSMDropDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlChartGSMDropReason = new DevExpress.XtraTab.XtraTabControl();
            this.gridControlGSMDropReason = new DevExpress.XtraGrid.GridControl();
            this.gridView29 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridViewGSMDropReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView17 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn129 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn131 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn132 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn130 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn133 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn134 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl9 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlGSMBlockDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlChartGSMBlockReason = new DevExpress.XtraTab.XtraTabControl();
            this.gridControlGSMBlockReason = new DevExpress.XtraGrid.GridControl();
            this.gridView30 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridViewGSMBlockReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn145 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn146 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn147 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn148 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn149 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn150 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl11 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlTDDropDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlChartTDDropReason = new DevExpress.XtraTab.XtraTabControl();
            this.gridControlTDDropReason = new DevExpress.XtraGrid.GridControl();
            this.gridView31 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridViewTDDropReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView13 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn151 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn152 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn153 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn154 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn155 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn156 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl13 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tabControlTDBlockDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabControlChartTDBlockReason = new DevExpress.XtraTab.XtraTabControl();
            this.gridControlTDBlockReason = new DevExpress.XtraGrid.GridControl();
            this.gridView32 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridViewTDBlockReason = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView14 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn157 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn158 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn159 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn160 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn215 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn216 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabLowSpeed = new System.Windows.Forms.TabPage();
            this.gridControlLowSpeed = new DevExpress.XtraGrid.GridControl();
            this.gridViewLowSpeed = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn170 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn171 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn172 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn173 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn174 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn175 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn176 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabWeakCover = new System.Windows.Forms.TabPage();
            this.gridControlWeakCover = new DevExpress.XtraGrid.GridControl();
            this.gridViewWeakCover = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn162 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn163 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn164 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn165 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn166 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn167 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn168 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView20 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView28)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl8)).BeginInit();
            this.splitContainerControl8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMDropDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl9)).BeginInit();
            this.splitContainerControl9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMBlockDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl11)).BeginInit();
            this.splitContainerControl11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDDropDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView31)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDDropReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl13)).BeginInit();
            this.splitContainerControl13.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDBlockDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView32)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDBlockReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView14)).BeginInit();
            this.tabLowSpeed.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLowSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLowSpeed)).BeginInit();
            this.tabWeakCover.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlWeakCover)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewWeakCover)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView20)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Size = new System.Drawing.Size(200, 100);
            this.splitContainerControl2.TabIndex = 0;
            // 
            // gridControl1
            // 
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView19;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(400, 200);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView19});
            // 
            // gridView19
            // 
            this.gridView19.GridControl = this.gridControl1;
            this.gridView19.Name = "gridView19";
            // 
            // gridView1
            // 
            this.gridView1.Name = "gridView1";
            // 
            // gridColumn1
            // 
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            this.gridColumn2.Name = "gridColumn2";
            // 
            // gridColumn3
            // 
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn5
            // 
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            this.gridColumn6.Name = "gridColumn6";
            // 
            // gridColumn7
            // 
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            this.gridColumn8.Name = "gridColumn8";
            // 
            // gridColumn9
            // 
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            this.gridColumn10.Name = "gridColumn10";
            // 
            // gridColumn11
            // 
            this.gridColumn11.Name = "gridColumn11";
            // 
            // gridColumn12
            // 
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            this.gridColumn14.Name = "gridColumn14";
            // 
            // gridColumn15
            // 
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn16
            // 
            this.gridColumn16.Name = "gridColumn16";
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl1.TabIndex = 0;
            // 
            // gridView2
            // 
            this.gridView2.Name = "gridView2";
            // 
            // gridColumn33
            // 
            this.gridColumn33.Name = "gridColumn33";
            // 
            // gridColumn34
            // 
            this.gridColumn34.Name = "gridColumn34";
            // 
            // gridColumn35
            // 
            this.gridColumn35.Name = "gridColumn35";
            // 
            // gridColumn36
            // 
            this.gridColumn36.Name = "gridColumn36";
            // 
            // gridColumn37
            // 
            this.gridColumn37.Name = "gridColumn37";
            // 
            // gridColumn38
            // 
            this.gridColumn38.Name = "gridColumn38";
            // 
            // gridColumn39
            // 
            this.gridColumn39.Name = "gridColumn39";
            // 
            // gridColumn40
            // 
            this.gridColumn40.Name = "gridColumn40";
            // 
            // gridColumn41
            // 
            this.gridColumn41.Name = "gridColumn41";
            // 
            // gridColumn42
            // 
            this.gridColumn42.Name = "gridColumn42";
            // 
            // gridColumn43
            // 
            this.gridColumn43.Name = "gridColumn43";
            // 
            // gridColumn44
            // 
            this.gridColumn44.Name = "gridColumn44";
            // 
            // gridColumn45
            // 
            this.gridColumn45.Name = "gridColumn45";
            // 
            // gridColumn46
            // 
            this.gridColumn46.Name = "gridColumn46";
            // 
            // gridColumn47
            // 
            this.gridColumn47.Name = "gridColumn47";
            // 
            // gridColumn48
            // 
            this.gridColumn48.Name = "gridColumn48";
            // 
            // gridControl2
            // 
            this.gridControl2.Location = new System.Drawing.Point(0, 0);
            this.gridControl2.MainView = this.gridView21;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(400, 200);
            this.gridControl2.TabIndex = 0;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView21});
            // 
            // gridView21
            // 
            this.gridView21.GridControl = this.gridControl2;
            this.gridView21.Name = "gridView21";
            // 
            // gridView3
            // 
            this.gridView3.Name = "gridView3";
            // 
            // gridColumn49
            // 
            this.gridColumn49.Name = "gridColumn49";
            // 
            // gridColumn50
            // 
            this.gridColumn50.Name = "gridColumn50";
            // 
            // gridColumn51
            // 
            this.gridColumn51.Name = "gridColumn51";
            // 
            // gridColumn52
            // 
            this.gridColumn52.Name = "gridColumn52";
            // 
            // gridColumn53
            // 
            this.gridColumn53.Name = "gridColumn53";
            // 
            // gridColumn54
            // 
            this.gridColumn54.Name = "gridColumn54";
            // 
            // gridColumn55
            // 
            this.gridColumn55.Name = "gridColumn55";
            // 
            // gridColumn56
            // 
            this.gridColumn56.Name = "gridColumn56";
            // 
            // gridColumn57
            // 
            this.gridColumn57.Name = "gridColumn57";
            // 
            // gridColumn58
            // 
            this.gridColumn58.Name = "gridColumn58";
            // 
            // gridColumn59
            // 
            this.gridColumn59.Name = "gridColumn59";
            // 
            // gridColumn60
            // 
            this.gridColumn60.Name = "gridColumn60";
            // 
            // gridColumn61
            // 
            this.gridColumn61.Name = "gridColumn61";
            // 
            // gridColumn62
            // 
            this.gridColumn62.Name = "gridColumn62";
            // 
            // gridColumn63
            // 
            this.gridColumn63.Name = "gridColumn63";
            // 
            // gridColumn64
            // 
            this.gridColumn64.Name = "gridColumn64";
            // 
            // xtraTabControl2
            // 
            this.xtraTabControl2.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl2.Name = "xtraTabControl2";
            this.xtraTabControl2.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl2.TabIndex = 0;
            // 
            // gridControl3
            // 
            this.gridControl3.Location = new System.Drawing.Point(0, 0);
            this.gridControl3.MainView = this.gridView22;
            this.gridControl3.Name = "gridControl3";
            this.gridControl3.Size = new System.Drawing.Size(400, 200);
            this.gridControl3.TabIndex = 0;
            this.gridControl3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView22});
            // 
            // gridView22
            // 
            this.gridView22.GridControl = this.gridControl3;
            this.gridView22.Name = "gridView22";
            // 
            // gridView4
            // 
            this.gridView4.Name = "gridView4";
            // 
            // gridColumn65
            // 
            this.gridColumn65.Name = "gridColumn65";
            // 
            // gridColumn66
            // 
            this.gridColumn66.Name = "gridColumn66";
            // 
            // gridColumn67
            // 
            this.gridColumn67.Name = "gridColumn67";
            // 
            // gridColumn68
            // 
            this.gridColumn68.Name = "gridColumn68";
            // 
            // gridColumn69
            // 
            this.gridColumn69.Name = "gridColumn69";
            // 
            // gridColumn70
            // 
            this.gridColumn70.Name = "gridColumn70";
            // 
            // gridColumn71
            // 
            this.gridColumn71.Name = "gridColumn71";
            // 
            // gridColumn72
            // 
            this.gridColumn72.Name = "gridColumn72";
            // 
            // gridColumn73
            // 
            this.gridColumn73.Name = "gridColumn73";
            // 
            // gridColumn74
            // 
            this.gridColumn74.Name = "gridColumn74";
            // 
            // gridColumn75
            // 
            this.gridColumn75.Name = "gridColumn75";
            // 
            // gridColumn76
            // 
            this.gridColumn76.Name = "gridColumn76";
            // 
            // gridColumn77
            // 
            this.gridColumn77.Name = "gridColumn77";
            // 
            // gridColumn78
            // 
            this.gridColumn78.Name = "gridColumn78";
            // 
            // gridColumn79
            // 
            this.gridColumn79.Name = "gridColumn79";
            // 
            // gridColumn80
            // 
            this.gridColumn80.Name = "gridColumn80";
            // 
            // xtraTabControl3
            // 
            this.xtraTabControl3.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl3.Name = "xtraTabControl3";
            this.xtraTabControl3.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl3.TabIndex = 0;
            // 
            // gridControl4
            // 
            this.gridControl4.Location = new System.Drawing.Point(0, 0);
            this.gridControl4.MainView = this.gridView23;
            this.gridControl4.Name = "gridControl4";
            this.gridControl4.Size = new System.Drawing.Size(400, 200);
            this.gridControl4.TabIndex = 0;
            this.gridControl4.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView23});
            // 
            // gridView23
            // 
            this.gridView23.GridControl = this.gridControl4;
            this.gridView23.Name = "gridView23";
            // 
            // gridView5
            // 
            this.gridView5.Name = "gridView5";
            // 
            // gridColumn81
            // 
            this.gridColumn81.Name = "gridColumn81";
            // 
            // gridColumn82
            // 
            this.gridColumn82.Name = "gridColumn82";
            // 
            // gridColumn83
            // 
            this.gridColumn83.Name = "gridColumn83";
            // 
            // gridColumn84
            // 
            this.gridColumn84.Name = "gridColumn84";
            // 
            // gridColumn85
            // 
            this.gridColumn85.Name = "gridColumn85";
            // 
            // gridColumn86
            // 
            this.gridColumn86.Name = "gridColumn86";
            // 
            // gridColumn87
            // 
            this.gridColumn87.Name = "gridColumn87";
            // 
            // gridColumn88
            // 
            this.gridColumn88.Name = "gridColumn88";
            // 
            // gridColumn89
            // 
            this.gridColumn89.Name = "gridColumn89";
            // 
            // gridColumn90
            // 
            this.gridColumn90.Name = "gridColumn90";
            // 
            // gridColumn91
            // 
            this.gridColumn91.Name = "gridColumn91";
            // 
            // gridColumn92
            // 
            this.gridColumn92.Name = "gridColumn92";
            // 
            // gridColumn93
            // 
            this.gridColumn93.Name = "gridColumn93";
            // 
            // gridColumn94
            // 
            this.gridColumn94.Name = "gridColumn94";
            // 
            // gridColumn95
            // 
            this.gridColumn95.Name = "gridColumn95";
            // 
            // gridColumn96
            // 
            this.gridColumn96.Name = "gridColumn96";
            // 
            // xtraTabControl4
            // 
            this.xtraTabControl4.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl4.Name = "xtraTabControl4";
            this.xtraTabControl4.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl4.TabIndex = 0;
            // 
            // gridControl5
            // 
            this.gridControl5.Location = new System.Drawing.Point(0, 0);
            this.gridControl5.MainView = this.gridView24;
            this.gridControl5.Name = "gridControl5";
            this.gridControl5.Size = new System.Drawing.Size(400, 200);
            this.gridControl5.TabIndex = 0;
            this.gridControl5.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView24});
            // 
            // gridView24
            // 
            this.gridView24.GridControl = this.gridControl5;
            this.gridView24.Name = "gridView24";
            // 
            // gridView6
            // 
            this.gridView6.Name = "gridView6";
            // 
            // gridColumn97
            // 
            this.gridColumn97.Name = "gridColumn97";
            // 
            // gridColumn98
            // 
            this.gridColumn98.Name = "gridColumn98";
            // 
            // gridColumn99
            // 
            this.gridColumn99.Name = "gridColumn99";
            // 
            // gridColumn100
            // 
            this.gridColumn100.Name = "gridColumn100";
            // 
            // gridColumn101
            // 
            this.gridColumn101.Name = "gridColumn101";
            // 
            // gridColumn102
            // 
            this.gridColumn102.Name = "gridColumn102";
            // 
            // gridColumn103
            // 
            this.gridColumn103.Name = "gridColumn103";
            // 
            // gridColumn104
            // 
            this.gridColumn104.Name = "gridColumn104";
            // 
            // gridColumn105
            // 
            this.gridColumn105.Name = "gridColumn105";
            // 
            // gridColumn106
            // 
            this.gridColumn106.Name = "gridColumn106";
            // 
            // gridColumn107
            // 
            this.gridColumn107.Name = "gridColumn107";
            // 
            // gridColumn108
            // 
            this.gridColumn108.Name = "gridColumn108";
            // 
            // gridColumn109
            // 
            this.gridColumn109.Name = "gridColumn109";
            // 
            // gridColumn110
            // 
            this.gridColumn110.Name = "gridColumn110";
            // 
            // gridColumn111
            // 
            this.gridColumn111.Name = "gridColumn111";
            // 
            // gridColumn112
            // 
            this.gridColumn112.Name = "gridColumn112";
            // 
            // xtraTabControl5
            // 
            this.xtraTabControl5.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl5.Name = "xtraTabControl5";
            this.xtraTabControl5.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl5.TabIndex = 0;
            // 
            // gridControl6
            // 
            this.gridControl6.Location = new System.Drawing.Point(0, 0);
            this.gridControl6.MainView = this.gridView25;
            this.gridControl6.Name = "gridControl6";
            this.gridControl6.Size = new System.Drawing.Size(400, 200);
            this.gridControl6.TabIndex = 0;
            this.gridControl6.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView25});
            // 
            // gridView25
            // 
            this.gridView25.GridControl = this.gridControl6;
            this.gridView25.Name = "gridView25";
            // 
            // gridView7
            // 
            this.gridView7.Name = "gridView7";
            // 
            // gridColumn113
            // 
            this.gridColumn113.Name = "gridColumn113";
            // 
            // gridColumn114
            // 
            this.gridColumn114.Name = "gridColumn114";
            // 
            // gridColumn115
            // 
            this.gridColumn115.Name = "gridColumn115";
            // 
            // gridColumn116
            // 
            this.gridColumn116.Name = "gridColumn116";
            // 
            // gridColumn117
            // 
            this.gridColumn117.Name = "gridColumn117";
            // 
            // gridColumn118
            // 
            this.gridColumn118.Name = "gridColumn118";
            // 
            // gridColumn119
            // 
            this.gridColumn119.Name = "gridColumn119";
            // 
            // gridColumn120
            // 
            this.gridColumn120.Name = "gridColumn120";
            // 
            // gridColumn121
            // 
            this.gridColumn121.Name = "gridColumn121";
            // 
            // gridColumn122
            // 
            this.gridColumn122.Name = "gridColumn122";
            // 
            // gridColumn123
            // 
            this.gridColumn123.Name = "gridColumn123";
            // 
            // gridColumn124
            // 
            this.gridColumn124.Name = "gridColumn124";
            // 
            // gridColumn125
            // 
            this.gridColumn125.Name = "gridColumn125";
            // 
            // gridColumn126
            // 
            this.gridColumn126.Name = "gridColumn126";
            // 
            // gridColumn127
            // 
            this.gridColumn127.Name = "gridColumn127";
            // 
            // gridColumn128
            // 
            this.gridColumn128.Name = "gridColumn128";
            // 
            // xtraTabControl6
            // 
            this.xtraTabControl6.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl6.Name = "xtraTabControl6";
            this.xtraTabControl6.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl6.TabIndex = 0;
            // 
            // gridControl7
            // 
            this.gridControl7.Location = new System.Drawing.Point(0, 0);
            this.gridControl7.MainView = this.gridView26;
            this.gridControl7.Name = "gridControl7";
            this.gridControl7.Size = new System.Drawing.Size(400, 200);
            this.gridControl7.TabIndex = 0;
            this.gridControl7.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView26});
            // 
            // gridView26
            // 
            this.gridView26.GridControl = this.gridControl7;
            this.gridView26.Name = "gridView26";
            // 
            // gridView8
            // 
            this.gridView8.Name = "gridView8";
            // 
            // gridColumn135
            // 
            this.gridColumn135.Name = "gridColumn135";
            // 
            // gridColumn136
            // 
            this.gridColumn136.Name = "gridColumn136";
            // 
            // gridColumn137
            // 
            this.gridColumn137.Name = "gridColumn137";
            // 
            // gridColumn138
            // 
            this.gridColumn138.Name = "gridColumn138";
            // 
            // gridColumn139
            // 
            this.gridColumn139.Name = "gridColumn139";
            // 
            // gridColumn140
            // 
            this.gridColumn140.Name = "gridColumn140";
            // 
            // gridColumn141
            // 
            this.gridColumn141.Name = "gridColumn141";
            // 
            // gridColumn142
            // 
            this.gridColumn142.Name = "gridColumn142";
            // 
            // gridColumn143
            // 
            this.gridColumn143.Name = "gridColumn143";
            // 
            // gridColumn144
            // 
            this.gridColumn144.Name = "gridColumn144";
            // 
            // gridColumn193
            // 
            this.gridColumn193.Name = "gridColumn193";
            // 
            // gridColumn194
            // 
            this.gridColumn194.Name = "gridColumn194";
            // 
            // gridColumn195
            // 
            this.gridColumn195.Name = "gridColumn195";
            // 
            // gridColumn196
            // 
            this.gridColumn196.Name = "gridColumn196";
            // 
            // gridColumn197
            // 
            this.gridColumn197.Name = "gridColumn197";
            // 
            // gridColumn198
            // 
            this.gridColumn198.Name = "gridColumn198";
            // 
            // xtraTabControl7
            // 
            this.xtraTabControl7.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl7.Name = "xtraTabControl7";
            this.xtraTabControl7.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl7.TabIndex = 0;
            // 
            // gridControl11
            // 
            this.gridControl11.Location = new System.Drawing.Point(0, 0);
            this.gridControl11.MainView = this.gridView27;
            this.gridControl11.Name = "gridControl11";
            this.gridControl11.Size = new System.Drawing.Size(400, 200);
            this.gridControl11.TabIndex = 0;
            this.gridControl11.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView27});
            // 
            // gridView27
            // 
            this.gridView27.GridControl = this.gridControl11;
            this.gridView27.Name = "gridView27";
            // 
            // gridView12
            // 
            this.gridView12.Name = "gridView12";
            // 
            // gridColumn199
            // 
            this.gridColumn199.Name = "gridColumn199";
            // 
            // gridColumn200
            // 
            this.gridColumn200.Name = "gridColumn200";
            // 
            // gridColumn201
            // 
            this.gridColumn201.Name = "gridColumn201";
            // 
            // gridColumn202
            // 
            this.gridColumn202.Name = "gridColumn202";
            // 
            // gridColumn203
            // 
            this.gridColumn203.Name = "gridColumn203";
            // 
            // gridColumn204
            // 
            this.gridColumn204.Name = "gridColumn204";
            // 
            // gridColumn205
            // 
            this.gridColumn205.Name = "gridColumn205";
            // 
            // gridColumn206
            // 
            this.gridColumn206.Name = "gridColumn206";
            // 
            // gridColumn207
            // 
            this.gridColumn207.Name = "gridColumn207";
            // 
            // gridColumn208
            // 
            this.gridColumn208.Name = "gridColumn208";
            // 
            // gridColumn209
            // 
            this.gridColumn209.Name = "gridColumn209";
            // 
            // gridColumn210
            // 
            this.gridColumn210.Name = "gridColumn210";
            // 
            // gridColumn211
            // 
            this.gridColumn211.Name = "gridColumn211";
            // 
            // gridColumn212
            // 
            this.gridColumn212.Name = "gridColumn212";
            // 
            // gridColumn213
            // 
            this.gridColumn213.Name = "gridColumn213";
            // 
            // gridColumn214
            // 
            this.gridColumn214.Name = "gridColumn214";
            // 
            // xtraTabControl11
            // 
            this.xtraTabControl11.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl11.Name = "xtraTabControl11";
            this.xtraTabControl11.Size = new System.Drawing.Size(300, 300);
            this.xtraTabControl11.TabIndex = 0;
            // 
            // gridView15
            // 
            this.gridView15.Name = "gridView15";
            // 
            // gridView16
            // 
            this.gridView16.Name = "gridView16";
            // 
            // tabControlChartTDProfile
            // 
            this.tabControlChartTDProfile.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartTDProfile.Name = "tabControlChartTDProfile";
            this.tabControlChartTDProfile.Size = new System.Drawing.Size(300, 300);
            this.tabControlChartTDProfile.TabIndex = 0;
            // 
            // gridControlTDProfile
            // 
            this.gridControlTDProfile.Location = new System.Drawing.Point(0, 0);
            this.gridControlTDProfile.MainView = this.gridView28;
            this.gridControlTDProfile.Name = "gridControlTDProfile";
            this.gridControlTDProfile.Size = new System.Drawing.Size(400, 200);
            this.gridControlTDProfile.TabIndex = 0;
            this.gridControlTDProfile.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView28});
            // 
            // gridView28
            // 
            this.gridView28.GridControl = this.gridControlTDProfile;
            this.gridView28.Name = "gridView28";
            // 
            // gridViewTDProfile
            // 
            this.gridViewTDProfile.Name = "gridViewTDProfile";
            // 
            // gridView9
            // 
            this.gridView9.Name = "gridView9";
            // 
            // gridView18
            // 
            this.gridView18.Name = "gridView18";
            // 
            // gridColumn17
            // 
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn18
            // 
            this.gridColumn18.Name = "gridColumn18";
            // 
            // gridColumn19
            // 
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            this.gridColumn21.Name = "gridColumn21";
            // 
            // gridColumn22
            // 
            this.gridColumn22.Name = "gridColumn22";
            // 
            // gridColumn23
            // 
            this.gridColumn23.Name = "gridColumn23";
            // 
            // gridColumn24
            // 
            this.gridColumn24.Name = "gridColumn24";
            // 
            // gridColumn25
            // 
            this.gridColumn25.Name = "gridColumn25";
            // 
            // gridColumn26
            // 
            this.gridColumn26.Name = "gridColumn26";
            // 
            // gridColumn27
            // 
            this.gridColumn27.Name = "gridColumn27";
            // 
            // gridColumn28
            // 
            this.gridColumn28.Name = "gridColumn28";
            // 
            // gridColumn29
            // 
            this.gridColumn29.Name = "gridColumn29";
            // 
            // gridColumn30
            // 
            this.gridColumn30.Name = "gridColumn30";
            // 
            // gridColumn31
            // 
            this.gridColumn31.Name = "gridColumn31";
            // 
            // gridColumn32
            // 
            this.gridColumn32.Name = "gridColumn32";
            // 
            // splitContainerControl8
            // 
            this.splitContainerControl8.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl8.Name = "splitContainerControl8";
            this.splitContainerControl8.Size = new System.Drawing.Size(200, 100);
            this.splitContainerControl8.TabIndex = 0;
            // 
            // tabControlGSMDropDetail
            // 
            this.tabControlGSMDropDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlGSMDropDetail.Name = "tabControlGSMDropDetail";
            this.tabControlGSMDropDetail.Size = new System.Drawing.Size(300, 300);
            this.tabControlGSMDropDetail.TabIndex = 0;
            // 
            // tabControlChartGSMDropReason
            // 
            this.tabControlChartGSMDropReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartGSMDropReason.Name = "tabControlChartGSMDropReason";
            this.tabControlChartGSMDropReason.Size = new System.Drawing.Size(300, 300);
            this.tabControlChartGSMDropReason.TabIndex = 0;
            // 
            // gridControlGSMDropReason
            // 
            this.gridControlGSMDropReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlGSMDropReason.MainView = this.gridView29;
            this.gridControlGSMDropReason.Name = "gridControlGSMDropReason";
            this.gridControlGSMDropReason.Size = new System.Drawing.Size(400, 200);
            this.gridControlGSMDropReason.TabIndex = 0;
            this.gridControlGSMDropReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView29});
            // 
            // gridView29
            // 
            this.gridView29.GridControl = this.gridControlGSMDropReason;
            this.gridView29.Name = "gridView29";
            // 
            // gridViewGSMDropReason
            // 
            this.gridViewGSMDropReason.Name = "gridViewGSMDropReason";
            // 
            // gridView10
            // 
            this.gridView10.Name = "gridView10";
            // 
            // gridView17
            // 
            this.gridView17.Name = "gridView17";
            // 
            // gridColumn129
            // 
            this.gridColumn129.Name = "gridColumn129";
            // 
            // gridColumn131
            // 
            this.gridColumn131.Name = "gridColumn131";
            // 
            // gridColumn132
            // 
            this.gridColumn132.Name = "gridColumn132";
            // 
            // gridColumn130
            // 
            this.gridColumn130.Name = "gridColumn130";
            // 
            // gridColumn133
            // 
            this.gridColumn133.Name = "gridColumn133";
            // 
            // gridColumn134
            // 
            this.gridColumn134.Name = "gridColumn134";
            // 
            // splitContainerControl9
            // 
            this.splitContainerControl9.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl9.Name = "splitContainerControl9";
            this.splitContainerControl9.Size = new System.Drawing.Size(200, 100);
            this.splitContainerControl9.TabIndex = 0;
            // 
            // tabControlGSMBlockDetail
            // 
            this.tabControlGSMBlockDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlGSMBlockDetail.Name = "tabControlGSMBlockDetail";
            this.tabControlGSMBlockDetail.Size = new System.Drawing.Size(300, 300);
            this.tabControlGSMBlockDetail.TabIndex = 0;
            // 
            // tabControlChartGSMBlockReason
            // 
            this.tabControlChartGSMBlockReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartGSMBlockReason.Name = "tabControlChartGSMBlockReason";
            this.tabControlChartGSMBlockReason.Size = new System.Drawing.Size(300, 300);
            this.tabControlChartGSMBlockReason.TabIndex = 0;
            // 
            // gridControlGSMBlockReason
            // 
            this.gridControlGSMBlockReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlGSMBlockReason.MainView = this.gridView30;
            this.gridControlGSMBlockReason.Name = "gridControlGSMBlockReason";
            this.gridControlGSMBlockReason.Size = new System.Drawing.Size(400, 200);
            this.gridControlGSMBlockReason.TabIndex = 0;
            this.gridControlGSMBlockReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView30});
            // 
            // gridView30
            // 
            this.gridView30.GridControl = this.gridControlGSMBlockReason;
            this.gridView30.Name = "gridView30";
            // 
            // gridViewGSMBlockReason
            // 
            this.gridViewGSMBlockReason.Name = "gridViewGSMBlockReason";
            // 
            // gridView11
            // 
            this.gridView11.Name = "gridView11";
            // 
            // gridColumn145
            // 
            this.gridColumn145.Name = "gridColumn145";
            // 
            // gridColumn146
            // 
            this.gridColumn146.Name = "gridColumn146";
            // 
            // gridColumn147
            // 
            this.gridColumn147.Name = "gridColumn147";
            // 
            // gridColumn148
            // 
            this.gridColumn148.Name = "gridColumn148";
            // 
            // gridColumn149
            // 
            this.gridColumn149.Name = "gridColumn149";
            // 
            // gridColumn150
            // 
            this.gridColumn150.Name = "gridColumn150";
            // 
            // splitContainerControl11
            // 
            this.splitContainerControl11.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl11.Name = "splitContainerControl11";
            this.splitContainerControl11.Size = new System.Drawing.Size(200, 100);
            this.splitContainerControl11.TabIndex = 0;
            // 
            // tabControlTDDropDetail
            // 
            this.tabControlTDDropDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlTDDropDetail.Name = "tabControlTDDropDetail";
            this.tabControlTDDropDetail.Size = new System.Drawing.Size(300, 300);
            this.tabControlTDDropDetail.TabIndex = 0;
            // 
            // tabControlChartTDDropReason
            // 
            this.tabControlChartTDDropReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartTDDropReason.Name = "tabControlChartTDDropReason";
            this.tabControlChartTDDropReason.Size = new System.Drawing.Size(300, 300);
            this.tabControlChartTDDropReason.TabIndex = 0;
            // 
            // gridControlTDDropReason
            // 
            this.gridControlTDDropReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlTDDropReason.MainView = this.gridView31;
            this.gridControlTDDropReason.Name = "gridControlTDDropReason";
            this.gridControlTDDropReason.Size = new System.Drawing.Size(400, 200);
            this.gridControlTDDropReason.TabIndex = 0;
            this.gridControlTDDropReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView31});
            // 
            // gridView31
            // 
            this.gridView31.GridControl = this.gridControlTDDropReason;
            this.gridView31.Name = "gridView31";
            // 
            // gridViewTDDropReason
            // 
            this.gridViewTDDropReason.Name = "gridViewTDDropReason";
            // 
            // gridView13
            // 
            this.gridView13.Name = "gridView13";
            // 
            // gridColumn151
            // 
            this.gridColumn151.Name = "gridColumn151";
            // 
            // gridColumn152
            // 
            this.gridColumn152.Name = "gridColumn152";
            // 
            // gridColumn153
            // 
            this.gridColumn153.Name = "gridColumn153";
            // 
            // gridColumn154
            // 
            this.gridColumn154.Name = "gridColumn154";
            // 
            // gridColumn155
            // 
            this.gridColumn155.Name = "gridColumn155";
            // 
            // gridColumn156
            // 
            this.gridColumn156.Name = "gridColumn156";
            // 
            // splitContainerControl13
            // 
            this.splitContainerControl13.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl13.Name = "splitContainerControl13";
            this.splitContainerControl13.Size = new System.Drawing.Size(200, 100);
            this.splitContainerControl13.TabIndex = 0;
            // 
            // tabControlTDBlockDetail
            // 
            this.tabControlTDBlockDetail.Location = new System.Drawing.Point(0, 0);
            this.tabControlTDBlockDetail.Name = "tabControlTDBlockDetail";
            this.tabControlTDBlockDetail.Size = new System.Drawing.Size(300, 300);
            this.tabControlTDBlockDetail.TabIndex = 0;
            // 
            // tabControlChartTDBlockReason
            // 
            this.tabControlChartTDBlockReason.Location = new System.Drawing.Point(0, 0);
            this.tabControlChartTDBlockReason.Name = "tabControlChartTDBlockReason";
            this.tabControlChartTDBlockReason.Size = new System.Drawing.Size(300, 300);
            this.tabControlChartTDBlockReason.TabIndex = 0;
            // 
            // gridControlTDBlockReason
            // 
            this.gridControlTDBlockReason.Location = new System.Drawing.Point(0, 0);
            this.gridControlTDBlockReason.MainView = this.gridView32;
            this.gridControlTDBlockReason.Name = "gridControlTDBlockReason";
            this.gridControlTDBlockReason.Size = new System.Drawing.Size(400, 200);
            this.gridControlTDBlockReason.TabIndex = 0;
            this.gridControlTDBlockReason.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView32});
            // 
            // gridView32
            // 
            this.gridView32.GridControl = this.gridControlTDBlockReason;
            this.gridView32.Name = "gridView32";
            // 
            // gridViewTDBlockReason
            // 
            this.gridViewTDBlockReason.Name = "gridViewTDBlockReason";
            // 
            // gridView14
            // 
            this.gridView14.Name = "gridView14";
            // 
            // gridColumn157
            // 
            this.gridColumn157.Name = "gridColumn157";
            // 
            // gridColumn158
            // 
            this.gridColumn158.Name = "gridColumn158";
            // 
            // gridColumn159
            // 
            this.gridColumn159.Name = "gridColumn159";
            // 
            // gridColumn160
            // 
            this.gridColumn160.Name = "gridColumn160";
            // 
            // gridColumn215
            // 
            this.gridColumn215.Name = "gridColumn215";
            // 
            // gridColumn216
            // 
            this.gridColumn216.Name = "gridColumn216";
            // 
            // tabLowSpeed
            // 
            this.tabLowSpeed.Controls.Add(this.gridControlLowSpeed);
            this.tabLowSpeed.Location = new System.Drawing.Point(4, 23);
            this.tabLowSpeed.Name = "tabLowSpeed";
            this.tabLowSpeed.Padding = new System.Windows.Forms.Padding(3);
            this.tabLowSpeed.Size = new System.Drawing.Size(1025, 542);
            this.tabLowSpeed.TabIndex = 1;
            this.tabLowSpeed.Text = "低速率";
            this.tabLowSpeed.UseVisualStyleBackColor = true;
            // 
            // gridControlLowSpeed
            // 
            this.gridControlLowSpeed.ContextMenuStrip = this.ctxMenu;
            this.gridControlLowSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlLowSpeed.Location = new System.Drawing.Point(3, 3);
            this.gridControlLowSpeed.MainView = this.gridViewLowSpeed;
            this.gridControlLowSpeed.Name = "gridControlLowSpeed";
            this.gridControlLowSpeed.Size = new System.Drawing.Size(1019, 536);
            this.gridControlLowSpeed.TabIndex = 5;
            this.gridControlLowSpeed.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLowSpeed});
            // 
            // gridViewLowSpeed
            // 
            this.gridViewLowSpeed.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.gridViewLowSpeed.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.gridViewLowSpeed.Appearance.FocusedCell.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridViewLowSpeed.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridViewLowSpeed.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gridViewLowSpeed.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewLowSpeed.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewLowSpeed.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Green;
            this.gridViewLowSpeed.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridViewLowSpeed.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gridViewLowSpeed.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewLowSpeed.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewLowSpeed.Appearance.SelectedRow.BorderColor = System.Drawing.Color.Green;
            this.gridViewLowSpeed.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gridViewLowSpeed.Appearance.SelectedRow.Options.UseBorderColor = true;
            this.gridViewLowSpeed.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn170,
            this.gridColumn171,
            this.gridColumn172,
            this.gridColumn173,
            this.gridColumn174,
            this.gridColumn175,
            this.gridColumn176});
            this.gridViewLowSpeed.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridViewLowSpeed.GridControl = this.gridControlLowSpeed;
            this.gridViewLowSpeed.IndicatorWidth = 50;
            this.gridViewLowSpeed.Name = "gridViewLowSpeed";
            this.gridViewLowSpeed.OptionsBehavior.Editable = false;
            this.gridViewLowSpeed.OptionsNavigation.AutoFocusNewRow = true;
            this.gridViewLowSpeed.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewLowSpeed.OptionsSelection.MultiSelect = true;
            this.gridViewLowSpeed.OptionsView.ColumnAutoWidth = false;
            this.gridViewLowSpeed.OptionsView.ShowGroupPanel = false;
            this.gridViewLowSpeed.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.gridViewLowSpeed_CustomDrawRowIndicator);
            // 
            // gridColumn170
            // 
            this.gridColumn170.Caption = "文件名";
            this.gridColumn170.FieldName = "FileName";
            this.gridColumn170.Name = "gridColumn170";
            this.gridColumn170.Visible = true;
            this.gridColumn170.VisibleIndex = 0;
            this.gridColumn170.Width = 391;
            // 
            // gridColumn171
            // 
            this.gridColumn171.Caption = "网络类型";
            this.gridColumn171.FieldName = "NetWorkType";
            this.gridColumn171.Name = "gridColumn171";
            this.gridColumn171.Visible = true;
            this.gridColumn171.VisibleIndex = 1;
            this.gridColumn171.Width = 80;
            // 
            // gridColumn172
            // 
            this.gridColumn172.Caption = "类型";
            this.gridColumn172.FieldName = "Type";
            this.gridColumn172.Name = "gridColumn172";
            this.gridColumn172.Visible = true;
            this.gridColumn172.VisibleIndex = 2;
            this.gridColumn172.Width = 61;
            // 
            // gridColumn173
            // 
            this.gridColumn173.Caption = "事件";
            this.gridColumn173.FieldName = "CqtEvent";
            this.gridColumn173.Name = "gridColumn173";
            this.gridColumn173.Visible = true;
            this.gridColumn173.VisibleIndex = 3;
            this.gridColumn173.Width = 86;
            // 
            // gridColumn174
            // 
            this.gridColumn174.Caption = "平均下载速率";
            this.gridColumn174.FieldName = "AverSpeed";
            this.gridColumn174.Name = "gridColumn174";
            this.gridColumn174.Visible = true;
            this.gridColumn174.VisibleIndex = 4;
            this.gridColumn174.Width = 124;
            // 
            // gridColumn175
            // 
            this.gridColumn175.Caption = "总下载速率分子";
            this.gridColumn175.FieldName = "SpeedNume";
            this.gridColumn175.Name = "gridColumn175";
            this.gridColumn175.Visible = true;
            this.gridColumn175.VisibleIndex = 5;
            this.gridColumn175.Width = 111;
            // 
            // gridColumn176
            // 
            this.gridColumn176.Caption = "总下载速率分母";
            this.gridColumn176.FieldName = "SpeedDenomi";
            this.gridColumn176.Name = "gridColumn176";
            this.gridColumn176.Visible = true;
            this.gridColumn176.VisibleIndex = 6;
            this.gridColumn176.Width = 110;
            // 
            // tabWeakCover
            // 
            this.tabWeakCover.Controls.Add(this.gridControlWeakCover);
            this.tabWeakCover.Location = new System.Drawing.Point(4, 23);
            this.tabWeakCover.Name = "tabWeakCover";
            this.tabWeakCover.Padding = new System.Windows.Forms.Padding(3);
            this.tabWeakCover.Size = new System.Drawing.Size(1025, 542);
            this.tabWeakCover.TabIndex = 0;
            this.tabWeakCover.Text = "弱覆盖";
            this.tabWeakCover.UseVisualStyleBackColor = true;
            // 
            // gridControlWeakCover
            // 
            this.gridControlWeakCover.ContextMenuStrip = this.ctxMenu;
            this.gridControlWeakCover.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlWeakCover.Location = new System.Drawing.Point(3, 3);
            this.gridControlWeakCover.MainView = this.gridViewWeakCover;
            this.gridControlWeakCover.Name = "gridControlWeakCover";
            this.gridControlWeakCover.Size = new System.Drawing.Size(1019, 536);
            this.gridControlWeakCover.TabIndex = 4;
            this.gridControlWeakCover.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewWeakCover});
            // 
            // gridViewWeakCover
            // 
            this.gridViewWeakCover.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.gridViewWeakCover.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.gridViewWeakCover.Appearance.FocusedCell.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.gridViewWeakCover.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridViewWeakCover.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gridViewWeakCover.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewWeakCover.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewWeakCover.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Green;
            this.gridViewWeakCover.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridViewWeakCover.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gridViewWeakCover.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewWeakCover.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gridViewWeakCover.Appearance.SelectedRow.BorderColor = System.Drawing.Color.Green;
            this.gridViewWeakCover.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gridViewWeakCover.Appearance.SelectedRow.Options.UseBorderColor = true;
            this.gridViewWeakCover.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn162,
            this.gridColumn163,
            this.gridColumn164,
            this.gridColumn165,
            this.gridColumn166,
            this.gridColumn167,
            this.gridColumn168});
            this.gridViewWeakCover.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridViewWeakCover.GridControl = this.gridControlWeakCover;
            this.gridViewWeakCover.IndicatorWidth = 50;
            this.gridViewWeakCover.Name = "gridViewWeakCover";
            this.gridViewWeakCover.OptionsBehavior.Editable = false;
            this.gridViewWeakCover.OptionsNavigation.AutoFocusNewRow = true;
            this.gridViewWeakCover.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewWeakCover.OptionsSelection.MultiSelect = true;
            this.gridViewWeakCover.OptionsView.ColumnAutoWidth = false;
            this.gridViewWeakCover.OptionsView.ShowGroupPanel = false;
            this.gridViewWeakCover.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.gridViewWeakCover_CustomDrawRowIndicator);
            // 
            // gridColumn162
            // 
            this.gridColumn162.Caption = "文件名";
            this.gridColumn162.FieldName = "FileName";
            this.gridColumn162.Name = "gridColumn162";
            this.gridColumn162.Visible = true;
            this.gridColumn162.VisibleIndex = 0;
            this.gridColumn162.Width = 403;
            // 
            // gridColumn163
            // 
            this.gridColumn163.Caption = "网络类型";
            this.gridColumn163.FieldName = "NetWorkType";
            this.gridColumn163.Name = "gridColumn163";
            this.gridColumn163.Visible = true;
            this.gridColumn163.VisibleIndex = 1;
            this.gridColumn163.Width = 80;
            // 
            // gridColumn164
            // 
            this.gridColumn164.Caption = "类型";
            this.gridColumn164.FieldName = "Type";
            this.gridColumn164.Name = "gridColumn164";
            this.gridColumn164.Visible = true;
            this.gridColumn164.VisibleIndex = 2;
            this.gridColumn164.Width = 61;
            // 
            // gridColumn165
            // 
            this.gridColumn165.Caption = "事件";
            this.gridColumn165.FieldName = "CqtEvent";
            this.gridColumn165.Name = "gridColumn165";
            this.gridColumn165.Visible = true;
            this.gridColumn165.VisibleIndex = 3;
            this.gridColumn165.Width = 86;
            // 
            // gridColumn166
            // 
            this.gridColumn166.Caption = "覆盖率(%)";
            this.gridColumn166.FieldName = "Coverage";
            this.gridColumn166.Name = "gridColumn166";
            this.gridColumn166.Visible = true;
            this.gridColumn166.VisibleIndex = 4;
            this.gridColumn166.Width = 78;
            // 
            // gridColumn167
            // 
            this.gridColumn167.Caption = "达标采样点数";
            this.gridColumn167.FieldName = "GoodSampleCount";
            this.gridColumn167.Name = "gridColumn167";
            this.gridColumn167.Visible = true;
            this.gridColumn167.VisibleIndex = 5;
            this.gridColumn167.Width = 111;
            // 
            // gridColumn168
            // 
            this.gridColumn168.Caption = "总采样点数";
            this.gridColumn168.FieldName = "TotalSampleCount";
            this.gridColumn168.Name = "gridColumn168";
            this.gridColumn168.Visible = true;
            this.gridColumn168.VisibleIndex = 6;
            this.gridColumn168.Width = 141;
            // 
            // gridView20
            // 
            this.gridView20.Name = "gridView20";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabWeakCover);
            this.tabControl1.Controls.Add(this.tabLowSpeed);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1033, 569);
            this.tabControl1.TabIndex = 6;
            // 
            // ZTReportEventStatQueryByFileListForm_GZ
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1033, 569);
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTReportEventStatQueryByFileListForm_GZ";
            this.Text = "异常事件统计";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl8)).EndInit();
            this.splitContainerControl8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMDropDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl9)).EndInit();
            this.splitContainerControl9.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlGSMBlockDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartGSMBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGSMBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGSMBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl11)).EndInit();
            this.splitContainerControl11.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDDropDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView31)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDDropReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl13)).EndInit();
            this.splitContainerControl13.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabControlTDBlockDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlChartTDBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTDBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView32)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTDBlockReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView14)).EndInit();
            this.tabLowSpeed.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlLowSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLowSpeed)).EndInit();
            this.tabWeakCover.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlWeakCover)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewWeakCover)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView20)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl2;
        private DevExpress.XtraGrid.GridControl gridControl3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl3;
        private DevExpress.XtraGrid.GridControl gridControl4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn90;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn91;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn92;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn93;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn94;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn95;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn96;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl4;
        private DevExpress.XtraGrid.GridControl gridControl5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn97;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn98;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn99;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn100;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn101;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn102;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn103;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn104;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn105;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn106;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn107;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn108;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn109;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn110;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn111;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn112;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl5;
        private DevExpress.XtraGrid.GridControl gridControl6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn113;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn114;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn115;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn116;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn117;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn118;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn119;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn120;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn121;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn122;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn123;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn124;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn125;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn126;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn127;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn128;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl6;
        private DevExpress.XtraGrid.GridControl gridControl7;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn135;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn136;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn137;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn138;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn139;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn140;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn141;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn142;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn143;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn144;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn193;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn194;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn195;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn196;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn197;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn198;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl7;
        private DevExpress.XtraGrid.GridControl gridControl11;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn199;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn200;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn201;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn202;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn203;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn204;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn205;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn206;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn207;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn208;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn209;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn210;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn211;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn212;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn213;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn214;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl11;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView15;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView16;
        private DevExpress.XtraTab.XtraTabControl tabControlChartTDProfile;
        private DevExpress.XtraGrid.GridControl gridControlTDProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTDProfile;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl8;
        private DevExpress.XtraTab.XtraTabControl tabControlGSMDropDetail;
        private DevExpress.XtraTab.XtraTabControl tabControlChartGSMDropReason;
        private DevExpress.XtraGrid.GridControl gridControlGSMDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGSMDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn129;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn131;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn132;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn130;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn133;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn134;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl9;
        private DevExpress.XtraTab.XtraTabControl tabControlGSMBlockDetail;
        private DevExpress.XtraTab.XtraTabControl tabControlChartGSMBlockReason;
        private DevExpress.XtraGrid.GridControl gridControlGSMBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGSMBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn145;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn146;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn147;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn148;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn149;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn150;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl11;
        private DevExpress.XtraTab.XtraTabControl tabControlTDDropDetail;
        private DevExpress.XtraTab.XtraTabControl tabControlChartTDDropReason;
        private DevExpress.XtraGrid.GridControl gridControlTDDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTDDropReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn151;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn152;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn153;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn154;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn155;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn156;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl13;
        private DevExpress.XtraTab.XtraTabControl tabControlTDBlockDetail;
        private DevExpress.XtraTab.XtraTabControl tabControlChartTDBlockReason;
        private DevExpress.XtraGrid.GridControl gridControlTDBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTDBlockReason;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn157;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn158;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn159;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn160;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn215;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn216;
        private System.Windows.Forms.TabPage tabLowSpeed;
        private System.Windows.Forms.TabPage tabWeakCover;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView20;
        private System.Windows.Forms.TabControl tabControl1;
        private DevExpress.XtraGrid.GridControl gridControlWeakCover;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewWeakCover;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn162;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn163;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn164;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn165;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn166;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn167;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn168;
        private DevExpress.XtraGrid.GridControl gridControlLowSpeed;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLowSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn170;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn171;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn172;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn173;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn174;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn175;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn176;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView19;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView21;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView22;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView23;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView24;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView25;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView26;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView27;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView28;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView29;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView30;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView31;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView32;

    }
}