﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRDownloadSpeedAnaInfo
    {
        public TestPoint TP { get; set; }
        public long Size { get; set; } = 0;
        public int Time { get; set; } = 0;

        public string CellName { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
    }

    public class NRDownloadSpeedAnaRes
    {
        public int Count { get; set; } = 0;
        public string FileName { get; set; }
        public string Time { get; set; }

        public long TransferSize { get; set; }
        public int TransferTime { get; set; }

        public string CellName { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
    }
}
