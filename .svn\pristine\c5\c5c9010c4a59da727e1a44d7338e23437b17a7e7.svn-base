﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLastWeakMosAnaByTPForm : MinCloseForm
    {
        public NRLastWeakMosAnaByTPForm()
        {
            InitializeComponent();

            this.gv.OptionsView.ColumnAutoWidth = false;
            this.gv.OptionsBehavior.Editable = false;

            ToolStripMenuItem item = new ToolStripMenuItem("导出Excel");
            item.Click += item_Click;
            ContextMenuStrip menu = new ContextMenuStrip();
            menu.Items.Add(item);
            this.gridControl1.ContextMenuStrip = menu;

            this.gv.DoubleClick += gridView1_DoubleClick;
        }

        private List<NRLastWeakMosByTPResult> listResult = new List<NRLastWeakMosByTPResult>();
        NRLastWeakMosByTPCondition curCondtion;
        public void FillData(List<NRLastWeakMosByTPResult> lr, NRLastWeakMosByTPCondition curCondtion)
        {
            this.curCondtion = curCondtion;
            this.listResult = lr;
            if (!curCondtion.IsACall)
            {
                gridColumnBeginTime.Visible = false;
                gridColumnEndTime.Visible = false;
            }
            this.gridControl1.DataSource = this.listResult;
            this.gridControl1.RefreshDataSource();
            this.gridControl1.Refresh();
        }

        void item_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);

            row.AddCellValue("文件名");
            row.AddCellValue("弱MOS点数");
            row.AddCellValue("MOS均值");
            row.AddCellValue("持续距离");
            row.AddCellValue("持续弱MOS起始时间");
            row.AddCellValue("持续弱MOS起始经度");
            row.AddCellValue("持续弱MOS起始纬度");
            if (curCondtion.IsACall)
            {
                row.AddCellValue("通话起始时间");
                row.AddCellValue("通话结束时间");
            }
            row.AddCellValue("平均NR_RSRP");
            row.AddCellValue("平均NR_SINR");
            row.AddCellValue("平均LTE_RSRP");
            row.AddCellValue("平均LTE_SINR");

            row.AddCellValue("弱MOS时间点");
            row.AddCellValue("弱MOS经度");
            row.AddCellValue("弱MOS纬度");
            row.AddCellValue("弱MOS值");
            row.AddCellValue("NR_RSRP");
            row.AddCellValue("NR_SINR");
            row.AddCellValue("NR_EARFCN");
            row.AddCellValue("NR_PCI");
            row.AddCellValue("LTE_RSRP");
            row.AddCellValue("LTE_SINR");
            row.AddCellValue("LTE_EARFCN");
            row.AddCellValue("LTE_PCI");

            foreach (NRLastWeakMosByTPResult res in listResult)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(res.FileName);
                row.AddCellValue(res.TpNum);
                row.AddCellValue(res.MOSInfo.Avg);
                row.AddCellValue(res.Distance);
                row.AddCellValue(res.DateTime);
                row.AddCellValue(res.Longitude);
                row.AddCellValue(res.Latitude);
                if (curCondtion.IsACall)
                {
                    row.AddCellValue(res.StartCallTime);
                    row.AddCellValue(res.EndCallTime);
                }
                row.AddCellValue(res.NRInfo.RsrpInfo.Avg);
                row.AddCellValue(res.NRInfo.SinrInfo.Avg);
                row.AddCellValue(res.LTEInfo.RsrpInfo.Avg);
                row.AddCellValue(res.LTEInfo.SinrInfo.Avg);
                foreach (NRLastWeakMosByTPResult.NRLastWeakMosByTPMosInfo mos in res.WeakMosTPs)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(mos.WeakMOSTP.DateTimeStringWithMillisecond);
                    subRow.AddCellValue(mos.WeakMOSTP.Longitude);
                    subRow.AddCellValue(mos.WeakMOSTP.Latitude);
                    subRow.AddCellValue(mos.MOS);
                    subRow.AddCellValue(mos.NRInfo.Rsrp);
                    subRow.AddCellValue(mos.NRInfo.Sinr);
                    subRow.AddCellValue(mos.NRInfo.Earfcn);
                    subRow.AddCellValue(mos.NRInfo.Pci);
                    subRow.AddCellValue(mos.LTEInfo.Rsrp);
                    subRow.AddCellValue(mos.LTEInfo.Sinr);
                    subRow.AddCellValue(mos.LTEInfo.Earfcn);
                    subRow.AddCellValue(mos.LTEInfo.Pci);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        void gridView1_DoubleClick(object sender, EventArgs e)
        {
            object info = gv.GetRow(gv.FocusedRowHandle);
            if (info == null)
            {
                return;
            }
            if (info is NRLastWeakMosByTPResult)
            {
                MainModel.ClearDTData();
                NRLastWeakMosByTPResult res = info as NRLastWeakMosByTPResult;
                foreach (var tp in res.MosTimePeriodTPs)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireSetDefaultMapSerialThemes("NR_PESQLQ", "NR_POLQA_Score_SWB");
                MainModel.MainForm.GetMapForm().GoToView(res.Longitude, res.Latitude);
                MainModel.FireDTDataChanged(this);
            }
        }

        private void gvWeakMos_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView tmpGv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object info = tmpGv.GetRow(tmpGv.FocusedRowHandle);
            if (info == null)
            {
                return;
            }
            if (info is NRLastWeakMosByTPResult.NRLastWeakMosByTPMosInfo)
            {
                MainModel.ClearDTData();
                NRLastWeakMosByTPResult.NRLastWeakMosByTPMosInfo mos = info as NRLastWeakMosByTPResult.NRLastWeakMosByTPMosInfo;
                MainModel.DTDataManager.Add(mos.WeakMOSTP);
                MainModel.FireSetDefaultMapSerialThemes("NR_PESQLQ", "NR_POLQA_Score_SWB");
                MainModel.MainForm.GetMapForm().GoToView(mos.WeakMOSTP.Longitude, mos.WeakMOSTP.Latitude);
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
