﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRBlockCallAna
{
    public abstract class NRBlockCallAnaCauseBase
    {
        public abstract bool IsSatisfy(NRBlockCallAnaInfo call);
        public virtual bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class VoiceHangupCause : NRBlockCallAnaCauseBase
    {
        public override bool IsSatisfy(NRBlockCallAnaInfo call)
        {
            //addMessage(child, new MessageInfo(0x7FFF1C43, "Voice_Hangup"));
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == 0x7FFF1C43)
                {
                    call.BlockCause = ENRBlockCallCause.VoiceHangup;
                    return true;
                }
            }
            return false;
        }
    }

    public class IMSErrorCause : NRBlockCallAnaCauseBase
    {
        public override bool IsSatisfy(NRBlockCallAnaInfo call)
        {
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID >= 0x42000000 && msg.ID < 0x43000000)
                {
                    int statusCode = (msg.ID & 0x00003fff);
                    if (400 <= statusCode)
                    {
                        call.SetErrorMsgName(msg);
                        call.BlockCause = ENRBlockCallCause.异常信令;
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public class UECancelCause : NRBlockCallAnaCauseBase
    {
        public override bool IsSatisfy(NRBlockCallAnaInfo call)
        {
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == (int)MessageManager.Msg_IMS_SIP_CANCEL)
                {
                    call.SetErrorMsgName(msg);
                    call.BlockCause = ENRBlockCallCause.UE_Cancel;
                    return true;
                }
            }
            return false;
        }
    }
}
