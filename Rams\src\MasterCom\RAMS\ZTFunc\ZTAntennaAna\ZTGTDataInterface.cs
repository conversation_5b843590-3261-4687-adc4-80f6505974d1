﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;
using System.IO;
using System.ComponentModel;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyGSMCellPara : DIYSQLBase
    {
        string strCity { get; set; }
        AntTimeCfg timeCfg { get; set; }
        public DiyGSMCellPara(MainModel mainModel, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.timeCfg = timeCfg;
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.sp_auto_cell_para_gsm @date = '" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyGsmCellPara"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[12];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;
            rType[10] = E_VType.E_Float;
            rType[11] = E_VType.E_Float;

            return rType;
        }

        public Dictionary<LaiKey, CellGsmPara> cellGsmParaEciDic { get; set; } = new Dictionary<LaiKey, CellGsmPara>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellGsmPara aPara = new CellGsmPara();
                    aPara.LAC = package.Content.GetParamInt();
                    aPara.CI = package.Content.GetParamInt();

                    aPara.无线接通率 = package.Content.GetParamFloat();
                    aPara.寻呼成功率 = package.Content.GetParamFloat();
                    aPara.TCH拥塞率不含切 = package.Content.GetParamFloat();
                    aPara.SDCCH拥塞率 = package.Content.GetParamFloat();
                    aPara.上行TBF建立成功率 = package.Content.GetParamFloat();
                    aPara.下行TBF建立成功率 = package.Content.GetParamFloat();
                    aPara.TCH掉话率含切换 = package.Content.GetParamFloat();
                    aPara.切换成功率 = package.Content.GetParamFloat();
                    aPara.下行TBF掉线率 = package.Content.GetParamFloat();
                    aPara.上行高干扰信道比例 = package.Content.GetParamFloat();

                    LaiKey lKey = new LaiKey();
                    lKey.ILac = aPara.LAC;
                    lKey.ICi = aPara.CI;

                    if (!cellGsmParaEciDic.ContainsKey(lKey))
                    {
                        cellGsmParaEciDic.Add(lKey, aPara);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyTDCellPara : DIYSQLBase
    {
        string strCity { get; set; }
        AntTimeCfg timeCfg { get; set; }
        public DiyTDCellPara(MainModel mainModel, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.timeCfg = timeCfg;
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.sp_auto_cell_para_td @date = '" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyTDCellPara"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[16];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;
            rType[10] = E_VType.E_Float;
            rType[11] = E_VType.E_Float;
            rType[12] = E_VType.E_Float;
            rType[13] = E_VType.E_Float;
            rType[14] = E_VType.E_Float;
            rType[15] = E_VType.E_Float;

            return rType;
        }

        public Dictionary<LaiKey, CellTdPara> cellTdParaDic { get; set; } = new Dictionary<LaiKey, CellTdPara>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellTdPara aPara = new CellTdPara();
                    aPara.LAC = package.Content.GetParamInt();
                    aPara.CI = package.Content.GetParamInt();

                    aPara.TD语音业务无线掉话率 = package.Content.GetParamFloat();
                    aPara.PS域无线掉线率 = package.Content.GetParamFloat();
                    aPara.RRC连接建立成功率 = package.Content.GetParamFloat();
                    aPara.RAB建立成功率 = package.Content.GetParamFloat();
                    aPara.无线接通率 = package.Content.GetParamFloat();
                    aPara.PS域接通率 = package.Content.GetParamFloat();
                    aPara.PS域下行重行率 = package.Content.GetParamFloat();
                    aPara.上行电路域误块率 = package.Content.GetParamFloat();
                    aPara.上行分组域误块率 = package.Content.GetParamFloat();
                    aPara.TD网内RNC间切换出成功率 = package.Content.GetParamFloat();

                    aPara.TD网内RNC间硬切换入成功率 = package.Content.GetParamFloat();
                    aPara.RNC内小区间切换成功率 = package.Content.GetParamFloat();
                    aPara.小区间切换出成功率 = package.Content.GetParamFloat();
                    aPara.小区间切换入成功率 = package.Content.GetParamFloat();

                    LaiKey lKey = new LaiKey();
                    lKey.ILac = aPara.LAC;
                    lKey.ICi = aPara.CI;

                    if (!cellTdParaDic.ContainsKey(lKey))
                    {
                        cellTdParaDic.Add(lKey, aPara);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyTDMRData : DIYSQLBase
    {
        int iArrayNum { get; set; } = 2;
        int iFunc { get; set; } = 0;
        int iNum { get; set; } = 0;
        AntTimeCfg timeCfg { get; set; }
        public DiyTDMRData(MainModel mainModel, int iFunc, int iNum, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            this.iFunc = iFunc;
            this.iNum = iNum;
            this.timeCfg = timeCfg;
        }

        protected override string getSqlTextString()
        {
            iArrayNum = iNum;
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.sp_auto_cell_mr_td_sample '" + dDate + "'," + iFunc;
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyTDMRData"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[iArrayNum + 2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            for (int i = 2; i < iArrayNum + 2; i++)//含总采样点数字段
            {
                rType[i] = E_VType.E_Float;
            }
            return rType;
        }

        public Dictionary<LaiKey, ZTAntGTMRBaseItem> gtMRDic { get; set; } = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ZTAntGTMRBaseItem mrItem = new ZTAntGTMRBaseItem();
                    mrItem.iLAC = package.Content.GetParamInt();
                    mrItem.iCI = package.Content.GetParamInt();
                    LaiKey lKey = new LaiKey();
                    lKey.ILac = mrItem.iLAC;
                    lKey.ICi = mrItem.iCI;

                    for (int i = 0; i < iArrayNum; i++)//含总采样点数字段
                    {
                        mrItem.dataValue[i] = (int)package.Content.GetParamFloat();
                    }

                    if (!gtMRDic.ContainsKey(lKey))
                    {
                        gtMRDic.Add(lKey, mrItem);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyTDMRKpi : DIYSQLBase
    {
        string strCity { get; set; }
        AntTimeCfg timeCfg { get; set; }
        public DiyTDMRKpi(MainModel mainModel, AntTimeCfg timeCfg)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.timeCfg = timeCfg;
        }

        protected override string getSqlTextString()
        {
            DateTime dDate = DateTime.Now;
            if (timeCfg.DEtime >= Convert.ToDateTime("2015-06-01"))
                dDate = timeCfg.DEtime;

            string strSql = @"exec dbo.[sp_auto_cell_mr_td] @strDate = '" + dDate + "'";
#if DEBUG
            log.Info("查询SQL：" + strSql);
#endif
            return strSql;
        }

        public override string Name
        {
            get { return "DiyTDMRKpi"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[10];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;

            return rType;
        }

        public Dictionary<LaiKey, CellTdMRKpi> cellTdMRKpiDic { get; set; } = new Dictionary<LaiKey, CellTdMRKpi>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellTdMRKpi aPara = new CellTdMRKpi();
                    aPara.LAC = package.Content.GetParamInt();
                    aPara.CI = package.Content.GetParamInt();

                    aPara.F弱覆盖采样点比例 = package.Content.GetParamFloat();
                    aPara.F良好覆盖采样点比例 = package.Content.GetParamFloat();
                    aPara.F下行平均PCCPCH_RSCP = package.Content.GetParamFloat();
                    aPara.F下行时隙干扰采样点比例 = package.Content.GetParamFloat();
                    aPara.F上行时隙干扰采样点比例 = package.Content.GetParamFloat();
                    aPara.FUPPTS干扰采样点比例 = package.Content.GetParamFloat();
                    aPara.FUE高发射功率占比 = package.Content.GetParamFloat();
                    aPara.FPS业务高误块率采样点占比 = package.Content.GetParamFloat();

                    LaiKey lKey = new LaiKey();
                    lKey.ILac = aPara.LAC;
                    lKey.ICi = aPara.CI;

                    if (!cellTdMRKpiDic.ContainsKey(lKey))
                    {
                        cellTdMRKpiDic.Add(lKey, aPara);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiyGsmMrCover : DIYSQLBase
    {
        public DiyGsmMrCover(MainModel mainModel)
            : base(mainModel)
        {
            
        }
        public void setCondition(DateTime stime)
        {
            this.stime = stime;
        }
        protected override string getSqlTextString()
        {
            string sql = string.Format(@"exec sp_auto_cell_gsmmr_kpi '{0}'", stime);
            return sql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[12];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;
            rType[10] = E_VType.E_Float;
            rType[11] = E_VType.E_Float;

            return rType;
        }
        public override string Name
        {
            get { return "DiyGsmMrCover"; }
        }
        private DateTime stime;
        public Dictionary<LaiKey, ZTAntGsmMrCover> antGsmMrCoverDic { get; set; } = new Dictionary<LaiKey, ZTAntGsmMrCover>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ZTAntGsmMrCover mr = new ZTAntGsmMrCover();
                    mr.iLac = package.Content.GetParamInt();
                    mr.iCi = package.Content.GetParamInt();
                    mr.上行100覆盖率 = package.Content.GetParamFloat();
                    mr.上行95覆盖率 = package.Content.GetParamFloat();
                    mr.上行90覆盖率 = package.Content.GetParamFloat();
                    mr.上行85覆盖率 = package.Content.GetParamFloat();
                    mr.下行100覆盖率 = package.Content.GetParamFloat();
                    mr.下行95覆盖率 = package.Content.GetParamFloat();
                    mr.下行90覆盖率 = package.Content.GetParamFloat();
                    mr.下行85覆盖率 = package.Content.GetParamFloat();
                    mr.上行567质量占比 = package.Content.GetParamFloat();
                    mr.下行567质量占比 = package.Content.GetParamFloat();

                    LaiKey LKey = new LaiKey();
                    LKey.ILac = mr.iLac;
                    LKey.ICi = mr.iCi;
                    if (!antGsmMrCoverDic.ContainsKey(LKey))
                        antGsmMrCoverDic[LKey] = new ZTAntGsmMrCover();
                    antGsmMrCoverDic[LKey] = mr;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    public class CellGsmPara
    {
        public int LAC { get; set; }
        public int CI { get; set; }

        public float 无线接通率 { get; set; }
        public float 寻呼成功率 { get; set; }
        public float TCH拥塞率不含切 { get; set; }
        public float SDCCH拥塞率 { get; set; }
        public float 上行TBF建立成功率 { get; set; }
        public float 下行TBF建立成功率 { get; set; }
        public float TCH掉话率含切换 { get; set; }
        public float 切换成功率 { get; set; }
        public float 下行TBF掉线率 { get; set; }
        public float 上行高干扰信道比例 { get; set; }

        public CellGsmPara()
        {
            LAC = 0;
            CI = 0;

            无线接通率 = 0;
            寻呼成功率 = 0;
            TCH拥塞率不含切 = 0;
            SDCCH拥塞率 = 0;
            上行TBF建立成功率 = 0;
            下行TBF建立成功率 = 0;
            TCH掉话率含切换 = 0;
            切换成功率 = 0;
            下行TBF掉线率 = 0;
            上行高干扰信道比例 = 0;
        }

        public string STR_无线接通率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 无线接通率, 2));
            }
        }

        public string STR_寻呼成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 寻呼成功率, 2));
            }
        }

        public string STR_TCH拥塞率不含切
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * TCH拥塞率不含切, 2));
            }
        }

        public string STR_SDCCH拥塞率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * SDCCH拥塞率, 2));
            }
        }

        public string STR_上行TBF建立成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 上行TBF建立成功率, 2));
            }
        }

        public string STR_下行TBF建立成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 下行TBF建立成功率, 2));
            }
        }

        public string STR_TCH掉话率含切换
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * TCH掉话率含切换, 2));
            }
        }

        public string STR_切换成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 切换成功率, 2));
            }
        }

        public string STR_下行TBF掉线率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 下行TBF掉线率, 2));
            }
        }

        public string STR_上行高干扰信道比例
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 上行高干扰信道比例, 2));
            }
        }
    }

    public class CellTdPara
    {
        public int LAC { get; set; }
        public int CI { get; set; }
        public float TD语音业务无线掉话率 { get; set; }
        public float PS域无线掉线率 { get; set; }
        public float RRC连接建立成功率 { get; set; }
        public float RAB建立成功率 { get; set; }
        public float 无线接通率 { get; set; }
        public float PS域接通率 { get; set; }
        public float PS域下行重行率 { get; set; }
        public float 上行电路域误块率 { get; set; }
        public float 上行分组域误块率 { get; set; }
        public float TD网内RNC间切换出成功率 { get; set; }
        public float TD网内RNC间硬切换入成功率 { get; set; }
        public float RNC内小区间切换成功率 { get; set; }
        public float 小区间切换出成功率 { get; set; }
        public float 小区间切换入成功率 { get; set; }

        public CellTdPara()
        {
            LAC = 0;
            CI = 0;
            TD语音业务无线掉话率 = 0;
            PS域无线掉线率 = 0;
            RRC连接建立成功率 = 0;
            RAB建立成功率 = 0;
            无线接通率 = 0;
            PS域接通率 = 0;
            PS域下行重行率 = 0;
            上行电路域误块率 = 0;
            上行分组域误块率 = 0;
            TD网内RNC间切换出成功率 = 0;
            TD网内RNC间硬切换入成功率 = 0;
            RNC内小区间切换成功率 = 0;
            小区间切换出成功率 = 0;
            小区间切换入成功率 = 0;
        }

        public string STR_TD语音业务无线掉话率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * TD语音业务无线掉话率, 2));
            }
        }
        public string STR_PS域无线掉线率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * PS域无线掉线率, 2));
            }
        }
        public string STR_RRC连接建立成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * RRC连接建立成功率, 2));
            } 
        }
        public string STR_RAB建立成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * RAB建立成功率, 2));
            }  
        }
        public string STR_无线接通率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 无线接通率, 2));
            }  
        }
        public string STR_PS域接通率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * PS域接通率, 2));
            }
        }
        public string STR_PS域下行重行率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * PS域下行重行率, 2));
            } 
        }
        public string STR_上行电路域误块率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 上行电路域误块率, 2));
            } 
        }
        public string STR_上行分组域误块率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 上行分组域误块率, 2));
            }  
        }
        public string STR_TD网内RNC间切换出成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * TD网内RNC间切换出成功率, 2));
            }  
        }
        public string STR_TD网内RNC间硬切换入成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * TD网内RNC间硬切换入成功率, 2));
            }   
        }
        public string STR_RNC内小区间切换成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * RNC内小区间切换成功率, 2));
            }  
        }
        public string STR_小区间切换出成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 小区间切换出成功率, 2));
            } 
        }
        public string STR_小区间切换入成功率
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * 小区间切换入成功率, 2));
            } 
        }
    }

    public class CellTdMRKpi
    {
        public int LAC { get; set; }
        public int CI { get; set; }
        public float F弱覆盖采样点比例 { get; set; }
        public float F良好覆盖采样点比例 { get; set; }
        public float F下行平均PCCPCH_RSCP { get; set; }
        public float F下行时隙干扰采样点比例 { get; set; }
        public float F上行时隙干扰采样点比例 { get; set; }
        public float FUPPTS干扰采样点比例 { get; set; }
        public float FUE高发射功率占比 { get; set; }
        public float FPS业务高误块率采样点占比 { get; set; }

        public CellTdMRKpi()
        {
            LAC = 0;
            CI = 0;
            F弱覆盖采样点比例 = 0;
            F良好覆盖采样点比例 = 0;
            F下行平均PCCPCH_RSCP = 0;
            F下行时隙干扰采样点比例 = 0;
            F上行时隙干扰采样点比例 = 0;
            FUPPTS干扰采样点比例 = 0;
            FUE高发射功率占比 = 0;
            FPS业务高误块率采样点占比 = 0;
        }

        public string S弱覆盖采样点比例
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * F弱覆盖采样点比例, 2));
            }
        }
        public string S良好覆盖采样点比例
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * F良好覆盖采样点比例, 2));
            }
        }
        public string S下行平均PCCPCH_RSCP
        {
            get
            {
                return string.Format("{0}", Math.Round(F下行平均PCCPCH_RSCP, 2));
            } 
        }
        public string S下行时隙干扰采样点比例
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * F下行时隙干扰采样点比例, 2));
            }  
        }
        public string S上行时隙干扰采样点比例
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * F上行时隙干扰采样点比例, 2));
            }  
        }
        public string SUPPTS干扰采样点比例
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * FUPPTS干扰采样点比例, 2));
            }   
        }
        public string SUE高发射功率占比
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * FUE高发射功率占比, 2));
            }   
        }
        public string SPS业务高误块率采样点占比
        {
            get
            {
                return string.Format("{0}%", Math.Round(100 * FPS业务高误块率采样点占比, 2));
            }   
        }
    }

    public class ZTAntGTMRBaseItem
    {
        public int iLAC { get; set; }
        public int iCI { get; set; }
        public int[] dataValue { get; set; }

        public ZTAntGTMRBaseItem()
        {
            iLAC = 0;
            iCI = 0;
            dataValue = new int[150];
        }

        public int[] getSubValue(int iNum)
        {
            int[] tmpValue = new int[iNum];
            for (int i = 0; i < iNum; i++)
            {
                tmpValue[i] = dataValue[i];
            }
            return tmpValue;
        }
    }

    public class GTCellMrData
    {
        //MR DATA
        public CellTdMRKpi tdKpiItem { get; set; }
        public ZTAntGTMRBaseItem tdMRRscpItem { get; set; }
        public ZTAntGTMRBaseItem tdMRTxPowerItem { get; set; }
        public ZTAntGTMRBaseItem tdMRAoaItem { get; set; }
        public ZTAntGTMRBaseItem tdMRTaItem { get; set; }
        public ZTAntGTMRBaseItem tdMRSirUlItem { get; set; }

        public GTCellMrData()
        {
            tdKpiItem = new CellTdMRKpi();
            tdMRRscpItem = new ZTAntGTMRBaseItem();
            tdMRTxPowerItem = new ZTAntGTMRBaseItem();
            tdMRAoaItem = new ZTAntGTMRBaseItem();
            tdMRTaItem = new ZTAntGTMRBaseItem();
            tdMRSirUlItem = new ZTAntGTMRBaseItem();
        }

        public int[,] AnaRttdAoa
        {
            get
            {
                if (tdMRTaItem == null || tdMRAoaItem == null)
                    return new int[37, 72];

                int[,] rttdAoaItem = new int[37, 72];
                for (int i = 0; i < 37; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        if (tdMRTaItem.dataValue[i] > 0 && tdMRAoaItem.dataValue[j] > 0)
                        {
                            rttdAoaItem[i, j] = tdMRTaItem.dataValue[i] + tdMRAoaItem.dataValue[j];
                        }
                    }
                }
                return rttdAoaItem;
            }
        }
    }
}
