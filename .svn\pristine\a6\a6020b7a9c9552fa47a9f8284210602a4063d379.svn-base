﻿namespace MasterCom.RAMS.Func
{
    partial class VolteMosAnalysisShow
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel1 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.LineSeriesView lineSeriesView1 = new DevExpress.XtraCharts.LineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel2 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.LineSeriesView lineSeriesView2 = new DevExpress.XtraCharts.LineSeriesView();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition1 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition2 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions1 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle2 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram4 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions2 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle3 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition3 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition4 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraCharts.XYDiagram xyDiagram5 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions3 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle4 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram6 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel10 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle5 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram7 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel11 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions4 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel12 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle6 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition5 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition6 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraCharts.XYDiagram xyDiagram8 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series8 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel13 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions5 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel14 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle7 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram9 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series9 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel15 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions6 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel16 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle8 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram10 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series10 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel17 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel18 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle9 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition7 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraGrid.StyleFormatCondition styleFormatCondition8 = new DevExpress.XtraGrid.StyleFormatCondition();
            DevExpress.XtraCharts.XYDiagram xyDiagram11 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series11 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel19 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions7 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel20 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle10 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram12 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series12 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel21 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions8 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel22 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle11 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram13 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series13 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel23 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel24 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle12 = new DevExpress.XtraCharts.ChartTitle();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl3 = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl4 = new DevExpress.XtraGrid.GridControl();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControl3 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl2 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl4 = new DevExpress.XtraCharts.ChartControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl6 = new DevExpress.XtraGrid.GridControl();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl7 = new DevExpress.XtraGrid.GridControl();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl5 = new DevExpress.XtraGrid.GridControl();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControl6 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl5 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl7 = new DevExpress.XtraCharts.ChartControl();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl9 = new DevExpress.XtraGrid.GridControl();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl10 = new DevExpress.XtraGrid.GridControl();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl8 = new DevExpress.XtraGrid.GridControl();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControl9 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl10 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl8 = new DevExpress.XtraCharts.ChartControl();
            this.panelControl3 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.cbxModel = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl12 = new DevExpress.XtraGrid.GridControl();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl13 = new DevExpress.XtraGrid.GridControl();
            this.gridView13 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControl11 = new DevExpress.XtraGrid.GridControl();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControl12 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl13 = new DevExpress.XtraCharts.ChartControl();
            this.chartControl11 = new DevExpress.XtraCharts.ChartControl();
            this.panelControl4 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.cbxCompareType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnExpore = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.btnMos = new DevExpress.XtraEditors.SimpleButton();
            this.txtMos = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView2)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).BeginInit();
            this.panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxModel.Properties)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl4)).BeginInit();
            this.panelControl4.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCompareType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMos.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "MOS均值";
            this.gridColumn4.FieldName = "MOS均值";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "MOS均值";
            this.gridColumn2.FieldName = "MOS均值";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "MOS均值";
            this.gridColumn6.FieldName = "MOS均值";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 1;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "MOS均值";
            this.gridColumn8.FieldName = "MOS均值";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(996, 460);
            this.xtraTabControl1.TabIndex = 20;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4,
            this.xtraTabPage5});
            this.xtraTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.splitContainerControl1);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(989, 430);
            this.xtraTabPage1.Text = "MOS区间分布";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControl1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.chartControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(989, 430);
            this.splitContainerControl1.SplitterPosition = 174;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(174, 430);
            this.gridControl1.TabIndex = 2;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(130, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(129, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // chartControl1
            // 
            xyDiagram1.AxisX.Label.Font = new System.Drawing.Font("Tahoma", 6F);
            xyDiagram1.AxisX.Label.Staggered = true;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl1.Legend.Visible = false;
            this.chartControl1.Location = new System.Drawing.Point(0, 0);
            this.chartControl1.Name = "chartControl1";
            pointSeriesLabel1.LineVisible = true;
            pointSeriesLabel1.Visible = false;
            series1.Label = pointSeriesLabel1;
            series1.Name = "Series 1";
            series1.View = lineSeriesView1;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            pointSeriesLabel2.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = pointSeriesLabel2;
            this.chartControl1.SeriesTemplate.View = lineSeriesView2;
            this.chartControl1.Size = new System.Drawing.Size(809, 430);
            this.chartControl1.TabIndex = 1;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.splitContainerControl2);
            this.xtraTabPage2.Controls.Add(this.panelControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(989, 430);
            this.xtraTabPage2.Text = "切换次数对MOS的影响";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 26);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControl3);
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControl4);
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControl2);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.chartControl3);
            this.splitContainerControl2.Panel2.Controls.Add(this.chartControl2);
            this.splitContainerControl2.Panel2.Controls.Add(this.chartControl4);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(989, 404);
            this.splitContainerControl2.SplitterPosition = 201;
            this.splitContainerControl2.TabIndex = 23;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridControl3
            // 
            this.gridControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl3.Location = new System.Drawing.Point(301, 0);
            this.gridControl3.MainView = this.gridView3;
            this.gridControl3.Name = "gridControl3";
            this.gridControl3.Size = new System.Drawing.Size(387, 201);
            this.gridControl3.TabIndex = 24;
            this.gridControl3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.gridView3.GridControl = this.gridControl3;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsSelection.MultiSelect = true;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridControl4
            // 
            this.gridControl4.Dock = System.Windows.Forms.DockStyle.Right;
            this.gridControl4.Location = new System.Drawing.Point(688, 0);
            this.gridControl4.MainView = this.gridView4;
            this.gridControl4.Name = "gridControl4";
            this.gridControl4.Size = new System.Drawing.Size(301, 201);
            this.gridControl4.TabIndex = 23;
            this.gridControl4.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView4});
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControl4;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsBehavior.Editable = false;
            this.gridView4.OptionsSelection.MultiSelect = true;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridControl2
            // 
            this.gridControl2.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControl2.Location = new System.Drawing.Point(0, 0);
            this.gridControl2.MainView = this.gridView2;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(301, 201);
            this.gridControl2.TabIndex = 22;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn4});
            styleFormatCondition1.Appearance.BackColor = System.Drawing.Color.Lime;
            styleFormatCondition1.Appearance.BackColor2 = System.Drawing.Color.Lime;
            styleFormatCondition1.Appearance.Options.UseBackColor = true;
            styleFormatCondition1.Column = this.gridColumn4;
            styleFormatCondition1.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition1.Expression = "[MOS均值] >= 2.8";
            styleFormatCondition2.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            styleFormatCondition2.Appearance.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            styleFormatCondition2.Appearance.Options.UseBackColor = true;
            styleFormatCondition2.Column = this.gridColumn4;
            styleFormatCondition2.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition2.Expression = "[MOS均值] < 2.8";
            this.gridView2.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition1,
            styleFormatCondition2});
            this.gridView2.GridControl = this.gridControl2;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsSelection.MultiSelect = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.OptionsView.ShowIndicator = false;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "切换次数";
            this.gridColumn3.FieldName = "切换次数";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            // 
            // chartControl3
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl3.Diagram = xyDiagram2;
            this.chartControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl3.Legend.Visible = false;
            this.chartControl3.Location = new System.Drawing.Point(301, 0);
            this.chartControl3.Name = "chartControl3";
            series2.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            sideBySideBarSeriesLabel1.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel1;
            series2.Name = "Series 1";
            pointOptions1.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions1.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series2.PointOptions = pointOptions1;
            this.chartControl3.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControl3.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControl3.Size = new System.Drawing.Size(387, 197);
            this.chartControl3.TabIndex = 16;
            chartTitle1.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle1.Text = "MOS>=2.8比例";
            this.chartControl3.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // chartControl2
            // 
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl2.Diagram = xyDiagram3;
            this.chartControl2.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControl2.Legend.Visible = false;
            this.chartControl2.Location = new System.Drawing.Point(0, 0);
            this.chartControl2.Name = "chartControl2";
            sideBySideBarSeriesLabel3.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel3;
            series3.Name = "Series 1";
            this.chartControl2.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3};
            sideBySideBarSeriesLabel4.LineVisible = true;
            this.chartControl2.SeriesTemplate.Label = sideBySideBarSeriesLabel4;
            this.chartControl2.Size = new System.Drawing.Size(301, 197);
            this.chartControl2.TabIndex = 13;
            chartTitle2.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle2.Text = "MOS均值";
            this.chartControl2.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle2});
            // 
            // chartControl4
            // 
            xyDiagram4.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram4.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram4.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl4.Diagram = xyDiagram4;
            this.chartControl4.Dock = System.Windows.Forms.DockStyle.Right;
            this.chartControl4.Legend.Visible = false;
            this.chartControl4.Location = new System.Drawing.Point(688, 0);
            this.chartControl4.Name = "chartControl4";
            series4.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            sideBySideBarSeriesLabel5.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel5;
            series4.Name = "Series 1";
            pointOptions2.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions2.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series4.PointOptions = pointOptions2;
            this.chartControl4.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series4};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControl4.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControl4.Size = new System.Drawing.Size(301, 197);
            this.chartControl4.TabIndex = 15;
            chartTitle3.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle3.Text = "切换次数比例";
            this.chartControl4.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle3});
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(989, 26);
            this.panelControl1.TabIndex = 22;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(5, 5);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(240, 14);
            this.labelControl1.TabIndex = 18;
            this.labelControl1.Text = "定位定位定位定位定位定位定位定位定位定位";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.splitContainerControl3);
            this.xtraTabPage3.Controls.Add(this.panelControl2);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(989, 430);
            this.xtraTabPage3.Text = "SINR对MOS的影响";
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 26);
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControl6);
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControl7);
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControl5);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControl6);
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControl5);
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControl7);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(989, 404);
            this.splitContainerControl3.SplitterPosition = 201;
            this.splitContainerControl3.TabIndex = 25;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // gridControl6
            // 
            this.gridControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl6.Location = new System.Drawing.Point(301, 0);
            this.gridControl6.MainView = this.gridView6;
            this.gridControl6.Name = "gridControl6";
            this.gridControl6.Size = new System.Drawing.Size(387, 201);
            this.gridControl6.TabIndex = 18;
            this.gridControl6.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView6});
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControl6;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsBehavior.Editable = false;
            this.gridView6.OptionsSelection.MultiSelect = true;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            this.gridView6.OptionsView.ShowIndicator = false;
            // 
            // gridControl7
            // 
            this.gridControl7.Dock = System.Windows.Forms.DockStyle.Right;
            this.gridControl7.Location = new System.Drawing.Point(688, 0);
            this.gridControl7.MainView = this.gridView7;
            this.gridControl7.Name = "gridControl7";
            this.gridControl7.Size = new System.Drawing.Size(301, 201);
            this.gridControl7.TabIndex = 17;
            this.gridControl7.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView7});
            // 
            // gridView7
            // 
            this.gridView7.GridControl = this.gridControl7;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.Editable = false;
            this.gridView7.OptionsSelection.MultiSelect = true;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            this.gridView7.OptionsView.ShowIndicator = false;
            // 
            // gridControl5
            // 
            this.gridControl5.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControl5.Location = new System.Drawing.Point(0, 0);
            this.gridControl5.MainView = this.gridView5;
            this.gridControl5.Name = "gridControl5";
            this.gridControl5.Size = new System.Drawing.Size(301, 201);
            this.gridControl5.TabIndex = 15;
            this.gridControl5.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView5});
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            styleFormatCondition3.Appearance.BackColor = System.Drawing.Color.Lime;
            styleFormatCondition3.Appearance.BackColor2 = System.Drawing.Color.Lime;
            styleFormatCondition3.Appearance.Options.UseBackColor = true;
            styleFormatCondition3.Column = this.gridColumn2;
            styleFormatCondition3.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition3.Expression = "[MOS均值] >= 2.8";
            styleFormatCondition4.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            styleFormatCondition4.Appearance.Options.UseBackColor = true;
            styleFormatCondition4.Column = this.gridColumn2;
            styleFormatCondition4.Condition = DevExpress.XtraGrid.FormatConditionEnum.Less;
            styleFormatCondition4.Value1 = "2.8";
            this.gridView5.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition3,
            styleFormatCondition4});
            this.gridView5.GridControl = this.gridControl5;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.Editable = false;
            this.gridView5.OptionsSelection.MultiSelect = true;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.OptionsView.ShowIndicator = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "Sinr";
            this.gridColumn1.FieldName = "Sinr";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // chartControl6
            // 
            xyDiagram5.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram5.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram5.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram5.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram5.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram5.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram5.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl6.Diagram = xyDiagram5;
            this.chartControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl6.Legend.Visible = false;
            this.chartControl6.Location = new System.Drawing.Point(301, 0);
            this.chartControl6.Name = "chartControl6";
            sideBySideBarSeriesLabel7.LineVisible = true;
            series5.Label = sideBySideBarSeriesLabel7;
            series5.Name = "Series 1";
            pointOptions3.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions3.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series5.PointOptions = pointOptions3;
            this.chartControl6.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5};
            sideBySideBarSeriesLabel8.LineVisible = true;
            this.chartControl6.SeriesTemplate.Label = sideBySideBarSeriesLabel8;
            this.chartControl6.Size = new System.Drawing.Size(387, 197);
            this.chartControl6.TabIndex = 23;
            chartTitle4.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle4.Text = "MOS>=2.8比例";
            this.chartControl6.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle4});
            // 
            // chartControl5
            // 
            xyDiagram6.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram6.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram6.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram6.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            xyDiagram6.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram6.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram6.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl5.Diagram = xyDiagram6;
            this.chartControl5.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControl5.Legend.Visible = false;
            this.chartControl5.Location = new System.Drawing.Point(0, 0);
            this.chartControl5.Name = "chartControl5";
            sideBySideBarSeriesLabel9.LineVisible = true;
            series6.Label = sideBySideBarSeriesLabel9;
            series6.Name = "Series 1";
            this.chartControl5.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series6};
            sideBySideBarSeriesLabel10.LineVisible = true;
            this.chartControl5.SeriesTemplate.Label = sideBySideBarSeriesLabel10;
            this.chartControl5.Size = new System.Drawing.Size(301, 197);
            this.chartControl5.TabIndex = 22;
            chartTitle5.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle5.Text = "MOS均值";
            this.chartControl5.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle5});
            // 
            // chartControl7
            // 
            xyDiagram7.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram7.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram7.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram7.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram7.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram7.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram7.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl7.Diagram = xyDiagram7;
            this.chartControl7.Dock = System.Windows.Forms.DockStyle.Right;
            this.chartControl7.Legend.Visible = false;
            this.chartControl7.Location = new System.Drawing.Point(688, 0);
            this.chartControl7.Name = "chartControl7";
            sideBySideBarSeriesLabel11.LineVisible = true;
            series7.Label = sideBySideBarSeriesLabel11;
            series7.Name = "Series 1";
            pointOptions4.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions4.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series7.PointOptions = pointOptions4;
            this.chartControl7.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series7};
            sideBySideBarSeriesLabel12.LineVisible = true;
            this.chartControl7.SeriesTemplate.Label = sideBySideBarSeriesLabel12;
            this.chartControl7.Size = new System.Drawing.Size(301, 197);
            this.chartControl7.TabIndex = 7;
            chartTitle6.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle6.Text = "SINR比例";
            this.chartControl7.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle6});
            // 
            // panelControl2
            // 
            this.panelControl2.Controls.Add(this.labelControl2);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl2.Location = new System.Drawing.Point(0, 0);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(989, 26);
            this.panelControl2.TabIndex = 24;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(5, 5);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(240, 14);
            this.labelControl2.TabIndex = 23;
            this.labelControl2.Text = "定位定位定位定位定位定位定位定位定位定位";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.splitContainerControl4);
            this.xtraTabPage4.Controls.Add(this.panelControl3);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(989, 430);
            this.xtraTabPage4.Text = "调制方式对MOS的影响";
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl4.Horizontal = false;
            this.splitContainerControl4.Location = new System.Drawing.Point(0, 26);
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControl9);
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControl10);
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControl8);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControl9);
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControl10);
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControl8);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(989, 404);
            this.splitContainerControl4.SplitterPosition = 201;
            this.splitContainerControl4.TabIndex = 26;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // gridControl9
            // 
            this.gridControl9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl9.Location = new System.Drawing.Point(301, 0);
            this.gridControl9.MainView = this.gridView9;
            this.gridControl9.Name = "gridControl9";
            this.gridControl9.Size = new System.Drawing.Size(387, 201);
            this.gridControl9.TabIndex = 22;
            this.gridControl9.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView9});
            // 
            // gridView9
            // 
            this.gridView9.GridControl = this.gridControl9;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsBehavior.Editable = false;
            this.gridView9.OptionsSelection.MultiSelect = true;
            this.gridView9.OptionsView.ShowGroupPanel = false;
            this.gridView9.OptionsView.ShowIndicator = false;
            // 
            // gridControl10
            // 
            this.gridControl10.Dock = System.Windows.Forms.DockStyle.Right;
            this.gridControl10.Location = new System.Drawing.Point(688, 0);
            this.gridControl10.MainView = this.gridView10;
            this.gridControl10.Name = "gridControl10";
            this.gridControl10.Size = new System.Drawing.Size(301, 201);
            this.gridControl10.TabIndex = 21;
            this.gridControl10.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView10});
            // 
            // gridView10
            // 
            this.gridView10.GridControl = this.gridControl10;
            this.gridView10.Name = "gridView10";
            this.gridView10.OptionsBehavior.Editable = false;
            this.gridView10.OptionsSelection.MultiSelect = true;
            this.gridView10.OptionsView.ShowGroupPanel = false;
            this.gridView10.OptionsView.ShowIndicator = false;
            // 
            // gridControl8
            // 
            this.gridControl8.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControl8.Location = new System.Drawing.Point(0, 0);
            this.gridControl8.MainView = this.gridView8;
            this.gridControl8.Name = "gridControl8";
            this.gridControl8.Size = new System.Drawing.Size(301, 201);
            this.gridControl8.TabIndex = 20;
            this.gridControl8.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView8});
            // 
            // gridView8
            // 
            this.gridView8.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6});
            styleFormatCondition5.Appearance.BackColor = System.Drawing.Color.Lime;
            styleFormatCondition5.Appearance.BackColor2 = System.Drawing.Color.Lime;
            styleFormatCondition5.Appearance.Options.UseBackColor = true;
            styleFormatCondition5.Column = this.gridColumn6;
            styleFormatCondition5.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition5.Expression = "[MOS均值] >= 2.8";
            styleFormatCondition6.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            styleFormatCondition6.Appearance.Options.UseBackColor = true;
            styleFormatCondition6.Column = this.gridColumn6;
            styleFormatCondition6.Condition = DevExpress.XtraGrid.FormatConditionEnum.Less;
            styleFormatCondition6.Value1 = "2.8";
            this.gridView8.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition5,
            styleFormatCondition6});
            this.gridView8.GridControl = this.gridControl8;
            this.gridView8.Name = "gridView8";
            this.gridView8.OptionsBehavior.Editable = false;
            this.gridView8.OptionsSelection.MultiSelect = true;
            this.gridView8.OptionsView.ShowGroupPanel = false;
            this.gridView8.OptionsView.ShowIndicator = false;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "调制方式QAM16占比";
            this.gridColumn5.FieldName = "调制方式";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 0;
            // 
            // chartControl9
            // 
            xyDiagram8.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram8.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram8.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram8.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram8.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram8.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram8.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl9.Diagram = xyDiagram8;
            this.chartControl9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl9.Legend.Visible = false;
            this.chartControl9.Location = new System.Drawing.Point(301, 0);
            this.chartControl9.Name = "chartControl9";
            sideBySideBarSeriesLabel13.LineVisible = true;
            series8.Label = sideBySideBarSeriesLabel13;
            series8.Name = "Series 1";
            pointOptions5.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions5.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series8.PointOptions = pointOptions5;
            this.chartControl9.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series8};
            sideBySideBarSeriesLabel14.LineVisible = true;
            this.chartControl9.SeriesTemplate.Label = sideBySideBarSeriesLabel14;
            this.chartControl9.Size = new System.Drawing.Size(387, 197);
            this.chartControl9.TabIndex = 24;
            chartTitle7.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle7.Text = "MOS>=2.8比例";
            this.chartControl9.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle7});
            // 
            // chartControl10
            // 
            xyDiagram9.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram9.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram9.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram9.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram9.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram9.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram9.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl10.Diagram = xyDiagram9;
            this.chartControl10.Dock = System.Windows.Forms.DockStyle.Right;
            this.chartControl10.Legend.Visible = false;
            this.chartControl10.Location = new System.Drawing.Point(688, 0);
            this.chartControl10.Name = "chartControl10";
            sideBySideBarSeriesLabel15.LineVisible = true;
            series9.Label = sideBySideBarSeriesLabel15;
            series9.Name = "Series 1";
            pointOptions6.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions6.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series9.PointOptions = pointOptions6;
            this.chartControl10.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series9};
            sideBySideBarSeriesLabel16.LineVisible = true;
            this.chartControl10.SeriesTemplate.Label = sideBySideBarSeriesLabel16;
            this.chartControl10.Size = new System.Drawing.Size(301, 197);
            this.chartControl10.TabIndex = 23;
            chartTitle8.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle8.Text = "调制方式上行QAM16比例";
            this.chartControl10.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle8});
            // 
            // chartControl8
            // 
            xyDiagram10.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram10.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram10.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram10.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            xyDiagram10.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram10.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram10.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl8.Diagram = xyDiagram10;
            this.chartControl8.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControl8.Legend.Visible = false;
            this.chartControl8.Location = new System.Drawing.Point(0, 0);
            this.chartControl8.Name = "chartControl8";
            sideBySideBarSeriesLabel17.LineVisible = true;
            series10.Label = sideBySideBarSeriesLabel17;
            series10.Name = "Series 1";
            this.chartControl8.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series10};
            sideBySideBarSeriesLabel18.LineVisible = true;
            this.chartControl8.SeriesTemplate.Label = sideBySideBarSeriesLabel18;
            this.chartControl8.Size = new System.Drawing.Size(301, 197);
            this.chartControl8.TabIndex = 22;
            chartTitle9.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle9.Text = "MOS均值";
            this.chartControl8.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle9});
            // 
            // panelControl3
            // 
            this.panelControl3.Controls.Add(this.labelControl7);
            this.panelControl3.Controls.Add(this.cbxModel);
            this.panelControl3.Controls.Add(this.labelControl3);
            this.panelControl3.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl3.Location = new System.Drawing.Point(0, 0);
            this.panelControl3.Name = "panelControl3";
            this.panelControl3.Size = new System.Drawing.Size(989, 26);
            this.panelControl3.TabIndex = 25;
            // 
            // labelControl7
            // 
            this.labelControl7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl7.Location = new System.Drawing.Point(795, 6);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(48, 14);
            this.labelControl7.TabIndex = 25;
            this.labelControl7.Text = "调制方式";
            // 
            // cbxModel
            // 
            this.cbxModel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxModel.EditValue = "上行QAM16";
            this.cbxModel.Location = new System.Drawing.Point(849, 2);
            this.cbxModel.Name = "cbxModel";
            this.cbxModel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxModel.Properties.Items.AddRange(new object[] {
            "上行QAM16",
            "下行QAM16",
            "下行QAM64",
            "下行QPSK"});
            this.cbxModel.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxModel.Size = new System.Drawing.Size(121, 21);
            this.cbxModel.TabIndex = 24;
            this.cbxModel.SelectedIndexChanged += new System.EventHandler(this.cbxModel_SelectedIndexChanged);
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(5, 5);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(216, 14);
            this.labelControl3.TabIndex = 23;
            this.labelControl3.Text = "定位定位定位定位定位定位定位定位定位";
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.splitContainerControl5);
            this.xtraTabPage5.Controls.Add(this.panelControl4);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(989, 430);
            this.xtraTabPage5.Text = "RSRP对MOS的影响";
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl5.Horizontal = false;
            this.splitContainerControl5.Location = new System.Drawing.Point(0, 26);
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.gridControl12);
            this.splitContainerControl5.Panel1.Controls.Add(this.gridControl13);
            this.splitContainerControl5.Panel1.Controls.Add(this.gridControl11);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.chartControl12);
            this.splitContainerControl5.Panel2.Controls.Add(this.chartControl13);
            this.splitContainerControl5.Panel2.Controls.Add(this.chartControl11);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.Size = new System.Drawing.Size(989, 404);
            this.splitContainerControl5.SplitterPosition = 201;
            this.splitContainerControl5.TabIndex = 26;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // gridControl12
            // 
            this.gridControl12.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl12.Location = new System.Drawing.Point(301, 0);
            this.gridControl12.MainView = this.gridView12;
            this.gridControl12.Name = "gridControl12";
            this.gridControl12.Size = new System.Drawing.Size(387, 201);
            this.gridControl12.TabIndex = 22;
            this.gridControl12.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView12});
            // 
            // gridView12
            // 
            this.gridView12.GridControl = this.gridControl12;
            this.gridView12.Name = "gridView12";
            this.gridView12.OptionsBehavior.Editable = false;
            this.gridView12.OptionsSelection.MultiSelect = true;
            this.gridView12.OptionsView.ShowGroupPanel = false;
            this.gridView12.OptionsView.ShowIndicator = false;
            // 
            // gridControl13
            // 
            this.gridControl13.Dock = System.Windows.Forms.DockStyle.Right;
            this.gridControl13.Location = new System.Drawing.Point(688, 0);
            this.gridControl13.MainView = this.gridView13;
            this.gridControl13.Name = "gridControl13";
            this.gridControl13.Size = new System.Drawing.Size(301, 201);
            this.gridControl13.TabIndex = 21;
            this.gridControl13.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView13});
            // 
            // gridView13
            // 
            this.gridView13.GridControl = this.gridControl13;
            this.gridView13.Name = "gridView13";
            this.gridView13.OptionsBehavior.Editable = false;
            this.gridView13.OptionsSelection.MultiSelect = true;
            this.gridView13.OptionsView.ShowGroupPanel = false;
            this.gridView13.OptionsView.ShowIndicator = false;
            // 
            // gridControl11
            // 
            this.gridControl11.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControl11.Location = new System.Drawing.Point(0, 0);
            this.gridControl11.MainView = this.gridView11;
            this.gridControl11.Name = "gridControl11";
            this.gridControl11.Size = new System.Drawing.Size(301, 201);
            this.gridControl11.TabIndex = 20;
            this.gridControl11.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView11});
            // 
            // gridView11
            // 
            this.gridView11.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8});
            styleFormatCondition7.Appearance.BackColor = System.Drawing.Color.Lime;
            styleFormatCondition7.Appearance.BackColor2 = System.Drawing.Color.Lime;
            styleFormatCondition7.Appearance.Options.UseBackColor = true;
            styleFormatCondition7.Column = this.gridColumn8;
            styleFormatCondition7.Condition = DevExpress.XtraGrid.FormatConditionEnum.Expression;
            styleFormatCondition7.Expression = "[MOS均值] >= 2.8";
            styleFormatCondition8.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            styleFormatCondition8.Appearance.Options.UseBackColor = true;
            styleFormatCondition8.Column = this.gridColumn8;
            styleFormatCondition8.Condition = DevExpress.XtraGrid.FormatConditionEnum.Less;
            styleFormatCondition8.Value1 = "2.8";
            this.gridView11.FormatConditions.AddRange(new DevExpress.XtraGrid.StyleFormatCondition[] {
            styleFormatCondition7,
            styleFormatCondition8});
            this.gridView11.GridControl = this.gridControl11;
            this.gridView11.Name = "gridView11";
            this.gridView11.OptionsBehavior.Editable = false;
            this.gridView11.OptionsSelection.MultiSelect = true;
            this.gridView11.OptionsView.ShowGroupPanel = false;
            this.gridView11.OptionsView.ShowIndicator = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "RSRP";
            this.gridColumn7.FieldName = "Rsrp";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            // 
            // chartControl12
            // 
            xyDiagram11.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram11.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram11.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram11.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram11.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram11.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram11.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl12.Diagram = xyDiagram11;
            this.chartControl12.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl12.Legend.Visible = false;
            this.chartControl12.Location = new System.Drawing.Point(301, 0);
            this.chartControl12.Name = "chartControl12";
            sideBySideBarSeriesLabel19.LineVisible = true;
            series11.Label = sideBySideBarSeriesLabel19;
            series11.Name = "Series 1";
            pointOptions7.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions7.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series11.PointOptions = pointOptions7;
            this.chartControl12.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series11};
            sideBySideBarSeriesLabel20.LineVisible = true;
            this.chartControl12.SeriesTemplate.Label = sideBySideBarSeriesLabel20;
            this.chartControl12.Size = new System.Drawing.Size(387, 197);
            this.chartControl12.TabIndex = 24;
            chartTitle10.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle10.Text = "MOS>=2.8比例";
            this.chartControl12.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle10});
            // 
            // chartControl13
            // 
            xyDiagram12.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram12.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram12.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram12.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram12.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram12.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram12.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl13.Diagram = xyDiagram12;
            this.chartControl13.Dock = System.Windows.Forms.DockStyle.Right;
            this.chartControl13.Legend.Visible = false;
            this.chartControl13.Location = new System.Drawing.Point(688, 0);
            this.chartControl13.Name = "chartControl13";
            sideBySideBarSeriesLabel21.LineVisible = true;
            series12.Label = sideBySideBarSeriesLabel21;
            series12.Name = "Series 1";
            pointOptions8.ArgumentNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            pointOptions8.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series12.PointOptions = pointOptions8;
            this.chartControl13.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series12};
            sideBySideBarSeriesLabel22.LineVisible = true;
            this.chartControl13.SeriesTemplate.Label = sideBySideBarSeriesLabel22;
            this.chartControl13.Size = new System.Drawing.Size(301, 197);
            this.chartControl13.TabIndex = 23;
            chartTitle11.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle11.Text = "RSRP比例";
            this.chartControl13.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle11});
            // 
            // chartControl11
            // 
            xyDiagram13.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram13.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram13.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram13.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            xyDiagram13.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram13.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram13.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl11.Diagram = xyDiagram13;
            this.chartControl11.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControl11.Legend.Visible = false;
            this.chartControl11.Location = new System.Drawing.Point(0, 0);
            this.chartControl11.Name = "chartControl11";
            sideBySideBarSeriesLabel23.LineVisible = true;
            series13.Label = sideBySideBarSeriesLabel23;
            series13.Name = "Series 1";
            this.chartControl11.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series13};
            sideBySideBarSeriesLabel24.LineVisible = true;
            this.chartControl11.SeriesTemplate.Label = sideBySideBarSeriesLabel24;
            this.chartControl11.Size = new System.Drawing.Size(301, 197);
            this.chartControl11.TabIndex = 22;
            chartTitle12.Font = new System.Drawing.Font("Tahoma", 10F);
            chartTitle12.Text = "MOS均值";
            this.chartControl11.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle12});
            // 
            // panelControl4
            // 
            this.panelControl4.Controls.Add(this.labelControl5);
            this.panelControl4.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl4.Location = new System.Drawing.Point(0, 0);
            this.panelControl4.Name = "panelControl4";
            this.panelControl4.Size = new System.Drawing.Size(989, 26);
            this.panelControl4.TabIndex = 25;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(5, 5);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(216, 14);
            this.labelControl5.TabIndex = 23;
            this.labelControl5.Text = "定位定位定位定位定位定位定位定位定位";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(504, 5);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(65, 14);
            this.labelControl4.TabIndex = 4;
            this.labelControl4.Text = "MOS值标准:";
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.cbxCompareType);
            this.panel1.Controls.Add(this.btnExpore);
            this.panel1.Controls.Add(this.labelControl6);
            this.panel1.Controls.Add(this.btnMos);
            this.panel1.Controls.Add(this.txtMos);
            this.panel1.Location = new System.Drawing.Point(667, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(322, 23);
            this.panel1.TabIndex = 26;
            this.panel1.Visible = false;
            // 
            // cbxCompareType
            // 
            this.cbxCompareType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxCompareType.EditValue = ">=";
            this.cbxCompareType.Location = new System.Drawing.Point(45, 1);
            this.cbxCompareType.Name = "cbxCompareType";
            this.cbxCompareType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxCompareType.Properties.Items.AddRange(new object[] {
            ">=",
            "<="});
            this.cbxCompareType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxCompareType.Size = new System.Drawing.Size(50, 21);
            this.cbxCompareType.TabIndex = 23;
            // 
            // btnExpore
            // 
            this.btnExpore.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExpore.Location = new System.Drawing.Point(231, 0);
            this.btnExpore.Name = "btnExpore";
            this.btnExpore.Size = new System.Drawing.Size(91, 22);
            this.btnExpore.TabIndex = 21;
            this.btnExpore.Text = "导出到Excel";
            this.btnExpore.Click += new System.EventHandler(this.btnExpore_Click);
            // 
            // labelControl6
            // 
            this.labelControl6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl6.Location = new System.Drawing.Point(2, 4);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(37, 14);
            this.labelControl6.TabIndex = 4;
            this.labelControl6.Text = "MOS值";
            // 
            // btnMos
            // 
            this.btnMos.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnMos.Location = new System.Drawing.Point(150, 0);
            this.btnMos.Name = "btnMos";
            this.btnMos.Size = new System.Drawing.Size(55, 22);
            this.btnMos.TabIndex = 6;
            this.btnMos.Text = "确定";
            this.btnMos.Click += new System.EventHandler(this.btnMos_Click);
            // 
            // txtMos
            // 
            this.txtMos.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtMos.EditValue = "2.8";
            this.txtMos.Location = new System.Drawing.Point(101, 1);
            this.txtMos.Name = "txtMos";
            this.txtMos.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtMos.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtMos.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtMos.Size = new System.Drawing.Size(43, 21);
            this.txtMos.TabIndex = 5;
            this.txtMos.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtMos_KeyPress);
            // 
            // VolteMosAnalysisShow
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(996, 460);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "VolteMosAnalysisShow";
            this.ShowIcon = false;
            this.Text = "MOS关联分析";
            this.Load += new System.EventHandler(this.VolteMosAnalysisShow_Load);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.panelControl2.PerformLayout();
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).EndInit();
            this.panelControl3.ResumeLayout(false);
            this.panelControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxModel.Properties)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl4)).EndInit();
            this.panelControl4.ResumeLayout(false);
            this.panelControl4.PerformLayout();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCompareType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMos.Properties)).EndInit();
            this.ResumeLayout(false);

        }
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraGrid.GridControl gridControl3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.GridControl gridControl4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraCharts.ChartControl chartControl3;
        private DevExpress.XtraCharts.ChartControl chartControl2;
        private DevExpress.XtraCharts.ChartControl chartControl4;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraGrid.GridControl gridControl6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.GridControl gridControl7;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.GridControl gridControl5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraCharts.ChartControl chartControl6;
        private DevExpress.XtraCharts.ChartControl chartControl5;
        private DevExpress.XtraCharts.ChartControl chartControl7;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraGrid.GridControl gridControl9;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.GridControl gridControl10;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraGrid.GridControl gridControl8;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraCharts.ChartControl chartControl9;
        private DevExpress.XtraCharts.ChartControl chartControl10;
        private DevExpress.XtraCharts.ChartControl chartControl8;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraGrid.GridControl gridControl12;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraGrid.GridControl gridControl13;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView13;
        private DevExpress.XtraGrid.GridControl gridControl11;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraCharts.ChartControl chartControl12;
        private DevExpress.XtraCharts.ChartControl chartControl13;
        private DevExpress.XtraCharts.ChartControl chartControl11;
        private DevExpress.XtraEditors.PanelControl panelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxCompareType;
        private DevExpress.XtraEditors.SimpleButton btnExpore;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SimpleButton btnMos;
        private DevExpress.XtraEditors.TextEdit txtMos;
        private DevExpress.XtraEditors.ComboBoxEdit cbxModel;
        private DevExpress.XtraEditors.LabelControl labelControl7;
    }
}