﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_CONFIG_TABLECOLUMN = 0x32;
    }

    public static partial class ResponseType
    {
        public const byte RESTYPE_CONFIG_TABLECOLUMN = 0x32;
    }
}

namespace MasterCom.RAMS.Model.Interface
{
   

    public class QueryInterfaceColumnDefInfo : QueryBase
    {

        public QueryInterfaceColumnDefInfo(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "查询接口定义"; }
        }

        public override string IconName
        {
            get { return "Images/cellcover.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            ClientProxy clientProxy = connectServerWithWaitBox();
            if (clientProxy.ConnectResult != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }

            try
            {
                WaitBox.Show("开始读取接口格式定义...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected void queryInThread(object o)
        {
            try
            {
                System.Threading.Thread.Sleep(20);
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_CONFIG_TABLECOLUMN;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                int index = 0;
                while (true)
                {
                    clientProxy.Recieve();

                    if (package.Content.Type == ResponseType.RESTYPE_CONFIG_TABLECOLUMN)
                    {
                        package.Content.PrepareGetParam();
                        ColumnDefItem itm = ColumnDefItem.FillFrom(package.Content);
                        InterfaceManager.GetInstance().AddColumnDef(itm);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    if ((Math.Log(index++) * 8) > WaitBox.ProgressPercent)
                    {
                        WaitBox.ProgressPercent++;
                    }
                }
            }
#if DEBUG
#else
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
#endif
            finally
            {
                WaitBox.Close();
            }
        }
    }
}
