<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">VOLTE报表</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp" />
          <Item typeName="Int32" key="RowAt">49</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">接通率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">掉话率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">呼叫建立时延(s)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS3.0以上占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">IMS注册成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">eSRVCC成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">eSRVCC切换时延-用户面（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">eSRVCC用户面中断时长</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">VLOTE弱信号质差</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]+evtIdCount[1069]+value9[1069])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]+evtIdCount[1079]+value9[1079]+evtIdCount[1089]+value9[1089]))/(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]+evtIdCount[1069]+value9[1069])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056]+evtIdCount[1077]+value9[1077]+evtIdCount[1078]+value9[1078]+evtIdCount[1085]+value9[1085]+evtIdCount[1086]+value9[1086])/((evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]+evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021]+evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041]+evtIdCount[1069]+value9[1069]+evtIdCount[1070]+value9[1070])-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]+evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]+evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]+evtIdCount[1079]+value9[1079]+evtIdCount[1080]+value9[1080]+evtIdCount[1089]+value9[1089]+evtIdCount[1090]+value9[1090]))}%
</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(value1[1011]+value1[1031]+value3[1051]+value1[1071])/(1000.0*(evtIdCount[1011]+evtIdCount[1031]+evtIdCount[1051]+evtIdCount[1071]))}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lte_6D2105+Lte_6D2106+Lte_6D2107+Lte_6D210C +Lte_61216205+Lte_61216206+Lte_61216207+Lte_6121620C)/(Lte_6D2108+Lte_61216208)*100 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[1165] + value9[1165]) / (evtIdCount[1165] + value9[1165] + evtIdCount[1166] + value9[1166]))*100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[1145] / evtIdCount[1144])*100}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value5[1145]/(1000.0*evtIdCount[1145])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value6[1145]/(evtIdCount[1145])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value4[1183] / 1000) / Lte_0806) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo" />
      <Item typeName="IList" key="ColWidth" />
    </Item>
  </Config>
</Configs>