﻿namespace MasterCom.RAMS.Func
{
    partial class MapLTECellLayerBTSPropertis
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label label1;
            this.grpLabel = new System.Windows.Forms.GroupBox();
            this.btnFont = new DevExpress.XtraEditors.SimpleButton();
            this.cbxBTSDescription = new System.Windows.Forms.CheckBox();
            this.cbxBTSType = new System.Windows.Forms.CheckBox();
            this.cbxBTSLatitude = new System.Windows.Forms.CheckBox();
            this.checkBoxDrawBTSLabel = new System.Windows.Forms.CheckBox();
            this.cbxBTSLongitude = new System.Windows.Forms.CheckBox();
            this.cbxBTSName = new System.Windows.Forms.CheckBox();
            this.numericUpDownSize = new System.Windows.Forms.NumericUpDown();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.label100 = new System.Windows.Forms.Label();
            this.label0 = new System.Windows.Forms.Label();
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.LabelOpacity = new System.Windows.Forms.Label();
            this.labelColor = new System.Windows.Forms.Label();
            this.colorBTS = new DevExpress.XtraEditors.ColorEdit();
            this.grpFactor = new System.Windows.Forms.GroupBox();
            this.Label = new System.Windows.Forms.Label();
            this.colorLable = new DevExpress.XtraEditors.ColorEdit();
            label1 = new System.Windows.Forms.Label();
            this.grpLabel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorBTS.Properties)).BeginInit();
            this.grpFactor.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorLable.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(264, 27);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(35, 12);
            label1.TabIndex = 81;
            label1.Text = "尺寸:";
            // 
            // grpLabel
            // 
            this.grpLabel.Controls.Add(this.Label);
            this.grpLabel.Controls.Add(this.colorLable);
            this.grpLabel.Controls.Add(this.btnFont);
            this.grpLabel.Controls.Add(this.cbxBTSDescription);
            this.grpLabel.Controls.Add(this.cbxBTSType);
            this.grpLabel.Controls.Add(this.cbxBTSLatitude);
            this.grpLabel.Controls.Add(this.checkBoxDrawBTSLabel);
            this.grpLabel.Controls.Add(this.cbxBTSLongitude);
            this.grpLabel.Controls.Add(this.cbxBTSName);
            this.grpLabel.Location = new System.Drawing.Point(23, 155);
            this.grpLabel.Name = "grpLabel";
            this.grpLabel.Size = new System.Drawing.Size(368, 76);
            this.grpLabel.TabIndex = 83;
            this.grpLabel.TabStop = false;
            // 
            // btnFont
            // 
            this.btnFont.Location = new System.Drawing.Point(285, 19);
            this.btnFont.Name = "btnFont";
            this.btnFont.Size = new System.Drawing.Size(75, 23);
            this.btnFont.TabIndex = 77;
            this.btnFont.Text = "字体";
            // 
            // cbxBTSDescription
            // 
            this.cbxBTSDescription.AutoSize = true;
            this.cbxBTSDescription.Location = new System.Drawing.Point(235, 23);
            this.cbxBTSDescription.Name = "cbxBTSDescription";
            this.cbxBTSDescription.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSDescription.TabIndex = 76;
            this.cbxBTSDescription.Text = "描述";
            this.cbxBTSDescription.UseVisualStyleBackColor = true;
            // 
            // cbxBTSType
            // 
            this.cbxBTSType.AutoSize = true;
            this.cbxBTSType.Location = new System.Drawing.Point(181, 23);
            this.cbxBTSType.Name = "cbxBTSType";
            this.cbxBTSType.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSType.TabIndex = 75;
            this.cbxBTSType.Text = "类型";
            this.cbxBTSType.UseVisualStyleBackColor = true;
            // 
            // cbxBTSLatitude
            // 
            this.cbxBTSLatitude.AutoSize = true;
            this.cbxBTSLatitude.Location = new System.Drawing.Point(127, 23);
            this.cbxBTSLatitude.Name = "cbxBTSLatitude";
            this.cbxBTSLatitude.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSLatitude.TabIndex = 74;
            this.cbxBTSLatitude.Text = "纬度";
            this.cbxBTSLatitude.UseVisualStyleBackColor = true;
            // 
            // checkBoxDrawBTSLabel
            // 
            this.checkBoxDrawBTSLabel.AutoSize = true;
            this.checkBoxDrawBTSLabel.Location = new System.Drawing.Point(6, 0);
            this.checkBoxDrawBTSLabel.Name = "checkBoxDrawBTSLabel";
            this.checkBoxDrawBTSLabel.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDrawBTSLabel.TabIndex = 79;
            this.checkBoxDrawBTSLabel.Text = "显示标签";
            this.checkBoxDrawBTSLabel.UseVisualStyleBackColor = true;
            // 
            // cbxBTSLongitude
            // 
            this.cbxBTSLongitude.AutoSize = true;
            this.cbxBTSLongitude.Location = new System.Drawing.Point(75, 23);
            this.cbxBTSLongitude.Name = "cbxBTSLongitude";
            this.cbxBTSLongitude.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSLongitude.TabIndex = 73;
            this.cbxBTSLongitude.Text = "经度";
            this.cbxBTSLongitude.UseVisualStyleBackColor = true;
            // 
            // cbxBTSName
            // 
            this.cbxBTSName.AutoSize = true;
            this.cbxBTSName.Checked = true;
            this.cbxBTSName.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxBTSName.Location = new System.Drawing.Point(26, 23);
            this.cbxBTSName.Name = "cbxBTSName";
            this.cbxBTSName.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSName.TabIndex = 70;
            this.cbxBTSName.Text = "名称";
            this.cbxBTSName.UseVisualStyleBackColor = true;
            // 
            // numericUpDownSize
            // 
            this.numericUpDownSize.Location = new System.Drawing.Point(300, 23);
            this.numericUpDownSize.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numericUpDownSize.Minimum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.Name = "numericUpDownSize";
            this.numericUpDownSize.Size = new System.Drawing.Size(34, 21);
            this.numericUpDownSize.TabIndex = 82;
            this.numericUpDownSize.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(13, 0);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDisplay.TabIndex = 78;
            this.checkBoxDisplay.Text = "显示图元";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            // 
            // label100
            // 
            this.label100.AutoSize = true;
            this.label100.Location = new System.Drawing.Point(205, 94);
            this.label100.Name = "label100";
            this.label100.Size = new System.Drawing.Size(53, 12);
            this.label100.TabIndex = 77;
            this.label100.Text = "100%透明";
            this.label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label0
            // 
            this.label0.AutoSize = true;
            this.label0.Location = new System.Drawing.Point(73, 94);
            this.label0.Name = "label0";
            this.label0.Size = new System.Drawing.Size(41, 12);
            this.label0.TabIndex = 76;
            this.label0.Text = "不透明";
            this.label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.AutoSize = false;
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(71, 54);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(187, 28);
            this.TrackBarOpacity.TabIndex = 74;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.AutoSize = true;
            this.LabelOpacity.Location = new System.Drawing.Point(18, 60);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(47, 12);
            this.LabelOpacity.TabIndex = 73;
            this.LabelOpacity.Text = "透明度:";
            // 
            // labelColor
            // 
            this.labelColor.Location = new System.Drawing.Point(28, 23);
            this.labelColor.Name = "labelColor";
            this.labelColor.Size = new System.Drawing.Size(37, 20);
            this.labelColor.TabIndex = 72;
            this.labelColor.Text = "颜色:";
            this.labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // colorBTS
            // 
            this.colorBTS.EditValue = System.Drawing.Color.Black;
            this.colorBTS.Location = new System.Drawing.Point(75, 21);
            this.colorBTS.Name = "colorBTS";
            this.colorBTS.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorBTS.Properties.ShowWebColors = false;
            this.colorBTS.Size = new System.Drawing.Size(183, 21);
            this.colorBTS.TabIndex = 84;
            // 
            // grpFactor
            // 
            this.grpFactor.Controls.Add(this.TrackBarOpacity);
            this.grpFactor.Controls.Add(this.colorBTS);
            this.grpFactor.Controls.Add(this.label100);
            this.grpFactor.Controls.Add(this.LabelOpacity);
            this.grpFactor.Controls.Add(this.label0);
            this.grpFactor.Controls.Add(this.numericUpDownSize);
            this.grpFactor.Controls.Add(this.checkBoxDisplay);
            this.grpFactor.Controls.Add(label1);
            this.grpFactor.Controls.Add(this.labelColor);
            this.grpFactor.Location = new System.Drawing.Point(23, 17);
            this.grpFactor.Name = "grpFactor";
            this.grpFactor.Size = new System.Drawing.Size(368, 132);
            this.grpFactor.TabIndex = 85;
            this.grpFactor.TabStop = false;
            // 
            // Label
            // 
            this.Label.Location = new System.Drawing.Point(254, 48);
            this.Label.Name = "Label";
            this.Label.Size = new System.Drawing.Size(56, 20);
            this.Label.TabIndex = 97;
            this.Label.Text = "标签颜色";
            this.Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // colorLable
            // 
            this.colorLable.EditValue = System.Drawing.Color.Black;
            this.colorLable.Location = new System.Drawing.Point(316, 48);
            this.colorLable.Name = "colorLable";
            this.colorLable.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorLable.Properties.ShowWebColors = false;
            this.colorLable.Size = new System.Drawing.Size(44, 21);
            this.colorLable.TabIndex = 96;
            // 
            // MapLTECellLayerBTSPropertis
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grpFactor);
            this.Controls.Add(this.grpLabel);
            this.Name = "MapLTECellLayerBTSPropertis";
            this.Size = new System.Drawing.Size(406, 314);
            this.grpLabel.ResumeLayout(false);
            this.grpLabel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorBTS.Properties)).EndInit();
            this.grpFactor.ResumeLayout(false);
            this.grpFactor.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorLable.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox grpLabel;
        private System.Windows.Forms.CheckBox cbxBTSDescription;
        private System.Windows.Forms.CheckBox cbxBTSType;
        private System.Windows.Forms.CheckBox cbxBTSLatitude;
        private System.Windows.Forms.CheckBox cbxBTSLongitude;
        private System.Windows.Forms.CheckBox cbxBTSName;
        private System.Windows.Forms.NumericUpDown numericUpDownSize;
        private System.Windows.Forms.CheckBox checkBoxDrawBTSLabel;
        private System.Windows.Forms.CheckBox checkBoxDisplay;
        private System.Windows.Forms.Label label100;
        private System.Windows.Forms.Label label0;
        private System.Windows.Forms.TrackBar TrackBarOpacity;
        private System.Windows.Forms.Label LabelOpacity;
        private System.Windows.Forms.Label labelColor;
        private DevExpress.XtraEditors.ColorEdit colorBTS;
        private DevExpress.XtraEditors.SimpleButton btnFont;
        private System.Windows.Forms.GroupBox grpFactor;
        private System.Windows.Forms.Label Label;
        private DevExpress.XtraEditors.ColorEdit colorLable;
    }
}
