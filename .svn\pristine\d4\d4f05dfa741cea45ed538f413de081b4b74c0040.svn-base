﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.UserMng;

namespace MasterCom.Util
{
    public partial class ExportSecuritySetDlg : XtraForm
    {
        public ExportSecuritySetDlg(FileSimpleType fileType, FuncExportPermit exportPermit)
        {
            InitializeComponent();
            init(fileType, exportPermit);
        }
        private void init(FileSimpleType fileType, FuncExportPermit exportPermit)
        {
            if (fileType == null)
            {
                panelFilePath.Visible = false;
                this.Height -= panelFilePath.Height;
                panelCause.Top -= panelFilePath.Height;
                panelZip.Top -= panelFilePath.Height;
            }
            else
            {
                panelFilePath.Visible = true;
                txtFileName.Text = ExportFuncResultManager.GetInstance().GetSubFuncName();
                comboBoxFileType.Properties.Items.Clear();
                comboBoxFileType.Properties.Items.AddRange(fileType.FileTypeList);
                comboBoxFileType.SelectedIndex = fileType.FilterIndex;
            }

            if (exportPermit.IsNeedExportCause)
            {
                panelCause.Visible = true;
            }
            else
            {
                this.Height -= panelCause.Height;
                panelZip.Top -= panelCause.Height;
            }

            if (exportPermit.IsExportToZip)
            {
                panelZip.Visible = true;
                txtZipPassword.Text = makePassword();
            }
            else
            {
                this.Height -= panelZip.Height;
            }
        }

        private string makePassword()
        {
            Random rnd = new Random();
            StringBuilder tmpstr = new StringBuilder();
            string pwdchars = "abcdefghijklmnopqrstuvwxyz0123456789";//ABCDEFGHIJKLMNOPQRSTUVWXYZ
            int iRandNum;
            for (int i = 0; i < 6; i++)
            {
                iRandNum = rnd.Next(pwdchars.Length);
                tmpstr.Append(pwdchars[iRandNum]);
            }
            return tmpstr.ToString();
        }

        private void btnPathSelect_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderDialog = new FolderBrowserDialog();
            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtFolderPath.Text = folderDialog.SelectedPath;
            }
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (panelFilePath.Visible)
            {
                if (string.IsNullOrEmpty(txtFileName.Text))
                {
                    XtraMessageBox.Show("文件名不能为空！");
                    txtFileName.Focus();
                    return;
                }
                if (txtFileName.Text.Contains("/") || txtFileName.Text.Contains("\\"))
                {
                    XtraMessageBox.Show("文件名不能包含‘/’或‘\\’符号！");
                    txtFileName.Focus();
                    return;
                }
                if (string.IsNullOrEmpty(txtFolderPath.Text))
                {
                    XtraMessageBox.Show("导出路径不能为空！");
                    txtFolderPath.Focus();
                    return;
                }
            }

            if (panelZip.Visible && string.IsNullOrEmpty(txtZipPassword.Text))
            {
                XtraMessageBox.Show("压缩包秘钥不能为空！");
                txtZipPassword.Focus();
                return;
            }
            if (panelCause.Visible && string.IsNullOrEmpty(txtExportCause.Text))
            {
                XtraMessageBox.Show("导出原因不能为空！");
                txtExportCause.Focus();
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void ExportSecuritySetDlg_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult == DialogResult.Retry)
            {
                e.Cancel = true;
            }
        }
        public ExportSecurityCondition GetCondition()
        {
            ExportSecurityCondition cond = new ExportSecurityCondition();
            cond.FileTitleName = txtFileName.Text;
            cond.FileType = comboBoxFileType.Text;
            cond.FolderPath = txtFolderPath.Text;
            cond.ZipPassword = txtZipPassword.Text;
            cond.ZipType = comboBoxZipType.Text;
            cond.ExportCause = txtExportCause.Text;
            return cond;
        }
    }

    public class ExportSecurityCondition
    {
        /// <summary>
        /// 文件标题（不包含文件夹路径和文件类型）
        /// </summary>
        public string FileTitleName { get; set; }

        /// <summary>
        /// 文件名（文件标题 + 文件类型）
        /// </summary>
        public string FileName
        {
            get
            {
                return FileTitleName + FileType;
            }
        }
        public string FileType { get; set; }
        public string FolderPath { get; set; }
        public string ZipPassword { get; set; }
        public string ZipType { get; set; }
        public string ZipSavePath
        {
            get
            {
                return FolderPath + "\\" + FileTitleName + ZipType;
            }
        }
        public string ExportCause { get; set; }
    }
}
