﻿namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    partial class ReplayEventByImsiPeriodForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.Label label5;
            this.numberReplayNext = new System.Windows.Forms.NumericUpDown();
            this.numberReplayPrev = new System.Windows.Forms.NumericUpDown();
            this.cbxGisInterval = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numberInterval = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            label4 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numberReplayNext)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numberReplayPrev)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numberInterval)).BeginInit();
            this.SuspendLayout();
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(230, 31);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(29, 12);
            label4.TabIndex = 16;
            label4.Text = "分钟";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(108, 31);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(29, 12);
            label3.TabIndex = 15;
            label3.Text = "分钟";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(144, 31);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(17, 12);
            label2.TabIndex = 13;
            label2.Text = "后";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(23, 31);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(17, 12);
            label1.TabIndex = 12;
            label1.Text = "前";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(185, 33);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(29, 12);
            label7.TabIndex = 24;
            label7.Text = "分钟";
            // 
            // numberReplayNext
            // 
            this.numberReplayNext.Location = new System.Drawing.Point(168, 28);
            this.numberReplayNext.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.numberReplayNext.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numberReplayNext.Name = "numberReplayNext";
            this.numberReplayNext.Size = new System.Drawing.Size(56, 21);
            this.numberReplayNext.TabIndex = 17;
            this.numberReplayNext.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numberReplayNext.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // numberReplayPrev
            // 
            this.numberReplayPrev.Location = new System.Drawing.Point(46, 28);
            this.numberReplayPrev.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.numberReplayPrev.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numberReplayPrev.Name = "numberReplayPrev";
            this.numberReplayPrev.Size = new System.Drawing.Size(55, 21);
            this.numberReplayPrev.TabIndex = 14;
            this.numberReplayPrev.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numberReplayPrev.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // cbxGisInterval
            // 
            this.cbxGisInterval.AutoSize = true;
            this.cbxGisInterval.Location = new System.Drawing.Point(11, 0);
            this.cbxGisInterval.Name = "cbxGisInterval";
            this.cbxGisInterval.Size = new System.Drawing.Size(90, 16);
            this.cbxGisInterval.TabIndex = 20;
            this.cbxGisInterval.Text = "GIS连线间隔";
            this.cbxGisInterval.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numberReplayNext);
            this.groupBox1.Controls.Add(label1);
            this.groupBox1.Controls.Add(label2);
            this.groupBox1.Controls.Add(this.numberReplayPrev);
            this.groupBox1.Controls.Add(label3);
            this.groupBox1.Controls.Add(label4);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(279, 70);
            this.groupBox1.TabIndex = 21;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "回放时间段";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(label5);
            this.groupBox2.Controls.Add(this.numberInterval);
            this.groupBox2.Controls.Add(label7);
            this.groupBox2.Controls.Add(this.cbxGisInterval);
            this.groupBox2.Location = new System.Drawing.Point(12, 88);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(279, 70);
            this.groupBox2.TabIndex = 22;
            this.groupBox2.TabStop = false;
            // 
            // numberInterval
            // 
            this.numberInterval.Location = new System.Drawing.Point(124, 28);
            this.numberInterval.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.numberInterval.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numberInterval.Name = "numberInterval";
            this.numberInterval.Size = new System.Drawing.Size(55, 21);
            this.numberInterval.TabIndex = 23;
            this.numberInterval.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numberInterval.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(53, 33);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(65, 12);
            label5.TabIndex = 25;
            label5.Text = "间隔时长≤";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(216, 171);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 23;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(135, 171);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 24;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // ReplayEventByImsiPeriodForm
            // 
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(303, 206);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "ReplayEventByImsiPeriodForm";
            this.Text = "时间范围";
            ((System.ComponentModel.ISupportInitialize)(this.numberReplayNext)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numberReplayPrev)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numberInterval)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numberReplayNext;
        private System.Windows.Forms.NumericUpDown numberReplayPrev;
        private System.Windows.Forms.CheckBox cbxGisInterval;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.NumericUpDown numberInterval;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}