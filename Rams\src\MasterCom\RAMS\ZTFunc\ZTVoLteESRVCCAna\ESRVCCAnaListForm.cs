﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTVoLteESRVCCAna;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ESRVCCAnaListForm : MinCloseForm
    {
        public string themeName { get; set; } = "";
        public ESRVCCAnaListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<ESRVCCAnaItem> eSRVCCItems)
        {
            gridControl.DataSource = eSRVCCItems;
            gridControl.RefreshDataSource();

            MainModel.ClearDTData();

            foreach (ESRVCCAnaItem eSRVCCAnaItem in eSRVCCItems)
            {
                foreach (TestPoint tp in eSRVCCAnaItem.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (Event evt in eSRVCCAnaItem.Events)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                foreach (MasterCom.RAMS.Model.Message msg in eSRVCCAnaItem.Messages)
                {
                    MainModel.DTDataManager.Add(msg);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            RePlay();
        }

        private void bandedGridView_DoubleClick(object sender, EventArgs e)
        {
            RePlay();
        }

        private void RePlay()
        {
            int[] rows = bandedGridView.GetSelectedRows();
            if (rows.Length == 0)
            {
                MessageBox.Show("请选择要回放的eSRVCC！");
                return;
            }
            ESRVCCAnaItem eSRVCCAnaItem = bandedGridView.GetRow(rows[0]) as ESRVCCAnaItem;
            if (eSRVCCAnaItem != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in eSRVCCAnaItem.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }

                foreach (Event evt in eSRVCCAnaItem.Events)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                foreach (MasterCom.RAMS.Model.Message msg in eSRVCCAnaItem.Messages)
                {
                    MainModel.DTDataManager.Add(msg);
                }
                MainModel.FireDTDataChanged(this);
            } 
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(bandedGridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }
    }
}
