﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedCellDlg_NR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numMinRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.numMinSinr = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxSinr = new DevExpress.XtraEditors.SpinEdit();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numProblemCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numProblemRate = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.radioThroughput = new System.Windows.Forms.RadioButton();
            this.radioApp = new System.Windows.Forms.RadioButton();
            this.grpThroughput = new System.Windows.Forms.GroupBox();
            this.cmbThroughputType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.chkThroughputDL = new System.Windows.Forms.CheckBox();
            this.numThroughputDLMin = new DevExpress.XtraEditors.SpinEdit();
            this.numThroughputDLMax = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.chkThroughputUL = new System.Windows.Forms.CheckBox();
            this.numThroughputULMin = new DevExpress.XtraEditors.SpinEdit();
            this.numThroughputULMax = new DevExpress.XtraEditors.SpinEdit();
            this.label28 = new System.Windows.Forms.Label();
            this.grpApp = new System.Windows.Forms.GroupBox();
            this.label29 = new System.Windows.Forms.Label();
            this.numFTPULMin = new DevExpress.XtraEditors.SpinEdit();
            this.label36 = new System.Windows.Forms.Label();
            this.numFTPULMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkFTPUL = new System.Windows.Forms.CheckBox();
            this.label32 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.numHTTPMin = new DevExpress.XtraEditors.SpinEdit();
            this.numEmailMin = new DevExpress.XtraEditors.SpinEdit();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numFTPDLMin = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.numFTPDLMax = new DevExpress.XtraEditors.SpinEdit();
            this.numHTTPMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkHttp = new System.Windows.Forms.CheckBox();
            this.numEmailMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkEmail = new System.Windows.Forms.CheckBox();
            this.chkFTPDL = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSinr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemRate.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.grpThroughput.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbThroughputType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDLMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDLMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputULMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputULMax.Properties)).BeginInit();
            this.grpApp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // numMinRsrp
            // 
            this.numMinRsrp.EditValue = new decimal(new int[] {
            125,
            0,
            0,
            -2147483648});
            this.numMinRsrp.Location = new System.Drawing.Point(30, 23);
            this.numMinRsrp.Name = "numMinRsrp";
            this.numMinRsrp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMinRsrp.Properties.Appearance.Options.UseFont = true;
            this.numMinRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRsrp.Properties.IsFloatValue = false;
            this.numMinRsrp.Properties.Mask.EditMask = "N00";
            this.numMinRsrp.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numMinRsrp.Properties.MinValue = new decimal(new int[] {
            130,
            0,
            0,
            -2147483648});
            this.numMinRsrp.Size = new System.Drawing.Size(73, 20);
            this.numMinRsrp.TabIndex = 0;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(437, 430);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(109, 27);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(78, 12);
            this.labelControl1.TabIndex = 20;
            this.labelControl1.Text = "≤ SS-RSRP ≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(271, 27);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 24;
            this.labelControl3.Text = "dBm";
            // 
            // numMaxRsrp
            // 
            this.numMaxRsrp.EditValue = new decimal(new int[] {
            25,
            0,
            0,
            -2147483648});
            this.numMaxRsrp.Location = new System.Drawing.Point(192, 23);
            this.numMaxRsrp.Name = "numMaxRsrp";
            this.numMaxRsrp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxRsrp.Properties.Appearance.Options.UseFont = true;
            this.numMaxRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRsrp.Properties.IsFloatValue = false;
            this.numMaxRsrp.Properties.Mask.EditMask = "N00";
            this.numMaxRsrp.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numMaxRsrp.Properties.MinValue = new decimal(new int[] {
            130,
            0,
            0,
            -2147483648});
            this.numMaxRsrp.Size = new System.Drawing.Size(73, 20);
            this.numMaxRsrp.TabIndex = 1;
            // 
            // numMinSinr
            // 
            this.numMinSinr.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMinSinr.Location = new System.Drawing.Point(332, 23);
            this.numMinSinr.Name = "numMinSinr";
            this.numMinSinr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMinSinr.Properties.Appearance.Options.UseFont = true;
            this.numMinSinr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinSinr.Properties.IsFloatValue = false;
            this.numMinSinr.Properties.Mask.EditMask = "N00";
            this.numMinSinr.Properties.MaxValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numMinSinr.Properties.MinValue = new decimal(new int[] {
            60,
            0,
            0,
            -2147483648});
            this.numMinSinr.Size = new System.Drawing.Size(73, 20);
            this.numMinSinr.TabIndex = 2;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(411, 27);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(78, 12);
            this.labelControl5.TabIndex = 28;
            this.labelControl5.Text = "≤ SS-SINR ≤";
            // 
            // numMaxSinr
            // 
            this.numMaxSinr.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSinr.Location = new System.Drawing.Point(493, 23);
            this.numMaxSinr.Name = "numMaxSinr";
            this.numMaxSinr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxSinr.Properties.Appearance.Options.UseFont = true;
            this.numMaxSinr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSinr.Properties.IsFloatValue = false;
            this.numMaxSinr.Properties.Mask.EditMask = "N00";
            this.numMaxSinr.Properties.MaxValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numMaxSinr.Properties.MinValue = new decimal(new int[] {
            60,
            0,
            0,
            -2147483648});
            this.numMaxSinr.Size = new System.Drawing.Size(73, 20);
            this.numMaxSinr.TabIndex = 3;
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnCancel.Location = new System.Drawing.Point(531, 430);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(572, 27);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 34;
            this.labelControl2.Text = "dB";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(77, 30);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(72, 12);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "问题点个数≥";
            // 
            // numProblemCount
            // 
            this.numProblemCount.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numProblemCount.Location = new System.Drawing.Point(158, 26);
            this.numProblemCount.Name = "numProblemCount";
            this.numProblemCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numProblemCount.Properties.Appearance.Options.UseFont = true;
            this.numProblemCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numProblemCount.Properties.IsFloatValue = false;
            this.numProblemCount.Properties.Mask.EditMask = "N00";
            this.numProblemCount.Properties.MaxValue = new decimal(new int[] {
            500000,
            0,
            0,
            0});
            this.numProblemCount.Size = new System.Drawing.Size(73, 20);
            this.numProblemCount.TabIndex = 0;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(357, 30);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(72, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "问题点占比≥";
            // 
            // numProblemRate
            // 
            this.numProblemRate.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numProblemRate.Location = new System.Drawing.Point(437, 26);
            this.numProblemRate.Name = "numProblemRate";
            this.numProblemRate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numProblemRate.Properties.Appearance.Options.UseFont = true;
            this.numProblemRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numProblemRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numProblemRate.Size = new System.Drawing.Size(73, 20);
            this.numProblemRate.TabIndex = 1;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.numMinRsrp);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.numMaxRsrp);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.numMinSinr);
            this.groupBox1.Controls.Add(this.labelControl5);
            this.groupBox1.Controls.Add(this.numMaxSinr);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(595, 341);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "采样点条件(且关系)";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.radioThroughput);
            this.groupBox3.Controls.Add(this.radioApp);
            this.groupBox3.Controls.Add(this.grpThroughput);
            this.groupBox3.Controls.Add(this.grpApp);
            this.groupBox3.Location = new System.Drawing.Point(18, 51);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(571, 284);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "NR速率(Mbps)";
            // 
            // radioThroughput
            // 
            this.radioThroughput.AutoSize = true;
            this.radioThroughput.Location = new System.Drawing.Point(6, 194);
            this.radioThroughput.Name = "radioThroughput";
            this.radioThroughput.Size = new System.Drawing.Size(83, 16);
            this.radioThroughput.TabIndex = 0;
            this.radioThroughput.Text = "Throughput";
            this.radioThroughput.UseVisualStyleBackColor = true;
            // 
            // radioApp
            // 
            this.radioApp.AutoSize = true;
            this.radioApp.Checked = true;
            this.radioApp.Location = new System.Drawing.Point(26, 39);
            this.radioApp.Name = "radioApp";
            this.radioApp.Size = new System.Drawing.Size(41, 16);
            this.radioApp.TabIndex = 0;
            this.radioApp.TabStop = true;
            this.radioApp.Text = "App";
            this.radioApp.UseVisualStyleBackColor = true;
            this.radioApp.CheckedChanged += new System.EventHandler(this.radioApp_CheckedChanged);
            // 
            // grpThroughput
            // 
            this.grpThroughput.Controls.Add(this.cmbThroughputType);
            this.grpThroughput.Controls.Add(this.label5);
            this.grpThroughput.Controls.Add(this.label4);
            this.grpThroughput.Controls.Add(this.chkThroughputDL);
            this.grpThroughput.Controls.Add(this.numThroughputDLMin);
            this.grpThroughput.Controls.Add(this.numThroughputDLMax);
            this.grpThroughput.Controls.Add(this.label3);
            this.grpThroughput.Controls.Add(this.chkThroughputUL);
            this.grpThroughput.Controls.Add(this.numThroughputULMin);
            this.grpThroughput.Controls.Add(this.numThroughputULMax);
            this.grpThroughput.Controls.Add(this.label28);
            this.grpThroughput.Enabled = false;
            this.grpThroughput.Location = new System.Drawing.Point(95, 183);
            this.grpThroughput.Name = "grpThroughput";
            this.grpThroughput.Size = new System.Drawing.Size(470, 95);
            this.grpThroughput.TabIndex = 0;
            this.grpThroughput.TabStop = false;
            this.grpThroughput.Text = "Throughput";
            // 
            // cmbThroughputType
            // 
            this.cmbThroughputType.EditValue = "MAC";
            this.cmbThroughputType.Location = new System.Drawing.Point(6, 2);
            this.cmbThroughputType.Name = "cmbThroughputType";
            this.cmbThroughputType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbThroughputType.Properties.Items.AddRange(new object[] {
            "MAC",
            "PDCP"});
            this.cmbThroughputType.Size = new System.Drawing.Size(100, 21);
            this.cmbThroughputType.TabIndex = 85;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(390, 67);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(29, 12);
            this.label5.TabIndex = 84;
            this.label5.Text = "Mbps";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(390, 35);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 83;
            this.label4.Text = "Mbps";
            // 
            // chkThroughputDL
            // 
            this.chkThroughputDL.AutoSize = true;
            this.chkThroughputDL.Checked = true;
            this.chkThroughputDL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkThroughputDL.Location = new System.Drawing.Point(57, 34);
            this.chkThroughputDL.Name = "chkThroughputDL";
            this.chkThroughputDL.Size = new System.Drawing.Size(15, 14);
            this.chkThroughputDL.TabIndex = 56;
            this.chkThroughputDL.UseVisualStyleBackColor = true;
            // 
            // numThroughputDLMin
            // 
            this.numThroughputDLMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numThroughputDLMin.Location = new System.Drawing.Point(78, 31);
            this.numThroughputDLMin.Name = "numThroughputDLMin";
            this.numThroughputDLMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputDLMin.Size = new System.Drawing.Size(60, 21);
            this.numThroughputDLMin.TabIndex = 50;
            // 
            // numThroughputDLMax
            // 
            this.numThroughputDLMax.EditValue = new decimal(new int[] {
            150,
            0,
            0,
            0});
            this.numThroughputDLMax.Location = new System.Drawing.Point(324, 31);
            this.numThroughputDLMax.Name = "numThroughputDLMax";
            this.numThroughputDLMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputDLMax.Size = new System.Drawing.Size(60, 21);
            this.numThroughputDLMax.TabIndex = 52;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(175, 35);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(119, 12);
            this.label3.TabIndex = 51;
            this.label3.Text = "≤ Download Rate ≤";
            // 
            // chkThroughputUL
            // 
            this.chkThroughputUL.AutoSize = true;
            this.chkThroughputUL.Checked = true;
            this.chkThroughputUL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkThroughputUL.Location = new System.Drawing.Point(57, 66);
            this.chkThroughputUL.Name = "chkThroughputUL";
            this.chkThroughputUL.Size = new System.Drawing.Size(15, 14);
            this.chkThroughputUL.TabIndex = 48;
            this.chkThroughputUL.UseVisualStyleBackColor = true;
            // 
            // numThroughputULMin
            // 
            this.numThroughputULMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numThroughputULMin.Location = new System.Drawing.Point(78, 63);
            this.numThroughputULMin.Name = "numThroughputULMin";
            this.numThroughputULMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputULMin.Size = new System.Drawing.Size(60, 21);
            this.numThroughputULMin.TabIndex = 42;
            // 
            // numThroughputULMax
            // 
            this.numThroughputULMax.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numThroughputULMax.Location = new System.Drawing.Point(324, 63);
            this.numThroughputULMax.Name = "numThroughputULMax";
            this.numThroughputULMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputULMax.Size = new System.Drawing.Size(60, 21);
            this.numThroughputULMax.TabIndex = 44;
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(181, 67);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(107, 12);
            this.label28.TabIndex = 43;
            this.label28.Text = "≤ Upload Rate ≤";
            // 
            // grpApp
            // 
            this.grpApp.Controls.Add(this.label29);
            this.grpApp.Controls.Add(this.numFTPULMin);
            this.grpApp.Controls.Add(this.label36);
            this.grpApp.Controls.Add(this.numFTPULMax);
            this.grpApp.Controls.Add(this.chkFTPUL);
            this.grpApp.Controls.Add(this.label32);
            this.grpApp.Controls.Add(this.label31);
            this.grpApp.Controls.Add(this.label30);
            this.grpApp.Controls.Add(this.numHTTPMin);
            this.grpApp.Controls.Add(this.numEmailMin);
            this.grpApp.Controls.Add(this.label14);
            this.grpApp.Controls.Add(this.label13);
            this.grpApp.Controls.Add(this.numFTPDLMin);
            this.grpApp.Controls.Add(this.label12);
            this.grpApp.Controls.Add(this.numFTPDLMax);
            this.grpApp.Controls.Add(this.numHTTPMax);
            this.grpApp.Controls.Add(this.chkHttp);
            this.grpApp.Controls.Add(this.numEmailMax);
            this.grpApp.Controls.Add(this.chkEmail);
            this.grpApp.Controls.Add(this.chkFTPDL);
            this.grpApp.Location = new System.Drawing.Point(95, 20);
            this.grpApp.Name = "grpApp";
            this.grpApp.Size = new System.Drawing.Size(470, 157);
            this.grpApp.TabIndex = 0;
            this.grpApp.TabStop = false;
            this.grpApp.Text = "App";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(390, 61);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(29, 12);
            this.label29.TabIndex = 92;
            this.label29.Text = "Mbps";
            // 
            // numFTPULMin
            // 
            this.numFTPULMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPULMin.Location = new System.Drawing.Point(78, 57);
            this.numFTPULMin.Name = "numFTPULMin";
            this.numFTPULMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPULMin.Size = new System.Drawing.Size(60, 21);
            this.numFTPULMin.TabIndex = 87;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(166, 61);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(137, 12);
            this.label36.TabIndex = 91;
            this.label36.Text = "≤ FTP Upload  Rate ≤";
            // 
            // numFTPULMax
            // 
            this.numFTPULMax.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numFTPULMax.Location = new System.Drawing.Point(324, 57);
            this.numFTPULMax.Name = "numFTPULMax";
            this.numFTPULMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPULMax.Size = new System.Drawing.Size(60, 21);
            this.numFTPULMax.TabIndex = 88;
            // 
            // chkFTPUL
            // 
            this.chkFTPUL.AutoSize = true;
            this.chkFTPUL.Checked = true;
            this.chkFTPUL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPUL.Location = new System.Drawing.Point(57, 60);
            this.chkFTPUL.Name = "chkFTPUL";
            this.chkFTPUL.Size = new System.Drawing.Size(15, 14);
            this.chkFTPUL.TabIndex = 89;
            this.chkFTPUL.UseVisualStyleBackColor = true;
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(390, 92);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(29, 12);
            this.label32.TabIndex = 83;
            this.label32.Text = "Mbps";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(390, 123);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(29, 12);
            this.label31.TabIndex = 82;
            this.label31.Text = "Mbps";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(390, 28);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(29, 12);
            this.label30.TabIndex = 81;
            this.label30.Text = "Mbps";
            // 
            // numHTTPMin
            // 
            this.numHTTPMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numHTTPMin.Location = new System.Drawing.Point(78, 88);
            this.numHTTPMin.Name = "numHTTPMin";
            this.numHTTPMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMin.Size = new System.Drawing.Size(60, 21);
            this.numHTTPMin.TabIndex = 62;
            // 
            // numEmailMin
            // 
            this.numEmailMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numEmailMin.Location = new System.Drawing.Point(78, 119);
            this.numEmailMin.Name = "numEmailMin";
            this.numEmailMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMin.Size = new System.Drawing.Size(60, 21);
            this.numEmailMin.TabIndex = 58;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(160, 92);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(149, 12);
            this.label14.TabIndex = 67;
            this.label14.Text = "≤ HTTP Download Rate ≤";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(169, 123);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(131, 12);
            this.label13.TabIndex = 66;
            this.label13.Text = "≤ EMail SMTP Rate ≤";
            // 
            // numFTPDLMin
            // 
            this.numFTPDLMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPDLMin.Location = new System.Drawing.Point(78, 24);
            this.numFTPDLMin.Name = "numFTPDLMin";
            this.numFTPDLMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDLMin.Size = new System.Drawing.Size(60, 21);
            this.numFTPDLMin.TabIndex = 56;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(163, 28);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(143, 12);
            this.label12.TabIndex = 65;
            this.label12.Text = "≤ FTP DownLoad Rate ≤";
            // 
            // numFTPDLMax
            // 
            this.numFTPDLMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numFTPDLMax.Location = new System.Drawing.Point(324, 24);
            this.numFTPDLMax.Name = "numFTPDLMax";
            this.numFTPDLMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDLMax.Size = new System.Drawing.Size(60, 21);
            this.numFTPDLMax.TabIndex = 57;
            // 
            // numHTTPMax
            // 
            this.numHTTPMax.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numHTTPMax.Location = new System.Drawing.Point(324, 88);
            this.numHTTPMax.Name = "numHTTPMax";
            this.numHTTPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMax.Size = new System.Drawing.Size(60, 21);
            this.numHTTPMax.TabIndex = 63;
            // 
            // chkHttp
            // 
            this.chkHttp.AutoSize = true;
            this.chkHttp.Checked = true;
            this.chkHttp.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkHttp.Location = new System.Drawing.Point(57, 91);
            this.chkHttp.Name = "chkHttp";
            this.chkHttp.Size = new System.Drawing.Size(15, 14);
            this.chkHttp.TabIndex = 61;
            this.chkHttp.UseVisualStyleBackColor = true;
            // 
            // numEmailMax
            // 
            this.numEmailMax.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numEmailMax.Location = new System.Drawing.Point(324, 119);
            this.numEmailMax.Name = "numEmailMax";
            this.numEmailMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMax.Size = new System.Drawing.Size(60, 21);
            this.numEmailMax.TabIndex = 60;
            // 
            // chkEmail
            // 
            this.chkEmail.AutoSize = true;
            this.chkEmail.Checked = true;
            this.chkEmail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkEmail.Location = new System.Drawing.Point(57, 122);
            this.chkEmail.Name = "chkEmail";
            this.chkEmail.Size = new System.Drawing.Size(15, 14);
            this.chkEmail.TabIndex = 53;
            this.chkEmail.UseVisualStyleBackColor = true;
            // 
            // chkFTPDL
            // 
            this.chkFTPDL.AutoSize = true;
            this.chkFTPDL.Checked = true;
            this.chkFTPDL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPDL.Location = new System.Drawing.Point(57, 27);
            this.chkFTPDL.Name = "chkFTPDL";
            this.chkFTPDL.Size = new System.Drawing.Size(15, 14);
            this.chkFTPDL.TabIndex = 59;
            this.chkFTPDL.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl9);
            this.groupBox2.Controls.Add(this.labelControl7);
            this.groupBox2.Controls.Add(this.numProblemCount);
            this.groupBox2.Controls.Add(this.numProblemRate);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Location = new System.Drawing.Point(12, 359);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(595, 62);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "过滤条件(且关系)";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(512, 30);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(6, 12);
            this.labelControl9.TabIndex = 39;
            this.labelControl9.Text = "%";
            // 
            // LowSpeedCellDlg_NR
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(615, 462);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "LowSpeedCellDlg_NR";
            this.Text = "NR低速率小区条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMinRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSinr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemRate.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.grpThroughput.ResumeLayout(false);
            this.grpThroughput.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbThroughputType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDLMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDLMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputULMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputULMax.Properties)).EndInit();
            this.grpApp.ResumeLayout(false);
            this.grpApp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit numMinRsrp;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numMaxRsrp;
        private DevExpress.XtraEditors.SpinEdit numMinSinr;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numMaxSinr;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numProblemCount;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numProblemRate;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.RadioButton radioThroughput;
        private System.Windows.Forms.RadioButton radioApp;
        private System.Windows.Forms.GroupBox grpThroughput;
        private System.Windows.Forms.GroupBox grpApp;
        private System.Windows.Forms.CheckBox chkThroughputUL;
        private DevExpress.XtraEditors.SpinEdit numThroughputULMin;
        private DevExpress.XtraEditors.SpinEdit numThroughputULMax;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.CheckBox chkThroughputDL;
        private DevExpress.XtraEditors.SpinEdit numThroughputDLMin;
        private DevExpress.XtraEditors.SpinEdit numThroughputDLMax;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label29;
        private DevExpress.XtraEditors.SpinEdit numFTPULMin;
        private System.Windows.Forms.Label label36;
        private DevExpress.XtraEditors.SpinEdit numFTPULMax;
        private System.Windows.Forms.CheckBox chkFTPUL;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label30;
        private DevExpress.XtraEditors.SpinEdit numHTTPMin;
        private DevExpress.XtraEditors.SpinEdit numEmailMin;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private DevExpress.XtraEditors.SpinEdit numFTPDLMin;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.SpinEdit numFTPDLMax;
        private DevExpress.XtraEditors.SpinEdit numHTTPMax;
        private System.Windows.Forms.CheckBox chkHttp;
        private DevExpress.XtraEditors.SpinEdit numEmailMax;
        private System.Windows.Forms.CheckBox chkEmail;
        private System.Windows.Forms.CheckBox chkFTPDL;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.ComboBoxEdit cmbThroughputType;
    }
}