﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyScanFarCellQueryByRegion_LTE : ZTDiyScanFarCellQueryByRegion_TD
    {
        private static ZTDiyScanFarCellQueryByRegion_LTE intance = null;
        public new static ZTDiyScanFarCellQueryByRegion_LTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDiyScanFarCellQueryByRegion_LTE();
                    }
                }
            }
            return intance;
        }

        protected ZTDiyScanFarCellQueryByRegion_LTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
        }

        public override string Name
        {
            get { return "过远信号分析_LTE扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23009, this.Name);
        }

        protected override int validSignal(TestPoint testPoint, int index, ref object cellObj, ref string cellName, ref double distance)
        {
            float? rxLev = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", index];
            short? uarfcn = (short?)(int?)testPoint["LTESCAN_TopN_EARFCN", index];
            int? cpi = (int?)(short?)testPoint["LTESCAN_TopN_PCI", index];
            if (rxLev == null || uarfcn == null || cpi == null || rxLev < setRxlev)
            {
                return -1;
            }
            LTECell cell = testPoint.GetCell_LTEScan(index);
            if (cell == null)
            {
                return 0;
            }
            cellObj = cell;
            cellName = cell.Name;
            distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude, testPoint.Longitude, testPoint.Latitude);
            return 1;
        }

        protected override void getResultsAfterQuery()
        {
            for (int i = 0; i < farCellCoverList.Count; i++)
            {
                farCellCoverList[i].GetResult();
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }
        #endregion
    }
}
