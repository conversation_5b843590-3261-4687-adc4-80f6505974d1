﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyScanFarCellQueryByRegion_NR : ZTDiyScanFarCellQueryByRegion_LTE
    {
        private static ZTDiyScanFarCellQueryByRegion_NR intance = null;
        public new static ZTDiyScanFarCellQueryByRegion_NR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDiyScanFarCellQueryByRegion_NR();
                    }
                }
            }
            return intance;
        }

        protected ZTDiyScanFarCellQueryByRegion_NR()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_Scan);
        }

        public override string Name
        {
            get { return "过远信号分析_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23009, this.Name);
        }

        protected override int validSignal(TestPoint testPoint, int index, ref object cellObj, ref string cellName, ref double distance)
        {
            float? rsrp = (float?)testPoint["NRSCAN_SSB_RSRP", index];
            int? arfcn = (int?)testPoint["NRSCAN_EARFCN", index];
            int? pci = (int?)testPoint["NRSCAN_PCI", index];
            if (rsrp == null || arfcn == null || pci == null || rsrp < setRxlev)
            {
                return -1;
            }

            NRCell cell = testPoint.GetCell_NRScan(index);
            if (cell == null)
            {
                return 0;
            }

            cellObj = cell;
            cellName = cell.Name;
            distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude, testPoint.Longitude, testPoint.Latitude);
            return 1;
        }

        protected override void getResultsAfterQuery()
        {
            for (int i = 0; i < farCellCoverList.Count; i++)
            {
                farCellCoverList[i].GetResult();
            }
            MainModel.FireSetDefaultMapSerialTheme("NR_SCAN", "SSB_RSRP");
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.NR扫频专题; }
        }
        #endregion
    }
}
