<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAa
        BgAAAk1TRnQBSQFMAwEBAAG4AQABuAEAARABAAEQAQAE/wEhAQAI/wFCAU0BNgcAATYDAAEoAwABQAMA
        ARADAAEBAQABIAYAARD/ACMAAaMBtAHHAf8BXQGFAa4B/wEzAUEBkgH/ARIBHQFgAf8BDAEVAVQB/wEm
        AS4BXwH/AVABWgGQAf8BmwGpAbYB/9wAAYsBnQHCAf8BJwE+AaUB/wEAAR8BpgH/AQABGwG1Af8BAAEa
        AcMB/wEAARkBwgH/AQABEwGqAf8BAAEIAYQB/wEMARQBUAH/AWEBgAGYAf/UAAGQAaIByQH/ARgBNQGv
        Af8BAAEiAb0B/wEAARwB0QH/AQABHAHPAf8BAAEcAc4B/wEAARsBywH/AQABGwHLAf8BAAEbAcsB/wEA
        AQ8BoQH/AQABAQFMAf8BZAGCAZoB/8wAAbIBwQHYAf8BMgFNAbsB/wEAAScByAH/AQABIAHgAf8BAAEg
        AeIB/wEAASAB4AH/AQABHgHcAf8BAAEeAdYB/wEAARwBzwH/AQABGwHLAf8BAAEbAcsB/wEAAQ8BoQH/
        AQ4BFQFRAf8BpwGxAcAB/8gAAYoBnQHQAf8BDwE1AcUB/wEAASQB7wH/AQABJQH0Af8B/AH+AfwB/wH8
        Af4B/AH/ATABWwHzAf8BJAFQAewB/wH8Af4B/AH/AfwB/gH8Af8BAAEbAcsB/wEAARsBywH/AQABCAGE
        Af8BWgFhAZgB/8gAAU8BZwHJAf8BAQExAeAB/wEAASwC/wEAATEC/wFfAZgB/gH/AfwB/gH8Af8B/AH+
        AfwB/wH8Af4B/AH/AfwB/gH8Af8BSAGCAesB/wEAAR0B0wH/AQABGwHLAf8BAAETAaoB/wEtATMBZAH/
        yAABNgFSAcUB/wEzAVwB+QH/ATQBYQL/ARgBTAL/AREBRgL/AbIBxgH9Af8B/AH+AfwB/wH8Af4B/AH/
        AZ0BtgH9Af8BAAEjAe0B/wEAAR8B3QH/AQABHAHPAf8BAAEZAcIB/wEPARcBVgH/yAABOAFUAcgB/wE8
        AWQB+QH/AUABgAL/AToBZAL/ASoBWQL/AbEBxQH9Af8B/AH+AfwB/wH8Af4B/AH/AaUBuwH9Af8BAAEm
        AfoB/wEAASIB5wH/AQABHgHWAf8BAAEaAcMB/wEUAR8BYQH/yAABVAGFAdIB/wE7AWAB7AH/AVABjQL/
        AVgBkgL/AZUBrwH+Af8B/AH+AfwB/wH8Af4B/AH/AfwB/gH8Af8B/AH+AfwB/wFaAZQB/gH/AQABJAHv
        Af8BAAEeAdwB/wEAARsBtgH/AToBRwGYAf/IAAGUAagB3QH/ATABVAHcAf8BYAGYAv8BhgGjAv8B/AH+
        AfwB/wH8Af4B/AH/AYMBoQH+Af8BYwGbAf4B/wH8Af4B/AH/AfwB/gH8Af8BAAElAfQB/wEAASAB4AH/
        AQEBIAGnAf8BhQGRAbsB/8gAAcQB0AHoAf8BRgFkAdUB/wFDAWYB7QH/AYcBpQL/AZcBsAL/AWYBnQL/
        AUEBgQL/ASoBWQL/AREBRgL/AQABMgL/AQABJgH2Af8BAAElAccB/wEsAUEBqAH/Ab8ByAHdAf/MAAGm
        AbcB4wH/AToBWwHXAf8BRQFoAewB/wFlAZwC/wGHAaQC/wFkAZsC/wFMAYkC/wEVAUkC/wEAATEC/wEA
        AS0B1QH/ARwBOAGxAf8BngGrAdEB/9QAAacBuAHkAf8BSQFmAdUB/wE4AVoB3gH/AToBYAHsAf8BQAFo
        AfoB/wE7AWMB+AH/ARgBRQHmAf8BFQE7AcwB/wE4AVEBvgH/AaEBrwHWAf/cAAHJAdQB6wH/AZgBqwHf
        Af8BVwGHAdQB/wE4AVUByQH/ATYBUwHGAf8BVAGCAcwB/wGVAaUB1wH/AccB0QHnAf//ANEAAUIBTQE+
        BwABPgMAASgDAAFAAwABEAMAAQEBAAEBBQABgBcAA/8BAAL/BgAB8AEPBgAB4AEHBgABwAEDBgABgAEB
        BgABgAEBBgABgAEBBgABgAEBBgABgAEBBgABgAEBBgABgAEBBgABgAEBBgABwAEDBgAB4AEHBgAB8AEP
        BgAC/wYACw==
</value>
  </data>
  <metadata name="toolTipController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>132, 17</value>
  </metadata>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>292, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>33</value>
  </metadata>
</root>