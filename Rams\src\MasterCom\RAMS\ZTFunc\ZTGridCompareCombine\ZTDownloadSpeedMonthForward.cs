﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTDownloadSpeedMonthForward : ZTGridCompareCountMutCarriers
    {
        public ZTDownloadSpeedMonthForward(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "速率对比(按栅格25M门限)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22076, "查询");
        }

        private int isTime = 0;
        private List<int> isHostyTimeList = null;
        private Dictionary<int, GridMatrix<ColorUnit>> iSTimeGridColorUnitMatrixDic = null;
        
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                int periodCount = Condition.Periods.Count;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                iSTimeGridColorUnitMatrixDic = new Dictionary<int, GridMatrix<ColorUnit>>();
                isHostyTimeList = new List<int>();
                isMonthForward = true;
                for (int i = periodCount - 1; i >= 0;i-- )
                {
                    isTime = condition.Periods[i].IBeginTime;
                    if (periodCount > 1 && i != periodCount - 1)
                    {
                        if (!iSTimeGridColorUnitMatrixDic.ContainsKey(isTime))
                        {
                            iSTimeGridColorUnitMatrixDic.Add(isTime, new GridMatrix<ColorUnit>());
                        }
                        queryPeriodInfo(condition.Periods[i], clientProxy, statImgIDSet, "NO");
                        isHostyTimeList.Add(isTime);
                    }
                    else
                    {
                        queryPeriodInfo(condition.Periods[i], clientProxy, statImgIDSet, "YES");
                    }
                }
                isHostyTimeList.Sort();
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;

                WaitBox.Text = strCityName + " 数据获取完毕，进行对比处理...";
                doCompare();
                WaitBox.Text = strCityName + " 对比完毕，进行栅格汇聚处理...";
                doCombine();
                gridFileNameListDic.Clear();

                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }
        /// <summary>
        /// 接收栅格信息
        /// </summary>
        /// <param name="clientProxy"></param>
        /// <param name="reservedParams"></param>
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            string strCurMonth = reservedParams[0] as string;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = strCityName + " 正在从服务器接收" + strCarrName + "数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(strCurMonth, package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(string strCurMonth, Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            ColorUnit cu = new ColorUnit();
            cu.LTLng = lng;
            cu.LTLat = lat;
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
            if (strCurMonth == "YES")
            {
                cu = MainModel.CurGridColorUnitMatrix[rAt, cAt];
                if (cu == null)
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    MainModel.CurGridColorUnitMatrix[rAt, cAt] = cu;
                }
            }
            else
            {
                cu = iSTimeGridColorUnitMatrixDic[isTime][rAt, cAt];
                if (cu == null)
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    iSTimeGridColorUnitMatrixDic[isTime][rAt, cAt] = cu;
                }
            }
            cu.Status = 1;
            cu.DataHub.AddStatData(singleStatData, false);
        }

        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic
                = new Dictionary<GridTypeName, GridMutCarriersCountInfo>();
            List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoListTmp
               = new List<GridDownLoadTimeSpeedInfo>();
            int iBeginTime = 1430409599 + 1;
            if (condition.Periods.Count > 0)
            {
                iBeginTime = condition.Periods[condition.Periods.Count - 1].IBeginTime;
            }
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType == "" || gridName.strGridName == "")
                {
                    continue;
                }
                if (!gridMutCarriersCountInfoDic.ContainsKey(gridName))
                {
                    gridMutCarriersCountInfoDic[gridName] = new GridMutCarriersCountInfo();
                    gridMutCarriersCountInfoDic[gridName].StrCity = strCityName;
                    gridMutCarriersCountInfoDic[gridName].StrGridType = gridName.strGridType;
                    gridMutCarriersCountInfoDic[gridName].StrGridName = gridName.strGridName;
                    for (int i = isHostyTimeList.Count - 1; i >= 0; i--)
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList.Add(0);
                        gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList.Add(0);
                    }
                }
                gridMutCarriersCountInfoDic[gridName].IAllGrid++;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
                StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
                if (dataStatTDD != null)
                {
                    gridMutCarriersCountInfoDic[gridName].IHostGrid++;
                }
                if (dataStatFDD != null)
                {
                    gridMutCarriersCountInfoDic[gridName].IGuestGrid++;
                }
                if (dataStatTDD == null && dataStatFDD == null)
                {//均无信息
                    continue;
                }
                
                double dHostDownTime = cu.DataHub.CalcValueByFormula(strTDDDownTime);
                double dHostDownSize = cu.DataHub.CalcValueByFormula(strTDDDownSize);
                double dHostSampleNum = cu.DataHub.CalcValueByFormula(strTDDSampleNum);
                double d主队RSRP采样点数 = cu.DataHub.CalcValueByFormula(str主队RSRP采样点数);
                double d主队脱网标记 = cu.DataHub.CalcValueByFormula(str主队脱网标记);
                double d主队占它网时长 = cu.DataHub.CalcValueByFormula(str主队占它网时长);
               
                string strTDDMonthText = "当前";
                double dGuestDownTime = cu.DataHub.CalcValueByFormula(strFDDDownTime);
                double dGuestDownSize = cu.DataHub.CalcValueByFormula(strFDDDownSize);
                double dGuestSampleNum = cu.DataHub.CalcValueByFormula(strFDDSampleNum);
                double d客队RSRP采样点数 = cu.DataHub.CalcValueByFormula(str客队RSRP采样点数);
                double d客队脱网标记 = cu.DataHub.CalcValueByFormula(str客队脱网标记);
                double d客队占它网时长 = cu.DataHub.CalcValueByFormula(str客队占它网时长);
                string strFDDMonthText = "当前";
                if (iBeginTime <= 1430409599)
                {
                    d主队RSRP采样点数 = cu.DataHub.CalcValueByFormula(str主队RSRP采样点数_旧);
                    d主队占它网时长 = cu.DataHub.CalcValueByFormula(str主队占它网时长_旧);
                    d客队RSRP采样点数 = cu.DataHub.CalcValueByFormula(str客队RSRP采样点数_旧);
                    d客队占它网时长 = cu.DataHub.CalcValueByFormula(str客队占它网时长_旧);
                }

                if (dataStatTDD != null)
                {
                    if (dHostDownTime <= 0 && dataStatFDD == null)
                    {//TDD有GPS信息但无下载业务，且FDD无GPS信息。-----不推移
                        if (dataStatFDD == null)
                        {
                            d客队RSRP采样点数 = -1;
                        }
                    }
                    else if ((dHostDownTime <= 0 && d主队RSRP采样点数 > 0 && d主队脱网标记 == 0 && d主队占它网时长 == 0)
                        && (dGuestDownTime <= 0 && d客队RSRP采样点数 > 0 && d客队脱网标记 == 0 && d客队占它网时长 == 0))
                    {//TDD无下载且不脱网,FDD无下载且不脱网。-----两者均需推移
                         List<double> cuTDDHosty = findGridHistoryData("TDD", rAt, cAt);
                         dHostDownTime = cuTDDHosty[0];
                         dHostDownSize = cuTDDHosty[1];
                         dHostSampleNum = cuTDDHosty[2];
                         d主队RSRP采样点数 = cuTDDHosty[3];
                         d主队脱网标记 = cuTDDHosty[4];
                         d主队占它网时长 = cuTDDHosty[5];
                         if (gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList.Count > int.Parse(cuTDDHosty[6].ToString()))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[int.Parse(cuTDDHosty[6].ToString())]
                           += int.Parse(cuTDDHosty[7].ToString());
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid += int.Parse(cuTDDHosty[7].ToString());
                            strTDDMonthText = "历史";
                        }
                        List<double> cuFDDDHosty = findGridHistoryData("FDD", rAt, cAt);
                        dGuestDownTime = cuFDDDHosty[0];
                        dGuestDownSize = cuFDDDHosty[1];
                        dGuestSampleNum = cuFDDDHosty[2];
                        d客队RSRP采样点数 = cuFDDDHosty[3];
                        d客队脱网标记 = cuFDDDHosty[4];
                        d客队占它网时长 = cuFDDDHosty[5];
                        if (gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList.Count > int.Parse(cuFDDDHosty[6].ToString()))
                        {
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList[int.Parse(cuFDDDHosty[6].ToString())]
                            += int.Parse(cuFDDDHosty[7].ToString());
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGrid += int.Parse(cuFDDDHosty[7].ToString());
                            strFDDMonthText = "历史";
                        }
                    }
                    else if ((dHostDownTime <= 0 && d主队RSRP采样点数 > 0 && d主队脱网标记 == 0 && d主队占它网时长 == 0)
                        && (dataStatFDD != null && dGuestDownTime > 0))
                    {//TDD无下载且不脱网，且FDD有下载业务。-----需推移
                        List<double> cuHosty = findGridHistoryData("TDD", rAt, cAt);
                        dHostDownTime = cuHosty[0];
                        dHostDownSize = cuHosty[1];
                        dHostSampleNum = cuHosty[2];
                        d主队RSRP采样点数 = cuHosty[3];
                        d主队脱网标记 = cuHosty[4];
                        d主队占它网时长 = cuHosty[5];
                        if (gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList.Count > int.Parse(cuHosty[6].ToString()))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[int.Parse(cuHosty[6].ToString())]
                           += int.Parse(cuHosty[7].ToString());
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid += int.Parse(cuHosty[7].ToString());
                            strTDDMonthText = "历史";
                        }
                    }
                    else if (dHostDownTime > 0
                        && (dataStatFDD == null || (dGuestDownTime <= 0 && d客队RSRP采样点数 > 0 && d客队脱网标记 == 0 && d客队占它网时长 == 0)))
                    {//TDD有下载业务，FDD无GPS信息或有GPS信息但无业务且不脱网。-----需推移
                        List<double> cuHosty = findGridHistoryData("FDD", rAt, cAt);
                        dGuestDownTime = cuHosty[0];
                        dGuestDownSize = cuHosty[1];
                        dGuestSampleNum = cuHosty[2];
                        d客队RSRP采样点数 = cuHosty[3];
                        d客队脱网标记 = cuHosty[4];
                        d客队占它网时长 = cuHosty[5];
                        if (gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList.Count > int.Parse(cuHosty[6].ToString()))
                        {
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGridList[int.Parse(cuHosty[6].ToString())]
                            += int.Parse(cuHosty[7].ToString());
                            gridMutCarriersCountInfoDic[gridName].IGuestHistoryGrid += int.Parse(cuHosty[7].ToString());
                            strFDDMonthText = "历史";
                        }
                    }
                }
                else
                {
                    if (dGuestDownTime <= 0)
                    {//TDD无GPS信息，且FDD无下载业务-----不推移
                        d主队RSRP采样点数 = -1;
                    }
                    else
                    {//FDD做下载业务，TDD无GPS信息。-----需推移
                        List<double> cuHosty = findGridHistoryData("TDD", rAt, cAt);
                        dHostDownTime = cuHosty[0];
                        dHostDownSize = cuHosty[1];
                        dHostSampleNum = cuHosty[2];
                        d主队RSRP采样点数 = cuHosty[3];
                        d主队脱网标记 = cuHosty[4];
                        d主队占它网时长 = cuHosty[5];
                        if (gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList.Count > int.Parse(cuHosty[6].ToString()))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGridList[int.Parse(cuHosty[6].ToString())]
                                += int.Parse(cuHosty[7].ToString());
                            gridMutCarriersCountInfoDic[gridName].IHostHistoryGrid += int.Parse(cuHosty[7].ToString());
                            strTDDMonthText = "历史";
                        }
                    }
                }
                double dHostDownSpeed = 0;
                if (dHostDownTime > 0 && dHostDownSize >= 0)
                {
                    dHostDownSpeed = dHostDownSize / dHostDownTime;
                }
                double dGuestDownSpeed = 0;
                if (dGuestDownTime > 0 && dGuestDownSize >= 0)
                {
                    dGuestDownSpeed = dGuestDownSize / dGuestDownTime;
                }
                if (GetShowGridDataInfo)
                {
                    GridDownLoadTimeSpeedInfo gridDownLoadTimeSpeedInfo = new GridDownLoadTimeSpeedInfo();
                    gridDownLoadTimeSpeedInfo.StrCityInfo = strCityName;
                    gridDownLoadTimeSpeedInfo.StrGridType = gridName.strGridType;
                    gridDownLoadTimeSpeedInfo.StrGridName = gridName.strGridName;
                    gridDownLoadTimeSpeedInfo.StrGirdCenterInfo = cu.CenterLng + "_" + cu.CenterLat;
                    gridDownLoadTimeSpeedInfo.Itllng = int.Parse((cu.LTLng * 10000000).ToString());
                    gridDownLoadTimeSpeedInfo.Itllat = int.Parse((cu.LTLat * 10000000).ToString());
                    gridDownLoadTimeSpeedInfo.Icentlng = int.Parse((cu.CenterLng * 10000000).ToString());
                    gridDownLoadTimeSpeedInfo.Icentlat = int.Parse((cu.CenterLat * 10000000).ToString());
                    gridDownLoadTimeSpeedInfo.DTDDDownTime = dHostDownTime;
                    gridDownLoadTimeSpeedInfo.DTDDDownSize = dHostDownSize;
                    gridDownLoadTimeSpeedInfo.DTDDDownSpeed = dHostDownSpeed;
                    gridDownLoadTimeSpeedInfo.DTDDSampleNum = dHostSampleNum;
                    gridDownLoadTimeSpeedInfo.D主队RSRP采样点数 = d主队RSRP采样点数;
                    gridDownLoadTimeSpeedInfo.D主队脱网标记 = d主队脱网标记;
                    gridDownLoadTimeSpeedInfo.D主队占它网时长 = d主队占它网时长;
                    gridDownLoadTimeSpeedInfo.Str主队数据源 = "";
                    gridDownLoadTimeSpeedInfo.DFDDDownTime = dGuestDownTime;
                    gridDownLoadTimeSpeedInfo.DFDDDownSize = dGuestDownSize;
                    gridDownLoadTimeSpeedInfo.DFDDDownSpeed = dGuestDownSpeed;
                    gridDownLoadTimeSpeedInfo.DFDDSampleNum = dGuestSampleNum;
                    gridDownLoadTimeSpeedInfo.D客队RSRP采样点数 = d客队RSRP采样点数;
                    gridDownLoadTimeSpeedInfo.D客队脱网标记 = d客队脱网标记;
                    gridDownLoadTimeSpeedInfo.D客队占它网时长 = d客队占它网时长;
                    gridDownLoadTimeSpeedInfo.Str客队数据源 = "";
                    gridDownLoadTimeSpeedInfoListTmp.Add(gridDownLoadTimeSpeedInfo);
                }

                if (dHostDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I含历史主队下载栅格数++;
                    if (strTDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].I主队本月下载栅格数++;
                    }
                }
                if (dGuestDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I含历史客队下载栅格数++;
                    if (strFDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].I客队本月下载栅格数++;
                    }
                }
                if (d主队RSRP采样点数 == -1 || d客队RSRP采样点数 == -1)
                {//任意一方无信息
                    continue;
                }
                if ((dHostDownTime <= 0 && dGuestDownTime <= 0) && (((d主队脱网标记 + d主队占它网时长) == 0 && d主队RSRP采样点数 > 0)
                   && ((d客队脱网标记 + d客队占它网时长) == 0 && d客队RSRP采样点数 > 0)))
                {//均无业务且均不脱网
                    continue;
                }
                if ((dHostDownTime <= 0 && dGuestDownTime <= 0) && ((d主队RSRP采样点数 == 0 && d客队RSRP采样点数 == 0)
                    || ((d主队脱网标记 + d主队占它网时长) > 0 && (d客队脱网标记 + d客队占它网时长) > 0) 
                    || ((d主队脱网标记 + d主队占它网时长) > 0 && d客队RSRP采样点数 == 0) 
                    || ((d客队脱网标记 + d客队占它网时长) > 0 && d主队RSRP采样点数 == 0)))
                {//均无业务且均脱网
                    continue;
                }
                if ((dHostDownTime > 0 && (dGuestDownTime <= 0 && (d客队脱网标记 + d客队占它网时长) == 0 && d客队RSRP采样点数 > 0))
                    || (dGuestDownTime > 0 && (dHostDownTime <= 0 && (d主队脱网标记 + d主队占它网时长) == 0 && d主队RSRP采样点数 > 0)))
                {//一方存在下载业务，另一方既无业务也不脱网
                    continue;
                }

                gridMutCarriersCountInfoDic[gridName].ICompareGrid++;

                if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                {
                    gridMutCarriersCountInfoDic[gridName].I本月对比总栅格数++;
                }

                if (dHostDownTime <= 0 && dGuestDownTime <= 0)
                {
                    if ((d主队脱网标记 + d主队占它网时长) > 0 || d主队RSRP采样点数 == 0)
                    {
                        gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                        gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                        if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                        }
                        GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", d主队脱网标记, d主队占它网时长, d主队RSRP采样点数);
                    }
                    else
                    {
                        gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                        if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                        }
                        GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", d客队脱网标记, d客队占它网时长, d客队RSRP采样点数);
                    }
                }
                else if (dHostDownTime > 0 && dGuestDownTime <= 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                    if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                    }
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", d客队脱网标记, d客队占它网时长, d客队RSRP采样点数);
                }
                else if (dHostDownTime <= 0 && dGuestDownTime > 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                    if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                    }
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", d主队脱网标记, d主队占它网时长, d主队RSRP采样点数);
                }
                else
                {
                    if (dHostDownSpeed > dSpeed && dGuestDownSpeed > dSpeed)
                    {
                        if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].IMoreGoodGrid++;
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].IMoreWeakGrid++;
                        }
                    }
                    else if (dHostDownSpeed > dSpeed && dGuestDownSpeed <= dSpeed)
                    {
                        gridMutCarriersCountInfoDic[gridName].IHostMoreGuestLess++;
                        gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                        if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                        }
                    }
                    else if (dHostDownSpeed <= dSpeed && dGuestDownSpeed > dSpeed)
                    {
                        if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostLessGuestMore++;
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                            }
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].IHostLessGuestMoreWeak++;
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                            }
                        }
                    }
                    else if (dHostDownSpeed <= dSpeed && dGuestDownSpeed <= dSpeed)
                    {
                        if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                        {
                            gridMutCarriersCountInfoDic[gridName].ILessGoodGrid++;
                            gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                            }
                        }
                        else
                        {
                            gridMutCarriersCountInfoDic[gridName].ILessWeakGrid++;
                            gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                            if (strTDDMonthText.Equals("当前") && strFDDMonthText.Equals("当前"))
                            {
                                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                            }
                        }
                    }
                }
            }
            if (!cityMutCarriersCountInfoDic.ContainsKey(strCityName))
            {
                cityMutCarriersCountInfoDic.Add(strCityName, gridMutCarriersCountInfoDic);
                cityGridDownLoadTimeSpeedInfoListDic.Add(strCityName, gridDownLoadTimeSpeedInfoListTmp);
            }
            iSTimeGridColorUnitMatrixDic.Clear();
            isHostyTimeList.Clear();
        }

        private List<double> findGridHistoryData(string strNet, int rAt, int cAt)
        {
            List<double> cuHosty = new List<double>() { -1, -1, -1, -1, 0, 0, 0, 0 };
            for (int i = isHostyTimeList.Count - 1; i >= 0;i--)
            {
                ColorUnit cu = iSTimeGridColorUnitMatrixDic[isHostyTimeList[i]][rAt, cAt];
                if (cu == null)
                {
                    continue;
                }
                if (strNet.Equals("TDD"))
                {
                    setTddData(cuHosty, i, cu);
                }
                else if (strNet.Equals("FDD"))
                {
                    setFddData(cuHosty, i, cu);
                }
                if (cuHosty[0] > 0)
                {
                    break;
                }
            }
            return cuHosty;
        }

        private void setTddData(List<double> cuHosty, int i, ColorUnit cu)
        {
            StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
            if (dataStatTDD != null)
            {
                cuHosty[0] = cu.DataHub.CalcValueByFormula(strTDDDownTime);
                if (cuHosty[0] > 0)
                {
                    cuHosty[1] = cu.DataHub.CalcValueByFormula(strTDDDownSize);
                    cuHosty[2] = cu.DataHub.CalcValueByFormula(strTDDSampleNum);
                    cuHosty[3] = cu.DataHub.CalcValueByFormula(str主队RSRP采样点数);
                    cuHosty[4] = cu.DataHub.CalcValueByFormula(str主队脱网标记);
                    cuHosty[5] = cu.DataHub.CalcValueByFormula(str主队占它网时长);
                    if (isHostyTimeList[i] <= 1430409599)//5月份之前的用旧公式
                    {
                        cuHosty[3] = cu.DataHub.CalcValueByFormula(str主队RSRP采样点数_旧);
                        cuHosty[5] = cu.DataHub.CalcValueByFormula(str主队占它网时长_旧);
                    }
                    cuHosty[6] = isHostyTimeList.Count - 1 - i;
                    cuHosty[7] = 1;
                }
            }
        }

        private void setFddData(List<double> cuHosty, int i, ColorUnit cu)
        {
            StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
            if (dataStatFDD != null)
            {
                cuHosty[0] = cu.DataHub.CalcValueByFormula(strFDDDownTime);
                if (cuHosty[0] > 0)
                {
                    cuHosty[1] = cu.DataHub.CalcValueByFormula(strFDDDownSize);
                    cuHosty[2] = cu.DataHub.CalcValueByFormula(strFDDSampleNum);
                    cuHosty[3] = cu.DataHub.CalcValueByFormula(str客队RSRP采样点数);
                    cuHosty[4] = cu.DataHub.CalcValueByFormula(str客队脱网标记);
                    cuHosty[5] = cu.DataHub.CalcValueByFormula(str客队占它网时长);
                    if (isHostyTimeList[i] <= 1430409599)//5月份之前的用旧公式
                    {
                        cuHosty[3] = cu.DataHub.CalcValueByFormula(str客队RSRP采样点数_旧);
                        cuHosty[5] = cu.DataHub.CalcValueByFormula(str客队占它网时长_旧);
                    }
                    cuHosty[6] = isHostyTimeList.Count - 1 - i;
                    cuHosty[7] = 1;
                }
            }
        }
    }
}
