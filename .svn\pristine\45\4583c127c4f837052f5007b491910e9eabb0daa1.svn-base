﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class PseudoStationInfoItem
    {
        public PseudoStationInfoItem(string strFileName)
        {
            this.FileName = strFileName;
            this.IsInNBCellBegin = "";
            this.TestPoints = new List<TestPoint>();
            this.Events = new List<Event>();
            this.Messages = new List<Message>();
            this.ListMsgCPData = new List<Message>();
        }
        public int SN { get; set; }
        public string Time { get; set; }
        public string FileName { get; set; }
        public string PhoneNumber { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public int? LAC { get; set; }
        public int? CI { get; set; }
        public short? EARFCN { get; set; }
        public string CellNameCsfbBegin { get; set; }
        public string CellNameLast { get; set; }
        public string IsInNBCellBegin { get; set; }
        public bool IsGotBegin { get; set; }
        public bool IsUpdateSucess { get; set; }
        public bool IsEnd { get; set; }
        public Event EvtBeforeLocationUpdate { get; set; }
        public Event EvtLocationUpdateRequest { get; set; }
        public Event EvtCSFBRequest { get; set; }
        public Event EvtLocationUpdateSuccess { get; set; }

        public List<TestPoint> TestPoints { get; set; }
        public List<Event> Events { get; set; }
        public List<Message> Messages { get; set; }

        public List<Message> ListMsgCPData { get; set; }

        public TestPoint TPCSFBRequestLast { get; set; }
        public TestPoint TPCPDataNext { get; set; }
    }
}
