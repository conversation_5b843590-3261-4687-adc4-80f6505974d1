﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLowSpeedByFile_TD : DIYQueryFileInfoByRegion
    {
        protected Dictionary<String, SampleSpeedInfo> sampleSpeedInfo = new Dictionary<string, SampleSpeedInfo>();
        protected Dictionary<string, SampleSpeedInfo_LTE> sampleSpeedInfo_LTE = new Dictionary<string, SampleSpeedInfo_LTE>();
        protected LowSpeedInfoByFileForm_TD lowSpeedInfoByFileForm_TD = null;
        protected LowSpeedInfoByFileForm_LTE lowSpeedInfoByFileForm_LTE = null;
        protected int transferedTimeLimit = 50;
        protected ICell cellType = null;


        public ZTLowSpeedByFile_TD(MainModel mainModel)
            : base(mainModel)
        {
            this.cellType = new TDCell();
        }

        public override string Name
        {
            get { return "TD按文件按小区每分钟下载速率统计分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13047, this.Name);
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            MainModel.ClearDTData();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            sampleSpeedInfo.Clear();
            sampleSpeedInfo_LTE.Clear();
            base.query();

            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);


            if (sampleSpeedInfo.Count == 0 && sampleSpeedInfo_LTE.Count == 0)
            {
                MessageBox.Show("查询结果不存在数据！");
                return;
            }
            fireShowForm();
        }

        protected virtual void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != (int)ServiceType.TDSCDMA_DATA
                        && fileInfo.ServiceType != (int)ServiceType.TDSCDMA_HSDPA
                        && fileInfo.ServiceType != (int)ServiceType.TDSCDMA_HSUPA)
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected virtual void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = true;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
            doStat();
        }

        protected virtual void doStat()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        addValidTPInfo(testPoint);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void addValidTPInfo(TestPoint testPoint)
        {
            if (isValidTestPoint(testPoint) && (int?)testPoint["TD_APP_ThroughputDL"] >= 0 && testPoint["TD_APP_ThroughputDL"] != null)
            {
                TDCell tdCell = testPoint.GetMainCell_TD_TDCell();
                if (tdCell != null)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.Append(testPoint.FileID);
                    sb.Append(tdCell.LAC);
                    sb.Append(tdCell.CI);
                    sb.Append(testPoint.DateTime.ToString("yy-MM-dd HH:mm"));
                    if (sampleSpeedInfo.ContainsKey(sb.ToString()))
                    {
                        sampleSpeedInfo[sb.ToString()].setData(testPoint, tdCell, testPoint.DateTime.ToString("yy-MM-dd HH:mm"));
                    }
                    else
                    {
                        sampleSpeedInfo[sb.ToString()] = new SampleSpeedInfo(testPoint, tdCell, testPoint.DateTime.ToString("yy-MM-dd HH:mm"));
                    }
                }
            }
        }

        protected virtual void fireShowForm()
        {
            List<SampleSpeedInfo> sampleList = new List<SampleSpeedInfo>();
            foreach (SampleSpeedInfo sample in sampleSpeedInfo.Values)
            {
                if(sample.TransferedTime >= transferedTimeLimit)
                {
                    sampleList.Add(sample);
                }
            }
            if (lowSpeedInfoByFileForm_TD == null || lowSpeedInfoByFileForm_TD.IsDisposed)
            {
                lowSpeedInfoByFileForm_TD = new LowSpeedInfoByFileForm_TD(MainModel, this.cellType);
            }
            lowSpeedInfoByFileForm_TD.FillData(sampleList);
            if (!lowSpeedInfoByFileForm_TD.Visible)
            {
                lowSpeedInfoByFileForm_TD.Show(MainModel.MainForm);
            }
        }

        protected virtual bool getCondition()
        {
            SetTransferedTimeLimitForm setTransferedTimeLimitForm = new SetTransferedTimeLimitForm();
            if (setTransferedTimeLimitForm.ShowDialog() == DialogResult.OK)
            {
                setTransferedTimeLimitForm.GetTransferedTimeLimit(out transferedTimeLimit);
                this.transferedTimeLimit = this.transferedTimeLimit * 1000;
                return true;
            }
            return false;
        }

        protected virtual bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

    }

    public class SampleSpeedInfo
    {
        public LTECell LTECell { get; set; }

        public TDCell TDCell { get; set; }

        public String FileName { get; set; }

        public String CellName { get; set; }

        public int LAC { get; set; }

        public int CI { get; set; }

        public int CellID { get; protected set; }

        public String DateTime { get; set; }

        public int TransferedSize { get; set; }

        public int TransferedTime { get; set; }

        public int SampleCount { get; set; }

        public SampleSpeedInfo()
        {

        }
        public SampleSpeedInfo(TestPoint tp, ICell cell, String dateTime)
        {
            setData(tp, cell, dateTime);
        }

        public void setData(TestPoint tp, ICell cell, String dateTime)
        {
            this.FileName = tp.FileName;
            this.DateTime = dateTime;
            if(cell is LTECell)
            {
                this.LTECell = cell as LTECell;
                this.CellName = LTECell.Name;
                this.LAC = LTECell.TAC;
                this.CI = LTECell.ECI;
                this.CellID = LTECell.SCellID;
                if (tp["lte_APP_TransferedSize"] != null)
                {
                    this.TransferedSize += (int)tp["lte_APP_TransferedSize"];
                }
                if (tp["lte_APP_TransferedTime"] != null)
                {
                    this.TransferedTime += (int)tp["lte_APP_TransferedTime"];
                }
            }
            else
            {
                this.TDCell = cell as TDCell;
                this.CellName = TDCell.Name;
                this.LAC = TDCell.LAC;
                this.CI = TDCell.CI;
                if (tp["TD_APP_TransferedSize"] != null)
                {
                    this.TransferedSize += (int)tp["TD_APP_TransferedSize"];
                }
                if (tp["TD_APP_TransferedTime"] != null)
                {
                    this.TransferedTime += (int)tp["TD_APP_TransferedTime"];
                }
            }
            this.SampleCount++;
        }
    }
}
