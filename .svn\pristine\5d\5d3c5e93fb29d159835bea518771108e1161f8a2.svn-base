﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateCellFusionInfoForm : CreateChildForm
    {
        public CreateCellFusionInfoForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建小区性能配置告警列表";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20049, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建小区性能配置告警列表窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.CellFusionDataForm";
            actionParam["Text"] = "小区性能配置告警列表";
            actionParam["ImageFilePath"] = @"images\dataview.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
