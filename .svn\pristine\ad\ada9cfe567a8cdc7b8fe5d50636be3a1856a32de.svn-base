﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCellCheck;
using MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPoorRoadCell_NR : DIYAnalyseByFileBackgroundBase
    {
        public QueryPoorRoadCell_NR() :
            base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "持续道路小区关联分析（按区域）"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 22033, this.Name);
        }

        /// <summary>
        /// 小区名称，三超问题字典
        /// </summary>
        private Dictionary<string, string> cellUltraDic = null;

        private Dictionary<string, string> getUltraCells()
        {
            Dictionary<string, string> cellDic = new Dictionary<string, string>();
            if (funcCondition.UltraCellByFile)
            {
                ultraCellByFile(cellDic);
            }
            else
            {
                ultraCellByCell(cellDic);
            }
            return cellDic;
        }

        private void ultraCellByFile(Dictionary<string, string> cellDic)
        {
            DataSet dataSet = ExcelNPOIManager.ImportFromExcel(funcCondition.FileName);
            DataTable table = dataSet.Tables[0];
            foreach (DataRow row in table.Rows)
            {
                string cellName = row[0].ToString();
                if (cellName == "小区名称")
                {
                    continue;
                }

                string probType = row[1].ToString();
                string desc;
                if (cellDic.TryGetValue(cellName, out desc))
                {
                    desc += "；" + probType;
                }
                else
                {
                    desc = probType;
                }
                cellDic[cellName] = desc;
            }
        }

        private void ultraCellByCell(Dictionary<string, string> cellDic)
        {
            UltraSiteQuery_NR qry = new UltraSiteQuery_NR();
            qry.SetQueryCondition(condition);
            qry.ShowSettingDlg = false;
            qry.nrUltraSiteCondition = funcCondition.UltraSiteCondition;
            qry.Query();

            ultraCellDic = new Dictionary<string, List<UltraSiteCell>>();
            foreach (ICell cell in qry.Result.Keys)
            {
                StringBuilder sb = new StringBuilder();
                List<UltraSiteCell> ultraCells = qry.Result[cell];
                foreach (UltraSiteCell ultraCell in ultraCells)
                {
                    if (!sb.ToString().Contains(ultraCell.TypeName))
                    {
                        sb.Append(ultraCell.TypeName + "；");
                    }
                }
                string ultra = sb.ToString().TrimEnd('；');
                cellDic[cell.Name] = ultra;
                ultraCellDic[cell.Name] = qry.Result[cell];
            }
        }

        private Dictionary<string, List<UltraSiteCell>> ultraCellDic = null;

        protected override bool getCondition()
        {
            ZTCellCheck.RoadSegment.ConditionDlg dlg = new ZTCellCheck.RoadSegment.ConditionDlg();
            dlg.SetCondition(funcCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }

            funcCondition = dlg.GetCondition_NR();
            roadSegments = new List<RoadSegment_NR>();
            try
            {
                cellUltraDic = getUltraCells();
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
                return false;
            }
            return cellUltraDic != null && cellUltraDic.Count > 0;
        }

        private ZTCellCheck.RoadSegment_NR.FuncCondition_NR funcCondition = null;

        /// <summary>
        /// 符合条件的路段
        /// </summary>
        private List<RoadSegment_NR> roadSegments = null;

        protected override void doStatWithQuery()
        {
            try
            {
                roadSegments.AddRange(judgeFileSegment(funcCondition.WeakCover));    //弱覆盖
                roadSegments.AddRange(judgeFileSegment(funcCondition.MultiCover));   //重叠覆盖
                roadSegments.AddRange(judgeFileSegment(funcCondition.PoorSINR));     //质差路段
                judgeFarCover();  //超远覆盖
                judgeCoverLap();  //过覆盖
                judgeOverHo();    //频繁切换
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace);
            }
        }

        /// <summary>
        /// 过覆盖小区字典
        /// </summary>
        private Dictionary<string, CoverLapCell_NR> coverLapCellDic = null;

        private CoverLapCell_NR getCoverLapCell(NRCell nrCell)
        {
            CoverLapCell_NR cell;
            if (coverLapCellDic == null)
            {
                coverLapCellDic = new Dictionary<string, CoverLapCell_NR>();
            }

            if (!coverLapCellDic.TryGetValue(nrCell.Name, out cell))
            {
                cell = new CoverLapCell_NR(nrCell, funcCondition.CoverLap);
                coverLapCellDic.Add(nrCell.Name, cell);
            }
            return cell;
        }

        private void judgeCoverLap()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint testPoint in file.TestPoints)
                {
                    if (!isValidTestPoint(testPoint))
                        continue; //区域外数据

                    NRCell cell = testPoint.GetMainCell_NR();
                    float? rsrp = GetRSRP(testPoint);
                    if (cell == null || rsrp == null || rsrp > 25 || rsrp < -141)
                    {
                        continue;
                    }

                    double distance = testPoint.Distance2(cell.Longitude, cell.Latitude);
                    CoverLapCell_NR coverCell = getCoverLapCell(cell);
                    bool coverLap = funcCondition.CoverLap.IsCoverLap((float)rsrp, distance,
                        coverCell.CellCvrDisMax);
                    coverCell.AddTestPoint(testPoint, coverLap);
                }
            }
        }

        /// <summary>
        /// 过远覆盖小区字典
        /// </summary>
        private Dictionary<string, FarCoverCell_NR> farCoverCellDic = null;

        private FarCoverCell_NR getFarCoverCell(NRCell cell)
        {
            FarCoverCell_NR cellRet;
            if (farCoverCellDic == null)
            {
                farCoverCellDic = new Dictionary<string, FarCoverCell_NR>();
            }
            if (!farCoverCellDic.TryGetValue(cell.Name, out cellRet))
            {
                cellRet = new FarCoverCell_NR(cell);
                farCoverCellDic.Add(cell.Name, cellRet);
            }
            return cellRet;
        }

        private void judgeFarCover()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint testPoint in file.TestPoints)
                {
                    if (!isValidTestPoint(testPoint))
                        continue;  //区域外

                    NRCell cell = testPoint.GetMainCell_NR();
                    float? rsrp = GetRSRP(testPoint);
                    if (cell == null || rsrp == null || rsrp > 25 || rsrp < -141)
                    {
                        continue;
                    }
                    FarCoverCell_NR coverCell = getFarCoverCell(cell);
                    double distance = testPoint.Distance2(cell.Longitude, cell.Latitude);
                    bool far = funcCondition.FarCover.IsValidDistance(distance);
                    far &= funcCondition.FarCover.IsValid((float)rsrp);
                    coverCell.AddTestPoint(testPoint, far);
                }
            }
        }

        private List<RoadSegment_NR> judgeFileSegment(LastSegmentConditionBase_NR cond)
        {
            List<RoadSegment_NR> retList = new List<RoadSegment_NR>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                RoadSegment_NR lastSegment = null;
                foreach (TestPoint testPoint in file.TestPoints)
                {
                    lastSegment = dealTP(cond, retList, lastSegment, testPoint);
                }

                if (!retList.Contains(lastSegment)
                    && cond.IsValidSegment(lastSegment))
                {//防止最后一段没有保存
                    retList.Add(lastSegment);
                }
            }
            return retList;
        }

        private RoadSegment_NR dealTP(LastSegmentConditionBase_NR cond, List<RoadSegment_NR> retList, RoadSegment_NR lastSegment, TestPoint testPoint)
        {
            if (!isValidTestPoint(testPoint))
            {//采样点不在区域内
                if (cond.IsValidSegment(lastSegment))
                {
                    retList.Add(lastSegment);
                }
                lastSegment = null;
            }
            else
            {//在区域内
                if (cond.IsValid(testPoint))
                {//指标值符合持续条件
                    if (lastSegment == null)
                    {
                        lastSegment = new RoadSegment_NR(cond.Name); //该采样点开始新一段持续道路
                    }
                    lastSegment.AddTestPoint(testPoint);
                }
                else
                {//不符合，保存前一持续路段
                    if (cond.IsValidSegment(lastSegment))
                    {
                        retList.Add(lastSegment);
                    }
                    lastSegment = null;
                }
            }

            return lastSegment;
        }

        /// <summary>
        /// 切换过繁序列
        /// </summary>
        private List<OverHandover_NR> overHoSet = null;

        private void judgeOverHo()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> hoEvents = new List<Event>();
                foreach (Event evt in file.Events)
                {
                    if (evt.ID == 9296 || evt.ID == 9299)
                    {
                        hoEvents.Add(evt);
                    }
                }
                int step = funcCondition.OverHandover.HoCount - 1;
                dealHoEvents(file, hoEvents, step);
            }
        }

        private void dealHoEvents(DTFileDataManager file, List<Event> hoEvents, int step)
        {
            for (int i = 0; i + step < hoEvents.Count; i++)
            {//次数
                Event beginEvt = hoEvents[i];
                int endIdx = i + step;
                Event endEvt = hoEvents[endIdx];
                if (endEvt.Time - beginEvt.Time <= funcCondition.OverHandover.Second)
                {//时间符合
                    getEndEvt(hoEvents, beginEvt, ref endIdx, ref endEvt);
                    int evtNum = endIdx - i + 1;

                    List<Event> list = getEvtList(hoEvents, i, evtNum);
                    //下一循环，过掉超出条件个数个
                    i += evtNum - funcCondition.OverHandover.HoCount;
                    double dis = getDis(list);
                    if (dis <= funcCondition.OverHandover.Distance)
                    {//距离
                        saveOverHo(list, file);
                    }
                }
            }
        }

        private void getEndEvt(List<Event> hoEvents, Event beginEvt, ref int endIdx, ref Event endEvt)
        {
            for (int k = endIdx + 1; k < hoEvents.Count; k++)
            {//取最后符合时间条件的事件
                endEvt = hoEvents[k];
                if (endEvt.Time - beginEvt.Time > funcCondition.OverHandover.Second)
                {
                    endIdx = k - 1;
                    break;
                }
            }
        }

        private List<Event> getEvtList(List<Event> hoEvents, int i, int evtNum)
        {
            List<Event> list = new List<Event>();
            for (int j = 0; j < evtNum; j++)
            {
                list.Add(hoEvents[i + j]);
            }

            return list;
        }

        private double getDis(List<Event> list)
        {
            double dis = 0;
            for (int j = 0; j < list.Count - 1; j++)
            {
                Event e1 = list[j];
                Event e2 = list[j + 1];
                dis += MathFuncs.GetDistance(e1.Longitude, e1.Latitude, e2.Longitude, e2.Latitude);
            }

            return dis;
        }

        private void saveOverHo(List<Event> overHoEvent, DTFileDataManager file)
        {
            if (overHoSet == null)
            {
                overHoSet = new List<OverHandover_NR>();
            }
            OverHandover_NR ho = new OverHandover_NR(overHoEvent, file);
            overHoSet.Add(ho);
        }

        private string getCellUltraDesc(string cellName)
        {
            string ultra;
            cellUltraDic.TryGetValue(cellName, out ultra);
            return ultra;
        }

        protected override void getResultsAfterQuery()
        {
            Dictionary<string, bool> haveDataCell = new Dictionary<string, bool>();
            results = new List<CellCheckItem_NR>();
            int sn = 1;
            if (roadSegments != null)
            {
                sn = dealRoadSegments(haveDataCell, sn);
            }

            if (overHoSet != null)
            {
                sn = dealOverHoSet(sn);
            }

            sn = addCoverLapCellRes(haveDataCell, sn);

            sn = addFarCoverRes(haveDataCell, sn);

            addCellUltraRes(haveDataCell, sn);

            results.Sort();
        }

        private int dealOverHoSet(int sn)
        {
            foreach (OverHandover_NR ho in overHoSet)
            {
                ho.MakeSummary();
                string[] hoCells = ho.HoDesc.Split(new string[] { "->" }, StringSplitOptions.RemoveEmptyEntries);

                bool includeNearSite = false;
                Dictionary<string, bool> nonNearCellDic = new Dictionary<string, bool>();
                Dictionary<string, bool> nearCellDic = new Dictionary<string, bool>();
                dealNonNearCell(ref sn, ho, hoCells, ref includeNearSite, nonNearCellDic);

                if (includeNearSite)
                {//分析超近站情况，超近站，切换序列必须为超近小区对之间切换，否则过滤掉该频繁切换结果
                    sn = dealNearCellDic(sn, ho, hoCells, nearCellDic);
                }
            }

            return sn;
        }

        private int dealRoadSegments(Dictionary<string, bool> haveDataCell, int sn)
        {
            foreach (RoadSegment_NR seg in roadSegments)
            {
                seg.MakeSummary();
                foreach (string cellName in seg.Cells)
                {
                    bool ignore = false;
                    string ultraType = getCellUltraDesc(cellName);
                    if (ultraType != null && ultraType.Contains("超高") &&
                        (seg.TypeName == "质差路段" || seg.TypeName == "重叠覆盖"))
                    {
                        ignore = dealUltraHighSite(seg, cellName, ignore);
                    }
                    if (ignore)
                    {
                        continue;
                    }
                    CellCheckItem_NR item = new CellCheckItem_NR();
                    item.SN = sn++;
                    item.CellName = cellName;
                    item.Data = seg;
                    item.UltraType = ultraType;
                    results.Add(item);
                    haveDataCell[cellName] = true;
                }
            }

            return sn;
        }

        private bool dealUltraHighSite(RoadSegment_NR seg, string cellName, bool ignore)
        {
            List<UltraSiteCell> lst;
            if (ultraCellDic.TryGetValue(cellName, out lst))
            {
                foreach (UltraSiteCell temp in lst)
                {
                    if (temp is UltraHighSite)
                    {
                        UltraHighSite highSite = temp as UltraHighSite;
                        if (MathFuncs.GetDistance(highSite.Cell.Longitude
                            , highSite.Cell.Latitude, seg.MidLng, seg.MidLat) <= highSite.DistanceByDir)
                        {//超高 ,质差路段/重叠覆盖时，路段中心与问题小区距离必须大于方位角站间距
                            ignore = true;
                            break;
                        }
                    }
                }
            }

            return ignore;
        }

        private void dealNonNearCell(ref int sn, OverHandover_NR ho, string[] hoCells, ref bool includeNearSite, Dictionary<string, bool> nonNearCellDic)
        {
            for (int i = 0; i < hoCells.Length; i++)
            {
                bool isNearSite = false;
                string cellName = hoCells[i];
                List<UltraSiteCell> ultrLst;
                if (ultraCellDic.TryGetValue(cellName, out ultrLst))
                {//超近站，切换序列必须为超近小区对之间切换，否则过滤掉该频繁切换结果
                    foreach (UltraSiteCell item in ultrLst)
                    {
                        if (item is UltraNearSite)
                        {
                            isNearSite = true;
                            includeNearSite = true;
                            break;
                        }
                    }
                }

                if (!isNearSite && !nonNearCellDic.ContainsKey(cellName))
                {//非超近站,保留;超近站，需再进一步判断
                    string ultraType = getCellUltraDesc(cellName);
                    CellCheckItem_NR cellItem = new CellCheckItem_NR();
                    cellItem.SN = sn++;
                    cellItem.CellName = cellName;
                    cellItem.Data = ho;
                    cellItem.UltraType = ultraType;
                    results.Add(cellItem);
                    nonNearCellDic[cellName] = true;
                }
            }
        }

        private int dealNearCellDic(int sn, OverHandover_NR ho, string[] hoCells, Dictionary<string, bool> nearCellDic)
        {
            bool? allNearSite = null;
            for (int i = 1; i < hoCells.Length; i++)
            {
                bool near = isNearSiteEachOther(hoCells[i - 1], hoCells[i]);
                allNearSite = judgeAllNearSite(allNearSite, near);
                if (allNearSite == false)
                {
                    break;
                }
            }

            if (allNearSite == true)
            {
                for (int i = 0; i < hoCells.Length; i++)
                {
                    if (!nearCellDic.ContainsKey(hoCells[i]))
                    {
                        string ultraType = getCellUltraDesc(hoCells[i]);
                        CellCheckItem_NR cellItem = new CellCheckItem_NR();
                        cellItem.SN = sn++;
                        cellItem.CellName = hoCells[i];
                        cellItem.Data = ho;
                        cellItem.UltraType = ultraType;
                        results.Add(cellItem);
                        nearCellDic[hoCells[i]] = true;
                    }
                }
            }

            return sn;
        }

        private bool? judgeAllNearSite(bool? allNearSite, bool near)
        {
            if (allNearSite == null)
            {
                allNearSite = near;
            }
            else
            {
                allNearSite &= near;
            }

            return allNearSite;
        }

        private int addCoverLapCellRes(Dictionary<string, bool> haveDataCell, int sn)
        {
            if (coverLapCellDic != null)
            {
                foreach (string cellName in coverLapCellDic.Keys)
                {
                    if (coverLapCellDic[cellName].Filter(funcCondition.CoverLap.Rate))
                    {
                        continue;
                    }
                    CoverLapCell_NR cvrLap = coverLapCellDic[cellName];
                    cvrLap.MakeSummary();
                    string ultraType = getCellUltraDesc(cellName);
                    CellCheckItem_NR item = new CellCheckItem_NR();
                    item.SN = sn++;
                    item.CellName = cellName;
                    item.Data = cvrLap;
                    item.UltraType = ultraType;
                    results.Add(item);
                    haveDataCell[cellName] = true;
                }
            }

            return sn;
        }

        private int addFarCoverRes(Dictionary<string, bool> haveDataCell, int sn)
        {
            if (farCoverCellDic != null)
            {
                foreach (string cellName in farCoverCellDic.Keys)
                {
                    FarCoverCell_NR farCvrCell = farCoverCellDic[cellName];
                    if (funcCondition.FarCover.Filter(farCvrCell))
                    {
                        continue;
                    }
                    farCvrCell.MakeSummary();
                    string ultraType = getCellUltraDesc(cellName);
                    CellCheckItem_NR item = new CellCheckItem_NR();
                    item.SN = sn++;
                    item.CellName = cellName;
                    item.Data = farCvrCell;
                    item.UltraType = ultraType;
                    results.Add(item);
                    haveDataCell[cellName] = true;
                }
            }

            return sn;
        }

        private void addCellUltraRes(Dictionary<string, bool> haveDataCell, int sn)
        {
            //保留区域内无对应场景的三超小区。
            foreach (string cellName in cellUltraDic.Keys)
            {
                if (haveDataCell.ContainsKey(cellName))
                {//有对应场景跳过
                    continue;
                }

                NRCell cell = CellManager.GetInstance().GetNRCellLatest(cellName);
                if (cell == null
                    || !condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {//小区名关联不到小区，区域外小区跳过
                    continue;
                }

                CellCheckItem_NR item = new CellCheckItem_NR();
                item.SN = sn++;
                item.CellName = cellName;
                item.UltraType = cellUltraDic[cellName];
                results.Add(item);
            }
        }

        private bool isNearSiteEachOther(string cell1, string cell2)
        {
            List<UltraSiteCell> ultrLst;
            if (ultraCellDic.TryGetValue(cell1, out ultrLst))
            {
                foreach (UltraSiteCell item in ultrLst)
                {
                    if (item is UltraNearSite)
                    {
                        UltraNearSite nearSite = item as UltraNearSite;
                        NRBTS otherSite = nearSite.OtherSite as NRBTS;
                        foreach (ICell otherCell in otherSite.LatestCells)
                        {
                            if (otherCell.Name == cell2)
                            {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }

        private List<CellCheckItem_NR> results = null;

        protected override void fireShowForm()
        {
            if (results == null || results.Count == 0)
            {
                MessageBox.Show("无符合条件的数据。");
                return;
            }

            ZTCellCheck.RoadSegment.ResultForm frm =
                MainModel.GetObjectFromBlackboard(typeof(ZTCellCheck.RoadSegment.ResultForm))
                    as ZTCellCheck.RoadSegment.ResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ZTCellCheck.RoadSegment.ResultForm();
            }
            frm.FillData(results);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            roadSegments = null;
            coverLapCellDic = null;
            farCoverCellDic = null;
            overHoSet = null;
            results = null;
        }

        #region Background

        //Background
        /*To be perfect
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.干扰; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = backgroundStat;
                param["C_IThreshold"] = c_iThreshold;
                param["RxLevThreshold"] = rxLevThreshold;
                param["DistanceLast"] = distanceLast;
                param["DitanceTP"] = distanceTP;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value as Dictionary<string, object>;
                if (param.ContainsKey("BackgroundStat"))
                {
                    backgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("C_IThreshold"))
                {
                    c_iThreshold = int.Parse(param["C_IThreshold"].ToString());
                }
                if (param.ContainsKey("RxLevThreshold"))
                {
                    rxLevThreshold = int.Parse(param["RxLevThreshold"].ToString());
                }
                if (param.ContainsKey("DistanceLast"))
                {
                    distanceLast = int.Parse(param["DistanceLast"].ToString());
                }
                if (param.ContainsKey("DitanceTP"))
                {
                    distanceTP = int.Parse(param["DitanceTP"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new WeakC2IRoadProperties_TDSCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (WeakC_IRoad block in weakC_IRoadList)
            {
                block.GetResult();
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            weakC_IRoadList.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                float c2iMean = bgResult.GetImageValueFloat();
                float c2iMin = bgResult.GetImageValueFloat();
                float c2iMax = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("C/I均值：");
                sb.Append(c2iMean);
                sb.Append("\r\n");
                sb.Append("C/I最小值：");
                sb.Append(c2iMin);
                sb.Append("\r\n");
                sb.Append("C/I最大值：");
                sb.Append(c2iMax);
                bgResult.ImageDesc = sb.ToString();
            }
        }*/

        #endregion

        protected virtual float? GetRSRP(TestPoint tp)
        {
            return (float?)tp["NR_SS_RSRP"];
        }
    }

}
