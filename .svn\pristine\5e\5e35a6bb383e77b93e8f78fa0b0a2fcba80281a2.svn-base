﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using Excel = Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Utils;
using static MasterCom.RAMS.ZTFunc.MOSChartAnaManager;

namespace MasterCom.RAMS.Func
{
    public partial class MOSAnalyticsShow : MinCloseForm
    {
        public MOSAnalyticsShow()
        {
            InitializeComponent();
            gridView1.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView2.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView3.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView4.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView5.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView6.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView7.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView8.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView9.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
            gridView10.Appearance.FocusedRow.BackColor = Color.FromArgb(60, 0, 0, 240);
        }

        public void FillData(MOSAnalyticsResult result)
        {
            this.moskeymax = result.Moskeymax;
            this.evenkeymax = result.Evenkeymax;
            this.rxQualkeymax = result.RxQualkeymax;
            this.dicMos = result.DicMos;
            this.dicEvenCountMos = result.DicEvenCountMos;
            this.dicRxqualCountMos = result.DicRxqualCountMos;
            this.dicSpeechCodeMos = result.DicSpeechCodeMos;
            this.dicEvenCountPer = result.DicEvenCountPer;
            this.dicRxqualCountPer = result.DicRxqualCountPer;
            this.dicSpeechCodePer = result.DicSpeechCodePer;
            fresh();
        }
        private void MOSAnalyticsShow_Load(object sender, EventArgs e)
        {
            fresh();
        }

        private void fresh()
        {
            labelControl1.Text = "注：MOS均值与MOS" + compareType + mosvalue + "比例是在固定编码速率（AFR、EFR、FR）和固定Rxqual（Rxqual<3）的情况下得出的.比例是在所有MOS周期内得出的.";
            labelControl2.Text = "注：MOS均值、MOS" + compareType + mosvalue + "比例是在固定编码速度（AFR、EFR、FR）和固定切换次数（无切换）的情况下得出的.比例是在所有MOS周期内得出的";
            labelControl3.Text = "注：MOS均值、MOS" + compareType + mosvalue + "比例是在固定切换次数（切换次数为0）和固定Rxqual（RxQual<3）的情况下得出的.比例是在所有MOS周期内得出的";
            chartControl3.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            chartControl6.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            chartControl9.Titles[0].Text = "MOS" + compareType + mosvalue + "比例";
            analytics();
            Show(dicDatas);
        }

        private void impact()
        {
            datas.Clear();
            float decreaseTotal = 0;
            for (int i = 2; i < 9; i += 3)
            {
                for (int j = 0; j < dicDatas[i].Rows.Count; j++)
                {
                    if (!compareWithMOSThreshold((float)dicDatas[i].Rows[j][1]) && (float)dicDatas[i].Rows[j][1] != 0)//(float)dicDatas[i].Rows[j][1] < mosvalue
                    {
                        decreaseTotal = dealDatas(decreaseTotal, i, j);
                    }
                }
            }
            foreach (List<object> row in datas)
            {
                 float decrease = Math.Abs(mosvalue - ((float)row[2]));
                 row.Add(decrease / decreaseTotal);
            }
            float affecTotal = 0;
            foreach (List<object> row in datas)
            {
                if (row[4]==null)
                {
                    continue;
                }
                float affect = (float)(Decimal.Parse((row[4] as string).TrimEnd('%')) / 100) * (float)row[5];
                affecTotal += affect;
            }
            foreach (List<object> row in datas)
            {
                if (row[4] == null)
                {
                    continue;
                }
                float affect = (float)(Decimal.Parse((row[4] as string).TrimEnd('%')) / 100) * (float)row[5];
                row.Add(affect / affecTotal);
            }
        }

        private float dealDatas(float decreaseTotal, int i, int j)
        {
            float mosAvg = (float)dicDatas[i].Rows[j][1];
            float decrease = Math.Abs(mosvalue - mosAvg);
            int num = -1;
            string speech = null;
            if (i != 8)
            {
                num = (int)dicDatas[i].Rows[j][0];
            }
            else
            {
                speech = dicDatas[i].Rows[j][0] as string;
            }
            string mosmorthanvalue = getMosmorthanvalue(i, num, speech);
            string persent = getPersent(i, num, speech);
            List<object> row = new List<object>();
            if (i == 2)
            {
                row.Add("切换次数");
            }
            else if (i == 5)
            {
                row.Add("RxQual");
            }
            else if (i == 8)
            {
                row.Add("语音编码");
            }
            else
            {
                row.Add("未知");
            }
            if (i == 8)
            {
                row.Add(speech);
            }
            else
            {
                row.Add(num);
            }
            row.Add(mosAvg);
            row.Add(mosmorthanvalue);
            row.Add(persent);
            datas.Add(row);
            decreaseTotal += decrease;
            return decreaseTotal;
        }

        private string getMosmorthanvalue(int i, int num, string speech)
        {
            string mosmorthanvalue = null;
            for (int m = 0; m < dicDatas[i + 1].Rows.Count; m++)
            {
                if (i == 8 ? dicDatas[i + 1].Rows[m][0] as string == speech : (int)dicDatas[i + 1].Rows[m][0] == num)
                {
                    mosmorthanvalue = dicDatas[i + 1].Rows[m][1] as string;
                    break;
                }
            }

            return mosmorthanvalue;
        }

        private string getPersent(int i, int num, string speech)
        {
            string persent = null;
            for (int n = 0; n < dicDatas[i + 2].Rows.Count; n++)
            {
                if (i == 8 ? dicDatas[i + 2].Rows[n][0] as string == speech : (int)dicDatas[i + 2].Rows[n][0] == num)
                {
                    persent = dicDatas[i + 2].Rows[n][1] as string;
                    break;
                }
            }

            return persent;
        }

        private void setColCondStyle(GridColumn col, FormatConditionEnum cond, object value, Color cellBackClr)
        {
            StyleFormatCondition cn;
            cn = new StyleFormatCondition(cond, col, null, value);
            cn.Appearance.BackColor = cellBackClr;
            col.View.FormatConditions.Add(cn);
        }

        //private GridColumn createGcAndAdd2View(GridView ownView, string fieldName, string caption, FormatType displayFormatType, string formatStr)
        //{
        //    GridColumn col = new GridColumn();
        //    col.FieldName = fieldName;
        //    col.Caption = caption;
        //    col.DisplayFormat.FormatType = displayFormatType;
        //    col.DisplayFormat.FormatString = formatStr;
        //    col.VisibleIndex = ownView.Columns.Count;
        //    ownView.Columns.Add(col);
        //    return col;
        //}

        private void setColDisplayFormat(GridColumn col, FormatType displayFormatType, string formatStr)
        {
            col.DisplayFormat.FormatType = displayFormatType;
            col.DisplayFormat.FormatString = formatStr;
        }

        private void analytics()
        {
            try
            {
                dicDatas.Clear();
                // 计算总个数
                int moscount = 0;
                foreach (List<float> fmos in dicMos.Values)
                {
                    moscount += fmos.Count;
                }
                int evencount = 0;
                foreach (List<float> feven in dicEvenCountPer.Values)
                {
                    evencount += feven.Count;
                }
                int rxqualcount = 0;
                foreach (List<float> frxqual in dicRxqualCountPer.Values)
                {
                    rxqualcount += frxqual.Count;
                }
                int speechcount = 0;
                foreach (List<float> fspeech in dicSpeechCodePer.Values)
                {
                    speechcount += fspeech.Count;
                }
                /////////////////MOS////////////////////
                dealMosPercent(moscount);

                //////////切换次数与MOS/MOS均值/////////////
                dealAvgHandover();

                //////////切换次数与MOS/MOS>=2.8比例/////////
                dealMoreThan3HandoverRadio();

                //////////切换次比例/////////
                dealHandoverRadio(evencount);

                //////////RxQual与MOS值关系/MOS均值/////////
                dealAvgRxQual();

                //////////RxQual与MOS值关系/MOS>=2.8比例/////////
                dealMoreThan3RxQualRadio();

                //////////RxQual与MOS值关系/比例/////////
                dealRxQualRatio(rxqualcount);

                string[] speechcodes = { "EFR", "AFR", "FR", "AHR", "HR" };
                //////////编码速率与MOS值关系/MOS均值/////////
                dealAvgMos(speechcodes);

                //////////编码速率与MOS值关系/MOS>=2.8比例/////////
                dealMoreThan3MosRadio(speechcodes);


                //////////编码速率与MOS值关系/比例/////////
                dealMosRadio(speechcount, speechcodes);
            }
            catch
            {
                MessageBox.Show("MOS统计分析失败.\n");
            }
        }

        private void dealMosPercent(int moscount)
        {
            DataTable dt1 = new DataTable();
            dt1.Columns.Add("MOS值", typeof(string));
            dt1.Columns.Add("比例", typeof(string));

            //////////MOS总体分布/////////
            for (int i = 10; i <= moskeymax * 10; i++)
            {
                double d = (double)i / 10;
                string percent = "0%";
                if (dicMos.ContainsKey(d))
                {
                    percent = ((double)dicMos[d].Count / (double)moscount).ToString("p");
                }
                dt1.Rows.Add(new object[] { "[" + d + "~" + (d + 0.1) + ")", percent });
            }
            dicDatas.Add(1, dt1);
        }

        private void dealAvgHandover()
        {
            DataTable dt2 = new DataTable();
            dt2.Columns.Add("切换次数", typeof(int));
            dt2.Columns.Add("MOS均值", typeof(float));
            for (int even = 0; even <= evenkeymax; even++)
            {
                float summos = 0;
                float avgmos = 0;
                if (dicEvenCountMos.ContainsKey(even))
                {
                    foreach (float mos in dicEvenCountMos[even])
                    {
                        summos += mos;
                    }
                    avgmos = (float)Math.Round(summos / dicEvenCountMos[even].Count, 2);
                }
                dt2.Rows.Add(new object[] { even, avgmos });
            }
            dicDatas.Add(2, dt2);
        }

        private void dealMoreThan3HandoverRadio()
        {
            string fieldName = "MOS" + compareType + mosvalue + "比例";
            DataTable dt3 = new DataTable();
            dt3.Columns.Add("切换次数", typeof(int));
            dt3.Columns.Add(fieldName, typeof(float));
            for (int even = 0; even <= evenkeymax; even++)
            {
                int morethan3 = 0;
                float permos = 0;
                if (dicEvenCountMos.ContainsKey(even))
                {
                    foreach (float mos in dicEvenCountMos[even])
                    {
                        if (compareWithMOSThreshold(mos))
                        {
                            morethan3++;
                        }
                    }
                    if (morethan3 != 0)
                    {
                        permos = (float)Math.Round(((float)morethan3 / (float)dicEvenCountMos[even].Count), 4);
                    }
                }
                dt3.Rows.Add(new object[] { even, permos });
            }
            dicDatas.Add(3, dt3);
        }

        private void dealHandoverRadio(int evencount)
        {
            DataTable dt4 = new DataTable();
            string fieldName = "比例";
            dt4.Columns.Add("切换次数", typeof(int));
            dt4.Columns.Add(fieldName, typeof(float));
            for (int even = 0; even <= evenkeymax; even++)
            {
                float permos = 0;
                if (dicEvenCountPer.ContainsKey(even))
                {
                    permos = (float)Math.Round((float)dicEvenCountPer[even].Count / (float)evencount, 4);
                }
                dt4.Rows.Add(new object[] { even, permos });
            }
            dicDatas.Add(4, dt4);
        }

        private void dealAvgRxQual()
        {
            DataTable dt5 = new DataTable();
            dt5.Columns.Add("RxQual", typeof(int));
            dt5.Columns.Add("MOS均值", typeof(float));
            for (int rxqual = 0; rxqual <= rxQualkeymax; rxqual++)
            {
                float summos = 0;
                float avgmos = 0;
                if (dicRxqualCountMos.ContainsKey(rxqual))
                {
                    foreach (float mos in dicRxqualCountMos[rxqual])
                    {
                        summos += mos;
                    }
                    avgmos = (float)Math.Round(summos / dicRxqualCountMos[rxqual].Count, 2);
                }
                dt5.Rows.Add(new object[] { rxqual, avgmos });
            }
            dicDatas.Add(5, dt5);
        }

        private void dealMoreThan3RxQualRadio()
        {
            string fieldName = "MOS" + compareType + mosvalue + "比例";
            DataTable dt6 = new DataTable();
            dt6.Columns.Add("RxQual", typeof(int));
            dt6.Columns.Add(fieldName, typeof(float));
            for (int rxqual = 0; rxqual <= rxQualkeymax; rxqual++)
            {
                int morethan3 = 0;
                float permos = 0;
                if (dicRxqualCountMos.ContainsKey(rxqual))
                {
                    foreach (float mos in dicRxqualCountMos[rxqual])
                    {
                        if (compareWithMOSThreshold(mos))
                        {
                            morethan3++;
                        }
                    }
                    if (morethan3 != 0)
                    {
                        permos = (float)Math.Round((float)morethan3 / (float)dicRxqualCountMos[rxqual].Count, 4);
                    }
                }
                dt6.Rows.Add(new object[] { rxqual, permos });
            }
            dicDatas.Add(6, dt6);
        }

        private void dealRxQualRatio(int rxqualcount)
        {
            string fieldName = "比例";
            DataTable dt7 = new DataTable();
            dt7.Columns.Add("RxQual", typeof(int));
            dt7.Columns.Add(fieldName, typeof(float));
            for (int rxqual = 0; rxqual <= rxQualkeymax; rxqual++)
            {
                float permos = 0;
                if (dicRxqualCountPer.ContainsKey(rxqual))
                {
                    permos = (float)Math.Round((float)dicRxqualCountPer[rxqual].Count / (float)rxqualcount, 4);
                }
                dt7.Rows.Add(new object[] { rxqual, permos });
            }
            dicDatas.Add(7, dt7);
        }

        private void dealAvgMos(string[] speechcodes)
        {
            DataTable dt8 = new DataTable();
            dt8.Columns.Add("编码方式", typeof(string));
            dt8.Columns.Add("MOS均值", typeof(float));
            foreach (string speechcode in speechcodes)
            {
                float totalmos = 0;
                float avgmos = 0;
                if (dicSpeechCodeMos.ContainsKey(speechcode))
                {
                    foreach (float mos in dicSpeechCodeMos[speechcode])
                    {
                        totalmos += mos;
                    }
                    avgmos = (float)Math.Round(totalmos / dicSpeechCodeMos[speechcode].Count, 2);
                }
                dt8.Rows.Add(new object[] { speechcode, avgmos });
            }
            dicDatas.Add(8, dt8);
        }

        private void dealMoreThan3MosRadio(string[] speechcodes)
        {
            string fieldName = "MOS" + compareType + mosvalue + "比例";
            DataTable dt9 = new DataTable();
            dt9.Columns.Add("编码方式", typeof(string));
            dt9.Columns.Add(fieldName, typeof(float));
            foreach (string speechcode in speechcodes)
            {
                int morethan3 = 0;
                float permos = 0;
                if (dicSpeechCodeMos.ContainsKey(speechcode))
                {
                    foreach (float mos in dicSpeechCodeMos[speechcode])
                    {
                        if (compareWithMOSThreshold(mos))
                        {
                            morethan3++;
                        }
                    }
                    if (morethan3 != 0)
                    {
                        permos = (float)Math.Round((float)morethan3 / (float)dicSpeechCodeMos[speechcode].Count, 4);
                    }
                }
                dt9.Rows.Add(new object[] { speechcode, permos });
            }
            dicDatas.Add(9, dt9);
        }

        private void dealMosRadio(int speechcount, string[] speechcodes)
        {
            string fieldName = "比例";
            DataTable dt10 = new DataTable();
            dt10.Columns.Add("编码方式", typeof(string));
            dt10.Columns.Add(fieldName, typeof(float));
            foreach (string speechcode in speechcodes)
            {
                float permos = 0;
                if (dicSpeechCodePer.ContainsKey(speechcode))
                {
                    permos = (float)Math.Round((float)dicSpeechCodePer[speechcode].Count / (float)speechcount, 4);
                }
                dt10.Rows.Add(new object[] { speechcode, permos });
            }
            dicDatas.Add(10, dt10);
        }

        private void Show(Dictionary<int,DataTable> dicdatas)
        {
            this.dicDatas = dicdatas;
            //////////////////////////////1///////////////////////////////////////
            gridControl1.DataSource = dicdatas[1];
            Series series1 = chartControl1.Series[0];
            series1.Points.Clear();
            for (int i = 0; i < dicdatas[1].Rows.Count; i++)
            {
                series1.Points.Add(new SeriesPoint(dicdatas[1].Rows[i][0], Math.Round(Decimal.Parse((dicdatas[1].Rows[i][1] as string).TrimEnd('%')) / 100, 2)));
            }
            /////////////////////////////2//////////////////////////////////////
            gridView2.Columns.Clear();
            gridControl2.DataSource = dicdatas[2];
            setColCondStyle(gridView2.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView2.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series2 = chartControl2.Series[0];
            series2.Points.Clear();
            for (int i = 0; i < dicdatas[2].Rows.Count; i++)
            {
                series2.Points.Add(new SeriesPoint(dicdatas[2].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[2].Rows[i][1]), 2)));
            }
            /////////////////////////////3//////////////////////////////////////
            gridView3.Columns.Clear();
            gridControl3.DataSource = dicdatas[3];
            setColDisplayFormat(gridView3.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView3.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series3 = chartControl3.Series[0];
            series3.Points.Clear();
            series3.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[3].Rows.Count; i++)
            {
                series3.Points.Add(new SeriesPoint(dicdatas[3].Rows[i][0], dicdatas[3].Rows[i][1]));
            }
            /////////////////////////////4//////////////////////////////////////
            gridControl4.DataSource = dicdatas[4];
            setColDisplayFormat(gridView4.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView4.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series4 = chartControl4.Series[0];
            series4.Points.Clear();
            series4.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[4].Rows.Count; i++)
            {
                series4.Points.Add(new SeriesPoint(dicdatas[4].Rows[i][0], dicdatas[4].Rows[i][1]));
            }
            /////////////////////////////5//////////////////////////////////////
            gridControl5.DataSource = dicdatas[5];
            setColCondStyle(gridView5.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView5.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series5 = chartControl5.Series[0];
            series5.Points.Clear();
            for (int i = 0; i < dicdatas[5].Rows.Count; i++)
            {
                series5.Points.Add(new SeriesPoint(dicdatas[5].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[5].Rows[i][1]), 2)));
            }
            /////////////////////////////6//////////////////////////////////////
            gridView6.Columns.Clear();
            gridControl6.DataSource = dicdatas[6];
            Series series6 = chartControl6.Series[0];
            setColDisplayFormat(gridView6.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView6.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            series6.Points.Clear();
            series6.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[6].Rows.Count; i++)
            {
                series6.Points.Add(new SeriesPoint(dicdatas[6].Rows[i][0], dicdatas[6].Rows[i][1]));
            }
            /////////////////////////////7//////////////////////////////////////
            gridControl7.DataSource = dicdatas[7];
            setColDisplayFormat(gridView7.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView7.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series7 = chartControl7.Series[0];
            series7.Points.Clear();
            series7.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[7].Rows.Count; i++)
            {
                series7.Points.Add(new SeriesPoint(dicdatas[7].Rows[i][0], dicdatas[7].Rows[i][1]));
            }
            /////////////////////////////8//////////////////////////////////////
            gridControl8.DataSource = dicdatas[8];
            setColCondStyle(gridView8.Columns[1], FormatConditionEnum.GreaterOrEqual, 2.8, Color.Lime);
            setColCondStyle(gridView8.Columns[1], FormatConditionEnum.Less, 2.8, Color.Orange);
            Series series8 = chartControl8.Series[0];
            series8.Points.Clear();
            for (int i = 0; i < dicdatas[8].Rows.Count; i++)
            {
                series8.Points.Add(new SeriesPoint(dicdatas[8].Rows[i][0], Math.Round(Convert.ToDouble(dicdatas[8].Rows[i][1]), 2)));
            }
            /////////////////////////////9//////////////////////////////////////
            gridView9.Columns.Clear();
            gridControl9.DataSource = dicdatas[9];
            setColDisplayFormat(gridView9.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView9.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series9 = chartControl9.Series[0];
            series9.Points.Clear();
            for (int i = 0; i < dicdatas[9].Rows.Count; i++)
            {
                series9.Points.Add(new SeriesPoint(dicdatas[9].Rows[i][0], dicdatas[9].Rows[i][1]));
            }
            /////////////////////////////10//////////////////////////////////////
            gridControl10.DataSource = dicdatas[10];
            setColDisplayFormat(gridView10.Columns[1], FormatType.Numeric, "p");
            setColCondStyle(gridView10.Columns[1], FormatConditionEnum.GreaterOrEqual, 0.9, Color.Lime);
            Series series10 = chartControl10.Series[0];
            series10.Points.Clear();
            series10.PointOptions.ValueNumericOptions.Precision = 2;
            for (int i = 0; i < dicdatas[10].Rows.Count; i++)
            {
                series10.Points.Add(new SeriesPoint(dicdatas[10].Rows[i][0], dicdatas[10].Rows[i][1]));
            }
        }

        private void export()
        {
            if (dicDatas != null)
            {
                impact();
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel file (*.xls)|*.xls";
                saveFileDialog.RestoreDirectory = true;
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Excel.Application excel = new Excel.Application();
                    Excel.Workbook workbook = excel.Workbooks.Add(true);
                    Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;
                    excel.Visible = true;
                    worksheet.Name = "MOS统计分析";

                    int[] firstrow = new int[11];
                    try
                    {
                        int excelrow = addHeader(worksheet, firstrow);
                        worksheet.Cells[excelrow, 1] = "影响因素";
                        worksheet.Cells[excelrow, 2] = "影响因素项";
                        worksheet.Cells[excelrow, 3] = "MOS均值";
                        worksheet.Cells[excelrow, 4] = "MOS" + compareType + mosvalue + "比率";
                        worksheet.Cells[excelrow, 5] = "比例分布";
                        worksheet.Cells[excelrow, 6] = "影响因子";
                        worksheet.Cells[excelrow, 7] = "实际影响因子";
                        excelrow++;

                        foreach (List<object> row in datas)
                        {
                            for (int column = 0; column < row.Count; column++)
                            {
                                worksheet.Cells[excelrow, column + 1] = row[column];
                            }
                            excelrow++;
                        }

                        //创建图表
                        CreateChart(ref workbook, ref worksheet, firstrow);
                        //保存文件
                        excel.DisplayAlerts = false;
                        workbook.SaveAs(saveFileDialog.FileName, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Excel.XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                    }
                    catch
                    {
                        MessageBox.Show("文件保存失败!");
                    }
                }
            }
        }

        private int addHeader(Excel.Worksheet worksheet, int[] firstrow)
        {
            int excelrow = 1;
            for (int i = 1; i <= 10; i++)
            {
                firstrow[i] = excelrow;
                worksheet.Cells[excelrow, 1] = dicDatas[i].Columns[0].ColumnName;
                worksheet.Cells[excelrow, 2] = dicDatas[i].Columns[1].ColumnName;
                excelrow++;

                for (int row = 0; row < dicDatas[i].Rows.Count; row++)
                {
                    for (int column = 0; column < 2; column++)
                    {
                        worksheet.Cells[excelrow, column + 1] = dicDatas[i].Rows[row][column];
                    }
                    excelrow++;
                }
                excelrow = setHeaderContent(worksheet, excelrow, i);
                excelrow++;
            }

            return excelrow;
        }

        private int setHeaderContent(Excel.Worksheet worksheet, int excelrow, int i)
        {
            if (i == 2 || i == 3)
            {
                if (dicDatas[i].Rows.Count < 15)
                {
                    excelrow += (15 - dicDatas[i].Rows.Count);
                }
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定编码速率（编码速率为AFR、EFR、FR）、固定Rxqual（Rxqual<3），固定内切次数（内切次数为0）和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            if (i == 5 || i == 6)
            {
                if (dicDatas[i].Rows.Count < 15)
                {
                    excelrow += (15 - dicDatas[i].Rows.Count);
                }
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定编码速率（编码速率为AFR、EFR、FR）、固定切换次数（切换次数为0），固定内切次数（内切次数为0）和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            if (i == 8 || i == 9)
            {
                excelrow += (15 - dicDatas[i].Rows.Count);
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在固定切换次数（切换次数为0）、固定Rxqual（RxQual<3），固定内切次数（内切次数为0）和固定手机发射功率为非“MS满功率”的情况下得出的。";
                excelrow += 2;
            }
            if (i == 4 || i == 7 || i == 10)
            {
                if (dicDatas[i].Rows.Count < 15)
                {
                    excelrow += (15 - dicDatas[i].Rows.Count);
                }
                worksheet.Cells[excelrow, 1] = "注：此分析结果是在所有MOS周期内得出的.";
                excelrow += 2;
            }

            return excelrow;
        }

        private void CreateChart(ref Excel.Workbook workbook, ref Excel.Worksheet worksheet, int[] firstrow)
        {
            int moscount = dicDatas[1].Rows.Count;
            int evencount = dicDatas[2].Rows.Count;
            int rxqualcount = dicDatas[5].Rows.Count;
            int speechcount = dicDatas[8].Rows.Count;
            //MOS比例图表
            Excel.Chart chart1 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange1 = worksheet.get_Range("B1", "B" + (moscount + 1));

            chart1.ChartWizard(chartrange1, Excel.XlChartType.xlLine, Missing.Value, Excel.XlRowCol.xlColumns, 0, 1, false,
                Missing.Value, Missing.Value, Missing.Value, Missing.Value);

            chart1.HasTitle = false;
            Excel.Series exseries1 = (Excel.Series)chart1.SeriesCollection(1);
            exseries1.XValues = worksheet.get_Range("A2", "A" + (moscount + 1));
            exseries1.HasDataLabels = false;

            Excel.Axis yAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis1.MinimumScale = 0;
            yAxis1.HasMajorGridlines = true;
            yAxis1.MajorGridlines.Border.ColorIndex = 15;

            Excel.Axis xAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlCategory, Excel.XlAxisGroup.xlPrimary);
            xAxis1.TickLabels.Font.Size = 10;

            chart1.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(1).Top = 2;
            worksheet.Shapes.Item(1).Left = 162;


            //切换次数与MOS均值
            Excel.Chart chart2 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange2 = worksheet.get_Range("B" + firstrow[2], "B" + (firstrow[2] + evencount));
            chart2.ChartWizard(chartrange2, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries2 = (Excel.Series)chart2.SeriesCollection(1);
            exseries2.BarShape = Excel.XlBarShape.xlCylinder;
            exseries2.XValues = worksheet.get_Range("A" + (firstrow[2] + 1), "A" + (firstrow[2] + evencount));
            exseries2.HasDataLabels = true;

            Excel.Axis yAxis2 = (Excel.Axis)chart2.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis2.MinimumScale = 0.0;
            yAxis2.MajorUnit = 1.0;
            yAxis2.HasMajorGridlines = true;
            yAxis2.MajorGridlines.Border.ColorIndex = 15;
            yAxis2.TickLabels.NumberFormat = "0.00";

            chart2.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            //worksheet.Shapes.Item(2).Top = 220;
            worksheet.Shapes.Item(2).Top = (float)(double)worksheet.get_Range("D" + firstrow[2], "J" + firstrow[2] + 16).Top;
            worksheet.Shapes.Item(2).Left = (float)(double)worksheet.get_Range("D" + firstrow[2], "J" + firstrow[2] + 16).Left;


            ////切换次数与MOS>=3
            Excel.Chart chart3 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange3 = worksheet.get_Range("B" + firstrow[3], "B" + (firstrow[3] + evencount));

            chart3.ChartWizard(chartrange3, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries3 = (Excel.Series)chart3.SeriesCollection(1);
            exseries3.BarShape = Excel.XlBarShape.xlCylinder;
            exseries3.XValues = worksheet.get_Range("A" + (firstrow[3] + 1), "A" + (firstrow[3] + evencount));
            exseries3.HasDataLabels = true;

            Excel.Axis yAxis3 = (Excel.Axis)chart3.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis3.MinimumScale = 0;
            yAxis3.HasMajorGridlines = true;
            yAxis3.MajorGridlines.Border.ColorIndex = 15;

            chart3.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(3).Top = (float)(double)worksheet.get_Range("D" + firstrow[3], "J" + firstrow[3] + 16).Top;
            worksheet.Shapes.Item(3).Left = (float)(double)worksheet.get_Range("D" + firstrow[3], "J" + firstrow[3] + 16).Left;


            ////切换次数与MOS比例
            Excel.Chart chart4 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange4 = worksheet.get_Range("B" + firstrow[4], "B" + (firstrow[4] + evencount));

            chart4.ChartWizard(chartrange4, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries4 = (Excel.Series)chart4.SeriesCollection(1);
            exseries4.BarShape = Excel.XlBarShape.xlCylinder;
            exseries4.XValues = worksheet.get_Range("A" + (firstrow[4] + 1), "A" + (firstrow[4] + evencount));
            exseries4.HasDataLabels = true;

            Excel.Axis yAxis4 = (Excel.Axis)chart4.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis4.MinimumScale = 0;
            yAxis4.HasMajorGridlines = true;
            yAxis4.MajorGridlines.Border.ColorIndex = 15;

            chart4.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(4).Top = (float)(double)worksheet.get_Range("D" + firstrow[4], "J" + firstrow[4] + 16).Top;
            worksheet.Shapes.Item(4).Left = (float)(double)worksheet.get_Range("D" + firstrow[4], "J" + firstrow[4] + 16).Left;

            /////////RxQual与MOS均值/////////////////////
            Excel.Chart chart5 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange5 = worksheet.get_Range("B" + firstrow[5], "B" + (firstrow[5] + rxqualcount));
            chart5.ChartWizard(chartrange5, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries5 = (Excel.Series)chart5.SeriesCollection(1);
            exseries5.BarShape = Excel.XlBarShape.xlCylinder;
            exseries5.XValues = worksheet.get_Range("A" + (firstrow[5] + 1), "A" + (firstrow[5] + rxqualcount));
            exseries5.HasDataLabels = true;

            Excel.Axis yAxis5 = (Excel.Axis)chart5.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis5.MinimumScale = 0;
            yAxis5.MajorUnit = 1.0;
            yAxis5.HasMajorGridlines = true;
            yAxis5.MajorGridlines.Border.ColorIndex = 15;
            yAxis5.TickLabels.NumberFormat = "0.00";

            chart5.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(5).Top = (float)(double)worksheet.get_Range("D" + firstrow[5], "J" + firstrow[5] + 16).Top;
            worksheet.Shapes.Item(5).Left = (float)(double)worksheet.get_Range("D" + firstrow[5], "J" + firstrow[5] + 16).Left;


            ////RxQual与MOS>=3
            Excel.Chart chart6 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange6 = worksheet.get_Range("B" + firstrow[6], "B" + (firstrow[6] + rxqualcount));

            chart6.ChartWizard(chartrange6, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries6 = (Excel.Series)chart6.SeriesCollection(1);
            exseries6.BarShape = Excel.XlBarShape.xlCylinder;
            exseries6.XValues = worksheet.get_Range("A" + (firstrow[6] + 1), "A" + (firstrow[6] + rxqualcount));
            exseries6.HasDataLabels = true;

            Excel.Axis yAxis6 = (Excel.Axis)chart6.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis6.MinimumScale = 0;
            yAxis6.HasMajorGridlines = true;
            yAxis6.MajorGridlines.Border.ColorIndex = 15;

            chart6.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(6).Top = (float)(double)worksheet.get_Range("D" + firstrow[6], "J" + firstrow[6] + 16).Top;
            worksheet.Shapes.Item(6).Left = (float)(double)worksheet.get_Range("D" + firstrow[6], "J" + firstrow[6] + 16).Left;


            ////RxQual与MOS比例
            Excel.Chart chart7 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange7 = worksheet.get_Range("B" + firstrow[7], "B" + (firstrow[7] + rxqualcount));

            chart7.ChartWizard(chartrange7, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries7 = (Excel.Series)chart7.SeriesCollection(1);
            exseries7.BarShape = Excel.XlBarShape.xlCylinder;
            exseries7.XValues = worksheet.get_Range("A" + (firstrow[7] + 1), "A" + (firstrow[7] + rxqualcount));
            exseries7.HasDataLabels = true;

            Excel.Axis yAxis7 = (Excel.Axis)chart7.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis7.MinimumScale = 0;
            yAxis7.HasMajorGridlines = true;
            yAxis7.MajorGridlines.Border.ColorIndex = 15;

            chart7.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(7).Top = (float)(double)worksheet.get_Range("D" + firstrow[7], "J" + firstrow[7] + 16).Top;
            worksheet.Shapes.Item(7).Left = (float)(double)worksheet.get_Range("D" + firstrow[7], "J" + firstrow[7] + 16).Left;


            //编码速率与MOS均值
            Excel.Chart chart8 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange8 = worksheet.get_Range("B" + firstrow[8], "B" + (firstrow[8] + speechcount));
            chart8.ChartWizard(chartrange8, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries8 = (Excel.Series)chart8.SeriesCollection(1);
            exseries8.BarShape = Excel.XlBarShape.xlCylinder;
            exseries8.XValues = worksheet.get_Range("A" + (firstrow[8] + 1), "A" + (firstrow[8] + speechcount));
            exseries8.HasDataLabels = true;

            Excel.Axis yAxis8 = (Excel.Axis)chart8.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis8.MinimumScale = 0;
            yAxis8.MajorUnit = 1.0;
            yAxis8.HasMajorGridlines = true;
            yAxis8.MajorGridlines.Border.ColorIndex = 15;
            yAxis8.TickLabels.NumberFormat = "0.00";

            chart8.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(8).Top = (float)(double)worksheet.get_Range("D" + firstrow[8], "J" + firstrow[8] + 16).Top;
            worksheet.Shapes.Item(8).Left = (float)(double)worksheet.get_Range("D" + firstrow[8], "J" + firstrow[8] + 16).Left;

            ////编码速率与MOS>=3
            Excel.Chart chart9 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange9 = worksheet.get_Range("B" + firstrow[9], "B" + (firstrow[9] + speechcount));

            chart9.ChartWizard(chartrange9, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS" + compareType + mosvalue + "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries9 = (Excel.Series)chart9.SeriesCollection(1);
            exseries9.BarShape = Excel.XlBarShape.xlCylinder;
            exseries9.XValues = worksheet.get_Range("A" + (firstrow[9] + 1), "A" + (firstrow[9] + speechcount));
            exseries9.HasDataLabels = true;

            Excel.Axis yAxis9 = (Excel.Axis)chart9.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis9.MinimumScale = 0;
            yAxis9.HasMajorGridlines = true;
            yAxis9.MajorGridlines.Border.ColorIndex = 15;

            chart9.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(9).Top = (float)(double)worksheet.get_Range("D" + firstrow[9], "J" + firstrow[9] + 16).Top;
            worksheet.Shapes.Item(9).Left = (float)(double)worksheet.get_Range("D" + firstrow[9], "J" + firstrow[9] + 16).Left;


            ////编码速率与MOS比例
            Excel.Chart chart10 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange10 = worksheet.get_Range("B" + firstrow[10], "B" + (firstrow[10] + speechcount));

            chart10.ChartWizard(chartrange10, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries10 = (Excel.Series)chart10.SeriesCollection(1);
            exseries10.BarShape = Excel.XlBarShape.xlCylinder;
            exseries10.XValues = worksheet.get_Range("A" + (firstrow[10] + 1), "A" + (firstrow[10] + speechcount));
            exseries10.HasDataLabels = true;

            Excel.Axis yAxis10 = (Excel.Axis)chart10.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis10.MinimumScale = 0;
            yAxis10.HasMajorGridlines = true;
            yAxis10.MajorGridlines.Border.ColorIndex = 15;

            chart10.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(10).Top = (float)(double)worksheet.get_Range("D" + firstrow[10], "J" + firstrow[10] + 16).Top;
            worksheet.Shapes.Item(10).Left = (float)(double)worksheet.get_Range("D" + firstrow[10], "J" + firstrow[10] + 16).Left;
        }

        private void btnMos_Click(object sender, EventArgs e)
        {
            mosvalue = float.Parse(txtMos.Text);
            compareType = cbxCompareType.Text;
            fresh();
        }
        private void btnExpore_Click(object sender, EventArgs e)
        {
            export(); 
        }

        private bool compareWithMOSThreshold(float mos)
        {
            if (compareType.Equals(">="))
            {
                return mos >= mosvalue;
            }
            else
            {
                return mos <= mosvalue;
            }
        }

        float mosvalue = 2.8F;
        string compareType = ">=";
        double moskeymax = 0;
        int evenkeymax = 0;
        int rxQualkeymax = 0;
        Dictionary<double, List<float>> dicMos = new Dictionary<double, List<float>>();
        Dictionary<int, List<float>> dicEvenCountMos = new Dictionary<int, List<float>>();
        Dictionary<int, List<float>> dicRxqualCountMos = new Dictionary<int, List<float>>();
        Dictionary<string, List<float>> dicSpeechCodeMos = new Dictionary<string, List<float>>();
        Dictionary<int, List<float>> dicEvenCountPer = new Dictionary<int, List<float>>();
        Dictionary<int, List<float>> dicRxqualCountPer = new Dictionary<int, List<float>>();
        Dictionary<string, List<float>> dicSpeechCodePer = new Dictionary<string, List<float>>();
        Dictionary<int, DataTable> dicDatas = new Dictionary<int, DataTable>();
        List<List<object>> datas = new List<List<object>>();

        private void txtMos_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!(((e.KeyChar >= '0') && (e.KeyChar <= '9')) || e.KeyChar <= 31))
            {
                if (e.KeyChar != '.')
                {
                    e.Handled = true;
                }
                else if (txtMos.Text.Trim().IndexOf('.') > -1)
                {
                    e.Handled = true;
                }
            }
            else
            {
                if (e.KeyChar <= 31)
                {
                    e.Handled = false;
                }
                else if (txtMos.Text.Trim().IndexOf('.') > -1)
                {
                    int mosLength = txtMos.Text.Trim().Substring(txtMos.Text.Trim().IndexOf('.') + 1).Length;
                    if (mosLength >= 4)
                    {
                        e.Handled = true;
                    }
                }
            }
        }
    }
}
