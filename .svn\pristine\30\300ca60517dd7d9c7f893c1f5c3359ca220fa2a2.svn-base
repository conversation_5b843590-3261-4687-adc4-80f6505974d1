﻿namespace MasterCom.RAMS.Func.ProblemBlock
{
    partial class StructProblemBlockForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(StructProblemBlockForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.问题点信息详情ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportBBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSelectedBBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.problemSummarryListView = new System.Windows.Forms.ListView();
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.filterObjectListView = new BrightIdeasSoftware.FilterObjectListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCreateDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstAbDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastAbDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTestDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCloseDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnsetBlockDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbDays = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNormalDays = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbEvents = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBlockedEventName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTestRound = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCloseReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEvaluate_date = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnevaluate_result = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCenterLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCenterLong = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPlaceDes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.rabEventLayer = new System.Windows.Forms.RadioButton();
            this.rabProblemLayer = new System.Windows.Forms.RadioButton();
            this.ctxMenu.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.filterObjectListView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.问题点信息详情ToolStripMenuItem,
            this.miExportExcel,
            this.miExportBBReport,
            this.miExportSelectedBBReport,
            this.ToolMenuItem});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(194, 114);
            // 
            // 问题点信息详情ToolStripMenuItem
            // 
            this.问题点信息详情ToolStripMenuItem.Name = "问题点信息详情ToolStripMenuItem";
            this.问题点信息详情ToolStripMenuItem.Size = new System.Drawing.Size(193, 22);
            this.问题点信息详情ToolStripMenuItem.Text = "问题点信息详情...";
            this.问题点信息详情ToolStripMenuItem.Click += new System.EventHandler(this.问题点信息详情ToolStripMenuItem_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(193, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportBBReport
            // 
            this.miExportBBReport.Name = "miExportBBReport";
            this.miExportBBReport.Size = new System.Drawing.Size(193, 22);
            this.miExportBBReport.Text = "导出全部问题点报告...";
            this.miExportBBReport.Click += new System.EventHandler(this.miExportBBReport_Click);
            // 
            // miExportSelectedBBReport
            // 
            this.miExportSelectedBBReport.Name = "miExportSelectedBBReport";
            this.miExportSelectedBBReport.Size = new System.Drawing.Size(193, 22);
            this.miExportSelectedBBReport.Text = "导出选中问题点报告...";
            this.miExportSelectedBBReport.Click += new System.EventHandler(this.miExportSelectedBBReport_Click);
            // 
            // ToolMenuItem
            // 
            this.ToolMenuItem.Name = "ToolMenuItem";
            this.ToolMenuItem.Size = new System.Drawing.Size(193, 22);
            this.ToolMenuItem.Text = "导出问题点图层";
            this.ToolMenuItem.Click += new System.EventHandler(this.ToolMenuItem_Click);
            // 
            // ToolStripMenuItem
            // 
            this.ToolStripMenuItem.Name = "ToolStripMenuItem";
            this.ToolStripMenuItem.Size = new System.Drawing.Size(129, 22);
            this.ToolStripMenuItem.Text = "导出Excel";
            this.ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.problemSummarryListView);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1087, 382);
            this.xtraTabPage3.Text = "各地市汇总信息";
            // 
            // problemSummarryListView
            // 
            this.problemSummarryListView.Alignment = System.Windows.Forms.ListViewAlignment.Left;
            this.problemSummarryListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader7});
            this.problemSummarryListView.ContextMenuStrip = this.ctxMenu;
            this.problemSummarryListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.problemSummarryListView.FullRowSelect = true;
            this.problemSummarryListView.GridLines = true;
            this.problemSummarryListView.Location = new System.Drawing.Point(0, 0);
            this.problemSummarryListView.Name = "problemSummarryListView";
            this.problemSummarryListView.ShowGroups = false;
            this.problemSummarryListView.ShowItemToolTips = true;
            this.problemSummarryListView.Size = new System.Drawing.Size(1087, 382);
            this.problemSummarryListView.TabIndex = 4;
            this.problemSummarryListView.UseCompatibleStateImageBehavior = false;
            this.problemSummarryListView.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "序号";
            this.columnHeader2.Width = 80;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "地市";
            this.columnHeader3.Width = 90;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "弱覆盖总数";
            this.columnHeader4.Width = 90;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "过覆盖总数";
            this.columnHeader5.Width = 90;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "重叠覆盖总数";
            this.columnHeader6.Width = 90;
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "覆盖不连续总数";
            this.columnHeader7.Width = 100;
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.filterObjectListView);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1087, 382);
            this.xtraTabPage1.Text = "问题点详细列表";
            // 
            // filterObjectListView
            // 
            this.filterObjectListView.AllColumns.Add(this.olvColumnSN);
            this.filterObjectListView.AllColumns.Add(this.olvColumnID);
            this.filterObjectListView.AllColumns.Add(this.olvColumnStatus);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCreateDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnFirstAbDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnLastAbDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnLastTestDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCloseDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnsetBlockDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnAbDays);
            this.filterObjectListView.AllColumns.Add(this.olvColumnNormalDays);
            this.filterObjectListView.AllColumns.Add(this.olvColumnAbEvents);
            this.filterObjectListView.AllColumns.Add(this.olvColumnBlockedEventName);
            this.filterObjectListView.AllColumns.Add(this.olvColumnTestRound);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCloseReason);
            this.filterObjectListView.AllColumns.Add(this.olvColumnEvaluate_date);
            this.filterObjectListView.AllColumns.Add(this.olvColumnevaluate_result);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCenterLat);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCenterLong);
            this.filterObjectListView.AllColumns.Add(this.olvColumnPlaceDes);
            this.filterObjectListView.AllColumns.Add(this.olvColumnReason);
            this.filterObjectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnID,
            this.olvColumnStatus,
            this.olvColumnCreateDate,
            this.olvColumnFirstAbDate,
            this.olvColumnLastAbDate,
            this.olvColumnLastTestDate,
            this.olvColumnCloseDate,
            this.olvColumnsetBlockDate,
            this.olvColumnAbDays,
            this.olvColumnNormalDays,
            this.olvColumnAbEvents,
            this.olvColumnBlockedEventName,
            this.olvColumnTestRound,
            this.olvColumnCloseReason,
            this.olvColumnEvaluate_date,
            this.olvColumnevaluate_result,
            this.olvColumnCenterLat,
            this.olvColumnCenterLong,
            this.olvColumnPlaceDes,
            this.olvColumnReason});
            this.filterObjectListView.ContextMenuStrip = this.ctxMenu;
            this.filterObjectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.filterObjectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.filterObjectListView.FullRowSelect = true;
            this.filterObjectListView.GridLines = true;
            this.filterObjectListView.HeaderWordWrap = true;
            this.filterObjectListView.IsNeedShowOverlay = false;
            this.filterObjectListView.Location = new System.Drawing.Point(0, 0);
            this.filterObjectListView.Name = "filterObjectListView";
            this.filterObjectListView.ShowGroups = false;
            this.filterObjectListView.Size = new System.Drawing.Size(1087, 382);
            this.filterObjectListView.TabIndex = 5;
            this.filterObjectListView.UseAlternatingBackColors = true;
            this.filterObjectListView.UseCompatibleStateImageBehavior = false;
            this.filterObjectListView.View = System.Windows.Forms.View.Details;
            this.filterObjectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.filterObjectListView_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "地市";
            this.olvColumnSN.Width = 66;
            // 
            // olvColumnID
            // 
            this.olvColumnID.AspectName = "blockId";
            this.olvColumnID.HeaderFont = null;
            this.olvColumnID.Text = "问题点ID";
            this.olvColumnID.Width = 77;
            // 
            // olvColumnStatus
            // 
            this.olvColumnStatus.AspectName = "StructStatusDes";
            this.olvColumnStatus.HeaderFont = null;
            this.olvColumnStatus.Text = "状态";
            this.olvColumnStatus.Width = 80;
            // 
            // olvColumnCreateDate
            // 
            this.olvColumnCreateDate.AspectName = "CreateDateString";
            this.olvColumnCreateDate.HeaderFont = null;
            this.olvColumnCreateDate.Text = "问题点创建时间";
            this.olvColumnCreateDate.Width = 100;
            // 
            // olvColumnFirstAbDate
            // 
            this.olvColumnFirstAbDate.AspectName = "First_abnormal_dateString";
            this.olvColumnFirstAbDate.HeaderFont = null;
            this.olvColumnFirstAbDate.Text = "第一个异常时间";
            this.olvColumnFirstAbDate.Width = 100;
            // 
            // olvColumnLastAbDate
            // 
            this.olvColumnLastAbDate.AspectName = "Last_abnormal_dateString";
            this.olvColumnLastAbDate.HeaderFont = null;
            this.olvColumnLastAbDate.Text = "最后异常时间";
            this.olvColumnLastAbDate.Width = 100;
            // 
            // olvColumnLastTestDate
            // 
            this.olvColumnLastTestDate.AspectName = "Last_test_dateString";
            this.olvColumnLastTestDate.HeaderFont = null;
            this.olvColumnLastTestDate.Text = "最后测试时间";
            this.olvColumnLastTestDate.Width = 100;
            // 
            // olvColumnCloseDate
            // 
            this.olvColumnCloseDate.AspectName = "ClosedDateString";
            this.olvColumnCloseDate.HeaderFont = null;
            this.olvColumnCloseDate.Text = "问题点关闭时间";
            this.olvColumnCloseDate.Width = 100;
            // 
            // olvColumnsetBlockDate
            // 
            this.olvColumnsetBlockDate.AspectName = "SetBlackspots_dateString";
            this.olvColumnsetBlockDate.HeaderFont = null;
            this.olvColumnsetBlockDate.Text = "定型时间";
            this.olvColumnsetBlockDate.Width = 100;
            // 
            // olvColumnAbDays
            // 
            this.olvColumnAbDays.AspectName = "abnormal_days";
            this.olvColumnAbDays.HeaderFont = null;
            this.olvColumnAbDays.Text = "问题天数";
            this.olvColumnAbDays.Width = 70;
            // 
            // olvColumnNormalDays
            // 
            this.olvColumnNormalDays.AspectName = "normal_days";
            this.olvColumnNormalDays.HeaderFont = null;
            this.olvColumnNormalDays.Text = "正常天数";
            this.olvColumnNormalDays.Width = 68;
            // 
            // olvColumnAbEvents
            // 
            this.olvColumnAbEvents.AspectName = "EventCount";
            this.olvColumnAbEvents.HeaderFont = null;
            this.olvColumnAbEvents.Text = "异常事件个数";
            this.olvColumnAbEvents.Width = 93;
            // 
            // olvColumnBlockedEventName
            // 
            this.olvColumnBlockedEventName.AspectName = "EventTypeDes";
            this.olvColumnBlockedEventName.HeaderFont = null;
            this.olvColumnBlockedEventName.Text = "事件名称";
            this.olvColumnBlockedEventName.Width = 120;
            // 
            // olvColumnTestRound
            // 
            this.olvColumnTestRound.AspectName = "round_id";
            this.olvColumnTestRound.HeaderFont = null;
            this.olvColumnTestRound.Text = "测试轮次ID";
            this.olvColumnTestRound.Width = 80;
            // 
            // olvColumnCloseReason
            // 
            this.olvColumnCloseReason.AspectName = "order_seq";
            this.olvColumnCloseReason.HeaderFont = null;
            this.olvColumnCloseReason.Text = "关闭原因";
            this.olvColumnCloseReason.Width = 120;
            // 
            // olvColumnEvaluate_date
            // 
            this.olvColumnEvaluate_date.AspectName = "Evaluate_DateString";
            this.olvColumnEvaluate_date.HeaderFont = null;
            this.olvColumnEvaluate_date.Text = "评估时间";
            this.olvColumnEvaluate_date.Width = 100;
            // 
            // olvColumnevaluate_result
            // 
            this.olvColumnevaluate_result.AspectName = "evaluate_result";
            this.olvColumnevaluate_result.HeaderFont = null;
            this.olvColumnevaluate_result.Text = "评估结果";
            this.olvColumnevaluate_result.Width = 200;
            // 
            // olvColumnCenterLat
            // 
            this.olvColumnCenterLat.AspectName = "latitude";
            this.olvColumnCenterLat.HeaderFont = null;
            this.olvColumnCenterLat.Text = "中心点纬度";
            this.olvColumnCenterLat.Width = 133;
            // 
            // olvColumnCenterLong
            // 
            this.olvColumnCenterLong.AspectName = "longitude";
            this.olvColumnCenterLong.HeaderFont = null;
            this.olvColumnCenterLong.Text = "中心点经度";
            this.olvColumnCenterLong.Width = 122;
            // 
            // olvColumnPlaceDes
            // 
            this.olvColumnPlaceDes.AspectName = "placeDes";
            this.olvColumnPlaceDes.HeaderFont = null;
            this.olvColumnPlaceDes.Text = "位置描述";
            this.olvColumnPlaceDes.Width = 181;
            // 
            // olvColumnReason
            // 
            this.olvColumnReason.AspectName = "reason";
            this.olvColumnReason.HeaderFont = null;
            this.olvColumnReason.Text = "来源";
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.xtraTabControl1.Location = new System.Drawing.Point(1, 33);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1094, 412);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage3});
            // 
            // rabEventLayer
            // 
            this.rabEventLayer.AutoSize = true;
            this.rabEventLayer.Location = new System.Drawing.Point(965, 9);
            this.rabEventLayer.Name = "rabEventLayer";
            this.rabEventLayer.Size = new System.Drawing.Size(109, 18);
            this.rabEventLayer.TabIndex = 5;
            this.rabEventLayer.Text = "显示事件点图层";
            this.rabEventLayer.UseVisualStyleBackColor = true;
            // 
            // rabProblemLayer
            // 
            this.rabProblemLayer.AutoSize = true;
            this.rabProblemLayer.Checked = true;
            this.rabProblemLayer.Location = new System.Drawing.Point(839, 9);
            this.rabProblemLayer.Name = "rabProblemLayer";
            this.rabProblemLayer.Size = new System.Drawing.Size(109, 18);
            this.rabProblemLayer.TabIndex = 4;
            this.rabProblemLayer.TabStop = true;
            this.rabProblemLayer.Text = "显示问题点图层";
            this.rabProblemLayer.UseVisualStyleBackColor = true;
            this.rabProblemLayer.CheckedChanged += new System.EventHandler(this.rabProblemLayer_CheckedChanged);
            // 
            // StructProblemBlockForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1094, 449);
            this.Controls.Add(this.rabEventLayer);
            this.Controls.Add(this.rabProblemLayer);
            this.Controls.Add(this.xtraTabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "StructProblemBlockForm";
            this.Text = "问题点列表";
            this.ctxMenu.ResumeLayout(false);
            this.xtraTabPage3.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.filterObjectListView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportBBReport;
        private System.Windows.Forms.ToolStripMenuItem miExportSelectedBBReport;
        private System.Windows.Forms.ToolStripMenuItem ToolMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private System.Windows.Forms.ListView problemSummarryListView;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ColumnHeader columnHeader7;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private BrightIdeasSoftware.FilterObjectListView filterObjectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnID;
        private BrightIdeasSoftware.OLVColumn olvColumnReason;
        private BrightIdeasSoftware.OLVColumn olvColumnAbDays;
        private BrightIdeasSoftware.OLVColumn olvColumnNormalDays;
        private BrightIdeasSoftware.OLVColumn olvColumnAbEvents;
        private BrightIdeasSoftware.OLVColumn olvColumnStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnCenterLong;
        private BrightIdeasSoftware.OLVColumn olvColumnCenterLat;
        private BrightIdeasSoftware.OLVColumn olvColumnPlaceDes;
        private BrightIdeasSoftware.OLVColumn olvColumnCreateDate;
        private BrightIdeasSoftware.OLVColumn olvColumnCloseDate;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstAbDate;
        private BrightIdeasSoftware.OLVColumn olvColumnLastAbDate;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTestDate;
        private BrightIdeasSoftware.OLVColumn olvColumnsetBlockDate;
        private BrightIdeasSoftware.OLVColumn olvColumnBlockedEventName;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private BrightIdeasSoftware.OLVColumn olvColumnTestRound;
        private BrightIdeasSoftware.OLVColumn olvColumnCloseReason;
        private BrightIdeasSoftware.OLVColumn olvColumnEvaluate_date;
        private BrightIdeasSoftware.OLVColumn olvColumnevaluate_result;
        private System.Windows.Forms.ToolStripMenuItem 问题点信息详情ToolStripMenuItem;
        private System.Windows.Forms.RadioButton rabEventLayer;
        private System.Windows.Forms.RadioButton rabProblemLayer;
    }
}