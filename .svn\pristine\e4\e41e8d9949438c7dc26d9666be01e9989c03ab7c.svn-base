﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MapWinGIS;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePCIOptimizeInfoForm : BaseFormStyle
    {
        private List<ArrangeResult> retCellList;
        private List<SINRToPCI> sinrList;

        private System.Windows.Forms.Label lbStart;
        private System.Windows.Forms.Label lbEnd;
        private DateTimePicker startTime;
        private DateTimePicker endTime;

        public LtePCIOptimizeInfoForm(MainModel mainModel)
        {
            retCellList = new List<ArrangeResult>();
            sinrList = new List<SINRToPCI>();
            InitializeComponent();
            xtraTabPageFilter.PageVisible = false;

            mainModel.MainForm.GetMapForm().GetLTECellLayer().DrawCellNewPCI = true;

            addTimeToolStrip();
        }

        private void addTimeToolStrip()
        {
            lbStart = new System.Windows.Forms.Label();
            lbStart.Text = "开始时间：";
            lbEnd = new System.Windows.Forms.Label();
            lbEnd.Text = "结束时间：";
            startTime = new DateTimePicker();
            endTime = new DateTimePicker();
            DateTime dtNow = DateTime.Now.Date;
            startTime.Value = dtNow.AddMonths(-1);
            endTime.Value = dtNow.AddDays(1).AddMilliseconds(-1);
            toolStrip1.Items.Insert(1, new ToolStripControlHost(endTime));
            toolStrip1.Items.Insert(1, new ToolStripControlHost(lbEnd));
            toolStrip1.Items.Insert(1, new ToolStripControlHost(startTime));
            toolStrip1.Items.Insert(1, new ToolStripControlHost(lbStart));
        }

        private void initCombobox(List<ArrangeResult> retCellList)
        {
            comboBoxCell.Items.Clear();
            comboBoxCell.Items.Add("所有小区");
            foreach (ArrangeResult aresult in retCellList)
            {
                comboBoxCell.Items.Add(aresult.cellname);
            }
            comboBoxCell.SelectedIndex = 0;
        }


        private void comboBoxCell_SelectedIndexChanged(object sender, EventArgs e)
        {
            string cellname = comboBoxCell.SelectedItem as string;
            if (cellname == null || cellname == "") return;
            refreshData(cellname);
        }

        private void refreshAll()
        {
            int sn = 0;
            foreach (SINRToPCI sinr in sinrList)
            {
                sinr.Sn = ++sn;
            }
            gridControlPCI.DataSource = sinrList;
            gridControlPCI.RefreshDataSource();
            refreshTextBox(sinrList);
            refreshLayer(null);
        }

        private void refreshTextBox(List<SINRToPCI> sList)
        {
            int before = 0, after = 0, addNum = 0, removeNum = 0, unchangeNum = 0;
            foreach (SINRToPCI sinr in sList)
            {
                if (sinr.stage.Contains("优化前"))
                {
                    before++;
                }
                if (sinr.stage.Contains("优化后"))
                {
                    after++;
                }
                switch (sinr.Stage)
                {
                    case "新增":
                        addNum++;
                        break;
                    case "消除":
                        removeNum++;
                        break;
                    case "不变":
                        unchangeNum++;
                        break;
                    default:
                        break;
                }
            }
            textBoxBefore.Text = before.ToString();
            textBoxAfter.Text = after.ToString();
            textBoxAdd.Text = addNum.ToString();
            textBoxRemove.Text = removeNum.ToString();
            textBoxUnchange.Text = unchangeNum.ToString();
        }

        private void refreshData(string name)
        {
            ArrangeResult arSelected = null;
            if (name == "所有小区")
            {
                refreshAll();
                return;
            }
            foreach (ArrangeResult aresult in retCellList)
            {
                if (aresult.cellname == name)
                {
                    arSelected = aresult;
                    break;
                }
            }
            List<SINRToPCI> sLst = new List<SINRToPCI>();
            if (arSelected != null)
            {
                sLst = arSelected.GetShowSinr(startTime.Value, endTime.Value);
            }
            int sn = 0;
            foreach (SINRToPCI sinr in sLst)
            {
                sinr.Sn = ++sn;
            }
            gridControlPCI.DataSource = sLst;
            gridControlPCI.RefreshDataSource();
            refreshTextBox(sLst);
        }

        private void toolStripBtnTimeFilter_Click(object sender, EventArgs e)
        {
            FillData(retCellList, true);
        }

        public void FillData(List<ArrangeResult> retCellList, bool bFilter)
        {
            this.retCellList = retCellList;
            gridControlCell.DataSource = retCellList;
            gridControlCell.RefreshDataSource();
            sinrList.Clear();
            foreach (ArrangeResult aresult in retCellList)
            {
                if (bFilter)
                    sinrList.AddRange(aresult.GetShowSinr(startTime.Value, endTime.Value));
                else
                    sinrList.AddRange(aresult.SinrList);
            }
            initCombobox(retCellList);

            refreshSummary(retCellList);
        }

        private void refreshSummary(List<ArrangeResult> retCellList)
        {
            Dictionary<string, PciCellSummary> nameSummaryDic = new Dictionary<string, PciCellSummary>();
            foreach (ArrangeResult result in retCellList)
            {
                foreach (SINRToPCI sinr in result.GetShowSinr(startTime.Value, endTime.Value))
                {
                    addSummary(sinr, nameSummaryDic);
                }
            }
            gridControlSummary.DataSource = new List<PciCellSummary>(nameSummaryDic.Values);
            gridControlSummary.RefreshDataSource();
        }

        private void addSummary(SINRToPCI sinr, Dictionary<string, PciCellSummary> nameSummaryDic)
        {
            if (sinr.beforenbcellname == sinr.afternbcellname)
            {
                addSummary(sinr.cellname, sinr.beforenbcellname, sinr, nameSummaryDic);
            }
            else
            {
                addSummary(sinr.cellname, sinr.beforenbcellname, sinr, nameSummaryDic);
                addSummary(sinr.cellname, sinr.afternbcellname, sinr, nameSummaryDic);
            }
        }

        private void addSummary(string cellname, string nbcellname, SINRToPCI sinr, Dictionary<string, PciCellSummary> nameSummaryDic)
        {
            if (nbcellname == "") return;

            PciCellSummary summary;
            string key = string.Format("{0}-{1}", cellname, nbcellname);
            if (!nameSummaryDic.TryGetValue(key, out summary))
            {
                summary = new PciCellSummary(cellname, nbcellname);
                nameSummaryDic[key] = summary;
            }
            summary.AddSinr(sinr);
        }

        private void gridView2_DoubleClick(object sender, EventArgs e)
        {
            refreshSummary(retCellList);
            if (mainModel.SelectedLTECells == null)
                mainModel.SelectedLTECells = new List<LTECell>();
            mainModel.SelectedLTECells.Clear();
            int[] hRows = gridView2.GetSelectedRows();
            if (hRows.Length <= 0) return;
            ArrangeResult aResult = gridView2.GetRow(hRows[0]) as ArrangeResult;
            if (aResult == null) return;
            
            mainModel.SelectedLTECells.Add(aResult.mainCell);
            foreach (SINRToPCI sinr in aResult.GetShowSinr(startTime.Value, endTime.Value))
            {
                mainModel.SelectedLTECells.Add(sinr.GetNBCell());
            }
            refreshLayer(aResult);
            //mainModel.MainForm.GetMapForm().GoToView(aResult.mainCell.Longitude, aResult.mainCell.Latitude);
        }

        private void gridView3_Click(object sender, EventArgs e)
        {
            if (mainModel.SelectedLTECells == null)
                mainModel.SelectedLTECells = new List<LTECell>();
            mainModel.SelectedLTECells.Clear();
            int[] hRows = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetSelectedRows();
            if (hRows.Length <= 0) return;
            SINRToPCI sinr = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetRow(hRows[0]) as SINRToPCI;
            if (sinr == null) return;

            mainModel.SelectedLTECells.Add(sinr.mainCell);
            mainModel.SelectedLTECells.Add(sinr.GetNBCell());
           // mainModel.MainForm.GetMapForm().GoToView(sinr.longitude, sinr.latitude);
            refreshLayer(sinr);
        }

        private void gridView3_DoubleClick(object sender, EventArgs e)
        {
            //
        }

        private void gridView3_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            DevExpress.Utils.AppearanceDefault appRed = new DevExpress.Utils.AppearanceDefault(
                Color.Black, Color.Red, Color.Empty, Color.SeaShell, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);
            DevExpress.Utils.AppearanceDefault appGreen = new DevExpress.Utils.AppearanceDefault(
                Color.Black, Color.Green, Color.Empty, Color.SeaShell, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);
            DevExpress.Utils.AppearanceDefault appYellow = new DevExpress.Utils.AppearanceDefault(
                Color.Black, Color.Yellow, Color.Empty, Color.SeaShell, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);
            switch (e.CellValue.ToString())
            {
                case "不变":
                        DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
                    break;
                case "消除":
                    DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appGreen);
                    break;
                case "新增":
                    DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appYellow);
                    break;
                default:
                    break;
            }
        }

        private void refreshLayer(object obj)
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            
            if (mf != null)
            {
                PCIOptimizeLayer cLayer = mf.GetCustomLayer(typeof(PCIOptimizeLayer)) as PCIOptimizeLayer;
                if (cLayer == null)
                {
                    cLayer = new PCIOptimizeLayer(mf.GetMapOperation(), "PCI优化图层");
                    mf.AddTempCustomLayer(cLayer);
                }

                cLayer.Fill(obj);
                cLayer.FillPnts(sinrList);
                cLayer.Invalidate();
            }
            MainModel.RefreshLegend();//更新图例
        }

        private void ToolStripMenuItemDrawAllSinr_Click(object sender, EventArgs e)
        {
            refreshLayer(sinrList);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                List<DevExpress.XtraGrid.Views.Grid.GridView> gvs = new List<DevExpress.XtraGrid.Views.Grid.GridView>();
                gvs.Add(gridView2);
                List<string> sheetNames = new List<string>();
                sheetNames.Add("小区信息");
                ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
            }
            catch
            {
                MessageBox.Show("导出到Xls失败");
            }
        }

        private void ToolStripMenuItemRefreshAll_Click(object sender, EventArgs e)
        {
            refreshLayer(sinrList);
        }

        private void ToolStripMenuItemExportSinr_Click(object sender, EventArgs e)
        {
            try
            {
                List<DevExpress.XtraGrid.Views.Grid.GridView> gvs = new List<DevExpress.XtraGrid.Views.Grid.GridView>();
                gvs.Add(gridView3);
                List<string> sheetNames = new List<string>();
                sheetNames.Add("质差点信息");
                ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
            }
            catch
            {
                MessageBox.Show("导出到Xls失败");
            }
        }

        private void ToolStripMenuItemExportShp_Click(object sender, EventArgs e)
        {
            if (sinrList.Count <= 0) return;

            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "保存shp图层文件";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                exportShpFile(saveFileDlg.FileName);
            }
        }

        private void exportShpFile(string filename)
        {
            Shapefile shpFile = new Shapefile();
            try
            {
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
                if (!result)
                {
                    System.Windows.Forms.MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return;
                }
                int idIdx = 0;
                int fiColor = idIdx++;
                int fiLongitude = idIdx++;
                int fiLatitude = idIdx;
                ShapeHelper.InsertNewField(shpFile, "质差点颜色", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
                ShapeHelper.InsertNewField(shpFile, "质差点经度", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                ShapeHelper.InsertNewField(shpFile, "质差点纬度", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                int numShp = 0;
                foreach (SINRToPCI sinr in sinrList)
                {
                    MapWinGIS.Shape shp = new MapWinGIS.Shape();
                    shp.Create(ShpfileType.SHP_POINT);
                    shp.AddPoint(sinr.longitude, sinr.latitude);
                    shpFile.EditInsertShape(shp, ref numShp);
                    shpFile.EditCellValue(fiColor, numShp, ColorTranslator.ToOle(((SolidBrush)sinr.GetBrush()).Color));
                    shpFile.EditCellValue(fiLongitude, numShp, sinr.longitude);
                    shpFile.EditCellValue(fiLatitude, numShp, sinr.latitude);
                }
                ShapeHelper.DeleteShpFile(filename);
                if (!shpFile.SaveAs(filename, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                shpFile.Close();
            }
        }

        private void ToolStripMenuItemReplay_Click(object sender, EventArgs e)
        {
            int[] hRows = gridView3.GetSelectedRows();
            if (hRows.Length <= 0) return;
            SINRToPCI sinr = gridView3.GetRow(hRows[0]) as SINRToPCI;
            if (sinr == null) return;

            SearchFileByFileID query = new SearchFileByFileID(MainModel.GetInstance());
            query.SetSinr(sinr);
            query.Query();
            FileInfo file = query.fileInfo;
            if (file != null)
            {
                MasterCom.RAMS.Net.DIYReplayFileQuery qry = new MasterCom.RAMS.Net.DIYReplayFileQuery(MainModel.GetInstance());
                QueryCondition codition = new QueryCondition();
                codition.DistrictID = MainModel.DistrictID;
                codition.FileInfos.Add(file);
                qry.SetQueryCondition(codition);
                qry.Query();
            }
        }

        private void contextMenuStripSinr_Opening(object sender, CancelEventArgs e)
        {
            int[] hRows = gridView3.GetSelectedRows();
            if (hRows.Length <= 0)
            {
                ToolStripMenuItemReplay.Enabled = false;
                return;
            }
            SINRToPCI sinr = gridView3.GetRow(hRows[0]) as SINRToPCI;
            if (sinr == null)
            {
                ToolStripMenuItemReplay.Enabled = false;
                return;
            }
            ToolStripMenuItemReplay.Enabled = true;
        }

        private void toolStripButtonFilter_Click(object sender, EventArgs e)
        {
            filter();
            refreshLV();
            xtraTabPageFilter.PageVisible = true;
        }

        private Dictionary<string, PCIOptimizeBtsInfo> nameBtsDic;
        private List<PCIOptimizeBtsInfo> btsInfoList;
        private void filter()
        {
            nameBtsDic = new Dictionary<string, PCIOptimizeBtsInfo>();
            btsInfoList = new List<PCIOptimizeBtsInfo>();
            foreach (ArrangeResult ar in retCellList)
            {
                string btsName = ar.BtsName;
                PCIOptimizeBtsInfo btsInfo;
                if (!nameBtsDic.TryGetValue(btsName, out btsInfo))
                {
                    btsInfo = new PCIOptimizeBtsInfo(btsName);
                    nameBtsDic.Add(btsName, btsInfo);
                    btsInfoList.Add(btsInfo);
                }
                btsInfo.AddRange(ar.GetShowSinr(startTime.Value, endTime.Value));
                btsInfo.AddCells(ar.mainCell);
            }
        }

        private void refreshLV()
        {
            int idx = 1;
            listViewBtsInfo.Items.Clear();
            foreach (PCIOptimizeBtsInfo btsInfo in btsInfoList)
            {
                if (btsInfo.SinrList.Count >= (int)spinEditSampleNum.Value &&
                    btsInfo.ScoreMean >= (double)spinEditScoreMean.Value)
                {
                    btsInfo.Sn = idx++;
                    ListViewItem lvi = new ListViewItem();
                    lvi.Tag = btsInfo;
                    lvi.Text = btsInfo.Sn.ToString();
                    lvi.SubItems.Add(btsInfo.BtsName);
                    lvi.SubItems.Add(btsInfo.SinrList.Count.ToString());
                    lvi.SubItems.Add(btsInfo.ScoreMean.ToString());

                    listViewBtsInfo.Items.Add(lvi);
                }
            }
        }

        private void spinEditSampleNum_ValueChanged(object sender, EventArgs e)
        {
            refreshLV();
        }

        private void spinEditScoreMean_ValueChanged(object sender, EventArgs e)
        {
            refreshLV();
        }

        private void simpleButtonOptimize_Click(object sender, EventArgs e)
        {
            if (listViewBtsInfo.Items.Count <= 0)
            {
                MessageBox.Show("列表为空，没有可再次优化的小区", "提示");
                return;
            }
            List<LTECell> lteCells = new List<LTECell>();
            foreach (ListViewItem lvi in listViewBtsInfo.Items)
            {
                PCIOptimizeBtsInfo bts = lvi.Tag as PCIOptimizeBtsInfo;
                lteCells.AddRange(bts.BelongCells);
            }
            this.Close();
            LtePCIOptimizeQuery query = new LtePCIOptimizeQuery(mainModel);
            query.SetQueryCondition(mainModel.QueryCondition);
            query.SetRegionCell(lteCells);
            query.Query();
        }
    }

    public class PCIOptimizeBtsInfo
    {
        public int Sn { get; set; }
        public string BtsName { get; set; }
        public List<SINRToPCI> SinrList { get; set; }
        public List<LTECell> BelongCells { get; set; }

        public PCIOptimizeBtsInfo(string btsName)
        {
            this.BtsName = btsName;
            this.SinrList = new List<SINRToPCI>();
            this.BelongCells = new List<LTECell>();
        }

        public void AddRange(List<SINRToPCI> sinrLst)
        {
            SinrList.AddRange(sinrLst);
        }

        public void AddCells(LTECell cell)
        {
            if (cell != null && !BelongCells.Contains(cell))
                BelongCells.Add(cell);
        }

        public int ScoreTotal
        {
            get
            {
                int score = 0;
                foreach (SINRToPCI sinr in SinrList)
                {
                    switch (sinr.Stage)
                    {
                        case "不变":
                            score += 0;
                            break;
                        case "消除":
                            score += 1;
                            break;
                        case "新增":
                            score -= 1;
                            break;
                        default:
                            break;
                    }
                }
                return score;
            }
        }

        public double ScoreMean
        {
            get
            {
                if (SinrList.Count <= 0)
                    return double.NaN;
                return Math.Round(1.0 * ScoreTotal / SinrList.Count, 2);
            }
        }
    }

    public class PciCellSummary
    {
        public string MainCellName { get; set; }

        public string NbCellName { get; set; }

        public int SinrNumAdd { get; set; }

        public int SinrNumRemove { get; set; }

        public int SinrNumUnChange { get; set; }

        public List<SINRToPCI> SinrVec { get; set; }

        public PciCellSummary(string mainCell, string nbCell)
        {
            this.MainCellName = mainCell;
            this.NbCellName = nbCell;

            SinrNumAdd = SinrNumRemove = SinrNumUnChange = 0;
            SinrVec = new List<SINRToPCI>();
        }

        public void AddSinr(SINRToPCI sinr)
        {
            SinrVec.Add(sinr);

            if (NbCellName == sinr.beforenbcellname &&
                NbCellName == sinr.afternbcellname)
                SinrNumUnChange++;
            else if (NbCellName == sinr.beforenbcellname)
                SinrNumRemove++;
            else
                SinrNumAdd++;
        }
    }
}
