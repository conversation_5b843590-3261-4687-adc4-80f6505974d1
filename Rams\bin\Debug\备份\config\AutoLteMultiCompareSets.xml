<?xml version="1.0"?>
<Configs>
  <Config name="MultiCompareCfg">
    <Item name="Set" typeName="IDictionary">
      <Item typeName="IList" key="Periods">
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635241312000000000</Item>
          <Item typeName="String" key="EndTime">635268095999990000</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635268096000000000</Item>
          <Item typeName="String" key="EndTime">635319071999990000</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635319072000000000</Item>
          <Item typeName="String" key="EndTime">635344991999990000</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635344992000000000</Item>
          <Item typeName="String" key="EndTime">635424479999990000</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635812416000000000</Item>
          <Item typeName="String" key="EndTime">635899679999990000</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="MsgParamList">
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">LongDrxCycle</Item>
          <Item typeName="String" key="ParamRightValue">7</Item>
          <Item typeName="String" key="ParamRightValueDes">SF160</Item>
          <Item typeName="Boolean" key="IsChecked">True</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">OnDurationTimer</Item>
          <Item typeName="String" key="ParamRightValue">6</Item>
          <Item typeName="String" key="ParamRightValueDes">PSF8</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">DrxInactivityTimer</Item>
          <Item typeName="String" key="ParamRightValue">12</Item>
          <Item typeName="String" key="ParamRightValueDes">PSF60</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">DrxRetransmissionTimer</Item>
          <Item typeName="String" key="ParamRightValue">2</Item>
          <Item typeName="String" key="ParamRightValueDes">PSF4</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">ShortDrxCycle</Item>
          <Item typeName="String" key="ParamRightValue">5</Item>
          <Item typeName="String" key="ParamRightValueDes">SF20</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">DrxShortCycleTimer</Item>
          <Item typeName="String" key="ParamRightValue">2</Item>
          <Item typeName="String" key="ParamRightValueDes">2</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">DefaultPagingCycle</Item>
          <Item typeName="String" key="ParamRightValue">2</Item>
          <Item typeName="String" key="ParamRightValueDes">rf128</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">Reference Signal Power</Item>
          <Item typeName="String" key="ParamRightValue">15</Item>
          <Item typeName="String" key="ParamRightValueDes">15dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">Pb</Item>
          <Item typeName="String" key="ParamRightValue">1;0</Item>
          <Item typeName="String" key="ParamRightValueDes">1(室分0)</Item>
          <Item typeName="Boolean" key="IsChecked">True</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">Pa</Item>
          <Item typeName="String" key="ParamRightValue">2;4</Item>
          <Item typeName="String" key="ParamRightValueDes">-3(室分0)</Item>
          <Item typeName="Boolean" key="IsChecked">True</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">PreambleInitial ReceivedTargetPower</Item>
          <Item typeName="String" key="ParamRightValue">8～12</Item>
          <Item typeName="String" key="ParamRightValueDes">-100dBm～-104dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">PreambleTransMax</Item>
          <Item typeName="String" key="ParamRightValue">5,6</Item>
          <Item typeName="String" key="ParamRightValueDes">n8，n10</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">PowerRampingStep</Item>
          <Item typeName="String" key="ParamRightValue">1,2</Item>
          <Item typeName="String" key="ParamRightValueDes">dB2，dB4</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">P-max</Item>
          <Item typeName="String" key="ParamRightValue">23</Item>
          <Item typeName="String" key="ParamRightValueDes">23dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">p0-NominalPUCCH</Item>
          <Item typeName="String" key="ParamRightValue">-100～-105</Item>
          <Item typeName="String" key="ParamRightValueDes"> -100dBm～-105dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">Alpha</Item>
          <Item typeName="String" key="ParamRightValue">5</Item>
          <Item typeName="String" key="ParamRightValueDes">0.8</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">P0NominalPusch</Item>
          <Item typeName="String" key="ParamRightValue">-87</Item>
          <Item typeName="String" key="ParamRightValueDes">-87dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">QRxLevMin</Item>
          <Item typeName="String" key="ParamRightValue">-63</Item>
          <Item typeName="String" key="ParamRightValueDes">-126dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">s-IntraSearch</Item>
          <Item typeName="String" key="ParamRightValue">-35</Item>
          <Item typeName="String" key="ParamRightValueDes">-70dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">q-OffsetCell + q-Hyst</Item>
          <Item typeName="String" key="ParamRightValue">2～4</Item>
          <Item typeName="String" key="ParamRightValueDes">2～4dB</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">同频 A3 offset + Hysteresis</Item>
          <Item typeName="String" key="ParamRightValue">2～4</Item>
          <Item typeName="String" key="ParamRightValueDes">2～4dB</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">同频 A3 Time-to-trigger</Item>
          <Item typeName="String" key="ParamRightValue">8</Item>
          <Item typeName="String" key="ParamRightValueDes">320ms</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="ParamNameDes">异系统A2门限(盲重定向）</Item>
          <Item typeName="String" key="ParamRightValue">14～22</Item>
          <Item typeName="String" key="ParamRightValueDes">-118dBm～-126dBm</Item>
          <Item typeName="Boolean" key="IsChecked">False</Item>
        </Item>
      </Item>
      <Item typeName="String" key="ReprortSavePath">C:\Users\<USER>\Desktop</Item>
      <Item typeName="Boolean" key="IsFilterFile">False</Item>
      <Item typeName="String" key="StrFileNameFilter">下</Item>
      <Item typeName="Double" key="DistanceEvt">51</Item>
      <Item typeName="Boolean" key="IsAnaMessageCellInfo">False</Item>
      <Item typeName="IList" key="SelectEvtIDSet">
        <Item typeName="Int32">1008</Item>
        <Item typeName="Int32">1009</Item>
        <Item typeName="Int32">1275</Item>
        <Item typeName="Int32">897</Item>
        <Item typeName="Int32">1242</Item>
        <Item typeName="Int32">1250</Item>
        <Item typeName="Int32">1134</Item>
        <Item typeName="Int32">871</Item>
        <Item typeName="Int32">872</Item>
        <Item typeName="Int32">1169</Item>
        <Item typeName="Int32">1172</Item>
        <Item typeName="Int32">1245</Item>
        <Item typeName="Int32">1107</Item>
        <Item typeName="Int32">1319</Item>
        <Item typeName="Int32">1109</Item>
        <Item typeName="Int32">1132</Item>
        <Item typeName="Int32">1274</Item>
        <Item typeName="Int32">1272</Item>
        <Item typeName="Int32">1078</Item>
        <Item typeName="Int32">1079</Item>
        <Item typeName="Int32">1092</Item>
        <Item typeName="Int32">1093</Item>
      </Item>
      <Item typeName="IList" key="DistrictIDSet" />
      <Item typeName="IList" key="ProjectIDSet">
        <Item typeName="Int32">4</Item>
        <Item typeName="Int32">3</Item>
        <Item typeName="Int32">14</Item>
        <Item typeName="Int32">15</Item>
        <Item typeName="Int32">16</Item>
        <Item typeName="Int32">17</Item>
        <Item typeName="Int32">18</Item>
        <Item typeName="Int32">19</Item>
        <Item typeName="Int32">22</Item>
        <Item typeName="Int32">25</Item>
        <Item typeName="Int32">9</Item>
        <Item typeName="Int32">24</Item>
        <Item typeName="Int32">20</Item>
        <Item typeName="Int32">5</Item>
        <Item typeName="Int32">2</Item>
        <Item typeName="Int32">1</Item>
        <Item typeName="Int32">8</Item>
        <Item typeName="Int32">11</Item>
        <Item typeName="Int32">13</Item>
        <Item typeName="Int32">23</Item>
        <Item typeName="Int32">21</Item>
        <Item typeName="Int32">12</Item>
        <Item typeName="Int32">6</Item>
        <Item typeName="Int32">7</Item>
        <Item typeName="Int32">10</Item>
      </Item>
      <Item typeName="IList" key="ServiceIDSet">
        <Item typeName="Int32">34</Item>
        <Item typeName="Int32">33</Item>
        <Item typeName="Int32">43</Item>
      </Item>
    </Item>
  </Config>
</Configs>