﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.KPISheet_HaiNan
{
    public class QueryKPIByTemplate : DIYSQLBase
    {
        readonly ReportTemplate template;
        readonly string testTasStr;
        public QueryKPIByTemplate(ReportTemplate template, IEnumerable<TestTag> testTagSet)
            : base(MainModel.GetInstance())
        {
            this.template = template;
            StringBuilder sb = new StringBuilder();
            foreach (TestTag tt in testTagSet)
            {
                if (sb.Length > 0)
                {
                    sb.Append(",'");
                }
                else
                {
                    sb.Append("'");
                }
                sb.Append(tt.Name);
                sb.Append("'");
            }
            testTasStr = sb.ToString();
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string tbName = null;
            StringBuilder sbFields = new StringBuilder();
            foreach (ReportColunm col in curReportColSet)
            {
                if (tbName == null)
                {
                    tbName = col.TableCol.Table.Name;
                } 
                sbFields.Append(",[");
                sbFields.Append(col.TableCol.Name);
                sbFields.Append("]");
            }
            return "select [testTag],[areaName]" + sbFields.ToString()
                + " from [KPIMNG_DB_HaiNan].[dbo]." + tbName + " where testTag in(" + testTasStr + ")";
        }

        List<ReportColunm> curReportColSet = null;
        protected override void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            try
            {
                foreach (TableTemplate tb in template.TableColDic.Keys)
                {
                    if (tb==TableCfgManager.Instance.AreaTable)
                    {
                        continue;
                    }
                    curReportColSet = template.TableColDic[tb];
                    string strsql = getSqlTextString();
                    E_VType[] retArrDef = getSqlRetTypeArr();
                    package.Command = Command.DIYSearch;
                    package.SubCommand = SubCommand.Request;
                    if (MainDB)
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                    }
                    else
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            sb.Append(",");
                        }
                    }

                    package.Content.AddParam(sb.ToString().TrimEnd(','));
                    clientProxy.Send();
                    receiveRetData(clientProxy);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                createResultTb();
            }
        }

        public DataTable DataTable
        {
            get;
            private set;
        }

        private void createResultTb()
        {
            DataTable = new DataTable();
            foreach (ReportColunm col in template.Columns)
            {
                if (col.TableCol.ValueType==E_VType.E_String)
                {
                    DataTable.Columns.Add(col.Name, typeof(string));
                }
                else
                {
                    DataTable.Columns.Add(col.Name, typeof(object));
                }
            }
            List<RecordRow> rows = new List<RecordRow>(recordDic.Values);
            rows.Sort();
            foreach (RecordRow record in rows)
            {
                DataRow row = DataTable.NewRow();
                DataTable.Rows.Add(row);
                foreach (ReportColunm col in template.Columns)
                {
                    if (col.TableCol.Table == TableCfgManager.Instance.AreaTable)
                    {
                        row[col.Name] = "市县";
                    }
                    else
                    {
                        row[col.Name] = record[col];
                    }
                }
            }
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] ret = new E_VType[2+curReportColSet.Count];
            int i = 0;
            ret[i++] = E_VType.E_String;
            ret[i++] = E_VType.E_String;
            foreach (ReportColunm col in curReportColSet)
            {
                ret[i++] = col.TableCol.ValueType;
            }
            return ret;
        }

        private readonly Dictionary<string, RecordRow> recordDic = new Dictionary<string, RecordRow>();

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            string testTag = package.Content.GetParamString();
            string areaName = package.Content.GetParamString();
            RecordRow row = new RecordRow(testTag, areaName);
            if (recordDic.ContainsKey(row.Key))
            {
                row = recordDic[row.Key];
            }
            else
            {
                recordDic[row.Key] = row;
            }
            foreach (ReportColunm col in curReportColSet)
            {
                if (col.TableCol.ValueType == E_VType.E_String)
                {
                    row[col] = package.Content.GetParamString();
                }
                else if (col.TableCol.ValueType == E_VType.E_Int)
                {
                    float v = package.Content.GetParamInt();
                    if (v != -255)
                    {
                        row[col] = v;
                    }
                }
                else
                {
                    float v = package.Content.GetParamFloat();
                    if (v != -255)
                    {
                        row[col] = v;
                    }
                }
            }
        }

    }

    public class RecordRow : IComparable<RecordRow>
    {
        private readonly string testTag;
        private readonly string areaName; 

        public TestTag TestTag
        {
            get;
            private set;
        }

        public RecordRow(string testTag, string areaName)
        {
            this.testTag = testTag;
            this.areaName = areaName;
            this.TestTag = TableCfgManager.Instance.GetTestTag(testTag);
        }

        public string Key
        {
            get { return string.Format("{0}&{1}", testTag, areaName); }
        }

        public Dictionary<ReportColunm, object> colValueDic { get; set; } = new Dictionary<ReportColunm, object>();

        public object this[ReportColunm col]
        {
            get
            {
                object ret = null;
                if (colValueDic.TryGetValue(col, out ret))
                {
                    return ret;
                }
                return null;
            }
            set
            {
                colValueDic[col] = value;
            }
        }


        #region IComparable<RecordRow> 成员

        public int CompareTo(RecordRow other)
        {
            return other.TestTag.CompareTo(this.TestTag);
        }

        #endregion
    }

}
