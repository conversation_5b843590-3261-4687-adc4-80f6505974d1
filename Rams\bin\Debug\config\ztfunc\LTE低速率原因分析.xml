<?xml version="1.0"?>
<Configs>
  <Config name="ConditionCfg">
    <Item name="Condition" typeName="IDictionary">
      <Item typeName="Int32" key="sampleCntMin">1</Item>
      <Item typeName="Double" key="distanceMin">0</Item>
      <Item typeName="Double" key="secondMin">0</Item>
      <Item typeName="Boolean" key="checkFTP">False</Item>
      <Item typeName="Double" key="ftpRateMax">10</Item>
      <Item typeName="Boolean" key="checkHTTP">True</Item>
      <Item typeName="Double" key="httpRateMax">2</Item>
      <Item typeName="Boolean" key="checkEmail">False</Item>
      <Item typeName="Double" key="emailRateMax">2</Item>
      <Item typeName="IList" key="CauseSet">
        <Item typeName="IDictionary">
          <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.HoUpdateCause</Item>
          <Item typeName="IList" key="SubCauseSet">
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.HoTooOftenCause</Item>
              <Item typeName="Int32" key="second">10</Item>
              <Item typeName="Int32" key="times">2</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.TAUpdateTooOftenCause</Item>
              <Item typeName="Int32" key="second">10</Item>
              <Item typeName="Int32" key="times">2</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.UnreasonableHoPoorMainRSRP</Item>
              <Item typeName="Int32" key="lastHoSecondDiff">5</Item>
              <Item typeName="Int32" key="beforeSecond">2</Item>
              <Item typeName="Int32" key="afterSecond">2</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.UnreasonableHoGoodNbRSRP</Item>
              <Item typeName="Int32" key="lastHoSecondDiff">5</Item>
              <Item typeName="Int32" key="afterSecond">3</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.HoBehindTime</Item>
              <Item typeName="Single" key="mainRSRPMin">-95</Item>
              <Item typeName="Int32" key="staySecond">5</Item>
              <Item typeName="Single" key="rsrpDiff">10</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.CoverCause</Item>
          <Item typeName="IList" key="SubCauseSet">
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.OutOfService</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WrongCover</Item>
              <Item typeName="Int32" key="angle">60</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WeakCoverCause</Item>
              <Item typeName="Single" key="rsrpMax">-95</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WeakCoverNoSite</Item>
                  <Item typeName="Single" key="radiusMin">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WeakCoverLackNbCell</Item>
                  <Item typeName="Single" key="radiusMin">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WeakCoverSCell</Item>
                  <Item typeName="Single" key="radiusMin">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.WeakCoverNCell</Item>
                  <Item typeName="Single" key="radiusMin">500</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.OverCoverCause</Item>
              <Item typeName="Single" key="rsrpMin">-85</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.OverCoverNoSite</Item>
                  <Item typeName="Single" key="radiusMin">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.OverCoverLackNbCell</Item>
                  <Item typeName="Single" key="radiusMin">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.OverCoverHandoverParam</Item>
                  <Item typeName="Single" key="rsrpDiff">10</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.IndoorLeakCoverCause</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.LeakCoverOverCover</Item>
                  <Item typeName="Double" key="distance">300</Item>
                  <Item typeName="Single" key="rsrpMin">-85</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.LeakCoverHandoverParam</Item>
                  <Item typeName="Int32" key="secondMin">5</Item>
                  <Item typeName="Single" key="rsrpMin">10</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.LeakCoverWeakOutdoor</Item>
                  <Item typeName="Double" key="distanceMin">300</Item>
                  <Item typeName="Single" key="rsrpDiffMax">10</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.MessCoverCause</Item>
              <Item typeName="Single" key="rsrp">6</Item>
              <Item typeName="Int32" key="cellCnt">3</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.MessCoverSCellOverCover</Item>
                  <Item typeName="Single" key="overRatio">1.6</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.MessCoverNCellOverCover</Item>
                  <Item typeName="Single" key="overRatio">1.6</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.MessCoverUnreasonableCover</Item>
                  <Item typeName="Double" key="distance">300</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.UnstabitilyCover</Item>
              <Item typeName="Single" key="rsrpDiff">10</Item>
              <Item typeName="Int32" key="second">5</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.QualCause</Item>
          <Item typeName="IList" key="SubCauseSet">
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorSINRCause</Item>
              <Item typeName="Single" key="sinrMax">6</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorQualMod3Interf</Item>
                  <Item typeName="Single" key="rsrpDiffMax">6</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorQualTDSInterfFFreqBand</Item>
                  <Item typeName="Double" key="distance">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorQualWeakCover</Item>
                  <Item typeName="Single" key="rsrpMax">-85</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorBLERCause</Item>
              <Item typeName="Single" key="blerMin">10</Item>
              <Item typeName="IList" key="SubCauseSet">
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorQualMod3Interf</Item>
                  <Item typeName="Single" key="rsrpDiffMax">6</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorQualTDSInterfFFreqBand</Item>
                  <Item typeName="Double" key="distance">500</Item>
                </Item>
                <Item typeName="IDictionary">
                  <Item typeName="String" key="TypeName">MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PoorQualWeakCover</Item>
                  <Item typeName="Single" key="rsrpMax">-85</Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>