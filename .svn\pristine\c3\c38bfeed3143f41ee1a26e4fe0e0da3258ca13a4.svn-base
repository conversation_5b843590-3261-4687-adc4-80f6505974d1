﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class AreaPKResultForm : MinCloseForm
    {
        private ZTAreaArchiveLayer layer
        {
            get
            {
                ZTAreaArchiveLayer layerTmp = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
                if (layerTmp == null)
                {
                    layerTmp = new ZTAreaArchiveLayer();
                    MainModel.MainForm.GetMapForm().AddLayerBase(layerTmp);
                }
                return layerTmp;
            }
        }

        private List<AreaBase> areas = null;

        private PKCondition pkCond;

        private Dictionary<AreaBase, List<AreaBase>> rootAreaDic;

        Dictionary<AreaBase, bool> areaSelDic;

        private CPkValue pkValue;

        private Dictionary<Alghirithm, List<AreaBase>> algVillageDic;

        private Dictionary<AreaBase, CPkResult> vilPkResultDic;

        public AreaPKResultForm()
            : base()
        {
            InitializeComponent();
            lbxLegend.DrawItem += new DrawItemEventHandler(lbxGis_DrawItem);
            lbxLegend.MouseClick += new MouseEventHandler(lbxGis_MouseClick);

            algVillageDic = new Dictionary<Alghirithm, List<AreaBase>>();
            vilPkResultDic = new Dictionary<AreaBase, CPkResult>();
        }

        public void FillData(PKCondition pkCond,
            CPkValue pkValue)
        {
            this.pkCond = pkCond;
            this.pkValue = pkValue;
            this.rootAreaDic = ArchiveSettingManager.GetInstance().Condition.VillageCondition.RootLeafDic;
            this.areaSelDic = ArchiveSettingManager.GetInstance().Condition.VillageCondition.AreaSelDic;

            areas = new List<AreaBase>();
            foreach (AreaBase root in rootAreaDic.Keys)
            {
                areas.AddRange(rootAreaDic[root]);
            }
            layer.Areas = areas;
            rebuildLVHeader(pkCond);
            WaitBox.Show("正在填充数据...", pkReport);
            refreshLVSummary();
        }

        private void pkReport()
        {
            try
            {
                algVillageDic.Clear();
                vilPkResultDic.Clear();
                treeList.BeginUpdate();
                treeList.Nodes.Clear();
                makeReportColumn(treeList);
                foreach (AreaBase area in rootAreaDic.Keys)
                {
                    if (area.SubAreas == null)
                    {
                        continue;
                    }
                    appendTreeNode(area, null);
                }

                cbxRank.Items.Clear();
                foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
                {
                    cbxRank.Items.Add(rank);
                }
                cbxRank.SelectedItem = ZTAreaManager.Instance.LowestRank;
                treeList.EndUpdate();
                refreshGis();
                refreshLegend();
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        private void makeReportColumn(DevExpress.XtraTreeList.TreeList treeList)
        {
            treeList.Columns.Clear();
            DevExpress.XtraTreeList.Columns.TreeListColumn colFixed = treeList.Columns.Add();
            colFixed.Caption = "区域名称";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Width = 160;
            colFixed.Fixed = FixedStyle.Left;

            colFixed = treeList.Columns.Add();
            colFixed.Caption = "行政级别";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Fixed = FixedStyle.Left;

            DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
            col.Caption = "竞比结果";
            col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            col.OptionsColumn.AllowEdit = false;
            col.OptionsColumn.AllowMoveToCustomizationForm = false;
            col.OptionsColumn.ReadOnly = true;
            col.Visible = true;

            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                col = treeList.Columns.Add();
                col.Caption = car.ToString();
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;
            }
        }

        private void appendTreeNode(AreaBase area, TreeListNode parentNode)
        {
            if (!areaSelDic.ContainsKey(area))
            {
                return;
            }
            double dcm = pkValue.GetCMValue(area);
            double dcu = pkValue.GetCUValue(area);
            double dct = pkValue.GetCTValue(area);
            Alghirithm althm = pkCond.GetAlghirithm(dcm, dcu, dct);
            TreeListNode node = treeList.AppendNode(new object[] { area.Name
                , area.RankName, althm.Name, getText(dcm), getText(dcu), getText(dct) }, parentNode);
            CPkResult pkResult = new CPkResult(area, althm, dcm, dcu, dct);
            node.Tag = pkResult;

            if (area.SubAreas == null)
            {
                List<AreaBase> villages;
                if (!algVillageDic.TryGetValue(althm, out villages))
                {
                    villages = new List<AreaBase>();
                    algVillageDic.Add(althm, villages);
                }
                villages.Add(area);
                vilPkResultDic[area] = pkResult;
            }

            if (area.SubAreas == null) return;

            foreach (AreaBase sub in area.SubAreas)
            {
                appendTreeNode(sub, node);
            }
        }

        private void rebuildLVHeader(PKCondition pkCond)
        {
            listViewSummary.Columns.Clear();
            listViewDetail.Columns.Clear();

            ColumnHeader col = new ColumnHeader();
            col.Text = "地市";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewSummary.Columns.Add(col);
            listViewDetail.Columns.Add(col.Clone() as ColumnHeader);

            col = new ColumnHeader();
            col.Text = "区县";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewSummary.Columns.Add(col);
            listViewDetail.Columns.Add(col.Clone() as ColumnHeader);

            col = new ColumnHeader();
            col.Text = "乡镇";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewSummary.Columns.Add(col);
            listViewDetail.Columns.Add(col.Clone() as ColumnHeader);

            col = new ColumnHeader();
            col.Text = "村庄";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewDetail.Columns.Add(col);

            foreach (Alghirithm thm in pkCond.SelTemplate.AlghirithmVec)
            {
                col = new ColumnHeader();
                col.Text = thm.Name;
                col.Tag = thm;
                col.Width = 120;
                listViewSummary.Columns.Add(col); 
            }

            col = new ColumnHeader();
            col.Text = pkCond.SelTemplate.OtherAlghirithm.Name;
            col.Tag = pkCond.SelTemplate.OtherAlghirithm;
            col.Width = 75;
            listViewSummary.Columns.Add(col);

            col = new ColumnHeader();
            col.Text = "移动指标";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewDetail.Columns.Add(col);

            col = new ColumnHeader();
            col.Text = "联通指标";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewDetail.Columns.Add(col);

            col = new ColumnHeader();
            col.Text = "电信指标";
            col.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            listViewDetail.Columns.Add(col);
        }

        private Dictionary<AreaBase, int> townIdxMap = new Dictionary<AreaBase, int>();
        private void refreshLVSummary()
        {
            listViewSummary.Items.Clear();
            Dictionary<AreaBase, SummaryAlthm> townSummaryMap = new Dictionary<AreaBase, SummaryAlthm>();
            foreach (Alghirithm thm in algVillageDic.Keys)
            {
                foreach (AreaBase village in algVillageDic[thm])
                {
                    AreaBase town = village.ParentArea;
                    SummaryAlthm sumAthm;
                    if (!townSummaryMap.TryGetValue(town, out sumAthm))
                    {
                        sumAthm = new SummaryAlthm(town);
                        sumAthm.FillAlthm(pkCond.SelTemplate);
                        townSummaryMap[town] = sumAthm;
                    }
                    sumAthm.StatAlthm(thm);

                    CPkResult result;
                    if(vilPkResultDic.TryGetValue(village, out result))
                        sumAthm.AddVillageResult(village, result);
                }
            }

            townIdxMap.Clear();
            foreach (SummaryAlthm sumAthm in townSummaryMap.Values)
            {
                townIdxMap[sumAthm.Town] = listViewSummary.Items.Count;

                ListViewItem lvi = new ListViewItem(sumAthm.Town.GetAreaPath());
                lvi.Tag = sumAthm;
                lvi.UseItemStyleForSubItems = false;

                foreach (ColumnHeader head in listViewSummary.Columns)
                {
                    if (head.Tag is Alghirithm)
                    {
                        Alghirithm thm = head.Tag as Alghirithm;
                        System.Windows.Forms.ListViewItem.ListViewSubItem sub = 
                            new System.Windows.Forms.ListViewItem.ListViewSubItem(lvi, Convert.ToString(sumAthm.AlthmCntMap[thm]));
                        sub.BackColor = thm.Color;
                        lvi.SubItems.Add(sub);
                    }
                }
                listViewSummary.Items.Add(lvi);
            }
        }

        private void refreshLegend()
        {
            lbxLegend.Items.Clear();
            foreach (Alghirithm thm in pkCond.SelTemplate.AlghirithmVec)
            {
                CheckAlthm chkItem = new CheckAlthm(thm);
                lbxLegend.Items.Add(chkItem);
            }
            lbxLegend.Items.Add(new CheckAlthm(pkCond.SelTemplate.OtherAlghirithm));

            lbxLegend.Items.Add("\r\n");
            lbxLegend.Items.Add("\r\n");
            lbxLegend.Items.Add(string.Format("移动有效范围：{0}", pkCond.SelTemplate.CMHub.PkBase.ValueRange));
            lbxLegend.Items.Add(string.Format("联通有效范围：{0}", pkCond.SelTemplate.CUHub.PkBase.ValueRange));
            lbxLegend.Items.Add(string.Format("电信有效范围：{0}", pkCond.SelTemplate.CTHub.PkBase.ValueRange));
            lbxLegend.Invalidate();
        }

        private void refreshGis()
        {
            AreaRank rank = cbxRank.SelectedItem as AreaRank;
            if (rank == null)
            {
                return;
            }
            Dictionary<AreaBase, Color> areaColorDic = new Dictionary<AreaBase, Color>();
            foreach (TreeListNode root in treeList.Nodes)
            {
                Color color = Color.Empty;
                getLeafNodeColor(root, rank, color, areaColorDic);
            }
            layer.AreaColorDic = areaColorDic;
            layer.Invalidate();
        }

        private void lbxGis_DrawItem(object sender, System.Windows.Forms.DrawItemEventArgs e)
        {
            System.Windows.Forms.ListBox listBoxLegend = sender as System.Windows.Forms.ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is CheckAlthm)
            {
                text = (item as CheckAlthm).BCheck ? "[√]" : "[  ]";
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y);
                e.Graphics.FillRectangle(new SolidBrush((item as CheckAlthm).Althm.Color), e.Bounds.X + 20, e.Bounds.Y, 16, 16);
                text = (item as CheckAlthm).Althm.Name;
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 40, e.Bounds.Y);
            }
            else if (item is string)
            {
                text = item.ToString();
                e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Red, e.Bounds.X, e.Bounds.Y);
            }
        }

        private void lbxGis_MouseClick(object sender, MouseEventArgs e)
        {
            int idx = lbxLegend.IndexFromPoint(e.Location);
            if (lbxLegend.Items.Count <= idx) return;

            if (lbxLegend.Items[idx] is CheckAlthm)
            {
                CheckAlthm chkItem = lbxLegend.Items[idx] as CheckAlthm;
                chkItem.BCheck = !chkItem.BCheck;
                lbxLegend.Invalidate(false);

                if (!algVillageDic.ContainsKey(chkItem.Althm)) return;

                if (chkItem.BCheck)
                {
                    layer.Areas.AddRange(algVillageDic[chkItem.Althm]);
                }
                else
                {
                    foreach (AreaBase area in algVillageDic[chkItem.Althm])
                    {
                        layer.Areas.Remove(area);
                    }
                }
                layer.Invalidate();
            }
        }

        private void getLeafNodeColor(TreeListNode node, AreaRank rank, Color color, Dictionary<AreaBase, Color> areaColorDic)
        {
            CPkResult pkData = node.Tag as CPkResult;
            if (pkData.Area.Rank == rank)
            {
                color = pkData.Althm.Color;
                areaColorDic[pkData.Area] = color;
            }
            if (node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    getLeafNodeColor(subNode, rank, color, areaColorDic);
                }
            }
            else
            {
                areaColorDic[pkData.Area] = color;
            }
        }

        private string getText(double dValue)
        {
            if (double.IsNaN(dValue))
            {
                return "-";
            }
            return Convert.ToString(dValue);
        }

        private void cbxRank_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshGis();
        }

        private void treeList_NodeCellStyle(object sender, GetCustomNodeCellStyleEventArgs e)
        {
            if (e.Node == null || e.Node.Tag == null)
            {
                return;
            }
            if (e.Column.Caption != "竞比结果")
            {
                return;
            }
            CPkResult pkResult = e.Node.Tag as CPkResult;
            if (pkResult == null)
            {
                return;
            }
            e.Appearance.BackColor = pkResult.Althm.Color;
        }

        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            System.Windows.Forms.MouseEventArgs me = e as System.Windows.Forms.MouseEventArgs;
            TreeListHitInfo info = treeList.CalcHitInfo(me.Location);

            if (info.Node == null)
            {
                return;
            }
            List<AreaBase> selAreas = new List<AreaBase>();
            layer.AreaColorDic.Clear();
            getLeafNode(info.Node, selAreas);
            layer.SelectedAreas = selAreas;
            DbRect bounds;

            double maxX = double.MinValue;
            double maxY = double.MinValue;
            double minX = double.MaxValue;
            double minY = double.MaxValue;
            foreach (AreaBase area in selAreas)
            {
                maxX = Math.Max(maxX, area.Bounds.x2);
                maxY = Math.Max(maxY, area.Bounds.y2);
                minY = Math.Min(minY, area.Bounds.y1);
                minX = Math.Min(minX, area.Bounds.x1);
            }
            bounds = new DbRect(minX, minY, maxX, maxY);
            MainModel.MainForm.GetMapForm().GoToView(bounds);
        }

        private void getLeafNode(TreeListNode node, List<AreaBase> areas)
        {
            if (node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    getLeafNode(subNode, areas);
                }
            }
            else
            {
                CPkResult result = node.Tag as CPkResult;
                areas.Add(result.Area);
                layer.AreaColorDic[result.Area] = result.Althm.Color;
            }
        }

        private AreaBase selVillage = null;
        private void treeList_FocusedNodeChanged(object sender, FocusedNodeChangedEventArgs e)
        {
            selVillage = null;
            CPkResult pkResult = e.Node.Tag as CPkResult;
            if (pkResult == null) return;

            AreaBase area = pkResult.Area;
            if (area.Rank == ZTAreaManager.Instance.LowestRank)
            {
                selVillage = area;
                listViewSummary_SelectedIndexChanged(null, null);
            }
            else if (area.Rank == ZTAreaManager.Instance.LowestRank.ParentRank)
            {
                setFocusItem(area);
            }
        }

        private void setFocusItem(AreaBase town)
        {
            listViewSummary.SelectedItems.Clear();
            listViewSummary.Select();
            if (town == null) return;

            int idx;
            if (townIdxMap.TryGetValue(town, out idx))
            {
                listViewSummary.EnsureVisible(idx);
                listViewSummary.Items[idx].Selected = true;
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(listViewSummary);
            }
            catch
            {
                MessageBox.Show("导出到Excel...失败！");
            }
        }

        private void listViewSummary_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewSummary.SelectedItems.Count == 0) return;

            SummaryAlthm sumAthm = listViewSummary.SelectedItems[0].Tag as SummaryAlthm;
            if (sumAthm == null) return;

            refreshLVDetail(sumAthm);
        }

        private void refreshLVDetail(SummaryAlthm sumAthm)
        {
            listViewDetail.Items.Clear();
            listViewDetail.SelectedItems.Clear();

            foreach (CPkResult result in sumAthm.VilPkResultMap.Values)
            {
                ListViewItem lvi = new ListViewItem(result.Area.GetAreaPath());
                lvi.Tag = result;
                lvi.SubItems.Add(double.IsNaN(result.DValueCM) ? "-" : Convert.ToString(result.DValueCM));
                lvi.SubItems.Add(double.IsNaN(result.DValueCU) ? "-" : Convert.ToString(result.DValueCU));
                lvi.SubItems.Add(double.IsNaN(result.DValueCT) ? "-" : Convert.ToString(result.DValueCT));

                listViewDetail.Items.Add(lvi);

                if (selVillage != null && result.Area == selVillage)
                {
                    listViewDetail.Select();
                    lvi.Selected = true;
                    listViewDetail.EnsureVisible(listViewDetail.Items.IndexOf(lvi));
                }
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            //
        }
    }

    public class CPkResult
    {
        public AreaBase Area { get; set; }

        public Alghirithm Althm { get; set; }

        public double DValueCM { get; set; }

        public double DValueCU { get; set; }

        public double DValueCT { get; set; }

        public CPkResult(AreaBase area, Alghirithm thm,
            double dcm, double dcu, double dct)
        {
            this.Area = area;
            this.Althm = thm;

            this.DValueCM = dcm;
            this.DValueCU = dcu;
            this.DValueCT = dct;
        }
    }

    public class CheckAlthm
    {
        public Alghirithm Althm { get; set; }

        public bool BCheck { get; set; }

        public CheckAlthm(Alghirithm thm)
        {
            this.Althm = thm;

            BCheck = true;
        }

        public override string ToString()
        {
            return Althm.ToString();
        }
    }

    public class SummaryAlthm
    {
        public AreaBase Town { get; set; }

        public Dictionary<Alghirithm, int> AlthmCntMap { get; set; }

        public Dictionary<AreaBase, CPkResult> VilPkResultMap { get; set; }

        public SummaryAlthm(AreaBase town)
        {
            this.Town = town;
            AlthmCntMap = new Dictionary<Alghirithm, int>();
            VilPkResultMap = new Dictionary<AreaBase, CPkResult>();
        }

        public void FillAlthm(PKModeTemplate template)
        {
            AlthmCntMap.Clear();
            foreach (Alghirithm thm in template.AlghirithmVec)
            {
                AlthmCntMap.Add(thm, 0);
            }
            AlthmCntMap[template.OtherAlghirithm] = 0;
        }

        public void StatAlthm(Alghirithm thm)
        {
            if (!AlthmCntMap.ContainsKey(thm))
            {
                AlthmCntMap.Add(thm, 0);
            }
            AlthmCntMap[thm]++;
        }

        public string[] GetContext()
        {
            List<string> conVec = new List<string>();
            conVec.AddRange(Town.GetAreaPath());
            foreach (Alghirithm thm in AlthmCntMap.Keys)
            {
                conVec.Add(Convert.ToString(AlthmCntMap[thm]));
            }
            return conVec.ToArray();
        }

        public void AddVillageResult(AreaBase village, CPkResult result)
        {
            VilPkResultMap[village] = result;
        }
    }
}
