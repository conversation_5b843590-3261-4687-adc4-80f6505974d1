﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryLteTddNetworkConfig : DiyQueryDataBase
    {
        public string tableName { get; set; } = "tb_xinjiang_LteTddNetworkConfig";
        public List<LteTddNetworkConfigDBInfo> LteTddNetworkConfigDBInfoList { get; private set; }

        public DIYQueryLteTddNetworkConfig()
            : base()
        { }

        public override string Name { get { return "查询LteTdd网管配置"; } }

        protected override string getSqlTextString()
        {
            string name = $"{tableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[站号],[带宽],[业务IP],[管理IP]
,[电子下倾角],[RsPower],[PA],[PB] FROM {0} where [基站名称]='{1}' order by 小区名称", name, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[10];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            LteTddNetworkConfigDBInfoList = new List<LteTddNetworkConfigDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            LteTddNetworkConfigDBInfo info = new LteTddNetworkConfigDBInfo();
            info.FillData(package);
            LteTddNetworkConfigDBInfoList.Add(info);
        }
    }

    public class LteTddNetworkConfigDBInfo
    {
        public int CellID { get; set; }

        public string BtsName { get; set; }
        public string CellName { get; set; }
        public int NodeBID { get; set; }
        public string Bandwidth { get; set; }
        public string ServiceIP { get; set; }
        public string ManageIP { get; set; }
        public int Downtilt { get; set; }
        public string RsPower { get; set; }
        public string PA { get; set; }
        public string PB { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            NodeBID = package.Content.GetParamInt();
            Bandwidth = package.Content.GetParamString();
            ServiceIP = package.Content.GetParamString();
            ManageIP = package.Content.GetParamString();
            Downtilt = package.Content.GetParamInt();
            RsPower = package.Content.GetParamString();
            PA = package.Content.GetParamString();
            PB = package.Content.GetParamString();
        }
    }
}
