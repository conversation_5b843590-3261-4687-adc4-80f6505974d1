﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRDropCallAnaHelper
    {
        public List<int> MoCallDropEventIds { get; } = NREventHelper.VoiceHelper.MoCallDropEventIds;
        public List<int> MoCallAttemptEventIds { get; } = NREventHelper.VoiceHelper.MoCallAttemptEventIds;
        public List<int> MoCallOverEventIds { get; } = NREventHelper.VoiceHelper.MoCallOverEventIds;

        public List<int> MtCallDropEventIds { get; } = NREventHelper.VoiceHelper.MtCallDropEventIds;
        public List<int> MtCallAttemptEventIds { get; } = NREventHelper.VoiceHelper.MtCallAttemptEventIds;
        public List<int> MtCallOverEventIds { get; } = NREventHelper.VoiceHelper.MtCallOverEventIds;

        public List<int> HandOverEvtIdList { get; } = NREventHelper.HandoverHelper.GetHandoverRequestEvt(false);
    }
}
