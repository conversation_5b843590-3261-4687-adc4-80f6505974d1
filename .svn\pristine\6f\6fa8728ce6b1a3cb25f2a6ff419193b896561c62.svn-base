﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    class LteFddSmallStationAcceptManager : LteFddIndoorStationAcceptManager
    {
        public override void SetAcceptCond(LteStationAcceptCondition cond)
        {
            workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteFddStationAcceptance");
            this.acceptCond = cond;
            errMsg = "";
            this.acceptorList = new List<LteFddIndoorStationAccept>()
            {
                new FddSmallAcpFtpDownload(),
                new FddSmallAcpFtpUpload(),
                new FddSmallAcpHandover(),
                new FddSmallAcpCsfbRate(),
                new FddSmallAcpRrcRate(),
                new FddSmallAcpErabRate(),
                new FddSmallAcpAccRate(),
                new FddSmallAcp24ReselectRate(),
                new FddSmallAcpLeakOutLock(),
                new FddSmallAcpLeakOutScan(),
                new FddSmallAcpVolteVoiceMo(),
                new FddSmallAcpVolteVoiceMt(),
                new FddSmallAcpCellName(),
                new FddSmallAcpLeveling(),
                new FddSmallAcpDotCoverage(),
                new FddSmallAcpCellActualPara(),
                new FddSmallAcpCellPlanPara(),
                new FddSmallAcpPerformance(),
                new FddSmallAcpAlarm()
            };
            sectorIDDic = new Dictionary<string, int>();
            DiyQueryFddDBSetting.GetInstance().Query();
        }

        public override string GetTargetFile(string btsName, int sectorCount, string saveFolder)
        {
            if (sectorCount > 3)
            {
                errMsg = string.Format("基站{0}PCI小区超过3个，不支持报告导出", btsName);
                return "";
            }

            string templateFile = "LTEFDD小站验收模板.xlsx";
            templateFile = Path.Combine(workDir, templateFile);
            if (!File.Exists(templateFile))
            {
                errMsg = string.Format("[{0}]路径下不存在报告模板文件", templateFile);
                return "";
            }

            string targetFile = string.Format("LTEFDD小站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        protected override void dealResult()
        {
            //
        }

        protected override string getPath()
        {
            return Singleton<FddSmallStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }
    }
}
