﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.CQT
{
    public class DIYHandOverNotIntime : DIYAnalyseFilesOneByOneByRegion
    {
        readonly string netType;
        public DIYHandOverNotIntime(MainModel mainModel, string netWorker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.netType = netWorker;
        }
        
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "CQT切换不及时"; }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        /// <summary>
        /// 判断是否选择区域
        /// </summary>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 功能序号
        /// </summary>
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21035, this.Name);
        }

        #region 全局变量

        readonly List<FileInfo> fileList = new List<FileInfo>();//存放所有文件列表
        readonly List<string> cqtNameList = new List<string>();//存放CQT的地点名
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>(); //按文件ID存放切换事件
        readonly Dictionary<string, List<FileInfo>> cqtFileDic = new Dictionary<string, List<FileInfo>>();//按地点存放对应文件
        readonly Dictionary<HoNotIntimeMainInfo, List<HoNotIntimeSubInfo>> hoNotIntimeResultDic
            = new Dictionary<HoNotIntimeMainInfo, List<HoNotIntimeSubInfo>>();//切换不及时信息结果集
        
        int iMainCellRxlev = 0;//所设置的主服电平值
        int iNbCellRxlev = 0;  //所设置的邻区电平值
        int iLastTime = 0;     //所设置的持续时长
        bool isDeticateOnly = true;//是否只分析占用状态采样点

        #endregion
        
        /// <summary>
        /// 使用前清空变量
        /// </summary>
        private void clealVar()
        {
            fileList.Clear();
            cqtNameList.Clear();
            condEventsDic.Clear();
            cqtFileDic.Clear();
            hoNotIntimeResultDic.Clear();
        }

        /// <summary>
        /// 准备查询数据
        /// </summary>
        protected override void query()
        {
            //清空以及重新初始化变量
            clealVar();

            SetHoCondForm condForm = new SetHoCondForm();
            if (condForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            else
            {
                if (condForm.MainCellRxlev == 0 || condForm.NbCellRxlev == 0 || condForm.LastTime == 0)
                {
                    MessageBox.Show("切换不及时条件设置有误，请确认！", "信息提示", MessageBoxButtons.OK);
                    return;
                }
                else
                {
                    iMainCellRxlev = condForm.MainCellRxlev;
                    iNbCellRxlev = condForm.NbCellRxlev;
                    iLastTime = condForm.LastTime;
                    isDeticateOnly = condForm.IsDeticateOnly;
                }
            }

            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);

            //找出所有文件中包含的地点名称（指有文件的地点）
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!cqtNameList.Contains(name[2]))
                {
                    cqtNameList.Add(name[2]);
                }
            }
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileDic();

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
                    ErrorInfo = "连接服务器失败!";
                else
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

                CQTHandOverNotIntiemForm showData = new CQTHandOverNotIntiemForm(mainModel, netType);
                showData.setData(hoNotIntimeResultDic);
                showData.Show();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void addFileDic()
        {
            foreach (string nameL in cqtNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                cqtFileDic.Add(nameL, subFileList);
            }
        }

        /// <summary>
        /// 按地点及其文件查询采样点信息
        /// </summary>
        private void queryInThread(object o)
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "开始查询...";
            int idx = 1;
            
            foreach (TimePeriod period in condition.Periods)
            {
                int countTotal = cqtNameList.Count;
                if (countTotal == 0)
                    continue;
                int iPartNum = 100 / countTotal;
                idx = anaHoNoIntimeByCqt(idx, countTotal, iPartNum);
            }
            WaitBox.Close();
        }

        private int anaHoNoIntimeByCqt(int idx, int countTotal, int iPartNum)
        {
            foreach (string strCqtName in cqtNameList)
            {
                if (WaitBox.CancelRequest)
                    break;

                WaitBox.ProgressPercent = idx * iPartNum;
                WaitBox.Text = "正在分析 (" + idx++ + "/" + countTotal + ")" + strCqtName + " 切换情况...";

                if (cqtFileDic.ContainsKey(strCqtName))
                {
                    List<FileInfo> files = cqtFileDic[strCqtName];
                    if (netType == "GSM")
                    {
                        anaGsmHoNoIntimeByCqt(files, strCqtName);
                    }
                    else if (netType == "TD")
                    {
                        anaTdHoNoIntimeByCqt(files, strCqtName);
                    }
                    else
                    {
                        anaTdGsmHoNoIntimeByCqt(files, strCqtName);
                    }
                }
            }

            return idx;
        }

        /// <summary>
        /// 查询全区域切换事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(17);
                eventIds.Add(18);

                eventIds.Add(8);
                eventIds.Add(10);
                eventIds.Add(82);

                eventIds.Add(59);
                eventIds.Add(6);
                eventIds.Add(7);
                eventIds.Add(907);
                eventIds.Add(908);

                eventIds.Add(41);
                eventIds.Add(43);
            }
            else
            {
                eventIds.Add(145);
                eventIds.Add(146);
                eventIds.Add(148);
                eventIds.Add(149);

                eventIds.Add(142);
                eventIds.Add(143);
                eventIds.Add(151);
                eventIds.Add(152);

                eventIds.Add(105);
                eventIds.Add(111);

                eventIds.Add(106);
                eventIds.Add(112);
                eventIds.Add(118);
                eventIds.Add(124);
                eventIds.Add(199);
                eventIds.Add(200);

                eventIds.Add(187);
                eventIds.Add(230);
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }

        /// <summary>
        /// 判断GSM是否为占用模式
        /// </summary>
        private bool judgeGsmIsDeticateMode(int ipara)
        {
            if (!isDeticateOnly)
            {
                return true;
            }
            else
            {
                if (ipara >= 0 && ipara <= 7)//包括Rxqual
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 判断是否为占用模式
        /// </summary>
        private bool judgeTdIsDeticateMode(int ipara)
        {
            if (!isDeticateOnly)
            {
                return true;
            }
            else
            {
                if (ipara >= -120 && ipara <= -10)//包括DPCH_RSCP
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 查询多个文件的采样点信息
        /// </summary>
        private Dictionary<int, List<TestPoint>> searchTestPoint(List<FileInfo> cqtFileList)
        {
            Dictionary<int, List<TestPoint>> testPointDic = new Dictionary<int, List<TestPoint>>();

            List<TestPoint> tpTmpList = new List<TestPoint>();
            QueryCondition condition = new QueryCondition();
            condition.FileInfos = cqtFileList;
            DIYReplayFileWithNoWaitBox fileQuery = new DIYReplayFileWithNoWaitBox(mainModel);
            fileQuery.Columns = getReplayCols();
            fileQuery.SetQueryCondition(condition);
            fileQuery.Query();
            if (fileQuery.TestPoints!=null)
            {
                tpTmpList.AddRange(fileQuery.TestPoints);
            }

            foreach (TestPoint tp in tpTmpList)
            {
                if (testPointDic.ContainsKey(tp.FileID))
                {
                    List<TestPoint> tmpTestPointList = testPointDic[tp.FileID];
                    tmpTestPointList.Add(tp);
                }
                else
                {
                    List<TestPoint> tmpTestPointList = new List<TestPoint>();
                    tmpTestPointList.Add(tp);
                    testPointDic.Add(tp.FileID, tmpTestPointList);
                }
            }
            return testPointDic;
        }

        private List<string> getReplayCols()
        {
            List<string> cols = new List<string>();
            cols.Add("isampleid");
            cols.Add("itime");
            cols.Add("ilongitude");
            cols.Add("ilatitude");
            cols.Add("LAC");
            cols.Add("CI");
            cols.Add("RxLevSub");
            cols.Add("N_RxLev");
            cols.Add("N_BCCH");
            cols.Add("N_BSIC");
            cols.Add("RxQualSub");
            cols.Add("APP_Speed");
            cols.Add("TD_SCell_LAC");
            cols.Add("TD_SCell_CI");
            cols.Add("TD_PCCPCH_RSCP");
            cols.Add("TD_NCell_PCCPCH_RSCP");
            cols.Add("TD_NCell_UARFCN");
            cols.Add("TD_NCell_CPI");
            cols.Add("TD_DPCH_RSCP");
            cols.Add("TD_GSM_NCell_Rxlev");
            cols.Add("TD_GSM_NCell_ARFCN");
            cols.Add("TD_GSM_NCell_BSIC");
            return cols;
        }

        /// <summary>
        /// 按文件ID组织文件集
        /// </summary>
        private Dictionary<int, FileInfo> organizeFileInfo(List<FileInfo> cqtFileList)
        {
            Dictionary<int, FileInfo> fileDic = new Dictionary<int, FileInfo>();
            foreach (FileInfo fInfo in cqtFileList)
            {
                int ifileid = fInfo.ID;
                if (!fileDic.ContainsKey(ifileid))
                {
                    fileDic.Add(ifileid, fInfo);
                }
            }
            return fileDic;
        }

        /// <summary>
        /// 按CQT分析GSM切换不及时问题点
        /// </summary>
        private void anaGsmHoNoIntimeByCqt(List<FileInfo> cqtFileList, string strCqtName)
        {
            if (WaitBox.CancelRequest)
                return;

            try
            {
                Dictionary<int, FileInfo> fileDic = organizeFileInfo(cqtFileList);
                Dictionary<int, List<TestPoint>> testPointDic = searchTestPoint(cqtFileList);

                foreach (int ifileid in testPointDic.Keys)
                {
                    #region 查询采样点
                    if (!fileDic.ContainsKey(ifileid))
                        continue;

                    FileInfo fileInfo = fileDic[ifileid];
                    List<TestPoint> tpList = testPointDic[ifileid];
                    tpList.Sort(TestPointSort.GetCompareBySnId());
                    List<SampleSubInfo> sampleSubList = getGsmSampleSubList(fileInfo, tpList);
                    sampleSubList.Sort(SampleSubInfo.GetCompareBySampleId());


                    #endregion

                    anaNoIntimeOnTestPoint(sampleSubList, fileInfo, strCqtName);
                }
            }
            catch(Exception exp)
            {
                log.Error("[CQT切换不及时专题]，地点：" + strCqtName + "，错误信息：" + exp.Message);
            }
        }

        private List<SampleSubInfo> getGsmSampleSubList(FileInfo fileInfo, List<TestPoint> tpList)
        {
            List<SampleSubInfo> sampleSubList = new List<SampleSubInfo>();
            foreach (TestPoint tp in tpList)
            {
                if (tp is TestPointDetail)
                {
                    if (tp["LAC"] == null || tp["CI"] == null
                        || tp["RxLevSub"] == null || tp["RxQualSub"] == null)
                        continue;
                    int iRxlevSub = (int)(short?)tp["RxLevSub"];
                    int iLAC = (int)tp["LAC"];
                    int iCI = (int)tp["CI"];
                    int iSampleid = tp.SN;
                    int iTime = tp.Time;
                    bool isValid;
                    int iRxqual = getRxqual(fileInfo, tp, out isValid);
                    if (!isValid)
                    {
                        continue;
                    }

                    if (iCI <= 0 || iRxlevSub > -10 || iRxlevSub < -120 || !judgeGsmIsDeticateMode(iRxqual))
                        continue;

                    List<NbSampleInfo> nbSamList = getGsmNCellSmpList(tp, iRxlevSub);
                    SampleSubInfo samInfo = new SampleSubInfo();
                    samInfo.iLAC = iLAC;
                    samInfo.iCI = iCI;
                    samInfo.iRxlevSub = iRxlevSub;
                    samInfo.iSampleid = iSampleid;
                    samInfo.iTime = iTime;
                    samInfo.dLongitude = fileInfo.TopLeftLongitude / 10000000;//文件左上经度替换采样点tp.Longitude
                    samInfo.dLatitude = fileInfo.TopLeftLatitude / 10000000;//文件左上纬度替换采样点tp.Latitude
                    samInfo.nbInfoList = nbSamList;
                    sampleSubList.Add(samInfo);
                }
            }

            return sampleSubList;
        }

        private static int getRxqual(FileInfo fileInfo, TestPoint tp, out bool isValid)
        {
            isValid = true;
            int iRxqual;
            if (fileInfo.ServiceType == 1 || fileInfo.ServiceType == 4)
                iRxqual = (int)(byte?)tp["RxQualSub"];
            else
            {
                if (tp["APP_Speed"] == null)
                    isValid = false;
                iRxqual = (int)(int?)tp["APP_Speed"];
            }

            return iRxqual;
        }

        private List<NbSampleInfo> getGsmNCellSmpList(TestPoint tp, int iRxlevSub)
        {
            List<NbSampleInfo> nbSamList = new List<NbSampleInfo>();
            for (int i = 0; i < 6; i++)
            {
                short? iTmpBcch = (short?)tp["N_BCCH", i];
                byte? iTmpBsic = (byte?)tp["N_BSIC", i];
                short? iTmpRxlev = (short?)tp["N_RxLev", i];

                if (iTmpBcch == null || iTmpBsic == null || iTmpRxlev == null)
                    continue;

                NbSampleInfo nbInfo = new NbSampleInfo();
                nbInfo.iNbBcch = (int)iTmpBcch;
                nbInfo.iNbBsic = (int)iTmpBsic;
                nbInfo.iNbRxlevSub = (int)iTmpRxlev;
                if (nbInfo.iNbBcch <= 0 || nbInfo.iNbBsic <= 0 ||
                    nbInfo.iNbRxlevSub < -120 || nbInfo.iNbRxlevSub > -10 || (nbInfo.iNbRxlevSub - iRxlevSub) < iNbCellRxlev)
                {
                    continue;//当邻区参数非法时，或邻区场强不符合时退出
                }
                nbSamList.Add(nbInfo);
            }

            return nbSamList;
        }

        /// <summary>
        /// 按CQT分析TD切换不及时问题点
        /// </summary>
        private void anaTdHoNoIntimeByCqt(List<FileInfo> cqtFileList, string strCqtName)
        {
            if (WaitBox.CancelRequest)
                return;

            try
            {
                Dictionary<int, FileInfo> fileDic = organizeFileInfo(cqtFileList);
                Dictionary<int, List<TestPoint>> testPointDic = searchTestPoint(cqtFileList);

                foreach (int ifileid in testPointDic.Keys)
                {
                    #region 查询采样点
                    if (!fileDic.ContainsKey(ifileid))
                        continue;

                    FileInfo fileInfo = fileDic[ifileid];
                    List<TestPoint> tpList = testPointDic[ifileid];
                    tpList.Sort(TestPointSort.GetCompareBySnId());
                    List<SampleSubInfo> sampleSubList = getSampleSubList(fileInfo, tpList, false);
                    sampleSubList.Sort(SampleSubInfo.GetCompareBySampleId());

                    #endregion

                    anaNoIntimeOnTestPoint(sampleSubList, fileInfo, strCqtName);
                }
            }
            catch (Exception exp)
            {
                log.Error("[CQT切换不及时专题]，地点：" + strCqtName + "，错误信息：" + exp.Message);
            }
        }

        private List<NbSampleInfo> getTDNCellSamList(TestPoint tp, int iPccpchRscp)
        {
            List<NbSampleInfo> nbSamList = new List<NbSampleInfo>();
            for (int i = 0; i < 6; i++)
            {
                NbSampleInfo nbInfo = new NbSampleInfo();
                if (tp["TD_NCell_UARFCN", i] == null || tp["TD_NCell_CPI", i] == null || tp["TD_NCell_PCCPCH_RSCP", i] == null)
                    continue;
                nbInfo.iNbBcch = (int)(int?)tp["TD_NCell_UARFCN", i];
                nbInfo.iNbBsic = (int)(int?)tp["TD_NCell_CPI", i];
                nbInfo.iNbRxlevSub = (int)(int?)tp["TD_NCell_PCCPCH_RSCP", i];
                if (nbInfo.iNbBcch <= 0 || nbInfo.iNbBsic <= 0 ||
                    nbInfo.iNbRxlevSub < -120 || nbInfo.iNbRxlevSub > -10 || (nbInfo.iNbRxlevSub - iPccpchRscp) < iNbCellRxlev)
                {
                    continue;//当邻区参数非法时，或邻区场强不符合时退出
                }
                nbSamList.Add(nbInfo);
            }

            return nbSamList;
        }

        /// <summary>
        /// 按CQT分析TD-GSM切换不及时问题点
        /// </summary>
        private void anaTdGsmHoNoIntimeByCqt(List<FileInfo> cqtFileList, string strCqtName)
        {
            if (WaitBox.CancelRequest)
                return;

            try
            {
                Dictionary<int, FileInfo> fileDic = organizeFileInfo(cqtFileList);
                Dictionary<int, List<TestPoint>> testPointDic = searchTestPoint(cqtFileList);

                foreach (int ifileid in testPointDic.Keys)
                {
                    #region 查询采样点
                    if (!fileDic.ContainsKey(ifileid))
                        continue;

                    FileInfo fileInfo = fileDic[ifileid];
                    List<TestPoint> tpList = testPointDic[ifileid];
                    tpList.Sort(TestPointSort.GetCompareBySnId());
                    List<SampleSubInfo> sampleSubList = getSampleSubList(fileInfo, tpList, true);
                    sampleSubList.Sort(SampleSubInfo.GetCompareBySampleId());

                    #endregion

                    anaNoIntimeOnTestPoint(sampleSubList, fileInfo, strCqtName);
                }
            }
            catch (Exception exp)
            {
                log.Error("[CQT切换不及时专题]，地点：" + strCqtName + "，错误信息：" + exp.Message);
            }
        }

        private List<SampleSubInfo> getSampleSubList(FileInfo fileInfo, List<TestPoint> tpList, bool isGsm)
        {
            List<SampleSubInfo> sampleSubList = new List<SampleSubInfo>();
            foreach (TestPoint tp in tpList)
            {
                if (tp is TDTestPointDetail)
                {
                    if (tp["TD_PCCPCH_RSCP"] == null || tp["TD_SCell_LAC"] == null
                        || tp["TD_SCell_CI"] == null || tp["TD_DPCH_RSCP"] == null)
                        continue;
                    int iPccpchRscp = (int)(float?)tp["TD_PCCPCH_RSCP"];
                    int iLAC = (int)tp["TD_SCell_LAC"];
                    int iCI = (int)tp["TD_SCell_CI"];
                    int iSampleid = tp.SN;
                    int iTime = tp.Time;
                    int iDpchRscp = (int)(float?)tp["TD_DPCH_RSCP"];

                    if (iCI <= 0 || iPccpchRscp > -10 || iPccpchRscp < -120 || !judgeTdIsDeticateMode(iDpchRscp))
                        continue;

                    List<NbSampleInfo> nbSamList;
                    if (isGsm)
                    {
                        nbSamList = getTDGsmNCellSamList(tp, iPccpchRscp);
                    }
                    else
                    {
                        nbSamList = getTDNCellSamList(tp, iPccpchRscp);
                    }
                    SampleSubInfo samInfo = new SampleSubInfo();
                    samInfo.iLAC = iLAC;
                    samInfo.iCI = iCI;
                    samInfo.iRxlevSub = iPccpchRscp;
                    samInfo.iSampleid = iSampleid;
                    samInfo.iTime = iTime;
                    samInfo.dLongitude = fileInfo.TopLeftLongitude / 10000000;//文件左上经度替换采样点tp.Longitude
                    samInfo.dLatitude = fileInfo.TopLeftLatitude / 10000000;//文件左上纬度替换采样点tp.Latitude
                    samInfo.nbInfoList = nbSamList;
                    sampleSubList.Add(samInfo);
                }
            }

            return sampleSubList;
        }

        private List<NbSampleInfo> getTDGsmNCellSamList(TestPoint tp, int iPccpchRscp)
        {
            List<NbSampleInfo> nbSamList = new List<NbSampleInfo>();
            for (int i = 0; i < 6; i++)
            {
                NbSampleInfo nbInfo = new NbSampleInfo();
                if (tp["TD_GSM_NCell_ARFCN", i] == null || tp["TD_GSM_NCell_Rxlev", i] == null || tp["TD_GSM_NCell_BSIC", i] == null)
                    continue;
                nbInfo.iNbBcch = (int)(short?)tp["TD_GSM_NCell_ARFCN", i];
                nbInfo.iNbBsic = (int)(byte?)tp["TD_GSM_NCell_BSIC", i];
                nbInfo.iNbRxlevSub = (int)(short?)tp["TD_GSM_NCell_Rxlev", i];
                if (nbInfo.iNbBcch <= 0 || nbInfo.iNbBsic <= 0 ||
                    nbInfo.iNbRxlevSub < -120 || nbInfo.iNbRxlevSub > -10 || (nbInfo.iNbRxlevSub - iPccpchRscp) < iNbCellRxlev)
                {
                    continue;//当邻区参数非法时，或邻区场强不符合时退出
                }
                nbSamList.Add(nbInfo);
            }

            return nbSamList;
        }

        /// <summary>
        /// 通过采样点分析切换不及时
        /// </summary>
        private void anaNoIntimeOnTestPoint(List<SampleSubInfo> sampleSubList, FileInfo fileInfo, string strCqtName)
        {
            SampleSubInfo beforeSample = new SampleSubInfo();
            List<SampleSubInfo> samTmpList = new List<SampleSubInfo>();
            int isTime = 0;//第一个符合切换不及时的时间
            //int isNoHoTime = 0;//符合切换门限的时间
            int ieTime = 0;//最后一个符合切换不及时的时间
            bool isHandoverNotIntime = false;//切换不及时状态
            foreach (SampleSubInfo sampleInfo in sampleSubList)
            {
                try
                {
                    if (beforeSample.iCI == 0 && beforeSample.iLAC == 0)//第一个采样点
                        beforeSample = sampleInfo;

                    if (sampleInfo.iRxlevSub <= iMainCellRxlev && sampleInfo.nbInfoList.Count > 0)
                    {
                        ieTime = judgeHandoverNotIntime(sampleSubList, fileInfo, strCqtName, ref samTmpList, ref isTime, ref isHandoverNotIntime, sampleInfo);
                    }
                    else
                    {
                        if (isHandoverNotIntime)//符合切换不及时退出
                        {
                            dealNotIntimeResult(sampleSubList, samTmpList, isTime, ieTime, fileInfo, strCqtName);
                            isHandoverNotIntime = false;
                            samTmpList = new List<SampleSubInfo>();
                            isTime = 0;
                            ieTime = 0;
                        }
                        else
                        {
                            samTmpList = new List<SampleSubInfo>();
                            isTime = 0;
                            ieTime = 0;
                        }
                    }
                }
                catch (Exception exp)
                {
                    log.Error("[CQT切换不及时专题]，地点：" + strCqtName + "，错误信息：" + exp.Message);
                }
            }
            if (isHandoverNotIntime)//符合切换不及时退出
            {
                dealNotIntimeResult(sampleSubList, samTmpList, isTime, ieTime, fileInfo, strCqtName);
            }
        }

        private int judgeHandoverNotIntime(List<SampleSubInfo> sampleSubList, FileInfo fileInfo, string strCqtName, ref List<SampleSubInfo> samTmpList, ref int isTime, ref bool isHandoverNotIntime, SampleSubInfo sampleInfo)
        {
            int ieTime;
            if (isTime == 0)
                isTime = sampleInfo.iTime;
            ieTime = sampleInfo.iTime;

            samTmpList.Add(sampleInfo);
            if (sampleInfo.iTime - isTime >= iLastTime)
            {
                //判断是否有切换事件(切换成功或失败)及没有小区变换
                if (!checkIsHaveHandOverEvent(fileInfo.ID, isTime, sampleInfo.iTime) && !isCellChange(samTmpList))
                {
                    if (!isHandoverNotIntime)
                    {
                        isHandoverNotIntime = true;
                    }
                }
                else if (isHandoverNotIntime)//符合切换不及时退出
                {
                    dealNotIntimeResult(sampleSubList, samTmpList, isTime, ieTime, fileInfo, strCqtName);
                    isHandoverNotIntime = false;
                    samTmpList = new List<SampleSubInfo>();
                    isTime = 0;
                    ieTime = 0;
                }
                else//不符合切换不及时退出
                {
                    samTmpList = new List<SampleSubInfo>();
                    isTime = 0;
                    ieTime = 0;
                }
            }

            return ieTime;
        }

        /// <summary>
        /// 分析切换不及时结果集
        /// </summary>
        private void dealNotIntimeResult(List<SampleSubInfo> sampleSubList, List<SampleSubInfo> samTmpList, 
            int istime, int ietime, FileInfo fileInfo,string strCqtName)
        {
            if (samTmpList.Count <= 0)
                return;

            DateTime tmpTime = JavaDate.GetDateTimeFromMilliseconds(ietime * 1000L);
            HoNotIntimeMainInfo noIntimeInfo = new HoNotIntimeMainInfo();
            noIntimeInfo.ILac = samTmpList[0].iLAC;
            noIntimeInfo.ICi = samTmpList[0].iCI;
            noIntimeInfo.DLongitude = samTmpList[0].dLongitude;
            noIntimeInfo.DLatitude = samTmpList[0].dLatitude;
            if (netType == "GSM")
            {
                Cell cell = CellManager.GetInstance().GetNearestCell(tmpTime, (ushort?)noIntimeInfo.ILac, (ushort?)noIntimeInfo.ICi, noIntimeInfo.DLongitude, noIntimeInfo.DLatitude);
                if (cell == null)
                    noIntimeInfo.StrCellName = noIntimeInfo.ILac + "_" + noIntimeInfo.ICi;
                else
                    noIntimeInfo.StrCellName = cell.Name;
            }
            else
            {
                TDCell tdCell = CellManager.GetInstance().GetNearestTDCell(tmpTime, (ushort?)noIntimeInfo.ILac, (ushort?)noIntimeInfo.ICi, noIntimeInfo.DLongitude, noIntimeInfo.DLatitude);
                if (tdCell == null)
                    noIntimeInfo.StrCellName = noIntimeInfo.ILac + "_" + noIntimeInfo.ICi;
                else
                    noIntimeInfo.StrCellName = tdCell.Name;
            }
            noIntimeInfo.StrCqtName = strCqtName;

            noIntimeInfo.IAllSampleNum = getServerSampleNumByTime(istime, ietime, sampleSubList, samTmpList);
            if (noIntimeInfo.IAllSampleNum >= samTmpList.Count)
                noIntimeInfo.IWeakCoverSampleNum = samTmpList.Count;
            else
                noIntimeInfo.IWeakCoverSampleNum = noIntimeInfo.IAllSampleNum;
            noIntimeInfo.IServerNum = 1;
            noIntimeInfo.INoIntimeNum = 1;
            noIntimeInfo.FNotIntime = ietime - istime;
            noIntimeInfo.INotIntimeRxlev = getAvgRxlev(samTmpList);
            getAbnormalEvent(fileInfo.ID, istime, ietime, ref noIntimeInfo);
            CellKey cellKey = getNextCellKey(sampleSubList, ietime, noIntimeInfo.ILac, noIntimeInfo.ICi);//获取切换后的小区

            List<NbSampleInfo> nbSampleList = new List<NbSampleInfo>();
            foreach (SampleSubInfo sInfo in samTmpList)
            {
                nbSampleList.AddRange(sInfo.nbInfoList);
            }
            List<HoNotIntimeSubInfo> notIntimeList = getNotIntimeNbCellInfo(nbSampleList, tmpTime, samTmpList[0].dLongitude, samTmpList[0].dLatitude, fileInfo, cellKey);
            mergeNotIntimeResult(noIntimeInfo, notIntimeList);            
        }

        /// <summary>
        /// 合并不及时采样点结果集
        /// </summary>
        private void mergeNotIntimeResult(HoNotIntimeMainInfo noIntimeInfo, List<HoNotIntimeSubInfo> notIntimeList)
        {
            if (hoNotIntimeResultDic.ContainsKey(noIntimeInfo))
            {
                List<HoNotIntimeSubInfo> subInfoList = hoNotIntimeResultDic[noIntimeInfo];
                HoNotIntimeMainInfo mainInfo = new HoNotIntimeMainInfo();
                foreach (HoNotIntimeMainInfo tmpInfo in hoNotIntimeResultDic.Keys)
                {
                    if (tmpInfo.Equals(noIntimeInfo))
                    {
                        mainInfo = tmpInfo;
                        break;
                    }
                }
                hoNotIntimeResultDic.Remove(mainInfo);
                HoNotIntimeMainInfo resultInfo = HoNotIntimeMainInfo.fillData(mainInfo, noIntimeInfo);

                foreach (HoNotIntimeSubInfo nbInfo in notIntimeList)
                {
                    HoNotIntimeSubInfo.fillData(ref subInfoList, nbInfo);
                }
                hoNotIntimeResultDic.Add(resultInfo, subInfoList);
            }
            else
            {
                hoNotIntimeResultDic.Add(noIntimeInfo, notIntimeList);
            }
        }

        /// <summary>
        /// 获取邻区信息
        /// </summary>
        private List<HoNotIntimeSubInfo> getNotIntimeNbCellInfo(List<NbSampleInfo> nbSampleList, DateTime tmpTime, double dLongitude, double dLatitude,
                                                             FileInfo fileInfo, CellKey cellKey)
        {
            List<HoNotIntimeSubInfo> tmpNotIntimeSubList = new List<HoNotIntimeSubInfo>();
            Dictionary<NbCellKey, List<NbSampleInfo>> nbCellDic = getNbSampleDic(nbSampleList);

            foreach (NbCellKey nbKey in nbCellDic.Keys)
            {
                List<NbSampleInfo> nbInfoList = nbCellDic[nbKey];
                
                HoNotIntimeSubInfo hoSubInfo = new HoNotIntimeSubInfo();
                hoSubInfo.IBcch = nbKey.iBcch;
                hoSubInfo.IBsic = nbKey.iBsic;

                if (netType == "TD")
                {
                    TDCell nbCell = CellManager.GetInstance().GetNearestTDCell(tmpTime, (short)nbKey.iBcch, (byte)nbKey.iBsic, dLongitude, dLatitude);
                    if (nbCell == null)
                    {
                        hoSubInfo.ILac = 0;
                        hoSubInfo.ICi = 0;
                        hoSubInfo.StrNbCellName = "未知小区";
                    }
                    else
                    {
                        hoSubInfo.ILac = nbCell.LAC;
                        hoSubInfo.ICi = nbCell.CI;
                        hoSubInfo.StrNbCellName = nbCell.Name;
                    }
                }
                else
                {
                    Cell nbCell = CellManager.GetInstance().GetNearestCell(tmpTime, (short)nbKey.iBcch, (byte)nbKey.iBsic, dLongitude, dLatitude);
                    if (nbCell == null)
                    {
                        hoSubInfo.ILac = 0;
                        hoSubInfo.ICi = 0;
                        hoSubInfo.StrNbCellName = "未知小区";
                    }
                    else
                    {
                        hoSubInfo.ILac = nbCell.LAC;
                        hoSubInfo.ICi = nbCell.CI;
                        hoSubInfo.StrNbCellName = nbCell.Name;
                    }
                }

                hoSubInfo.IFileid = fileInfo.ID;
                hoSubInfo.IProjectType = fileInfo.ProjectID;
                hoSubInfo.IServiceType = fileInfo.ServiceType;
                hoSubInfo.StrSampleTbName = fileInfo.SampleTbName;
                hoSubInfo.DTime = tmpTime;
                hoSubInfo.StrLogFile = fileInfo.LogTable;

                if (hoSubInfo.ILac == cellKey.iLAC && hoSubInfo.ICi == cellKey.iCI)
                    hoSubInfo.IHoSuccNum = 1;
                hoSubInfo.INotIntimeRxlev = getAvgRxlev(nbInfoList);
                hoSubInfo.INotIntimeNum = 1;
                hoSubInfo.INotIntimeSample = nbInfoList.Count;
                hoSubInfo.StrFileName = fileInfo.Name;
                tmpNotIntimeSubList.Add(hoSubInfo);
            }
            return tmpNotIntimeSubList;
        }

        /// <summary>
        /// 按频点色码组织采样点
        /// </summary>
        private Dictionary<NbCellKey, List<NbSampleInfo>> getNbSampleDic(List<NbSampleInfo> nbSampleList)
        {
            Dictionary<NbCellKey, List<NbSampleInfo>> nbCellDic = new Dictionary<NbCellKey, List<NbSampleInfo>>();
            foreach (NbSampleInfo nbInfo in nbSampleList)
            {
                NbCellKey nbKey = new NbCellKey();
                nbKey.iBcch = nbInfo.iNbBcch;
                nbKey.iBsic = nbInfo.iNbBsic;
                if (nbCellDic.ContainsKey(nbKey))
                {
                    List<NbSampleInfo> tmpList = nbCellDic[nbKey];
                    tmpList.Add(nbInfo);
                }
                else
                {
                    List<NbSampleInfo> tmpList = new List<NbSampleInfo>();
                    tmpList.Add(nbInfo);
                    nbCellDic.Add(nbKey, tmpList);
                }
            }
            return nbCellDic;
        }

        /// <summary>
        /// 获取异常事件数
        /// </summary>
        private void getAbnormalEvent(int ifileid,int istime ,int ietime,ref HoNotIntimeMainInfo noIntimeInfo)
        {
            if (!condEventsDic.ContainsKey(ifileid))
                return;

            int iDropNum = 0;
            int iNoConnNum = 0;
            int iWeakQualNum = 0;
            int iWeakCoverNum = 0;
            int iHoFailNum = 0;
            List<Event> evtList = condEventsDic[ifileid];
            foreach (Event evt in evtList)
            {
                if (evt.Time > ietime || evt.Time < istime)
                    continue;

                if (netType == "GSM")
                {
                    switch (evt.ID)
                    {
                        case 6:
                        case 7:
                        case 907:
                        case 908:
                        case 59:
                            iDropNum++;
                            break;
                        case 8:
                        case 10:
                        case 82:
                            iNoConnNum++;
                            break;
                        case 18:
                            iHoFailNum++;
                            break;
                        case 41:
                            iWeakCoverNum++;
                            break;
                        case 43:
                            iWeakQualNum++;
                            break;
                    }
                }
                else
                {
                    switch (evt.ID)
                    {
                        case 106:
                        case 112:
                        case 118:
                        case 124:
                        case 199:
                        case 200:
                        case 59:
                            iDropNum++;
                            break;
                        case 105:
                        case 111:
                            iNoConnNum++;
                            break;
                        case 143:
                        case 146:
                        case 149:
                        case 152:
                            iHoFailNum++;
                            break;
                        case 187:
                            iWeakCoverNum++;
                            break;
                        case 230:
                            iWeakQualNum++;
                            break;
                    }
                }
            }

            noIntimeInfo.IDropCallNum = iDropNum;
            noIntimeInfo.INoConnNum = iNoConnNum;
            noIntimeInfo.IHoFailNum = iHoFailNum;
            noIntimeInfo.IWeakCoverNum = iWeakCoverNum;
            noIntimeInfo.IWeakQualNum = iWeakQualNum;
        }

        /// <summary>
        /// 求平均电平
        /// </summary>
        private int getAvgRxlev(List<SampleSubInfo> sampleSubList)
        {
            float fRxlevTotal = 0;
            foreach (SampleSubInfo sInfo in sampleSubList)
            {
                fRxlevTotal += sInfo.iRxlevSub;
            }
            return (int)(fRxlevTotal / sampleSubList.Count);
        }

        /// <summary>
        /// 求平均电平
        /// </summary>
        private int getAvgRxlev(List<NbSampleInfo> sampleSubList)
        {
            float fRxlevTotal = 0;
            foreach (NbSampleInfo nInfo in sampleSubList)
            {
                fRxlevTotal += nInfo.iNbRxlevSub;
            }
            return (int)(fRxlevTotal / sampleSubList.Count);
        }

        /// <summary>
        /// 是否有小区变换的情况
        /// </summary>
        private bool isCellChange(List<SampleSubInfo> samTmpList)
        {
            if (samTmpList.Count <= 0)
                return true;

            int iLac = samTmpList[0].iLAC;
            int iCi = samTmpList[0].iCI;
            foreach(SampleSubInfo sInfo in samTmpList)
            {
                if (sInfo.iLAC != iLac || sInfo.iCI != iCi)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取切换不及时后的第一个切换小区
        /// </summary>
        private CellKey getNextCellKey(List<SampleSubInfo> samTmpList, int ietime, int ilac, int ici)
        {
            CellKey cKey = new CellKey();
            foreach (SampleSubInfo sInfo in samTmpList)
            {
                if (sInfo.iTime >= ietime - 2 && sInfo.iTime <= ietime + 5
                    && sInfo.iLAC != ilac && sInfo.iCI != ici)
                {
                    cKey.iLAC = sInfo.iLAC;
                    cKey.iCI = sInfo.iCI;
                    break;
                }
            }
            return cKey;
        }

        /// <summary>
        /// 获取切换不及时占用的主服小区信息
        /// </summary>
        /// <param name="sampleSubList">全部采样点</param>
        /// <param name="samTmpList">问题采样点</param>
        private int getServerSampleNumByTime(int istime, int ietime, List<SampleSubInfo> sampleSubList, List<SampleSubInfo> samTmpList)
        {
            if (samTmpList.Count <= 0)
                return 0;

            int iLac = samTmpList[0].iLAC;
            int iCi = samTmpList[0].iCI;
            int isSampleTime = 0;
            int ieSampleTime = 0;
            int iSampleNum = 0;
            foreach (SampleSubInfo sInfo in sampleSubList)
            {
                if (sInfo.iLAC == iLac && sInfo.iCI == iCi)
                {
                    if (iSampleNum == 0)
                    {
                        isSampleTime = sInfo.iTime;
                    }
                    ieSampleTime = sInfo.iTime;
                    iSampleNum++;
                }
                else if (isSampleTime != 0)
                {
                    if (judgeValid(istime, ietime, isSampleTime, sInfo.iTime))
                    {
                        return iSampleNum;
                    }
                    iSampleNum = 0;
                    isSampleTime = 0;
                    ieSampleTime = 0;
                }
                else
                {
                    iSampleNum = 0;
                    isSampleTime = 0;
                    ieSampleTime = 0;
                }
            }
            if (judgeValid(istime, ietime, isSampleTime, ieSampleTime))
                return iSampleNum;

            return 0;
        }

        private bool judgeValid(int istime, int ietime, int isSampleTime, int ieSampleTime)
        {
            return isSampleTime <= istime && ietime <= ieSampleTime;
        }

        /// <summary>
        /// 判断是否出来包含切换事件
        /// </summary>
        private bool checkIsHaveHandOverEvent(int ifileid, int istime, int ietime)
        {
            if (!condEventsDic.ContainsKey(ifileid))
                return false;

            List<Event> eventList = condEventsDic[ifileid];
            List<int> evtIDList = new List<int>() { 17, 18, 145, 146, 148, 149, 142, 143, 151, 152 };
            foreach (Event evt in eventList)
            {
                if (evtIDList.Contains(evt.ID) && evt.Time > istime || evt.Time <= ietime)
                {
                    return true;
                }
            }
            return false;
        }
    }

    /// <summary>
    /// 按指定文件回放GSM采样点
    /// </summary>
    public class ReplayFile : DIYReplayFileWithNoWaitBox
    {
        public List<TestPoint> testPointList { get; set; }
        public ReplayFile(MainModel mainModel)
            : base(mainModel)
        {
            IncludeEvent = false;
            IsAddSampleToDTDataManager = false;
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {

            List<string> cols = new List<string>();
            DIYReplayContentOption option = new DIYReplayContentOption();
            if (IncludeTestPoint)
            {
                List<ColumnDefItem> columns = null;

                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("isampleid");
                cols.Add("isampleid");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("itime");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("ilongitude");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("ilatitude");
                checkAndAddSampleRange(columns, ref option);

                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("LAC");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("CI");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("RxLevSub");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("N_RxLev");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("N_BCCH");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("N_BSIC");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("RxQualSub");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("APP_Speed");
                checkAndAddSampleRange(columns, ref option);

                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_SCell_LAC");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_SCell_CI");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_PCCPCH_RSCP");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_NCell_PCCPCH_RSCP");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_NCell_UARFCN");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_NCell_CPI");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_DPCH_RSCP");
                checkAndAddSampleRange(columns, ref option);

                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_GSM_NCell_Rxlev");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_GSM_NCell_ARFCN");
                checkAndAddSampleRange(columns, ref option);
                columns = InterfaceManager.GetInstance().GetColumnDefByShowName("TD_GSM_NCell_BSIC");
                checkAndAddSampleRange(columns, ref option);
            }
            option.EventInclude = true;
            if (!IncludeEvent)
            {
                option.EventInclude = false;
            }
            if (IncludeMessage)
            {
                option.MessageInclude = true;
                option.MessageL3HexCode = true;
            }
            return option;
        }

        /// <summary>
        /// 检查并添加采样点
        /// </summary>
        private void checkAndAddSampleRange(List<ColumnDefItem> columns, ref DIYReplayContentOption option)
        {
            if (columns != null && columns.Count > 0)
                option.SampleColumns.AddRange(columns);
        }

        protected override void query()
        {
            testPointList = new List<TestPoint>();
            base.query();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            testPointList.Add(tp);
        }
    }

    /// <summary>
    /// 实现对采样点ID的排序类
    /// </summary>
    public class TestPointSort
    {
        //实现排序的接口
        public static IComparer<TestPoint> GetCompareBySnId()
        {
            if (comparerBySnId == null)
            {
                comparerBySnId = new CompareBySampleId();
            }
            return comparerBySnId;
        }
        public class CompareBySampleId : IComparer<TestPoint>
        {
            public int Compare(TestPoint x, TestPoint y)
            {
                return x.SN.CompareTo(y.SN);
            }
        }
        private static IComparer<TestPoint> comparerBySnId;
    }

    public class SampleSubInfo
    {
        public int iLAC { get; set; }
        public int iCI { get; set; }
        public int iRxlevSub { get; set; }
        public int iSampleid { get; set; }
        public int iTime { get; set; }
        public double dLongitude { get; set; }
        public double dLatitude { get; set; }
        public List<NbSampleInfo> nbInfoList { get; set; }

        public SampleSubInfo()
        {
            iLAC = 0;
            iCI = 0;
            iRxlevSub = 0;
            iSampleid = 0;
            iTime = 0;
            dLongitude = 0;
            dLatitude = 0;
            nbInfoList = new List<NbSampleInfo>();
        }

        //实现排序的接口
        public static IComparer<SampleSubInfo> GetCompareBySampleId()
        {
            if (comparerBySampleId == null)
            {
                comparerBySampleId = new CompareBySampleId();
            }
            return comparerBySampleId;
        }
        public class CompareBySampleId : IComparer<SampleSubInfo>
        {
            public int Compare(SampleSubInfo x, SampleSubInfo y)
            {
                return x.iSampleid.CompareTo(y.iSampleid);
            }
        }
        private static IComparer<SampleSubInfo> comparerBySampleId;
    }

    public class NbSampleInfo
    {
        public int iNbBcch { get; set; }
        public int iNbBsic { get; set; }
        public int iNbRxlevSub { get; set; }

        public NbSampleInfo()
        {
            iNbBcch = 0;
            iNbBsic = 0;
            iNbRxlevSub = 0;
        }
    }

    public class HoNotIntimeMainInfo
    {
        public string StrCqtName { get; set; }
        public string StrCellName { get; set; }
        public int ILac { get; set; }
        public int ICi { get; set; }
        public double DLongitude { get; set; }
        public double DLatitude { get; set; }
        /// <summary>
        /// 主服采样点数
        /// </summary>
        public int IAllSampleNum { get; set; }
        /// <summary>
        /// 弱覆盖采样点数
        /// </summary>
        public int IWeakCoverSampleNum { get; set; }
        /// 弱覆盖采样点占比
        /// </summary>
        public string strWeakCoverRate
        {
            get
            {
                return (100 * ((float)IWeakCoverSampleNum) / IAllSampleNum).ToString("0.00") + "%";
            }
        }
        /// <summary>
        /// 占用主服次数
        /// </summary>
        public int IServerNum { get; set; }
        /// <summary>
        /// 切换不及时次数
        /// </summary>
        public int INoIntimeNum { get; set; }
        /// <summary>
        /// 切换平均时延
        /// </summary>
        public float FNotIntime { get; set; }
        /// <summary>
        /// 切换不及时较主服电平
        /// </summary>
        public int INotIntimeRxlev { get; set; }
        /// <summary>
        ///  掉话(掉线)次数
        /// </summary>
        public int IDropCallNum { get; set; }
        /// <summary>
        /// 未接通次数
        /// </summary>
        public int INoConnNum { get; set; }
        /// <summary>
        /// 质差(高BLER)次数
        /// </summary>
        public int IWeakQualNum { get; set; }
        /// <summary>
        /// 弱覆盖次数
        /// </summary>
        public int IWeakCoverNum { get; set; }
        /// <summary>
        /// 切换失败次数
        /// </summary>
        public int IHoFailNum { get; set; }

        public HoNotIntimeMainInfo()
        {
            StrCqtName = "";
            StrCellName = "";
            ILac = 0;
            ICi = 0;
            DLongitude = 0;
            DLatitude = 0;
            IAllSampleNum = 0;
            IWeakCoverSampleNum = 0;
            IServerNum = 0;
            INoIntimeNum = 0;
            FNotIntime = 0;
            INotIntimeRxlev = 0;
            IDropCallNum = 0;
            INoConnNum = 0;
            IWeakQualNum = 0;
            IWeakCoverNum = 0;
            IHoFailNum = 0;
        }

        /// <summary>
        /// 数据累加器
        /// </summary>
        public static HoNotIntimeMainInfo fillData(HoNotIntimeMainInfo mainInfo, HoNotIntimeMainInfo newInfo)
        {
            HoNotIntimeMainInfo resultInfo = new HoNotIntimeMainInfo();
            resultInfo.StrCqtName = mainInfo.StrCqtName;
            resultInfo.StrCellName = mainInfo.StrCellName;
            resultInfo.ILac = mainInfo.ILac;
            resultInfo.ICi = mainInfo.ICi;
            resultInfo.DLongitude = mainInfo.DLongitude;
            resultInfo.DLatitude = mainInfo.DLatitude;

            resultInfo.IAllSampleNum += mainInfo.IAllSampleNum + newInfo.IAllSampleNum;
            resultInfo.IWeakCoverSampleNum += mainInfo.IWeakCoverSampleNum + newInfo.IWeakCoverSampleNum;
            resultInfo.IServerNum += mainInfo.IServerNum + newInfo.IServerNum;
            resultInfo.INoIntimeNum += mainInfo.INoIntimeNum + newInfo.INoIntimeNum;
            resultInfo.FNotIntime += (float)Math.Round((mainInfo.FNotIntime * mainInfo.INoIntimeNum + newInfo.FNotIntime * newInfo.INoIntimeNum) / (mainInfo.INoIntimeNum + newInfo.INoIntimeNum), 2);
            resultInfo.INotIntimeRxlev += ((mainInfo.INotIntimeRxlev * mainInfo.IWeakCoverSampleNum + newInfo.INotIntimeRxlev * newInfo.IWeakCoverSampleNum) / (mainInfo.IWeakCoverSampleNum + newInfo.IWeakCoverSampleNum));

            resultInfo.IDropCallNum += mainInfo.IDropCallNum + newInfo.IDropCallNum;
            resultInfo.INoConnNum += mainInfo.INoConnNum + newInfo.INoConnNum;
            resultInfo.IWeakCoverNum += mainInfo.IWeakCoverNum + newInfo.IWeakCoverNum;
            resultInfo.IWeakQualNum += mainInfo.IWeakQualNum + newInfo.IWeakQualNum;
            resultInfo.IHoFailNum += mainInfo.IHoFailNum + newInfo.IHoFailNum;
            
            return resultInfo;
        }

        public override bool Equals(object obj)
        {
            HoNotIntimeMainInfo other = obj as HoNotIntimeMainInfo;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.StrCqtName.Equals(other.StrCqtName) &&
                    this.ILac.Equals(other.ILac) &&
                    this.ICi.Equals(other.ICi));
        }

        public override int GetHashCode()
        {
            return this.ICi.GetHashCode();
        }
    }

    public class HoNotIntimeSubInfo
    {
        public string StrNbCellName { get; set; }
        public int ILac { get; set; }
        public int ICi { get; set; }
        public int IBcch { get; set; }
        public int IBsic { get; set; }
        //回放文件必备
        public int IFileid { get; set; }
        public DateTime DTime { get; set; }
        public int IProjectType { get; set; }
        public int IServiceType { get; set; }
        public string StrSampleTbName { get; set; }
        public string StrLogFile { get; set; }
        /// <summary>
        /// 切换不及时次数
        /// </summary>
        public int INotIntimeNum { get; set; }
        /// <summary>
        /// 切换不及时采样点数
        /// </summary>
        public int INotIntimeSample { get; set; }
        /// <summary>
        /// 切换不及时较主服电平
        /// </summary>
        public int INotIntimeRxlev { get; set; }
        /// <summary>
        /// 成功切换次数
        /// </summary>
        public int IHoSuccNum { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string StrFileName { get; set; }

        public HoNotIntimeSubInfo()
        {
            StrNbCellName = "";
            ILac = 0;
            ICi = 0;
            IBcch = 0;
            IBsic = 0;

            IFileid = 0;
            DTime = Convert.ToDateTime("1970-1-1 8:00:00");
            IProjectType = 0;
            IServiceType = 0;
            StrSampleTbName = "";
            StrLogFile = "";

            INotIntimeNum = 0;
            INotIntimeSample = 0;
            INotIntimeRxlev = 0;
            IHoSuccNum = 0;
            StrFileName = "";
        }

        /// <summary>
        /// 数据累加器
        /// </summary>
        public static void fillData(ref List<HoNotIntimeSubInfo> subInfoList, HoNotIntimeSubInfo newInfo)
        {
            bool isContain = false;
            HoNotIntimeSubInfo resultInfo = new HoNotIntimeSubInfo();
            foreach (HoNotIntimeSubInfo nbInfo in subInfoList)
            {
                if (nbInfo.Equals(newInfo))
                {
                    resultInfo = nbInfo;
                    isContain = true;
                    break;
                }
            }

            if (isContain)
            {
                subInfoList.Remove(resultInfo);
                resultInfo.INotIntimeNum = resultInfo.INotIntimeNum + newInfo.INotIntimeNum;
                resultInfo.INotIntimeSample = resultInfo.INotIntimeSample + newInfo.INotIntimeSample;
                resultInfo.IHoSuccNum = resultInfo.IHoSuccNum + newInfo.IHoSuccNum;
                resultInfo.INotIntimeRxlev = ((resultInfo.INotIntimeRxlev * resultInfo.INotIntimeSample + newInfo.INotIntimeRxlev * newInfo.INotIntimeSample) / (resultInfo.INotIntimeSample + newInfo.INotIntimeSample));
                subInfoList.Add(resultInfo);
            }
            else
            {
                subInfoList.Add(newInfo);
            }
        }

        public override bool Equals(object obj)
        {
            HoNotIntimeSubInfo other = obj as HoNotIntimeSubInfo;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.ILac.Equals(other.ILac) &&
                    this.ICi.Equals(other.ICi) &&
                    this.IFileid.Equals(other.IFileid));
        }

        public override int GetHashCode()
        {
            return this.ICi.GetHashCode();
        }
    }

    public class NbCellKey
    {
        public int iBcch { get; set; }
        public int iBsic { get; set; }

        public NbCellKey()
        {
            iBcch = 0;
            iBsic = 0;
        }

        public override bool Equals(object obj)
        {
            NbCellKey other = obj as NbCellKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.iBcch.Equals(other.iBcch) &&
                    this.iBsic.Equals(other.iBsic));
        }

        public override int GetHashCode()
        {
            return this.iBcch.GetHashCode();
        }
    }

    public class CellKey
    {
        public int iLAC { get; set; }
        public int iCI { get; set; }

        public CellKey()
        {
            iLAC = 0;
            iLAC = 0;
        }

        public override bool Equals(object obj)
        {
            CellKey other = obj as CellKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.iLAC.Equals(other.iLAC) &&
                    this.iCI.Equals(other.iCI));
        }

        public override int GetHashCode()
        {
            return this.iCI.GetHashCode();
        }
    }
}
