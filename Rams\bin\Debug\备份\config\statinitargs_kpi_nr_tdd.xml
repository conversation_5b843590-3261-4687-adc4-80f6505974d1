<?xml version="1.0"?>
<Configs>
  <Config name="StatParamCfg">
    <Item name="configs" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">5G_NR参数</Item>
        <Item typeName="String" key="FDesc" />
        <Item typeName="String" key="FName" />
        <Item typeName="IList" key="children">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">基础指标</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Index</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FileId</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">TestType</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Wtime</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040003</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FileTime</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040004</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040005</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040006</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040007</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">TLLongitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040008</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">TLLatitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040009</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">BRLongitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004000A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">BRLatitude</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004000B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LAC</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004000C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">RAC</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004000D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CI</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004000E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_PCI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040043</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_A频段_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040158</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_D频段_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040159</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_E频段_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04015A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_F频段_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04015B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_FDD900频段_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04015C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_PCI_FDD1800频段_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04015D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PCI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PCI_Count</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040044</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_EARFCN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040151</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040152</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040153</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040154</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040155</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_FDD900M频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040156</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_EARFCN_FDD1800M频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040157</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_SSB_ARFCN</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SSB_ARFCN_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Bandwidth_MHz</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Bandwidth_MHz_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401CF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Bandwidth_MHz_100M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Bandwidth_MHz_80M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Bandwidth_MHz_60M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">时长里程</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004000F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040010</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_2G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040011</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_3G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040013</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040015</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">DMO_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040017</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_CM_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040019</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_CU_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004001B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_CT_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004001D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_5G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004001F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_A_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040020</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D1_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040021</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D2_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040022</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D3_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040023</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D4_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040024</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D5_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040025</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D6_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040026</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D7_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040027</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_D8_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040028</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_E1_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040029</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_E2_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004002A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_E3_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004002B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_F1_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004002C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_F2_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004002D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_FDD900_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004002E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_in_FDD1800_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004002F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">DataUpDownload_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400AC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Nr_in_DataUpDownload_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400AD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Nr_2d6G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Nr_700M_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Nr_CT_2d1G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Nr_CT_3d5G_Duration</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_2G_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040012</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_3G_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040014</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040016</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">DMO_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_80040018</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_CM_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004001A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_CU_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004001C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_4G_CT_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_8004001E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP[-88,)_SS_SINR[-3,)_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400CE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP[-91,)_SS_SINR[-3,)_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040176</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP[-93,)_SS_SINR[-3,)_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040177</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP[-96,)_SS_SINR[-3,)_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040178</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_5G_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401DA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR(,-3)_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401DC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Band_2d6G_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401EA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Band_700M_Distance</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401EB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">覆盖率</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_RSRP</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040016</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040017</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-100,)_And_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040045</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_SINR_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040050</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040054</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-105,)_LTE_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04005D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_Edge</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04005E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-108,)_LTE_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400AE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_And_LTE_SINR_[-3,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400BC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400BD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400BE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400BF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-110,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-93,)_And_LTE_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-95,)_And_LTE_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400DB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-95,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400DF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_A频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040194</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_D频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040195</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_E频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040196</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_F频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040197</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_FDD900频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040198</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_FDD1800频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040199</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-100,)_And_LTE_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRP_[-105,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_SS_RSRP</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-100,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040003</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-105,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040004</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-110,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040005</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-115,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040006</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-120,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040007</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-100,)_And_NR_SS_SINR_[0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04000C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-105,)_And_NR_SS_SINR_[0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04000D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-110,)_And_NR_SS_SINR_[0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04000E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-115,)_And_NR_SS_SINR_[0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04000F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-120,)_And_NR_SS_SINR_[0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040010</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-100,)_And_NR_SS_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040011</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-105,)_And_NR_SS_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040012</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-110,)_And_NR_SS_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040013</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-115,)_And_NR_SS_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040014</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-120,)_And_NR_SS_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040015</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-88,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040051</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_And_NR_SS_SINR_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040052</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-91,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040053</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-105,)_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040059</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_Edge</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04005A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-99,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04005B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-96,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04005C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-93,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-96,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400DC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-110,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400DD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-93,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400DE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-109,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_Min</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-128,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401CB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-113,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401CC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-103,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401CD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-95,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401CE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-99,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_(,-105)_Or_NR_SS_SINR_(,-3)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_Or_NR_SS_SINR_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401DB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-100,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401DD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-93,)_And_NR_SS_SINR_[-2,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401DE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-98,)_And_NR_SS_SINR_[-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-93,)_And_NR_SS_SINR_[-3,)_700M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401EC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_[-93,)_And_NR_SS_SINR_[-3,)_2D6G_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401ED</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_2d6G_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401EE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_700M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401EF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_700M_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_2d6G_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_And_NR_SS_SINR_700M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRP_And_NR_SS_SINR_2d6G_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">质量</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_SINR</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040018</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040019</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_Edge</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040062</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_[-3,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_[-3,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_[-3,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_[-3,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400B9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_[-3,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400BA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_[-3,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400BB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400C9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400CA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400CB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400CC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400CD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040143</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04015E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04015F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040160</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040161</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040162</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_(0,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040163</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_A频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04019A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_D频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04019B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_E频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04019C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_F频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04019D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_FDD900频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04019E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_SINR_FDD1800频段_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04019F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_RSRQ</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRQ_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040065</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRQ_Edge</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040066</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RSRQ_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040081</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_SS_SINR</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040008</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040009</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_[-5,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04000A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_[0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04000B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_Edge</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04005F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_(-3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040060</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_(0,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040061</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_Min</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_700M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_2d6G_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_700M_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_2d6G_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_(-3,)_700M_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_SINR_(-3,)_2d6G_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401F9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_SS_RSRQ</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRQ_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040063</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRQ_Edge</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040064</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRQ_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040080</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRQ_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRQ_Min</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_SS_RSRQ_[-14,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401CA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">应用层</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">APP_TransferedSize</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedSize</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040084</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedSize</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040086</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedSize_Fail</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedSize_Fail</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedSize_700M</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401FA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedSize_700M</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401FB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedSize_2d6G</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401FC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedSize_2d6G</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401FD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">APP_TransferedTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedTime</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040085</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedTime</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040087</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedTime_Fail</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedTime_Fail</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedTime_700M</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401FE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedTime_700M</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401FF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Download_TransferedTime_2d6G</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040200</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">FTP_Upload_TransferedTime_2d6G</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">APP_Speed</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_[0,30M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401AA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_[0,100M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401AB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_(150M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401AC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_(800M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401AD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_[0,2M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401AE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_(80M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401AF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_[0,5M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_UL_(160M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">APP_Speed_DL_[0,60M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401D9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">速率</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_MAC_UL_Throughput</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_UL_Throughput_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040069</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_UL_Throughput_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04006A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_UL_Throughput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_UL_Throughput_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_MAC_DL_Throughput</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_DL_Throughput_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040067</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_DL_Throughput_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040068</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_DL_Throughput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_MAC_DL_Throughput_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_RLC_UL_Throughput</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_UL_Throughput_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040038</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_UL_Throughput_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040039</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_UL_Throughput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_UL_Throughput_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_RLC_DL_Throughput</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_DL_Throughput_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040036</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_DL_Throughput_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040037</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_DL_Throughput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_RLC_DL_Throughput_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400FA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_MAC_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04003B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04003C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[0,2M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040055</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[80M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040057</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_(80M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04008C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04008D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[0,10M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400AF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_(10M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400FB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400FC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[0,2M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400FD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[80M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400FE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_(80M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400FF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040100</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_(10M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[0,10M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040179</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[0,5M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04017A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_(160M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_[0,5M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_UL_(160M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_MAC_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04003D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04003E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_(150M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040047</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,100M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040056</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[800M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040058</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_(800M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04008A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04008B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_(500M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400DA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040103</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_(150M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,100M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040105</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[800M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040106</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_(800M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040107</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040108</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_(500M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040109</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,150M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04017B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,300M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,500M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,150M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,300M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401B9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_MAC_DL_[0,500M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401BA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_RLC_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04001C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04001D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[0M,1M]_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040023</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[0M,5M]_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040024</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[0M,10M]_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040025</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[60M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040026</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[80M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040027</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[100M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040028</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04009B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04010A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04010B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[0M,1M]_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04010C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[0M,5M]_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04010D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[0M,10M]_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04010E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[60M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04010F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[80M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040110</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_[100M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040111</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_UL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040112</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_RLC_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04001A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04001B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[0M,50M]_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04001E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[0M,100M]_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04001F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[450M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040020</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[800M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040021</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[1000M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040022</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04009A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040113</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040114</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[0M,50M]_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040115</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[0M,100M]_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040116</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[450M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040117</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[800M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040118</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_[1000M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040119</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_RLC_DL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04011A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_PHY_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040091</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040092</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_UL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040093</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04011B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04011C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_UL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04011D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_PHY_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04008E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04008F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_DL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040090</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04011E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04011F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PHY_DL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040120</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_PDCP_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040097</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040098</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040099</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040121</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040122</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040123</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_[0,5M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401BB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_[0,5M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401BC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_(160M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401BD</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_UL_(160M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401BE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_PDCP_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040094</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040095</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_Max_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040096</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040124</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040125</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_Max</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040126</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_[0,100M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401BF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_[0,100M)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_(800M,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_PDCP_DL_(800M,)_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401C2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_MAC_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040127</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040128</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_UL_[0,2M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_MAC_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04009E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04009F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040129</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04012A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_MAC_DL_[0,100M)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401A3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_RLC_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04012B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04012C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_RLC_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04012D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_RLC_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04012E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_PHY_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04012F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040130</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_PHY_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04009C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04009D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040131</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PHY_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040132</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_DL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_DL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400A3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040133</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040134</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_UL_SampleNum_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400AA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_UL_Avg_BS</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400AB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040135</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Throughput_DC_PDCP_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040136</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">调度类</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_MCS_UL_Info</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_MCS_UL_Info_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040041</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_MCS_UL_Info_PacketNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040076</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_MCS_DL_Info</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_MCS_DL_Info_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040042</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_MCS_DL_Info_PacketNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040083</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_BPSK_Ratio_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_BPSK_Ratio_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040088</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_BPSK_Ratio_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040089</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040034</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040073</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040033</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040072</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040032</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040071</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_UL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040031</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040070</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_TB0_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_TB0_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040048</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_TB0_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040078</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_TB0_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_TB0_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040049</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_TB0_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040079</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_TB0_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_TB0_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04004A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_TB0_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04007A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_TB0_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_TB0_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04004B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_TB0_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04007B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_TB1_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_TB1_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04004C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_TB1_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04007C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_TB1_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_TB1_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04004D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_TB1_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04007D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_TB1_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_TB1_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04004E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_TB1_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04007E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_TB1_s</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_TB1_s_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04004F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_TB1_s_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04007F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04002E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_QPSK_Ratio_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04006F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04002D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_16QAM_Ratio_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04006E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04002C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_64QAM_Ratio_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04006D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04002B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_256QAM_Ratio_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04006C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_TYPE_DL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_QPSK_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_16QAM_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_64QAM_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_256QAM_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_QPSK_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400EB</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_16QAM_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400EC</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_64QAM_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400ED</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_DL_256QAM_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400EE</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Modulation_TYPE_UL</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_QPSK_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_16QAM_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_64QAM_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400E9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_256QAM_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400EA</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_QPSK_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400EF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_16QAM_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_64QAM_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Modulation_TYPE_UL_256QAM_SampleNum_Percent</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400F2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">误块率</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PUSCH_BLER</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_BLER_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04002F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_BLER_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PUSCH_Initial_BLER</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_Initial_BLER_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040030</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_Initial_BLER_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D2</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PUSCH_Residual_BLER</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_Residual_BLER_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400CF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_Residual_BLER_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D3</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PDSCH_BLER</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDSCH_BLER_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040029</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDSCH_BLER_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D4</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PDSCH_Initial_BLER</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDSCH_Initial_BLER_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04002A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDSCH_Initial_BLER_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D5</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PDSCH_Residual_BLER</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDSCH_Residual_BLER_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDSCH_Residual_BLER_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0400D6</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">重叠覆盖</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MultiCoverage</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">MultiCoverage_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040144</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">MultiCoverage_[3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040145</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">通话质量</Item>
            <Item typeName="String" key="FDesc">包括[语音通话]和[视屏通话]</Item>
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">PESQ_LQ</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04013D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04013E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040146</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040164</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040165</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040166</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040167</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040168</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[3,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040169</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04017C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04017D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04017E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04017F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040180</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040181</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_LQ_[4,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E7</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">PESQ_Score</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04013F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040140</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040147</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04016A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04016B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04016C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04016D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04016E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[3,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04016F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040182</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040183</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040184</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040185</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040186</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040187</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_Score_[4,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E8</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">PESQ_MOS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040141</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040142</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040148</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040170</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040171</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040172</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040173</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040174</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[3,)_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040175</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_A频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040188</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_D频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040189</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_E频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04018A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_F频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04018B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_FDD900频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04018C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_FDD1800频段_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04018D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">PESQ_MOS_[4,)_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E9</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">VOLTE_RTP_Packets_Num</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Num_Audio_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040149</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Num_Audio_Sum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04014A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Num_Video_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04014C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Num_Video_Sum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04014D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">VOLTE_RTP_Packets_Lost_Num</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Lost_Num_Audio_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04014B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Lost_Num_Video_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04014E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Lost_Num_Audio_Sum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04014F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">VOLTE_RTP_Packets_Lost_Num_Video_Sum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040150</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">其他</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">LTE_Pathloss</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_Pathloss_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04003A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">LTE_Pathloss_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040075</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PUSCH_Path_Loss</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_Path_Loss_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040035</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PUSCH_Path_Loss_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040074</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_CQI</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_CQI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04003F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_CQI_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040040</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PDCCH_DL_GrantCount</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDCCH_DL_GrantCount_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040046</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDCCH_DL_GrantCount_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040077</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PDCCH_UL_GrantCount</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDCCH_UL_GrantCount_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04006B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PDCCH_UL_GrantCount_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040082</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_Rank_Indicator</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Rank_Indicator_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040137</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Rank_Indicator_Rank1_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040138</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Rank_Indicator_Rank2_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA040139</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_Rank_Indicator_Rank3_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04013A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_PRB_Num_DL_Slot</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PRB_Num_DL_Slot_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04013B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_PRB_Num_DL_Slot_Avg</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA04013C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">NR_BAND</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_BAND_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401DF</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_BAND_41_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E0</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">NR_BAND_28_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Nr_BA0401E1</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>