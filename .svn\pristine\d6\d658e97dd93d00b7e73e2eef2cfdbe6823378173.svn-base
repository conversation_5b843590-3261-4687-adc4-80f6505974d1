﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellOccupySameCarTest : DIYQueryFileInfoBase
    {
        string hostKeyStr = "下载";
        string guestKeyStr = "上传";

        protected List<FileInfo> hostFileList;
        protected List<FileInfo> guestFileList;
        protected List<NRSameCarTestResult> lstOccupyResults;
        NRCellOccupySameCarTestCondition curCondition = new NRCellOccupySameCarTestCondition();
        protected static readonly object lockObj = new object();

        public NRCellOccupySameCarTest()
            : base(MainModel.GetInstance())
        {
            hostFileList = new List<FileInfo>();
            guestFileList = new List<FileInfo>();
            lstOccupyResults = new List<NRSameCarTestResult>();
        }

        public override string Name
        {
            get { return "同车小区占用"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.Region;
        }

        protected virtual void queryFileToAnalyse()
        {
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            condition.FileInfos = MainModel.FileInfos;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35044, this.Name);
        }

        protected virtual bool getCondition()
        {
            NRCellOccupySameCarTestDlg dlg = new NRCellOccupySameCarTestDlg();
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondition = dlg.GetCondtion();
                return true;
            }
            return false;
        }

        protected void getReadyBeforeQuery()
        {
            hostFileList.Clear();
            guestFileList.Clear();
            lstOccupyResults.Clear();
            if (curCondition.Type == NRCellOccupySameCarTestType.DlAndUl)
            {
                hostKeyStr = "下载";
                guestKeyStr = "上传";
            }
            else
            {
                hostKeyStr = "VOLTE语音";
                guestKeyStr = "下载";
            }
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            queryFileToAnalyse();

            if (condition.FileInfos == null || condition.FileInfos.Count <= 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("未查询到文件！");
                return;
            }

            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);

            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;

            MainModel.FireDTDataChanged(this);
            fireShowForm();
        }

        protected void analyseFiles()
        {
            try
            {
                hostFileList = GetHasKeyStrFiles(condition.FileInfos, hostKeyStr);
                guestFileList = GetHasKeyStrFiles(condition.FileInfos, guestKeyStr);
                Dictionary<FileInfo, List<FileInfo>> dicPair;
                if (curCondition.Type == NRCellOccupySameCarTestType.DlAndUl)
                {
                    dicPair = getFilePairDlAndUl(ref hostFileList, ref guestFileList);
                }
                else
                {
                    dicPair = getFilePairVoLteAndDl(ref hostFileList, ref guestFileList);
                }


                foreach (KeyValuePair<FileInfo, List<FileInfo>> pair in dicPair)
                {
                    NRSameCarTestHelper occupyAna = new NRSameCarTestHelper(pair.Key, pair.Value, curCondition.Type);
                    occupyAna.SetCondition(this.condition);
                    occupyAna.Analyse();

                    occupyAna.OccupyResult.Calculate();
                    lstOccupyResults.Add(occupyAna.OccupyResult);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        public static List<FileInfo> GetHasKeyStrFiles(List<FileInfo> lstFiles, string keyWords)
        {
            List<FileInfo> lstRts = new List<FileInfo>();
            if (lstFiles != null)
            {
                foreach (FileInfo file in lstFiles)
                {
                    if (file.Name.ToUpper().Contains(keyWords))
                    {
                        lstRts.Add(file);
                    }
                }
            }
            lstRts.Sort(FileInfo.GetCompareByBeginTimeAsc());
            return lstRts;
        }

        #region 下载上传对比(一对一)
        private Dictionary<FileInfo, List<FileInfo>> getFilePairDlAndUl(ref List<FileInfo> lstDl, ref List<FileInfo> lstUl)
        {
            Dictionary<FileInfo, List<FileInfo>> dicPair = new Dictionary<FileInfo, List<FileInfo>>();

            for (int indexDl = 0; indexDl < lstDl.Count; indexDl++)
            {
                FileInfo fileDl = lstDl[indexDl];
                bool isGroup = false;
                for (int indexUl = 0; indexUl < lstUl.Count; indexUl++)
                {
                    FileInfo fileUl = lstUl[indexUl];
                    if (isSameCarGroupDlAndUl(fileDl, fileUl))
                    {
                        isGroup = true;
                        dicPair.Add(fileDl, new List<FileInfo> { fileUl });

                        lstUl.Remove(fileUl);
                        break;
                    }
                }

                if (isGroup)
                {
                    lstDl.Remove(fileDl);
                    indexDl--;
                }
            }

            return dicPair;
        }

        protected virtual bool isSameCarGroupDlAndUl(FileInfo fdownload, FileInfo fupload)
        {
            if (fdownload.ServiceType == (int)ServiceType.NR_NSA_TDD_DATA
                     && fupload.ServiceType == (int)ServiceType.NR_NSA_TDD_DATA
                     && fdownload.ProjectID == fupload.ProjectID
                     && fdownload.CarrierType == (int)CarrierType.ChinaMobile
                     && fupload.CarrierType == (int)CarrierType.ChinaMobile)
            {
                bool isSameCar = //Math.Abs(fdownload.BeginTime - fupload.BeginTime) < 150 &&
                //Math.Abs(fdownload.EndTime - fupload.EndTime) < 150 &&
                Math.Abs(fdownload.TopLeftLongitude - fupload.TopLeftLongitude) < 10000 &&
                Math.Abs(fdownload.BottomRightLongitude - fupload.BottomRightLongitude) < 10000 &&
                Math.Abs(fdownload.TopLeftLatitude - fupload.TopLeftLatitude) < 9000 &&
                Math.Abs(fdownload.BottomRightLatitude - fupload.BottomRightLatitude) < 9000;//&&
                //fdownload.BeginTime < fupload.EndTime && fdownload.EndTime > fupload.BeginTime;

                return isSameCar;
                //string dlFileName = getValidCompareStringDlAndUl(fdownload);
                //string ulFileName = getValidCompareStringDlAndUl(fupload);
                //return dlFileName.Equals(ulFileName);
            }
            return false;
        }

        protected string getValidCompareStringDlAndUl(FileInfo file)
        {
            string fileName = file.Name.ToUpper();
            if (fileName.Contains("MS") && (fileName.Contains(guestKeyStr) || fileName.Contains(hostKeyStr)))
            {
                fileName = fileName.Replace(guestKeyStr, "").Replace(hostKeyStr, "");
                int msIndex = fileName.LastIndexOf("MS");
                if (msIndex > -1)
                {
                    fileName = fileName.Substring(0, msIndex);
                    return fileName;
                }
            }
            return fileName;
        }

        #endregion

        #region VoLTE语音和下载对比(一对多)

        private Dictionary<FileInfo, List<FileInfo>> getFilePairVoLteAndDl(ref List<FileInfo> lstVolte, ref List<FileInfo> lstDl)
        {
            Dictionary<FileInfo, List<FileInfo>> dicPair = new Dictionary<FileInfo, List<FileInfo>>();

            for (int indexVolte = 0; indexVolte < lstVolte.Count; indexVolte++)
            {
                FileInfo fileVolte = lstVolte[indexVolte];

                bool isGroup = false;
                if (!dicPair.ContainsKey(fileVolte))
                {
                    isGroup = dealDLFilePair(lstVolte, lstDl, dicPair, fileVolte, isGroup);
                }
                else
                {
                    isGroup = true;
                }

                if (isGroup)
                {
                    lstVolte.Remove(fileVolte);
                    indexVolte--;
                }
            }

            return dicPair;
        }

        private bool dealDLFilePair(List<FileInfo> lstVolte, List<FileInfo> lstDl, Dictionary<FileInfo, List<FileInfo>> dicPair, FileInfo fileVolte, bool isGroup)
        {
            for (int indexDl = 0; indexDl < lstDl.Count; indexDl++)
            {
                FileInfo fielDl = lstDl[indexDl];
                if (isSameCarGroupVoLteAndDl(fileVolte, fielDl))
                {
                    isGroup = true;
                    List<FileInfo> fileList = getSameFileDlAndDl(indexDl, lstDl, fileVolte.EndTime);
                    dicPair[fileVolte] = fileList;

                    FileInfo fileVoltePair = getVolteMoMtPairFile(fileVolte, lstVolte);
                    if (fileVoltePair != null)
                    {
                        dicPair[fileVoltePair] = fileList;
                    }

                    foreach (FileInfo file in fileList)
                    {
                        lstDl.Remove(file);
                    }
                    break;
                }
            }

            return isGroup;
        }

        protected virtual bool isSameCarGroupVoLteAndDl(FileInfo fVolte, FileInfo fDl)
        {
            return fVolte.ServiceType == (int)ServiceType.LTE_TDD_VOLTE && fDl.ServiceType == (int)ServiceType.LTE_TDD_DATA
                && fVolte.ProjectID == fDl.ProjectID
                && fVolte.CarrierType == (int)CarrierType.ChinaMobile && fDl.CarrierType == (int)CarrierType.ChinaMobile
                && (getValidCompareStringVoLteAndDl(fVolte).Equals(getValidCompareStringVoLteAndDl(fDl)));
        }

        protected string getValidCompareStringVoLteAndDl(FileInfo file)
        {
            string fileName = file.Name.ToUpper();
            if (fileName.Contains("MS") && (fileName.Contains(guestKeyStr) || fileName.Contains(hostKeyStr)))
            {
                int msIndex = fileName.LastIndexOf("MS");
                if (msIndex > -1)
                {
                    fileName = fileName.Substring(0, msIndex).Replace(guestKeyStr, "").Replace(hostKeyStr, "");
                    string[] strArray = fileName.Split('_');
                    if (strArray.Length > 3)
                    {
                        string strRemove = strArray[strArray.Length - 3];
                        fileName = fileName.Replace(strRemove, "");
                        return fileName;
                    }
                }
            }
            return fileName;
        }

        private List<FileInfo> getSameFileDlAndDl(int curFileIndex, List<FileInfo> lstDl, int volteFileEndTime)
        {
            List<FileInfo> fileList = new List<FileInfo>();
            FileInfo fileDl = lstDl[curFileIndex];
            for (int i = curFileIndex; i < lstDl.Count; i++)
            {
                FileInfo fi = lstDl[i];
                bool isSamCar = false;
                if (i == curFileIndex)
                {
                    isSamCar = true;
                }
                else
                {
                    isSamCar = isSameCarGroupDlAndDl(fileDl, fi);
                }

                if (isSamCar)
                {
                    fileList.Add(fi);
                    if ((volteFileEndTime - fi.EndTime <= curCondition.TimeSpanVolteAndDl) || fi.EndTime > volteFileEndTime)
                    {
                        break;
                    }
                }
            }
            return fileList;
        }

        protected virtual bool isSameCarGroupDlAndDl(FileInfo fDl1, FileInfo fDl2)
        {
            return fDl1.ServiceType == (int)ServiceType.LTE_TDD_DATA && fDl2.ServiceType == (int)ServiceType.LTE_TDD_DATA
                && fDl1.ProjectID == fDl2.ProjectID
                && fDl1.CarrierType == (int)CarrierType.ChinaMobile && fDl2.CarrierType == (int)CarrierType.ChinaMobile
                && (getValidCompareStringDlAndDl(fDl1).Equals(getValidCompareStringDlAndDl(fDl2)));
        }

        protected string getValidCompareStringDlAndDl(FileInfo fileDl)
        {
            string fileName = fileDl.Name.ToUpper();
            int lackIndex = fileName.LastIndexOf("_缺失");
            if (lackIndex > 0)
            {
                int lackMinuteIndex = fileName.LastIndexOf("分钟.");
                if (lackMinuteIndex > lackIndex)
                {
                    int length = lackMinuteIndex - lackIndex + 2;
                    fileName = fileName.Remove(lackIndex, length);
                }
            }
            string[] strArray = fileName.Split('_');
            if (strArray.Length > 2)
            {
                int index = 0;
                StringBuilder strb = new StringBuilder();
                foreach (string str in strArray)
                {
                    index++;
                    if (index != strArray.Length - 1)
                    {
                        strb.Append(str);
                    }
                }
                return strb.ToString();
            }

            return fileName;
        }

        private FileInfo getVolteMoMtPairFile(FileInfo fileVolte, List<FileInfo> lstVolte)
        {
            FileInfo file = null;
            if (fileVolte != null && lstVolte != null)
            {
                file = lstVolte.Find(delegate (FileInfo x)
                {
                    return x.ID == fileVolte.EventCount;
                });
            }
            return file;
        }
        #endregion



        protected void fireShowForm()
        {
            NRCellOccupySameCarTestForm form = MainModel.GetObjectFromBlackboard(typeof(NRCellOccupySameCarTestForm)) as NRCellOccupySameCarTestForm;

            if (form == null || form.IsDisposed)
            {
                form = new NRCellOccupySameCarTestForm();
            }
            form.FillData(this.condition, hostFileList, guestFileList, lstOccupyResults, curCondition.Type);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();
        }
    }

    public class NRCellOccupySameCarTestByFile : NRCellOccupySameCarTest
    {
        private NRCellOccupySameCarTestByFile()
            : base()
        {
        }

        private static NRCellOccupySameCarTestByFile instance = null;
        public static NRCellOccupySameCarTestByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRCellOccupySameCarTestByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR同车小区占用(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
    }

    public class NRCellOccupySameCarTestByRegion : NRCellOccupySameCarTest
    {
        protected NRCellOccupySameCarTestByRegion()
            : base()
        {
        }

        private static NRCellOccupySameCarTestByRegion instance = null;
        public static NRCellOccupySameCarTestByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRCellOccupySameCarTestByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR同车小区占用(按区域)"; }
        }
    }
}
