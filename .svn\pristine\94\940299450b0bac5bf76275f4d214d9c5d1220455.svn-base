﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Stat
{
    public class FileKPIQuery : ReportStatQueryBase
    {
        public Dictionary<string, StatInfoBase> FileKeyDataDic { get; set; }

        public FileKPIQuery(ReporterTemplate template, Dictionary<string, StatInfoBase> fileKeyDataDic)
            : base()
        {
            this.rptTemplate = template;
            this.IsShowResultForm = false;
            this.FileKeyDataDic = fileKeyDataDic;
        }
        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.log;
        }

        public override string Name
        {
            get { return "工作量统计"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
                package.Content.PrepareAddParam();
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            return true;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            // Method intentionally left empty.
        }

        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);

            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            StatInfoBase tmp = new StatInfoBase();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = GetKeyUnionString(this.rptTemplate, tmp);
            StatInfoBase fsi = null;
            if (this.FileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.FileKeyDataDic[flieKey] = fsi;
            }
            if (FileKeyDataDic.ContainsKey("All"))
            {
                FileKeyDataDic["All"].KPIData.AddStatDataTotal(fi, singleStatData, false);
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            StatInfoBase tmp = new StatInfoBase();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = GetKeyUnionString(this.rptTemplate, tmp);
            StatInfoBase fsi = null;
            if (this.FileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.FileKeyDataDic[flieKey] = fsi;
            }
            if (FileKeyDataDic.ContainsKey("All"))
            {
                FileKeyDataDic["All"].KPIData.AddStatDataTotal(fi, singleStatData, false);
            }
        }
        protected override string GetKeyValue(string keyFieldRet, ReporterTemplate tpl, StatInfoBase statInfo)
        {
            string val = "";
            if (statInfo == null || statInfo.FileHeader == null)
            {
                val = "-";
            }
            else
            {
                val = GetKeyValueBase(keyFieldRet, tpl
                    , statInfo.FileHeader.AreaTypeID, statInfo.FileHeader.AreaID, statInfo.FileHeader.DistrictID, statInfo);
            }

            return val;
        }
    }
}
