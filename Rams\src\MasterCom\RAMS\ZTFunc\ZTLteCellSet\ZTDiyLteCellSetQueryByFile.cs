﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyLteCellSetQueryByFile : DIYSampleByRegion
    {
        protected Dictionary<string, Dictionary<LTECell, LteCellInfo>> fileLteCellInfoDic;
        protected Dictionary<string, Dictionary<string, LteCellInfo>> fileNotFindCellInfoDic;

        public ZTDiyLteCellSetQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public ZTDiyLteCellSetQueryByFile(bool isVoLTE)
            : this(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }

        public override string Name
        {
            get { return "LTE小区集（按文件）"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22051, this.Name);
        }
        
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }
        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            //AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);

        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            //AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRQ");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_SINR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
       
            return cellSetGroup;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            fileLteCellInfoDic = new Dictionary<string, Dictionary<LTECell, LteCellInfo>>();
            fileNotFindCellInfoDic = new Dictionary<string, Dictionary<string, LteCellInfo>>();
        }

        protected override void getResultAfterQuery()
        {
            statCell();
            fileNames.Clear();
            fileSampleCount.Clear();
        }

        protected override void query()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            regionMopDic = new Dictionary<string, MapOperation2>();
            InitRegionMop2();
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                WaitBox.CanCancel = true;
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

            ShowResultForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void ShowResultForm()
        {
            LTECellSetByFileForm form = MainModel.CreateResultForm(typeof(LTECellSetByFileForm)) as LTECellSetByFileForm;
            form.FillData(fileLteCellInfoDic, fileNotFindCellInfoDic);
            form.Visible = true;
            form.BringToFront();
        }

        Dictionary<string, MapOperation2> regionMopDic = null;
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }


        /// <summary>
        /// 定位所在网格
        /// </summary>
        /// <param name="iType"></param>
        /// <returns>返回区域名称</returns>
        //private string getGrid(double longitude, double latitude)
        //{
        //    foreach (string strKey in regionMopDic.Keys)
        //    {
        //        if (regionMopDic[strKey].CheckPointInRegion(longitude, latitude))
        //        {
        //            return strKey;
        //        }
        //    }
        //    return null;
        //}

        protected override void doWithDTData(TestPoint tp)
        {
            if (!isValidLTEData(tp))
                return;

            string fileName = tp.FileName;
            if (!fileNames.Contains(fileName))
            {
                fileNames.Add(fileName);
            }

            LTECell cell = tp.GetMainCell_LTE();
            if (cell == null)
            {
                addNotFindCellInfo(tp);
                return;
            }

            Dictionary<LTECell, LteCellInfo> lteCellInfoDic = null;
            if(!fileLteCellInfoDic.TryGetValue(fileName, out lteCellInfoDic))
            {
                lteCellInfoDic = new Dictionary<LTECell, LteCellInfo>();
                fileLteCellInfoDic.Add(fileName, lteCellInfoDic);
            }

            if (lteCellInfoDic.ContainsKey(cell))
            {
                LteCellInfo lcInfo = lteCellInfoDic[cell];
                lcInfo.calInfo(cell, tp);
            }
            else
            {
                LteCellInfo lcInfo = new LteCellInfo();
                lcInfo.FileName = fileName;//文件名
                lcInfo.Cellname = cell.Name;
                lcInfo.CellCode = cell.Code;
                lcInfo.Tac = (int)(ushort)tp["lte_TAC"];
                lcInfo.Eci = (int)tp["lte_ECI"];
                lcInfo.SampleCount = 1;

                lteCellInfoDic.Add(cell, lcInfo);
            }

            if (fileSampleCount.ContainsKey(fileName))
            {
                fileSampleCount[fileName]++;
            }
            else
            {
                fileSampleCount[fileName] = 1;
            }
        }

        private bool isValidLTEData(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            float? rsrq = (float?)tp["lte_RSRQ"];
            float? sinr = (float?)tp["lte_SINR"];

            if (tp["lte_TAC"] == null || tp["lte_ECI"] == null
                || tp["lte_EARFCN"] == null || tp["lte_PCI"] == null
                || rsrp == null || rsrq == null || sinr == null)
            {
                return false;
            }
            else if (rsrp < -140 || rsrp > -10 || rsrq < -100 || rsrq > 100 || sinr < -100 || sinr > 100)
            {
                return false;
            }
            return true;
        }

        private void addNotFindCellInfo(TestPoint tp)
        {
            int iTAC = (int)(ushort)tp["lte_TAC"];
            int iEci = (int)tp["lte_ECI"];
            string strTAC_ECI = "" + iTAC + "_" + iEci;
            string fileName = tp.FileName;//文件名

            Dictionary<string, LteCellInfo> notFindCellInfoDic = null;

            if (!fileNotFindCellInfoDic.TryGetValue(fileName, out notFindCellInfoDic))
            {
                notFindCellInfoDic = new Dictionary<string, LteCellInfo>();
                fileNotFindCellInfoDic.Add(fileName, notFindCellInfoDic);
            }

            if (notFindCellInfoDic.ContainsKey(strTAC_ECI))
            {
                float rsrp = (float)tp["lte_RSRP"];
                float rsrq = (float)tp["lte_RSRQ"];
                float sinr = (float)tp["lte_SINR"];

                notFindCellInfoDic[strTAC_ECI].MaxRsrp = getMaxValue(notFindCellInfoDic[strTAC_ECI].MaxRsrp, rsrp);
                notFindCellInfoDic[strTAC_ECI].MaxRsrq = getMaxValue(notFindCellInfoDic[strTAC_ECI].MaxRsrq, rsrq);
                notFindCellInfoDic[strTAC_ECI].MaxSinr = getMaxValue(notFindCellInfoDic[strTAC_ECI].MaxSinr, sinr);

                notFindCellInfoDic[strTAC_ECI].MinRsrp = getMinValue(notFindCellInfoDic[strTAC_ECI].MinRsrp, rsrp);
                notFindCellInfoDic[strTAC_ECI].MinRsrq = getMinValue(notFindCellInfoDic[strTAC_ECI].MinRsrq, rsrq);
                notFindCellInfoDic[strTAC_ECI].MinSinr = getMinValue(notFindCellInfoDic[strTAC_ECI].MinSinr, sinr);

                notFindCellInfoDic[strTAC_ECI].AvgRsrp = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgRsrp * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + rsrp) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);
                notFindCellInfoDic[strTAC_ECI].AvgRsrq = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgRsrq * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + rsrq) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);
                notFindCellInfoDic[strTAC_ECI].AvgSinr = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgSinr * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + sinr) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);

                notFindCellInfoDic[strTAC_ECI].SampleCount++;
            }
            else
            {
                LteCellInfo lcInfo = new LteCellInfo();
                lcInfo.FileName = fileName;//文件名
                lcInfo.Cellname = strTAC_ECI;//匹配不上的小区显示为空
                lcInfo.Tac = iTAC;
                lcInfo.Eci = iEci;
                lcInfo.SampleCount = 1;

                notFindCellInfoDic.Add(strTAC_ECI, lcInfo);
            }

            if(fileSampleCount.ContainsKey(fileName))
            {
                fileSampleCount[fileName]++;
            }
            else
            {
                fileSampleCount[fileName] = 1;
            }
        }

        private static float getMaxValue(float infoValue, float tpValue)
        {
            if (infoValue < tpValue)
            {
                infoValue = tpValue;
            }
            return infoValue;
        }

        private static float getMinValue(float infoValue, float tpValue)
        {
            if (infoValue > tpValue)
            {
                infoValue = tpValue;
            }
            return infoValue;
        }

        private void statCell()
        {
            foreach (string fileName in fileNames)
            {
                long totalCount = 0;
                fileSampleCount.TryGetValue(fileName, out totalCount);

                if (fileLteCellInfoDic.Count > 0 && fileLteCellInfoDic.ContainsKey(fileName))
                {
                    foreach (LteCellInfo lcInfo in fileLteCellInfoDic[fileName].Values)
                    {
                        setCellInfo(fileName, totalCount, lcInfo);
                    }
                }

                if (fileNotFindCellInfoDic.Count > 0 && fileNotFindCellInfoDic.ContainsKey(fileName))
                {
                    foreach (LteCellInfo lcInfo in fileNotFindCellInfoDic[fileName].Values)
                    {
                        setCellInfo(fileName, totalCount, lcInfo);
                    }
                }
            }
        }

        private static void setCellInfo(string fileName, long totalCount, LteCellInfo lcInfo)
        {
            if (lcInfo.FileName == fileName && totalCount != 0)
            {
                lcInfo.SampleTotalCount = totalCount;
                lcInfo.SampleCountRatio = float.Parse((lcInfo.SampleCount * 100.0 / totalCount).ToString("0.00"));
            }
        }

        protected List<string> fileNames = new List<string>();
        protected Dictionary<string, long> fileSampleCount = new Dictionary<string, long>();

    }

}
