﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCellWrongDir;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanCellWrongDirQuery : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        Dictionary<ICell, CellWrongDirItem_NRScan> cellWrongDirDic = null;

        private static NRScanCellWrongDirQuery instance = null;
        protected static readonly object lockObj = new object();
        public static NRScanCellWrongDirQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRScanCellWrongDirQuery();
                    }
                }
            }
            return instance;
        }

        protected NRScanCellWrongDirQuery()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_Scan);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "覆盖不符_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36009, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return NRTpHelper.InitNrScanParamSample(NRTpHelper.NrScanTpManager.RsrpThemeName);
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            FuncCond = FuncCond ?? new CellWrongDirCondition(-120, 30, 60, 0, false, new TimePeriod(MainModel.PPStartTime, MainModel.PPEndTime), new TimePeriod(MainModel.PPStartTime, MainModel.PPEndTime));
            CellWrongDirSettingDlg_TD dlg = new CellWrongDirSettingDlg_TD();
            dlg.SetCondition(FuncCond);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            FuncCond = dlg.GetConditon();
            this.cellWrongDirDic = new Dictionary<ICell, CellWrongDirItem_NRScan>();
            this.finalResultList = new List<CellWrongDirItem_NRScan>();
            return true;
        }

        protected override void FireShowFormAfterQuery()
        {
            var scanCellWrongDirForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NRScanCellWrongDirForm)) as NRScanCellWrongDirForm;
            if (scanCellWrongDirForm == null || scanCellWrongDirForm.IsDisposed)
            {
                scanCellWrongDirForm = new NRScanCellWrongDirForm();
            }
            scanCellWrongDirForm.FillData(finalResultList);
            scanCellWrongDirForm.Owner = MainModel.MainForm;
            scanCellWrongDirForm.Visible = true;
            scanCellWrongDirForm.BringToFront();
            finalResultList = null;
        }

        protected virtual float? getRxLev(TestPoint tp, int idx)
        {
            return NRTpHelper.NrScanTpManager.GetCellRsrp(tp, idx);
        }

        public CellWrongDirCondition FuncCond { get; set; }
        protected override void doWithDTData(TestPoint tp)
        {
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var index in groupDic.Values)
            {
                NRCell cell = tp.GetCell_NRScan(index);
                if (cell == null)
                {
                    continue;
                }

                float? rxLev = getRxLev(tp, index);
                if (!FuncCond.FilterByValue(rxLev))
                {
                    bool inRange = FuncCond.FilterByDistance(tp.Distance2(cell.Longitude, cell.Latitude));
                    if (!inRange)
                    {
                        bool wrongDir = FuncCond.IsWrongDir(cell, tp);
                        CellWrongDirItem_NRScan wrongItem = null;
                        if (!cellWrongDirDic.TryGetValue(cell, out wrongItem))
                        {
                            wrongItem = new CellWrongDirItem_NRScan(cell);
                            cellWrongDirDic[cell] = wrongItem;
                        }
                        wrongItem.AddPoint(tp, (float)rxLev, wrongDir, index == 0);
                    }
                }
            }
        }

        private List<CellWrongDirItem_NRScan> finalResultList = null;
        protected override void getResultAfterQuery()
        {
            if (cellWrongDirDic == null)
            {
                return;
            }
            foreach (CellWrongDirItem_NRScan item in cellWrongDirDic.Values)
            {
                if (item.WrongPntCnt == 0 || FuncCond.FilterByRate(item.WrongRate))
                {
                    continue;
                }
                finalResultList.Add(item);
            }
            cellWrongDirDic = null;
        }
    }
}


