﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS
{
    /// <summary>
    /// 网络类型
    /// </summary>
    public class CQTNetType
    {
        public bool GSMCheckBoxRes { get; set; }
        public bool TDCheckBoxRes { get; set; }
        public CQTNetType()
        {
            GSMCheckBoxRes = false;
            TDCheckBoxRes = false;
        }
        /// <summary>
        /// 获得网络类型，GSM为1，TD为2
        /// </summary>
        /// <returns></returns>
        public int GetNetType()
        {
            return GSMCheckBoxRes ? 1 : 2;
        }
    }

    /// <summary>
    /// 小区集
    /// </summary>
    public class CQTCellVillage
    {
        public bool MainCellCheckBoxRes { get; set; }
        public bool CoverCellChaeckBoxRes { get; set; }
        public bool AllCellChaeckBoxRes { get; set; }
        public CQTCellVillage()
        {
            MainCellCheckBoxRes = false;
            CoverCellChaeckBoxRes = false;
            AllCellChaeckBoxRes = false;
        }
        /// <summary>
        /// 获得小区集信息，1:主覆盖小区;2:覆盖小区;3:全部小区
        /// </summary>
        /// <returns></returns>
        public int GetCellVillage()
        {
            if (MainCellCheckBoxRes)
            {
                return 1;
            }
            if (CoverCellChaeckBoxRes)
            {
                return 2;
            }
            if (AllCellChaeckBoxRes)
            {
                return 3;
            }
            return 0;
        }
    }

    /// <summary>
    /// 对比时段
    /// </summary>
    public class CQTCompareTime
    {
        public bool ThreedDayCheckBoxRes{ get; set; }
        public bool FiveDayCheckBoxRes{ get; set; }
        public bool TenDayCheckBoxRes{ get; set; }
        public DateTime dTimePickerSt{ get; set; }
        public DateTime dTimePickerEt{ get; set; }
        public DateTime compareDTimePickerSt{ get; set; }
        public DateTime compareDTimePickerEt{ get; set; }
        public CQTCompareTime()
        {
        }

        public int GetCompareTime()
        {
            TimeSpan sp = compareDTimePickerEt.AddMinutes(-1).Subtract(compareDTimePickerSt);
            return sp.Days;

        }
        public DateTime GetSTime()
        {
            return dTimePickerSt;
        }
        public DateTime GetCompareETime()
        {
            return compareDTimePickerEt.AddMinutes(-1);
        }
        public DateTime GetEtime()
        {
            return dTimePickerEt;
        }
        public DateTime GetCompareSTime()
        {
            return compareDTimePickerSt;
        }
    }

    /// <summary>
    /// 分析模式
    /// </summary>
    public class CQTAnalysisModel
    {
        public bool AverageModelCheckBoxRes{ get; set; }
        public bool MaxModelCheckBoxRes{ get; set; }
        public bool MinModelCheckBoxRes{ get; set; }
        public CQTAnalysisModel()
        {
            AverageModelCheckBoxRes = false;
            MaxModelCheckBoxRes = false;
            MinModelCheckBoxRes = false;
        }
        public int GetAnalysisModel()
        {
            if (AverageModelCheckBoxRes)
            {
                return 1;
            }
            if (MaxModelCheckBoxRes)
            {
                return 2;
            }
            if (MinModelCheckBoxRes)
            {
                return 3;
            }
            return 0;
        }
    }
   
}
