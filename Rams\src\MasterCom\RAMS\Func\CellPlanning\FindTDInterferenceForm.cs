using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class FindTDInterferenceForm : Form
    {
        MainModel mainModel;
        public FindTDInterferenceForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }

        private void buttonFind_Click(object sender, EventArgs e)
        {
            new TDInterferenceForm(mainModel, (int)numericUpDownFreq.Value).Show(Owner);
        }

        private void buttonClose_Click(object sender, EventArgs e)
        {
            Close();
        }
    }
}