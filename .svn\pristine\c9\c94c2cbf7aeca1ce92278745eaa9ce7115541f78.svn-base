﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RsrpEarfcnForm : MinCloseForm
    {
        public RsrpEarfcnForm()
            : base()
        {    
            InitializeComponent();
            init();
        }
        private List<EarfcnCoverageRangeItem> rangeInfoList = new List<EarfcnCoverageRangeItem>();
        private void init()
        {
            this.olvColumnRegion.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.RegionName;
            };
            this.olvColumnRsrpRange.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.RsrpRangeName;
            };

            this.olvColumnTddEarfcnD.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.EarfcnDCount;
            };
            this.olvColumnTddPercentD.AspectGetter=delegate(object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.PercentEarfcnD.ToString("0.##%");
            };

            this.olvColumnTddEarfcnE.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.EarfcnECount;
            };
            this.olvColumnTddPercentE.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.PercentEarfcnE.ToString("0.##%");
            };

            this.olvColumnTddEarfcnF.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.EarfcnFCount;
            };
            this.olvColumnTddPercentF.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.PercentEarfcnF.ToString("0.##%");
            };

            this.olvColumnTddEarfcn900.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.EarfcnFDD_900Count;
            };
            this.olvColumnTddPercent900.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.PercentEarfcnFDD_900.ToString("0.##%");
            };

            this.olvColumnTddEarfcn1800.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.EarfcnFDD_1800Count;
            };
            this.olvColumnTddPercent1800.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.PercentEarfcnFDD_1800.ToString("0.##%");
            };
            this.olvColumnTddPointCount.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.TddPointCount;
            };
            this.olvColumnFddPointCount.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.FddPointCount;
            };
            this.olvColumnPointCount.AspectGetter = delegate (object row)
            {
                EarfcnCoverageRangeItem info = row as EarfcnCoverageRangeItem;
                return info.PointCount;
            };
        }

        public void FillData(EarfcnCoverageResult result)
        {
            objectListView.ClearObjects();
            foreach (var regionInfo in result.RegionInfoDic.Values)
            {
                this.rangeInfoList.AddRange(regionInfo.RangeItemInfo.Values);
            }
            if(result.AllRegionSummary != null)
            {
                this.rangeInfoList.AddRange(result.AllRegionSummary.RangeItemInfo.Values);
            }
            objectListView.SetObjects(rangeInfoList);
        }

        private void exportToExcel()
        {
            List<List<object>> datas = new List<List<object>>();
            List<object> rowTitle = new List<object>();

            rowTitle.Add("区域");
            rowTitle.Add("RSRP区间");
            rowTitle.Add("D频采样点");
            rowTitle.Add("E频采样点");
            rowTitle.Add("F频采样点");
            rowTitle.Add("FDD_900频采样点");
            rowTitle.Add("FDD_1800频采样点");
            rowTitle.Add("TDD采样点个数");
            rowTitle.Add("FDD采样点个数");
            rowTitle.Add("总采样点个数");
            rowTitle.Add("D频占比");
            rowTitle.Add("E频占比");
            rowTitle.Add("F频占比");
            rowTitle.Add("FDD_900频占比");
            rowTitle.Add("FDD_1800频占比");
            datas.Add(rowTitle);
            
            foreach (EarfcnCoverageRangeItem info in rangeInfoList)
            {
                List<object> row = new List<object>();
                row.Add(info.RegionName);
                row.Add(info.RsrpRangeName);
                row.Add(info.EarfcnDCount);
                row.Add(info.EarfcnECount);
                row.Add(info.EarfcnFCount);
                row.Add(info.EarfcnFDD_900Count);
                row.Add(info.EarfcnFDD_1800Count);
                row.Add(info.TddPointCount);
                row.Add(info.FddPointCount);
                row.Add(info.PointCount);
                row.Add(info.PercentEarfcnD);
                row.Add(info.PercentEarfcnE);
                row.Add(info.PercentEarfcnF);
                row.Add(info.PercentEarfcnFDD_900);
                row.Add(info.PercentEarfcnFDD_1800);
                datas.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(datas);
        }

        private void ToolStripMenuItemExportExcel_Click(object sender, EventArgs e)
        {
            exportToExcel();
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            ToolStripMenuItemExportExcel.Enabled = objectListView.GetItemCount() > 0;
        }
    }
}
