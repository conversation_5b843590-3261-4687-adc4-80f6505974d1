﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYLeakOutCellSetByRegion_NB : ZTDIYLeakOutCellSetByRegion_LTE
    {
        public ZTDIYLeakOutCellSetByRegion_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "NB室分外泄"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34020, this.Name);
        }
    }
}
