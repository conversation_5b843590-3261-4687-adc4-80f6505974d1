﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellSetByStreetForm : MinCloseForm
    {
        public NRCellSetByStreetForm()
        {
            InitializeComponent();
        }

        public void FillData(CellSetResultDetail res)
        {
            gcEarfcn.DataSource = res.EarfcnCellResultList;
            gcEarfcn.RefreshDataSource();

            gcCell.DataSource = res.CellSetOfRegionResultList;
            gcCell.RefreshDataSource();

            gcBTS.DataSource = res.BtsSetOfRegionResultList;
            gcBTS.RefreshDataSource();

            gcUnusedCell.DataSource = res.UnusedCellSetOfRegionResultList;
            gcUnusedCell.RefreshDataSource();

            gcNbCell.DataSource = res.NBCellSetOfRegionResultList;
            gcNbCell.RefreshDataSource();

            gcSNCell.DataSource = res.SCellAndNCellResultList;
            gcSNCell.RefreshDataSource();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            List<DevExpress.XtraGrid.Views.Grid.GridView> gvs = new List<DevExpress.XtraGrid.Views.Grid.GridView>()
            {
                gvEarfcn,gvCell,gvBTS,gvUnusedCell,gvNbCell,gvSNCell
            };
            List<string> sheetNames = new List<string>()
            {
                "覆盖频点","覆盖小区","覆盖基站","区域内未使用小区","邻区中小区","主服及邻区汇总"
            };

            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }
    }
}
