﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.ObjectModel;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class WNodeBVoiCoverManager : GSMBTSVoiCoverManager
    {
        public Dictionary<WNodeB, List<Vertex[]>> WBtsToPolygonDict { get; set; }

        public new static WNodeBVoiCoverManager GetInstance()
        {
            if (instance == null)
            {
                instance = new WNodeBVoiCoverManager();
            }
            return instance;
        }

        public override void Show()
        {
            if (WBtsToPolygonDict == null)
            {
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (WNodeB bts in WBtsToPolygonDict.Keys)
            {
                drawList.Add(WBtsToPolygonDict[bts]);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }

        public override void Clear()
        {
            Reset();
            VoronoiLayer.GetInstance().Clear();
        }

        protected override void Construct()
        {
            ReadOnlyCollection<WNodeB> lst = null;
            if (MapCellLayer.DrawCurrent)
            {
                lst = MainModel.GetInstance().CellManager.GetCurrentWNodeBs();
            }
            else
            {
                lst = MainModel.GetInstance().CellManager.GetWNodeBs(MapCellLayer.CurShowTimeAt);
            }
            WBtsToPolygonDict = VoronoiManager<WNodeB>.GetInstance().Construct(lst, ValidFilter, isShowProgress);
            LastErrorText = VoronoiManager<WNodeB>.GetInstance().LastErrorText;
        }

        private bool ValidFilter(WNodeB bts, MapOperation2 mop2)
        {
            if (bts.Type != Condition.WType)
            {
                return false;
            }
            return mop2.CheckPointInRegion(bts.VertexX, bts.VertexY);
        }

        protected WNodeBVoiCoverManager()
        {
        }

        private static WNodeBVoiCoverManager instance;
    }
}
