﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellWrongDirForm_Scan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCellID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colECI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEARFCN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDirectionCfg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongDir = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongPntCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongRxLevAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongRxLevMin = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongRxLevMax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colGoodPntCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongMCellCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWrongNoneMCellCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1076, 361);
            this.gridControl.TabIndex = 0;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(151, 26);
            // 
            // miExport
            // 
            this.miExport.Name = "miExport";
            this.miExport.Size = new System.Drawing.Size(150, 22);
            this.miExport.Text = "导出到Excel...";
            this.miExport.Click += new System.EventHandler(this.miExport_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colCellName,
            this.colCellID,
            this.colTAC,
            this.colECI,
            this.colEARFCN,
            this.colPCI,
            this.colDirectionCfg,
            this.colWrongDir,
            this.gridColumn1,
            this.colWrongPntCnt,
            this.colWrongRxLevAvg,
            this.colWrongRxLevMin,
            this.colWrongRxLevMax,
            this.colGoodPntCnt,
            this.colWrongRate,
            this.colWrongMCellCnt,
            this.colWrongNoneMCellCnt});
            this.gridView.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // colCellName
            // 
            this.colCellName.Caption = "小区名";
            this.colCellName.FieldName = "CellName";
            this.colCellName.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.colCellName.Name = "colCellName";
            this.colCellName.Visible = true;
            this.colCellName.VisibleIndex = 0;
            this.colCellName.Width = 188;
            // 
            // colCellID
            // 
            this.colCellID.Caption = "CellID";
            this.colCellID.FieldName = "CellID";
            this.colCellID.Name = "colCellID";
            this.colCellID.Visible = true;
            this.colCellID.VisibleIndex = 1;
            this.colCellID.Width = 66;
            // 
            // colTAC
            // 
            this.colTAC.Caption = "TAC";
            this.colTAC.FieldName = "TAC";
            this.colTAC.Name = "colTAC";
            this.colTAC.Visible = true;
            this.colTAC.VisibleIndex = 2;
            this.colTAC.Width = 71;
            // 
            // colECI
            // 
            this.colECI.Caption = "ECI";
            this.colECI.FieldName = "ECI";
            this.colECI.Name = "colECI";
            this.colECI.Visible = true;
            this.colECI.VisibleIndex = 3;
            this.colECI.Width = 73;
            // 
            // colEARFCN
            // 
            this.colEARFCN.Caption = "EARFCN";
            this.colEARFCN.FieldName = "EARFCN";
            this.colEARFCN.Name = "colEARFCN";
            this.colEARFCN.Visible = true;
            this.colEARFCN.VisibleIndex = 4;
            this.colEARFCN.Width = 58;
            // 
            // colPCI
            // 
            this.colPCI.Caption = "PCI";
            this.colPCI.FieldName = "PCI";
            this.colPCI.Name = "colPCI";
            this.colPCI.Visible = true;
            this.colPCI.VisibleIndex = 5;
            this.colPCI.Width = 46;
            // 
            // colDirectionCfg
            // 
            this.colDirectionCfg.Caption = "工参方向角";
            this.colDirectionCfg.FieldName = "DirectionCfg";
            this.colDirectionCfg.Name = "colDirectionCfg";
            this.colDirectionCfg.Visible = true;
            this.colDirectionCfg.VisibleIndex = 6;
            // 
            // colWrongDir
            // 
            this.colWrongDir.Caption = "判断方位角";
            this.colWrongDir.FieldName = "WrongDirMean";
            this.colWrongDir.Name = "colWrongDir";
            this.colWrongDir.Visible = true;
            this.colWrongDir.VisibleIndex = 7;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "两角差值";
            this.gridColumn1.FieldName = "DirDiff";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 8;
            // 
            // colWrongPntCnt
            // 
            this.colWrongPntCnt.Caption = "异常点个数";
            this.colWrongPntCnt.FieldName = "WrongPntCnt";
            this.colWrongPntCnt.Name = "colWrongPntCnt";
            this.colWrongPntCnt.Visible = true;
            this.colWrongPntCnt.VisibleIndex = 9;
            // 
            // colWrongRxLevAvg
            // 
            this.colWrongRxLevAvg.Caption = "异常点平均场强";
            this.colWrongRxLevAvg.FieldName = "WrongRxLevAvg";
            this.colWrongRxLevAvg.Name = "colWrongRxLevAvg";
            this.colWrongRxLevAvg.Visible = true;
            this.colWrongRxLevAvg.VisibleIndex = 10;
            this.colWrongRxLevAvg.Width = 99;
            // 
            // colWrongRxLevMin
            // 
            this.colWrongRxLevMin.Caption = "异常点最小场强";
            this.colWrongRxLevMin.FieldName = "WrongRxLevMin";
            this.colWrongRxLevMin.Name = "colWrongRxLevMin";
            this.colWrongRxLevMin.Visible = true;
            this.colWrongRxLevMin.VisibleIndex = 11;
            this.colWrongRxLevMin.Width = 94;
            // 
            // colWrongRxLevMax
            // 
            this.colWrongRxLevMax.Caption = "异常点最大场强";
            this.colWrongRxLevMax.FieldName = "WrongRxLevMax";
            this.colWrongRxLevMax.Name = "colWrongRxLevMax";
            this.colWrongRxLevMax.Visible = true;
            this.colWrongRxLevMax.VisibleIndex = 12;
            this.colWrongRxLevMax.Width = 98;
            // 
            // colGoodPntCnt
            // 
            this.colGoodPntCnt.Caption = "正常点个数";
            this.colGoodPntCnt.FieldName = "GoodPntCnt";
            this.colGoodPntCnt.Name = "colGoodPntCnt";
            this.colGoodPntCnt.Visible = true;
            this.colGoodPntCnt.VisibleIndex = 13;
            // 
            // colWrongRate
            // 
            this.colWrongRate.Caption = "异常百分比(%)";
            this.colWrongRate.FieldName = "WrongRate";
            this.colWrongRate.Name = "colWrongRate";
            this.colWrongRate.Visible = true;
            this.colWrongRate.VisibleIndex = 14;
            this.colWrongRate.Width = 95;
            // 
            // colWrongMCellCnt
            // 
            this.colWrongMCellCnt.Caption = "主强异常点个数";
            this.colWrongMCellCnt.FieldName = "WrongMCellCnt";
            this.colWrongMCellCnt.Name = "colWrongMCellCnt";
            this.colWrongMCellCnt.Visible = true;
            this.colWrongMCellCnt.VisibleIndex = 15;
            // 
            // colWrongNoneMCellCnt
            // 
            this.colWrongNoneMCellCnt.Caption = "非主强异常点个数";
            this.colWrongNoneMCellCnt.FieldName = "WrongNoneMCellCnt";
            this.colWrongNoneMCellCnt.Name = "colWrongNoneMCellCnt";
            this.colWrongNoneMCellCnt.Visible = true;
            this.colWrongNoneMCellCnt.VisibleIndex = 16;
            // 
            // CellWrongDirForm_Scan
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1076, 361);
            this.Controls.Add(this.gridControl);
            this.Name = "CellWrongDirForm_Scan";
            this.Text = "扫频覆盖异常";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn colCellName;
        private DevExpress.XtraGrid.Columns.GridColumn colCellID;
        private DevExpress.XtraGrid.Columns.GridColumn colTAC;
        private DevExpress.XtraGrid.Columns.GridColumn colECI;
        private DevExpress.XtraGrid.Columns.GridColumn colEARFCN;
        private DevExpress.XtraGrid.Columns.GridColumn colPCI;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongPntCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongRxLevAvg;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongRxLevMin;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongRxLevMax;
        private DevExpress.XtraGrid.Columns.GridColumn colGoodPntCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongRate;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongMCellCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongNoneMCellCnt;
        private DevExpress.XtraGrid.Columns.GridColumn colDirectionCfg;
        private DevExpress.XtraGrid.Columns.GridColumn colWrongDir;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
    }
}