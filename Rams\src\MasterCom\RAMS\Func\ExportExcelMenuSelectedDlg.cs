﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 批量导出Excel-需要导出的菜单项选择窗口
    /// </summary>
    public partial class ExportExcelMenuSelectedDlg : BaseDialog
    {
        private List<ChbMenusItem> lstMenus = null;
        private List<ExportToExcelModel> lstDatas = null;
        /// <summary>
        /// 待导出的数据集
        /// </summary>
        private List<ExportToExcelModel> lstExportDataSelected = null;

        /// <summary>
        /// 菜单项选择窗口
        /// </summary>
        /// <param name="lstMenus">菜单项集合</param>
        /// <param name="lstDatas">构造的数据，ExportToExcelModel结构:菜单名/数据</param>
        public ExportExcelMenuSelectedDlg(List<ChbMenusItem> lstMenus, List<ExportToExcelModel> lstDatas)
        {
            InitializeComponent();

            this.lstMenus = lstMenus;
            this.lstDatas = lstDatas;

            InitCheckListBoxData();
        }



        private void InitCheckListBoxData()
        {
            this.chbMenus.Items.Clear();

            int index = 0;
            foreach (ChbMenusItem menu in lstMenus)
            {
                this.chbMenus.Items.Add(menu.MenusName);
                this.chbMenus.SetItemChecked(index, menu.IsChecked);
                index++;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (checkHasSelected())
            {
                ExcelNPOIManager.ExportToExcelMore(lstExportDataSelected);

                this.DialogResult = System.Windows.Forms.DialogResult.OK;
            }
            else
            {
                MessageBox.Show("请选择要导出的菜单页");
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private bool checkHasSelected()
        {
            bool result = false;
            lstExportDataSelected = new List<ExportToExcelModel>();
            foreach (string item in this.chbMenus.CheckedItems)
            {
                result = true;

                ExportToExcelModel selectedData = lstDatas.Find(d => d.SheetName == item);
                if (selectedData != null)
                {
                    lstExportDataSelected.Add(selectedData);
                }
            }

            return result;
        }

        private void cbxAllNone_Click(object sender, EventArgs e)
        {
            bool checkAll = cbxAllNone.Checked;
            int ct = chbMenus.Items.Count;
            for (int i = 0; i < ct; i++)
            {
                chbMenus.SetItemChecked(i, checkAll);
            }
        }

    }

    /// <summary>
    /// CheckListBox菜单项
    /// </summary>
    public class ChbMenusItem
    {
        /// <summary>
        /// 显示的菜单名
        /// </summary>
        public string MenusName { get; set; }

        /// <summary>
        /// 在弹出窗口中是否默认选择该项
        /// </summary>
        public bool IsChecked { get; set; }

        public ChbMenusItem() { }

        public ChbMenusItem(string name, bool isChecked = false)
        {
            this.MenusName = name;
            this.IsChecked = isChecked;
        }
    }
}
