﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    public class TaskEventItem
    {
        public TaskEventItem(string name, int districtID, int fileID, int eventID, int eventSN, DateTime dateTime)
        {
            this.Name = name;
            this.DistrictID = districtID;
            this.FileID = fileID;
            this.EventID = eventID;
            this.EventSN = eventSN;
            this.DateTime = dateTime;
        }
        public string Name
        {
            get;
            private set;
        }
        public int DistrictID
        {
            get;
            private set;
        }
        public int FileID
        {
            get;
            private set;
        }
        public int EventID
        {
            get;
            private set;
        }
        public int EventSN
        {
            get;
            private set;
        }
        public DateTime DateTime
        {
            get;
            private set;
        }

        public string ESResultTbName
        {
            get
            {
                return string.Format("tb_focus_event_result_{0}", this.DateTime.ToString("yyMM"));
            }
        }

        public ESResultInfo ESResultInfo
        {
            get;
            set;
        }

        public ESResultInfo ESResultInfoV2017
        {
            get;
            set;
        }

        /// <summary>
        /// 新（2017版本）预判流程，预判结果存放表与旧版不同，需区分
        /// </summary>
        public string ESResultTbNameV2017
        {
            get
            {
                return string.Format("tb_focus_event_result_v2017_{0}", this.DateTime.ToString("yyMM"));
            }
        }
    }
}
