﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Util;
using DevExpress.XtraGrid.Views.Base;


namespace MasterCom.RAMS.ZTFunc
{
    public partial class QueryFileStatusInfo : MinCloseForm
    {
        List<FileStoredInfo> fileInfoStoredList = new List<FileStoredInfo>();
        List<FileNotStoredInfo> fileInfoNotStoredList = new List<FileNotStoredInfo>();


        public QueryFileStatusInfo()
            :base()
        {
            InitializeComponent();
            datEnd.Value = DateTime.Today;
            datImportEndTime.Value = datEnd.Value;
            datStart.Value = DateTime.Today.AddDays(-DateTime.Today.Day + 1);
            datImportStartTime.Value = datStart.Value;
            cbxImportStatus.SelectedIndex = 0;
            cbxEventDefineStatus.SelectedIndex = 0;
            cbxESStatus.SelectedIndex = 0;
            cbxGridStatStatus.SelectedIndex = 0;
            cbxAreaStatStatus.SelectedIndex = 0;
            Run();
        }

        private void butQuery_Click(object sender, EventArgs e)
        {
            Run();
        }

        private void Run()
        {
            if (tabControl1.SelectedIndex == 0)
            {
                QueryFileStoredInfo();
            }
            else
            {
                QueryFileNotStoredInfo();
            }
        }

        private void QueryFileNotStoredInfo()
        {
            if (datImportStartTime.Value > datImportEndTime.Value)
            {
                XtraMessageBox.Show(this, "起始日期不能大于结束日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                datImportEndTime.Focus();
                return;
            }
            fileInfoNotStoredList.Clear();
            queryNotStoredFiles();
            fileInfoNotStoredList.Sort(FileNotStoredInfo.GetCompareaByImportTime());
            InitFileNotStoredList();
        }

        private void QueryFileStoredInfo()
        {
            if (datStart.Value > datEnd.Value)
            {
                XtraMessageBox.Show(this, "起始日期不能大于结束日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                datEnd.Focus();
                return;
            }
            fileInfoStoredList.Clear();
            List<string> logTbNameList = GetLogTbName();
            foreach (string logTbName in logTbNameList)
            {
                queryStoredFiles(logTbName);
            }
            fileInfoStoredList.Sort(FileStoredInfo.GetCompareaBySTime());
            InitFileStoredList();
        }

        private List<string> GetLogTbName()
        {
            DateTime startTime = datStart.Value;
            DateTime endTime = datEnd.Value;
            int startYear = startTime.Year;
            int startMonth = startTime.Month;
            int endYear = endTime.Year;
            int endMonth = endTime.Month;
            int monthDiff = endMonth + (endYear - startYear) * 12 - startMonth;
            List<string> listTmp = new List<string>();
            for (int i = 0; i <= monthDiff; i++)
            {
                DateTime dateTmp = startTime.AddMonths(i);
                listTmp.Add(string.Format("tb_log_file_{0}_{1:D2}", dateTmp.Year, dateTmp.Month));
            }
            return listTmp;
        }

        private void queryStoredFiles(string logTbName)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select t.strfilename,pr.strname,t.statstatus,isnull(p.istatus, 255) as istatus, t.iimporttime, t.istime, t.ietime from ");
            sb.Append(logTbName + " t ");
            sb.Append(" left join tb_log_event_predeal p on t.ifileid = p.ifileid ");
            sb.Append(" left join tb_cfg_static_project pr  on t.iprojecttype = pr.iid ");
            sb.Append("where t.istime > ");
            sb.Append(JavaDate.GetMilliseconds(datStart.Value) / 1000);
            sb.Append(" and t.ietime < ");
            sb.Append(JavaDate.GetMilliseconds(datEnd.Value) / 1000);
            QueryFileStored fileStored = new QueryFileStored(MainModel, sb.ToString());
            fileStored.Query();
            fileInfoStoredList.AddRange(fileStored.fileList);
        }

        private void queryNotStoredFiles()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select t.strfilename,pr.strname, t.iimporttime,t.ipriority,t.ifileid from tb_adapter_file_list t");
            sb.Append(" left join tb_cfg_static_project pr  on t.iprojecttype = pr.iid ");
            sb.Append("where t.statstatus = 0 ");
            sb.Append(" and t.iimporttime > ");
            sb.Append(JavaDate.GetMilliseconds(datImportStartTime.Value) / 1000);
            sb.Append(" and t.iimporttime < ");
            sb.Append(JavaDate.GetMilliseconds(datImportEndTime.Value) / 1000);
            QueryFileNotStored fileNotStored = new QueryFileNotStored(MainModel, sb.ToString());
            fileNotStored.Query();
            fileInfoNotStoredList.AddRange(fileNotStored.fileList);
        }

        //private void InitFileStoredList()
        //{
        //    string success = "成功";
        //    string fail = "异常";
        //    string disposing = "正在处理";
        //    listViewFileStatus.Items.Clear();
        //    listViewFileStatus.BeginUpdate();
        //    foreach (FileStoredInfo file in fileInfoStoredList)
        //    {
        //        if (cbxImportStatus.SelectedItem.Equals(success) && !file.ImportStatus.Equals(success))
        //        {
        //            continue;
        //        }
        //        if (cbxImportStatus.SelectedItem.Equals(fail) && !file.ImportStatus.Equals(fail))
        //        {
        //            continue;
        //        }
        //        if (cbxEventDefineStatus.SelectedItem.Equals(success) && !file.EventDefineStatus.Equals(success))
        //        {
        //            continue;
        //        }
        //        if (cbxEventDefineStatus.SelectedItem.Equals(fail) && !file.EventDefineStatus.Equals(fail))
        //        {
        //            continue;
        //        }
        //        if (cbxEventDefineStatus.SelectedItem.Equals(disposing) && !file.EventDefineStatus.Equals(disposing))
        //        {
        //            continue;
        //        }
        //        if (cbxESStatus.SelectedItem.Equals(success) && !file.ESStatus.Equals(success))
        //        {
        //            continue;
        //        }
        //        if (cbxGridStatStatus.SelectedItem.Equals(success) && !file.GridStatStatus.Equals(success))
        //        {
        //            continue;
        //        }
        //        if (cbxGridStatStatus.SelectedItem.Equals(fail) && !file.GridStatStatus.Equals(fail))
        //        {
        //            continue;
        //        }
        //        if (cbxAreaStatStatus.SelectedItem.Equals(success) && !file.AreaStatStatus.Equals(success))
        //        {
        //            continue;
        //        }
        //        if (cbxAreaStatStatus.SelectedItem.Equals(fail) && !file.AreaStatStatus.Equals(fail))
        //        {
        //            continue;
        //        }
        //        ListViewItem list = new ListViewItem();
        //        list.SubItems[0].Text = file.FileName;
        //        string proName = "未知";
        //        if (file.ProjectName != null && !file.ProjectName.Trim().Equals(""))
        //        {
        //            proName = file.ProjectName;
        //        }
        //        list.SubItems.Add(proName);
        //        list.SubItems.Add(file.IsTimeToString);
        //        list.SubItems.Add(file.IeTimeToString);
        //        list.SubItems.Add(file.IImportTimeToString);
        //        list.SubItems.Add(file.ImportStatus);
        //        list.SubItems.Add(file.EventDefineStatus);
        //        list.SubItems.Add(file.ESStatus);
        //        list.SubItems.Add(file.GridStatStatus);
        //        list.SubItems.Add(file.AreaStatStatus);
        //        listViewFileStatus.Items.Add(list);
        //    }
        //    listViewFileStatus.EndUpdate();
        //}

        private void InitFileStoredList()
        {
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = fileInfoStoredList;
            gridControl.DataSource = bindingSource;
        }

        private void InitFileNotStoredList()
        {
            lvNotStored.Items.Clear();
            lvNotStored.BeginUpdate();
            foreach (FileNotStoredInfo file in fileInfoNotStoredList)
            {
                ListViewItem list = new ListViewItem();
                list.SubItems[0].Text = file.FileName;
                string proName = "未知";
                if (file.ProjectName != null && !file.ProjectName.Trim().Equals(""))
                {
                    proName = file.ProjectName;
                }
                list.SubItems.Add(proName);
                list.SubItems.Add(file.IImportTimeToString);
                list.SubItems.Add(file.PriorityToString);
                list.Tag = file;
                lvNotStored.Items.Add(list);
            }
            lvNotStored.EndUpdate();
        }

        private void UpdatePriority(int priority)
        {
            List<FileNotStoredInfo> fileInfoList = new List<FileNotStoredInfo>();
            foreach (ListViewItem item in lvNotStored.SelectedItems)
            {
                fileInfoList.Add((FileNotStoredInfo)item.Tag);
            }
            foreach (FileNotStoredInfo fileInfo in fileInfoList)
            {
                if (fileInfo.Priority == priority)
                {
                    continue;
                }
                DIYSQLUpdateAdapterFilePriority sqlUpdatePriority = new DIYSQLUpdateAdapterFilePriority(this.MainModel);
                StringBuilder sb = new StringBuilder();
                sb.Append("update tb_adapter_file_list set ipriority = ");
                sb.Append(priority);
                sb.Append(" where ifileid = ");
                sb.Append(fileInfo.FileId);
                sqlUpdatePriority.sql = sb.ToString();
                sqlUpdatePriority.Query();
            }
            Run();
        }

        private void miPriorityOne_Click(object sender, EventArgs e)
        {
            UpdatePriority(Convert.ToInt32(((ToolStripMenuItem)sender).Text));
        }

        private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
        {
            if (lvNotStored.SelectedItems.Count <= 0)
            {
                e.Cancel = true;
            }
        }

        private void toolStripMenuItemExport2Xls_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }
    }
}