﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ExMap;
using MasterCom.MTGis;
using GMap.NET;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.MapControlTool
{
    public class ExMapToolAddRect
    {
        private readonly MTExGMap mapControl;
        public DbRect Rectangle
        {
            get
            {
                DbRect rect = null;
                if (firstPnt != PointLatLng.Zero && secondPnt != PointLatLng.Zero)
                {
                    rect = new DbRect(firstPnt.Lng, firstPnt.Lat, secondPnt.Lng, secondPnt.Lat);
                }
                return rect;
            }
            set
            {
                if (value == null)
                {
                    firstPnt = PointLatLng.Zero;
                    secondPnt = PointLatLng.Zero;
                }
                else
                {
                    firstPnt = new PointLatLng(value.y2, value.x1);
                    secondPnt = new PointLatLng(value.y1, value.x2);
                }
            }
        }
        public event EventHandler RectangleChanged;
        public ExMapToolAddRect(MTExGMap map)
        {
            mapControl = map;
        }

        private bool isActive = false;
        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDown += mapControl_MouseDown;
                mapControl.MouseUp += mapControl_MouseUp;
            }
            isActive = true;
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.MouseDown -= mapControl_MouseDown;
            mapControl.MouseUp -= mapControl_MouseUp;
            isActive = false;
        }

        public void Clear()
        {
            firstPnt = PointLatLng.Zero;
            secondPnt = PointLatLng.Zero;
        }

        void mapControl_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                mapControl.MouseMove -= new System.Windows.Forms.MouseEventHandler(mapControl_MouseMove);
                secondPnt = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
                mapControl.Invalidate();
                mapControl.Update();
                Graphics g = mapControl.CreateGraphics();
                g.SmoothingMode = SmoothingMode.AntiAlias;
                drawRect(g);
                RectangleChanged(this, EventArgs.Empty);
            }
        }
        PointLatLng firstPnt;
        PointLatLng secondPnt;
        void mapControl_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            mapControl.MouseMove -= new System.Windows.Forms.MouseEventHandler(mapControl_MouseMove);
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                mapControl.MouseMove += new System.Windows.Forms.MouseEventHandler(mapControl_MouseMove);
                firstPnt = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
                secondPnt = PointLatLng.Zero;
            }
        }

        void mapControl_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (firstPnt != PointLatLng.Zero)
            {
                secondPnt = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
                if (secondPnt.Lat != firstPnt.Lat || secondPnt.Lng != firstPnt.Lng)
                {
                    mapControl.Invalidate();
                    mapControl.Update();
                    Graphics g = mapControl.CreateGraphics();
                    g.SmoothingMode = SmoothingMode.AntiAlias;
                    drawRect(g);
                }
            }
            else
            {
                mapControl.MouseMove -= mapControl_MouseMove;
            }
        }

        public void Draw(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;
            drawRect(g);
        }

        private void drawRect(Graphics g)
        {
            if (firstPnt == PointLatLng.Zero || secondPnt == PointLatLng.Zero)
            {
                return;
            }
            GPoint firstPixel = mapControl.FromLatLngToLocalAdaptered(firstPnt);
            GPoint secondPixel = mapControl.FromLatLngToLocalAdaptered(secondPnt);
            g.DrawRectangle(new Pen(Color.Green, 3), Math.Min(firstPixel.X, secondPixel.X), Math.Min(firstPixel.Y, secondPixel.Y),
              Math.Abs(firstPixel.X - secondPixel.X), Math.Abs(firstPixel.Y - secondPixel.Y));
        }

    }

}
