﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public class CellCoverMainItem
    {
        public CellCoverMainItem(ICell cell)
        {
            this.Cell = cell;
            dataGroup = new KPIDataGroup(cell);
        }

        public ICell Cell
        {
            get;
            private set;
        }

        public void AddStatData(FileInfo fileInfo, KPIStatDataBase data, bool saveAsGrid)
        {
            GridUnitBase grid = new GridUnitBase(data.LTLng, data.LTLat);
            int rowIdx, colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.CenterLng, grid.CenterLat, out rowIdx, out colIdx);
            GridDataHub gridHub = MatrixData[rowIdx, colIdx];
            if (gridHub == null)
            {
                gridHub = new GridDataHub(data.LTLng, data.LTLat);
                MatrixData[rowIdx, colIdx] = gridHub;
            }
            gridHub.AddStatData(fileInfo, data, false);
        }

        public GridMatrix<GridDataHub> MatrixData { get; set; } = new GridMatrix<GridDataHub>();

        private readonly KPIDataGroup dataGroup;

        public List<CellCoverAreaItem> CoverAreaItems
        {
            get;
            set;
        }

        private readonly Dictionary<string, double> expValueDic = new Dictionary<string, double>();
        public double this[string exp]
        {
            get
            {
                double ret;
                if (!expValueDic.TryGetValue(exp, out ret))
                {
                    ret = double.NaN;
                }
                return ret;
            }
            set
            {
                expValueDic[exp] = value;
            }
        }

        private AreaBase getArea(MasterCom.MTGis.DbRect rect, AreaBase area)
        {
            if (area.Rank == ZTAreaManager.Instance.LowestRank)
            {
                if (area.Bounds.Within(rect)
                    && area.MapOper.CheckRectIntersectWithRegion(rect))
                {
                    return area;
                }
            }
            else if (area.Bounds.Within(rect))
            {
                foreach (AreaBase sub in area.SubAreas)
                {
                    AreaBase rtArea = getArea(rect, sub);
                    if (rtArea != null)
                    {
                        return rtArea;
                    }
                }
            }
            return null;
        }

        public void MakeSummary(CellCoverRptTemplate template, List<AreaBase> rootArea)
        {
            Dictionary<AreaBase, CellCoverAreaItem> areaDic = new Dictionary<AreaBase, CellCoverAreaItem>();
            foreach (GridDataHub gridHub in MatrixData)
            {
                dataGroup.Merge(gridHub.DataGroup);

                foreach (AreaBase root in rootArea)
                {
                    AreaBase vil = getArea(gridHub.Bounds, root);
                    if (vil != null)
                    {
                        CellCoverAreaItem areaCover = null;
                        if (!areaDic.TryGetValue(vil, out areaCover))
                        {
                            areaCover = new CellCoverAreaItem(this, vil);
                            areaDic[vil] = areaCover;
                        }
                        areaCover.DataGroup.Merge(gridHub.DataGroup);
                        break;//只匹配一个区域
                    }
                }
                gridHub.DataGroup.FinalMtMoGroup();
            }

            dataGroup.FinalMtMoGroup();
            foreach (CellCoverAreaItem item in areaDic.Values)
            {
                item.DataGroup.FinalMtMoGroup();
                item.MakeSumamry();
            }

            setTemplateColumnValue(template, areaDic);

            this.CoverAreaItems = new List<CellCoverAreaItem>(areaDic.Values);

        }

        private void setTemplateColumnValue(CellCoverRptTemplate template, Dictionary<AreaBase, CellCoverAreaItem> areaDic)
        {
            foreach (TemplateColumn col in template.Columns)
            {
                double value = dataGroup.CalcFormula((CarrierType)col.CarrierID, col.MoMtFlag, col.Expression);
                this[col.Expression] = value;
                if (col.IsCalcProportion)
                {
                    this[col.ExpProportion] = double.IsNaN(value) ? value : 100.0;
                }
                foreach (CellCoverAreaItem areaCoverItem in areaDic.Values)
                {
                    double areaValue = areaCoverItem.DataGroup.CalcFormula((CarrierType)col.CarrierID, col.MoMtFlag, col.Expression);
                    areaCoverItem[col.Expression] = areaValue;
                    if (col.IsCalcProportion)
                    {
                        areaCoverItem[col.ExpProportion] = Math.Round(areaValue * 100 / value, 2);
                    }
                }
            }
        }
    }
}
