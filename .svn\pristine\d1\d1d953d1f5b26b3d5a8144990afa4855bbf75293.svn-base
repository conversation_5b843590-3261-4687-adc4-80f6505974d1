﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTHandOverNotIntiemForm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        public CQTHandOverNotIntiemForm(MainModel mainModel,string netWorker)
        {
            InitializeComponent();
            for (int co = 0; co < gridView1.Columns.Count; co++)
            {
                if(co < 4)
                    gridView1.Columns[co].Width = 80;
                else
                    gridView1.Columns[co].Width = 88;
                gridView1.Columns[co].AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
                gridView1.Columns[co].AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center; 
            }
            gridView1.ColumnPanelRowHeight = 35;
            for (int col = 0; col < gridView2.Columns.Count; col++)
            {
                if (col < 5)
                    gridView2.Columns[col].Width = 80;
                else if (col == gridView2.Columns.Count - 1)
                    gridView2.Columns[col].Width = 140;
                else
                    gridView2.Columns[col].Width = 95;
                gridView2.Columns[col].AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
                gridView2.Columns[col].AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center; 
            }
            gridView2.ColumnPanelRowHeight = 35;
            if (netWorker.Equals("GSM"))
                this.Text = "GSM切换不及时";
            else
                this.Text = "TD切换不及时";
            hoNotIntimeResultDicNew = new Dictionary<HoNotIntimeMainInfo, List<HoNotIntimeSubInfo>>();
            this.mainModel = mainModel;
        }
        private Dictionary<HoNotIntimeMainInfo, List<HoNotIntimeSubInfo>> hoNotIntimeResultDicNew;
        private List<HoNotIntimeMainInfo> hoNotIntimeMainInfoList = new List<HoNotIntimeMainInfo>();
        public void setData(Dictionary<HoNotIntimeMainInfo, List<HoNotIntimeSubInfo>> hoNotIntimeResultDic)
        {
            hoNotIntimeMainInfoList.Clear();
            hoNotIntimeResultDicNew = hoNotIntimeResultDic;
            hoNotIntimeMainInfoList.AddRange(hoNotIntimeResultDic.Keys);
            this.gridControl1.DataSource = hoNotIntimeMainInfoList;
            
        }
        private void gridView1_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            try
            {
                if (gridView1.SelectedRowsCount > 0)
                {
                    List<HoNotIntimeSubInfo> cellSetList = new List<HoNotIntimeSubInfo>();
                    int[] rows = this.gridView1.GetSelectedRows();
                    foreach (int i in rows)
                    {
                        HoNotIntimeMainInfo coverItem = this.gridView1.GetRow(i) as HoNotIntimeMainInfo;
                        cellSetList.AddRange(hoNotIntimeResultDicNew[coverItem]);
                    }
                    this.gridControl2.DataSource = cellSetList;
                    this.gridControl2.RefreshDataSource();
                }
            }
            catch
            {
                //continue
            }
        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            outPutExcel();
        }
        /// <summary>
        /// 导出EXCEL
        /// </summary>
        private void outPutExcel()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            #region EXCEL-SHEET1列表构造
            cols.Add("CQT地点名");
            cols.Add("源小区名称");
            cols.Add("源小区LAC");
            cols.Add("源小区CI");
            cols.Add("主服采样点数");
            cols.Add("弱覆盖采样点数");
            cols.Add("弱覆盖采样点占比");

            cols.Add("占用主服次数");
            cols.Add("切换不及时次数");
            cols.Add("切换不及时平均时延");
            cols.Add("切换不及时平均电平");
            cols.Add("掉话次数");
            cols.Add("未接通次数");
            cols.Add("切换失败次数");
            cols.Add("质差次数");
            cols.Add("弱覆盖次数");

            cols.Add("邻小区名称");
            cols.Add("邻小区LAC");
            cols.Add("邻小区CI");
            cols.Add("邻小区BCCH");
            cols.Add("邻小区BSIC");
            cols.Add("邻小区切换不及时次数");
            cols.Add("邻小区切换不及时采样点数");
            cols.Add("邻小区切换不及时较主服电平");
            cols.Add("邻小区成功切换次数");
            cols.Add("问题点切换时间");
            cols.Add("文件名");

            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            foreach (HoNotIntimeMainInfo hoKey in hoNotIntimeResultDicNew.Keys)
            {
                List<HoNotIntimeSubInfo> nbInfoList = hoNotIntimeResultDicNew[hoKey];

                foreach (HoNotIntimeSubInfo nbInfo in nbInfoList)
                {
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    objs.Add(hoKey.StrCqtName);
                    objs.Add(hoKey.StrCellName);
                    objs.Add(hoKey.ILac);
                    objs.Add(hoKey.ICi);
                    objs.Add(hoKey.IAllSampleNum);
                    objs.Add(hoKey.IWeakCoverSampleNum);
                    objs.Add(hoKey.strWeakCoverRate);

                    objs.Add(hoKey.IServerNum);
                    objs.Add(hoKey.INoIntimeNum);
                    objs.Add(hoKey.FNotIntime);
                    objs.Add(hoKey.INotIntimeRxlev);
                    objs.Add(hoKey.IDropCallNum);
                    objs.Add(hoKey.INoConnNum);
                    objs.Add(hoKey.IHoFailNum);
                    objs.Add(hoKey.IWeakQualNum);
                    objs.Add(hoKey.IWeakCoverNum);

                    objs.Add(nbInfo.StrNbCellName);
                    objs.Add(nbInfo.ILac);
                    objs.Add(nbInfo.ICi);
                    objs.Add(nbInfo.IBcch);
                    objs.Add(nbInfo.IBsic);
                    objs.Add(nbInfo.INotIntimeNum);
                    objs.Add(nbInfo.INotIntimeSample);
                    objs.Add(nbInfo.INotIntimeRxlev);
                    objs.Add(nbInfo.IHoSuccNum);
                    objs.Add(nbInfo.DTime.ToString("HH:mm:ss"));
                    objs.Add(nbInfo.StrFileName);

                    nr.cellValues = objs;
                    datas.Add(nr);
                }
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("切换不及时问题详情");
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
        /// <summary>
        /// 回放文件
        /// </summary>
        private void ReplayEventToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int[] row = gridView2.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gridView2.GetRow(row[0]);
            HoNotIntimeSubInfo replayFile = o as HoNotIntimeSubInfo;
            FileInfo fileInfo = new FileInfo();
            fileInfo.ID = replayFile.IFileid;
            fileInfo.ProjectID = replayFile.IProjectType;
            fileInfo.ServiceType = replayFile.IServiceType;
            fileInfo.SampleTbName = replayFile.StrSampleTbName;
            fileInfo.LogTable = replayFile.StrLogFile;

            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(fileInfo, replayFile.DTime);
        }
    }
}