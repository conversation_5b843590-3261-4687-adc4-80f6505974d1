﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanLTELowSINRRoadListForm : MinCloseForm
    {
        public ZTScanLTELowSINRRoadListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<LowSINRRoad> roadLst)
        {
            gridControl1.DataSource = roadLst;
            gridControl1.RefreshDataSource();
        }

        private void gridControl1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.SelectedRowsCount > 0)
            {
                LowSINRRoad info = gridView1.GetRow(gridView1.GetSelectedRows()[0]) as LowSINRRoad;
                MainModel.ClearDTData();

                foreach (TestPoint tp in info.TestPntLst)
                {
                    MainModel.DTDataManager.Add(tp);
                }

                MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSSINR");
                mModel.DrawFlyLines = true;
                MainModel.FireDTDataChanged(this);
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView1);
            }
            catch
            {
                MessageBox.Show("导出到xls失败!");
            }
        }
    }
}
