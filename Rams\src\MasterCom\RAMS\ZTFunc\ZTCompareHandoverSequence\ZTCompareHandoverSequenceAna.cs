﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCompareHandoverSequenceAna : DIYAnalyseFilesOneByOneByRegion
    {
        protected List<int> eventIDLst;
        protected List<HandoverSequenceCondition> HOSQConditionLst;
        protected List<TimePeriodPoints> tpEventLst;

        protected List<HandoverSequenceFileInfo> hosqFileInfoLst;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 25000, 25004, this.Name);
        }

        public ZTCompareHandoverSequenceAna(MainModel mModel)
            : base(mModel)
        {
            eventIDLst = new List<int>();
            eventIDLst.Add(-1);
            hosqFileInfoLst = new List<HandoverSequenceFileInfo>();
            tpEventLst = new List<TimePeriodPoints>();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            hosqFileInfoLst.Clear();
            tpEventLst.Clear();
        }

        protected override void doStatWithQuery()
        {
            ZTAnaTpAmongEvents analyse = new ZTAnaTpAmongEvents(null, MainModel);
            analyse.SetEventID(eventIDLst);
            analyse.DealData();
            tpEventLst = analyse.TpPntList;
            dealData();
        }

        private void dealData()
        {
            if (tpEventLst.Count > 0)
            {
                foreach (HandoverSequenceCondition hosq in HOSQConditionLst)
                {
                    if (hosq.HOSFile.Name == tpEventLst[0].eStart.FileName)
                    {
                        HandoverSequenceFileInfo hosqFile = new HandoverSequenceFileInfo(hosqFileInfoLst.Count + 1, hosq);
                        hosqFileInfoLst.Add(hosqFile);
                        hosqFile.CompareSequence(tpEventLst, HandoverStreetCellCfgManager.GetInstance().IdealStreetCells);
                    }
                }
            }
        }

        protected override bool getCondition()
        {
            if (!HandoverStreetCellCfgManager.GetInstance().IsValidCfg())
            {
                MessageBox.Show("tb_cfg_street_cell表为空，请检查...", "提示");
                return false;
            }

            ZTHandoverSequenceSettingDlg dlg = new ZTHandoverSequenceSettingDlg(Condition.FileInfos);
            if (dlg.ShowDialog() != DialogResult.OK) return false;
            HOSQConditionLst = dlg.GetCondition();
            return true;
        }

        protected override void fireShowForm()
        {
            HandoverSequenceInfoForm form = MainModel.GetObjectFromBlackboard(typeof(HandoverSequenceInfoForm)) as HandoverSequenceInfoForm;
            if (form == null || form.IsDisposed)
            {
                form = new HandoverSequenceInfoForm(MainModel);
            }
            form.FillData(hosqFileInfoLst);
            form.Show(MainModel.MainForm);
        }
    }

    public class HandoverSequenceFileInfo
    {
        public int Sn { get; set; }
        private readonly HandoverSequenceCondition hosq;
        public HandoverSequenceCondition HOSQ
        {
            get { return hosq; }
        }
        private readonly HandoverSequenceCompareInfo compareInfo;
        private List<HandoverGroupPairInfo> groupPairLst;
        public List<HandoverGroupPairInfo> GroupPairLst
        {
            get { return groupPairLst; }
        }

        private readonly CompareStreetCell compareSC = new CompareStreetCell();

        public HandoverSequenceFileInfo(int sn, HandoverSequenceCondition hosq)
        {
            this.Sn = sn;
            this.hosq = hosq;
            compareInfo = new HandoverSequenceCompareInfo();
            groupPairLst = new List<HandoverGroupPairInfo>();
        }

        public void CompareSequence(List<TimePeriodPoints> tpeventsFile, List<StreetCellInfo> idealStreetCells)
        {
            List<StreetCellInfo> idealHandover;
            getIdealHandover(idealStreetCells, out idealHandover);
            idealHandover.Sort(compareSC);
            compareInfo.DoCompare(tpeventsFile, idealHandover);
            compareInfo.MakeGroupPair(out groupPairLst);
        }

        private void getIdealHandover(List<StreetCellInfo> idealStreetCells, out List<StreetCellInfo> idealHandover)
        {
            idealHandover = new List<StreetCellInfo>();

            foreach (StreetCellInfo cellInfo in idealStreetCells)
            {
                if (cellInfo.IsBelong(hosq))
                    idealHandover.Add(cellInfo);
            }
        }

        private class CompareStreetCell : IComparer<StreetCellInfo>
        {
            #region IComparer<StreetCellInfo> 成员

            int IComparer<StreetCellInfo>.Compare(StreetCellInfo x, StreetCellInfo y)
            {
                return x.SeqNo - y.SeqNo;
            }

            #endregion
        }
    }

    public class HandoverSequenceCompareInfo
    {
        private readonly List<TimePeriodPoints> sequenceFileE;
        private readonly List<StreetCellInfo> sequenceIdealSC;

        private readonly List<string> fileBuf;
        private readonly List<string> idealBuf;

        private readonly List<TimePeriodPoints> fileEventLst;
        private readonly List<StreetCellInfo> idealSCLst;

        public HandoverSequenceCompareInfo()
        {
            sequenceFileE = new List<TimePeriodPoints>();
            sequenceIdealSC = new List<StreetCellInfo>();

            fileBuf = new List<string>();
            idealBuf = new List<string>();
            fileEventLst = new List<TimePeriodPoints>();
            idealSCLst = new List<StreetCellInfo>();
        }

        public void DoCompare(List<TimePeriodPoints> eventsFile, List<StreetCellInfo> idealHandover)
        {
            int idxE = 0, idxHO = 0;

            while (idxE < eventsFile.Count || idxHO < idealHandover.Count)
            {
                if (idxE < eventsFile.Count)
                {
                    TimePeriodPoints tpFile = eventsFile[idxE++];
                    string filelacci = string.Format("{0}_{1}", tpFile.eStart["LAC"], tpFile.eStart["CI"]);
                    fileBuf.Add(filelacci);
                    fileEventLst.Add(tpFile);
                    if (idealBuf.Contains(filelacci))
                    {
                        idealBufFinded(filelacci);
                    }
                }
                if (idxHO < idealHandover.Count)
                {
                    StreetCellInfo sc = idealHandover[idxHO++];
                    string ideallacci = string.Format("{0}_{1}", sc.LAC, sc.CI);
                    idealBuf.Add(ideallacci);
                    idealSCLst.Add(sc);
                    if (fileBuf.Contains(ideallacci))
                    {
                        fileBufFinded(ideallacci);
                    }
                }
            }

            finalCompare();
        }

        private void idealBufFinded(string filelacci)
        {
            int idx = idealBuf.IndexOf(filelacci);

            int i;
            for (i = 0; i < idx;i++ )
            {
                sequenceFileE.Add(null);
                sequenceIdealSC.Add(idealSCLst[i]);
            }
            int j;
            for (j = 0; j < fileBuf.Count - 1;j++ )
            {
                sequenceFileE.Add(fileEventLst[j]);
                sequenceIdealSC.Add(null);
            }
            sequenceIdealSC.Add(idealSCLst[i]);
            sequenceFileE.Add(fileEventLst[j]);

            
            fileBuf.Clear();
            idealBuf.RemoveRange(0, idx + 1);
            fileEventLst.Clear();
            idealSCLst.RemoveRange(0, idx + 1);
        }

        private void fileBufFinded(string ideallacci)
        {
            int idx = fileBuf.IndexOf(ideallacci);

            int i;
            for (i = 0; i < idx; i++)
            {
                sequenceFileE.Add(fileEventLst[i]);
                sequenceIdealSC.Add(null);
            }
            int j;
            for (j = 0; j < idealBuf.Count - 1; j++)
            {
                sequenceFileE.Add(null);
                sequenceIdealSC.Add(idealSCLst[j]);
            }
            sequenceFileE.Add(fileEventLst[i]);
            sequenceIdealSC.Add(idealSCLst[j]);

            fileBuf.RemoveRange(0, idx + 1);
            idealBuf.Clear();
            fileEventLst.RemoveRange(0, idx + 1);
            idealSCLst.Clear();
        }

        private void finalCompare()
        {
            for (int i = 0; i < idealBuf.Count; i++)
            {
                sequenceFileE.Add(null);
                sequenceIdealSC.Add(idealSCLst[i]);
            }
            for (int i = 0; i < fileBuf.Count; i++)
            {
                sequenceFileE.Add(fileEventLst[i]);
                sequenceIdealSC.Add(null);
            }
            idealBuf.Clear();
            fileBuf.Clear();
            idealSCLst.Clear();
            fileEventLst.Clear();
        }

        public void MakeGroupPair(out List<HandoverGroupPairInfo> groupPair)
        {
            groupPair = new List<HandoverGroupPairInfo>();
            for (int i = 0; i < sequenceFileE.Count; i++)
            {
                HandoverGroupPairInfo hogp = new HandoverGroupPairInfo(groupPair.Count + 1, sequenceIdealSC[i], sequenceFileE[i]);
                groupPair.Add(hogp);
            }
        }
    }

    public class HandoverGroupPairInfo
    {
        public int Sn { get; set; }
        public string IdealSequence { get; set; }
        public string FileSequence { get; set; }
        public string IdealLAC { get; set; }
        public string IdealCI { get; set; }
        public string FileLAC { get; set; }
        public string FileCI { get; set; }

        private readonly StreetCellInfo idealSC;
        public StreetCellInfo IdealSC { get { return idealSC; } }
        private readonly TimePeriodPoints fileE;
        public TimePeriodPoints FileE { get { return fileE; } }

        public HandoverGroupPairInfo(int sn, StreetCellInfo idealSC, TimePeriodPoints fileE)
        {
            this.Sn = sn;
            this.idealSC = idealSC;
            this.fileE = fileE;
            this.IdealSequence = idealSC == null ? "" : idealSC.CellName;
            this.FileSequence = fileE == null?"":fileE.eStart.CellNameSrc;
            this.IdealLAC = idealSC == null ? "" : idealSC.LAC.ToString();
            this.IdealCI = idealSC == null ? "" : idealSC.CI.ToString();
            this.FileLAC = fileE == null ? "" : fileE.eStart["LAC"].ToString();
            this.FileCI = fileE == null ? "" : fileE.eStart["CI"].ToString();
        }
    }
}
