﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    /// <summary>
    /// NB单验可能有很多代码和LTE相同,并且不是继承LTE
    /// 是由于之前将LTE和NB分离过,分离后2者完全独立
    /// 但改动量太大,后还原,考虑到在NB规划后有可能还会进行分离,暂时先保留代码
    /// </summary>
    public class NbIotStationAcceptAna : DIYAnalyseByCellBackgroundBaseByFile
    {
        /// <summary>
        /// 功能ID
        /// </summary>
        protected int subFuncId { get; set; }
        protected NbIotStationAcceptManager manager { get; set; }
        /// <summary>
        /// 后台配置的条件参数
        /// </summary>
        public NbIotStationAcceptAutoSet FuncSet { get; set; }
        /// <summary>
        /// 需单验的基站小区工参
        /// </summary>
        protected Dictionary<string, Dictionary<int, NbIotBtsWorkParam>> workParamSumDic { get; set; }
        /// <summary>
        /// 需包含的文件名关键字(小区名+基站名)
        /// </summary>
        protected string fileNameKeyStr { get; set; }
        /// <summary>
        /// 当前地市名
        /// </summary>
        protected string curDistrictName { get; set; }
        /// <summary>
        /// 上传单验报告路径
        /// </summary>
        public List<string> ReportFilePaths { get; set; }

        #region instance
        protected static readonly object lockObj = new object();
        private static NbIotStationAcceptAna instance = null;
        public static NbIotStationAcceptAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NbIotStationAcceptAna();
                    }
                }
            }
            return instance;
        }
        #endregion

        protected NbIotStationAcceptAna()
            : base(MainModel.GetInstance())
        {
            this.isIgnoreExport = true;
            FilterSampleByRegion = false;
            FilterEventByRegion = false;

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NBIOT));

            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_PDCP_UL_Mb");
            Columns.Add("lte_PDCP_DL_Mb");
            Columns.Add("lte_MAC_UL");
            Columns.Add("lte_MAC_DL");

            FuncSet = new NbIotStationAcceptAutoSet();
            ReportFilePaths = new List<string>();
        }

        public override string Name
        {
            get { return "NBIot单站验收"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22121, "NBIot单站验收");
        }

        public override void DealBeforeBackgroundQueryByCity()
        {
            subFuncId = GetSubFuncID();
            //获取待分析的工参信息
            workParamSumDic = NbIotGetWorkParamsHelper.GetWorkParamsInfo(FuncSet);
            ReportFilePaths.Clear();
        }

        protected override bool getCondition()
        {
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID).Replace("市", "");
            if (workParamSumDic == null || workParamSumDic.Count <= 0
                || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待评估对象数据");
                return false;
            }
            MainModel.MainForm.GetMapForm().updateMap();
            return true;
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                clientProxy.Close();

                bool stop = analyseCurWorkParam(clientProxy);
                if (stop)
                {
                    return;
                }
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
        }

        private bool analyseCurWorkParam(ClientProxy clientProxy)
        {
            Dictionary<int, NbIotBtsWorkParam> curDistrictWorkParam = workParamSumDic[curDistrictName.Replace("市", "")];
            if (curDistrictWorkParam != null)
            {
                reportBackgroundInfo("读取到" + curDistrictWorkParam.Count + "个站点的工参信息");
                //按工参表中的基站数进行分析
                foreach (NbIotBtsWorkParam btsInfo in curDistrictWorkParam.Values)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        return true;
                    }

                    bool isAccepted = setFileNameKeyStr(btsInfo);
                    if (!isAccepted)
                    {
                        analyseCurBts(clientProxy, btsInfo);
                    }
                }
            }
            return false;
        }

        private void analyseCurBts(ClientProxy clientProxy, NbIotBtsWorkParam btsInfo)
        {
            LTEBTS bts = null;
            bool isNewAdd = addCellInfoToCellManager(btsInfo, ref bts);

            reportBackgroundInfo(string.Format("开始读取基站 {0} 的待分析文件...", btsInfo.BtsName));
            doBackgroundStatByFile(clientProxy);

            reportBackgroundInfo(string.Format("开始读取基站 {0} 的预处理信息...", btsInfo.BtsName));
            exportReportByBgData(btsInfo);

            if (isNewAdd)//将动态添加的工参移除
            {
                foreach (LTECell cell in bts.Cells)
                {
                    MainModel.CellManager.Remove(cell);
                    foreach (LTEAntenna ant in cell.Antennas)
                    {
                        MainModel.CellManager.Remove(ant);
                    }
                }
                MainModel.CellManager.Remove(bts);
            }
        }

        private bool setFileNameKeyStr(NbIotBtsWorkParam btsInfo)
        {
            bool? isAllCellPassed = null;
            StringBuilder strbFilter = new StringBuilder();
            strbFilter.Append(btsInfo.BtsName);//部分站点的小区名并不包含基站名
            foreach (CellWorkParamBase info in btsInfo.CellWorkParamDic.Values)
            {
                NbIotCellWorkParam cellInfo = (NbIotCellWorkParam)info;
                bool isCurCellPassed = cellInfo.StrDes.Contains("已通过");
                if (isAllCellPassed != false)
                {
                    isAllCellPassed = isCurCellPassed;
                }
                strbFilter.Append(string.Format(" or {0}", cellInfo.CellName));
            }
            if (isAllCellPassed == true)
            {
                string folderPath = GetBtsPicFolder(btsInfo.BtsName);
                if (System.IO.Directory.Exists(folderPath))//删除本站之前的覆盖截图
                {
                    System.IO.Directory.Delete(folderPath, true);
                }
                reportBackgroundInfo(string.Format("基站 {0} 已通过验收！", btsInfo.BtsName));
                return true;
            }
            fileNameKeyStr = strbFilter.ToString();
            return false;
        }

        /// <summary>
        /// 添加上传的工参
        /// </summary>
        /// <param name="btsParam"></param>
        /// <param name="bts"></param>
        /// <returns></returns>
        private bool addCellInfoToCellManager(NbIotBtsWorkParam btsParam, ref LTEBTS bts)
        {
            foreach (CellWorkParamBase info in btsParam.CellWorkParams)
            {
                NbIotCellWorkParam cellInfo = (NbIotCellWorkParam)info;
                LTECell nbiotCell = CellManager.GetInstance().GetLTECellByECI(DateTime.Now, cellInfo.Eci);
                if (nbiotCell != null)
                {
                    bts = nbiotCell.BelongBTS;
                    return false;
                }
            }

            bts = new LTEBTS();
            int snapShotId = -1;

            #region 暂时动态添加工参到CellManager，稍后移除

            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = btsParam.BtsName;
            bts.BTSID = btsParam.ENodeBID;
            bts.Type = btsParam.IsOutDoorBts ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

            foreach (CellWorkParamBase info in btsParam.CellWorkParams)
            {
                NbIotCellWorkParam cellParamInfo = (NbIotCellWorkParam)info;
                bts.Longitude = cellParamInfo.Longitude;
                bts.Latitude = cellParamInfo.Latitude;

                snapShotId--;
                LTECell cell = new LTECell();
                cell.Fill(snapShotId, 0, 2147483647);
                cell.BelongBTS = bts;
                cell.Name = cellParamInfo.CellName;
                cell.TAC = cellParamInfo.Tac;
                cell.ECI = cellParamInfo.Eci;
                cell.CellID = cellParamInfo.CellID;
                cell.PCI = cellParamInfo.Pci;
                cell.EARFCN = cellParamInfo.Earfcn;
                cell.DESC = cellParamInfo.AreaType;
                bts.AddCell(cell);
                MainModel.CellManager.Add(cell);

                LTEAntenna antenna = new LTEAntenna();
                snapShotId--;
                antenna.Fill(snapShotId, 0, 2147483647);
                antenna.Cell = cell;
                antenna.Longitude = cellParamInfo.Longitude;
                antenna.Latitude = cellParamInfo.Latitude;
                antenna.Direction = (short)cellParamInfo.Direction;
                antenna.Downward = (short)cellParamInfo.Downward;
                antenna.Altitude = cellParamInfo.Altitude;
            }
            MainModel.CellManager.Add(bts);
            #endregion

            return true;
        }

        /// <summary>
        /// 调用"sp_probchk_cellAccept_file_filterGet"存储过程获取待分析的文件信息
        /// </summary>
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(subFuncId, ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", fileNameKeyStr);
        }

        /// <summary>
        /// 文件过滤(此时从存储过程读出的文件信息仅有文件名可用,后续看是否会规范文件名,否则不在此处过滤)
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <returns></returns>
        protected override bool filterFile(FileInfo fileInfo)
        {
            return false;
        }

        /// <summary>
        /// 文件回放后,将其数据按文件名进行不同的单站验收功能
        /// </summary>
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new NbIotStationAcceptManager();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            MainModel.DTDataManager.Clear();
        }

        /// <summary>
        /// 上传分析结果
        /// </summary>
        protected override void saveBackgroundData()
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            if (manager.NBIotAcceptFileInfo != null && manager.NBIotAcceptFileInfo.AcceptKpiDic.Count > 0)
            {
                BackgroundResult result = manager.NBIotAcceptFileInfo.ConvertToBackgroundResult(subFuncId, BackgroundFuncConfigManager.GetInstance().ProjectType);
                resultList.Add(result);
                manager.NBIotAcceptFileInfo = null;

                //未匹配到目标小区或未获取到指标信息的文件信息暂不保留
                //（有可能是未更新工参信息导致的,或者DT上传下载文件是截取覆盖图用的，每次出报告都要重新查询回放）
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(subFuncId, curAnaFileInfo, resultList);
            }
        }
        /// <summary>
        /// 查询预处理信息，然后导出报告
        /// </summary>
        /// <param name="btsWorkParamInfo"></param>
        protected void exportReportByBgData(NbIotBtsWorkParam btsWorkParamInfo)
        {
            if (MainModel.BackgroundStopRequest)
            {
                return;
            }

            try
            {
                getBackgroundData();
                reportBackgroundInfo(string.Format("共读取到{0}条预处理信息", BackgroundResultList.Count));
                if (BackgroundResultList.Count <= 0)
                {
                    return;
                }
                BackgroundResultList.Sort(BackgroundResult.ComparerByISTimeDesc);

                if (btsWorkParamInfo.IsOutDoorBts)
                {
                    exportOutdoorBtsReport(BackgroundResultList, btsWorkParamInfo);
                }
                else
                {
                    //室内站
                }
            }
            catch(Exception ex)
            {
                reportBackgroundError(ex);
            }
        }

        /// <summary>
        /// 调用"sp_probchk_cellAccept_result_filterGet"存储过程,查询之前上传的结果信息
        /// </summary>
        protected override void getBackgroundData()
        {
            BackgroundFuncConfigManager bgConfigManager = BackgroundFuncConfigManager.GetInstance();
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetFilterResult_CellAccept(new BackgroundFuncQueryManager.CellAcceptCondition(bgConfigManager.ISTime, bgConfigManager.IETime, subFuncId, bgConfigManager.ProjectType, "fileName", fileNameKeyStr), Name, StatType);
        }

        /// <summary>
        /// 导出室外站
        /// </summary>
        /// <param name="bgResultList"></param>
        /// <param name="btsWorkParamInfo"></param>
        protected virtual void exportOutdoorBtsReport(List<BackgroundResult> bgResultList, NbIotBtsWorkParam btsWorkParamInfo)
        {
            NbIotOutDoorBtsAcceptInfo curBtsAcceptInfo = NbIotStaionAcceptResultHelper.GetOutDoorBtsResultByBgData(
                bgResultList, btsWorkParamInfo.ENodeBID);

            if (curBtsAcceptInfo != null)
            {
                bool hasExportReport = NbIotExportOutdoorBtsReportHelper.ExportReports(curBtsAcceptInfo, btsWorkParamInfo);
                updateBtsAcceptDes(curBtsAcceptInfo.IsAccordAccept, hasExportReport, btsWorkParamInfo);
            }
            else
            {
                reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
            }
        }

        /// <summary>
        /// 导出室内站
        /// </summary>
        /// <param name="bgResultList"></param>
        /// <param name="btsWorkParamInfo"></param>
        protected virtual void exportIndoorBtsReport(List<BackgroundResult> bgResultList, NbIotBtsWorkParam btsWorkParamInfo)
        {
            NBIotInDoorBtsAcceptInfo curBtsAcceptInfo = NbIotStaionAcceptResultHelper.GetInDoorBtsResultByBgData(
              bgResultList, btsWorkParamInfo.ENodeBID);

            if (curBtsAcceptInfo != null)
            {
                bool hasExportReport = NbIotExportIndoorBtsReportHelper.ExportReports(curBtsAcceptInfo, btsWorkParamInfo);
                updateBtsAcceptDes(curBtsAcceptInfo.IsAccordAccept, hasExportReport, btsWorkParamInfo);
            }
            else
            {
                reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
            }
        }

        protected void getFusionInfo(BtsAcceptInfoBase btsInfo, NbIotBtsWorkParam btsWorkParamInfo)
        {
            //关联信息,暂未添加
        }

        /// <summary>
        /// 修改上传的工参描述
        /// </summary>
        /// <param name="hasPassedAccept"></param>
        /// <param name="hasExportReport"></param>
        /// <param name="btsParam"></param>
        protected void updateBtsAcceptDes(bool hasPassedAccept, bool hasExportReport, NbIotBtsWorkParam btsParam)
        {
            if (hasPassedAccept && hasExportReport)
            {
                foreach (CellWorkParamBase info in btsParam.CellWorkParamDic.Values)
                {
                    NbIotCellWorkParam cellParamInfo = (NbIotCellWorkParam)info;
                    cellParamInfo.StrDes = "已通过";
                }

                NbIotUpdateWorkParamDes upFunc = new NbIotUpdateWorkParamDes(btsParam);
                upFunc.Query();
            }
        }

        /// <summary>
        /// 获取某种覆盖截图的保存路径
        /// </summary>
        /// <param name="btsName"></param>
        /// <param name="cellName"></param>
        /// <param name="paramName"></param>
        /// <returns></returns>
        public string GetCoverPicPath(string btsName, string cellName, string paramName)
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
        }

        /// <summary>
        /// 获取站点覆盖截图保存文件夹地址
        /// </summary>
        /// <param name="btsName"></param>
        /// <returns></returns>
        public virtual string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(NbIotExportOutdoorBtsReportHelper.WorkDir, btsName.Trim());
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("ExportReportSet");
                ignoreParamKeys.Add("Params_FTP");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["ExportReportSet"] = FuncSet.Params;
                param["Params_FTP"] = FuncSet.FtpSetInfo.Params;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    FuncSet.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
                if (param.ContainsKey("Params_FTP"))
                {
                    FuncSet.FtpSetInfo.Params = param["Params_FTP"] as Dictionary<string, object>;
                }
            }
        }

        /// <summary>
        /// 设置功能条件属性
        /// </summary>
        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptPropertiesXJ_LTE(this, FuncSet);
            }
        }

        public override void DealAfterBackgroundQueryByCity()
        {
            //上传单验报告,暂未添加
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.NBIot业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
    }
}
