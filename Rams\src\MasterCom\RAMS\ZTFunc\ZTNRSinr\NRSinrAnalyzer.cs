﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRSinrAnalyzer
    {
        public NRSinrCondition SinrCond { get; set; }
        //保存所有结果
        public List<NRSinrResultModel> Results { get; set; }

        public NRSinrAnalyzer()
        {
            SinrCond = new NRSinrCondition();
            Results = new List<NRSinrResultModel>();
        }

        public void Refreshs()
        {
            if (Results == null)
            {
                Results = new List<NRSinrResultModel>();
            }
            else
            {
                Results.Clear();
            }
            NRSinrCondition sinr = SinrCond;
            for (int i = 1; i < 4; i++)
            {
                NRSinrResultModel result = new NRSinrResultModel(sinr.SINRType, sinr.SINR, sinr.KPIName, i);
                List<NRSinrItemModel> items = createModelList(sinr);
                List<NRSinrItemModel> totalItems = createModelList(sinr);
                result.NRSinrModel.Items = items;
                result.totalModel.Items = totalItems;

                Results.Add(result);
            }
        }

        public List<NRSinrResultModel> GetResult()
        {
            return Results;
        }

        public void ClearResult()
        {
            Results = new List<NRSinrResultModel>();
        }

        public void Analyze(List<TestPoint> tps, int carrierType)
        {
            foreach (TestPoint tp in tps)
            {
                analyzeSINR(tp, carrierType);
            }
        }

        private void analyzeSINR(TestPoint tp, int carrierType)
        {
            NRSinrCondition sinr = SinrCond;

            float sinrValue = (float)NRTpHelper.NrTpManager.GetSCellSinr(tp);
            if (sinr.IsEligibility(sinrValue))
            {
                analyzeItem(tp, Results[carrierType - 1].NRSinrModel);
            }
            analyzeItem(tp, Results[carrierType - 1].totalModel);
        }

        private void analyzeItem(TestPoint tp, NRSinrModel NRSinrModel)
        {
            foreach (NRSinrItemModel item in NRSinrModel.Items)
            {
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                if (rsrp == null)
                {
                    return;
                }
                bool isAdded = addValidPointCount(item, rsrp);
                if (isAdded)
                {
                    return;
                }
            }
        }

        private bool addValidPointCount(NRSinrItemModel item, float? rsrp)
        {
            bool isAdded = false;
            if (item.OperType == (int)NRSinrOperatorsType.GreaterThan)
            {
                if (rsrp > item.ExtremumFirst)
                {
                    item.PointCount++;
                    isAdded = true;
                }
            }
            else if (item.OperType == (int)NRSinrOperatorsType.GreaterThanOrEqual)
            {
                if (rsrp > item.ExtremumFirst && rsrp <= item.ExtremumSecond)
                {
                    item.PointCount++;
                    isAdded = true;
                }
            }
            else if (item.OperType == (int)NRSinrOperatorsType.LessThanOrEqual)
            {
                if (rsrp <= item.ExtremumFirst)
                {
                    item.PointCount++;
                    isAdded = true;
                }
            }
            else
            {
                isAdded = false;
            }
            return isAdded;
        }

        private List<NRSinrItemModel> createModelList(NRSinrCondition sinr)
        {
            List<NRSinrItemModel> itemModels = new List<NRSinrItemModel>();
            NRSinrItemModel temp;
            foreach (NRSinrConditionItem item in sinr.Conditions)
            {
                temp = new NRSinrItemModel(item.OperType, item.ExtremumFirst, item.ExtremumSecond, sinr.KPIName);
                itemModels.Add(temp);
            }
            return itemModels;
        }
    }
}
