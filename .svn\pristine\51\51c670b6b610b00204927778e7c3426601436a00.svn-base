﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GSMCellEmulateCovMRForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShapefile = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportPcb = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportCheckBcp = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportMapBcp = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.colorEdit70 = new DevExpress.XtraEditors.ColorEdit();
            this.labelControl70 = new DevExpress.XtraEditors.LabelControl();
            this.btnDraw = new DevExpress.XtraEditors.SimpleButton();
            this.colorEditNonCov = new DevExpress.XtraEditors.ColorEdit();
            this.colorEdit95 = new DevExpress.XtraEditors.ColorEdit();
            this.colorEdit94 = new DevExpress.XtraEditors.ColorEdit();
            this.colorEdit90 = new DevExpress.XtraEditors.ColorEdit();
            this.colorEdit85 = new DevExpress.XtraEditors.ColorEdit();
            this.colorEdit80 = new DevExpress.XtraEditors.ColorEdit();
            this.labelControlNonCov = new DevExpress.XtraEditors.LabelControl();
            this.labelControl95 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl94 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl90 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl85 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl80 = new DevExpress.XtraEditors.LabelControl();
            this.button1 = new System.Windows.Forms.Button();
            this.miExportResultBcp = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit70.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEditNonCov.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit95.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit94.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit90.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit85.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit80.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6});
            this.listView1.ContextMenuStrip = this.contextMenuStrip1;
            this.listView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView1.GridLines = true;
            this.listView1.Location = new System.Drawing.Point(2, 23);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(634, 415);
            this.listView1.TabIndex = 0;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "区域名称";
            this.columnHeader1.Width = 88;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "总栅格数";
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "总面积(平方公里)";
            this.columnHeader3.Width = 118;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "覆盖栅格数";
            this.columnHeader4.Width = 109;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "覆盖面积(平方公里)";
            this.columnHeader5.Width = 133;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "面积覆盖率(%)";
            this.columnHeader6.Width = 118;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExportShapefile,
            this.miExportTxt,
            this.miExportPcb});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(185, 114);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(184, 22);
            this.miExportExcel.Text = "导出到Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportShapefile
            // 
            this.miExportShapefile.Name = "miExportShapefile";
            this.miExportShapefile.Size = new System.Drawing.Size(184, 22);
            this.miExportShapefile.Text = "导出图层Shape文件";
            this.miExportShapefile.Click += new System.EventHandler(this.miExportShapefile_Click);
            // 
            // miExportTxt
            // 
            this.miExportTxt.Name = "miExportTxt";
            this.miExportTxt.Size = new System.Drawing.Size(184, 22);
            this.miExportTxt.Text = "导出图层Txt文件";
            this.miExportTxt.Click += new System.EventHandler(this.miExportTxt_Click);
            // 
            // miExportPcb
            // 
            this.miExportPcb.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportCheckBcp,
            this.miExportMapBcp,
            this.miExportResultBcp});
            this.miExportPcb.Name = "miExportPcb";
            this.miExportPcb.Size = new System.Drawing.Size(184, 22);
            this.miExportPcb.Text = "导出图层bcp文件";
            // 
            // miExportCheckBcp
            // 
            this.miExportCheckBcp.Name = "miExportCheckBcp";
            this.miExportCheckBcp.Size = new System.Drawing.Size(214, 22);
            this.miExportCheckBcp.Text = "导出历史查询条件bcp";
            this.miExportCheckBcp.Click += new System.EventHandler(this.miExportCheckBcp_Click);
            // 
            // miExportMapBcp
            // 
            this.miExportMapBcp.Name = "miExportMapBcp";
            this.miExportMapBcp.Size = new System.Drawing.Size(214, 22);
            this.miExportMapBcp.Text = "导出地图结果bcp";
            this.miExportMapBcp.Click += new System.EventHandler(this.miExportMapBcp_Click);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.listView1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Left;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(638, 440);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "数据";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.colorEdit70);
            this.groupControl2.Controls.Add(this.labelControl70);
            this.groupControl2.Controls.Add(this.btnDraw);
            this.groupControl2.Controls.Add(this.colorEditNonCov);
            this.groupControl2.Controls.Add(this.colorEdit95);
            this.groupControl2.Controls.Add(this.colorEdit94);
            this.groupControl2.Controls.Add(this.colorEdit90);
            this.groupControl2.Controls.Add(this.colorEdit85);
            this.groupControl2.Controls.Add(this.colorEdit80);
            this.groupControl2.Controls.Add(this.labelControlNonCov);
            this.groupControl2.Controls.Add(this.labelControl95);
            this.groupControl2.Controls.Add(this.labelControl94);
            this.groupControl2.Controls.Add(this.labelControl90);
            this.groupControl2.Controls.Add(this.labelControl85);
            this.groupControl2.Controls.Add(this.labelControl80);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(638, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(236, 440);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "    图例";
            // 
            // colorEdit70
            // 
            this.colorEdit70.EditValue = System.Drawing.Color.Empty;
            this.colorEdit70.Location = new System.Drawing.Point(113, 69);
            this.colorEdit70.Name = "colorEdit70";
            this.colorEdit70.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit70.Size = new System.Drawing.Size(100, 21);
            this.colorEdit70.TabIndex = 28;
            // 
            // labelControl70
            // 
            this.labelControl70.Location = new System.Drawing.Point(53, 72);
            this.labelControl70.Name = "labelControl70";
            this.labelControl70.Size = new System.Drawing.Size(54, 14);
            this.labelControl70.TabIndex = 27;
            this.labelControl70.Text = "70 覆盖率";
            // 
            // btnDraw
            // 
            this.btnDraw.Location = new System.Drawing.Point(78, 258);
            this.btnDraw.Name = "btnDraw";
            this.btnDraw.Size = new System.Drawing.Size(75, 23);
            this.btnDraw.TabIndex = 26;
            this.btnDraw.Text = "绘制";
            this.btnDraw.Click += new System.EventHandler(this.btnDraw_Click);
            // 
            // colorEditNonCov
            // 
            this.colorEditNonCov.EditValue = System.Drawing.Color.Empty;
            this.colorEditNonCov.Location = new System.Drawing.Point(113, 40);
            this.colorEditNonCov.Name = "colorEditNonCov";
            this.colorEditNonCov.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEditNonCov.Size = new System.Drawing.Size(100, 21);
            this.colorEditNonCov.TabIndex = 25;
            // 
            // colorEdit95
            // 
            this.colorEdit95.EditValue = System.Drawing.Color.Empty;
            this.colorEdit95.Location = new System.Drawing.Point(113, 219);
            this.colorEdit95.Name = "colorEdit95";
            this.colorEdit95.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit95.Size = new System.Drawing.Size(100, 21);
            this.colorEdit95.TabIndex = 24;
            // 
            // colorEdit94
            // 
            this.colorEdit94.EditValue = System.Drawing.Color.Empty;
            this.colorEdit94.Location = new System.Drawing.Point(113, 189);
            this.colorEdit94.Name = "colorEdit94";
            this.colorEdit94.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit94.Size = new System.Drawing.Size(100, 21);
            this.colorEdit94.TabIndex = 23;
            // 
            // colorEdit90
            // 
            this.colorEdit90.EditValue = System.Drawing.Color.Empty;
            this.colorEdit90.Location = new System.Drawing.Point(113, 159);
            this.colorEdit90.Name = "colorEdit90";
            this.colorEdit90.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit90.Size = new System.Drawing.Size(100, 21);
            this.colorEdit90.TabIndex = 22;
            // 
            // colorEdit85
            // 
            this.colorEdit85.EditValue = System.Drawing.Color.Empty;
            this.colorEdit85.Location = new System.Drawing.Point(113, 129);
            this.colorEdit85.Name = "colorEdit85";
            this.colorEdit85.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit85.Size = new System.Drawing.Size(100, 21);
            this.colorEdit85.TabIndex = 21;
            // 
            // colorEdit80
            // 
            this.colorEdit80.EditValue = System.Drawing.Color.Empty;
            this.colorEdit80.Location = new System.Drawing.Point(113, 99);
            this.colorEdit80.Name = "colorEdit80";
            this.colorEdit80.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit80.Size = new System.Drawing.Size(100, 21);
            this.colorEdit80.TabIndex = 20;
            // 
            // labelControlNonCov
            // 
            this.labelControlNonCov.Location = new System.Drawing.Point(71, 43);
            this.labelControlNonCov.Name = "labelControlNonCov";
            this.labelControlNonCov.Size = new System.Drawing.Size(36, 14);
            this.labelControlNonCov.TabIndex = 19;
            this.labelControlNonCov.Text = "无覆盖";
            // 
            // labelControl95
            // 
            this.labelControl95.Location = new System.Drawing.Point(53, 222);
            this.labelControl95.Name = "labelControl95";
            this.labelControl95.Size = new System.Drawing.Size(54, 14);
            this.labelControl95.TabIndex = 18;
            this.labelControl95.Text = "95 覆盖率";
            // 
            // labelControl94
            // 
            this.labelControl94.Location = new System.Drawing.Point(53, 192);
            this.labelControl94.Name = "labelControl94";
            this.labelControl94.Size = new System.Drawing.Size(54, 14);
            this.labelControl94.TabIndex = 17;
            this.labelControl94.Text = "94 覆盖率";
            // 
            // labelControl90
            // 
            this.labelControl90.Location = new System.Drawing.Point(53, 162);
            this.labelControl90.Name = "labelControl90";
            this.labelControl90.Size = new System.Drawing.Size(54, 14);
            this.labelControl90.TabIndex = 16;
            this.labelControl90.Text = "90 覆盖率";
            // 
            // labelControl85
            // 
            this.labelControl85.Location = new System.Drawing.Point(53, 132);
            this.labelControl85.Name = "labelControl85";
            this.labelControl85.Size = new System.Drawing.Size(54, 14);
            this.labelControl85.TabIndex = 15;
            this.labelControl85.Text = "85 覆盖率";
            // 
            // labelControl80
            // 
            this.labelControl80.Location = new System.Drawing.Point(53, 102);
            this.labelControl80.Name = "labelControl80";
            this.labelControl80.Size = new System.Drawing.Size(54, 14);
            this.labelControl80.TabIndex = 14;
            this.labelControl80.Text = "80 覆盖率";
            // 
            // button1
            // 
            this.button1.Dock = System.Windows.Forms.DockStyle.Left;
            this.button1.Location = new System.Drawing.Point(638, 0);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(14, 440);
            this.button1.TabIndex = 3;
            this.button1.Text = "<";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // miExportResultBcp
            // 
            this.miExportResultBcp.Name = "miExportResultBcp";
            this.miExportResultBcp.Size = new System.Drawing.Size(214, 22);
            this.miExportResultBcp.Text = "导出MR仿真覆盖结果bcp";
            this.miExportResultBcp.Click += new System.EventHandler(this.miExportResultBcp_Click);
            // 
            // GSMCellEmulateCovMRForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(874, 440);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Name = "GSMCellEmulateCovMRForm";
            this.Text = "MR仿真覆盖";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit70.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEditNonCov.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit95.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit94.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit90.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit85.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit80.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listView1;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnDraw;
        private DevExpress.XtraEditors.ColorEdit colorEditNonCov;
        private DevExpress.XtraEditors.ColorEdit colorEdit95;
        private DevExpress.XtraEditors.ColorEdit colorEdit94;
        private DevExpress.XtraEditors.ColorEdit colorEdit90;
        private DevExpress.XtraEditors.ColorEdit colorEdit85;
        private DevExpress.XtraEditors.ColorEdit colorEdit80;
        private DevExpress.XtraEditors.LabelControl labelControlNonCov;
        private DevExpress.XtraEditors.LabelControl labelControl95;
        private DevExpress.XtraEditors.LabelControl labelControl94;
        private DevExpress.XtraEditors.LabelControl labelControl90;
        private DevExpress.XtraEditors.LabelControl labelControl85;
        private DevExpress.XtraEditors.LabelControl labelControl80;
        private System.Windows.Forms.Button button1;
        private DevExpress.XtraEditors.ColorEdit colorEdit70;
        private DevExpress.XtraEditors.LabelControl labelControl70;
        private System.Windows.Forms.ToolStripMenuItem miExportShapefile;
        private System.Windows.Forms.ToolStripMenuItem miExportTxt;
        private System.Windows.Forms.ToolStripMenuItem miExportPcb;
        private System.Windows.Forms.ToolStripMenuItem miExportCheckBcp;
        private System.Windows.Forms.ToolStripMenuItem miExportMapBcp;
        private System.Windows.Forms.ToolStripMenuItem miExportResultBcp;
    }
}