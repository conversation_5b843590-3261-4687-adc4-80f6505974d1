﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using DTLibrary;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraPerformanceDrawSetForm : DevExpress.XtraEditors.XtraForm
    {
        public XtraPerformanceDrawSetForm(MainModel mainmodel, string mynetwork, List<DtParaColumnItem> KPISplit)
        {
            InitializeComponent();
            mainModel = mainmodel;
            network = mynetwork;
            newKPISplit = new List<DtParaColumnItem>();
            newKPISplit.AddRange(KPISplit);
            comboBoxKPI.Items.Clear();
            foreach (DtParaColumnItem kpi in KPISplit)
            {
                comboBoxKPI.Items.Add(kpi.StrKpiName);
            }
            if (comboBoxKPI.Items.Count > 0)
            {
                comboBoxKPI.SelectedIndex = 0;
                foreach (DtParaColumnItem kpi in KPISplit)
                {
                    if (comboBoxKPI.Items[0].Equals(kpi.StrKpiName))
                    {
                        minKpi.Text = kpi.StrThresholdDraw.Replace("[", "").Replace("]", "").Replace("-","").Split(',')[0].ToString();
                        maxKpi.Text = kpi.StrThresholdDraw.Replace("[", "").Replace("]", "").Replace("-","").Split(',')[1].ToString();
                        if (double.Parse(minKpi.Text.ToString()) > double.Parse(maxKpi.Text.ToString()))
                        {
                            minKpi.Text = "-" + minKpi.Text;
                            maxKpi.Text = "-" + maxKpi.Text;
                        }
                    }
                }
            }
        }
        MainModel mainModel;
        string network;
        List<DtParaColumnItem> newKPISplit;
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        /// <summary>
        /// 获取设置的值
        /// </summary>
        public void getSelect(out string name,out DateTime time,out double  minKPI,out double maxKPI)
        {
            name = comboBoxKPI.SelectedItem.ToString();
            time = dateTimePickerKPI.Value.AddMinutes(-dateTimePickerKPI.Value.Minute).AddSeconds(-dateTimePickerKPI.Value.Second);
            minKPI = double.Parse(minKpi.Text);
            maxKPI = double.Parse(maxKpi.Text);
        }
        /// <summary>
        /// 选择不同指标，显示相应的阈值
        /// </summary>
        private void comboBoxKPI_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (comboBoxKPI.SelectedItem.ToString() != "")
            {
                foreach (DtParaColumnItem kpi in newKPISplit)
                {
                    if (comboBoxKPI.SelectedItem.ToString().Equals(kpi.StrKpiName))
                    {
                        minKpi.Text = kpi.StrThresholdDraw.Replace("[", "").Replace("]", "").Replace("-", "").Split(',')[0].ToString();
                        maxKpi.Text = kpi.StrThresholdDraw.Replace("[", "").Replace("]", "").Replace("-", "").Split(',')[1].ToString();
                        if (double.Parse(minKpi.Text.ToString()) > double.Parse(maxKpi.Text.ToString()))
                        {
                            minKpi.Text = "-" + minKpi.Text;
                            maxKpi.Text = "-" + maxKpi.Text;
                        }
                    }
                }
            }
        }
    }
}
