using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using System.Drawing.Drawing2D;
using MasterCom.Util;

namespace MasterCom.RAMS.Util
{
    public partial class ListViewPrinterForm : BaseFormStyle
    {
        private ListView listView;
        public ListViewPrinterForm(ListView listView)
        {
            InitializeComponent();
            this.ListView = listView;
            InitializeListPrinting();
        }

        public ListView ListView
        {
            get { return listView; }
            set
            {
                this.listView = value;
                InitializeListPrinting();
            }
        }

        void InitializeListPrinting()
        {
            this.printPreviewControl1.Zoom = 1;
            this.printPreviewControl1.AutoZoom = true;

            this.UpdatePrintPreview();
        }

        void UpdatePrintPreview()
        {
            this.listViewPrinter1.ListView = this.listView;
            this.listViewPrinter1.DocumentName = this.tbTitle.Text;
            this.listViewPrinter1.Header = this.tbHeader.Text.Replace("\\t", "\t");
            this.listViewPrinter1.Footer = this.tbFooter.Text.Replace("\\t", "\t");
            this.listViewPrinter1.Watermark = this.tbWatermark.Text;

            this.listViewPrinter1.IsShrinkToFit = this.cbShrinkToFit.Checked;
            this.listViewPrinter1.IsTextOnly = !this.cbIncludeImages.Checked;
            this.listViewPrinter1.IsPrintSelectionOnly = this.cbPrintOnlySelection.Checked;

            this.ApplyModernFormatting();

            if (!this.cbCellGridLines.Checked)
                this.listViewPrinter1.ListGridPen = null;

            this.listViewPrinter1.FirstPage = (int)this.numericUpDown1.Value;
            this.listViewPrinter1.LastPage = (int)this.numericUpDown2.Value;

            this.printPreviewControl1.InvalidatePreview();
        }

        public void ApplyModernFormatting()
        {
            this.listViewPrinter1.CellFormat = null;
            this.listViewPrinter1.ListFont = new Font("Ms Sans Serif", 9);
            this.listViewPrinter1.ListGridPen = new Pen(Color.DarkGray, 0.5f);

            this.listViewPrinter1.HeaderFormat = BlockFormat.Header(new Font("Verdana", 24, FontStyle.Bold));
            this.listViewPrinter1.HeaderFormat.BackgroundBrush = new LinearGradientBrush(new Point(0, 0), new Point(200, 0), Color.DarkBlue, Color.White);

            this.listViewPrinter1.FooterFormat = BlockFormat.Footer();
            this.listViewPrinter1.FooterFormat.BackgroundBrush = new LinearGradientBrush(new Point(0, 0), new Point(200, 0), Color.White, Color.Blue);

            this.listViewPrinter1.GroupHeaderFormat = BlockFormat.GroupHeader();
            this.listViewPrinter1.ListHeaderFormat = BlockFormat.ListHeader(new Font("Verdana", 12));

            this.listViewPrinter1.WatermarkFont = null;
            this.listViewPrinter1.WatermarkColor = Color.Empty;
        }

        void UpdatePreview(object sender, EventArgs e)
        {
            this.UpdatePrintPreview();
        }

        private void button6_Click(object sender, EventArgs e)
        {
            this.listViewPrinter1.PageSetup();
            this.UpdatePrintPreview();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            this.listViewPrinter1.PrintPreview();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            this.listViewPrinter1.PrintWithDialog();
        }

    }
}