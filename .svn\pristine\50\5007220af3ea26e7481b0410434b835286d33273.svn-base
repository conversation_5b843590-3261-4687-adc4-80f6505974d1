﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 竞对分析显示列
    /// </summary>
    public class CompareDisplayColumn
    {
        public static string MakeShortCaption(DTDisplayParameterInfo param, int paramArrIdx)
        {
            string idxStr = paramArrIdx == -1 ? " " : "[" + paramArrIdx.ToString() + "] ";
            return param.Name + idxStr;
        }
        public CompareDisplayColumn() { }
        public CompareDisplayColumn(string caption, string param, string flag)
        {
            this.Caption = caption;
            this.DisplayIndexName = param;
            this.Flag = flag;
        }
        public string Caption { get; set; } = "未命名";
        /// <summary>
        /// 由 ParamName 和 ParamArrayIndex 组成的参数唯一key
        /// </summary>
        public string ParamKey
        {
            get
            {
                return string.Format("{0}[{1}]", DisplayIndexName,
                    Flag);
            }
        }
        public override string ToString()
        {
            return Caption;
        }
        public string DisplayIndexName
        {
            get;
            set;
        }
        public string Flag
        {
            get;
            set;
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["Caption"] = this.Caption;
                paramDic["ParamName"] = this.DisplayIndexName;
                paramDic["Flag"] = this.Flag;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Caption = value["Caption"].ToString();
                this.DisplayIndexName = value["ParamName"].ToString();
                this.Flag = value["Flag"].ToString();
            }
        }

    }
}
