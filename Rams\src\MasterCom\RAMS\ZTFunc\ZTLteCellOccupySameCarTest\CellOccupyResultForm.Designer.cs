﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellOccupyResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvFileResult = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcPortFileID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPortFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBothIdleSameCellPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBothIdleDiffCellPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcHostIdleGuestOccupySameCellPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcHostIdleGuestOccupyDiffCellPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcHostOccupyGuestIdleSameCellPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcHostOccupyGuestIdleDiffCellPercent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcSameCellDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDiffCellDiFreqDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDiffCellCoFreqDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlResult = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuStripResult = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsMenuItemExportResult = new System.Windows.Forms.ToolStripMenuItem();
            this.tsMenuItemExpand = new System.Windows.Forms.ToolStripMenuItem();
            this.tsMenuItemCollapse = new System.Windows.Forms.ToolStripMenuItem();
            this.gvResult = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcGroupSn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcGroupFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.gridControlFile = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuStripFiles = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsMenuItemExportFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.gvFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcFileID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcProjectType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcServiceType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCarrier = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.simBtnDeleteFile = new DevExpress.XtraEditors.SimpleButton();
            this.simBtnCombine = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.panel2 = new System.Windows.Forms.Panel();
            this.simBtnDeleteGroup = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.gvFileResult)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlResult)).BeginInit();
            this.ctxMenuStripResult.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvResult)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlFile)).BeginInit();
            this.ctxMenuStripFiles.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvFile)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // gvFileResult
            // 
            this.gvFileResult.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcPortFileID,
            this.gcPortFileName,
            this.gcBothIdleSameCellPercent,
            this.gcBothIdleDiffCellPercent,
            this.gcHostIdleGuestOccupySameCellPercent,
            this.gcHostIdleGuestOccupyDiffCellPercent,
            this.gcHostOccupyGuestIdleSameCellPercent,
            this.gcHostOccupyGuestIdleDiffCellPercent,
            this.gcSameCellDuration,
            this.gcDiffCellDiFreqDuration,
            this.gcDiffCellCoFreqDuration,
            this.gridColumn1});
            this.gvFileResult.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gvFileResult.GridControl = this.gridControlResult;
            this.gvFileResult.Name = "gvFileResult";
            this.gvFileResult.OptionsBehavior.Editable = false;
            this.gvFileResult.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvFileResult.OptionsView.AllowCellMerge = true;
            this.gvFileResult.OptionsView.ColumnAutoWidth = false;
            this.gvFileResult.OptionsView.ShowGroupPanel = false;
            // 
            // gcPortFileID
            // 
            this.gcPortFileID.Caption = "文件ID";
            this.gcPortFileID.FieldName = "ID";
            this.gcPortFileID.Name = "gcPortFileID";
            this.gcPortFileID.Visible = true;
            this.gcPortFileID.VisibleIndex = 0;
            // 
            // gcPortFileName
            // 
            this.gcPortFileName.Caption = "文件名";
            this.gcPortFileName.FieldName = "Name";
            this.gcPortFileName.Name = "gcPortFileName";
            this.gcPortFileName.Visible = true;
            this.gcPortFileName.VisibleIndex = 1;
            // 
            // gcBothIdleSameCellPercent
            // 
            this.gcBothIdleSameCellPercent.Caption = "均空闲且占同一小区(%)";
            this.gcBothIdleSameCellPercent.FieldName = "BothIdle_SameCell_Percent";
            this.gcBothIdleSameCellPercent.Name = "gcBothIdleSameCellPercent";
            this.gcBothIdleSameCellPercent.Visible = true;
            this.gcBothIdleSameCellPercent.VisibleIndex = 2;
            this.gcBothIdleSameCellPercent.Width = 120;
            // 
            // gcBothIdleDiffCellPercent
            // 
            this.gcBothIdleDiffCellPercent.Caption = "均空闲且占不同小区(%)";
            this.gcBothIdleDiffCellPercent.FieldName = "BothIdle_DiffCell_Percent";
            this.gcBothIdleDiffCellPercent.Name = "gcBothIdleDiffCellPercent";
            this.gcBothIdleDiffCellPercent.Visible = true;
            this.gcBothIdleDiffCellPercent.VisibleIndex = 3;
            this.gcBothIdleDiffCellPercent.Width = 120;
            // 
            // gcHostIdleGuestOccupySameCellPercent
            // 
            this.gcHostIdleGuestOccupySameCellPercent.Caption = "主端空闲，对端业务态，且占同一小区(%)";
            this.gcHostIdleGuestOccupySameCellPercent.FieldName = "HostIdleGuestOccupy_SameCell_Percent";
            this.gcHostIdleGuestOccupySameCellPercent.Name = "gcHostIdleGuestOccupySameCellPercent";
            this.gcHostIdleGuestOccupySameCellPercent.Visible = true;
            this.gcHostIdleGuestOccupySameCellPercent.VisibleIndex = 4;
            this.gcHostIdleGuestOccupySameCellPercent.Width = 120;
            // 
            // gcHostIdleGuestOccupyDiffCellPercent
            // 
            this.gcHostIdleGuestOccupyDiffCellPercent.Caption = "主端空闲，对端业务态，且占不同小区(%)";
            this.gcHostIdleGuestOccupyDiffCellPercent.FieldName = "HostIdleGuestOccupy_DiffCell_Percent";
            this.gcHostIdleGuestOccupyDiffCellPercent.Name = "gcHostIdleGuestOccupyDiffCellPercent";
            this.gcHostIdleGuestOccupyDiffCellPercent.Visible = true;
            this.gcHostIdleGuestOccupyDiffCellPercent.VisibleIndex = 5;
            this.gcHostIdleGuestOccupyDiffCellPercent.Width = 120;
            // 
            // gcHostOccupyGuestIdleSameCellPercent
            // 
            this.gcHostOccupyGuestIdleSameCellPercent.Caption = "主端业务态、对端空闲，且占同一小区(%)";
            this.gcHostOccupyGuestIdleSameCellPercent.FieldName = "HostOccupyGuestIdle_SameCell_Percent";
            this.gcHostOccupyGuestIdleSameCellPercent.Name = "gcHostOccupyGuestIdleSameCellPercent";
            this.gcHostOccupyGuestIdleSameCellPercent.Visible = true;
            this.gcHostOccupyGuestIdleSameCellPercent.VisibleIndex = 6;
            this.gcHostOccupyGuestIdleSameCellPercent.Width = 120;
            // 
            // gcHostOccupyGuestIdleDiffCellPercent
            // 
            this.gcHostOccupyGuestIdleDiffCellPercent.Caption = "主端业务态、对端空闲，且占不同小区(%)";
            this.gcHostOccupyGuestIdleDiffCellPercent.FieldName = "HostOccupyGuestIdle_DiffCell_Percent";
            this.gcHostOccupyGuestIdleDiffCellPercent.Name = "gcHostOccupyGuestIdleDiffCellPercent";
            this.gcHostOccupyGuestIdleDiffCellPercent.Visible = true;
            this.gcHostOccupyGuestIdleDiffCellPercent.VisibleIndex = 7;
            this.gcHostOccupyGuestIdleDiffCellPercent.Width = 120;
            // 
            // gcSameCellDuration
            // 
            this.gcSameCellDuration.Caption = "均处业务态且占同一小区(%)";
            this.gcSameCellDuration.FieldName = "SameCellOccupyPercent";
            this.gcSameCellDuration.Name = "gcSameCellDuration";
            this.gcSameCellDuration.Visible = true;
            this.gcSameCellDuration.VisibleIndex = 8;
            this.gcSameCellDuration.Width = 120;
            // 
            // gcDiffCellDiFreqDuration
            // 
            this.gcDiffCellDiFreqDuration.Caption = "均处业务态且占异频不同小区(%)";
            this.gcDiffCellDiFreqDuration.FieldName = "DiffCellDiFreqOccupyPercent";
            this.gcDiffCellDiFreqDuration.Name = "gcDiffCellDiFreqDuration";
            this.gcDiffCellDiFreqDuration.Visible = true;
            this.gcDiffCellDiFreqDuration.VisibleIndex = 9;
            this.gcDiffCellDiFreqDuration.Width = 120;
            // 
            // gcDiffCellCoFreqDuration
            // 
            this.gcDiffCellCoFreqDuration.Caption = "均处业务态且占同频不同小区(%)";
            this.gcDiffCellCoFreqDuration.FieldName = "DiffCellCoFreqOccupyPercent";
            this.gcDiffCellCoFreqDuration.Name = "gcDiffCellCoFreqDuration";
            this.gcDiffCellCoFreqDuration.Visible = true;
            this.gcDiffCellCoFreqDuration.VisibleIndex = 10;
            this.gcDiffCellCoFreqDuration.Width = 120;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "测试时长(s)";
            this.gridColumn1.FieldName = "TestDuration";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 11;
            this.gridColumn1.Width = 100;
            // 
            // gridControlResult
            // 
            this.gridControlResult.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControlResult.ContextMenuStrip = this.ctxMenuStripResult;
            gridLevelNode1.LevelTemplate = this.gvFileResult;
            gridLevelNode1.RelationName = "OccupyCellResults";
            this.gridControlResult.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlResult.Location = new System.Drawing.Point(2, 25);
            this.gridControlResult.MainView = this.gvResult;
            this.gridControlResult.Name = "gridControlResult";
            this.gridControlResult.ShowOnlyPredefinedDetails = true;
            this.gridControlResult.Size = new System.Drawing.Size(908, 244);
            this.gridControlResult.TabIndex = 1;
            this.gridControlResult.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvResult,
            this.gvFileResult});
            // 
            // ctxMenuStripResult
            // 
            this.ctxMenuStripResult.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsMenuItemExportResult,
            this.tsMenuItemExpand,
            this.tsMenuItemCollapse});
            this.ctxMenuStripResult.Name = "ctxMenuStripResult";
            this.ctxMenuStripResult.Size = new System.Drawing.Size(151, 70);
            // 
            // tsMenuItemExportResult
            // 
            this.tsMenuItemExportResult.Name = "tsMenuItemExportResult";
            this.tsMenuItemExportResult.Size = new System.Drawing.Size(150, 22);
            this.tsMenuItemExportResult.Text = "导出到Excel...";
            this.tsMenuItemExportResult.Click += new System.EventHandler(this.tsMenuItemExportResult_Click);
            // 
            // tsMenuItemExpand
            // 
            this.tsMenuItemExpand.Name = "tsMenuItemExpand";
            this.tsMenuItemExpand.Size = new System.Drawing.Size(150, 22);
            this.tsMenuItemExpand.Text = "全部展开";
            this.tsMenuItemExpand.Click += new System.EventHandler(this.tsMenuItemExpand_Click);
            // 
            // tsMenuItemCollapse
            // 
            this.tsMenuItemCollapse.Name = "tsMenuItemCollapse";
            this.tsMenuItemCollapse.Size = new System.Drawing.Size(150, 22);
            this.tsMenuItemCollapse.Text = "全部收缩";
            this.tsMenuItemCollapse.Click += new System.EventHandler(this.tsMenuItemCollapse_Click);
            // 
            // gvResult
            // 
            this.gvResult.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvResult.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvResult.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Green;
            this.gvResult.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvResult.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvResult.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvResult.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvResult.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Green;
            this.gvResult.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvResult.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvResult.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvResult.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvResult.Appearance.SelectedRow.BorderColor = System.Drawing.Color.Green;
            this.gvResult.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvResult.Appearance.SelectedRow.Options.UseBorderColor = true;
            this.gvResult.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcGroupSn,
            this.gcGroupFileName});
            this.gvResult.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gvResult.GridControl = this.gridControlResult;
            this.gvResult.Name = "gvResult";
            this.gvResult.OptionsBehavior.Editable = false;
            this.gvResult.OptionsDetail.ShowDetailTabs = false;
            this.gvResult.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvResult.OptionsSelection.MultiSelect = true;
            this.gvResult.OptionsView.ColumnAutoWidth = false;
            this.gvResult.OptionsView.ShowGroupPanel = false;
            this.gvResult.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gvResult_CustomDrawCell);
            // 
            // gcGroupSn
            // 
            this.gcGroupSn.Caption = "组号";
            this.gcGroupSn.Name = "gcGroupSn";
            this.gcGroupSn.Visible = true;
            this.gcGroupSn.VisibleIndex = 0;
            // 
            // gcGroupFileName
            // 
            this.gcGroupFileName.Caption = "文件名";
            this.gcGroupFileName.FieldName = "Name";
            this.gcGroupFileName.Name = "gcGroupFileName";
            this.gcGroupFileName.Visible = true;
            this.gcGroupFileName.VisibleIndex = 1;
            this.gcGroupFileName.Width = 741;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.groupControl1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainer1.Size = new System.Drawing.Size(912, 491);
            this.splitContainer1.SplitterDistance = 185;
            this.splitContainer1.TabIndex = 0;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridControlFile);
            this.groupControl1.Controls.Add(this.panel1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(912, 185);
            this.groupControl1.TabIndex = 2;
            this.groupControl1.Text = "未关联文件列表";
            // 
            // gridControlFile
            // 
            this.gridControlFile.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControlFile.ContextMenuStrip = this.ctxMenuStripFiles;
            this.gridControlFile.Location = new System.Drawing.Point(2, 24);
            this.gridControlFile.MainView = this.gvFile;
            this.gridControlFile.Name = "gridControlFile";
            this.gridControlFile.Size = new System.Drawing.Size(908, 129);
            this.gridControlFile.TabIndex = 0;
            this.gridControlFile.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvFile});
            // 
            // ctxMenuStripFiles
            // 
            this.ctxMenuStripFiles.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsMenuItemExportFiles});
            this.ctxMenuStripFiles.Name = "ctxMenuStripFiles";
            this.ctxMenuStripFiles.Size = new System.Drawing.Size(151, 26);
            // 
            // tsMenuItemExportFiles
            // 
            this.tsMenuItemExportFiles.Name = "tsMenuItemExportFiles";
            this.tsMenuItemExportFiles.Size = new System.Drawing.Size(150, 22);
            this.tsMenuItemExportFiles.Text = "导出到Excel...";
            this.tsMenuItemExportFiles.Click += new System.EventHandler(this.tsMenuItemExportFiles_Click);
            // 
            // gvFile
            // 
            this.gvFile.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvFile.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvFile.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Green;
            this.gvFile.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvFile.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvFile.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvFile.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvFile.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Green;
            this.gvFile.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvFile.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvFile.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvFile.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvFile.Appearance.SelectedRow.BorderColor = System.Drawing.Color.Green;
            this.gvFile.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvFile.Appearance.SelectedRow.Options.UseBorderColor = true;
            this.gvFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcFileID,
            this.gcFileName,
            this.gcProjectType,
            this.gcServiceType,
            this.gcCarrier});
            this.gvFile.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gvFile.GridControl = this.gridControlFile;
            this.gvFile.Name = "gvFile";
            this.gvFile.OptionsBehavior.Editable = false;
            this.gvFile.OptionsNavigation.AutoFocusNewRow = true;
            this.gvFile.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvFile.OptionsSelection.MultiSelect = true;
            this.gvFile.OptionsView.ColumnAutoWidth = false;
            this.gvFile.OptionsView.ShowGroupPanel = false;
            this.gvFile.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gvFile_RowStyle);
            this.gvFile.MouseDown += new System.Windows.Forms.MouseEventHandler(this.gvFile_MouseDown);
            this.gvFile.MouseUp += new System.Windows.Forms.MouseEventHandler(this.gvFile_MouseUp);
            this.gvFile.MouseMove += new System.Windows.Forms.MouseEventHandler(this.gvFile_MouseMove);
            // 
            // gcFileID
            // 
            this.gcFileID.Caption = "文件ID";
            this.gcFileID.FieldName = "ID";
            this.gcFileID.Name = "gcFileID";
            this.gcFileID.Visible = true;
            this.gcFileID.VisibleIndex = 0;
            this.gcFileID.Width = 135;
            // 
            // gcFileName
            // 
            this.gcFileName.Caption = "文件名";
            this.gcFileName.FieldName = "Name";
            this.gcFileName.Name = "gcFileName";
            this.gcFileName.Visible = true;
            this.gcFileName.VisibleIndex = 1;
            this.gcFileName.Width = 454;
            // 
            // gcProjectType
            // 
            this.gcProjectType.Caption = "项目类型";
            this.gcProjectType.FieldName = "ProjectDescription";
            this.gcProjectType.Name = "gcProjectType";
            this.gcProjectType.Visible = true;
            this.gcProjectType.VisibleIndex = 2;
            // 
            // gcServiceType
            // 
            this.gcServiceType.Caption = "业务类型";
            this.gcServiceType.FieldName = "ServiceTypeDescription";
            this.gcServiceType.Name = "gcServiceType";
            this.gcServiceType.Visible = true;
            this.gcServiceType.VisibleIndex = 3;
            // 
            // gcCarrier
            // 
            this.gcCarrier.Caption = "运营商";
            this.gcCarrier.FieldName = "CarrierTypeDescription";
            this.gcCarrier.Name = "gcCarrier";
            this.gcCarrier.Visible = true;
            this.gcCarrier.VisibleIndex = 4;
            // 
            // panel1
            // 
            this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.panel1.Controls.Add(this.simBtnDeleteFile);
            this.panel1.Controls.Add(this.simBtnCombine);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(2, 154);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(908, 29);
            this.panel1.TabIndex = 1;
            // 
            // simBtnDeleteFile
            // 
            this.simBtnDeleteFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simBtnDeleteFile.Location = new System.Drawing.Point(819, 2);
            this.simBtnDeleteFile.Name = "simBtnDeleteFile";
            this.simBtnDeleteFile.Size = new System.Drawing.Size(75, 23);
            this.simBtnDeleteFile.TabIndex = 0;
            this.simBtnDeleteFile.Text = "删除";
            this.simBtnDeleteFile.Click += new System.EventHandler(this.simBtnDeleteFile_Click);
            // 
            // simBtnCombine
            // 
            this.simBtnCombine.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simBtnCombine.Location = new System.Drawing.Point(724, 2);
            this.simBtnCombine.Name = "simBtnCombine";
            this.simBtnCombine.Size = new System.Drawing.Size(75, 23);
            this.simBtnCombine.TabIndex = 0;
            this.simBtnCombine.Text = "关联分析";
            this.simBtnCombine.Click += new System.EventHandler(this.simBtnCombine_Click);
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.gridControlResult);
            this.groupControl2.Controls.Add(this.panel2);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(912, 302);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "分析结果";
            // 
            // panel2
            // 
            this.panel2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.panel2.Controls.Add(this.simBtnDeleteGroup);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(2, 271);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(908, 29);
            this.panel2.TabIndex = 2;
            // 
            // simBtnDeleteGroup
            // 
            this.simBtnDeleteGroup.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simBtnDeleteGroup.Location = new System.Drawing.Point(819, 2);
            this.simBtnDeleteGroup.Name = "simBtnDeleteGroup";
            this.simBtnDeleteGroup.Size = new System.Drawing.Size(75, 23);
            this.simBtnDeleteGroup.TabIndex = 0;
            this.simBtnDeleteGroup.Text = "删除";
            this.simBtnDeleteGroup.Click += new System.EventHandler(this.simBtnDeleteGroup_Click);
            // 
            // CellOccupyResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(912, 491);
            this.Controls.Add(this.splitContainer1);
            this.Name = "CellOccupyResultForm";
            this.Text = "同车测试小区占用";
            ((System.ComponentModel.ISupportInitialize)(this.gvFileResult)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlResult)).EndInit();
            this.ctxMenuStripResult.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvResult)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlFile)).EndInit();
            this.ctxMenuStripFiles.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvFile)).EndInit();
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraGrid.GridControl gridControlFile;
        private DevExpress.XtraGrid.Views.Grid.GridView gvFile;
        private DevExpress.XtraGrid.Columns.GridColumn gcFileID;
        private DevExpress.XtraGrid.Columns.GridColumn gcFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gcProjectType;
        private DevExpress.XtraGrid.Columns.GridColumn gcServiceType;
        private DevExpress.XtraGrid.Columns.GridColumn gcCarrier;
        private DevExpress.XtraEditors.SimpleButton simBtnDeleteFile;
        private DevExpress.XtraEditors.SimpleButton simBtnCombine;
        private DevExpress.XtraGrid.GridControl gridControlResult;
        private DevExpress.XtraGrid.Views.Grid.GridView gvResult;
        private DevExpress.XtraGrid.Columns.GridColumn gcGroupSn;
        private System.Windows.Forms.Panel panel2;
        private DevExpress.XtraEditors.SimpleButton simBtnDeleteGroup;
        private DevExpress.XtraGrid.Columns.GridColumn gcGroupFileName;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gvFileResult;
        private DevExpress.XtraGrid.Columns.GridColumn gcPortFileID;
        private DevExpress.XtraGrid.Columns.GridColumn gcPortFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gcBothIdleSameCellPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gcHostIdleGuestOccupyDiffCellPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gcHostOccupyGuestIdleDiffCellPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gcSameCellDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gcDiffCellDiFreqDuration;
        private System.Windows.Forms.ContextMenuStrip ctxMenuStripFiles;
        private System.Windows.Forms.ToolStripMenuItem tsMenuItemExportFiles;
        private System.Windows.Forms.ContextMenuStrip ctxMenuStripResult;
        private System.Windows.Forms.ToolStripMenuItem tsMenuItemExportResult;
        private System.Windows.Forms.ToolStripMenuItem tsMenuItemExpand;
        private System.Windows.Forms.ToolStripMenuItem tsMenuItemCollapse;
        private DevExpress.XtraGrid.Columns.GridColumn gcDiffCellCoFreqDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gcBothIdleDiffCellPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gcHostIdleGuestOccupySameCellPercent;
        private DevExpress.XtraGrid.Columns.GridColumn gcHostOccupyGuestIdleSameCellPercent;
    }
}