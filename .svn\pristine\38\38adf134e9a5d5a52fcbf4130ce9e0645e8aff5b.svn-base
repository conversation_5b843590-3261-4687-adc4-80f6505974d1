﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.Util;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWrongDir_GScan : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        public int rxLevThreshold { get; set; } = -80;
        public int distanceThreshold { get; set; } = 100;
        public int badSampleRate { get; set; } = 5;
        readonly Dictionary<Cell, CellWrongDir> cellWrongDirDic = new Dictionary<Cell, CellWrongDir>();
        private readonly List<CellWrongDir> cellWrongDirList = new List<CellWrongDir>();

        private static ZTDIYCellWrongDir_GScan intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYCellWrongDir_GScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWrongDir_GScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWrongDir_GScan()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "覆盖方向异常_GSM扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15005, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        CellWrongDirSettingDlg conditionDlg = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new CellWrongDirSettingDlg();
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                int rxLev;
                int distance;
                int badSample;
                conditionDlg.getCondition(out rxLev, out distance, out badSample);
                rxLevThreshold = rxLev;
                distanceThreshold = distance;
                badSampleRate = badSample;
                return true;
            }
            return false;
        }

        protected override void FireShowFormAfterQuery()
        {
            ScanCellWrongDirForm scanCellWrongDirForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanCellWrongDirForm)) as ScanCellWrongDirForm;
            if (scanCellWrongDirForm == null || scanCellWrongDirForm.IsDisposed)
            {
                scanCellWrongDirForm = new ScanCellWrongDirForm(MainModel);
            }
            scanCellWrongDirForm.FillData(cellWrongDirList);
            scanCellWrongDirForm.Owner = MainModel.MainForm;
            scanCellWrongDirForm.Visible = true;
            scanCellWrongDirForm.BringToFront();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            for (int i = 0; i < 50; i++)
            {
                float? rxLev = (float?)tp["GSCAN_RxLev", i];
                if (rxLev == null || rxLev < rxLevThreshold || rxLev > -10)
                {
                    return;
                }
                int? bcch = (int?)tp["GSCAN_BCCH", i];
                int? bsic = (int?)tp["GSCAN_BSIC", i];
                Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
                if (cell != null)
                {
                    saveSample(cell, tp, (float)rxLev, isGoodDirection(cell, tp.Longitude, tp.Latitude));
                }
            }
        }

        private void saveSample(Cell cell, TestPoint tp, float rxLev, bool isGood)
        {
            if (cellWrongDirDic.ContainsKey(cell))
            {
                cellWrongDirDic[cell].AddTestPoint(tp, rxLev, isGood);
            }
            else
            {
                cellWrongDirDic[cell] = new CellWrongDir(cell, tp, rxLev, isGood);
            }
        }

        private bool isGoodDirection(Cell cell, double longitude, double latitude)
        {
            double dist = cell.GetDistance(longitude, latitude);
            return dist < distanceThreshold || MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, longitude, latitude, cell.Direction, 100);
        }

        protected override void getResultAfterQuery()
        {
            cellWrongDirList.Clear();
            foreach (Cell cell in cellWrongDirDic.Keys)
            {
                if (cellWrongDirDic[cell].BadSampleRate * 100 >= badSampleRate)
                {
                    cellWrongDirList.Add(cellWrongDirDic[cell]);
                }
            }
            cellWrongDirDic.Clear();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RxLevThreshold"] = rxLevThreshold;
                param["DistanceThreshold"] = distanceThreshold;
                param["BadSampleRate"] = badSampleRate;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RxLevThreshold"))
                {
                    rxLevThreshold = int.Parse(param["RxLevThreshold"].ToString());
                }
                if (param.ContainsKey("DistanceThreshold"))
                {
                    distanceThreshold = int.Parse(param["DistanceThreshold"].ToString());
                }
                if (param.ContainsKey("BadSampleRate"))
                {
                    badSampleRate = int.Parse(param["BadSampleRate"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CellWrongDirProperties_GSCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (CellWrongDir block in cellWrongDirList)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            cellWrongDirList.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int goodSampleCount = bgResult.GetImageValueInt();
                float badSampleScale = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("正常采样点数：");
                sb.Append(goodSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例(%)：");
                sb.Append(badSampleScale);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    public class CellWrongDir
    {
        public CellWrongDir(Cell cell, TestPoint tp, float rxLev, bool isGood)
        {
            this.cell = cell;
            AddTestPoint(tp, rxLev, isGood);
        }

        public void AddTestPoint(TestPoint tp, float rxLev, bool isGood)
        {
            if (isGood)
            {
                GoodSampleCount++;
            }
            else
            {
                if (!badSampleList.Contains(tp))
                {
                    rxLevMin = Math.Min(rxLevMin, (double)rxLev);
                    rxLevMax = Math.Max(rxLevMax, (double)rxLev);
                    rxLevSum += rxLev;
                    istime = Math.Min(tp.Time, istime);
                    ietime = Math.Max(tp.Time, ietime);
                    badSampleList.Add(tp);
                }
            }
        }

        private double rxLevMin = double.MaxValue;
        public double RxLevMin
        {
            get { return Math.Round(rxLevMin, 2); }
        }

        private double rxLevMax = double.MinValue;
        public double RxLevMax
        {
            get { return Math.Round(rxLevMax, 2); }
        }

        private double rxLevSum;
        public double RxLevMean
        {
            get { return Math.Round(rxLevSum / BadSampleCount, 2); }
        }

        public double LongitudeCell
        {
            get { return cell.Longitude; }
        }

        public double LatitudeCell
        {
            get { return cell.Latitude; }
        }

        public string CellName
        {
            get { return cell.Name; }
        }

        public int LAC
        {
            get { return cell.LAC; }
        }

        public int CI
        {
            get { return cell.CI; }
        }

        public int BCCH
        {
            get { return cell.BCCH; }
        }

        public int BSIC
        {
            get { return cell.BSIC; }
        }

        public int BadSampleCount
        {
            get { return badSampleList.Count; }
        }
        
        public int GoodSampleCount { get; set; }

        public double BadSampleRate
        {
            get { return BadSampleCount * 1.0 / (BadSampleCount + GoodSampleCount); }
        }

        public string BadSampleRateString
        {
            get { return Math.Round(BadSampleRate, 4) * 100 + "%"; }
        }

        public Cell cell { get; set; }
        public List<TestPoint> badSampleList { get; set; } = new List<TestPoint>();

        public int WrongDirMean
        { get; private set; }

        public int WrongDirMax
        { get; private set; }

        public int WrongDirMin
        { get; private set; }

        public int DirDiff
        {
            get;
            private set;
        }
        public short DirectionCfg
        {
            get { return cell.Direction; }
        }
        public void CalcWrongDir()
        {
            WrongDirMax = -1;
            WrongDirMin = 361;
            foreach (TestPoint tp in badSampleList)
            {
                int dir = MathFuncs.getAngleFromPointToPoint(this.cell.Longitude, this.cell.Latitude
                    , tp.Longitude, tp.Latitude);
                WrongDirMin = Math.Min(dir, WrongDirMin);
                WrongDirMax = Math.Max(dir, WrongDirMax);
            }
            int diffDir = WrongDirMax - WrongDirMin;
            if (diffDir > 180)
            {
                double meanDir = (360 - diffDir) / 2.0;
                if (meanDir > WrongDirMin)
                {
                    WrongDirMean = (int)(360 - (meanDir - WrongDirMin));
                }
                else
                {
                    WrongDirMean = (int)(WrongDirMin - meanDir);
                }
            }
            else
            {
                double halfDir = diffDir / 2.0;
                WrongDirMean = (short)(WrongDirMin + halfDir);
            }

            DirDiff = Math.Abs(WrongDirMean - this.cell.Direction);
            DirDiff = DirDiff > 180 ? 360 - DirDiff : DirDiff;
        }

        private int istime = int.MaxValue;
        private int ietime = int.MinValue;

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.GSM;
            bgResult.LAC = LAC;
            bgResult.CI = CI;
            bgResult.BCCH = BCCH;
            bgResult.BSIC = BSIC;
            bgResult.ISTime = istime;
            bgResult.IETime = ietime;
            bgResult.LongitudeMid = LongitudeCell;
            bgResult.LatitudeMid = LatitudeCell;
            bgResult.SampleCount = BadSampleCount;
            bgResult.RxLevMean = (float)RxLevMean;
            bgResult.RxLevMin = (float)RxLevMin;
            bgResult.RxLevMax = (float)RxLevMax;
            bgResult.AddImageValue(GoodSampleCount);
            bgResult.AddImageValue((float)Math.Round(BadSampleRate, 4) * 100);
            return bgResult;
        }
    }
}
