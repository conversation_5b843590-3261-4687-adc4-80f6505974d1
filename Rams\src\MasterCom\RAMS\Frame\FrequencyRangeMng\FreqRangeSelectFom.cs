﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Grid;
using System.Runtime.InteropServices;
using System.Drawing;
using MasterCom.RAMS.Net;
using System.Reflection;
using MasterCom.MControls;
using MasterCom.Grid;
using MasterCom.RAMS.Stat;
using System.Xml;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using DevExpress.XtraTab;
using MasterCom.RAMS.Frame;
using System.IO;
//using MasterCom.RAMS.Frame.FrequencyRangeMng;

namespace MasterCom.Util
{
    public partial class FreqRangeSelectFom : BaseFormStyle
    {
        //FreqRgeConfMng
        public FreqRangeSelectFom()
        {
            InitializeComponent();
        }
        private void FreqRangeSelectFom_Load(object sender, EventArgs e)
        {
            cboFreqRange.Items.Clear();
            cboFreqRange.Items.Add("全部");
            cboFreqRange.Items.Add("A段：36200-36349");
            cboFreqRange.Items.Add("D段：37750-38249");
            cboFreqRange.Items.Add("F段：38250-38649");
            cboFreqRange.Items.Add("E段：38650-39649");
            cboFreqRange.Text = "全部";
        }

        private void cboFreqRange_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cboFreqRange.Text == "全部")
            {
                this.numFreq.Enabled = false;
                this.cbxFreq.Enabled = false;
            }
            else
            {
                if (cboFreqRange.Text.IndexOf("D段") >= 0)
                {
                    numFreq.Minimum = 37750;
                    numFreq.Maximum = 38249;
                }
                else if (cboFreqRange.Text.IndexOf("F段") >= 0)
                {
                    numFreq.Minimum = 38250;
                    numFreq.Maximum = 38649;
                }
                else if (cboFreqRange.Text.IndexOf("A段") >= 0)
                {
                    numFreq.Minimum = 36200;
                    numFreq.Maximum = 36349;
                }
                else if (cboFreqRange.Text.IndexOf("E段") >= 0)
                {
                    numFreq.Minimum = 38650;
                    numFreq.Maximum = 39649;
                }
                cbxFreq.Enabled = true;
                numFreq.Enabled = cbxFreq.Checked;
            }
        }


        private List<int> selectedLst = new List<int>();
        public List<int> resultLst
        {
            get
            {
                return selectedLst;
            }
        }
        private void btnConfu_Click(object sender, EventArgs e)
        {
            selectedLst = new List<int>();
            if (cbxFreq.Enabled && cbxFreq.Checked)
            {
                selectedLst.Add((int)numFreq.Value);
            }
            else
            {
                if (cboFreqRange.Text.Trim() != "全部")
                {
                    addSelectedLst();
                }
            }
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.Close();
        }

        private void addSelectedLst()
        {
            if (cboFreqRange.Text.IndexOf("D段") >= 0)
            {
                //result.Add("FreqRangS", 37750);
                //result.Add("FreqRangE", 38249);
                for (int i = 37750; i < 38250; i++)
                {
                    selectedLst.Add(i);
                }
            }
            else if (cboFreqRange.Text.IndexOf("F段") >= 0)
            {
                //result.Add("FreqRangS", 38250);
                //result.Add("FreqRangE", 38649);
                for (int i = 38250; i < 38650; i++)
                {
                    selectedLst.Add(i);
                }
            }
            else if (cboFreqRange.Text.IndexOf("A段") >= 0)
            {
                //result.Add("FreqRangS", 38250);
                //result.Add("FreqRangE", 38649);
                for (int i = 36200; i < 36350; i++)
                {
                    selectedLst.Add(i);
                }
            }
            else if (cboFreqRange.Text.IndexOf("E段") >= 0)
            {
                //result.Add("FreqRangS", 38250);
                //result.Add("FreqRangE", 38649);
                for (int i = 38650; i < 39650; i++)
                {
                    selectedLst.Add(i);
                }
            }
        }

        private void cbxSelect_CheckedChanged(object sender, EventArgs e)
        {
            if (cbxFreq.Checked)
            {
                this.numFreq.Enabled = true;
            }
            else
            {
                this.numFreq.Enabled = false;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.Close();
        }
    }
}
