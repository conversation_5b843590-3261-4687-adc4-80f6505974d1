﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.Problem
{
    partial class ProblemOptionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.txtIdxNameCT = new System.Windows.Forms.TextBox();
            this.txtWeakCvrExpCT = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.txtIdxNameCU = new System.Windows.Forms.TextBox();
            this.txtWeakCvrExpCU = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.txtIdxNameCM = new System.Windows.Forms.TextBox();
            this.txtWeakCvrExpCM = new System.Windows.Forms.TextBox();
            this.cmbWeakCvr = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.numWeakCvr = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label10 = new System.Windows.Forms.Label();
            this.txtQualName = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.cmbPoorQual = new System.Windows.Forms.ComboBox();
            this.numPoorQual = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.txtPoorQualExp = new System.Windows.Forms.TextBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.gridCtrlEvt = new DevExpress.XtraGrid.GridControl();
            this.gvEvt = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnEmptyEvent = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveEvent = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddEvent = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvr)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorQual)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlEvt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvEvt)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox6);
            this.groupBox1.Controls.Add(this.groupBox5);
            this.groupBox1.Controls.Add(this.groupBox4);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(479, 276);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "弱覆盖问题点(双击文本框可编辑公式)";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.label7);
            this.groupBox6.Controls.Add(this.txtIdxNameCT);
            this.groupBox6.Controls.Add(this.txtWeakCvrExpCT);
            this.groupBox6.Controls.Add(this.label6);
            this.groupBox6.Location = new System.Drawing.Point(8, 188);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(463, 78);
            this.groupBox6.TabIndex = 6;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "电信";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(29, 23);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 1;
            this.label7.Text = "指标名称：";
            // 
            // txtIdxNameCT
            // 
            this.txtIdxNameCT.Location = new System.Drawing.Point(102, 20);
            this.txtIdxNameCT.Name = "txtIdxNameCT";
            this.txtIdxNameCT.Size = new System.Drawing.Size(222, 21);
            this.txtIdxNameCT.TabIndex = 5;
            // 
            // txtWeakCvrExpCT
            // 
            this.txtWeakCvrExpCT.Location = new System.Drawing.Point(102, 47);
            this.txtWeakCvrExpCT.Name = "txtWeakCvrExpCT";
            this.txtWeakCvrExpCT.ReadOnly = true;
            this.txtWeakCvrExpCT.Size = new System.Drawing.Size(223, 21);
            this.txtWeakCvrExpCT.TabIndex = 4;
            this.toolTip.SetToolTip(this.txtWeakCvrExpCT, "双击编辑公式");
            this.txtWeakCvrExpCT.DoubleClick += new System.EventHandler(this.txtExp_DoubleClick);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(5, 50);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 1;
            this.label6.Text = "电信覆盖指标：";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.label3);
            this.groupBox5.Controls.Add(this.txtIdxNameCU);
            this.groupBox5.Controls.Add(this.txtWeakCvrExpCU);
            this.groupBox5.Controls.Add(this.label5);
            this.groupBox5.Location = new System.Drawing.Point(8, 104);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(460, 78);
            this.groupBox5.TabIndex = 6;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "联通覆盖指标";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(29, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 1;
            this.label3.Text = "指标名称：";
            // 
            // txtIdxNameCU
            // 
            this.txtIdxNameCU.Location = new System.Drawing.Point(102, 20);
            this.txtIdxNameCU.Name = "txtIdxNameCU";
            this.txtIdxNameCU.Size = new System.Drawing.Size(222, 21);
            this.txtIdxNameCU.TabIndex = 5;
            // 
            // txtWeakCvrExpCU
            // 
            this.txtWeakCvrExpCU.Location = new System.Drawing.Point(102, 47);
            this.txtWeakCvrExpCU.Name = "txtWeakCvrExpCU";
            this.txtWeakCvrExpCU.ReadOnly = true;
            this.txtWeakCvrExpCU.Size = new System.Drawing.Size(223, 21);
            this.txtWeakCvrExpCU.TabIndex = 3;
            this.toolTip.SetToolTip(this.txtWeakCvrExpCU, "双击编辑公式");
            this.txtWeakCvrExpCU.DoubleClick += new System.EventHandler(this.txtExp_DoubleClick);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(5, 50);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 1;
            this.label5.Text = "联通覆盖指标：";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label8);
            this.groupBox4.Controls.Add(this.label2);
            this.groupBox4.Controls.Add(this.txtIdxNameCM);
            this.groupBox4.Controls.Add(this.txtWeakCvrExpCM);
            this.groupBox4.Controls.Add(this.cmbWeakCvr);
            this.groupBox4.Controls.Add(this.label4);
            this.groupBox4.Controls.Add(this.numWeakCvr);
            this.groupBox4.Location = new System.Drawing.Point(8, 20);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(462, 78);
            this.groupBox4.TabIndex = 6;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "移动覆盖指标";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.ForeColor = System.Drawing.Color.Red;
            this.label8.Location = new System.Drawing.Point(330, 49);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(125, 12);
            this.label8.TabIndex = 1;
            this.label8.Text = "符合此条件视为弱覆盖";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(29, 23);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "指标名称：";
            // 
            // txtIdxNameCM
            // 
            this.txtIdxNameCM.Location = new System.Drawing.Point(102, 20);
            this.txtIdxNameCM.Name = "txtIdxNameCM";
            this.txtIdxNameCM.Size = new System.Drawing.Size(222, 21);
            this.txtIdxNameCM.TabIndex = 5;
            // 
            // txtWeakCvrExpCM
            // 
            this.txtWeakCvrExpCM.Location = new System.Drawing.Point(102, 46);
            this.txtWeakCvrExpCM.Name = "txtWeakCvrExpCM";
            this.txtWeakCvrExpCM.ReadOnly = true;
            this.txtWeakCvrExpCM.Size = new System.Drawing.Size(100, 21);
            this.txtWeakCvrExpCM.TabIndex = 0;
            this.toolTip.SetToolTip(this.txtWeakCvrExpCM, "双击编辑公式");
            this.txtWeakCvrExpCM.DoubleClick += new System.EventHandler(this.txtExp_DoubleClick);
            // 
            // cmbWeakCvr
            // 
            this.cmbWeakCvr.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbWeakCvr.FormattingEnabled = true;
            this.cmbWeakCvr.Items.AddRange(new object[] {
            "<",
            "≤",
            ">",
            "≥"});
            this.cmbWeakCvr.Location = new System.Drawing.Point(209, 45);
            this.cmbWeakCvr.Name = "cmbWeakCvr";
            this.cmbWeakCvr.Size = new System.Drawing.Size(34, 22);
            this.cmbWeakCvr.TabIndex = 1;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(5, 49);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 1;
            this.label4.Text = "移动覆盖指标：";
            // 
            // numWeakCvr
            // 
            this.numWeakCvr.DecimalPlaces = 2;
            this.numWeakCvr.Location = new System.Drawing.Point(249, 46);
            this.numWeakCvr.Name = "numWeakCvr";
            this.numWeakCvr.Size = new System.Drawing.Size(75, 21);
            this.numWeakCvr.TabIndex = 2;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.txtQualName);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.cmbPoorQual);
            this.groupBox2.Controls.Add(this.numPoorQual);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.txtPoorQualExp);
            this.groupBox2.Location = new System.Drawing.Point(12, 294);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(479, 78);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "质差问题点(双击文本框可编辑公式)";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(37, 23);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(65, 12);
            this.label10.TabIndex = 6;
            this.label10.Text = "指标名称：";
            // 
            // txtQualName
            // 
            this.txtQualName.Location = new System.Drawing.Point(110, 20);
            this.txtQualName.Name = "txtQualName";
            this.txtQualName.Size = new System.Drawing.Size(222, 21);
            this.txtQualName.TabIndex = 7;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.ForeColor = System.Drawing.Color.Red;
            this.label9.Location = new System.Drawing.Point(339, 48);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(113, 12);
            this.label9.TabIndex = 1;
            this.label9.Text = "符合此条件视为质差";
            // 
            // cmbPoorQual
            // 
            this.cmbPoorQual.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbPoorQual.FormattingEnabled = true;
            this.cmbPoorQual.Items.AddRange(new object[] {
            "<",
            "≤",
            ">",
            "≥"});
            this.cmbPoorQual.Location = new System.Drawing.Point(216, 44);
            this.cmbPoorQual.Name = "cmbPoorQual";
            this.cmbPoorQual.Size = new System.Drawing.Size(34, 22);
            this.cmbPoorQual.TabIndex = 1;
            // 
            // numPoorQual
            // 
            this.numPoorQual.DecimalPlaces = 2;
            this.numPoorQual.Location = new System.Drawing.Point(258, 44);
            this.numPoorQual.Name = "numPoorQual";
            this.numPoorQual.Size = new System.Drawing.Size(75, 21);
            this.numPoorQual.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(61, 48);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "质差：";
            // 
            // txtPoorQualExp
            // 
            this.txtPoorQualExp.Location = new System.Drawing.Point(110, 45);
            this.txtPoorQualExp.Name = "txtPoorQualExp";
            this.txtPoorQualExp.ReadOnly = true;
            this.txtPoorQualExp.Size = new System.Drawing.Size(100, 21);
            this.txtPoorQualExp.TabIndex = 0;
            this.toolTip.SetToolTip(this.txtPoorQualExp, "双击编辑公式");
            this.txtPoorQualExp.DoubleClick += new System.EventHandler(this.txtExp_DoubleClick);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.gridCtrlEvt);
            this.groupBox3.Controls.Add(this.btnEmptyEvent);
            this.groupBox3.Controls.Add(this.btnRemoveEvent);
            this.groupBox3.Controls.Add(this.btnAddEvent);
            this.groupBox3.Location = new System.Drawing.Point(12, 378);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(479, 197);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "异常事件";
            // 
            // gridCtrlEvt
            // 
            this.gridCtrlEvt.Location = new System.Drawing.Point(10, 20);
            this.gridCtrlEvt.MainView = this.gvEvt;
            this.gridCtrlEvt.Name = "gridCtrlEvt";
            this.gridCtrlEvt.ShowOnlyPredefinedDetails = true;
            this.gridCtrlEvt.Size = new System.Drawing.Size(379, 171);
            this.gridCtrlEvt.TabIndex = 3;
            this.gridCtrlEvt.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvEvt});
            // 
            // gvEvt
            // 
            this.gvEvt.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gvEvt.GridControl = this.gridCtrlEvt;
            this.gvEvt.Name = "gvEvt";
            this.gvEvt.OptionsBehavior.Editable = false;
            this.gvEvt.OptionsSelection.MultiSelect = true;
            this.gvEvt.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "事件名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // btnEmptyEvent
            // 
            this.btnEmptyEvent.Location = new System.Drawing.Point(395, 167);
            this.btnEmptyEvent.Name = "btnEmptyEvent";
            this.btnEmptyEvent.Size = new System.Drawing.Size(75, 24);
            this.btnEmptyEvent.TabIndex = 2;
            this.btnEmptyEvent.Text = "清空";
            this.btnEmptyEvent.Click += new System.EventHandler(this.btnEmptyEvent_Click);
            // 
            // btnRemoveEvent
            // 
            this.btnRemoveEvent.Location = new System.Drawing.Point(395, 104);
            this.btnRemoveEvent.Name = "btnRemoveEvent";
            this.btnRemoveEvent.Size = new System.Drawing.Size(75, 24);
            this.btnRemoveEvent.TabIndex = 2;
            this.btnRemoveEvent.Text = "移除";
            this.btnRemoveEvent.Click += new System.EventHandler(this.btnRemoveEvent_Click);
            // 
            // btnAddEvent
            // 
            this.btnAddEvent.Location = new System.Drawing.Point(395, 41);
            this.btnAddEvent.Name = "btnAddEvent";
            this.btnAddEvent.Size = new System.Drawing.Size(75, 24);
            this.btnAddEvent.TabIndex = 1;
            this.btnAddEvent.Text = "添加";
            this.btnAddEvent.Click += new System.EventHandler(this.btnAddEvent_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(326, 592);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 24);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(407, 592);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 24);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            // 
            // ProblemOptionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(502, 627);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Name = "ProblemOptionDlg";
            this.Text = "问题点定义设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvr)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorQual)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlEvt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvEvt)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox txtWeakCvrExpCM;
        private System.Windows.Forms.NumericUpDown numWeakCvr;
        private System.Windows.Forms.TextBox txtWeakCvrExpCU;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtWeakCvrExpCT;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.NumericUpDown numPoorQual;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtPoorQualExp;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.ComboBox cmbWeakCvr;
        private System.Windows.Forms.ComboBox cmbPoorQual;
        private DevExpress.XtraEditors.SimpleButton btnRemoveEvent;
        private DevExpress.XtraEditors.SimpleButton btnAddEvent;
        private DevExpress.XtraGrid.GridControl gridCtrlEvt;
        private DevExpress.XtraGrid.Views.Grid.GridView gvEvt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.SimpleButton btnEmptyEvent;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox txtIdxNameCT;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox txtIdxNameCU;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtIdxNameCM;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox txtQualName;
    }
}