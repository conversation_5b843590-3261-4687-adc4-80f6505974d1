﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYQuerySampleByRegion_TD_DPCH : DIYSampleByRegion
    {
        public ZTDIYQuerySampleByRegion_TD_DPCH(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "指定TD_DPCH频点查询(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13010, this.Name);
        }
        
        protected override bool isValidTestPoint(TestPoint tp)
        {  
            try
            {
                if (validTestPoint(tp))
                {
                    return base.isValidTestPoint(tp);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool validTestPoint(TestPoint tp)
        {
            try
            {
                if (tp is TDTestPointDetail)
                {
                    int? dpch = (int?)tp["TD_DPCH_UARFCN"];
                    if (dpch != null)
                    {
                        return curSelDPCH_UARFCNDic.ContainsKey((int)dpch);
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private readonly Dictionary<int, bool> curSelDPCH_UARFCNDic = new Dictionary<int, bool>();
        protected override bool getConditionBeforeQuery()
        {
            TextInputBox tbxInput = new TextInputBox("指定频点", "请输入频点(多个用,隔开)", "");
            if (DialogResult.OK != tbxInput.ShowDialog())
            {
                return false;
            }
            curSelDPCH_UARFCNDic.Clear();
            string uarfcnStr = tbxInput.TextInput.Trim();
            string[] uarfcnVec = uarfcnStr.Split(',');
            foreach (string s in uarfcnVec)
            {
                int vvv;
                if (int.TryParse(s, out vvv))
                {
                    curSelDPCH_UARFCNDic[vvv] = true;
                }
            }
            if (curSelDPCH_UARFCNDic.Count == 0)
            {
                MessageBox.Show("请指定频点！");
                return false;
            }
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_DPCH_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("themeName", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
    }
}
