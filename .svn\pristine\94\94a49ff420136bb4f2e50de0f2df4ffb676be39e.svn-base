﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class NopServer
    {
        public NopServer()
        {
            this.Name = string.Empty;
        }

        public string Name
        { get; set; }

        public string Ip
        { get; set; }

        public int Port
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> retDic = new Dictionary<string, object>();
                retDic["Name"] = Name;
                retDic["Ip"] = Ip;
                retDic["Port"] = Port;
                return retDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                object obj = null;
                if (value.TryGetValue("Name", out obj) && obj != null)
                {
                    this.Name = obj.ToString();
                }

                if (value.TryGetValue("Ip", out obj) && obj != null)
                {
                    this.Ip = obj.ToString();
                }

                if (value.TryGetValue("Port", out obj) && obj != null)
                {
                    Port = int.Parse(obj.ToString());
                }
            }
        }

    }
}
