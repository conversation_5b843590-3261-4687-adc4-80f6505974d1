﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VolteWeakMosSettingDlg : BaseForm
    {
        public VolteWeakMosSettingDlg(double mos)
        {
            InitializeComponent();
            setCondition(mos);
        }

        private void setCondition(double mos)
        {
            numMaxValue.Value = (decimal)mos;
        }

        public double GetCondition()
        {
            double mos = (double)numMaxValue.Value;
            return mos;
        }

    }
}
