﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    public class TestPointBlockCompetitionLayer : CustomDrawLayer
    {
        public TestPointBlockCompetitionLayer(MapOperation mp, string name)
            : base(mp, name)
        {

        }

        public double BlockRadius
        {
            get;
            set;
        }
        
        public bool DrawHost { get; set; } = true;
        public bool DrawGuest { get; set; } = true;
        public bool DrawOverlap
        {
            get;
            set;
        }
        private readonly SolidBrush hostBrush = new SolidBrush(Color.FromArgb(200, Color.Blue));
        public Color HostColor
        {
            get { return hostBrush.Color; }
            set { hostBrush.Color = Color.FromArgb(200, value); }
        }
        private readonly SolidBrush guestBrush = new SolidBrush(Color.FromArgb(200, Color.Lime));
        public Color GuestColor
        {
            get { return guestBrush.Color; }
            set { guestBrush.Color = Color.FromArgb(200, value); }
        }
        private readonly SolidBrush overlapBrush = new SolidBrush(Color.FromArgb(200, Color.Red));
        public Color OverlapColor
        {
            get { return overlapBrush.Color; }
            set { overlapBrush.Color = Color.FromArgb(100, value); }
        }

        public List<TestPointBlock> HostBlocks
        {
            get;
            set;
        }
        public List<TestPointBlock> GuestBlocks
        {
            get;
            set;
        }
        public List<TestPointBlock> OverlapBlocks
        {
            get;
            set;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951;
            double llGap = (0.0001951 / 20) * BlockRadius;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);
            int rGap = (int)((scrPointSel2.X - scrPointSel.X) / 2) + 1;
            if (DrawHost && HostBlocks != null && HostBlocks.Count > 0)
            {
                drawBlocks(graphics, dRect, hostBrush, HostBlocks, rGap);
            }
            if (DrawGuest && GuestBlocks != null && GuestBlocks.Count > 0)
            {
                drawBlocks(graphics, dRect, guestBrush, GuestBlocks, rGap);
            }
            if (DrawOverlap && OverlapBlocks != null && OverlapBlocks.Count > 0)
            {
                drawBlocks(graphics, dRect, overlapBrush, OverlapBlocks, rGap);
            }
        }

        public TestPointBlock CurSelBlock
        {
            get;
            set;
        }

        private void drawBlocks(Graphics g, DbRect viewRect, SolidBrush brush, List<TestPointBlock> blocks, double radius)
        {
            if (blocks == null)
            {
                return;
            }
            float r = radius > 4 ? (float)radius : 4;
            foreach (TestPointBlock blk in blocks)
            {
                if (blk.Within(viewRect.x1, viewRect.y1, viewRect.x2, viewRect.y2))
                {
                    foreach (TestPoint evt in blk.TestPoints)
                    {
                        if (!evt.Within(viewRect.x1, viewRect.y1, viewRect.x2, viewRect.y2))
                        {
                            continue;
                        }
                        DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
                        PointF point;
                        this.Map.ToDisplay(dPoint, out point);
                        RectangleF rect = new RectangleF(point.X - r, point.Y - r, r * 2, r * 2);
                        GraphicsPath gp = new GraphicsPath();
                        gp.AddEllipse(rect);
                        g.FillPath(brush, gp);
                    }
                }
            }
        }

        #region ExportShapefile
        public void Export2Shp(string shpFile)
        {
            WaitBox.Show(Export2ShpInThread, shpFile);
        }

        private void Export2ShpInThread(object o)
        {
            Shapefile shp = null;
            try
            {
                shp = CreateShpfile();
                if (DrawHost && HostBlocks != null && HostBlocks.Count > 0)
                {
                    WaitBox.Text = "正在导出: Host";
                    ExportBlocks(shp, HostBlocks, "Host");
                }
                if (DrawGuest && GuestBlocks != null && GuestBlocks.Count > 0)
                {
                    WaitBox.Text = "正在导出: Guest";
                    ExportBlocks(shp, GuestBlocks, "Guest");
                }
                if (DrawOverlap && OverlapBlocks != null && OverlapBlocks.Count > 0)
                {
                    WaitBox.Text = "正在导出: Overlap";
                    ExportBlocks(shp, OverlapBlocks, "Overlap");
                }
                if (!shp.SaveAs(o as string, null))
                {
                    throw (new Exception(shp.get_ErrorMsg(shp.LastErrorCode)));
                }
                System.Windows.Forms.MessageBox.Show("导出图层成功！", "提示");
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show("导出失败: " + ex.Message, "错误");
            }
            finally
            {
                if (shp != null)
                {
                    shp.Close();
                }
                WaitBox.Close();
            }
        }

        private Shapefile CreateShpfile()
        {
            Shapefile shp = new Shapefile();
            shp.CreateNew("", ShpfileType.SHP_POLYGON);

            int fieldIndex = shp.NumFields;
            Field field = new Field();
            field.Name = "BlockType";
            field.Type = FieldType.STRING_FIELD;
            shp.EditInsertField(field, ref fieldIndex, null);

            fieldIndex = shp.NumFields;
            field = new Field();
            field.Name = "BlockID";
            field.Type = FieldType.INTEGER_FIELD;
            shp.EditInsertField(field, ref fieldIndex, null);

            return shp;
        }

        private void ExportBlocks(Shapefile shp, List<TestPointBlock> blocks, string blockType)
        {
            int shapeIndex = shp.NumShapes;
            double llGap = (0.0001951 / 20) * BlockRadius;

            int total = 0;
            foreach (TestPointBlock blk in blocks)
            {
                total += blk.TestPoints.Count;
            }

            int current = 0;
            for (int i = 0; i < blocks.Count; ++i)
            {
                TestPointBlock blk = blocks[i];
                foreach (TestPoint tp in blk.TestPoints)
                {
                    ++current;
                    if (current % 20 == 0)
                    {
                        WaitBox.ProgressPercent = current * 100 / total;
                    }

                    Shape shape = ShapeHelper.CreateCircleShape(tp.Longitude, tp.Latitude, llGap);
                    if (!shp.EditInsertShape(shape, ref shapeIndex))
                    {
                        continue;
                    }
                    shp.EditCellValue(0, shapeIndex, blockType);
                    shp.EditCellValue(1, shapeIndex, i);
                    ++shapeIndex;
                }
            }
        }
        #endregion
    }
}
