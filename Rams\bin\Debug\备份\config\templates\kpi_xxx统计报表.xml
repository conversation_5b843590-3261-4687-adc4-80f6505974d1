<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">统计报表</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]))/(evtIdCount[0]+value9[0]+value9[7]+value9[9]+value9[81])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">20</Item>
          <Item typeName="IList" key="Rcells">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">0</Item>
              <Item typeName="Single" key="MaxV">95</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">95</Item>
              <Item typeName="Single" key="MaxV">97</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">127</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">97</Item>
              <Item typeName="Single" key="MaxV">98</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">98</Item>
              <Item typeName="Single" key="MaxV">100</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
          </Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]))}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">21</Item>
          <Item typeName="IList" key="Rcells">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">0</Item>
              <Item typeName="Single" key="MaxV">0.5</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">255</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">0.5</Item>
              <Item typeName="Single" key="MaxV">1</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">1</Item>
              <Item typeName="Single" key="MaxV">2</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">2</Item>
              <Item typeName="Single" key="MaxV">4</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">128</Item>
              <Item typeName="Int32" key="ColorB">64</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">4</Item>
              <Item typeName="Single" key="MaxV">100</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
          </Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[0]+value9[0]+value9[7]+value9[9]+value9[81]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]))}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010807+Mx_5A010809)/(Mx_5A010806+Mx_5A010807+Mx_5A010808+Mx_5A010809+Mx_5A01080A) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">22</Item>
          <Item typeName="IList" key="Rcells">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">0</Item>
              <Item typeName="Single" key="MaxV">10</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">10</Item>
              <Item typeName="Single" key="MaxV">15</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">15</Item>
              <Item typeName="Single" key="MaxV">100</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
          </Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Mx_5A010D01/(Mx_5A010D01+Mx_5A010D03+Mx_5A010D06) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">23</Item>
          <Item typeName="IList" key="Rcells">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">0</Item>
              <Item typeName="Single" key="MaxV">40</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">40</Item>
              <Item typeName="Single" key="MaxV">60</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">60</Item>
              <Item typeName="Single" key="MaxV">100</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
          </Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">0</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Mx_5A011401/Mx_5A011402 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">25</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C)*(Mx_5A011401/Mx_5A011402) }％</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">24</Item>
          <Item typeName="IList" key="Rcells">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">0</Item>
              <Item typeName="Single" key="MaxV">85</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">85</Item>
              <Item typeName="Single" key="MaxV">90</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="MinV">90</Item>
              <Item typeName="Single" key="MaxV">100</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item key="desInfo" />
            </Item>
          </Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">26</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(evtIdCount[0]+value9[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]+value9[87]))/(evtIdCount[0]+value9[0])/(1-(evtIdCount[5]+evtIdCount[906]+value9[5]+value9[906]+evtIdCount[6]+evtIdCount[907]+value9[6]+value9[907])/((evtIdCount[0]+value9[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]+value9[87]))+(evtIdCount[1]+value9[1]-(evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[8]+value9[10]+value9[82])))) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108+Mx_640107)/Mx_640101}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(evtIdCount[0]+value9[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]+value9[87])) /(evtIdCount[0]+value9[0]) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(evtIdCount[5]+evtIdCount[906]+value9[5]+value9[906]+evtIdCount[6]+evtIdCount[907]+value9[6]+value9[907])/((evtIdCount[0]+value9[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]+value9[87]))+(evtIdCount[1]+value9[1]-(evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[8]+value9[10]+value9[82])))}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506)/Mx_5A01050C)*(Mx_5A011401/Mx_5A011402) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010B58+Mx_5A010B59+Mx_5A010B5A+Mx_5A010B5B)/Mx_5A010B5C }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[5]+evtIdCount[906]+value9[5]+value9[906]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[6]+value9[6]+evtIdCount[907]+value9[907])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+value9[104]+value9[110]+value9[188]))/(evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])*(evtIdCount[105]+evtIdCount[111]+evtIdCount[117]+evtIdCount[123]+value9[105]+value9[111]+value9[117]+value9[123])/(2*((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+value9[104]+value9[110]+value9[188]))) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Tx_640407+Tx_640408+Tx_640409+Tx_64040A+Tx_640417)/Tx_640401}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">11</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Tx_5C040311/Tx_5C04030A }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">12</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Tx_640407+Tx_640408+Tx_640409+Tx_64040A+Tx_640417+Tx_5C040311)/(Tx_640401+Tx_5C04030A)}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">13</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106])-(evtIdCount[104]+evtIdCount[110]+evtIdCount[188]+value9[104]+value9[110]+value9[188]))/(evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106]) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">14</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[100]+evtIdCount[106]+value9[100]+value9[106] }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">16</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505+Mx_5A010506+Mx_5A010507+Mx_5A010508 )}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">27</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo">
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">20</Item>
          <Item typeName="String" key="Name">接通率</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">219</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">219</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">21</Item>
          <Item typeName="String" key="Name">掉话率</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">213</Item>
          <Item typeName="Int32" key="BkColorG">213</Item>
          <Item typeName="Int32" key="BkColorB">213</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">6</Item>
          <Item typeName="String" key="Name">试呼次数</Item>
          <Item typeName="Int32" key="Width">72</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">7</Item>
          <Item typeName="String" key="Name">接通总次数</Item>
          <Item typeName="Int32" key="Width">70</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">9</Item>
          <Item typeName="String" key="Name">被叫掉话</Item>
          <Item typeName="Int32" key="Width">69</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">22</Item>
          <Item typeName="String" key="Name">半速率占比</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">23</Item>
          <Item typeName="String" key="Name">占比900</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">24</Item>
          <Item typeName="String" key="Name">语音质量</Item>
          <Item typeName="Int32" key="Width">78</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">25</Item>
          <Item typeName="String" key="Name">MOS2.8</Item>
          <Item typeName="Int32" key="Width">72</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">26</Item>
          <Item typeName="String" key="Name">Rxqualsub0-5</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">0</Item>
          <Item typeName="String" key="Name">全程呼叫成功率</Item>
          <Item typeName="Int32" key="Width">75</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">1</Item>
          <Item typeName="String" key="Name">覆盖率</Item>
          <Item typeName="Int32" key="Width">47</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">2</Item>
          <Item typeName="String" key="Name">接通率</Item>
          <Item typeName="Int32" key="Width">63</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">3</Item>
          <Item typeName="String" key="Name">掉话率</Item>
          <Item typeName="Int32" key="Width">59</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">4</Item>
          <Item typeName="String" key="Name">GSM语音质量</Item>
          <Item typeName="Int32" key="Width">74</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">5</Item>
          <Item typeName="String" key="Name">语音MOS质量</Item>
          <Item typeName="Int32" key="Width">76</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">8</Item>
          <Item typeName="String" key="Name">主叫掉话</Item>
          <Item typeName="Int32" key="Width">58</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">10</Item>
          <Item typeName="String" key="Name">全程呼叫成功率</Item>
          <Item typeName="Int32" key="Width">96</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">11</Item>
          <Item typeName="String" key="Name">G网覆盖率</Item>
          <Item typeName="Int32" key="Width">81</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">12</Item>
          <Item typeName="String" key="Name">T网覆盖率</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">13</Item>
          <Item typeName="String" key="Name">综合覆盖率</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">14</Item>
          <Item typeName="String" key="Name">接通率</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">15</Item>
          <Item typeName="String" key="Name">掉话率</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">16</Item>
          <Item typeName="String" key="Name">试呼次数</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">17</Item>
          <Item typeName="String" key="Name">接通次数</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">18</Item>
          <Item typeName="String" key="Name">主叫掉话</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">19</Item>
          <Item typeName="String" key="Name">被叫掉话</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="Column">27</Item>
          <Item typeName="String" key="Name">Rxqualsub0-4</Item>
          <Item typeName="Int32" key="Width">100</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">158</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>