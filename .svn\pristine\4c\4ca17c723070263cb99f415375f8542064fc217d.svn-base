﻿namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    partial class NRDominantAreaAnaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gcSence = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.btnExportSence = new System.Windows.Forms.ToolStripMenuItem();
            this.btnExportLogDetail = new System.Windows.Forms.ToolStripMenuItem();
            this.gvSence = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gcLogDetail = new DevExpress.XtraGrid.GridControl();
            this.gvLogDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcSence)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvSence)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcLogDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvLogDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(800, 450);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gcSence);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(793, 420);
            this.xtraTabPage1.Text = "场景指标分析结果";
            // 
            // gcSence
            // 
            this.gcSence.ContextMenuStrip = this.contextMenuStrip1;
            this.gcSence.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSence.Location = new System.Drawing.Point(0, 0);
            this.gcSence.MainView = this.gvSence;
            this.gcSence.Name = "gcSence";
            this.gcSence.Size = new System.Drawing.Size(793, 420);
            this.gcSence.TabIndex = 0;
            this.gcSence.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSence});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnExportSence,
            this.btnExportLogDetail});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(198, 48);
            // 
            // btnExportSence
            // 
            this.btnExportSence.Name = "btnExportSence";
            this.btnExportSence.Size = new System.Drawing.Size(197, 22);
            this.btnExportSence.Text = "导出场景指标分析结果";
            this.btnExportSence.Click += new System.EventHandler(this.btnExportSence_Click);
            // 
            // btnExportLogDetail
            // 
            this.btnExportLogDetail.Name = "btnExportLogDetail";
            this.btnExportLogDetail.Size = new System.Drawing.Size(197, 22);
            this.btnExportLogDetail.Text = "导出测试LOG核查明细";
            this.btnExportLogDetail.Click += new System.EventHandler(this.btnExportLogDetail_Click);
            // 
            // gvSence
            // 
            this.gvSence.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn34,
            this.gridColumn11,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26});
            this.gvSence.GridControl = this.gcSence;
            this.gvSence.Name = "gvSence";
            this.gvSence.OptionsBehavior.Editable = false;
            this.gvSence.OptionsDetail.ShowDetailTabs = false;
            this.gvSence.OptionsView.ColumnAutoWidth = false;
            this.gvSence.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "指标生成时间";
            this.gridColumn27.FieldName = "CreateDate";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 0;
            this.gridColumn27.Width = 101;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "地市";
            this.gridColumn28.FieldName = "DistrictName";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 1;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "区县";
            this.gridColumn29.FieldName = "CountryName";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 2;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "场景区域名称";
            this.gridColumn30.FieldName = "SceneName";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 3;
            this.gridColumn30.Width = 100;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "场景类型";
            this.gridColumn31.FieldName = "SceneTypeDesc";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "测试时间";
            this.gridColumn6.FieldName = "TestTime";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 81;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "测试总时间(s)";
            this.gridColumn7.FieldName = "TotalTestTime";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 97;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "测试总里程(Km)";
            this.gridColumn8.FieldName = "TotalTestDistance";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 110;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "5G 网络测试覆盖率(%)";
            this.gridColumn9.FieldName = "CoverRate";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 124;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "综合覆盖率(%)";
            this.gridColumn10.FieldName = "ComprehensiveCoverRate";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            this.gridColumn10.Width = 84;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "下载速率(Mbps)";
            this.gridColumn11.FieldName = "DLMacSpeed";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 11;
            this.gridColumn11.Width = 112;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "应用层上行平均速率(Mbps)";
            this.gridColumn32.FieldName = "FtpULSpeed";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 12;
            this.gridColumn32.Width = 141;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "应用层下行平均速率(Mbps)";
            this.gridColumn33.FieldName = "FtpDLSpeed";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 13;
            this.gridColumn33.Width = 141;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "SS-RSRP均值";
            this.gridColumn12.FieldName = "RSRP";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 14;
            this.gridColumn12.Width = 99;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "SS-SINR均值";
            this.gridColumn13.FieldName = "SINR";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 15;
            this.gridColumn13.Width = 91;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "区域内4G小区数";
            this.gridColumn14.FieldName = "LteCellTotalCount";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 16;
            this.gridColumn14.Width = 118;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "区域内5G小区数";
            this.gridColumn15.FieldName = "NrCellTotalCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 17;
            this.gridColumn15.Width = 111;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "4G占用小区数";
            this.gridColumn16.FieldName = "LteCellValidCount";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 18;
            this.gridColumn16.Width = 97;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "NR占用小区数";
            this.gridColumn17.FieldName = "NrCellValidCount";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 19;
            this.gridColumn17.Width = 97;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "优势区域内5G小区占用比例(%)";
            this.gridColumn18.FieldName = "NrCellRate";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 20;
            this.gridColumn18.Width = 172;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "道路总距离(公里)";
            this.gridColumn19.FieldName = "RoadTotalDistance";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 21;
            this.gridColumn19.Width = 123;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "渗透距离(公里)";
            this.gridColumn20.FieldName = "RoadDistance";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 22;
            this.gridColumn20.Width = 110;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "渗透率(%)";
            this.gridColumn21.FieldName = "RoadRate";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 23;
            this.gridColumn21.Width = 94;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "测试LOG占用服务小区/室内区域小区(%)";
            this.gridColumn22.FieldName = "NrIndoorCellRate";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 24;
            this.gridColumn22.Width = 101;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "上报测试LOG个数";
            this.gridColumn23.FieldName = "TotalFileCount";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 25;
            this.gridColumn23.Width = 119;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "有效测试LOG个数";
            this.gridColumn24.FieldName = "ValidFileCount";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 26;
            this.gridColumn24.Width = 119;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "涉及无效LOG数量";
            this.gridColumn25.FieldName = "InValidFileCount";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 27;
            this.gridColumn25.Width = 115;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "场景输出指标是否有效";
            this.gridColumn26.FieldName = "IsValid";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 28;
            this.gridColumn26.Width = 137;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gcLogDetail);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(793, 420);
            this.xtraTabPage2.Text = "测试LOG核查明细";
            // 
            // gcLogDetail
            // 
            this.gcLogDetail.ContextMenuStrip = this.contextMenuStrip1;
            this.gcLogDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcLogDetail.Location = new System.Drawing.Point(0, 0);
            this.gcLogDetail.MainView = this.gvLogDetail;
            this.gcLogDetail.Name = "gcLogDetail";
            this.gcLogDetail.Size = new System.Drawing.Size(793, 420);
            this.gcLogDetail.TabIndex = 1;
            this.gcLogDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvLogDetail});
            // 
            // gvLogDetail
            // 
            this.gvLogDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5});
            this.gvLogDetail.GridControl = this.gcLogDetail;
            this.gvLogDetail.Name = "gvLogDetail";
            this.gvLogDetail.OptionsBehavior.Editable = false;
            this.gvLogDetail.OptionsDetail.ShowDetailTabs = false;
            this.gvLogDetail.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "测试LOG名";
            this.gridColumn1.FieldName = "FileName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 154;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "LOG上传时间";
            this.gridColumn2.FieldName = "FileTime";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 138;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "LOG测试时间";
            this.gridColumn3.FieldName = "TestTime";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 139;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "LOG是否合格";
            this.gridColumn4.FieldName = "IsValidDesc";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 95;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "不合格原因";
            this.gridColumn5.FieldName = "InValidReason";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 246;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "业务态下行速率（Mbps）";
            this.gridColumn34.FieldName = "DLMacBSSpeed";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 10;
            // 
            // NRDominantAreaAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "NRDominantAreaAnaForm";
            this.Text = "5G优势区域分析";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcSence)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvSence)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcLogDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvLogDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gcSence;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSence;
        private DevExpress.XtraGrid.GridControl gcLogDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gvLogDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem btnExportSence;
        private System.Windows.Forms.ToolStripMenuItem btnExportLogDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
    }
}