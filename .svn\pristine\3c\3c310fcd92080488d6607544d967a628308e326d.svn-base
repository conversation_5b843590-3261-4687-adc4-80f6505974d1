using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.Util.UiEx
{
    class ToolStripCheckBox : ToolStripControlHost
    {
        public ToolStripCheckBox()
            : base(new CheckBox())
        {
            
            this.Size = new System.Drawing.Size(60, 21);
            ((CheckBox)this.Control).CheckStateChanged += new EventHandler(ToolStripCheckBox_CheckStateChanged);
        }

        public bool Checked
        {
            get { return ((CheckBox)this.Control).Checked; }
            set { ((CheckBox)this.Control).Checked = value; }
        }

        private void ToolStripCheckBox_CheckStateChanged(object send, EventArgs e)
        {
            if (CheckStateChanged != null)
                CheckStateChanged(this, new EventArgs());
        }

        public event EventHandler CheckStateChanged;
    }
}
