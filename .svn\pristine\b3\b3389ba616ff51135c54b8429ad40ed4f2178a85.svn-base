﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;
namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_AREA_COVER = 0xa0;
        public const byte REQTYPE_CELL_COVER = 0xb0;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_AREA_COVER_GRID_IDLE = 0xa0;
        public const byte RESTYPE_AREA_COVER_GRID_DEDICATED = 0xa1;
        public const byte RESTYPE_AREA_COVER_GRID_GPRS = 0xa2;
        public const byte RESTYPE_AREA_COVER_GRID_SCAN = 0xa3;
        public const byte RESTYPE_AREA_COVER_GRID_TD_AMR = 0xa4;
        public const byte RESTYPE_AREA_COVER_GRID_TD_PS = 0xa5;
        public const byte RESTYPE_AREA_COVER_GRID_TD_VP = 0xa6;
        public const byte RESTYPE_AREA_COVER_CDMA_V_GRID = 0xaf;
        public const byte RESTYPE_AREA_COVER_CDMA_D_GRID = 0xb0;
        public const byte RESTYPE_AREA_COVER_CDMA2000_D_GRID = 0xba;

        public const byte RESTYPE_AREA_COVER_GRID_WCDMA_AMR = 0xa7;
        public const byte RESTYPE_AREA_COVER_GRID_WCDMA_PS = 0xa8;
        public const byte RESTYPE_AREA_COVER_GRID_WCDMA_VP = 0xa9;
        public const byte RESTYPE_AREA_COVER_GRID_WCDMA_PSHS = 0xaa;

        public const byte RESTYPE_CELL_COVER_GRID_IDLE = 0xa0;
        public const byte RESTYPE_CELL_COVER_GRID_DEDICATED = 0xa1;
        public const byte RESTYPE_CELL_COVER_GRID_GPRS = 0xa2;
        public const byte RESTYPE_CELL_COVER_GRID_SCAN = 0xb3;
        public const byte RESTYPE_CELL_COVER_GRID_TD_AMR = 0xa4;
        public const byte RESTYPE_CELL_COVER_GRID_TD_PS = 0xa5;
        public const byte RESTYPE_CELL_COVER_GRID_TD_VP = 0xa6;
        public const byte RESTYPE_CELL_COVER_GRID_CDMA = 0xa8;
        public const byte RESTYPE_CELL_COVER_GRID_CDMA_DATA = 0xa9;
        public const byte RESTYPE_CELL_COVER_GRID_WCDMA_AMR = 0xad;
        public const byte RESTYPE_CELL_COVER_GRID_WCDMA_VP = 0xae;
        public const byte RESTYPE_CELL_COVER_GRID_WCDMA_PS = 0xb0;
        public const byte RESTYPE_CELL_COVER_GRID_WCDMA_PSHS = 0xb1;

    }

    public abstract class DIYGridQuery : QueryKPIStatBase
    {
        protected DIYGridQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override bool getConditionBeforeQuery()
        {
            MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
            return true;
        }

        protected override void fireShowResult()
        {
            MainModel.RefreshLegend();//更新图例
            MainModel.FireGridCoverQueried(this);
            FireQueryEndEvent(this);
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MapGridLayer gridLayer = this.MainModel.MainForm.GetMapForm().GetGridShowLayer();
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                List<GridColorModeItem> modeItems = new List<GridColorModeItem>();
                if (gridLayer.CurUsingColorModeList != null && gridLayer.CurUsingColorModeList.Count > 0)
                {
                    modeItems.AddRange(gridLayer.CurUsingColorModeList);
                }
                else
                {
                    modeItems.Add(gridLayer.CurUsingColorMode);
                }
                string statImgIDSet = getStatImgNeededTriadID(modeItems);
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                }
                else
                {
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        /// <summary>
        /// 查询某时间段内的数据
        /// </summary>
        /// <param name="period">当该参数为null时，视为按轮查询</param>
        /// <param name="clientProxy"></param>
        /// <param name="package"></param>
        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            System.Diagnostics.Debug.Assert(reservedParams.Length > 1, "缺少参数");
            //query kpi stat only
            isQueringEvent = false;
            preparePackageBasicContent(clientProxy.Package, period);
            preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams[0]);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy, reservedParams[1]);

            afterRecieveOnePeriodData();
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            if (paramSet == null || paramSet.Length < 1)
            {
                return "-1,-1,-1";
            }
            List<GridColorModeItem> modeSet = paramSet[0] as List<GridColorModeItem>;
            List<string> formulaSet = new List<string>();
            foreach (GridColorModeItem item in modeSet)
            {
                if (!string.IsNullOrEmpty(item.formula)
                    &&!formulaSet.Contains(item.formula))
                {
                    formulaSet.Add(item.formula);
                }
            }
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy,params object[] reservedParams)
        {
            GridMatrix<ColorUnit> colorMatrix = reservedParams[0] as GridMatrix<ColorUnit>;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(colorMatrix, package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(GridMatrix<ColorUnit> colorMatrix, Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            if (isValidStatImg(lng, lat))
            {
                fillStatData(package, curImgColumnDef, singleStatData);
                ColorUnit cu = new ColorUnit();
                cu.LTLng = lng;
                cu.LTLat = lat;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                cu = colorMatrix[rAt, cAt];
                if (cu == null)
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    colorMatrix[rAt, cAt] = cu;
                }
                cu.Status = 1;
                cu.DataHub.AddStatData(singleStatData, false);
            }
        }

        protected virtual bool isValidStatImg(double lng, double lat)
        {
            return true;
        }
    }
}
