﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MapWinGIS;
using AxMapWinGIS;
using System.Windows.Forms;
using System.Drawing;

namespace MasterCom.RAMS.FuncLayer
{
    public class RsvStreetShowLayer
    {
        readonly private MapForm mapForm;
        private MapOperation mop { get; set; }
        Shapefile shapeFile = null;
        public RsvStreetShowLayer(MapForm form, MapOperation op)
        {
            this.mapForm = form;
            this.mop = op;
        }
        internal void ApplyStreets(List<MapWinGIS.Shape> list)
        {
            AxMap mapMain = mapForm.GetMapFormControl();
            if (shapeFile == null)
            {
                shapeFile = new Shapefile();
                bool result = shapeFile.CreateNewWithShapeID("", ShpfileType.SHP_POLYLINE);
                if (!result)
                {
                    MessageBox.Show(shapeFile.get_ErrorMsg(shapeFile.LastErrorCode));
                    return;
                }
                int hnd =mapMain.AddLayer(shapeFile, true);
                mapMain.set_ShapeLayerDrawLine(hnd, true);
                mapMain.set_ShapeLayerLineWidth(hnd, 2);
                mapMain.set_ShapeLayerLineColor(hnd, (uint)ColorTranslator.ToOle(Color.Red));
                
            }
            shapeFile.EditClear();
            int shpIdx = 0;
            foreach (MapWinGIS.Shape street in list)
            {
                shapeFile.EditInsertShape(street, ref shpIdx);
                shpIdx++;
            }
        }
        internal void ClearData()
        {
            if (shapeFile != null)
            {
                shapeFile.EditClear();
            }
        }
    }
}
