﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCoverRoadLTE
    {
        public List<string> cellNames_opt { get; set; } = new List<string>();
        public string CellName_opt
        {
            get
            {
                StringBuilder cellNameStr = new StringBuilder();
                foreach (string name in cellNames_opt)
                {
                    if (cellNameStr.Length > 0)
                    {
                        cellNameStr.Append(" | ");
                    }
                    cellNameStr.Append(name);
                }
                return cellNameStr.ToString();
            }
        }

        public List<string> lacciList_opt { get; set; } = new List<string>();
        public string LACCIs_opt
        {
            get
            {
                StringBuilder laccis = new StringBuilder();
                foreach (string lacci in lacciList_opt)
                {
                    if (laccis.Length > 0)
                    {
                        laccis.Append(" | ");
                    }
                    laccis.Append(lacci);
                }
                return laccis.ToString();
            }
        }

        public List<string> cellNames_plan { get; set; } = new List<string>();
        public string CellName_plan
        {
            get
            {
                StringBuilder cellNameStr = new StringBuilder();
                foreach (string name in cellNames_plan)
                {
                    if (cellNameStr.Length > 0)
                    {
                        cellNameStr.Append(" | ");
                    }
                    cellNameStr.Append(name);
                }
                return cellNameStr.ToString();
            }
        }

        public List<string> lacciList_plan { get; set; } = new List<string>();
        public string LACCIs_plan
        {
            get
            {
                StringBuilder laccis = new StringBuilder();
                foreach (string lacci in lacciList_plan)
                {
                    if (laccis.Length > 0)
                    {
                        laccis.Append(" | ");
                    }
                    laccis.Append(lacci);
                }
                return laccis.ToString();
            }
        }
        public int SN
        {
            get;
            set;
        }
        public double Second
        {
            get
            {
                double sec = 0;
                if (testPoints.Count > 1)
                {
                    sec = (testPoints[testPoints.Count - 1].DateTime - testPoints[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }
        public string CityName { get; set; }

        public string StartTime
        {
            get
            {
                return testPoints[0].DateTime.ToString();
            }
        }

        public double Distance
        {

            get
            {
                return Math.Round(distance, 2);
            }
        }
        public double Duration
        {
            get;
            set;
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (testPoints.Count > 0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }
        public int FileID
        {
            get
            {
                int id = -1;
                if (testPoints.Count > 0)
                {
                    id = testPoints[0].FileID;
                }
                return id;
            }
        }
        
        private readonly List<LTECell> nbCellList = new List<LTECell>();

        public int IMaxNBCellCount
        {
            get { return nbCellList.Count; }
        }
        /// <summary>
        /// 最强小区名
        /// </summary>
        public string StrMaxRsrpCellName { get; set; } = "";

        private string strMaxNBCellMinDistince;
        /// <summary>
        /// 最强邻区与中心的距离
        /// </summary>
        public string StrMaxNBCellMinDistince
        {
            get 
            {
                if (strMaxNBCellMinDistince == null)
                {
                    return "-";
                }
                return strMaxNBCellMinDistince; 
            }
            set { strMaxNBCellMinDistince = value; }
        }


        private double distance = 0;
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }
        private float totalRsrp = 0;
        private float minRsrp = float.MaxValue;
        public float MinRsrp
        {
            get { return minRsrp; }
        }
        public float MaxRsrp
        {
            get { return maxRsrp; }
        }
        private float maxRsrp = float.MinValue;
        public float AvgRsrp
        {
            get { return (float)Math.Round(totalRsrp / testPoints.Count, 2); }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }
        
        public int WeakTestPointCount { get; set; }
        public double WeakPointPercent { get; set; }

        public int TestPointCount_Opt { get; set; }
        public float TestPointRate_Opt
        {
            get { return (float)Math.Round((float)100 * TestPointCount_Opt / (float)testPoints.Count, 2); }
        }
        
        public int TestPointCount_Plan { get; set; }
        public float TestPointRate_Plan
        {
            get { return (float)Math.Round((float)100 * TestPointCount_Plan / (float)testPoints.Count, 2); }
        }

        int validSINRCnt = 0;
        float minSINR = float.MaxValue;
        public float MinSINR
        {
            get { return minSINR; }
        }
        float maxSINR = float.MinValue;
        public float MaxSINR
        {
            get { return maxSINR; }
        }
        private float totalSINR = 0;
        public float AvgSINR
        {
            get
            {
                if (validSINRCnt == 0)
                {
                    return float.NaN;
                }
                return (float)Math.Round(totalSINR / validSINRCnt, 2);
            }
        }
        private double speedSum = 0;
        private double speedNum = 0;
        public float AvgSpeed
        {
            get
            {
                if (speedNum == 0)
                {
                    return float.NaN;
                }
                return (float)Math.Round(speedSum / speedNum, 2);
            }
        }

        private double ftpDownSum = 0;
        private double ftpDownNum = 0;
        private double ftpDown2MbNum = 0;
        public double AvgFtpDownSpeed
        {
            get
            {
                if (ftpDownNum == 0)
                {
                    return double.NaN;
                }
                return Math.Round(ftpDownSum / ftpDownNum, 2);
            }
        }
        public double? FtpDown2MbPer
        {
            get
            {
                if (ftpDownNum == 0)
                {
                    return null;
                }
                return Math.Round(100 * ftpDown2MbNum / ftpDownNum, 2);
            }
        }


        private double ftpUpSum = 0;
        private double ftpUpNum = 0;
        private double ftpUp512KbNum = 0;
        public double AvgFtpUpSpeed
        {
            get
            {
                if (ftpUpNum == 0)
                {
                    return double.NaN;
                }
                return Math.Round(ftpUpSum / ftpUpNum, 2);
            }
        }
        public double? FtpUp512KbPer
        {
            get
            {
                if (ftpUpNum == 0)
                {
                    return null;
                }
                return Math.Round(100 * ftpUp512KbNum / ftpUpNum, 2);
            }
        }
        #region nb rsrp
        int validNbRsrpCnt = 0;

        float minNbRsrp = float.MaxValue;
        public float MinNbRsrp
        {
            get
            {
                return minNbRsrp;
            }
        }

        float maxNbRsrp = float.MinValue;
        public float MaxNbRsrp
        {
            get
            {
                return maxNbRsrp;
            }
        }

        private float totalNbRsrp = 0;
        public float AvgNbRsrp
        {
            get
            {
                if (validNbRsrpCnt == 0)
                {
                    return float.MinValue;
                }
                return (float)Math.Round(totalNbRsrp / validNbRsrpCnt, 2);
            }
        }
        #endregion

        public List<WeakCoverRoadLTEAssocSCell> AssocSCells
        {
            get;
            set;
        }

        internal void Add(float? rsrp, float? nbMaxRsrp, float? sinr, int? tac, double distance, TestPoint testPoint, LTECell lteCell)
        {
            if (sinr != null)
            {
                validSINRCnt++;
                totalSINR += (float)sinr;
                minSINR = Math.Min((float)sinr, minSINR);
                maxSINR = Math.Max((float)sinr, maxSINR);
            }

            if (nbMaxRsrp != null)
            {
                validNbRsrpCnt++;
                totalNbRsrp += (float)nbMaxRsrp;
                minNbRsrp = Math.Min((float)nbMaxRsrp, minNbRsrp);
                maxNbRsrp = Math.Max((float)nbMaxRsrp, maxNbRsrp);
            }
         
            if (rsrp != null)
            {
                totalRsrp += (float)rsrp;
                minRsrp = Math.Min(minRsrp, (float)rsrp);
                maxRsrp = Math.Max(maxRsrp, (float)rsrp);
            }
            this.distance += distance;
            testPoints.Add(testPoint);

            if (lteCell != null && tac != null)
            {
                string lacci = tac.ToString() + "_" + lteCell.SCellID.ToString();
                if (!lacciList_plan.Contains(lacci))
                {
                    lacciList_plan.Add(lacci);
                }
                if (!cellNames_plan.Contains(lteCell.Name))
                {
                    cellNames_plan.Add(lteCell.Name);
                }
            }
        }

        public void AddSpeed(short? type, double? speed)
        {
            if (speed != null)
            {
                speedNum++;
                speedSum += (double)speed;

                if (type == 2)
                {
                    ftpDownNum++;
                    ftpDownSum += (double)speed;
                    if ((double)speed < 2)
                    {
                        ftpDown2MbNum++;
                    }
                }
                else if (type == 3)
                {
                    ftpUpNum++;
                    ftpUpSum += (double)speed;
                    if ((double)speed < 0.5)
                    {
                        ftpUp512KbNum++;
                    }
                }
            }
        }

        internal void JudeNbMaxRsrpCelll(Dictionary<float, LTECell> nbCell, TestPoint testPoint, string tpNCell_EARFCN, string tpNCell_PCI, int index)
        {
            foreach (float fRsrp in nbCell.Keys)
            {
                
                if (fRsrp >= maxNbRsrp)
                {
                    addNbCellList(nbCell, testPoint, tpNCell_EARFCN, tpNCell_PCI, index, fRsrp);
                }
            }
        }

        private void addNbCellList(Dictionary<float, LTECell> nbCell, TestPoint testPoint, string tpNCell_EARFCN, string tpNCell_PCI, int index, float fRsrp)
        {
            strMaxNBCellMinDistince = "";
            if (nbCell[fRsrp].Name != null && nbCell[fRsrp].Name != "")
            {
                StrMaxRsrpCellName = nbCell[fRsrp].Name;
                if (!nbCellList.Contains(nbCell[fRsrp]))
                {
                    nbCellList.Add(nbCell[fRsrp]);
                }
                strMaxNBCellMinDistince = MathFuncs.GetDistance(nbCell[fRsrp].Longitude, nbCell[fRsrp].Latitude, MidLng, MidLat).ToString("0.00");
            }
            else
            {
                if (testPoint[tpNCell_EARFCN, index] != null
                    && testPoint[tpNCell_PCI, index] != null)
                {
                    StrMaxRsrpCellName = (int?)testPoint[tpNCell_EARFCN, index] + "_" + (int?)(short?)testPoint[tpNCell_PCI, index];
                }
                nbCellList.Add(nbCell[fRsrp]);
            }
        }

        public string RoadName { get; set; } = string.Empty;
        public string MotorWay { get; set; } = "";
        public void SetMotorWay(int areaID, int areaTypeID)
        {
            MotorWay = AreaManager.GetInstance().GetAreaDesc(areaTypeID, areaID);    
        }    
        
        public string AreaName { get; set; } = string.Empty;
        public string GridName { get; set; } = string.Empty;
        public string AreaAgentName { get; set; } = string.Empty;

        public double MidLng
        {
            get
            {
                double lng = double.NaN;
                if (testPoints.Count > 0)
                {
                    lng = testPoints[testPoints.Count / 2].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (testPoints.Count > 0)
                {
                    lat = testPoints[testPoints.Count / 2].Latitude;
                }
                return lat;
            }
        }
        public void FindRoadName()
        {
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }

        public void FindAreaName()
        {
            string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(MidLng, MidLat);
            if (strAreaName != null)
            {
                if (AreaName == null || AreaName == "")
                {
                    AreaName = strAreaName;
                }
                else
                {
                    if (!AreaName.Contains(strAreaName) && strAreaName != "")
                    {
                        AreaName += "," + strAreaName;
                    }
                }
            }
        }

        public void FindGridName()
        {
            string strGridName = GISManager.GetInstance().GetGridDesc(MidLng, MidLat);
            if (strGridName != null)
            {
                if (GridName == null || GridName == "")
                {
                    GridName = strGridName;
                }
                else
                {
                    if (!GridName.Contains(strGridName) && strGridName != "")
                    {
                        GridName += "," + strGridName;
                    }
                }
            }
        }

        public void FindAgentName()
        {
            string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(MidLng, MidLat);
            if (strAreaAgentName != null)
            {
                if (AreaAgentName == null || AreaAgentName == "")
                {
                    AreaAgentName = strAreaAgentName;
                }
                else
                {
                    if (!AreaAgentName.Contains(strAreaAgentName) && strAreaAgentName != "")
                    {
                        AreaAgentName += "," + strAreaAgentName;
                    }
                }
            }
        }

        public virtual void MakeSummary()
        {
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = toBackgroundResult();

            bgResult.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
            bgResult.AddImageValue(bgResult.ProjectString);

            getImage(bgResult);
            return bgResult;
        }
        public BackgroundResult ConvertToBackgroundResult(string projectString)
        {
            BackgroundResult bgResult = toBackgroundResult();

            bgResult.ProjectString = projectString;
            bgResult.AddImageValue(bgResult.ProjectString);

            getImage(bgResult);
            return bgResult;
        }
        private BackgroundResult toBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.FileID = FileID;
            bgResult.FileName = FileName;
            bgResult.LongitudeStart = 0;
            bgResult.LatitudeStart = 0;
            bgResult.LongitudeEnd = 0;
            bgResult.LatitudeEnd = 0;
            bgResult.ISTime = 0;
            bgResult.IETime = 0;
            if (testPoints.Count > 0)
            {
                bgResult.LongitudeStart = testPoints[0].Longitude;
                bgResult.LatitudeStart = testPoints[0].Latitude;
                bgResult.LongitudeEnd = testPoints[testPoints.Count - 1].Longitude;
                bgResult.LatitudeEnd = testPoints[testPoints.Count - 1].Latitude;
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(testPoints[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(testPoints[testPoints.Count - 1].DateTime) / 1000);
            }
            bgResult.LongitudeMid = MidLng;
            bgResult.LatitudeMid = MidLat;
            bgResult.DistanceLast = Distance;
            bgResult.SampleCount = TestPointCount;
            bgResult.RxLevMean = AvgRsrp;
            bgResult.RxLevMin = MinRsrp;
            bgResult.RxLevMax = MaxRsrp;
            bgResult.RxQualMean = AvgSINR;
            bgResult.RxQualMin = MinSINR;
            bgResult.RxQualMax = MaxSINR;
            bgResult.RoadDesc = RoadName;
            bgResult.AreaDesc = AreaName;
            bgResult.GridDesc = GridName;
            bgResult.AreaAgentDesc = AreaAgentName;

            bgResult.AddImageValue(TestPointRate_Opt);
            bgResult.AddImageValue(LACCIs_opt);
            bgResult.AddImageValue(CellName_opt);
            bgResult.AddImageValue(TestPointRate_Plan);
            bgResult.AddImageValue(LACCIs_plan);
            bgResult.AddImageValue(CellName_plan);
            bgResult.AddImageValue(IMaxNBCellCount);
            bgResult.AddImageValue(StrMaxRsrpCellName);
            bgResult.AddImageValue(StrMaxNBCellMinDistince);
            bgResult.AddImageValue(MaxNbRsrp);
            bgResult.AddImageValue(MinNbRsrp);
            bgResult.AddImageValue(AvgNbRsrp);
            bgResult.AddImageValue(MaxSINR);
            bgResult.AddImageValue(MinSINR);
            bgResult.AddImageValue(AvgSINR);
            bgResult.AddImageValue(AvgSpeed);
            bgResult.AddImageValue((float)WeakPointPercent);

            return bgResult;
        }
        public List<BackgroundResult> ConvertToBackgroundResultList(WeakCoverRoadLTE item)
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            try
            {
                foreach (WeakCoverRoadLTEAssocSCell wRoadCell in item.AssocSCells)
                {
                    BackgroundResult bgResult = new BackgroundResult();
                    bgResult.FileID = FileID;
                    bgResult.FileName = FileName;
                    bgResult.LongitudeStart = 0;
                    bgResult.LatitudeStart = 0;
                    bgResult.LongitudeEnd = 0;
                    bgResult.LatitudeEnd = 0;
                    bgResult.ISTime = 0;
                    bgResult.IETime = 0;
                    if (testPoints.Count > 0)
                    {
                        bgResult.LongitudeStart = testPoints[0].Longitude;
                        bgResult.LatitudeStart = testPoints[0].Latitude;
                        bgResult.LongitudeEnd = testPoints[testPoints.Count - 1].Longitude;
                        bgResult.LatitudeEnd = testPoints[testPoints.Count - 1].Latitude;
                        bgResult.ISTime = (int)(JavaDate.GetMilliseconds(testPoints[0].DateTime) / 1000);
                        bgResult.IETime = (int)(JavaDate.GetMilliseconds(testPoints[testPoints.Count - 1].DateTime) / 1000);
                    }
                    bgResult.LongitudeMid = MidLng;
                    bgResult.LatitudeMid = MidLat;
                    bgResult.DistanceLast = Distance;
                    bgResult.SampleCount = TestPointCount;
                    bgResult.RxLevMean = AvgRsrp;
                    bgResult.RxLevMin = MinRsrp;
                    bgResult.RxLevMax = MaxRsrp;
                    bgResult.RxQualMean = AvgSINR;
                    bgResult.RxQualMin = MinSINR;
                    bgResult.RxQualMax = MaxSINR;
                    bgResult.RoadDesc = RoadName;
                    bgResult.AreaDesc = AreaName;
                    bgResult.GridDesc = GridName;
                    bgResult.AreaAgentDesc = AreaAgentName;
                    bgResult.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;

                    bgResult.AddImageValue(TestPointRate_Opt);
                    bgResult.AddImageValue(LACCIs_opt);
                    bgResult.AddImageValue(CellName_opt);
                    bgResult.AddImageValue(TestPointRate_Plan);
                    bgResult.AddImageValue(LACCIs_plan);
                    bgResult.AddImageValue(CellName_plan);
                    bgResult.AddImageValue(IMaxNBCellCount);
                    bgResult.AddImageValue(StrMaxRsrpCellName);
                    bgResult.AddImageValue(StrMaxNBCellMinDistince);
                    bgResult.AddImageValue(MaxNbRsrp);
                    bgResult.AddImageValue(MinNbRsrp);
                    bgResult.AddImageValue(AvgNbRsrp);
                    bgResult.AddImageValue(MaxSINR);
                    bgResult.AddImageValue(MinSINR);
                    bgResult.AddImageValue(AvgSINR);
                    bgResult.AddImageValue(AvgSpeed);
                    bgResult.AddImageValue((float)WeakPointPercent);

                    bgResult.AddImageValue(getValidData(wRoadCell.MainCellName));
                    bgResult.AddImageValue(getValidData(wRoadCell.TopNbName));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsNbRelationCorrect));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsNbCellAlarm));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsNbOcnCorrect));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsNbOcsCorrect));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsExistRadiusSite));
                    bgResult.AddImageValue(getValidData(wRoadCell.AlarmSiteName));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsPaCorrect));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsPbCorrect));
                    bgResult.AddImageValue(getValidData(wRoadCell.IsRefSignalCorrect));
                    bgResult.AddImageValue(getValidData(wRoadCell.DistanceCenter));
                    bgResult.AddImageValue(getValidData(wRoadCell.AngleCenter));
                    bgResult.AddImageValue(getValidData(wRoadCell.Direction));
                    bgResult.AddImageValue(getValidData(wRoadCell.Downward));
                    bgResult.AddImageValue(getValidData(wRoadCell.Altitude));
                    bgResult.AddImageValue(bgResult.ProjectString);

                    bgResultList.Add(bgResult);
                }
            }
            catch
            {
                //continue
            }
            return bgResultList;
        }

        private string getValidData(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return "";
            }
            return value;
        }

        protected virtual void getImage(BackgroundResult bgResult)
        {
        }

    }

    public class WeakCoverRoadLTEAssocSCell
    {
        public WeakCoverRoadLTEAssocSCell(LTECell mainCell)
        {
            this.MainCell = mainCell;
            this.MainModel = MainModel.GetInstance();
            this.TestPoints = new List<TestPoint>();
        }

        public List<LTEBTS> BtsList
        {
            get;
            set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            private set;
        }

        public List<AlarmDrawItem> AlarmItems
        {
            get { return this.alarmItems; }
        }

        public LTECell MainCell
        {
            get;
            private set;
        }

        public LTECell TopNbCell
        {
            get;
            private set;
        }

        public WeakCoverRoadLTEAssocNCell TopNbCellEx
        {
            get;
            private set;
        }

        public float TopNbRsrp
        {
            get;
            private set;
        }

        public string MainCellName
        {
            get { return this.MainCell.Name; }
        }

        public string Direction
        {
            get { return MainCell == null || MainCell.Direction == 999 ? "" : MainCell.Direction.ToString(); }
        }

        public string Altitude
        {
            get { return MainCell == null || MainCell.Altitude == 999 ? "" : MainCell.Altitude.ToString(); }
        }

        public string Downward
        {
            get { return MainCell == null || MainCell.Downward == 999 ? "" : MainCell.Downward.ToString(); }
        }

        public string TopNbName
        {
            get { return TopNbCell == null ? null : TopNbCell.Name; }
        }

        public string IsExistRadiusSite
        {
            get;
            private set;
        }

        public string AlarmSiteName
        {
            get;
            private set;
        }

        public string IsPaCorrect
        {
            get;
            private set;
        }

        public string IsPbCorrect
        {
            get;
            private set;
        }

        public string IsRefSignalCorrect
        {
            get;
            private set;
        }

        public string IsNbRelationCorrect
        {
            get;
            private set;
        }

        public string IsNbCellAlarm
        {
            get;
            private set;
        }

        public string IsOutsideCorrect
        {
            get;
            private set;
        }

        public string IsNbOcnCorrect
        {
            get;
            private set;
        }

        public string IsNbOcsCorrect
        {
            get;
            private set;
        }

        public string DistanceCenter
        {
            get;
            private set;
        }

        public string AngleCenter
        {
            get;
            private set;
        }

        public void AddNbCell(LTECell nbCell, TestPoint tp, float rsrp)
        {
            if (!nCellDic.ContainsKey(nbCell))
            {
                nCellDic.Add(nbCell, new WeakCoverRoadLTEAssocNCell(nbCell));
            }
            nCellDic[nbCell].AddTestPoint(tp, rsrp);
        }

        public void GetResult(WeakCoverRoadLTEAssocCondtion cond)
        {
            this.cond = cond;
            CellArgumentManager.Instance.FireLoadCellArguments(MainModel.DistrictID, false);
            AlarmRecordManager.Instance.FireLoadAlarmRecords(MainModel.DistrictID, false);

            FindNbCell();

            TestPoint centPt = TestPoints[TestPoints.Count / 2];
            double dis = MathFuncs.GetDistance(this.MainCell.Longitude, this.MainCell.Latitude, centPt.Longitude, centPt.Latitude);
            DistanceCenter = Math.Round(dis, 2).ToString();
            int angle = MathFuncs.getAngleFromPointToPoint(this.MainCell.Longitude, this.MainCell.Latitude, centPt.Longitude, centPt.Latitude);
            AngleCenter = this.MainCell.Direction == 0 || this.MainCell.Direction == 999 ?
                null : Math.Abs(this.MainCell.Direction - angle).ToString();

            BtsList = null;
            radiusBtsList.Clear();
            nCellDic.Clear();
        }

        private void FindNbCell()
        {
            foreach (WeakCoverRoadLTEAssocNCell nCell in nCellDic.Values)
            {
                if (nCell.TopRsrp < this.cond.NbRsrpThreshold)
                {
                    continue;
                }
                if (TopNbCell == null || TopNbRsrp < nCell.TopRsrp)
                {
                    TopNbCell = nCell.NbCell;
                    TopNbRsrp = nCell.TopRsrp;
                    TopNbCellEx = nCell;
                }
            }
            if (TopNbCell == null)
            {
                FindRadiusSite();
            }
            else
            {
                FindNbRelation();
            }
        }

        private void FindNbRelation()
        {
            CellArgumentRecord mainCellArgument = CellArgumentManager.Instance.GetCellArgument(this.MainCell.Name);
            CellArgumentRecord nbCellArgument = CellArgumentManager.Instance.GetCellArgument(this.TopNbCell.Name);
            if (mainCellArgument == null || nbCellArgument == null || !mainCellArgument.NbCells.Contains(nbCellArgument.CellID))
            {
                IsNbRelationCorrect = "未添加邻区";
            }
            else
            {
                IsNbRelationCorrect = "邻区已添加";
                FindAlarmNbCell();
            }
        }

        private void FindRadiusSite()
        {
            double dis = 0;
            foreach (LTEBTS bts in BtsList)
            {
                if (bts == this.MainCell.BelongBTS || bts.Type == LTEBTSType.Indoor)
                {
                    continue;
                }

                dis = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, this.MainCell.Longitude, this.MainCell.Latitude);
                if (dis < this.cond.SiteRadius)
                {
                    radiusBtsList.Add(bts);
                }
            }
            if (radiusBtsList.Count > 0)
            {
                IsExistRadiusSite = "是";
                FindAlarmSite();
            }
            else
            {
                IsExistRadiusSite = "否";
            }
        }

        private void FindAlarmSite()
        {
            StringBuilder sb = new StringBuilder();
            foreach (LTEBTS bts in this.radiusBtsList)
            {
                foreach (TestPoint tp in this.TestPoints)
                {
                    List<AlarmRecord> tmpRecords = AlarmRecordManager.Instance.GetAlarmRecords(bts.Name, tp.DateTime);
                    if (tmpRecords.Count != 0)
                    {
                        AlarmDrawItem drawItem = new AlarmDrawItem(bts);
                        drawItem.Add(tmpRecords[0]);
                        this.alarmItems.Add(drawItem);

                        sb.Append(string.Format("{0}[{1}],", bts.Name, tmpRecords[0].AlarmName));
                        break;
                    }
                }
            }
            if (sb.Length > 0)
            {
                AlarmSiteName = sb.Remove(sb.Length - 1, 1).ToString();
            }
            else
            {
                AlarmSiteName = "无";
                FindMainCellArgument();
            }
        }

        private void FindMainCellArgument()
        {
            CellArgumentRecord mainCellArgument = CellArgumentManager.Instance.GetCellArgument(this.MainCell.Name);
            if (mainCellArgument == null)
            {
                IsPaCorrect = IsPbCorrect = IsRefSignalCorrect = null;
                return;
            }

            if (mainCellArgument.Pa == null)
            {
                IsPaCorrect = null;
            }
            else if (mainCellArgument.Pa != -3 && mainCellArgument.Pa != 0)
            {
                IsPaCorrect = "不合理";
            }
            else
            {
                IsPaCorrect = "合理";
            }

            if (mainCellArgument.Pb == null)
            {
                IsPbCorrect = null;
            }
            else if (mainCellArgument.Pb != 1 && mainCellArgument.Pb != 0)
            {
                IsPbCorrect = "不合理";
            }
            else
            {
                IsPbCorrect = "合理";
            }

            if (mainCellArgument.RefSignalPower == null)
            {
                IsRefSignalCorrect = null;
            }
            else if (mainCellArgument.RefSignalPower < this.cond.RefSignalPower)
            {
                IsRefSignalCorrect = "非满配";
            }
            else
            {
                IsRefSignalCorrect = "满配";
            }
        }

        private void FindAlarmNbCell()
        {
            WeakCoverRoadLTEAssocNCell nCell = nCellDic[TopNbCell];
            foreach (TestPoint tp in nCell.TestPoints)
            {
                List<AlarmRecord> records = AlarmRecordManager.Instance.GetAlarmRecords(TopNbCell.Name, tp.DateTime);
                if (records.Count != 0)
                {
                    AlarmDrawItem drawItem = new AlarmDrawItem(TopNbCell);
                    drawItem.Add(records[0]);
                    this.alarmItems.Add(drawItem);

                    IsNbCellAlarm = records[0].AlarmName;
                    break;
                }
            }
            if (IsNbCellAlarm == null)
            {
                IsNbCellAlarm = "无";
                IsOutsideCorrect = "‘需人工核查";
                FindNbCellArgument();
            }
        }

        private void FindNbCellArgument()
        {
            CellArgumentRecord nbCellArgument = CellArgumentManager.Instance.GetCellArgument(this.TopNbCell.Name);
            if (nbCellArgument == null)
            {
                return;
            }

            if (nbCellArgument.CellOcn != null && nbCellArgument.CellOcn <= 6 && nbCellArgument.CellOcn >= -6)
            {
                IsNbOcnCorrect = "合理";
            }
            else if (nbCellArgument.CellOcn != null)
            {
                IsNbOcnCorrect = "不合理";
            }

            if (nbCellArgument.CellOcs != null && nbCellArgument.CellOcs == 0)
            {
                IsNbOcsCorrect = "合理";
            }
            else
            {
                IsNbOcsCorrect = "不合理";
            }
        }

        private MainModel MainModel { get; set; }

        private WeakCoverRoadLTEAssocCondtion cond;

        private Dictionary<LTECell, WeakCoverRoadLTEAssocNCell> nCellDic { get; set; } = new Dictionary<LTECell, WeakCoverRoadLTEAssocNCell>();

        private List<LTEBTS> radiusBtsList { get; set; } = new List<LTEBTS>();

        private List<AlarmDrawItem> alarmItems { get; set; } = new List<AlarmDrawItem>();
    }

    public class WeakCoverRoadLTEAssocNCell
    {
        public WeakCoverRoadLTEAssocNCell(LTECell nbCell)
        {
            this.NbCell = nbCell;
            this.TestPoints = new List<TestPoint>();
            this.TopRsrp = float.MinValue;
        }

        public LTECell NbCell
        {
            get;
            private set;
        }

        /// <summary>
        /// 当前小区以最强邻区出现时的采样点列表
        /// </summary>
        public List<TestPoint> TestPoints
        {
            get;
            private set;
        }

        public float TopRsrp
        {
            get;
            private set;
        }

        public void AddTestPoint(TestPoint tp, float rsrp)
        {
            TestPoints.Add(tp);
            TopRsrp = Math.Max(TopRsrp, rsrp);
        }
    }

    public class WeakCoverRoadLTEAssocCondtion
    {
        public float NbRsrpThreshold { get; set; }

        public double SiteRadius { get; set; }

        public float RefSignalPower { get; set; }
    }

}
