﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;

using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakCoverRoadLTEFormEx : MinCloseForm
    {
        public WeakCoverRoadLTEFormEx(MainModel mm):base(mm)
        {
            InitializeComponent();
            this.subGv.DoubleClick += gv_DoubleClick;
        }

        public WeakCoverRoadCondition_LTE weakCondition { get; set; }
        public void FillData(List<WeakCoverRoadLTE> list, WeakCoverRoadLTEAssocCondtion cond)
        {
            gridColumnRadiusSite.Caption = string.Format("半径{0}米内是否有基站", cond.SiteRadius);
            gridColumnAlarmSite.Caption = string.Format("半径{0}米内基站是否告警", cond.SiteRadius);

            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (WeakCoverRoadLTE item in list)
            {
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);   
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("TD_LTE_RSRP");
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            object o = (sender as GridView).GetFocusedRow();

            if (o is WeakCoverRoadLTE)
            {
                WeakCoverRoadLTE weakCover = o as WeakCoverRoadLTE;
                MainModel.SelectedLTECells.Clear();
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in weakCover.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(weakCover.TestPoints);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
            else if (o is WeakCoverRoadLTEAssocSCell)
            {
                WeakCoverRoadLTEAssocSCell sCell = o as WeakCoverRoadLTEAssocSCell;
                MainModel.SelectedLTECells.Clear();
                MainModel.SelectedLTECells.Add(sCell.MainCell);
                if (MainModel.MainForm.GetMapForm().alarmRecordLayer != null)
                {
                    MainModel.MainForm.GetMapForm().alarmRecordLayer.CustomAlarmItems.Clear();
                    MainModel.MainForm.GetMapForm().alarmRecordLayer.CustomAlarmItems.AddRange(sCell.AlarmItems);
                }
                MainModel.MainForm.GetMapForm().GoToView(sCell.MainCell.Longitude, sCell.MainCell.Latitude);
                MainModel.FireSelectedCellChanged(this);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl);
            ExcelNPOIManager.ExportToExcel(exportList);
        }
    }
}
