﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using System;
using System.IO;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    class NbIotExportOutdoorBtsReportHelperXJ : ExportOutdoorBtsReportBase
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public static readonly string WorkDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/NBIOTStationAcceptance_XJ");
        protected static string getTargetFile(string btsName, int cellCount, string saveFolder)
        {
            if (cellCount > 3)
            {
                throw (new Exception(string.Format("基站{0}小区数超过3个，不支持报告导出", btsName)));
            }

            string templateFile = "新疆NBIOT单站验证模板表.xlsx";
            templateFile = Path.Combine(WorkDir, templateFile);

            string targetFile = string.Format("新疆NBIOT新站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        public static bool ExportReports(NbIotOutDoorBtsAcceptInfoXJ btsInfo, NbIotBtsWorkParam btsWorkParamInfo, string savePath)
        {
            if (MainModel.GetInstance().IsBackground && MainModel.GetInstance().BackgroundStopRequest)
            {
                return false;
            }

            string folderPath = Path.Combine(savePath, btsWorkParamInfo.DateDes);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
            string targetFile = getTargetFile(btsInfo.BtsName, btsInfo.CellsAcceptDic.Count, folderPath);
            if (MainModel.GetInstance().IsBackground)
            {
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));
            }

            bool isValid = false;
            if (!string.IsNullOrEmpty(targetFile))
            {
                if (MainModel.GetInstance().IsBackground)
                {
                    reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));
                }

                isValid = exportFile(btsInfo, btsWorkParamInfo, targetFile);
            }
            saveCurReport(btsInfo, btsWorkParamInfo);

            return isValid;
        }

        private static bool exportFile(NbIotOutDoorBtsAcceptInfoXJ btsInfo, NbIotBtsWorkParam btsWorkParamInfo, string targetFile)
        {
            Excel.Application xlApp = null;
            try
            {
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsWorkParamInfo, btsInfo);
                fillParameterPage(eBook, btsInfo);
                fillKpiPage(eBook, btsInfo);
                fillCoverPicPage(eBook, btsInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                NbIotStationAcceptAnaXJ.GetInstance().ReportFilePaths.Add(targetFile);
                if (MainModel.GetInstance().IsBackground)
                {
                    reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                }
                return true;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        private static void saveCurReport(NbIotOutDoorBtsAcceptInfoXJ btsInfo, NbIotBtsWorkParam btsWorkParamInfo)
        {
            string path = getPath();
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            string targetFile = getTargetFile(btsInfo.BtsName, btsInfo.CellsAcceptDic.Count, path);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(btsInfo, btsWorkParamInfo, targetFile);
            }
        }

        private static string getPath()
        {
            return Singleton<NbiotStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }


        //填充报告首页
        protected static void fillHomePage(Excel.Workbook eBook, NbIotBtsWorkParam btsWorkParamInfo
            , NbIotOutDoorBtsAcceptInfoXJ btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];
            string districtName = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);

            //站名
            homePageSheet.get_Range("h10").set_Value(Type.Missing, btsInfo.BtsName);
            //日期
            homePageSheet.get_Range("ac10").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            //站号
            homePageSheet.get_Range("h12").set_Value(Type.Missing, srcLteBts.BTSID);
            //地市名
            homePageSheet.get_Range("ac12").set_Value(Type.Missing, districtName);
            //站型
            homePageSheet.get_Range("ac14").set_Value(Type.Missing, srcLteBts.TypeStringDesc);
            homePageSheet.get_Range("h16").set_Value(Type.Missing, srcLteBts.TypeStringDesc);
        }

        //填充报告第二页-参数验证
        protected static void fillParameterPage(Excel.Workbook eBook, NbIotOutDoorBtsAcceptInfoXJ btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            #region 基站参数(工程)
            kpiPageSheet.get_Range("h5").set_Value(Type.Missing, srcLteBts.Longitude);
            kpiPageSheet.get_Range("h6").set_Value(Type.Missing, srcLteBts.Latitude);
            if (srcLteBts.Cells.Count > 0)
            {
                kpiPageSheet.get_Range("h7").set_Value(Type.Missing, srcLteBts.Cells[0].TAC);
            }
            kpiPageSheet.get_Range("h8").set_Value(Type.Missing, srcLteBts.BTSID);
            #endregion

            #region 小区参数
            int cellIndex = 0;
            foreach (LTECell cell in srcLteBts.Cells)
            {
                NbIotOutDoorCellAcceptInfoXJ cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    LTECell icell = cellInfo.NBIOTCell;
                    int cellParamColIndex = 8 + (cellIndex * 8);//小区参数列：首个小区所在列号为8，其他小区列号以8递增
                    //小区名
                    kpiPageSheet.Cells[11, cellParamColIndex] = icell.Name;
                    //小区ID
                    kpiPageSheet.Cells[14, cellParamColIndex] = icell.CellID;
                    //PCI
                    kpiPageSheet.Cells[15, cellParamColIndex] = icell.PCI;
                    //频点
                    kpiPageSheet.Cells[16, cellParamColIndex] = icell.EARFCN;
                    //小区名
                    kpiPageSheet.Cells[19, cellParamColIndex] = icell.Name;
                    //经度
                    kpiPageSheet.Cells[21, cellParamColIndex] = icell.Longitude;
                    //纬度
                    kpiPageSheet.Cells[22, cellParamColIndex] = icell.Latitude;
                    //挂高
                    kpiPageSheet.Cells[24, cellParamColIndex] = icell.Altitude;
                    //方位角
                    kpiPageSheet.Cells[25, cellParamColIndex] = icell.Direction;
                    //下倾角
                    kpiPageSheet.Cells[26, cellParamColIndex] = icell.Downward;
                    cellIndex++;
                }
            }
            #endregion
        }

        //填充报告第六页-性能验收-CQT
        protected static void fillKpiPage(Excel.Workbook eBook, NbIotOutDoorBtsAcceptInfoXJ btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[6];
            kpiPageSheet.get_Range("b5").set_Value(Type.Missing, btsInfo.BtsName);
            kpiPageSheet.get_Range("e5").set_Value(Type.Missing, srcLteBts.BTSID);
            kpiPageSheet.get_Range("h5").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));

            int cellIndex = 0;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                NbIotOutDoorCellAcceptInfoXJ cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int rowIndex = 12 + (cellIndex * 8);//首个小区所在行号为8，其他小区行号以5递增
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.AttachRate.SuccessRateStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.AttachRate.Distance;
                    rowIndex++;
                    //重选现在无法判断,现场要求填100%,重选距离没有就和Attach保持一致
                    kpiPageSheet.Cells[rowIndex, 4] = "100%";
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.AttachRate.Distance;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.PingRate.SuccessRateStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.PingRate.Distance;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.PingDelay.DataStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.PingDelay.Distance;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.ULRSRP.DataStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.ULThroughput.Distance;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.ULSINR.DataStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.ULThroughput.Distance;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.ULThroughput.DataStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.ULThroughput.Distance;
                    rowIndex++;
                    kpiPageSheet.Cells[rowIndex, 4] = cellInfo.DLThroughput.DataStr;
                    kpiPageSheet.Cells[rowIndex, 5] = cellInfo.DLThroughput.Distance;
                    cellIndex++;
                }
            }
        }
        protected static void insertCoverPic(Excel.Workbook eBook, string coverPicPath, string colIndex)
        {
            if (File.Exists(coverPicPath))
            {
                NbIotAcpAutoCoverPictureXJ.InsertExcelPicture(eBook, colIndex, coverPicPath);
            }
        }

        //填充报告第七页-性能验收-DT
        protected static void fillCoverPicPage(Excel.Workbook eBook, NbIotOutDoorBtsAcceptInfoXJ btsInfo)
        {
            Excel.Worksheet coverPicPageSheet = (Excel.Worksheet)eBook.Sheets[7];
            LTEBTS srcLteBts = btsInfo.NBIOTBts;
            coverPicPageSheet.get_Range("b3").set_Value(Type.Missing, btsInfo.BtsName);
            coverPicPageSheet.get_Range("d3").set_Value(Type.Missing, srcLteBts.BTSID);
            coverPicPageSheet.get_Range("f3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));

            int rowIndex = 14;
            foreach (LTECell cell in srcLteBts.Cells)
            {
                NbIotOutDoorCellAcceptInfoXJ cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    insertCoverPic(eBook, cellInfo.CoverProperty.CoverPicPath_Rsrp, "b" + rowIndex.ToString());
                    insertCoverPic(eBook, cellInfo.CoverProperty.CoverPicPath_Sinr, "f" + rowIndex.ToString());
                    insertCoverPic(eBook, cellInfo.CoverProperty.CoverPicPath_UL, "j" + rowIndex.ToString());
                    coverPicPageSheet.Cells[rowIndex, 14] = cellInfo.CoverProperty.AvgRSRPStr;
                    coverPicPageSheet.Cells[rowIndex, 15] = cellInfo.CoverProperty.AvgSINRStr;
                    coverPicPageSheet.Cells[rowIndex, 16] = cellInfo.CoverProperty.CoverAvgSpeedStr;
                    coverPicPageSheet.Cells[rowIndex, 17] = cellInfo.CoverProperty.CoverSpeedUpTenStr;
                    rowIndex += 2;
                }
            }
        }

        protected static new void reportInfo(string str)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
            }
            else
            {
                log.Info(str);
            }
        }
        protected static new void reportError(Exception ex)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                log.Info(ex);
            }
        }
    }
}
