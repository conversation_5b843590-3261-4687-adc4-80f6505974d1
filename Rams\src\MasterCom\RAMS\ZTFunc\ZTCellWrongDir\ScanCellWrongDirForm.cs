using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Collections;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanCellWrongDirForm : MinCloseForm
    {
        public ScanCellWrongDirForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate(object row)
            {
                if (row is CellWrongDir)
                {
                    CellWrongDir item = row as CellWrongDir;
                    IList list = objectListView.Objects as IList;
                    return list.IndexOf(item) + 1;
                }
                return "";
            };
        }

        public void FillData(List<CellWrongDir> cellWrongDirList)
        {
            foreach (CellWrongDir item in cellWrongDirList)
            {
                item.CalcWrongDir();
            }
            objectListView.ClearObjects();
            objectListView.SetObjects(cellWrongDirList);
            MainModel.RefreshLegend();
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (objectListView.SelectedObject is CellWrongDir)
            {
                CellWrongDir info = objectListView.SelectedObject as CellWrongDir;
                MainModel.CurCellWrongDir = info;

                mModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.badSampleList)
                {
                    mModel.DTDataManager.Add(tp);
                }
                mModel.FireDTDataChanged(this);

                MainModel.ServerCells.Clear();
                MainModel.ServerCells.Add(info.cell);
                MainModel.SelectedCell = info.cell;
                MainModel.FireServerCellsChanged(this);

                string serialInfoName = "GSM_SCAN_RxLev";
                if (info.badSampleList[0] is ScanTestPoint_TD)
                {
                    serialInfoName = "TDSCAN_PCCPCH_RSCP";
                }
                foreach (MapSerialInfo serialInfo in mModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    if (serialInfo.Name.Equals(serialInfoName))
                    {
                        mModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                        break;
                    }
                }
                mModel.DrawFlyLines = true;
            }
        }

        /**
        private void GoToView(CellWrongDir cellWrongDir)
        {
            double ltLong = 100000;
            double ltLat = -100000;
            double brLong = -100000;
            double brLat = 100000;
            foreach (TestPoint tp in cellWrongDir.badSampleList)
            {
                if (tp.Longitude < ltLong)
                {
                    ltLong = tp.Longitude;
                }
                if (tp.Longitude > brLong)
                {
                    brLong = tp.Longitude;
                }
                if (tp.Latitude < brLat)
                {
                    brLat = tp.Latitude;
                }
                if (tp.Latitude > ltLat)
                {
                    ltLat = tp.Latitude;
                }
            }
            if (cellWrongDir.LongitudeCell < ltLong)
            {
                ltLong = cellWrongDir.LongitudeCell;
            }
            if (cellWrongDir.LongitudeCell > brLong)
            {
                brLong = cellWrongDir.LongitudeCell;
            }
            if (cellWrongDir.LatitudeCell < brLat)
            {
                brLat = cellWrongDir.LatitudeCell;
            }
            if (cellWrongDir.LatitudeCell > ltLat)
            {
                ltLat = cellWrongDir.LatitudeCell;
            }
            mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        }
        */

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(objectListView);
        }
    }
}