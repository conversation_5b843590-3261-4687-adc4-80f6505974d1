﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    class AcceptHistoryQuery : QueryBase
    {
        public AcceptHistoryQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "验收结果查询"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22061, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在查询分析验收结果...", DoStatInThread);
            FireShowResult();
        }

        private void DoStatInThread()
        {
            try
            {
                this.result = AcceptHistoryResultCreator.GetResult();
                this.error = null;
            }
            catch (Exception ex)
            {
                this.error = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                WaitTextBox.Close();
            }
        }

        private void FireShowResult()
        {
            if (error != null)
            {
                System.Windows.Forms.MessageBox.Show(error.Message + Environment.NewLine + error.StackTrace,
                    this.Name, System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            else
            {
                AcceptHistoryResultForm resultForm = MainModel.GetObjectFromBlackboard(
                    typeof(AcceptHistoryResultForm).FullName) as AcceptHistoryResultForm;
                if (resultForm == null || resultForm.IsDisposed)
                {
                    resultForm = new AcceptHistoryResultForm(MainModel);
                }
                resultForm.FillData(result);
                if (!resultForm.Visible)
                {
                    resultForm.Show(MainModel.MainForm);
                }
            }

            error = null;
            result = null;
        }

        private Exception error = null;
        private object result = null;
    }
}
