﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class OverCoverCell_LTE
    {
        public OverCoverCell_LTE(LTECell cell, double idealCoverDis,double overFactor,string nearBtsNames)
        {
            this.Cell = cell;
            IdealCoverDistance = Math.Round(idealCoverDis, 2);
            OverDistance = Math.Round(idealCoverDis * overFactor, 2);
            NearestBtsNames = nearBtsNames;
            if (cell.Altitude != 0 && cell.Altitude != 999)
            {
                double dir = 90 + cell.Downward / 2 - Math.Atan(idealCoverDis / cell.Altitude) * 180 / Math.PI;
                CellDownDirSuggest = Math.Round(dir, 1).ToString();
            }
            else
            {
                CellDownDirSuggest = "无挂高信息，无法计算建议下倾角";
            }
            OverDianpingMax = float.MinValue;
            OverDianpingMin = float.MaxValue;
            OverDistanceMin = float.MaxValue;
            OverDistanceMax = float.MinValue;
            OverCoverPoints = new List<TestPoint>();
        }

        public double OverDistance
        {
            get;
            private set;
        }

        public string NearestBtsNames
        { get; private set; }

        public void AddTestPoint(TestPoint tp, int idx, float rsrp, double distance)
        {
            TestPointNum++;
            if (distance >= OverDistance)
            {
                OverCoverPoints.Add(tp);

                OverDistanceMin = Math.Min(OverDistanceMin, distance);
                OverDistanceMax = Math.Max(OverDistanceMax, distance);
                overDistanceTotal += distance;

                OverDianpingMin = Math.Min(OverDianpingMin, rsrp);
                OverDianpingMax = Math.Max(OverDianpingMax, rsrp);
                overDianpingTotal += rsrp;

                float? sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", idx];
                if (sinr <= 50 && sinr >= -50)
                {
                    overPointZhiliangNum++;
                    overZhiliangTotal += (float)sinr;
                    OverZhiliangMax = OverZhiliangMax == null ? (float)sinr : Math.Max((float)OverZhiliangMax, (float)sinr);
                    OverZhiliangMin = OverZhiliangMin == null ? (float)sinr : Math.Min((float)OverZhiliangMin, (float)sinr);
                }
            }
        }

        public void MakeSummary()
        {
            OverDistanceAvg = Math.Round(overDistanceTotal / OverPointNum, 2);
            OverDianpingAvg = (float)Math.Round(overDianpingTotal / OverPointNum, 2);
            if (overPointZhiliangNum != 0)
            {
                OverZhiliangAvg = (float)Math.Round(overZhiliangTotal / overPointZhiliangNum, 2);
            }
        }

        public float OverDianpingMin { get; private set; }
        public float OverDianpingMax { get; private set; }
        private float overDianpingTotal = 0;
        public float OverDianpingAvg { get; private set; }

        public float? OverZhiliangMin { get; private set; }
        public float? OverZhiliangMax { get; private set; }
        private int overPointZhiliangNum = 0;
        private float overZhiliangTotal = 0;
        public float? OverZhiliangAvg { get; private set; }

        private double overDistanceTotal = 0;
        public double OverDistanceMin
        { get; private set; }
        public double OverDistanceMax
        { get; private set; }
        public double OverDistanceAvg
        { get; private set; }

        public int TestPointNum
        { get; private set; }

        public int OverPointNum
        {
            get { return OverCoverPoints.Count; }
        }

        public double OverPercent
        {
            get { return Math.Round(100.0 * OverPointNum / TestPointNum, 2); }
        }

        public List<TestPoint> OverCoverPoints
        { get; private set; }

        public double IdealCoverDistance
        {
            get;
            private set;
        }

        public LTECell Cell { get; private set; }

        public int CellID
        {
            get { return this.Cell.SCellID; }
        }
        public string CellName
        {
            get { return this.Cell.Name; }
        }

        public int CellAltitude
        {
            get { return this.Cell.Altitude; }
        }

        public short CellDownDir
        {
            get { return this.Cell.Downward; }
        }

        public string CellDownDirSuggest
        {
            get;
            private set;
        }

    }
}
