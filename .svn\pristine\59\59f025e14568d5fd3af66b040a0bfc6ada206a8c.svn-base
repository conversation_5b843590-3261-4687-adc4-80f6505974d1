using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using GeneGraph;
using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func.AssistLayer
{
    [Serializable()]
    public class ArrowElementEx : ArrowElement
    {
        public ArrowElementEx(DbPoint startF, DbPoint endF)
            :base(0,0,0,0)
        {
            this.startF = startF;
            this.endF = endF;
        }

        protected DbPoint startF;
        protected DbPoint endF;
        
        public IGisAdapter GisAdapter { get; set; }

        public override void Draw(System.Drawing.Graphics g)
        {
            if (!Visible) return;
            PointF pf1, pf2;
            GisAdapter.ToDisplay(startF, out pf1);
            if (pf1.X < -100 || pf1.X > 5000 || pf1.Y < -100 || pf1.Y > 5000)
                return;
            GisAdapter.ToDisplay(endF, out pf2);
            if (pf2.X < -100 || pf2.X > 5000 || pf2.Y < -100 || pf2.Y > 5000)
                return;
            this.points.Clear();
            this.points.Add(pf1);
            this.points.Add(pf2);
            base.Draw(g);
        }
    }
}
