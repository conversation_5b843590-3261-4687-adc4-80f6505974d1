﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRWeakMosReasonForm : MinCloseForm
    {
        public NRWeakMosReasonForm()
        {
            InitializeComponent();
        }

        public void FillData(int mosTpCount, List<NRWeakMosReasonInfo> resultList, List<string> reasonNameList)
        {
            this.gridControlDetail.DataSource = resultList;//问题详情
            this.gridControlDetail.RefreshDataSource();

            fillSum(mosTpCount, resultList, reasonNameList);//原因汇总
        }

        private void fillSum(int mosTpCount, List<NRWeakMosReasonInfo> resultList, List<string> reasonNameList)
        {
            Dictionary<string, WeakMosSumReasonInfo> reasonInfoDic = new Dictionary<string, WeakMosSumReasonInfo>();
            int weakMosTpCount = resultList.Count;
            foreach (string reason in reasonNameList)
            {
                WeakMosSumReasonInfo reasonInfo = new WeakMosSumReasonInfo(reason);
                reasonInfo.WeakMosInfo.TotalCount = mosTpCount;
                reasonInfo.WeakMosInfo.Count = weakMosTpCount;
                reasonInfo.ReasonMosInfo.Count = 0;
                reasonInfoDic.Add(reason, reasonInfo);
            }

            foreach (NRWeakMosReasonInfo result in resultList)
            {
                foreach (string strReasonDes in result.ReasonList)
                {
                    string reasonName = strReasonDes;
                    WeakMosSumReasonInfo reasonInfo;
                    if (strReasonDes.Contains("RRC重建"))
                    {
                        reasonName = "RRC重建";//RRC重建描述中包含了解析出的原因值
                    }

                    if (reasonInfoDic.TryGetValue(reasonName, out reasonInfo))
                    {
                        reasonInfo.ReasonMosInfo.Count++;
                    }
                    else
                    {
                        reasonInfo = new WeakMosSumReasonInfo(reasonName);
                        reasonInfo.WeakMosInfo.TotalCount = mosTpCount;
                        reasonInfo.WeakMosInfo.Count = weakMosTpCount;
                        reasonInfo.ReasonMosInfo.Count = 1;
                        reasonInfoDic.Add(reasonName, reasonInfo);
                    }
                }
            }

            foreach (var item in reasonInfoDic.Values)
            {
                item.WeakMosInfo.Calculate();
                item.ReasonMosInfo.TotalCount = item.WeakMosInfo.Count;
                item.ReasonMosInfo.Calculate();
            }

            this.gridControlSum.DataSource = new List<WeakMosSumReasonInfo>(reasonInfoDic.Values);
            this.gridControlSum.RefreshDataSource();
        }

        private void toolStripSumExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridViewSum);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void toolStripDetailExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridViewDetail);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void toolStripReplay_Click(object sender, EventArgs e)
        {
            NRWeakMosReasonInfo callInfo = (NRWeakMosReasonInfo)gridViewDetail.GetFocusedRow();
            if (callInfo == null)
            {
                MessageBox.Show("请选择一行信息进行回放！");
                return;
            }
            FileReplayer.ReplayOnePart(callInfo.MosTestPoint);
        }

        private void gridViewDetail_DoubleClick(object sender, EventArgs e)
        {
            NRWeakMosReasonInfo callInfo = (NRWeakMosReasonInfo)gridViewDetail.GetFocusedRow();
            if (callInfo != null)
            {
                MainModel.ClearDTData();
                MainModel.DTDataManager.Add(callInfo.MosTestPoint);
                if (callInfo.CallBeginEvt != null)
                {
                    MainModel.DTDataManager.Add(callInfo.CallBeginEvt);
                }
                if (callInfo.EsrvccEvt != null)
                {
                    MainModel.DTDataManager.Add(callInfo.EsrvccEvt);
                }
                if (callInfo.RRCReestablishMsg != null)
                {
                    MainModel.DTDataManager.Add(callInfo.RRCReestablishMsg);
                }

                foreach (TestPoint tp in callInfo.MosTestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (Event evt in callInfo.HandOverEvents)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
                MainModel.FireDTDataChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(callInfo.MosTestPoint.Longitude, callInfo.MosTestPoint.Latitude, 5000);
            }
        }
    }
}
