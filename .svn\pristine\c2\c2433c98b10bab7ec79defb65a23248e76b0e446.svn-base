﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTPoorRSRQRoad
{
    public partial class ResultFormNR : MinCloseForm
    {
        public ResultFormNR()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<PoorRsrqRoad_NR> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
           
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            PoorRsrqRoad_NR weakCover = gv.GetFocusedRow() as PoorRsrqRoad_NR;
            if (weakCover != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in weakCover.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(weakCover.MidLng, weakCover.MidLat, 6000);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }
    }
}
