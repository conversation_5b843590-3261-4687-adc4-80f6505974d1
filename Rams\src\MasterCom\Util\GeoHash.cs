﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    /// <summary>
    /// 将大量数据按照经纬度划分成1个个唯一编号的小区域
    /// 可以用于快速比对一个经纬度点在一定范围内的最近小区
    /// </summary>
    public static class GeoHash
    {
        static GeoHash()
        {
            SetLength(8);
        }

        //Base32编码字符
        private static readonly char[] CHARS = {'0','1','2','3','4','5','6','7','8','9',
                                       'b','c','d','e','f','g','h','j','k','m',
                                       'n','p','q','r','s','t','u','v','w','x','y','z'};

        //LngLength+LatLength必须是5的倍数,否则无法计算Base32
        private static int LatLength = 20;
        private static int LngLength = 20;
        //经纬度范围最好不要变,否则所有的精度和范围都将改变,如果有信心重新适配一遍那就随便改
        private const double MinLat = -90;
        private const double MaxLat = 90;
        private const double MinLng = -180;
        private const double MaxLng = 180;

        //根据编码长度获取对应级别下最小经纬度间隔
        private static double minLatGap;
        private static double minLngGap;

        public static void SetLengthByDistance(double distance)
        {
            if (distance >= 20000)
            {
                SetLength(2);
            }
            else if (distance >= 600)
            {
                SetLength(4);
            }
            else if (distance >= 20)
            {
                //为什么这里用6呢,因为8的精度小了,7有点问题,所以用6

                //例 : 如果距离是50M,8的经度是20M
                //如果一个点刚好在区域的边缘,那么算上周边一圈区域,最小的距离可能也就是20M
                //这样小与50M,部分点可能就算不到

                //奇数级数存在问题,故直接使用6
                SetLength(6);
            }
            else if (distance >= 1)
            {
                SetLength(8);
            }
        }

        public static void SetLength(int level)
        {
            switch (level)
            {
                case 1://2500km
                    LatLength = 3;
                    LngLength = 2;
                    break;
                case 2://630km
                    LatLength = 5;
                    LngLength = 5;
                    break;
                case 3://78km
                    LatLength = 8;
                    LngLength = 7;
                    break;
                case 4://20km
                    LatLength = 10;
                    LngLength = 10;
                    break;
                case 5://2.4km
                    LatLength = 13;
                    LngLength = 12;
                    break;
                case 6://600m
                    LatLength = 15;
                    LngLength = 15;
                    break;
                case 7://80m
                    LatLength = 18;
                    LngLength = 17;
                    break;
                case 8://20m
                    LatLength = 20;
                    LngLength = 20;
                    break;
                case 9://2m
                    LatLength = 23;
                    LngLength = 22;
                    break;
                case 10://600cm
                    LatLength = 25;
                    LngLength = 25;
                    break;
                case 11://75cm
                    LatLength = 28;
                    LngLength = 27;
                    break;
                case 12://18cm
                    LatLength = 30;
                    LngLength = 30;
                    break;
                default:
                    break;
            }
            setMinLatLng();
        }

        private static void setMinLatLng()
        {
            minLatGap = MaxLat - MinLat;
            for (int i = 0; i < LatLength; i++)
            {
                minLatGap /= 2.0;
            }
            minLngGap = MaxLng - MinLng;
            for (int i = 0; i < LngLength; i++)
            {
                minLngGap /= 2.0;
            }
        }

        //Geo算法使用Z曲线存在一定的缺陷,因此需要对比九宫格区域内的所有点才能确定最近距离
        public static List<string> GetAroundGeoHash(double lat, double lng)
        {
            List<string> list = new List<string>();
            double uplat = lat + minLatGap;
            double downLat = lat - minLatGap;

            double leftlng = lng - minLngGap;
            double rightLng = lng + minLngGap;
            //使用奇数level时,周边区域计算的有问题,本身应该得到8个方位,不同的编码,
            //但是实际会有重复,所以有的区域没有包括进来,会导致结果异常,感觉可能与最小经纬度间隔有关

            string leftUp = EnCode(uplat, leftlng);
            list.Add(leftUp);

            string leftMid = EnCode(lat, leftlng);
            list.Add(leftMid);

            string leftDown = EnCode(downLat, leftlng);
            list.Add(leftDown);

            string midUp = EnCode(uplat, lng);
            list.Add(midUp);

            //string midMid = EnCode(lat, lng);
            //list.Add(midMid);

            string midDown = EnCode(downLat, lng);
            list.Add(midDown);

            string rightUp = EnCode(uplat, rightLng);
            list.Add(rightUp);

            string rightMid = EnCode(lat, rightLng);
            list.Add(rightMid);

            string rightDown = EnCode(downLat, rightLng);
            list.Add(rightDown);

            return list;
        }

        #region EnCode
        public static string EnCode(double lat, double lng)
        {
            bool[] latArray = getHashArray(lat, MinLat, MaxLat, LatLength);
            bool[] lngArray = getHashArray(lng, MinLng, MaxLng, LngLength);
            bool[] bools = getMergeArray(latArray, lngArray);
            int len = bools.Length;
            if (len == 0)
            {
                return null;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < len; i += 5)
            {
                bool[] base32 = new bool[5];
                for (int j = 0; j < 5; j++)
                {
                    base32[j] = bools[i + j];
                }
                char? cha = getBase32Char(base32);
                if (cha == null)
                {
                    return null;
                }
                sb.Append(cha.Value);
            }
            return sb.ToString();
        }

        private static bool[] getHashArray(double value, double min, double max, int length)
        {
            if (value < min || value > max || length < 1)
            {
                return new bool[0];
            }
            bool[] result = new bool[length];
            //二分法,左区间标记为false,右区间标记为true
            for (int i = 0; i < length; i++)
            {
                double mid = (min + max) / 2;
                if (value > mid)
                {
                    result[i] = true;
                    min = mid;
                }
                else
                {
                    result[i] = false;
                    max = mid;
                }
            }
            return result;
        }

        private static bool[] getMergeArray(bool[] latArray, bool[] lngArray)
        {
            if (latArray.Length == 0 || lngArray.Length == 0)
            {
                return new bool[0];
            }
            bool[] result = new bool[LngLength + LatLength];
            for (int i = 0; i < LngLength; i++)
            {
                result[2 * i] = lngArray[i];
            }
            //LatLength与LatLength不等时,LngLength < LatLength,所以采用LngLength
            for (int i = 0; i < LngLength; i++)
            {
                result[2 * i + 1] = latArray[i];
            }
            return result;
        }

        //5个二进制位转换成一个base32码
        private static char? getBase32Char(bool[] base32)
        {
            if (base32 == null || base32.Length != 5)
            {
                return null;
            }
            int num = 0;
            foreach (bool item in base32)
            {
                num <<= 1;
                if (item)
                {
                    num += 1;
                }
            }
            return CHARS[num % CHARS.Length];
        }
        #endregion

        #region DeCode
        public static double[] DeCode(string geoCode)
        {
            bool even = true;
            double[] lat = { MinLat, MaxLat };
            double[] lon = { MinLng, MaxLng };
            List<char> chList = new List<char>(CHARS);
            foreach (char c in geoCode)
            {
                int cd = chList.IndexOf(c);
                for (int j = 0; j < 5; j++)
                {
                    int mask = int.Parse(Math.Pow(2, 4 - j).ToString());
                    if (even)
                    {
                        refineInterval(ref lon, cd, mask);
                    }
                    else
                    {
                        refineInterval(ref lat, cd, mask);
                    }
                    even = !even;
                }
            }
            return new[] { (lat[0] + lat[1]) / 2, (lon[0] + lon[1]) / 2 };
        }

        private static void refineInterval(ref double[] interval, int cd, int mask)
        {
            if ((cd & mask) != 0)
            {
                interval[0] = (interval[0] + interval[1]) / 2;
            }
            else
            {
                interval[1] = (interval[0] + interval[1]) / 2;
            }
        }
        #endregion
    }
}
