﻿using System;
using System.Collections.Generic;
using System.Text;
using System.ComponentModel;

using MasterCom.RAMS.Model;
using MapWinGIS;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsCitySettingItem
    {
        public LteMgrsCitySettingItem(string cityName)
        {
            CityName = cityName;
            FieldNames = new List<string>();
        }

        public LteMgrsCitySettingItem(string cityName, string shpFile, string fieldName)
        {
            this.CityName = cityName;
            this.FieldNames = new List<string>();
            this.ShpFile = shpFile;
            this.FieldName = fieldName;
        }

        public bool Enable
        {
            get;
            set;
        }

        public string ShpFile
        {
            get;
            set;
        }

        [Browsable(false)]
        public string FieldName
        {
            get;
            set;
        }

        public string CityName
        {
            get;
            private set;
        }

        public List<string> FieldNames
        {
            get;
            private set;
        }

        public LteMgrsCitySettingItem Clone()
        {
            LteMgrsCitySettingItem item = new LteMgrsCitySettingItem(CityName);
            item.Enable = Enable;
            item.ShpFile = ShpFile;
            item.FieldName = FieldName;
            item.FieldNames.AddRange(FieldNames);
            return item;
        }

        public LteMgrsCity Convert()
        {
            Shapefile shp = new Shapefile();
            if (!shp.Open(ShpFile, null))
            {
                throw (new Exception(shp.get_ErrorMsg(shp.LastErrorCode)));
            }
            if (shp.ShapefileType != ShpfileType.SHP_POLYGON)
            {
                shp.Close();
                throw (new Exception("不是多边形图层"));
            }

            int fieldIndex = -1;
            int fieldCount = shp.NumFields;
            for (int i = 0; i < fieldCount; ++i)
            {
                Field field = shp.get_Field(i);
                if (field.Name == FieldName)
                {
                    fieldIndex = i;
                    break;
                }
            }
            if (fieldIndex == -1)
            {
                shp.Close();
                throw (new Exception("在图层文件中未找到选定的字段名"));
            }

            Dictionary<string, MapWinGIS.Shape> regShapeDic = new Dictionary<string, MapWinGIS.Shape>();
            int shapeCount = shp.NumShapes;
            for (int i = 0; i < shapeCount; ++i)
            {
                string regionName = shp.get_CellValue(fieldIndex, i).ToString();
                if (regShapeDic.ContainsKey(regionName))
                {
                    shp.Close();
                    throw (new Exception("选定的字段名发现重复值: " + regionName));
                }
                MapWinGIS.Shape shape = shp.get_Shape(i);
                regShapeDic.Add(regionName, shape);
            }
            shp.Close();

            return new LteMgrsCity(CityName, regShapeDic);
        }
    }

    public class LteMgrsBaseSettingManager
    {
        public static LteMgrsBaseSettingManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LteMgrsBaseSettingManager();
                }
                return instance;
            }
        }

        // 基本设置的查询地市
        public List<LteMgrsCitySettingItem> CityItems
        {
            get
            {
                List<LteMgrsCitySettingItem> lst = new List<LteMgrsCitySettingItem>();
                foreach (LteMgrsCitySettingItem city in cityItems)
                {
                    lst.Add(city.Clone());
                }
                return lst;
            }
        }

        public void SetCityItems(List<LteMgrsCitySettingItem> value)
        {
            foreach (LteMgrsCitySettingItem si in cityItems)
            {
                si.Enable = false;
            }

            foreach (LteMgrsCitySettingItem si in value)
            {
                for (int i = 0; i < cityItems.Count; ++i)
                {
                    if (cityItems[i].CityName == si.CityName)
                    {
                        cityItems[i] = si.Clone();
                        break;
                    }
                }
            }
        }

        // 查询条件是否有效
        public bool IsValid
        {
            get
            {
                string invalidReason;
                return CheckCityItems(cityItems, out invalidReason);
            }
        }

        // 栅格大小
        public int GridSize
        {
            get { return LteMgrsGrid.SGridSize; }
            set { LteMgrsGrid.SGridSize = value; }
        }

        // 是否查询基准库数据
        public bool BaseDataEnable
        {
            get;
            set;
        }

        // 基准库查询条件
        public QueryCondition BaseQueryCondition
        {
            get;
            set;
        }

        public bool CheckCityItems(List<LteMgrsCitySettingItem> citys, out string invalidReason)
        {
            bool isFound = false;
            foreach (LteMgrsCitySettingItem item in citys)
            {
                if (!item.Enable)
                {
                    continue;
                }

                if (string.IsNullOrEmpty(item.ShpFile) || string.IsNullOrEmpty(item.FieldName))
                {
                    invalidReason = item.CityName + " 的图层文件或者网格字段名未设置";
                    return false;
                }
                isFound = true;
            }

            if (!isFound)
            {
                invalidReason = "至少选择一个地市进行查询";
                return false;
            }
            invalidReason = "";
            return true;
        }

        public void SaveConfig(XmlConfigFile xcfg)
        {
            XmlElement configBaseSet = xcfg.AddConfig("BaseSetting");
            xcfg.AddItem(configBaseSet, "GridSize", this.GridSize);
            foreach (LteMgrsCitySettingItem cityItem in cityItems)
            {
                XmlElement configCity = xcfg.AddConfig(cityItem.CityName);
                xcfg.AddItem(configCity, "ShapeFile", cityItem.ShpFile);
                xcfg.AddItem(configCity, "FieldName", cityItem.FieldName);
                xcfg.AddItem(configCity, "Enable", cityItem.Enable);
            }
        }

        private readonly List<LteMgrsCitySettingItem> cityItems;

        public readonly string ConfigPath = Application.StartupPath + @"\config\LteMgrsCondition.xml";

        private LteMgrsBaseSettingManager()
        {
            cityItems = new List<LteMgrsCitySettingItem>();
            XmlConfigFile configFile = new MyXmlConfigFile(ConfigPath);
            if (configFile.Load())
            {
                XmlElement configBaseSet = configFile.GetConfig("BaseSetting");
                object obj = configFile.GetItemValue(configBaseSet, "GridSize");
                this.GridSize = obj == null ? this.GridSize : (int)obj;
            }
            foreach (string cityName in DistrictManager.GetInstance().DistrictNames)
            {
                if (cityName == "")
                {
                    continue;
                }
                if (configFile.Load())
                {
                    XmlElement configCity = configFile.GetConfig(cityName);
                    object shapeObj = configFile.GetItemValue(configCity, "ShapeFile");
                    object fieldObj = configFile.GetItemValue(configCity, "FieldName");
                    if (shapeObj != null && fieldObj != null)
                    {
                        bool enable = getEnable(configFile, configCity);
                        LteMgrsCitySettingItem item = new LteMgrsCitySettingItem(cityName, shapeObj.ToString(), fieldObj.ToString());
                        item.Enable = enable;
                        item.FieldNames.AddRange(getFieldNames(item.ShpFile));
                        cityItems.Add(item);
                        continue;
                    }
                }
                cityItems.Add(new LteMgrsCitySettingItem(cityName));
            }
        }

        private bool getEnable(XmlConfigFile configFile, XmlElement configCity)
        {
            bool enable;
            if (configFile.GetItemValue(configCity, "Enable") == null)
            {
                enable = (bool)configFile.GetItemValue(configCity, "Enable");
            }
            else
            {
                enable = false;
            }

            return enable;
        }

        private List<string> getFieldNames(string shpFile)
        {
            List<string> retList = new List<string>();
            MapWinGIS.Shapefile shp = new MapWinGIS.Shapefile();
            if (shp.Open(shpFile, null))
            {
                for (int i = 0; i < shp.NumFields; ++i)
                {
                    MapWinGIS.Field field = shp.get_Field(i);
                    retList.Add(field.Name);
                }
                shp.Close();
            }
            return retList;
        }

        private static LteMgrsBaseSettingManager instance = null;
    }
}
