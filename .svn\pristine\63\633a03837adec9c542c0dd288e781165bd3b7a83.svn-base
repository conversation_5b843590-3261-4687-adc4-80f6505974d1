﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTESanHighCoverateRoadListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripRelative = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportSummaryRel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportRel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportCellRel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpandRel = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseRel = new System.Windows.Forms.ToolStripMenuItem();
            this.RelListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDitance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPercent = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCPI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.AbsListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.absSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absCellType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absCPI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absMaxRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absMinRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absAvgRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absLongitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absLatitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absFirstTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.absLastTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStripAbsolute = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportSummaryAbs = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAbs = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportCellAbs = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpandAbs = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAbs = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.relativeBox = new System.Windows.Forms.GroupBox();
            this.absoluteBox = new System.Windows.Forms.GroupBox();
            this.absPercent = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStripRelative.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.RelListViewRoad)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsListViewRoad)).BeginInit();
            this.contextMenuStripAbsolute.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.relativeBox.SuspendLayout();
            this.absoluteBox.SuspendLayout();
            this.SuspendLayout();
            // 
            // contextMenuStripRelative
            // 
            this.contextMenuStripRelative.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportSummaryRel,
            this.miExportRel,
            this.miExportCellRel,
            this.miExpandRel,
            this.miCollapseRel});
            this.contextMenuStripRelative.Name = "contextMenuStrip1";
            this.contextMenuStripRelative.Size = new System.Drawing.Size(190, 114);
            this.contextMenuStripRelative.Text = "导出概要信息到Excel";
            // 
            // miExportSummaryRel
            // 
            this.miExportSummaryRel.Name = "miExportSummaryRel";
            this.miExportSummaryRel.Size = new System.Drawing.Size(189, 22);
            this.miExportSummaryRel.Text = "导出概要信息到Excel";
            this.miExportSummaryRel.Click += new System.EventHandler(this.miExportSummaryRel_Click);
            // 
            // miExportRel
            // 
            this.miExportRel.Name = "miExportRel";
            this.miExportRel.Size = new System.Drawing.Size(189, 22);
            this.miExportRel.Text = "导出详细信息到Excel";
            this.miExportRel.Click += new System.EventHandler(this.miExportRel_Click);
            // 
            // miExportCellRel
            // 
            this.miExportCellRel.Name = "miExportCellRel";
            this.miExportCellRel.Size = new System.Drawing.Size(189, 22);
            this.miExportCellRel.Text = "导出调整小区到Excel";
            this.miExportCellRel.Click += new System.EventHandler(this.miExportCellRel_Click);
            // 
            // miExpandRel
            // 
            this.miExpandRel.Name = "miExpandRel";
            this.miExpandRel.Size = new System.Drawing.Size(189, 22);
            this.miExpandRel.Text = "全部展开";
            this.miExpandRel.Click += new System.EventHandler(this.miExpandRel_Click);
            // 
            // miCollapseRel
            // 
            this.miCollapseRel.Name = "miCollapseRel";
            this.miCollapseRel.Size = new System.Drawing.Size(189, 22);
            this.miCollapseRel.Text = "全部合并";
            this.miCollapseRel.Click += new System.EventHandler(this.miCollapseRel_Click);
            // 
            // RelListViewRoad
            // 
            this.RelListViewRoad.AllColumns.Add(this.olvColumnSN);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnRoadName);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnDitance);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnPercent);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnSample);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnCellName);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnCellType);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnLAC);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnCI);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnARFCN);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnCPI);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnRscp);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnCellDistance);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnMaxRscp);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnMinRscp);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnAvgRscp);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnLongitudeMid);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnLatitudeMid);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnFileName);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnFirstTime);
            this.RelListViewRoad.AllColumns.Add(this.olvColumnLastTime);
            this.RelListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRoadName,
            this.olvColumnDitance,
            this.olvColumnPercent,
            this.olvColumnSample,
            this.olvColumnCellName,
            this.olvColumnCellType,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnARFCN,
            this.olvColumnCPI,
            this.olvColumnRscp,
            this.olvColumnCellDistance,
            this.olvColumnMaxRscp,
            this.olvColumnMinRscp,
            this.olvColumnAvgRscp,
            this.olvColumnLongitudeMid,
            this.olvColumnLatitudeMid,
            this.olvColumnFileName,
            this.olvColumnFirstTime,
            this.olvColumnLastTime});
            this.RelListViewRoad.ContextMenuStrip = this.contextMenuStripRelative;
            this.RelListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.RelListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.RelListViewRoad.FullRowSelect = true;
            this.RelListViewRoad.GridLines = true;
            this.RelListViewRoad.HeaderWordWrap = true;
            this.RelListViewRoad.IsNeedShowOverlay = false;
            this.RelListViewRoad.Location = new System.Drawing.Point(3, 18);
            this.RelListViewRoad.Name = "RelListViewRoad";
            this.RelListViewRoad.OwnerDraw = true;
            this.RelListViewRoad.ShowGroups = false;
            this.RelListViewRoad.Size = new System.Drawing.Size(1216, 185);
            this.RelListViewRoad.TabIndex = 8;
            this.RelListViewRoad.UseCompatibleStateImageBehavior = false;
            this.RelListViewRoad.View = System.Windows.Forms.View.Details;
            this.RelListViewRoad.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 80;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 120;
            // 
            // olvColumnDitance
            // 
            this.olvColumnDitance.HeaderFont = null;
            this.olvColumnDitance.Text = "距离(米)";
            // 
            // olvColumnPercent
            // 
            this.olvColumnPercent.HeaderFont = null;
            this.olvColumnPercent.Text = "相对覆盖点占比(%)";
            this.olvColumnPercent.Width = 70;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnCellType
            // 
            this.olvColumnCellType.AspectName = "";
            this.olvColumnCellType.HeaderFont = null;
            this.olvColumnCellType.Text = "小区状态";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnARFCN
            // 
            this.olvColumnARFCN.HeaderFont = null;
            this.olvColumnARFCN.Text = "ARFCN";
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "CPI";
            // 
            // olvColumnRscp
            // 
            this.olvColumnRscp.HeaderFont = null;
            this.olvColumnRscp.Text = "PCCPCH_RSCP";
            this.olvColumnRscp.Width = 100;
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "与采样点距离（米）";
            this.olvColumnCellDistance.Width = 120;
            // 
            // olvColumnMaxRscp
            // 
            this.olvColumnMaxRscp.HeaderFont = null;
            this.olvColumnMaxRscp.Text = "第一强最大值";
            this.olvColumnMaxRscp.Width = 80;
            // 
            // olvColumnMinRscp
            // 
            this.olvColumnMinRscp.HeaderFont = null;
            this.olvColumnMinRscp.Text = "第一强最小值";
            this.olvColumnMinRscp.Width = 80;
            // 
            // olvColumnAvgRscp
            // 
            this.olvColumnAvgRscp.HeaderFont = null;
            this.olvColumnAvgRscp.Text = "第一强平均值";
            this.olvColumnAvgRscp.Width = 80;
            // 
            // olvColumnLongitudeMid
            // 
            this.olvColumnLongitudeMid.HeaderFont = null;
            this.olvColumnLongitudeMid.Text = "经度";
            this.olvColumnLongitudeMid.Width = 80;
            // 
            // olvColumnLatitudeMid
            // 
            this.olvColumnLatitudeMid.HeaderFont = null;
            this.olvColumnLatitudeMid.Text = "纬度";
            this.olvColumnLatitudeMid.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            // 
            // olvColumnFirstTime
            // 
            this.olvColumnFirstTime.HeaderFont = null;
            this.olvColumnFirstTime.Text = "开始时间";
            this.olvColumnFirstTime.Width = 100;
            // 
            // olvColumnLastTime
            // 
            this.olvColumnLastTime.HeaderFont = null;
            this.olvColumnLastTime.Text = "结束时间";
            this.olvColumnLastTime.Width = 100;
            // 
            // AbsListViewRoad
            // 
            this.AbsListViewRoad.AllColumns.Add(this.absSN);
            this.AbsListViewRoad.AllColumns.Add(this.absRoadName);
            this.AbsListViewRoad.AllColumns.Add(this.absDistance);
            this.AbsListViewRoad.AllColumns.Add(this.absPercent);
            this.AbsListViewRoad.AllColumns.Add(this.absSample);
            this.AbsListViewRoad.AllColumns.Add(this.absCellName);
            this.AbsListViewRoad.AllColumns.Add(this.absCellType);
            this.AbsListViewRoad.AllColumns.Add(this.absLAC);
            this.AbsListViewRoad.AllColumns.Add(this.absCI);
            this.AbsListViewRoad.AllColumns.Add(this.absARFCN);
            this.AbsListViewRoad.AllColumns.Add(this.absCPI);
            this.AbsListViewRoad.AllColumns.Add(this.absRscp);
            this.AbsListViewRoad.AllColumns.Add(this.absCellDistance);
            this.AbsListViewRoad.AllColumns.Add(this.absMaxRscp);
            this.AbsListViewRoad.AllColumns.Add(this.absMinRscp);
            this.AbsListViewRoad.AllColumns.Add(this.absAvgRscp);
            this.AbsListViewRoad.AllColumns.Add(this.absLongitudeMid);
            this.AbsListViewRoad.AllColumns.Add(this.absLatitudeMid);
            this.AbsListViewRoad.AllColumns.Add(this.absFileName);
            this.AbsListViewRoad.AllColumns.Add(this.absFirstTime);
            this.AbsListViewRoad.AllColumns.Add(this.absLastTime);
            this.AbsListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.absSN,
            this.absRoadName,
            this.absDistance,
            this.absPercent,
            this.absSample,
            this.absCellName,
            this.absCellType,
            this.absLAC,
            this.absCI,
            this.absARFCN,
            this.absCPI,
            this.absRscp,
            this.absCellDistance,
            this.absMaxRscp,
            this.absMinRscp,
            this.absAvgRscp,
            this.absLongitudeMid,
            this.absLatitudeMid,
            this.absFileName,
            this.absFirstTime,
            this.absLastTime});
            this.AbsListViewRoad.ContextMenuStrip = this.contextMenuStripAbsolute;
            this.AbsListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.AbsListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.AbsListViewRoad.FullRowSelect = true;
            this.AbsListViewRoad.GridLines = true;
            this.AbsListViewRoad.HeaderWordWrap = true;
            this.AbsListViewRoad.IsNeedShowOverlay = false;
            this.AbsListViewRoad.Location = new System.Drawing.Point(3, 18);
            this.AbsListViewRoad.Name = "AbsListViewRoad";
            this.AbsListViewRoad.OwnerDraw = true;
            this.AbsListViewRoad.ShowGroups = false;
            this.AbsListViewRoad.Size = new System.Drawing.Size(1216, 306);
            this.AbsListViewRoad.TabIndex = 8;
            this.AbsListViewRoad.UseCompatibleStateImageBehavior = false;
            this.AbsListViewRoad.View = System.Windows.Forms.View.Details;
            this.AbsListViewRoad.VirtualMode = true;
            // 
            // absSN
            // 
            this.absSN.AspectName = "";
            this.absSN.HeaderFont = null;
            this.absSN.Text = "序号";
            this.absSN.Width = 80;
            // 
            // absRoadName
            // 
            this.absRoadName.HeaderFont = null;
            this.absRoadName.Text = "道路名称";
            this.absRoadName.Width = 120;
            // 
            // absDistance
            // 
            this.absDistance.HeaderFont = null;
            this.absDistance.Text = "距离(米)";
            // 
            // absSample
            // 
            this.absSample.HeaderFont = null;
            this.absSample.Text = "采样点数";
            // 
            // absCellName
            // 
            this.absCellName.HeaderFont = null;
            this.absCellName.Text = "小区名称";
            this.absCellName.Width = 100;
            // 
            // absCellType
            // 
            this.absCellType.AspectName = "";
            this.absCellType.HeaderFont = null;
            this.absCellType.Text = "小区状态";
            // 
            // absLAC
            // 
            this.absLAC.HeaderFont = null;
            this.absLAC.Text = "LAC";
            // 
            // absCI
            // 
            this.absCI.HeaderFont = null;
            this.absCI.Text = "CI";
            // 
            // absARFCN
            // 
            this.absARFCN.HeaderFont = null;
            this.absARFCN.Text = "ARFCN";
            // 
            // absCPI
            // 
            this.absCPI.HeaderFont = null;
            this.absCPI.Text = "CPI";
            // 
            // absRscp
            // 
            this.absRscp.HeaderFont = null;
            this.absRscp.Text = "PCCPCH_RSCP";
            this.absRscp.Width = 100;
            // 
            // absCellDistance
            // 
            this.absCellDistance.HeaderFont = null;
            this.absCellDistance.Text = "与采样点距离（米）";
            this.absCellDistance.Width = 120;
            // 
            // absMaxRscp
            // 
            this.absMaxRscp.HeaderFont = null;
            this.absMaxRscp.Text = "第一强最大值";
            this.absMaxRscp.Width = 80;
            // 
            // absMinRscp
            // 
            this.absMinRscp.HeaderFont = null;
            this.absMinRscp.Text = "第一强最小值";
            this.absMinRscp.Width = 80;
            // 
            // absAvgRscp
            // 
            this.absAvgRscp.HeaderFont = null;
            this.absAvgRscp.Text = "第一强平均值";
            this.absAvgRscp.Width = 80;
            // 
            // absLongitudeMid
            // 
            this.absLongitudeMid.HeaderFont = null;
            this.absLongitudeMid.Text = "经度";
            this.absLongitudeMid.Width = 80;
            // 
            // absLatitudeMid
            // 
            this.absLatitudeMid.HeaderFont = null;
            this.absLatitudeMid.Text = "纬度";
            this.absLatitudeMid.Width = 80;
            // 
            // absFileName
            // 
            this.absFileName.HeaderFont = null;
            this.absFileName.Text = "文件名";
            // 
            // absFirstTime
            // 
            this.absFirstTime.HeaderFont = null;
            this.absFirstTime.Text = "开始时间";
            this.absFirstTime.Width = 100;
            // 
            // absLastTime
            // 
            this.absLastTime.HeaderFont = null;
            this.absLastTime.Text = "结束时间";
            this.absLastTime.Width = 100;
            // 
            // contextMenuStripAbsolute
            // 
            this.contextMenuStripAbsolute.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportSummaryAbs,
            this.miExportAbs,
            this.miExportCellAbs,
            this.miExpandAbs,
            this.miCollapseAbs});
            this.contextMenuStripAbsolute.Name = "contextMenuStrip2";
            this.contextMenuStripAbsolute.Size = new System.Drawing.Size(190, 114);
            // 
            // miExportSummaryAbs
            // 
            this.miExportSummaryAbs.Name = "miExportSummaryAbs";
            this.miExportSummaryAbs.Size = new System.Drawing.Size(189, 22);
            this.miExportSummaryAbs.Text = "导出概要信息到Excel";
            this.miExportSummaryAbs.Click += new System.EventHandler(this.miExportSummaryAbs_Click);
            // 
            // miExportAbs
            // 
            this.miExportAbs.Name = "miExportAbs";
            this.miExportAbs.Size = new System.Drawing.Size(189, 22);
            this.miExportAbs.Text = "导出详细信息到Excel";
            this.miExportAbs.Click += new System.EventHandler(this.miExportAbs_Click);
            // 
            // miExportCellAbs
            // 
            this.miExportCellAbs.Name = "miExportCellAbs";
            this.miExportCellAbs.Size = new System.Drawing.Size(189, 22);
            this.miExportCellAbs.Text = "导出调整小区到Excel";
            this.miExportCellAbs.Click += new System.EventHandler(this.miExportCellAbs_Click);
            // 
            // miExpandAbs
            // 
            this.miExpandAbs.Name = "miExpandAbs";
            this.miExpandAbs.Size = new System.Drawing.Size(189, 22);
            this.miExpandAbs.Text = "全部展开";
            this.miExpandAbs.Click += new System.EventHandler(this.miExpandAbs_Click);
            // 
            // miCollapseAbs
            // 
            this.miCollapseAbs.Name = "miCollapseAbs";
            this.miCollapseAbs.Size = new System.Drawing.Size(189, 22);
            this.miCollapseAbs.Text = "全部合并";
            this.miCollapseAbs.Click += new System.EventHandler(this.miCollapseAbs_Click);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.relativeBox);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.absoluteBox);
            this.splitContainer1.Size = new System.Drawing.Size(1222, 537);
            this.splitContainer1.SplitterDistance = 206;
            this.splitContainer1.TabIndex = 9;
            // 
            // relativeBox
            // 
            this.relativeBox.Controls.Add(this.RelListViewRoad);
            this.relativeBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.relativeBox.Location = new System.Drawing.Point(0, 0);
            this.relativeBox.Name = "relativeBox";
            this.relativeBox.Size = new System.Drawing.Size(1222, 206);
            this.relativeBox.TabIndex = 0;
            this.relativeBox.TabStop = false;
            this.relativeBox.Text = "相对覆盖路段";
            // 
            // absoluteBox
            // 
            this.absoluteBox.Controls.Add(this.AbsListViewRoad);
            this.absoluteBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.absoluteBox.Location = new System.Drawing.Point(0, 0);
            this.absoluteBox.Name = "absoluteBox";
            this.absoluteBox.Size = new System.Drawing.Size(1222, 327);
            this.absoluteBox.TabIndex = 0;
            this.absoluteBox.TabStop = false;
            this.absoluteBox.Text = "绝对覆盖路段";
            // 
            // absPercent
            // 
            this.absPercent.HeaderFont = null;
            this.absPercent.Text = "绝对覆盖点占比(%)";
            this.absPercent.Width = 70;
            // 
            // ZTLTESanHighCoverateRoadListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1222, 537);
            this.Controls.Add(this.splitContainer1);
            this.Name = "ZTLTESanHighCoverateRoadListForm";
            this.Text = "高重叠覆盖路段";
            this.contextMenuStripRelative.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.RelListViewRoad)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AbsListViewRoad)).EndInit();
            this.contextMenuStripAbsolute.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.relativeBox.ResumeLayout(false);
            this.absoluteBox.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripRelative;
        private System.Windows.Forms.ToolStripMenuItem miExportRel;
        private System.Windows.Forms.ToolStripMenuItem miExpandRel;
        private System.Windows.Forms.ToolStripMenuItem miCollapseRel;
        private BrightIdeasSoftware.TreeListView RelListViewRoad;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnDitance;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTime;
        private System.Windows.Forms.ToolStripMenuItem miExportCellRel;
        private BrightIdeasSoftware.TreeListView AbsListViewRoad;
        private BrightIdeasSoftware.OLVColumn absSN;
        private BrightIdeasSoftware.OLVColumn absRoadName;
        private BrightIdeasSoftware.OLVColumn absDistance;
        private BrightIdeasSoftware.OLVColumn absSample;
        private BrightIdeasSoftware.OLVColumn absCellName;
        private BrightIdeasSoftware.OLVColumn absCellType;
        private BrightIdeasSoftware.OLVColumn absLAC;
        private BrightIdeasSoftware.OLVColumn absCI;
        private BrightIdeasSoftware.OLVColumn absARFCN;
        private BrightIdeasSoftware.OLVColumn absCPI;
        private BrightIdeasSoftware.OLVColumn absRscp;
        private BrightIdeasSoftware.OLVColumn absCellDistance;
        private BrightIdeasSoftware.OLVColumn absMaxRscp;
        private BrightIdeasSoftware.OLVColumn absMinRscp;
        private BrightIdeasSoftware.OLVColumn absAvgRscp;
        private BrightIdeasSoftware.OLVColumn absLongitudeMid;
        private BrightIdeasSoftware.OLVColumn absLatitudeMid;
        private BrightIdeasSoftware.OLVColumn absFileName;
        private BrightIdeasSoftware.OLVColumn absFirstTime;
        private BrightIdeasSoftware.OLVColumn absLastTime;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.GroupBox relativeBox;
        private System.Windows.Forms.GroupBox absoluteBox;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripAbsolute;
        private System.Windows.Forms.ToolStripMenuItem miExportAbs;
        private System.Windows.Forms.ToolStripMenuItem miExportCellAbs;
        private System.Windows.Forms.ToolStripMenuItem miExpandAbs;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAbs;
        private System.Windows.Forms.ToolStripMenuItem miExportSummaryRel;
        private System.Windows.Forms.ToolStripMenuItem miExportSummaryAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnPercent;
        private BrightIdeasSoftware.OLVColumn absPercent;
    }
}