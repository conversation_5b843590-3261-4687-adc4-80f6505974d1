using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class BusyHourSettingForm : Form
    {
        public BusyHourSettingForm()
        {
            InitializeComponent();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (isPermit())
            {
                CloseSimuBusyHour busyHour = new CloseSimuBusyHour((int)numericUpDownBeginBusy.Value, (int)numericUpDownEndBusy.Value);
                listBoxBusyHour.Items.Add(busyHour);
            }
        }

        private bool isPermit()
        {
            if ((int)numericUpDownBeginBusy.Value <= (int)numericUpDownEndBusy.Value)
            {
                CloseSimuBusyHour busyHour = new CloseSimuBusyHour((int)numericUpDownBeginBusy.Value, (int)numericUpDownEndBusy.Value);
                foreach (object item in listBoxBusyHour.Items)
                {
                    CloseSimuBusyHour busyHourCompare = item as CloseSimuBusyHour;
                    if (busyHour.IsIntersect(busyHourCompare))
                    {
                        return false;
                    }
                }
            }
            else
            {
                return false;
            }
            return true;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (listBoxBusyHour.SelectedItem != null && listBoxBusyHour.SelectedItems.Count > 0)
            {
                List<CloseSimuBusyHour> list = new List<CloseSimuBusyHour>();
                foreach (object item in listBoxBusyHour.SelectedItems)
                {
                    CloseSimuBusyHour busyHour = item as CloseSimuBusyHour;
                    list.Add(busyHour);
                }

                foreach (CloseSimuBusyHour busyHour in list)
                {
                    listBoxBusyHour.Items.Remove(busyHour);
                }
            }
        }

        public List<CloseSimuBusyHour> CloseSimuBusyHourPeriods
        {
            get
            {
                List<CloseSimuBusyHour> list = new List<CloseSimuBusyHour>();
                foreach (object item in listBoxBusyHour.Items)
                {
                    CloseSimuBusyHour period = item as CloseSimuBusyHour;
                    list.Add(period);
                }
                return list;
            }
        }

        private void BusyHourSettingForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Visible = false;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}