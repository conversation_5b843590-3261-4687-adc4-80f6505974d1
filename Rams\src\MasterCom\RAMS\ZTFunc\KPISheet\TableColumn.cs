﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class TableColumn
    {
        public TableTemplate Table
        { get; set; }

        public string Name
        { get; set; }

        public string FullName
        {
            get { return string.Format("[{0}].[{1}]", Table.TypeName, Name); }
        }

        public string HashName
        {
            get { return string.Format("[{0}].[{1}]", Table.Name, Name); }
        }

        public TableColumn(TableTemplate table, string name)
        {
            this.Table = table;
            this.Name = name;
        }

        public E_VType ValueType
        {
            get;
            private set;
        }

        public TableColumn(TableTemplate tableTemplate, string colName, Model.Interface.E_VType valueType)
        {
            this.Table = tableTemplate;
            this.Name = colName;
            this.ValueType = valueType;
        }

        public override string ToString()
        {
            if (this.Name == null)
            {
                return string.Empty;
            }
            return Name;
        }

    }
}
