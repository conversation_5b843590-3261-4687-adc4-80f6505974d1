﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TAAnalyseForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TAAnalyseForm));
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnTAInternal = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTAAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistanceAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBadSampleCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBadSampleScale = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(810, 417);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnTAInternal,
            this.gridColumnSampleCount,
            this.gridColumnTAAvg,
            this.gridColumnDistanceAvg,
            this.gridColumnBadSampleCount,
            this.gridColumnBadSampleScale});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsCustomization.AllowGroup = false;
            this.gridView.OptionsDetail.AllowZoomDetail = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnTAInternal
            // 
            this.gridColumnTAInternal.Caption = "TA区间";
            this.gridColumnTAInternal.FieldName = "Range";
            this.gridColumnTAInternal.Name = "gridColumnTAInternal";
            this.gridColumnTAInternal.Visible = true;
            this.gridColumnTAInternal.VisibleIndex = 0;
            // 
            // gridColumnSampleCount
            // 
            this.gridColumnSampleCount.Caption = "采样点数";
            this.gridColumnSampleCount.FieldName = "SampleCount";
            this.gridColumnSampleCount.Name = "gridColumnSampleCount";
            this.gridColumnSampleCount.Visible = true;
            this.gridColumnSampleCount.VisibleIndex = 1;
            // 
            // gridColumnTAAvg
            // 
            this.gridColumnTAAvg.Caption = "TA平均值";
            this.gridColumnTAAvg.FieldName = "TAAvg";
            this.gridColumnTAAvg.Name = "gridColumnTAAvg";
            this.gridColumnTAAvg.Visible = true;
            this.gridColumnTAAvg.VisibleIndex = 2;
            // 
            // gridColumnDistanceAvg
            // 
            this.gridColumnDistanceAvg.Caption = "采样点与服务小区平均距离";
            this.gridColumnDistanceAvg.FieldName = "DistanceAvg";
            this.gridColumnDistanceAvg.Name = "gridColumnDistanceAvg";
            this.gridColumnDistanceAvg.Visible = true;
            this.gridColumnDistanceAvg.VisibleIndex = 3;
            // 
            // gridColumnBadSampleCount
            // 
            this.gridColumnBadSampleCount.Caption = "异常采样点个数";
            this.gridColumnBadSampleCount.FieldName = "BadSampleCount";
            this.gridColumnBadSampleCount.Name = "gridColumnBadSampleCount";
            this.gridColumnBadSampleCount.Visible = true;
            this.gridColumnBadSampleCount.VisibleIndex = 4;
            // 
            // gridColumnBadSampleScale
            // 
            this.gridColumnBadSampleScale.Caption = "异常采样点比例(%)";
            this.gridColumnBadSampleScale.FieldName = "BadSampleScale";
            this.gridColumnBadSampleScale.Name = "gridColumnBadSampleScale";
            this.gridColumnBadSampleScale.Visible = true;
            this.gridColumnBadSampleScale.VisibleIndex = 5;
            // 
            // TAAnalyseForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("TAAnalyseForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(810, 417);
            this.Controls.Add(this.gridControl);
            this.Name = "TAAnalyseForm";
            this.Text = "TA分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTAInternal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistanceAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBadSampleCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBadSampleScale;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTAAvg;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
    }
}