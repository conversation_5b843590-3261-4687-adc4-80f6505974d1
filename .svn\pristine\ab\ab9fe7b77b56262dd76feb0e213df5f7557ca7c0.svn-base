﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage;
using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighReverseFlowCoverage : QueryBase
    {
        /* 1.读取5G,4G工参表
         * 2.对5G工参进行处理
         * 3.保存配置表
         * 4.处理4G工参
         * 5.输出4G,5G结果表
         */

        public HighReverseFlowCoverage()
           : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition { get { return false; } }

        public override string Name
        {
            get { return "4G-5G高倒流共覆盖"; }
        }

        CoverageCondition curCondition = null;
        protected override bool isValidCondition()
        {
            if (curCondition == null)
            {
                curCondition = HighReverseFlowCoverageConfig.Instance.LoadConfig();
                if (!string.IsNullOrEmpty(HighReverseFlowCoverageConfig.Instance.ErrMsg))
                {
                    HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(HighReverseFlowCoverageConfig.Instance.ErrMsg);
                }
            }

            HighReverseFlowCoverageDlg dlg = new HighReverseFlowCoverageDlg();
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                HighReverseFlowCoverageConfig.Instance.SaveConfig(curCondition);
                return true;
            }
            return false;
        }

        Dictionary<int, List<CellParamInfo>> nrCityCellInfos;
        Dictionary<int, List<CellParamInfo>> lteCityCellInfos;
        bool isValid = false;

        protected override void query()
        {
            nrCityCellInfos = new Dictionary<int, List<CellParamInfo>>();
            lteCityCellInfos = new Dictionary<int, List<CellParamInfo>>();
            isValid = false;
            try
            {
                HighReverseFlowCoverageConfig.Instance.WriteLog($"开始分析{Name}数据...", "info");
                WaitBox.Show("开始分析数据...", queryInThread);

                if (isValid)
                {
                    exportExcel(nrCityCellInfos, lteCityCellInfos);
                }
                HighReverseFlowCoverageConfig.Instance.WriteLog("分析完毕...", "info");

                nrCityCellInfos = null;
                lteCityCellInfos = null;
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(ex);
            }
        }

        protected void queryInThread()
        {
            try
            {
                nrCityCellInfos = readNRParams();
                lteCityCellInfos = readLTEParams();
                if (nrCityCellInfos.Count == 0 || lteCityCellInfos.Count == 0)
                {
                    string errMsg = "没有读取到'LTE'或'2.6G NR'工参信息";
                    HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(errMsg);
                    return;
                }

                //读取配置表信息
                DiyQuerySerialNumber query = new DiyQuerySerialNumber();
                query.Query();
                var infoDic = query.InfoDic;
                int maxSerialNumber = query.MaxSerialNumber;
                HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到编号配置[{infoDic.Count}]条,MAXID[{maxSerialNumber}]", "info");

                dealCityNRParams(nrCityCellInfos);

                bool isSuccess = dealSerialNumber(infoDic, maxSerialNumber, nrCityCellInfos);
                if (isSuccess)
                {
                    dealCoverage(nrCityCellInfos, lteCityCellInfos);

                    doAfterDeal(nrCityCellInfos);

                    isValid = insertResult(nrCityCellInfos, lteCityCellInfos);
                }
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(ex);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }

        #region 读取工参
        private Dictionary<int, List<CellParamInfo>> readNRParams()
        {
            WaitBox.Text = "正在读取5G工参...";
            DiyQueryNRCellInfo query = new DiyQueryNRCellInfo();
            query.Query();
            HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到NR工参[{query.TotalCount}]条, 无效数据{query.ErrTotalCount}条", "info");
            return query.CityCellInfosDic;
        }

        private Dictionary<int, List<CellParamInfo>> readLTEParams()
        {
            WaitBox.Text = "正在读取4G工参...";
            DiyQueryLTECellInfo query = new DiyQueryLTECellInfo();
            query.Query();
            HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到LTE工参[{query.TotalCount}]条, 无效数据{query.ErrTotalCount}条", "info");
            return query.CityCellInfosDic;
        }
        #endregion

        #region 处理5G工参共覆盖
        private void dealCityNRParams(Dictionary<int, List<CellParamInfo>> nrCityCellInfos)
        {
            WaitBox.Text = "正在处理5G工参共覆盖...";
            HighReverseFlowCoverageConfig.Instance.WriteLog(WaitBox.Text, "info");

            //把5G 50米内的小区对应关系记录下来
            foreach (var nrCells in nrCityCellInfos.Values)
            {
                dealNRParams(nrCells);
            }
        }

        private void dealNRParams(List<CellParamInfo> nrCells)
        {
            int totalLen = nrCells.Count;
            int len = totalLen - 1;
            for (int i = 0; i < len; i++)
            {
                var curNrCell = nrCells[i];
                if (curNrCell.ParentCell != null)
                {
                    continue;
                }

                for (int j = i + 1; j < totalLen; j++)
                {
                    var compNrCell = nrCells[j];
                    if (compNrCell.ParentCell != null)
                    {
                        continue;
                    }

                    var distance = curNrCell.GetDistance(compNrCell);
                    if (distance < curCondition.Distance)
                    {
                        //子小区关联主小区编号
                        compNrCell.ParentCell = curNrCell;
                    }
                }
            }
        }
        #endregion

        #region 处理编号,以5G工参为基准
        private bool dealSerialNumber(Dictionary<string, SerialNumber> infoDic, int maxSerialNumber
            , Dictionary<int, List<CellParamInfo>> nrCityCellInfos)
        {
            WaitBox.Text = "正在处理工参编号...";
            HighReverseFlowCoverageConfig.Instance.WriteLog(WaitBox.Text, "info");
            List<SerialNumber> addList = new List<SerialNumber>();
            foreach (var cityCells in nrCityCellInfos.Values)
            {
                foreach (var nrCell in cityCells)
                {
                    if (nrCell.ParentCell == null)
                    {
                        //处理没有关联父节点的主小区
                        maxSerialNumber = getMainNodeSerialNumber(infoDic, maxSerialNumber, addList, nrCell);
                    }
                    else
                    {
                        //处理有关联父节点的子小区
                        getSubNodeSerialNumber(infoDic, addList, nrCell);
                    }
                }
            }

            //保存工参编号
            return insertSerialNumber(maxSerialNumber, addList);
        }

        private int getMainNodeSerialNumber(Dictionary<string, SerialNumber> infoDic, int maxSerialNumber
            , List<SerialNumber> addList, CellParamInfo nrCell)
        {
            if (infoDic.TryGetValue(nrCell.Key, out var number))
            {
                //编号表中存在该节点,使用已有编号
                nrCell.Number = number.Number;
            }
            else
            {
                //没有添加新编号
                maxSerialNumber++;
                nrCell.Number = SerialNumberHelper.GetSerialNumber(nrCell, maxSerialNumber);
                var addNumber = new SerialNumber();
                addNumber.FillData(maxSerialNumber, nrCell);
                infoDic.Add(nrCell.Key, addNumber);
                addList.Add(addNumber);
            }

            return maxSerialNumber;
        }

        private void getSubNodeSerialNumber(Dictionary<string, SerialNumber> infoDic
            , List<SerialNumber> addList, CellParamInfo nrCell)
        {
            if (infoDic.TryGetValue(nrCell.ParentCell.Key, out var number))
            {
                //使用父节点编号
                nrCell.Number = number.Number;
                var addNumber = new SerialNumber();
                addNumber.FillData(number.ID, nrCell);
                if (!infoDic.TryGetValue(addNumber.Key, out var _))
                {
                    infoDic.Add(addNumber.Key, addNumber);
                    addList.Add(addNumber);
                }
            }
            else
            {
                //不存在父节点编号为异常现象,理论上不会出现
                HighReverseFlowCoverageConfig.Instance.WriteLog($"不存在{nrCell.ParentCell.Key}", "error");
            }
        }

        private bool insertSerialNumber(int maxSerialNumber, List<SerialNumber> addList)
        {
            int addCount = addList.Count;
            HighReverseFlowCoverageConfig.Instance.WriteLog($"需新增的条数[{addCount}],MAXID[{maxSerialNumber}]", "info");
            if (addCount > 0)
            {
                var init = new DiyInitSerialNumber();
                init.Query();
                bool isSuccess = judgeInsertResult("初始化编号表失败", init.ErrMsg);
                if (!isSuccess)
                {
                    return false;
                }

                var insert = new DiyInsertSerialNumber(addList);
                insert.Query();
                isSuccess = judgeInsertResult("插入编号表失败", init.ErrMsg);
                if (!isSuccess)
                {
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 处理4/5G共覆盖
        private void dealCoverage(Dictionary<int, List<CellParamInfo>> nrCityCellInfos
            , Dictionary<int, List<CellParamInfo>> lteCityCellInfos)
        {
            WaitBox.Text = "正在分析共覆盖情况...";
            HighReverseFlowCoverageConfig.Instance.WriteLog(WaitBox.Text, "info");
            var stopWatch = new System.Diagnostics.Stopwatch();

            GeoHash.SetLengthByDistance(curCondition.Distance);
            var nrCityCellDic = getNrCityCellDic(nrCityCellInfos);
            dealCoverageNumber(lteCityCellInfos, nrCityCellDic);
        }

        private Dictionary<int, Dictionary<string, List<CellParamInfo>>> getNrCityCellDic(
            Dictionary<int, List<CellParamInfo>> nrCityCellInfos)
        {
            var nrCityCellDic = new Dictionary<int, Dictionary<string, List<CellParamInfo>>>();

            //获取编号区域内的5G小区
            foreach (var keyValue in nrCityCellInfos)
            {
                var nrCellDic = new Dictionary<string, List<CellParamInfo>>();
                nrCityCellDic.Add(keyValue.Key, nrCellDic);

                var nrCityCells = keyValue.Value;
                foreach (var cell in nrCityCells)
                {
                    var geoHash = GeoHash.EnCode(cell.Cell.DLatitude, cell.Cell.DLongitude);
                    if (!nrCellDic.TryGetValue(geoHash, out var cellList))
                    {
                        cellList = new List<CellParamInfo>();
                        nrCellDic.Add(geoHash, cellList);
                    }
                    cellList.Add(cell);
                }
            }

            return nrCityCellDic;
        }

        private void dealCoverageNumber(Dictionary<int, List<CellParamInfo>> lteCityCellInfos
            , Dictionary<int, Dictionary<string, List<CellParamInfo>>> nrCityCellDic)
        {
            var lteCityGeoNrCellDic = new Dictionary<int, Dictionary<string, List<CellParamInfo>>>();
            foreach (var keyValue in lteCityCellInfos)
            {
                var city = keyValue.Key;
                if (!nrCityCellDic.TryGetValue(city, out var nrCellDic))
                {
                    continue;
                }

                var lteGeoNrCellDic = new Dictionary<string, List<CellParamInfo>>();
                lteCityGeoNrCellDic.Add(city, lteGeoNrCellDic);

                var lteCityCells = keyValue.Value;
                foreach (var lteCell in lteCityCells)
                {
                    List<CellParamInfo> cellList = getAroundCells(nrCellDic, lteGeoNrCellDic, lteCell);

                    getCoverageNumber(lteCell, cellList);
                }
            }
        }

        private List<CellParamInfo> getAroundCells(Dictionary<string, List<CellParamInfo>> nrCellDic
            , Dictionary<string, List<CellParamInfo>> lteGeoNrCellDic, CellParamInfo lteCell)
        {
            //获取4G周边编号的所有5G小区
            var geoHash = GeoHash.EnCode(lteCell.Cell.DLatitude, lteCell.Cell.DLongitude);
            if (!lteGeoNrCellDic.TryGetValue(geoHash, out var cellList))
            {
                var arroundGeoHashs = GeoHash.GetAroundGeoHash(lteCell.Cell.DLatitude, lteCell.Cell.DLongitude);
                arroundGeoHashs.Add(geoHash);
                cellList = new List<CellParamInfo>();
                foreach (var curGeoHash in arroundGeoHashs)
                {
                    if (nrCellDic.TryGetValue(curGeoHash, out var cells))
                    {
                        cellList.AddRange(cells);
                    }
                }
                lteGeoNrCellDic.Add(geoHash, cellList);
            }

            return cellList;
        }

        private void getCoverageNumber(CellParamInfo lteCell, List<CellParamInfo> cellList)
        {
            if (cellList.Count == 0)
            {
                return;
            }
            double minDistance = 10000;
            var lte = lteCell as LTECellInfo;
            foreach (var nrCell in cellList)
            {
                var distance = nrCell.GetDistance(lteCell);
                minDistance = Math.Min(minDistance, distance);
                if (distance < curCondition.Distance)
                {
                    lte.Number = nrCell.Number;
                    nrCell.IsConnectToLte = true;
                    if (nrCell.ParentCell != null)
                    {
                        nrCell.ParentCell.IsConnectToLte = true;
                    }
                    break;
                }
            }
            lte.Distance = Math.Round(minDistance, 2);
        }
        #endregion

        private void doAfterDeal(Dictionary<int, List<CellParamInfo>> nrCityCellInfos)
        {
            //理论上5G基站覆盖的位置必有4G基站覆盖
            //但是当4G工参经纬度存在异常或为空时，会造成5G编号在4G高倒流表中查询不到
            //这时需要将查询不到的5G编号删除
            foreach (var cells in nrCityCellInfos.Values)
            {
                foreach (var cell in cells)
                {
                    if (!cell.IsConnectToLte && (cell.ParentCell == null || !cell.ParentCell.IsConnectToLte))
                    {
                        cell.Number = "";
                    }
                }
            }
        }

        #region 结果插入数据库
        private bool insertResult(Dictionary<int, List<CellParamInfo>> nrCityCellInfos
            , Dictionary<int, List<CellParamInfo>> lteCityCellInfos)
        {
            bool isNRSuccess = insertNRResult(nrCityCellInfos);
            bool isLTESuccess = insertLTEResult(lteCityCellInfos);
            if (isNRSuccess && isLTESuccess)
            {
                return true;
            }
            return false;
        }

        private bool insertNRResult(Dictionary<int, List<CellParamInfo>> nrCityCellInfos)
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导入5G工参结果...", "info");
            var insert = new DiyInsertNRResultInfo(nrCityCellInfos);
            //insert.Query();

            insert.Bcp(curCondition.DBCond.SqlConnect);

            return judgeInsertResult("插入5G工参结果表失败", insert.ErrMsg);
        }

        private bool insertLTEResult(Dictionary<int, List<CellParamInfo>> lteCityCellInfos)
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导入4G工参结果...", "info");
            var insert = new DiyInsertLTEResultInfo(lteCityCellInfos);
            //insert.Query();
            insert.Bcp(curCondition.DBCond.SqlConnect);

            return judgeInsertResult("插入4G工参结果表失败", insert.ErrMsg);
        }

        private static bool judgeInsertResult(string desc, string errMsg)
        {
            if (!string.IsNullOrEmpty(errMsg))
            {
                HighReverseFlowCoverageConfig.Instance.WriteLog($"{desc}:{errMsg}", "error");
                return false;
            }
            return true;
        }
        #endregion

        #region 结果导出
        private void exportExcel(Dictionary<int, List<CellParamInfo>> nrCityCellInfos
            , Dictionary<int, List<CellParamInfo>> lteCityCellInfos)
        {
            HighReverseFlowCoverageConfig.Instance.WriteLog("正在导出Excel...", "info");
            List<NPOIRow> lteTables = new List<NPOIRow>();
            NPOIRow lteTitleRow = new NPOIRow();
            lteTitleRow.AddCellValue("省份");
            lteTitleRow.AddCellValue("省份ID");
            lteTitleRow.AddCellValue("地市");
            lteTitleRow.AddCellValue("地市ID");
            lteTitleRow.AddCellValue("区域");
            lteTitleRow.AddCellValue("网格");
            lteTitleRow.AddCellValue("基站号");
            lteTitleRow.AddCellValue("小区名");
            lteTitleRow.AddCellValue("覆盖类型");
            lteTitleRow.AddCellValue("TAC");
            lteTitleRow.AddCellValue("ECI");
            lteTitleRow.AddCellValue("TAC16");
            lteTitleRow.AddCellValue("ECI16");
            lteTitleRow.AddCellValue("编号");
            lteTables.Add(lteTitleRow);

            foreach (var cityCells in lteCityCellInfos.Values)
            {
                foreach (var lte in cityCells)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(lte.Province);
                    row.AddCellValue(lte.ProvinceID);
                    row.AddCellValue(lte.City);
                    row.AddCellValue(lte.CityID);
                    row.AddCellValue(lte.Region);
                    row.AddCellValue(lte.Grid);
                    row.AddCellValue(lte.Enodebid);
                    row.AddCellValue(lte.CellName);
                    row.AddCellValue(lte.Type);
                    row.AddCellValue(lte.TAC);
                    row.AddCellValue(lte.CI);
                    row.AddCellValue(lte.TAC16);
                    row.AddCellValue(lte.CI16);
                    row.AddCellValue(lte.Number);
                    lteTables.Add(row);
                }
            }

            List<NPOIRow> nrTables = new List<NPOIRow>();
            NPOIRow nrTitleRow = new NPOIRow();
            nrTitleRow.AddCellValue("省份");
            nrTitleRow.AddCellValue("省份ID");
            nrTitleRow.AddCellValue("地市");
            nrTitleRow.AddCellValue("地市ID");
            nrTitleRow.AddCellValue("区域");
            nrTitleRow.AddCellValue("网格");
            nrTitleRow.AddCellValue("基站号");
            nrTitleRow.AddCellValue("小区名");
            nrTitleRow.AddCellValue("覆盖类型");
            nrTitleRow.AddCellValue("TAC");
            nrTitleRow.AddCellValue("NCI");
            nrTitleRow.AddCellValue("TAC16");
            nrTitleRow.AddCellValue("NCI16");
            nrTitleRow.AddCellValue("编号");
            nrTables.Add(nrTitleRow);
            foreach (var cityCells in nrCityCellInfos.Values)
            {
                foreach (var nr in cityCells)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(nr.Province);
                    row.AddCellValue(nr.ProvinceID);
                    row.AddCellValue(nr.City);
                    row.AddCellValue(nr.CityID);
                    row.AddCellValue(nr.Region);
                    row.AddCellValue(nr.Grid);
                    row.AddCellValue(nr.Enodebid);
                    row.AddCellValue(nr.CellName);
                    row.AddCellValue(nr.Type);
                    row.AddCellValue(nr.TAC);
                    row.AddCellValue(nr.CI);
                    row.AddCellValue(nr.TAC16);
                    row.AddCellValue(nr.CI16);
                    row.AddCellValue(nr.Number);
                    nrTables.Add(row);
                }
            }

            List<List<NPOIRow>> npoiRowsList = new List<List<NPOIRow>>() { lteTables, nrTables };
            List<string> sheetNames = new List<string>() { "4G结果", "5G结果" };

            try
            {
                ExcelNPOIManager.ExportToExcel(npoiRowsList, sheetNames);
            }
            catch (Exception ex)
            {
                HighReverseFlowCoverageConfig.Instance.WriteLogWithMsgBox(ex);
            }
        }
        #endregion
    }
}
