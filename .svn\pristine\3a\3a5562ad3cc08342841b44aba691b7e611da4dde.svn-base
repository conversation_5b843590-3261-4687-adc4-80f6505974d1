﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDeviceManageForm : MinCloseForm
    {
        private Dictionary<string, DeviceManageInfo> changedDeviceInfo = null;
        private bool formClose = true;

        public ZTDeviceManageForm()
        {
            InitializeComponent();
        }

        public void FillData(List<DeviceManageInfo> deviceInfolist)
        {
            changedDeviceInfo = new Dictionary<string, DeviceManageInfo>();
            gridControl1.DataSource = deviceInfolist;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            WaitBox.Show("正在更新设备信息...", UpdateDeviceInfo);
        }

        private void UpdateDeviceInfo()
        {
            //提交修改,更改数据库
            List<DeviceManageInfo> changedDeviceInfolist = new List<DeviceManageInfo>(changedDeviceInfo.Values);
            DiyUpdateDeviceManageInfo query = new DiyUpdateDeviceManageInfo(changedDeviceInfolist);
            query.Query();
        }

        private void gv_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            object row = gv.GetRow(e.RowHandle);
            if (row is DeviceManageInfo)
            {
                //仅保存修改的数据以供更新
                DeviceManageInfo res = row as DeviceManageInfo;
                string boxID = res.BoxID;
                changedDeviceInfo[boxID] = res;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (changedDeviceInfo.Count > 0)
            {
                DialogResult result = MessageBox.Show("确认放弃修改吗?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
                if (result == DialogResult.Cancel)
                {
                    formClose = false;
                }
            }
        }

        private void ZTDeviceManageForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!formClose)
            {
                e.Cancel = true;
                formClose = true;
            }
        }
    }
}
