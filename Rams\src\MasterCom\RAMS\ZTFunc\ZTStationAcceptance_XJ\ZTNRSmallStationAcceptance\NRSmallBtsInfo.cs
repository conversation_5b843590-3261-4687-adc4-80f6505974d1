﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRSmallBtsInfo : BtsInfoBase
    {
        /// <summary>
        /// 由于存在拉远站,使用拉远小区名中的部分作为基站名
        /// </summary>
        public string FileBtsName { get; set; }

        //public NRIndoorBtsServiceInfo BtsInfo { get; set; }

        public NRSmallBtsInfo(NRBTS bts, string fileBtsName)
            : base(bts)
        {
            BtsName = bts.Name;
            BtsID = bts.BTSID;
            FileBtsName = fileBtsName;
        }

        public void Init(NRServiceName serviceType)
        {
            //
        }

        //public override void Calculate()
        //{
        //    base.Calculate();

        //    foreach (var cellInfo in CellInfoList)
        //    {
        //        cellInfo.Calculate();
        //    }
        //}
    }
}
