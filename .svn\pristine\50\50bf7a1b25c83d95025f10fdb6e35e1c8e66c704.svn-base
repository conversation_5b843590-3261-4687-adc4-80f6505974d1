﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ProblemGridQuery
{
    public class ProblemGrid : GridUnitBase
    {
        public ProblemGrid()
        {
            KPIDic = new Dictionary<string, double>();
        }

        public ProblemGrid(Net.Content content):this()
        {
            //txt = "select orderSN,栅格号,中心经度,中心纬度,测试文件 from [KPIMNG_DB_SHANDONG].[dbo].[tb_grid_info]";
            OrderID = content.GetParamInt();
            GridSN = content.GetParamString();
            CenterLngStr = content.GetParamFloat().ToString();
            CenterLatStr = content.GetParamFloat().ToString();
            string[] arr = GridSN.Split('-');
            int colIdx = int.Parse(arr[0]);
            int rowIdx = int.Parse(arr[1]);
            double lng, lat;
            GridHelper.GetLeftTopByCustomSizeGridIndex(1, rowIdx, colIdx, out lng, out lat);
            this.LTLng = lng;
            this.LTLat = lat;
            FileNames = content.GetParamString();
            CellNames = content.GetParamString();
        }

        public ProblemOrder Order
        {
            get;
            set;
        }
        public string AreaName
        { get; set; }
        public string GridSN
        {
            get;
            set;
        }

        public Dictionary<string, double> KPIDic
        {
            get;
            set;
        }

        public int OrderID { get; set; }

        public string CenterLngStr { get; set; }

        public string CenterLatStr { get; set; }

        public string FileNames { get; set; }

        public string CellNames { get; set; }

        private List<ICell> cells = null;
        public List<ICell> Cells {
            get
            {
                if (cells==null)
                {
                    addCells();
                }
                return cells;
            }
        }

        private void addCells()
        {
            cells = new List<ICell>();
            string[] arr = CellNames.Split(',');

            if (Order.ProbType.Contains("GSM"))
            {
                foreach (string name in arr)
                {
                    Cell c = CellManager.GetInstance().GetCellByName(name);
                    if (c != null)
                    {
                        cells.Add(c);
                    }
                }
            }
            else
            {
                foreach (string name in arr)
                {
                    LTECell c = CellManager.GetInstance().GetLTECellLatest(name);
                    if (c != null)
                    {
                        cells.Add(c);
                    }
                }
            }
        }
    }
}
