﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo
{
    public class CellSignalLevInfo
    {
        public ICell Cell
        {
            get;
            protected set;
        }

        float minLev = float.MaxValue;
        public float MinLev
        {
            get { return minLev; }
        }
        float maxLev = float.MinValue;
        public float MaxLev
        {
            get { return maxLev; }
        }
        float sumLev = 0;
        public float AvgLev
        {
            get { return (float)Math.Round(sumLev / TestPointNum, 2); }
        }
        
        public int TestPointNum { get; set; }

        public CellSignalLevInfo(ICell cell,float signalLev)
        {
            this.Cell = cell;
            AddSignalLev(signalLev);
        }

        public void AddSignalLev(float signalLev)
        {
            TestPointNum++;
            minLev = Math.Min(minLev, signalLev);
            maxLev = Math.Max(maxLev, signalLev);
            sumLev += signalLev;
        }

    }
}
