﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class LTEBTSInfoForm : BaseForm
    {
        private LTEBTS bts = null;
        public LTEBTSInfoForm(MainModel mm,LTEBTS bts):base(mm)
        {
            InitializeComponent();
            if (bts!=null)
            {
                this.bts = bts;
                fillBTSInfo(bts);
            }
        }

        private void fillBTSInfo(LTEBTS bts)
        {
            txtName.Text = bts.Name;
            txtType.Text = bts.TypeStringDesc;
            txtLng.Text = bts.Longitude.ToString("F7");
            txtLat.Text = bts.Latitude.ToString("F7");
            txtBTSDesc.Text = bts.Description;
            txtBtsId.Text = bts.BTSID.ToString();
            List<LTECell> cells = new List<LTECell>();
            foreach (LTECell cell in bts.Cells)
            {
                if (MapLTECellLayer.DrawCurrent)
                {
                    if (cell.ValidPeriod.Contains(DateTime.Now.Date))
                    {
                        cells.Add(cell);
                    }
                }
                else
                {
                    if (cell.ValidPeriod.Contains(MapLTECellLayer.CurShowSnapshotTime))
                    {
                        cells.Add(cell);
                    }
                }
            }
            gridCtrlLTE.DataSource = cells;
            gridCtrlLTE.RefreshDataSource();
        }

        private void gridViewLTE_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            LTECell cell = gridViewLTE.GetRow(gridViewLTE.FocusedRowHandle) as LTECell;
            fillCellInfo(cell);
        }

        private void fillCellInfo(LTECell cell)
        {
            if (cell == null)
            {
                txtCellName.Text = "";
                txtID.Text = "";
                txtCode.Text = "";
                txtTAC.Text = "";
                txtECI.Text = "";
                txtPCI.Text = "";
                txtEarfcn.Text = "";
                txtDirection.Text = "";
                txtSectorID.Text = "";
                txtDesc.Text = "";
                txtFreqList.Text = "";
            }
            else
            {
                txtCellName.Text = cell.Name;
                txtID.Text = cell.ID.ToString();
                txtCode.Text = cell.Code;
                txtTAC.Text = cell.TAC.ToString();
                txtECI.Text = cell.ECI.ToString();
                txtPCI.Text = cell.PCI.ToString();
                txtEarfcn.Text = cell.EARFCN.ToString();
                txtDirection.Text = cell.Direction.ToString();
                txtSectorID.Text = cell.SectorID.ToString();
                txtDesc.Text = cell.DESC;
                txtFreqList.Text = cell.FreqListStr;
            }
        }

        private void gridViewLTE_DoubleClick(object sender, EventArgs e)
        {
            locatCell();
        }

        private void btnCellLocation_Click(object sender, EventArgs e)
        {
            locatCell();
        }

        private void locatCell()
        {
            LTECell cell = gridViewLTE.GetRow(gridViewLTE.FocusedRowHandle) as LTECell;
            if (cell != null)
            {
                MainModel.SelectedLTECell = cell;
                MainModel.FireSelectedCellChanged(this);
            }
        }



    }
}
