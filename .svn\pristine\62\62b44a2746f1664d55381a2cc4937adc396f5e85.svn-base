﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    class QueryCqtKpi : QueryKPIStatAll
    {
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21037, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override string Description
        {
            get
            {
                return "全区域/框选区域 CQT地点文件KPI统计分析";
            }
        }
        public override string Name
        {
            get
            {
                return "CQT地点指标统计";
            }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;
        }

        protected CQTKPIReport curSelReport = null;
        protected List<CQTPoint> cqtPoint2Stat = new List<CQTPoint>();
        protected override bool getConditionBeforeQuery()
        {
            CQTKPIDataManager.GetInstance().Clear();
            List<CQTPoint> pnts = getRegionCQTPoints();

             CQTKPIReportCfgManager rptMng = CQTKPIReportCfgManager.GetInstance();
            if (rptMng.Reports.Count == 0)
            {
                rptMng.LoadByDefault();
            }

            CQTKPIStatSettingDlg dlg = new CQTKPIStatSettingDlg();
            dlg.IsStatMainPointOnly = CQTKPIDataManager.GetInstance().IsStatMainPointOnly;
            dlg.FillData(MainModel.SelCQTPoint, pnts, rptMng);
            dlg.ShowDialog();
            if (dlg.DialogResult != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            CQTKPIDataManager.GetInstance().IsStatMainPointOnly = dlg.IsStatMainPointOnly;
            cqtPoint2Stat = dlg.SelCQTPnts;
            curSelReport = dlg.SelReport;
            CQTKPIDataManager.GetInstance().CurStatPoints = cqtPoint2Stat;
            if (cqtPoint2Stat.Count == 0 || curSelReport == null)
            {
                return false;
            }

            return true;
        }

        private List<CQTPoint> getRegionCQTPoints()
        {
            List<CQTPoint> pnts = new List<CQTPoint>();
            if (condition.Geometorys != null && condition.Geometorys.IsSelectRegion())
            {
                pnts = CQTPointManager.GetInstance().GetPointsByRegion(condition.Geometorys.GeoOp);
            }
            return pnts;
        }

        protected override void fireShowResult()
        {
            CQTKPIReportForm frm = MainModel.GetObjectFromBlackboard(typeof(CQTKPIReportForm)) as CQTKPIReportForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new CQTKPIReportForm(MainModel);
            }
            frm.FillData(condition.Periods[0], CQTKPIDataManager.GetInstance(), curSelReport);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        TimePeriod curPeriod = null;
        protected override void queryInThread(object o)
        {
            WaitBox.Text = "开始查询KPI统计数据...";
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;
            try
            {
                string imgTriadIDSet = getStatImgNeededTriadID();
                WaitBox.ProgressPercent = 10;
                foreach (TimePeriod period in condition.Periods)
                {
                    curPeriod = period;
                    queryPeriod(clientProxy, imgTriadIDSet, period);
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error " + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryPeriod(ClientProxy clientProxy, string imgTriadIDSet, TimePeriod period)
        {
            int countTotal = cqtPoint2Stat.Count;
            for (int i = 0; i < cqtPoint2Stat.Count;)
            {
                StringBuilder sb = getCqtPntName(i);
                i += 10;
                if (i > countTotal)
                {
                    i = countTotal;
                }
                if (sb.Length > 0)
                {
                    string fileName = sb.ToString().Substring(0, sb.Length - " or ".Length);
                    int splitorNum = 0;
                    condition.FileName = QueryCondition.MakeFileFilterString(fileName, ref splitorNum);
                    condition.FileNameOrNum = splitorNum;
                    condition.NameFilterType = FileFilterType.ByFileName;
                    WaitBox.Text = string.Format("{0}/{1} 正在统计...", i, countTotal);
                    queryPeriodInfo(period, clientProxy, imgTriadIDSet);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
        }

        private StringBuilder getCqtPntName(int i)
        {
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < 10; j++)
            {
                int p = i + j;
                if (p < cqtPoint2Stat.Count)
                {
                    CQTPoint cqtPnt = cqtPoint2Stat[p];
                    sb.Append(cqtPnt.Name);
                    sb.Append(" or ");
                }
                else
                {
                    break;
                }
            }

            return sb;
        }

        protected override  string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            foreach (CQTKPIReportColumn col in curSelReport.Columns)
            {
                if (!string.IsNullOrEmpty(col.Formula)
                    && !formulaSet.Contains(col.Formula))
                {
                    formulaSet.Add(col.Formula);
                }
            }
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            CQTKPIDataManager.GetInstance().AddKPIStatData(fi, singleStatData, curPeriod);
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, false, null);
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            CQTKPIDataManager.GetInstance().AddKPIStatData(fi, eventData, curPeriod);
        }


    }
}
