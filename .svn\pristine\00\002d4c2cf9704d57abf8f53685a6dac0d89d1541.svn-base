﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ProblemGridQuery
{
    class ProblemOrderGridLayer : LayerBase
    {
        public ProblemOrderGridLayer()
            : base("问题栅格图层")
        {
        }

        public List<ProblemOrder> Orders = new List<ProblemOrder>();
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (Orders == null || Orders.Count == 0)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            this.gisAdapter.FromDisplay(updateRect, out dRect);

            RectangleF? size = null;
            foreach (ProblemOrder order in Orders)
            {
                foreach (ProblemGrid grid in order.Grids)
                {
                    if (grid.Bounds.Within(dRect))
                    {
                        drawGrid(graphics, grid, ref size);
                    }
                }
            }
        }

        private void drawGrid(Graphics graphics, ProblemGrid grid, ref RectangleF? size)
        {
            PointF pointLt;
            this.gisAdapter.ToDisplay(new DbPoint(grid.LTLng, grid.LTLat), out pointLt);
            if (size == null)
            {
                DbPoint brPoint = new DbPoint(grid.BRLng, grid.BRLat);
                PointF pointBr;
                this.gisAdapter.ToDisplay(brPoint, out pointBr);
                size = new RectangleF(pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
            RectangleF rect = new RectangleF(pointLt.X, pointLt.Y, ((RectangleF)size).Width, ((RectangleF)size).Height);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(200, Color.Red)), rect);

            PointF gridCenter = new PointF(rect.X + rect.Width / 2, rect.Y + rect.Height / 2);
            foreach (ICell cell in grid.Cells)
            {
                DbPoint cellPt = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                PointF cellPf;
                gisAdapter.ToDisplay(cellPt, out cellPf);
                graphics.DrawLine(Pens.Blue, cellPf, gridCenter);
            }
            if (grid == this.Grid)
            {
                Pen p = new Pen(Color.Green, 2);
                graphics.DrawRectangle(p, rect.X, rect.Y, rect.Width, rect.Height);
            }
        }

        public ProblemGrid Grid { get; set; }

    }
}
