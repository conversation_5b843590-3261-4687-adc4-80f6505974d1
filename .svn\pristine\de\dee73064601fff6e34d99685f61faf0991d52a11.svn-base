﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class GsmIndoorStationAcceptQuery : GsmStationAcceptQuery
    {
        public GsmIndoorStationAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "GSM室分站验收";
            }
        }

        protected override void analyzeFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            try
            {
                manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            }
            catch (Exception ex)
            {
                this.errEx = ex;
                throw;
            }
        }

        protected override bool isValidCondition()
        {
            string path = string.Empty;
            System.Windows.Forms.FolderBrowserDialog fbd = new System.Windows.Forms.FolderBrowserDialog();
            if (fbd.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                path = fbd.SelectedPath;
            }
            else
            {
                return false;
            }

            manager = new GsmIndoorStationAcceptManager();
            manager.SetAcceptCond(path);
            return true;
        }

        protected override void afterAnalyzeInThread()
        {
            try
            {
                if (this.errEx == null)
                {
                    manager.DoWorkAfterAnalyze();
                    manager = null;
                    this.errEx = null;
                }
            }
            catch (Exception ex)
            {
                this.errEx = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        protected new GsmIndoorStationAcceptManager manager;
    }
}
