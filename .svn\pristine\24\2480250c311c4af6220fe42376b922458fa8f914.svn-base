using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class FindRepeaterForm : Form
    {
        public FindRepeaterForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            if (MapCellLayer.DrawCurrent)
            {
                repeaters = mainModel.CellManager.GetCurrentRepeaters();
            }
            else
            {
                repeaters = mainModel.CellManager.GetRepeaters(MapCellLayer.CurShowTimeAt);
            }
            repeaters.Sort(Repeater.GetCompareByName());
            findRepeater();
            checkControlStatus();
        }

        private void checkBoxName_CheckedChanged(object sender, EventArgs e)
        {
            textBoxName.Enabled = checkBoxName.Checked;
        }

        private void buttonFind_Click(object sender, EventArgs e)
        {
            findRepeater();
        }

        private void listBoxRepeater_SelectedIndexChanged(object sender, EventArgs e)
        {
            checkControlStatus();
        }

        private void buttonInfo_Click(object sender, EventArgs e)
        {
            new RepeaterInfoForm(mainModel, (Repeater)listBoxRepeater.SelectedItem).Show(Owner);
        }

        private void buttonLocation_Click(object sender, EventArgs e)
        {
            mainModel.SelectedRepeater = (Repeater)listBoxRepeater.SelectedItem;
            mainModel.FireSelectedRepeaterChanged(this);
        }

        private void buttonClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void checkControlStatus()
        {
            textBoxName.Enabled = checkBoxName.Checked;
            bool repeaterSelected = listBoxRepeater.SelectedItem != null;
            buttonInfo.Enabled = repeaterSelected;
            buttonLocation.Enabled = repeaterSelected;
        }

        private void findRepeater()
        {
            List<Repeater> list = null;
            if ((checkBoxName.Checked && textBoxName.Text.Trim().Length > 0)
                || !checkBoxBandGSM900.Checked
                || !checkBoxBandDSC1800.Checked)
            {
                findedRepeaters.Clear();
                string name = textBoxName.Text.Trim();
                foreach (Repeater repeater in repeaters)
                {
                    if ((checkBoxName.Checked && name.Length > 0 && repeater.Name.ToUpper().IndexOf(name.ToUpper()) < 0)
                        || (!checkBoxBandGSM900.Checked && repeater.BandType == BTSBandType.GSM900)
                        || (!checkBoxBandDSC1800.Checked && repeater.BandType == BTSBandType.DSC1800))
                    {
                        continue;
                    }
                    findedRepeaters.Add(repeater);
                }
                list = findedRepeaters;
            }
            else
            {
                list = repeaters;
            }
            listBoxRepeater.BeginUpdate();
            listBoxRepeater.DataSource = null;
            listBoxRepeater.DataSource = list;
            listBoxRepeater.DisplayMember = "Name";
            listBoxRepeater.EndUpdate();
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.GroupBox groupBox1;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.GroupBox groupBox3;
            System.Windows.Forms.GroupBox groupBox2;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FindRepeaterForm));
            this.checkBoxBandDSC1800 = new System.Windows.Forms.CheckBox();
            this.checkBoxBandGSM900 = new System.Windows.Forms.CheckBox();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.checkBoxName = new System.Windows.Forms.CheckBox();
            this.listBoxRepeater = new System.Windows.Forms.ListBox();
            this.buttonInfo = new System.Windows.Forms.Button();
            this.buttonLocation = new System.Windows.Forms.Button();
            this.buttonFind = new System.Windows.Forms.Button();
            this.buttonClose = new System.Windows.Forms.Button();
            groupBox1 = new System.Windows.Forms.GroupBox();
            label2 = new System.Windows.Forms.Label();
            groupBox3 = new System.Windows.Forms.GroupBox();
            groupBox2 = new System.Windows.Forms.GroupBox();
            groupBox1.SuspendLayout();
            groupBox3.SuspendLayout();
            groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(this.checkBoxBandDSC1800);
            groupBox1.Controls.Add(this.checkBoxBandGSM900);
            groupBox1.Controls.Add(this.textBoxName);
            groupBox1.Controls.Add(this.checkBoxName);
            groupBox1.Location = new System.Drawing.Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(303, 68);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "Condition";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(4, 46);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(35, 12);
            label2.TabIndex = 2;
            label2.Text = "Band:";
            // 
            // checkBoxBandDSC1800
            // 
            this.checkBoxBandDSC1800.AutoSize = true;
            this.checkBoxBandDSC1800.Checked = true;
            this.checkBoxBandDSC1800.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxBandDSC1800.Location = new System.Drawing.Point(156, 45);
            this.checkBoxBandDSC1800.Name = "checkBoxBandDSC1800";
            this.checkBoxBandDSC1800.Size = new System.Drawing.Size(66, 16);
            this.checkBoxBandDSC1800.TabIndex = 4;
            this.checkBoxBandDSC1800.Text = "&DSC1800";
            this.checkBoxBandDSC1800.UseVisualStyleBackColor = true;
            // 
            // checkBoxBandGSM900
            // 
            this.checkBoxBandGSM900.AutoSize = true;
            this.checkBoxBandGSM900.Checked = true;
            this.checkBoxBandGSM900.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxBandGSM900.Location = new System.Drawing.Point(69, 45);
            this.checkBoxBandGSM900.Name = "checkBoxBandGSM900";
            this.checkBoxBandGSM900.Size = new System.Drawing.Size(60, 16);
            this.checkBoxBandGSM900.TabIndex = 3;
            this.checkBoxBandGSM900.Text = "&GSM900";
            this.checkBoxBandGSM900.UseVisualStyleBackColor = true;
            // 
            // textBoxName
            // 
            this.textBoxName.Location = new System.Drawing.Point(69, 19);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.Size = new System.Drawing.Size(228, 21);
            this.textBoxName.TabIndex = 1;
            // 
            // checkBoxName
            // 
            this.checkBoxName.AutoSize = true;
            this.checkBoxName.Checked = true;
            this.checkBoxName.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxName.Location = new System.Drawing.Point(6, 21);
            this.checkBoxName.Name = "checkBoxName";
            this.checkBoxName.Size = new System.Drawing.Size(54, 16);
            this.checkBoxName.TabIndex = 0;
            this.checkBoxName.Text = "&Name:";
            this.checkBoxName.UseVisualStyleBackColor = true;
            this.checkBoxName.CheckedChanged += new System.EventHandler(this.checkBoxName_CheckedChanged);
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(this.listBoxRepeater);
            groupBox3.Location = new System.Drawing.Point(12, 86);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new System.Drawing.Size(175, 236);
            groupBox3.TabIndex = 1;
            groupBox3.TabStop = false;
            groupBox3.Text = "&Repeater";
            // 
            // listBoxRepeater
            // 
            this.listBoxRepeater.FormattingEnabled = true;
            this.listBoxRepeater.HorizontalScrollbar = true;
            this.listBoxRepeater.ItemHeight = 12;
            this.listBoxRepeater.Location = new System.Drawing.Point(6, 17);
            this.listBoxRepeater.Name = "listBoxRepeater";
            this.listBoxRepeater.Size = new System.Drawing.Size(163, 208);
            this.listBoxRepeater.TabIndex = 0;
            this.listBoxRepeater.SelectedIndexChanged += new System.EventHandler(this.listBoxRepeater_SelectedIndexChanged);
            this.listBoxRepeater.DoubleClick += new System.EventHandler(this.buttonLocation_Click);
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(this.buttonInfo);
            groupBox2.Controls.Add(this.buttonLocation);
            groupBox2.Location = new System.Drawing.Point(194, 86);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new System.Drawing.Size(121, 236);
            groupBox2.TabIndex = 2;
            groupBox2.TabStop = false;
            groupBox2.Text = "Operation";
            // 
            // buttonInfo
            // 
            this.buttonInfo.Location = new System.Drawing.Point(6, 19);
            this.buttonInfo.Name = "buttonInfo";
            this.buttonInfo.Size = new System.Drawing.Size(109, 23);
            this.buttonInfo.TabIndex = 0;
            this.buttonInfo.Text = "&Info...";
            this.buttonInfo.UseVisualStyleBackColor = true;
            this.buttonInfo.Click += new System.EventHandler(this.buttonInfo_Click);
            // 
            // buttonLocation
            // 
            this.buttonLocation.Location = new System.Drawing.Point(6, 48);
            this.buttonLocation.Name = "buttonLocation";
            this.buttonLocation.Size = new System.Drawing.Size(109, 23);
            this.buttonLocation.TabIndex = 1;
            this.buttonLocation.Text = "&Location";
            this.buttonLocation.UseVisualStyleBackColor = true;
            this.buttonLocation.Click += new System.EventHandler(this.buttonLocation_Click);
            // 
            // buttonFind
            // 
            this.buttonFind.Location = new System.Drawing.Point(12, 328);
            this.buttonFind.Name = "buttonFind";
            this.buttonFind.Size = new System.Drawing.Size(75, 23);
            this.buttonFind.TabIndex = 3;
            this.buttonFind.Text = "&Find";
            this.buttonFind.UseVisualStyleBackColor = true;
            this.buttonFind.Click += new System.EventHandler(this.buttonFind_Click);
            // 
            // buttonClose
            // 
            this.buttonClose.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonClose.Location = new System.Drawing.Point(240, 328);
            this.buttonClose.Name = "buttonClose";
            this.buttonClose.Size = new System.Drawing.Size(75, 23);
            this.buttonClose.TabIndex = 4;
            this.buttonClose.Text = "&Close";
            this.buttonClose.UseVisualStyleBackColor = true;
            this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
            // 
            // FindRepeaterForm
            // 
            this.AcceptButton = this.buttonFind;
            this.CancelButton = this.buttonClose;
            this.ClientSize = new System.Drawing.Size(327, 363);
            this.Controls.Add(groupBox2);
            this.Controls.Add(this.buttonFind);
            this.Controls.Add(this.buttonClose);
            this.Controls.Add(groupBox1);
            this.Controls.Add(groupBox3);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FindRepeaterForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Find Repeater";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            groupBox3.ResumeLayout(false);
            groupBox2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        private readonly MainModel mainModel;

        private readonly List<Repeater> repeaters;

        private readonly List<Repeater> findedRepeaters = new List<Repeater>();

        private CheckBox checkBoxName;

        private TextBox textBoxName;

        private CheckBox checkBoxBandGSM900;

        private CheckBox checkBoxBandDSC1800;

        private Button buttonFind;

        private ListBox listBoxRepeater;

        private Button buttonInfo;

        private Button buttonLocation;

        private Button buttonClose;
    }
}
