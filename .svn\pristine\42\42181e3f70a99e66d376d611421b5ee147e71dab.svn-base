﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakQualAnaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripReason = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcelReasons = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShpfile = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem12 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem9 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem10 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem11 = new System.Windows.Forms.ToolStripMenuItem();
            this.miGisShowAllTp = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlQualBurr = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlDetail = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcelCell = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowTestPoints = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridViewCell = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBandNetworkModulus = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.lblCountDetail = new DevExpress.XtraEditors.LabelControl();
            this.CbxFileName = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.gridControlTP = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripTp = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayTestpoint = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayTestpointCompare = new System.Windows.Forms.ToolStripMenuItem();
            this.miLocateLineChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTp = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewTP = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.treeList1 = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumnidx = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnFilename = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnReason = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnTime = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnRelevSub = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnRxqual = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnDistance = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStripReason.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlQualBurr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).BeginInit();
            this.contextMenuStripCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridViewCell)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTP)).BeginInit();
            this.contextMenuStripTp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6});
            this.gridView2.GridControl = this.gridControl1;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ShowDetailButtons = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "质差类型";
            this.gridColumn3.FieldName = "Reason";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "采样点数";
            this.gridColumn4.FieldName = "TpCount";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 1;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "采样点占比";
            this.gridColumn5.FieldName = "WeakQualTpPct";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 2;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "平均RxQualSub";
            this.gridColumn6.FieldName = "AvgQual";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 3;
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStripReason;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.LookAndFeel.SkinName = "Office 2010 Silver";
            this.gridControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.gridControl1.MainView = this.gridView;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(700, 233);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView,
            this.gridView2});
            // 
            // contextMenuStripReason
            // 
            this.contextMenuStripReason.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcelReasons,
            this.miExportShpfile,
            this.miGisShowAllTp});
            this.contextMenuStripReason.Name = "contextMenuStrip";
            this.contextMenuStripReason.Size = new System.Drawing.Size(197, 70);
            // 
            // miExportExcelReasons
            // 
            this.miExportExcelReasons.Name = "miExportExcelReasons";
            this.miExportExcelReasons.Size = new System.Drawing.Size(196, 22);
            this.miExportExcelReasons.Text = "导出Excel";
            this.miExportExcelReasons.Click += new System.EventHandler(this.miExportExcelReasons_Click);
            // 
            // miExportShpfile
            // 
            this.miExportShpfile.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem12,
            this.toolStripSeparator1,
            this.toolStripMenuItem1,
            this.toolStripMenuItem2,
            this.toolStripMenuItem3,
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.toolStripMenuItem6,
            this.toolStripMenuItem7,
            this.toolStripMenuItem8,
            this.toolStripMenuItem9,
            this.toolStripMenuItem10,
            this.toolStripMenuItem11});
            this.miExportShpfile.Name = "miExportShpfile";
            this.miExportShpfile.Size = new System.Drawing.Size(196, 22);
            this.miExportShpfile.Text = "导出Shp文件";
            // 
            // toolStripMenuItem12
            // 
            this.toolStripMenuItem12.Name = "toolStripMenuItem12";
            this.toolStripMenuItem12.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem12.Text = "全部";
            this.toolStripMenuItem12.Click += new System.EventHandler(this.toolStripMenuItem12_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem1.Text = "室分泄漏";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.toolStripMenuItem1_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem2.Text = "占用不合理";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem3.Text = "弱覆盖";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.toolStripMenuItem3_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem4.Text = "重选问题";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.toolStripMenuItem4_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem5.Text = "覆盖杂乱";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.toolStripMenuItem5_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem6.Text = "背向覆盖";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.toolStripMenuItem6_Click);
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem7.Text = "切换不合理";
            this.toolStripMenuItem7.Click += new System.EventHandler(this.toolStripMenuItem7_Click);
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem8.Text = "切换不及时";
            this.toolStripMenuItem8.Click += new System.EventHandler(this.toolStripMenuItem8_Click);
            // 
            // toolStripMenuItem9
            // 
            this.toolStripMenuItem9.Name = "toolStripMenuItem9";
            this.toolStripMenuItem9.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem9.Text = "质量毛刺";
            this.toolStripMenuItem9.Click += new System.EventHandler(this.toolStripMenuItem9_Click);
            // 
            // toolStripMenuItem10
            // 
            this.toolStripMenuItem10.Name = "toolStripMenuItem10";
            this.toolStripMenuItem10.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem10.Text = "频率干扰或故障";
            this.toolStripMenuItem10.Click += new System.EventHandler(this.toolStripMenuItem10_Click);
            // 
            // toolStripMenuItem11
            // 
            this.toolStripMenuItem11.Name = "toolStripMenuItem11";
            this.toolStripMenuItem11.Size = new System.Drawing.Size(160, 22);
            this.toolStripMenuItem11.Text = "其它";
            this.toolStripMenuItem11.Click += new System.EventHandler(this.toolStripMenuItem11_Click);
            // 
            // miGisShowAllTp
            // 
            this.miGisShowAllTp.Name = "miGisShowAllTp";
            this.miGisShowAllTp.Size = new System.Drawing.Size(196, 22);
            this.miGisShowAllTp.Text = "地图显示全网的质差点";
            this.miGisShowAllTp.Click += new System.EventHandler(this.miGisShowAllTp_Click);
            // 
            // gridView
            // 
            this.gridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView.Appearance.OddRow.BackColor = System.Drawing.Color.AliceBlue;
            this.gridView.Appearance.OddRow.Options.UseBackColor = true;
            this.gridView.Appearance.Row.Options.UseTextOptions = true;
            this.gridView.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn18,
            this.gridColumn1,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9});
            this.gridView.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView.GridControl = this.gridControl1;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsCustomization.AllowGroup = false;
            this.gridView.OptionsCustomization.AllowSort = false;
            this.gridView.OptionsDetail.ShowDetailTabs = false;
            this.gridView.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gridView.OptionsFilter.AllowFilterEditor = false;
            this.gridView.OptionsFilter.AllowMRUFilterList = false;
            this.gridView.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gridView.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gridView.OptionsMenu.EnableColumnMenu = false;
            this.gridView.OptionsView.AllowCellMerge = true;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.OptionsView.ShowPreviewLines = false;
            this.gridView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "网格类型";
            this.gridColumn2.FieldName = "RegionType";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            this.gridColumn2.Width = 127;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "总采样点数";
            this.gridColumn18.FieldName = "TpAllCount";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 1;
            this.gridColumn18.Width = 127;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "质差类型";
            this.gridColumn1.FieldName = "Reason";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 2;
            this.gridColumn1.Width = 127;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "质差采样点数";
            this.gridColumn7.FieldName = "TpCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 3;
            this.gridColumn7.Width = 127;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "质差采样点占比";
            this.gridColumn8.FieldName = "WeakQualTpPct";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 4;
            this.gridColumn8.Width = 127;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "平均质量";
            this.gridColumn9.FieldName = "AvgQual";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 5;
            this.gridColumn9.Width = 127;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.LookAndFeel.SkinName = "Office 2010 Silver";
            this.xtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(712, 478);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.splitContainerControl1);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(704, 448);
            this.xtraTabPage1.Text = "原因分析概况";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Appearance.BackColor = System.Drawing.Color.Black;
            this.splitContainerControl1.Appearance.Options.UseBackColor = true;
            this.splitContainerControl1.AppearanceCaption.Options.UseBorderColor = true;
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControl1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlQualBurr);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(704, 448);
            this.splitContainerControl1.SplitterPosition = 233;
            this.splitContainerControl1.TabIndex = 3;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlQualBurr
            // 
            this.gridControlQualBurr.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlQualBurr.Location = new System.Drawing.Point(0, 0);
            this.gridControlQualBurr.LookAndFeel.SkinName = "Office 2010 Silver";
            this.gridControlQualBurr.LookAndFeel.UseDefaultLookAndFeel = false;
            this.gridControlQualBurr.MainView = this.bandedGridView2;
            this.gridControlQualBurr.Name = "gridControlQualBurr";
            this.gridControlQualBurr.Size = new System.Drawing.Size(700, 205);
            this.gridControlQualBurr.TabIndex = 2;
            this.gridControlQualBurr.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView2});
            // 
            // bandedGridView2
            // 
            this.bandedGridView2.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView2.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView2.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView2.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand8,
            this.gridBand4,
            this.gridBand5,
            this.gridBand6,
            this.gridBand7});
            this.bandedGridView2.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn24,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn22,
            this.bandedGridColumn23});
            this.bandedGridView2.GridControl = this.gridControlQualBurr;
            this.bandedGridView2.GroupPanelText = "质量毛刺详细";
            this.bandedGridView2.Name = "bandedGridView2";
            this.bandedGridView2.OptionsBehavior.Editable = false;
            this.bandedGridView2.OptionsCustomization.AllowGroup = false;
            this.bandedGridView2.OptionsCustomization.AllowSort = false;
            this.bandedGridView2.OptionsDetail.ShowDetailTabs = false;
            this.bandedGridView2.OptionsFilter.AllowColumnMRUFilterList = false;
            this.bandedGridView2.OptionsFilter.AllowFilterEditor = false;
            this.bandedGridView2.OptionsFilter.AllowMRUFilterList = false;
            this.bandedGridView2.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.bandedGridView2.OptionsFilter.UseNewCustomFilterDialog = true;
            this.bandedGridView2.OptionsMenu.EnableColumnMenu = false;
            this.bandedGridView2.OptionsView.AllowCellMerge = true;
            this.bandedGridView2.OptionsView.ShowDetailButtons = false;
            this.bandedGridView2.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.bandedGridView2.OptionsView.ShowPreviewLines = false;
            // 
            // gridBand8
            // 
            this.gridBand8.Caption = "网格";
            this.gridBand8.Columns.Add(this.bandedGridColumn17);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 102;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.AutoFillDown = true;
            this.bandedGridColumn17.Caption = "网格";
            this.bandedGridColumn17.FieldName = "RegionType";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            this.bandedGridColumn17.Width = 102;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "设备";
            this.gridBand4.Columns.Add(this.bandedGridColumn24);
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 208;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.AutoFillDown = true;
            this.bandedGridColumn24.Caption = "设备";
            this.bandedGridColumn24.FieldName = "EquipmentName";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            this.bandedGridColumn24.Width = 208;
            // 
            // gridBand5
            // 
            this.gridBand5.Caption = "切换";
            this.gridBand5.Columns.Add(this.bandedGridColumn18);
            this.gridBand5.Columns.Add(this.bandedGridColumn19);
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 118;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "切换";
            this.bandedGridColumn18.FieldName = "IsHandover";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            this.bandedGridColumn18.Width = 58;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "无切换";
            this.bandedGridColumn19.FieldName = "IsNotHandover";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            this.bandedGridColumn19.Width = 60;
            // 
            // gridBand6
            // 
            this.gridBand6.Caption = "频段";
            this.gridBand6.Columns.Add(this.bandedGridColumn20);
            this.gridBand6.Columns.Add(this.bandedGridColumn21);
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 118;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "900";
            this.bandedGridColumn20.FieldName = "Is900";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            this.bandedGridColumn20.Width = 56;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "1800";
            this.bandedGridColumn21.FieldName = "Is1800";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            this.bandedGridColumn21.Width = 62;
            // 
            // gridBand7
            // 
            this.gridBand7.Caption = "覆盖";
            this.gridBand7.Columns.Add(this.bandedGridColumn22);
            this.gridBand7.Columns.Add(this.bandedGridColumn23);
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 136;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "覆盖快衰";
            this.bandedGridColumn22.FieldName = "IsFastFailure";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.Visible = true;
            this.bandedGridColumn22.Width = 68;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "其他";
            this.bandedGridColumn23.FieldName = "IsOther";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.Visible = true;
            this.bandedGridColumn23.Width = 68;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.splitContainerControl2);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(704, 448);
            this.xtraTabPage2.Text = "问题详情";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControlDetail);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.splitContainer1);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(704, 448);
            this.splitContainerControl2.SplitterPosition = 255;
            this.splitContainerControl2.TabIndex = 1;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridControlDetail
            // 
            this.gridControlDetail.ContextMenuStrip = this.contextMenuStripCell;
            this.gridControlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDetail.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlDetail.Location = new System.Drawing.Point(0, 0);
            this.gridControlDetail.LookAndFeel.SkinName = "Office 2010 Silver";
            this.gridControlDetail.LookAndFeel.UseDefaultLookAndFeel = false;
            this.gridControlDetail.MainView = this.bandedGridViewCell;
            this.gridControlDetail.Name = "gridControlDetail";
            this.gridControlDetail.Size = new System.Drawing.Size(700, 255);
            this.gridControlDetail.TabIndex = 0;
            this.gridControlDetail.UseEmbeddedNavigator = true;
            this.gridControlDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridViewCell});
            // 
            // contextMenuStripCell
            // 
            this.contextMenuStripCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcelCell,
            this.miShowTestPoints});
            this.contextMenuStripCell.Name = "contextMenuStripCell";
            this.contextMenuStripCell.Size = new System.Drawing.Size(245, 48);
            // 
            // miExportExcelCell
            // 
            this.miExportExcelCell.Name = "miExportExcelCell";
            this.miExportExcelCell.Size = new System.Drawing.Size(244, 22);
            this.miExportExcelCell.Text = "导出Excel";
            this.miExportExcelCell.Click += new System.EventHandler(this.miExportExcelCell_Click);
            // 
            // miShowTestPoints
            // 
            this.miShowTestPoints.Name = "miShowTestPoints";
            this.miShowTestPoints.Size = new System.Drawing.Size(244, 22);
            this.miShowTestPoints.Text = "地图显示选择小区的质差采样点";
            this.miShowTestPoints.Click += new System.EventHandler(this.miShowTestPoints_Click);
            // 
            // bandedGridViewCell
            // 
            this.bandedGridViewCell.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridViewCell.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridViewCell.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridViewCell.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridViewCell.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridViewCell.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridViewCell.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridViewCell.Appearance.SelectedRow.BackColor = System.Drawing.Color.AliceBlue;
            this.bandedGridViewCell.Appearance.SelectedRow.Options.UseBackColor = true;
            this.bandedGridViewCell.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand10,
            this.gridBand9,
            this.gridBand1,
            this.gridBand2,
            this.gridBand3,
            this.gridBandNetworkModulus});
            this.bandedGridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn33,
            this.bandedGridColumn16,
            this.bandedGridColumn25,
            this.gridColumn10,
            this.gridColumn11,
            this.bandedGridColumn34,
            this.bandedGridColumn35,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn26,
            this.bandedGridColumn11,
            this.bandedGridColumn13,
            this.bandedGridColumn12,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32});
            this.bandedGridViewCell.GridControl = this.gridControlDetail;
            this.bandedGridViewCell.Name = "bandedGridViewCell";
            this.bandedGridViewCell.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.bandedGridViewCell.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.bandedGridViewCell.OptionsBehavior.Editable = false;
            this.bandedGridViewCell.OptionsCustomization.AllowBandMoving = false;
            this.bandedGridViewCell.OptionsCustomization.AllowBandResizing = false;
            this.bandedGridViewCell.OptionsCustomization.AllowGroup = false;
            this.bandedGridViewCell.OptionsFilter.AllowColumnMRUFilterList = false;
            this.bandedGridViewCell.OptionsFilter.AllowFilterEditor = false;
            this.bandedGridViewCell.OptionsFilter.AllowMRUFilterList = false;
            this.bandedGridViewCell.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.bandedGridViewCell.OptionsFilter.UseNewCustomFilterDialog = true;
            this.bandedGridViewCell.OptionsSelection.MultiSelect = true;
            this.bandedGridViewCell.OptionsView.ColumnAutoWidth = false;
            this.bandedGridViewCell.OptionsView.EnableAppearanceEvenRow = true;
            this.bandedGridViewCell.OptionsView.EnableAppearanceOddRow = true;
            this.bandedGridViewCell.OptionsView.ShowGroupPanel = false;
            this.bandedGridViewCell.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.bandedGridView1_FocusedRowChanged);
            this.bandedGridViewCell.DoubleClick += new System.EventHandler(this.bandedGridView1_DoubleClick);
            // 
            // gridBand10
            // 
            this.gridBand10.Columns.Add(this.bandedGridColumn33);
            this.gridBand10.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 50;
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "序号";
            this.bandedGridColumn33.FieldName = "SN";
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            this.bandedGridColumn33.Visible = true;
            this.bandedGridColumn33.Width = 50;
            // 
            // gridBand9
            // 
            this.gridBand9.Caption = "小区信息";
            this.gridBand9.Columns.Add(this.bandedGridColumn16);
            this.gridBand9.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 150;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "小区名";
            this.bandedGridColumn16.FieldName = "CellName";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            this.bandedGridColumn16.Width = 150;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "基本信息";
            this.gridBand1.Columns.Add(this.gridColumn10);
            this.gridBand1.Columns.Add(this.gridColumn11);
            this.gridBand1.Columns.Add(this.bandedGridColumn34);
            this.gridBand1.Columns.Add(this.bandedGridColumn35);
            this.gridBand1.Columns.Add(this.bandedGridColumn25);
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.MinWidth = 20;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 394;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "LAC";
            this.gridColumn10.FieldName = "Lac";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 50;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "CI";
            this.gridColumn11.FieldName = "Ci";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 50;
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "经度";
            this.bandedGridColumn34.FieldName = "Longitude";
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            this.bandedGridColumn34.Visible = true;
            this.bandedGridColumn34.Width = 70;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "纬度";
            this.bandedGridColumn35.FieldName = "Latitude";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.Visible = true;
            this.bandedGridColumn35.Width = 70;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "网格";
            this.bandedGridColumn25.FieldName = "RegionType";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            this.bandedGridColumn25.Width = 54;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "问题路段";
            this.bandedGridColumn1.FieldName = "ProblemRoad";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            this.bandedGridColumn1.Width = 100;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "采样点信息";
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.bandedGridColumn5);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 305;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "采样点数";
            this.bandedGridColumn2.FieldName = "TpCountAll";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            this.bandedGridColumn2.Width = 70;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "质差点数";
            this.bandedGridColumn3.FieldName = "WeakQualTpCount";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            this.bandedGridColumn3.Width = 70;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "质量(0-4)占比";
            this.bandedGridColumn4.FieldName = "RxQualSub0_4Percent";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            this.bandedGridColumn4.Width = 95;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "覆盖距离";
            this.bandedGridColumn5.FieldName = "AvgTpDistance";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            this.bandedGridColumn5.Width = 70;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "质差原因归类";
            this.gridBand3.Columns.Add(this.bandedGridColumn6);
            this.gridBand3.Columns.Add(this.bandedGridColumn7);
            this.gridBand3.Columns.Add(this.bandedGridColumn8);
            this.gridBand3.Columns.Add(this.bandedGridColumn9);
            this.gridBand3.Columns.Add(this.bandedGridColumn10);
            this.gridBand3.Columns.Add(this.bandedGridColumn26);
            this.gridBand3.Columns.Add(this.bandedGridColumn11);
            this.gridBand3.Columns.Add(this.bandedGridColumn13);
            this.gridBand3.Columns.Add(this.bandedGridColumn12);
            this.gridBand3.Columns.Add(this.bandedGridColumn14);
            this.gridBand3.Columns.Add(this.bandedGridColumn15);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 815;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "室分泄漏";
            this.bandedGridColumn6.FieldName = "Indoor";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            this.bandedGridColumn6.Width = 70;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "占用不合理";
            this.bandedGridColumn7.FieldName = "CoverLap";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            this.bandedGridColumn7.Width = 80;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "弱覆盖";
            this.bandedGridColumn8.FieldName = "WeakCover";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            this.bandedGridColumn8.Width = 60;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "覆盖杂乱";
            this.bandedGridColumn9.FieldName = "NoMainCell";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            this.bandedGridColumn9.Width = 70;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "背向覆盖";
            this.bandedGridColumn10.FieldName = "BackCover";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            this.bandedGridColumn10.Width = 70;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "重选问题";
            this.bandedGridColumn26.FieldName = "ReselectProblem";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            this.bandedGridColumn26.Width = 70;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "切换不合理";
            this.bandedGridColumn11.FieldName = "HandoverProblem";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            this.bandedGridColumn11.Width = 80;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "切换不及时";
            this.bandedGridColumn13.FieldName = "HandoverNotInTime";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            this.bandedGridColumn13.Width = 80;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "频率干扰或故障";
            this.bandedGridColumn12.FieldName = "Interfere_C_I";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            this.bandedGridColumn12.Width = 105;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "质量毛剌";
            this.bandedGridColumn14.FieldName = "QualBurr";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            this.bandedGridColumn14.Width = 70;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "其它";
            this.bandedGridColumn15.FieldName = "Other";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            this.bandedGridColumn15.Width = 60;
            // 
            // gridBandNetworkModulus
            // 
            this.gridBandNetworkModulus.Caption = "网络评估系数";
            this.gridBandNetworkModulus.Columns.Add(this.bandedGridColumn27);
            this.gridBandNetworkModulus.Columns.Add(this.bandedGridColumn28);
            this.gridBandNetworkModulus.Columns.Add(this.bandedGridColumn29);
            this.gridBandNetworkModulus.Columns.Add(this.bandedGridColumn30);
            this.gridBandNetworkModulus.Columns.Add(this.bandedGridColumn31);
            this.gridBandNetworkModulus.Columns.Add(this.bandedGridColumn32);
            this.gridBandNetworkModulus.Name = "gridBandNetworkModulus";
            this.gridBandNetworkModulus.Width = 492;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "干扰系数";
            this.bandedGridColumn27.FieldName = "InterferenceFactor";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            this.bandedGridColumn27.Width = 78;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "网络结构指数";
            this.bandedGridColumn28.FieldName = "NetworkStructureIndex";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.Visible = true;
            this.bandedGridColumn28.Width = 90;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "冗余覆盖指数";
            this.bandedGridColumn29.FieldName = "RedundantCoverageIndex";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            this.bandedGridColumn29.Visible = true;
            this.bandedGridColumn29.Width = 90;
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "重叠覆盖度";
            this.bandedGridColumn30.FieldName = "MutilCovIndex";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            this.bandedGridColumn30.Visible = true;
            this.bandedGridColumn30.Width = 78;
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "干扰源系数";
            this.bandedGridColumn31.FieldName = "InterferenceSourcesFactor";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            this.bandedGridColumn31.Visible = true;
            this.bandedGridColumn31.Width = 78;
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "过覆盖系数";
            this.bandedGridColumn32.FieldName = "OverlapCovIndex";
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            this.bandedGridColumn32.Visible = true;
            this.bandedGridColumn32.Width = 78;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.lblCountDetail);
            this.splitContainer1.Panel1.Controls.Add(this.CbxFileName);
            this.splitContainer1.Panel1.Controls.Add(this.label1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.gridControlTP);
            this.splitContainer1.Panel2.Controls.Add(this.treeList1);
            this.splitContainer1.Size = new System.Drawing.Size(700, 183);
            this.splitContainer1.SplitterDistance = 25;
            this.splitContainer1.TabIndex = 0;
            // 
            // lblCountDetail
            // 
            this.lblCountDetail.Location = new System.Drawing.Point(487, 8);
            this.lblCountDetail.Name = "lblCountDetail";
            this.lblCountDetail.Size = new System.Drawing.Size(212, 14);
            this.lblCountDetail.TabIndex = 3;
            this.lblCountDetail.Text = "文件共 个，当前文件 条记录（质差点）";
            // 
            // CbxFileName
            // 
            this.CbxFileName.FormattingEnabled = true;
            this.CbxFileName.Location = new System.Drawing.Point(79, 1);
            this.CbxFileName.Name = "CbxFileName";
            this.CbxFileName.Size = new System.Drawing.Size(357, 22);
            this.CbxFileName.TabIndex = 1;
            this.CbxFileName.SelectedIndexChanged += new System.EventHandler(this.CbxFileName_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(13, 5);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "文件列表：";
            // 
            // gridControlTP
            // 
            this.gridControlTP.ContextMenuStrip = this.contextMenuStripTp;
            this.gridControlTP.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTP.Location = new System.Drawing.Point(0, 0);
            this.gridControlTP.LookAndFeel.SkinName = "Office 2010 Silver";
            this.gridControlTP.LookAndFeel.UseDefaultLookAndFeel = false;
            this.gridControlTP.MainView = this.gridViewTP;
            this.gridControlTP.Name = "gridControlTP";
            this.gridControlTP.Size = new System.Drawing.Size(700, 154);
            this.gridControlTP.TabIndex = 4;
            this.gridControlTP.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTP});
            // 
            // contextMenuStripTp
            // 
            this.contextMenuStripTp.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayTestpoint,
            this.miReplayTestpointCompare,
            this.miLocateLineChart,
            this.miExportTp});
            this.contextMenuStripTp.Name = "contextMenuStripTp";
            this.contextMenuStripTp.Size = new System.Drawing.Size(149, 92);
            // 
            // miReplayTestpoint
            // 
            this.miReplayTestpoint.Image = global::MasterCom.RAMS.Properties.Resources.replay;
            this.miReplayTestpoint.Name = "miReplayTestpoint";
            this.miReplayTestpoint.Size = new System.Drawing.Size(148, 22);
            this.miReplayTestpoint.Text = "回放采样点";
            this.miReplayTestpoint.Click += new System.EventHandler(this.miReplayTestpoint_Click);
            // 
            // miReplayTestpointCompare
            // 
            this.miReplayTestpointCompare.Name = "miReplayTestpointCompare";
            this.miReplayTestpointCompare.Size = new System.Drawing.Size(148, 22);
            this.miReplayTestpointCompare.Text = "对比回放文件";
            this.miReplayTestpointCompare.Click += new System.EventHandler(this.miReplayTestpointCompare_Click);
            // 
            // miLocateLineChart
            // 
            this.miLocateLineChart.Name = "miLocateLineChart";
            this.miLocateLineChart.Size = new System.Drawing.Size(148, 22);
            this.miLocateLineChart.Text = "定位时序图";
            this.miLocateLineChart.Click += new System.EventHandler(this.miLocateLineChart_Click);
            // 
            // miExportTp
            // 
            this.miExportTp.Name = "miExportTp";
            this.miExportTp.Size = new System.Drawing.Size(148, 22);
            this.miExportTp.Text = "导出Excel";
            this.miExportTp.Click += new System.EventHandler(this.miExportTp_Click);
            // 
            // gridViewTP
            // 
            this.gridViewTP.Appearance.FocusedRow.BackColor = System.Drawing.Color.LightBlue;
            this.gridViewTP.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridViewTP.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewTP.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewTP.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewTP.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.gridViewTP.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17});
            this.gridViewTP.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridViewTP.GridControl = this.gridControlTP;
            this.gridViewTP.Name = "gridViewTP";
            this.gridViewTP.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewTP.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewTP.OptionsBehavior.Editable = false;
            this.gridViewTP.OptionsCustomization.AllowColumnMoving = false;
            this.gridViewTP.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gridViewTP.OptionsFilter.AllowFilterEditor = false;
            this.gridViewTP.OptionsFilter.AllowMRUFilterList = false;
            this.gridViewTP.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "序号";
            this.gridColumn12.FieldName = "Sn";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 0;
            this.gridColumn12.Width = 50;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "质差原因";
            this.gridColumn13.FieldName = "Reason";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 1;
            this.gridColumn13.Width = 124;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "采样点时间";
            this.gridColumn14.FieldName = "Time";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 2;
            this.gridColumn14.Width = 124;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "电平";
            this.gridColumn15.FieldName = "RxLevSub";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 3;
            this.gridColumn15.Width = 124;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "质量";
            this.gridColumn16.FieldName = "RxQualSub";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 4;
            this.gridColumn16.Width = 124;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "覆盖距离";
            this.gridColumn17.FieldName = "CovDistance";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 5;
            this.gridColumn17.Width = 136;
            // 
            // treeList1
            // 
            this.treeList1.Appearance.SelectedRow.BackColor = System.Drawing.Color.AliceBlue;
            this.treeList1.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumnidx,
            this.treeListColumnFilename,
            this.treeListColumnReason,
            this.treeListColumnTime,
            this.treeListColumnRelevSub,
            this.treeListColumnRxqual,
            this.treeListColumnDistance});
            this.treeList1.ContextMenuStrip = this.contextMenuStripTp;
            this.treeList1.CustomizationFormBounds = new System.Drawing.Rectangle(481, 387, 216, 187);
            this.treeList1.Location = new System.Drawing.Point(0, 0);
            this.treeList1.LookAndFeel.SkinName = "Office 2010 Silver";
            this.treeList1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeList1.Name = "treeList1";
            this.treeList1.OptionsBehavior.Editable = false;
            this.treeList1.Size = new System.Drawing.Size(679, 36);
            this.treeList1.TabIndex = 3;
            // 
            // treeListColumnidx
            // 
            this.treeListColumnidx.Caption = "序号";
            this.treeListColumnidx.FieldName = "Sn";
            this.treeListColumnidx.Name = "treeListColumnidx";
            this.treeListColumnidx.Visible = true;
            this.treeListColumnidx.VisibleIndex = 0;
            this.treeListColumnidx.Width = 51;
            // 
            // treeListColumnFilename
            // 
            this.treeListColumnFilename.Caption = "文件名";
            this.treeListColumnFilename.FieldName = "文件名";
            this.treeListColumnFilename.Name = "treeListColumnFilename";
            this.treeListColumnFilename.Width = 139;
            // 
            // treeListColumnReason
            // 
            this.treeListColumnReason.Caption = "质差原因";
            this.treeListColumnReason.FieldName = "质差原因";
            this.treeListColumnReason.Name = "treeListColumnReason";
            this.treeListColumnReason.Visible = true;
            this.treeListColumnReason.VisibleIndex = 1;
            this.treeListColumnReason.Width = 96;
            // 
            // treeListColumnTime
            // 
            this.treeListColumnTime.Caption = "采样点时间";
            this.treeListColumnTime.FieldName = "采样点时间";
            this.treeListColumnTime.Name = "treeListColumnTime";
            this.treeListColumnTime.Visible = true;
            this.treeListColumnTime.VisibleIndex = 2;
            this.treeListColumnTime.Width = 129;
            // 
            // treeListColumnRelevSub
            // 
            this.treeListColumnRelevSub.Caption = "电平";
            this.treeListColumnRelevSub.FieldName = "电平";
            this.treeListColumnRelevSub.Name = "treeListColumnRelevSub";
            this.treeListColumnRelevSub.Visible = true;
            this.treeListColumnRelevSub.VisibleIndex = 3;
            this.treeListColumnRelevSub.Width = 126;
            // 
            // treeListColumnRxqual
            // 
            this.treeListColumnRxqual.Caption = "质量";
            this.treeListColumnRxqual.FieldName = "质量";
            this.treeListColumnRxqual.Name = "treeListColumnRxqual";
            this.treeListColumnRxqual.Visible = true;
            this.treeListColumnRxqual.VisibleIndex = 4;
            this.treeListColumnRxqual.Width = 135;
            // 
            // treeListColumnDistance
            // 
            this.treeListColumnDistance.Caption = "覆盖距离";
            this.treeListColumnDistance.FieldName = "覆盖距离";
            this.treeListColumnDistance.Name = "treeListColumnDistance";
            this.treeListColumnDistance.Visible = true;
            this.treeListColumnDistance.VisibleIndex = 5;
            this.treeListColumnDistance.Width = 145;
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(157, 6);
            // 
            // WeakQualAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(712, 478);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "WeakQualAnaForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "质差原因";
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStripReason.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlQualBurr)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).EndInit();
            this.contextMenuStripCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridViewCell)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel1.PerformLayout();
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTP)).EndInit();
            this.contextMenuStripTp.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlDetail;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridViewCell;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.GridControl gridControlQualBurr;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripReason;
        private System.Windows.Forms.ToolStripMenuItem miExportExcelReasons;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripTp;
        private System.Windows.Forms.ToolStripMenuItem miReplayTestpoint;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripCell;
        private System.Windows.Forms.ToolStripMenuItem miExportExcelCell;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private DevExpress.XtraTreeList.TreeList treeList1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnFilename;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnTime;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnReason;
        private System.Windows.Forms.ComboBox CbxFileName;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnRelevSub;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnRxqual;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnidx;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnDistance;
        private System.Windows.Forms.ToolStripMenuItem miShowTestPoints;
        private DevExpress.XtraGrid.GridControl gridControlTP;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTP;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraEditors.LabelControl lblCountDetail;
        private System.Windows.Forms.ToolStripMenuItem miGisShowAllTp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private System.Windows.Forms.ToolStripMenuItem miExportTp;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBandNetworkModulus;
        private System.Windows.Forms.ToolStripMenuItem miReplayTestpointCompare;
        private System.Windows.Forms.ToolStripMenuItem miLocateLineChart;
        private System.Windows.Forms.ToolStripMenuItem miExportShpfile;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem7;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem8;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem9;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem10;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem12;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem11;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
    }
}