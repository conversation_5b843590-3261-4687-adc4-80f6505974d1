﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatCellGridByGrids : QueryKPIStatBase
    {
        public GridMatrix<GridCellsUnit> RtGridCells { get; set; }

        public GridMatrix<GridUnitBase> Grids { get; set; }

        public Dictionary<ICell, StatDataHubBase> CellDataDic
        {
            get
            {
                Dictionary<ICell, StatDataHubBase> cellDataDic = new Dictionary<ICell, StatDataHubBase>();
                foreach (GridCellsUnit unit in RtGridCells)
                {
                    foreach (ICell cell in unit.CellDataHubDic.Keys)
                    {
                        StatDataHubBase hub;
                        if (!cellDataDic.TryGetValue(cell, out hub))
                        {
                            hub = new StatDataHubBase();
                            cellDataDic[cell] = hub;
                        }
                        hub.Merge(unit.CellDataHubDic[cell]);
                    }
                }
                return cellDataDic;
            }
        }

        private double ltLng = 360;
        private double ltLat;// = 360
        private double rbLng = 0;
        private double rbLat = 0;

        public QueryKPIStatCellGridByGrids()
            : base()
        {
            Grids = new GridMatrix<GridUnitBase>();
            RtGridCells = new GridMatrix<GridCellsUnit>();

            ltLat = 0;
            rbLat = 360;
        }

        protected override MasterCom.RAMS.Model.Interface.StatTbToken getTableNameToken()
        {
            return MasterCom.RAMS.Model.Interface.StatTbToken.cell_grid;
        }

        public void AddGrid(GridUnitBase grid)
        {
            int rowIdx, colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.LTLng, grid.LTLat, out rowIdx, out colIdx);
            Grids[rowIdx, colIdx] = grid;

            ltLng = Math.Min(ltLng, grid.LTLng);
            ltLat = Math.Max(ltLat, grid.LTLat);
            rbLng = Math.Max(rbLng, grid.BRLng);
            rbLat = Math.Min(rbLat, grid.BRLat);
        }

        protected override void preparePackageCommand(MasterCom.RAMS.Net.Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.KPI_CELL_GRID;
            package.Content.PrepareAddParam();
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(ltLng);
            package.Content.AddParam(ltLat);
            package.Content.AddParam(rbLng);
            package.Content.AddParam(rbLat);
            AddDIYEndOpFlag(package);
        }

        private void clear()
        {
            RtGridCells.Grids.Clear();
        }

        protected override bool getConditionBeforeQuery()
        {
            clear();
            return Grids.Length > 0;
        }

        public IEnumerable<string> FormulaSet
        {
            get;
            set;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(FormulaSet);
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<MasterCom.RAMS.Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            double dLong = package.Content.GetParamDouble();
            double dLat = package.Content.GetParamDouble();
            GridUnitBase grid = new GridUnitBase(dLong, dLat);
            int rowIdx, colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.LTLng, grid.LTLat, out rowIdx, out colIdx);

            if (Grids[rowIdx, colIdx] == null)
            {
                return;
            }
            GridCellsUnit unit = RtGridCells[rowIdx, colIdx];
            if (unit == null)
            {
                unit = new GridCellsUnit();
                unit.LTLng = dLong;
                unit.LTLat = dLat;
                RtGridCells[rowIdx, colIdx] = unit;
            }

            MasterCom.RAMS.Model.ICell cell = MasterCom.RAMS.Model.CellManager.GetInstance().GetICellByLACCI(lac, ci);
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            unit.AddCellData(cell, singleStatData, false);
        }
    }

    public class GridCellsUnit : GridUnitBase
    {
        public Dictionary<ICell, StatDataHubBase> CellDataHubDic { get; set; }

        public GridCellsUnit()
        {
            CellDataHubDic = new Dictionary<ICell, StatDataHubBase>();
        }

        public void AddCellData(ICell cell, KPIStatDataBase kpiData, bool makeGridMatrix)
        {
            StatDataHubBase data;
            if (!CellDataHubDic.TryGetValue(cell, out data))
            {
                data = new StatDataHubBase();
                CellDataHubDic.Add(cell, data);
            }
            data.AddStatData(kpiData, makeGridMatrix);
        }
    }
}
