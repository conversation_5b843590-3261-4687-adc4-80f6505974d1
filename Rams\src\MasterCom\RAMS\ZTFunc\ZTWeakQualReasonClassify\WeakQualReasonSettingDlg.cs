﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakQualReasonSettingDlg : BaseForm
    {
        private static WeakQualReasonSettingDlg instance = null;
        public static WeakQualReasonSettingDlg GetInstance()
        {
            if (instance==null)
            {
                instance = new WeakQualReasonSettingDlg();
            }
            return instance;
        }

        public WeakQualReasonSettingDlg()
        {
            InitializeComponent();

            initReasonsOrder();
            this.numericUpDown1.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown2.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown3.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown4.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown5.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown6.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown7.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown8.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown9.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown10.ValueChanged += numericUpDown_ValueChanged;
            this.numericUpDown11.ValueChanged += numericUpDown_ValueChanged;
        }

        private void numericUpDown_ValueChanged(object sender, EventArgs e)
        {
            this.chkDefaultParam.Checked = false;
        }

        public int CoverLapThreshold
        {
            get { return (int)this.numericUpDown1.Value; }
        }

        public double CoverLapDistanceTimes
        {
            get { return (double)this.numericUpDown2.Value; }
        }

        public int WeakCovMainCellThreshold
        {
            get { return (int)this.numericUpDown3.Value; }
        }

        public int WeakCovNbCell1Threshold
        {
            get { return (int)this.numericUpDown4.Value; }
        }

        public int NoMainCellDifferRxlev
        {
            get { return (int)this.numericUpDown5.Value; }
        }

        public int NoMainCellCellCount
        {
            get { return (int)this.numericUpDown6.Value; }
        }

        public int C2IThreshold
        {
            get { return (int)this.numericUpDown7.Value; }
        }

        public int HandoverProblemSecond
        {
            get { return (int)this.numericUpDown11.Value; }
        }

        public int HandoverFreqSecond
        {
            get { return (int)this.numericUpDown8.Value; }
        }

        public int BackCovDegree
        {
            get { return (int)this.numericUpDown9.Value; }
        }

        public int BackCovDistance
        {
            get { return (int)this.numericUpDown10.Value; }
        }

        /// <summary>
        /// 原因判断先后顺序
        /// </summary>
        public List<string> ReasonsOrder
        {
            get
            {
                List<string> order = new List<string>();
                foreach (String reason in this.listBoxReasonsOrder.Items)
                {
                    order.Add(reason);
                }
                return order;
            }
        }

        private void initReasonsOrder()
        {
            this.listBoxReasonsOrder.Items.Add("室分泄漏");
            this.listBoxReasonsOrder.Items.Add("占用不合理");
            this.listBoxReasonsOrder.Items.Add("弱覆盖");
            this.listBoxReasonsOrder.Items.Add("重选问题");
            this.listBoxReasonsOrder.Items.Add("覆盖杂乱");
            this.listBoxReasonsOrder.Items.Add("背向覆盖");
            this.listBoxReasonsOrder.Items.Add("切换不合理");
            this.listBoxReasonsOrder.Items.Add("切换不及时");
            this.listBoxReasonsOrder.Items.Add("质量毛刺");
            this.listBoxReasonsOrder.Items.Add("频率干扰或故障");
            this.listBoxReasonsOrder.Items.Add("其它");
        }

        private void chkDefaultParam_Click(object sender, EventArgs e)
        {
            if (!this.chkDefaultParam.Checked)
            {
                this.numericUpDown1.Value = -90;
                this.numericUpDown2.Value = 1.6m;
                this.numericUpDown3.Value = -85;
                this.numericUpDown4.Value = -85;
                this.numericUpDown5.Value = 6;
                this.numericUpDown6.Value = 3;
                this.numericUpDown7.Value = 12;
                this.numericUpDown8.Value = 20;
                this.numericUpDown9.Value = 90;
                this.numericUpDown10.Value = 500;
                this.numericUpDown11.Value = 3;
            }
        }

        private void chkDefaultOrder_Click(object sender, EventArgs e)
        {
            if (!this.chkDefaultOrder.Checked)
            {
                this.listBoxReasonsOrder.Items.Clear();
                initReasonsOrder();
            }
        }

        private void listBoxReasonsOrder_DragDrop(object sender, DragEventArgs e)
        {
            //
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            this.chkDefaultOrder.Checked = false;
            object si = listBoxReasonsOrder.SelectedItem;
            if (si != null)
            {
                int selIndex = listBoxReasonsOrder.SelectedIndex;
                if (selIndex > 0 && selIndex != listBoxReasonsOrder.Items.Count-1)
                {
                    listBoxReasonsOrder.Items.Remove(si);
                    listBoxReasonsOrder.Items.Insert(selIndex - 1, si);
                    listBoxReasonsOrder.SelectedIndex = selIndex - 1;
                }
            }
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            this.chkDefaultOrder.Checked = false;
            object si = listBoxReasonsOrder.SelectedItem;
            if (si != null)
            {
                int selIndex = listBoxReasonsOrder.SelectedIndex;
                if (selIndex < listBoxReasonsOrder.Items.Count - 2)
                {
                    listBoxReasonsOrder.Items.Remove(si);
                    listBoxReasonsOrder.Items.Insert(selIndex + 1, si);
                    listBoxReasonsOrder.SelectedIndex = selIndex + 1;
                }
            }
        }

        private void listBoxReasonsOrder_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.listBoxReasonsOrder.SelectedIndex == -1)
            {
                return;
            }

            if (this.listBoxReasonsOrder.SelectedItem.ToString().Equals("其它"))
            {
                this.btnUp.Enabled = false;
                this.btnDown.Enabled = false;
            }
            else
            {
                this.btnUp.Enabled = this.listBoxReasonsOrder.SelectedIndex != 0;
                this.btnDown.Enabled = this.listBoxReasonsOrder.SelectedIndex < this.listBoxReasonsOrder.Items.Count - 2;
            }

            switch (listBoxReasonsOrder.SelectedItem.ToString())
            {
                case "室分泄漏":
                    textBoxDesc.Text = "若采样点占用了室分小区，该采样点为室分泄漏采样点。";
                    break;
                case "占用不合理":
                    textBoxDesc.Text = "采样点场强大于 N1 dBm，若大于理想覆盖半径(在采样点正瓣覆盖方向最近的三个站点平均距离的 N2 倍)，则认为该采样点为过覆盖采样点。\r\n(默认值：N1=-90，N2=1.6)";
                    break;
                case "弱覆盖":
                    textBoxDesc.Text = "主服低于门限 N1 dBm及第一邻小区低于门限 N2 dBm，该采样点为弱覆盖采样点。\r\n(默认值：N1=-85，N2=-85)";
                    break;
                case "重选问题":
                    textBoxDesc.Text = "占用试呼前3秒，主服小区电平持续低于-85dBm。最强邻区电平高于-85dBm且比主服高5dbm以上。"
                                                + "若该类起呼后持续时间（起呼时间至首次切换、或至接通、或至未接通的持续时长）内存在质差采样点，则为重选问题类型。";
                    break;
                case "覆盖杂乱":
                    textBoxDesc.Text = "结合无主导小区算法，该采样点与主覆盖小区电平相差 N1 dBm以内小区数大于等于 N2 个，则认为为覆盖杂乱采样点。\r\n(默认值：N1=6，N2=3)";
                    break;
                case "背向覆盖":
                    textBoxDesc.Text = "小区的测试采样点未落在该小区的方位角的正负 N1 度，且小区与采样点的距离大于 N2 米。\r\n(默认值：N1=90，N2=500)";
                    break;
                case "切换不合理":
                    textBoxDesc.Text = "判断前 N1 秒是否存在切换不合理事件，事件定义：(1)切换后2秒的电平均值比前2秒均值差的，不考虑900至1800情况；"
                                                 + "(2)切换后主服小区电平小于邻小区电平+3dBm(RxlevSub<N_Level[0]+3)持续 N1 秒。"
                                                 + "\r\n切换频繁：持续 N2 秒以内及250米以下，切换次数大于等于4次。\r\n(默认值：N1=3，N2=20)";
                    break;
                case "切换不及时":
                    textBoxDesc.Text = "邻小区比主服小区的电平大于等于5dBm,且持续5秒以上为同一小区(5秒之后可为其他小区),另外1800至900不在该判断范围"
                                                   +"，在该持续的时长中，若存在质差采样点，则认为该采样点是由切换不及时引起的。";
                    break;
                case "质量毛刺":
                    textBoxDesc.Text = "使用突然高质差事件(持续3秒以上质量低于4级，随后3秒以内大于等于5级，最后降到4及以下持续3秒以上)，该采样点为质量毛剌采样点"
                                                  + "，若为质量毛剌点，还需要分设备、前后3秒切换次数、900/1800频段、是否覆盖快衰落";
                    break;
                case "频率干扰或故障":
                    textBoxDesc.Text = "C/I低于等于N，该采样点为干扰采样点。\r\n(默认值：N=12)";
                    break;
                case "其它":
                    textBoxDesc.Text = "未在现有规则范围内。";
                    break;
                default:
                    break;
            }
        }
    }
}
