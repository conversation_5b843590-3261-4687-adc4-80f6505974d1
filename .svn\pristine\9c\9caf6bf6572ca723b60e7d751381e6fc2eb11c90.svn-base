﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.IO;
using MasterCom.Util;
using System.Xml;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public abstract class NRLowSpeedCauseBase
    {
        public override string ToString()
        {
            return this.Name;
        }
        public abstract string Name
        {
            get;
        }

        protected void AddSubReason(NRLowSpeedCauseBase r)
        {
            if (SubCauses == null)
            {
                SubCauses = new List<NRLowSpeedCauseBase>();
            }
            r.Parent = this;
            SubCauses.Add(r);
        }

        [NonSerialized]
        private NRLowSpeedCauseBase parent = null;
        public NRLowSpeedCauseBase Parent
        {
            get { return parent; }
            set { parent = value; }
        }

        public NRLowSpeedCauseBase Ancestor
        {
            get
            {
                NRLowSpeedCauseBase ret = this;
                while (ret != null)
                {
                    if (ret.parent == null)
                    {
                        break;
                    }
                    ret = ret.parent;
                }
                return ret;
            }
        }

        public virtual List<NRLowSpeedCauseBase> SubCauses
        {
            get;
            set;
        }

        public abstract string Desc
        {
            get;
        }

        public abstract string Suggestion
        {
            get;
        }

        public virtual NRLowSpeedCauseBase Clone()
        {
            using (MemoryStream objectStream = new MemoryStream())
            {
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(objectStream, this);
                objectStream.Seek(0, SeekOrigin.Begin);
                NRLowSpeedCauseBase r = formatter.Deserialize(objectStream) as NRLowSpeedCauseBase;
                r.parent = this.parent;
                return r;
            }
        }

        public virtual void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {

        }

        public virtual void JudgeSinglePoint(NRLowSpeedSeg segItem, TestPoint testPoint, NRTpManagerBase nRCond)
        {

        }


        public abstract Dictionary<string, object> CfgParam
        {
            get;
            set;
        }
    }

    public class NRLowSpeedUnknowReason : NRLowSpeedCauseBase
    {
        public override string Name
        {
            get { return "未知原因"; }
        }

        public override string Desc
        {
            get { return Name; }
        }

        public override string Suggestion
        {
            get { return string.Empty; }
        }


        public override void Judge(NRLowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            //
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }
}
