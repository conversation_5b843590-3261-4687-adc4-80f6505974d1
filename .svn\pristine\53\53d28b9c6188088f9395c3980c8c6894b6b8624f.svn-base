﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EndToEndResultListForm : MinCloseForm
    {
        private MapForm mapForm = null;
        List<EndToEndInfo> resultList = new List<EndToEndInfo>();
        public EndToEndResultListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
            mapForm = MainModel.MainForm.GetMapForm();
            init();
        }

        public void FillData(List<EndToEndInfo> resultList)
        {
            this.resultList = resultList;
            this.ListViewEndToEnd.RebuildColumns();
            this.ListViewEndToEnd.ClearObjects();
            this.ListViewEndToEnd.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        /// <summary>
        /// 绑定界面数据
        /// </summary>
        private void init()
        {
            this.olvColumnStatSN.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    EndToEndInfo info = row as EndToEndInfo;
                    return info.SN;
                }
                return null;
            };

            this.olvColumnMoFileName.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    EndToEndInfo info = row as EndToEndInfo;
                    if (info.MoFile != null)
                    {
                        return info.MoFile.Name;
                    }
                }
                return null;
            };

            this.olvColumnMtFileName.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    EndToEndInfo info = row as EndToEndInfo;
                    if (info.MtFile != null)
                    {
                        return info.MtFile.Name;
                    }
                }
                return null;
            };

            this.olvColumnMessage.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    EndToEndInfo info = row as EndToEndInfo;
                    return info.MessageNames;
                }
                return null;
            };

            this.olvColumnDate.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    EndToEndInfo info = row as EndToEndInfo;
                    return info.BeginTime.ToShortDateString();
                }
                return null;
            };

            this.olvColumnTime.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    EndToEndInfo info = row as EndToEndInfo;
                    return info.BeginTime.ToString("HH:mm:ss");
                }
                return null;
            };

            setPhoneInfo();
        }

        private void setPhoneInfo()
        {
            this.olvColumnMoPhone.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    string strdesc = "";
                    EndToEndInfo info = row as EndToEndInfo;
                    if (info.MoFile != null)
                    {
                        strdesc = info.MoFile.StrDesc;
                    }
                    else
                    {
                        strdesc = info.MtFile.StrDesc;
                    }
                    string[] str = strdesc.Split('<', '>');
                    strdesc = str[3].Substring(12);
                    return strdesc;
                }
                return null;
            };

            this.olvColumnMtPhone.AspectGetter = delegate (object row)
            {
                if (row is EndToEndInfo)
                {
                    string strdesc = "";
                    EndToEndInfo info = row as EndToEndInfo;
                    if (info.MoFile != null)
                    {
                        strdesc = info.MoFile.StrDesc;
                    }
                    else
                    {
                        strdesc = info.MtFile.StrDesc;
                    }
                    string[] str = strdesc.Split('<', '>');
                    strdesc = str[5].Substring(12);
                    return strdesc;
                }
                return null;
            };
        }

        private void ToolStripReplay_Click(object sender, EventArgs e)
        {
            object row = ListViewEndToEnd.GetSelectedObjects();
            if (row == null)
            {
                MessageBox.Show("请选择要回放的文件");
                return;
            }
            EndToEndInfo info = ListViewEndToEnd.GetSelectedObject() as EndToEndInfo;
            if (info == null)
            {
                return;
            }

            MainModel.MainForm.NeedChangeWorkSpace(false);
            PreNextMinutesForm preNextMinuteForm = new PreNextMinutesForm(false);
            preNextMinuteForm.Pre = 2;
            preNextMinuteForm.Next = 2;
            if (preNextMinuteForm.ShowDialog() == DialogResult.OK)
            {
                //int pre = preNextMinuteForm.Pre;
                //int next = preNextMinuteForm.Next;
                //DateTime timeStart = info.BeginTime.AddMinutes(-pre);
                //DateTime timeEnd = info.BeginTime.AddMinutes(next);

                try
                {
                    QueryCondition condition = new QueryCondition();
                    condition.isCompareMode = true;
                    condition.QueryType = QueryType.General;
                    ReplayFileCompare query = new ReplayFileCompare(MainModel);
                    if (info.MoFile != null)
                    {
                        condition.FileInfos.Add(info.MoFile);
                    }
                    else
                    {
                        condition.FileInfos.Add(info.MtFile);
                    }
                    query.SetQueryCondition(condition);
                    query.Query();
                }
                catch
                {
                    MainModel.MainForm.CancelChange = true;
                }
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void ToolStripExport_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewEndToEnd);
        }
    }
}
