﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraEditors;
using System.Data.OleDb;
using Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDSInterfereCpiConditionDlg : BaseDialog
    {
        public TDSInterfereCpiConditionDlg()
        {
            InitializeComponent();

            initalTType();
        }

        private void initalTType()
        {
            cbxThresholdType.Items.Add("相对门限");
            cbxThresholdType.Items.Add("绝对门限");
            cbxThresholdType.Items.Add("相对门限&绝对门限");

            cbxThresholdType.SelectedIndex = 0;
        }

        public void getCondition(out CpiInterfereCond cpiInterfereCond)
        {
            cpiInterfereCond = new CpiInterfereCond();
            cpiInterfereCond.ShadowThr = (float)spinEditfShadowThr.Value;
            cpiInterfereCond.CoefficienThr = (float)spinEditfInterfireCoefficienThr.Value;
            cpiInterfereCond.RelativeThr = (float)spinEditRelativeThr.Value;
            cpiInterfereCond.AbsThr = (float)spinEditAbsThr.Value;
            if (cbxThresholdType.SelectedIndex == 0)
            {
                cpiInterfereCond.BeltType = thresholdType.relative;
            }
            else if (cbxThresholdType.SelectedIndex == 1)
            {
                cpiInterfereCond.BeltType = thresholdType.absolute;
            }
            else
            {
                cpiInterfereCond.BeltType = thresholdType.relativeAndAbsolute;
            }

            cpiInterfereCond.IsDefineCell = cbxDefinedCell.Checked;
            cpiInterfereCond.IsBasicGroup = cbxBasicGroup.Checked;
        }

        private void cbxThresholdType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxThresholdType.SelectedIndex==0)
            {
                lblAbsThr.Enabled = false;
                spinEditAbsThr.Enabled = false;
                labelControl8.Enabled = false;
                lblShadow.Enabled = true;
                spinEditfShadowThr.Enabled = true;
                labelControl2.Enabled = true;
            }
            else if(cbxThresholdType.SelectedIndex==1)
            {
                lblAbsThr.Enabled = true;
                spinEditAbsThr.Enabled = true;
                labelControl8.Enabled = true;
                lblShadow.Enabled = false;
                spinEditfShadowThr.Enabled = false;
                labelControl2.Enabled = false;
            }
            else
            {
                lblAbsThr.Enabled = true;
                spinEditAbsThr.Enabled = true;
                labelControl8.Enabled = true;
                lblShadow.Enabled = true;
                spinEditfShadowThr.Enabled = true;
                labelControl2.Enabled = true;
            }
        }

        public List<DefineTDCell> DefineCellList { get; set; }
        private void btnImport_Click(object sender, EventArgs e)
        {
            bool isImportOK = false;
            ImportExcel(out isImportOK);
            if (isImportOK)
            {
                cellImportForm = CellImportForm.GetDlg();
                cellImportForm.fillComboBoxs(columnList);
                if (cellImportForm.ShowDialog() == DialogResult.OK)
                {
                    WaitBox.Show("正在导入工参...", import);
                }
            }
            else
            {
                XtraMessageBox.Show("读取EXCEL失败");
            }

        }

        List<string> columnList = null;
        CellParamColumn cellParamColumn = null;
        CellImportForm cellImportForm = null;
        private void import()
        {
            try
            {
                cellParamColumn = cellImportForm.cellParamColumn;
                DefineCellList = new List<DefineTDCell>();

                DefineCellList = GetDefineCells();
                WaitBox.ProgressPercent = 80;
                if (DefineCellList != null)
                    XtraMessageBox.Show("导入完成");
                else
                    XtraMessageBox.Show("导入中断");
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        string filename = "";
        private void ImportExcel(out bool isImportOK)
        {
            try
            {
                columnList = new List<string>();

                OpenFileDialog dlg = new OpenFileDialog();
                dlg.Filter = "Excel2003文件(*.xls)|*.xls|Excel2007,2010文件(*.xlsx)|*.xlsx";
                dlg.RestoreDirectory = true;
                if (dlg.ShowDialog(this) == DialogResult.OK)
                {
                    filename = dlg.FileName;

                    Microsoft.Office.Interop.Excel.Application _excelApp = new Microsoft.Office.Interop.Excel.Application();
                    Workbook objWB = _excelApp.Workbooks.Open(filename, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing);
                    Sheets sheets = objWB.Worksheets;
                    Worksheet worksheet = (Worksheet)sheets.get_Item(1);
                    int colCount = worksheet.UsedRange.Columns.Count;
                    for (int i = 0; i < colCount; i++)
                    {
                        Range range = worksheet.Cells[1, i + 1] as Range;
                        columnList.Add(range.Text.ToString());
                    }
                    objWB.Close(Type.Missing, Type.Missing, Type.Missing);
                    _excelApp.Quit();
                    isImportOK = true;
                }
                else
                    isImportOK = false;
            }
            catch
            {
                isImportOK = false;
            }
        }


        /// <summary>
        /// 读取Excel表，生成小区工参
        /// </summary>
        private List<DefineTDCell> GetDefineCells()
        {
            List<DefineTDCell> defineCellList = new List<DefineTDCell>();
            string strConn = "";
            int index = filename.LastIndexOf(".");
            int lenFilename = filename.Length;
            if (filename.Substring(index + 1, lenFilename - index - 1) == "xls") //Excel2003版本引擎
            {
                strConn = "Provider=Microsoft.Jet.OLEDB.4.0;" + "Data Source=" + filename + ";" + "Extended Properties=Excel 8.0;";
            }
            else //Excel2007版本引擎
            {
                strConn = "Provider=Microsoft.ACE.OLEDB.12.0;" + "Data Source=" + filename + ";" + "Extended Properties=Excel 8.0;";
            }
            OleDbConnection con = new OleDbConnection(strConn);
            try
            {
                string importWorkSheetName = GetFirstSheetNameFromExcelFileName(filename);
                string cmdText = "select * from [" + importWorkSheetName.Replace('.', '#') + "$]";
                OleDbCommand command = new OleDbCommand(cmdText, con);
                con.Open();
                OleDbDataReader reader = command.ExecuteReader();

                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        WaitBox.ProgressPercent = 70;
                        DefineTDCell dcell = new DefineTDCell();

                        dcell.CellName = Convert.ToString(reader[cellParamColumn.col_cellname]).Trim();

                        if (reader[cellParamColumn.col_freq].ToString() == "")
                        {
                            XtraMessageBox.Show(dcell.CellName + "等小区的主频为空，数据无效！");
                            defineCellList = null;
                            break;
                        }
                        else
                        {
                            short freq;
                            if (short.TryParse(reader[cellParamColumn.col_freq].ToString(), out freq))
                                dcell.BCCH = freq;
                            else
                            {
                                defineCellList = null;
                                XtraMessageBox.Show(dcell.CellName + "等小区的主频数据类型错误！");
                                break;
                            }
                        }


                        if (reader[cellParamColumn.col_cpi].ToString() == "")
                        {
                            XtraMessageBox.Show(dcell.CellName + "等小区的扰码为空，数据无效！");
                            defineCellList = null;
                            break;
                        }
                        else
                        {
                            byte cpi;
                            if (byte.TryParse(reader[cellParamColumn.col_cpi].ToString(), out cpi))
                                dcell.BSIC = cpi;
                            else
                            {
                                defineCellList = null;
                                XtraMessageBox.Show(dcell.CellName + "等小区的扰码数据类型错误！");
                                break;
                            }
                        }

                        if (reader[cellParamColumn.col_Lac].ToString() != "")
                        {
                            int lac;
                            if (Int32.TryParse(reader[cellParamColumn.col_Lac].ToString(), out lac))
                                dcell.Lac = lac;
                            else
                            {
                                XtraMessageBox.Show(dcell.CellName + "等小区的LAC数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Lac = -255;


                        if (reader[cellParamColumn.col_ci].ToString() != "")
                        {
                            int ci;
                            if (Int32.TryParse(reader[cellParamColumn.col_ci].ToString(), out ci))
                                dcell.Ci = ci;
                            else
                            {
                                XtraMessageBox.Show(dcell.CellName + "等小区的CI数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Ci = -255;

                        if (reader[cellParamColumn.col_Longitude].ToString() != "")
                        {
                            double longitude;
                            if (double.TryParse(reader[cellParamColumn.col_Longitude].ToString(), out longitude))
                                dcell.Longitude = longitude;
                            else
                            {
                                XtraMessageBox.Show(dcell.CellName + "等小区的经度数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Longitude = 0;

                        if (reader[cellParamColumn.col_latitude].ToString() != "")
                        {
                            double latitude;
                            if (double.TryParse(reader[cellParamColumn.col_latitude].ToString(), out latitude))
                                dcell.Latitude = latitude;
                            else
                            {
                                XtraMessageBox.Show(dcell.CellName + "等小区的纬度数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Latitude = 0;

                        if (reader[cellParamColumn.col_direction].ToString() == "")
                        {
                            dcell.Direction = -1;
                        }
                        else
                        {
                            int direction;
                            if (Int32.TryParse(reader[cellParamColumn.col_direction].ToString(), out direction))
                                dcell.Direction = direction;
                            else
                            {
                                XtraMessageBox.Show(dcell.CellName + "等小区的方向角数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }

                        defineCellList.Add(dcell);
                    }
                    reader.Close();
                    con.Close();
                    WaitBox.ProgressPercent = 99;
                    return defineCellList;
                }
                defineCellList = null;
                return defineCellList;
            }
            catch
            {
                con.Close();
                defineCellList = null;
                return defineCellList;
            }
        }

        /// <summary>
        /// 获取Excel中第一个Sheet名称
        /// </summary>
        /// <param name="filepath"></param>
        /// <param name="numberSheetID"></param>
        /// <returns></returns>
        /// <example>
        /// string sheetName = GetFirstSheetNameFromExcelFileName(strFileUpLoadPath;
        /// </example>
        public static string GetFirstSheetNameFromExcelFileName(string filepath)
        {
            try
            {
                string strFirstSheetName = null;
                Microsoft.Office.Interop.Excel.Application _excelApp = new Microsoft.Office.Interop.Excel.Application();
                Microsoft.Office.Interop.Excel.Workbook objWB = _excelApp.Workbooks.Open(filepath, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing);

                strFirstSheetName = ((Microsoft.Office.Interop.Excel.Worksheet)objWB.Worksheets[1]).Name;

                objWB.Close(Type.Missing, Type.Missing, Type.Missing);
                _excelApp.Quit();
                return strFirstSheetName;
            }
            catch (Exception Err)
            {
                return Err.Message;
            }
        }

        private void checkEdit1_CheckedChanged(object sender, EventArgs e)
        {
            btnImport.Enabled = cbxDefinedCell.Checked;
        }
    }
}
