﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using MasterCom.MTGis;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VolteMosWithRtpAnaResultForm : MinCloseForm
    {
        List<VolteMosAndRtpItem> volteMosAndRtpList;
        Dictionary<string, VolteMosAndRtpSum> sumInfoDic;
        public VolteMosWithRtpAnaResultForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += miExportSimpleExcel_Click;
            gridViewMos.DoubleClick += gridViewMos_DoubleClick;
            gridViewRtp.DoubleClick += gridViewRtp_DoubleClick;

        }
        public void FillData(List<VolteMosAndRtpItem> mosAndRtpList, List<VolteRtpAndMosItem> rtpAndMosList
            , Dictionary<string, VolteMosAndRtpSum> sumInfoDic, VolteMosAndRtpSum sumInfo_Sum)
        {
            this.volteMosAndRtpList = mosAndRtpList;
            this.sumInfoDic = sumInfoDic;

            gridControlMosDetail.DataSource = mosAndRtpList;
            gridControlMosDetail.RefreshDataSource();
            gridControlRtpDetail.DataSource = rtpAndMosList;
            gridControlRtpDetail.RefreshDataSource();

            fillSumInfo(sumInfoDic, sumInfo_Sum);
            MainModel.DTDataManager.Clear();
            foreach (VolteMosAndRtpItem item in mosAndRtpList)
            {
                MainModel.DTDataManager.Add(item.MosTestpoint.Testpoint);
                foreach (RtpEvent evt in item.RtpEvts)
                {
                    MainModel.DTDataManager.Add(evt.Evt);
                }
            }
            MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:PESQMos", "LTE_TDD:POLQA_Score_SWB");
            MainModel.FireDTDataChanged(this);
        }
        private void fillSumInfo(Dictionary<string, VolteMosAndRtpSum> sumInfoDic, VolteMosAndRtpSum sumInfo_Sum)
        {
            DataTable dt1 = new DataTable();
            dt1.Columns.Add("MOS区间", typeof(string));
            dt1.Columns.Add("MOS时长", typeof(string));
            dt1.Columns.Add("RTP时长", typeof(string));
            dt1.Columns.Add("RTP时长占比", typeof(string));
            dt1.Columns.Add("MOS点个数", typeof(int));
            dt1.Columns.Add("RTP问题事件个数", typeof(int));

            Series series1 = chartControl1.Series[0];
            series1.Points.Clear();
            foreach (VolteMosAndRtpSum sumItem in sumInfoDic.Values)
            {
                if (sumItem.MosDuration <= 0)
                {
                    continue;
                }
                dt1.Rows.Add(getDataRow(sumItem));
                series1.Points.Add(new SeriesPoint(sumItem.MosValueDes, sumItem.Rate));
            }
            dt1.Rows.Add(getDataRow(sumInfo_Sum));
            gridControlSum.DataSource = dt1;
        }
        private object[] getDataRow(VolteMosAndRtpSum sumItem)
        {
            return new object[] { sumItem.MosValueDes, sumItem.MosDuration, sumItem.RtpDuration
                , sumItem.Rate.ToString("p"), sumItem.MosTpCount, sumItem.RtpEvtCount };
        }
        private void gridViewMos_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is VolteMosAndRtpItem)
            {
                VolteMosAndRtpItem item = gv.GetRow(gv.GetSelectedRows()[0]) as VolteMosAndRtpItem;
                MainModel.DTDataManager.Clear();

                MainModel.DTDataManager.Add(item.MosTestpoint.Testpoint);
                foreach (RtpEvent evt in item.RtpEvts)
                {
                    MainModel.DTDataManager.Add(evt.Evt);
                }
                MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:PESQMos", "LTE_TDD:POLQA_Score_SWB");
                MainModel.FireDTDataChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(item.MosTestpoint.Testpoint.Longitude, item.MosTestpoint.Testpoint.Latitude);
            }
        }
        private void gridViewRtp_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is VolteRtpAndMosItem)
            {
                VolteRtpAndMosItem item = gv.GetRow(gv.GetSelectedRows()[0]) as VolteRtpAndMosItem;
                MainModel.DTDataManager.Clear();

                MainModel.DTDataManager.Add(item.RtpEvt.Evt);
                foreach (VolteMosTp tp in item.VolteTps)
                {
                    MainModel.DTDataManager.Add(tp.Testpoint);
                }
                MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:PESQMos", "LTE_TDD:POLQA_Score_SWB");
                MainModel.FireDTDataChanged(this);
                MainModel.MainForm.GetMapForm().GoToView(item.RtpEvt.Evt.Longitude, item.RtpEvt.Evt.Latitude);
            }
        }
        private void miExportSimpleExcel_Click(object sender, EventArgs e)
        {
            int selectTabIdx = xtraTabControl1.SelectedTabPageIndex;
            if (selectTabIdx == 1)
            {
                ExcelNPOIManager.ExportToExcel(gridViewMos);
            }
            else if (selectTabIdx == 2)
            {
                ExcelNPOIManager.ExportToExcel(gridViewRtp);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(gridViewSum);
            }
        }
    }
}
