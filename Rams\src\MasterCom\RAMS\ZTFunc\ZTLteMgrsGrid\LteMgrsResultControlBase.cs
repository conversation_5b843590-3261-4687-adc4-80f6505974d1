﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsResultControlBase : UserControl
    {
        protected MainModel MainModel;

        public LteMgrsResultControlBase()
        {
            InitializeComponent();
            this.MainModel = MainModel.GetInstance();
        }

        public virtual string Desc
        {
            get { return "结果标题"; }
        }

        public virtual void DrawOnLayer()
        {
            LteMgrsLayer.DrawList = null;
            LteMgrsLayer.LegendGroup = null;
            MainModel.RefreshLegend();
            MainModel.MainForm.GetMapForm().updateMap();
        }

        public virtual void Clear()
        {

        }

        protected void SetNormalMapScale()
        {
            MapOperation mop = MainModel.MainForm.GetMapForm().GetMapOperation();
            if (mop.Scale < 15000)
            {
                mop.Scale = 15000;
            }
        }

        #region export all excel
        protected void MiExportExcelAll_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            LteMgrsResultForm resultForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteMgrsResultForm).FullName) as LteMgrsResultForm;
            List<LteMgrsResultControlBase> bindingControls = resultForm.BindingControls;
            foreach (LteMgrsResultControlBase ctrl in bindingControls)
            {
                ctrl.ExportAllExcel(dlg.SelectedPath);
            }
            MessageBox.Show("导出全部Excel完成!", "导出完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected virtual void ExportAllExcel(string savePath)
        {

        }
        #endregion

        #region export word
        protected void MiExportWord_Click(object sender, EventArgs e)
        {
            //MessageBox.Show("导出Word报告功能未实现！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //return;
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.OverwritePrompt = false;
            dlg.Filter = FilterHelper.Word;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            WaitTextBox.Show(DoExportWordInThread, dlg.FileName);
            LteMgrsResultForm resultForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteMgrsResultForm).FullName) as LteMgrsResultForm;
            resultForm.SelectedPageByType(typeof(LteMgrsSampleRateResult));
        }

        protected void DoExportWordInThread(object o)
        {
            string fileName = o as string;
            try
            {
                LteMgrsResultForm resultForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteMgrsResultForm).FullName) as LteMgrsResultForm;
                List<LteMgrsResultControlBase> bindingControls = resultForm.BindingControls;
                List<LteMgrsFuncItem> bindingFuncs = resultForm.BindingFuncs;
                LteMgrsFuncItem tmpItem = bindingFuncs[0];

                WordControl word = null;
                if (!System.IO.File.Exists(fileName))
                {
                    word = new LteMgrsWordControl(false);
                }
                else
                {
                    word = new LteMgrsWordControl(fileName, false);
                }

                #region 1. 测试数据说明
                if (LteMgrsGrid.FileType == "路测")
                {
                    word.InsertText(tmpItem.CurQueryCitys[tmpItem.SelectedCityIndex].CityName + "LTE路测分析报告", "标题");
                }
                else if (LteMgrsGrid.FileType == "扫频")
                {
                    word.InsertText(tmpItem.CurQueryCitys[tmpItem.SelectedCityIndex].CityName + "LTE扫频分析报告", "标题");
                }
                word.NewLine();
                word.InsertText("1. 测试数据说明", "标题 1");
                word.NewLine();
                string txt = string.Format("本次测试时间段：{0} -> {1}", tmpItem.QueryCondition.Periods[0].BeginTime.ToString("yyyy-MM-dd"),
                    tmpItem.QueryCondition.Periods[0].EndTime.ToString("yyyy-MM-dd"));
                word.InsertText(txt, "正文");
                word.NewLine();
                if (tmpItem.BaseQueryCitys == null)
                {
                    txt = "基准库时间段：未设置";
                }
                else
                {
                    txt = string.Format("基准库时间段：{0} -> {1}", tmpItem.BaseCondition.Periods[0].BeginTime.ToString("yyyy-MM-dd"),
                    tmpItem.BaseCondition.Periods[0].EndTime.AddDays(1).ToString("yyyy-MM-dd"));
                }
                word.InsertText(txt, "正文");
                word.NewLine();
                #endregion

                #region 2. 测试深度
                word.InsertText("2. 测试栅格深度统计", "标题 1");
                word.NewLine();
                ExportByControl(bindingControls, word, "测试深度");
                #endregion

                #region 3. 覆盖强度统计
                word.InsertText("3. 覆盖强度统计", "标题 1");
                word.NewLine();
                word.InsertText("3.1. 整体覆盖呈现", "标题 2");
                word.NewLine();
                ExportByControl(bindingControls, word, "GIS场强分布");

                word.InsertText("3.2. 最强信号区间分布及占比", "标题 2");
                word.NewLine();
                ExportByControl(bindingControls, word, "场强分布图表");

                word.InsertText("3.3. 最强信号连续弱覆盖分布", "标题 2");
                word.NewLine();
                ExportByControl(bindingControls, word, "连续弱覆盖");
                #endregion

                #region 4.重叠覆盖度

                #region 4.1 最强信号不分段
                word.InsertText("4. 道路重叠覆盖度", "标题 1");
                word.NewLine();
                word.InsertText("4.1. 最强信号不区分归属频段", "标题 2");
                word.NewLine();

                word.InsertText("4.1.1. 整体重叠覆盖度", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "不分段重叠覆盖度渲染");

                word.InsertText("4.1.2. 网格级分布情况及占比", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "不分段重叠覆盖度统计");

                word.InsertText("4.1.3. 连续高重叠覆盖度道路", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "不分段高重叠覆盖度区域");
                #endregion

                #region 4.2 最强归属段
                word.InsertText("4.2. 最强信号归属频段", "标题 2");
                word.NewLine();

                word.InsertText("4.2.1. 整体重叠覆盖度", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "最强归属段重叠覆盖度渲染");

                word.InsertText("4.2.2. 网格级分布情况及占比", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "最强归属段重叠覆盖度统计");

                word.InsertText("4.2.3. 连续高重叠覆盖度道路", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "最强归属段高重叠覆盖度区域");
                #endregion

                #region 4.3 单独D频段
                word.InsertText("4.3. 单独D频段", "标题 2");
                word.NewLine();

                word.InsertText("4.3.1. 整体重叠覆盖度", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "D频段重叠覆盖度渲染");

                word.InsertText("4.3.2. 网格级分布情况及占比", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "D频段重叠覆盖度统计");

                word.InsertText("4.3.3. 连续高重叠覆盖度道路", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "D频段高重叠覆盖度区域");
                #endregion

                #region 4.4 单独F频段
                word.InsertText("4.4. 单独F频段", "标题 2");
                word.NewLine();

                word.InsertText("4.4.1. 整体重叠覆盖度", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "F频段重叠覆盖度渲染");

                word.InsertText("4.4.2. 网格级分布情况及占比", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "F频段重叠覆盖度统计");

                word.InsertText("4.4.3. 连续高重叠覆盖度道路", "标题 3");
                word.NewLine();
                ExportByControl(bindingControls, word, "F频段高重叠覆盖度区域");

                #endregion

                #endregion

                (word as LteMgrsWordControl).ApplyGlobalStyle();
                if (!System.IO.File.Exists(fileName))
                {
                    word.SavedAsWord(fileName);
                }
                else
                {
                    word.SavedWord();
                }
                //MessageBox.Show("导出Word报告完成!", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "导出错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        protected void ExportByControl(List<LteMgrsResultControlBase> bindingControls, WordControl word, string title)
        {
            foreach (LteMgrsResultControlBase ctrl in bindingControls)
            {
                if (ctrl.ExportWord(word, title))
                {
                    break;
                }
            }
        }

        protected virtual bool ExportWord(WordControl word, string title)
        {
            return false;
        }
        #endregion
    }
}
