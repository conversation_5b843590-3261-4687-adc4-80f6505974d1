﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class VillagePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.checkedListBoxControlVillage = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.checkEditAll = new DevExpress.XtraEditors.CheckEdit();
            this.simpleButtonAdd = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlVillage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditAll.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // checkedListBoxControlVillage
            // 
            this.checkedListBoxControlVillage.CheckOnClick = true;
            this.checkedListBoxControlVillage.Location = new System.Drawing.Point(0, 0);
            this.checkedListBoxControlVillage.Name = "checkedListBoxControlVillage";
            this.checkedListBoxControlVillage.Size = new System.Drawing.Size(150, 159);
            this.checkedListBoxControlVillage.TabIndex = 0;
            // 
            // checkEditAll
            // 
            this.checkEditAll.Location = new System.Drawing.Point(4, 166);
            this.checkEditAll.Name = "checkEditAll";
            this.checkEditAll.Properties.Caption = "全部";
            this.checkEditAll.Size = new System.Drawing.Size(75, 19);
            this.checkEditAll.TabIndex = 1;
            this.checkEditAll.CheckedChanged += new System.EventHandler(this.checkEditAll_CheckedChanged);
            // 
            // simpleButtonAdd
            // 
            this.simpleButtonAdd.Location = new System.Drawing.Point(71, 164);
            this.simpleButtonAdd.Name = "simpleButtonAdd";
            this.simpleButtonAdd.Size = new System.Drawing.Size(76, 23);
            this.simpleButtonAdd.TabIndex = 2;
            this.simpleButtonAdd.Text = "添加";
            // 
            // VillagePanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.Controls.Add(this.simpleButtonAdd);
            this.Controls.Add(this.checkEditAll);
            this.Controls.Add(this.checkedListBoxControlVillage);
            this.Name = "VillagePanel";
            this.Size = new System.Drawing.Size(150, 192);
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlVillage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditAll.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxControlVillage;
        private DevExpress.XtraEditors.CheckEdit checkEditAll;
        private DevExpress.XtraEditors.SimpleButton simpleButtonAdd;
    }
}
