﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.AnyStat;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyTAAnalyse : DIYSampleByRegion
    {
        private readonly List<TAIntervalInfo> taInfos = new List<TAIntervalInfo>();
        public ZTDiyTAAnalyse(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "TA分析"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 12000, 12049, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TA";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_Distance";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "Distance";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM RxLevSub");
            tmpDic.Add("themeName", (object)"GSM RxLevSub");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        readonly TAAnalyseSettingDlg conditionDlg = new TAAnalyseSettingDlg();
        protected override bool getConditionBeforeQuery()
        {
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                taInfos.Clear();
                foreach (ValueRange range in conditionDlg.GetValueRanges())
                {
                    taInfos.Add(new TAIntervalInfo(range));
                }
                return true;
            }
            return false;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                int? ta = (int?)(byte?)tp["TA"];
                if (ta == null || ta < 0 || ta > 100)
                {
                    return;
                }

                double distance = 0;
                Cell gsmCell = tp.GetMainCell_GSM();
                if (gsmCell != null)
                {
                    distance = gsmCell.GetDistance(tp.Longitude, tp.Latitude);
                }

                foreach (TAIntervalInfo taInfo in taInfos)
                {
                    if (taInfo.Range.Contains((float)ta))
                    {
                        taInfo.AddTA((int)ta, distance);
                        if (IsBadTestPoint(tp))
                        {
                            taInfo.BadTestPoints.Add(tp);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                log.Error(e.Message);
            }
        }

        /// <summary>
        /// 是否异常采样点：服务小区不是距离最近的室外小区且不是最强的室外小区的采样点
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        private bool IsBadTestPoint(TestPoint tp)
        {
            double distance = 0;
            Cell gsmCell = tp.GetMainCell_GSM();
            if (gsmCell != null)
            {
                distance = gsmCell.GetDistance(tp.Longitude, tp.Latitude);

                float? rxLev = (short?)tp["RxLevSub"];
                for (int i = 0; i < 50; i++)
                {
                    float? nRxLev = (short?)tp["N_RxLev", i];
                    if (nRxLev == null || nRxLev <= rxLev)
                    {
                        return false;
                    }
                    Cell cell = getNeighborCell(tp, i);
                    if (cell != null && cell.Type == BTSType.Outdoor)
                    {
                        double nDistance = 0;
                        nDistance = gsmCell.GetDistance(tp.Longitude, tp.Latitude);

                        if (nDistance <= distance)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        private Cell getNeighborCell(TestPoint tp, int index)
        {
            if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
            {
                return getCell(tp, index, judgeDistance);
            }
            else
            {   //没有距离限制按CQT的方法来找邻区
                return getCell(tp, index, notjudgeDistance);
            }
        }

        delegate bool Func(Cell cell, double longitude, double latitude);

        private bool notjudgeDistance(Cell cell, double longitude, double latitude)
        {
            return true;
        }

        private bool judgeDistance(Cell cell, double longitude, double latitude)
        {
            return cell.GetDistance(longitude, latitude) < CD.MAX_COV_DISTANCE_GSM;
        }

        private Cell getCell(TestPoint tp, int index, Func func)
        {
            if (tp.Longitude == 0 && tp.Latitude == 0)//若无经纬度，则尝试找到主服务小区的位置，以其位置进行查找
            {
                Cell servCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"],
                    (short?)tp["BCCH"], (byte?)tp["BSIC"], tp.Longitude, tp.Latitude);
                if (servCell != null)
                {
                    Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short?)tp["N_BCCH", index], (byte?)tp["N_BSIC", index],
                        servCell.Longitude, servCell.Latitude, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"],
                        (byte?)tp["BSIC"]);
                    if (cell != null && func(cell, servCell.Longitude, servCell.Latitude))
                    {
                        return cell;
                    }
                }
                return null;
            }
            else
            {
                Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short?)tp["N_BCCH", index], (byte?)tp["N_BSIC", index],
                    tp.Longitude, tp.Latitude, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"], (byte?)tp["BSIC"]);
                if (cell != null && func(cell, tp.Longitude, tp.Latitude))
                {
                    return cell;
                }
                return null;
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            TAAnalyseForm taAnalyseForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(TAAnalyseForm)) as TAAnalyseForm;
            if (taAnalyseForm == null || taAnalyseForm.IsDisposed)
            {
                taAnalyseForm = new TAAnalyseForm(MainModel);
            }
            taAnalyseForm.FillData(taInfos);
            taAnalyseForm.Owner = MainModel.MainForm;
            taAnalyseForm.Visible = true;
            taAnalyseForm.BringToFront();
        }
    }

    public class TAIntervalInfo
    {
        public TAIntervalInfo(ValueRange range)
        {
            Range = range;
        }

        public void AddTA(int ta, double distance)
        {
            taSum += ta;
            sampleToServerCellDistance += distance;
            sampleCount++;
        }
        
        public ValueRange Range { get; set; }

        private double sampleToServerCellDistance = 0;
        private int taSum = 0;
        private int sampleCount = 0;
        public int SampleCount
        {
            get { return sampleCount; }
        }
        public double DistanceAvg
        {
            get { return sampleCount > 0 ? Math.Round(sampleToServerCellDistance / sampleCount, 2) : 0; }
        }
        public double TAAvg
        {
            get { return sampleCount > 0 ? Math.Round(1.0 * taSum / sampleCount, 4) : 0; }
        }

        private readonly List<TestPoint> badTestPoints = new List<TestPoint>();
        public List<TestPoint> BadTestPoints
        {
            get { return badTestPoints; }
        }
        public int BadSampleCount
        {
            get { return badTestPoints.Count; }
        }
        public double BadSampleScale
        {
            get { return sampleCount > 0 ? Math.Round(100.0 * badTestPoints.Count / sampleCount, 2) : 0; }
        }
    }
}
