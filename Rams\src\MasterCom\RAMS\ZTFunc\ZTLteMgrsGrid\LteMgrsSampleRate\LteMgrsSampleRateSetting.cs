﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsSampleRateSetting : LteMgrsConditionControlBase
    {
        public LteMgrsSampleRateSetting()
        {
            InitializeComponent();
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configSampleRate = configFile.GetConfig("SampleRate");
                object obj = configFile.GetItemValue(configSampleRate, "RSRP");
                if (obj != null)
                {
                    numRsrp.Value = (decimal)(double)obj;
                }
            }
        }

        public override string Title
        {
            get { return "扫频点占比"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return (double)numRsrp.Value;
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configSampleRate = xcfg.AddConfig("SampleRate");
            xcfg.AddItem(configSampleRate, "RSRP", (double)numRsrp.Value);
        }
    }
}
