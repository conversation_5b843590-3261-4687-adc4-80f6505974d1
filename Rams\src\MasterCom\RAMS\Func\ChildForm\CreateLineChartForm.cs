﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateLineChartForm : CreateChildForm
    {
        public CreateLineChartForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建信令图窗口 LineChartForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20019, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建信令图窗口";
            }
        }

        protected override void initAction()
        {
            action = new ActionCreateChildFrame();
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.LineChartForm";
            actionParam["Text"] = "时序图";
            actionParam["ImageFilePath"] = @"images\chart.gif";
            action.Param = actionParam;
        }
    }
}
