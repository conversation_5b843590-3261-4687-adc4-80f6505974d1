﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public static class LteRRCHelper
    {
        public static void FindMessages(TreeNode node, ref LteRRCInfo rrcInfo)
        {
            if (node.Nodes.Count > 0)
            {
                if (rrcInfo.IPower != int.MinValue && rrcInfo.FP_A != int.MinValue && rrcInfo.IP_B != int.MinValue)
                {
                    return;
                }
                foreach (TreeNode child in node.Nodes)
                {
                    setRrcInfo(rrcInfo, child);
                    FindMessages(child, ref rrcInfo);
                }
            }
        }

        private static void setRrcInfo(LteRRCInfo rrcInfo, TreeNode child)
        {
            string strText = child.Text.ToString();
            if (strText.Contains("referenceSignalPower: "))
                rrcInfo.IPower = Convert.ToInt32(strText.Replace("referenceSignalPower: ", "").Replace("dBm", ""));
            if (strText.Contains("p-a: "))
                rrcInfo.FP_A = Convert.ToSingle(strText.Split('B')[1]);
            if (strText.Contains("p-b: "))
                rrcInfo.IP_B = Convert.ToInt32(strText.Replace("p-b: ", ""));
        }

        public static LTECell FindCellByMessage(int index, List<DTData> dtDatas)
        {
            LTECell lteCell = null;
            for (int i = index; i >= 0; i--)
            {
                DTData dtData = dtDatas[i];
                if (dtData is TestPoint)
                {
                    LTETestPointDetail lteTestPoint = dtData as LTETestPointDetail;
                    lteCell = CellManager.GetInstance().GetNearestLTECellByTACCI(lteTestPoint.DateTime, (int?)(ushort?)lteTestPoint["lte_TAC"]
                                           , (int?)lteTestPoint["lte_ECI"], lteTestPoint.Longitude, lteTestPoint.Latitude);
                    break;
                }
            }
            return lteCell;
        }
    }

    public class LteRRCInfo
    {
        public int ISn { get; set; }
        public string StrCellName { get; set; } = "";
        public string StrCGI { get; set; } = "";
        public int IPower { get; set; } = int.MinValue;
        public float FP_A { get; set; } = int.MinValue;
        public int IP_B { get; set; } = int.MinValue;
        public int IMessageNum { get; set; }

        public string StrPower
        {
            get
            {
                if (this.IPower == int.MinValue || this.IPower == -99999)
                    return "";
                else
                    return this.IPower.ToString();
            }
        }

        public string StrP_A
        {
            get
            {
                if (this.FP_A == int.MinValue || this.FP_A == -99999)
                    return "";
                else
                    return this.FP_A.ToString();
            }
        }

        public string StrP_B
        {
            get
            {
                if (this.IP_B == int.MinValue || this.IP_B == -99999)
                    return "";
                else
                    return this.IP_B.ToString();
            }
        }

        public string StrInfoKey
        {
            get { return this.StrPower + "^" + this.StrP_A + "^" + this.StrP_B; }
        }

        public bool IsValid
        {
            get
            {
                bool isValid = false;
                if (IPower != int.MinValue || FP_A != int.MinValue || IP_B != int.MinValue)
                {
                    isValid = true;
                }
                return isValid; 
            }
        }
    }

    public class DIYQueryLteRRCInfo : DIYSQLBase
    {
        public DIYQueryLteRRCInfo(MainModel mainModel)
            : base(mainModel)
        {
            needQueryFileDic = new Dictionary<int, int>();
        }

        public override string Name
        {
            get { return "DIYQueryLteRRCInfo"; }
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, condition.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }

        public Dictionary<string, LteRRCInfo> cellRRCInfoDic { get; set; } = new Dictionary<string, LteRRCInfo>();
        string strMonth = "";
        Dictionary<int,int> needQueryFileDic = null;
        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(" select ifileid,cellname,icgi,irsprower,ipb,fpa from tb_cell_message_unionresult ");
            sb.Append(" where yearmonth in (" + strMonth + ") ");
            sb.Append(" order by ifileid,icgi  ");
            return sb.ToString();
        }

        public void SetCondition(string strMonth, Dictionary<int, int> needQueryFileDic)
        {
            this.strMonth = strMonth;
            this.needQueryFileDic = needQueryFileDic;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[6];
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            cellRRCInfoDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        int iFileID = package.Content.GetParamInt();
                        if (!needQueryFileDic.ContainsKey(iFileID))
                        {
                            continue;
                        }
                        LteRRCInfo rrcInfo = new LteRRCInfo();
                        rrcInfo.StrCellName = package.Content.GetParamString();
                        rrcInfo.StrCGI = package.Content.GetParamString();
                        rrcInfo.IPower = package.Content.GetParamInt();
                        rrcInfo.IP_B = package.Content.GetParamInt();
                        rrcInfo.FP_A = package.Content.GetParamFloat();
                        rrcInfo.IMessageNum = 1;
                        string strKey = rrcInfo.StrCGI + rrcInfo.StrInfoKey;
                        if (!cellRRCInfoDic.ContainsKey(strKey))
                            cellRRCInfoDic[strKey] = rrcInfo;
                        else
                            cellRRCInfoDic[strKey].IMessageNum++;
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
