using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public enum EAnalyseType
    {
        AnaBySample,
        AnaByEvent,
    }

    public partial class ZTDIYGSMCellOccupySetForm : BaseDialog
    {
        public ZTDIYGSMCellOccupySetForm()
        {
            InitializeComponent();
        }

        internal void SetCondition(CellOccupyCondition funcCond)
        {
            if (funcCond == null)
            {
                funcCond = new CellOccupyCondition();
            }
            edtSecond.Value = (decimal)funcCond.SecondMax;
            edtDistance.Value = (decimal)funcCond.DistanceMax;
        }

        internal CellOccupyCondition GetCondition()
        {
            CellOccupyCondition funcCond = new CellOccupyCondition();
            funcCond.SecondMax = (int)edtSecond.Value;
            funcCond.DistanceMax = (int)edtDistance.Value;
            return funcCond;
        }
    }
}

