﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class LineMarkCreateDlg : BaseForm
    {
        private List<DbPoint> points;
        private double length;
        private MapForm mf;
        private bool isNeedClose = true;
        private int fieldIndex = 0;
        private int shapeIndex = 0;


        public LineMarkCreateDlg(MapForm mf, List<DbPoint> points, double length)
        {
            InitializeComponent();
            this.mf = mf;
            this.points = points;
            this.length = length;
            this.txtField.Text = "";
            this.labelPointCount.Text = this.points.Count.ToString();
            this.labelLength.Text = string.Format("{0:F0}米", this.length);
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveDlg = new SaveFileDialog();
            saveDlg.InitialDirectory = Application.StartupPath + @"\userData";
            saveDlg.Filter = FilterHelper.Shp;
            saveDlg.OverwritePrompt = false;
            if (saveDlg.ShowDialog() != DialogResult.OK)
            {
                //this.DialogResult = DialogResult.Retry;
                return;
            }

            string errTxt = SaveToFile(saveDlg.FileName);
            if (errTxt == "")
            {
                this.DialogResult = DialogResult.OK;
                return;
            }

            MessageBox.Show(errTxt, "提示");
            //this.DialogResult = DialogResult.Retry;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private string SaveToFile(string fileName)
        {
            Shapefile shp = initShp(fileName);

            // 检测类型
            if (shp.ShapefileType != ShpfileType.SHP_POLYLINE)
            {
                if (isNeedClose)
                {
                    shp.Close();
                }
                return "选择的文件类型不正确!";
            }

            // 检测字段名
            fieldIndex = -1;
            for (int i = 0; i < shp.NumFields; ++i)
            {
                Field field = shp.get_Field(i);
                if (field.Name == "NAME")
                {
                    fieldIndex = i;
                    break;
                }
            }
            if (fieldIndex == -1)
            {
                if (isNeedClose)
                {
                    shp.Close();
                }
                return "选择的文件没有名称字段";
            }

            // 插入shape和name
            shapeIndex = shp.NumShapes;
            MapWinGIS.Shape shape = new MapWinGIS.Shape();
            shape.ShapeType = ShpfileType.SHP_POLYLINE;
            int pointIndex = shape.numPoints;
            for (int i = 0; i < points.Count; ++i)
            {
                MapWinGIS.Point point = new MapWinGIS.Point();
                point.x = points[i].x;
                point.y = points[i].y;
                shape.InsertPoint(point, ref pointIndex);
                ++pointIndex;
            }
            shp.EditInsertShape(shape, ref shapeIndex);
            shp.EditCellValue(fieldIndex, shapeIndex, this.txtField.Text);
            shp.StopEditingShapes(true, true, null);
            if (isNeedClose)
            {
                shp.Close();
            }
            return "";
        }

        private Shapefile initShp(string fileName)
        {
            Shapefile shp = null;

            // 已经打开
            foreach (SfLayerInfo sfInfo in mf.AllLayerList)
            {
                if (Path.GetDirectoryName(sfInfo.sf.Filename) == Path.GetDirectoryName(fileName)
                    && Path.GetFileName(sfInfo.sf.Filename) == Path.GetFileName(fileName))
                {
                    shp = sfInfo.sf;
                    isNeedClose = false;
                    shp.StartEditingShapes(true, null);
                    break;
                }
            }

            // 已经存在
            if (shp == null && File.Exists(fileName))
            {
                shp = new Shapefile();
                shp.Open(fileName, null);
                isNeedClose = true;
                shp.StartEditingShapes(true, null);
            }

            // 不存在
            if (shp == null)
            {
                shp = new Shapefile();
                shp.CreateNew(fileName, ShpfileType.SHP_POLYLINE);
                Field newField = new Field();
                newField.Name = "NAME";
                newField.Type = FieldType.STRING_FIELD;
                int numField = 0;
                shp.EditInsertField(newField, ref numField, null);
                isNeedClose = true;
            }

            return shp;
        }
    }
}
