﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMainCellLastOccupyAnaByRegion_GSM : ZTMainCellLastOccupyAnaBase
    {
        public ZTMainCellLastOccupyAnaByRegion_GSM(MainModel mainModel)
            : base(mainModel) 
        {
            baseParamName = new ParamName("RxLevSub", "RxQualSub");
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_VOICE);
            ServiceTypes.Add(ServiceType.EDGE_DATA);
            ServiceTypes.Add(ServiceType.GPRS_DATA);

            eventIDLst.Clear();
            foreach (EEventGSMID id in Enum.GetValues(typeof(EEventGSMID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override string Name
        {
            get { return "GSM主服占用时长(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        ZTMainCellLastOccupySetGSMConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetGSMConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22023, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is TestPointDetail)
                {
                    return base.isValidTestPoint(testPoint);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class ZTMainCellLastOccupyAnaByRegion_TD : ZTMainCellLastOccupyAnaByRegion_GSM
    {
        public ZTMainCellLastOccupyAnaByRegion_TD(MainModel mainModel)
            : base(mainModel) 
        {
            baseParamName = new ParamName("TD_PCCPCH_RSCP", "TD_PCCPCH_C2I");
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSDPA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSUPA);

            eventIDLst.Clear();
            foreach (EEventTDID id in Enum.GetValues(typeof(EEventTDID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override string Name
        {
            get { return "TD主服占用时长(按区域)"; }
        }

        protected override bool getCondition()
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is TDTestPointDetail)
                {
                    return Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class ZTMainCellLastOccupyAnaByRegion_LTE : ZTMainCellLastOccupyAnaByRegion_TD
    {
        public ZTMainCellLastOccupyAnaByRegion_LTE(MainModel mainModel)
            : base(mainModel) 
        {
            baseParamName = new ParamName("lte_RSRP", "lte_SINR");
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);

            eventIDLst.Clear();
            foreach (EEventLTEID id in Enum.GetValues(typeof(EEventLTEID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override string Name
        {
            get { return "LTE主服占用时长(按区域)"; }
        }

        ZTMainCellLastOccupySetLTEConditionForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTMainCellLastOccupySetLTEConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
