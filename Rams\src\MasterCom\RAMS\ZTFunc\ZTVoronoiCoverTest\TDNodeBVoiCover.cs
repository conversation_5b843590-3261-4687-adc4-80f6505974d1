﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDNodeBVoiCoverManager : GSMBTSVoiCoverManager
    {
        public Dictionary<TDNodeB, List<Vertex[]>> NodeBToPolygonDict { get; set; }

        public new static TDNodeBVoiCoverManager GetInstance()
        {
            if (instance == null)
            {
                instance = new TDNodeBVoiCoverManager();
            }
            return instance;
        }

        public override void Show()
        {
            if (NodeBToPolygonDict == null)
            {
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (TDNodeB bts in NodeBToPolygonDict.Keys)
            {
                drawList.Add(NodeBToPolygonDict[bts]);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }

        public override void Clear()
        {
            Reset();
            VoronoiLayer.GetInstance().Clear();
        }

        protected override void Construct()
        {
            List<TDNodeB> nodeList = null;
            if (MapCellLayer.DrawCurrent)
            {
                nodeList = MainModel.GetInstance().CellManager.GetCurrentTDBTSs();
            }
            else
            {
                nodeList = MainModel.GetInstance().CellManager.GetTDBTSs(MapCellLayer.CurShowTimeAt);
            }
            NodeBToPolygonDict = VoronoiManager<TDNodeB>.GetInstance().Construct(nodeList, ValidFilter, isShowProgress);
            LastErrorText = VoronoiManager<TDNodeB>.GetInstance().LastErrorText;
        }

        private bool ValidFilter(TDNodeB nodeB, MapOperation2 mop2)
        {
            if (nodeB.Type != Condition.TdType)
            {
                return false;
            }
            return mop2.CheckPointInRegion(nodeB.VertexX, nodeB.VertexY);
        }

        protected TDNodeBVoiCoverManager()
        {
        }

        private static TDNodeBVoiCoverManager instance;
    }
}
