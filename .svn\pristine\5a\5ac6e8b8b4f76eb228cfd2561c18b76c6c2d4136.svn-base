﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsDualFreqResult : LteMgrsResultControlBase
    {
        public LteMgrsDualFreqResult()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportWord.Click += base.MiExportWord_Click;
        }

        public override string Desc
        {
            get { return "双频网统计"; }
        }

        public void FillData(object data)
        {
            gridControl1.DataSource = data;
            gridControl1.RefreshDataSource();
        }

        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = "双频网统计";
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
