﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class DIYReplayOptionDlg : BaseDialog
    {
        List<DIYReplayContentOption> curOptionList = null;
        List<DIYReplayContentOption> allOptionList = null;
/*Need2BePerfect_Qiujianwei
     * 该窗体应该只会由回放文件的时候调用，并showdialog
 * 现存在个别地方调用该窗体，但不是show出来的做法，是需要改过来的。
     */ 
        public DIYReplayOptionDlg()
        {
            InitializeComponent();

#if NotShowLoadCQTPicture
            chkLoadCQTPicture.Checked = false;
            chkLoadCQTPicture.Visible = false;
#else
            chkLoadCQTPicture.Checked = false;
            chkLoadCQTPicture.Visible = true;
#endif

            this.Owner = mainModel.MainForm;
        }

        public bool IsAutoLoadCQTPicture
        {
            get { return chkLoadCQTPicture.Checked; }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(cbxReplayContSel.SelectedItem!=null)
            {
                this.DialogResult = DialogResult.OK;
            }
        }
        //modified by yht  参数增加
        internal void InitLoadInfo(string descFilter,string descExcept)
        {
            if(allOptionList==null)
            {
                allOptionList = loadOptionSettings();
                curOptionList = filterCurOptionList(allOptionList,descFilter, descExcept);
            }
        }
        string curDescFilter = "";
        string descExcept = "";
        private List<DIYReplayContentOption> filterCurOptionList(List<DIYReplayContentOption> allList,string descFilter, string descExceptStr)
        {
            curDescFilter = descFilter;
            descExcept = descExceptStr;
            List<DIYReplayContentOption> optionList = new List<DIYReplayContentOption>();
            try
            {
                foreach (DIYReplayContentOption option in allList)
                {
                    addValidOption(descFilter, optionList, option);
                }
            }
            catch
            {
                //continue
            }
            return optionList;
        }

        private void addValidOption(string descFilter, List<DIYReplayContentOption> optionList, DIYReplayContentOption option)
        {
            if (descFilter == "")
            {
                if (descExcept == "")
                {
                    optionList.Add(option);
                }
                else
                {
                    if (option.Desc == null || judgeValidOptionDesc(option, descExcept))
                    {
                        optionList.Add(option);
                    }
                }
            }
            else if (judgeValidOptionDesc(option, descFilter))
            {
                if (descExcept == "")
                {
                    optionList.Add(option);
                }
                else
                {
                    if (judgeValidOptionDesc(option, descExcept))
                    {
                        optionList.Add(option);
                    }
                }
            }
        }

        private bool judgeValidOptionDesc(DIYReplayContentOption option,string desc)
        {
            return option.Desc != null && option.Desc.IndexOf(desc) == -1;
        }

        private List<DIYReplayContentOption> loadOptionSettings()
        {
            List<DIYReplayContentOption> optionList = new List<DIYReplayContentOption>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/diyreplayoption.xml"));
                List<Object> list = configFile.GetItemValue("DIYReplayOptions", "options") as List<Object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        DIYReplayContentOption option = new DIYReplayContentOption();
                        option.Param = value as Dictionary<string, object>;
                        optionList.Add(option);
                    }
                }
            }
            catch
            {
                //continue
            }
            return optionList;
        }
        //modified by yht end 参数增加
        private void btnOption_Click(object sender, EventArgs e)
        {
            DIYReplayContentSettingDlg dlg = new DIYReplayContentSettingDlg();
            dlg.FillAllReplayOptions(allOptionList, curDescFilter, this.CurOption);
            dlg.ShowDialog();
            curOptionList = dlg.AllOptionList;
            this.FillCurrentServiceType(curSvrType);

            dlg.Dispose();
        }

        public DIYReplayContentOption GetCurDeepestOption()
        {
            DIYReplayContentOption option = null;
            int max = int.MinValue;
            foreach (object item in cbxReplayContSel.Items)
            {
                DIYReplayContentOption tmp = item as DIYReplayContentOption;
                if (tmp != null && tmp.SampleColumns.Count > max)
                {
                    option = tmp;
                    max = tmp.SampleColumns.Count;
                }
            }
            return option;
        }

        internal DIYReplayContentOption GetSelectedReplayOption()
        {
            return cbxReplayContSel.SelectedItem as DIYReplayContentOption;
        }

        internal void SelectDeepReplay()
        {
            for (int i = 0; i < cbxReplayContSel.Items.Count; i++ )
            {
                if (cbxReplayContSel.Items[i].ToString().Contains("深度"))
                {
                    cbxReplayContSel.SelectedIndex = i;
                    break;
                }
            }
        }

        internal void SelectDetailReplay()
        {
            for (int i = 0; i < cbxReplayContSel.Items.Count; i++)
            {
                if (cbxReplayContSel.Items[i].ToString().Contains("详细"))
                {
                    cbxReplayContSel.SelectedIndex = i;
                    break;
                }
            }
        }

        int curSvrType = -1;
        /// <summary>
        /// 根据业务类型，设置可用的采样点模板
        /// </summary>
        /// <param name="svtype"></param>
        internal void FillCurrentServiceType(int svtype)
        {
            this.curSvrType = svtype;
            int refSvType = GetCurServiceType(svtype);

            cbxReplayContSel.Items.Clear();
            foreach (DIYReplayContentOption option in curOptionList)
            {
                if (option.SampleServiceType == refSvType)
                {
                    cbxReplayContSel.Items.Add(option);
                }
            }
            if (cbxReplayContSel.Items.Count > 0)
            {
                cbxReplayContSel.SelectedIndex = 0;
            }
        }

        public static int GetCurServiceType(int svtype)
        {
            int refSvType = svtype;
            switch (svtype)
            {
                case 1://GSM_VOICE
                case 22://GSM_IDLE
                    refSvType = 1;
                    break;
                case 2://GPRS_DATA
                case 3://EDGE_DATA
                    refSvType = 2;
                    break;
                case 4://TD-SCDMA_VOICE
                case 13://TDSCDMA_VIDEO
                case 17://TDSCDMA_IDLE
                    refSvType = 4;
                    break;
                case 5://TD-SCDMA_DATA
                case 18://TDSCDMA_HSDPA
                case 27://TDSCDMA_HSUPA
                    refSvType = 5;
                    break;
                case 6://CDMA_VOICE
                case 26://CDMA_IDLE
                    refSvType = 6;
                    break;
                case 7://CDMA1X_DATA
                    refSvType = 7;
                    break;
                case 8://CDMA2000_VOICE
                case 9://CDMA2000_DATA:
                case 16://CDMA2000_VIDEO
                case 40://CDMA2000_IDLE
                    refSvType = 9;
                    break;
                case 10://WCDMA_VOICE
                case 14://WCDMA_VIDEO
                case 25://WCDMA_IDLE
                    refSvType = 10;
                    break;
                case 11://WCDMA_DATA
                case 15://WCDMA_HSDPA
                case 28://WCDMA_HSUPA
                    refSvType = 11;
                    break;
                case 12://GSMSCAN
                    refSvType = 12;
                    break;
                case 19://SCAN_TD
                    refSvType = 19;
                    break;
                case 20://SCAN_CDMA
                    refSvType = 20;
                    break;
                case 21://SCAN_WCDMA
                    refSvType = 21;
                    break;
                case 23://GSM_MOS
                    refSvType = 23;
                    break;
                case 24://GSM_UPLINK
                    refSvType = 24;
                    break;
                case 29://GSM_CALLTRACE
                    refSvType = 29;
                    break;
                case 30://TD_CALLTRACE
                    refSvType = 30;
                    break;
                case 31://WLAN
                    refSvType = 31;
                    break;
                case 33://LTE_VOICE
                case 34://LTE_DATA
                case 41://TDD_LTE_IDLE	TDD_LTE_空闲
                case 42://TDD_LTE_MULTI	TDD_LTE_并发
                case 43://SER_TDD_LTE_VIDEO	TDD_LTE_视频
                case 51://SER_LTE_TDD_VIDEO_VOLTE视频
                case 56://Ser_NBIOT_DATA  NB-IOT数据
                    refSvType = 33;
                    break;
                case 35://LTE_SCAN_TOPN
                    refSvType = 35;
                    break;
                case 36://LTE_SCAN_CW
                    refSvType = 36;
                    break;
                case 37://LTE扫频_频谱分析
                case 38://TD扫频_频谱分析
                case 39://GSM扫频_频谱分析
                case 72://NR扫频_频谱分析
                    refSvType = 37;
                    break;
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                case 52:
                    refSvType = 45;
                    break;
                case 55:
                    refSvType = 55;
                    break;
                case 57:
                case 58:
                case 59:
                case 60:
                case 61:
                case 62:
                case 63:
                case 64:
                case 65:
                case 66:
                case 67:
                case 68:
                case 69:
                case 70:
                case 73:
                    refSvType = 57;
                    break;
                case 71:
                    refSvType = 71;
                    break;
                default:
                    break;
            }

            return refSvType;
        }

        private void cbxReplayContSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            DIYReplayContentOption option = cbxReplayContSel.SelectedItem as DIYReplayContentOption;
            if(option!=null)
            {
                lbDesc.Text = option.Desc;
            }
        }

        public DIYReplayContentOption CurOption
        {
            get { return cbxReplayContSel.SelectedItem as DIYReplayContentOption; }
        }

        public DIYReplayContentOption SelectLastOption
        {
            get 
            {
                if (cbxReplayContSel.Items.Count > 0)
                    return cbxReplayContSel.Items[cbxReplayContSel.Items.Count - 1] as DIYReplayContentOption;
                else
                    return null;
            }
        }
    }
}