﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ZTFunc.ZTNRScanModCell;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanModCellQueryByFile : DIYReplayFileQuery
    {
        public NRScanModCellQueryByFile(MainModel mm)
            : base(mm)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            queryer = NRScanModCellQuery.Instance;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryer.QueryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            return option;
        }

        protected override void doPostReplayAction()
        {
            queryer.GetResultAfterQuery();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowResult();
            queryer.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            queryer.DoWithTestPoint(tp);
        }

        private readonly NRScanModCellQuery queryer;
    }

    public class NRScanModCellQueryByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public NRScanModCellQueryByRegion(MainModel mm)
            : base(mm)
        {
            IncludeEvent = false;
            IncludeMessage = false;
            FilterEventByRegion = false;
            FilterSampleByRegion = true;
            queryer = NRScanModCellQuery.Instance;
            Columns = new List<string>(queryer.QueryColumns);
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override void fireShowForm()
        {
            queryer.FireShowResult();
            queryer.Clear();
        }

        protected override void getResultsAfterQuery()
        {
            queryer.GetResultAfterQuery();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                foreach (TestPoint tp in testPointList)
                {
                    queryer.DoWithTestPoint(tp);
                }
            }
        }

        protected NRScanModCellQuery queryer;
    }

    public class NRScanModCellQuery
    {
        public static NRScanModCellQuery Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NRScanModCellQuery();
                }
                return instance;
            }
        }

        public virtual string Name
        {
            get { return "小区PCI模间干扰_NR扫频"; }
        }

        public virtual MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36014, Name);
        }

        public virtual List<string> QueryColumns
        {
            get { return queryColumns; }
        }

        public virtual bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new NRScanModCellSettingDlg();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            queryCond = setForm.GetCondition();
            return true;
        }

        protected Dictionary<NRCell, NRScanModTargetCell> tarCellDic = new Dictionary<NRCell, NRScanModTargetCell>();
        public void DoWithTestPoint(TestPoint tp)
        {
            float? maxRsrp = null;
            float? maxSinr = null;
            NRScanModTargetCell targetCell = null;

            bool isInterfered = false;
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var index in groupDic.Values)
            {
                bool hasGet = setTargetCell(tp, ref maxRsrp, ref maxSinr, ref targetCell, ref isInterfered, index);
                if (!hasGet)
                {
                    break;
                }
            }

            if (targetCell != null)
            {
                targetCell.AddTestPoint(tp, (double)maxRsrp, (double)maxSinr, isInterfered);
            }
        }

        private bool setTargetCell(TestPoint tp, ref float? maxRsrp, ref float? maxSinr, ref NRScanModTargetCell targetCell, ref bool isInterfered, int i)
        {
            // 小区匹配
            float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, i);
            float? sinr = NRTpHelper.NrScanTpManager.GetCellSinr(tp, i);
            int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(tp, i);
            int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(tp, i);
            if (rsrp == null || sinr == null || earfcn == null || pci == null)
            {
                return false;
            }
            NRCell nrCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);
            if (nrCell == null)
            {
                return false;
            }

            // 第一强作为目标小区
            if (i == 0)
            {
                maxRsrp = rsrp;
                maxSinr = sinr;
                if (!tarCellDic.TryGetValue(nrCell, out targetCell))
                {
                    targetCell = new NRScanModTargetCell(nrCell, (int)earfcn, (int)pci);
                    tarCellDic.Add(nrCell, targetCell);
                }

                if (rsrp < queryCond.MinRsrp || sinr > queryCond.MaxSinr)
                {
                    return false; // rsrp和sinr不满足条件，不需要判断干扰
                }
            }
            else
            {
                bool isValid = judgeContainsMod(maxRsrp, targetCell, rsrp, pci);
                if (isValid)
                {
                    // 对目标小区产生干扰的源小区
                    isInterfered = true;
                    addSourceCell(tp, targetCell, rsrp, sinr, earfcn, pci, nrCell);
                }
            }
            return true;
        }

        private bool judgeContainsMod(float? maxRsrp, NRScanModTargetCell targetCell, float? rsrp, int? pci)
        {
            // 非第一强频点，但不存在模间干扰
            if (maxRsrp - rsrp <= queryCond.DiffRsrp)
            {
                int modTarget = targetCell.Pci % queryCond.FilterCond.ModX;
                int mod = (int)pci % queryCond.FilterCond.ModX;
                if (modTarget == mod)
                {
                    return true;
                }
            }
            return false;
        }

        private void addSourceCell(TestPoint tp, NRScanModTargetCell targetCell, float? rsrp, float? sinr, int? earfcn, int? pci, NRCell nrCell)
        {
            NRScanModSourceCell sourceCell = null;
            if (!targetCell.SrcCellDic.TryGetValue(nrCell, out sourceCell))
            {
                sourceCell = new NRScanModSourceCell(nrCell, (int)earfcn, (int)pci);
                targetCell.SrcCellDic.Add(nrCell, sourceCell);
            }
            sourceCell.AddTestPoint(tp, (double)rsrp, (double)sinr);
        }

        protected List<NRScanModTargetCell> targetCellList = new List<NRScanModTargetCell>();
        public virtual void GetResultAfterQuery()
        {
            foreach (NRScanModTargetCell tarCell in tarCellDic.Values)
            {
                tarCell.GetResult();
                if (tarCell.SampleCount < queryCond.MinSampleCount || tarCell.SourceCells.Count == 0)
                {
                    continue;
                }
                targetCellList.Add(tarCell);
            }
        }

        public virtual void FireShowResult()
        {
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);

            NRScanModCellResultForm resultForm = MainModel.GetInstance().CreateResultForm(typeof(NRScanModCellResultForm)) as NRScanModCellResultForm;
            resultForm.FillData(new List<NRScanModTargetCell>(targetCellList));
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        public virtual void Clear()
        {
            tarCellDic.Clear();
            targetCellList.Clear();
        }

        private NRScanModCellQuery()
        {
            this.MainModel = MainModel.GetInstance();
        }

        protected MainModel MainModel;
        protected NRScanModCellCondition queryCond;
        protected NRScanModCellSettingDlg setForm;

        private List<string> queryColumns
        {
            get
            {
                return NRTpHelper.InitNrScanParamBackground();
            }
        }

        private static NRScanModCellQuery instance;
    }
}