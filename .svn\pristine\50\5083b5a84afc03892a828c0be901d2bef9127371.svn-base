﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Util;
using MasterCom.Util.UiEx;
using Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MainScanCoverCellChangeTableForm : MinCloseForm
    {
        private List<DIYMainCovercellChangeTableQueryByRegion.MainCoverInfo> mainCoverInfoList = null;

        public MainScanCoverCellChangeTableForm()
            : base()
        {
            InitializeComponent();
            init();

            mainCoverInfoList = MainModel.MainCoverInfoList;
        }

        private void init()
        {
            olvColumnSN.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeFileItem)
                {
                    TDScanMainCellChangeFileItem fileItem = o as TDScanMainCellChangeFileItem;
                    return fileItem.Sn;
                }
                else if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.Sn;
                }
                return null;
            };

            olvColumnName.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeFileItem)
                {
                    TDScanMainCellChangeFileItem fileItem = o as TDScanMainCellChangeFileItem;
                    return fileItem.FileName;
                }
                else if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.cellname;
                }
                return "";
            };

            addTDScanMainCellChangeCellItem();

            listViewTotal.CanExpandGetter = delegate (object x)
            {
                return x is TDScanMainCellChangeFileItem;
            };
            listViewTotal.ChildrenGetter = delegate (object o)
            {
                return ((TDScanMainCellChangeFileItem)o).CellItems;
            };
        }

        private void addTDScanMainCellChangeCellItem()
        {
            olvColumnCellId.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    if (subItem.cellId != int.MaxValue)
                    {
                        return subItem.cellId;
                    }
                }
                return null;
            };

            olvColumnArfcn.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.uarfcn;
                }
                return null;
            };

            olvColumnCpi.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.cpi;
                }
                return null;
            };

            olvColumnLongitude.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.startLocationLon;
                }
                return null;
            };

            olvColumnLatitude.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.startLocationLati;
                }
                return null;
            };

            olvColumnSampleCount.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.tpCount;
                }
                return null;
            };

            olvColumnCvrDis.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem subItem = o as TDScanMainCellChangeCellItem;
                    return subItem.cvrLength;
                }
                return null;
            };

            olvColumnPreRxLev.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem item = o as TDScanMainCellChangeCellItem;
                    if (item.preRxLev != float.MaxValue)
                    {
                        return item.preRxLev;
                    }
                }
                return null;
            };

            olvColumnNextRxLev.AspectGetter = delegate (object o)
            {
                if (o is TDScanMainCellChangeCellItem)
                {
                    TDScanMainCellChangeCellItem item = o as TDScanMainCellChangeCellItem;
                    if (item.nextRxLev != float.MaxValue)
                    {
                        return item.nextRxLev;
                    }
                }
                return null;
            };
        }

        public void FillData()
        {
            listViewTotal.ClearObjects();
            listViewTotal.SetObjects(MainModel.TDScanMCChangeFileItemLst);


            //mainCoverInfoList = MainModel.MainCoverInfoList;
            //dataGridViewChangeTable.RowCount = mainCoverInfoList.Count;

            //for (int row = 0; row < mainCoverInfoList.Count; row++)
            //{
            //    dataGridViewChangeTable.Rows[row].Cells[0].Value = row + 1;  //路段系列号
            //    dataGridViewChangeTable.Rows[row].Cells[1].Value = mainCoverInfoList[row].cellId; //小区ID
            //    dataGridViewChangeTable.Rows[row].Cells[2].Value = mainCoverInfoList[row].cellname;   //小区名
            //    dataGridViewChangeTable.Rows[row].Cells[3].Value = mainCoverInfoList[row].uarfcn;  // 频点
            //    dataGridViewChangeTable.Rows[row].Cells[4].Value = mainCoverInfoList[row].cpi;    //扰码
            //    dataGridViewChangeTable.Rows[row].Cells[5].Value = mainCoverInfoList[row].startLocationLon;  //路段起始点经度
            //    dataGridViewChangeTable.Rows[row].Cells[6].Value = mainCoverInfoList[row].startLocationLati;   //路段起始点纬度
            //    dataGridViewChangeTable.Rows[row].Cells[7].Value = mainCoverInfoList[row].tpCount;    //采样点数目
            //    dataGridViewChangeTable.Rows[row].Cells[8].Value = String.Format("{0:N3}", mainCoverInfoList[row].roadLenth); //路段长度
            //}
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            try
            {
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: ERROR: worksheet == null"));
                }
                worksheet.Name = "小区变更";
                //====列标题
                int colCount = 1;
                int idx = 1;
                makeTitle(worksheet, idx++, "序号", 10);
                makeTitle(worksheet, idx++, "文件名", 20);

                makeTitle(worksheet, idx++, "切换序号", 15);
                makeTitle(worksheet, idx++, "小区名", 15);
                makeTitle(worksheet, idx++, "小区ID", 10);
                makeTitle(worksheet, idx++, "频点(ARFCN)", 10);
                makeTitle(worksheet, idx++, "扰码(CPI)", 15);
                makeTitle(worksheet, idx++, "变更前场强", 10);
                makeTitle(worksheet, idx++, "变更后场强", 10);
                makeTitle(worksheet, idx++, "变更点经度", 15);
                makeTitle(worksheet, idx++, "变更点纬度", 15);
                makeTitle(worksheet, idx++, "采样点个数", 10);
                makeTitle(worksheet, idx++, "覆盖里程(米)", 10);

                colCount = idx - 1;
                int rowAt = 2;
                rowAt = setRowValue(worksheet, rowAt);
                for (int c = 1; c <= colCount; c++)
                {
                    Range range = worksheet.Cells[1, c] as Range;
                    range.EntireColumn.AutoFit();
                }
                for (int r = 1; r <= rowAt; r++)
                {
                    Range range = worksheet.Cells[r, 1] as Range;
                    range.EntireRow.AutoFit();
                }
                Range oRng = worksheet.get_Range("A1", "J" + rowAt);
                oRng.AutoFilter(1, Type.Missing, XlAutoFilterOperator.xlAnd, Type.Missing, true);

                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出Excel出错：" + ex.Message);
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private int setRowValue(_Worksheet worksheet, int rowAt)
        {
            foreach (TDScanMainCellChangeFileItem fileItem in MainModel.TDScanMCChangeFileItemLst)
            {
                Range firstGroupRge_1 = worksheet.Cells[rowAt, 1] as Range;
                firstGroupRge_1.Value2 = fileItem.Sn;
                Range firstGroupRge_2 = worksheet.Cells[rowAt, 2] as Range;
                firstGroupRge_2.Value2 = fileItem.FileName;

                int rowOffset = 0;
                foreach (TDScanMainCellChangeCellItem cellItem in fileItem.CellItems)
                {
                    int xx = 3;
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.Sn + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.cellname);
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.cellId == int.MaxValue ? "" : cellItem.cellId + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.uarfcn + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.cpi + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.preRxLev == float.MaxValue ? "" : cellItem.preRxLev + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.nextRxLev == float.MaxValue ? "" : cellItem.nextRxLev + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.startLocationLon + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.startLocationLati + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx++, cellItem.tpCount + "");
                    makeItemRow(worksheet, rowAt + rowOffset, xx, cellItem.cvrLength + "");
                    rowOffset++;
                }
                if (rowOffset > 1)
                {
                    Range lastGroupRge_1 = worksheet.Cells[rowAt + rowOffset - 1, 1] as Range;
                    Range mRange_1 = worksheet.get_Range(firstGroupRge_1, lastGroupRge_1);
                    mRange_1.MergeCells = true;
                    Range lastGroupRge_2 = worksheet.Cells[rowAt + rowOffset - 1, 2] as Range;
                    Range mRange_2 = worksheet.get_Range(firstGroupRge_2, lastGroupRge_2);
                    mRange_2.MergeCells = true;
                }
                rowAt += rowOffset;
            }

            return rowAt;
        }

        private void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }
        //private void makeItemRow(_Worksheet worksheet, int row, int column, string str, Color txtColor)
        //{
        //    Range range = worksheet.Cells[row, column] as Range;
        //    range.Value2 = str;
        //    range.Font.Color = System.Drawing.ColorTranslator.ToOle(txtColor);
        //}
        private void makeTitle(_Worksheet worksheet, int col, string title, int width)
        {
            Range range = worksheet.Cells[1, col] as Range;
            range.Value2 = title;
            range.Font.Bold = true;
            range.ColumnWidth = width;
        }

        private void tsmiExplandAll_Click(object sender, EventArgs e)
        {
            listViewTotal.ExpandAll();
        }

        private void tsmiCallapsAll_Click(object sender, EventArgs e)
        {
            listViewTotal.CollapseAll();
        }

        private void tsmiShowLine_Click(object sender, EventArgs e)
        {
            MainModel.CurSelTdScanMcChangeCellItems.Clear();
            TDCell cell = null;
            foreach (object item in listViewTotal.SelectedObjects)
            {
                cell = addCurSelTdScanMcChangeCellItems(cell, item);
            }
            if (cell != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude);
            }
        }

        private TDCell addCurSelTdScanMcChangeCellItems(TDCell cell, object item)
        {
            if (item is TDScanMainCellChangeFileItem)
            {
                TDScanMainCellChangeFileItem fileItem = item as TDScanMainCellChangeFileItem;
                MainModel.CurSelTdScanMcChangeCellItems.AddRange(fileItem.CellItems);
                if (cell == null)
                {
                    foreach (TDScanMainCellChangeCellItem cellItem in fileItem.CellItems)
                    {
                        cell = cellItem.MainCell;
                    }
                }
            }
            else if (item is TDScanMainCellChangeCellItem)
            {
                TDScanMainCellChangeCellItem cellItem = item as TDScanMainCellChangeCellItem;
                if (!MainModel.CurSelTdScanMcChangeCellItems.Contains(cellItem))
                {
                    MainModel.CurSelTdScanMcChangeCellItems.Add(cellItem);
                }
                if (cell == null)
                {
                    cell = cellItem.MainCell;
                }
            }

            return cell;
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            tsmiShowLine.Enabled = true;
            if (listViewTotal.SelectedObject is TDScanMainCellChangeCellItem && listViewTotal.SelectedObjects.Count == 1)
            {
                TDScanMainCellChangeCellItem cellItem = listViewTotal.SelectedObject as TDScanMainCellChangeCellItem;
                if (cellItem.MainCell == null)
                {
                    tsmiShowLine.Enabled = false;
                }
            }
        }
    }
}
