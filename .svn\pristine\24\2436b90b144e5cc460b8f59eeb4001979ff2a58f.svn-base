﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public static class DistanceTranslator
    {
        private static double a = 6378137;
        private static double b = setB();
        private static double f = 298.257223563;
        //private static double equator = setEquator();
        private static double meridian= setMeridian();

        static double setB()
        {
            return a - a / f;
        }

        //static double setEquator()
        //{
        //    return 2 * Math.PI * a;
        //}

        static double setMeridian()
        {
            return Math.PI * ((a + b) * 3 / 2 - Math.Sqrt(a * b));
        }

        public static double MeterPerLongitude(double latitude)
        {
            double tanLat = Math.Tan(latitude * Math.PI / 180);
            double radius = a * b / Math.Sqrt(b * b + a * a * tanLat * tanLat);
            return 2 * radius * Math.PI / 360;
        }

        public static double LongitudePerMeter(double latitude)
        {
            return 1 / MeterPerLongitude(latitude);
        }

        public static double MeterPerLatitude()
        {
            return meridian / 360;
        }

        public static double LatitudePerMeter()
        {
            return 360 / meridian;
        }

        public static double AreaPerLongLat(double latitude)
        {
            return MeterPerLatitude() * MeterPerLongitude(latitude);
        }
    }
}
