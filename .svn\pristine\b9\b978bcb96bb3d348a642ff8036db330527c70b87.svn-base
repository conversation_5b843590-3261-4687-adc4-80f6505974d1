﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors.Controls;
using System.Collections;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Util;
using System.IO;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class WirelessNetworkMonitoringInfoPanel : UserControl, PopShowPanelInterface
    {
        //第一命令字：config
        public const byte REQTYPE_CONFIG_KPI_TABLE = 0x2b;  //REQUEST
        public const byte RESTYPE_CONFIG_KPI_TABLE = 0x2b;

        //第一命令字：查询    启动预读
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83;
        private MainModel MainModel;

        private Stack<PopKPIQueryCondition> backStacks = new Stack<PopKPIQueryCondition>(); //后退栈
        private Stack<PopKPIQueryCondition> forwardStacks = new Stack<PopKPIQueryCondition>();//前进栈
        List<KPIResultInfo> allKPIResultInfoList = new List<KPIResultInfo>();//所有原始记录

        private ToolStripDropDown toolStripDropDownCity = new ToolStripDropDown();

        private string curShowType = "";//当前时间方式
        private List<string> selectedDateStrs = new List<string>();//按周、按月选择的日期列表

        private CitySelectionPanel cityPanel = null;
        public string panelName { get; set; } //表面板名

        TableKind tablekind; //表类型
        enum TableKind
        { rcu,phone}

        public WirelessNetworkMonitoringInfoPanel()
        {
            InitializeComponent();
#if PopShow_KPI_Color
            //btnColor.Visible = true;
#endif
            toolStripDropDownCity.Closed += new ToolStripDropDownClosedEventHandler(toolStripDropDownCity_Closed);
            initCityOrder();

            curRetDataDic = new Dictionary<string, KPIPopTable>();
        }

        private void toolStripDropDownCity_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            updateToolTip();
        }

        int minTime = 0x7fffffff;  //查询结果中的时间最小值
        int maxTime = 0;           //查询结果中的时间最大值

        private void setTime(int time)
        {
            if (time > maxTime)
            {
                maxTime = time;
            }

            if (time < minTime)
            {
                minTime = time;
            }
        }

        private void resetTime()
        {
            minTime = 0x7fffffff;
            maxTime = 0;
        }

        private void initShowInfo()
        {
            if (panelName=="tb_popkpi_wxwljc_rcu_cmpstat" || panelName=="tb_popkpi_wxwljc_rcugsm_stat"||panelName=="tb_popkpi_wxwljc_rcutd_stat"
                || panelName == "tb_popkpi_wxwljc_phonetd_stat")
            {
                cbxShowType.Items.Add("前一月");
                cbxShowType.Items.Add("本月");
                cbxShowType.Items.Add("按月");

                tablekind = TableKind.rcu;
            }
            cbxShowType.SelectedIndex = 0;

            btnBack.Enabled = false;
            btnForward.Enabled = false;
        }

        private void updateToolTip()
        {
            if (this.toolStripDropDownCity.Tag != null)
            {
                if (MainModel.User.DBID == -1) //省用户显示
                {
                    if (((IList)this.toolStripDropDownCity.Tag).Count > 0)
                    {
                        if (((IList)this.toolStripDropDownCity.Tag).Count == 1)
                        {
                            btnShowCityNames.Text = "请选择地市↓(已选择 " + ((List<string>)this.toolStripDropDownCity.Tag)[0] + " )";
                        }
                        else
                        {
                            btnShowCityNames.Text = "请选择地市↓(已选择" + ((List<string>)this.toolStripDropDownCity.Tag).Count.ToString() + "个地市)";
                        }
                    }
                    else
                    {
                        btnShowCityNames.Text = "请选择地市↓";
                    }
                }
                else//地市用户，只能显示自己地市的，不能选择
                {
                    string cityName = DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID);
                    btnShowCityNames.Text = "当前地市:" + cityName;
                    btnShowCityNames.Enabled = false;
                }
            }
            checkedCbxDate.ToolTip = checkedCbxDate.Text;
        }

        private void initCityOrder()
        {
            if (cityPanel == null)
            {
                cityPanel = new CitySelectionPanel(this.toolStripDropDownCity);
            }
            toolStripDropDownCity.Items.Clear();
            toolStripDropDownCity.Items.Add(new ToolStripControlHost(cityPanel));
        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            Dictionary<string, KPIPopTable> entryHeaderDic = queryPopEntry(worker); //从popkpi获取每个表信息，key:表名 value.entryList 对应表的列信息
            allKPIResultInfoList = new List<KPIResultInfo>();
            foreach (string tbkey in entryHeaderDic.Keys)
            {
                KPIPopTable hdUnit = entryHeaderDic[tbkey];
                hdUnit.Sort();
                hdUnit.initFinderDic();
                List<KPIResultInfo> resultList = queryResultFromHeader(worker, hdUnit, MainModel.User.DBID);
                allKPIResultInfoList.AddRange(resultList);
                hdUnit.cityDataResult = buildCityStruct(resultList);
            }
            task.retResultInfo = entryHeaderDic;
        }

        public Dictionary<string, KPIPopTable> curRetDataDic { get; set; }
        public void FireFreshShowData(TaskInfo task)
        {
            initShowInfo();

            if (!(task.retResultInfo is Dictionary<string, KPIPopTable>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, KPIPopTable>;
            }
            foreach (KPIPopTable popTable in curRetDataDic.Values)
            {
                if (popTable.tablename != panelName)
                {
                    continue;
                }
                textboxProject.Text = popTable.ToString();
                lblTitle.Text = "KPI指标 - " + popTable;
            }

            refreshShowReport(true);

            selectedDateStrs = new List<string>();
            foreach (CheckedListBoxItem item in checkedCbxDate.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    selectedDateStrs.Add(item.Value.ToString());
                }
            }
            resetTime();
        }

        private void refreshShowReport(bool isClickFresh)
        {
            dataGridView.Columns.Clear();
            string selShowType = cbxShowType.SelectedItem as string;  //记录已选方式
            bool isByEquipment = radioButtonByEquipment.Checked;    //按设备
            bool isByGather = radioButtonByGather.Checked;    //按汇总
            bool isByBoth = radioButtonByBoth.Checked;   //按设备及汇总
            List<string> selectedCityNames = this.toolStripDropDownCity.Tag as List<string>;

            if (isClickFresh)
            {
                PopKPIQueryCondition queryCondition = new PopKPIQueryCondition();
                foreach (string selectedCityName in selectedCityNames)
                {
                    queryCondition.AddCheckedCityName(selectedCityName);
                }
                queryCondition.AddCheckedProject(panelName);
                foreach (CheckedListBoxItem item in checkedCbxDate.Properties.Items)
                {
                    queryCondition.AddDate(item.Value.ToString(), item.CheckState == CheckState.Checked);
                }
                queryCondition.SetIsByEquipment(isByEquipment);
                queryCondition.SetIsByGather(isByGather);
                queryCondition.SetIsByBoth(isByBoth);
                queryCondition.SetShowType(selShowType);

                backStacks.Push(queryCondition);
                btnBack.Enabled = backStacks.Count > 1;
                btnForward.Enabled = forwardStacks.Count > 0;
            }
            FinalShowResult showRet = parseShowFromTable(selShowType, isByEquipment, isByGather, isByBoth, selectedCityNames);
            showInGrid(showRet);
        }

        private FinalShowResult parseShowFromTable(string selShowType, bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            if (selShowType == "按月")
            {
                sRet = prepareShowByMonth(isByEquipment, isByGather, isByBoth, selectedCityNames);
            }
            else if (selShowType == "按周")
            {
                sRet = prepareShowByWeek(isByEquipment, isByGather, isByBoth, selectedCityNames);
            }
            else if (selShowType == "本月")
            {
                sRet = prepareShowByThisMonth(isByEquipment, isByGather, isByBoth, selectedCityNames);
            }
            else if (selShowType == "本周")
            {
                sRet = prepareShowByThisWeek(isByEquipment, isByGather, isByBoth, selectedCityNames);
            }
            else if (selShowType == "前一周")
            {
                sRet = prepareShowByBackOneWeek(isByEquipment, isByGather, isByBoth, selectedCityNames);
            }
            else if (selShowType == "前一月")
            {
                sRet = prepareShowByOneBackMonth(isByEquipment, isByGather, isByBoth, selectedCityNames);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByMonth(bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("地市");
            KPIPopTable kpiPopTable = curRetDataDic[panelName];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (kpiPopTable.tablename == tableData.tablename)
                {
                    tableData.reloadDataResult((MainModel.User.DBID == -1), "", "");
                    foreach (KPIResultInfo retInfo in tableData.dataResult)
                    {
                        setTime(getMonthBeginTime(retInfo.stime));
                        setTime(getMonthEndTime(retInfo.stime));

                        string dateKey = "";
                        if (tablekind == TableKind.phone)
                        {
                            dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["name"]].ToString();
                            if (retInfo.valueList[tableData.keyIndexDic["name"]].ToString() == "")
                            {
                                dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                            }
                        }
                        else if (tablekind==TableKind.rcu)
                        {
                            dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (retInfo.valueList[tableData.keyIndexDic["devid"]].ToString() == "")
                            {
                                dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                            }
                        }
                        dateKey += ":";
                        dateKey = dateKey + DistrictManager.GetInstance().getDistrictName(retInfo.dbid);  //记录地市名
                        dateKey += ":";
                        if (isByEquipment)  //显示含有设备号的记录
                        {
                            string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (devid != "" && !devid.Contains("汇总"))
                            {
                                dateKey += devid;
                            }
                            else
                            {
                                continue;
                            }
                        }
                        dateKey += ":";
                        if (isByGather)  //显示含有汇总的记录
                        {
                            string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (devid != "" && devid.Contains("汇总"))
                            {
                                dateKey += devid;
                            }
                            else
                            {
                                continue;
                            }
                        }
                        dateKey += ":";
                        if (isByBoth)  //显示含有设备号或汇总的记录
                        {
                            string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (devid != "")
                            {
                                dateKey += devid;
                            }
                        }
                        string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
                        if (!selectedCityNames.Contains(cityName))
                        {
                            continue;
                        }
                        KPIResultInfo ginfo = null;
                        if (!retDic.TryGetValue(dateKey, out ginfo))
                        {
                            retDic[dateKey] = retInfo.copyInstance();
                            retDic[dateKey].strname = dateKey;
                        }
                        else
                        {
                            dateKey = retDic.Count + "位置" + dateKey;  //同一个人含多个记录，用其在数据库中记录的位置作标记
                            retDic[dateKey] = retInfo.copyInstance();
                            retDic[dateKey].strname = dateKey;
                        }
                    }
                    break;
                }
            }
            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                objList.Add(getMonthStr(ginfo.stime));

                string[] strs = ginfo.strname.Split(':');
                string strShowName = "";
                if (strs.Length == 5 && strs[1].Length > 0)
                {
                    strShowName += strs[1];
                }
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByWeek(bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("地市");
            KPIPopTable kpiPopTable = curRetDataDic[panelName];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (kpiPopTable.tablename == tableData.tablename)
                {
                    tableData.reloadDataResult((MainModel.User.DBID == -1), "", "");
                    foreach (KPIResultInfo retInfo in tableData.dataResult)
                    {
                        setTime(getWeekBeginTime(retInfo.stime));
                        setTime(getWeekEndTime(retInfo.stime));

                        string dateKey = "";
                        if (tablekind == TableKind.phone)
                        {
                            dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["name"]].ToString();
                            if (retInfo.valueList[tableData.keyIndexDic["name"]].ToString() == "")
                            {
                                dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                            }
                        }
                        else if (tablekind == TableKind.rcu)
                        {
                            dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (retInfo.valueList[tableData.keyIndexDic["devid"]].ToString() == "")
                            {
                                dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                            }
                        }
                        dateKey += ":";
                        dateKey = dateKey + DistrictManager.GetInstance().getDistrictName(retInfo.dbid);  //记录地市名
                        dateKey += ":";
                        if (isByEquipment)  //显示含有设备号的记录
                        {
                            string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (devid != "" && !devid.Contains("汇总"))
                            {
                                dateKey += devid;
                            }
                            else
                            {
                                continue;
                            }
                        }
                        dateKey += ":";
                        if (isByGather)  //显示含有汇总的记录
                        {
                            string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (devid != "" && devid.Contains("汇总"))
                            {
                                dateKey += devid;
                            }
                            else
                            {
                                continue;
                            }
                        }
                        dateKey += ":";
                        if (isByBoth)  //显示含有设备号或汇总的记录
                        {
                            string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                            if (devid != "")
                            {
                                dateKey += devid;
                            }
                        }
                        string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
                        if (!selectedCityNames.Contains(cityName))
                        {
                            continue;
                        }
                        KPIResultInfo ginfo = null;
                        if (!retDic.TryGetValue(dateKey, out ginfo))
                        {
                            retDic[dateKey] = retInfo.copyInstance();
                            retDic[dateKey].strname = dateKey;
                        }
                        else
                        {
                            dateKey = retDic.Count + "位置" + dateKey;  //同一个人含多个记录，用其在数据库中记录的位置作标记
                            retDic[dateKey] = retInfo.copyInstance();
                            retDic[dateKey].strname = dateKey;
                        }
                    }
                    break;
                }
            }
            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));

                string[] strs = ginfo.strname.Split(':');
                string strShowName = "";
                if (strs.Length == 5 && strs[1].Length > 0)
                {
                    strShowName += strs[1];
                }
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByThisMonth(bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("地市");
            KPIPopTable kpiPopTable = curRetDataDic[panelName];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();

            DateTime dtNow = DateTime.Now;
            DateTime dtThisMonthBeginTime = new DateTime(dtNow.Year, dtNow.Month, 1).ToLocalTime();
            int thisMonthTime = (int)(JavaDate.GetMilliseconds(dtThisMonthBeginTime) / 1000L);

            setTime(thisMonthTime);
            setTime(getMonthEndTime(thisMonthTime));

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (kpiPopTable.tablename == tableData.tablename)
                {
                    tableData.reloadDataResult((MainModel.User.DBID == -1), "", "");
                    foreach (KPIResultInfo retInfo in tableData.dataResult)
                    {
                        if (thisMonthTime == getMonthBeginTime(retInfo.stime))
                        {
                            string dateKey = "";
                            if (tablekind == TableKind.phone)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["name"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["name"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            else if (tablekind == TableKind.rcu)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["devid"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            dateKey += ":";
                            dateKey = dateKey + DistrictManager.GetInstance().getDistrictName(retInfo.dbid);  //记录地市名
                            dateKey += ":";
                            if (isByEquipment)  //显示含有设备号的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && !devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByGather)  //显示含有汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByBoth)  //显示含有设备号或汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "")
                                {
                                    dateKey += devid;
                                }
                            }
                            string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
                            if (!selectedCityNames.Contains(cityName))
                            {
                                continue;
                            }
                            KPIResultInfo ginfo = null;
                            if (!retDic.TryGetValue(dateKey, out ginfo))
                            {
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                            else
                            {
                                dateKey = retDic.Count + "位置" + dateKey;  //同一个人含多个记录，用其在数据库中记录的位置作标记
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                        }
                    }
                    break;
                }
            }
            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                objList.Add(getMonthStr(ginfo.stime));
                string[] strs = ginfo.strname.Split(':');
                string strShowName = "";
                if (strs.Length == 5 && strs[1].Length > 0)
                {
                    strShowName += strs[1];
                }
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByThisWeek(bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("地市");
            KPIPopTable kpiPopTable = curRetDataDic[panelName];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();

            DateTime dtNow = DateTime.Now;
            int nowTime = (int)(JavaDate.GetMilliseconds(dtNow) / 1000L);
            int thisWeekTime = getWeekBeginTime(nowTime);

            setTime(thisWeekTime);
            setTime(getWeekEndTime(thisWeekTime));

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (kpiPopTable.tablename == tableData.tablename)
                {
                    tableData.reloadDataResult((MainModel.User.DBID == -1), "", "");
                    foreach (KPIResultInfo retInfo in tableData.dataResult)
                    {
                        if (thisWeekTime == getWeekBeginTime(retInfo.stime))
                        {
                            string dateKey = "";
                            if (tablekind == TableKind.phone)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["name"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["name"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            else if (tablekind == TableKind.rcu)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["devid"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            dateKey += ":";
                            dateKey = dateKey + DistrictManager.GetInstance().getDistrictName(retInfo.dbid);  //记录地市名
                            dateKey += ":";
                            if (isByEquipment)  //显示含有设备号的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && !devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByGather)  //显示含有汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByBoth)  //显示含有设备号或汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "")
                                {
                                    dateKey += devid;
                                }
                            }
                            string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
                            if (!selectedCityNames.Contains(cityName))
                            {
                                continue;
                            }
                            KPIResultInfo ginfo = null;
                            if (!retDic.TryGetValue(dateKey, out ginfo))
                            {
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                            else
                            {
                                dateKey = retDic.Count + "位置" + dateKey;  //同一个人含多个记录，用其在数据库中记录的位置作标记
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                        }
                    }
                    break;
                }
            }

            minTime = thisWeekTime;
            maxTime = thisWeekTime + 6 * 24 * 3600;

            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));

                string[] strs = ginfo.strname.Split(':');
                string strShowName = "";
                if (strs.Length == 5 && strs[1].Length > 0)
                {
                    strShowName += strs[1];
                }
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private FinalShowResult prepareShowByBackOneWeek(bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("地市");
            KPIPopTable kpiPopTable = curRetDataDic[panelName];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();

            DateTime dtNow = DateTime.Now;
            DateTime dtBackOneWeek = dtNow.AddDays(-7);
            int backOneWeekTime = (int)(JavaDate.GetMilliseconds(dtBackOneWeek) / 1000L);
            int backOneWeekBeginTime = getWeekBeginTime(backOneWeekTime);

            setTime(backOneWeekBeginTime);
            setTime(getWeekEndTime(backOneWeekBeginTime));
            bool isProvUser = (MainModel.User.DBID == -1);

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (kpiPopTable.tablename == tableData.tablename)
                {
                    tableData.reloadDataResult(isProvUser, "", "");
                    foreach (KPIResultInfo retInfo in tableData.dataResult)
                    {
                        if (backOneWeekBeginTime == getWeekBeginTime(retInfo.stime))
                        {
                            string dateKey = "";
                            if (tablekind == TableKind.phone)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["name"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["name"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            else if (tablekind == TableKind.rcu)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["devid"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            dateKey += ":";
                            dateKey = dateKey + DistrictManager.GetInstance().getDistrictName(retInfo.dbid);  //记录地市名
                            dateKey += ":";
                            if (isByEquipment)  //显示含有设备号的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && !devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByGather)  //显示含有汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByBoth)  //显示含有设备号或汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "")
                                {
                                    dateKey += devid;
                                }
                            }
                            string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
                            if (!selectedCityNames.Contains(cityName))
                            {
                                continue;
                            }
                            KPIResultInfo ginfo = null;
                            if (!retDic.TryGetValue(dateKey, out ginfo))
                            {
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                            else
                            {
                                dateKey = retDic.Count + "位置" + dateKey;  //同一个人含多个记录，用其在数据库中记录的位置作标记
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                        }
                    }
                    break;
                }
            }

            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));

                string[] strs = ginfo.strname.Split(':');
                string strShowName = "";
                if (strs.Length == 5 && strs[1].Length > 0)
                {
                    strShowName += strs[1];
                }
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        } //前一周

        private FinalShowResult prepareShowByOneBackMonth(bool isByEquipment, bool isByGather, bool isByBoth, List<string> selectedCityNames) //前一月
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("地市");

            KPIPopTable kpiPopTable = curRetDataDic[panelName];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();

            DateTime dtBackOneMonthTime = DateTime.Now.AddMonths(-1);
            DateTime dtBackOneMonthBeginTime = new DateTime(dtBackOneMonthTime.Year, dtBackOneMonthTime.Month, 1).ToLocalTime();
            int backOneMonthTime = (int)(JavaDate.GetMilliseconds(dtBackOneMonthBeginTime) / 1000L);

            setTime(backOneMonthTime);
            setTime(getMonthEndTime(backOneMonthTime));

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (kpiPopTable.tablename == tableData.tablename)
                {
                    tableData.reloadDataResult((MainModel.User.DBID == -1), "", "");
                    foreach (KPIResultInfo retInfo in tableData.dataResult)
                    {
                        if (backOneMonthTime == getMonthBeginTime(retInfo.stime))
                        {
                            string dateKey = "";
                            if (tablekind == TableKind.phone)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["name"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["name"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            else if (tablekind == TableKind.rcu)
                            {
                                dateKey = getMonthStr(retInfo.stime) + "_" + retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (retInfo.valueList[tableData.keyIndexDic["devid"]].ToString() == "")
                                {
                                    dateKey = retDic.Count + 1 + "位置汇总" + dateKey;
                                }
                            }
                            dateKey += ":";
                            dateKey = dateKey + DistrictManager.GetInstance().getDistrictName(retInfo.dbid);  //记录地市名
                            dateKey += ":";
                            if (isByEquipment)  //显示含有设备号的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && !devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByGather)  //显示含有汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "" && devid.Contains("汇总"))
                                {
                                    dateKey += devid;
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            dateKey += ":";
                            if (isByBoth)  //显示含有设备号或汇总的记录
                            {
                                string devid = retInfo.valueList[tableData.keyIndexDic["devid"]].ToString();
                                if (devid != "")
                                {
                                    dateKey += devid;
                                }
                            }
                            string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
                            if (!selectedCityNames.Contains(cityName))
                            {
                                continue;
                            }
                            KPIResultInfo ginfo = null;
                            if (!retDic.TryGetValue(dateKey, out ginfo))
                            {
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                            else
                            {
                                dateKey = retDic.Count + "位置" + dateKey;  //同一个人含多个记录，用其在数据库中记录的位置作标记
                                retDic[dateKey] = retInfo.copyInstance();
                                retDic[dateKey].strname = dateKey;
                            }
                        }
                    }
                    break;
                }
            }
            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                objList.Add(getMonthStr(ginfo.stime));

                string[] strs = ginfo.strname.Split(':');
                string strShowName = "";
                if (strs.Length == 5 && strs[1].Length > 0)
                {
                    strShowName += strs[1];
                }
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private int getWeekBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            long seconds = JavaDate.GetMilliseconds(dtbegin) / 1000;
            return (int)seconds;
        }

        private int getWeekEndTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            DateTime dtEnd = dtbegin.AddDays(6);
            long seconds = JavaDate.GetMilliseconds(dtEnd) / 1000;
            return (int)seconds;
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }

        private int getMonthBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            return (int)(JavaDate.GetMilliseconds(dtbegin) / 1000);
        }

        private int getMonthEndTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            DateTime dtEnd = dtbegin.AddMonths(1).AddDays(-1);
            return (int)(JavaDate.GetMilliseconds(dtEnd) / 1000);
        }

        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private void showInGrid(FinalShowResult showRet)
        {
            dataGridView.Rows.Clear();
            dataGridView.Columns.Clear();
            if (showRet==null)
            {
                return;
            }
            for (int i = 0; i < showRet.columnNames.Count; i++)
            {
                string columnname = showRet.columnNames[i];
                dataGridView.Columns.Add("Column" + i, columnname);
                if (columnname == "姓名" || columnname.Contains("车速"))
                    dataGridView.Columns[i].Visible = false;

            }
            dataGridView.Columns[0].SortMode = DataGridViewColumnSortMode.Automatic;
            if (showRet.dataRows.Count > 0)
            {
                int indexRowAt = 0;
                for (int r = 0; r < showRet.dataRows.Count; r++)
                {
                    List<object> dataRow = showRet.dataRows[r];
                    indexRowAt = AddRowToGrid(indexRowAt, dataRow);
                }
            }
            dataGridView.Sort(dataGridView.Columns[0], ListSortDirection.Descending);
            dataGridView.Columns[0].Frozen = true;
            dataGridView.Columns[1].Frozen = true;
            
            curShowType = cbxShowType.SelectedItem.ToString();
            selectedDateStrs = new List<string>();
            foreach (CheckedListBoxItem item in checkedCbxDate.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    selectedDateStrs.Add(item.Value.ToString());
                }
            }
        }

        private int AddRowToGrid(int indexRowAt, List<object> dataRow)
        {
            if (checkedCbxDate.Enabled && !checkedCbxDate.Text.Contains((string)dataRow[0]))
            {
                return indexRowAt;
            }
            dataGridView.Rows.Add(1);
            for (int c = 0; c < dataRow.Count; c++)
            {
                object dv = dataRow[c];
                if (dv is DateTime)
                {
                    dataGridView.Rows[indexRowAt].Cells[c].Value = ((DateTime)dv).ToString("yyyy-MM-dd");
                }
                else
                {
                    dataGridView.Rows[indexRowAt].Cells[c].Value = dataRow[c];
                }
            }
            indexRowAt++;
            return indexRowAt;
        }

        private Dictionary<string, List<KPIResultInfo>> buildCityStruct(List<KPIResultInfo> resultList)
        {
            Dictionary<string, List<KPIResultInfo>> cityDic = new Dictionary<string, List<KPIResultInfo>>();
            foreach (KPIResultInfo info in resultList)
            {
                string strcity = DistrictManager.GetInstance().getDistrictName(info.dbid);
                List<KPIResultInfo> list = null;
                if (!cityDic.TryGetValue(strcity, out list))
                {
                    list = new List<KPIResultInfo>();
                    cityDic[strcity] = list;
                }
                list.Add(info);
            }
            return cityDic;
        }

        private List<KPIResultInfo> queryResultFromHeader(BackgroundWorker worker, KPIPopTable hdUnit, int dbid)
        {
            List<KPIResultInfo> retList = new List<KPIResultInfo>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);

                package.Content.AddParam(hdUnit.tablename);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        hdUnit.icoltype5NeedDivide1000 = false;
                        KPIResultInfo retItem = hdUnit.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }

        }
        private Dictionary<string, KPIPopTable> queryPopEntry(BackgroundWorker worker)
        {
            Dictionary<string, KPIPopTable> entryDicList = new Dictionary<string, KPIPopTable>();
            ClientProxy clientProxy = new ClientProxy();
            string username;
            string password;
            int dbid;

            username = MainModel.MainDbUser.LoginName;
            password = MainModel.MainDbUser.Password;
            dbid = 2;

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, dbid) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return entryDicList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_CONFIG_KPI_TABLE;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_CONFIG_KPI_TABLE)
                    {
                        package.Content.PrepareGetParam();
                        PopKPIEntryItem entry = new PopKPIEntryItem();
                        entry.Fill(package.Content); //数据库表tb_popkpi_entry
                        if (entry.strtablename.Contains("tb_popkpi_wxwljc"))  //显示无线网络监测相关的表
                        //if (entry.strtablename.Equals("tb_popkpi_wxwljc_phone_wk_cmpstat"))  //手机测试完成率周报 注意:当前只显示此表
                        {
                            KPIPopTable headerUnit = null;
                            if (!entryDicList.TryGetValue(entry.strtablename, out headerUnit))
                            {
                                headerUnit = new KPIPopTable();
                                headerUnit.tablename = entry.strtablename;
                                entryDicList[headerUnit.tablename] = headerUnit;
                            }
                            headerUnit.entryList.Add(entry);
                        }
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return entryDicList;
            }
            finally
            {
                clientProxy.Close();
            }
        }
        #endregion


        #region PopShowPanelInterface 成员


        public void SetMainModal(MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion

        private void btnFresh_Click(object sender, EventArgs e)
        {
            refreshShowReport(true);
            resetTime();
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            PopKPIQueryCondition queryCondition = backStacks.Pop();
            forwardStacks.Push(queryCondition);
            queryCondition = backStacks.Peek();
            updatePanel(queryCondition);
        }

        private void btnForward_Click(object sender, EventArgs e)
        {
            PopKPIQueryCondition queryCondition = forwardStacks.Pop();
            backStacks.Push(queryCondition);
            updatePanel(queryCondition);
        }

        private void updatePanel(PopKPIQueryCondition queryCondition)
        {
            btnBack.Enabled = backStacks.Count > 1;
            btnForward.Enabled = forwardStacks.Count > 0;

            foreach (object item in cbxShowType.Items)
            {
                if (item.ToString() == queryCondition.showType)
                {
                    cbxShowType.SelectedItem = item;
                }
            }

            checkedCbxDate.Properties.Items.Clear();
            foreach (string key in queryCondition.DateMap.Keys)
            {
                checkedCbxDate.Properties.Items.Add(key, queryCondition.DateMap[key]);
            }

            this.toolStripDropDownCity.Tag = queryCondition.CheckedCityNames;

            this.radioButtonByEquipment.Checked = queryCondition.isByEquipment;
            this.radioButtonByGather.Checked = queryCondition.isByGather;
            this.radioButtonByBoth.Checked = queryCondition.isByEquipmentAndGather;

            updateToolTip();

            refreshShowReport(false);
            resetTime();
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxShowType.Text == "按月" || cbxShowType.Text == "按周" || cbxShowType.Text == "按天")
            {
                labelDate.Enabled = true;
                checkedCbxDate.Enabled = true;
            }
            else
            {
                labelDate.Enabled = false;
                checkedCbxDate.Enabled = false;
            }

            checkedCbxDate.Properties.Items.Clear();
            List<int> timeList = new List<int>();
            foreach (KPIResultInfo retInfo in allKPIResultInfoList)
            {
                timeList.Add(retInfo.stime);
            }
            timeList.Sort();
            List<string> showTypeStrs = getShowTypeStrs(timeList);

            checkedCbxDate.Properties.Items.Clear();
            for (int i = showTypeStrs.Count - 1; i >= 0; i--)
            {
                checkedCbxDate.Properties.Items.Add(showTypeStrs[i], true);
            }
        }

        private List<string> getShowTypeStrs(List<int> timeList)
        {
            List<string> showTypeStrs = new List<string>();
            if (cbxShowType.Text == "前一周" || cbxShowType.Text == "本周" || cbxShowType.Text == "按周")
            {
                foreach (int time in timeList)
                {
                    string typeStr = getWeekStr(time);
                    if (!showTypeStrs.Contains(typeStr))
                    {
                        showTypeStrs.Add(typeStr);
                    }
                }
            }
            else if (cbxShowType.Text == "前一月" || cbxShowType.Text == "本月" || cbxShowType.Text == "按月")
            {
                foreach (int time in timeList)
                {
                    string typeStr = getMonthStr(time);
                    if (!showTypeStrs.Contains(typeStr))
                    {
                        showTypeStrs.Add(typeStr);
                    }
                }
            }

            return showTypeStrs;
        }

        private void btnShowCityNames_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnShowCityNames.Width, btnShowCityNames.Height);
            toolStripDropDownCity.Show(this.btnShowCityNames, pt, ToolStripDropDownDirection.BelowLeft);
        }

        public void ExportInfo(ExcelControl excel)
        {
            KPIExport2XlsParam excelParam = new KPIExport2XlsParam();
            excelParam.excelApp = excel;
            excelParam.dgv = dataGridView;
            string title = "";
            title += cbxShowType.SelectedItem.ToString();
            excelParam.title = textboxProject.Text + "_" + title;
            WaitBox.Show(this, fireExp2Xls, excelParam);
        }

        private void fireExp2Xls(object param)
        {
            WaitBox.Text = "正在导出...";
            KPIExport2XlsParam excelParam = param as KPIExport2XlsParam;
            excelParam.excelApp.Sheet.Name = excelParam.title;
            excelParam.excelApp.ExportExcel(excelParam.excelApp.Sheet, excelParam.dgv);

            WaitBox.Close();
        }

        private void btnExportAll_Click(object sender, EventArgs e)
        {
            WelcomForm welFrm = this.Parent.Parent.Parent as WelcomForm;
            if (welFrm != null)
            {
                welFrm.ExpWNMonitorAll();
            }
        }

        private void btnExportThisPage_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Title = "选择要保存文档的路径";
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    excel.CreateSheet();
                    this.ExportInfo(excel);
                    excel.SaveFile(dlg.FileName);

                }
                catch (Exception)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("导出数据出错！");
                    return;
                }
                finally
                {
                    excel.CloseExcel();
                    WaitBox.Close();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(dlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + dlg.FileName);
                    }
                }
            }
        }

        public class KPIExport2XlsParam
        {
            public ExcelControl excelApp { get; set; }
            public DataGridView dgv { get; set; }
            public string title { get; set; }
        }
    }

    public class CityOrder
    {
        private readonly Dictionary<int, int> dbIDCityPriorityID = new Dictionary<int, int>();//数据库地市ID,地市优先级ID

        private static CityOrder instance = null;
        public static CityOrder getInstance()
        {
            if (instance == null)
            {
                instance = new CityOrder();
            }
            return instance;
        }
        public int getCityPriorityID(int cityID)
        {
            int priorityID;
            dbIDCityPriorityID.TryGetValue(cityID, out priorityID);
            return priorityID;
        }

        public int getCityidByPriority(int priorityID)
        {
            foreach (int dbid in dbIDCityPriorityID.Keys)
            {
                if (dbIDCityPriorityID[dbid] == priorityID)
                {
                    return dbid;
                }
            }
            return -999;
        }
        public CityOrder()
        {
            dbIDCityPriorityID.Clear();
            dbIDCityPriorityID.Add(2, 21);
            dbIDCityPriorityID.Add(3, 20);
            dbIDCityPriorityID.Add(4, 16);
            dbIDCityPriorityID.Add(5, 17);
            dbIDCityPriorityID.Add(6, 18);
            dbIDCityPriorityID.Add(7, 7);
            dbIDCityPriorityID.Add(8, 3);
            dbIDCityPriorityID.Add(9, 4);
            dbIDCityPriorityID.Add(10, 14);
            dbIDCityPriorityID.Add(11, 2);
            dbIDCityPriorityID.Add(12, 19);
            dbIDCityPriorityID.Add(13, 15);
            dbIDCityPriorityID.Add(14, 13);
            dbIDCityPriorityID.Add(15, 5);
            dbIDCityPriorityID.Add(16, 12);
            dbIDCityPriorityID.Add(17, 11);
            dbIDCityPriorityID.Add(18, 9);
            dbIDCityPriorityID.Add(19, 10);
            dbIDCityPriorityID.Add(20, 6);
            dbIDCityPriorityID.Add(21, 8);
            dbIDCityPriorityID.Add(22, 1);
        }
    }

    public class PopKPIQueryCondition //欢迎界面KPI查询条件
    {
        public PopKPIQueryCondition()
        {
            CheckedProjects = new List<string>();
            showType = "";
            CheckedCityNames = new List<string>();
            DateMap = new Dictionary<string, bool>();
            isByEquipment = false;
            isByGather = false;
            isByEquipmentAndGather = false;
        }

        public List<string> CheckedProjects { get; set; } //已选的项目
        public string showType { get; set; }//已选的周期时间
        public List<string> CheckedCityNames { get; set; }//已选的城市名
        public Dictionary<string, bool> DateMap { get; set; }//已选的时间段
        public bool isByEquipment { get; set; }
        public bool isByGather { get; set; }
        public bool isByEquipmentAndGather { get; set; }

        public void AddCheckedProject(string checkedProject)
        {
            this.CheckedProjects.Add(checkedProject);
        }

        public void SetShowType(string showType)
        {
            this.showType = showType;
        }

        public void AddCheckedCityName(string cityName)
        {
            this.CheckedCityNames.Add(cityName);
        }

        public void AddDate(string dateDesc, bool isCheckedDate)
        {
            this.DateMap[dateDesc] = isCheckedDate;
        }

        public void SetIsByEquipment(bool isByEquipment)
        {
            this.isByEquipment = isByEquipment;
        }

        public void SetIsByGather(bool isByGather)
        {
            this.isByGather = isByGather;
        }

        public void SetIsByBoth(bool isByBoth)
        {
            this.isByEquipmentAndGather = isByBoth;
        }
    }

}
