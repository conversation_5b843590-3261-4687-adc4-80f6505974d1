﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTMainCellLastOccupyResultForm : MinCloseForm
    {
        public ZTMainCellLastOccupyResultForm()
        {
            InitializeComponent();
            Init();
        }

        private void Init()
        {
            #region  获得界面数据

            setCellLastOccupyFileInfo();

            this.olvColumnStatSN.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyFileInfo)
                {
                    CellLastOccupyFileInfo file = row as CellLastOccupyFileInfo;
                    return file.Sn;
                }
                else if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.Sn;
                }
                return "";
            };

            this.olvColumnStatCellName.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.CellName;
                }
                return "";
            };

            this.olvColumnECI.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.ECI;
                }
                return "";
            };

            this.olvColumnTAC.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.TAC;
                }
                return "";
            };

            this.olvColumnDuration.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.Duration;
                }
                return "";
            };

            this.olvColumnLongitude.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.Longitude;
                }
                return "";
            };

            this.olvColumnLatitude.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.Latitude;
                }
                return "";
            };

            this.olvColumnTime.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.Time;
                }
                return "";
            };

            this.olvColumnAvgRSRP.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.AvgRSRP;
                }
                return "";
            };

            this.olvColumnAvgSINR.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.AvgSINR;
                }
                return "";
            };

            this.olvColumnRSRPNum.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.SmallRSRPNum;
                }
                return "";
            };

            this.olvColumnSINRNum.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.SmallSINRNum;
                }
                return "";
            };

            this.olvColumnTestPointNum.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyCellInfo)
                {
                    CellLastOccupyCellInfo cell = row as CellLastOccupyCellInfo;
                    return cell.TestPntList.Count;
                }
                return "";
            };

            #endregion
        }

        private void setCellLastOccupyFileInfo()
        {
            this.treeViewCellOccupy.CanExpandGetter = delegate (object row)
            {
                return row is CellLastOccupyFileInfo;
            };

            this.treeViewCellOccupy.ChildrenGetter = delegate (object row)
            {
                if (row is CellLastOccupyFileInfo)
                {
                    CellLastOccupyFileInfo file = row as CellLastOccupyFileInfo;
                    return file.CellInfoList;
                }
                return null;
            };

            this.olvColumnFileName.AspectGetter = delegate (object row)
            {
                if (row is CellLastOccupyFileInfo)
                {
                    CellLastOccupyFileInfo file = row as CellLastOccupyFileInfo;
                    return file.FileName;
                }
                return "";
            };
        }

        public void FillData(List<CellLastOccupyFileInfo> resultList)
        {
            treeViewCellOccupy.RebuildColumns();
            treeViewCellOccupy.ClearObjects();
            treeViewCellOccupy.SetObjects(resultList);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(treeViewCellOccupy);
        }

        private void treeViewCellOccupy_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = this.treeViewCellOccupy.OlvHitTest(e.X, e.Y);
            if (info.RowObject is CellLastOccupyFileInfo)
            {
                MainModel.ClearDTData();
                CellLastOccupyFileInfo file = info.RowObject as CellLastOccupyFileInfo;
                foreach (CellLastOccupyCellInfo cell in file.CellInfoList)
                {
                    foreach (TestPoint tp in cell.TestPntList)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                }
            }
            else if (info.RowObject is CellLastOccupyCellInfo)
            {
                MainModel.ClearDTData();
                CellLastOccupyCellInfo cell = info.RowObject as CellLastOccupyCellInfo;
                foreach (TestPoint tp in cell.TestPntList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            this.MainModel.IsFileReplayByCompareMode = false;
            this.MainModel.FireDTDataChanged(this);
            this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP","LTE_FDD:RSRP");
        }
    }
}
