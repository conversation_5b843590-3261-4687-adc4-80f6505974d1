﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHttpPageSettingDlg : BaseDialog
    {
        public NRHttpPageSettingDlg(NRHttpPageCondion condition)
        {
            InitializeComponent();
            setCondition(condition);
        }
        private void setCondition(NRHttpPageCondion condition)
        {
            numGreatPageTime.Value = condition.GreatPageTime;
            numLessPageTime.Value = condition.LessPageTime;
            cbNoGreat.Checked = condition.IsNoGreat;
            numPreLoad.Value = condition.PreLoadTime;
        }
        public NRHttpPageCondion GetCondition()
        {
            NRHttpPageCondion condition = new NRHttpPageCondion();
            condition.GreatPageTime = (int)numGreatPageTime.Value;
            condition.LessPageTime = (int)numLessPageTime.Value;
            condition.IsNoGreat = cbNoGreat.Checked;
            condition.PreLoadTime = (int)numPreLoad.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void numGreatPageTime_ValueChanged(object sender, EventArgs e)
        {
            if ((int)numGreatPageTime.Value < (int)numLessPageTime.Value)
            {
                numGreatPageTime.Value = numLessPageTime.Value;
            }
        }

        private void numLessPageTime_ValueChanged(object sender, EventArgs e)
        {
            if ((int)numLessPageTime.Value > (int)numGreatPageTime.Value)
            {
                numLessPageTime.Value = numGreatPageTime.Value;
            }
        }

        private void cbNoGreat_CheckedChanged(object sender, EventArgs e)
        {
            if (cbNoGreat.Checked)
            {
                numGreatPageTime.Enabled = false;
            }
            else
            {
                numGreatPageTime.Enabled = true;
            }
        }
    }
}
