﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP.BatchImport
{
    public partial class BatchImportConfirmForm : BaseDialog
    {
        public BatchImportConfirmForm()
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnContinue.Click += BtnContinue_Click;
        }

        public void FillData(List<BatchImportItem> importList)
        {
            BatchImportItemView view = new BatchImportItemView();
            view.FillData(new List<BatchImportItem>(importList));
            view.Dock = DockStyle.Fill;

            panelView.Controls.Clear();
            panelView.Controls.Add(view);

            bool hasValidItem = false;
            foreach (BatchImportItem item in importList)
            {
                if (item.IsValid)
                {
                    hasValidItem = true;
                }
            }
            btnContinue.Enabled = hasValidItem;
        }

        private void BtnContinue_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }
}
