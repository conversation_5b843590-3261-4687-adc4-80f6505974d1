﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.BandedGrid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.CellParam;

namespace MasterCom.RAMS.Model
{
    public partial class CellSignParamForm : MinCloseForm
    {
        protected List<CellParamInfo> cellParamResults = null;
        public CellSignParamForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<CellParamInfo> results, Dictionary<CellParamTable, List<CellParamColumn>> tableColsDic)
        {
            fillView(results, tableColsDic);
        }

        private void fillView(List<CellParamInfo> results, Dictionary<CellParamTable, List<CellParamColumn>> tableColsDic)
        {
            bandedView.Bands.Clear();
            bandedView.Columns.Clear();

            string fieldName = "小区名";
            DataTable tb = new DataTable();
            tb.Columns.Add(fieldName, typeof(CellParamInfo));
            GridBand band = createGVBand("基本信息");
            band.Fixed = FixedStyle.Left;
            BandedGridColumn gvCol = createGVCol(fieldName, fieldName);
            gvCol.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            gvCol.Fixed = FixedStyle.Left;
            bandedView.Bands.Add(band);
            band.Columns.Add(gvCol);
            bandedView.Columns.Add(gvCol);

            fieldName = "记录时间";
            tb.Columns.Add(fieldName, typeof(DateTime));
            gvCol = createGVCol(fieldName, fieldName);
            gvCol.Fixed = FixedStyle.Left;
            band.Columns.Add(gvCol);
            bandedView.Columns.Add(gvCol);

            foreach (KeyValuePair<CellParamTable, List<CellParamColumn>> kvp in tableColsDic)
            {
                band = createGVBand(kvp.Key.Alias);
                bandedView.Bands.Add(band);
                foreach (CellParamColumn col in kvp.Value)
                {
                    tb.Columns.Add(col.FullName, typeof(object));
                    gvCol = createGVCol(col.FullName, col.Alias);
                    band.Columns.Add(gvCol);
                    bandedView.Columns.Add(gvCol);
                }
            }
            foreach (CellParamInfo info in results)
            {
                foreach (CellParamRecord record in info.Records)
                {
                    DataRow row = tb.NewRow();
                    row["小区名"] = info;
                    row["记录时间"] = record.RecordTime;
                    foreach (TableParamRecord tableRec in record.TableRecords)
                    {
                        foreach (CellParamColumn col in tableRec.ColValueDic.Keys)
                        {
                            row[col.FullName] = tableRec.ColValueDic[col];
                        }
                    }
                    tb.Rows.Add(row);
                }
            }
            gridCtrl.DataSource = tb;
            gridCtrl.RefreshDataSource();
            bandedView.BestFitColumns();
        }

        private BandedGridColumn createGVCol(string fieldName, string caption)
        {
            BandedGridColumn gvCol = new BandedGridColumn();
            gvCol.FieldName = fieldName;
            gvCol.Caption = caption;
            gvCol.Visible = true;
            gvCol.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            return gvCol;
        }

        private GridBand createGVBand(string caption)
        {
            GridBand band = new GridBand();
            band.Caption = caption;
            return band;
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Excel2007文件(*.xlsx)|*.xlsx";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                DevExpress.XtraPrinting.XlsxExportOptions options = new DevExpress.XtraPrinting.XlsxExportOptions();
                options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                gridCtrl.ExportToXlsx(dlg.FileName, options);
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
        }

        private void bandedView_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = bandedView.GetSelectedRows();
            if (rows.Length==0)
            {
                return;
            }
            DataRow dr = bandedView.GetDataRow(rows[0]);
            CellParamInfo cellInfo = dr[0] as CellParamInfo;
            if (cellInfo==null)
            {
                return;
            }
            MainModel.SelectedCell = null;
            MainModel.SelectedTDCell = null;
            if (cellInfo.Cell is Cell)
            {
                Cell cell = cellInfo.Cell as Cell;
                MainModel.SelectedCell = cell;
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude, 6000);
            }
            else if (cellInfo.Cell is TDCell)
            {
                TDCell cell = cellInfo.Cell as TDCell;
                MainModel.SelectedTDCell = cell;
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude, 6000);
            }
        }


    }
}
