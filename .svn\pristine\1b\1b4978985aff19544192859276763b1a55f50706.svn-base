﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.NOP.DataSet;
using MasterCom.NOP.WF.Core;

namespace MasterCom.RAMS.NOP
{
    class RepeatStater
    {
        public List<RepeatSummaryTask> Stat(RepeatStatCondition cond)
        {
            TaskOrderQuerier querier = new TaskOrderQuerier(MainModel.GetInstance());

            // set schema
            Schema schema = querier.QuerySchema();
            RepeatHistoryTask.SNopSchema = schema;

            // query tasks
            List<Task> summaryTasks = QueryTasks(querier, cond.TpSummary.BeginTime, cond.TpSummary.EndTime);
            List<Task> historyTasks = QueryTasks(querier, cond.TpHistory.BeginTime, cond.TpHistory.EndTime);

            // repeat judge
            List<RepeatSummaryTask> retList = new List<RepeatSummaryTask>();

            if (summaryTasks.Count == 0|| historyTasks.Count == 0)
            {
                return retList;
            }

            foreach (Task sTask in summaryTasks)
            {
                double sLng = (double)sTask.GetValue("问题点经度");
                double sLat = (double)sTask.GetValue("问题点纬度");
                DateTime sTime = (DateTime)sTask.GetValue("工单月份");

                RepeatSummaryTask curRepeat = getCurRepeat(cond, historyTasks, sTask, sLng, sLat, sTime);

                // 如果出现过重复
                if (curRepeat != null)
                {
                    curRepeat.CalcResult();
                    retList.Add(curRepeat);
                }
            }

            // 按地市赋值SN
            SetCitySN(retList);
            return retList;
        }

        private static RepeatSummaryTask getCurRepeat(RepeatStatCondition cond, List<Task> historyTasks, Task sTask, double sLng, double sLat, DateTime sTime)
        {
            RepeatSummaryTask curRepeat = null;
            foreach (Task hTask in historyTasks)
            {
                if (sTask == hTask)
                {
                    continue;
                }

                // 历史工单迟于当前工单
                DateTime hTime = (DateTime)hTask.GetValue("工单月份");
                if (sTime < hTime)
                {
                    continue;
                }

                // 不在汇聚半径内
                double hLng = (double)hTask.GetValue("问题点经度");
                double hLat = (double)hTask.GetValue("问题点纬度");
                double dis = MathFuncs.GetDistance(sLng, sLat, hLng, hLat);
#if DEBUG
                if (dis > cond.GatherRadius * 1000)
#else
                    if (dis > cond.GatherRadius)
#endif
                {
                    continue;
                }

                // 首次创建加入自身
                RepeatHistoryTask hisRepeat = null;
                if (curRepeat == null)
                {
                    curRepeat = new RepeatSummaryTask(sTask);
                    hisRepeat = new RepeatHistoryTask(sTask);
                    curRepeat.AddHistoryTask(hisRepeat);
                }

                // 加入重复单
                hisRepeat = new RepeatHistoryTask(hTask);
                curRepeat.AddHistoryTask(hisRepeat);
            }

            return curRepeat;
        }

        protected void SetCitySN(List<RepeatSummaryTask> tasks)
        {
            Dictionary<string, int> citySnDic = new Dictionary<string, int>();
            foreach (RepeatSummaryTask t in tasks)
            {
                if (!citySnDic.ContainsKey(t.CityName))
                {
                    citySnDic.Add(t.CityName, 0);
                }
                t.SN = ++citySnDic[t.CityName];
            }

            tasks.Sort(new SummaryTaskComparer());
        }

        protected List<Task> QueryTasks(TaskOrderQuerier querier, DateTime sTime, DateTime eTime)
        {
            ICollection<Task> tasks = querier.Query(sTime, eTime);
            if (tasks == null)
            {
                return new List<Task>();
            }
            List<Task> retList = new List<Task>(tasks);
            retList.Sort(new TaskTimeComparer());
            retList.Reverse();
            return retList;
        }
    }

    class TaskTimeComparer : IComparer<Task>
    {
        public int Compare(Task x, Task y)
        {
            DateTime xTime = (DateTime)x.GetValue("工单月份");
            DateTime yTime = (DateTime)y.GetValue("工单月份");
            if (x == y || xTime == yTime)
            {
                return 0;
            }
            return xTime > yTime ? 1 : -1;
        }
    }

    class SummaryTaskComparer : IComparer<RepeatSummaryTask>
    {
        public int Compare(RepeatSummaryTask x, RepeatSummaryTask y)
        {
            if (x == y)
            {
                return 0;
            }

            if (x.CityName == y.CityName && x.SN == y.SN)
            {
                return 0;
            }
            else if (x.CityName == y.CityName)
            {
                return x.SN > y.SN ? 1 : -1;
            }
            return string.Compare(x.CityName, y.CityName);
        }
    }
}
