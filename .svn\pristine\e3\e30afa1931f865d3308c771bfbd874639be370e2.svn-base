﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanMod3IndexResultForm : MinCloseForm
    {
        public ScanMod3IndexResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();

            gvCell.DoubleClick += GridView_DoubleClick;
            gvSample.DoubleClick += GridView_DoubleClick;
            miExportExcel.Click += MiExportExcel_Click;
            xtraTabControl1.SelectedPageChanged += TabControl_SelectedChanged;
        }

        public void FillData(List<ScanMod3IndexSampleView> sampleViews, List<ScanMod3IndexCellView> cellViews)
        {
            sampleDic.Clear();
            foreach (ScanMod3IndexSampleView sample in sampleViews)
            {
                if (!sampleDic.ContainsKey(sample.Earfcn))
                {
                    sampleDic.Add(sample.Earfcn, new List<ScanMod3IndexSampleView>());
                }
                sampleDic[sample.Earfcn].Add(sample);
            }
            InitCbxEarfcn(new List<int>(sampleDic.Keys));
            CbxEarfcn_SelectedChanged(cbxEarfcn, EventArgs.Empty);

            cellViews.Sort();
            gcCell.DataSource = cellViews;
            gcCell.RefreshDataSource();
        }

        private void InitCbxEarfcn(List<int> earfcns)
        {
            cbxEarfcn.SelectedIndexChanged -= CbxEarfcn_SelectedChanged;
            cbxEarfcn.Items.Clear();
            earfcns.Sort();
            foreach (int e in earfcns)
            {
                cbxEarfcn.Items.Add(e);
            }
            if (earfcns.Count > 0)
            {
                cbxEarfcn.SelectedIndex = 0;
            }
            cbxEarfcn.SelectedIndexChanged += CbxEarfcn_SelectedChanged;
        }

        private void RefreshLayer()
        {
            List<ScanMod3IndexSampleView> sampleList = gcSample.DataSource as List<ScanMod3IndexSampleView>;
            List<ScanMod3IndexDrawItem> drawList = new List<ScanMod3IndexDrawItem>();
            foreach (ScanMod3IndexSampleView sample in sampleList)
            {
                ScanMod3IndexDrawItem drawItem = new ScanMod3IndexDrawItem(sample.TestPoint);
                drawItem.IsInvalid = sample.IsInvalid == "是";
                drawItem.IndexValue = sample.InterfereIndex;
                drawList.Add(drawItem);
            }

            MapForm mf = MainModel.MainForm.GetMapForm();
            mf.ScanMod3IndexLayer.Clear();
            mf.ScanMod3IndexLayer.Entitys2Draw = drawList;
            mf.ScanMod3IndexLayer.Legends.Add(mf.ScanMod3IndexLayer.Legend);
            mf.updateMap();
            mf.MainModel.RefreshLegend();
        }

        private void CbxEarfcn_SelectedChanged(object sender, EventArgs e)
        {
            if (cbxEarfcn.SelectedItem == null)
            {
                return;
            }

            int earfcn = (int)cbxEarfcn.SelectedItem;
            if (!sampleDic.ContainsKey(earfcn))
            {
                return;
            }

            gcSample.DataSource = sampleDic[earfcn];
            gcSample.RefreshDataSource();
            this.RefreshLayer();
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object o = gv.GetRow(gv.GetSelectedRows()[0]);

            MainModel.DTDataManager.Clear();
            MainModel.ClearSelectedTestPoints();

            if (o is ScanMod3IndexSampleView)
            {
                ScanMod3IndexSampleView sampleView = o as ScanMod3IndexSampleView;
                sampleView.TestPoint.Selected = true;
                MainModel.SelectedTestPoints.Add(sampleView.TestPoint);
                MainModel.DTDataManager.Add(sampleView.TestPoint);
                MainModel.MainForm.GetMapForm().GoToView(sampleView.TestPoint.Longitude, sampleView.TestPoint.Latitude);
                MainModel.FireSelectedTestPointsChanged(this);
            }
            else if (o is ScanMod3IndexCellView)
            {
                ScanMod3IndexCellView cellView = o as ScanMod3IndexCellView;
                MainModel.SelectedLTECell = cellView.LteCell;
                foreach (TestPoint tp in cellView.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.MainForm.GetMapForm().GoToView(cellView.LteCell.Longitude, cellView.LteCell.Latitude);
                MainModel.FireSelectedCellChanged(this);
            }

            MainModel.FireDTDataChanged(this);
            if (o is ScanMod3IndexCellView && MainModel.MainForm.GetMapForm().ExMapPanel != null)
            {
                MainModel.MainForm.GetMapForm().ExMapPanel.getMainMap().Zoom = 16;
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex == 0)
            {
                ExcelNPOIManager.ExportToExcel(gvSample);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(gvCell);
            }
        }

        private void TabControl_SelectedChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            mf.ScanMod3IndexLayer.IsVisible = xtraTabControl1.SelectedTabPageIndex == 0;

            if (xtraTabControl1.SelectedTabPageIndex == 0)
            {
                DTLayerSerialManager.Instance.ClearSelectedSerials();
                MainModel.RefreshLegend();
            }
            else
            {
                MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            }
            mf.updateMap();
        }

        private Dictionary<int, List<ScanMod3IndexSampleView>> sampleDic = new Dictionary<int, List<ScanMod3IndexSampleView>>();
    }
}
