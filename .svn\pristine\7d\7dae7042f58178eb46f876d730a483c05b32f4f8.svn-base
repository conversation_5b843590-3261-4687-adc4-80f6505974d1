﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTNBScanHighCoverateRoadQueryByRegion : ZTLTEScanHighCoverateRoadQueryByRegion
    {
        public ZTNBScanHighCoverateRoadQueryByRegion(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "高重叠覆盖路段_NB扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33012, this.Name);
        }
    }
}
