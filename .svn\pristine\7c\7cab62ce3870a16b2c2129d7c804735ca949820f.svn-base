﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VoiCoverSettingForm : BaseForm
    {
        private delegate void ConditionHandler(ref VoiCoverCondition cond);
        private ConditionHandler handler;
        public VoiCoverSettingForm(object caller)
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;

            Type t = caller.GetType();
            if (t == typeof(GSMBTSVoiCoverManager))
            {
                handler = GetGSMCondition;
            }
            if (t == typeof(DCSBTSVoiCoverManager))
            {
                handler = GetDCSCondtion;
            }
            else if (t == typeof(TDNodeBVoiCoverManager))
            {
                handler = GetTDCondtion;
            }
            else if (t == typeof(LTEBTSVoiCoverManager))
            {
                handler = GetLTECondition;
            }
            else if (t == typeof(WNodeBVoiCoverManager))
            {
                handler = GetWCDMACondition;
            }
        }

        public VoiCoverCondition GetCondition()
        {
            VoiCoverCondition cond = new VoiCoverCondition();
            handler(ref cond);
            return cond;
        }

        private void GetGSMCondition(ref VoiCoverCondition cond)
        {
            cond.GsmType = radioOutdoor.Checked ? BTSType.Outdoor : BTSType.Indoor;
        }

        private void GetDCSCondtion(ref VoiCoverCondition cond)
        {
            cond.GsmType = radioOutdoor.Checked ? BTSType.Outdoor : BTSType.Indoor;
        }

        private void GetTDCondtion(ref VoiCoverCondition cond)
        {
            cond.TdType = radioOutdoor.Checked ? TDNodeBType.Outdoor : TDNodeBType.Indoor;
        }

        private void GetLTECondition(ref VoiCoverCondition cond)
        {
            cond.LteType = radioOutdoor.Checked ? LTEBTSType.Outdoor : LTEBTSType.Indoor;
        }

        private void GetWCDMACondition(ref VoiCoverCondition cond)
        {
            cond.WType = radioOutdoor.Checked ? WNodeBType.Outdoor : WNodeBType.Indoor;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
