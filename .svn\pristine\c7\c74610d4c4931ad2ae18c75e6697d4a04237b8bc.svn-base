﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedRoadDlg_NR : BaseDialog
    {
        public LowSpeedRoadDlg_NR()
        {
            InitializeComponent();
        }

        public void SetCondition(LowSpeedRoadCondition_NR cond)
        {
            if (cond == null)
            {
                return;
            }

            radioApp.Checked = cond.SpeedTypeSelected == LowSpeedRoadCondition_NR.ESpeedType.APP;
            if (radioApp.Checked)
            {
                cmbThroughput.Text = LowSpeedRoadCondition_NR.ESpeedType.MAC.ToString();
            }
            else
            {
                cmbThroughput.Text = cond.SpeedTypeSelected.ToString();
            }

            radioThroughput.Checked = !radioApp.Checked;
            chkFTPDL.Checked = cond.CheckFTPDL;
            chkFTPUL.Checked = cond.CheckFTPUL;
            chkEmail.Checked = cond.CheckEmail;
            chkHttp.Checked = cond.CheckHTTP;
            chkVideo.Checked = cond.CheckVideo;
            chkThroughputDl.Checked = cond.CheckThroughputDl;
            chkThroughputUl.Checked = cond.CheckThroughputUl;

            numEmailMax.Value = (decimal)cond.EmailRateMax;
            numFTPDLMax.Value = (decimal)cond.FTPDLRateMax;
            numFTPULMax.Value = (decimal)cond.FTPULRateMax;
            numHTTPMax.Value = (decimal)cond.HTTPRateMax;
            numVideoMax.Value = (decimal)cond.VideoRateMax;

            numEmailMin.Value = (decimal)cond.EmailRateMin;
            numFTPDLMin.Value = (decimal)cond.FTPDLRateMin;
            numFTPULMin.Value = (decimal)cond.FTPULRateMin;
            numHTTPMin.Value = (decimal)cond.HTTPRateMin;
            numVideoMin.Value = (decimal)cond.VideoRateMin;

            numFTPDLDistance.Value = (decimal)cond.DistanceFTPDLMin;
            numFTPULDistance.Value = (decimal)cond.DistanceFTPULMin;
            numEMailDistance.Value = (decimal)cond.DistanceEMailMin;
            numHTTPDistance.Value = (decimal)cond.DistanceHTTPMin;
            numVideoDistance.Value = (decimal)cond.DistanceVideoMin;
            num2TpDis.Value = (decimal)cond.TestPointDistance;

            numThroughputDlMax.Value = (decimal)cond.ThroughputDlRateMax;
            numThroughputDlMin.Value = (decimal)cond.ThroughputDlRateMin;
            numThroughputUlMax.Value = (decimal)cond.ThroughputUlRateMax;
            numThroughputUlMin.Value = (decimal)cond.ThroughputUlRateMin;
            numThroughputUlDistance.Value = (decimal)cond.DistanceThroughputUlMin;
            numThroughputDlDistance.Value = (decimal)cond.DistanceThroughputDlMin;

            chkRsrp.Checked = cond.CheckRsrp;
            numRsrpMin.Value = (decimal)cond.RsrpMin;
            numRsrpMax.Value = (decimal)cond.RsrpMax;

            chkSinr.Checked = cond.CheckSinr;
            numSinrMin.Value = (decimal)cond.SinrMin;
            numSinrMax.Value = (decimal)cond.SinrMax;

            numLowPer.Value = (decimal)cond.LowPercent;

            //网络
            this.checkBoxSynthesisLowSpeed.Checked = cond.CheckSynthesisLowSpeed;
            this.checkBoxLTELowSpeed.Checked = cond.CheckLTELowSpeed;
            this.checkBoxLowSpeed_NR.Checked = cond.CheckNRLowSpeed;
        }

        public LowSpeedRoadCondition_NR GetCondition()
        {
            LowSpeedRoadCondition_NR cond = new LowSpeedRoadCondition_NR();
            if (radioApp.Checked)
            {
                cond.SpeedTypeSelected = LowSpeedRoadCondition_NR.ESpeedType.APP;
            }
            else
            {
                cond.SpeedTypeSelected = (LowSpeedRoadCondition_NR.ESpeedType)cmbThroughput.SelectedIndex;
            }
            cond.CheckFTPDL = chkFTPDL.Checked;
            cond.CheckFTPUL = chkFTPUL.Checked;
            cond.CheckEmail = chkEmail.Checked;
            cond.CheckHTTP = chkHttp.Checked;
            cond.CheckVideo = chkVideo.Checked;

            cond.EmailRateMax = (double)numEmailMax.Value;
            cond.FTPDLRateMax = (double)numFTPDLMax.Value;
            cond.FTPULRateMax = (double)numFTPULMax.Value;
            cond.HTTPRateMax = (double)numHTTPMax.Value;
            cond.VideoRateMax = (double)numVideoMax.Value;

            cond.EmailRateMin = (double)numEmailMin.Value;
            cond.FTPDLRateMin = (double)numFTPDLMin.Value;
            cond.FTPULRateMin = (double)numFTPULMin.Value;
            cond.HTTPRateMin = (double)numHTTPMin.Value;
            cond.VideoRateMin = (double)numVideoMin.Value;

            cond.DistanceFTPDLMin = (double)numFTPDLDistance.Value;
            cond.DistanceFTPULMin = (double)numFTPULDistance.Value;
            cond.DistanceEMailMin = (double)numEMailDistance.Value;
            cond.DistanceHTTPMin = (double)numHTTPDistance.Value;
            cond.DistanceVideoMin = (double)numVideoDistance.Value;

            cond.CheckThroughputDl = chkThroughputDl.Checked;
            cond.CheckThroughputUl = chkThroughputUl.Checked;
            cond.ThroughputDlRateMax = (double)numThroughputDlMax.Value;
            cond.ThroughputDlRateMin = (double)numThroughputDlMin.Value;
            cond.ThroughputUlRateMax = (double)numThroughputUlMax.Value;
            cond.ThroughputUlRateMin = (double)numThroughputUlMin.Value;

            cond.DistanceThroughputDlMin = (double)numThroughputDlDistance.Value;
            cond.DistanceThroughputUlMin = (double)numThroughputUlDistance.Value;

            cond.CheckRsrp = chkRsrp.Checked;
            cond.RsrpMax = (float)numRsrpMax.Value;
            cond.RsrpMin = (float)numRsrpMin.Value;

            cond.CheckSinr = chkSinr.Checked;
            cond.SinrMax = (float)numSinrMax.Value;
            cond.SinrMin = (float)numSinrMin.Value;

            cond.TestPointDistance = (double)num2TpDis.Value;
            cond.LowPercent = (double)numLowPer.Value;

            //网络
            cond.CheckSynthesisLowSpeed = this.checkBoxSynthesisLowSpeed.Checked;
            cond.CheckLTELowSpeed = this.checkBoxLTELowSpeed.Checked;
            cond.CheckNRLowSpeed = this.checkBoxLowSpeed_NR.Checked;

            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (radioApp.Checked
                && !chkEmail.Checked
                && !chkFTPDL.Checked
                && !chkFTPUL.Checked
                && !chkHttp.Checked 
                && !chkVideo.Checked)
            {
                MessageBox.Show("请至少选取一种速率！");
                return;
            }

            if (chkRsrp.Checked && numRsrpMin.Value > numRsrpMax.Value)
            {
                MessageBox.Show("最小RSRP不能大于最大RSRP！");
                return;
            }

            if (chkSinr.Checked && numSinrMin.Value > numSinrMax.Value)
            {
                MessageBox.Show("最小SINR不能大于最大SINR！");
                return;
            }

            if (!checkBoxSynthesisLowSpeed.Checked &&
                !checkBoxLTELowSpeed.Checked &&
                !checkBoxLowSpeed_NR.Checked)
            {
                MessageBox.Show("请至少选择一种网络！");
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void radioApp_CheckedChanged(object sender, EventArgs e)
        {
            grpThroughput.Enabled = !radioApp.Checked;
        }

        private void chkRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numRsrpMax.Enabled = numRsrpMin.Enabled = chkRsrp.Checked;
        }

        private void chkSinr_CheckedChanged(object sender, EventArgs e)
        {
            numSinrMax.Enabled = numSinrMin.Enabled = chkSinr.Checked;
        }
        private void radioThroughput_CheckedChanged(object sender, EventArgs e)
        {
            groupBox2.Enabled = !radioThroughput.Checked;
        }

    }
}
