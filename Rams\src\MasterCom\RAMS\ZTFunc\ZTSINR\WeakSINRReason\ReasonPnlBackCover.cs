﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlBackCover : ReasonPanelBase
    {
        public ReasonPnlBackCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numDirectionDif.ValueChanged -= numDirectionDif_ValueChanged;
            numDirectionDif.Value = (decimal)((ReasonsBackCover)reason).DirectionDif;
            numDirectionDif.ValueChanged += numDirectionDif_ValueChanged;
            numCell2TpDis.ValueChanged -= numCell2TpDis_ValueChanged;
            numCell2TpDis.Value = (decimal)((ReasonsBackCover)reason).Cell2TpDis;
            numCell2TpDis.ValueChanged += numCell2TpDis_ValueChanged;
        }

        void numDirectionDif_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonsBackCover)reason).DirectionDif = (double)numDirectionDif.Value;
        }
        void numCell2TpDis_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonsBackCover)reason).Cell2TpDis = (double)numCell2TpDis.Value;
        }
    }
}
