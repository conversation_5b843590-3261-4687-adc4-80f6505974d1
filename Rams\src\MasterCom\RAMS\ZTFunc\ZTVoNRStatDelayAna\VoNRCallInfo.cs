﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTVoNRStatDelayAna
{
    public class VoNRCallInfo
    {
        public VoNRCallInfo(string fileName, int flag)
        {
            this.FileName = fileName;
            this.CallResultDesc = "";
            this.MoMtFlag = flag;
        }
        public string FileName { get; set; }
        public string PhoneNum { get; set; }
        public string GridName
        {
            get
            {
                if (ListEvents.Count > 0)
                {
                    return ListEvents[0].GridDesc;
                }
                return "";
            }
        }
        public int MoMtFlag { get; set; }
        public string CallNetType
        {
            get 
            {
                if (MsgExtendedSR != null)
                {
                    return "CSFB";
                }
                else if (EvtCallAttempt != null && (EvtCallAttempt.ID == 9149 || EvtCallAttempt.ID == 9150 || EvtCallAttempt.ID == 9187 || EvtCallAttempt.ID == 9188))
                {
                    return "VoLTE";
                }
                else if (EvtCallAttempt != null && (EvtCallAttempt.ID == 9334 || EvtCallAttempt.ID == 9335 || EvtCallAttempt.ID == 9346 || EvtCallAttempt.ID == 9347))
                {
                    return "EPSFB";
                }
                else if (EvtCallAttempt != null && (EvtCallAttempt.ID == 9834 || EvtCallAttempt.ID == 9835 || EvtCallAttempt.ID == 9860 || EvtCallAttempt.ID == 9861))
                {
                    return "VoNR";
                }
                else if (MsgIMS_SIP_INVITE != null || MsgInviteTrying100 != null)
                {
                    return "VoNR";
                }
                else
                {
                    return "";
                }
            }
        }

        public string DelayTimesReason
        {
            get
            {
                var reason = "";

                int delayTime = 0;
                if (MoMtFlag == (int)MoMtFile.MtFlag && CallSetUpDelayTimes1 != null)
                {
                    delayTime = (int)CallSetUpDelayTimes1;
                    reason = "合理";
                }

                if (MoMtFlag == (int)MoMtFile.MoFlag && CallSetUpDelayTimes2 != null)
                {
                    delayTime = (int)CallSetUpDelayTimes2;
                    reason = "合理";
                }

                if (delayTime > 2500)
                {
                    reason = "不合理,呼叫建立时延超时";
                }

                return reason;
            }
        }

        public string CallResultDesc { get; set; }

        public string Node1Time 
        {
            get 
            {
                if (MsgNode1 != null)
                {
                    return MsgNode1.DateTime.ToString("HH:mm:ss.fff");
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag && !string.IsNullOrEmpty(FileName))
                {
                    return "MT 连接态";
                }
                else
                {
                    return "";
                }
            }
        }
        public string Node2Time
        {
            get
            {
                if (MsgNode2 != null)
                {
                    return MsgNode2.DateTime.ToString("HH:mm:ss.fff");
                }
                else if (MoMtFlag == (int)MoMtFile.MoFlag && !string.IsNullOrEmpty(FileName))
                {
                    return "连接态起呼";
                }
                else
                {
                    return "";
                }
            }
        }
        public string Node3Time { get { return MsgNode3 == null ? "" : MsgNode3.DateTime.ToString("HH:mm:ss.fff"); } }
        public string Node4ime { get { return MsgNode4 == null ? "" : MsgNode4.DateTime.ToString("HH:mm:ss.fff"); } }
        public string Node5Time { get { return MsgNode5 == null ? "" : MsgNode5.DateTime.ToString("HH:mm:ss.fff"); } }

        public int? CallSetUpDelayTimes1
        {
            get
            {
                if (MsgNode5 != null && MsgNode1 != null)
                {
                    return (int)(MsgNode5.DateTime - MsgNode1.DateTime).TotalMilliseconds;
                }
                else if (MsgNode5 != null && MsgNode2 != null && MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    return (int)(MsgNode5.DateTime - MsgNode2.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? CallSetUpDelayTimes2
        {
            get
            {
                if (MsgNode4 != null && MsgNode1 != null)
                {
                    return (int)(MsgNode4.DateTime - MsgNode1.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? Node1To2DelayTimes
        {
            get
            {
                if (MsgNode2 != null && MsgNode1 != null)
                {
                    return (int)(MsgNode2.DateTime - MsgNode1.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? Node2To3DelayTimes
        {
            get
            {
                if (MsgNode3 != null && MsgNode2 != null)
                {
                    return (int)(MsgNode3.DateTime - MsgNode2.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? Node3To4DelayTimes
        {
            get
            {
                if (MsgNode4 != null && MsgNode3 != null)
                {
                    return (int)(MsgNode4.DateTime - MsgNode3.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? Node4To5DelayTimes
        {
            get
            {
                if (MsgNode5 != null && MsgNode4 != null)
                {
                    return (int)(MsgNode5.DateTime - MsgNode4.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public string N1CsfbExtendedSRTime { get { return MsgExtendedSR == null ? "" : MsgExtendedSR.DateTime.ToString("HH:mm:ss.fff"); } }
        public string N2MoCsfbCMSRTime { get { return MsgCMSR == null ? "" : MsgCMSR.DateTime.ToString("HH:mm:ss.fff"); } }
        public string N2MtCsfbPagingResponseTime { get { return MsgPagingResponse == null ? "" : MsgPagingResponse.DateTime.ToString("HH:mm:ss.fff"); } }
        public string N3CsfbAlertingTime { get { return MsgAlerting == null ? "" : MsgAlerting.DateTime.ToString("HH:mm:ss.fff"); } }

        public int? CsfbDelayTimes
        {
            get
            {
                if (MsgAlerting != null && MsgExtendedSR != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgExtendedSR.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? N1Csfb1To2DelayTimes
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag && MsgCMSR != null && MsgExtendedSR != null)
                {
                    return (int)(MsgCMSR.DateTime - MsgExtendedSR.DateTime).TotalMilliseconds;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag && MsgPagingResponse != null && MsgExtendedSR != null)
                {
                    return (int)(MsgPagingResponse.DateTime - MsgExtendedSR.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? N2Csfb2To3DelayTimes
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag && MsgAlerting != null && MsgCMSR != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgCMSR.DateTime).TotalMilliseconds;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag && MsgAlerting != null && MsgPagingResponse != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgPagingResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public string CallAttemptResult
        {
            get { return EvtCallAttempt == null ? "Fail" : "Success"; }
        }

        public Message MsgNode1
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    return MsgIMS_SIP_INVITE;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    return MsgPaging;
                }
                else
                {
                    return null;
                }
            }
        }
        public Message MsgNode2
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    return MsgRRCCRComplete;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    return MsgIMS_SIP_INVITE;
                }
                else
                {
                    return null;
                }
            }
        }
        public Message MsgNode3
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    return MsgEPSBearerCR;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    return MsgSessionProgress183;
                }
                else
                {
                    return null;
                }
            }
        }
        public Message MsgNode4
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    return MsgRinging180;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    return MsgEPSBearerCR;
                }
                else
                {
                    return null;
                }
            }
        }
        public Message MsgNode5
        {
            get
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    return MsgIMS_SIP_ACK;
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    return MsgRinging180;
                }
                else
                {
                    return null;
                }
            }
        }

        public List<TestPoint> ListPoints { get; set; } = new List<TestPoint>();
        public List<Event> ListEvents { get; set; } = new List<Event>();
        public List<Message> ListMessage { get; set; } = new List<Message>();
        public Event EvtCallAttempt  { get; set; } 

        public Message MsgIMS_SIP_ACK  { get; set; } 
        public Message MsgSetup  { get; set; } 
        public Message MsgConnectAcknowledge  { get; set; } 
        public Message MsgIMS_SIP_INVITE  { get; set; } 
        public Message MsgRRCCRComplete  { get; set; } 
        public Message MsgSessionProgress183  { get; set; } 
        public Message MsgEPSBearerCR  { get; set; } 
        public Message MsgRinging180  { get; set; } 
        public Message MsgExtendedSR  { get; set; } 
        public Message MsgCMSR  { get; set; } 
        public Message MsgAlerting  { get; set; } 
        public Message MsgInviteTrying100  { get; set; } 
        public Message MsgRRCConnectionRequest  { get; set; }
        public Message MsgRRCSetupRequest { get; set; }
        public Message MsgRRCSetupComplete { get; set; }
        public Message MsgPaging  { get; set; } 
        public Message MsgPagingResponse  { get; set; } 

        /// <summary>
        /// 添加事件
        /// </summary>
        /// <param name="evt"></param>
        /// <returns>通话是否已有结果（含成功、失败）</returns>
        public bool AddEvent(Event evt)
        {
            if (CallResultDesc != "")
            {
                return false;
            }

            ListEvents.Add(evt);

            if (evt.ID == (int)NREventManager.VoLTE_Audio_MO_Call_Attempt || evt.ID == (int)NREventManager.VoLTE_Audio_MT_Call_Attempt ||
                evt.ID == (int)NREventManager.VoLTE_Video_MO_Call_Attempt || evt.ID == (int)NREventManager.VoLTE_Video_MT_Call_Attempt ||
                evt.ID == (int)NREventManager.EPSFB_Audio_MO_Call_Attempt || evt.ID == (int)NREventManager.EPSFB_Audio_MT_Call_Attempt ||
                evt.ID == (int)NREventManager.EPSFB_Video_MO_Call_Attempt || evt.ID == (int)NREventManager.EPSFB_Video_MT_Call_Attempt ||
                evt.ID == (int)NREventManager.VoNR_Audio_MO_Call_Attempt || evt.ID == (int)NREventManager.VoNR_Audio_MT_Call_Attempt ||
                evt.ID == (int)NREventManager.VoNR_Video_MO_Call_Attempt || evt.ID == (int)NREventManager.VoNR_Video_MT_Call_Attempt)
            {
                EvtCallAttempt = evt;
            }
            else if (evt.ID == (int)NREventManager.VoLTE_Audio_MO_Call_End || evt.ID == (int)NREventManager.VoLTE_Audio_MT_Call_End ||
                     evt.ID == (int)NREventManager.VoLTE_Video_MO_Call_End || evt.ID == (int)NREventManager.VoLTE_Video_MT_Call_End || 
                     evt.ID == (int)NREventManager.EPSFB_Audio_MO_Call_End || evt.ID == (int)NREventManager.EPSFB_Audio_MT_Call_End ||
                     evt.ID == (int)NREventManager.EPSFB_Video_MO_Call_End || evt.ID == (int)NREventManager.EPSFB_Video_MT_Call_End ||
                     evt.ID == (int)NREventManager.VoNR_Audio_MO_Call_End || evt.ID == (int)NREventManager.VoNR_Audio_MT_Call_End ||
                     evt.ID == (int)NREventManager.VoNR_Video_MO_Call_End || evt.ID == (int)NREventManager.VoNR_Video_MT_Call_End)
            {
                //Call END
                CallResultDesc = "是";
                return true;
            }
            else if (evt.ID == (int)NREventManager.VoNR_Audio_MO_Drop_Call || evt.ID == (int)NREventManager.VoNR_Audio_MT_Drop_Call ||
                     evt.ID == (int)NREventManager.VoNR_Video_MO_Drop_Call || evt.ID == (int)NREventManager.VoNR_Video_MT_Drop_Call)
            {
                //Drop Call
                CallResultDesc = "否";
                return true;
            }

            return false;
        }
    }
}
