﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DIYQueryAbnormalDataShowForm : MinCloseForm
    {
        public DIYQueryAbnormalDataShowForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
        }

       
        public void setData(List<AbnormalFileInfo> result)
        {
            this.gridOptimalCover.DataSource = result;
            this.gridOptimalCover.RefreshDataSource();
        }

        private void excelStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}
