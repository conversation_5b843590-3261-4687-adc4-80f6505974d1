﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDCellWrongDir
    {
        public string BtsName { get; set; }

        public TDCellWrongDir(ICell cell)
        {
            this.cell = cell;
            resultFirstBatch = new ResultCellWrong();
            resultSecondBatch = new ResultCellWrong();
        }

        protected ICell cell;
        public TDCell Cell
        {
            get { return (TDCell)cell; }
        }

        public virtual int LAC
        {
            get { return Cell.LAC; }
        }

        public virtual int CI
        {
            get { return Cell.CI; }
        }

        public virtual int FREQ
        {
            get { return Cell.FREQ; }
        }

        public virtual int CPI
        {
            get { return Cell.CPI; }
        }

        public virtual string CellName
        {
            get
            {
                return cell.Name;
            }
        }

        public virtual double Longitude
        {
            get
            {
                return cell.Longitude;
            }
        }

        public virtual double Latitude
        {
            get
            {
                return cell.Latitude;
            }
        }

        public virtual int Direction
        {
            get 
            { 
                return cell.Direction; 
            }
        }

        public CellWrongBatch cellWrongBatch { get; set; } = CellWrongBatch.First;

        public ResultCellWrong resultFirstBatch { get; set; }

        public ResultCellWrong resultSecondBatch { get; set; }

        protected ResultCellWrong resultShow
        {
            get
            {
                if (cellWrongBatch == CellWrongBatch.First)
                    return resultFirstBatch;

                return resultSecondBatch;
            }
        }

        public virtual int CellDirSuggest
        {
            get
            {
                return resultShow.WrongDirMean;
            }
        }

        public int WrongTestPointCount
        {
            get 
            {
                return resultShow.WrongTestPointCount;
            }
        }

        public int GoodTestPointCount
        {
            get
            {
                return resultShow.GoodTestPointCount;
            }
        }

        public double WrongPercentage
        {
            get
            {
                return resultShow.WrongPercentage;
            }
        }

        public string CompetedResult
        {
            get
            {
                if(resultFirstBatch.WrongPercentage > resultSecondBatch.WrongPercentage)
                {
                    return "变好";
                }
                else if(resultFirstBatch.WrongPercentage == resultSecondBatch.WrongPercentage)
                {
                    return "相当";
                }
                else
                {
                    return "变差";
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="?"></param>
        /// <returns>true代表覆盖不符点</returns>
        public void AddTestPoint(TestPoint tp, int angleMin, CellWrongBatch cellWrongBatch)
        {
            switch (cellWrongBatch)
            {
                case CellWrongBatch.First:
                    resultFirstBatch.AddTestPoint(this.cell, tp, angleMin);
                    break;
                case CellWrongBatch.Second:
                    resultSecondBatch.AddTestPoint(this.cell, tp, angleMin);
                    break;
                default:
                    break;
            }
        }

        public virtual TDCellWrongDir Clone()
        {
            TDCellWrongDir cellWrong = new TDCellWrongDir(Cell);
            cellWrong.BtsName = this.BtsName;
            cellWrong.cellWrongBatch = this.cellWrongBatch;
            cellWrong.resultFirstBatch = this.resultFirstBatch;
            cellWrong.resultSecondBatch = this.resultSecondBatch;

            return cellWrong;
        }

        public virtual BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.CellType = BackgroundCellType.TD;
            bgResult.LAC = LAC;
            bgResult.CI = CI;
            bgResult.BCCH = FREQ;
            bgResult.BSIC = CPI;
            bgResult.ISTime = resultShow.istime;
            bgResult.IETime = resultShow.ietime;
            bgResult.LongitudeMid = Longitude;
            bgResult.LatitudeMid = Latitude;
            bgResult.SampleCount = WrongTestPointCount;
            bgResult.AddImageValue(resultShow.GoodTestPointCount);
            bgResult.AddImageValue((float)WrongPercentage);
            return bgResult;
        }
    }

    public enum CellWrongBatch
    {
        First,
        Second,
    }

    public class ResultCellWrong
    {
        public ResultCellWrong()
        {
            wrongDirTestPoints = new List<TestPoint>();
        }

        private int wrongDirMean = -1;
        public int WrongDirMean
        {
            get
            {
                if (wrongDirMean == -1 && this.Cell!=null)
                {
                    CalcWrongDir();
                }
                return wrongDirMean;
            }
            private set { wrongDirMean = value; }
        }

        public int WrongDirMax
        {
            get;
            private set;
        }

        public int WrongDirMin
        {
            get;
            private set;
        }

        public int DirDiff
        {
            get;
            private set;
        }

        public void CalcWrongDir()
        {
            WrongDirMax = -1;
            WrongDirMin = 361;
            foreach (TestPoint tp in wrongDirTestPoints)
            {
                int dir = MathFuncs.getAngleFromPointToPoint(this.Cell.Longitude, this.Cell.Latitude
                    , tp.Longitude, tp.Latitude);
                WrongDirMin = Math.Min(dir, WrongDirMin);
                WrongDirMax = Math.Max(dir, WrongDirMax);
            }
            wrongDirMean = CalcSuggestDir(WrongDirMax, WrongDirMin);
            DirDiff = Math.Abs(wrongDirMean - this.Cell.Direction);
            DirDiff = DirDiff > 180 ? 360 - DirDiff : DirDiff;
        }

        public static int CalcSuggestDir(int WrongDirMax, int WrongDirMin)
        {
            int sugDir;
            int diffDir = WrongDirMax - WrongDirMin;
            if (diffDir > 180)
            {
                double meanDir = (360 - diffDir) / 2.0;
                if (meanDir > WrongDirMin)
                {
                    sugDir = (int)(360 - (meanDir - WrongDirMin));
                }
                else
                {
                    sugDir = (int)(WrongDirMin - meanDir);
                }
            }
            else
            {
                double halfDir = diffDir / 2.0;
                sugDir = (short)(WrongDirMin + halfDir);
            }

            return sugDir;
        }

        public virtual int CellDirSuggest
        {
            get
            {
                int cellDirSuggest = 0;
                int count = wrongDirTestPoints.Count + goodTestPointCount;
                if (count > 0)
                {
                    cellDirSuggest = (int)Math.Round((double)angle_Cell_TestPointSum / count);
                }
                return cellDirSuggest;
            }
        }

        protected List<TestPoint> wrongDirTestPoints;
        public List<TestPoint> WrongPoints
        {
            get { return wrongDirTestPoints; }
        }

        public int WrongTestPointCount
        {
            get { return wrongDirTestPoints.Count; }
        }

        protected int goodTestPointCount = 0;
        public int GoodTestPointCount
        {
            get { return goodTestPointCount; }
        }

        public double WrongPercentage
        {
            get
            {
                double ret = 0;
                int count = wrongDirTestPoints.Count + goodTestPointCount;
                if (count > 0)
                {
                    ret = Math.Round(100.0 * wrongDirTestPoints.Count / count, 2);
                }
                return ret;
            }
        }

        public int istime { get; set; } = int.MaxValue;
        public int ietime { get; set; } = int.MinValue;
        private int angle_Cell_TestPointSum = 0;
        public ICell Cell
        {
            get;
            set;
        }

        public bool AddTestPoint(ICell cell, TestPoint tp, int angleMin)
        {
            this.Cell = cell;
            //采样点相对于小区的正北方向角度
            int angleN = MathFuncs.getAngleFromPointToPoint(cell.Longitude, cell.Latitude, tp.Longitude, tp.Latitude);
            //采样点与小区的最小夹角
            int angle_Cell_TestPoint = Math.Min(360 - Math.Abs(angleN - cell.Direction), Math.Abs(angleN - cell.Direction));
            angle_Cell_TestPointSum += angle_Cell_TestPoint;
            bool isWrongDir = false;
            if (angle_Cell_TestPoint >= angleMin)  //wrong direction
            {
                wrongDirTestPoints.Add(tp);
                isWrongDir = true;
                if (istime > tp.Time)
                {
                    istime = tp.Time;
                }
                if (ietime < tp.Time)
                {
                    ietime = tp.Time;
                }
            }
            else
            {
                goodTestPointCount++;
            }
            return isWrongDir;
        }

    }
}
