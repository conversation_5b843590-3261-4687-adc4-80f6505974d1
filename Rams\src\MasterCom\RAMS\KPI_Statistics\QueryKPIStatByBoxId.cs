﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByBoxId : QueryKPIStatAll
    {
        List<string> listBoxId = null;
        public override string Name
        {
            get { return "按设备号统计"; }
        }

        public QueryKPIStatByBoxId(MainModel mainModel)
        {
            this.mainModel = mainModel;

        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11063, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            if (this.listBoxId == null || this.listBoxId.Count <= 0)
            {
                DiySqlBoxId query = new DiySqlBoxId(this.mainModel);
                query.Query();

                if (query.ListBoxId.Count <= 0)
                {
                    System.Windows.Forms.MessageBox.Show("数据库中无设备号表,请检查!");
                    return false;
                }

                this.listBoxId = query.ListBoxId;
            }

            return base.getConditionBeforeQuery();
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            if (!string.IsNullOrEmpty(fi.Name) && fi.Name.Length >= 8)
            {
                string boxId = fi.Name.Substring(0, 8);

                if (listBoxId.Contains(boxId))
                {
                    KpiDataManager.AddStatData(string.Empty, boxId, fi, singleStatData
                        , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
                }
            }

        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            if (!string.IsNullOrEmpty(fi.Name) && fi.Name.Length >= 8)
            {
                string boxId = fi.Name.Substring(0, 8);

                if (listBoxId.Contains(boxId))
                {
                    KpiDataManager.AddStatData(string.Empty, boxId, fi, eventData,
                        this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
                }
            }
        }

    }

    public class DiySqlBoxId : DIYSQLBase
    {
        public DiySqlBoxId(MainModel mainModel)
            : base(mainModel)
        {
        }

        public List<string> ListBoxId { get; set; }

        protected override string getSqlTextString()
        {
            return "SELECT boxId FROM tb_stat_boxid";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            this.ListBoxId = new List<string>();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string id = package.Content.GetParamString();
                    this.ListBoxId.Add(id);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "DiySqlBoxId"; }
        }
    }
}
