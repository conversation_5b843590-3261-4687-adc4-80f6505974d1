﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    public partial class CompetitionSettingDlg : BaseForm
    {
        public CompetitionSettingDlg(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            if (!TestPointBlockReportManager.GetInstance().HasLoaded)
            {
                TestPointBlockReportManager.GetInstance().Load();
            }
            fillCbxReport(TestPointBlockReportManager.GetInstance().Reports, 0);

            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            CategoryEnumItem[] svcItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            if (projItems == null || svcItems == null)
            {
                return;
            }
            MapFormItemSelection itemSelection = mm.MainForm.GetMapForm().ItemSelection;
            fillCategoryView(lvProjectHost, CurSelReport != null ? CurSelReport.HostProjIDList : null, projItems);
            fillCategoryView(lvProjectGuest, CurSelReport != null ? CurSelReport.GuestProjIDList : null, projItems);

            lblProjCntHost.Text = "[" + lvProjectHost.Items.Count.ToString() + "]";
            lblProjCntGuest.Text = "[" + lvProjectGuest.Items.Count.ToString() + "]";
            ItemSelectionPanel projPanelOne = new ItemSelectionPanel(dropDownHost, lvProjectHost, lblProjCntHost, itemSelection, "Project", true);
            ItemSelectionPanel projPanelTwo = new ItemSelectionPanel(dropDownGuest, lvProjectGuest, lblProjCntGuest, itemSelection, "Project", true);
            projPanelOne.FreshItems();
            projPanelTwo.FreshItems();
            dropDownHost.Items.Add(new ToolStripControlHost(projPanelOne));
            dropDownGuest.Items.Add(new ToolStripControlHost(projPanelTwo));

            if (CurSelReport != null)
            {
                dtBeginHost.Value = CurSelReport.HostBeginTime;
                dtEndHost.Value = CurSelReport.HostEndTime;
                dtBeginGuest.Value = CurSelReport.GuestBeginTime;
                dtEndGuest.Value = CurSelReport.GuestEndTime;
            }
        }


        private void fillCategoryView(ListView lv, List<int> projIDs, CategoryEnumItem[] projItems)
        {
            lv.Items.Clear();
            if (projIDs == null)
            {
                foreach (CategoryEnumItem item in projItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.Name;
                    lvi.Tag = item.ID;
                    lv.Items.Add(lvi);
                }
            }
            else
            {
                foreach (int id in projIDs)
                {
                    foreach (CategoryEnumItem item in projItems)
                    {
                        if (id == item.ID)
                        {
                            ListViewItem lvi = new ListViewItem();
                            lvi.Text = item.Description;
                            lvi.Tag = id;
                            lv.Items.Add(lvi);
                        }
                    }
                }
            }
        }

        private List<TestPointBlockReport> reports = null;

        private void fillCbxReport(List<TestPointBlockReport> reports, int selIdx)
        {
            this.reports = reports;
            cbxReport.Items.Clear();
            foreach (TestPointBlockReport rpt in reports)
            {
                cbxReport.Items.Add(rpt);
            }
            if (cbxReport.Items.Count > selIdx)
            {
                cbxReport.SelectedIndex = selIdx;
            }
        }

        public TestPointBlockReport CurSelReport
        {
            get { return cbxReport.SelectedItem as TestPointBlockReport; }
        }


        private void btnProjSelHost_Click(object sender, EventArgs e)
        {
            dropDownHost.Closed -= dropDownHost_Closed;
            System.Drawing.Point pt = new System.Drawing.Point(btnProjSelHost.Width, btnProjSelHost.Height);
            dropDownHost.Show(btnProjSelHost, pt, ToolStripDropDownDirection.BelowLeft);
            dropDownHost.Closed += dropDownHost_Closed;
        }

        void dropDownHost_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            if (CurSelReport != null)
            {
                CurSelReport.HostProjIDList.Clear();
                foreach (ListViewItem item in lvProjectHost.Items)
                {
                    CurSelReport.HostProjIDList.Add((int)item.Tag);
                }
            }
        }
    
        private void btnProjSelGuest_Click(object sender, EventArgs e)
        {
            dropDownGuest.Closed -= dropDownGuest_Closed;
            System.Drawing.Point pt = new System.Drawing.Point(btnProjSelGuest.Width, btnProjSelGuest.Height);
            dropDownGuest.Show(btnProjSelGuest, pt, ToolStripDropDownDirection.BelowLeft);
            dropDownGuest.Closed += dropDownGuest_Closed;
        }

        void dropDownGuest_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            if (CurSelReport != null)
            {
                CurSelReport.GuestProjIDList.Clear();
                foreach (ListViewItem item in lvProjectGuest.Items)
                {
                    CurSelReport.GuestProjIDList.Add((int)item.Tag);
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (CurSelReport == null)
            {
                MessageBox.Show("请选择或定制一个竞比报表！");
                return;
            }
            if (!checkDateTime(dtBeginHost.Value, dtEndHost.Value))
            {
                MessageBox.Show("主队开始时间不能大于结束时间！请正确设置时间段！");
                return;
            }
            CurSelReport.HostBeginTime = dtBeginHost.Value.Date;
            CurSelReport.HostEndTime = dtEndHost.Value.Date.AddDays(1).AddMilliseconds(-1);
            if (!checkDateTime(dtBeginGuest.Value, dtEndGuest.Value))
            {
                MessageBox.Show("客队开始时间不能大于结束时间！请正确设置时间段！");
                return;
            }
            CurSelReport.GuestBeginTime = dtBeginGuest.Value.Date;
            CurSelReport.GuestEndTime = dtEndGuest.Value.Date.AddDays(1).AddMilliseconds(-1);
            if (lvProjectHost.Items.Count == 0)
            {
                MessageBox.Show("请选择主队的数据来源！");
                return;
            }
            if (lvProjectGuest.Items.Count == 0)
            {
                MessageBox.Show("请选择客队的数据来源！");
                return;
            }
            if (CurSelReport.HostServiceTypeList.Count == 0)
            {
                MessageBox.Show("请配置好主队的服务类型！");
                return;
            }
            if (CurSelReport.GuestServiceTypeList.Count == 0)
            {
                MessageBox.Show("请配置客队的服务类型！");
                return;
            }
            if (CurSelReport.HostCarrierType == 0)
            {
                MessageBox.Show("请配置主队运营商！");
                return;
            }
            if (CurSelReport.GuestCarrierType == 0)
            {
                MessageBox.Show("请配置客队运营商！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private bool checkDateTime(DateTime begin, DateTime end)
        {
            return begin <= end;
        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            CustomReporForm frm = new CustomReporForm(TestPointBlockReportManager.GetInstance(), cbxReport.SelectedItem as TestPointBlockReport);
            frm.ShowDialog();
            fillCbxReport(TestPointBlockReportManager.GetInstance().Reports, 0);
        }

        private void cbxReport_SelectedIndexChanged(object sender, EventArgs e)
        {
            TestPointBlockReport rpt = CurSelReport;
            if (rpt == null)
            {
                return;
            }
            txtMainDesc.Text = rpt.BlockDesc;
            txtHost.Text = rpt.HostDescription;
            txtGuest.Text = rpt.GuestDescription;
        }

    }
}
