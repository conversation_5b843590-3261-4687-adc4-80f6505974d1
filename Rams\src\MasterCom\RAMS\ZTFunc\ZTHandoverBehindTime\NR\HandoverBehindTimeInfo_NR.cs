﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverBehindTimeInfo_NR : LTEHandoverBehindTime
    {
        public ENetType Netype { get; set; }
        public HandoverBehindTimeInfo_NR(ENetType netype)
        {
            this.Netype = netype;
        }
        private float minNcellRsrp = float.MaxValue;
        public float MinNcellRsrp
        {
            get
            {
                return minNcellRsrp == float.MaxValue ? float.NaN : minNcellRsrp;
            }
        }

        private float maxNcellRsrp = float.MinValue;
        public float MaxNcellRsrp
        {
            get
            {
                return maxNcellRsrp == float.MinValue ? float.NaN : maxNcellRsrp;
            }
        }

        private float sumNcellRsrp = 0;
        public float AvgNcellRsrp
        {
            get { return (float)Math.Round(sumNcellRsrp / points.Count, 2); }
        }

        public void AddTestPoint(TestPoint tp, float rsrp, float ncellRsrp)
        {
            base.AddTestPoint(tp, rsrp);

            minNcellRsrp = Math.Min(minNcellRsrp, ncellRsrp);
            maxNcellRsrp = Math.Max(maxNcellRsrp, ncellRsrp);
            sumNcellRsrp += ncellRsrp;
        }
        protected override void getCellNameByTP(StringBuilder svrCellName, List<string> tempList, TestPoint tp)
        {
            if (Netype == ENetType.LTE)
            {
                LTECell lteCell = tp.GetMainCell_LTE();
                if (lteCell != null)
                {
                    addCellName(svrCellName, tempList, lteCell.Name);
                }
            }
            else
            {
                NRCell nrCell = tp.GetMainCell_NR();
                if (nrCell != null)
                {
                    addCellName(svrCellName, tempList, nrCell.Name);
                }
            }
        }
        protected override void getNbCellNameByTP(StringBuilder nbCellName, List<string> tempList, TestPoint tp)
        {
            if (Netype == ENetType.LTE)
            {
                for (int i = 0; i < 10; i++)
                {
                    LTECell lteCell = tp.GetNBCell_LTE(i);
                    if (lteCell != null)
                    {
                        addCellName(nbCellName, tempList, lteCell.Name);
                    }
                }
            }
            else
            {
                NRCell nrMainCell = tp.GetMainCell_NR();
                for (int i = 0; i < 10; i++)
                {
                    NRCell nrNCell = tp.GetNBCell_NR(i);
                    if (nrNCell != null && nrNCell != nrMainCell)
                    {
                        addCellName(nbCellName, tempList, nrNCell.Name);
                    }
                }
            }
        }

        public string StartTime 
        {
            get
            {
                if (points.Count > 0)
                {
                    return points[0].DateTimeStringWithMillisecond;
                }
                return "";
            }
        }

        public string SvrCellNCI
        {
            get
            {
                return getData<string>(points, getCellNCIByTP);
            }
        }

        private void getCellNCIByTP(StringBuilder svrCellNCI, List<string> tempList, TestPoint tp)
        {
            if (Netype == ENetType.LTE)
            {
                int? eciTmp = (int?)NRTpHelper.NrLteTpManager.GetNCI(tp);
                if (eciTmp != null)
                {
                    string eci = eciTmp.ToString();
                    if (!tempList.Contains(eci))
                    {
                        tempList.Add(eci);
                        svrCellNCI.Append(eci);
                        svrCellNCI.Append(";");
                    }
                }
            }
            else
            {
                getNRSCellToken(svrCellNCI, tempList, tp);
            }
        }

        private void getNRSCellToken(StringBuilder svrCellNCI, List<string> tempList, TestPoint tp)
        {
            long? nciTmp = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            if (nciTmp != null)
            {
                string nci = nciTmp.ToString();
                if (!tempList.Contains(nci))
                {
                    tempList.Add(nci);
                    svrCellNCI.Append(nci);
                    svrCellNCI.Append(";");
                }
            }
            else
            {
                int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                string token = $"{earfcn}_{pci}";
                if (!tempList.Contains(token))
                {
                    tempList.Add(token);
                    svrCellNCI.Append(token);
                    svrCellNCI.Append(";");
                }
            }
        }

        public string NbCellToken
        {
            get
            {
                return getData<string>(points, getNbCellTokenByTP);
            }
        }

        private void getNbCellTokenByTP(StringBuilder nbCellToken, List<string> tempList, TestPoint tp)
        {
            if (Netype == ENetType.LTE)
            {
                getLTENCellToken(nbCellToken, tempList, tp);
            }
            else
            {
                getNRNCellToken(nbCellToken, tempList, tp);
            }
        }

        private static void getLTENCellToken(StringBuilder nbCellToken, List<string> tempList, TestPoint tp)
        {
            for (int i = 0; i < 10; i++)
            {
                int? earfcn = (int?)NRTpHelper.NrLteTpManager.GetNEARFCN(tp, i);
                int? pci = (int?)NRTpHelper.NrLteTpManager.GetNPCI(tp, i);
                if (earfcn == null || pci == null)
                {
                    continue;
                }
                string token = $"{earfcn}_{pci}";
                if (!tempList.Contains(token))
                {
                    tempList.Add(token);
                    nbCellToken.Append($"{token};");
                }
            }
        }

        private void getNRNCellToken(StringBuilder nbCellToken, List<string> tempList, TestPoint tp)
        {
            int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            string token = $"{earfcn}_{pci}";
            for (int i = 0; i < 10; i++)
            {
                int? nEarfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(tp, i);
                int? nPci = (int?)NRTpHelper.NrTpManager.GetNPCI(tp, i);
                if (earfcn == null || pci == null)
                {
                    continue;
                }
                string nToken = $"{nEarfcn}_{nPci}";
                if (!tempList.Contains(nToken) && token != nToken)
                {
                    tempList.Add(nToken);
                    nbCellToken.Append($"{nToken};");
                }
            }
        }
    }
    public enum ENetType
    {
        LTE,
        NR
    }
}
