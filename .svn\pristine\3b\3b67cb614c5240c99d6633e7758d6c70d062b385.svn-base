﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTGSMCellCheckInfoXtraForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTGSMCellCheckInfoXtraForm(MainModel Mainmodel)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
        }
        MainModel mainmodel = null;

        public void setData(Dictionary<string, List<CQTLibrary.PublicItem.CellCfg>> result)
        {
            if (result.ContainsKey("GSM"))
            {
                List<CQTLibrary.PublicItem.CellCfg> result2 = new List<CQTLibrary.PublicItem.CellCfg>();
                foreach (CQTLibrary.PublicItem.CellCfg item in result["GSM"])
                {
                    Cell cell = null;
                    cell = mainmodel.CellManager.GetCurrentCell(item.Ilac, item.Ici);
                    if (cell != null)
                    {
                        item.StrCellname = cell.Name;
                    }
                    else
                    {
                        item.StrCellname = "";
                    }
                    int same = sameItem(result2, item);
                    if (same == -1)
                    {
                        result2.Add(item);
                    }
                    else
                    {
                        if (result2[same].Dtime < item.Dtime)
                        {
                            result2.RemoveAt(same);
                            result2.Add(item);
                        }
                    }
                }

                this.gridControl1.DataSource = result2;
            }
        }

        public int sameItem(List<CQTLibrary.PublicItem.CellCfg> resultTem, CQTLibrary.PublicItem.CellCfg item)
        {
            int sameID = -1;
            for (int i = 0; i < resultTem.Count;i++)
            {
                if (resultTem[i].Strnet.Equals(item.Strnet) && resultTem[i].StrCellname.Equals(item.StrCellname) && resultTem[i].Num.Equals(item.Num)
                    && resultTem[i].Ilac.Equals(item.Ilac) && resultTem[i].Ici.Equals(item.Ici) && resultTem[i].Icfgbsic.Equals(item.Icfgbsic)
                    && resultTem[i].Icfgbcch.Equals(item.Icfgbcch) && resultTem[i].Ibsic.Equals(item.Ibsic) && resultTem[i].Ibcch.Equals(item.Ibcch))
                {
                    sameID = i;
                }
            }
            return sameID;
        }

        private void gridView1_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            //if (e.Column.Caption == "网络覆盖" || e.Column.Caption == "网络质量" || e.Column.Caption == "网络容量" || e.Column.Caption == "网络感知" || e.Column.Caption == "网络满意度")
            //{
            //    string aa = gridView1.GetRowCellDisplayText(e.RowHandle, e.Column);
            //    if (aa == "健康")
            //    {
            //        e.Appearance.BackColor = Color.Green;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }

            //    if (aa == "合格")
            //    {
            //        e.Appearance.BackColor = Color.Orange;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }

            //    if (aa == "不合格")
            //    {
            //        e.Appearance.BackColor = Color.Red;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }

            //    if (aa == "满意")
            //    {
            //        e.Appearance.BackColor = Color.Green;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }
            //}
        }

        //双击查看详情
        private void gridView1_DoubleClick(object sender, EventArgs e)
        {

            string imlongitude = gridView1.GetFocusedRowCellValue("Ilac").ToString();
            string imlatitude = gridView1.GetFocusedRowCellValue("Ici").ToString();
            if (imlongitude != "" && imlatitude != "")
            {

                float fLong = float.Parse(imlongitude);
                float fLat = float.Parse(imlatitude);
                if (fLong > 100 && fLat > 20)
                {
                    mainmodel.MainForm.GetMapForm().GoToView(fLong, fLat);
                }
            }
            
            //int[] rows = gridView1.GetSelectedRows();
            //object o = gridView1.GetRow(rows[0]);
            //CQTLibrary.PublicItem.EvaluateResult evaluate = o as CQTLibrary.PublicItem.EvaluateResult;

            //CQTBubbleXtraForm form = new CQTBubbleXtraForm();
            //form.setData(evaluate);
            //form.Show();
        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }

        private void CQTGSMCellCheckInfoXtraForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}