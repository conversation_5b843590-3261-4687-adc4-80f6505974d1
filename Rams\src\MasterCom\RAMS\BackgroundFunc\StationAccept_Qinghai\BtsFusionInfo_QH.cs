﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsFusionInfo_QH : BtsFusionInfoBase
    {
        public BtsFusionInfo_QH(BtsWorkParamBase btsWorkParamInfo, DateTime beginTime, DateTime endTime)
            : base(btsWorkParamInfo, beginTime, endTime)
        {
        }
    }
    public class BtsAlarmData_QH : BtsAlarmDataBase
    {
    }
    public class CellPerfData_QH : CellPerfDataBase
    {
        public CellPerfData_QH(bool isOutdoor)
            : base()
        {
            this.IsOutDoor = isOutdoor;
        }
        public bool IsOutDoor { get; set; }
        public float PdcpThroughputSum { get; private set; }
        public float WirelessConnectRate_QCI1 { get; set; }
        public float ErabDropRate_QCI1 { get; set; }
        public float EsrvccHandoverSuccessRate { get; set; }
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            this.CGI = content.GetParamString();
            this.BeginTime = DateTime.Parse(content.GetParamString());
            this.RrcConnectTryCount = content.GetParamInt();
            this.RrcSetupSuccessRate = content.GetParamFloat() * 100;
            this.ErabConnectTryCount = content.GetParamInt();
            this.ErabSetupSuccessRate = content.GetParamFloat() * 100;
            this.ErabDropRate = content.GetParamFloat() * 100;
            this.WirelessConnectRate = content.GetParamFloat() * 100;
            this.WirelessDropRate = content.GetParamFloat() * 100;
            this.InnerHandoverSuccessRate = content.GetParamFloat() * 100;
            this.PdcpThroughput_DL = content.GetParamFloat();//单位为Gb
            this.PdcpThroughput_UL = content.GetParamFloat();
            this.WirelessConnectRate_QCI1 = content.GetParamFloat() * 100;
            this.ErabDropRate_QCI1 = content.GetParamFloat() * 100;
            this.EsrvccHandoverSuccessRate = content.GetParamFloat() * 100;

            this.PdcpThroughputSum = PdcpThroughput_DL + PdcpThroughput_UL;
        }
        public override bool IsAccord
        {
            get
            {
                float pdcpThroughputSum_M = PdcpThroughputSum * 1000;
                return RrcConnectTryCount >= 20 && RrcSetupSuccessRate >= 99 && ErabConnectTryCount >= 20
                    && ErabSetupSuccessRate >= 99 && WirelessConnectRate >= 98 && WirelessDropRate < 2
                    && ErabDropRate < 2 && InnerHandoverSuccessRate >= 90 && EsrvccHandoverSuccessRate >= 90
                    && WirelessConnectRate_QCI1 >= 98 && ErabDropRate_QCI1 < 2
                    && (IsOutDoor ? pdcpThroughputSum_M >= 50 : pdcpThroughputSum_M >= 100);
            }
        }
    }
    public class CellMRData_QH : CellMRDataBase
    {
        public CellMRData_QH(bool isOutdoor)
            : base()
        {
            this.IsOutDoor = isOutdoor;
        }
        public bool IsOutDoor { get; set; }
        public override bool IsAccord
        {
            get
            {
                return IsOutDoor ? MrCoverRate >= 70 : MrCoverRate >= 95;
            }
        }
    }
}
