﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTProblemSummary4G
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabDataTD = new DevExpress.XtraTab.XtraTabControl();
            this.outPutExcel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miOutPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridXQ = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.dataGridSumarry = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridSum = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.tabDataTD)).BeginInit();
            this.tabDataTD.SuspendLayout();
            this.outPutExcel.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridXQ)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.dataGridSumarry.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridSum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // tabDataTD
            // 
            this.tabDataTD.ContextMenuStrip = this.outPutExcel;
            this.tabDataTD.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabDataTD.Location = new System.Drawing.Point(0, 0);
            this.tabDataTD.Name = "tabDataTD";
            this.tabDataTD.SelectedTabPage = this.xtraTabPage1;
            this.tabDataTD.Size = new System.Drawing.Size(1106, 480);
            this.tabDataTD.TabIndex = 13;
            this.tabDataTD.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.dataGridSumarry});
            // 
            // outPutExcel
            // 
            this.outPutExcel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miOutPutExcel});
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(125, 26);
            // 
            // miOutPutExcel
            // 
            this.miOutPutExcel.Name = "miOutPutExcel";
            this.miOutPutExcel.Size = new System.Drawing.Size(124, 22);
            this.miOutPutExcel.Text = "导出Excel";
            this.miOutPutExcel.Click += new System.EventHandler(this.miOutPutExcel_Click);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.dataGridXQ);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1099, 450);
            this.xtraTabPage1.Text = "详情";
            // 
            // dataGridXQ
            // 
            this.dataGridXQ.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridXQ.Location = new System.Drawing.Point(0, 0);
            this.dataGridXQ.MainView = this.gridView1;
            this.dataGridXQ.Name = "dataGridXQ";
            this.dataGridXQ.Size = new System.Drawing.Size(1099, 450);
            this.dataGridXQ.TabIndex = 1;
            this.dataGridXQ.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn5,
            this.gridColumn7,
            this.gridColumn13,
            this.gridColumn15});
            this.gridView1.GridControl = this.dataGridXQ;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "IID";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 62;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "城市";
            this.gridColumn2.FieldName = "StrCity";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 91;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "测试日期";
            this.gridColumn3.FieldName = "StrTestTime";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 130;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "测试点名称";
            this.gridColumn4.FieldName = "StrTestPoint";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 98;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "测试点经度";
            this.gridColumn11.FieldName = "DLongitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 4;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "测试点纬度";
            this.gridColumn12.FieldName = "DLatitude";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 5;
            this.gridColumn12.Width = 71;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "测试点属性";
            this.gridColumn5.FieldName = "Strcqttype";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 6;
            this.gridColumn5.Width = 103;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "覆盖属性";
            this.gridColumn7.FieldName = "StrCoverType";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 7;
            this.gridColumn7.Width = 94;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "问题类型";
            this.gridColumn15.FieldName = "StrMainType";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 9;
            this.gridColumn15.Width = 232;
            // 
            // dataGridSumarry
            // 
            this.dataGridSumarry.Controls.Add(this.dataGridSum);
            this.dataGridSumarry.Name = "dataGridSumarry";
            this.dataGridSumarry.Size = new System.Drawing.Size(1099, 450);
            this.dataGridSumarry.Text = "汇总";
            // 
            // dataGridSum
            // 
            this.dataGridSum.ContextMenuStrip = this.outPutExcel;
            this.dataGridSum.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridSum.Location = new System.Drawing.Point(0, 0);
            this.dataGridSum.MainView = this.gridView2;
            this.dataGridSum.Name = "dataGridSum";
            this.dataGridSum.Size = new System.Drawing.Size(1099, 450);
            this.dataGridSum.TabIndex = 0;
            this.dataGridSum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn6,
            this.gridColumn8,
            this.gridColumn25,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn26});
            this.gridView2.GridControl = this.dataGridSum;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "城市类别";
            this.gridColumn16.FieldName = "StrCityType";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 0;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "地市名称";
            this.gridColumn17.FieldName = "StrCity";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 1;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "CSFB回落不达标";
            this.gridColumn18.FieldName = "ICallCSFBLess";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 2;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "全程呼叫成功率不达标";
            this.gridColumn19.FieldName = "ICallSuccessLess";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 3;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "语音未达标点数";
            this.gridColumn20.FieldName = "ILessThenColeVoice";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 4;
            this.gridColumn20.Width = 97;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "下载速率低";
            this.gridColumn21.FieldName = "ILessDownDn";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 5;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "下载掉线";
            this.gridColumn22.FieldName = "IDownDrop";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 6;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "数据未达标点数";
            this.gridColumn23.FieldName = "ILessThenColeData";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 7;
            this.gridColumn23.Width = 97;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "问题点总数";
            this.gridColumn24.FieldName = "ICityColePoint";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 8;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "语音测试总数";
            this.gridColumn6.FieldName = "ITestColeVioce";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 9;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "数据测试总数";
            this.gridColumn8.FieldName = "ITestColeData";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 10;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "测试总数";
            this.gridColumn25.FieldName = "ITestCole";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 12;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "语音通过率";
            this.gridColumn9.FieldName = "StrTestSucessVioce";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 11;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "数据通过率";
            this.gridColumn10.FieldName = "StrTestSucessData";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 13;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "测试通过率";
            this.gridColumn26.FieldName = "StrTestSucess";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 14;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "匹配站点信息";
            this.gridColumn13.FieldName = "StrValue8";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 8;
            this.gridColumn13.Width = 122;
            // 
            // CQTProblemSummary4G
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1106, 480);
            this.Controls.Add(this.tabDataTD);
            this.Name = "CQTProblemSummary4G";
            this.Text = "CQT问题点4G分析";
            ((System.ComponentModel.ISupportInitialize)(this.tabDataTD)).EndInit();
            this.tabDataTD.ResumeLayout(false);
            this.outPutExcel.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridXQ)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.dataGridSumarry.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridSum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabDataTD;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage dataGridSumarry;
        private System.Windows.Forms.ContextMenuStrip outPutExcel;
        private System.Windows.Forms.ToolStripMenuItem miOutPutExcel;
        private DevExpress.XtraGrid.GridControl dataGridXQ;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.GridControl dataGridSum;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
    }
}