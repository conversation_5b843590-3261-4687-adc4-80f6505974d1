﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DropCallReasonAnalyseBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static DropCallReasonAnalyseBase instance = null;

        List<DropCallReasonInfo> resultList = null;
        List<DropCallReasonInfo> resultListMt = null;
        DropCallReasonCondition con = null;
        public static DropCallReasonAnalyseBase GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new DropCallReasonAnalyseBase();
                    }
                }
            }
            return instance;
        }
        protected DropCallReasonAnalyseBase()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }
        public override string Name
        {
            get
            {
                return "VOLTE掉话原因分析(按区域)";
            }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27016, this.Name);
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        DropCallReasonSettingDlg setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new DropCallReasonSettingDlg();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                resultList = new List<DropCallReasonInfo>();
                resultListMt = new List<DropCallReasonInfo>();
                setForm.GetCondition(out con);
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0 && resultListMt.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("主叫没有符合条件的信息！");
            }
            if (resultListMt.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("被叫没有符合条件的信息！");
            }
            DropCallReasonResultDlg frm = MainModel.CreateResultForm(typeof(DropCallReasonResultDlg)) as DropCallReasonResultDlg;
            frm.FillData(resultList, resultListMt);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }

        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.Momt == 1)
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate(FileInfo x)
                    {
                        return x.ID == fileInfo.EventCount;
                    });
                    moMtPair[fileInfo] = mtFile;
                }
            }
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.Momt == 2 && !moMtPair.ContainsValue(fileInfo))
                {
                    moMtPair[fileInfo] = null;
                }
            }
            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }
        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == 1)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == 2)
                {
                    mtFile = file;
                }
            }
            DropCallReasonInfo info = null;
            List<DataInfo> dataList = new List<DataInfo>();

            #region Mo掉话原因分析
            if (moFile != null)
            {
                DataInfo datainfo = null;
                foreach (DTData data in moFile.DTDatas)
                {
                    if (datainfo != null)
                    {
                        datainfo.AddDt(data);
                    }
                    if (data is Event)
                    {
                        Event evt = data as Event;
                        if (evt.ID == 1078 && datainfo != null)              //VoLTE MO Drop Call
                        {
                            datainfo.DropCall = evt;
                            dataList.Add(datainfo);
                            datainfo = null;
                        }
                        if (evt.ID == 1070)             //VoLTE MO Call Attempt
                        {
                            datainfo = new DataInfo();
                            datainfo.DtDatas.Add(data);
                            datainfo.CallAttempt = evt;
                        }
                    }
                }
                foreach (DataInfo di in dataList)
                {
                    bool isBye = false;
                    bool isByeOK = false;
                    foreach (DTData data in di.DtDatas)
                    {
                        if (data is Message)
                        {
                            Message msg = data as Message;
                            if (msg.ID == (int)EMsg.IMS_SIP_ACK)
                            {
                                info = new DropCallReasonInfo(moFile, mtFile, di);
                                info.Imssipack = msg;
                            }
                            else if (info != null)
                            {
                                if (msg.ID == (int)EMsg.IMS_SIP_BYE)
                                {
                                    isBye = true;
                                    info.Imssipbye = msg;
                                    if (msg.Direction == 1)
                                    {
                                        info.Reason = "未收到上行BYE请求";
                                        info.ReasonDesc = "收到下行BYE请求";
                                        info.SN = resultList.Count + 1;
                                        resultList.Add(info);
                                        info = null;
                                        isBye = false;
                                    }
                                }
                                if (msg.ID == (int)EMsg.IMS_SIP_BYE200OK && isBye)
                                {
                                    isByeOK = true;
                                    if ((di.DropCall.DateTime - di.CallAttempt.DateTime).TotalSeconds >= 180)
                                    {
                                        info.Reason = "呼叫完整";
                                        info.ReasonDesc = "通话时长大于180s";
                                        info.SN = resultList.Count + 1;
                                        resultList.Add(info);
                                        info = null;
                                    }
                                    else if (Convert.ToDouble(di.DropCall["Value10"].ToString()) == 7)
                                    {
                                        info.Reason = "呼叫不完整";
                                        info.ReasonDesc = "存在RTP单通";
                                        info.SN = resultList.Count + 1;
                                        resultList.Add(info);
                                        info = null;
                                    }
                                    else
                                    {
                                        //待确定是否加上后续判断
                                    }
                                }
                            }
                        }
                    }
                    if (!isBye
                        && !hapESRVCC(di, moFile, mtFile, ref resultList, true)
                        && !hapCover(di, moFile, mtFile, ref resultList, true)
                        && !hapTaRccFail(di, moFile, mtFile, ref resultList, true)
                        && !hapRealese(di, moFile, mtFile, ref resultList, true)
                        && info != null)
                    {
                        info.Reason = "未发送上行BYE请求";
                        info.ReasonDesc = "未伴随切换释放承载";
                        info.SN = resultList.Count + 1;
                        resultList.Add(info);
                        info = null;
                    }
                    if (!isByeOK && isBye
                        && !hapCover(di, info, ref resultList, true)
                        && !hapRccFail(di, info, ref resultList, true)
                        && !hapESRVCC(di, info, ref resultList, true)
                        && !hapRealese(di, info, true)
                        && !hapMt200ok(info, mtFile, true) && info != null)
                    {
                        info.Reason = "未收到下行BYE_200_OK";
                        info.ReasonDesc = "被叫未发送BYE_200_OK";
                        info.SN = resultList.Count + 1;
                        resultList.Add(info);
                        info = null;
                    }
                }
            }
            #endregion

            info = null;
            #region Mt掉话原因分析
            if (mtFile != null)
            {
                dataList = new List<DataInfo>();
                DataInfo di = null;
                foreach (DTData data in mtFile.DTDatas)
                {
                    if (di != null)
                    {
                        di.DtDatas.Add(data);
                    }
                    if (data is Event)
                    {
                        Event evt = data as Event;
                        if (evt.ID == 1079 && di != null)    //VoLTE MT Drop Call
                        {
                            di.DropCall = evt;
                            dataList.Add(di);
                            di = null;
                        }
                        if (evt.ID == 1071)    //VoLTE MT Call Attempt
                        {
                            di = new DataInfo();
                            di.DtDatas.Add(data);
                            di.CallAttempt = evt;
                        }
                    }
                }
                foreach (DataInfo df in dataList)
                {
                    bool isMtBye = false;
                    bool isMtByeOK = false;
                    bool isMtByeUp = false;
                    foreach (DTData data in df.DtDatas)
                    {
                        if (data is Message)
                        {
                            Message msg = data as Message;
                            if (msg.ID == (int)EMsg.IMS_SIP_ACK)
                            {
                                info = new DropCallReasonInfo(moFile, mtFile, df);
                                info.Imssipack = msg;
                            }
                            else if (info != null)
                            {
                                if (msg.ID == (int)EMsg.IMS_SIP_BYE)
                                {
                                    if (msg.Direction == 1)
                                    {
                                        isMtBye = true;       //收到bye
                                        info.Imssipbye = msg;
                                        if (info.Imssipbyeup != null)
                                        {
                                            info.ReasonDesc = "收到双BYE";
                                            info.SN = resultListMt.Count + 1;
                                            resultListMt.Add(info);
                                            info = null;
                                            isMtByeUp = true;
                                        }
                                    }
                                    else if (msg.Direction == 2)
                                    {
                                        info.Reason = "发送上行BYE";
                                        info.Imssipbyeup = msg;
                                    }
                                }
                                if (info != null && info.Imssipbye != null
                                    && msg.ID == (int)EMsg.IMS_SIP_BYE200OK && msg.Direction == 2)
                                {
                                    isMtByeOK = true;

                                    if ((df.DropCall.DateTime - df.CallAttempt.DateTime).TotalSeconds >= 180)
                                    {
                                        info.Reason = "呼叫完整";
                                        info.ReasonDesc = "通话时长大于180s";
                                        info.SN = resultListMt.Count + 1;
                                        resultListMt.Add(info);
                                        info = null;
                                    }
                                    else if (Convert.ToDouble(df.DropCall["Value10"].ToString()) == 7)
                                    {
                                        info.Reason = "呼叫不完整";
                                        info.ReasonDesc = "存在RTP单通";
                                        info.SN = resultListMt.Count + 1;
                                        resultListMt.Add(info);
                                        info = null;
                                    }
                                    else
                                    {
                                        //待确定是否加上后续判断
                                    }
                                }
                            }
                        }
                    }
                    if (!isMtByeUp)
                    {
                        //未收到bye
                        if (!isMtBye
                            && !hapESRVCC(df, moFile, mtFile, ref resultListMt, false)
                            && !hapCover(df, moFile, mtFile, ref resultListMt, false)
                            && !hapTaRccFail(df, moFile, mtFile, ref resultListMt, false)
                            && !hapRealese(df, moFile, mtFile, ref resultListMt, false)
                            && info != null)
                        {
                            info.Reason = "未发送上行BYE请求";
                            info.ReasonDesc = "未伴随切换释放承载";
                            info.SN = resultListMt.Count + 1;
                            resultListMt.Add(info);
                            info = null;
                        }
                        if (!isMtByeOK && isMtBye
                            && !hapCover(df, info, ref resultListMt, false)
                            && !hapRccFail(df, info, ref resultListMt, false)
                            && !hapESRVCC(df, info, ref resultListMt, false) && info != null)
                        {
                            info.Reason = "未发送上行BYE_200_OK";
                            info.ReasonDesc = "未发生ESRVCC";
                            info.SN = resultListMt.Count + 1;
                            resultListMt.Add(info);
                            info = null;
                        }
                    }
                }
            }
            #endregion
        }
        /// <summary>
        /// 判断是否发生ESRVCC
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        private bool hapESRVCC(DataInfo di, DTFileDataManager moFile, DTFileDataManager mtFile, ref List<DropCallReasonInfo> result, bool isMo)
        {
            foreach (DTData data in di.DtDatas)
            {
                DropCallReasonInfo info = new DropCallReasonInfo(moFile, mtFile, di);
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMsg.IMS_SIP_ACK)
                    {
                        info.Imssipack = msg;
                    }
                }
                if (data is Event && info.Imssipack != null)
                {
                    Event evt = data as Event;
                    if (evt.ID == 1145)
                    {
                        setReason(isMo, info);
                        info.ReasonDesc = "发生ESRVCC";
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }

        private static void setReason(bool isMo, DropCallReasonInfo info)
        {
            if (isMo)     //MO
            {
                info.Reason = "未发送上行BYE请求";
            }
            else    //MT
            {
                info.Reason = "未收到下行BYE请求";
            }
        }

        private bool hapESRVCC(DataInfo di, DropCallReasonInfo info, ref List<DropCallReasonInfo> result, bool isMo)
        {
            foreach (DTData data in di.DtDatas)
            {
                if (data.SN >= info.Imssipbye.SN && data is Event && info.Imssipack != null)
                {
                    Event evt = data as Event;
                    if (evt.ID == 1145)
                    {
                        if (isMo)     //MO
                        {
                            info.Reason = "未收到下行BYE_2oo_OK请求";
                        }
                        else    //MT
                        {
                            info.Reason = "未发送上行BYE_200_OK请求";
                        }
                        info.ReasonDesc = "发生ESRVCC";
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 判断是否弱覆盖/干扰
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        private bool hapCover(DataInfo di, DTFileDataManager moFile,DTFileDataManager mtFile, ref List<DropCallReasonInfo> result, bool isMo)
        {
            DropCallReasonInfo info = new DropCallReasonInfo(moFile, mtFile, di);
            foreach (DTData data in di.DtDatas)
            {
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMsg.IMS_SIP_ACK)
                    {
                        info.Imssipack = msg;
                    }
                }
                if (data is TestPoint && info.Imssipack != null)
                {
                    TestPoint tp = data as TestPoint;
                    float? rsrp = (float?)GetRSRP(tp);
                    float? sinr = (float?)GetSINR(tp);
                    if (rsrp != null && rsrp < -110 && sinr != null && sinr < -5)
                    {
                        setReason(isMo, info);
                        info.ReasonDesc = "存在弱覆盖/干扰";
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        private bool hapCover(DataInfo di, DropCallReasonInfo info, ref List<DropCallReasonInfo> result, bool isMo)
        {
            foreach (DTData data in di.DtDatas)
            {
                if (data.SN >= info.Imssipbye.SN && data is TestPoint)
                {
                    TestPoint tp = data as TestPoint;
                    float? rsrp = (float?)GetRSRP(tp);
                    float? sinr = (float?)GetSINR(tp);
                    if (rsrp != null && rsrp < -110 && sinr != null && sinr < -5)
                    {
                        if (isMo)
                        {
                            info.Reason = "未收到下行BYE_200_OK";
                        }
                        else
                        {
                            info.Reason = "未发送上行BYE_200_OK";
                        }
                        info.ReasonDesc = "存在弱覆盖/干扰";
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 判断是否伴随TA更新失败、RRC重建失败、Service失败
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        private bool hapTaRccFail(DataInfo di,DTFileDataManager moFile,DTFileDataManager mtFile, ref List<DropCallReasonInfo> result, bool isMo)
        {
            DropCallReasonInfo info = new DropCallReasonInfo(moFile, mtFile, di);
            setReason(isMo, info);
            foreach (DTData data in di.DtDatas)
            {
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMsg.IMS_SIP_ACK)
                    {
                        info.Imssipack = msg;
                    }
                }
                if (data is Event && info.Imssipack != null)
                {
                    Event evt = data as Event;
                    if (evt.ID == 868 || evt.ID == 1260 || evt.ID == 854)
                    {
                        setReasonDesc(info, evt);
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }

        private static void setReasonDesc(DropCallReasonInfo info, Event evt)
        {
            if (evt.ID == 854)           //Track Area Update Fail
            {
                info.ReasonDesc = "TA更新失败";
            }
            else if (evt.ID == 868)      //RRC Connection Reestablish Fail
            {
                info.ReasonDesc = "RRC重建失败";
            }
            else              //LTE Service Failure
            {
                info.ReasonDesc = "Service失败";
            }
        }

        private bool hapRccFail(DataInfo di, DropCallReasonInfo info, ref List<DropCallReasonInfo> result, bool isMo)
        {
            if (isMo)
            {
                info.Reason = "未收到下行BYE_200_OK";
            }
            else
            {
                info.Reason = "未发送上行BYE_200_OK";
            }
            foreach (DTData data in di.DtDatas)
            {
                if (data.SN >= info.Imssipbye.SN && data is Event)
                {
                    Event evt = data as Event;
                    if (evt.ID == 868 || evt.ID == 1260)
                    {
                        if (evt.ID == 868)
                        {
                            info.ReasonDesc = "RRC重建失败";
                        }
                        else
                        {
                            info.ReasonDesc = "Service失败";
                        }
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 判断是否伴随切换释放重载
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        private bool hapRealese(DataInfo di,DTFileDataManager moFile,DTFileDataManager mtFile, ref List<DropCallReasonInfo> result, bool isMo)
        {
            DropCallReasonInfo info = new DropCallReasonInfo(moFile, mtFile, di);
            if (isMo)
            {
                info.Reason = "未发送上行BYE请求";
            }
            else
            {
                info.Reason = "未收到下行BYE请求";
            }
            foreach (DTData data in di.DtDatas)
            {
                if (data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == (int)EMsg.IMS_SIP_ACK)
                    {
                        info.Imssipack = msg;
                    }
                    if (msg.ID == 1097532110 && info.Imssipack != null)
                    {
                        info.ReasonDesc = "伴随切换释放承载";
                        info.SN = result.Count + 1;
                        result.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        private bool hapRealese(DataInfo di, DropCallReasonInfo info, bool isMo)
        {
            if (isMo)
            {
                info.Reason = "未收到下行BYE_200_OK";
            }
            else
            {
                info.Reason = "未发送上行BYE_200_OK";
            }
            foreach (DTData data in di.DtDatas)
            {
                if (data.SN > info.Imssipbye.SN && data is Message)
                {
                    Message msg = data as Message;
                    if (msg.ID == 1097532110)
                    {
                        info.ReasonDesc = "伴随切换释放承载";
                        info.SN = resultList.Count + 1;
                        resultList.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 判断被叫是否正常发送200OK
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="info"></param>
        /// <param name="mtFile"></param>
        /// <returns></returns>
        private bool hapMt200ok(DropCallReasonInfo info, DTFileDataManager mtFile, bool isMo)
        {
            if (isMo)
            {
                info.Reason = "未收到下行BYE_200_OK";
            }
            else
            {
                info.Reason = "未发送上行BYE_200_OK";
            }
            if (mtFile != null)
            {
                foreach (Message m in mtFile.Messages)
                {
                    if (info.Imssipbye.DateTime < m.DateTime && (m.DateTime-info.Imssipbye.DateTime).TotalSeconds<=con.MoMtContact && m.ID == (int)EMsg.IMS_SIP_BYE200OK)      //设置条件，限制主被叫关联时间差
                    {
                        info.ReasonDesc = "被叫已发送BYE_200_OK";
                        info.SN = resultList.Count + 1;
                        resultList.Add(info);
                        return true;
                    }
                }
            }
            return false;
        }
        protected object GetRSRP(TestPoint tp)
        {
            return tp["lte_RSRP"];
        }
        protected object GetSINR(TestPoint tp)
        {
            return tp["lte_SINR"];
        }
    }

    public enum EMsg
    {
        IMS_SIP_ACK=1107361792,
        IMS_SIP_BYE=1107427328,
        IMS_SIP_BYE200OK=1107443912,
    }

    public class DropCallReasonCondition
    {
        public int MoMtContact { get; set; }
        public DropCallReasonCondition()
        {
            this.MoMtContact = 10;
        }
    }

    public class DataInfo
    {
        private readonly List<DTData> dtList = new List<DTData>();
        public void AddDt(DTData data)
        {
            dtList.Add(data);
        }
        public List<DTData> DtDatas
        {
            get { return dtList; }
        }
        public Event DropCall { get; set; }
        public Event CallAttempt { get; set; }
    }

    public class DropCallReasonInfo
    {
        public DropCallReasonInfo(DTFileDataManager mo, DTFileDataManager mt, DataInfo df)
        {
            if (mo != null)
            {
                this.MoFileName = mo.FileName;
            }
            if (mt != null)
            {
                this.MtFileName = mt.FileName;
            }
            this.Dropcall = df.DropCall;
            this.Callattempt = df.CallAttempt;
        }
        public string MtFileName { get; set; }
        public string MoFileName { get; set; }
        public int SN { get; set; }
        public string Reason { get; set; }
        public string ReasonDesc { get; set; }
        public Message Imssipack { get; set; }
        public Message Imssipbye { get; set; }
        public Message Imssipbyeup { get; set; }
        public Event Dropcall { get; set; }
        public Event Callattempt { get; set; }
    }
}
