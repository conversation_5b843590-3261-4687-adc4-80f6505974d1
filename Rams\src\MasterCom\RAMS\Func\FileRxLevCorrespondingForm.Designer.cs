﻿namespace MasterCom.RAMS.Func
{
    partial class FileRxLevCorrespondingForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnXH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileOneName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileTwoName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevSubSpread = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miCompareReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.ToolStripMenuItemToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnXH);
            this.objectListView.AllColumns.Add(this.olvColumnFileOneName);
            this.objectListView.AllColumns.Add(this.olvColumnFileTwoName);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevSubSpread);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnXH,
            this.olvColumnFileOneName,
            this.olvColumnFileTwoName,
            this.olvColumnRxLevSubSpread});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 25);
            this.objectListView.MultiSelect = false;
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(747, 362);
            this.objectListView.TabIndex = 9;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnXH
            // 
            this.olvColumnXH.AspectName = "";
            this.olvColumnXH.HeaderFont = null;
            this.olvColumnXH.Text = "序号";
            this.olvColumnXH.Width = 40;
            // 
            // olvColumnFileOneName
            // 
            this.olvColumnFileOneName.AspectName = "FileMOName";
            this.olvColumnFileOneName.HeaderFont = null;
            this.olvColumnFileOneName.Text = "主叫文件";
            this.olvColumnFileOneName.Width = 300;
            // 
            // olvColumnFileTwoName
            // 
            this.olvColumnFileTwoName.AspectName = "FileMTName";
            this.olvColumnFileTwoName.HeaderFont = null;
            this.olvColumnFileTwoName.Text = "被叫文件";
            this.olvColumnFileTwoName.Width = 300;
            // 
            // olvColumnRxLevSubSpread
            // 
            this.olvColumnRxLevSubSpread.AspectName = "RxlevSubSpread";
            this.olvColumnRxLevSubSpread.HeaderFont = null;
            this.olvColumnRxLevSubSpread.Text = "平均电平差值";
            this.olvColumnRxLevSubSpread.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miCompareReplay,
            this.ToolStripMenuItemToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 70);
            // 
            // miCompareReplay
            // 
            this.miCompareReplay.Name = "miCompareReplay";
            this.miCompareReplay.Size = new System.Drawing.Size(152, 22);
            this.miCompareReplay.Text = "对比回放";
            this.miCompareReplay.Click += new System.EventHandler(this.miCompareReplay_Click);
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.SystemColors.Control;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(747, 25);
            this.toolStrip1.TabIndex = 8;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(57, 22);
            this.toolStripLabel1.Text = "电平差 > ";
            // 
            // ToolStripMenuItemToExcel
            // 
            this.ToolStripMenuItemToExcel.Name = "ToolStripMenuItemToExcel";
            this.ToolStripMenuItemToExcel.Size = new System.Drawing.Size(152, 22);
            this.ToolStripMenuItemToExcel.Text = "导出EXCEL";
            this.ToolStripMenuItemToExcel.Click += new System.EventHandler(this.ToolStripMenuItemToExcel_Click);
            // 
            // FileRxLevCorrespondingForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(747, 387);
            this.Controls.Add(this.objectListView);
            this.Controls.Add(this.toolStrip1);
            this.Name = "FileRxLevCorrespondingForm";
            this.Text = "主被叫电平差值分析";
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnXH;
        private BrightIdeasSoftware.OLVColumn olvColumnFileOneName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileTwoName;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevSubSpread;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplay;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemToExcel;
    }
}