﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo
{
    public class NCellInfo : CellSignalLevInfo
    {
        public CellSignalLevInfo SCell
        {
            get;
            private set;
        }
        public double Distance2SCell
        {
            get;
            private set;
        }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public NCellInfo(TestPoint tp, ICell sCell, float sCellSignalLev, ICell cell, float signalLev)
            : base(cell, signalLev)
        {
            if (tp != null)
            {
                TestPoints.Add(tp);
            }
            SCell = new CellSignalLevInfo(sCell, sCellSignalLev);
            Distance2SCell = Math.Round(MasterCom.Util.MathFuncs.GetDistance(cell.Longitude, cell.Latitude, sCell.Longitude, sCell.Latitude)
                , 2);
        }

        public void AddSignalLev(TestPoint tp, float sCellSignalLev, float signalLev)
        {
            if (tp != null)
            {
                TestPoints.Add(tp);
            }
            SCell.AddSignalLev(sCellSignalLev);
            AddSignalLev(signalLev);
        }

    }
}
