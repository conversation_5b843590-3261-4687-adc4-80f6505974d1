﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsTestDepthResult : LteMgrsResultControlBase
    {
        public LteMgrsTestDepthResult()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            miExportWord.Click += base.MiExportWord_Click;
            miExportAllExcel.Click += base.MiExportExcelAll_Click;
        }

        public override string Desc
        {
            get { return "测试深度"; }
        }

        public void FillData(object data, List<LteMgrsDrawItem> drawList)
        {
            gridControl1.DataSource = data;
            gridControl1.RefreshDataSource();
            this.drawList = drawList;
        }

        public override void DrawOnLayer()
        {
            if (drawList == null || drawList.Count == 0)
            {
                base.DrawOnLayer();
                return;
            }

            LteMgrsLayer.DrawList = drawList;
            DbRect rect = LteMgrsLayer.GetDrawBound(drawList);
            if (rect != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(rect);
                SetNormalMapScale();
            }
            LteMgrsLayer.LegendGroup = LteMgrsTestDepthStater.Ranges.GetLegend();
            MainModel.RefreshLegend();
        }

        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = "测试深度";
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
        }

        protected override bool ExportWord(WordControl word, string title)
        {
            if (title != "测试深度")
            {
                return false;
            }
            WaitTextBox.Text = "正在导出测试深度章节...";

            if (gridControl1.DataSource == null) // 未有基准库
            {
                word.InsertText("由于基准库时间段未设置，无法进行测试深度计算。", "正文");
                word.NewLine();
                return true;
            }

            List<LteMgrsTestDepthView> resultViewList = gridControl1.DataSource as List<LteMgrsTestDepthView>;
            LteMgrsTestDepthView summaryView = resultViewList[resultViewList.Count - 1]; // 
            string text = string.Format("{0}整体统计如下：基准库栅格数量为{1}，本次测试新增栅格数量为{2}，本次测试网格内测试栅格深度为{3}%（未统计网格外区域）。",
                summaryView.CityName, summaryView.BaseGridCount, summaryView.NewGridCount, Math.Round(summaryView.TestDepth * 100, 2));
            word.InsertText(text, "正文");
            word.NewLine();
            word.InsertText("整体渲染效果如下：", "正文");
            word.NewLine();

            this.DrawOnLayer();
            word.InsertPicture(LteMgrsScreenShoter.GisScreenshot("LTE测试深度"));
            word.NewLine();

            word.InsertText("分网格统计数据如下：", "正文");
            word.NewLine();
            (word as LteMgrsWordControl).InsertGridView(gridView1, null);

            int cnt = 0;
            for (int i = 1; i < resultViewList.Count - 1; ++i )
            {
                if (resultViewList[i].TestDepth < 0.75)
                {
                    ++cnt;
                }
            }
            text = string.Format("测试深度低于75%的网格共有{0}个（不含网格外）。", cnt);
            word.InsertText(text, "正文");
            word.NewLine();

            return true;
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private List<LteMgrsDrawItem> drawList;
    }
}
