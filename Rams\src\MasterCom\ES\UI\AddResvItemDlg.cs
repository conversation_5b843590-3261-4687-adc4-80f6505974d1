﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class AddResvItemDlg : BaseFormStyle
    {
        public string InputName
        {
            get 
            {
                return tbxRsvName.Text;
            }
        }
        /// <summary>
        /// 0 long ; 
        /// 1 string
        /// </summary>
        public int InputType
        {
            get
            {
                return rblong.Checked ? 0 : 1;
            }
        }
        public AddResvItemDlg()
        {
            InitializeComponent();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}