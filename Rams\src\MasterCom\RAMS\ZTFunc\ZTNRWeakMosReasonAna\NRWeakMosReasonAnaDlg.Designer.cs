﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRWeakMosReasonAnaDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.numMosWeakGate = new DevExpress.XtraEditors.SpinEdit();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numHandoverCountGate = new DevExpress.XtraEditors.SpinEdit();
            this.label11 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label14 = new System.Windows.Forms.Label();
            this.numWeakRsrpTpCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.numWeakRsrpGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.numWeakSinrTpCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numWeakSinrGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numRtpLostGate = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.checkedListBoxControlReason = new DevExpress.XtraEditors.ListBoxControl();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.tbxDescription = new System.Windows.Forms.TextBox();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMosWeakGate.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverCountGate.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRsrpTpCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRsrpGate.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakSinrTpCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakSinrGate.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRtpLostGate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).BeginInit();
            this.groupBox6.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.numMosWeakGate);
            this.splitContainerControl1.Panel1.Controls.Add(this.label8);
            this.splitContainerControl1.Panel1.Controls.Add(this.label9);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox1);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox2);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox3);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(580, 584);
            this.splitContainerControl1.SplitterPosition = 327;
            this.splitContainerControl1.TabIndex = 4;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // numMosWeakGate
            // 
            this.numMosWeakGate.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numMosWeakGate.Location = new System.Drawing.Point(155, 16);
            this.numMosWeakGate.Name = "numMosWeakGate";
            this.numMosWeakGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMosWeakGate.Properties.Appearance.Options.UseFont = true;
            this.numMosWeakGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMosWeakGate.Properties.Mask.EditMask = "f";
            this.numMosWeakGate.Properties.MaxValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMosWeakGate.Size = new System.Drawing.Size(60, 20);
            this.numMosWeakGate.TabIndex = 35;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(33, 19);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(119, 12);
            this.label8.TabIndex = 33;
            this.label8.Text = "弱Mos门限：Mos值 ≤";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(223, 19);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(23, 12);
            this.label9.TabIndex = 32;
            this.label9.Text = "dBm";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numHandoverCountGate);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Location = new System.Drawing.Point(35, 51);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(480, 55);
            this.groupBox1.TabIndex = 30;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "频繁切换";
            // 
            // numHandoverCountGate
            // 
            this.numHandoverCountGate.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numHandoverCountGate.Location = new System.Drawing.Point(187, 25);
            this.numHandoverCountGate.Name = "numHandoverCountGate";
            this.numHandoverCountGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numHandoverCountGate.Properties.Appearance.Options.UseFont = true;
            this.numHandoverCountGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHandoverCountGate.Properties.IsFloatValue = false;
            this.numHandoverCountGate.Properties.Mask.EditMask = "f0";
            this.numHandoverCountGate.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numHandoverCountGate.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numHandoverCountGate.Size = new System.Drawing.Size(60, 20);
            this.numHandoverCountGate.TabIndex = 26;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(20, 28);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(161, 12);
            this.label11.TabIndex = 25;
            this.label11.Text = "低MOS时间段内切换事件次数>";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.numWeakRsrpTpCount);
            this.groupBox2.Controls.Add(this.labelControl18);
            this.groupBox2.Controls.Add(this.numWeakRsrpGate);
            this.groupBox2.Controls.Add(this.labelControl38);
            this.groupBox2.Location = new System.Drawing.Point(35, 120);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(480, 55);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "弱覆盖";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.Location = new System.Drawing.Point(385, 28);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(23, 12);
            this.label14.TabIndex = 33;
            this.label14.Text = "dBm";
            // 
            // numWeakRsrpTpCount
            // 
            this.numWeakRsrpTpCount.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numWeakRsrpTpCount.Location = new System.Drawing.Point(151, 25);
            this.numWeakRsrpTpCount.Name = "numWeakRsrpTpCount";
            this.numWeakRsrpTpCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakRsrpTpCount.Properties.Appearance.Options.UseFont = true;
            this.numWeakRsrpTpCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakRsrpTpCount.Properties.IsFloatValue = false;
            this.numWeakRsrpTpCount.Properties.Mask.EditMask = "f0";
            this.numWeakRsrpTpCount.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numWeakRsrpTpCount.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numWeakRsrpTpCount.Size = new System.Drawing.Size(60, 20);
            this.numWeakRsrpTpCount.TabIndex = 9;
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Location = new System.Drawing.Point(19, 28);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(126, 12);
            this.labelControl18.TabIndex = 8;
            this.labelControl18.Text = "低MOS时间段内至少连续";
            // 
            // numWeakRsrpGate
            // 
            this.numWeakRsrpGate.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.numWeakRsrpGate.Location = new System.Drawing.Point(319, 25);
            this.numWeakRsrpGate.Name = "numWeakRsrpGate";
            this.numWeakRsrpGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakRsrpGate.Properties.Appearance.Options.UseFont = true;
            this.numWeakRsrpGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakRsrpGate.Properties.Mask.EditMask = "f";
            this.numWeakRsrpGate.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numWeakRsrpGate.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numWeakRsrpGate.Size = new System.Drawing.Size(60, 20);
            this.numWeakRsrpGate.TabIndex = 4;
            // 
            // labelControl38
            // 
            this.labelControl38.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl38.Appearance.Options.UseFont = true;
            this.labelControl38.Location = new System.Drawing.Point(217, 28);
            this.labelControl38.Name = "labelControl38";
            this.labelControl38.Size = new System.Drawing.Size(96, 12);
            this.labelControl38.TabIndex = 0;
            this.labelControl38.Text = "个采样点的RSRP<=";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.numWeakSinrTpCount);
            this.groupBox3.Controls.Add(this.labelControl1);
            this.groupBox3.Controls.Add(this.numWeakSinrGate);
            this.groupBox3.Controls.Add(this.labelControl2);
            this.groupBox3.Location = new System.Drawing.Point(35, 190);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(480, 55);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "质差";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(385, 28);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 38;
            this.label1.Text = "dB";
            // 
            // numWeakSinrTpCount
            // 
            this.numWeakSinrTpCount.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numWeakSinrTpCount.Location = new System.Drawing.Point(151, 25);
            this.numWeakSinrTpCount.Name = "numWeakSinrTpCount";
            this.numWeakSinrTpCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakSinrTpCount.Properties.Appearance.Options.UseFont = true;
            this.numWeakSinrTpCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakSinrTpCount.Properties.IsFloatValue = false;
            this.numWeakSinrTpCount.Properties.Mask.EditMask = "f0";
            this.numWeakSinrTpCount.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numWeakSinrTpCount.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numWeakSinrTpCount.Size = new System.Drawing.Size(60, 20);
            this.numWeakSinrTpCount.TabIndex = 37;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(22, 28);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(126, 12);
            this.labelControl1.TabIndex = 36;
            this.labelControl1.Text = "低MOS时间段内至少连续";
            // 
            // numWeakSinrGate
            // 
            this.numWeakSinrGate.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numWeakSinrGate.Location = new System.Drawing.Point(319, 25);
            this.numWeakSinrGate.Name = "numWeakSinrGate";
            this.numWeakSinrGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakSinrGate.Properties.Appearance.Options.UseFont = true;
            this.numWeakSinrGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakSinrGate.Properties.Mask.EditMask = "f";
            this.numWeakSinrGate.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numWeakSinrGate.Properties.MinValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numWeakSinrGate.Size = new System.Drawing.Size(60, 20);
            this.numWeakSinrGate.TabIndex = 35;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(217, 28);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(96, 12);
            this.labelControl2.TabIndex = 34;
            this.labelControl2.Text = "个采样点的SINR<=";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.numRtpLostGate);
            this.groupBox4.Controls.Add(this.label12);
            this.groupBox4.Location = new System.Drawing.Point(35, 260);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(480, 56);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "丢包";
            // 
            // numRtpLostGate
            // 
            this.numRtpLostGate.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRtpLostGate.Location = new System.Drawing.Point(259, 23);
            this.numRtpLostGate.Name = "numRtpLostGate";
            this.numRtpLostGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRtpLostGate.Properties.Appearance.Options.UseFont = true;
            this.numRtpLostGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRtpLostGate.Properties.IsFloatValue = false;
            this.numRtpLostGate.Properties.Mask.EditMask = "f0";
            this.numRtpLostGate.Properties.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numRtpLostGate.Size = new System.Drawing.Size(60, 20);
            this.numRtpLostGate.TabIndex = 22;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(20, 26);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(233, 12);
            this.label12.TabIndex = 20;
            this.label12.Text = "低MOS时间段内RTP packets Lost number >";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.checkedListBoxControlReason);
            this.groupControl1.Controls.Add(this.groupBox6);
            this.groupControl1.Controls.Add(this.simpleButtonCancel);
            this.groupControl1.Controls.Add(this.simpleButtonOK);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(580, 251);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "分析原因";
            // 
            // checkedListBoxControlReason
            // 
            this.checkedListBoxControlReason.Location = new System.Drawing.Point(12, 38);
            this.checkedListBoxControlReason.Name = "checkedListBoxControlReason";
            this.checkedListBoxControlReason.Size = new System.Drawing.Size(140, 199);
            this.checkedListBoxControlReason.TabIndex = 7;
            this.checkedListBoxControlReason.SelectedIndexChanged += new System.EventHandler(this.listBoxControlReason_SelectedIndexChanged);
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.tbxDescription);
            this.groupBox6.Location = new System.Drawing.Point(169, 38);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(405, 160);
            this.groupBox6.TabIndex = 6;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "分析场景说明";
            // 
            // tbxDescription
            // 
            this.tbxDescription.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbxDescription.Location = new System.Drawing.Point(13, 20);
            this.tbxDescription.Multiline = true;
            this.tbxDescription.Name = "tbxDescription";
            this.tbxDescription.ReadOnly = true;
            this.tbxDescription.Size = new System.Drawing.Size(379, 129);
            this.tbxDescription.TabIndex = 5;
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.simpleButtonCancel.Location = new System.Drawing.Point(474, 213);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.simpleButtonOK.Location = new System.Drawing.Point(357, 213);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            // 
            // NRWeakMosReasonAnaDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(580, 584);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "NRWeakMosReasonAnaDlg";
            this.Text = "弱MOS原因分析";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numMosWeakGate.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverCountGate.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRsrpTpCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRsrpGate.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakSinrTpCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakSinrGate.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRtpLostGate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).EndInit();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SpinEdit numMosWeakGate;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit numHandoverCountGate;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label14;
        private DevExpress.XtraEditors.SpinEdit numWeakRsrpTpCount;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.SpinEdit numWeakRsrpGate;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numWeakSinrTpCount;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numWeakSinrGate;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraEditors.SpinEdit numRtpLostGate;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.ListBoxControl checkedListBoxControlReason;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.TextBox tbxDescription;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
    }
}