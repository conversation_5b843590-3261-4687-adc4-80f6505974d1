﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYCellSetBriefDatabase : QueryBase
    {
        #region 全局变量
        Dictionary<CellKey, List<LongLat>> cellkeyDic;
        int curDistricID { get; set; } = -1;
        int iCurCity { get; set; } = 0;
        int iCityNum { get; set; } = 0;
        float fCityPart { get; set; } = 0;
        string strCityName { get; set; } = "";

        public bool isMaiCell { get; set; } = false;
        public bool isNearCell { get; set; } = false;
        public bool isContainNotGrid { get; set; } = false;

        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> tdcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> tdGsmcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> wcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> wGsmcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cdcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> evdocellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> evdocdma1xcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cd1xcellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();

        List<CellSetDetailInfo> cellSetDetailInfoList { get; set; } = new List<CellSetDetailInfo>();
        List<CellSetSummaryInfo> cellSetSummaryInfoList { get; set; } = new List<CellSetSummaryInfo>();
        List<CellSetStat> cellSetStatList { get; set; } = new List<CellSetStat>();

        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cellSetSeverNullInfoDic { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        #endregion

        public DIYCellSetBriefDatabase(MainModel mainModel)
            : base(mainModel)
        { }
        public override string Name
        {
            get { return "小区集精简查询"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12078, this.Name);
        }
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegion(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic = new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegion(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        /// <summary>
        /// 合并小区与基站数目信息
        /// </summary>
        private void MergeCellInfo()
        {
            cellSetDetailInfoList.AddRange(cellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(tdcellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(tdGsmcellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(wcellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(wGsmcellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(cdcellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(evdocellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(evdocdma1xcellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(cd1xcellStaterMap.Keys);

            cellSetDetailInfoList.Sort(CellSetDetailInfo.GetCompareByGridType());

            MergeCellSummaryInfo();//小区集概要统计

            MergeCellByServiceInfo();//小区集按业务汇总

        }
        /// <summary>
        /// 小区集概要统计
        /// </summary>
        private void MergeCellSummaryInfo()
        {
            Dictionary<CellSetSummaryInfo, CellSetSummaryInfo> cellSetSummaryInfoDic = new Dictionary<CellSetSummaryInfo, CellSetSummaryInfo>();
            foreach (CellSetDetailInfo csInfo in cellSetDetailInfoList)
            {
                CellSetSummaryInfo csSubInfo = new CellSetSummaryInfo();
                csSubInfo.strCarrier = csInfo.strCarrier;
                csSubInfo.strCellType = csInfo.strCellType;
                csSubInfo.strCity = csInfo.strCity;
                csSubInfo.strGridType = csInfo.strGridType;
                csSubInfo.strGrid = csInfo.strGrid;
                csSubInfo.strServiceType = csInfo.strServiceType;

                if (cellSetSummaryInfoDic.ContainsKey(csSubInfo))
                {
                    cellSetSummaryInfoDic[csSubInfo].iCellCount += 1;
                    List<string> cellList = cellSetSummaryInfoDic[csSubInfo].indoorCellList;
                    List<string> btsList = cellSetSummaryInfoDic[csSubInfo].btsList;
                    if (csInfo.strBtsType == "室内" && !cellList.Contains(csInfo.strCellName))
                    {
                        cellList.Add(csInfo.strCellName);
                    }
                    if (!btsList.Contains(csInfo.strBtsName))
                    {
                        btsList.Add(csInfo.strBtsName);
                    }
                }
                else
                {
                    List<string> cellList = new List<string>();
                    List<string> btsList = new List<string>();
                    if (csInfo.strBtsType == "室内")
                        cellList.Add(csInfo.strCellName);
                    btsList.Add(csInfo.strBtsName);
                    csSubInfo.indoorCellList = cellList;
                    csSubInfo.btsList = btsList;
                    csSubInfo.iCellCount = 1;
                    cellSetSummaryInfoDic.Add(csSubInfo, csSubInfo);
                }
            }

            foreach (CellSetSummaryInfo cellSetSum in cellSetSummaryInfoDic.Keys)
            {
                cellSetSummaryInfoList.Add(cellSetSummaryInfoDic[cellSetSum]);
            }
        }
        /// <summary>
        /// 按业务类型逐项累加
        /// </summary>
        private void StatCellNumOnByOne(Dictionary<StatKey, CellSetStat> cellSetDic, CellSetDetailInfo cellSetInfo, string strNet)
        {
            bool issame = isTheSame(cellSetInfo);
            StatKey statKey = new StatKey();
            statKey.strCity = cellSetInfo.strCity;
            statKey.strGridType = cellSetInfo.strGridType;
            statKey.strGrid = cellSetInfo.strGrid;

            CellSetStat csStat = new CellSetStat();
            if (cellSetDic.ContainsKey(statKey))
                csStat = cellSetDic[statKey];
            setCsStatByNet(cellSetInfo, strNet, issame, csStat);
            cellSetDic[statKey] = csStat;
        }

        private void setCsStatByNet(CellSetDetailInfo cellSetInfo, string strNet, bool issame, CellSetStat csStat)
        {
            if (strNet == "GSM")
            {
                setGSMCsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "TD")
            {
                setTDCsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "TDGSM")
            {
                setTDGSMCsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "WCDMA")
            {
                setWCDMACsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "WCDMAGSM")
            {
                setWCDMAGSMCsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "CDMA")
            {
                setCDMACsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "CDMA1X")
            {
                setCDMA1XCsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "EVDO")
            {
                setEVDOCsStat(cellSetInfo, issame, csStat);
            }
            else if (strNet == "EVDOCDMA1X")
            {
                setEVDOCDMA1XCsStat(cellSetInfo, issame, csStat);
            }
        }

        private static void setGSMCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (cellSetInfo.strCarrier == "移动")
            {
                if (!issame)
                    csStat.iAllYDGsmCellNum += 1;
                if (cellSetInfo.strServiceType == "语音")
                    csStat.iVGsmCellNum += 1;
                else if (cellSetInfo.strServiceType == "数据")
                    csStat.iDGsmCellNum += 1;
                else if (cellSetInfo.strServiceType == "空闲")
                    csStat.iFYDGsmCellNum += 1;
            }
            else
            {
                if (!issame)
                    csStat.iAllLTGsmCellNum += 1;
                if (cellSetInfo.strServiceType == "语音")
                    csStat.iVLtGsmCellNum += 1;
                else if (cellSetInfo.strServiceType == "数据")
                    csStat.iDLtGsmCellNum += 1;
                else if (cellSetInfo.strServiceType == "空闲")
                    csStat.iFLTGsmCellNum += 1;
            }
        }

        private static void setTDCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllTdCellNum += 1;
            if (cellSetInfo.strServiceType == "语音")
                csStat.iVTdCellNum += 1;
            else if (cellSetInfo.strServiceType == "数据")
                csStat.iDTdCellNum += 1;
            else if (cellSetInfo.strServiceType == "空闲")
                csStat.iFTdCellNum += 1;
        }

        private static void setTDGSMCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllYDGsmCellNum += 1;
            if (cellSetInfo.strServiceType == "语音")
                csStat.iVTdGsmCellNum += 1;
            else if (cellSetInfo.strServiceType == "数据")
                csStat.iDTdGsmCellNum += 1;
        }

        private static void setWCDMACsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllWCellNum += 1;
            if (cellSetInfo.strServiceType == "语音")
                csStat.iVWCellNum += 1;
            else if (cellSetInfo.strServiceType == "数据")
                csStat.iDWCellNum += 1;
            else if (cellSetInfo.strServiceType == "空闲")
                csStat.iFWCellNum += 1;
        }

        private static void setWCDMAGSMCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllLTGsmCellNum += 1;
            if (cellSetInfo.strServiceType == "语音")
                csStat.iVWGsmCellNum += 1;
            else if (cellSetInfo.strServiceType == "数据")
                csStat.iDWGsmCellNum += 1;
        }

        private static void setCDMACsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllCdmaCellNum += 1;
            if (cellSetInfo.strServiceType == "语音")
                csStat.iVCdCellNum += 1;
            else if (cellSetInfo.strServiceType == "空闲")
                csStat.iFDXCdmaCellNum += 1;
        }

        private static void setCDMA1XCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllCdmaCellNum += 1;
            if (cellSetInfo.strServiceType == "数据")
                csStat.iDCdCellNum += 1;
        }

        private static void setEVDOCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllEvodCellNum += 1;
            if (cellSetInfo.strServiceType == "数据")
                csStat.iDEvdoCellNum += 1;
            else if (cellSetInfo.strServiceType == "语音")
                csStat.iVCd2000CellNum += 1;
            else if (cellSetInfo.strServiceType == "空闲")
                csStat.iFEvodoCellNum += 1;
        }

        private void setEVDOCDMA1XCsStat(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllCdmaCellNum += 1;
            if (cellSetInfo.strServiceType == "数据")
                csStat.iDEvdoCdma1xCellNum += 1;
        }

        /// <summary>
        /// 小区集按业务汇总
        /// </summary>
        private void MergeCellByServiceInfo()
        {
            Dictionary<StatKey, CellSetStat> cellSetDic = new Dictionary<StatKey, CellSetStat>();
            foreach (CellSetDetailInfo cellSetInfo in cellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "GSM");
            }

            foreach (CellSetDetailInfo cellSetInfo in tdcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "TD");
            }

            foreach (CellSetDetailInfo cellSetInfo in tdGsmcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "TDGSM");
            }

            foreach (CellSetDetailInfo cellSetInfo in wcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "WCDMA");
            }

            foreach (CellSetDetailInfo cellSetInfo in wGsmcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "WCDMAGSM");
            }

            foreach (CellSetDetailInfo cellSetInfo in cdcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "CDMA");
            }

            foreach (CellSetDetailInfo cellSetInfo in evdocellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "EVDO");
            }

            foreach (CellSetDetailInfo cellSetInfo in evdocdma1xcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "EVDOCDMA1X");
            }

            foreach (CellSetDetailInfo cellSetInfo in cd1xcellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "CDMA1X");
            }

            MergeCellByServiceStat(cellSetDic);
        }
        /// <summary>
        /// 小区集按业务统计
        /// </summary>
        private void MergeCellByServiceStat(Dictionary<StatKey, CellSetStat> cellSetDic)
        {
            Dictionary<string, CellSetStat> allCellSetStatDic = new Dictionary<string, CellSetStat>();
            foreach (StatKey statKey in cellSetDic.Keys)
            {
                CellSetStat cellSetStat = cellSetDic[statKey];
                cellSetStat.strCity = statKey.strCity;
                cellSetStat.strGridType = statKey.strGridType;
                cellSetStat.strGrid = statKey.strGrid;
                cellSetStatList.Add(cellSetStat);

                if (allCellSetStatDic.ContainsKey(statKey.strCity))
                {
                    CellSetStat csStat = allCellSetStatDic[statKey.strCity];

                    csStat.iAllTdCellNum += cellSetStat.iAllTdCellNum;
                    csStat.iAllYDGsmCellNum += cellSetStat.iAllYDGsmCellNum;
                    csStat.iAllWCellNum += cellSetStat.iAllWCellNum;
                    csStat.iAllLTGsmCellNum += cellSetStat.iAllLTGsmCellNum;
                    csStat.iAllEvodCellNum += cellSetStat.iAllEvodCellNum;
                    csStat.iAllCdmaCellNum += cellSetStat.iAllCdmaCellNum;

                    csStat.iFTdCellNum += cellSetStat.iFTdCellNum;
                    csStat.iFWCellNum += cellSetStat.iFWCellNum;
                    csStat.iFEvodoCellNum += cellSetStat.iFEvodoCellNum;
                    csStat.iFYDGsmCellNum += cellSetStat.iFYDGsmCellNum;
                    csStat.iFLTGsmCellNum += cellSetStat.iFLTGsmCellNum;
                    csStat.iFDXCdmaCellNum += cellSetStat.iFDXCdmaCellNum;

                    csStat.iVTdCellNum += cellSetStat.iVTdCellNum;
                    csStat.iVTdGsmCellNum += cellSetStat.iVTdGsmCellNum;
                    csStat.iVWCellNum += cellSetStat.iVWCellNum;
                    csStat.iVWGsmCellNum += cellSetStat.iVWGsmCellNum;
                    csStat.iVCd2000CellNum += cellSetStat.iVCd2000CellNum;
                    csStat.iVGsmCellNum += cellSetStat.iVGsmCellNum;
                    csStat.iVLtGsmCellNum += cellSetStat.iVLtGsmCellNum;
                    csStat.iVCdCellNum += cellSetStat.iVCdCellNum;

                    csStat.iDTdCellNum += cellSetStat.iDTdCellNum;
                    csStat.iDTdGsmCellNum += cellSetStat.iDTdGsmCellNum;
                    csStat.iDWCellNum += cellSetStat.iDWCellNum;
                    csStat.iDWGsmCellNum += cellSetStat.iDWGsmCellNum;
                    csStat.iDEvdoCellNum += cellSetStat.iDEvdoCellNum;
                    csStat.iDEvdoCdma1xCellNum += cellSetStat.iDEvdoCdma1xCellNum;
                    csStat.iDGsmCellNum += cellSetStat.iDGsmCellNum;
                    csStat.iDLtGsmCellNum += cellSetStat.iDLtGsmCellNum;
                    csStat.iDCdCellNum += cellSetStat.iDCdCellNum;
                    allCellSetStatDic[statKey.strCity] = csStat;
                }
                else
                {
                    CellSetStat csStat = new CellSetStat();
                    csStat.strCity = cellSetStat.strCity;
                    csStat.strGridType = "汇总";
                    csStat.strGrid = "汇总";

                    csStat.iAllTdCellNum = cellSetStat.iAllTdCellNum;
                    csStat.iAllYDGsmCellNum = cellSetStat.iAllYDGsmCellNum;
                    csStat.iAllWCellNum = cellSetStat.iAllWCellNum;
                    csStat.iAllLTGsmCellNum = cellSetStat.iAllLTGsmCellNum;
                    csStat.iAllEvodCellNum = cellSetStat.iAllEvodCellNum;
                    csStat.iAllCdmaCellNum = cellSetStat.iAllCdmaCellNum;

                    csStat.iFTdCellNum = cellSetStat.iFTdCellNum;
                    csStat.iFWCellNum = cellSetStat.iFWCellNum;
                    csStat.iFEvodoCellNum = cellSetStat.iFEvodoCellNum;
                    csStat.iFYDGsmCellNum = cellSetStat.iFYDGsmCellNum;
                    csStat.iFLTGsmCellNum = cellSetStat.iFLTGsmCellNum;
                    csStat.iFDXCdmaCellNum = cellSetStat.iFDXCdmaCellNum;

                    csStat.iVTdCellNum = cellSetStat.iVTdCellNum;
                    csStat.iVTdGsmCellNum = cellSetStat.iVTdGsmCellNum;
                    csStat.iVWCellNum = cellSetStat.iVWCellNum;
                    csStat.iVWGsmCellNum = cellSetStat.iVWGsmCellNum;
                    csStat.iVCd2000CellNum = cellSetStat.iVCd2000CellNum;
                    csStat.iVGsmCellNum = cellSetStat.iVGsmCellNum;
                    csStat.iVLtGsmCellNum = cellSetStat.iVLtGsmCellNum;
                    csStat.iVCdCellNum = cellSetStat.iVCdCellNum;

                    csStat.iDTdCellNum = cellSetStat.iDTdCellNum;
                    csStat.iDTdGsmCellNum = cellSetStat.iDTdGsmCellNum;
                    csStat.iDWCellNum = cellSetStat.iDWCellNum;
                    csStat.iDWGsmCellNum = cellSetStat.iDWGsmCellNum;
                    csStat.iDEvdoCellNum = cellSetStat.iDEvdoCellNum;
                    csStat.iDEvdoCdma1xCellNum = cellSetStat.iDEvdoCdma1xCellNum;
                    csStat.iDGsmCellNum = cellSetStat.iDGsmCellNum;
                    csStat.iDLtGsmCellNum = cellSetStat.iDLtGsmCellNum;
                    csStat.iDCdCellNum = cellSetStat.iDCdCellNum;

                    allCellSetStatDic.Add(statKey.strCity, csStat);
                }
            }

            foreach (string strCity in allCellSetStatDic.Keys)
            {
                CellSetStat cellSetStat = allCellSetStatDic[strCity];
                cellSetStat.strCity = strCity;
                cellSetStat.strGridType = "汇总";
                cellSetStat.strGrid = "汇总";
                cellSetStatList.Add(cellSetStat);
            }
        }
        /// <summary>
        /// 判断去重
        /// </summary>
        private bool isTheSame(CellSetDetailInfo cellSetInfo)
        {
            bool isSame = false;
            CellSetDetailInfo cellTem = new CellSetDetailInfo();
            cellTem.strCity = cellSetInfo.strCity;
            cellTem.strCarrier = cellSetInfo.strCarrier;
            cellTem.strGridType = cellSetInfo.strGridType;
            cellTem.strGrid = cellSetInfo.strGrid;
            cellTem.strCellName = cellSetInfo.strCellName;
            cellTem.strBtsName = cellSetInfo.strBtsName;
            cellTem.strBtsType = cellSetInfo.strBtsType;

            if (cellSetSeverNullInfoDic.ContainsKey(cellTem))
                isSame = true;
            else
                cellSetSeverNullInfoDic.Add(cellTem, cellTem);
            return isSame;
        }
        /// <summary>
        /// 查询准备条件
        /// </summary>
        protected override void query()
        {
            ZTCellSetBriefSetForm cellTypeForm = new ZTCellSetBriefSetForm();
            if (cellTypeForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool maiCell = false, nearCell = false, containNotGrid = false;
            cellTypeForm.getSelect(ref maiCell, ref nearCell, ref containNotGrid);
            isMaiCell = maiCell;
            isNearCell = nearCell;
            isContainNotGrid = containNotGrid;

            cellkeyDic = new Dictionary<CellKey, List<LongLat>>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            InitRegionMop2();
            initDataMap();

            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy;
            iCurCity = 0;
            iCityNum = condition.DistrictIDs.Count;
            if (iCityNum == 0)
                iCityNum = 1;
            fCityPart = 90 / ((float)iCityNum);
            WaitBox.ProgressPercent = 0;

            curDistricID = MainModel.DistrictID;
            foreach (int DistrictID in condition.DistrictIDs)
            {
                iCurCity++;
                strCityName = DistrictManager.GetInstance().getDistrictName(DistrictID);
                clientProxy = new ClientProxy();
                MainModel.DistrictID = DistrictID;
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    curDistricID = -1;
                    return;
                }
                WaitBox.CanCancel = true;
                WaitBox.Show("正在获取小区...", queryInThread);
            }
            MainModel.DistrictID = curDistricID;
            MergeCellInfo();
            ShowFormAfterQueryNew();
        }
        /// <summary>
        /// 数据初始化
        /// </summary>
        private void initDataMap()
        {
            cellStaterMap.Clear();
            tdcellStaterMap.Clear();
            tdGsmcellStaterMap.Clear();
            wcellStaterMap.Clear();
            wGsmcellStaterMap.Clear();
            cdcellStaterMap.Clear();
            evdocellStaterMap.Clear();
            evdocdma1xcellStaterMap.Clear();
            cd1xcellStaterMap.Clear();

            cellSetDetailInfoList.Clear();
            cellSetSummaryInfoList.Clear();
            cellSetStatList.Clear();

            cellSetSeverNullInfoDic.Clear();
        }
        /// <summary>
        /// 查询数据过程
        /// </summary>
        private void queryInThread()
        {
            try
            {
                string projIdStr = "";
                string servIdStr = "";
                string carrierTypeStr = "";
                string areaTypeIdStr = "";
                string agentIdStr = "";
                cellkeyDic = new Dictionary<CellKey, List<LongLat>>();
                getConditionStr(ref projIdStr, ref servIdStr, ref carrierTypeStr, ref areaTypeIdStr, ref agentIdStr);
                DateTime dBeginTime = condition.Periods[0].BeginTime;
                DateTime dEndTime = condition.Periods[0].EndTime;
                int beginTime = (int)(JavaDate.GetMilliseconds(condition.Periods[0].BeginTime) / 1000);
                int endTime = (int)(JavaDate.GetMilliseconds(condition.Periods[0].EndTime) / 1000);
                List<DateTime> dTimeList = new List<DateTime>();
                while (dBeginTime <= dEndTime)
                {
                    DateTime dTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-01 00:00:00", dBeginTime));
                    if (!dTimeList.Contains(dTime))
                        dTimeList.Add(dTime);
                    dBeginTime = dBeginTime.AddDays(1);
                }

                int iCurRound = 0;
                int iRoundNum = dTimeList.Count;
                float fRoundPart = fCityPart / ((float)iRoundNum);
                DiySqlQueryTbLogFile queryTbLogFile = new DiySqlQueryTbLogFile(MainModel, projIdStr, servIdStr, carrierTypeStr, areaTypeIdStr, agentIdStr);
                DiySqlQueryCellDatabase queryCell = new DiySqlQueryCellDatabase(MainModel, projIdStr, servIdStr, carrierTypeStr, areaTypeIdStr, agentIdStr);
                foreach (DateTime dTime in dTimeList)
                {
                    iCurRound++;
                    DiySqlQueryCellDatabase.dayDBList.Clear();
                    string logFileName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", dTime);
                    queryTbLogFile.FillData(logFileName, beginTime, endTime);
                    queryTbLogFile.Query();
                    int iTableNum = queryTbLogFile.sampletbnameLst.Count;
                    if (iTableNum == 0)
                        iTableNum = 1;
         
                    queryCell.FillData(Condition.FileName, logFileName, cellkeyDic, queryTbLogFile.sampletbnameLst, dEndTime);
                    queryCellInfo(iCurRound, iRoundNum, fRoundPart, queryTbLogFile, iTableNum, queryCell);
                }
                WaitBox.Text = "正在获取小区名称...";
                GetCells();
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryCellInfo(int iCurRound, int iRoundNum, float fRoundPart, DiySqlQueryTbLogFile queryTbLogFile, int iTableNum, DiySqlQueryCellDatabase queryCell)
        {
            string tbsample = "";
            for (int i = -1; i < queryTbLogFile.sampletbnameLst.Count; i++)
            {
                bool hasGet = true;
                if (isMaiCell)
                {
                    hasGet = getTbSample(queryTbLogFile, ref tbsample, i);
                    if (hasGet)
                    {
                        queryCell.FillData(tbsample, "sp_auto_stat_cellinfo_all");
                        queryCell.SetQueryCondition(Condition);
                        queryCell.Query();
                    }
                }
                //邻小区信息查询
                if (isNearCell)
                {
                    hasGet = getTbSample(queryTbLogFile, ref tbsample, i);
                    if (hasGet)
                    {
                        queryCell.FillData(tbsample, "sp_auto_stat_nbcellinfo");
                        queryCell.SetQueryCondition(Condition);
                        queryCell.Query();
                    }
                }
                if (hasGet)
                {
                    WaitBox.Text = string.Format("当前城市{2},{3}/{4};月份:{0}/{1},数据表：{5}/{6}...", iCurRound, iRoundNum, strCityName, iCurCity, iCityNum, i + 1, iTableNum);
                    WaitBox.ProgressPercent = (int)((iCurCity - 1) * fCityPart + (iCurRound - 1) * fRoundPart);
                }
            }
        }

        private bool getTbSample(DiySqlQueryTbLogFile queryTbLogFile, ref string tbsample, int i)
        {
            if (i != -1)
            {
                if (!DiySqlQueryCellDatabase.dayDBList.Contains(queryTbLogFile.sampletbnameLst[i]))
                {
                    tbsample = queryTbLogFile.sampletbnameLst[i];
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 查询完毕后显示小区集
        /// </summary>
        private void ShowFormAfterQueryNew()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(DIYCellSetBriefDataForm).FullName);
            DIYCellSetBriefDataForm cellSetBriefForm = obj == null ? null : obj as DIYCellSetBriefDataForm;
            if (cellSetBriefForm == null || cellSetBriefForm.IsDisposed)
            {
                cellSetBriefForm = new DIYCellSetBriefDataForm(MainModel);
            }
            cellSetBriefForm.FillData(cellSetDetailInfoList, cellSetSummaryInfoList, cellSetStatList);
            if (!cellSetBriefForm.Visible)
            {
                cellSetBriefForm.Show(MainModel.MainForm);
            }
        }

        #region getCondition
        /// <summary>
        /// 构造SQL的查询条件
        /// </summary>
        private void getConditionStr(ref string projIdStr, ref string servIdStr, ref string carrierTypeStr, ref string areaTypeIdStr, ref string agentIdStr)
        {
            projIdStr = getProjIdStr(projIdStr);

            servIdStr = getServIdStr(servIdStr);

            carrierTypeStr = getCarrierTypeStr(carrierTypeStr);

            areaTypeIdStr = getAreaTypeIdStr(areaTypeIdStr);

            agentIdStr = getAgentIdStr(agentIdStr);
        }

        private string getProjIdStr(string projIdStr)
        {
            for (int i = 0; i < Condition.Projects.Count; i++)
            {
                if (i == 0)
                    projIdStr = Condition.Projects[i].ToString();
                else
                {
                    projIdStr = string.Format(projIdStr + "," + Condition.Projects[i]);
                }
            }
            projIdStr = "(" + projIdStr + ")";
            return projIdStr;
        }

        private string getServIdStr(string servIdStr)
        {
            for (int i = 0; i < Condition.ServiceTypes.Count; i++)
            {
                if (i == 0)
                {
                    servIdStr = Condition.ServiceTypes[i].ToString();
                }
                else
                {
                    servIdStr = string.Format(servIdStr + "," + Condition.ServiceTypes[i]);
                }
            }
            servIdStr = "(" + servIdStr + ")";
            return servIdStr;
        }

        private string getCarrierTypeStr(string carrierTypeStr)
        {
            for (int i = 0; i < Condition.CarrierTypes.Count; i++)
            {
                if (i == 0)
                {
                    carrierTypeStr = Condition.CarrierTypes[i].ToString();
                }
                else
                {
                    carrierTypeStr = string.Format(carrierTypeStr + "," + Condition.CarrierTypes[i]);
                }
            }
            carrierTypeStr = "(" + carrierTypeStr + ")";
            return carrierTypeStr;
        }

        private string getAreaTypeIdStr(string areaTypeIdStr)
        {
            int iKey = 0;
            foreach (int id in Condition.Areas.Keys)
            {
                for (int j = 0; j < Condition.Areas[id].Count; j++)
                {
                    if (iKey == 0)
                    {
                        areaTypeIdStr = Condition.Areas[id][0].ToString();
                        iKey++;
                    }
                    else
                    {
                        areaTypeIdStr = string.Format(areaTypeIdStr + "," + Condition.Areas[id][j]);
                    }
                }
            }
            if (areaTypeIdStr != "")
                areaTypeIdStr = "(" + areaTypeIdStr + ")";
            return areaTypeIdStr;
        }

        private string getAgentIdStr(string agentIdStr)
        {
            for (int i = 0; i < Condition.AgentIds.Count; i++)
            {
                if (i == 0)
                {
                    agentIdStr = Condition.AgentIds[i].ToString();
                }
                else
                {
                    agentIdStr = string.Format(agentIdStr + "," + Condition.AgentIds[i]);
                }
            }
            agentIdStr = "(" + agentIdStr + ")";
            return agentIdStr;
        }
        #endregion

        protected override bool isValidCondition()
        {
            return true;
        }
        #region 处理及组织各类小区集信息
        /// <summary>
        /// 通过LAC\CI\时间获取小区
        /// </summary>
        private void GetCells()
        {
            foreach (CellKey key in cellkeyDic.Keys)
            {
                List<LongLat> tmpList = cellkeyDic[key];
                if (key.iServicetype == 1 || key.iServicetype == 2 || key.iServicetype == 3 || key.iServicetype == 22)  //GSM
                {
                    doWithGSMData(key, tmpList[0]);
                }
                else if (key.iServicetype == 4 || key.iServicetype == 5 || key.iServicetype == 13 || key.iServicetype == 17
                    || key.iServicetype == 18 || key.iServicetype == 27)//TD
                {
                    doWithTDData(key, tmpList[0]);
                }
                else if (key.iServicetype == 10 || key.iServicetype == 11 || key.iServicetype == 14 || key.iServicetype == 15
                    || key.iServicetype == 25 || key.iServicetype == 28) //WCDMA
                {
                    doWithWCDMAData(key, tmpList[0]);
                }
                else if (key.iServicetype == 6 || key.iServicetype == 7 || key.iServicetype == 26)//CDMA
                {
                    doWithCDMAData(key, tmpList[0]);
                }
                else if (key.iServicetype == 8 || key.iServicetype == 9 || key.iServicetype == 40)//EVDO
                {
                    doWithEvdoData(key, tmpList[0]);
                }
            }
        }
        /// <summary>
        /// 定位所在网格
        /// </summary>
        private void isContainPoint(double x, double y, ref Dictionary<string, string> gridTypeGrid)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y) && !gridTypeGrid.ContainsKey(gridType))
                    {
                        gridTypeGrid.Add(gridType, grid);
                        break;
                    }
                }
            }
            if (isContainNotGrid && gridTypeGrid.Count == 0)
            {
                gridTypeGrid.Add("无网格类型", "无网格名称");
            }
        }
        /// <summary>
        /// 获取业务类型
        /// </summary>
        private string getServiceType(int iServiceType)
        {
            if (iServiceType == 1 || iServiceType == 4 || iServiceType == 10 || iServiceType == 6 || iServiceType == 8)
            {
                return "语音";
            }
            else if (iServiceType == 22 || iServiceType == 17 || iServiceType == 25 || iServiceType == 26 || iServiceType == 40)
            {
                return "空闲";
            }
            else
            {
                return "数据";
            }
        }
        /// <summary>
        /// 按LAC\CI构造小区及基站
        /// </summary>
        private void getCellByLacCi(CellKey cKey, ref string strLacCi, ref string strBtsKey)
        {
            strLacCi = string.Format("{0}_{1}", cKey.iLac, cKey.iCi);
            string strSubCi = cKey.iCi.ToString().Length > 1 ? cKey.iCi.ToString().Substring(0, cKey.iCi.ToString().Length - 1) : cKey.iCi.ToString();
            strBtsKey = string.Format("{0}_{1}", cKey.iLac, strSubCi);
        }
        /// <summary>
        /// 处理GSM小区信息
        /// </summary>
        private void doWithGSMData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            Cell cell = CellManager.GetInstance().GetCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
            if (cKey.iCarriertype == 2)
                cell = null;
            foreach (string gridType in gridTypeGrid.Keys)
            {
                addGSMCell(cell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
            }
        }
        /// <summary>
        /// 分别组织移动、联通GSM
        /// </summary>
        private void addGSMCell(Cell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "GSM";
            stater.strCarrier = cKey.iCarriertype == 1 ? "移动" : "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == BTSType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!cellStaterMap.ContainsKey(stater))
            {
                cellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理TD小区信息
        /// </summary>
        private void doWithTDData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.inet == 2)
            {
                Cell cell = CellManager.GetInstance().GetCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addTDGSMCell(cell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
            else
            {
                TDCell tdcell = null;
                if (cKey.iRncID == 0)
                    tdcell = CellManager.GetInstance().GetTDCell(cKey.dTime, cKey.iLac, cKey.iCi);
                else
                    tdcell = CellManager.GetInstance().GetNearestCurrentTDCell((int?)cKey.iRncID, (int?)cKey.iCi, longLat.fLongitude, longLat.fLatitude);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addTDCell(tdcell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
        }
        /// <summary>
        /// 组织移动TD
        /// </summary>
        private void addTDCell(TDCell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "TD";
            stater.strCarrier = "移动";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == TDNodeBType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!tdcellStaterMap.ContainsKey(stater))
            {
                tdcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动TD-GSM
        /// </summary>
        private void addTDGSMCell(Cell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "TD-GSM";
            stater.strCarrier = "移动";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == BTSType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!tdGsmcellStaterMap.ContainsKey(stater))
            {
                tdGsmcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理WCDMA小区信息
        /// </summary>
        private void doWithWCDMAData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.inet == 2)
            {
                Cell cell = CellManager.GetInstance().GetCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addWCDMAGSMCell(cell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
            else
            {
                WCell wcell = CellManager.GetInstance().GetWCell(cKey.dTime, (ushort)cKey.iLac, (ushort)cKey.iCi);
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addWCDMACell(wcell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
        }
        /// <summary>
        /// 组织移动WCDMA
        /// </summary>
        private void addWCDMACell(WCell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "WCDMA";
            stater.strCarrier = "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == WNodeBType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongNodeBs[0].Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!wcellStaterMap.ContainsKey(stater))
            {
                wcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动WCDMA-GSM
        /// </summary>
        private void addWCDMAGSMCell(Cell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "WCDMA-GSM";
            stater.strCarrier = "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = cell.Type == BTSType.Indoor ? "室内" : "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }

            if (!wGsmcellStaterMap.ContainsKey(stater))
            {
                wGsmcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理CDMA小区信息
        /// </summary>
        private void doWithCDMAData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.iServicetype == 7)
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addCDMA1xCell(cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
            else
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addCDMACell(cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
        }
        /// <summary>
        /// 组织移动CDMA
        /// </summary>
        private void addCDMACell(CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "CDMA";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!cdcellStaterMap.ContainsKey(stater))
            {
                cdcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动CDMA1X
        /// </summary>
        private void addCDMA1xCell(CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "CDMA1x";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!cd1xcellStaterMap.ContainsKey(stater))
            {
                cd1xcellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理CDMA小区信息
        /// </summary>
        private void doWithEvdoData(CellKey cKey, LongLat longLat)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }

            if (cKey.inet == 3)
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addEvdoCell(cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
            else
            {
                foreach (string gridType in gridTypeGrid.Keys)
                {
                    addEvdoCdma1XCell(cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
                }
            }
        }
        /// <summary>
        /// 组织移动CDMA
        /// </summary>
        private void addEvdoCell(CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "EVDO";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!evdocellStaterMap.ContainsKey(stater))
            {
                evdocellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 组织移动CDMA
        /// </summary>
        private void addEvdoCdma1XCell(CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = "EVDO-CDMA1x";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype);
            stater.sampelSum = sampleNum;

            string strLacCi = "";
            string strBtsKey = "";
            getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
            stater.strCellName = strLacCi;
            stater.strBtsName = strBtsKey;

            if (!evdocdma1xcellStaterMap.ContainsKey(stater))
            {
                evdocdma1xcellStaterMap.Add(stater, stater);
            }
        }
        #endregion
    }

    public class DiySqlQueryTbLogFile : DIYSQLBase
    {
        string logfilename { get; set; }
        int beginTime{ get; set; }
        int endTime{ get; set; }
        string projIdStr{ get; set; }
        string servIdStr{ get; set; }
        string carrierTypeStr{ get; set; }
        string areaTypeIdStr{ get; set; }
        string agentIdStr{ get; set; }
        public List<string> sampletbnameLst{ get; set; }
        public DiySqlQueryTbLogFile(MainModel mainModel, string projIdStr, string servIdStr, string carrierTypeStr, string areaTypeIdStr, string agentIdStr)
            : base(mainModel)
        {
            this.projIdStr = projIdStr;
            this.servIdStr = servIdStr;
            this.carrierTypeStr = carrierTypeStr;
            this.areaTypeIdStr = areaTypeIdStr;
            this.agentIdStr = agentIdStr;
        }

        public void FillData(string logfilename, int beginTime, int endTime)
        {
            this.logfilename = logfilename;
            this.beginTime = beginTime;
            this.endTime = endTime;
        }

        public override string Name
        {
            get { return "查询log文件"; }
        }

        protected override string getSqlTextString()
        {
            string sql;
            if (areaTypeIdStr == "" && agentIdStr == "")
            {
                sql = "SELECT distinct strsampletbname FROM " + logfilename + " WHERE istime>" + beginTime + " AND ietime<" + endTime + " AND iprojecttype in" + projIdStr + " AND iservicetype in" + servIdStr + " AND icarriertype in" + carrierTypeStr;
            }
            else if (areaTypeIdStr == "")
            {
                sql = "SELECT distinct strsampletbname FROM " + logfilename + " WHERE istime>" + beginTime + " AND ietime<" + endTime + " AND iprojecttype in" + projIdStr + " AND iservicetype in" + servIdStr + " AND icarriertype in" + carrierTypeStr
                + " And iagentid in" + agentIdStr;
            }
            else if (agentIdStr == "")
            {
                sql = "SELECT distinct strsampletbname FROM " + logfilename + " WHERE istime>" + beginTime + " AND ietime<" + endTime + " AND iprojecttype in" + projIdStr + " AND iservicetype in" + servIdStr + " AND icarriertype in" + carrierTypeStr
                + " AND iareatype in" + areaTypeIdStr;
            }
            else
            {
                sql = "SELECT distinct strsampletbname FROM " + logfilename + " WHERE istime>" + beginTime + " AND ietime<" + endTime + " AND iprojecttype in" + projIdStr + " AND iservicetype in" + servIdStr + " AND icarriertype in" + carrierTypeStr
                   + " AND iareatype in" + areaTypeIdStr + " And iagentid in" + agentIdStr;
            }

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            sampletbnameLst = new List<string>();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string filename = package.Content.GetParamString();
                    sampletbnameLst.Add(filename);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlQueryCellDatabase : DIYSQLBase
    {
        string strCity { get; set; }
        string strFileName { get; set; }
        string sampletbname { get; set; }
        string logfilename { get; set; }
        string projIdStr{ get; set; }
        string servIdStr{ get; set; }
        string carrierTypeStr{ get; set; }
        string areaTypeIdStr{ get; set; }
        string agentIdStr{ get; set; }
        DateTime dTime{ get; set; }
        string strProcName { get; set; }
        public Dictionary<CellKey, List<LongLat>> cellkeyDic { get; set; }
        public List<string> sampletbnameLst { get; set; }
        public DiySqlQueryCellDatabase(MainModel mainModel, string projIdStr, string servIdStr, string carrierTypeStr, string areaTypeIdStr, string agentIdStr)
            : base(mainModel)
        {
            strCity = DistrictManager.GetInstance().getDistrictName(mainModel.DistrictID);
            this.projIdStr = projIdStr;
            this.servIdStr = servIdStr;
            this.carrierTypeStr = carrierTypeStr;
            this.areaTypeIdStr = areaTypeIdStr;
            this.agentIdStr = agentIdStr;
        }

        public void FillData(string strFileName, string logfilename , Dictionary<CellKey, List<LongLat>> cellkeyDic, List<string> sampletbnameLst,DateTime dTime)
        {
            this.cellkeyDic = cellkeyDic;
            this.logfilename = logfilename;
            this.sampletbnameLst = sampletbnameLst;
            this.dTime = dTime;
            if (strFileName != null && strFileName != "")
            {
                this.strFileName = " and strfilename like ''%" + strFileName + "%'' ";
            }
        }

        public void FillData(string sampletbname, string strProcName)
        {
            this.sampletbname = sampletbname;
            this.strProcName = strProcName;
        }

        public override string Name
        {
            get { return "查询小区lacci"; }
        }

        DbRect rect { get; set; }
        public void setLonLatRange(ResvRegion region)
        {
            //rect.x1<rect.x2 , rect.y1<rect.y2 
            if (region == null)
                rect = MapOperation.GetShapeBounds(condition.Geometorys.Region);
            else
                rect = MapOperation.GetShapeBounds(region.Shape);
        }

        /// <summary>
        /// 构造SQL
        /// </summary>
        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            if (areaTypeIdStr != "")
                sb.Append(" And iareatype in " + areaTypeIdStr);
            if (agentIdStr != "")
                sb.Append(" And iagentid in " + agentIdStr);
            if (projIdStr != "")
                sb.Append(" And iprojecttype in " + projIdStr);
            if (servIdStr != "")
                sb.Append(" And iservicetype in " + servIdStr);
            if (carrierTypeStr != "")
                sb.Append(" And icarriertype in " + carrierTypeStr);
            sb.Append(strFileName);

            string sql = string.Format("exec {5} '{0}','{3}','{4}','{1}','{2}'", logfilename, sampletbname, sb.ToString(), projIdStr, servIdStr, strProcName);
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int iCount = 14;
            if (!this.strProcName.Contains("_all"))
                iCount = 11;
            E_VType[] rType = new E_VType[iCount];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            if (this.strProcName.Contains("_all"))
            {
                rType[11] = E_VType.E_Int;
                rType[12] = E_VType.E_Int;
                rType[13] = E_VType.E_Int;
            }
            return rType;
        }
        public static List<string> dayDBList { get; set; } = new List<string>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        fillData(package);
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            CellKey cKey = new CellKey();
            LongLat ll = new LongLat();
            cKey.strCity = strCity;
            string strDb = package.Content.GetParamString();
            if (sampletbnameLst.Contains(strDb))
            {
                if (!dayDBList.Contains(strDb))
                    dayDBList.Add(strDb);
                cKey.iLac = package.Content.GetParamInt();
                cKey.iCi = package.Content.GetParamInt();
                cKey.iServicetype = package.Content.GetParamInt();
                cKey.iCarriertype = package.Content.GetParamInt();
                ll.fLongitude = ((float)package.Content.GetParamInt()) / 10000000;
                ll.fLatitude = ((float)package.Content.GetParamInt()) / 10000000;
                ll.fMaxLongitude = ((float)package.Content.GetParamInt()) / 10000000;
                ll.fMaxLatitude = ((float)package.Content.GetParamInt()) / 10000000;
                cKey.inet = package.Content.GetParamInt();
                cKey.dTime = dTime;
                ll.iSampleNum = package.Content.GetParamInt();
                if (this.strProcName.Contains("_all"))
                {
                    cKey.iFcn = package.Content.GetParamInt();
                    cKey.iMnc = package.Content.GetParamInt();
                    cKey.iRncID = package.Content.GetParamInt();
                }
                if (cellkeyDic.ContainsKey(cKey))
                {
                    List<LongLat> llList = cellkeyDic[cKey];
                    llList[0].iSampleNum += ll.iSampleNum;
                    llList.Add(ll);
                    cellkeyDic[cKey] = llList;
                }
                else
                {
                    List<LongLat> llList = new List<LongLat>();
                    llList.Add(ll);
                    cellkeyDic.Add(cKey, llList);
                }
            }
        }
    }

    public class CellSetDetailInfo
    {
        public string strCity{ get; set; }//城市
        public string strCarrier{ get; set; }//运营商
        public string strCellType{ get; set; }//小区类型
        public string strGridType{ get; set; }//图层类型
        public string strGrid{ get; set; }//网格
        public string strServiceType{ get; set; }//业务类型

        public string strCellName{ get; set; }//小区名称
        public string strBtsName{ get; set; } //基站名称
        public int iLac{ get; set; }
        public int iCi{ get; set; }
        public string strBtsType{ get; set; }//是否室内小区
        public int sampelSum{ get; set; }

        public CellSetDetailInfo()
        {
            strCity = "";
            strCarrier = "";
            strCellType = "";
            strGridType = "";
            strGrid = "";
            strServiceType = "";

            strCellName = "";
            iLac = 0;
            iCi = 0;
            strBtsType = "室外";
            sampelSum = 0;
        }

        public override bool Equals(object obj)
        {
            CellSetDetailInfo other = obj as CellSetDetailInfo;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strCity.Equals(other.strCity)
                    && this.strCarrier.Equals(other.strCarrier)
                    && this.strCellType.Equals(other.strCellType)
                    && this.strGridType.Equals(other.strGridType)
                    && this.strGrid.Equals(other.strGrid)
                    && this.strServiceType.Equals(other.strServiceType)
                    && this.strCellName.Equals(other.strCellName));
        }

        public override int GetHashCode()
        {
            return (this.strCity +
                   this.strCarrier +
                   this.strCellType +
                   this.strGridType +
                   this.strGrid +
                   this.strServiceType +
                   this.strCellName).GetHashCode();
        }

        //实现排序的接口
        public static IComparer<CellSetDetailInfo> GetCompareByGridType()
        {
            if (comparerByGridType == null)
            {
                comparerByGridType = new CompareByGridType();
            }
            return comparerByGridType;
        }
        public class CompareByGridType : IComparer<CellSetDetailInfo>
        {
            public int Compare(CellSetDetailInfo x, CellSetDetailInfo y)
            {
                return x.strGridType.CompareTo(y.strGridType);
            }
        }
        private static IComparer<CellSetDetailInfo> comparerByGridType;
    }

    public class CellSetSummaryInfo
    {
        public string strCity { get; set; }//城市
        public string strCarrier { get; set; }//运营商
        public string strCellType { get; set; }//小区类型
        public string strGridType { get; set; }//图层类型
        public string strGrid { get; set; }//网格
        public string strServiceType { get; set; }//业务类型
        
        public int IBtsCount//基站数目
        {
            get { return btsList.Count; }
        }
        public int iCellCount { get; set; }//小区数目
        public int IIndoorCellCount//室内小区数目
        {
            get { return indoorCellList.Count; }
        }

        public List<string> btsList { get; set; } = new List<string>();
        public List<string> indoorCellList { get; set; } = new List<string>();

        public CellSetSummaryInfo()
        {
            strCity = "";
            strCarrier = "";
            strCellType = "";
            strGridType = "";
            strGrid = "";
            strServiceType = "";
            
            iCellCount = 0;
        }

        public override bool Equals(object obj)
        {
            CellSetSummaryInfo other = obj as CellSetSummaryInfo;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strCity.Equals(other.strCity)
                    && this.strCarrier.Equals(other.strCarrier)
                    && this.strCellType.Equals(other.strCellType)
                    && this.strGridType.Equals(other.strGridType)
                    && this.strGrid.Equals(other.strGrid)
                    && this.strServiceType.Equals(other.strServiceType));
        }

        public override int GetHashCode()
        {
            return strGrid.GetHashCode();
        }
    }

    public class CellSetStat
    {
        public string strCity { get; set; }
        public string strGridType { get; set; }
        public string strGrid { get; set; }

        public int iAllTdCellNum { get; set; }
        public int iAllYDGsmCellNum { get; set; }
        public int iAllWCellNum { get; set; }
        public int iAllLTGsmCellNum { get; set; }
        public int iAllEvodCellNum { get; set; }
        public int iAllCdmaCellNum { get; set; }
        public int iAllLteCellNum { get; set; }
        public int iAllLteLTCellNum { get; set; }
        public int iAllLteDXCellNum { get; set; }

        public int iFTdCellNum { get; set; }
        public int iFWCellNum { get; set; }
        public int iFEvodoCellNum { get; set; }
        public int iFYDGsmCellNum { get; set; }
        public int iFLTGsmCellNum { get; set; }
        public int iFDXCdmaCellNum { get; set; }
        public int iFLteCellNum { get; set; }
        public int iFLTLteCellNum { get; set; }
        public int iFDXLteCellNum { get; set; }

        public int iVTdCellNum { get; set; }
        public int iVTdGsmCellNum { get; set; }
        public int iVWCellNum { get; set; }
        public int iVWGsmCellNum { get; set; }
        public int iVCd2000CellNum { get; set; }
        public int iVGsmCellNum { get; set; }
        public int iVLtGsmCellNum { get; set; }
        public int iVCdCellNum { get; set; }
        public int iVLteCellNum { get; set; }
        public int iVLTLteCellNum { get; set; }
        public int iVDXLteCellNum { get; set; }

        public int iDTdCellNum { get; set; }
        public int iDTdGsmCellNum { get; set; }
        public int iDWCellNum { get; set; }
        public int iDWGsmCellNum { get; set; }
        public int iDEvdoCellNum { get; set; }
        public int iDEvdoCdma1xCellNum { get; set; }
        public int iDGsmCellNum { get; set; }
        public int iDLtGsmCellNum { get; set; }
        public int iDCdCellNum { get; set; }
        public int iDLteCellNum { get; set; }
        public int iDLTLteCellNum { get; set; }
        public int iDDXLteCellNum { get; set; }

        public int iBLteCellNum { get; set; }
        public int iBLTLteCellNum { get; set; }
        public int iBDXLteCellNum { get; set; }

        public int iVoLteCellNum { get; set; }
        public int iVoLTLteCellNum { get; set; }
        public int iVoDXLteCellNum { get; set; }

        public CellSetStat()
        {
            strCity = "";
            strGridType = "";
            strGrid = "";

            iAllTdCellNum = 0;
            iAllYDGsmCellNum = 0;
            iAllWCellNum = 0;
            iAllLTGsmCellNum = 0;
            iAllEvodCellNum = 0;
            iAllCdmaCellNum = 0;
            iAllLteCellNum = 0;
            iAllLteLTCellNum = 0;
            iAllLteDXCellNum = 0;

            iFTdCellNum = 0;
            iFWCellNum = 0;
            iFEvodoCellNum = 0;
            iFYDGsmCellNum = 0;
            iFLTGsmCellNum = 0;
            iFDXCdmaCellNum = 0;
            iFLteCellNum = 0;
            iFLTLteCellNum = 0;
            iFDXLteCellNum = 0;

            iVTdCellNum = 0;
            iVTdGsmCellNum = 0;
            iVWCellNum = 0;
            iVWGsmCellNum = 0;
            iVCd2000CellNum = 0;
            iVGsmCellNum = 0;
            iVLtGsmCellNum = 0;
            iVCdCellNum = 0;
            iVLteCellNum = 0;
            iVLTLteCellNum = 0;
            iVDXLteCellNum = 0;

            iDTdCellNum = 0;
            iDTdGsmCellNum = 0;
            iDWCellNum = 0;
            iDWGsmCellNum = 0;
            iDEvdoCellNum = 0;
            iDEvdoCdma1xCellNum = 0;
            iDGsmCellNum = 0;
            iDLtGsmCellNum = 0;
            iDCdCellNum = 0;
            iDLteCellNum = 0;
            iDLTLteCellNum = 0;
            iDDXLteCellNum = 0;

            iBLteCellNum = 0;
            iBLTLteCellNum = 0;
            iBDXLteCellNum = 0;

            iVoLteCellNum = 0;
            iVoLTLteCellNum = 0;
            iVoDXLteCellNum = 0;
        }
    }

    public class StatKey
    {
        public string strCity { get; set; }
        public string strGridType { get; set; }
        public string strGrid { get; set; }

        public StatKey()
        {
            strCity = "";
            strGridType = "";
            strGrid = "";
        }

        public override bool Equals(object obj)
        {
            StatKey other = obj as StatKey;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strCity.Equals(other.strCity)
                && this.strGridType.Equals(other.strGridType)
                && this.strGrid.Equals(other.strGrid));
        }

        public override int GetHashCode()
        {
            return (this.strCity +
                this.strGridType +
                this.strGrid).GetHashCode();
        }
    }

    public class CellKey
    {
        public string strCity { get; set; }
        public int iLac { get; set; }
        public int iCi { get; set; }
        public int iServicetype { get; set; }
        public int iCarriertype { get; set; }
        public DateTime dTime { get; set; }
        public int inet { get; set; }
        public int iFcn { get; set; }
        public int iRncID { get; set; }
        public int iMnc { get; set; }

        public CellKey()
        {
            strCity = "";
            iLac = 0;
            iCi = 0;
            iServicetype = 0;
            iCarriertype = 0;
            dTime = DateTime.Now;
            inet = 0;
            iFcn = 0;
            iRncID = 0;
            iMnc = 0;
        }

        public override bool Equals(object obj)
        {
            CellKey other = obj as CellKey;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strCity.Equals(other.strCity)
                    && this.inet.Equals(other.inet)
                    && this.iLac.Equals(other.iLac)
                    && this.iCi.Equals(other.iCi)
                    && this.iServicetype.Equals(other.iServicetype)
                    && this.iCarriertype.Equals(other.iCarriertype)
                    && this.iFcn.Equals(other.iFcn)
                    && this.iRncID.Equals(other.iRncID)
                    && this.iMnc.Equals(other.iMnc));
        }

        public override int GetHashCode()
        {
            return this.iCi.GetHashCode();
        }
    }

    public class LongLat
    {
        public float fLongitude { get; set; }
        public float fLatitude { get; set; }

        public float fMaxLongitude { get; set; }
        public float fMaxLatitude { get; set; }

        public int iSampleNum { get; set; }

        public LongLat()
        {
            fLongitude = 0;
            fLatitude = 0;
            fMaxLongitude = 0;
            fMaxLatitude = 0;
            iSampleNum = 0;
        }
    }
}
