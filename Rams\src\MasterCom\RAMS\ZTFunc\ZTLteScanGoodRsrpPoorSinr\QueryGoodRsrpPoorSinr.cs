﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLteScanGoodRsrpPoorSinr;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryGoodRsrpPoorSinr : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        private static QueryGoodRsrpPoorSinr intance = null;
        protected static readonly object lockObj = new object();
        public static QueryGoodRsrpPoorSinr GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new QueryGoodRsrpPoorSinr();
                    }
                }
            }
            return intance;
        }

        protected QueryGoodRsrpPoorSinr()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "强信号质差_LTEScan"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23013, this.Name);
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDic = new Dictionary<LTECell, GoodRsrpPoorSinrCell>();
        }

        private Dictionary<LTECell, GoodRsrpPoorSinrCell> cellDic = null;
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                float mainRsrp = float.NaN;
                for (int i = 0; i < 10; i++)
                {
                    LTECell cell = tp.GetCell_LTEScan(i);
                    cell = setCellName(tp, i, cell);
                    float rsrp;
                    float sinr;
                    bool isValid = getValidData(tp, out rsrp, out sinr);
                    if (!isValid)
                    {
                        break;
                    }

                    if (float.IsNaN(mainRsrp))
                    {
                        mainRsrp = rsrp;
                    }
                    isValid = addValidData(tp, mainRsrp, i, cell, rsrp, sinr);
                    if (!isValid)
                    {
                        break;
                    }
                }
            }
            return false;
        }

        private bool addValidData(TestPoint tp, float mainRsrp, int i, LTECell cell, float rsrp, float sinr)
        {
            if (rsrp >= funcCond.Rsrp && sinr <= funcCond.Sinr)
            {
                if (funcCond.CheckBand)
                {
                    float band = mainRsrp - rsrp;
                    if (band > funcCond.CoverBand)
                    {
                        //超过设定覆盖带，跳出
                        return false;
                    }
                }
                GoodRsrpPoorSinrCell cellItem;
                if (!cellDic.TryGetValue(cell, out cellItem))
                {
                    cellItem = new GoodRsrpPoorSinrCell(cell);
                    cellDic.Add(cell, cellItem);
                }
                cellItem.AddPoint(tp, rsrp, sinr, i == 0);
            }
            return true;
        }

        private bool getValidData(TestPoint tp, out float rsrp, out float sinr)
        {
            rsrp = -9999;
            sinr = -9999;
            float? value = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            if (value == null || value < -141 || value > 25)
            {
                return false;
            }
            rsrp = (float)value;

            value = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR"];
            if (value == null || value < -50 || value > 50)
            {
                return false;
            }
            sinr = (float)value;
            return true;
        }

        private static LTECell setCellName(TestPoint tp, int i, LTECell cell)
        {
            if (cell == null)
            {
                cell = new LTECell();

                int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
                int? pci = (int?)(short?)tp["LTESCAN_TopN_PCI", i];
                if (earfcn != null && pci != null)
                {
                    cell.Name = earfcn.ToString() + "_" + pci.ToString();
                }
            }

            return cell;
        }

        FuncCondition funcCond = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            GoodRsrpPoorSinrSettingDlg_LteScan dlg = new GoodRsrpPoorSinrSettingDlg_LteScan();
            dlg.Condition = funcCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void getResultAfterQuery()
        {
            int sn = 1;
            foreach (GoodRsrpPoorSinrCell cell in cellDic.Values)
            {
                cell.Sn = sn++;
                cell.MakeSummary();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            ResultForm form = MainModel.GetObjectFromBlackboard(typeof(ResultForm)) as ResultForm;
            if (form==null||form.IsDisposed)
            {
                form = new ResultForm();
                form.Owner = MainModel.MainForm;
            }
            form.FillData(new List<GoodRsrpPoorSinrCell>(cellDic.Values));
            form.Visible = true;
            form.BringToFront();
            cellDic = null;
        }

    }
}
