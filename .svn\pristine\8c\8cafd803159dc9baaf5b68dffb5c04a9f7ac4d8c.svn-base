﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WorkloadCountAndQueryInfoForm_HN : MinCloseForm
    {
        public WorkloadCountAndQueryInfoForm_HN()
        {
            InitializeComponent();
        }
        public void SetType_HN(WorkloadType_HN type)
        {

            if (type == WorkloadType_HN.测试厂家)
            {
                this.gridColumn9.Visible = true;
            }
            else
            {
                this.gridColumn9.Visible = false;
            }
        }
        public void FillData(List<WorkloadCountAndQueryInfo> infoList)
        {
            gridControl.DataSource = infoList;
            gridControl.RefreshDataSource();
        }

        private void TSMItemToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            } 
        }
    }
}
