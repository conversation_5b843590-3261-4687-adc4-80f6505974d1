﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakRxqualityInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListViewWeakCovQual = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLtLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLtLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBrLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBrLat = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLac = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxlev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxqual = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.treeListViewIndexStru = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSn_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLtLong_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLtLat_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBrLong_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBrLat_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxlev_Index = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxquality_Index = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStripIndexStru = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExportIndexStru = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewWeakCovQual)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewIndexStru)).BeginInit();
            this.contextMenuStripIndexStru.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListViewWeakCovQual
            // 
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnSN);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnLtLong);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnLtLat);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnBrLong);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnBrLat);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnCellName);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnLac);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnCI);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnRxlev);
            this.treeListViewWeakCovQual.AllColumns.Add(this.olvColumnRxqual);
            this.treeListViewWeakCovQual.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnLtLong,
            this.olvColumnLtLat,
            this.olvColumnBrLong,
            this.olvColumnBrLat,
            this.olvColumnCellName,
            this.olvColumnLac,
            this.olvColumnCI,
            this.olvColumnRxlev,
            this.olvColumnRxqual});
            this.treeListViewWeakCovQual.ContextMenuStrip = this.contextMenuStrip1;
            this.treeListViewWeakCovQual.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewWeakCovQual.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewWeakCovQual.FullRowSelect = true;
            this.treeListViewWeakCovQual.GridLines = true;
            this.treeListViewWeakCovQual.Location = new System.Drawing.Point(3, 3);
            this.treeListViewWeakCovQual.Name = "treeListViewWeakCovQual";
            this.treeListViewWeakCovQual.OwnerDraw = true;
            this.treeListViewWeakCovQual.ShowGroups = false;
            this.treeListViewWeakCovQual.Size = new System.Drawing.Size(741, 304);
            this.treeListViewWeakCovQual.TabIndex = 2;
            this.treeListViewWeakCovQual.UseCompatibleStateImageBehavior = false;
            this.treeListViewWeakCovQual.View = System.Windows.Forms.View.Details;
            this.treeListViewWeakCovQual.VirtualMode = true;
            this.treeListViewWeakCovQual.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListViewWeakCovQual_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 75;
            // 
            // olvColumnLtLong
            // 
            this.olvColumnLtLong.HeaderFont = null;
            this.olvColumnLtLong.Text = "左上角经度";
            this.olvColumnLtLong.Width = 81;
            // 
            // olvColumnLtLat
            // 
            this.olvColumnLtLat.HeaderFont = null;
            this.olvColumnLtLat.Text = "左上角纬度";
            this.olvColumnLtLat.Width = 77;
            // 
            // olvColumnBrLong
            // 
            this.olvColumnBrLong.HeaderFont = null;
            this.olvColumnBrLong.Text = "右下角经度";
            this.olvColumnBrLong.Width = 81;
            // 
            // olvColumnBrLat
            // 
            this.olvColumnBrLat.HeaderFont = null;
            this.olvColumnBrLat.Text = "右下角纬度";
            this.olvColumnBrLat.Width = 83;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            // 
            // olvColumnLac
            // 
            this.olvColumnLac.HeaderFont = null;
            this.olvColumnLac.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnRxlev
            // 
            this.olvColumnRxlev.HeaderFont = null;
            this.olvColumnRxlev.Text = "场强";
            // 
            // olvColumnRxqual
            // 
            this.olvColumnRxqual.HeaderFont = null;
            this.olvColumnRxqual.Text = "质量";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(143, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(142, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(755, 337);
            this.tabControl1.TabIndex = 3;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.treeListViewWeakCovQual);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(747, 310);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "弱覆盖质差";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.treeListViewIndexStru);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(747, 310);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "道路结构指数质差";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // treeListViewIndexStru
            // 
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnSn_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnLtLong_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnLtLat_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnBrLong_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnBrLat_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnCellName_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnLAC_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnCI_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnRxlev_Index);
            this.treeListViewIndexStru.AllColumns.Add(this.olvColumnRxquality_Index);
            this.treeListViewIndexStru.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSn_Index,
            this.olvColumnLtLong_Index,
            this.olvColumnLtLat_Index,
            this.olvColumnBrLong_Index,
            this.olvColumnBrLat_Index,
            this.olvColumnCellName_Index,
            this.olvColumnLAC_Index,
            this.olvColumnCI_Index,
            this.olvColumnRxlev_Index,
            this.olvColumnRxquality_Index});
            this.treeListViewIndexStru.ContextMenuStrip = this.contextMenuStripIndexStru;
            this.treeListViewIndexStru.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewIndexStru.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewIndexStru.FullRowSelect = true;
            this.treeListViewIndexStru.GridLines = true;
            this.treeListViewIndexStru.Location = new System.Drawing.Point(3, 3);
            this.treeListViewIndexStru.Name = "treeListViewIndexStru";
            this.treeListViewIndexStru.OwnerDraw = true;
            this.treeListViewIndexStru.ShowGroups = false;
            this.treeListViewIndexStru.Size = new System.Drawing.Size(741, 304);
            this.treeListViewIndexStru.TabIndex = 3;
            this.treeListViewIndexStru.UseCompatibleStateImageBehavior = false;
            this.treeListViewIndexStru.View = System.Windows.Forms.View.Details;
            this.treeListViewIndexStru.VirtualMode = true;
            this.treeListViewIndexStru.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListViewIndexStru_MouseDoubleClick);
            // 
            // olvColumnSn_Index
            // 
            this.olvColumnSn_Index.HeaderFont = null;
            this.olvColumnSn_Index.Text = "序号";
            this.olvColumnSn_Index.Width = 75;
            // 
            // olvColumnLtLong_Index
            // 
            this.olvColumnLtLong_Index.HeaderFont = null;
            this.olvColumnLtLong_Index.Text = "左上角经度";
            this.olvColumnLtLong_Index.Width = 81;
            // 
            // olvColumnLtLat_Index
            // 
            this.olvColumnLtLat_Index.HeaderFont = null;
            this.olvColumnLtLat_Index.Text = "左上角纬度";
            this.olvColumnLtLat_Index.Width = 77;
            // 
            // olvColumnBrLong_Index
            // 
            this.olvColumnBrLong_Index.HeaderFont = null;
            this.olvColumnBrLong_Index.Text = "右下角经度";
            this.olvColumnBrLong_Index.Width = 81;
            // 
            // olvColumnBrLat_Index
            // 
            this.olvColumnBrLat_Index.HeaderFont = null;
            this.olvColumnBrLat_Index.Text = "右下角纬度";
            this.olvColumnBrLat_Index.Width = 83;
            // 
            // olvColumnCellName_Index
            // 
            this.olvColumnCellName_Index.HeaderFont = null;
            this.olvColumnCellName_Index.Text = "小区名";
            // 
            // olvColumnLAC_Index
            // 
            this.olvColumnLAC_Index.HeaderFont = null;
            this.olvColumnLAC_Index.Text = "LAC";
            // 
            // olvColumnCI_Index
            // 
            this.olvColumnCI_Index.HeaderFont = null;
            this.olvColumnCI_Index.Text = "CI";
            // 
            // olvColumnRxlev_Index
            // 
            this.olvColumnRxlev_Index.HeaderFont = null;
            this.olvColumnRxlev_Index.Text = "场强";
            // 
            // olvColumnRxquality_Index
            // 
            this.olvColumnRxquality_Index.HeaderFont = null;
            this.olvColumnRxquality_Index.Text = "质量";
            // 
            // contextMenuStripIndexStru
            // 
            this.contextMenuStripIndexStru.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExportIndexStru});
            this.contextMenuStripIndexStru.Name = "contextMenuStripIndexStru";
            this.contextMenuStripIndexStru.Size = new System.Drawing.Size(143, 26);
            // 
            // ToolStripMenuItemExportIndexStru
            // 
            this.ToolStripMenuItemExportIndexStru.Name = "ToolStripMenuItemExportIndexStru";
            this.ToolStripMenuItemExportIndexStru.Size = new System.Drawing.Size(142, 22);
            this.ToolStripMenuItemExportIndexStru.Text = "导出到xls...";
            this.ToolStripMenuItemExportIndexStru.Click += new System.EventHandler(this.ToolStripMenuItemExportIndexStru_Click);
            // 
            // WeakRxqualityInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(755, 337);
            this.Controls.Add(this.tabControl1);
            this.Name = "WeakRxqualityInfoForm";
            this.Text = "弱覆盖关联质差列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewWeakCovQual)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewIndexStru)).EndInit();
            this.contextMenuStripIndexStru.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListViewWeakCovQual;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnLtLong;
        private BrightIdeasSoftware.OLVColumn olvColumnLtLat;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLong;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLat;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLac;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnRxqual;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private BrightIdeasSoftware.TreeListView treeListViewIndexStru;
        private BrightIdeasSoftware.OLVColumn olvColumnSn_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnLtLong_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnLtLat_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLong_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLat_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnCI_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev_Index;
        private BrightIdeasSoftware.OLVColumn olvColumnRxquality_Index;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripIndexStru;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportIndexStru;
    }
}