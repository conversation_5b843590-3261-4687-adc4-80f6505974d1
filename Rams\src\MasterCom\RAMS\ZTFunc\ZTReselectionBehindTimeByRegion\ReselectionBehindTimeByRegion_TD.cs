﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ReselectionBehindTimeByRegion_TD : DIYAnalyseFilesOneByOneByRegion
    {
		protected List<BehindTimeSCellInfo> infoList = new List<BehindTimeSCellInfo>();
        protected new ReselectionBehindTimeCondition condition = new ReselectionBehindTimeCondition();

        public ReselectionBehindTimeByRegion_TD(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSDPA);
            ServiceTypes.Add(ServiceType.TDSCDMA_HSUPA);
            FilterSampleByRegion = false;
        }

        public override string Name
        {
            get { return "TD重选不及时"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13039, this.Name);
        }

        protected override bool getCondition()
        {
            ReselectionBehindTimeSettingForm form = new ReselectionBehindTimeSettingForm();
            if (form.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            form.GetCondition(ref condition);
            return true;
        }

        protected override void fireShowForm()
        {
            if (infoList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ReselectionBehindTimeResultForm).FullName);
            ReselectionBehindTimeResultForm form = obj == null ? null : obj as ReselectionBehindTimeResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new ReselectionBehindTimeResultForm(MainModel);
            }
            form.FillData(infoList);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            infoList.Clear();
        }

        protected override void getResultsAfterQuery()
        {
            FilterInfoListByTime();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                dealTPList(testPointList);
            }
        }

        private void dealTPList(List<TestPoint> testPointList)
        {
            int reviseCount = 0;
            int lastLac = -255, lastCi = -255;
            ValidTestPoint validPoint = NewPoint();
            foreach (TestPoint tp in testPointList)
            {
                // 采样点数据提取，修正非法点
                validPoint.Clear();
                ConvertResult result = validPoint.Convert(tp, condition.RxLevDiff);
                if (result != ConvertResult.Succeed && result != ConvertResult.NCellNotFound)
                {
                    if (reviseCount++ < condition.ReviseCount)
                    {
                        continue;
                    }
                    else
                    {
                        reviseCount = 0;
                    }
                }

                // 采样点非法，强制结束本轮
                if (result != ConvertResult.Succeed)
                {
                    lastLac = lastCi = -255;
                }
                // 需要重选切换，本轮第一个点；前一个点为空或者跟前一个点lacci不同
                else if ((lastLac == -255 && lastCi == -255)
                    || (validPoint.MainLac != lastLac || validPoint.MainCi != lastCi))
                {
                    lastLac = validPoint.MainLac;
                    lastCi = validPoint.MainCi;
                    BehindTimeSCellInfo sInfo = new BehindTimeSCellInfo();
                    sInfo.AppendPoint(validPoint);
                    infoList.Add(sInfo);
                }
                // 需要重选切换，跟前一点的lacci相同，为本轮的持续点
                else if (lastLac == validPoint.MainLac && lastCi == validPoint.MainCi)
                {
                    BehindTimeSCellInfo info = infoList[infoList.Count - 1];
                    info.AppendPoint(validPoint);
                }
            }
        }

        protected virtual ValidTestPoint NewPoint()
        {
            return new ValidTestPoint();
        }

        /// <summary>
        /// 按持续时间过滤
        /// </summary>
        public void FilterInfoListByTime()
        {
            List<BehindTimeSCellInfo> newInfoList = new List<BehindTimeSCellInfo>();
			int idLoop = 0;
            foreach (BehindTimeSCellInfo info in infoList)
            {
                if (info.Duration >= condition.Duration)
                {
                    info.GetResult();
					info.ID = ++idLoop;
                    newInfoList.Add(info);
                }
            }
            infoList = newInfoList;
        }
    }

    public class ReselectionBehindTimeCondition
    {
        public int RxLevDiff { get; set; }
        public int Duration { get; set; }
        /// <summary>
        /// 非法点修正次数
        /// </summary>
        public int ReviseCount { get; set; }

        public ReselectionBehindTimeCondition()
        {
			ReviseCount = 2;
            RxLevDiff = 4;
            Duration = 5;
        }
    }

    /// <summary>
    /// 一次性解释出一个TestPoint中所需的数据
    /// </summary>
	public class ValidTestPoint
	{
		public int MainLac { get; set; }
		public int MainCi { get; set; }
		public float MainRxLev { get; set; }
		public List<int> NLac { get; set; }
		public List<int> NCi { get; set; }
		public List<float> NRxLev { get; set; }
		public int Time { get; set; }
		public DateTime DTime { get; set; }

        public ValidTestPoint()
        {
            NLac = new List<int>();
            NCi = new List<int>();
            NRxLev = new List<float>();
        }

        public virtual ConvertResult Convert(TestPoint tp, int rxLevDiff)
        {
            int? mainLac = (int?)tp["TD_SCell_LAC"];
            if (mainLac == null || (int)mainLac == -255)
            {
                MainLac = MainCi = -255;
                return ConvertResult.MainLacciInvalid;
            }
            int? mainCi = (int?)tp["TD_SCell_CI"];
            if (mainCi == null || (int)mainCi == -255)
            {
                MainLac = MainCi = -255;
                return ConvertResult.MainLacciInvalid;
            }
            float? mainRxLev = (float?)tp["TD_PCCPCH_RSCP"];
            if (mainRxLev == null || (float)mainRxLev > -10 || (float)mainRxLev < -140)
            {
                return ConvertResult.MainRxLevInvalid;
            }
            MainLac = (int)mainLac;
            MainCi = (int)mainCi;
            MainRxLev = (float)mainRxLev;

            int? nRxLev;
            for (int i = 0; i < 6; ++i)
            {
                TDCell tdCell = tp.GetNBCell(i) as TDCell;
                if (tdCell == null)
                {
                    continue;
                }
                nRxLev = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                if (nRxLev == null || nRxLev > -10 || nRxLev < -140 || nRxLev - mainRxLev < rxLevDiff)
                {
                    continue;
                }
                
                NRxLev.Add((float)nRxLev);
                NLac.Add(tdCell.LAC);
                NCi.Add(tdCell.CI);
            }
            if (NLac.Count == 0)
            {
                return ConvertResult.NCellNotFound;
            }

            Time = tp.Time;
            DTime = tp.DateTime;
            return ConvertResult.Succeed;
        }

        public void Clear()
        {
            NLac.Clear();
            NCi.Clear();
            NRxLev.Clear();
        }
	}

	public enum ConvertResult
	{
		Succeed,
		MainLacciInvalid,
		MainRxLevInvalid,
		NCellNotFound,
	}

    /// <summary>
    /// 主区信息
    /// </summary>
	public class BehindTimeSCellInfo : BehindTimeNCellInfo
	{
        protected int sTime;
		protected Dictionary<string, BehindTimeNCellInfo> NCellInfoDict;
        public List<BehindTimeNCellInfo> NCellInfoList { get; private set; }
		public int Duration { get; protected set; }

		public BehindTimeSCellInfo() : base()
		{
            sTime = 0;
			NCellInfoDict = new Dictionary<string, BehindTimeNCellInfo>();
			NCellInfoList = new List<BehindTimeNCellInfo>();
		}

		public void AppendPoint(ValidTestPoint validPoint)
		{
			Lac = validPoint.MainLac;
			Ci = validPoint.MainCi;
			MaxRxLev = Math.Max(MaxRxLev, validPoint.MainRxLev);
			MinRxLev = Math.Min(MinRxLev, validPoint.MainRxLev);
			SumRxLev += validPoint.MainRxLev;
			Time = validPoint.Time;
			DTime = validPoint.DTime;
			++TestPointCount;

			// 追加邻区
            for (int i = 0; i < validPoint.NLac.Count; ++i)
            {
                string lacci = validPoint.NLac[i] + ":" + validPoint.NCi[i];
                if (!NCellInfoDict.ContainsKey(lacci))
                {
                    
                    BehindTimeNCellInfo nInfo = new BehindTimeNCellInfo();
                    NCellInfoDict.Add(lacci, nInfo);
                }
                NCellInfoDict[lacci].AppendPoint(validPoint, i);
            }

			// 记录第一个采样点的时间，计算持续时间
			if (sTime == 0)
            {
                sTime = Time;
            }
			Duration = Time - sTime;
		}

		public override void GetResult()
		{
			base.GetResult();
			foreach (BehindTimeNCellInfo nInfo in NCellInfoDict.Values)
			{
				nInfo.GetResult();
				NCellInfoList.Add(nInfo);
			}
			NCellInfoList.Sort();
			for (int i = 0; i < NCellInfoList.Count; ++i)
			{
				NCellInfoList[i].ID = i + 1;
			}
		}
	}

    /// <summary>
    /// 邻区信息
    /// </summary>
    public class BehindTimeNCellInfo : IComparable
    {
        public int ID { get; set; }
        public TDCell TdCell { get; protected set; }
		public string CellName { get; protected set; }
        public int Lac { get; protected set; }
        public int Ci { get; protected set; }
        public double MaxRxLev { get; protected set; }
        public double MinRxLev { get; protected set; }
        public double MeanRxLev { get; protected set; }
        public double MeanRxLevDiff { get; protected set; }
        public double SumRxLevDiff { get; protected set; }
        public double SumRxLev { get; protected set; }
        public int TestPointCount { get; protected set; }
        public int Time { get; protected set; }
        public DateTime DTime { get; protected set; }

        public BehindTimeNCellInfo()
        {
            MaxRxLev = -255;
            MinRxLev = 255;
            MeanRxLev = -255;
            SumRxLev = 0;
            MeanRxLevDiff = 0;
            SumRxLevDiff = 0;
            MeanRxLevDiff = 0;
            TestPointCount = 0;
        }

		public void AppendPoint(ValidTestPoint validPoint, int index)
		{
			Lac = validPoint.NLac[index];
			Ci = validPoint.NCi[index];
			MaxRxLev = Math.Max(MaxRxLev, validPoint.NRxLev[index]);
			MinRxLev = Math.Min(MinRxLev, validPoint.NRxLev[index]);
			SumRxLev += validPoint.NRxLev[index];
            SumRxLevDiff += (validPoint.NRxLev[index] - validPoint.MainRxLev);
			Time = validPoint.Time;
			DTime = validPoint.DTime;
			++TestPointCount;
		}

		public virtual void GetResult()
		{
			if (TestPointCount != 0)
			{
				MeanRxLev = Math.Round(SumRxLev / TestPointCount, 2);
                MeanRxLevDiff = Math.Round(SumRxLevDiff / TestPointCount, 2);
			}
			TdCell = MainModel.GetInstance().CellManager.GetTDCell(DTime, Lac, Ci);
			if (TdCell != null)
			{
				CellName = TdCell.Name;
			}
			else
			{
				CellName = string.Format("{0}-{1}", Lac, Ci);
			}
		}

        /// <summary>
        /// 邻区按平均场强从小到大排序
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
		public int CompareTo(object obj)
		{
			BehindTimeNCellInfo nInfo = obj as BehindTimeNCellInfo;
			if (this.MeanRxLev == nInfo.MeanRxLev)
			{
				return 0;
			}
			return this.MeanRxLev > nInfo.MeanRxLev ? -1 : 1;
		}
    }
}