﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRMainCellLastOccupyDlg : BaseDialog
    {
        public NRMainCellLastOccupyDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(NRMainCellLastOccupyCondition cond)
        {
            if (cond == null)
            {
                cond = new NRMainCellLastOccupyCondition();
            }
            numSiteRSRP.Value = (decimal)cond.RSRP;
            numSiteSINR.Value = (decimal)cond.SINR;
        }

        public NRMainCellLastOccupyCondition GetCondition()
        {
            NRMainCellLastOccupyCondition cond = new NRMainCellLastOccupyCondition();
            cond.RSRP = (int)numSiteRSRP.Value;
            cond.SINR = (float)numSiteSINR.Value;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class NRMainCellLastOccupyCondition
    {
        public float RSRP { get; set; } = -110;
        public float SINR { get; set; } = -10;
    }
}
