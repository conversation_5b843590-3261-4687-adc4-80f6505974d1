﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class FDDStationSettingDlgConfig_XJ : ConfigHelper<FDDStationSettingDlgConfigModel_XJ>
    {
        private static FDDStationSettingDlgConfig_XJ instance = null;
        public static FDDStationSettingDlgConfig_XJ Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FDDStationSettingDlgConfig_XJ();
                }
                return instance;
            }
        }

        public override string ConfigPath { get; } = $@"{AppDomain.CurrentDomain.BaseDirectory}\config\StationDlgConfig\FDDStationSettingDlg.xml";

        public override string LogPath { get; } = @"\BackGroundLog\StationDlgConfig\";
        public override string LogName { get; } = "-4GFDD单验门限设置.txt";

        protected override void loadConfig(XmlConfigFile xcfg, FDDStationSettingDlgConfigModel_XJ configInfo)
        {
            try
            {
                XmlElement config = xcfg.GetConfig("Configs");
                configInfo.AccessSuccessRate = getValidData(xcfg, config, "AccessSuccessRate", "");
                configInfo.AccessTestCount = getValidData(xcfg, config, "AccessTestCount", 0);
                configInfo.CSFBTestCount = getValidData(xcfg, config, "CSFBTestCount", 0);
                configInfo.CSFBSuccessRate = getValidData(xcfg, config, "CSFBSuccessRate", "");
                configInfo.VOLTETestCount = getValidData(xcfg, config, "VOLTETestCount", 0);
                configInfo.VOLTESuccessRate = getValidData(xcfg, config, "VOLTESuccessRate", "");
                configInfo.FTPDownloadThroughput = getValidData(xcfg, config, "FTPDownloadThroughput", "");
                configInfo.FTPUploadThroughput = getValidData(xcfg, config, "FTPUploadThroughput", "");
                configInfo.SwitchCount = getValidData(xcfg, config, "SwitchCount", 0);
                configInfo.SwtichSuccessRate = getValidData(xcfg, config, "SwtichSuccessRate", "");
            }
            catch (Exception ex)
            {
                ErrMsg = $"加载配置出错:{ex.Message}";
            }
        }

        public override void SaveConfig(FDDStationSettingDlgConfigModel_XJ configInfo)
        {
            try
            {
                var newConfig = new XmlConfigFile();
                XmlElement cfg = newConfig.AddConfig("Configs");
                newConfig.AddItem(cfg, "AccessSuccessRate", configInfo.AccessSuccessRate);
                newConfig.AddItem(cfg, "AccessTestCount", configInfo.AccessTestCount);
                newConfig.AddItem(cfg, "CSFBTestCount", configInfo.CSFBTestCount);
                newConfig.AddItem(cfg, "CSFBSuccessRate", configInfo.CSFBSuccessRate);
                newConfig.AddItem(cfg, "VOLTETestCount", configInfo.VOLTETestCount);
                newConfig.AddItem(cfg, "VOLTESuccessRate", configInfo.VOLTESuccessRate);
                newConfig.AddItem(cfg, "FTPDownloadThroughput", configInfo.FTPDownloadThroughput);
                newConfig.AddItem(cfg, "FTPUploadThroughput", configInfo.FTPUploadThroughput);
                newConfig.AddItem(cfg, "SwitchCount", configInfo.SwitchCount);
                newConfig.AddItem(cfg, "SwtichSuccessRate", configInfo.SwtichSuccessRate);
                newConfig.Save(ConfigPath);
            }
            catch (Exception ex)
            {
                ErrMsg = $"保存配置出错:{ex.Message}";
            }
        }
    }


    public class FDDStationSettingDlgConfigModel_XJ : ConfigDataInfo
    {
        public string AccessSuccessRate { get; set; }

        public int AccessTestCount { get; set; }

        public int CSFBTestCount { get; set; }

        public string CSFBSuccessRate { get; set; }

        public int VOLTETestCount { get; set; }

        public string VOLTESuccessRate { get; set; }

        public string FTPDownloadThroughput { get; set; }

        public string FTPUploadThroughput { get; set; }

        public int SwitchCount { get; set; }

        public string SwtichSuccessRate { get; set; }
    }
}
