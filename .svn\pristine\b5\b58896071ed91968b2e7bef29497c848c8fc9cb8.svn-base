﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Util;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDScanCellInfoResultForm : MinCloseForm
    {
        private List<TDScanCellInfo> cellInfoList;
        public TDScanCellInfoResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            InitGridView();
            InitCbxCellType();
            cbxCellType.SelectedIndexChanged += CbxCellType_SelectedChanged;
            gridView.DoubleClick += GridView_DoubleClick;
            miExportExcel.Click += MiExportExcel_Click;
        }

        private void InitGridView()
        {
            gridView.BeginInit();
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsView.ShowIndicator = false;
            gridView.OptionsView.ShowGroupPanel = false;

            GridColumn column = new GridColumn();
            column.Caption = "小区名";
            column.FieldName = "CellName";
            column.Visible = true;
            column.VisibleIndex = 0;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "LAC";
            column.FieldName = "Lac";
            column.Visible = true;
            column.VisibleIndex = 1;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "CI";
            column.FieldName = "Ci";
            column.Visible = true;
            column.VisibleIndex = 2;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "主频";
            column.FieldName = "Freq";
            column.Visible = true;
            column.VisibleIndex = 3;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "扰码";
            column.FieldName = "Cpi";
            column.Visible = true;
            column.VisibleIndex = 4;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "小区类型";
            column.FieldName = "TypeDesc";
            column.Visible = true;
            column.VisibleIndex = 5;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "最强小区采样点数";
            column.FieldName = "FirstSampleCount";
            column.Visible = true;
            column.VisibleIndex = 6;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "最强小区平均场强";
            column.FieldName = "FirstAvgRxlev";
            column.Visible = true;
            column.VisibleIndex = 7;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "总采样点数";
            column.FieldName = "TotalSampleCount";
            column.Visible = true;
            column.VisibleIndex = 8;
            gridView.Columns.Add(column);

            column = new GridColumn();
            column.Caption = "平均场强";
            column.FieldName = "TotalAvgRxlev";
            column.Visible = true;
            column.VisibleIndex = 9;
            gridView.Columns.Add(column);
            gridView.EndInit();
        }

        private void InitCbxCellType()
        {
            cbxCellType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            cbxCellType.Properties.Items.Add("室外");
            cbxCellType.Properties.Items.Add("室内");
            cbxCellType.Properties.Items.Add("全部");
            cbxCellType.SelectedIndex = 0;
        }

        private void FreshResult()
        {
            List<TDScanCellInfo> showList = new List<TDScanCellInfo>();
            if (this.cellInfoList == null)
            {
                return;
            }

            if (cbxCellType.SelectedIndex == 2)
            {
                showList.AddRange(this.cellInfoList);
            }
            else
            {
                foreach (TDScanCellInfo cellInfo in this.cellInfoList)
                {
                    if (cellInfo.TypeDesc == (cbxCellType.SelectedItem as string))
                    {
                        showList.Add(cellInfo);
                    }
                }
            }

            gridControl.DataSource = showList;
            gridControl.RefreshDataSource();
            gridView.RefreshData();
            labelCellCount.Text = "当前小区个数: " + showList.Count;
        }

        private void CbxCellType_SelectedChanged(object sender, EventArgs e)
        {
            FreshResult();
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                TDScanCellInfo cellInfo = gridView.GetRow(gridView.GetSelectedRows()[0]) as TDScanCellInfo;
                MainModel.MainForm.GetMapForm().GoToView(cellInfo.Longitude, cellInfo.Latitude);
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        public void FillData(List<TDScanCellInfo> cellInfoList)
        {
            this.cellInfoList = new List<TDScanCellInfo>(cellInfoList);
            cbxCellType.SelectedIndex = 0;
            FreshResult();
        }
    }
}
