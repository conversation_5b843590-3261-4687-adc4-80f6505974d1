﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyUpdateTaskOrderInfo : DiySqlMultiNonQuery
    {
        private readonly TaskOrderFailAnalyseFile failAnalyseOrderFiles;
        public DiyUpdateTaskOrderInfo(TaskOrderFailAnalyseFile failAnalyseOrderFiles)
        {
            MainDB = true;
            this.failAnalyseOrderFiles = failAnalyseOrderFiles;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                ErrorInfo = "更新数据时发生异常";
                log.Error(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (string fileName in failAnalyseOrderFiles.FileName)
            {
                strb.AppendFormat(@"update [tb_lowtask_file] set [备注]='解析异常' where [工单号] = '{0}' and [文件名] = '{1}';", failAnalyseOrderFiles.OrderID, fileName);
            }
            return strb.ToString();
        }

        public override string Name
        {
            get { return "更新工单信息表"; }
        }
    }
}
