﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanFartherCoverSettingDlg : Form
    {
        private ScanFartherCoverCondition cond = null;

        public ScanFartherCoverSettingDlg(ScanFartherCoverCondition condition)
        {
            InitializeComponent();
            this.cond = condition;
            if (this.cond != null)
            {
                fillCondition();
            }
        }

        private void fillCondition()
        {
            spinEditRSRP.Value = cond.RsrpThreshold;
            spinEditSampleNum.Value = cond.SampleNum;
            spinEditDistanceMin.Value = cond.DistanceMin;
            spinEditDistanceMax.Value = cond.DistanceMax;
            chbMaxRsrpCell.Checked = cond.ChkMaxRsrpCell;
        }

        public ScanFartherCoverCondition GetCondition()
        {
            if (cond == null)
            {
                cond = new ScanFartherCoverCondition();
            }
            cond.ResetValue((int)spinEditRSRP.Value, (int)spinEditSampleNum.Value, (int)spinEditDistanceMin.Value,
                (int)spinEditDistanceMax.Value, chbMaxRsrpCell.Checked);
            return cond;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class ScanFartherCoverCondition
    {
        public int RsrpThreshold { get; set; } = -110;
        public int SampleNum { get; set; } = 15;
        public int DistanceMin { get; set; } = 1500;
        public int DistanceMax { get; set; } = 6000;
        public bool ChkMaxRsrpCell { get; set; } = true;

        public void ResetValue(int rsrp, int sampleNum, int distanceMin, int distanceMax, bool chkMaxRsrpCell)
        {
            RsrpThreshold = rsrp;
            SampleNum = sampleNum;
            DistanceMin = distanceMin;
            DistanceMax = distanceMax;
            ChkMaxRsrpCell = chkMaxRsrpCell;
        }
    }
}
