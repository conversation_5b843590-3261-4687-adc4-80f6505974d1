﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterfereCellFilterForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTScanInterfereCellFilterForm));
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numSameValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numNbhValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numMainRxLevMinValue = new DevExpress.XtraEditors.SpinEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.rdbCaleStrongestCell = new System.Windows.Forms.RadioButton();
            this.rdbCaleOtherCell = new System.Windows.Forms.RadioButton();
            this.numRxLevDiff = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numSameValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNbhValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMainRxLevMinValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDiff.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(50, 24);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 4;
            this.labelControl1.Text = "同频保护比";
            // 
            // numSameValue
            // 
            this.numSameValue.EditValue = new decimal(new int[] {
            9,
            0,
            0,
            0});
            this.numSameValue.Location = new System.Drawing.Point(128, 21);
            this.numSameValue.Name = "numSameValue";
            this.numSameValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numSameValue.Properties.Appearance.Options.UseFont = true;
            this.numSameValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSameValue.Properties.IsFloatValue = false;
            this.numSameValue.Properties.Mask.EditMask = "N00";
            this.numSameValue.Size = new System.Drawing.Size(68, 20);
            this.numSameValue.TabIndex = 0;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(202, 24);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 6;
            this.labelControl2.Text = "dB";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(202, 51);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 12);
            this.labelControl3.TabIndex = 9;
            this.labelControl3.Text = "dB";
            // 
            // numNbhValue
            // 
            this.numNbhValue.EditValue = new decimal(new int[] {
            9,
            0,
            0,
            -2147483648});
            this.numNbhValue.Location = new System.Drawing.Point(128, 48);
            this.numNbhValue.Name = "numNbhValue";
            this.numNbhValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numNbhValue.Properties.Appearance.Options.UseFont = true;
            this.numNbhValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNbhValue.Properties.IsFloatValue = false;
            this.numNbhValue.Properties.Mask.EditMask = "N00";
            this.numNbhValue.Size = new System.Drawing.Size(68, 20);
            this.numNbhValue.TabIndex = 1;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(50, 51);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 7;
            this.labelControl4.Text = "邻频保护比";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(202, 78);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 12;
            this.labelControl5.Text = "dBm";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(26, 78);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(84, 12);
            this.labelControl6.TabIndex = 10;
            this.labelControl6.Text = "服务小区电平≥";
            // 
            // numMainRxLevMinValue
            // 
            this.numMainRxLevMinValue.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.numMainRxLevMinValue.Location = new System.Drawing.Point(128, 75);
            this.numMainRxLevMinValue.Name = "numMainRxLevMinValue";
            this.numMainRxLevMinValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMainRxLevMinValue.Properties.Appearance.Options.UseFont = true;
            this.numMainRxLevMinValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMainRxLevMinValue.Properties.IsFloatValue = false;
            this.numMainRxLevMinValue.Properties.Mask.EditMask = "N00";
            this.numMainRxLevMinValue.Size = new System.Drawing.Size(68, 20);
            this.numMainRxLevMinValue.TabIndex = 2;
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(202, 242);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.Location = new System.Drawing.Point(294, 242);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // rdbCaleStrongestCell
            // 
            this.rdbCaleStrongestCell.AutoSize = true;
            this.rdbCaleStrongestCell.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbCaleStrongestCell.Location = new System.Drawing.Point(27, 33);
            this.rdbCaleStrongestCell.Name = "rdbCaleStrongestCell";
            this.rdbCaleStrongestCell.Size = new System.Drawing.Size(143, 16);
            this.rdbCaleStrongestCell.TabIndex = 3;
            this.rdbCaleStrongestCell.TabStop = true;
            this.rdbCaleStrongestCell.Text = "最强小区作为服务小区";
            this.rdbCaleStrongestCell.UseVisualStyleBackColor = true;
            // 
            // rdbCaleOtherCell
            // 
            this.rdbCaleOtherCell.AutoSize = true;
            this.rdbCaleOtherCell.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbCaleOtherCell.Location = new System.Drawing.Point(27, 67);
            this.rdbCaleOtherCell.Name = "rdbCaleOtherCell";
            this.rdbCaleOtherCell.Size = new System.Drawing.Size(71, 16);
            this.rdbCaleOtherCell.TabIndex = 4;
            this.rdbCaleOtherCell.TabStop = true;
            this.rdbCaleOtherCell.Text = "与最强差";
            this.rdbCaleOtherCell.UseVisualStyleBackColor = true;
            this.rdbCaleOtherCell.CheckedChanged += new System.EventHandler(this.rdbCaleOtherCell_CheckedChanged);
            // 
            // numRxLevDiff
            // 
            this.numRxLevDiff.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numRxLevDiff.Location = new System.Drawing.Point(102, 66);
            this.numRxLevDiff.Name = "numRxLevDiff";
            this.numRxLevDiff.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numRxLevDiff.Properties.Appearance.Options.UseFont = true;
            this.numRxLevDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDiff.Properties.IsFloatValue = false;
            this.numRxLevDiff.Properties.Mask.EditMask = "N00";
            this.numRxLevDiff.Size = new System.Drawing.Size(51, 20);
            this.numRxLevDiff.TabIndex = 5;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(159, 69);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(143, 12);
            this.label1.TabIndex = 18;
            this.label1.Text = "dB 内的小区作为服务小区";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rdbCaleStrongestCell);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.rdbCaleOtherCell);
            this.groupBox1.Controls.Add(this.numRxLevDiff);
            this.groupBox1.Location = new System.Drawing.Point(26, 115);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(343, 103);
            this.groupBox1.TabIndex = 19;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "服务小区选取";
            // 
            // ZTScanInterfereCellFilterForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(399, 290);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.numMainRxLevMinValue);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.numNbhValue);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.numSameValue);
            this.Controls.Add(this.labelControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTScanInterfereCellFilterForm";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numSameValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNbhValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMainRxLevMinValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDiff.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numSameValue;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numNbhValue;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit numMainRxLevMinValue;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.RadioButton rdbCaleStrongestCell;
        private System.Windows.Forms.RadioButton rdbCaleOtherCell;
        private DevExpress.XtraEditors.SpinEdit numRxLevDiff;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}