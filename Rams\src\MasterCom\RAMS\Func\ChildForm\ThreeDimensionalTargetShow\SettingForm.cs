using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.ThreeDimensionalTargetShow
{
    public partial class SettingForm : Form
    {
        public SettingForm()
        {
            InitializeComponent();
        }

        private void init()
        {
            loadSystem();
            loadTargetXYZ();
            initComponent();
        }

        private void initComponent()
        {
            comboBoxSys.Text = Parameter.Sys;
            XcomboBox.Text = Parameter.TargetX;
            YcomboBox.Text = Parameter.TargetY;
            ZcomboBox.Text = Parameter.TargetZ;
            labelColor.BackColor = Color.FromArgb(255, Parameter.ColorR, Parameter.ColorG, Parameter.ColorB);
        }

        private void loadSystem()
        {
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                comboBoxSys.Items.Add(system.Name);
            }
            if (comboBoxSys.Items.Count > 0)
            {
                comboBoxSys.SelectedIndex = 0;
            }
        }

        private void loadTargetXYZ()
        {
            XcomboBox.Items.Clear();
            YcomboBox.Items.Clear();
            ZcomboBox.Items.Clear();
            string systemName = (string)comboBoxSys.SelectedItem;
            foreach (DTDisplayParameterInfo paramInfo in DTDisplayParameterManager.GetInstance()[systemName].DisplayParamInfos)
            {
                if ((paramInfo.Type & (int)DTDisplayParameterInfoType.Range) != 0)
                {
                    XcomboBox.Items.Add(paramInfo.Name);
                    YcomboBox.Items.Add(paramInfo.Name);
                    ZcomboBox.Items.Add(paramInfo.Name);
                }
            }
        }

        private void comboBoxSys_SelectedIndexChanged(object sender, EventArgs e)
        {
            loadTargetXYZ();
            XcomboBox.SelectedIndex = 0;
            YcomboBox.SelectedIndex = 0;
            ZcomboBox.SelectedIndex = 0;
        }

        private void XcomboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void YcomboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void ZcomboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Parameter.Sys = comboBoxSys.Text;
            Parameter.TargetX = XcomboBox.Text;
            Parameter.TargetY = YcomboBox.Text;
            Parameter.TargetZ = ZcomboBox.Text;
            Parameter.ColorR = (int)labelColor.BackColor.R;
            Parameter.ColorG = (int)labelColor.BackColor.G;
            Parameter.ColorB = (int)labelColor.BackColor.B;
            this.Dispose();

        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }

        private void SettingForm_Load(object sender, EventArgs e)
        {
            init();
        }

        private void labelColor_Click(object sender, EventArgs e)
        {
            ColorDialog colordialog = new ColorDialog();
            if (colordialog.ShowDialog()==DialogResult.OK)
            {
                labelColor.BackColor = colordialog.Color;
            }
        }
    }

    public static class Parameter
    {
        public static string Sys { get; set; } = "GSM";
        public static string TargetX { get; set; } = "RxLevSub";
        public static string TargetY { get; set; } = "RxQualSub";
        public static string TargetZ { get; set; } = "RxLevFull";
        public static int ColorR { get; set; }
        public static int ColorG { get; set; }
        public static int ColorB { get; set; } = 255;
    }
}