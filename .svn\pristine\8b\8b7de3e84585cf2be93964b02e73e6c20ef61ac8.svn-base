﻿using MasterCom.RAMS.Chris.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MosAnaCondition
    {
        public MosAnaCondition()
        {
            SinrRangeSet = new RangeSet();
            RsrpRangeSet = new RangeSet();
            QamPerRangeSet = new RangeSet();
            DlQam16PerRangeSet = new RangeSet();
            DlQam64PerRangeSet = new RangeSet();
            DlQpskPerRangeSet = new RangeSet();
        }
        public RangeSet SinrRangeSet { get; set; }
        public RangeSet RsrpRangeSet { get; set; }
        public RangeSet QamPerRangeSet { get; set; }
        public RangeSet DlQam16PerRangeSet { get; set; }
        public RangeSet DlQam64PerRangeSet { get; set; }
        public RangeSet DlQpskPerRangeSet { get; set; }
    }
}
