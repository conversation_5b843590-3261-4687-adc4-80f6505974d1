﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryDLSpeedLimitAna : DIYAnalyseByFileBackgroundBase
    {
        public QueryDLSpeedLimitAna()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "下载速率限速分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22112, this.Name);
        }

        DLSeepLimitCond limitCond = new DLSeepLimitCond();
        List<DLSpeedLimitInfo> limitInfoList = new List<DLSpeedLimitInfo>();

        protected override bool getCondition()
        {
            DLSpeedLimitSet conForm = new DLSpeedLimitSet();
            if (conForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            limitCond = conForm.GetCond();
            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            limitInfoList = new List<DLSpeedLimitInfo>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                dealTestPoint(dtFile);
            }
        }

        private void dealTestPoint(DTFileDataManager dtFile)
        {
            List<int> calIndex = new List<int>();
            Dictionary<int, int> timeSNDic = new Dictionary<int, int>();
            DLSpeedLimitInfo limitInfo = new DLSpeedLimitInfo();
            int iCount = dtFile.TestPoints.Count;
            int iSpeedTime = -1;
            int iValidLastIndex = iFindValidLastIndex(dtFile.TestPoints);
            for (int i = 0; i < iCount; i++)
            {
                try
                {
                    dealSingleTP(dtFile, calIndex, timeSNDic, ref limitInfo, ref iSpeedTime, iValidLastIndex, ref i);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void dealSingleTP(DTFileDataManager dtFile, List<int> calIndex, Dictionary<int, int> timeSNDic, 
            ref DLSpeedLimitInfo limitInfo, ref int iSpeedTime, int iValidLastIndex, ref int i)
        {
            TestPoint tp = dtFile.TestPoints[i];
            double speed;
            bool isValid = getValidSpeed(tp, out speed);
            if (isValid)
            {
                if (!timeSNDic.ContainsKey(tp.Time))
                {
                    timeSNDic[tp.Time] = i;
                }
                limitInfo = addDLSpeedLimitInfo(dtFile, limitInfo, iSpeedTime, tp);
                iSpeedTime = tp.Time;
                if (limitInfo.TpCount != 0 || (speed >= limitCond.DSeepAvgMin && speed <= limitCond.DSeepAvgMax))
                {
                    limitInfo.Add(tp, speed);
                    if (limitInfo.TpCount >= 2 && limitInfo.ITimeSpan > limitCond.DLastTime)
                    {
                        addLastDLSpeedLimitInfo(dtFile, calIndex, timeSNDic, ref limitInfo, iValidLastIndex, ref i);
                    }
                }
            }
        }

        private DLSpeedLimitInfo addDLSpeedLimitInfo(DTFileDataManager dtFile, DLSpeedLimitInfo limitInfo, int iSpeedTime, TestPoint tp)
        {
            if (iSpeedTime != -1 && (tp.Time - iSpeedTime > 2))
            {
                if (limitInfo.TpCount > 2 && isLimitInfoValid(limitInfo))
                {
                    limitInfo.FileInfoMsg = dtFile.GetFileInfo();
                    limitInfo.ISN = limitInfoList.Count + 1;
                    limitInfo.ConvertInfo(limitCond);
                    limitInfoList.Add(limitInfo);
                }
                limitInfo = new DLSpeedLimitInfo();
            }

            return limitInfo;
        }

        private void addLastDLSpeedLimitInfo(DTFileDataManager dtFile, List<int> calIndex, Dictionary<int, int> timeSNDic, 
            ref DLSpeedLimitInfo limitInfo, int iValidLastIndex, ref int i)
        {
            if (!isLimitInfoValid(limitInfo) || i == iValidLastIndex)
            {
                limitInfo.ReMove();
                if (isLimitInfoValid(limitInfo))
                {
                    limitInfo.FileInfoMsg = dtFile.GetFileInfo();
                    limitInfo.ISN = limitInfoList.Count + 1;
                    limitInfo.ConvertInfo(limitCond);
                    limitInfoList.Add(limitInfo);
                }
                else
                {
                    i = timeSNDic[limitInfo.TpList[1].Time] - 1;
                    if (calIndex.Contains(i))
                        i++;
                    else
                        calIndex.Add(i);
                }
                limitInfo = new DLSpeedLimitInfo();
            }
        }

        private bool getValidSpeed(TestPoint tp, out double speed)
        {
            bool ret = isValidTestPoint(tp);
            if (!ret)
            {
                object obj = tp["lte_APP_ThroughputDL_Mb"];
                if (obj != null)
                {
                    speed = double.Parse(obj.ToString());
                    if (speed >= 0)
                    {
                        return true;
                    }
                }
            }
            speed = 0;
            return false;
        }

        private bool isLimitInfoValid(DLSpeedLimitInfo limitInfo)
        {
            bool isValid = false;
            if (limitInfo.ITimeSpan >= limitCond.DLastTime 
                && limitInfo.DSpeedAvg >= limitCond.DSeepAvgMin && limitInfo.DSpeedAvg <= limitCond.DSeepAvgMax
                && limitInfo.DVariance >= limitCond.DVarianceMin && limitInfo.DVariance <= limitCond.DVarianceMax)
            {
                isValid = true;
            }
            return isValid;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ret = false;
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    short? type = (short?)testPoint["lte_APP_type"];
                    if (type == null || type != (int)AppType.FTP_Download)
                    {
                        return false;
                    }
                    if (limitCond.IsTpRegion)
                        ret = condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                    else
                        ret = true;
                }
            }
            catch
            {
                //continue
            }
            return ret;
        }

        private int iFindValidLastIndex(List<TestPoint> tpList)
        {
            int dex = tpList.Count - 1;
            for (int i = tpList.Count - 1; i >= 0; i--)
            {
                TestPoint tp = tpList[i];
                bool ret = isValidTestPoint(tp);
                if (!ret) 
                    continue;
                object obj = tp["lte_APP_ThroughputDL_Mb"];
                if (obj == null) 
                    continue;
                double speed = double.Parse(obj.ToString());
                if (speed < 0) 
                    continue;
                dex = i;
                break;
            }
            return dex;
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(DLSpeedLimitForm).FullName);
            DLSpeedLimitForm resultForm = obj == null ? null : obj as DLSpeedLimitForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new DLSpeedLimitForm(MainModel);
            }
            resultForm.FillData(limitInfoList);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }
    }
}
