﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.Func.NebulaForm
{
    public partial class NebulaForm : ChildForm
    {
        List<DataXY> dataxy = new List<DataXY>();
        public NebulaForm()
        {
            InitializeComponent();
            this.panel.BackColor = Color.White;
        }

        public override void Init()
        {
            base.Init();
            MainModel.GetInstance().DistrictChanged += districtChanged;
            MainModel.GetInstance().DTDataChanged += dtDataChanged;
            MainModel.GetInstance().SelectedMessageChanged += selectedMessageChanged;
            Disposed += disposed;
            dtDataChanged(null, null);
        }
        private void disposed(object sender,EventArgs e)
        {
            MainModel.GetInstance().DistrictChanged -= districtChanged;
            MainModel.GetInstance().DTDataChanged -= dtDataChanged;
            MainModel.GetInstance().SelectedMessageChanged -= selectedMessageChanged;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["SystemName"] = XYData.Sys;
                param["Xtarget"] = XYData.TargetX;
                param["Ytarget"] = XYData.TargetY;
                param["Xsmall"] = XYData.Xsmall;
                param["Xlarge"] = XYData.Xlarge;
                param["XcomboboxIndex"] = XYData.Xcomboboxindex;
                param["Ysmall"] = XYData.Ysmall;
                param["Ylarge"] = XYData.Ylarge;
                param["YcomboboxIndex"] = XYData.Ycomboboxindex;
                param["Xcenter"] = XYData.Xcenter;
                param["Ycenter"] = XYData.Ycenter;
                param["Xcheckbox"] = XYData.Xcheckbox;
                param["Ycheckbox"] = XYData.Ycheckbox;
                param["Pointcheckbox"] = XYData.Pointcheckbox;
                param["FirstColorR"] = (int)XYData.ColorFirst.R;
                param["FirstColorG"] = (int)XYData.ColorFirst.G;
                param["FirstColorB"] = (int)XYData.ColorFirst.B;
                param["SecondColorR"] = (int)XYData.ColorSecond.R;
                param["SecondColorG"] = (int)XYData.ColorSecond.G;
                param["SecondColorB"] = (int)XYData.ColorSecond.B;
                param["ThirdColorR"] = (int)XYData.ColorThird.R;
                param["ThirdColorG"] = (int)XYData.ColorThird.G;
                param["ThirdColorB"] = (int)XYData.ColorThird.B;
                param["FourthColorR"] = (int)XYData.ColorFourth.R;
                param["FourthColorG"] = (int)XYData.ColorFourth.G;
                param["FourthColorB"] = (int)XYData.ColorFourth.B;
                param["ScaleListX"] = XYData.ListScaleItem2String(XYData.ListScaleItemX);
                param["ScaleListY"] = XYData.ListScaleItem2String(XYData.ListScaleItemY);
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                XYData.Sys = (string)value["SystemName"];
                XYData.TargetX = (string)value["Xtarget"];
                XYData.TargetY = (string)value["Ytarget"];
                XYData.Xsmall = (float)value["Xsmall"];
                XYData.Xlarge = (float)value["Xlarge"];
                XYData.Xcomboboxindex = (int)value["XcomboboxIndex"];
                XYData.Ysmall = (float)value["Ysmall"];
                XYData.Ylarge = (float)value["Ylarge"];
                XYData.Ycomboboxindex = (int)value["YcomboboxIndex"];
                XYData.Xcheckbox = (bool)value["Xcheckbox"];
                XYData.Ycheckbox = (bool)value["Ycheckbox"];
                XYData.Pointcheckbox = (bool)value["Pointcheckbox"];
                XYData.Xcenter = (float)value["Xcenter"];
                XYData.Ycenter = (float)value["Ycenter"];
                XYData.ColorFirst = Color.FromArgb(255, (int)value["FirstColorR"], (int)value["FirstColorG"], (int)value["FirstColorB"]);
                XYData.ColorSecond = Color.FromArgb(255, (int)value["SecondColorR"], (int)value["SecondColorG"], (int)value["SecondColorB"]);
                XYData.ColorThird = Color.FromArgb(255, (int)value["ThirdColorR"], (int)value["ThirdColorG"], (int)value["ThirdColorB"]);
                XYData.ColorFourth = Color.FromArgb(255, (int)value["FourthColorR"], (int)value["FourthColorG"], (int)value["FourthColorB"]);
                XYData.ListScaleItemX = new List<ScaleItem>();
                XYData.ListScaleItemY = new List<ScaleItem>();
                if (value.ContainsKey("ScaleListX") && value.ContainsKey("ScaleListY"))
                {
                    XYData.ListScaleItemX = XYData.String2ListScaleItem((string)value["ScaleListX"]);
                    XYData.ListScaleItemY = XYData.String2ListScaleItem((string)value["ScaleListY"]);
                }
            }
        }

        private void districtChanged(object sender, EventArgs e)
        {
            dtDataChanged(null, null);
        }

        private void dtDataChanged(object sender, EventArgs e)
        {
            if (XYData.IsBigDataReplaying)
            {
                this.isBigData = true;
                return;
            }
            this.isBigData = false;
            if (MainModel.GetInstance().DTDataManager.FileDataManagers.Count > 0)
            {
                DTFileDataManager fdm = MainModel.GetInstance().DTDataManager.FileDataManagers[IsCompareForm ? 1 : 0];
                if (fdm.DTDatas.Count > 0)
                {
                    int ms = fdm.DTDatas[0].MS;
                    for (int i = 0; i < chartInfoManager.ChartCount; i++)
                    {
                        chartInfoManager[i].ChangeMS(ms);
                    }
                }
            }
            freshPixelCount();
            charInfoChanged();
        }

        private void freshPixelCount()
        {
            pixels.Clear();
            pixelDataIndexMap.Clear();
            pixelCount = 0;
            for (int i = 0; i < MainModel.GetInstance().DTDataManager.FileDataManagers.Count; i++)  //计算总共有多少个元素点
            {
                if (MainModel.GetInstance().IsFileReplayByCompareMode && i != (IsCompareForm ? 1 : 0))
                {
                    continue;
                }
                DTFileDataManager fdm = MainModel.GetInstance().DTDataManager.FileDataManagers[i];//实例化第i个文件
                DTData preData = null;
                int index = 0;
                foreach (DTData data in fdm.DTDatas) //通过循环拿到文件中的所有数据
                {
                    preData = addPixel(fdm, preData, index, data);
                    index++;
                }
                if (fdm.TestPoints.Count + fdm.Events.Count > 0)
                {
                    pixelCount += graphRatio;
                }
            }
        }

        private DTData addPixel(DTFileDataManager fdm, DTData preData, int index, DTData data)
        {
            if (data is TestPoint || data is Event)
            {
                if (preData == null || data.Time - preData.Time < 2)
                {
                    pixelCount += graphRatio;
                }
                else if (data.Time - preData.Time < 20)
                {
                    pixelCount += (data.Time - preData.Time) * graphRatio;
                }
                else
                {
                    pixelCount += 20 * graphRatio;
                }
                preData = data;
                pixels.Add(pixelCount - 1);
                pixelDataIndexMap[pixelCount - 1] = new DTDataIndex(fdm.FileID, DTDataType.DTData, index);
            }

            return preData;
        }

        private void charInfoChanged()
        {
            GetPoint();
            updateGraphDraw();
        }
        /**
         * 标识现在这批数据是否是属于大批量数据功能的，
         * 若是，则在改变了参数的时候不可以重新GetPoint，
         * 否则，在改变了参数的时候需要重新GetPoint，也就是保留原来的回放触发显示数据时候的流程。
         */
        private bool isBigData = false;
        private void GetPoint()
        {
            if (this.isBigData)
            {
                return;
            }
            ChartSerialInfo serialInfo = new ChartSerialInfo();
            serialInfo.DisplayParam = DTDisplayParameterManager.GetInstance()[XYData.Sys, XYData.TargetX, 0];  //x轴信息
            ChartSerialInfo seeialInfoy = new ChartSerialInfo();
            seeialInfoy.DisplayParam = DTDisplayParameterManager.GetInstance()[XYData.Sys, XYData.TargetY, 0];  //y轴信息
            dataxy.Clear();
            XYData.ListDataXY = new List<DataXY>();
            float unitX=0;
            float unitY=0;
            if (XYData.Xlarge - XYData.Xsmall > 0)
            {
                unitX = (panel.Width - (widthEdgeLeft+widthEdgeRight)) / (XYData.Xlarge - XYData.Xsmall);
            }
            if (XYData.Ylarge - XYData.Ysmall > 0)
            {
                unitY = (panel.Height - (heightEdgeTop+heightEdgeBottom)) / (XYData.Ylarge - XYData.Ysmall);
            }
            foreach (int i in pixels)
            {
                DTData dtData = MainModel.GetInstance().DTDataManager[pixelDataIndexMap[i]];
                if (dtData is TestPoint)
                {
                    addDataxy(serialInfo, seeialInfoy, unitX, unitY, dtData);
                }
            }
            XYData.ListDataXY = dataxy;
        }

        private void addDataxy(ChartSerialInfo serialInfo, ChartSerialInfo seeialInfoy, float unitX, float unitY, DTData dtData)
        {
            Random random = new Random();
            float? value = serialInfo.GetValue(dtData as TestPoint);
            float? valuey = seeialInfoy.GetValue(dtData as TestPoint);
            int randomX = random.Next(0, (int)unitX);
            int randomY = random.Next(0, (int)unitY);
            if (value != null && valuey != null)
            {
                DataXY dtxy = new DataXY();
                dtxy.Xvalue = (float)value;
                dtxy.Yvalue = (float)valuey;
                //x轴的随机数
                if (dtxy.Xvalue <= XYData.Xsmall + 0.5)
                {
                    dtxy.Xrandom = (float)randomX;
                }
                else if (dtxy.Xvalue >= XYData.Xlarge - 0.5)
                {
                    dtxy.Xrandom = -(float)randomX;
                }
                else
                {
                    dtxy.Xrandom = (float)(randomX - (int)unitX / 2);
                }
                //y轴的随机数
                if (dtxy.Yvalue <= XYData.Ysmall + 0.5)
                {
                    dtxy.Yrandom = (float)randomY;
                }
                else if (dtxy.Yvalue >= XYData.Ylarge - 0.5)
                {
                    dtxy.Yrandom = -(float)randomY;
                }
                else
                {
                    dtxy.Yrandom = (float)(randomY - (int)unitY / 2);
                }
                dataxy.Add(dtxy);
            }
        }

        private void panel_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                this.ContextMenuStrip = this.contextMenuStrip1;
            }
        }

        private void SettingToolStripMenuItem_Click(object sender, EventArgs e)
        {

            SettingForm box = new SettingForm(this);
            if (box.ShowDialog() == DialogResult.OK)
            {
                //charInfoChanged()
            }
        }

        private List<int> pixels = new List<int>();

        private static int chartCount = 1;

        private ChartInfoManager chartInfoManager = new ChartInfoManager(chartCount);

        private int pixelCount;

        private int selectedPixel = -1;

        private int graphRatio = 2;

        private Dictionary<int, DTDataIndex> pixelDataIndexMap = new Dictionary<int, DTDataIndex>();

        public void ChangeMSAs(int ms)
        {
            int count = chartInfoManager.ChartCount;
            for (int i = 0; i < count; i++)
            {
                ChartInfo chartInfo = chartInfoManager[i];
                chartInfo.ChangeMS(ms);
            }
            freshPixelCount();
            charInfoChanged();
        }


        private void panel_SizeChanged(object sender, EventArgs e)
        {
            this.Invalidate();
        }

        private void selectedMessageChanged(object sender, EventArgs e)
        {
            if (sender != this)
            {
                selectedPixel = -1;
                if (MainModel.SelectedMessage != null)
                {
                    if (MainModel.SelectedTestPoints.Count == 0)
                    {
                        MainModel.SelectTestPointByMessage();
                        if (MainModel.SelectedTestPoints.Count == 0) return;
                    }
                    DTDataManager dtDataManager = MainModel.DTDataManager;
                    TestPoint stp = getSTP(dtDataManager);
                    setSelectedPixel(stp, dtDataManager);
                }
            }
        }

        private TestPoint getSTP(DTDataManager dtDataManager)
        {
            TestPoint stp = MainModel.SelectedTestPoints[0];
            if (MainModel.IsFileReplayByCompareMode && stp.FileID != dtDataManager.FileDataManagers[IsCompareForm ? 1 : 0].FileID)
            {
                int time = stp.Time;
                stp = dtDataManager.FileDataManagers[IsCompareForm ? 1 : 0].TestPoints.Find(delegate (TestPoint p) { return p.Time == time; });
            }

            return stp;
        }

        private void setSelectedPixel(TestPoint stp, DTDataManager dtDataManager)
        {
            foreach (int i in pixels)
            {
                if (dtDataManager[pixelDataIndexMap[i]] == stp)
                {
                    selectedPixel = i;
                    break;
                }
            }
        }

        Image image;

        
        private Pen pen = new Pen(Color.Black, 2);// pen for X,Y line
        private Pen penR = new Pen(Color.Black, 1);// pen for ruler
        private int widthEdgeLeft = 25;//坐标区间到窗口左边的距离
        private int widthEdgeRight = 100;
        private int heightEdgeTop = 25; //坐标区间到窗口上边的距离
        private int heightEdgeBottom = 55;//
        private int widthArrow = 4;//箭头半宽
        private int heightArrow = 5;//箭头高度
        float blank = 7f;               //坐标轴与字符串之间空白距离
        float scaleLen = 5f;            //刻度线长度
        private void drawLabel(Graphics dc)
        {
            dc.DrawString(XYData.TargetX + "  VS  " + XYData.TargetY, new Font("Arial", 10, FontStyle.Bold), new SolidBrush(Color.Red), new PointF(25, 4));
            dc.DrawString(XYData.TargetX, new Font("Arial", 10), new SolidBrush(Color.Red), new PointF(25 + (panel.Width - 125) / 2 - 3 * XYData.TargetX.Length, panel.Height - 25));
            dc.DrawString(XYData.TargetY, new Font("Arial", 10), new SolidBrush(Color.Red), new PointF(panel.Width - 25, 25 + (panel.Height - 80) / 2 - 3 * XYData.TargetY.Length), new StringFormat(StringFormatFlags.DirectionVertical));
            dc.DrawString("采样点个数：" + dataxy.Count.ToString("N"), new Font("Arial", 8), new SolidBrush(Color.Red), new PointF(panel.Width - 70 - 7 * dataxy.Count.ToString("N").Length, panel.Height - 14));
        }
        private void getXY(out float x, out float y)
        {
            if (XYData.Xcheckbox)  //x轴居中
            {
                y = (panel.Height - (heightEdgeBottom+heightEdgeTop)) / 2 + heightEdgeTop;
            }
            else  //x轴不居中
            {
                if ((XYData.Ylarge - XYData.Ysmall) != 0)
                {
                    y = heightEdgeTop + (panel.Height - (heightEdgeBottom + heightEdgeTop)) * (XYData.Ycenter - XYData.Ysmall) / (XYData.Ylarge - XYData.Ysmall);
                }
                else
                    y = heightEdgeTop;
            }

            if (XYData.Ycheckbox)
            {
                x = widthEdgeLeft + (panel.Width - (widthEdgeRight + widthEdgeLeft)) / 2;
            }
            else
            {
                if ((XYData.Xlarge - XYData.Xsmall) != 0)
                {
                    x = widthEdgeLeft + (panel.Width - (widthEdgeRight + widthEdgeLeft)) * (XYData.Xcenter - XYData.Xsmall) / (XYData.Xlarge - XYData.Xsmall);
                }
                else
                    x = widthEdgeLeft;
            }
        }
        private void drawXLine(Graphics dc, PointF start, PointF end, int arrowX)
        {
            dc.DrawLine(pen, start,end);
            dc.DrawLine(pen, end, new PointF(arrowX, end.Y - widthArrow));
            dc.DrawLine(pen, end, new PointF(arrowX, end.Y + widthArrow));
        }
        private void drawYLine(Graphics dc, PointF start, PointF end, int arrowY)
        {
            dc.DrawLine(pen, start, end);
            dc.DrawLine(pen, end, new PointF(end.X-widthArrow, arrowY));
            dc.DrawLine(pen, end, new PointF(end.X + widthArrow, arrowY));
        }
        private void drawXRLine(Graphics dc, PointF start, PointF end, PointF pointCenter)
        {
            string strCenter = XYData.Xcenter.ToString("N");
            Font font = new Font("Arial", 10);
            SolidBrush b = new SolidBrush(Color.Black);
            dc.DrawLine(penR, start, end);
            if (XYData.Ycheckbox)//y轴居中
            {
                dc.DrawString(strCenter, font, b, new PointF(pointCenter.X - dc.MeasureString(strCenter, font).Width / 2, pointCenter.Y + blank));
            }
        }
        private void drawYRLine(Graphics dc, PointF start, PointF end, PointF pointCenter)
        {
            string strCenter = XYData.Ycenter.ToString("N");
            Font font = new Font("Arial", 10);
            SolidBrush b = new SolidBrush(Color.Black);
            float yOffset = dc.MeasureString("1", font).Height/2;
            dc.DrawLine(penR, start, end);
            if (XYData.Xcheckbox)  //x轴居中
            {
                dc.DrawString(strCenter, font, b, new PointF(pointCenter.X + blank, pointCenter.Y - yOffset/2));
            }
        }
        private void drawScale(Graphics dc)
        {
            if (XYData.ListScaleItemX.Count < 2)
            {
                XYData.InitScaleItem(XYData.ListScaleItemX, XYData.Xsmall, XYData.Xlarge);
                XYData.InitScaleItem(XYData.ListScaleItemY, XYData.Ysmall, XYData.Ylarge);
            }
            Font font = new Font("Arial", 10);
            SolidBrush brush = new SolidBrush(Color.Black);
            //坐标线的刻度线
            float XlenL = 0.00f;
            float YlenL = 0.00f;

            float xR = panel.Width - widthEdgeRight;
            float yR = panel.Height - heightEdgeBottom;
            float widthArea = panel.Width - widthEdgeLeft - widthEdgeRight;
            float heigthArea = panel.Height - (heightEdgeTop + heightEdgeBottom);
            float yOffset = dc.MeasureString("1", font).Height / 2;
            drawYCoordinate(dc, font, brush, YlenL, xR, heigthArea, yOffset);
            drawXCoordinate(dc, font, brush, XlenL, yR, widthArea);
        }

        private void drawYCoordinate(Graphics dc, Font font, SolidBrush brush, float YlenL, float xR, float heigthArea, float yOffset)
        {
            //画Y坐标尺
            if (XYData.Xcheckbox)  //x轴居中
            {
                for (int j = 0; j < 5; j++)
                {
                    dc.DrawLine(penR, new PointF(xR, heightEdgeTop + YlenL), new PointF(xR + scaleLen, heightEdgeTop + YlenL));
                    YlenL += heigthArea / 4;
                }
                float smY = heightEdgeTop;
                float laY = heightEdgeTop + heigthArea;
                if (XYData.Ycomboboxindex == 1)
                {
                    smY += heigthArea;
                    laY -= heigthArea;
                }
                string str = XYData.Ysmall.ToString("N");
                dc.DrawString(str, font, brush, new PointF(xR + scaleLen + blank, smY - yOffset));
                str = XYData.Ylarge.ToString("N");
                dc.DrawString(str, font, brush, new PointF(xR + scaleLen + blank, laY - yOffset));
            }
            else
            {
                int direction = 1;
                float step = 0;
                if (XYData.Ylarge != XYData.Ysmall)
                {
                    step = heigthArea / (XYData.Ylarge - XYData.Ysmall);
                }
                if (XYData.Ycomboboxindex == 1)
                {
                    direction = -1;
                    YlenL = heigthArea;
                }
                for (int j = 0; j < XYData.ListScaleItemY.Count; j++)
                {
                    string str = XYData.ListScaleItemY[j].Val.ToString("N");
                    dc.DrawLine(penR, new PointF(xR, heightEdgeTop + YlenL), new PointF(xR + scaleLen, heightEdgeTop + YlenL));
                    dc.DrawString(str, font, brush, new PointF(xR + scaleLen + blank, heightEdgeTop + YlenL - yOffset));
                    if (j < XYData.ListScaleItemY.Count - 1)
                    {
                        YlenL += step * (XYData.ListScaleItemY[j + 1].Val - XYData.ListScaleItemY[j].Val) * direction;
                    }
                }
            }
        }

        private void drawXCoordinate(Graphics dc, Font font, SolidBrush brush, float XlenL, float yR, float widthArea)
        {
            //画X坐标尺
            if (XYData.Ycheckbox)  //y轴居中
            {
                for (int i = 0; i < 5; i++)
                {
                    dc.DrawLine(penR, new PointF(widthEdgeLeft + XlenL, yR), new PointF(widthEdgeLeft + XlenL, yR + scaleLen));
                    XlenL += widthArea / 4;
                }
                float smX = widthEdgeLeft;
                float laX = widthEdgeLeft + widthArea;
                if (XYData.Xcomboboxindex == 1)
                {
                    smX += widthArea;
                    laX -= widthArea;
                }
                string str = XYData.Xsmall.ToString("N");
                dc.DrawString(str, font, brush, new PointF(smX - dc.MeasureString(str, font).Width / 2, yR + scaleLen + blank));
                str = XYData.Xlarge.ToString("N");
                dc.DrawString(str, font, brush, new PointF(laX - dc.MeasureString(str, font).Width / 2, yR + scaleLen + blank));
            }
            else
            {
                int direction = 1;
                float step = 0;
                if (XYData.Xlarge != XYData.Xsmall)
                {
                    step = widthArea / (XYData.Xlarge - XYData.Xsmall);
                }
                if (XYData.Xcomboboxindex == 1)
                {
                    direction = -1;
                    XlenL = widthArea;
                }
                for (int j = 0; j < XYData.ListScaleItemX.Count; j++)
                {
                    string str = XYData.ListScaleItemX[j].Val.ToString("N");
                    dc.DrawLine(penR, new PointF(widthEdgeLeft + XlenL, yR), new PointF(widthEdgeLeft + XlenL, yR + scaleLen));
                    dc.DrawString(str, font, brush, new PointF(widthEdgeLeft + XlenL - dc.MeasureString(str, font).Width / 2, yR + scaleLen + blank));
                    if (j < XYData.ListScaleItemX.Count - 1)
                    {
                        XlenL += step * (XYData.ListScaleItemX[j + 1].Val - XYData.ListScaleItemX[j].Val) * direction;
                    }
                }
            }
        }

        private void addRandom(DataXY dtxy, ref PointF pointTp)
        {
            if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 0)//x轴从左向右递增，y轴从上向下
            {
                pointTp.X += dtxy.Xrandom;
                pointTp.Y += dtxy.Yrandom;
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 1)//x轴从左向右递增，y轴从下向上
            {
                pointTp.X += dtxy.Xrandom;
                pointTp.Y -= dtxy.Yrandom;
            }
            else if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 0)//x轴从右向左递增,y轴从上向下
            {
                pointTp.X -= dtxy.Xrandom;
                pointTp.Y += dtxy.Yrandom;
            }
            else if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 1)//x轴从右向左递增，y轴从下向上
            {
                pointTp.X -= dtxy.Xrandom;
                pointTp.Y -= dtxy.Yrandom;
            }
        }
        private void drawPercent(Graphics dc, float x, float y)
        {
            double firstcount = 100 * (float)XYData.FistCount / (float)dataxy.Count;
            double secondcount = 100 * (float)XYData.SecondCount / (float)dataxy.Count;
            double thirdcount = 100 * (float)XYData.ThirdCount / (float)dataxy.Count;
            double fourthcount = 100 - Math.Round(firstcount, 2) - Math.Round(secondcount, 2) - Math.Round(thirdcount, 2);
            Font font = new Font("Arial", 13, FontStyle.Bold);
            SolidBrush sb = new SolidBrush(Color.Black);
            string s1 = firstcount.ToString("f2") + "%";
            string s2 = secondcount.ToString("f2") + "%";
            string s3 = thirdcount.ToString("f2") + "%";
            string s4 = fourthcount.ToString("f2") + "%";
            float widthDiff = widthEdgeRight - widthEdgeLeft;
            float heightDiff = heightEdgeBottom - heightEdgeTop;
            if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 0)
            {
                dc.DrawString(s1, font, sb, new PointF((panel.Width - x - widthDiff) / 2, y / 2));
                dc.DrawString(s2, font, sb, new PointF(panel.Width - x / 2 - widthDiff, y / 2));
                dc.DrawString(s3, font, sb, new PointF(panel.Width - x / 2 - widthDiff, y + (panel.Height - y - heightEdgeBottom) / 2));
                dc.DrawString(s4, font, sb, new PointF((panel.Width - x - widthDiff) / 2, y + (panel.Height - y - heightEdgeBottom) / 2));
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 0)
            {
                dc.DrawString(s1, font, sb,new PointF((panel.Width - x - widthEdgeRight) / 2 + x, y / 2));
                dc.DrawString(s2, font, sb, new PointF(x / 2, y / 2));
                dc.DrawString(s3, font, sb, new PointF(x / 2, y + (panel.Height - y - heightEdgeBottom) / 2));
                dc.DrawString(s4, font, sb, new PointF(x + (panel.Width - x - widthEdgeRight) / 2, y + (panel.Height - y - heightEdgeBottom) / 2));
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 1)
            {
                dc.DrawString(s1, font, sb, new PointF((panel.Width - x - widthEdgeRight) / 2 + x, panel.Height - y / 2 - heightDiff));
                dc.DrawString(s2, font, sb, new PointF(x / 2, panel.Height - y / 2 - heightDiff));
                dc.DrawString(s3, font, sb, new PointF(x / 2, (panel.Height - y) / 2));
                dc.DrawString(s4, font, sb, new PointF(x + (panel.Width - x - widthEdgeRight) / 2, (panel.Height - y - heightDiff) / 2));
            }
            else
            {
                dc.DrawString(s1, font, sb, new PointF((panel.Width - x - widthDiff) / 2, panel.Height - y / 2 - heightDiff));
                dc.DrawString(s2, font, sb, new PointF(panel.Width - x / 2 - widthDiff, panel.Height - y / 2 - heightDiff));
                dc.DrawString(s3, font, sb, new PointF(panel.Width - x / 2 - widthDiff, (panel.Height - y - heightDiff) / 2));
                dc.DrawString(s4, font, sb, new PointF((panel.Width - x - widthDiff) / 2, (panel.Height - y - heightDiff) / 2));
            }
        }
        private void updateGraphDraw()
        {
            Bitmap bitmap = new Bitmap(panel.Width, panel.Height);
            Graphics dc = Graphics.FromImage(bitmap);
            dc.Clear(Color.White);

            this.dataxy = XYData.ListDataXY;
            this.drawLabel(dc);
            //箭头轴
            float x, y;
            this.getXY(out x, out y);//x轴的y位置、y轴的x位置 

            //坐标线
            float xR = panel.Width - widthEdgeRight;//y坐标线的X位置
            float yR = panel.Height - heightEdgeBottom;//x坐标线的Y位置
            PointF startX = new PointF(0, 0);//x坐标线的起点
            PointF centerX = startX;//x坐标线的中点
            PointF endX = startX;//x坐标线的终点
            PointF startY = startX;
            PointF centerY = startX;
            PointF endY = startX;

            if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 0)  //x轴从右向左递增,y轴从上向下
            {
                #region
                //画X轴
                this.drawXLine(dc, new PointF(panel.Width - widthEdgeRight, y), new PointF(widthEdgeLeft, y)
                                        , widthEdgeLeft + heightArrow);
                //画Y轴
                float xx = panel.Width - x - (widthEdgeRight - widthEdgeLeft);
                this.drawYLine(dc, new PointF(xx, heightEdgeTop), new PointF(xx, panel.Height - heightEdgeBottom)
                                        , panel.Height - heightEdgeBottom - heightArrow);
                //坐标线
                startX = new PointF(panel.Width - widthEdgeRight, yR);
                centerX = new PointF(xx, yR);
                endX = new PointF(widthEdgeLeft, yR);
                startY = new PointF(xR, heightEdgeTop);
                centerY = new PointF(xR, y);
                endY = new PointF(xR, panel.Height - heightEdgeBottom);
                #endregion
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 0) //x轴从左向右递增，y轴从上向下
            {
                #region
                //画x轴
                this.drawXLine(dc, new PointF(widthEdgeLeft, y), new PointF(panel.Width - widthEdgeRight, y), (panel.Width - widthEdgeRight - heightArrow));
                //画y轴
                this.drawYLine(dc, new PointF(x, heightEdgeTop), new PointF(x, panel.Height - heightEdgeBottom), panel.Height - heightEdgeBottom - heightArrow);

                //坐标线
                endX = new PointF(panel.Width - widthEdgeRight, yR);
                centerX = new PointF(x, yR);
                startX = new PointF(widthEdgeLeft, yR);
                startY = new PointF(xR, heightEdgeTop);
                centerY = new PointF(xR, y);
                endY = new PointF(xR, panel.Height - heightEdgeBottom);
                #endregion
            }
            else if (XYData.Xcomboboxindex == 0 && XYData.Ycomboboxindex == 1) //x轴从左向右递增，y轴从下向上
            {
                #region
                //画x轴
                float yy = panel.Height - y - (heightEdgeBottom - heightEdgeTop);
                this.drawXLine(dc, new PointF(widthEdgeLeft, yy),
                                                new PointF(this.panel.Width - widthEdgeRight, yy),
                                                this.panel.Width - widthEdgeRight - heightArrow);
                //画y轴
                this.drawYLine(dc, new PointF(x, panel.Height - heightEdgeBottom), new PointF(x, heightEdgeTop), heightEdgeTop + heightArrow);

                //坐标线
                endX = new PointF(panel.Width - widthEdgeRight, yR);
                centerX = new PointF(x, yR);
                startX = new PointF(widthEdgeLeft, yR);
                endY = new PointF(xR, heightEdgeTop);
                centerY = new PointF(xR, yy);
                startY = new PointF(xR, panel.Height - heightEdgeBottom);
                #endregion
            }
            else if (XYData.Xcomboboxindex == 1 && XYData.Ycomboboxindex == 1)//x轴从右向左递增，y轴从下向上
            {
                #region
                //画x轴
                float yy = panel.Height - y - (heightEdgeBottom - heightEdgeTop);
                this.drawXLine(dc, new PointF(this.panel.Width - widthEdgeRight, yy),
                                                new PointF(widthEdgeLeft, yy),
                                                widthEdgeLeft + heightArrow);
                //画y轴
                float xx = panel.Width - x - (widthEdgeRight - widthEdgeLeft);
                this.drawYLine(dc, new PointF(xx, panel.Height - heightEdgeBottom), new PointF(xx, heightEdgeTop), heightEdgeTop + heightArrow);

                //坐标线
                startX = new PointF(panel.Width - widthEdgeRight, yR);
                centerX = new PointF(xx, yR);
                endX = new PointF(widthEdgeLeft, yR);
                endY = new PointF(xR, heightEdgeTop);
                centerY = new PointF(xR, yy);
                startY = new PointF(xR, panel.Height - heightEdgeBottom);
                #endregion
            }
            //画坐标线
            this.drawXRLine(dc, startX, endX, centerX);
            this.drawYRLine(dc, startY, endY, centerY);
            //画刻度线
            this.drawScale(dc);
            //画点
            XYData.FistCount = 0;
            XYData.SecondCount = 0;
            XYData.ThirdCount = 0;
            XYData.FourthCount = 0;
            float radius = 4f;
            Brush brushTp = null;
            foreach (DataXY dtxy in XYData.ListDataXY)//dataxy
            {
                DrawPoint dp = new DrawPoint(dtxy.Xvalue, dtxy.Yvalue);
                brushTp = dp.getBrush(this.panel);
                PointF pointTp = new PointF(dp.getX(this.panel) - radius / 2, dp.getY(this.panel) - radius / 2);
                if (XYData.Pointcheckbox)
                {
                    this.addRandom(dtxy, ref pointTp);
                } 
                dc.FillEllipse(brushTp, pointTp.X, pointTp.Y, radius, radius);
            }
            this.drawPercent(dc, x, y);

            image = bitmap;
        }
         
        private void panel_Paint(object sender, PaintEventArgs e)
        {
            if (XYData.XsmallChanged && !(XYData.TarXchanged || XYData.TarYchanged))
            {
                updateGraphDraw();
                XYData.XsmallChanged = false;
            }
            else if (XYData.TarXchanged || XYData.TarYchanged)
            {
                dtDataChanged(null, null);
                XYData.TarXchanged = false;
                XYData.TarYchanged = false;
                XYData.XsmallChanged = false;
            }
            Graphics g = this.panel.CreateGraphics();
            g.DrawImage(image, new Rectangle(new Point(0, 0), new Size(panel.Width, panel.Height)));
        }
    }

    public class DataXY
    {
        public float Xvalue { get; set; }
        public float Yvalue { get; set; }
        public float Xrandom { get; set; }
        public float Yrandom { get; set; }
    }
    public class DrawPoint
    {
        private readonly float x;
        private readonly float y;
        public DrawPoint(float x, float y)
        {
            this.x = x;
            this.y = y;
        }
        public Brush getBrush(Panel panel)
        {
            SolidBrush brush;
            if (XYData.Xcheckbox && XYData.Ycheckbox)   //x轴，y轴居中
            {
                return getCenteredXYBrush(panel, out brush);
            }
            else if (XYData.Xcheckbox && !XYData.Ycheckbox)  //x轴居中
            {
                return getCenteredXBrush(panel, out brush);
            }
            else if (!XYData.Xcheckbox && XYData.Ycheckbox)  //y轴居中
            {
                return getCenteredYBrush(panel, out brush);
            }
            else //x,y轴都没居中
            {
                return getNotCenteredBrush(out brush);
            }
        }

        private Brush getCenteredXYBrush(Panel panel, out SolidBrush brush)
        {
            if (getX(panel) <= 25 + (panel.Width - 125) / 2 && getY(panel) < 25 + (panel.Height - 80) / 2)     //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;
            }
            else if (getX(panel) > 25 + (panel.Width - 125) / 2 && getY(panel) <= 25 + (panel.Height - 80) / 2) //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;
            }
            else if (getX(panel) < 25 + (panel.Width - 125) / 2 && getY(panel) >= 25 + (panel.Height - 80) / 2) //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else   //第4象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        private Brush getCenteredXBrush(Panel panel, out SolidBrush brush)
        {
            if (x <= XYData.Xcenter && getY(panel) < 25 + (panel.Height - 80) / 2)  //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;
            }
            else if (x > XYData.Xcenter && getY(panel) <= 25 + (panel.Height - 80) / 2)  //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;
            }
            else if (x < XYData.Xcenter && getY(panel) >= 25 + (panel.Height - 80) / 2)  //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else        //第1象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        private Brush getCenteredYBrush(Panel panel, out SolidBrush brush)
        {
            if (getX(panel) <= 25 + (panel.Width - 125) / 2 && y < XYData.Ycenter)  //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;

            }
            else if (getX(panel) > 25 + (panel.Width - 125) / 2 && y <= XYData.Ycenter)  //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;

            }
            else if (getX(panel) < 25 + (panel.Width - 125) / 2 && y >= XYData.Ycenter)  //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else     //第1象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        private Brush getNotCenteredBrush(out SolidBrush brush)
        {
            if (x <= XYData.Xcenter && y < XYData.Ycenter)  //第2象限
            {
                brush = new SolidBrush(XYData.ColorSecond);
                XYData.SecondCount++;
                return brush;
            }
            else if (x > XYData.Xcenter && y <= XYData.Ycenter)  //第1象限
            {
                brush = new SolidBrush(XYData.ColorFirst);
                XYData.FistCount++;
                return brush;
            }
            else if (x < XYData.Xcenter && y >= XYData.Ycenter)  //第3象限
            {
                brush = new SolidBrush(XYData.ColorThird);
                XYData.ThirdCount++;
                return brush;
            }
            else   //第1象限
            {
                brush = new SolidBrush(XYData.ColorFourth);
                XYData.FourthCount++;
                return brush;
            }
        }

        public float getX(Panel panel)
        {
            float X;
            int width = panel.Width - 125;
            if (XYData.Ycheckbox)   //y轴居中
            {
                if (XYData.Xcomboboxindex == 1)  //箭头向左
                {
                    X = getCenteredLeftValue(width, XYData.Xlarge, XYData.Xsmall, XYData.Xcenter, x);
                }
                else                         //箭头向右
                {
                    X = getCenteredRightValue(width, XYData.Xlarge, XYData.Xsmall, XYData.Xcenter, x);
                }
            }
            else                         //y轴就是中心线位置
            {
                if (XYData.Xcomboboxindex == 1)   //箭头向左
                {
                    X = getNotCenteredRightValue(width, XYData.Xlarge, XYData.Xsmall, x);
                }
                else                          //箭头向右
                {
                    X = getNotCenteredLeftValue(width, XYData.Xlarge, XYData.Xsmall, x);
                }
            }
            return X;
        }

        public float getY(Panel panel)
        {
            float Y;
            int height = panel.Height - 80;
            if (XYData.Xcheckbox)   //x轴居中
            {
                if (XYData.Ycomboboxindex == 1)  //Y轴箭头向上
                {
                    Y = getCenteredLeftValue(height, XYData.Ylarge, XYData.Ysmall, XYData.Ycenter, y);
                }
                else                         //箭头向右
                {
                    Y = getCenteredRightValue(height, XYData.Ylarge, XYData.Ysmall, XYData.Ycenter, y);
                }
            }
            else                         //y轴居中
            {
                if (XYData.Ycomboboxindex == 1)   //箭头向左
                {
                    Y = getNotCenteredRightValue(height, XYData.Ylarge, XYData.Ysmall, y);
                }
                else                          //箭头向右
                {
                    Y = getNotCenteredLeftValue(height, XYData.Ylarge, XYData.Ysmall, y);
                }
            }
            return Y;
        }

        private float getCenteredLeftValue(int panelValue, float large, float small, float center, float data)
        {
            float value;
            if (data > center)    //y轴左边
            {
                if ((large - center) != 0)
                {
                    value = 25 + (panelValue / 2) * (large - data) / (large - center);
                }
                else
                {
                    value = 0;
                }
            }
            else
            {
                if ((center - small) != 0)
                {
                    value = 25 + (panelValue / 2) + (panelValue / 2 * (center - data) / (center - small));
                }
                else
                {
                    value = 0;
                }
            }

            return value;
        }

        private float getCenteredRightValue(int panelValue, float large, float small, float center, float data)
        {
            float value;
            if (data < center)
            {
                if ((center - small) != 0)
                {
                    value = 25 + panelValue / 2 * (data - small) / (center - small);
                }
                else
                {
                    value = 0;
                }
            }
            else
            {
                if ((large - center) != 0)
                {
                    value = 25 + panelValue / 2 + panelValue / 2 * (data - center) / (large - center);
                }
                else
                {
                    value = 0;
                }
            }

            return value;
        }

        private float getNotCenteredRightValue(int panelValue, float large, float small, float data)
        {
            float value;
            if ((large - small) != 0)
            {
                value = 25 + panelValue * (small - data) / (large - small);
            }
            else
                value = 0;
            return value;
        }

        private float getNotCenteredLeftValue(int panelValue, float large, float small, float data)
        {
            float value;
            if ((large - small) != 0)
            {
                value = 25 + panelValue * (data - small) / (XYData.Xlarge - small);
            }
            else
            {
                value = 0;
            }
            return value;
        }
    }

    public class ChartInfoManager : ICloneable
    {
        public ChartInfoManager(int chartCount)
        {
            chartInfos = new List<ChartInfo>(chartCount);
            for (int i = 0; i < chartCount; i++)
            {
                chartInfos.Add(new ChartInfo());
            }
        }

        public int ChartCount               //////////////////////
        {
            get { return chartInfos.Count; }
        }

        public ChartInfo this[int index]         ////////////////
        {
            get { return chartInfos[index]; }
            set { chartInfos[index] = value; }
        }

        public object Clone()
        {
            int count = chartInfos.Count;
            ChartInfoManager clone = new ChartInfoManager(count);
            for (int i = 0; i < count; i++)
            {
                clone[i] = this[i];
            }
            return clone;
        }

        private readonly List<ChartInfo> chartInfos;
    }

    public class ChartInfo : ICloneable
    {
        public string Name { get; set; }

        public List<ChartSerialInfo> SerialInfos           /////////////////
        {
            get { return serialInfos; }
        }

        public void ChangeMS(int ms)       ////////////////
        {
            for (int i = 0; i < SerialInfos.Count; i++)
            {
                SerialInfos[i].MS = ms;
            }
        }

        public object Clone()
        {
            ChartInfo clone = new ChartInfo();
            clone.Name = Name;
            for (int i = 0; i < SerialInfos.Count; i++)
            {
                clone.SerialInfos.Add((ChartSerialInfo)SerialInfos[i].Clone());
            }
            return clone;
        }

        private readonly List<ChartSerialInfo> serialInfos = new List<ChartSerialInfo>();
    }

    public class ChartSerialInfo : ICloneable
    {
        public float? GetValue(TestPoint testPoint)
        {
            object objectValue = testPoint[displayParam.Parameter];
            if (objectValue != null && DTParameterManager.GetInstance().CanConvertToFloat(objectValue, displayParam.Parameter.Info.ValueType))
            {
                float value = DTParameterManager.GetInstance().ConvertToFloat(objectValue, displayParam.Parameter.Info.ValueType);
                if (value >= displayParam.Info.ValueMin && value <= displayParam.Info.ValueMax)
                {
                    return value;
                }
            }

            return null;
        }

        public string Name
        {
            get
            {
                string text = displayParam.Info.Name;
                if (displayParam.Info.ArrayBounds > 1)
                {
                    text += "[" + displayParam.ArrayIndex + "]";
                }
                if (MS > 0)
                {
                    text += "(MS" + MS + ")";
                }
                return text;
            }
        }

        public DTDisplayParameter DisplayParam
        {
            get { return displayParam; }
            set
            {
                displayParam = value;
                if (displayParam != null && displayParam.Info != null)
                {
                    Min = displayParam.Info.ValueMin;
                    Max = displayParam.Info.ValueMax;
                }
            }
        }

        public int MS { get; set; } = 1;
        public float Max { get; set; }
        public float Min { get; set; }

        public object Clone()
        {
            ChartSerialInfo clone = new ChartSerialInfo();
            clone.DisplayParam = DisplayParam;
            clone.MS = MS;
            clone.Max = Max;
            clone.Min = Min;
            return clone;
        }

        private DTDisplayParameter displayParam;
    }
}

