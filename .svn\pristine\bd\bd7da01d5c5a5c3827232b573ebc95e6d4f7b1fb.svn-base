﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class AdjustToolQualRelateMutilCov : Form
    {
        GridMatrix<GridContent> gridContentMatrix;
        GridMatrix<ColorUnit> colorMatrix;
        int qualityDividingLine;
        int multiCoverageDividingLine;
        CoverDegreeType coverType;
        MainModel mainModel;
        public AdjustToolQualRelateMutilCov(MainModel mainModel, GridMatrix<GridContent> gridContentMatrix, GridMatrix<ColorUnit> colorMatrix, int qualityDividingLine, int multiCoverageDividingLine, CoverDegreeType coverType)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.gridContentMatrix = gridContentMatrix;
            this.colorMatrix = colorMatrix;
            this.qualityDividingLine = qualityDividingLine;
            this.multiCoverageDividingLine = multiCoverageDividingLine;
            this.coverType = coverType;
            InitValue();
        }

        private void InitValue()
        {
            this.numQualityDividingLine.Value = qualityDividingLine;
            this.numMultiCoverageDividingLine.Value = multiCoverageDividingLine;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            CalculateColor((int)numQualityDividingLine.Value, (int)numMultiCoverageDividingLine.Value);

            mainModel.CurGridColorUnitMatrix = colorMatrix;
            mainModel.IsPrepareWithoutGridPartParam = true;
            mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = null;

            MapGridLayer.NeedFreshFullImg = true;
            mainModel.FireGridCoverQueried(this);

            setLegendType();
            mainModel.RefreshLegend();

            mainModel.IsPrepareWithoutGridPartParam = false; //查询结束，回复原始的布尔值，不影响其他栅格绘图
        }

        private void CalculateColor(int qualityDividingLine, int multiCoverageDividingLine)
        {
            this.qualityDividingLine = qualityDividingLine;
            this.multiCoverageDividingLine = multiCoverageDividingLine;
            foreach (ColorUnit cu in colorMatrix)
            {
                GridContent gc = gridContentMatrix[cu.RowIdx, cu.ColIdx];
                if (gc != null)
                {
                    #region 显示与GridContent相应的栅格颜色
                    if (gc.EnableCompare)
                    {
                        if (coverType == CoverDegreeType.Relative)
                        {
                            setRelativeColor(qualityDividingLine, multiCoverageDividingLine, cu, gc);
                        }
                        else if (coverType == CoverDegreeType.Absolute)
                        {
                            setAbsoluteColor(qualityDividingLine, multiCoverageDividingLine, cu, gc);
                        }
                        else if (coverType == CoverDegreeType.RelAndAbs)
                        {
                            setRelAndAbsColor(qualityDividingLine, multiCoverageDividingLine, cu, gc);
                        }
                    }
                    else //数据缺失，栅格无法比较
                    {
                        cu.color = Color.Transparent;
                    }
                    #endregion
                }
            }
        }

        private static void setRelativeColor(int qualityDividingLine, int multiCoverageDividingLine, ColorUnit cu, GridContent gc)
        {
            if (gc.rxquality0_5 < qualityDividingLine && gc.RelAvgCoverDegree < multiCoverageDividingLine)
            {
                cu.color = Color.Maroon;
            }
            else if (gc.rxquality0_5 >= qualityDividingLine && gc.RelAvgCoverDegree < multiCoverageDividingLine)
            {
                cu.color = Color.Blue;
            }
            else if (gc.rxquality0_5 < qualityDividingLine && gc.RelAvgCoverDegree >= multiCoverageDividingLine)
            {
                cu.color = Color.Yellow;
            }
            else if (gc.rxquality0_5 >= qualityDividingLine && gc.RelAvgCoverDegree >= multiCoverageDividingLine)
            {
                cu.color = Color.Green;
            }
        }

        private void setAbsoluteColor(int qualityDividingLine, int multiCoverageDividingLine, ColorUnit cu, GridContent gc)
        {
            if (gc.rxquality0_5 < qualityDividingLine && gc.AbsAvgCoverDegreeAvg < multiCoverageDividingLine)
            {
                cu.color = Color.Maroon;
            }
            else if (gc.rxquality0_5 >= qualityDividingLine && gc.AbsAvgCoverDegreeAvg < multiCoverageDividingLine)
            {
                cu.color = Color.Blue;
            }
            else if (gc.rxquality0_5 < qualityDividingLine && gc.AbsAvgCoverDegreeAvg >= multiCoverageDividingLine)
            {
                cu.color = Color.Yellow;
            }
            else if (gc.rxquality0_5 >= qualityDividingLine && gc.AbsAvgCoverDegreeAvg >= multiCoverageDividingLine)
            {
                cu.color = Color.Green;
            }
        }

        private void setRelAndAbsColor(int qualityDividingLine, int multiCoverageDividingLine, ColorUnit cu, GridContent gc)
        {
            if (gc.rxquality0_5 < qualityDividingLine && gc.RelAndAbsAvgCoverDegree < multiCoverageDividingLine)
            {
                cu.color = Color.Maroon;
            }
            else if (gc.rxquality0_5 >= qualityDividingLine && gc.RelAndAbsAvgCoverDegree < multiCoverageDividingLine)
            {
                cu.color = Color.Blue;
            }
            else if (gc.rxquality0_5 < qualityDividingLine && gc.RelAndAbsAvgCoverDegree >= multiCoverageDividingLine)
            {
                cu.color = Color.Yellow;
            }
            else if (gc.rxquality0_5 >= qualityDividingLine && gc.RelAndAbsAvgCoverDegree >= multiCoverageDividingLine)
            {
                cu.color = Color.Green;
            }
        }

        private void setLegendType()
        {
            GridColorFixed gridColorFixed = new GridColorFixed();
            gridColorFixed.items = new List<GridColorFixedItem>();
            gridColorFixed.theme = "质量RxQuality0-5级占比 & 重叠覆盖度对比栅格";
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = "质量<" + qualityDividingLine + "% &" + "重叠覆盖度<" + multiCoverageDividingLine;
            item.color = Color.Maroon;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "质量>=" + qualityDividingLine + "% &" + "重叠覆盖度<" + multiCoverageDividingLine;
            item.color = Color.Blue;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "质量<" + qualityDividingLine + "% &" + "重叠覆盖度>=" + multiCoverageDividingLine;
            item.color = Color.Yellow;
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "质量>=" + qualityDividingLine + "% &" + "重叠覆盖度>=" + multiCoverageDividingLine;
            item.color = Color.Green;
            gridColorFixed.items.Add(item);

            mainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = gridColorFixed;
        }
    }
}
