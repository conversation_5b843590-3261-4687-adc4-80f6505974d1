﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTNRScanLowSinrRoad;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanLowSinrRoad : DIYAnalyseFilesOneByOneByRegion
    {
        NRScanLowSinrRoadCond cond = new NRScanLowSinrRoadCond();
        private List<NRLowSinrRoad> roadLst;

        public NRScanLowSinrRoad(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            roadLst = new List<NRLowSinrRoad>();
        }

        public override string Name
        {
            get { return "质差路段_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36011, this.Name);
        }

        protected override bool getCondition()
        {
            NRScanLowSinrRoadDlg dlg = new NRScanLowSinrRoadDlg();
            dlg.ResetDlg(cond);
            if (DialogResult.OK != dlg.ShowDialog())
            {
                return false;
            }
            cond = dlg.Condition;
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            roadLst = new List<NRLowSinrRoad>();
        }

        private bool isValid(TestPoint tp)
        {
            var isValid = isValidTestPoint(tp);
            if (!isValid)
            {
                return false;
            }

            float sinrMax = float.MinValue;
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var idx in groupDic.Values)
            {
                float? sinr = NRTpHelper.NrScanTpManager.GetCellSinr(tp, idx, true);
                float s = sinr == null ? float.MinValue : (float)sinr;
                sinrMax = Math.Max(s, sinrMax);
            }
            isValid = sinrMax != float.MinValue && sinrMax < cond.SINRMax;
            return isValid;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                List<TestPoint> tmpLst = new List<TestPoint>();
                foreach (TestPoint tp in testPointList)
                {
                    if (isValid(tp))
                    {
                        tmpLst.Add(tp);
                    }
                    else
                    {
                        dealRoad(tmpLst, fileDataManager.FileName);
                        tmpLst.Clear();
                    }
                }
                dealRoad(tmpLst, fileDataManager.FileName);
            }
        }

        private void dealRoad(List<TestPoint> tPntLst, string fileName)
        {
            double distance = 0;
            double duration = 0;
            TestPoint tpPrev = null;
            foreach (TestPoint tp in tPntLst)
            {
                if (tpPrev != null)
                {
                    distance += MathFuncs.GetDistance(tp.Longitude, tp.Latitude, tpPrev.Longitude, tpPrev.Latitude);
                    duration += tp.Time - tpPrev.Time;
                }
                tpPrev = tp;
            }
            if (distance >= cond.DistanceMin)
            {
                NRLowSinrRoad road = new NRLowSinrRoad(fileName, distance, duration);
                road.AddRange(tPntLst);
                roadLst.Add(road);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (NRLowSinrRoad road in roadLst)
            {
                foreach (TestPoint tp in road.TestPntLst)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                road.Calculate();
            }
        }

        protected override void fireShowForm()
        {
            mainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.SinrFullThemeName);

            NRScanLowSinrRoadForm resultForm = MainModel.GetInstance().CreateResultForm(typeof(NRScanLowSinrRoadForm)) as NRScanLowSinrRoadForm;
            resultForm.FillData(roadLst);
            resultForm.Visible = true;
            resultForm.BringToFront();
            roadLst = null;
        }
    }
}
