﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using DBDataViewer;

namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareCoverAna : ZTGridCompareCombineBase
    {
        public ZTGridCompareCoverAna(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "竞对弱覆盖低路段(按栅格)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22077, "查询");
        }

        /// <summary>
        /// 设置查询指标
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            if (!condition.CarrierTypes.Contains(1) || condition.CarrierTypes.Count != 2)
            {
                MessageBox.Show("运营商选择有误，请选择移动联通或者移动电信");
                return false;
            }

            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            List<string> formulaSet = new List<string>();
            formulaSet.Add("Lte_61210309");
            formulaSet.Add("Lte_61210403");
            formulaSet.Add("Lte_61210301");
            formulaSet.Add("Lte_61210401");
            formulaSet.Add("Lf_612D0309");
            formulaSet.Add("Lf_612D0403");
            formulaSet.Add("Lf_612D0301");
            formulaSet.Add("Lf_612D0401");

            statImgIDSet = getTriadIDIgnoreServiceType(formulaSet);
            return true;
        }
        
        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            gridCountInfo = new GridCountInfo();
            //gridCountInfo.IAllGridCount = MainModel.CurGridColorUnitMatrix.Grids.Count;
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType == "" || gridName.strGridName == "")
                {
                    continue;
                }
                gridCountInfo.IAllGridCount++;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                bool isNotValid = judgeValidStat(cu);
                if (isNotValid)
                {
                    continue;
                }

                float fHostRsrp = (float)cu.DataHub.CalcValueByFormula("Lte_61210309");
                float fHostSinr = (float)cu.DataHub.CalcValueByFormula("Lte_61210403");
                int iHostRsrpSample = (int)cu.DataHub.CalcValueByFormula("Lte_61210301");
                int iHostSinrSample = (int)cu.DataHub.CalcValueByFormula("Lte_61210401");
                float fGuestRsrp = (float)cu.DataHub.CalcValueByFormula("Lf_612D0309");
                float fGuestSinr = (float)cu.DataHub.CalcValueByFormula("Lf_612D0403");
                int iGuestRsrpSample = (int)cu.DataHub.CalcValueByFormula("Lf_612D0301");
                int iGuestSinrSample = (int)cu.DataHub.CalcValueByFormula("Lf_612D0401");

                isNotValid = judgeValidCount(iHostRsrpSample, iHostSinrSample, iGuestRsrpSample, iGuestSinrSample);
                if (isNotValid)
                {
                    continue;
                }

                gridCountInfo.ICompareGridCount++;

                isNotValid = judgeValidIndex(fHostRsrp, fHostSinr, fGuestRsrp, fGuestSinr);
                if (isNotValid)
                {
                    continue;
                }

                GridColorUnit griCu = new GridColorUnit();
                griCu.CuUnit = cu;
                griCu.FHostRsrp = fHostRsrp;
                griCu.IHostRsrpSample = iHostRsrpSample;
                griCu.FHostSinr = fHostSinr;
                griCu.IHostSinrSample = iHostSinrSample;
                griCu.FGuestRsrp = fGuestRsrp;
                griCu.IGuestRsrpSample = iGuestRsrpSample;
                griCu.FGuestSinr = fGuestSinr;
                griCu.IGuestSinrSample = iGuestSinrSample;
                griCu.CuUnit.DataHub = null;//降低内存

                if (!gridColorUnit.ContainsKey(gridName))
                {
                    gridColorUnit[gridName] = new GridMatrix<GridColorUnit>();
                }
                gridColorUnit[gridName][rAt, cAt] = griCu;
                cu.DataHub = null;
            }
            MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
        }

        private bool judgeValidStat(ColorUnit cu)
        {
            StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
            StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
            if (dataStatTDD != null)
            {
                gridCountInfo.IHostGridCount++;
            }
            if (dataStatFDD != null)
            {
                gridCountInfo.IGuestGridCount++;
            }
            if (dataStatTDD == null || dataStatFDD == null)
            {
                cu.DataHub = null;
                return true;
            }
            return false;
        }

        private static bool judgeValidCount(int iHostRsrpSample, int iHostSinrSample, int iGuestRsrpSample, int iGuestSinrSample)
        {
            return iHostRsrpSample <= 0 || iHostSinrSample <= 0 || iGuestRsrpSample <= 0 || iGuestSinrSample <= 0;
        }

        private bool judgeValidIndex(float fHostRsrp, float fHostSinr, float fGuestRsrp, float fGuestSinr)
        {
            return (fHostRsrp >= -110 && fHostSinr >= -3) || fGuestRsrp < -110 || fGuestSinr < -3;
        }

        /// <summary>
        /// 重写转化过程
        /// </summary>
        protected override GridCompareCombineInfo doChangGridCompareResult(GridCompareCombineBlock block)
        {
            if (block.Grids.Count == 0)
            {
                return null;
            }
            GridCompareCombineInfo gridItem = new GridCompareCombineInfo();
            double sumLng = 0;
            double sumLat = 0;
            double sumHostRsrpMo = 0;
            int sumHostRsrpBase = 0;
            double sumGuestRsrpMo = 0;
            int sumGuestRsrpBase = 0;
            double sumHostSinrMo = 0;
            int sumHostSinrBase = 0;
            double sumGuestSinrMo = 0;
            int sumGuestSinrBase = 0;
            StringBuilder sbLng = new StringBuilder();
            StringBuilder sbLat = new StringBuilder();
            foreach (GridColorUnit gridCU in block.Grids)
            {
                sumLng += gridCU.CuUnit.CenterLng;
                sumLat += gridCU.CuUnit.CenterLat;
                sbLng.Append(gridCU.CuUnit.CenterLng + ";");
                sbLat.Append(gridCU.CuUnit.CenterLat + ";");

                sumHostRsrpMo += gridCU.FHostRsrp * gridCU.IHostRsrpSample;
                sumHostRsrpBase += gridCU.IHostRsrpSample;
                sumGuestRsrpMo += gridCU.FGuestRsrp * gridCU.IGuestRsrpSample;
                sumGuestRsrpBase += gridCU.IGuestRsrpSample;

                sumHostSinrMo += gridCU.FHostSinr * gridCU.IHostSinrSample;
                sumHostSinrBase += gridCU.IHostSinrSample;
                sumGuestSinrMo += gridCU.FGuestSinr * gridCU.IGuestSinrSample;
                sumGuestSinrBase += gridCU.IGuestSinrSample;

                setStrFileName(gridItem, gridCU);
            }
            gridItem.StrLngList += sbLng.ToString();
            gridItem.StrLatList += sbLat.ToString();

            double dHostRsrpMeanVale = -999;
            double dGuestRsrpMeanVale = -999;
            double dHostSinrMeanVale = -999;
            double dGuestSinrMeanVale = -999;
            if (sumHostRsrpBase != 0)
            {
                dHostRsrpMeanVale = sumHostRsrpMo / sumHostRsrpBase;
            }
            if (sumHostSinrBase != 0)
            {
                dHostSinrMeanVale = sumHostSinrMo / sumHostSinrBase;
            }
            if (sumGuestRsrpBase != 0)
            {
                dGuestRsrpMeanVale = sumGuestRsrpMo / sumGuestRsrpBase;
            }
            if (sumGuestSinrBase != 0)
            {
                dGuestSinrMeanVale = sumGuestSinrMo / sumGuestSinrBase;
            }
            if (condition.CarrierTypes.Contains(1) && condition.CarrierTypes.Contains(2))
            {
                gridItem.StrCompareInfo = "劣于联通";
                gridItem.StrProblemInfo = "移动RSRP：" + dHostRsrpMeanVale.ToString("0.00") + "dBm，移动SINR：" + dHostSinrMeanVale.ToString("0.00") + "；联通RSRP："
                    + dGuestRsrpMeanVale.ToString("0.00") + "dBm，联通SINR：" + dGuestSinrMeanVale.ToString("0.00") + "dBm，连续栅格个数：" + block.Grids.Count + "个";
            }
            else if (condition.CarrierTypes.Contains(1) && condition.CarrierTypes.Contains(3))
            {
                gridItem.StrCompareInfo = "劣于电信";
                gridItem.StrProblemInfo = "移动RSRP：" + dHostRsrpMeanVale.ToString("0.00") + "dBm，移动SINR：" + dHostSinrMeanVale.ToString("0.00") + "；电信RSRP："
                    + dGuestRsrpMeanVale.ToString("0.00") + "dBm，电信SINR：" + dGuestSinrMeanVale.ToString("0.00") + "，连续栅格个数：" + block.Grids.Count + "个";
            }
            gridItem.DLng = sumLng / block.Grids.Count;
            gridItem.DLat = sumLat / block.Grids.Count;
            gridItem.StrProblemType = "覆盖问题";
            return gridItem;
        }

        private void setStrFileName(GridCompareCombineInfo gridItem, GridColorUnit gridCU)
        {
            CenterLongLat cll = new CenterLongLat(gridCU.CuUnit.CenterLng, gridCU.CuUnit.CenterLat);
            if (gridFileNameListDic.ContainsKey(cll))
            {
                StringBuilder sbName = new StringBuilder();
                foreach (string strFileName in gridFileNameListDic[cll])
                {
                    if (!sbName.ToString().Contains(strFileName))
                    {
                        sbName.Append(strFileName + ";");
                    }
                }
                gridItem.StrFileName = sbName.ToString();
            }
        }
    }
}
