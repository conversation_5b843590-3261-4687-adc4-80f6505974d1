﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTECellSetForm : MinCloseForm
    {
        Dictionary<LTECell, LteCellInfo> lteCellInfoDic;

        public LTECellSetForm()
            :base()
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<LTECell, LteCellInfo> lteCellInfoDic
            , Dictionary<string, LteCellInfo> notFindCellInfoDic)
        {
            this.lteCellInfoDic = lteCellInfoDic;
            List<LteCellInfo> lcInfoLst = new List<LteCellInfo>();
            int sn = 1;
            foreach (LteCellInfo lcInfo in lteCellInfoDic.Values)
            {
                lcInfo.SN = sn++;
                lcInfoLst.Add(lcInfo);
            }
            foreach (LteCellInfo lcInfo in notFindCellInfoDic.Values)
            {
                lcInfo.SN = sn++;
                lcInfoLst.Add(lcInfo);
            }
            this.gridControl1.DataSource = lcInfoLst;
            this.gridControl1.RefreshDataSource();
        }

        public void FillData(Dictionary<LTECell, LteFddCellInfo> lteCellInfoDic
            , Dictionary<string, LteFddCellInfo> notFindCellInfoDic)
        {
            BindingSource source = new BindingSource();
            List<LteFddCellInfo> lcInfoLst = new List<LteFddCellInfo>();
            int sn = 1;
            foreach (LteFddCellInfo lcInfo in lteCellInfoDic.Values)
            {
                lcInfo.SN = sn++;
                lcInfoLst.Add(lcInfo);
            }
            foreach (LteFddCellInfo lcInfo in notFindCellInfoDic.Values)
            {
                lcInfo.SN = sn++;
                lcInfoLst.Add(lcInfo);
            }
            source.DataSource = lcInfoLst;
            this.gridControl1.DataSource = source;
            this.gridControl1.RefreshDataSource();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            LteCellInfo lteCellInfo = gv.GetRow(gv.GetSelectedRows()[0]) as LteCellInfo;
            MainModel.SelectedLTECell = MainModel.GetInstance().CellManager.GetCurrentLTECell(lteCellInfo.Tac, lteCellInfo.Eci);
            MainModel.FireSelectedCellChanged(MainModel.MainForm);
            MainModel.MainForm.GetMapForm().GoToView(lteCellInfo.Longitude, lteCellInfo.Latitude, 5000);
        }
    }
}
