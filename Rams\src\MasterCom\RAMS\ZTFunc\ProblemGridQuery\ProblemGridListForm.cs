﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ProblemGridQuery
{
    public partial class ProblemGridListForm : MinCloseForm
    {
        public ProblemGridListForm()
            : base()
        {
            InitializeComponent();

            lvMOS.CanExpandGetter += delegate(object row)
            {
                return row is ProblemOrder;
            };
            lvMOS.ChildrenGetter += delegate(object row)
            {
                if (row is ProblemOrder)
                {
                    ProblemOrder s = row as ProblemOrder;
                    return s.Grids;
                }
                return null;
            };

            lvGSM.CanExpandGetter += delegate(object row)
            {
                return row is ProblemOrder;
            };
            lvGSM.ChildrenGetter += delegate(object row)
            {
                if (row is ProblemOrder)
                {
                    ProblemOrder s = row as ProblemOrder;
                    return s.Grids;
                }
                return null;
            };

            lvLTECvr.CanExpandGetter += delegate(object row)
            {
                return row is ProblemOrder;
            };
            lvLTECvr.ChildrenGetter += delegate(object row)
            {
                if (row is ProblemOrder)
                {
                    ProblemOrder s = row as ProblemOrder;
                    return s.Grids;
                }
                return null;
            };

            lvLTEDl.CanExpandGetter += delegate(object row)
            {
                return row is ProblemOrder;
            };
            lvLTEDl.ChildrenGetter += delegate(object row)
            {
                if (row is ProblemOrder)
                {
                    ProblemOrder s = row as ProblemOrder;
                    return s.Grids;
                }
                return null;
            };

            lvLTEUl.CanExpandGetter += delegate(object row)
            {
                return row is ProblemOrder;
            };
            lvLTEUl.ChildrenGetter += delegate(object row)
            {
                if (row is ProblemOrder)
                {
                    ProblemOrder s = row as ProblemOrder;
                    return s.Grids;
                }
                return null;
            };
            lvGSM.MouseDoubleClick += lv_MouseDoubleClick;
            lvLTECvr.MouseDoubleClick += lv_MouseDoubleClick;
            lvLTEDl.MouseDoubleClick += lv_MouseDoubleClick;
            lvLTEUl.MouseDoubleClick += lv_MouseDoubleClick;
            lvMOS.MouseDoubleClick += lv_MouseDoubleClick;
        }

        ProblemOrderGridLayer gridLayer = null;
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            gridLayer = mf.GetLayerBase(typeof(ProblemOrderGridLayer)) as ProblemOrderGridLayer;
        }


        void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListView lv = sender as TreeListView;
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            object x = info.RowObject;
            makeSureLayerVisible();
            gridLayer.Orders.Clear();
            if (x is ProblemOrder)
            {
                ProblemOrder order = x as ProblemOrder;
                gridLayer.Orders.Add(order);
                gridLayer.Grid = null;
                MapForm mf = MainModel.MainForm.GetMapForm();
                mf.GoToView(order.Bounds);
            }
            else if (x is ProblemGrid)
            {
                ProblemGrid g = x as ProblemGrid;
                gridLayer.Orders.Add(g.Order);
                gridLayer.Grid = g;
                MapForm mf = MainModel.MainForm.GetMapForm();
                mf.GoToView(g.Bounds);
            }
        }


        public void FillData(IEnumerable<ProblemOrder> orders)
        {
            addCol2ListView(lvGSM, "GSM问题栅格", orders);
            addCol2ListView(lvLTECvr, "LTE问题栅格_覆盖问题", orders);
            addCol2ListView(lvLTEDl, "LTE问题栅格_下载问题", orders);
            addCol2ListView(lvLTEUl, "LTE问题栅格_上传问题", orders);
            addCol2ListView(lvMOS, "VoLTE问题栅格_MOS问题", orders);
        }

        private void addCol2ListView(TreeListView lv, string orderType, IEnumerable<ProblemOrder> orders)
        {
            foreach (OLVColumn oldCol in lv.Columns)
            {
                lv.AllColumns.Remove(oldCol);
                lv.Columns.Remove(oldCol);
                oldCol.Dispose();
            }

            setProblemOrder(lv);

            OLVColumn col = new OLVColumn();
            col.Text = "小区";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).Cells;
                }
                else if (row is ProblemGrid)
                {
                    return (row as ProblemGrid).CellNames;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "文件";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).FileNames;
                }
                else if (row is ProblemGrid)
                {
                    return (row as ProblemGrid).FileNames;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "栅格编号";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemGrid)
                {
                    return (row as ProblemGrid).GridSN;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "中心经度";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).CenterLng;
                }
                else if (row is ProblemGrid)
                {
                    return (row as ProblemGrid).CenterLng;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "中心纬度";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).CenterLat;
                }
                else if (row is ProblemGrid)
                {
                    return (row as ProblemGrid).CenterLat;
                }
                return null;
            };

            List<ProblemOrder> orderSet = new List<ProblemOrder>();
            foreach (ProblemOrder order in orders)
            {
                if (order.ProbType == orderType)
                {
                    orderSet.Add(order);
                }
            }
            lv.ClearObjects();
            lv.SetObjects(orderSet);
            if (orderSet.Count == 0)
            {
                lv.EmptyListMsg = "无数据！";
                return;
            }
            ProblemGrid grid = orderSet[0].Grids[0];

            foreach (string name in grid.KPIDic.Keys)
            {
                OLVColumn colx = new OLVColumn();
                colx.Text = name;
                colx.Tag = name;
                lv.AllColumns.Add(colx);
                lv.Columns.AddRange(new ColumnHeader[] { colx });
                setColumnAspectGetter(colx);
            }
            lv.RebuildColumns();
        }

        private void setProblemOrder(TreeListView lv)
        {
            OLVColumn col = new OLVColumn();
            col.Text = "地市";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).CityName;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "ID";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).ID;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "网格号";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).AreaName;
                }
                return null;
            };

            col = new OLVColumn();
            col.Text = "栅格个数";
            lv.AllColumns.Add(col);
            lv.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                if (row is ProblemOrder)
                {
                    return (row as ProblemOrder).GridCount;
                }
                return null;
            };
        }

        private void setColumnAspectGetter(OLVColumn colx)
        {
            colx.AspectGetter = delegate (object row)
            {
                if (row is ProblemGrid)
                {
                    ProblemGrid g = row as ProblemGrid;
                    double retVal;
                    if (!g.KPIDic.TryGetValue(colx.Tag.ToString(), out retVal))
                    {
                        return null;
                    }
                    return retVal;
                }
                return null;
            };
        }

        TreeListView getCurLv()
        {
            return this.tabCtrl.SelectedTabPage.Controls[0] as TreeListView;
        }

        private void miShowAll_Click(object sender, EventArgs e)
        {
            TreeListView lv = getCurLv();
            makeSureLayerVisible();
            List<ProblemOrder> list = new List<ProblemOrder>();
            foreach (ProblemOrder order in lv.Roots)
            {
                list.Add(order);
            }
            if (list.Count == 0)
            {
                return;
            }
            gridLayer.Orders = list;
            gridLayer.Grid = null;

            DbRect bounds = null;
            foreach (ProblemOrder order in gridLayer.Orders)
            {
                if (bounds == null)
                {
                    bounds = order.Bounds;
                }
                else
                {
                    bounds.MergeRects(order.Bounds);
                }
            }
            MapForm mf = MainModel.MainForm.GetMapForm();
            mf.GoToView(bounds);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            TreeListView lv = getCurLv();
            lv.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            TreeListView lv = getCurLv();
            lv.CollapseAll();
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            TreeListView lv = getCurLv();
            ExcelNPOIManager.ExportToExcel(lv);
        }



      
    }
}
