﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFastFadingByRegion_W : DIYFastFadingByRegion_GSM
    {
        public DIYFastFadingByRegion_W(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_VOICE);
            ServiceTypes.Add(ServiceType.WCDMA_DATA);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14022, this.Name);//////
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    dealTPInfo(testPointList);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTPInfo(List<TestPoint> testPointList)
        {
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
                    float? rxLevMain = (float?)testPoint["W_TotalRSCP"];
                    WCell mainCell = testPoint.GetMainCell_W();
                    if (mainCell != null)
                    {
                        cellRxLevDic[mainCell.Name] = (float)rxLevMain;
                        judgeCell(rxLevMain, mainCell.Name, testPointList, i);
                    }

                    judgeTestPoint(testPointList, i, cellRxLevDic);
                }
                else
                {
                    clearIndermediateVariable();
                }
            }
        }
    }
}
