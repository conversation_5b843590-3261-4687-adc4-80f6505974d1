﻿namespace MasterCom.RAMS.ZTFunc.ZTEpsfbDelayAna
{
    partial class EpsfbCallStatListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.bandedGridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1147, 453);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.toolStripSeparator1,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 54);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(138, 22);
            this.miReplay.Text = "回放文件...";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(135, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // bandedGridView
            // 
            this.bandedGridView.BandPanelRowHeight = 4;
            this.bandedGridView.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2});
            this.bandedGridView.ColumnPanelRowHeight = 4;
            this.bandedGridView.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn22,
            this.bandedGridColumn23,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            //this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            //this.bandedGridColumn9,
            //this.bandedGridColumn10,
            //this.bandedGridColumn11,
            this.bandedGridColumn24,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            //this.bandedGridColumn14,
            //this.bandedGridColumn15,
            //this.bandedGridColumn16,
            this.bandedGridColumn25,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn17,
            this.bandedGridColumn18});
            //this.bandedGridColumn19,
            //this.bandedGridColumn20,
            //this.bandedGridColumn21});
            this.bandedGridView.GridControl = this.gridControl;
            this.bandedGridView.Name = "bandedGridView";
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "Call";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 75;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "序号";
            this.bandedGridColumn1.FieldName = "Sn";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.RowCount = 4;
            this.bandedGridColumn1.Visible = true;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "EPSFB语音通话";
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn22);
            this.gridBand2.Columns.Add(this.bandedGridColumn23);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.bandedGridColumn5);
            //this.gridBand2.Columns.Add(this.bandedGridColumn6);
            this.gridBand2.Columns.Add(this.bandedGridColumn7);
            this.gridBand2.Columns.Add(this.bandedGridColumn8);
            //this.gridBand2.Columns.Add(this.bandedGridColumn9);
            //this.gridBand2.Columns.Add(this.bandedGridColumn10);
            //this.gridBand2.Columns.Add(this.bandedGridColumn11);
            this.gridBand2.Columns.Add(this.bandedGridColumn24);
            this.gridBand2.Columns.Add(this.bandedGridColumn12);
            this.gridBand2.Columns.Add(this.bandedGridColumn13);
            //this.gridBand2.Columns.Add(this.bandedGridColumn14);
            //this.gridBand2.Columns.Add(this.bandedGridColumn15);
            //this.gridBand2.Columns.Add(this.bandedGridColumn16);
            this.gridBand2.Columns.Add(this.bandedGridColumn25);
            this.gridBand2.Columns.Add(this.bandedGridColumn26);
            this.gridBand2.Columns.Add(this.bandedGridColumn27);
            this.gridBand2.Columns.Add(this.bandedGridColumn28);
            this.gridBand2.Columns.Add(this.bandedGridColumn17);
            this.gridBand2.Columns.Add(this.bandedGridColumn18);
            //this.gridBand2.Columns.Add(this.bandedGridColumn19);
            //this.gridBand2.Columns.Add(this.bandedGridColumn20);
            //this.gridBand2.Columns.Add(this.bandedGridColumn21);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 900;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "log名称";
            this.bandedGridColumn2.FieldName = "MoFileName";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.RowCount = 4;
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "回落时延(ms)";
            this.bandedGridColumn22.FieldName = "FBDelayTime";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.RowCount = 4;
            this.bandedGridColumn22.Visible = true;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "返回时延(ms)";
            this.bandedGridColumn23.FieldName = "FRDelayTime";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.RowCount = 4;
            this.bandedGridColumn23.Visible = true;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "起呼时间";
            this.bandedGridColumn3.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn3.FieldName = "CallAttemptTime";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.RowCount = 4;
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "回落前_经度";
            this.bandedGridColumn4.FieldName = "BeforeLng";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.RowCount = 4;
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "回落前_纬度";
            this.bandedGridColumn5.FieldName = "BeforeLat";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.RowCount = 4;
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "呼叫类型";
            this.bandedGridColumn6.FieldName = "BeforeNetType";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.RowCount = 4;
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "回落前_TAC";
            this.bandedGridColumn7.FieldName = "BeforeNrTac";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.RowCount = 4;
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "回落前_NCI";
            this.bandedGridColumn8.FieldName = "BeforeNrNci";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.RowCount = 4;
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "回落前_GNodeBID";
            this.bandedGridColumn9.FieldName = "BeforeGNodeBId";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.RowCount = 4;
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "回落前_CellID";
            this.bandedGridColumn10.FieldName = "BeforeCellId";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.RowCount = 4;
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "回落前_NR-RSRP(dBm)";
            this.bandedGridColumn11.FieldName = "BeforeSSRsrp";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.RowCount = 4;
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "回落小区_TAC";
            this.bandedGridColumn12.FieldName = "NowLteTac";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.RowCount = 4;
            this.bandedGridColumn12.Visible = true;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "回落小区_ECI";
            this.bandedGridColumn13.FieldName = "NowLteEci";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.RowCount = 4;
            this.bandedGridColumn13.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "回落小区_ENodeBID";
            this.bandedGridColumn14.FieldName = "NowENodeBId";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.RowCount = 4;
            this.bandedGridColumn14.Visible = true;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "回落小区_CellID";
            this.bandedGridColumn15.FieldName = "NowCellId";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.RowCount = 4;
            this.bandedGridColumn15.Visible = true;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "回落小区_LTE-RSRP";
            this.bandedGridColumn16.FieldName = "NowLteRsrp";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.RowCount = 4;
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "返回小区_TAC";
            this.bandedGridColumn17.FieldName = "AfterNrTac";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.RowCount = 4;
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "返回小区_NCI";
            this.bandedGridColumn18.FieldName = "AfterNrNci";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.RowCount = 4;
            this.bandedGridColumn18.Visible = true;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "返回小区_GNodeBID";
            this.bandedGridColumn19.FieldName = "AfterGNodeBId";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.RowCount = 4;
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "返回小区_CellID";
            this.bandedGridColumn20.FieldName = "AfterCellId";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.RowCount = 4;
            this.bandedGridColumn20.Visible = true;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "返回小区_NR-RSRP";
            this.bandedGridColumn21.FieldName = "AfterSSRsrp";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.RowCount = 4;
            this.bandedGridColumn21.Visible = true;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "回落成功时间";
            this.bandedGridColumn24.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn24.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn24.FieldName = "FBSuccTime";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.RowCount = 4;
            this.bandedGridColumn24.Visible = true;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "通话结束时间";
            this.bandedGridColumn25.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn25.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn25.FieldName = "CallEndTime";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.RowCount = 4;
            this.bandedGridColumn25.Visible = true;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "返回成功时间";
            this.bandedGridColumn26.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn26.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn26.FieldName = "FRSuccTime";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.RowCount = 4;
            this.bandedGridColumn26.Visible = true;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "返回_经度";
            this.bandedGridColumn27.FieldName = "AfterLng";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.RowCount = 4;
            this.bandedGridColumn27.Visible = true;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "返回_纬度";
            this.bandedGridColumn28.FieldName = "AfterLat";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.RowCount = 4;
            this.bandedGridColumn28.Visible = true;

            // 
            // gridBand3
            // 
            //this.gridBand3.Caption = "被叫";
            //this.gridBand3.Name = "gridBand3";
            //this.gridBand3.Width = 75;
            // 
            // EpsfbCallStatListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1147, 453);
            this.Controls.Add(this.gridControl);
            this.Name = "EpsfbCallStatListForm";
            this.Text = "EPSFB主被叫统计";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView;

        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;

        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;    // 序号
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;    // log名称
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;    // 起呼时间
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;    // 经度
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;    // 纬度
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;    // 呼叫类型
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;    // TAC
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;    // NCI
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;    // ENodeBID
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;   // CellID
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;   // NR RSRP
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;   // 回落 TAC
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;   // 回落 ECI
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;   // 回落 ENodeBID
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;   // 回落 CellID
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;   // 回落 LTE RSRP
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;   // 返回 TAC
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;   // 返回 NCI
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;   // 返回 ENodeBID
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;   // 返回 CellID
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;   // 返回 NR RSRP
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;   // EPSFB 回落时延
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;   // EPSFB 返回时延
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;   // 回落成功时间
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;   // 通话结束时间
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;   // 返回成功时间
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;   // 返回 经度
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;   // 返回 纬度


        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
    }
}