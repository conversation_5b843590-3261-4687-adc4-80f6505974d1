﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public partial class MultiInStaionAcceptResultForm : MinCloseForm
    {
        List<InDoorBtsAcceptInfo> inDoorBtsAcceptInfoList;
        public MultiInStaionAcceptResultForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
        }

        public void FillData(List<InDoorBtsAcceptInfo> InDoorBtsAcceptInfos)
        {
            inDoorBtsAcceptInfoList = InDoorBtsAcceptInfos;
            gridControl1.DataSource = InDoorBtsAcceptInfos;
            gridControl1.RefreshDataSource();
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(GetNPOIRows(inDoorBtsAcceptInfoList));
        }

        private void tsMenuItemExpand_Click(object sender, EventArgs e)
        {
            for (int row = 0; row < gvResult.RowCount; row++)
            {
                gvResult.SetMasterRowExpanded(row, true);
            }
        }

        private void tsMenuItemCollapse_Click(object sender, EventArgs e)
        {
            gvResult.CollapseAllDetails();
        }

        public static List<NPOIRow> GetNPOIRows(List<InDoorBtsAcceptInfo> btsAcceptInfoList)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("日期");
            row.AddCellValue("基站名称");
            row.AddCellValue("基站ID");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("基站是否通过验收");

            row.AddCellValue("小区Id");
            row.AddCellValue("小区是否通过验收");
            row.AddCellValue("FTP下载");
            row.AddCellValue("FTP上传");
            row.AddCellValue("RRC Setup Success Rate");
            row.AddCellValue("ERAB Setup Success Rate");
            row.AddCellValue("Access Success Rate");
            row.AddCellValue("CSFB呼叫成功率");
            row.AddCellValue("楼层覆盖范围");
            rows.Add(row);
            foreach (InDoorBtsAcceptInfo btsInfo in btsAcceptInfoList)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(btsInfo.SN);
                row.AddCellValue(btsInfo.Time);
                row.AddCellValue(btsInfo.BtsName);
                row.AddCellValue(btsInfo.BtsId);
                row.AddCellValue(btsInfo.Longitude);
                row.AddCellValue(btsInfo.Latitude);
                row.AddCellValue(btsInfo.IsAccordAcceptStr);

                foreach (InDoorCellAcceptInfo cellInfo in btsInfo.CellsAcceptDic.Values)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(cellInfo.CellId);
                    subRow.AddCellValue(cellInfo.IsAccordDes);
                    subRow.AddCellValue(cellInfo.IsFtpDlAccordDes);
                    subRow.AddCellValue(cellInfo.IsFtpUlAccordDes);
                    subRow.AddCellValue(cellInfo.RrcInfo);
                    subRow.AddCellValue(cellInfo.ErabInfo);
                    subRow.AddCellValue(cellInfo.AccessInfo);
                    subRow.AddCellValue(cellInfo.CsfbInfo);
                    subRow.AddCellValue(cellInfo.CoveredFloorDes);
                }
            }
            return rows;
        }
    }
}
