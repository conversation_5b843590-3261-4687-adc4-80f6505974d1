﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTCellMultiCoverage_NBIOT : ZTCellMultiCoverage_LTE
    {
        public ZTCellMultiCoverage_NBIOT(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "小区重叠覆盖度分析_NB扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33011, this.Name);
        }
    }
}
