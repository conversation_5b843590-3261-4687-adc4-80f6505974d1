﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellReverseResultWrongForm : MinCloseForm
    {
        public NRCellReverseResultWrongForm()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(Dictionary<string, List<NRCellWrongDir>> wrongList, bool isTwiceBatch)
        {
            gcCompetedResult.Visible = isTwiceBatch;
            
            bindData(wrongList, isTwiceBatch);

            refreshTestPoint(wrongList);
        }
        
        private void bindData(Dictionary<string, List<NRCellWrongDir>> wrongList, bool isTwiceBatch)
        {
            var lstWrongBind = new List<NRCellWrongDir>();

            foreach (var btsWrong in wrongList)
            {
                if (btsWrong.Value.Count <= 1)
                {
                    continue;
                }

                var btsName = btsWrong.Key;
                foreach (var cellWrong in btsWrong.Value)
                {
                    var firstBatch = cellWrong.Clone();
                    firstBatch.BtsName = btsName;
                    firstBatch.cellWrongBatch = CellWrongBatch.First;
                    lstWrongBind.Add(firstBatch);
                }
            }
            gridControl.DataSource = lstWrongBind;
            gridControl.RefreshDataSource();
        }

        private void refreshTestPoint(Dictionary<string, List<NRCellWrongDir>> wrongList)
        {
            MainModel.DTDataManager.Clear();
            foreach (var btsWrong in wrongList)
            {
                foreach (var cellWrong in btsWrong.Value)
                {
                    foreach (var tp in cellWrong.resultFirstBatch.WrongPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    foreach (TestPoint tp in cellWrong.resultSecondBatch.WrongPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void refreshTestPoint(NRCellWrongDir cellWrong)
        {
            MainModel.DTDataManager.Clear();

            foreach (TestPoint tp in cellWrong.resultFirstBatch.WrongPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (TestPoint tp in cellWrong.resultSecondBatch.WrongPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }

            MainModel.FireDTDataChanged(this);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.SelectedTDCell = null;
            int[] hds = gridView.GetSelectedRows();
            object row = null;
            if (hds.Length > 0)
            {
                row = gridView.GetRow(hds[0]);
            }
            else
            {
                return;
            }

            NRCellWrongDir wrongCell = null;
            if (row is NRCellWrongDir)
            {
                var wrongNRCell = row as NRCellWrongDir;
                wrongCell = wrongNRCell;
                MainModel.SetSelectedNRCell(wrongNRCell.Cell);
            }
            else
            {
                return;
            }

            refreshTestPoint(wrongCell);

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GoToView(wrongCell.Longitude, wrongCell.Latitude);
            }
        }
    }
}
