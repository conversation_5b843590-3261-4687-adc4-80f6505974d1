﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.ES.ColorManager;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class BlackBlock_PointDetailForm : BaseDialog
    {
        public BlackBlock_PointDetailForm()
        {
            InitializeComponent();
        }

        public void FillData(BlackBlock_Point bb)
        {
            edtBlockID.Text = bb.ID.ToString();
            BindingSource source = new BindingSource();
            source.DataSource = bb.Events;
            gridControlEvent.DataSource = source;
            gridControlEvent.RefreshDataSource();
            source = new BindingSource();
            source.DataSource = bb.ValidateDates;
            gridControlDate.DataSource = source;
            gridControlDate.RefreshDataSource();
            source = new BindingSource();
            source.DataSource = bb.EventsValidate;
            gridControlEventValidate.DataSource = source;
            gridControlEventValidate.RefreshDataSource();
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            if (gridViewEvent.IsFocusedView)
            {
                if (gridViewEvent.SelectedRowsCount > 0)
                {
                    BlackBlock_Point_Event be = gridViewEvent.GetRow(gridViewEvent.GetSelectedRows()[0]) as BlackBlock_Point_Event;
                    doReplay(be);
                }
            }
            else if (gridViewEventValidate.SelectedRowsCount > 0)
            {
                BlackBlock_Point_Event be = gridViewEventValidate.GetRow(gridViewEventValidate.GetSelectedRows()[0]) as BlackBlock_Point_Event;
                doReplay(be);
            }
        }

        private void doReplay(BlackBlock_Point_Event be)
        {
            Event eve = be.ConvertToEvent();
            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(eve);
        }

        private void gridViewEvent_DoubleClick(object sender, EventArgs e)
        {
            if (gridViewEvent.IsFocusedView)
            {
                if (gridViewEvent.SelectedRowsCount > 0)
                {
                    BlackBlock_Point_Event be = gridViewEvent.GetRow(gridViewEvent.GetSelectedRows()[0]) as BlackBlock_Point_Event;
                    locateEvent(be);
                }
            }
            else if (gridViewEventValidate.SelectedRowsCount > 0)
            {
                BlackBlock_Point_Event be = gridViewEventValidate.GetRow(gridViewEventValidate.GetSelectedRows()[0]) as BlackBlock_Point_Event;
                locateEvent(be);
            }
        }

        private void locateEvent(BlackBlock_Point_Event be)
        {
            Event eve = be.ConvertToEvent();
            MainModel mainModel = MainModel.GetInstance();
            mainModel.ClearDTData();
            mainModel.DTDataManager.Add(eve);
            eve.Selected = true;
            mainModel.SelectedEvents.Add(eve);
            mainModel.FireDTDataChanged(null);
        }
    }
}
