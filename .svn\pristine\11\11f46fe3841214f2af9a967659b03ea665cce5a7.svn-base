using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWFindInterferenceByRegion : ZTTDFindInterferenceByRegion
    {
        public ZTWFindInterferenceByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19041, this.Name);
        }
        protected override void query()
        {
            fireShowForm();
        }

        protected new void fireShowForm()
        {
            ZTWInterfereCellsForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTWInterfereCellsForm)) as ZTWInterfereCellsForm;
            frm.Owner = MainModel.MainForm;
            frm.Run();
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
