﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTPlanBtsAna : QueryBase
    {
        readonly List<CellCloudPictureData> cloudDataList = new List<CellCloudPictureData>();
        /// <summary>
        /// 当前的云图画图配置
        /// </summary>
        CellCloudPictureConfig curConfig { get; set; }
        public ZTPlanBtsAna(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        
        public override string Name
        {
            get { return ""; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string IconName
        {
            get { return "images/投诉管理/MR分析.png"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        ChooseDataTypeForm chooseForm = null;
        protected override void query()
        {
            cloudDataList.Clear();
            curConfig = null;
            if (chooseForm == null)
                chooseForm = new ChooseDataTypeForm();
            if (chooseForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    object[] objs = new object[3];
                    objs[0] = chooseForm.Data;
                    objs[1] = chooseForm.City;
                    objs[2] = chooseForm.Radius;
                    WaitBox.CanCancel = true;
                    WaitBox.Show("正在查询…", queryData, objs);
                }
                catch
                {
                    //continue
                }

                CellCloudPictureLayer layer = CellCloudPictureLayer.GetInstance();
                if (curConfig != null && cloudDataList.Count > 0)
                {
                    layer.Config = curConfig;
                    layer.Datas = cloudDataList;
                    layer.ClearCache();
                    System.Threading.Thread.Sleep(500);
                    mainModel.MainForm.GetMapForm().FireCellCloudPictureShow();
                }
                else
                {
                    MessageBox.Show("查询没合符条件数据！");
                }
            }
        }

        private void queryData(object o)
        {
            object[] objs = o as object[];
            string data = objs[0] as string;
            string city = objs[1] as string;
            int radius = (int)objs[2];
            switch (data)
            {
                case "MR数据良好覆盖率":
                    WaitBox.Text = "正在统计MR数据良好覆盖率…";
                    getMrData(city,"良好覆盖率");
                    break;
                case "MR数据弱覆盖率":
                    WaitBox.Text = "正在统计MR数据弱覆盖率…";
                    getMrData(city,"弱覆盖率");
                    break;
                case "倒流量情况":
                    WaitBox.Text = "正在统计倒流量情况…";
                    getBackFlow(city);
                    break;
                case "栅格竞争对比（移动绝对值）":
                    WaitBox.Text = "正在统计栅格竞争对比…";
                    getChinaMobileCompGrid(city, radius);
                    break;
                case "栅格竞争对比（移动与联通相对值）":
                    WaitBox.Text = "正在统计栅格竞争对比…";
                    getChinaUnicomCompGrid(city, radius);
                    break;
                case "用户投诉量":
                    WaitBox.Text = "正在统计用户投诉量…";
                    getComplainNum(city, radius);
                    break;
                case "高流量小区":
                    WaitBox.Text = "正在统计高流量小区…";
                    getHighFlow(city);
                    break;
                case "驻留率情况":
                    WaitBox.Text = "正在统计驻留率情况…";
                    getResidentRate(city);
                    break;
                case "持续差道路":
                    WaitBox.Text = "正在统计持续差道路…";
                    getLastWeakRoad(city, radius);
                    break;
                case "规划站评估":
                    WaitBox.Text = "正在统计规划站评估…";
                    getPlanningBtsScore(city, radius);
                    break;
                default:
                    break;
            }
            WaitBox.Close();
        }

        private void getMrData(string city,string showValue)
        {
            setConfigColor(showValue);

            PlanBtsDbHelper.DiySqlMrData querySql = new PlanBtsDbHelper.DiySqlMrData(mainModel, city);
            querySql.Query();

            int loop = 0;
            int count = querySql.mrDataDic.Values.Count;
            foreach (PlanBtsDbHelper.MrItem item in querySql.mrDataDic.Values)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                TDCell tdCell = CellManager.GetInstance().GetCurrentTDCell(item.ILac, item.ICi);
                if (tdCell == null)
                {
                    continue;
                }
                CellRadiusManager.Set(tdCell);

                double radius = CellRadiusManager.Get(item.ILac, item.ICi, CellType.TD);
                if (radius == -1)
                {
                    continue;
                }

                CellCloudPictureData data = new CellCloudPictureData();
                if (showValue == "良好覆盖率")
                {
                    data.Weight = item.FBetterCoverRate;
                }
                else if (showValue == "弱覆盖率")
                {
                    if (item.FWeakCoverRate > 0.05)
                        data.Weight = 0.05;
                    else
                        data.Weight = item.FWeakCoverRate;
                }
                data.Direction = tdCell.Direction;
                data.Longitude = tdCell.Longitude;
                data.Latitude = tdCell.Latitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void setConfigColor(string showValue)
        {
            if (showValue == "良好覆盖率")
            {
                curConfig = new CellCloudPictureConfig();
                curConfig.MinWeight = 0.2;
                curConfig.MaxWeight = 0.85;
                int[] colorZone =
                {
                    0x00FF0000,//蓝色,作为底色
                    0x000000ff,  //红色  
                    0x0000ffff,   //黄色
                    0x00ffff00,   //浅蓝
                    0x00FF0000, //蓝色
                };//蓝色到红色到蓝色
                curConfig.ColorZone = colorZone;
            }
            else if (showValue == "弱覆盖率")
            {
                curConfig = new CellCloudPictureConfig();
                curConfig.MinWeight = 0;
                curConfig.MaxWeight = 0.05;
                int[] colorZone =
                {
                    0x00FF0000, //蓝色 
                    0x00ffff00,   //浅蓝
                    0x0000ffff,   //黄色    
                    0x000000ff,  //红色  
                };//蓝色到红色
                curConfig.ColorZone = colorZone;
            }
        }

        private void getBackFlow(string city)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight = 0;
            curConfig.MaxWeight = 3;
            int[] colorZone = 
            { 
                0x00FF0000, //蓝色 
                0x00ffff00,   //浅蓝
                0x0000ffff,   //黄色    
                0x000000ff,  //红色  
            };//蓝色到红色
            curConfig.ColorZone = colorZone;
            PlanBtsDbHelper.DiySqlBackFlow querySql = new PlanBtsDbHelper.DiySqlBackFlow(mainModel, city);
            querySql.Query();

            int loop = 0;
            int count = querySql.backFlowDic.Values.Count;
            foreach (PlanBtsDbHelper.ResidentItem item in querySql.backFlowDic.Values)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                Cell cell = CellManager.GetInstance().GetCurrentCell(item.ILac, item.ICi);
                if (cell == null)
                {
                    continue;
                }
                CellRadiusManager.Set(cell);

                double radius = CellRadiusManager.Get(item.ILac, item.ICi, CellType.GSM);
                if (radius == -1)
                {
                    continue;
                }

                CellCloudPictureData data = new CellCloudPictureData();
                data.Weight = item.FEdgeBackFlow;
                data.Direction = cell.Direction;
                data.Longitude = cell.Longitude;
                data.Latitude = cell.Latitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getChinaMobileCompGrid(string city,int curRadius)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight =85;
            curConfig.MaxWeight = 95;//实际配置是范围[-95,-85]，颜色带由红色至蓝色。
            int[] colorZone = 
            { 
                0x00FF0000, //蓝色 
                0x00ffff00,   //浅蓝
                0x0000ffff,   //黄色    
                0x000000ff,  //红色  
            };//蓝色到红色
            curConfig.ColorZone = colorZone;
            PlanBtsDbHelper.DiySqlCompGrid querySql = new PlanBtsDbHelper.DiySqlCompGrid(mainModel, city);
            querySql.Query();

            int loop = 0;
            int count = querySql.compGridList.Count;
            foreach (PlanBtsDbHelper.CompGridItem item in querySql.compGridList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                double radius = 0.0001951 / 20 * curRadius; //覆盖半径长度

                CellCloudPictureData data = new CellCloudPictureData();
                if (item.FAbsolutePccpchRscp > -85)
                    data.Weight = 85;
                else if (item.FAbsolutePccpchRscp < -95)
                    data.Weight = 95;
                else
                    data.Weight = Math.Abs(item.FAbsolutePccpchRscp);
                data.Direction = 0;
                data.Longitude = item.FLongitude;
                data.Latitude = item.FLatitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getChinaUnicomCompGrid(string city,int curRadius)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight = 0;
            curConfig.MaxWeight = 15;
            curConfig.GradientOffsetRate = 1;
            int[] colorZone = 
            { 
                0x00FF0000, //蓝色 
                0x00ffff00,   //浅蓝
                0x0000ffff,   //黄色    
                0x000000ff,  //红色  
            };//蓝色到红色
            curConfig.ColorZone = colorZone;
            PlanBtsDbHelper.DiySqlCompGrid querySql = new PlanBtsDbHelper.DiySqlCompGrid(mainModel, city);
            querySql.Query();

            int loop = 0;
            int count = querySql.compGridList.Count;
            foreach (PlanBtsDbHelper.CompGridItem item in querySql.compGridList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                double radius = 0.0001951 / 20 * curRadius; //覆盖半径长度

                CellCloudPictureData data = new CellCloudPictureData();
                if (item.FRelativeRscp < -15)
                    data.Weight = 15;
                else
                    data.Weight = Math.Abs(item.FRelativeRscp);
                data.Direction = 0;
                data.Longitude = item.FLongitude;
                data.Latitude = item.FLatitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getComplainNum(string city,int curRadius)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight = 0;
            curConfig.MaxWeight = 50;
            curConfig.GradientOffsetRate = 1;
            int[] colorZone = 
            { 
                0x00FF0000, //蓝色 
                0x00ffff00,   //浅蓝
                0x0000ffff,   //黄色    
                0x000000ff,  //红色  
            };//蓝色到红色
            curConfig.ColorZone = colorZone;
            PlanBtsDbHelper.DiySqlComplainNum querySql = new PlanBtsDbHelper.DiySqlComplainNum(mainModel, city);
            querySql.Query();
            int loop = 0;
            int count = querySql.compList.Count;
            foreach (PlanBtsDbHelper.ComplainItem item in querySql.compList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                double radius = 0.0001951 / 20 * curRadius; //覆盖半径长度

                CellCloudPictureData data = new CellCloudPictureData();
                if (item.INum > 50)
                    data.Weight = 50;
                else
                    data.Weight = item.INum;
                data.Direction = 0;
                data.Longitude = item.FLongitude;
                data.Latitude = item.FLatitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getHighFlow(string city)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight = 0;
            curConfig.MaxWeight = 300;
            int[] colorZone = 
            { 
                0x00FF0000, //蓝色 
                0x00ffff00,   //浅蓝
                0x0000ffff,   //黄色    
                0x000000ff,  //红色  
            };//蓝色到红色
            curConfig.ColorZone = colorZone;
            PlanBtsDbHelper.DiySqlHighFlow querySql = new PlanBtsDbHelper.DiySqlHighFlow(mainModel, city);
            querySql.Query();

            int loop = 0;
            int count = querySql.highFlowDic.Values.Count;
            foreach (PlanBtsDbHelper.HighFlow item in querySql.highFlowDic.Values)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                Cell cell = CellManager.GetInstance().GetCurrentCell(item.ILac, item.ICi);
                if (cell == null)
                {
                    continue;
                }
                CellRadiusManager.Set(cell);

                double radius = CellRadiusManager.Get(item.ILac, item.ICi, CellType.GSM);
                if (radius == -1)
                {
                    continue;
                }

                CellCloudPictureData data = new CellCloudPictureData();
                if (item.FFlow > 300)
                    data.Weight = 300;
                else
                    data.Weight = item.FFlow;
                data.Direction = cell.Direction;
                data.Longitude = cell.Longitude;
                data.Latitude = cell.Latitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getResidentRate(string city)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight = 0.2;
            curConfig.MaxWeight = 0.85;
            int[] colorZone = 
            { 
                0x00FF0000, //蓝色
                0x000000ff,  //红色  
                0x0000ffff,   //黄色
                0x00ffff00,   //浅蓝
                0x00FF0000, //蓝色
            };//红色到蓝色
            curConfig.ColorZone = colorZone;

            PlanBtsDbHelper.DiySqlResidentRate querySql = new PlanBtsDbHelper.DiySqlResidentRate(mainModel, city);
            querySql.Query();

            int loop = 0;
            int count = querySql.residentDic.Values.Count;
            foreach (PlanBtsDbHelper.ResidentItem item in querySql.residentDic.Values)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                TDCell tdCell = CellManager.GetInstance().GetCurrentTDCell(item.ILac, item.ICi);
                if (tdCell == null)
                {
                    continue;
                }
                CellRadiusManager.Set(tdCell);

                double radius = CellRadiusManager.Get(item.ILac, item.ICi, CellType.TD);
                if (radius == -1)
                {
                    continue;
                }

                CellCloudPictureData data = new CellCloudPictureData();
                if (item.Fresident > 0.85)
                {
                    data.Weight = 0.85;
                }
                else if (item.Fresident<0.2)
                {
                    data.Weight = 0.2;
                }
                else
                {
                    data.Weight = item.Fresident;
                }
                data.Direction = tdCell.Direction;
                data.Longitude = tdCell.Longitude;
                data.Latitude = tdCell.Latitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getPlanningBtsScore(string city,int curRadius)
        {
            curConfig = new CellCloudPictureConfig();
            curConfig.MinWeight = 0;
            curConfig.MaxWeight = 100;
            curConfig.GradientOffsetRate = 1;
            int[] colorZone = 
            { 
                0x00FF0000,//蓝色,作为底色
                0x000000ff,  //红色  
                0x0000ffff,   //黄色
                0x00ffff00,   //浅蓝
                0x00FF0000, //蓝色
            };//蓝色到红色到蓝色
            curConfig.ColorZone = colorZone;
            PlanBtsDbHelper.DiySqlPlanningBtsScore querySql = new PlanBtsDbHelper.DiySqlPlanningBtsScore(mainModel, city);
            querySql.Query();
            int loop = 0;
            int count = querySql.btsScoreList.Count;
            foreach (PlanBtsDbHelper.PlanningBtsScore item in querySql.btsScoreList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (WaitBox.CancelRequest)
                    break;

                double radius = 0.0001951 / 20 * curRadius; //覆盖半径长度

                CellCloudPictureData data = new CellCloudPictureData();
                if (item.FScore > 100)
                    data.Weight = 100;
                else if (item.FScore < 0)
                    data.Weight = 0;
                else
                    data.Weight = item.FScore;
                data.Direction = 0;
                data.Longitude = item.FLongitude;
                data.Latitude = item.FLatitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }
        }

        private void getLastWeakRoad(string city,int curRadius)
        {
            /**
            //curConfig = new CellCloudPictureConfig();
            //curConfig.MinWeight = 85;
            //curConfig.MaxWeight = 95;//实际配置是范围[-95,-85]，颜色带由红色至绿色。因计算颜色需要正数，数值取其绝对值，而默认颜色带为绿到红，设置使方向相反，效果负负得正。

            //PlanBtsDbHelper.DiySqlTdLastWeakRoad querySql = new PlanBtsDbHelper.DiySqlTdLastWeakRoad(mainModel, city);
            //querySql.Query();

            //int loop = 0;
            //int count = querySql.wrList.Count;
            //foreach (PlanBtsDbHelper.WeakRoadItem item in querySql.wrList)
            //{
            //    WaitBox.ProgressPercent = ++loop * 100 / count;
            //    if (WaitBox.CancelRequest)
            //        break;

            //    double radius = CellRadiusManager.Get(item.ILac, item.ICi, CellType.TD);
            //    if (radius == -1)
            //    {
            //        continue;
            //    }

            //    CellCloudPictureData data = new CellCloudPictureData();
            //    data.Weight = item.IRxlmean;
            //    data.Direction = 0;
            //    data.Longitude = tdCell.Longitude;
            //    data.Latitude = tdCell.Latitude;
            //    data.Radius = radius;
            //    cloudDataList.Add(data);
            //}
            *///
        }

    }
}
