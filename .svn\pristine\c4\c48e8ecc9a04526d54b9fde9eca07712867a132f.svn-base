using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class PlanBTS : Snapshot<PlanBTS>
    {
        public PlanBTS()
        {
            Value = this;
        }
        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }
        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append("Name:").Append(Name);
                info.Append("\r\nNO.:").Append(No);
                info.Append("\r\nType:").Append(TypeDes);
                info.Append("\r\nLongitude:").Append(Longitude);
                info.Append("\r\nLatitude:").Append(Latitude);
                info.Append("\r\nReason:").Append(Reason);
                info.Append("\r\nProgress:").Append(Progress);
                info.Append("\r\nAddress:").Append(Address);
                info.Append("\r\nComment:").Append(Comment);
                return info.ToString();
            }
        }

        public string Name { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string No { get; set; }
        public string TypeDes { get; set; }
        public string Reason { get; set; }
        public string Progress { get; set; }
        public string Address { get; set; }
        public string Comment { get; set; }
    }
}
