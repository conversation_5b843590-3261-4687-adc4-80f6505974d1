﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKpiStatByRegionTestPlan : QueryKPIStatByRegion
    {
        List<string> list_TestPlan = null;

        readonly MainModel modelForRegion = null;

        public QueryKpiStatByRegionTestPlan(MainModel mainModel)
            : base()
        {
            this.modelForRegion = mainModel;
        }

        public override string Name
        {
            get
            {
                return "按测试计划统计(区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11059, this.Name);
        }

        /// <summary>
        /// 报表统计前的条件设定（测试计划和报表类型）
        /// </summary>
        protected override bool getConditionBeforeQuery()
        {
            DIYQueryFileInfoByRegion diyeryFileInfo = new DIYQueryFileInfoByRegion(this.modelForRegion);
            diyeryFileInfo.IsShowFileInfoForm = false;
            diyeryFileInfo.SetQueryCondition(this.condition);
            diyeryFileInfo.Query();
            List<FileInfo> fileInfos = modelForRegion.FileInfos;
            List<string> testPlans = new List<string>();
            foreach (FileInfo file in fileInfos)
            {
                if (file.TestPlanName != null && !testPlans.Contains(file.TestPlanName))
                    testPlans.Add(file.TestPlanName);
            }
            modelForRegion.FileInfos.Clear();                                              //移除fileInfos，避免文件列表加载数据
            if (testPlans.Count <= 0)
            {
                MessageBox.Show("无测试计划，请重新设定条件！");
                return false;
            }            
            TestPlanReportPickerDlg dlg = new TestPlanReportPickerDlg(testPlans);           
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            list_TestPlan = dlg.list_TestPlan;
            KpiDataManager = new KPIDataManager();
            return true;
        }

        protected Dictionary<int, FileInfo> iFileIDInfoDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            if (this.iFileIDInfoDic == null)
            {
                this.iFileIDInfoDic = new Dictionary<int, FileInfo>();
            }

            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
                return;
            fillStatData(package, curImgColumnDef, singleStatData);
            if (condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                #region 选择了预选区域
                addSelectedRegionData(singleStatData, grid);
                #endregion               
            }
            else
            {
                #region 未选择预选区域
                addUnknownRegionData(singleStatData);
                #endregion
            }
        }

        private void addSelectedRegionData(KPIStatDataBase singleStatData, Grid.GridUnitBase grid)
        {
            for (int i = 0; i < condition.Geometorys.SelectedResvRegions.Count; i++)
            {
                if (condition.Geometorys.SelectedResvRegions[i].GeoOp.Contains(grid.CenterLng, grid.CenterLat))
                {
                    int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
                    FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

                    if (!string.IsNullOrEmpty(fi.TestPlanName) && list_TestPlan.Contains(fi.TestPlanName))
                    {
                        string testPlanName = condition.Geometorys.SelectedResvRegions[i].RegionName != null ? mainModel.SearchGeometrys.SelectedResvRegions[i].RegionName : string.Empty;
                        testPlanName = testPlanName + " - " + fi.TestPlanName;
                        KpiDataManager.AddStatData(string.Empty, testPlanName, fi, singleStatData, false);
                        if (!this.iFileIDInfoDic.ContainsKey(fileID))
                            this.iFileIDInfoDic[fileID] = fi;
                        break;
                    }
                }
            }
        }

        private void addUnknownRegionData(KPIStatDataBase singleStatData)
        {
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            if (!string.IsNullOrEmpty(fi.TestPlanName) && list_TestPlan.Contains(fi.TestPlanName))
            {
                string testPlanName = "未知区域 - " + fi.TestPlanName;
                KpiDataManager.AddStatData(string.Empty, testPlanName, fi, singleStatData, false);
                if (!this.iFileIDInfoDic.ContainsKey(fileID))
                    this.iFileIDInfoDic[fileID] = fi;
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
                return;
            if (condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                #region 选择了预选区域
                addSelectedRegionEvtData(evt);
                #endregion              
            }
            else
            {
                #region 未选择预选区域
                addUnknownRegionEvtData(evt);
                #endregion                 
            }
        }

        private void addSelectedRegionEvtData(Event evt)
        {
            for (int i = 0; i < condition.Geometorys.SelectedResvRegions.Count; i++)
            {
                if (condition.Geometorys.SelectedResvRegions[i].GeoOp.Contains(evt.Longitude, evt.Latitude))
                {
                    StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
                    FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
                    if (this.iFileIDInfoDic.ContainsKey(evt.FileID))
                    {
                        fi = this.iFileIDInfoDic[evt.FileID];
                    }
                    if (!string.IsNullOrEmpty(fi.TestPlanName) && list_TestPlan.Contains(fi.TestPlanName))
                    {
                        string testPlanName = condition.Geometorys.SelectedResvRegions[i].RegionName != null ? mainModel.SearchGeometrys.SelectedResvRegions[i].RegionName : string.Empty;
                        testPlanName = testPlanName + " - " + fi.TestPlanName;
                        KpiDataManager.AddStatData(string.Empty, testPlanName, fi, eventData, false);
                        break;
                    }
                }
            }
        }

        private void addUnknownRegionEvtData(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            if (this.iFileIDInfoDic.ContainsKey(evt.FileID))
            {
                fi = this.iFileIDInfoDic[evt.FileID];
            }
            if (!string.IsNullOrEmpty(fi.TestPlanName) && list_TestPlan.Contains(fi.TestPlanName))
            {
                string testPlanName = "未知区域 - " + fi.TestPlanName;
                KpiDataManager.AddStatData(string.Empty, testPlanName, fi, eventData, false);
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            if (KpiDataManager != null)
                KpiDataManager.FinalMtMoStatData();
            this.iFileIDInfoDic = null;
        }
    }
}
