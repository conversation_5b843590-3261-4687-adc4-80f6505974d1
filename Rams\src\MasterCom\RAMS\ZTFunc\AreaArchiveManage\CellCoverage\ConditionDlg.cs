﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public partial class ConditionDlg : BaseDialog
    {
        public ConditionDlg()
            : base()
        {
            InitializeComponent();
            gridCtrlSelected.DataSource = selectedCells;
            fillTemplate(null);
        }

        private void cellPropCtrl_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                findCell();
            }
        }

        private void btnFind_Click(object sender, EventArgs e)
        {
            findCell();
        }

        private List<ICell> allNetFilteredCells;
        public List<ICell> OptionalCells
        {
            get { return allNetFilteredCells; }
            set
            {
                allNetFilteredCells = value;
                fillOptionaleCells();
            }
        }

        private List<ICell> selectedCells = new List<ICell>();
        public List<ICell> SelectedCells
        {
            get { return selectedCells; }
            set
            {
                selectedCells = new List<ICell>();
                allNetFilteredCells = new List<ICell>();
                if (value == null)
                {
                    return;
                }
                selectedCells.AddRange(value);
                fillOptionaleCells();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void fillOptionaleCells()
        {
            gridCtrlOptionalCell.DataSource = allNetFilteredCells;
            gridCtrlOptionalCell.RefreshDataSource();
        }

        private void findCell()
        {
            string name = txtName.Text.Trim().ToUpper();
            string code = txtCode.Text.Trim().ToUpper();
            int lac = (int)numLac.Value;
            int ci = (int)numCi.Value;
            if (name.Length == 0 && code.Length == 0 && lac == 0 && ci == 0)
            {
                return;
            }
            List<ICell> cells = new List<ICell>();
            if (chk2G.Checked)
            {
                List<ICell> tempCells = new List<ICell>();
                foreach (ICell cell in CellManager.GetInstance().GetCurrentCells())
                {
                    tempCells.Add(cell);
                }
                cells.AddRange(getFilterCells(tempCells
                    , name, code, lac, ci));
            }
            if (chk3G.Checked)
            {
                List<ICell> tempCells = new List<ICell>();
                foreach (ICell cell in CellManager.GetInstance().GetCurrentTDCells())
                {
                    tempCells.Add(cell);
                }
                cells.AddRange(getFilterCells(tempCells
                    , name, code, lac, ci));
            }
            if (chk4G.Checked)
            {
                List<ICell> tempCells = new List<ICell>();
                foreach (ICell cell in CellManager.GetInstance().GetCurrentTDCells())
                {
                    tempCells.Add(cell);
                }
                cells.AddRange(getFilterCells(tempCells
                    , name, code, lac, ci));
            }
            this.OptionalCells = cells;
        }

        private List<ICell> getFilterCells(List<ICell> cells, string nameUpper, string codeUpper
            , int lac, int ci)
        {
            List<ICell> filteredCells = new List<ICell>();
            foreach (ICell cell in cells)
            {
                if ((nameUpper.Length != 0 && cell.Name.ToUpper().IndexOf(nameUpper) == -1)
                    || (codeUpper.Length != 0 && cell.Code.ToUpper().IndexOf(codeUpper) == -1))
                {
                    continue;
                }
                int cellLac = -1;
                int cellCi = -1;
                if (cell is Cell)
                {
                    Cell curCell = cell as Cell;
                    cellLac = curCell.LAC;
                    cellCi = curCell.CI;
                }
                else if (cell is TDCell)
                {
                    TDCell curCell = cell as TDCell;
                    cellLac = curCell.LAC;
                    cellCi = curCell.CI;
                }
                else if (cell is LTECell)
                {
                    LTECell curCell = cell as LTECell;
                    cellLac = curCell.TAC;
                    cellCi = curCell.ECI;
                }
                if ((lac != 0 && lac != cellLac)
                || (ci != 0 && ci != cellCi))
                {
                    continue;
                }
                filteredCells.Add(cell);
            }
            return filteredCells;
        }

        private void networkType_CheckedChanged(object sender, EventArgs e)
        {
            //
        }

        private void gvOptionalCell_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gvOptionalCell.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int idx = gvOptionalCell.GetDataSourceRowIndex(info.RowHandle);
            if (idx >= 0)
            {
                selectedCells.Add(allNetFilteredCells[idx]);
                allNetFilteredCells.RemoveAt(idx);
                gridCtrlOptionalCell.RefreshDataSource();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void gvSelected_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gvSelected.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int idx = gvSelected.GetDataSourceRowIndex(info.RowHandle);
            if (idx >= 0)
            {
                allNetFilteredCells.Add(selectedCells[idx]);
                selectedCells.RemoveAt(idx);
                gridCtrlOptionalCell.RefreshDataSource();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (OptionalCells==null)
            {
                return;
            }
            foreach (ICell cell in OptionalCells)
            {
                if (!selectedCells.Contains(cell))
                {
                    selectedCells.Add(cell);
                }
            }
            OptionalCells.Clear();
            gridCtrlOptionalCell.RefreshDataSource();
            gridCtrlSelected.RefreshDataSource();
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            foreach (ICell cell in selectedCells)
            {
                if (!OptionalCells.Contains(cell))
                {
                    OptionalCells.Add(cell);
                }
            }
            selectedCells.Clear();
            gridCtrlOptionalCell.RefreshDataSource();
            gridCtrlSelected.RefreshDataSource();
        }

        private void btnTemplate_Click(object sender, EventArgs e)
        {
            CellCoverTemplateOptionForm dlg = new CellCoverTemplateOptionForm();
            dlg.SetTemplate(this.cbxTemplate.SelectedItem as CellCoverRptTemplate);
            dlg.ShowDialog();
            fillTemplate(dlg.CurTemplate);
        }

        private void fillTemplate(CellCoverRptTemplate selTemplate)
        {
            cbxTemplate.Properties.Items.Clear();
            foreach (CellCoverRptTemplate rpt in CellCoverTemplateMngr.Instance.ReportTemplates)
            {
                cbxTemplate.Properties.Items.Add(rpt);
            }
            if (selTemplate != null)
            {
                cbxTemplate.SelectedItem = selTemplate;
            }
            else if (cbxTemplate.Properties.Items.Count > 0)
            {
                cbxTemplate.SelectedIndex = 0;
            }
        }

        public CellCoverRptTemplate CurTemplate
        {
            get;
            private set;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            CurTemplate = cbxTemplate.SelectedItem as CellCoverRptTemplate;
            if (CurTemplate==null)
            {
                MessageBox.Show("请选择统计模板！");
                return;
            }
            if (selectedCells.Count==0)
            {
                MessageBox.Show("请选择要统计的小区！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnClearSel_Click(object sender, EventArgs e)
        {
            this.selectedCells.Clear();
            gridCtrlSelected.RefreshDataSource();
        }






    }
}
