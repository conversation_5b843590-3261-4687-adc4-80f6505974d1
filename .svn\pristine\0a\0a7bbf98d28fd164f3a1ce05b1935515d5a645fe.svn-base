﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCellAnaNRBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        WeakLteCellCondition cond = new WeakLteCellCondition();
        protected Dictionary<string, WeakCellInfoNR> nrWeakCellInfoDic = new Dictionary<string, WeakCellInfoNR>();
        protected List<WeakCellInfoNR> resultList = new List<WeakCellInfoNR>();

        protected WeakCellAnaNRBase()
            : base(MainModel.GetInstance())
        {
            this.FilterSampleByRegion = true;
            this.IncludeEvent = false;
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "弱覆盖小区分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35105, this.Name);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            nrWeakCellInfoDic.Clear();
            resultList.Clear();
        }

        WeakCellConditionNRDlg dlg;
        protected override bool getCondition()
        {
            if (dlg == null)
            {
                cond.RsrpMax = -88;
                dlg = new WeakCellConditionNRDlg();
            }
            dlg.SetCondition(cond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = dlg.GetCondition();
            return true;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            return;
                        }
                        NRCell nrCell = tp.GetMainCell_NR();
                        if (nrCell == null)
                        {
                            continue;
                        }

                        WeakCellInfoNR weakInfo;
                        if (!nrWeakCellInfoDic.TryGetValue(nrCell.Token, out weakInfo))
                        {
                            weakInfo = new WeakCellInfoNR(tp.DistrictID, nrCell);
                            nrWeakCellInfoDic.Add(nrCell.Token, weakInfo);
                        }
                        weakInfo.AddTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (WeakCellInfoNR info in nrWeakCellInfoDic.Values)
            {
                addValidRes(info);
            }
            nrWeakCellInfoDic.Clear();
        }

        private void addValidRes(WeakCellInfoNR info)
        {
            bool isWeakRsrp = info.RsrpAvg <= cond.RsrpMax;
            bool isWeakSinr = info.SinrAvg <= cond.SinrMax;
            if (cond.IsCheckSinr)
            {
                if (cond.SinrIsAnd)
                {
                    if (isWeakRsrp && isWeakSinr)
                    {
                        addToResult(info);
                    }
                }
                else
                {
                    if (isWeakRsrp || isWeakSinr)
                    {
                        addToResult(info);
                    }
                }
            }
            else if (isWeakRsrp)
            {
                addToResult(info);
            }
        }

        protected void addToResult(WeakCellInfoNR info)
        {
            info.SN = resultList.Count + 1;
            resultList.Add(info);
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            WeakCellInfoNRForm frm = MainModel.CreateResultForm(typeof(WeakCellInfoNRForm)) as WeakCellInfoNRForm;
            frm.FillData(resultList, this.Name);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
        }
    }

    public class WeakCellAnaNRByRegion : WeakCellAnaNRBase
    {
        private static WeakCellAnaNRByRegion instance = null;
        public static WeakCellAnaNRByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new WeakCellAnaNRByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "弱覆盖小区分析(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35005, this.Name);
        }
    }

    public class WeakCellAnaNRByFile : WeakCellAnaNRBase
    {
        private static WeakCellAnaNRByFile intance = null;
        public static WeakCellAnaNRByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new WeakCellAnaNRByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "弱覆盖小区分析(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35005, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
