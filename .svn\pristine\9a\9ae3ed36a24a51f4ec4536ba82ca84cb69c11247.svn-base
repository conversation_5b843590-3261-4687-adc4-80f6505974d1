﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo
{
    public partial class SLevNLevDiffDlg : BaseDialog
    {
        public SLevNLevDiffDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(int levDiff,bool saveTp)
        {
            this.numDiff.Value = levDiff;
            this.chkSaveTp.Checked = saveTp;
        }

        public void GetCondition(out int levDiff, out bool saveTp)
        {
            levDiff = (int)this.numDiff.Value;
            saveTp = this.chkSaveTp.Checked;
        }

    }
}
