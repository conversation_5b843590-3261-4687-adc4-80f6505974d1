﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEHnadoverNcellQueryAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static LTEHnadoverNcellQueryAnaByRegion instance = null;

        protected List<LteHandoverInfo> handoverInfoList = new List<LteHandoverInfo>();

        public static LTEHnadoverNcellQueryAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEHnadoverNcellQueryAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected LTEHnadoverNcellQueryAnaByRegion()
            : base(MainModel.GetInstance())
        {
        }
        public override string Name
        {
            get
            {
                return "LTE切换主邻区信息(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22068, this.Name);
        }

        protected override bool getCondition()
        {
            ServiceTypes.Clear();
            //ServiceTypes.Add(ServiceType.LTE_DATA);
            //ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");
            Columns.Add("lte_NCell_RSRP");

            return true;
        }

        protected override void fireShowForm()
        {
            if (handoverInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTEHnadoverNcellInfoForm frm = MainModel.CreateResultForm(typeof(LTEHnadoverNcellInfoForm)) as LTEHnadoverNcellInfoForm;
            frm.FillData(handoverInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            handoverInfoList = new List<LteHandoverInfo>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (Event evt in file.Events)
                    {
                        if ((evt.ID == 899 || evt.ID == 851 || evt.ID == 3156 || evt.ID == 3159) && evt.CellNameSrc != "-1_-1")
                        {
                            LteHandoverInfo handoverInfo = new LteHandoverInfo(file.FileName);
                            addToResultList(evt, file.TestPoints, ref handoverInfo);
                        }
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void addToResultList(Event evt, List<TestPoint> tpList, ref LteHandoverInfo curItem)
        {
            curItem.CellName = evt.CellNameSrc;
            curItem.Evt = evt;
            curItem.Time = evt.DateTime.ToString();
            curItem.Longitude = evt.Longitude;
            curItem.Latitude = evt.Latitude;
            curItem.Earcn = evt.CellBCCHSrc;
            curItem.Pci = evt.CellBSICSrc;
            TestPoint lastTp;
            int tpIndex = getChangeTestpointIndex(tpList, evt);
            for (int index = tpIndex; index > 0; index--)
            {
                lastTp = tpList[tpIndex];
                curItem.TestPonitList.Add(lastTp);
                for (int i = 0; i < 20; i++)
                {
                    LTECell cell = lastTp.GetNBLTECell_TdOrFdd(i);
                    if (cell != null && cell.Name != "-1_-1")
                    {
                        LteNcellInfo ncell = new LteNcellInfo();
                        ncell.SN = curItem.NcellList.Count + 1;
                        ncell.CellName = cell.Name;
                        ncell.Earcn = cell.EARFCN;
                        ncell.Pci = cell.PCI;
                        ncell.Longitude = cell.Longitude;
                        ncell.Latitude = cell.Latitude;
                        ncell.Rsrp = GetNRSRP(lastTp, i);
                        curItem.NcellList.Add(ncell);
                    }
                }
                if (GetRSRP(lastTp) != null || (evt.DateTime - lastTp.DateTime).Seconds > 60)
                {
                    curItem.Rsrp = GetRSRP(lastTp);
                    break;
                }
            }
            curItem.SN = handoverInfoList.Count + 1;
            handoverInfoList.Add(curItem);
        }

        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        protected virtual float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected virtual float? GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_NCell_RSRP", index];
            }
            return (float?)tp["lte_NCell_RSRP", index];
        }
    }

    public class LTEHnadoverNcellQueryAnaByRegion_FDD : LTEHnadoverNcellQueryAnaByRegion
    {
        private static LTEHnadoverNcellQueryAnaByRegion_FDD instance = null;
        public static new LTEHnadoverNcellQueryAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEHnadoverNcellQueryAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected LTEHnadoverNcellQueryAnaByRegion_FDD()
            : base()
        {

        }

        public override string Name
        {
            get
            {
                return "LTE_FDD切换主邻区信息(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26039, this.Name);
        }

        protected override bool getCondition()
        {
            ServiceTypes.Clear();
            //ServiceTypes.Add(ServiceType.LTE_DATA);
            //ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_NCell_EARFCN");
            Columns.Add("lte_fdd_NCell_PCI");
            Columns.Add("lte_fdd_NCell_RSRP");

            return true;
        }
    }
}
