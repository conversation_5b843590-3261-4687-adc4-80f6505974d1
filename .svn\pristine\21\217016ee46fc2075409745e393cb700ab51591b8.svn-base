﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLteLastRoadScene
{
    public partial class ConditionDlg : BaseDialog
    {
        public ConditionDlg()
        {
            InitializeComponent();
        }

        public RoadSceneCondition Condition
        {
            get
            {
                RoadSceneCondition cond = new RoadSceneCondition();

                cond.FastFadeDistance = (double)this.numFastFadeDistance.Value;
                cond.FastFadeRsrp = (float)this.numFastFadeRsrp.Value;
                cond.FastFadeRsrpDiff = (float)this.numFastFadeRsrpDiff.Value;

                cond.MultiBand = (int)this.numMultiBand.Value;
                cond.MultiCellDistance = (double)this.numMultiCellDistance.Value;
                cond.MultiCellNum = (int)this.numMultiCellNum.Value;
                cond.MultiDistance = (double)this.numMultiDistance.Value;
                cond.MultiLev = (int)this.numMultiLev.Value;
                cond.MultiRsrp = (float)this.numMultiRsrp.Value;

                cond.SiteAltitude = (int)this.numSiteAltitude.Value;

                cond.WeakCvrCellDis = (double)this.numWeakCvrCellDis.Value;
                cond.WeakCvrDistance = (double)this.numWeakCvrDistance.Value;
                cond.WeakCvrRsrp = (float)this.numWeakCvrRsrp.Value;

                return cond;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.numFastFadeDistance.Value = (decimal)value.FastFadeDistance;
                this.numFastFadeRsrp.Value = (decimal)value.FastFadeRsrp;
                this.numFastFadeRsrpDiff.Value = (decimal)value.FastFadeRsrpDiff;

                this.numMultiBand.Value = (decimal)value.MultiBand;
                this.numMultiCellDistance.Value = (decimal)value.MultiCellDistance;
                this.numMultiCellNum.Value = value.MultiCellNum;
                this.numMultiDistance.Value = (decimal)value.MultiDistance;
                this.numMultiLev.Value = value.MultiLev;
                this.numMultiRsrp.Value = (decimal)value.MultiRsrp;

                this.numSiteAltitude.Value = value.SiteAltitude;

                this.numWeakCvrCellDis.Value = (decimal)value.WeakCvrCellDis;
                this.numWeakCvrDistance.Value = (decimal)value.WeakCvrDistance;
                this.numWeakCvrRsrp.Value = (decimal)value.WeakCvrRsrp;
            }
        }

        private void numMultiCellDistance_ValueChanged(object sender, EventArgs e)
        {
            numMultiCellDistance2.Value = numMultiCellDistance.Value;
        }

        private void numMultiCellNum_ValueChanged(object sender, EventArgs e)
        {
            numMultiCellNum2.Value = numMultiCellNum.Value;
        }


        private void numMultiCellDistance2_ValueChanged(object sender, EventArgs e)
        {
            numMultiCellDistance.Value = numMultiCellDistance2.Value;
        }

        private void numMultiCellNum2_ValueChanged(object sender, EventArgs e)
        {
            numMultiCellNum.Value = numMultiCellNum2.Value;
        }

    }
}
