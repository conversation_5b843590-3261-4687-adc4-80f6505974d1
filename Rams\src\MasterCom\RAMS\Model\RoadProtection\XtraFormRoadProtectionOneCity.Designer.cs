﻿namespace MasterCom.RAMS.Model.RoadProtection
{
    partial class XtraFormRoadProtectionOneCity
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.导出ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.展开所有ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.合并所有ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Grid_Export = new DevExpress.XtraGrid.GridControl();
            this.gview_GSM = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colistatnum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col严重程度 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col权值 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col距离 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col状态 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col首次发生时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col最后发生时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col事件总数 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col问题天数 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col所属网格 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col位置信息 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col事件类型 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col发生时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col占用小区 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col经度 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col纬度 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col问题类型 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col原因描述 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col建议方案 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Grid_Export)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gview_GSM)).BeginInit();
            this.SuspendLayout();
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54});
            this.gridView2.GridControl = this.gridControl1;
            this.gridView2.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Istatnum", null, "")});
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ColumnAutoWidth = false;
            this.gridView2.OptionsView.ShowDetailButtons = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.Click += new System.EventHandler(this.gridView2_Click);
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "组ID";
            this.gridColumn30.FieldName = "Istatnum";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // gridColumn31
            // 
            this.gridColumn31.FieldName = "严重程度";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn31.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn31.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn31.Width = 153;
            // 
            // gridColumn32
            // 
            this.gridColumn32.FieldName = "权值";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.OptionsColumn.AllowEdit = false;
            this.gridColumn32.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn32.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn32.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn32.Width = 133;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "距离";
            this.gridColumn33.FieldName = "Idistance";
            this.gridColumn33.Name = "gridColumn33";
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "状态";
            this.gridColumn34.FieldName = "Istatus";
            this.gridColumn34.Name = "gridColumn34";
            // 
            // gridColumn35
            // 
            this.gridColumn35.FieldName = "首次发生时间";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn35.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn35.Width = 101;
            // 
            // gridColumn36
            // 
            this.gridColumn36.FieldName = "最后发生时间";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn36.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn36.Width = 101;
            // 
            // gridColumn37
            // 
            this.gridColumn37.FieldName = "事件总数";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn37.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // gridColumn38
            // 
            this.gridColumn38.FieldName = "问题天数";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn38.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // gridColumn39
            // 
            this.gridColumn39.FieldName = "所属网格";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn39.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // gridColumn40
            // 
            this.gridColumn40.FieldName = "位置信息";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn40.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // gridColumn41
            // 
            this.gridColumn41.FieldName = "事件类型";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 0;
            // 
            // gridColumn42
            // 
            this.gridColumn42.FieldName = "发生时间";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 1;
            // 
            // gridColumn43
            // 
            this.gridColumn43.FieldName = "占用小区";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 2;
            // 
            // gridColumn44
            // 
            this.gridColumn44.FieldName = "LAC";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 3;
            // 
            // gridColumn45
            // 
            this.gridColumn45.FieldName = "CI";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 4;
            // 
            // gridColumn46
            // 
            this.gridColumn46.FieldName = "经度";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 5;
            // 
            // gridColumn47
            // 
            this.gridColumn47.FieldName = "纬度";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 6;
            // 
            // gridColumn48
            // 
            this.gridColumn48.FieldName = "问题类型";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 7;
            // 
            // gridColumn49
            // 
            this.gridColumn49.FieldName = "原因描述";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 8;
            // 
            // gridColumn50
            // 
            this.gridColumn50.FieldName = "建议方案";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 9;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "gridColumn1";
            this.gridColumn51.FieldName = "Ifileid";
            this.gridColumn51.Name = "gridColumn51";
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "gridColumn2";
            this.gridColumn52.FieldName = "Strfilename";
            this.gridColumn52.Name = "gridColumn52";
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "gridColumn3";
            this.gridColumn53.FieldName = "Iprojecttype";
            this.gridColumn53.Name = "gridColumn53";
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "gridColumn4";
            this.gridColumn54.FieldName = "Iservicetype";
            this.gridColumn54.Name = "gridColumn54";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridView2;
            gridLevelNode1.RelationName = "RoadWarningEntityList";
            this.gridControl1.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(732, 453);
            this.gridControl1.TabIndex = 7;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1,
            this.gridView2});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.导出ToolStripMenuItem,
            this.展开所有ToolStripMenuItem,
            this.合并所有ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(123, 70);
            // 
            // 导出ToolStripMenuItem
            // 
            this.导出ToolStripMenuItem.Name = "导出ToolStripMenuItem";
            this.导出ToolStripMenuItem.Size = new System.Drawing.Size(122, 22);
            this.导出ToolStripMenuItem.Text = "导出";
            this.导出ToolStripMenuItem.Click += new System.EventHandler(this.导出ToolStripMenuItem_Click);
            // 
            // 展开所有ToolStripMenuItem
            // 
            this.展开所有ToolStripMenuItem.Name = "展开所有ToolStripMenuItem";
            this.展开所有ToolStripMenuItem.Size = new System.Drawing.Size(122, 22);
            this.展开所有ToolStripMenuItem.Text = "展开所有";
            this.展开所有ToolStripMenuItem.Click += new System.EventHandler(this.展开所有ToolStripMenuItem_Click);
            // 
            // 合并所有ToolStripMenuItem
            // 
            this.合并所有ToolStripMenuItem.Name = "合并所有ToolStripMenuItem";
            this.合并所有ToolStripMenuItem.Size = new System.Drawing.Size(122, 22);
            this.合并所有ToolStripMenuItem.Text = "合并所有";
            this.合并所有ToolStripMenuItem.Click += new System.EventHandler(this.合并所有ToolStripMenuItem_Click);
            // 
            // gridView1
            // 
            this.gridView1.ChildGridLevelName = "gridView2";
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Istatnum", null, "")});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsDetail.ShowDetailTabs = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            this.gridView1.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView1_CustomDrawCell);
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "组ID";
            this.gridColumn5.FieldName = "Istatnum";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // gridColumn6
            // 
            this.gridColumn6.FieldName = "严重程度";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn6.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn6.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 0;
            this.gridColumn6.Width = 93;
            // 
            // gridColumn7
            // 
            this.gridColumn7.DisplayFormat.FormatString = "0.00";
            this.gridColumn7.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn7.FieldName = "权值";
            this.gridColumn7.GroupFormat.FormatString = "0.00";
            this.gridColumn7.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowEdit = false;
            this.gridColumn7.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn7.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 1;
            this.gridColumn7.Width = 56;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "距离(米)";
            this.gridColumn8.FieldName = "Idistance";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 2;
            this.gridColumn8.Width = 66;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "状态";
            this.gridColumn9.FieldName = "Istatus";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 3;
            // 
            // gridColumn10
            // 
            this.gridColumn10.FieldName = "首次发生时间";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn10.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 4;
            this.gridColumn10.Width = 101;
            // 
            // gridColumn11
            // 
            this.gridColumn11.FieldName = "最后发生时间";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn11.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 5;
            this.gridColumn11.Width = 101;
            // 
            // gridColumn12
            // 
            this.gridColumn12.FieldName = "事件总数";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn12.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 6;
            // 
            // gridColumn13
            // 
            this.gridColumn13.FieldName = "问题天数";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn13.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 7;
            // 
            // gridColumn14
            // 
            this.gridColumn14.FieldName = "所属网格";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn14.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 8;
            // 
            // gridColumn15
            // 
            this.gridColumn15.FieldName = "位置信息";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn15.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 9;
            // 
            // gridColumn16
            // 
            this.gridColumn16.FieldName = "事件类型";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn17
            // 
            this.gridColumn17.FieldName = "发生时间";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn18
            // 
            this.gridColumn18.FieldName = "占用小区";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn19
            // 
            this.gridColumn19.FieldName = "LAC";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn20
            // 
            this.gridColumn20.FieldName = "CI";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn21
            // 
            this.gridColumn21.FieldName = "经度";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn22
            // 
            this.gridColumn22.FieldName = "纬度";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn23
            // 
            this.gridColumn23.FieldName = "问题类型";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn24
            // 
            this.gridColumn24.FieldName = "原因描述";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn25
            // 
            this.gridColumn25.FieldName = "建议方案";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "gridColumn1";
            this.gridColumn26.FieldName = "Ifileid";
            this.gridColumn26.Name = "gridColumn26";
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "gridColumn2";
            this.gridColumn27.FieldName = "Strfilename";
            this.gridColumn27.Name = "gridColumn27";
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "gridColumn3";
            this.gridColumn28.FieldName = "Iprojecttype";
            this.gridColumn28.Name = "gridColumn28";
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "gridColumn4";
            this.gridColumn29.FieldName = "Iservicetype";
            this.gridColumn29.Name = "gridColumn29";
            // 
            // Grid_Export
            // 
            this.Grid_Export.ContextMenuStrip = this.contextMenuStrip1;
            this.Grid_Export.Location = new System.Drawing.Point(0, 0);
            this.Grid_Export.MainView = this.gview_GSM;
            this.Grid_Export.Name = "Grid_Export";
            this.Grid_Export.Size = new System.Drawing.Size(555, 309);
            this.Grid_Export.TabIndex = 6;
            this.Grid_Export.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gview_GSM});
            this.Grid_Export.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.Grid_Export_MouseDoubleClick);
            // 
            // gview_GSM
            // 
            this.gview_GSM.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colistatnum,
            this.col严重程度,
            this.col权值,
            this.col距离,
            this.col状态,
            this.col首次发生时间,
            this.col最后发生时间,
            this.col事件总数,
            this.col问题天数,
            this.col所属网格,
            this.col位置信息,
            this.col事件类型,
            this.col发生时间,
            this.col占用小区,
            this.colLAC,
            this.colCI,
            this.col经度,
            this.col纬度,
            this.col问题类型,
            this.col原因描述,
            this.col建议方案,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4});
            this.gview_GSM.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gview_GSM.GridControl = this.Grid_Export;
            this.gview_GSM.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Average, "权值", this.col权值, "", "权值")});
            this.gview_GSM.Name = "gview_GSM";
            this.gview_GSM.OptionsCustomization.AllowGroup = false;
            this.gview_GSM.OptionsCustomization.AllowRowSizing = true;
            this.gview_GSM.OptionsCustomization.AllowSort = false;
            this.gview_GSM.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gview_GSM.OptionsFilter.AllowFilterEditor = false;
            this.gview_GSM.OptionsFilter.AllowMRUFilterList = false;
            this.gview_GSM.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gview_GSM.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gview_GSM.OptionsMenu.EnableColumnMenu = false;
            this.gview_GSM.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gview_GSM.OptionsView.AllowCellMerge = true;
            this.gview_GSM.OptionsView.ColumnAutoWidth = false;
            this.gview_GSM.OptionsView.ShowDetailButtons = false;
            this.gview_GSM.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gview_GSM.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gview_GSM.OptionsView.ShowGroupPanel = false;
            this.gview_GSM.OptionsView.ShowPreviewLines = false;
            this.gview_GSM.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            this.gview_GSM.CellMerge += new DevExpress.XtraGrid.Views.Grid.CellMergeEventHandler(this.gview1_GSM_CellMerge);
            // 
            // colistatnum
            // 
            this.colistatnum.Caption = "istatnum";
            this.colistatnum.FieldName = "Istatnum";
            this.colistatnum.Name = "colistatnum";
            this.colistatnum.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col严重程度
            // 
            this.col严重程度.FieldName = "严重程度";
            this.col严重程度.Name = "col严重程度";
            this.col严重程度.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col严重程度.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col严重程度.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.col严重程度.Visible = true;
            this.col严重程度.VisibleIndex = 0;
            this.col严重程度.Width = 153;
            // 
            // col权值
            // 
            this.col权值.FieldName = "权值";
            this.col权值.FieldNameSortGroup = "权值";
            this.col权值.GroupInterval = DevExpress.XtraGrid.ColumnGroupInterval.Value;
            this.col权值.Name = "col权值";
            this.col权值.OptionsColumn.AllowEdit = false;
            this.col权值.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col权值.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col权值.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.col权值.Visible = true;
            this.col权值.VisibleIndex = 1;
            this.col权值.Width = 133;
            // 
            // col距离
            // 
            this.col距离.Caption = "距离";
            this.col距离.FieldName = "Idistance";
            this.col距离.Name = "col距离";
            this.col距离.Visible = true;
            this.col距离.VisibleIndex = 2;
            // 
            // col状态
            // 
            this.col状态.Caption = "状态";
            this.col状态.FieldName = "Istatus";
            this.col状态.Name = "col状态";
            this.col状态.Visible = true;
            this.col状态.VisibleIndex = 3;
            // 
            // col首次发生时间
            // 
            this.col首次发生时间.FieldName = "首次发生时间";
            this.col首次发生时间.Name = "col首次发生时间";
            this.col首次发生时间.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col首次发生时间.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col首次发生时间.Visible = true;
            this.col首次发生时间.VisibleIndex = 4;
            this.col首次发生时间.Width = 101;
            // 
            // col最后发生时间
            // 
            this.col最后发生时间.FieldName = "最后发生时间";
            this.col最后发生时间.Name = "col最后发生时间";
            this.col最后发生时间.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col最后发生时间.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col最后发生时间.Visible = true;
            this.col最后发生时间.VisibleIndex = 5;
            this.col最后发生时间.Width = 101;
            // 
            // col事件总数
            // 
            this.col事件总数.FieldName = "事件总数";
            this.col事件总数.Name = "col事件总数";
            this.col事件总数.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col事件总数.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col事件总数.Visible = true;
            this.col事件总数.VisibleIndex = 6;
            // 
            // col问题天数
            // 
            this.col问题天数.FieldName = "问题天数";
            this.col问题天数.Name = "col问题天数";
            this.col问题天数.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col问题天数.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col问题天数.Visible = true;
            this.col问题天数.VisibleIndex = 7;
            // 
            // col所属网格
            // 
            this.col所属网格.FieldName = "所属网格";
            this.col所属网格.Name = "col所属网格";
            this.col所属网格.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col所属网格.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col所属网格.Visible = true;
            this.col所属网格.VisibleIndex = 8;
            // 
            // col位置信息
            // 
            this.col位置信息.FieldName = "位置信息";
            this.col位置信息.Name = "col位置信息";
            this.col位置信息.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col位置信息.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col位置信息.Visible = true;
            this.col位置信息.VisibleIndex = 9;
            // 
            // col事件类型
            // 
            this.col事件类型.FieldName = "事件类型";
            this.col事件类型.Name = "col事件类型";
            this.col事件类型.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col事件类型.Visible = true;
            this.col事件类型.VisibleIndex = 10;
            // 
            // col发生时间
            // 
            this.col发生时间.FieldName = "发生时间";
            this.col发生时间.Name = "col发生时间";
            this.col发生时间.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col发生时间.Visible = true;
            this.col发生时间.VisibleIndex = 11;
            // 
            // col占用小区
            // 
            this.col占用小区.FieldName = "占用小区";
            this.col占用小区.Name = "col占用小区";
            this.col占用小区.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col占用小区.Visible = true;
            this.col占用小区.VisibleIndex = 12;
            // 
            // colLAC
            // 
            this.colLAC.FieldName = "LAC";
            this.colLAC.Name = "colLAC";
            this.colLAC.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLAC.Visible = true;
            this.colLAC.VisibleIndex = 13;
            // 
            // colCI
            // 
            this.colCI.FieldName = "CI";
            this.colCI.Name = "colCI";
            this.colCI.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colCI.Visible = true;
            this.colCI.VisibleIndex = 14;
            // 
            // col经度
            // 
            this.col经度.FieldName = "经度";
            this.col经度.Name = "col经度";
            this.col经度.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col经度.Visible = true;
            this.col经度.VisibleIndex = 15;
            // 
            // col纬度
            // 
            this.col纬度.FieldName = "纬度";
            this.col纬度.Name = "col纬度";
            this.col纬度.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col纬度.Visible = true;
            this.col纬度.VisibleIndex = 16;
            // 
            // col问题类型
            // 
            this.col问题类型.FieldName = "问题类型";
            this.col问题类型.Name = "col问题类型";
            this.col问题类型.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col问题类型.Visible = true;
            this.col问题类型.VisibleIndex = 17;
            // 
            // col原因描述
            // 
            this.col原因描述.FieldName = "原因描述";
            this.col原因描述.Name = "col原因描述";
            this.col原因描述.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col原因描述.Visible = true;
            this.col原因描述.VisibleIndex = 18;
            // 
            // col建议方案
            // 
            this.col建议方案.FieldName = "建议方案";
            this.col建议方案.Name = "col建议方案";
            this.col建议方案.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col建议方案.Visible = true;
            this.col建议方案.VisibleIndex = 19;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "gridColumn1";
            this.gridColumn1.FieldName = "Ifileid";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "gridColumn2";
            this.gridColumn2.FieldName = "Strfilename";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "gridColumn3";
            this.gridColumn3.FieldName = "Iprojecttype";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "gridColumn4";
            this.gridColumn4.FieldName = "Iservicetype";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // XtraFormRoadProtectionOneCity
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(732, 453);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.Grid_Export);
            this.Name = "XtraFormRoadProtectionOneCity";
            this.Text = "XtraFormRoadProtectionOneCity";
            this.Load += new System.EventHandler(this.XtraFormRoadProtectionOneCity_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Grid_Export)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gview_GSM)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl Grid_Export;
        private DevExpress.XtraGrid.Views.Grid.GridView gview_GSM;
        private DevExpress.XtraGrid.Columns.GridColumn colistatnum;
        private DevExpress.XtraGrid.Columns.GridColumn col严重程度;
        private DevExpress.XtraGrid.Columns.GridColumn col权值;
        private DevExpress.XtraGrid.Columns.GridColumn col首次发生时间;
        private DevExpress.XtraGrid.Columns.GridColumn col最后发生时间;
        private DevExpress.XtraGrid.Columns.GridColumn col事件总数;
        private DevExpress.XtraGrid.Columns.GridColumn col问题天数;
        private DevExpress.XtraGrid.Columns.GridColumn col所属网格;
        private DevExpress.XtraGrid.Columns.GridColumn col位置信息;
        private DevExpress.XtraGrid.Columns.GridColumn col事件类型;
        private DevExpress.XtraGrid.Columns.GridColumn col发生时间;
        private DevExpress.XtraGrid.Columns.GridColumn col占用小区;
        private DevExpress.XtraGrid.Columns.GridColumn colLAC;
        private DevExpress.XtraGrid.Columns.GridColumn colCI;
        private DevExpress.XtraGrid.Columns.GridColumn col经度;
        private DevExpress.XtraGrid.Columns.GridColumn col纬度;
        private DevExpress.XtraGrid.Columns.GridColumn col问题类型;
        private DevExpress.XtraGrid.Columns.GridColumn col原因描述;
        private DevExpress.XtraGrid.Columns.GridColumn col建议方案;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 导出ToolStripMenuItem;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn col距离;
        private DevExpress.XtraGrid.Columns.GridColumn col状态;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private System.Windows.Forms.ToolStripMenuItem 展开所有ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 合并所有ToolStripMenuItem;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;

    }
}