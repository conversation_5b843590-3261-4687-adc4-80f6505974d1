﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NbIotMgrsNoRsrpSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.numGridCount = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numMaxRSRP = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numMaxSINR = new System.Windows.Forms.NumericUpDown();
            this.radAndOr = new DevExpress.XtraEditors.RadioGroup();
            this.chkSINR = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRSRP)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radAndOr.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // numGridCount
            // 
            this.numGridCount.Location = new System.Drawing.Point(276, 26);
            this.numGridCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numGridCount.Name = "numGridCount";
            this.numGridCount.Size = new System.Drawing.Size(120, 21);
            this.numGridCount.TabIndex = 9;
            this.numGridCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridCount.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(181, 30);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 8;
            this.label3.Text = "连续栅格个数≥";
            // 
            // numMaxRSRP
            // 
            this.numMaxRSRP.Location = new System.Drawing.Point(276, 62);
            this.numMaxRSRP.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMaxRSRP.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMaxRSRP.Name = "numMaxRSRP";
            this.numMaxRSRP.Size = new System.Drawing.Size(120, 21);
            this.numMaxRSRP.TabIndex = 6;
            this.numMaxRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxRSRP.Value = new decimal(new int[] {
            94,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(229, 66);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "RSRP＜";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radAndOr);
            this.groupBox1.Controls.Add(this.chkSINR);
            this.groupBox1.Controls.Add(this.numMaxSINR);
            this.groupBox1.Controls.Add(this.numGridCount);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numMaxRSRP);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 208);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            // 
            // numMaxSINR
            // 
            this.numMaxSINR.Location = new System.Drawing.Point(276, 99);
            this.numMaxSINR.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMaxSINR.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMaxSINR.Name = "numMaxSINR";
            this.numMaxSINR.Size = new System.Drawing.Size(120, 21);
            this.numMaxSINR.TabIndex = 12;
            this.numMaxSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxSINR.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // radAndOr
            // 
            this.radAndOr.EditValue = true;
            this.radAndOr.Enabled = false;
            this.radAndOr.Location = new System.Drawing.Point(276, 138);
            this.radAndOr.Name = "radAndOr";
            this.radAndOr.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "且关系"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "或关系")});
            this.radAndOr.Size = new System.Drawing.Size(100, 53);
            this.radAndOr.TabIndex = 66;
            // 
            // chkSINR
            // 
            this.chkSINR.AutoSize = true;
            this.chkSINR.Location = new System.Drawing.Point(210, 101);
            this.chkSINR.Name = "chkSINR";
            this.chkSINR.Size = new System.Drawing.Size(60, 16);
            this.chkSINR.TabIndex = 65;
            this.chkSINR.Text = "SINR＜";
            this.chkSINR.UseVisualStyleBackColor = true;
            this.chkSINR.CheckedChanged += new System.EventHandler(this.chkSINR_CheckedChanged);
            // 
            // NBIOTMgrsNoRsrpSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "NBIOTMgrsNoRsrpSetting";
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRSRP)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radAndOr.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numGridCount;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numMaxRSRP;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numMaxSINR;
        private DevExpress.XtraEditors.RadioGroup radAndOr;
        private System.Windows.Forms.CheckBox chkSINR;
    }
}
