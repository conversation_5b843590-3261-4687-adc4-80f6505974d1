﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    class NRPingPangLayer
    {
        public NRPingPangLayer()
        {
            CustomLineCap lineCap = new AdjustableArrowCap(5, 5);
            this.goPen = new Pen(Color.Blue, 3);
            this.goPen.CustomEndCap = lineCap;
            this.backPen = new Pen(Color.Red, 3);
            this.backPen.CustomEndCap = lineCap;

            this.drawer = new NRArcDrawer(2f / 5);
        }

        public void DrawPingPangLines(double sLng, double sLat, double eLng, double eLat)
        {
            if (sLng == eLng && sLat == eLat)
            {
                this.Clear();
                return;
            }
            this.startPoint = new DbPoint(sLng, sLat);
            this.endPoint = new DbPoint(eLng, eLat);
            this.layer.Draw(DrawLines);
        }

        public void Clear()
        {
            layer.Clear();
        }

        private void DrawLines(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            PointF sp, ep;
            mop.ToDisplay(startPoint, out sp);
            mop.ToDisplay(endPoint, out ep);
            drawer.DrawArc(graphics, goPen, sp.X, sp.Y, ep.X, ep.Y);
            drawer.DrawArc(graphics, backPen, ep.X, ep.Y, sp.X, sp.Y);
        }

        private DbPoint startPoint;
        private DbPoint endPoint;
        private readonly Pen goPen;
        private readonly Pen backPen;
        private readonly NRArcDrawer drawer;
        private readonly TempLayer layer = TempLayer.Instance;
    }

    class NRArcDrawer
    {
        /// <summary>
        /// 画出一段经过给定两点的圆弧
        /// </summary>
        /// <param name="rate">两点距离长度与圆弧所在圆的直径比例</param>
        public NRArcDrawer(float rate)
        {
            this.rate = rate;
        }

        public void DrawArc(Graphics graphics, Pen pen, float x1, float y1, float x2, float y2)
        {
            // offsetX, offsetY为起点到圆的外接矩形的左上角的偏移
            float len = (float)Math.Sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1)) / 2;
            float radius = len / this.rate;
            float offsetX = radius - len;

            float height = (float)Math.Sqrt(radius * radius - len * len);
            float offsetY = radius - height;

            // 坐标偏移，以起点作为坐标原点
            graphics.TranslateTransform(x1, y1);
            RectangleF rectF = new RectangleF(-offsetX, -offsetY, radius * 2, radius * 2);

            // 坐标旋转，起点与终点的角度
            float rotateAngle = TwoPointAngle(x1, y1, x2, y2);
            graphics.RotateTransform(rotateAngle);

            // 起点与圆心的夹角，其两倍即为弧线角度
            float angle = (float)(Math.Asin(len / radius) / Math.PI * 180);
            graphics.DrawArc(pen, rectF, 270 - angle, angle * 2);

            // 重置坐标
            graphics.ResetTransform();
        }

        private float TwoPointAngle(float x1, float y1, float x2, float y2)
        {
            if (x1 == x2 && y1 == y2)
            {
                return 0;
            }
            else if (x1 == x2 && y1 > y2)
            {
                return 270;
            }
            else if (x1 == x2)//y1 < y2
            {
                return 90;
            }
            else if (x1 > x2 && y1 == y2)
            {
                return 180;
            }
            else if (x1 < x2 && y1 == y2)
            {
                return 0;
            }

            float angle = (float)(Math.Atan((y2 - y1) / (x2 - x1)) / Math.PI * 180);
            if (x1 > x2 && y1 > y2) // 三
            {
                return 180 + angle;
            }
            else if (x1 > x2) // 二 y1 < y2
            {
                return 180 + angle;
            }
            else if (y1 > y2) // 四 x1 < x2
            {
                return 360 + angle;
            }
            else //  (x1 < x2 && y1 < y2)
            {
                return angle;
            }
        }

        private readonly float rate;
    }
}
