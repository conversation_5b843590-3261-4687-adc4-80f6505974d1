﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.NOP.Stat;

namespace MasterCom.RAMS.NOP
{
    public partial class GISPanel : UserControl
    {
        public GISPanel()
        {
            InitializeComponent();
            DisplayNameLabel = true;
            DisplayValueLabel = true;
            cityFieldIdx = 0;
            mapControl.CursorMode = MapWinGIS.tkCursorMode.cmPan;
            mapControl.MapCursor = tkCursor.crsrUserDefined;
            mapControl.UDCursorHandle = (new Cursor(RAMS.Properties.Resources.cursor_hand.GetHicon())).Handle.ToInt32();
        }

        private Shapefile pntLayer = null;
        public void SetEvents(List<Event> evts)
        {
            pntLayer.EditClear();
            try
            {
                if (evts == null)
                {
                    return;
                }
                for (int i = 0; i < evts.Count; i++)
                {
                    MapWinGIS.Shape shp = new MapWinGIS.Shape();
                    shp.ShapeType = ShpfileType.SHP_POINT;
                    //MapWinGIS.Point pnt = new MapWinGIS.Point();
                    //pnt.x = evts[i].Longitude;
                    //pnt.y = evts[i].Latitude;
                    shp.AddPoint(evts[i].Longitude, evts[i].Latitude);
                    pntLayer.EditAddShape(shp);
                }
            }
            finally
            {
                mapControl.Redraw();
            }
           
        }

        public void Init()
        {
            string filePath = Application.StartupPath + "\\GEOGRAPHIC\\其它\\行政区界_region.shp";
            LoadLayer(filePath);
        }

        private Shapefile sfLayer = null;
        private int cityFieldIdx;
        private int layerHandle;

        public void LoadLayer(string layerPath)
        {
            mapControl.RemoveAllLayers();
            sfLayer = new Shapefile();
            if (!sfLayer.Open(layerPath, null))
            {
                MessageBox.Show("图层未能打开:" + layerPath);
            }
            layerHandle = mapControl.AddLayer(sfLayer, true);
            sfLayer.CollisionMode = tkCollisionMode.AllowCollisions;
            mapControl.set_ShapeLayerFillColor(layerHandle, (uint)ColorTranslator.ToOle(Color.White));
            mapControl.set_ShapeLayerLineColor(layerHandle, (uint)ColorTranslator.ToOle(Color.CornflowerBlue));

            pntLayer = new Shapefile();
            pntLayer.CreateNew(null, ShpfileType.SHP_POINT);
            pntLayer.DefaultDrawingOptions.PointShape = tkPointShapeType.ptShapeCircle;
            pntLayer.DefaultDrawingOptions.FillColor = (uint)ColorTranslator.ToOle(Color.Black);
            pntLayer.DefaultDrawingOptions.VerticesType = tkVertexType.vtCircle;
            pntLayer.CollisionMode = tkCollisionMode.AllowCollisions;
            mapControl.AddLayer(pntLayer, true);
        }

        public bool DisplayNameLabel
        {
            get;
            set;
        }

        public bool DisplayValueLabel
        {
            get;
            set;
        }

        private List<GISShapeRenderOption> renders = null;
        public List<GISShapeRenderOption> Renders
        {
            get { return renders; }
            set
            {
                renders = value;
                refreshLegend();
                refreshLayer();
            }
        }

        private ColumnRender curColRender = null;
        public ColumnRender CurColRender
        {
            get {
                return curColRender;
            }
            set {
                curColRender = value;
                refreshLegend();
            }
        }

        private void refreshLegend()
        {
            lbxLegend.Items.Clear();
            if (curColRender==null)
            {
                return;
            }
            lbxLegend.Items.Add(curColRender.Caption);
            foreach (DTParameterRangeColor rng in curColRender.RangeColorSet)
            {
                lbxLegend.Items.Add(rng);
            }
        }

        private void refreshLayer()
        {
            if (this.DesignMode || sfLayer == null)
            {
                return;
            }
            sfLayer.Labels.ClearCategories();
            sfLayer.Labels.Clear();

            sfLayer.Categories.Clear();
            Dictionary<uint, int> categoryByColor = new Dictionary<uint, int>();
            for (int i = 0; i < sfLayer.NumShapes; i++)
            {
                MapWinGIS.Shape shp = sfLayer.get_Shape(i);
                GISShapeRenderOption render = null;
                string lable = null;
                getLable(i, ref render, ref lable);

                addCategory(categoryByColor, i, render);
                if (!string.IsNullOrEmpty(lable))
                {
                    sfLayer.Labels.AddLabel(lable, shp.Centroid.x, shp.Centroid.y, 0, 0);
                }
            }
            sfLayer.Labels.AvoidCollisions = false;
            mapControl.Redraw();
        }

        private void getLable(int i, ref GISShapeRenderOption render, ref string lable)
        {
            if (DisplayNameLabel)
            {
                lable = sfLayer.get_CellValue(cityFieldIdx, i) as string;
            }
            if (DisplayValueLabel && renders != null && renders.Count > 0)
            {
                render = findRender(lable);
                if (render != null)
                {
                    lable = lable == null ? render.Value.ToString()
                        : lable + " " + render.Value.ToString();
                }
            }
        }

        private GISShapeRenderOption findRender(string lable)
        {
            return renders.Find(
               delegate (GISShapeRenderOption x)
               { return lable.IndexOf(x.CityName) != -1; });
        }

        private void addCategory(Dictionary<uint, int> categoryByColor, int i, GISShapeRenderOption render)
        {
            if (render != null)
            {
                uint oleColor = (uint)ColorTranslator.ToOle(render.Color);
                if (!categoryByColor.ContainsKey(oleColor))
                {
                    string name = sfLayer.Categories.Count.ToString();
                    MapWinGIS.ShapefileCategory cat = sfLayer.Categories.Add(name);
                    if (cat != null)
                    {
                        cat.DrawingOptions.FillColor = oleColor;
                        categoryByColor.Add(oleColor, sfLayer.Categories.Count - 1);
                    }
                }
                sfLayer.set_ShapeCategory(i, categoryByColor[oleColor]);
            }
        }

        private void GISPanel_SizeChanged(object sender, EventArgs e)
        {
            mapControl.ZoomToMaxExtents();
        }

        private void lbxLegend_DrawItem(object sender, DrawItemEventArgs e)
        {
            ListBox listBoxLegend = sender as ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is DTParameterRangeColor)
            {
                e.Graphics.FillRectangle(new SolidBrush((item as DTParameterRangeColor).Value), e.Bounds.X, e.Bounds.Y, 16, 16);
                text = ((DTParameterRange)item).RangeDescription + "  " + ((DTParameterRange)item).DesInfo;
            }
            else if (item is string)
            {
                text = item.ToString();
            }
            e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 20, e.Bounds.Y);
        }

        bool legendMoving = false;
        System.Drawing.Point legendLastLoc = System.Drawing.Point.Empty;
        System.Drawing.Point mouseLastLoc = System.Drawing.Point.Empty;
        private void lbxLegend_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button==MouseButtons.Left)
            {
                legendMoving = true;
                legendLastLoc = lbxLegend.Location;
                mouseLastLoc = Cursor.Position;
            }
        }

        private void lbxLegend_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                legendMoving = false;
                if (!mapControl.Bounds.IntersectsWith(lbxLegend.Bounds))
                {
                    lbxLegend.Location = legendLastLoc;
                }
            }
        }

        private void lbxLegend_MouseMove(object sender, MouseEventArgs e)
        {
            if (legendMoving && legendLastLoc != System.Drawing.Point.Empty)
            {
                lbxLegend.Anchor = AnchorStyles.None;

                System.Drawing.Point pCursor = Cursor.Position;

                int xOffset = pCursor.X - mouseLastLoc.X;
                int yOffset = pCursor.Y - mouseLastLoc.Y;

                lbxLegend.Left = legendLastLoc.X + xOffset;
                lbxLegend.Top = legendLastLoc.Y + yOffset;
            }
        }

        private Color pointColor = Color.Black;
        public Color PointColor
        {
            get { return pointColor; }
            set
            {
                pointColor = value;
                if (pntLayer != null)
                {
                    pntLayer.DefaultDrawingOptions.FillColor = (uint)ColorTranslator.ToOle(value);
                    mapControl.Redraw();
                }
            }
        }
    }

}
