﻿namespace MasterCom.RAMS.Func
{
    partial class EventBlockForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EventBlockForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miCompareReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miAnalyseEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTab = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportEBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSelEBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.imageList = new System.Windows.Forms.ImageList(this.components);
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.btnQueryFileCount = new System.Windows.Forms.ToolStripButton();
            this.tsBtnShowSn = new System.Windows.Forms.ToolStripButton();
            this.tsBtnLegendOption = new System.Windows.Forms.ToolStripButton();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnWeight = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDominCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRecessCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEvtName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLogfile = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPreType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReasonDsc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSolutionDsc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miCompareReplayEvent,
            this.miAnalyseEvent,
            this.toolStripMenuItem3,
            this.miExportExcel,
            this.miExportTab,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll,
            this.toolStripMenuItem2,
            this.miExportEBReport,
            this.miExportSelEBReport});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(194, 220);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(193, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miCompareReplayEvent
            // 
            this.miCompareReplayEvent.Name = "miCompareReplayEvent";
            this.miCompareReplayEvent.Size = new System.Drawing.Size(193, 22);
            this.miCompareReplayEvent.Text = "对比回放事件";
            this.miCompareReplayEvent.Click += new System.EventHandler(this.miCompareReplayEvent_Click);
            // 
            // miAnalyseEvent
            // 
            this.miAnalyseEvent.Name = "miAnalyseEvent";
            this.miAnalyseEvent.Size = new System.Drawing.Size(193, 22);
            this.miAnalyseEvent.Text = "智能分析...";
            this.miAnalyseEvent.Click += new System.EventHandler(this.miAnalyseEvent_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(190, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(193, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportTab
            // 
            this.miExportTab.Name = "miExportTab";
            this.miExportTab.Size = new System.Drawing.Size(193, 22);
            this.miExportTab.Text = "导出shp文件";
            this.miExportTab.Click += new System.EventHandler(this.miExportTab_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(190, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(193, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(193, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(190, 6);
            // 
            // miExportEBReport
            // 
            this.miExportEBReport.Name = "miExportEBReport";
            this.miExportEBReport.Size = new System.Drawing.Size(193, 22);
            this.miExportEBReport.Text = "导出所有汇聚点报告...";
            this.miExportEBReport.Click += new System.EventHandler(this.miExportEBReport_Click);
            // 
            // miExportSelEBReport
            // 
            this.miExportSelEBReport.Name = "miExportSelEBReport";
            this.miExportSelEBReport.Size = new System.Drawing.Size(193, 22);
            this.miExportSelEBReport.Text = "导出选中汇聚点报告...";
            this.miExportSelEBReport.Click += new System.EventHandler(this.miExportSelEBReport_Click);
            // 
            // imageList
            // 
            this.imageList.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList.ImageStream")));
            this.imageList.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList.Images.SetKeyName(0, "jt.gif");
            this.imageList.Images.SetKeyName(1, "local.gif");
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnQueryFileCount,
            this.tsBtnShowSn,
            this.tsBtnLegendOption});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.toolStrip1.Size = new System.Drawing.Size(1094, 25);
            this.toolStrip1.TabIndex = 1;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // btnQueryFileCount
            // 
            this.btnQueryFileCount.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btnQueryFileCount.Image = ((System.Drawing.Image)(resources.GetObject("btnQueryFileCount.Image")));
            this.btnQueryFileCount.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnQueryFileCount.Name = "btnQueryFileCount";
            this.btnQueryFileCount.Size = new System.Drawing.Size(108, 22);
            this.btnQueryFileCount.Text = "查询汇聚测试次数";
            this.btnQueryFileCount.Click += new System.EventHandler(this.btnQueryFileCount_Click);
            // 
            // tsBtnShowSn
            // 
            this.tsBtnShowSn.Checked = true;
            this.tsBtnShowSn.CheckOnClick = true;
            this.tsBtnShowSn.CheckState = System.Windows.Forms.CheckState.Checked;
            this.tsBtnShowSn.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsBtnShowSn.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnShowSn.Image")));
            this.tsBtnShowSn.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnShowSn.Name = "tsBtnShowSn";
            this.tsBtnShowSn.Size = new System.Drawing.Size(60, 22);
            this.tsBtnShowSn.Text = "显示序号";
            this.tsBtnShowSn.ToolTipText = "GIS显示汇聚序号";
            this.tsBtnShowSn.Click += new System.EventHandler(this.tsBtnShowSn_Click);
            // 
            // tsBtnLegendOption
            // 
            this.tsBtnLegendOption.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsBtnLegendOption.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnLegendOption.Image")));
            this.tsBtnLegendOption.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnLegendOption.Name = "tsBtnLegendOption";
            this.tsBtnLegendOption.Size = new System.Drawing.Size(60, 22);
            this.tsBtnLegendOption.Text = "图例设置";
            this.tsBtnLegendOption.Click += new System.EventHandler(this.tsBtnLegendOption_Click);
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnWeight);
            this.listViewTotal.AllColumns.Add(this.olvColumnAbCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnDominCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnRecessCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnEvtName);
            this.listViewTotal.AllColumns.Add(this.olvColumnTime);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLogfile);
            this.listViewTotal.AllColumns.Add(this.olvColumnRoadDesc);
            this.listViewTotal.AllColumns.Add(this.olvColumnAreaDesc);
            this.listViewTotal.AllColumns.Add(this.olvColumnPreType);
            this.listViewTotal.AllColumns.Add(this.olvColumnReasonDsc);
            this.listViewTotal.AllColumns.Add(this.olvColumnSolutionDsc);
            this.listViewTotal.AllColumns.Add(this.olvColumnFileCount);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnWeight,
            this.olvColumnAbCount,
            this.olvColumnDominCount,
            this.olvColumnRecessCount,
            this.olvColumnEvtName,
            this.olvColumnTime,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnLogfile,
            this.olvColumnRoadDesc,
            this.olvColumnAreaDesc,
            this.olvColumnPreType,
            this.olvColumnReasonDsc,
            this.olvColumnSolutionDsc,
            this.olvColumnFileCount});
            this.listViewTotal.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 25);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1094, 547);
            this.listViewTotal.TabIndex = 3;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.SelectedIndexChanged += new System.EventHandler(this.listViewTotal_SelectedIndexChanged);
            this.listViewTotal.KeyDown += new System.Windows.Forms.KeyEventHandler(this.listViewTotal_KeyDown);
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnWeight
            // 
            this.olvColumnWeight.HeaderFont = null;
            this.olvColumnWeight.Text = "权重";
            // 
            // olvColumnAbCount
            // 
            this.olvColumnAbCount.AspectName = "AbnormalEventCount";
            this.olvColumnAbCount.HeaderFont = null;
            this.olvColumnAbCount.Text = "事件数量";
            this.olvColumnAbCount.Width = 78;
            // 
            // olvColumnDominCount
            // 
            this.olvColumnDominCount.HeaderFont = null;
            this.olvColumnDominCount.Text = "显性事件数量";
            this.olvColumnDominCount.Width = 78;
            // 
            // olvColumnRecessCount
            // 
            this.olvColumnRecessCount.HeaderFont = null;
            this.olvColumnRecessCount.Text = "隐性事件数量";
            this.olvColumnRecessCount.Width = 78;
            // 
            // olvColumnEvtName
            // 
            this.olvColumnEvtName.HeaderFont = null;
            this.olvColumnEvtName.Text = "事件";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.AspectName = "";
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "测试时间";
            this.olvColumnTime.Width = 146;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 128;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 123;
            // 
            // olvColumnLogfile
            // 
            this.olvColumnLogfile.HeaderFont = null;
            this.olvColumnLogfile.Text = "测试文件";
            this.olvColumnLogfile.Width = 335;
            // 
            // olvColumnRoadDesc
            // 
            this.olvColumnRoadDesc.HeaderFont = null;
            this.olvColumnRoadDesc.Text = "道路";
            // 
            // olvColumnAreaDesc
            // 
            this.olvColumnAreaDesc.HeaderFont = null;
            this.olvColumnAreaDesc.Text = "网格";
            // 
            // olvColumnPreType
            // 
            this.olvColumnPreType.HeaderFont = null;
            this.olvColumnPreType.Text = "原因类型";
            // 
            // olvColumnReasonDsc
            // 
            this.olvColumnReasonDsc.HeaderFont = null;
            this.olvColumnReasonDsc.Text = "问题描述";
            // 
            // olvColumnSolutionDsc
            // 
            this.olvColumnSolutionDsc.HeaderFont = null;
            this.olvColumnSolutionDsc.Text = "解决方案";
            // 
            // olvColumnFileCount
            // 
            this.olvColumnFileCount.HeaderFont = null;
            this.olvColumnFileCount.Text = "测试次数";
            // 
            // EventBlockForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1094, 572);
            this.Controls.Add(this.listViewTotal);
            this.Controls.Add(this.toolStrip1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "EventBlockForm";
            this.ShowInTaskbar = false;
            this.Text = "事件位置汇聚列表";
            this.ctxMenu.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportTab;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.ImageList imageList;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem miAnalyseEvent;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem miExportEBReport;
        private System.Windows.Forms.ToolStripMenuItem miExportSelEBReport;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplayEvent;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnAbCount;
        private BrightIdeasSoftware.OLVColumn olvColumnEvtName;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLogfile;
        private BrightIdeasSoftware.OLVColumn olvColumnPreType;
        private BrightIdeasSoftware.OLVColumn olvColumnReasonDsc;
        private BrightIdeasSoftware.OLVColumn olvColumnSolutionDsc;
        private System.Windows.Forms.ToolStripButton btnQueryFileCount;
        private BrightIdeasSoftware.OLVColumn olvColumnFileCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnDominCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRecessCount;
        private BrightIdeasSoftware.OLVColumn olvColumnWeight;
        private System.Windows.Forms.ToolStripButton tsBtnShowSn;
        private System.Windows.Forms.ToolStripButton tsBtnLegendOption;
    }
}