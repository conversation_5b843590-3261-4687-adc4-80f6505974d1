﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningCellCondition
    {
        public string XlsFileName { get; set; }

        public bool OpenedTestEnable { get; set; } = true;

        public bool SampleLessEnable  { get; set; } = true;
        public int SampleLessCount  { get; set; } = 100;

        public bool OverCoverageEnable  { get; set; } = true;
        public double OverCoverageRadiusRatio  { get; set; } = 1.6;
        public double OverCoverageRate  { get; set; } = 0.3;
        public int OverCoverageMinSampleCount  { get; set; } = 20;
        public double OverCoverageMaxCellDistance  { get; set; } = 500;

        public bool WeakCoverageEnable  { get; set; } = true;
        public double WeakCoverageRsrp  { get; set; } = -105;
        public double WeakCoverageRate  { get; set; } = 0.1;
        public int WeakCoverageMinSampleCount  { get; set; } = 20;
        public double WeakCoverageMaxCellDistance  { get; set; } = 500;

        public bool WeakQualityEnable  { get; set; } = true;
        public double WeakQualityRsrp  { get; set; } = -105;
        public double WeakQualitySinr  { get; set; } = -3;
        public double WeakQualityRate  { get; set; } = 0.05;
        public int WeakQualityMinSampleCount  { get; set; } = 20;
        public double WeakQualityMaxCellDistance  { get; set; } = 500;
    }

    #region 评估分析器

    public interface ILtePlanningCellEvaluator
    {
        bool Enable { get; }
        int ProblemCount { get; }
        void DoWithTestPoint(LtePlanningTestPoint tp);
        void CalcResult();
        object GetResult();
        void Clear();
    }

    public class SummaryEvaluator : ILtePlanningCellEvaluator
    {
        public SummaryEvaluator(List<ILtePlanningCellEvaluator> evaluators)
        {
            this.evaluators = evaluators;
            this.mainCellDic = new Dictionary<LTECell, int>();
            this.nbCellDic = new Dictionary<LTECell, int>();
        }

        public bool Enable
        {
            get { return true; }
        }

        public int ProblemCount
        {
            get;
            private set;
        }
        
        public void DoWithTestPoint(LtePlanningTestPoint tp)
        {
            ++sampleCount;
            if (tp.MainCell != null)
            {
                ++mainSampleCount;
                if (!mainCellDic.ContainsKey(tp.MainCell))
                {
                    mainCellDic.Add(tp.MainCell, 0);
                }
                ++mainCellDic[tp.MainCell];
            }

            if (tp.NbCells.Count > 0)
            {
                ++nbSampleCount;
                foreach (LTECell nbCell in tp.NbCells)
                {
                    if (!nbCellDic.ContainsKey(nbCell))
                    {
                        nbCellDic.Add(nbCell, 0);
                    }
                    ++nbCellDic[nbCell];
                }
            }
        }

        public void CalcResult()
        {
            //
        }

        public object GetResult()
        {
            List<LtePlanningSummaryView> result = new List<LtePlanningSummaryView>();
            LtePlanningSummaryView summary = new LtePlanningSummaryView();
            result.Add(summary);

            summary.BtsCount = LtePlanningInfoManager.Instance.LteBTSs.Count;
            summary.CellCount = LtePlanningInfoManager.Instance.LteCells.Count;
            summary.SampleCount = sampleCount;
            summary.MainSampleCount = mainSampleCount;
            summary.NbSampleCount = nbSampleCount;
            summary.MainCellCount = mainCellDic.Count;
            summary.NbCellCount = nbCellDic.Count;
            foreach (ILtePlanningCellEvaluator eva in this.evaluators)
            {
                if (eva is SummaryEvaluator)
                {
                    //
                }
                else if (eva is NotOpenedEvaluator)
                {
                    summary.NotOpenedCount += eva.ProblemCount;
                }
                else if (eva is SampleLessCellEvaluator)
                {
                    summary.SampleLessCount += eva.ProblemCount;
                }
                else if (eva is WeakCoverageCellEvaluator)
                {
                    summary.WeakCoverageCount += eva.ProblemCount;
                }
                else if (eva is OverCoverageCellEvaluator)
                {
                    summary.OverCoverageCount += eva.ProblemCount;
                }
                else if (eva is WeakQualityCellEvaluator)
                {
                    summary.WeakQualityCount += eva.ProblemCount;
                }
            }
            return result;
        }

        public void Clear()
        {
            mainCellDic.Clear();
            nbCellDic.Clear();
        }

        private readonly List<ILtePlanningCellEvaluator> evaluators;
        private readonly Dictionary<LTECell, int> mainCellDic;
        private readonly Dictionary<LTECell, int> nbCellDic;
        private int sampleCount;
        private int mainSampleCount;
        private int nbSampleCount;
    }

    public class NotOpenedEvaluator : ILtePlanningCellEvaluator
    {
        public NotOpenedEvaluator(LtePlanningCellCondition cond)
        {
            this.Enable = cond.OpenedTestEnable;
            this.btsCountDic = new Dictionary<LTEBTS, int>();
            this.result = new List<LtePlanningBtsView>();
        }

        public bool Enable
        {
            get;
            private set;
        }

        public int ProblemCount
        {
            get;
            private set;
        }

        public void DoWithTestPoint(LtePlanningTestPoint tp)
        {
            List<LTECell> cellList = new List<LTECell>(tp.NbCells);
            cellList.Add(tp.MainCell);
            foreach (LTECell cell in cellList)
            {
                if (cell == null || cell.BelongBTS == null)
                {
                    continue;
                }
                if (!btsCountDic.ContainsKey(cell.BelongBTS))
                {
                    btsCountDic.Add(cell.BelongBTS, 0);
                }
                ++btsCountDic[cell.BelongBTS];
            }
        }

        public void CalcResult()
        {
            foreach (LTEBTS bts in LtePlanningInfoManager.Instance.LteBTSs)
            {
                if (btsCountDic.ContainsKey(bts))
                {
                    continue;
                }

                LtePlanningBtsView view = new LtePlanningBtsView(bts);
                result.Add(view);
            }
            ProblemCount = result.Count;
        }

        public object GetResult()
        {
            return new List<LtePlanningBtsView>(result);
        }

        public void Clear()
        {
            result.Clear();
            btsCountDic.Clear();
        }

        private readonly Dictionary<LTEBTS, int> btsCountDic;
        private readonly List<LtePlanningBtsView> result;
    }

    public class SampleLessCellEvaluator : ILtePlanningCellEvaluator
    {
        public SampleLessCellEvaluator(LtePlanningCellCondition cond)
        {
            this.Enable = cond.SampleLessEnable;
            this.sampleLessCount = cond.SampleLessCount;
            this.cellViewDic = new Dictionary<LTECell, SampleLessCellView>();
            this.result = new List<SampleLessCellView>();
        }

        public bool Enable
        {
            get;
            private set;
        }

        public int ProblemCount
        {
            get;
            private set;
        }

        public void DoWithTestPoint(LtePlanningTestPoint tp)
        {
            if (tp.MainCell == null)
            {
                return;
            }

            if (!cellViewDic.ContainsKey(tp.MainCell))
            {
                cellViewDic.Add(tp.MainCell, new SampleLessCellView(tp.MainCell));
            }
            ++cellViewDic[tp.MainCell].MainSampleCount;

            foreach (LTECell nbCell in tp.NbCells)
            {
                if (!cellViewDic.ContainsKey(nbCell))
                {
                    cellViewDic.Add(nbCell, new SampleLessCellView(nbCell));
                }
                ++cellViewDic[nbCell].NbSampleCount;
            }
        }

        public void CalcResult()
        {
            foreach (KeyValuePair<LTECell, SampleLessCellView> kvp in cellViewDic)
            {
                kvp.Value.SampleCount = kvp.Value.MainSampleCount + kvp.Value.NbSampleCount;
                if (kvp.Value.SampleCount > this.sampleLessCount)
                {
                    continue;
                }
                result.Add(kvp.Value);
            }
            ProblemCount = result.Count;
        }

        public object GetResult()
        {
            return new List<SampleLessCellView>(result);
        }

        public void Clear()
        {
            result.Clear();
            cellViewDic.Clear();
        }

        private readonly int sampleLessCount;
        private readonly Dictionary<LTECell, SampleLessCellView> cellViewDic;
        private readonly List<SampleLessCellView> result;
    }

    public class OverCoverageCellEvaluator : ILtePlanningCellEvaluator
    {
        private readonly LtePlanningCellCondition cond;
        public OverCoverageCellEvaluator(LtePlanningCellCondition cond)
        {
            this.Enable = cond.OverCoverageEnable;
            this.overCoverageRate = cond.OverCoverageRate;
            this.overCoverageRadiusRatio = cond.OverCoverageRadiusRatio;
            this.cellViewDic = new Dictionary<LTECell, OverCoverageCellView>();
            this.result = new List<OverCoverageCellView>();
            this.cond = cond;
        }

        public bool Enable
        {
            get;
            private set;
        }

        public int ProblemCount
        {
            get;
            private set;
        }

        public void DoWithTestPoint(LtePlanningTestPoint tp)
        {
            if (tp.MainCell == null)
            {
                return;
            }
            if (tp.CellDistance > cond.OverCoverageMaxCellDistance)
            {
                return;
            }

            LTECell mainCell = tp.MainCell;
            double cellRadius = LtePlanningInfoManager.Instance.GetCellRadius(mainCell);
            if (cellRadius <= 0)
            {
                return;
            }
            if (!cellViewDic.ContainsKey(mainCell))
            {
                cellViewDic.Add(mainCell, new OverCoverageCellView(mainCell));
            }
            ++cellViewDic[mainCell].SampleCount;

            double dis = MathFuncs.GetDistance(tp.TestPoint.Longitude, tp.TestPoint.Latitude, mainCell.Longitude, mainCell.Latitude);
            cellViewDic[mainCell].OverCoverCellRadius = cellRadius;
            if (dis > cellRadius * overCoverageRadiusRatio)
            {
                ++cellViewDic[mainCell].OverCoverSampleCount;
            }
        }

        public void CalcResult()
        {
            foreach (KeyValuePair<LTECell, OverCoverageCellView> kvp in cellViewDic)
            {
                if (kvp.Value.OverCoverSampleRate < this.overCoverageRate)
                {
                    continue;
                }
                if (kvp.Value.SampleCount < this.cond.OverCoverageMinSampleCount)
                {
                    continue;
                }
                result.Add(kvp.Value);
            }
            ProblemCount = result.Count;
        }

        public object GetResult()
        {
            return new List<OverCoverageCellView>(result);
        }

        public void Clear()
        {
            result.Clear();
            cellViewDic.Clear();
        }

        private readonly double overCoverageRate;
        private readonly double overCoverageRadiusRatio;
        private readonly Dictionary<LTECell, OverCoverageCellView> cellViewDic;
        private readonly List<OverCoverageCellView> result;
    }

    public class WeakCoverageCellEvaluator : ILtePlanningCellEvaluator
    {
        private readonly LtePlanningCellCondition cond;
        public WeakCoverageCellEvaluator(LtePlanningCellCondition cond)
        {
            this.Enable = cond.WeakCoverageEnable;
            this.weakCoverRsrp = cond.WeakCoverageRsrp;
            this.weakCoverRate = cond.WeakCoverageRate;
            this.cellViewDic = new Dictionary<LTECell, WeakCoverageCellView>();
            this.result = new List<WeakCoverageCellView>();
            this.cond = cond;
        }

        public bool Enable
        {
            get;
            private set;
        }

        public int ProblemCount
        {
            get;
            private set;
        }

        public void DoWithTestPoint(LtePlanningTestPoint tp)
        {
            if (tp.MainCell == null)
            {
                return;
            }
            if (tp.CellDistance > cond.WeakCoverageMaxCellDistance)
            {
                return;
            }

            LTECell mainCell = tp.MainCell;
            if (!cellViewDic.ContainsKey(mainCell))
            {
                cellViewDic.Add(mainCell, new WeakCoverageCellView(mainCell));
            }
            ++cellViewDic[mainCell].SampleCount;

            if (tp.Rsrp <= this.weakCoverRsrp)
            {
                ++cellViewDic[mainCell].WeakCoverSampleCount;
            }
        }

        public void CalcResult()
        {
            foreach (KeyValuePair<LTECell, WeakCoverageCellView> kvp in cellViewDic)
            {
                if (kvp.Value.WeakCoverSampleRate < this.weakCoverRate)
                {
                    continue;
                }
                if (kvp.Value.SampleCount < this.cond.WeakCoverageMinSampleCount)
                {
                    continue;
                }
                result.Add(kvp.Value);
            }
            ProblemCount = result.Count;
        }

        public object GetResult()
        {
            return new List<WeakCoverageCellView>(result);
        }

        public void Clear()
        {
            result.Clear();
            cellViewDic.Clear();
        }

        private readonly double weakCoverRsrp;
        private readonly double weakCoverRate;
        private readonly List<WeakCoverageCellView> result;
        private readonly Dictionary<LTECell, WeakCoverageCellView> cellViewDic;
    }

    public class WeakQualityCellEvaluator : ILtePlanningCellEvaluator
    {
        private readonly LtePlanningCellCondition cond;
        public WeakQualityCellEvaluator(LtePlanningCellCondition cond)
        {
            this.Enable = cond.WeakQualityEnable;
            this.weakQualRate = cond.WeakQualityRate;
            this.weakQualRsrp = cond.WeakQualityRsrp;
            this.weakQualSinr = cond.WeakQualitySinr;
            this.cellViewDic = new Dictionary<LTECell, WeakQualityCellView>();
            this.result = new List<WeakQualityCellView>();
            this.cond = cond;
        }

        public bool Enable
        {
            get;
            private set;
        }

        public int ProblemCount
        {
            get;
            private set;
        }

        public void DoWithTestPoint(LtePlanningTestPoint tp)
        {
            if (tp.MainCell == null)
            {
                return;
            }
            if (tp.CellDistance > cond.WeakQualityMaxCellDistance)
            {
                return;
            }

            LTECell mainCell = tp.MainCell;
            if (!cellViewDic.ContainsKey(mainCell))
            {
                cellViewDic.Add(mainCell, new WeakQualityCellView(mainCell));
            }
            ++cellViewDic[mainCell].SampleCount;

            if (tp.Rsrp != null && tp.Rsrp >= this.weakQualRsrp && tp.Sinr != null && tp.Sinr <= this.weakQualSinr)
            {
                ++cellViewDic[mainCell].WeakQualSampleCount;
            }
        }

        public void CalcResult()
        {
            foreach (KeyValuePair<LTECell, WeakQualityCellView> kvp in cellViewDic)
            {
                if (kvp.Value.WeakQualSampleRate < this.weakQualRate)
                {
                    continue;
                }
                if (kvp.Value.SampleCount < this.cond.WeakQualityMinSampleCount)
                {
                    continue;
                }
                result.Add(kvp.Value);
            }
            ProblemCount = result.Count;
        }

        public object GetResult()
        {
            return new List<WeakQualityCellView>(result);
        }

        public void Clear()
        {
            result.Clear();
            cellViewDic.Clear();
        }

        private readonly double weakQualRsrp;
        private readonly double weakQualSinr;
        private readonly double weakQualRate;
        private readonly List<WeakQualityCellView> result;
        private readonly Dictionary<LTECell, WeakQualityCellView> cellViewDic;
    }

    #endregion

    #region 用于结果呈现的数据结构

    public class LtePlanningSummaryView
    {
        public int BtsCount
        {
            get;
            set;
        }

        public int CellCount
        {
            get;
            set;
        }

        public int MainCellCount
        {
            get;
            set;
        }

        public int NbCellCount
        {
            get;
            set;
        }

        public int SampleCount
        {
            get;
            set;
        }

        public int MainSampleCount
        {
            get;
            set;
        }

        public int NbSampleCount
        {
            get;
            set;
        }

        public int NotOpenedCount
        {
            get;
            set;
        }

        public double NotOpenedRate
        {
            get { return BtsCount == 0 ? 0 : 1d * NotOpenedCount / BtsCount; }
        }

        public int SampleLessCount
        {
            get;
            set;
        }

        public double SampleLessRate
        {
            get { return CellCount == 0 ? 0 : 1d * SampleLessCount / CellCount; }
        }

        public int WeakCoverageCount
        {
            get;
            set;
        }

        public double WeakCoverageRate
        {
            get { return CellCount == 0 ? 0 : 1d * WeakCoverageCount / CellCount; }
        }

        public int OverCoverageCount
        {
            get;
            set;
        }

        public double OverCoverageRate
        {
            get { return CellCount == 0 ? 0 : 1d * OverCoverageCount / CellCount; }
        }

        public int WeakQualityCount
        {
            get;
            set;
        }

        public double WeakQualityRate
        {
            get { return CellCount == 0 ? 0 : 1d * WeakQualityCount / CellCount; }
        }
    }

    public class LtePlanningBtsView
    {
        public LtePlanningBtsView(LTEBTS bts)
        {
            this.lteBts = bts;
        }

        public LTEBTS LteBts
        {
            get { return lteBts; }
        }

        public string BtsName
        {
            get { return lteBts.Name; }
        }

        public string Address
        {
            get { return lteBts.Description; }
        }

        public int ENodeBID
        {
            get { return lteBts.BTSID; }
        }

        public double Longitude
        {
            get { return lteBts.Longitude; }
        }

        public double Latitude
        {
            get { return lteBts.Latitude; }
        }

        protected LTEBTS lteBts;
    }

    public class LtePlanningCellView
    {
        public LtePlanningCellView(LTECell lteCell)
        {
            this.lteCell = lteCell;
        }

        public int SampleCount
        {
            get;
            set;
        }

        public LTECell LteCell
        {
            get { return lteCell; }
        }

        public string BtsName
        {
            get { return lteCell.BTSName; }
        }

        public int ENodeBID
        {
            get { return lteCell.BelongBTS.BTSID; }
        }

        public double Longitude
        {
            get { return lteCell.Longitude; }
        }

        public double Latitude
        {
            get { return lteCell.Latitude; }
        }

        public string CellName
        {
            get { return lteCell.Name; }
        }

        public int CellID
        {
            get { return lteCell.SCellID; }
        }

        public int Tac
        {
            get { return lteCell.TAC; }
        }

        public int Eci
        {
            get { return lteCell.ECI; }
        }

        public int Earfcn
        {
            get { return lteCell.EARFCN; }
        }

        public int Pci
        {
            get { return lteCell.PCI; }
        }

        public int Direction
        {
            get { return lteCell.Direction; }
        }

        public int Downward
        {
            get { return lteCell.Downward; }
        }

        public int Altitude
        {
            get { return lteCell.Altitude; }
        }

        protected LTECell lteCell;
    }

    public class SampleLessCellView : LtePlanningCellView
    {
        public SampleLessCellView(LTECell lteCell) : base(lteCell)
        {
            MainSampleCount = 0;
            NbSampleCount = 0;
        }

        public int MainSampleCount
        {
            get;
            set;
        }

        public int NbSampleCount
        {
            get;
            set;
        }
    }

    public class OverCoverageCellView : LtePlanningCellView
    {
        public OverCoverageCellView(LTECell lteCell) : base(lteCell)
        {
            OverCoverSampleCount = 0;
        }

        public int OverCoverSampleCount
        {
            get;
            set;
        }

        public double OverCoverSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1D * OverCoverSampleCount / SampleCount; }
        }

        public double OverCoverCellRadius
        {
            get;
            set;
        }
    }

    public class WeakCoverageCellView : LtePlanningCellView
    {
        public WeakCoverageCellView(LTECell lteCell) : base(lteCell)
        {
        }

        public int WeakCoverSampleCount
        {
            get;
            set;
        }

        public double WeakCoverSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1D * WeakCoverSampleCount / SampleCount; }
        }
    }

    public class WeakQualityCellView : LtePlanningCellView
    {
        public WeakQualityCellView(LTECell lteCell) : base(lteCell)
        {
        }

        public int WeakQualSampleCount
        {
            get;
            set;
        }

        public double WeakQualSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1D * WeakQualSampleCount / SampleCount; }
        }
    }

    #endregion
}
