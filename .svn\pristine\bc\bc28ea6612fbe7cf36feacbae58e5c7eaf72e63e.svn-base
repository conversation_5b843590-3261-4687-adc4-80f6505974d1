﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTCellSetFormWithObjectList : Form
    {
        public CQTCellSetFormWithObjectList()
        {
            InitializeComponent();
            this.objectListViewCoverCell.ShowGroups = false;
            this.objectListViewCoverBTS.ShowGroups = false;
            this.objectListViewNBCell.ShowGroups = false;
            this.objectListViewCoverFrequencyPoint.ShowGroups = false;
            List<ComboBoxItem> listItem =  new List<ComboBoxItem>(new ComboBoxItem[]{
                                                                                                              new ComboBoxItem(InOutDoor.Unknown, true),
                                                                                                              new ComboBoxItem(InOutDoor.Indoor, false),
                                                                                                               new ComboBoxItem(InOutDoor.Outdoor, false)}) ;
            this.comboBoxInOutDoor.DataSource = listItem;
            this.comboBoxInOutDoor.DisplayMember = "Text";
            this.comboBoxInOutDoor.SelectedIndex = 0;
            this.comboBoxInOutDoor.SelectedIndexChanged += comboBoxInOutDoor_SelectedIndexChanged;
        }

        void comboBoxInOutDoor_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.refleshObjectListViewsDataSource();
        }

        List<ObjectListItemCoverCell> listItemCoverCell = null;
        List<ObjectListItemCoverBTS> listItemCoverBTS = null;
        List<ObjectListItemNBCell> listItemNBCell = null;
        List<ObjectListItemCoverFrequencyPoint> listItemCoverFrequencyPoint = null;

        public void fillData(List<ObjectListItemCoverCell> argListItemCoverCell, List<ObjectListItemCoverBTS> argListItemCoverBTS,
                                        List<ObjectListItemNBCell> argListItemNBCell, List<ObjectListItemCoverFrequencyPoint> argListItemCoverFrequencyPoint)
        {
            this.listItemCoverCell = argListItemCoverCell == null ? new List<ObjectListItemCoverCell>() : argListItemCoverCell;
            this.listItemCoverBTS = argListItemCoverBTS == null ? new List<ObjectListItemCoverBTS>() : argListItemCoverBTS;
            this.listItemNBCell = argListItemNBCell == null ? new List<ObjectListItemNBCell>() : argListItemNBCell;
            this.listItemCoverFrequencyPoint = argListItemCoverFrequencyPoint == null ? new List<ObjectListItemCoverFrequencyPoint>() : argListItemCoverFrequencyPoint;
            this.refleshObjectListViewsDataSource();
        }
        /// <summary>
        /// 根据室内外类型选项筛选显示目录，并更新显示界面
        /// </summary>
        private void refleshObjectListViewsDataSource()
        {
            List<ObjectListItemCoverCell> listItemCoverCellShow = new List<ObjectListItemCoverCell>();
            List<ObjectListItemCoverBTS> listItemCoverBTSShow = new List<ObjectListItemCoverBTS>();
            List<ObjectListItemNBCell> listItemNBCellShow = new List<ObjectListItemNBCell>();
            ComboBoxItem selectedItem = (this.comboBoxInOutDoor.DataSource as List<ComboBoxItem>)[this.comboBoxInOutDoor.SelectedIndex];
            InOutDoor inOutDoor = selectedItem.inOutDoor;
            bool isShowAll = selectedItem.isShowAll;

            #region 根据室内外类型选项筛选数据,并更新序号(调整序号宽度以便排序)
            int numWidth = this.listItemCoverCell.Count.ToString().Length;
            int sn = 1;
            foreach (ObjectListItemCoverCell item in this.listItemCoverCell)
            {
                if (isShowAll || inOutDoor == item.inOutDoor)
                {
                    item.SN = (sn++).ToString().PadLeft(numWidth, '0');
                    listItemCoverCellShow.Add(item);
                }
            }
            numWidth = this.listItemCoverBTS.Count.ToString().Length;
            sn = 1;
            foreach (ObjectListItemCoverBTS item in this.listItemCoverBTS)
            {
                if (isShowAll || inOutDoor == item.inOutDoor)
                {
                    item.SN = (sn++).ToString().PadLeft(numWidth, '0');
                    listItemCoverBTSShow.Add(item);
                }
            }
            numWidth = this.listItemNBCell.Count.ToString().Length;
            sn = 1;
            foreach (ObjectListItemNBCell item in this.listItemNBCell)
            {
                if (isShowAll || inOutDoor == item.inOutDoor)
                {
                    item.SN = (sn++).ToString().PadLeft(numWidth, '0');
                    listItemNBCellShow.Add(item);
                }
            }
            numWidth = this.listItemCoverFrequencyPoint.Count.ToString().Length;
            sn = 1;
            foreach (ObjectListItemCoverFrequencyPoint item in this.listItemCoverFrequencyPoint)
            {
                item.SN = (sn++).ToString().PadLeft(numWidth, '0');
            }
            #endregion

            this.objectListViewCoverCell.SetObjects(listItemCoverCellShow);
            this.objectListViewCoverBTS.SetObjects(listItemCoverBTSShow);
            this.objectListViewNBCell.SetObjects(listItemNBCellShow);
            this.objectListViewCoverFrequencyPoint.SetObjects(this.listItemCoverFrequencyPoint);

            this.objectListViewCoverCell.Refresh();
            this.objectListViewCoverBTS.Refresh();
            this.objectListViewNBCell.Refresh();
            this.objectListViewCoverFrequencyPoint.Refresh();
        }
    }

    public class ObjectListItemBase
    {
        //====================显示的字段=======================
        public string SN { get; set; }

        //=================不显示，仅用来标记属性的字段========================
        public InOutDoor inOutDoor { get; set; }
    }
    /// <summary>
    /// 覆盖频点列表所对应的一行
    /// </summary>
    public class ObjectListItemCoverFrequencyPoint : ObjectListItemBase
    {
        public string FreqCqtName { get; set; }
        public string FreqType { get; set; }
        public string Freq { get; set; }
        public string FreqCellCount { get; set; }
        public string FreqCellRatio { get; set; }
        public string FreqSampleCount { get; set; }
        public string FreqSampleRatio { get; set; }

    }

    /// <summary>
    /// 覆盖小区列表所对应的一行
    /// </summary>
    public class ObjectListItemCoverCell : ObjectListItemBase
    {
        //===============要显示的字段================
        public string CqtName { get; set; }
        public string Name { get; set; }
        public string LAC { get; set; }
        public string CI { get; set; }
        public string Type { get; set; }
        public string Property { get; set; }
        public string TestPointCount { get; set; }
        public string FileTestPointRatio { get; set; }
        public string RxLevMin { get; set; }
        public string RxLevMax { get; set; }
        public string RxLevAvg { get; set; }
        public string RxQualMin { get; set; }
        public string RxQualMax { get; set; }
        public string RxQualAvg { get; set; }
        public string DistanceMin { get; set; }
        public string DistanceMax { get; set; }
        public string DistanceAvg { get; set; }
        public string Longitude { get; set; }
        public string Latitude { get; set; }
        public string C_IMin { get; set; }
        public string C_IMax { get; set; }
        public string C_IAvg { get; set; }
        public string AreaPlaceDesc { get; set; }
    }

    /// <summary>
    /// 覆盖基站列表所对应的一行
    /// </summary>
    public class ObjectListItemCoverBTS : ObjectListItemBase
    {
        //===================显示的字段============================
        public string BTsCqtName { get; set; }
        public string BTSName { get; set; }
        public string BTSType { get; set; }
        public string BTSSampleCount { get; set; }
        public string BTSSampleRatio { get; set; }
        public string BTSRxlevMin { get; set; }
        public string BTSRxlevMax { get; set; }
        public string BTSRxlevAvg { get; set; }
        public string BTSRxQualMin { get; set; }
        public string BTSRxQualMax { get; set; }
        public string BTSRxQualAvg { get; set; }
        public string BTSDistanceMin { get; set; }
        public string BTSDistanceMax { get; set; }
        public string BTSDistanceAvg { get; set; }
        public string BTSLongitude { get; set; }
        public string BTSLatitude { get; set; }
        public string BTSC2IMin { get; set; }
        public string BTSC2IMax { get; set; }
        public string BTSC2IAvg { get; set; }
        public string BTSAreaPlaceDesc { get; set; }
    }

    /// <summary>
    /// 邻区中小区列表所对应的一行
    /// </summary>
    public class ObjectListItemNBCell : ObjectListItemBase
    {
        //========================要显示的字段=========================
        public string NBCqtName { get; set; }
        public string NBName { get; set; }
        public string NBLAC { get; set; }
        public string NBCI { get; set; }
        public string NBType { get; set; }
        public string Count { get; set; }
        public string NBRxlevAvg { get; set; }
    }

    public class ComboBoxItem
    {
        public InOutDoor inOutDoor { set; get; }
        public bool isShowAll { get; set; }
        public string Text { set; get; }

        public ComboBoxItem(InOutDoor argInOutDoor, bool argShowAll)
        {
            if (argShowAll) this.isShowAll = true;
            this.inOutDoor = argInOutDoor;
            if (argShowAll) this.Text = "全部";
            else this.Text = StringHelper.GetInOutDoorString(argInOutDoor);
        }
    }
}
