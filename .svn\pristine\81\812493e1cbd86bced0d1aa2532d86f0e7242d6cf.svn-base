﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakCoverReasonAnaSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.tbxSuggest = new System.Windows.Forms.TextBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.tbxDescription = new System.Windows.Forms.TextBox();
            this.simpleButtonDown = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonUp = new DevExpress.XtraEditors.SimpleButton();
            this.checkedListBoxControlReason = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numBeforeWeakRxLevGate = new DevExpress.XtraEditors.SpinEdit();
            this.numBeforeWeakSecdsGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.numNcellCountGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevGate = new DevExpress.XtraEditors.SpinEdit();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.numPoorBtsDisGate = new DevExpress.XtraEditors.SpinEdit();
            this.label11 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numRxLevMainLessNCell = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.lblNCell = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numLackNcellDisGate = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBeforeWeakRxLevGate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeforeWeakSecdsGate.Properties)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNcellCountGate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevGate.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorBtsDisGate.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMainLessNCell.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLackNcellDisGate.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox7);
            this.groupControl1.Controls.Add(this.groupBox6);
            this.groupControl1.Controls.Add(this.simpleButtonDown);
            this.groupControl1.Controls.Add(this.simpleButtonUp);
            this.groupControl1.Controls.Add(this.checkedListBoxControlReason);
            this.groupControl1.Controls.Add(this.simpleButtonCancel);
            this.groupControl1.Controls.Add(this.simpleButtonOK);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(584, 251);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "分析顺序";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.tbxSuggest);
            this.groupBox7.Location = new System.Drawing.Point(236, 131);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(338, 76);
            this.groupBox7.TabIndex = 7;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "优化建议";
            // 
            // tbxSuggest
            // 
            this.tbxSuggest.Location = new System.Drawing.Point(13, 20);
            this.tbxSuggest.Multiline = true;
            this.tbxSuggest.Name = "tbxSuggest";
            this.tbxSuggest.ReadOnly = true;
            this.tbxSuggest.Size = new System.Drawing.Size(312, 44);
            this.tbxSuggest.TabIndex = 5;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.tbxDescription);
            this.groupBox6.Location = new System.Drawing.Point(236, 38);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(338, 87);
            this.groupBox6.TabIndex = 6;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "分析场景说明";
            // 
            // tbxDescription
            // 
            this.tbxDescription.Location = new System.Drawing.Point(13, 20);
            this.tbxDescription.Multiline = true;
            this.tbxDescription.Name = "tbxDescription";
            this.tbxDescription.ReadOnly = true;
            this.tbxDescription.Size = new System.Drawing.Size(312, 56);
            this.tbxDescription.TabIndex = 5;
            // 
            // simpleButtonDown
            // 
            this.simpleButtonDown.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonDown.Appearance.Options.UseFont = true;
            this.simpleButtonDown.Location = new System.Drawing.Point(160, 98);
            this.simpleButtonDown.Name = "simpleButtonDown";
            this.simpleButtonDown.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonDown.TabIndex = 4;
            this.simpleButtonDown.Text = "↓";
            this.simpleButtonDown.Click += new System.EventHandler(this.simpleButtonDown_Click);
            // 
            // simpleButtonUp
            // 
            this.simpleButtonUp.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonUp.Appearance.Options.UseFont = true;
            this.simpleButtonUp.Location = new System.Drawing.Point(160, 58);
            this.simpleButtonUp.Name = "simpleButtonUp";
            this.simpleButtonUp.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonUp.TabIndex = 4;
            this.simpleButtonUp.Text = "↑";
            this.simpleButtonUp.Click += new System.EventHandler(this.simpleButtonUp_Click);
            // 
            // checkedListBoxControlReason
            // 
            this.checkedListBoxControlReason.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxControlReason.Appearance.Options.UseFont = true;
            this.checkedListBoxControlReason.Location = new System.Drawing.Point(24, 38);
            this.checkedListBoxControlReason.Name = "checkedListBoxControlReason";
            this.checkedListBoxControlReason.Size = new System.Drawing.Size(123, 192);
            this.checkedListBoxControlReason.TabIndex = 3;
            this.checkedListBoxControlReason.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxControlReason_SelectedIndexChanged);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.simpleButtonCancel.Location = new System.Drawing.Point(474, 213);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.simpleButtonOK.Location = new System.Drawing.Point(357, 213);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.AutoScroll = true;
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox8);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox5);
            this.splitContainerControl1.Panel1.Controls.Add(this.numRxLevGate);
            this.splitContainerControl1.Panel1.Controls.Add(this.label8);
            this.splitContainerControl1.Panel1.Controls.Add(this.label9);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox1);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox2);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox3);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(584, 682);
            this.splitContainerControl1.SplitterPosition = 425;
            this.splitContainerControl1.TabIndex = 3;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.labelControl6);
            this.groupBox8.Controls.Add(this.numBeforeWeakRxLevGate);
            this.groupBox8.Controls.Add(this.numBeforeWeakSecdsGate);
            this.groupBox8.Controls.Add(this.labelControl4);
            this.groupBox8.Controls.Add(this.labelControl5);
            this.groupBox8.Location = new System.Drawing.Point(35, 362);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(480, 55);
            this.groupBox8.TabIndex = 37;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "覆盖不稳定";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(329, 24);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(18, 12);
            this.labelControl6.TabIndex = 37;
            this.labelControl6.Text = "dBm";
            // 
            // numBeforeWeakRxLevGate
            // 
            this.numBeforeWeakRxLevGate.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numBeforeWeakRxLevGate.Location = new System.Drawing.Point(253, 20);
            this.numBeforeWeakRxLevGate.Name = "numBeforeWeakRxLevGate";
            this.numBeforeWeakRxLevGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numBeforeWeakRxLevGate.Properties.Appearance.Options.UseFont = true;
            this.numBeforeWeakRxLevGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBeforeWeakRxLevGate.Properties.Mask.EditMask = "f";
            this.numBeforeWeakRxLevGate.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numBeforeWeakRxLevGate.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numBeforeWeakRxLevGate.Size = new System.Drawing.Size(70, 20);
            this.numBeforeWeakRxLevGate.TabIndex = 36;
            // 
            // numBeforeWeakSecdsGate
            // 
            this.numBeforeWeakSecdsGate.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numBeforeWeakSecdsGate.Location = new System.Drawing.Point(73, 20);
            this.numBeforeWeakSecdsGate.Name = "numBeforeWeakSecdsGate";
            this.numBeforeWeakSecdsGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numBeforeWeakSecdsGate.Properties.Appearance.Options.UseFont = true;
            this.numBeforeWeakSecdsGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBeforeWeakSecdsGate.Properties.Mask.EditMask = "f0";
            this.numBeforeWeakSecdsGate.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numBeforeWeakSecdsGate.Size = new System.Drawing.Size(60, 20);
            this.numBeforeWeakSecdsGate.TabIndex = 9;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(19, 24);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(48, 12);
            this.labelControl4.TabIndex = 8;
            this.labelControl4.Text = "弱覆盖前";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(139, 23);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(108, 12);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "秒内，主服场强均≥";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.numNcellCountGate);
            this.groupBox5.Controls.Add(this.labelControl2);
            this.groupBox5.Controls.Add(this.labelControl3);
            this.groupBox5.Location = new System.Drawing.Point(35, 301);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(480, 55);
            this.groupBox5.TabIndex = 36;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "邻小区覆盖不足";
            // 
            // numNcellCountGate
            // 
            this.numNcellCountGate.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numNcellCountGate.Location = new System.Drawing.Point(95, 20);
            this.numNcellCountGate.Name = "numNcellCountGate";
            this.numNcellCountGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numNcellCountGate.Properties.Appearance.Options.UseFont = true;
            this.numNcellCountGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNcellCountGate.Properties.Mask.EditMask = "f0";
            this.numNcellCountGate.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numNcellCountGate.Size = new System.Drawing.Size(60, 20);
            this.numNcellCountGate.TabIndex = 9;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(19, 24);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(72, 12);
            this.labelControl2.TabIndex = 8;
            this.labelControl2.Text = "邻区个数少于";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(161, 23);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 12);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "个";
            // 
            // numRxLevGate
            // 
            this.numRxLevGate.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numRxLevGate.Location = new System.Drawing.Point(130, 16);
            this.numRxLevGate.Name = "numRxLevGate";
            this.numRxLevGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevGate.Properties.Appearance.Options.UseFont = true;
            this.numRxLevGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevGate.Properties.Mask.EditMask = "f";
            this.numRxLevGate.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLevGate.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numRxLevGate.Size = new System.Drawing.Size(70, 20);
            this.numRxLevGate.TabIndex = 35;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(33, 19);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(95, 12);
            this.label8.TabIndex = 33;
            this.label8.Text = "弱覆盖点场强 ＜";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(205, 19);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(23, 12);
            this.label9.TabIndex = 32;
            this.label9.Text = "dBm";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numPoorBtsDisGate);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Location = new System.Drawing.Point(35, 53);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(480, 55);
            this.groupBox1.TabIndex = 30;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "缺少规划站";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(118, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(95, 12);
            this.label1.TabIndex = 27;
            this.label1.Text = "米内没有GSM基站";
            // 
            // numPoorBtsDisGate
            // 
            this.numPoorBtsDisGate.EditValue = new decimal(new int[] {
            800,
            0,
            0,
            0});
            this.numPoorBtsDisGate.Location = new System.Drawing.Point(54, 20);
            this.numPoorBtsDisGate.Name = "numPoorBtsDisGate";
            this.numPoorBtsDisGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numPoorBtsDisGate.Properties.Appearance.Options.UseFont = true;
            this.numPoorBtsDisGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPoorBtsDisGate.Properties.Mask.EditMask = "f0";
            this.numPoorBtsDisGate.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numPoorBtsDisGate.Size = new System.Drawing.Size(58, 20);
            this.numPoorBtsDisGate.TabIndex = 26;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(19, 23);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(29, 12);
            this.label11.TabIndex = 25;
            this.label11.Text = "距离";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.numRxLevMainLessNCell);
            this.groupBox2.Controls.Add(this.labelControl18);
            this.groupBox2.Controls.Add(this.labelControl38);
            this.groupBox2.Location = new System.Drawing.Point(35, 240);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(480, 55);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "主服小区覆盖不足";
            // 
            // numRxLevMainLessNCell
            // 
            this.numRxLevMainLessNCell.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numRxLevMainLessNCell.Location = new System.Drawing.Point(172, 25);
            this.numRxLevMainLessNCell.Name = "numRxLevMainLessNCell";
            this.numRxLevMainLessNCell.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevMainLessNCell.Properties.Appearance.Options.UseFont = true;
            this.numRxLevMainLessNCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevMainLessNCell.Properties.Mask.EditMask = "f0";
            this.numRxLevMainLessNCell.Properties.MaxValue = new decimal(new int[] {
            110,
            0,
            0,
            0});
            this.numRxLevMainLessNCell.Properties.MinValue = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            this.numRxLevMainLessNCell.Size = new System.Drawing.Size(60, 20);
            this.numRxLevMainLessNCell.TabIndex = 9;
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Location = new System.Drawing.Point(21, 28);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(144, 12);
            this.labelControl18.TabIndex = 8;
            this.labelControl18.Text = "主服场强比最大邻区场强小";
            // 
            // labelControl38
            // 
            this.labelControl38.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl38.Appearance.Options.UseFont = true;
            this.labelControl38.Location = new System.Drawing.Point(238, 28);
            this.labelControl38.Name = "labelControl38";
            this.labelControl38.Size = new System.Drawing.Size(18, 12);
            this.labelControl38.TabIndex = 0;
            this.labelControl38.Text = "dBm";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.lblNCell);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.numLackNcellDisGate);
            this.groupBox3.Location = new System.Drawing.Point(35, 114);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(480, 55);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "邻区漏配";
            // 
            // lblNCell
            // 
            this.lblNCell.AutoSize = true;
            this.lblNCell.Location = new System.Drawing.Point(251, 28);
            this.lblNCell.Name = "lblNCell";
            this.lblNCell.Size = new System.Drawing.Size(197, 12);
            this.lblNCell.TabIndex = 5;
            this.lblNCell.Text = "米，且周围{0}米内，有其它GSM基站";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(17, 28);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(161, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "主服和邻区与采样点距离都≥";
            // 
            // numLackNcellDisGate
            // 
            this.numLackNcellDisGate.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numLackNcellDisGate.Location = new System.Drawing.Point(184, 23);
            this.numLackNcellDisGate.Name = "numLackNcellDisGate";
            this.numLackNcellDisGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLackNcellDisGate.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numLackNcellDisGate.Size = new System.Drawing.Size(60, 21);
            this.numLackNcellDisGate.TabIndex = 4;
            this.numLackNcellDisGate.EditValueChanged += new System.EventHandler(this.numLackNcellDisGate_EditValueChanged);
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.labelControl1);
            this.groupBox4.Location = new System.Drawing.Point(35, 178);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(480, 56);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "室分泄漏";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(21, 25);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 12);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "占用到室分小区";
            // 
            // WeakCoverReasonAnaSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoScroll = true;
            this.ClientSize = new System.Drawing.Size(584, 682);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "WeakCoverReasonAnaSetForm";
            this.Text = "弱覆盖原因条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBeforeWeakRxLevGate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeforeWeakSecdsGate.Properties)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNcellCountGate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevGate.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorBtsDisGate.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMainLessNCell.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLackNcellDisGate.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxControlReason;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUp;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDown;
        private System.Windows.Forms.TextBox tbxDescription;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.TextBox tbxSuggest;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numPoorBtsDisGate;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.SpinEdit numRxLevMainLessNCell;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.SpinEdit numRxLevGate;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SpinEdit numLackNcellDisGate;
        private System.Windows.Forms.Label lblNCell;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraEditors.SpinEdit numNcellCountGate;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private System.Windows.Forms.GroupBox groupBox8;
        private DevExpress.XtraEditors.SpinEdit numBeforeWeakSecdsGate;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numBeforeWeakRxLevGate;
        private DevExpress.XtraEditors.LabelControl labelControl6;

    }
}