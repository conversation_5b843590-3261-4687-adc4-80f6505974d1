﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat.Data;

namespace MasterCom.RAMS.Model
{
    public class DTDataHeader : FileInfo
    {
        public int DBValue { get; set; }

        public void FillHeader(MasterCom.RAMS.Net.Content content)
        {
            TestType = content.GetParamInt();
            DeviceType = content.GetParamInt();
            FileType = content.GetParamInt();
            ServiceType = content.GetParamInt();
            CarrierType = content.GetParamInt();
            ProjectID = content.GetParamInt();
            Batch = content.GetParamInt();
            ID = content.GetParamInt();
            Name = content.GetParamString();
            LogTable = content.GetParamString();
        }
    }

    public class DTDataHeaderManager
    {
        private static DTDataHeaderManager instance;
        public static DTDataHeaderManager GetInstance()
        {
            if (instance == null)
                instance = new DTDataHeaderManager();
            return instance;
        }
        public void Clear()
        {
            dtDataHeaderMap.Clear();
        }

        public void AddDTDataHeader(DTDataHeader header)
        {
            if (header == null)
            {
                return;
            }
            dtDataHeaderMap[header.ID] = header;
        }

        public void FillDTDataHeader(MasterCom.RAMS.Net.Content content)
        {
            DTDataHeader header = new DTDataHeader();
            header.FillHeader(content);
            AddDTDataHeader(header);
        }

        public DTDataHeader GetHeaderByFileID(int fileID)
        {
            if (dtDataHeaderMap.ContainsKey(fileID))
            {
                return dtDataHeaderMap[fileID];
            }
            else
            {
                return null;
            }
        }

        public Dictionary<int, DTDataHeader> GetHeaderMap()
        {
            return dtDataHeaderMap;
        }

        private readonly Dictionary<int, DTDataHeader> dtDataHeaderMap = new Dictionary<int, DTDataHeader>();
        public FileInfo[] GetFiles()
        {
            List<FileInfo> fis = new List<FileInfo>();
            foreach (FileInfo item in dtDataHeaderMap.Values)
            {
                fis.Add(item);
            }
            return fis.ToArray();
        }
    }
}
