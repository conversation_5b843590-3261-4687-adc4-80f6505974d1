﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMOverCoverReasonCondtion
    {
        public GSMOverCoverReasonCondtion()
        {
            InventoryDic = new Dictionary<string, bool>();
            InventoryDic.Add("缺少基站", true);
            InventoryDic.Add("邻区漏配", true);
            InventoryDic.Add("切换不合理", true);
            InventoryDic.Add("室分泄漏", true);
        }
        
        public float PoorBtsDisGate { get; set; } = 800;
        public float LackNcellDisGate { get; set; } = 700;
        public int MinSecondsOverCoverBefore { get; set; } = 5;
        public float RxLevOverGate { get; set; } = -90;
        public int IdealCoverBtsCount { get; set; } = 3;
        public float IdealCoverRadFactor { get; set; } = 1.6F;
        public Dictionary<string, bool> InventoryDic { get; set; }
    }
}
