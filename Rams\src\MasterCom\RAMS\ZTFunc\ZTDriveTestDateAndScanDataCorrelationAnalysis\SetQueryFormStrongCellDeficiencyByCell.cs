﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SetQueryFormStrongCellDeficiencyByCell : BaseDialog
    {
        protected ItemSelectionPanel projPanelDT;
        protected ItemSelectionPanel projPanelScan;
        protected ItemSelectionPanel servPanelDT;
        protected ItemSelectionPanel servPanelScan;
        protected QueryCondition CurCondition;
        protected MapFormItemSelection ItemSelection;
        Dictionary<TestPoint, tpGridposition> tpGridDicDT = null;
        Dictionary<TestPoint, tpGridposition> tpGridDicScan = null;
        List<markGrid> markGirds = null;
        List<TestPoint> tpInconnectionDTList = null;
        List<TestPoint> tpInconnectionScanList = null;

        public double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG;//当前运算中使用的值
        public double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT;//当前运算中使用的值
        public DbRect bounds { get; set; }

        private List<Item_Maincell> item_MaincellList = null;

        /// <summary>
        /// 栅格内采样点的最强信号小区字典
        /// </summary>
        Dictionary<string, Cell> gridStrongestCellDic = null;
        /// <summary>
        /// 栅格对应其最强小区电平字典
        /// </summary>
        Dictionary<string, int> gridStrongestRxlevDic = null;
        /// <summary>
        /// 栅格对应的异常采样点数目字典
        /// </summary>
        Dictionary<string, int> gridAbnormalSampleCountDic = null;
        /// <summary>
        /// 栅格对应的采样点数目字典
        /// </summary>
        Dictionary<string, int> gridSampleCountDic = null;

        public SetQueryFormStrongCellDeficiencyByCell(MainModel mModel, MapFormItemSelection itemSelection, QueryCondition condition)
        {
            InitializeComponent();

            mainModel = mModel;
            CurCondition = condition;
            ItemSelection = itemSelection;

            dateTimePickerBeginTimeDT.Value = DateTime.Now.AddDays(-1);
            dateTimePickerBeginTimeScan.Value = DateTime.Now.AddDays(-1);
            dateTimePickerEndTimeDT.Value = DateTime.Now;
            dateTimePickerEndTimeScan.Value = DateTime.Now;

            listViewProjectDT.Items.Clear();
            listViewProjectScan.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanelDT = new ItemSelectionPanel(toolStripDropDownProjectDT, listViewProjectDT, lbProjCountDT, itemSelection, "Project", true);
                projPanelScan = new ItemSelectionPanel(toolStripDropDownProjectScan, listViewProjectScan, lbProjCountScan, itemSelection, "Project", true);
                toolStripDropDownProjectDT.Items.Clear();
                toolStripDropDownProjectScan.Items.Clear();
                projPanelDT.FreshItems();
                projPanelScan.FreshItems();
                toolStripDropDownProjectDT.Items.Add(new ToolStripControlHost(projPanelDT));
                toolStripDropDownProjectScan.Items.Add(new ToolStripControlHost(projPanelScan));
            }

            listViewServiceDT.Items.Clear();
            listViewServiceScan.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelDT = new ItemSelectionPanel(toolStripDropDownServiceDT, listViewServiceDT, lbSvCountDT, itemSelection, "ServiceType", true);
                servPanelScan = new ItemSelectionPanel(toolStripDropDownServiceScan, listViewServiceScan, lbSvCountScan, itemSelection, "ServiceType", true);
                toolStripDropDownServiceDT.Items.Clear();
                toolStripDropDownServiceScan.Items.Clear();
                servPanelDT.FreshItems();
                servPanelScan.FreshItems();
                toolStripDropDownServiceDT.Items.Add(new ToolStripControlHost(servPanelDT));
                toolStripDropDownServiceScan.Items.Add(new ToolStripControlHost(servPanelScan));
            }

            setDefaultService();

            if (mainModel.ConditionRecorderSCellDeficencyByCell != null && mainModel.ConditionRecorderSCellDeficencyByCell.kind == conditionRecorder.Kind.StrongCellDeficencyByCell)
            {
                conditionRecorder recorder = mainModel.ConditionRecorderSCellDeficencyByCell;
                this.dateTimePickerBeginTimeDT.Value = recorder.dateTimePickerBeginTimeDTValue;
                this.dateTimePickerBeginTimeScan.Value = recorder.dateTimePickerBeginTimeScanValue;
                this.dateTimePickerEndTimeDT.Value = recorder.dateTimePickerEndTimeDTValue;
                this.dateTimePickerEndTimeScan.Value = recorder.dateTimePickerEndTimeScanValue;
                this.listViewProjectDT.Items.Clear();
                this.listViewProjectScan.Items.Clear();
                this.listViewServiceDT.Items.Clear();
                this.listViewServiceScan.Items.Clear();
                foreach (saveItem item in recorder.listViewProjectDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewProjectScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectScan.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceScan.Items.Add(lvi);
                }
                this.trackBarChannel.Value = recorder.trackBarChannelValue;
                this.trackBarChannel_Scroll(null, null);
            }
        }

        private void setDefaultService()
        {
            ListViewItem lviDt = new ListViewItem();
            lviDt.Text = "GSM语音业务";
            lviDt.Tag = 1;
            this.listViewServiceDT.Items.Add(lviDt);

            ListViewItem lviScan = new ListViewItem();
            lviScan.Text = "扫频业务";
            lviScan.Tag = 12;
            this.listViewServiceScan.Items.Add(lviScan);
        }

        private void buttonProjDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjDT.Width, buttonProjDT.Height);
            toolStripDropDownProjectDT.Show(buttonProjDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjScan.Width, buttonProjScan.Height);
            toolStripDropDownProjectScan.Show(buttonProjScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServDT.Width, buttonServDT.Height);
            toolStripDropDownServiceDT.Show(buttonServDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServScan.Width, buttonServScan.Height);
            toolStripDropDownServiceScan.Show(buttonServScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void trackBarChannel_Scroll(object sender, EventArgs e)
        {
            if (trackBarChannel.Value == 1)
            {
                label900.ForeColor = Color.Red;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value == 2)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Red;
                label900and1800.ForeColor = Color.Black;
            }
            else if (trackBarChannel.Value == 3)
            {
                label900.ForeColor = Color.Black;
                label1800.ForeColor = Color.Black;
                label900and1800.ForeColor = Color.Red;
            }
        }

        private bool isValidCondition()
        {
            if (dateTimePickerBeginTimeDT.Value > dateTimePickerEndTimeDT.Value)
            {
                MessageBox.Show("查询路测数据，结束时间必须大于开始时间。");
            }
            if (dateTimePickerBeginTimeScan.Value > dateTimePickerEndTimeScan.Value)
            {
                MessageBox.Show("查询扫频数据，结束时间必须大于开始时间。");
            }
            else if (listViewProjectDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个项目。");
            }
            else if (listViewServiceDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个业务。");
            }
            else if (listViewProjectScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个项目。");
            }
            else if (listViewServiceScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个业务。");
            }
            else
                return true;
            return false;
        }

        private void buttonQuery_Click(object sender, EventArgs e)
        {
            conditionRecorder recorder = new conditionRecorder();
            recorder.fill(conditionRecorder.Kind.StrongCellDeficencyByCell, this.dateTimePickerBeginTimeDT.Value, this.dateTimePickerBeginTimeScan.Value, this.dateTimePickerEndTimeDT.Value
                , this.dateTimePickerEndTimeScan.Value, this.trackBarChannel.Value);
            recorder.fillCollection(this.listViewProjectDT.Items, this.listViewProjectScan.Items, this.listViewServiceDT.Items, this.listViewServiceScan.Items);
            mainModel.ConditionRecorderSCellDeficencyByCell = recorder;

            item_MaincellList = new List<Item_Maincell>();

            tpGridDicDT = queryDTData();
            if (tpGridDicDT == null || tpGridDicDT.Count == 0)
                return;
            tpGridDicScan = queryScanData();
            if (tpGridDicScan == null || tpGridDicScan.Count == 0)
                return;

            MapGridLayer gridShowLayer = mainModel.MainForm.GetMapForm().GetGridShowLayer();
            GRID_SPAN_LONG = gridShowLayer.GRID_SPAN_LONG;
            GRID_SPAN_LAT = gridShowLayer.GRID_SPAN_LAT;

            DIYQueryCoverGridByRegion GridQuery = new DIYQueryCoverGridByRegion(mainModel);
            GridQuery.SetQueryCondition(CurCondition);
            GridQuery.Query();

            WaitBox.Show(compareTpGridDicDTToTpGridDicScan);//保留DT和Scan都在同一栅格内的采样点集合

            WaitBox.Show(calAbnormalSamplePct);  //计算各个栅格内异常采样点的比例

            fireShowForm();

            clearTempObjects();

            this.Close();
        }

        private Dictionary<TestPoint, tpGridposition> queryDTData()
        {
            if (isValidCondition())
            {
                DIYSampleToGridQueryByRegion sampleToGridQueryForDT = new DIYSampleToGridQueryByRegion(mainModel);
                CurCondition.Periods.Clear();
                CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeDT.Value.Date, dateTimePickerEndTimeDT.Value.Date.AddDays(1).AddMilliseconds(-1)));
                foreach (ListViewItem item in listViewProjectDT.Items)
                {
                    CurCondition.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in listViewServiceDT.Items)
                {
                    CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
                }
                CurCondition.DistrictIDs.Clear();
                CurCondition.DistrictIDs.Add(mainModel.DistrictID);
                sampleToGridQueryForDT.CurCondition = CurCondition;
                sampleToGridQueryForDT.Query();

                return sampleToGridQueryForDT.tpGridDic;
            }
            return new Dictionary<TestPoint, tpGridposition>();
        }

        private Dictionary<TestPoint, tpGridposition> queryScanData()
        {
            DIYSampleToGridQueryByRegion sampleToGridQueryForScan = new DIYSampleToGridQueryByRegion(mainModel);
            CurCondition.Periods.Clear();
            CurCondition.Periods.Add(new TimePeriod(dateTimePickerBeginTimeScan.Value.Date, dateTimePickerEndTimeScan.Value.Date.AddDays(1).AddMilliseconds(-1)));
            CurCondition.Projects.Clear();
            CurCondition.ServiceTypes.Clear();
            foreach (ListViewItem item in listViewProjectScan.Items)
            {
                CurCondition.Projects.Add((byte)(int)item.Tag);
            }
            foreach (ListViewItem item in listViewServiceScan.Items)
            {
                CurCondition.ServiceTypes.Add((byte)(int)item.Tag);
            }
            CurCondition.DistrictIDs.Clear();
            CurCondition.DistrictIDs.Add(mainModel.DistrictID);
            sampleToGridQueryForScan.CurCondition = CurCondition;
            sampleToGridQueryForScan.Query();

            return sampleToGridQueryForScan.tpGridDic;
        }

        public void compareTpGridDicDTToTpGridDicScan()
        {
            try
            {
                WaitBox.Text = "正在调整频段……";
                WaitBox.ProgressPercent = 30;

                markGirds = new List<markGrid>();
                tpInconnectionDTList = new List<TestPoint>();
                tpInconnectionScanList = new List<TestPoint>();

                List<TestPoint> curBandTestpointDT = new List<TestPoint>();
                List<TestPoint> curBandTestpointScan = new List<TestPoint>();
                addCurBandTestpointDT(curBandTestpointDT);
                addCurBandTestpointScan(curBandTestpointScan);
                WaitBox.ProgressPercent = 50;

                markGrid markGrid = null;
                bool needAdd = false;
                foreach (TestPoint tpDT in curBandTestpointDT)
                {
                    addTPDT(ref markGrid, ref needAdd, tpDT);
                }

                foreach (TestPoint tpScan in curBandTestpointScan)
                {
                    addTPScan(ref markGrid, ref needAdd, tpScan);
                }

                foreach (markGrid mg in markGirds)
                {
                    tpInconnectionDTList.AddRange(mg.testPointDtList);
                    tpInconnectionScanList.AddRange(mg.testPointScanList);
                }

                WaitBox.ProgressPercent = 90;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void addCurBandTestpointDT(List<TestPoint> curBandTestpointDT)
        {
            foreach (TestPoint tpDT in tpGridDicDT.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)tpDT["BCCH"];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointDT.Add(tpDT);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointDT.Add(tpDT);
                }
            }
        }

        private void addCurBandTestpointScan(List<TestPoint> curBandTestpointScan)
        {
            foreach (TestPoint tpScan in tpGridDicScan.Keys)
            {
                if (this.trackBarChannel.Value == 1)   //选择900频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch < 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else if (this.trackBarChannel.Value == 2)  //选择1800频段
                {
                    short? bcch = (short?)(int?)tpScan["GSCAN_BCCH", 0];
                    if (bcch != null && bcch >= 512)
                    {
                        curBandTestpointScan.Add(tpScan);
                    }
                }
                else  //选择900和1800频段
                {
                    curBandTestpointScan.Add(tpScan);
                }
            }
        }

        private void addTPDT(ref markGrid markGrid, ref bool needAdd, TestPoint tpDT)
        {
            tpGridposition posDT = tpGridDicDT[tpDT];
            if (markGirds.Count == 0)
            {
                markGrid = new markGrid(posDT.row, posDT.col);
                markGrid.hasDtSample = true;
                markGirds.Add(markGrid);
            }
            else
            {
                addTPDT(ref markGrid, ref needAdd, tpDT, posDT);
            }
        }

        private void addTPDT(ref markGrid markGrid, ref bool needAdd, TestPoint tpDT, tpGridposition posDT)
        {
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posDT.row && mg.col == posDT.col)
                {
                    mg.testPointDtList.Add(tpDT);  //往栅格加入该采样点
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        needAdd = true;
                        markGrid = new markGrid(posDT.row, posDT.col);
                        markGrid.testPointDtList.Add(tpDT);
                        markGrid.hasDtSample = true;
                    }
                }
            }
            if (needAdd)    //将含有路测采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private void addTPScan(ref markGrid markGrid, ref bool needAdd, TestPoint tpScan)
        {
            tpGridposition posScan = tpGridDicScan[tpScan];
            int num = markGirds.Count;
            int count = 0;
            foreach (markGrid mg in markGirds)
            {
                if (mg.row == posScan.row && mg.col == posScan.col)
                {
                    mg.testPointScanList.Add(tpScan);  //往栅格加入该采样点
                    mg.hasScanSample = true;
                    needAdd = false;
                }
                else
                {
                    count++;
                    if (count == num)
                    {
                        markGrid = new markGrid(posScan.row, posScan.col);
                        markGrid.hasScanSample = true;
                        markGrid.testPointScanList.Add(tpScan);
                        needAdd = true;
                    }
                }
            }
            if (needAdd)    //将含有扫频采样点的栅格收集标记起来
                markGirds.Add(markGrid);
        }

        private void calAbnormalSamplePct()
        {
            try
            {
                WaitBox.Text = "计算各个栅格内异常采样点的比例";
                WaitBox.ProgressPercent = 20;
                dealGridScanSample();
                WaitBox.ProgressPercent = 50;
                dealGridDTSample();
                WaitBox.ProgressPercent = 90;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void dealGridScanSample()
        {
            gridStrongestCellDic = new Dictionary<string, Cell>();
            gridStrongestRxlevDic = new Dictionary<string, int>();
            foreach (TestPoint testpointScan in tpInconnectionScanList)  //统计扫频数据在栅格内的最强小区
            {
                bool hasStrongCell = false;
                float? rxLevSub = (float?)testpointScan["GSCAN_RxLev", 0];
                short? bcch = (short?)(int?)testpointScan["GSCAN_BCCH", 0];
                byte? bsic = (byte?)(int?)testpointScan["GSCAN_BSIC", 0];
                if (rxLevSub != null && bcch != null && bsic != null)
                {
                    Cell cell = CellManager.GetInstance().GetNearestCell(testpointScan.DateTime, (short)bcch, (byte)bsic, testpointScan.Longitude, testpointScan.Latitude);
                    hasStrongCell = judgeHasStrongCell(testpointScan, hasStrongCell, cell);
                    if (hasStrongCell)
                    {
                        addGridScanStrongest(testpointScan, rxLevSub, cell);
                    }
                }
            }
        }

        private void addGridScanStrongest(TestPoint testpointScan, float? rxLevSub, Cell cell)
        {
            int pGridUnitRow = (int)(Math.Round((bounds.y2 - testpointScan.Latitude) / GRID_SPAN_LAT));
            int pGridUnitCol = (int)(Math.Round((testpointScan.Longitude - bounds.x1) / GRID_SPAN_LONG));

            if (pGridUnitRow >= mainModel.CurGridColorUnitMatrix.MaxRowIdx)
            {
                pGridUnitRow = mainModel.CurGridColorUnitMatrix.MaxRowIdx - 1;
            }
            else if (pGridUnitCol >= mainModel.CurGridColorUnitMatrix.MaxColIdx)
            {
                pGridUnitCol = mainModel.CurGridColorUnitMatrix.MaxColIdx - 1;
            }

            string rowCol = pGridUnitRow + "_" + pGridUnitCol;
            if (!gridStrongestCellDic.ContainsKey(rowCol))
            {
                gridStrongestRxlevDic.Add(rowCol, (int)rxLevSub);
                gridStrongestCellDic.Add(rowCol, cell);
            }
            else
            {
                if (rxLevSub > gridStrongestRxlevDic[rowCol])
                {
                    gridStrongestRxlevDic[rowCol] = (int)rxLevSub;
                    gridStrongestCellDic[rowCol] = cell;
                }
            }
        }

        private static bool judgeHasStrongCell(TestPoint testpointScan, bool hasStrongCell, Cell cell)
        {
            if (cell != null)
            {
                if (MainModel.GetInstance().SystemConfigInfo.distLimit)//距离限制设置
                {
                    if (cell.GetDistance(testpointScan.Longitude, testpointScan.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                    {
                        hasStrongCell = true;
                    }
                }
                else
                {
                    hasStrongCell = true;
                }
            }

            return hasStrongCell;
        }

        private void dealGridDTSample()
        {
            gridAbnormalSampleCountDic = new Dictionary<string, int>();
            gridSampleCountDic = new Dictionary<string, int>();
            foreach (TestPoint testpointDt in tpInconnectionDTList)    //用路测数据查找采样点对应的小区，
            {
                bool hasStrongCell = false;
                bool hasStrongNbcell = false;
                float? rxLevSub = (float?)(short?)testpointDt["RxLevSub"];
                Cell cell = CellManager.GetInstance().GetNearestCell(testpointDt.DateTime, (ushort?)(int?)testpointDt["LAC"], (ushort?)(int?)testpointDt["CI"], (short?)testpointDt["BCCH"], (byte?)testpointDt["BSIC"], testpointDt.Longitude, testpointDt.Latitude);
                hasStrongCell = judgeHasStrongCell(testpointDt, hasStrongCell, rxLevSub, cell);

                List<Cell> nbcellList = getNbcellList(testpointDt, ref hasStrongNbcell);

                int pGridUnitRow = (int)(Math.Round((bounds.y2 - testpointDt.Latitude) / GRID_SPAN_LAT));
                int pGridUnitCol = (int)(Math.Round((testpointDt.Longitude - bounds.x1) / GRID_SPAN_LONG));
                if (pGridUnitRow >= mainModel.CurGridColorUnitMatrix.MaxRowIdx)
                {
                    pGridUnitRow = mainModel.CurGridColorUnitMatrix.MaxRowIdx - 1;
                }
                else if (pGridUnitCol >= mainModel.CurGridColorUnitMatrix.MaxColIdx)
                {
                    pGridUnitCol = mainModel.CurGridColorUnitMatrix.MaxColIdx - 1;
                }
                string rowCol = pGridUnitRow + "_" + pGridUnitCol;

                if (!gridSampleCountDic.ContainsKey(rowCol)) //记录栅格内的路测采样点数目
                    gridSampleCountDic.Add(rowCol, 1);
                else
                    gridSampleCountDic[rowCol]++;

                bool isMainCellExist = true; //主小区与同栅格的扫频小区符合
                bool isNbCellExist = true; //邻区与栅格的扫频小区符合
                if ((hasStrongCell || hasStrongNbcell) && gridStrongestCellDic.ContainsKey(rowCol))
                {
                    addValidRetCell(cell, nbcellList, rowCol, ref isMainCellExist, ref isNbCellExist);
                }
            }
        }

        private static bool judgeHasStrongCell(TestPoint testpointDt, bool hasStrongCell, float? rxLevSub, Cell cell)
        {
            if (cell != null && rxLevSub != null)
            {
                if (MainModel.GetInstance().SystemConfigInfo.distLimit)//距离限制设置
                {
                    if (cell.GetDistance(testpointDt.Longitude, testpointDt.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                    {
                        hasStrongCell = true;
                    }
                }
                else
                {
                    hasStrongCell = true;
                }
            }

            return hasStrongCell;
        }

        private static List<Cell> getNbcellList(TestPoint testpointDt, ref bool hasStrongNbcell)
        {
            List<Cell> nbcellList = new List<Cell>();
            for (int i = 0; i < 6; i++)
            {
                short? nbcch = (short?)testpointDt["N_BCCH", i];
                byte? nbsic = (byte?)testpointDt["N_BSIC", i];
                float? nRxLev = (float?)(short?)testpointDt["N_RxLev", i];

                if (nbcch != null && nbsic != null && nRxLev != null)
                {
                    Cell nbCell = CellManager.GetInstance().GetNearestCell(testpointDt.DateTime, (short)nbcch, (byte)nbsic, testpointDt.Longitude, testpointDt.Latitude);

                    if (nbCell != null && MainModel.GetInstance().SystemConfigInfo.distLimit
                        && nbCell.GetDistance(testpointDt.Longitude, testpointDt.Latitude) < CD.MAX_COV_DISTANCE_GSM)//距离限制设置
                    {
                        nbcellList.Add(nbCell);
                        hasStrongNbcell = true;
                    }
                }
            }

            return nbcellList;
        }

        private void addValidRetCell(Cell cell, List<Cell> nbcellList, string rowCol, ref bool isMainCellExist, ref bool isNbCellExist)
        {
            if (cell != null && gridStrongestCellDic[rowCol] != cell)
            {
                isMainCellExist = false;
            }

            if (nbcellList.Count != 0)
            {
                if (!isFoundInNbCell(nbcellList, gridStrongestCellDic[rowCol]))
                {
                    isNbCellExist = false;
                }
            }
            else
            {
                isNbCellExist = false;
            }
            if (!isMainCellExist && !isNbCellExist)  //在同一栅格内，路测采样点的主小区及邻区与扫频的小区不同，或不存在小区，记为异常采样点
            {
                if (!gridAbnormalSampleCountDic.ContainsKey(rowCol))
                {
                    gridAbnormalSampleCountDic.Add(rowCol, 1);
                }
                else
                {
                    gridAbnormalSampleCountDic[rowCol]++;
                }

                AddRetCell(cell, rowCol);
            }
        }

        private void AddRetCell(Cell cell,string rowCol)
        {
            bool needAddNewItem = true;
            if (item_MaincellList.Count != 0)
            {
                foreach (Item_Maincell mc in item_MaincellList)
                {
                    if (mc.maincellName == cell.Name)
                    {
                        needAddNewItem = false;
                        break;
                    }
                }
            }
            if (needAddNewItem)  //添加新的主服小区下的信息
            {
                Item_Maincell item_Maincell = new Item_Maincell();
                item_Maincell.maincellName = cell.Name;
                item_Maincell.lacCi = cell.LAC.ToString() + "_" + cell.CI.ToString();
                item_Maincell.abnormalSampleCount = gridAbnormalSampleCountDic[rowCol];
                item_Maincell.allSampleCount = gridSampleCountDic[rowCol];
                item_Maincell.cell = cell;
                item_MaincellList.Add(item_Maincell);


                Childitem_MissStrongCell childitem_missStrongCell = new Childitem_MissStrongCell();
                childitem_missStrongCell.cellName = gridStrongestCellDic[rowCol].Name;
                childitem_missStrongCell.lacCi = gridStrongestCellDic[rowCol].LAC.ToString() + "_" + gridStrongestCellDic[rowCol].CI.ToString();
                childitem_missStrongCell.cell = gridStrongestCellDic[rowCol];

                List<Childitem_MissStrongCell> childitem_MissStrongCellList = new List<Childitem_MissStrongCell>();
                childitem_MissStrongCellList.Add(childitem_missStrongCell);
                item_Maincell.missStrongCellList = childitem_MissStrongCellList;
            }
            else
            {
                foreach (Item_Maincell mc in item_MaincellList)
                {
                    if (mc.maincellName == cell.Name)
                    {
                        mc.abnormalSampleCount = gridAbnormalSampleCountDic[rowCol];
                        mc.allSampleCount = gridSampleCountDic[rowCol];
                    }
                }
            }
        }

        private bool isFoundInNbCell(List<Cell> nbCellList, Cell cell)
        {
            List<Cell> retCells = new List<Cell>();
            foreach (Cell c in nbCellList)
            {
                if (cell != c)
                {
                    retCells.Add(c);
                }
            }
            if (retCells.Count != 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 清除之前用于计算的对象，减少其对内存的占用
        /// </summary>
        private void clearTempObjects()
        {
            this.markGirds.Clear();
            this.Dispose();
            //GC.Collect();
        }

        private void fireShowForm()
        {
            mainModel.Item_MaincellList = item_MaincellList;
            StrongCellDeficiencyForm frm = MainModel.GetInstance().CreateResultForm(typeof(StrongCellDeficiencyForm)) as StrongCellDeficiencyForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

    }

    public class Item_Maincell
    {
        public string maincellName { get; set; } //主服小区名称
        public string lacCi { get; set; }    //主服小区LAC-CI
        public int allSampleCount { get; set; } = 0;  //异常采样点数量
        public int abnormalSampleCount { get; set; } = 0;  //总采样点数量
        /// <summary>
        /// 异常采样点占比
        /// </summary>
        public string GetAbSamplePct()
        {
            return ((double)abnormalSampleCount / (double)allSampleCount).ToString("P");
        }
        public Cell cell { get; set; }
        public List<Childitem_MissStrongCell> missStrongCellList { get; set; }     // 主服小区-强信号缺失小区生成异常小区对
    }

    public class Childitem_MissStrongCell
    {
        public string cellName { get; set; }    //缺失小区名称
        public string lacCi { get; set; }    //缺失小区（LAC、CI）
        public Cell cell { get; set; }
    }
}
