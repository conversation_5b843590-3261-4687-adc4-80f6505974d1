<?xml version="1.0"?>
<Configs>
  <Config name="ExportCfg">
    <Item name="Templates" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE自定义</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">CELLNAME</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_NCell_Name</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">EARFCN</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TAC</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_TAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">DL</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_Average_Speed</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">传输字节</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_TransferedSize</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">传输时间</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_TransferedTime</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">4G-DT</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TAC</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">TAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">temp</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_ThroughputDL_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">传输字节</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_TransferedSize</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">传输时间</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_TransferedTime</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">速率</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDCP_DL_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">双模测试联通采样点（主服邻区）</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">文件名</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">时间</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSCP</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">TotalRSCP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECIO</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">TotalEc_Io</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">srxlev</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">Sgsmrxlevsub</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rxlev0</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">NGsmRXLev</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rxlev1</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">NGsmRXLev</Item>
            <Item typeName="Int32" key="ParamArrayIndex">1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rxlev2</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">NGsmRXLev</Item>
            <Item typeName="Int32" key="ParamArrayIndex">2</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rxlev3</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">NGsmRXLev</Item>
            <Item typeName="Int32" key="ParamArrayIndex">3</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">GSMrxlev</Item>
            <Item typeName="String" key="SysName">GSM</Item>
            <Item typeName="String" key="ParamName">RxLevSub</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">上行功率控制调研</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">时间戳</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECGI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">本小区RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">本区频点</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">最强邻区PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">NCell_PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">最强邻区RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">NCell_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">最强邻区频点</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">NCell_EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">终端上行发射功率</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PUSCH_Power</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行PUSCH的RB占用数</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PUSCH_RB_Number</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PHR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Power_Headroom</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS0</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS1</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS2</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">2</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS3</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">3</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS4</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">4</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS5</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">5</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS6</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">6</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS7</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">7</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS8</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">8</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS9</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">9</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS10</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">10</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS11</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">11</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS12</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">12</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS13</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">13</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS14</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">14</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS15</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">15</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS16</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">16</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS17</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">17</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS18</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">18</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS19</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">19</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS20</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">20</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS21</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">21</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS22</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">22</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS23</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">23</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS24</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">24</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS25</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">25</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS26</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">26</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS27</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">27</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS28</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">28</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS29</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">29</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS30</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">30</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上行MCS31</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCS_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">31</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">RSRP</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">filename</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">time</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">apptype</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_type</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">latitude</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">longitude</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">EARFCN</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">spead</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_ThroughputDL_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">app spead</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_Speed_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PRB_Num_s</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_PRb_Num_s</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PRB_Num_slot</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_PRb_Num_slot</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PRB_RB_Number</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_RB_Number</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PQLQA_Score_SWB</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">POLQA_Score_SWB</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PESQLQ</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PESQLQ</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PESQmos</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PESQMos</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">集团采样点报表</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">time</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">经度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">纬度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">传输模式</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Transmission_Mode</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">速率_ThroughputDL_Mb</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_ThroughputDL_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">速率_APP_Speed_Mb</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_Speed_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">APP_Average_Speed_Mb</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_Average_Speed_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">FDDRSRP</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">经度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">纬度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp</Item>
            <Item typeName="String" key="SysName">LTE_FDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">sinr</Item>
            <Item typeName="String" key="SysName">LTE_FDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">上传速率</Item>
            <Item typeName="String" key="SysName">LTE_FDD</Item>
            <Item typeName="String" key="ParamName">APP_ThroughputUL_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">扫频</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_Timing_offset</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">PRB调度率</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">经度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">纬度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">频点</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">每秒调度次数</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_PRb_Num_s</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">每时隙调度次数</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_PRb_Num_slot</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">123</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">时间</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">sinr</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">单验采样点导出</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">文件名</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">时间</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">经度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">纬度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDCP层速率</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDCP_DL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">单站验证</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">文件名称</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">测试时间</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">主频</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TAC</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">TAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">PESQLQ</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PESQLQ</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PESQLQ</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE扫频</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FILENAME</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Time</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">EARFCN</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TAC</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_TAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>