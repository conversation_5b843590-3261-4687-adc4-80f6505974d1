﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class FreqBandControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.ColumnHeader columnHeader2;
            this.lvFreqBand = new System.Windows.Forms.ListView();
            this.chkFreqBand = new DevExpress.XtraEditors.CheckEdit();
            this.btnFreqBand = new DevExpress.XtraEditors.SimpleButton();
            this.toolStripDropDownFreq = new System.Windows.Forms.ToolStripDropDown();
            columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            ((System.ComponentModel.ISupportInitialize)(this.chkFreqBand.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // columnHeader2
            // 
            columnHeader2.Width = 150;
            // 
            // lvFreqBand
            // 
            this.lvFreqBand.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            columnHeader2});
            this.lvFreqBand.Enabled = false;
            this.lvFreqBand.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.None;
            this.lvFreqBand.Location = new System.Drawing.Point(13, 29);
            this.lvFreqBand.Name = "lvFreqBand";
            this.lvFreqBand.Size = new System.Drawing.Size(154, 74);
            this.lvFreqBand.TabIndex = 14;
            this.lvFreqBand.UseCompatibleStateImageBehavior = false;
            this.lvFreqBand.View = System.Windows.Forms.View.Details;
            // 
            // chkFreqBand
            // 
            this.chkFreqBand.Location = new System.Drawing.Point(11, 4);
            this.chkFreqBand.Name = "chkFreqBand";
            this.chkFreqBand.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFreqBand.Properties.Appearance.Options.UseFont = true;
            this.chkFreqBand.Properties.Caption = "频点";
            this.chkFreqBand.Size = new System.Drawing.Size(51, 19);
            this.chkFreqBand.TabIndex = 13;
            this.chkFreqBand.CheckedChanged += new System.EventHandler(this.chkFreqBand_CheckedChanged);
            // 
            // btnFreqBand
            // 
            this.btnFreqBand.Enabled = false;
            this.btnFreqBand.Location = new System.Drawing.Point(108, 3);
            this.btnFreqBand.Name = "btnFreqBand";
            this.btnFreqBand.Size = new System.Drawing.Size(59, 21);
            this.btnFreqBand.TabIndex = 12;
            this.btnFreqBand.Text = "请选择↓";
            this.btnFreqBand.Click += new System.EventHandler(this.btnFreqBand_Click);
            // 
            // toolStripDropDownFreq
            // 
            this.toolStripDropDownFreq.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownFreq.Name = "toolStripDropDown1";
            this.toolStripDropDownFreq.Size = new System.Drawing.Size(2, 4);
            // 
            // FreqBandControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.lvFreqBand);
            this.Controls.Add(this.chkFreqBand);
            this.Controls.Add(this.btnFreqBand);
            this.Name = "FreqBandControl";
            this.Size = new System.Drawing.Size(179, 114);
            ((System.ComponentModel.ISupportInitialize)(this.chkFreqBand.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownFreq;
        public System.Windows.Forms.ListView lvFreqBand;
        public DevExpress.XtraEditors.CheckEdit chkFreqBand;
        public DevExpress.XtraEditors.SimpleButton btnFreqBand;
    }
}
