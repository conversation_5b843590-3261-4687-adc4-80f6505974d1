<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">5G FTP下行平均吞吐率对比</Item>
      <Item typeName="IDictionary" key="HubContext">
        <Item typeName="IDictionary" key="移动">
          <Item typeName="String" key="Name">移动</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">73</Item>
            <Item typeName="Int32">70</Item>
            <Item typeName="Int32">69</Item>
            <Item typeName="Int32">68</Item>
            <Item typeName="Int32">67</Item>
            <Item typeName="Int32">66</Item>
            <Item typeName="Int32">65</Item>
            <Item typeName="Int32">64</Item>
            <Item typeName="Int32">63</Item>
            <Item typeName="Int32">62</Item>
            <Item typeName="Int32">61</Item>
            <Item typeName="Int32">60</Item>
            <Item typeName="Int32">59</Item>
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{(Nr_BA040084*8)/(Nr_BA040085/1000)/1000/1000 }</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-10000</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">10000</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="联通">
          <Item typeName="String" key="Name">联通</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">73</Item>
            <Item typeName="Int32">70</Item>
            <Item typeName="Int32">69</Item>
            <Item typeName="Int32">68</Item>
            <Item typeName="Int32">67</Item>
            <Item typeName="Int32">66</Item>
            <Item typeName="Int32">65</Item>
            <Item typeName="Int32">64</Item>
            <Item typeName="Int32">63</Item>
            <Item typeName="Int32">62</Item>
            <Item typeName="Int32">61</Item>
            <Item typeName="Int32">60</Item>
            <Item typeName="Int32">59</Item>
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{(Nr_BA040086*8)/(Nr_BA040087/1000)/1000/1000 }</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-10000</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">10000</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="电信">
          <Item typeName="String" key="Name">电信</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">73</Item>
            <Item typeName="Int32">70</Item>
            <Item typeName="Int32">69</Item>
            <Item typeName="Int32">68</Item>
            <Item typeName="Int32">67</Item>
            <Item typeName="Int32">66</Item>
            <Item typeName="Int32">65</Item>
            <Item typeName="Int32">64</Item>
            <Item typeName="Int32">63</Item>
            <Item typeName="Int32">62</Item>
            <Item typeName="Int32">61</Item>
            <Item typeName="Int32">60</Item>
            <Item typeName="Int32">59</Item>
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{(Nr_BA040086*8)/(Nr_BA040087/1000)/1000/1000 }</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-10000</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">10000</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IList" key="AlghirithmVec">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动优于联通电信</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-16711936</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">10000</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">10000</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动优于联通劣于电信</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-16711681</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">10000</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-10000</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">0</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动优于电信劣于联通</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-256</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-10000</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">0</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">10000</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动劣于联通电信</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-65536</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-10000</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">0</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-10000</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">0</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="OtherAlghirithm">
        <Item typeName="String" key="Name">其他</Item>
        <Item typeName="Boolean" key="IsSpecial">False</Item>
        <Item typeName="String" key="ENaNCM">忽略</Item>
        <Item typeName="String" key="ENaNCU">忽略</Item>
        <Item typeName="String" key="ENaNCT">忽略</Item>
        <Item typeName="Int32" key="Color">-4144960</Item>
        <Item typeName="IList" key="ValueRangeVec" />
      </Item>
    </Item>
  </Config>
</Configs>