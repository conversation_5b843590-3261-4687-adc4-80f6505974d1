﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NoCoverRoadCondition_NR
    {
        public float MaxRsrp { get; set; } = -138;
        public double MinNoCoverPercent { get; set; } = 80;
        public double MaxTpDistance { get; set; } = 50;
        public bool CheckMinDistance { get; set; } = true;
        public double MinRoadDistance { get; set; } = 50;
        public bool CheckMinDuration { get; set; } = false;
        public double MinRoadDuration { get; set; } = 10;
        public bool CheckFilterMultiVoice { get; set; } = true;
    }
}
