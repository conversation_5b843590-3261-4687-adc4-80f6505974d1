﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteScanAntennaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram19 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY5 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY6 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series37 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel29 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView3 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel30 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram20 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series38 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel5 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView5 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel6 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView6 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram21 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series39 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel7 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView7 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series40 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel8 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView8 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel9 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView9 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram7 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series41 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel19 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint51 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint52 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint53 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint54 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint55 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView7 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series42 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel20 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView8 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel21 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView9 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram17 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series35 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel25 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel26 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram18 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series36 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel27 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel28 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram22 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series43 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel31 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel32 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram23 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series44 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel33 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel34 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram24 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series45 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel35 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel36 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.RadarDiagram radarDiagram8 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series46 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel22 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint56 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint57 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint58 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint59 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint60 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView13 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series47 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel23 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView14 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel24 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView15 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram9 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series48 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel25 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint61 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint62 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint63 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint64 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint65 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView16 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series49 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel26 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView17 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel27 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView18 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.XYDiagram3D xyDiagram3D3 = new DevExpress.XtraCharts.XYDiagram3D();
            DevExpress.XtraCharts.Series series50 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.Bar3DSeriesLabel bar3DSeriesLabel7 = new DevExpress.XtraCharts.Bar3DSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint66 = new DevExpress.XtraCharts.SeriesPoint("5", new object[] {
            ((object)(-75D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint67 = new DevExpress.XtraCharts.SeriesPoint("7", new object[] {
            ((object)(-94D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint68 = new DevExpress.XtraCharts.SeriesPoint("10", new object[] {
            ((object)(-86D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint69 = new DevExpress.XtraCharts.SeriesPoint("15", new object[] {
            ((object)(-67D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint70 = new DevExpress.XtraCharts.SeriesPoint("20", new object[] {
            ((object)(-90D))});
            DevExpress.XtraCharts.ManhattanBarSeriesView manhattanBarSeriesView7 = new DevExpress.XtraCharts.ManhattanBarSeriesView();
            DevExpress.XtraCharts.Series series51 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.Bar3DSeriesLabel bar3DSeriesLabel8 = new DevExpress.XtraCharts.Bar3DSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint71 = new DevExpress.XtraCharts.SeriesPoint("5", new object[] {
            ((object)(-84D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint72 = new DevExpress.XtraCharts.SeriesPoint("7", new object[] {
            ((object)(-89D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint73 = new DevExpress.XtraCharts.SeriesPoint("10", new object[] {
            ((object)(-72D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint74 = new DevExpress.XtraCharts.SeriesPoint("15", new object[] {
            ((object)(-90D))});
            DevExpress.XtraCharts.SeriesPoint seriesPoint75 = new DevExpress.XtraCharts.SeriesPoint("20", new object[] {
            ((object)(-78D))});
            DevExpress.XtraCharts.ManhattanBarSeriesView manhattanBarSeriesView8 = new DevExpress.XtraCharts.ManhattanBarSeriesView();
            DevExpress.XtraCharts.Bar3DSeriesLabel bar3DSeriesLabel9 = new DevExpress.XtraCharts.Bar3DSeriesLabel();
            DevExpress.XtraCharts.ManhattanBarSeriesView manhattanBarSeriesView9 = new DevExpress.XtraCharts.ManhattanBarSeriesView();
            this.scanAntDataXTCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.colCellname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBcch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellChanel = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.coldMaxPcc0_30_150_180 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSection = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRxlevSampleNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSamplePect = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgRsrp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colMaxRsrp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSinr = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSampleTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAnaType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_dir = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgRsrp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgSinr = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowGis = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowMRSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.拆分导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.colDate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCityName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCell = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colGridName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBscName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colFreq = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSampleNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRsrpAvg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSinrRate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSinrAvg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellDistAvg = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colvender = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colbeamwidth = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcovertype = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colgmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col1ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col2ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col1para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col2para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col4para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col5para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col7para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column27 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column33 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column34 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column35 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column36 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column37 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column38 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.xtraTabPage6 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridViewAnt = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column39 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.cluCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cluTarget = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbbxSeries2 = new System.Windows.Forms.ComboBox();
            this.cbbxSeries1 = new System.Windows.Forms.ComboBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.chartControl5 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chartControl3 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartControl2 = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage7 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.rtbDesc = new System.Windows.Forms.RichTextBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.chartControlAoa = new DevExpress.XtraCharts.ChartControl();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.chartControlSinr = new DevExpress.XtraCharts.ChartControl();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.chartControlRsrp = new DevExpress.XtraCharts.ChartControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.chartControlPower = new DevExpress.XtraCharts.ChartControl();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.chartControlTA = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage8 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl5 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox13 = new System.Windows.Forms.GroupBox();
            this.chartControl6 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.chartControl8 = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.chartControl4 = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.scanAntDataXTCtrl)).BeginInit();
            this.scanAntDataXTCtrl.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.xtraTabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAnt)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series37)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel30)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series38)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView6)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series39)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series40)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView9)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series42)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView9)).BeginInit();
            this.xtraTabPage7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            this.groupBox12.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAoa)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series35)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel26)).BeginInit();
            this.groupBox11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlSinr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series36)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel28)).BeginInit();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series43)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel31)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel32)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlPower)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series44)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel33)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel34)).BeginInit();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTA)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series45)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel35)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel36)).BeginInit();
            this.xtraTabPage8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).BeginInit();
            this.groupControl5.SuspendLayout();
            this.groupBox13.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series46)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series47)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView15)).BeginInit();
            this.groupBox15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series48)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series49)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView18)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3D3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series50)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(bar3DSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(manhattanBarSeriesView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series51)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(bar3DSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(manhattanBarSeriesView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(bar3DSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(manhattanBarSeriesView9)).BeginInit();
            this.SuspendLayout();
            // 
            // scanAntDataXTCtrl
            // 
            this.scanAntDataXTCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.scanAntDataXTCtrl.Location = new System.Drawing.Point(0, 0);
            this.scanAntDataXTCtrl.Name = "scanAntDataXTCtrl";
            this.scanAntDataXTCtrl.SelectedTabPage = this.xtraTabPage1;
            this.scanAntDataXTCtrl.Size = new System.Drawing.Size(1184, 650);
            this.scanAntDataXTCtrl.TabIndex = 1;
            this.scanAntDataXTCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage4,
            this.xtraTabPage6,
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage7,
            this.xtraTabPage8,
            this.xtraTabPage5});
            this.scanAntDataXTCtrl.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.dataGridView);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage1.Text = "覆盖角度区间化统计分析";
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colCellname,
            this.colBcch,
            this.cellChanel,
            this.coldMaxPcc0_30_150_180,
            this.colSection,
            this.colRxlevSampleNum,
            this.colSamplePect,
            this.colAvgRsrp,
            this.colMaxRsrp,
            this.colSinr,
            this.colAvgSampleDistance,
            this.colSampleTotal,
            this.colAnaType,
            this.colIangle_ob,
            this.colIaltitude,
            this.colIangle_dir,
            this.colCellAvgRsrp,
            this.colCellAvgSinr,
            this.colCellAvgSampleDistance});
            this.dataGridView.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(1177, 620);
            this.dataGridView.TabIndex = 0;
            // 
            // colCellname
            // 
            this.colCellname.Frozen = true;
            this.colCellname.HeaderText = "小区名称";
            this.colCellname.Name = "colCellname";
            this.colCellname.ReadOnly = true;
            this.colCellname.Width = 250;
            // 
            // colBcch
            // 
            this.colBcch.HeaderText = "频点";
            this.colBcch.Name = "colBcch";
            this.colBcch.ReadOnly = true;
            // 
            // cellChanel
            // 
            this.cellChanel.HeaderText = "频段";
            this.cellChanel.Name = "cellChanel";
            // 
            // coldMaxPcc0_30_150_180
            // 
            this.coldMaxPcc0_30_150_180.HeaderText = "[0,30]区间与(150,180]区间最强RSRP的差值";
            this.coldMaxPcc0_30_150_180.Name = "coldMaxPcc0_30_150_180";
            this.coldMaxPcc0_30_150_180.ReadOnly = true;
            this.coldMaxPcc0_30_150_180.Width = 150;
            // 
            // colSection
            // 
            this.colSection.HeaderText = "方向角偏差区间";
            this.colSection.Name = "colSection";
            this.colSection.ReadOnly = true;
            // 
            // colRxlevSampleNum
            // 
            this.colRxlevSampleNum.HeaderText = "区间采样点数";
            this.colRxlevSampleNum.Name = "colRxlevSampleNum";
            this.colRxlevSampleNum.ReadOnly = true;
            // 
            // colSamplePect
            // 
            this.colSamplePect.HeaderText = "采样点占比";
            this.colSamplePect.Name = "colSamplePect";
            this.colSamplePect.ReadOnly = true;
            // 
            // colAvgRsrp
            // 
            this.colAvgRsrp.HeaderText = "区间平均RSRP(dBm)";
            this.colAvgRsrp.Name = "colAvgRsrp";
            this.colAvgRsrp.ReadOnly = true;
            // 
            // colMaxRsrp
            // 
            this.colMaxRsrp.HeaderText = "区间最大RSRP(dBm)";
            this.colMaxRsrp.Name = "colMaxRsrp";
            this.colMaxRsrp.ReadOnly = true;
            // 
            // colSinr
            // 
            this.colSinr.HeaderText = "区间SINR平均";
            this.colSinr.Name = "colSinr";
            this.colSinr.ReadOnly = true;
            // 
            // colAvgSampleDistance
            // 
            this.colAvgSampleDistance.HeaderText = "区间平均通信距离";
            this.colAvgSampleDistance.Name = "colAvgSampleDistance";
            this.colAvgSampleDistance.ReadOnly = true;
            // 
            // colSampleTotal
            // 
            this.colSampleTotal.HeaderText = "总采样点数";
            this.colSampleTotal.Name = "colSampleTotal";
            this.colSampleTotal.ReadOnly = true;
            // 
            // colAnaType
            // 
            this.colAnaType.HeaderText = "天线类型";
            this.colAnaType.Name = "colAnaType";
            this.colAnaType.ReadOnly = true;
            // 
            // colIangle_ob
            // 
            this.colIangle_ob.HeaderText = "下倾角";
            this.colIangle_ob.Name = "colIangle_ob";
            this.colIangle_ob.ReadOnly = true;
            // 
            // colIaltitude
            // 
            this.colIaltitude.HeaderText = "挂高";
            this.colIaltitude.Name = "colIaltitude";
            this.colIaltitude.ReadOnly = true;
            // 
            // colIangle_dir
            // 
            this.colIangle_dir.HeaderText = "方向角";
            this.colIangle_dir.Name = "colIangle_dir";
            this.colIangle_dir.ReadOnly = true;
            // 
            // colCellAvgRsrp
            // 
            this.colCellAvgRsrp.HeaderText = "小区RSRP平均";
            this.colCellAvgRsrp.Name = "colCellAvgRsrp";
            this.colCellAvgRsrp.ReadOnly = true;
            // 
            // colCellAvgSinr
            // 
            this.colCellAvgSinr.HeaderText = "小区SINR平均";
            this.colCellAvgSinr.Name = "colCellAvgSinr";
            this.colCellAvgSinr.ReadOnly = true;
            // 
            // colCellAvgSampleDistance
            // 
            this.colCellAvgSampleDistance.HeaderText = "小区平均通信距离";
            this.colCellAvgSampleDistance.Name = "colCellAvgSampleDistance";
            this.colCellAvgSampleDistance.ReadOnly = true;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowChart,
            this.miShowGis,
            this.miShowSimulation,
            this.miShowMRSimulation,
            this.拆分导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(209, 136);
            // 
            // miShowChart
            // 
            this.miShowChart.Name = "miShowChart";
            this.miShowChart.Size = new System.Drawing.Size(208, 22);
            this.miShowChart.Text = "显示天线辐射波形重建";
            this.miShowChart.Click += new System.EventHandler(this.miShowChart_Click);
            // 
            // miShowGis
            // 
            this.miShowGis.Name = "miShowGis";
            this.miShowGis.Size = new System.Drawing.Size(208, 22);
            this.miShowGis.Text = "显示选择小区及其采样点";
            this.miShowGis.Click += new System.EventHandler(this.miShowGis_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(208, 22);
            this.miShowSimulation.Text = "显示天线覆盖仿真";
            this.miShowSimulation.Click += new System.EventHandler(this.miShowSimulation_Click);
            // 
            // miShowMRSimulation
            // 
            this.miShowMRSimulation.Name = "miShowMRSimulation";
            this.miShowMRSimulation.Size = new System.Drawing.Size(208, 22);
            this.miShowMRSimulation.Text = "显示MR覆盖仿真";
            this.miShowMRSimulation.Click += new System.EventHandler(this.miShowMRSimulation_Click);
            // 
            // 拆分导出CSVToolStripMenuItem
            // 
            this.拆分导出CSVToolStripMenuItem.Name = "拆分导出CSVToolStripMenuItem";
            this.拆分导出CSVToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.拆分导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.拆分导出CSVToolStripMenuItem.Click += new System.EventHandler(this.拆分导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(208, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.btnNextpage);
            this.xtraTabPage4.Controls.Add(this.btnPrevpage);
            this.xtraTabPage4.Controls.Add(this.label5);
            this.xtraTabPage4.Controls.Add(this.txtPage);
            this.xtraTabPage4.Controls.Add(this.labPage);
            this.xtraTabPage4.Controls.Add(this.label4);
            this.xtraTabPage4.Controls.Add(this.btnGo);
            this.xtraTabPage4.Controls.Add(this.labNum);
            this.xtraTabPage4.Controls.Add(this.label3);
            this.xtraTabPage4.Controls.Add(this.label2);
            this.xtraTabPage4.Controls.Add(this.btnSearch);
            this.xtraTabPage4.Controls.Add(this.txtCellName);
            this.xtraTabPage4.Controls.Add(this.label1);
            this.xtraTabPage4.Controls.Add(this.dataGridViewCell);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage4.Text = "小区权值设置及覆盖统计";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(862, 595);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 42;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(823, 595);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 41;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(765, 597);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 40;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(700, 594);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 39;
            this.txtPage.Text = "1";
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(600, 599);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 38;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(557, 598);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 37;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(784, 594);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 36;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(512, 599);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 35;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(634, 598);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 34;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(436, 598);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 33;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1130, 594);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 32;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(968, 595);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 31;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(907, 599);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 30;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllowUserToAddRows = false;
            this.dataGridViewCell.AllowUserToDeleteRows = false;
            this.dataGridViewCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colDate,
            this.colCityName,
            this.colCell,
            this.colGridName,
            this.colBscName,
            this.colFreq,
            this.colSampleNum,
            this.colRsrpAvg,
            this.colSinrRate,
            this.colSinrAvg,
            this.colCellDistAvg,
            this.colvender,
            this.colcgi,
            this.colbeamwidth,
            this.colcovertype,
            this.colgmax,
            this.col3db,
            this.col6db,
            this.colrange1,
            this.colrange2,
            this.colrange3,
            this.colrange4,
            this.colrange5,
            this.colrange6,
            this.colrange7,
            this.colrange8,
            this.colphase1,
            this.colphase2,
            this.colphase3,
            this.colphase4,
            this.colphase5,
            this.colphase6,
            this.colphase7,
            this.colphase8,
            this.col1ob,
            this.col2ob,
            this.col3ob,
            this.colaltitude,
            this.col1para,
            this.col2para,
            this.col3para,
            this.col4para,
            this.col5para,
            this.col6para,
            this.col7para,
            this.Column26,
            this.Column27,
            this.Column28,
            this.Column29,
            this.Column33,
            this.Column34,
            this.Column35,
            this.Column36,
            this.Column37,
            this.Column38,
            this.Column30,
            this.Column31,
            this.Column32});
            this.dataGridViewCell.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.Size = new System.Drawing.Size(1177, 590);
            this.dataGridViewCell.TabIndex = 2;
            // 
            // colDate
            // 
            this.colDate.Frozen = true;
            this.colDate.HeaderText = "时间";
            this.colDate.Name = "colDate";
            this.colDate.ReadOnly = true;
            // 
            // colCityName
            // 
            this.colCityName.Frozen = true;
            this.colCityName.HeaderText = "地市名称";
            this.colCityName.Name = "colCityName";
            this.colCityName.ReadOnly = true;
            // 
            // colCell
            // 
            this.colCell.Frozen = true;
            this.colCell.HeaderText = "小区名";
            this.colCell.Name = "colCell";
            this.colCell.ReadOnly = true;
            // 
            // colGridName
            // 
            this.colGridName.HeaderText = "网格号";
            this.colGridName.Name = "colGridName";
            this.colGridName.ReadOnly = true;
            // 
            // colBscName
            // 
            this.colBscName.HeaderText = "BSC名";
            this.colBscName.Name = "colBscName";
            this.colBscName.ReadOnly = true;
            // 
            // colFreq
            // 
            this.colFreq.HeaderText = "频点标示";
            this.colFreq.Name = "colFreq";
            this.colFreq.ReadOnly = true;
            // 
            // colSampleNum
            // 
            this.colSampleNum.HeaderText = "采样点总数";
            this.colSampleNum.Name = "colSampleNum";
            this.colSampleNum.ReadOnly = true;
            // 
            // colRsrpAvg
            // 
            this.colRsrpAvg.HeaderText = "小区RSRP均值";
            this.colRsrpAvg.Name = "colRsrpAvg";
            this.colRsrpAvg.ReadOnly = true;
            // 
            // colSinrRate
            // 
            this.colSinrRate.HeaderText = "小区SINR<=-3占比(%)";
            this.colSinrRate.Name = "colSinrRate";
            this.colSinrRate.ReadOnly = true;
            // 
            // colSinrAvg
            // 
            this.colSinrAvg.HeaderText = "小区C/I平均";
            this.colSinrAvg.Name = "colSinrAvg";
            this.colSinrAvg.ReadOnly = true;
            // 
            // colCellDistAvg
            // 
            this.colCellDistAvg.HeaderText = "小区平均通信距离";
            this.colCellDistAvg.Name = "colCellDistAvg";
            this.colCellDistAvg.ReadOnly = true;
            // 
            // colvender
            // 
            this.colvender.HeaderText = "主设备厂家";
            this.colvender.Name = "colvender";
            this.colvender.ReadOnly = true;
            // 
            // colcgi
            // 
            this.colcgi.HeaderText = "CGI";
            this.colcgi.Name = "colcgi";
            this.colcgi.ReadOnly = true;
            // 
            // colbeamwidth
            // 
            this.colbeamwidth.HeaderText = "波束宽度";
            this.colbeamwidth.Name = "colbeamwidth";
            this.colbeamwidth.ReadOnly = true;
            // 
            // colcovertype
            // 
            this.colcovertype.HeaderText = "覆盖类型";
            this.colcovertype.Name = "colcovertype";
            this.colcovertype.ReadOnly = true;
            // 
            // colgmax
            // 
            this.colgmax.HeaderText = "Gmax";
            this.colgmax.Name = "colgmax";
            this.colgmax.ReadOnly = true;
            // 
            // col3db
            // 
            this.col3db.HeaderText = "3dB功率角";
            this.col3db.Name = "col3db";
            this.col3db.ReadOnly = true;
            // 
            // col6db
            // 
            this.col6db.HeaderText = "6dB功率角";
            this.col6db.Name = "col6db";
            this.col6db.ReadOnly = true;
            // 
            // colrange1
            // 
            this.colrange1.HeaderText = "端口1幅度";
            this.colrange1.Name = "colrange1";
            this.colrange1.ReadOnly = true;
            // 
            // colrange2
            // 
            this.colrange2.HeaderText = "端口2幅度";
            this.colrange2.Name = "colrange2";
            this.colrange2.ReadOnly = true;
            // 
            // colrange3
            // 
            this.colrange3.HeaderText = "端口3幅度";
            this.colrange3.Name = "colrange3";
            this.colrange3.ReadOnly = true;
            // 
            // colrange4
            // 
            this.colrange4.HeaderText = "端口4幅度";
            this.colrange4.Name = "colrange4";
            this.colrange4.ReadOnly = true;
            // 
            // colrange5
            // 
            this.colrange5.HeaderText = "端口5幅度";
            this.colrange5.Name = "colrange5";
            this.colrange5.ReadOnly = true;
            // 
            // colrange6
            // 
            this.colrange6.HeaderText = "端口6幅度";
            this.colrange6.Name = "colrange6";
            this.colrange6.ReadOnly = true;
            // 
            // colrange7
            // 
            this.colrange7.HeaderText = "端口7幅度";
            this.colrange7.Name = "colrange7";
            this.colrange7.ReadOnly = true;
            // 
            // colrange8
            // 
            this.colrange8.HeaderText = "端口8幅度";
            this.colrange8.Name = "colrange8";
            this.colrange8.ReadOnly = true;
            // 
            // colphase1
            // 
            this.colphase1.HeaderText = "端口1相位";
            this.colphase1.Name = "colphase1";
            this.colphase1.ReadOnly = true;
            // 
            // colphase2
            // 
            this.colphase2.HeaderText = "端口2相位";
            this.colphase2.Name = "colphase2";
            this.colphase2.ReadOnly = true;
            // 
            // colphase3
            // 
            this.colphase3.HeaderText = "端口3相位";
            this.colphase3.Name = "colphase3";
            this.colphase3.ReadOnly = true;
            // 
            // colphase4
            // 
            this.colphase4.HeaderText = "端口4相位";
            this.colphase4.Name = "colphase4";
            this.colphase4.ReadOnly = true;
            // 
            // colphase5
            // 
            this.colphase5.HeaderText = "端口5相位";
            this.colphase5.Name = "colphase5";
            this.colphase5.ReadOnly = true;
            // 
            // colphase6
            // 
            this.colphase6.HeaderText = "端口6相位";
            this.colphase6.Name = "colphase6";
            this.colphase6.ReadOnly = true;
            // 
            // colphase7
            // 
            this.colphase7.HeaderText = "端口7相位";
            this.colphase7.Name = "colphase7";
            this.colphase7.ReadOnly = true;
            // 
            // colphase8
            // 
            this.colphase8.HeaderText = "端口8相位";
            this.colphase8.Name = "colphase8";
            this.colphase8.ReadOnly = true;
            // 
            // col1ob
            // 
            this.col1ob.HeaderText = "预置下倾角";
            this.col1ob.Name = "col1ob";
            this.col1ob.ReadOnly = true;
            // 
            // col2ob
            // 
            this.col2ob.HeaderText = "机械下倾角";
            this.col2ob.Name = "col2ob";
            this.col2ob.ReadOnly = true;
            // 
            // col3ob
            // 
            this.col3ob.HeaderText = "电调下倾角";
            this.col3ob.Name = "col3ob";
            this.col3ob.ReadOnly = true;
            // 
            // colaltitude
            // 
            this.colaltitude.HeaderText = "挂高";
            this.colaltitude.Name = "colaltitude";
            this.colaltitude.ReadOnly = true;
            // 
            // col1para
            // 
            this.col1para.HeaderText = "上行吞吐量(MB)";
            this.col1para.Name = "col1para";
            this.col1para.ReadOnly = true;
            // 
            // col2para
            // 
            this.col2para.HeaderText = "下行吞吐量(MB)";
            this.col2para.Name = "col2para";
            this.col2para.ReadOnly = true;
            // 
            // col3para
            // 
            this.col3para.HeaderText = "无线接通率(%)";
            this.col3para.Name = "col3para";
            this.col3para.ReadOnly = true;
            // 
            // col4para
            // 
            this.col4para.HeaderText = "无线掉线率(%)";
            this.col4para.Name = "col4para";
            this.col4para.ReadOnly = true;
            // 
            // col5para
            // 
            this.col5para.HeaderText = "切换成功率(%)";
            this.col5para.Name = "col5para";
            this.col5para.ReadOnly = true;
            // 
            // col6para
            // 
            this.col6para.HeaderText = "ERAB建立成功率(%)";
            this.col6para.Name = "col6para";
            this.col6para.ReadOnly = true;
            // 
            // col7para
            // 
            this.col7para.HeaderText = "ERAB掉线率(%)";
            this.col7para.Name = "col7para";
            this.col7para.ReadOnly = true;
            // 
            // Column26
            // 
            this.Column26.HeaderText = "RSRP均值";
            this.Column26.Name = "Column26";
            // 
            // Column27
            // 
            this.Column27.HeaderText = "SINR均值";
            this.Column27.Name = "Column27";
            // 
            // Column28
            // 
            this.Column28.HeaderText = "95覆盖率";
            this.Column28.Name = "Column28";
            // 
            // Column29
            // 
            this.Column29.HeaderText = "110覆盖率";
            this.Column29.Name = "Column29";
            // 
            // Column33
            // 
            this.Column33.HeaderText = "MRO总采样点数";
            this.Column33.Name = "Column33";
            // 
            // Column34
            // 
            this.Column34.HeaderText = "重叠覆盖条件采样点数";
            this.Column34.Name = "Column34";
            // 
            // Column35
            // 
            this.Column35.HeaderText = "重叠覆盖指数";
            this.Column35.Name = "Column35";
            // 
            // Column36
            // 
            this.Column36.HeaderText = "过覆盖影响小区数";
            this.Column36.Name = "Column36";
            // 
            // Column37
            // 
            this.Column37.HeaderText = "高重叠覆盖小区";
            this.Column37.Name = "Column37";
            // 
            // Column38
            // 
            this.Column38.HeaderText = "过覆盖小区";
            this.Column38.Name = "Column38";
            // 
            // Column30
            // 
            this.Column30.HeaderText = "MR覆盖角";
            this.Column30.Name = "Column30";
            // 
            // Column31
            // 
            this.Column31.HeaderText = "与天线方位角差值";
            this.Column31.Name = "Column31";
            // 
            // Column32
            // 
            this.Column32.HeaderText = "MR天线分析";
            this.Column32.Name = "Column32";
            // 
            // xtraTabPage6
            // 
            this.xtraTabPage6.Controls.Add(this.dataGridViewAnt);
            this.xtraTabPage6.Name = "xtraTabPage6";
            this.xtraTabPage6.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage6.Text = "天线辐射性能分析";
            // 
            // dataGridViewAnt
            // 
            this.dataGridViewAnt.AllowUserToAddRows = false;
            this.dataGridViewAnt.AllowUserToDeleteRows = false;
            this.dataGridViewAnt.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewAnt.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAnt.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn2,
            this.dataGridViewTextBoxColumn3,
            this.dataGridViewTextBoxColumn4,
            this.dataGridViewTextBoxColumn15,
            this.dataGridViewTextBoxColumn5,
            this.dataGridViewTextBoxColumn6,
            this.dataGridViewTextBoxColumn9,
            this.dataGridViewTextBoxColumn14,
            this.dataGridViewTextBoxColumn12,
            this.dataGridViewTextBoxColumn17,
            this.dataGridViewTextBoxColumn8,
            this.Column39,
            this.Column1,
            this.Column2,
            this.Column6,
            this.Column7,
            this.Column9,
            this.Column10,
            this.Column11,
            this.Column12,
            this.Column14,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column19,
            this.Column20,
            this.Column21,
            this.Column24,
            this.Column22});
            this.dataGridViewAnt.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewAnt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewAnt.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewAnt.Name = "dataGridViewAnt";
            this.dataGridViewAnt.RowTemplate.Height = 23;
            this.dataGridViewAnt.Size = new System.Drawing.Size(1177, 620);
            this.dataGridViewAnt.TabIndex = 3;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "地市";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.HeaderText = "小区名";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.HeaderText = "所在网格";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn15
            // 
            this.dataGridViewTextBoxColumn15.HeaderText = "主设备厂家";
            this.dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
            this.dataGridViewTextBoxColumn15.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.HeaderText = "BSC名";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            this.dataGridViewTextBoxColumn5.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.HeaderText = "频点标示";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            this.dataGridViewTextBoxColumn6.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn9
            // 
            this.dataGridViewTextBoxColumn9.HeaderText = "小区RSRP均值";
            this.dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
            this.dataGridViewTextBoxColumn9.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn14
            // 
            this.dataGridViewTextBoxColumn14.HeaderText = "小区SINR均值";
            this.dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
            this.dataGridViewTextBoxColumn14.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn12
            // 
            this.dataGridViewTextBoxColumn12.HeaderText = "小区平均通信距离";
            this.dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
            this.dataGridViewTextBoxColumn12.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn17
            // 
            this.dataGridViewTextBoxColumn17.HeaderText = "波束宽度";
            this.dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
            this.dataGridViewTextBoxColumn17.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn8
            // 
            this.dataGridViewTextBoxColumn8.HeaderText = "采样点总数";
            this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            this.dataGridViewTextBoxColumn8.ReadOnly = true;
            // 
            // Column39
            // 
            this.Column39.HeaderText = "天线方位角";
            this.Column39.Name = "Column39";
            // 
            // Column1
            // 
            this.Column1.HeaderText = "±(0,60°)范围内采样点比例";
            this.Column1.Name = "Column1";
            // 
            // Column2
            // 
            this.Column2.HeaderText = "±(0,60°)范围内最强信号强度";
            this.Column2.Name = "Column2";
            // 
            // Column6
            // 
            this.Column6.HeaderText = "疑似旁瓣数量";
            this.Column6.Name = "Column6";
            // 
            // Column7
            // 
            this.Column7.HeaderText = "旁瓣1辐射方向";
            this.Column7.Name = "Column7";
            // 
            // Column9
            // 
            this.Column9.HeaderText = "旁瓣1最强信号强度";
            this.Column9.Name = "Column9";
            // 
            // Column10
            // 
            this.Column10.HeaderText = "旁瓣1平均信号强度";
            this.Column10.Name = "Column10";
            // 
            // Column11
            // 
            this.Column11.HeaderText = "旁瓣1采样点比例";
            this.Column11.Name = "Column11";
            // 
            // Column12
            // 
            this.Column12.HeaderText = "旁瓣2辐射方向";
            this.Column12.Name = "Column12";
            // 
            // Column14
            // 
            this.Column14.HeaderText = "旁瓣2最强信号强度";
            this.Column14.Name = "Column14";
            // 
            // Column15
            // 
            this.Column15.HeaderText = "旁瓣2平均信号强度";
            this.Column15.Name = "Column15";
            // 
            // Column16
            // 
            this.Column16.HeaderText = "旁瓣2采样点比例";
            this.Column16.Name = "Column16";
            // 
            // Column17
            // 
            this.Column17.HeaderText = "旁瓣3辐射方向";
            this.Column17.Name = "Column17";
            // 
            // Column19
            // 
            this.Column19.HeaderText = "旁瓣3最强信号强度";
            this.Column19.Name = "Column19";
            // 
            // Column20
            // 
            this.Column20.HeaderText = "旁瓣3平均信号强度";
            this.Column20.Name = "Column20";
            // 
            // Column21
            // 
            this.Column21.HeaderText = "旁瓣3采样点比例";
            this.Column21.Name = "Column21";
            // 
            // Column24
            // 
            this.Column24.HeaderText = "背瓣采样点比例";
            this.Column24.Name = "Column24";
            // 
            // Column22
            // 
            this.Column22.HeaderText = "前后比";
            this.Column22.Name = "Column22";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupControl2);
            this.xtraTabPage2.Controls.Add(this.groupControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage2.Text = "天线角度级采样数据分析";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGridViewAngle);
            this.groupControl2.Location = new System.Drawing.Point(10, 390);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1142, 218);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "天线角度";
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.AllowUserToAddRows = false;
            this.dataGridViewAngle.AllowUserToDeleteRows = false;
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAngle.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.cluCellName,
            this.cluTarget});
            this.dataGridViewAngle.Location = new System.Drawing.Point(12, 32);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1121, 181);
            this.dataGridViewAngle.TabIndex = 0;
            // 
            // cluCellName
            // 
            this.cluCellName.HeaderText = "小区名称";
            this.cluCellName.Name = "cluCellName";
            this.cluCellName.Width = 150;
            // 
            // cluTarget
            // 
            this.cluTarget.HeaderText = "指标项";
            this.cluTarget.Name = "cluTarget";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1177, 620);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "图表";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.panel1);
            this.groupBox2.Controls.Add(this.chartControl1);
            this.groupBox2.Location = new System.Drawing.Point(10, 75);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1142, 295);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Location = new System.Drawing.Point(1092, 21);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(29, 268);
            this.panel1.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram19.AxisX.MinorCount = 1;
            xyDiagram19.AxisX.Range.Auto = false;
            xyDiagram19.AxisX.Range.MaxValueInternal = 3.4999999999999991D;
            xyDiagram19.AxisX.Range.MinValueInternal = -0.5D;
            xyDiagram19.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram19.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram19.AxisX.Tickmarks.MinorLength = 1;
            xyDiagram19.AxisX.Title.Alignment = System.Drawing.StringAlignment.Near;
            xyDiagram19.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram19.AxisY.Color = System.Drawing.Color.DarkOrange;
            xyDiagram19.AxisY.Interlaced = true;
            xyDiagram19.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram19.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram19.AxisY.Thickness = 2;
            xyDiagram19.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram19.DefaultPane.BackColor = System.Drawing.Color.Transparent;
            xyDiagram19.EnableAxisXScrolling = true;
            xyDiagram19.EnableAxisXZooming = true;
            xyDiagram19.EnableAxisYScrolling = true;
            xyDiagram19.Margins.Bottom = 1;
            xyDiagram19.Margins.Left = 1;
            xyDiagram19.Margins.Right = 1;
            xyDiagram19.Margins.Top = 1;
            xyDiagram19.PaneDistance = 5;
            secondaryAxisY5.AxisID = 0;
            secondaryAxisY5.Color = System.Drawing.Color.Blue;
            secondaryAxisY5.GridLines.Color = System.Drawing.Color.White;
            secondaryAxisY5.Interlaced = true;
            secondaryAxisY5.Name = "Secondary AxisY 1";
            secondaryAxisY5.Range.Auto = false;
            secondaryAxisY5.Range.MaxValueSerializable = "1";
            secondaryAxisY5.Range.MinValueSerializable = "0";
            secondaryAxisY5.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY5.Range.SideMarginsEnabled = true;
            secondaryAxisY5.Thickness = 2;
            secondaryAxisY5.VisibleInPanesSerializable = "-1";
            secondaryAxisY6.AxisID = 1;
            secondaryAxisY6.Name = "Secondary AxisY 2";
            secondaryAxisY6.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY6.Range.SideMarginsEnabled = true;
            secondaryAxisY6.VisibleInPanesSerializable = "-1";
            xyDiagram19.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY5,
            secondaryAxisY6});
            this.chartControl1.Diagram = xyDiagram19;
            this.chartControl1.Location = new System.Drawing.Point(9, 21);
            this.chartControl1.Name = "chartControl1";
            this.chartControl1.RefreshDataOnRepaint = false;
            this.chartControl1.RuntimeHitTesting = false;
            sideBySideBarSeriesLabel29.LineVisible = false;
            sideBySideBarSeriesLabel29.Visible = false;
            series37.Label = sideBySideBarSeriesLabel29;
            series37.Name = "Series 3";
            series37.ShowInLegend = false;
            sideBySideBarSeriesView3.AxisYName = "Secondary AxisY 2";
            sideBySideBarSeriesView3.BarWidth = 1D;
            sideBySideBarSeriesView3.Color = System.Drawing.Color.Red;
            series37.View = sideBySideBarSeriesView3;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series37};
            sideBySideBarSeriesLabel30.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel30;
            this.chartControl1.SideBySideBarDistanceVariable = 0.01D;
            this.chartControl1.Size = new System.Drawing.Size(1112, 268);
            this.chartControl1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbbxSeries2);
            this.groupBox1.Controls.Add(this.cbbxSeries1);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Location = new System.Drawing.Point(10, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1142, 50);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例";
            // 
            // cbbxSeries2
            // 
            this.cbbxSeries2.FormattingEnabled = true;
            this.cbbxSeries2.Location = new System.Drawing.Point(365, 18);
            this.cbbxSeries2.Name = "cbbxSeries2";
            this.cbbxSeries2.Size = new System.Drawing.Size(112, 22);
            this.cbbxSeries2.TabIndex = 3;
            this.cbbxSeries2.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries2_SelectedIndexChanged);
            // 
            // cbbxSeries1
            // 
            this.cbbxSeries1.FormattingEnabled = true;
            this.cbbxSeries1.Location = new System.Drawing.Point(133, 18);
            this.cbbxSeries1.Name = "cbbxSeries1";
            this.cbbxSeries1.Size = new System.Drawing.Size(116, 22);
            this.cbbxSeries1.TabIndex = 2;
            this.cbbxSeries1.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries1_SelectedIndexChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(299, 21);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "折线图指标";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(67, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "柱形图指标";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.groupControl3);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage3.Text = "天线辐射波形重构";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.groupBox6);
            this.groupControl3.Controls.Add(this.groupBox4);
            this.groupControl3.Controls.Add(this.groupBox3);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1177, 620);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "全向分析";
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Controls.Add(this.chartControl5);
            this.groupBox6.Location = new System.Drawing.Point(605, 26);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(565, 272);
            this.groupBox6.TabIndex = 2;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "权值理想波形图";
            // 
            // chartControl5
            // 
            this.chartControl5.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram20.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram20.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram20.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram20.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram20.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram20.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl5.Diagram = xyDiagram20;
            this.chartControl5.Legend.Visible = false;
            this.chartControl5.Location = new System.Drawing.Point(6, 21);
            this.chartControl5.Name = "chartControl5";
            series38.Label = stackedBarSeriesLabel5;
            series38.Name = "Series 1";
            series38.View = sideBySideStackedBarSeriesView5;
            this.chartControl5.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series38};
            this.chartControl5.SeriesTemplate.Label = stackedBarSeriesLabel6;
            this.chartControl5.SeriesTemplate.View = sideBySideStackedBarSeriesView6;
            this.chartControl5.Size = new System.Drawing.Size(553, 245);
            this.chartControl5.TabIndex = 0;
            this.chartControl5.CustomDrawSeriesPoint += new DevExpress.XtraCharts.CustomDrawSeriesPointEventHandler(this.chartControl5_CustomDrawSeriesPoint);
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.chartControl3);
            this.groupBox4.Location = new System.Drawing.Point(599, 304);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(571, 309);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "垂直面覆盖图";
            // 
            // chartControl3
            // 
            this.chartControl3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram21.AxisX.Range.ScrollingRange.SideMarginsEnabled = false;
            xyDiagram21.AxisX.Range.SideMarginsEnabled = false;
            xyDiagram21.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram21.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram21.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram21.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl3.Diagram = xyDiagram21;
            this.chartControl3.Location = new System.Drawing.Point(6, 21);
            this.chartControl3.Name = "chartControl3";
            pointSeriesLabel7.LineVisible = true;
            series39.Label = pointSeriesLabel7;
            series39.Name = "AntSeries";
            series39.ShowInLegend = false;
            series39.View = splineAreaSeriesView7;
            pointSeriesLabel8.LineVisible = true;
            series40.Label = pointSeriesLabel8;
            series40.Name = "StandardSeries";
            series40.ShowInLegend = false;
            series40.View = splineAreaSeriesView8;
            this.chartControl3.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series39,
        series40};
            pointSeriesLabel9.LineVisible = true;
            this.chartControl3.SeriesTemplate.Label = pointSeriesLabel9;
            splineAreaSeriesView9.Transparency = ((byte)(0));
            this.chartControl3.SeriesTemplate.View = splineAreaSeriesView9;
            this.chartControl3.Size = new System.Drawing.Size(559, 282);
            this.chartControl3.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Controls.Add(this.chartControl2);
            this.groupBox3.Location = new System.Drawing.Point(10, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 587);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "俯视面辐射图";
            // 
            // chartControl2
            // 
            this.chartControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControl2.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram7.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram7.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram7.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControl2.Diagram = radarDiagram7;
            this.chartControl2.Location = new System.Drawing.Point(6, 20);
            this.chartControl2.Name = "chartControl2";
            radarPointSeriesLabel19.LineVisible = true;
            series41.Label = radarPointSeriesLabel19;
            series41.Name = "AntSeries";
            series41.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint51,
            seriesPoint52,
            seriesPoint53,
            seriesPoint54,
            seriesPoint55});
            series41.ShowInLegend = false;
            series41.View = radarLineSeriesView7;
            radarPointSeriesLabel20.LineVisible = true;
            series42.Label = radarPointSeriesLabel20;
            series42.Name = "StandardSeries";
            series42.ShowInLegend = false;
            series42.View = radarLineSeriesView8;
            this.chartControl2.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series41,
        series42};
            radarPointSeriesLabel21.LineVisible = true;
            this.chartControl2.SeriesTemplate.Label = radarPointSeriesLabel21;
            this.chartControl2.SeriesTemplate.View = radarLineSeriesView9;
            this.chartControl2.Size = new System.Drawing.Size(571, 560);
            this.chartControl2.TabIndex = 0;
            this.chartControl2.SizeChanged += new System.EventHandler(this.chartControl2_SizeChanged);
            // 
            // xtraTabPage7
            // 
            this.xtraTabPage7.Controls.Add(this.groupControl4);
            this.xtraTabPage7.Name = "xtraTabPage7";
            this.xtraTabPage7.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage7.Text = "MR数据统计图";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.groupBox12);
            this.groupControl4.Controls.Add(this.groupBox8);
            this.groupControl4.Controls.Add(this.groupBox11);
            this.groupControl4.Controls.Add(this.groupBox10);
            this.groupControl4.Controls.Add(this.groupBox7);
            this.groupControl4.Controls.Add(this.groupBox9);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(1177, 620);
            this.groupControl4.TabIndex = 1;
            this.groupControl4.Text = "MR测量项数据图表";
            // 
            // groupBox12
            // 
            this.groupBox12.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox12.Controls.Add(this.rtbDesc);
            this.groupBox12.Location = new System.Drawing.Point(592, 414);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(576, 188);
            this.groupBox12.TabIndex = 4;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "MR测量项说明";
            // 
            // rtbDesc
            // 
            this.rtbDesc.Location = new System.Drawing.Point(6, 21);
            this.rtbDesc.Name = "rtbDesc";
            this.rtbDesc.ReadOnly = true;
            this.rtbDesc.Size = new System.Drawing.Size(564, 161);
            this.rtbDesc.TabIndex = 0;
            this.rtbDesc.Text = "";
            // 
            // groupBox8
            // 
            this.groupBox8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox8.Controls.Add(this.chartControlAoa);
            this.groupBox8.Location = new System.Drawing.Point(592, 220);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(576, 188);
            this.groupBox8.TabIndex = 4;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "ENB天线到达角（度）";
            // 
            // chartControlAoa
            // 
            xyDiagram17.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram17.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram17.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram17.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram17.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram17.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlAoa.Diagram = xyDiagram17;
            this.chartControlAoa.Legend.Visible = false;
            this.chartControlAoa.Location = new System.Drawing.Point(6, 21);
            this.chartControlAoa.Name = "chartControlAoa";
            sideBySideBarSeriesLabel25.LineVisible = true;
            series35.Label = sideBySideBarSeriesLabel25;
            series35.Name = "Series 1";
            this.chartControlAoa.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series35};
            sideBySideBarSeriesLabel26.LineVisible = true;
            this.chartControlAoa.SeriesTemplate.Label = sideBySideBarSeriesLabel26;
            this.chartControlAoa.Size = new System.Drawing.Size(564, 161);
            this.chartControlAoa.TabIndex = 1;
            // 
            // groupBox11
            // 
            this.groupBox11.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox11.Controls.Add(this.chartControlSinr);
            this.groupBox11.Location = new System.Drawing.Point(10, 414);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(576, 188);
            this.groupBox11.TabIndex = 1;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "上行信噪比（dB）";
            // 
            // chartControlSinr
            // 
            xyDiagram18.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram18.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram18.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram18.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram18.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram18.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlSinr.Diagram = xyDiagram18;
            this.chartControlSinr.Legend.Visible = false;
            this.chartControlSinr.Location = new System.Drawing.Point(6, 21);
            this.chartControlSinr.Name = "chartControlSinr";
            sideBySideBarSeriesLabel27.LineVisible = true;
            series36.Label = sideBySideBarSeriesLabel27;
            series36.Name = "Series 1";
            this.chartControlSinr.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series36};
            sideBySideBarSeriesLabel28.LineVisible = true;
            this.chartControlSinr.SeriesTemplate.Label = sideBySideBarSeriesLabel28;
            this.chartControlSinr.Size = new System.Drawing.Size(564, 161);
            this.chartControlSinr.TabIndex = 2;
            // 
            // groupBox10
            // 
            this.groupBox10.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox10.Controls.Add(this.chartControlRsrp);
            this.groupBox10.Location = new System.Drawing.Point(10, 220);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(576, 188);
            this.groupBox10.TabIndex = 3;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "参考信号接收功率（dB）";
            // 
            // chartControlRsrp
            // 
            xyDiagram22.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram22.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram22.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram22.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram22.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram22.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRsrp.Diagram = xyDiagram22;
            this.chartControlRsrp.Legend.Visible = false;
            this.chartControlRsrp.Location = new System.Drawing.Point(6, 21);
            this.chartControlRsrp.Name = "chartControlRsrp";
            sideBySideBarSeriesLabel31.LineVisible = true;
            series43.Label = sideBySideBarSeriesLabel31;
            series43.Name = "Series 1";
            this.chartControlRsrp.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series43};
            sideBySideBarSeriesLabel32.LineVisible = true;
            this.chartControlRsrp.SeriesTemplate.Label = sideBySideBarSeriesLabel32;
            this.chartControlRsrp.Size = new System.Drawing.Size(564, 161);
            this.chartControlRsrp.TabIndex = 1;
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox7.Controls.Add(this.chartControlPower);
            this.groupBox7.Location = new System.Drawing.Point(592, 26);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(576, 188);
            this.groupBox7.TabIndex = 2;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "UE发射功率余量（dB）";
            // 
            // chartControlPower
            // 
            xyDiagram23.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram23.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram23.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram23.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram23.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram23.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlPower.Diagram = xyDiagram23;
            this.chartControlPower.Legend.Visible = false;
            this.chartControlPower.Location = new System.Drawing.Point(6, 21);
            this.chartControlPower.Name = "chartControlPower";
            sideBySideBarSeriesLabel33.LineVisible = true;
            series44.Label = sideBySideBarSeriesLabel33;
            series44.Name = "Series 1";
            this.chartControlPower.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series44};
            sideBySideBarSeriesLabel34.LineVisible = true;
            this.chartControlPower.SeriesTemplate.Label = sideBySideBarSeriesLabel34;
            this.chartControlPower.Size = new System.Drawing.Size(564, 161);
            this.chartControlPower.TabIndex = 1;
            // 
            // groupBox9
            // 
            this.groupBox9.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox9.Controls.Add(this.chartControlTA);
            this.groupBox9.Location = new System.Drawing.Point(10, 26);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(576, 188);
            this.groupBox9.TabIndex = 0;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "时间提前量（米）";
            // 
            // chartControlTA
            // 
            xyDiagram24.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram24.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram24.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram24.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram24.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram24.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlTA.Diagram = xyDiagram24;
            this.chartControlTA.Legend.Visible = false;
            this.chartControlTA.Location = new System.Drawing.Point(6, 21);
            this.chartControlTA.Name = "chartControlTA";
            sideBySideBarSeriesLabel35.LineVisible = true;
            series45.Label = sideBySideBarSeriesLabel35;
            series45.Name = "Series 1";
            this.chartControlTA.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series45};
            sideBySideBarSeriesLabel36.LineVisible = true;
            this.chartControlTA.SeriesTemplate.Label = sideBySideBarSeriesLabel36;
            this.chartControlTA.Size = new System.Drawing.Size(564, 161);
            this.chartControlTA.TabIndex = 1;
            // 
            // xtraTabPage8
            // 
            this.xtraTabPage8.Controls.Add(this.groupControl5);
            this.xtraTabPage8.Name = "xtraTabPage8";
            this.xtraTabPage8.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage8.Text = "MR数据二维图";
            // 
            // groupControl5
            // 
            this.groupControl5.Controls.Add(this.groupBox13);
            this.groupControl5.Controls.Add(this.groupBox15);
            this.groupControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl5.Location = new System.Drawing.Point(0, 0);
            this.groupControl5.Name = "groupControl5";
            this.groupControl5.Size = new System.Drawing.Size(1177, 620);
            this.groupControl5.TabIndex = 1;
            this.groupControl5.Text = "MR全向分析";
            // 
            // groupBox13
            // 
            this.groupBox13.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox13.Controls.Add(this.chartControl6);
            this.groupBox13.Location = new System.Drawing.Point(599, 26);
            this.groupBox13.Name = "groupBox13";
            this.groupBox13.Size = new System.Drawing.Size(583, 587);
            this.groupBox13.TabIndex = 2;
            this.groupBox13.TabStop = false;
            this.groupBox13.Text = "MR覆盖模拟图";
            // 
            // chartControl6
            // 
            this.chartControl6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControl6.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram8.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram8.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram8.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControl6.Diagram = radarDiagram8;
            this.chartControl6.Location = new System.Drawing.Point(6, 21);
            this.chartControl6.Name = "chartControl6";
            radarPointSeriesLabel22.LineVisible = true;
            series46.Label = radarPointSeriesLabel22;
            series46.Name = "AntSeries";
            series46.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint56,
            seriesPoint57,
            seriesPoint58,
            seriesPoint59,
            seriesPoint60});
            series46.ShowInLegend = false;
            radarPointSeriesView13.PointMarkerOptions.Size = 10;
            series46.View = radarPointSeriesView13;
            radarPointSeriesLabel23.LineVisible = true;
            series47.Label = radarPointSeriesLabel23;
            series47.Name = "StandardSeries";
            series47.ShowInLegend = false;
            radarPointSeriesView14.PointMarkerOptions.Size = 10;
            series47.View = radarPointSeriesView14;
            this.chartControl6.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series46,
        series47};
            radarPointSeriesLabel24.LineVisible = true;
            this.chartControl6.SeriesTemplate.Label = radarPointSeriesLabel24;
            radarPointSeriesView15.PointMarkerOptions.Size = 10;
            this.chartControl6.SeriesTemplate.View = radarPointSeriesView15;
            this.chartControl6.Size = new System.Drawing.Size(565, 560);
            this.chartControl6.TabIndex = 0;
            // 
            // groupBox15
            // 
            this.groupBox15.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox15.Controls.Add(this.chartControl8);
            this.groupBox15.Location = new System.Drawing.Point(10, 26);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(583, 587);
            this.groupBox15.TabIndex = 0;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "MR采样点分布图";
            // 
            // chartControl8
            // 
            this.chartControl8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControl8.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram9.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram9.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram9.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControl8.Diagram = radarDiagram9;
            this.chartControl8.Legend.AlignmentHorizontal = DevExpress.XtraCharts.LegendAlignmentHorizontal.Right;
            this.chartControl8.Location = new System.Drawing.Point(6, 21);
            this.chartControl8.Name = "chartControl8";
            radarPointSeriesLabel25.LineVisible = true;
            series48.Label = radarPointSeriesLabel25;
            series48.Name = "AntSeries";
            series48.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint61,
            seriesPoint62,
            seriesPoint63,
            seriesPoint64,
            seriesPoint65});
            radarPointSeriesView16.PointMarkerOptions.Size = 10;
            series48.View = radarPointSeriesView16;
            radarPointSeriesLabel26.LineVisible = true;
            series49.Label = radarPointSeriesLabel26;
            series49.Name = "StandardSeries";
            radarPointSeriesView17.PointMarkerOptions.Size = 10;
            series49.View = radarPointSeriesView17;
            this.chartControl8.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series48,
        series49};
            radarPointSeriesLabel27.LineVisible = true;
            this.chartControl8.SeriesTemplate.Label = radarPointSeriesLabel27;
            radarPointSeriesView18.PointMarkerOptions.Size = 10;
            this.chartControl8.SeriesTemplate.View = radarPointSeriesView18;
            this.chartControl8.Size = new System.Drawing.Size(571, 560);
            this.chartControl8.TabIndex = 0;
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.groupBox5);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.PageVisible = false;
            this.xtraTabPage5.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage5.Text = "天线辐射三维分析";
            // 
            // groupBox5
            // 
            this.groupBox5.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox5.Controls.Add(this.chartControl4);
            this.groupBox5.Location = new System.Drawing.Point(3, 3);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(1171, 614);
            this.groupBox5.TabIndex = 0;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "三维绘图";
            // 
            // chartControl4
            // 
            this.chartControl4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram3D3.AxisX.Range.Auto = false;
            xyDiagram3D3.AxisX.Range.MaxValueSerializable = "50";
            xyDiagram3D3.AxisX.Range.MinValueSerializable = "0";
            xyDiagram3D3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3D3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3D3.AxisY.GridSpacing = 10D;
            xyDiagram3D3.AxisY.GridSpacingAuto = false;
            xyDiagram3D3.AxisY.Range.Auto = false;
            xyDiagram3D3.AxisY.Range.MaxValueSerializable = "-45";
            xyDiagram3D3.AxisY.Range.MinValueSerializable = "-120";
            xyDiagram3D3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3D3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3D3.RotationMatrixSerializable = "0.766044443118978;-0.219846310392954;0.604022773555054;0;0;0.939692620785908;0.34" +
    "2020143325669;0;-0.642787609686539;-0.262002630229385;0.719846310392954;0;0;0;0;" +
    "1";
            this.chartControl4.Diagram = xyDiagram3D3;
            this.chartControl4.Legend.AlignmentHorizontal = DevExpress.XtraCharts.LegendAlignmentHorizontal.Right;
            this.chartControl4.Location = new System.Drawing.Point(7, 21);
            this.chartControl4.Name = "chartControl4";
            bar3DSeriesLabel7.LineVisible = true;
            bar3DSeriesLabel7.Visible = false;
            series50.Label = bar3DSeriesLabel7;
            series50.Name = "Series 1";
            series50.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint66,
            seriesPoint67,
            seriesPoint68,
            seriesPoint69,
            seriesPoint70});
            manhattanBarSeriesView7.Transparency = ((byte)(135));
            series50.View = manhattanBarSeriesView7;
            bar3DSeriesLabel8.LineVisible = true;
            bar3DSeriesLabel8.Visible = false;
            series51.Label = bar3DSeriesLabel8;
            series51.Name = "Series 2";
            series51.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint71,
            seriesPoint72,
            seriesPoint73,
            seriesPoint74,
            seriesPoint75});
            manhattanBarSeriesView8.Transparency = ((byte)(135));
            series51.View = manhattanBarSeriesView8;
            this.chartControl4.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series50,
        series51};
            bar3DSeriesLabel9.LineVisible = true;
            bar3DSeriesLabel9.Visible = true;
            this.chartControl4.SeriesTemplate.Label = bar3DSeriesLabel9;
            this.chartControl4.SeriesTemplate.View = manhattanBarSeriesView9;
            this.chartControl4.Size = new System.Drawing.Size(1158, 587);
            this.chartControl4.TabIndex = 0;
            this.chartControl4.CustomDrawSeriesPoint += new DevExpress.XtraCharts.CustomDrawSeriesPointEventHandler(this.chartControl4_CustomDrawSeriesPoint);
            // 
            // LteScanAntennaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 650);
            this.Controls.Add(this.scanAntDataXTCtrl);
            this.Name = "LteScanAntennaForm";
            this.Text = "天线分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.scanAntDataXTCtrl)).EndInit();
            this.scanAntDataXTCtrl.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.xtraTabPage4.ResumeLayout(false);
            this.xtraTabPage4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.xtraTabPage6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAnt)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series37)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series38)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).EndInit();
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series39)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series40)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series42)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).EndInit();
            this.xtraTabPage7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupBox12.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series35)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAoa)).EndInit();
            this.groupBox11.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series36)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlSinr)).EndInit();
            this.groupBox10.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel31)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series43)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel32)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrp)).EndInit();
            this.groupBox7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel33)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series44)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel34)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlPower)).EndInit();
            this.groupBox9.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel35)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series45)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel36)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTA)).EndInit();
            this.xtraTabPage8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).EndInit();
            this.groupControl5.ResumeLayout(false);
            this.groupBox13.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series46)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series47)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl6)).EndInit();
            this.groupBox15.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series48)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series49)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl8)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3D3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(bar3DSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(manhattanBarSeriesView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series50)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(bar3DSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(manhattanBarSeriesView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series51)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(bar3DSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(manhattanBarSeriesView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl4)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl scanAntDataXTCtrl;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private System.Windows.Forms.DataGridView dataGridView;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluTarget;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cbbxSeries2;
        private System.Windows.Forms.ComboBox cbbxSeries1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShowChart;
        private System.Windows.Forms.ToolStripMenuItem miShowGis;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartControl3;
        private DevExpress.XtraCharts.ChartControl chartControl2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraCharts.ChartControl chartControl4;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraCharts.ChartControl chartControl5;
        private System.Windows.Forms.ToolStripMenuItem 拆分导出CSVToolStripMenuItem;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage6;
        private System.Windows.Forms.DataGridView dataGridViewAnt;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage7;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox groupBox11;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.GroupBox groupBox9;
        private DevExpress.XtraCharts.ChartControl chartControlTA;
        private DevExpress.XtraCharts.ChartControl chartControlAoa;
        private DevExpress.XtraCharts.ChartControl chartControlSinr;
        private DevExpress.XtraCharts.ChartControl chartControlRsrp;
        private DevExpress.XtraCharts.ChartControl chartControlPower;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage8;
        private DevExpress.XtraEditors.GroupControl groupControl5;
        private System.Windows.Forms.GroupBox groupBox15;
        private DevExpress.XtraCharts.ChartControl chartControl8;
        private System.Windows.Forms.GroupBox groupBox13;
        private DevExpress.XtraCharts.ChartControl chartControl6;
        private System.Windows.Forms.ToolStripMenuItem miShowMRSimulation;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column39;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column7;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellname;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBcch;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellChanel;
        private System.Windows.Forms.DataGridViewTextBoxColumn coldMaxPcc0_30_150_180;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSection;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRxlevSampleNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSamplePect;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgRsrp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colMaxRsrp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSinr;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSampleTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAnaType;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_dir;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgRsrp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgSinr;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colDate;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCityName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCell;
        private System.Windows.Forms.DataGridViewTextBoxColumn colGridName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBscName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colFreq;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSampleNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRsrpAvg;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSinrRate;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSinrAvg;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellDistAvg;
        private System.Windows.Forms.DataGridViewTextBoxColumn colvender;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn colbeamwidth;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcovertype;
        private System.Windows.Forms.DataGridViewTextBoxColumn colgmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3db;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6db;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange8;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase8;
        private System.Windows.Forms.DataGridViewTextBoxColumn col1ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn col2ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn col1para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col2para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col4para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col5para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col7para;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column27;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column29;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column33;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column34;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column35;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column36;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column37;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column38;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column30;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column31;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column32;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RichTextBox rtbDesc;
    }
}