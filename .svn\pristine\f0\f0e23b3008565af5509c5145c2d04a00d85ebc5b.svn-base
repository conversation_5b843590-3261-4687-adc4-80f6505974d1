﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class FileDistributeSetting : ShowFuncForm
    {
        public FileDistributeSetting(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "文件分发配置"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18030, this.Name);
        }

        protected override void showForm()
        {
            FileDistributeConfigForm configForm = new FileDistributeConfigForm();
            configForm.ShowDialog();
        }
    }
}
