﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.ZTFuncZTAngleCalculate;
using StationRoadDistanceUtil;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTAngleCalculate : QueryBase
    {
        private AngleCalculateResultForm angleCalculateResultForm = null;
        private Condition curCondition = null;
        private List<AngleCalculateResultItem> listResultItem = null;
        private string resultString = "";

        public ZTAngleCalculate(MainModel mainModel)
            : base(mainModel)
        {

        }
        protected override void query()
        {
            this.resultString = "";
            ConditionSettingForm conForm = new ConditionSettingForm();
            if (conForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            this.curCondition = conForm.GetCondition();
            this.doMainWork();
            if (this.angleCalculateResultForm == null || this.angleCalculateResultForm.IsDisposed)
            {
                this.angleCalculateResultForm = new AngleCalculateResultForm();
            }
            this.angleCalculateResultForm.fillData(this.listResultItem);
            this.angleCalculateResultForm.Show();
            this.angleCalculateResultForm.Focus();
            MessageBox.Show(this.resultString);
        }
        private void doMainWork()
        {
            try
            {
                this.listResultItem = null ;
                string fileFullName = this.curCondition.FileFullName;
                List<AngleCalculateResultItem> listItem = new List<AngleCalculateResultItem>();
                ExcelNPOIReaderExt excel = new ExcelNPOIReaderExt(fileFullName);
                excel.UseSheet(excel.GetSheets()[0]);
                int rowIndex = 1;
                string errorLines = "";
                double Lon0 = 0, Lat0 = 0;
                double Lon1 = 0, Lat1 = 0;
                double angle = 0;
                int counter = 1;
                StringBuilder sb = new StringBuilder(errorLines);
                while (true)
                {
                    string[] row = excel.GetRowOfUsingSheet(rowIndex++);
                    if (row.Length == 0) break;
                    if (row.Length < 4 ||
                        (!double.TryParse(row[0], out Lon0)) || (!double.TryParse(row[1], out Lat0)) ||
                        (!double.TryParse(row[2], out Lon1)) || (!double.TryParse(row[3], out Lat1)) ||
                        Lon0 < -180 || Lon0 > 180 || Lat0 < -90 || Lat0 > 90 ||
                        Lon1 < -180 || Lon1 > 180 || Lat1 < -90 || Lat1 > 90)
                    {
                        sb.Append(rowIndex + "  ");
                    }
                    else
                    {
                        angle = Math.Round(MathFuncs.getAngleFromPointToPoint_D(Lon0, Lat0, Lon1, Lat1), 2);
                        AngleCalculateResultItem item = new AngleCalculateResultItem();
                        item.SN = (counter++).ToString();
                        item.Lon0 = Lon0.ToString();
                        item.Lat0 = Lat0.ToString();
                        item.Lon1 = Lon1.ToString();
                        item.Lat1 = Lat1.ToString();
                        item.Angle = Math.Round(angle, 2).ToString();
                        listItem.Add(item);
                    }
                }
                errorLines = sb.ToString();
                this.listResultItem = listItem;
                if (String.IsNullOrEmpty(errorLines))
                {
                    this.resultString = "读取文件完毕，没有发现异常!";
                    return;
                }
                else
                {
                    this.resultString = "读取文件完毕，发现异常!\n异常的行下标：" + errorLines;
                    return;
                }
            }
            catch (Exception ex)
            { MessageBox.Show(ex.Message); }
        }
        public override string Name
        {
            get { return "夹角计算"; }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20042, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }
    }

    public class Condition
    {
        public string FileFullName { set; get; }
    }
}
