using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    [System.ComponentModel.ToolboxItem(false)]
    public partial class MapFormCellLayerInterfereProperties : MTLayerPropUserControl
    {
        private MapCellLayer layer;
        private ColorDialog colorDialog = new ColorDialog();
        private FontDialog fontDialog = new FontDialog();
        public MapFormCellLayerInterfereProperties()
        {
            InitializeComponent();
        }
        public override void Setup(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            Text = "Interfere";
            layer = (MapCellLayer)obj;
            cbxDisplay.Checked = layer.DrawInterfereDis;
            labelDisplayColor.BackColor = layer.DrawInterfereColor;
            numericUpDownSize.Value = layer.DrawInterfereSizi;
            groupBox1.Enabled = buttonFont.Enabled = cbxDrawBTSLabel.Checked = layer.DrawInterfereLabel;

            cbxName.Checked = layer.DrawInterfereName;
            //cbxLongitude.Checked = layer.DrawPlanBTSLongi;
            //cbxLatitude.Checked = layer.DrawPlanBTSLati;
            //cbxType.Checked = layer.DrawPlanBTSType;
            //cbxReason.Checked = layer.DrawPlanBTSReason;
            //cbxProgress.Checked = layer.DrawPlanBTSProgress;
            //cbxAddress.Checked = layer.DrawPlanBTSAddr;
            //cbxComment.Checked = layer.DrawPlanBTSComment;
        }

        private void cbxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawInterfereDis = cbxDisplay.Checked;
        }
        private void labelDisplayColor_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelDisplayColor.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                labelDisplayColor.BackColor = Color.FromArgb(trackBarOpacity.Value, colorDialog.Color);
                layer.DrawInterfereColor = labelDisplayColor.BackColor;
                layer.RefreshBrushes();
            }
        }

        private void TrackBarOpacity_Scroll(object sender, EventArgs e)
        {
            labelDisplayColor.BackColor = Color.FromArgb(trackBarOpacity.Value, labelDisplayColor.BackColor);
            layer.RefreshBrushes();
        }

        private void checkBoxDrawBTSLabel_CheckedChanged(object sender, EventArgs e)
        {
            groupBox1.Enabled = buttonFont.Enabled = layer.DrawInterfereLabel = cbxDrawBTSLabel.Checked;
        }

        private void buttonFont_Click(object sender, EventArgs e)
        {
            //fontDialog.Font = layer.DrawInterfereFont;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                //layer.DrawInterfereFont = fontDialog.Font;
            }
        }

        private void cbxName_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawInterfereName = cbxName.Checked;
        }

        private void cbxLongitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSLongi = cbxLongitude.Checked;
        }

        private void cbxLatitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSLati = cbxLatitude.Checked;
        }

        private void cbxType_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSType = cbxType.Checked;
        }

        //private void cbxNO_CheckedChanged(object sender, EventArgs e)
        //{
        //    layer.DrawPlanBTSNo = cbxNO.Checked;
        //}

        private void checkReason_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSReason = cbxReason.Checked;
        }

        private void cbxProgress_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSProgress = cbxProgress.Checked;
        }

        private void cbxAddress_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSAddr = cbxAddress.Checked;
        }

        private void cbxComment_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawPlanBTSComment = cbxComment.Checked;
        }

        private void numericUpDownSize_ValueChanged(object sender, EventArgs e)
        {
            layer.DrawInterfereSizi = (int)numericUpDownSize.Value;
        }
    }
}
