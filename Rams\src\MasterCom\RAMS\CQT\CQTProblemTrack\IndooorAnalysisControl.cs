﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;

namespace BusinessTopicAnalysisFeature.InDoorAnalysis
{
    public partial class IndooorAnalysisControl : UserControl
    {
        public IndooorAnalysisControl()
        {
            InitializeComponent();
        }

        public delegate void FillDataCallback(string title, DataTable table);

        public void FillData(string title,DataTable table)
        {
            if (this.chartControl1.InvokeRequired)
            {
                this.chartControl1.Invoke(new FillDataCallback(this.FillData), title, table);
                return;
            }
            Series serialdt = chartControl1.Series[0]; 
            chartControl1.Titles[0].Text = title;
            serialdt.DataSource = table;
            serialdt.ArgumentScaleType = ScaleType.Qualitative;
            serialdt.ArgumentDataMember = "项";
            serialdt.ValueScaleType = ScaleType.Numerical;
            serialdt.ValueDataMembers.AddRange(new string[] { "值" });
            serialdt.PointOptions.PointView = PointView.ArgumentAndValues;
            serialdt.PointOptions.ValueNumericOptions.Format = NumericFormat.Percent;
        }

    }
}
