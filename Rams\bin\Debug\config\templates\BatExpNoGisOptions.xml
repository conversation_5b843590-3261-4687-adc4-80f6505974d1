<?xml version="1.0"?>
<Configs>
  <Config name="Config">
    <Item name="SavePath" typeName="String">C:\Users\<USER>\Desktop</Item>
    <Item name="Items" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">GSM数据</Item>
        <Item typeName="Boolean" key="IsCheck">True</Item>
        <Item typeName="IList" key="ReportNames">
          <Item typeName="String">GSM数据业务_日表</Item>
          <Item typeName="String">GSM数据业务_月表</Item>
        </Item>
        <Item typeName="IList" key="DistrictIDs">
          <Item typeName="Int32">1</Item>
        </Item>
        <Item typeName="IList" key="ProjectIDs">
          <Item typeName="Int32">5</Item>
          <Item typeName="Int32">4</Item>
          <Item typeName="Int32">3</Item>
          <Item typeName="Int32">2</Item>
          <Item typeName="Int32">1</Item>
          <Item typeName="Int32">8</Item>
          <Item typeName="Int32">6</Item>
          <Item typeName="Int32">7</Item>
        </Item>
        <Item typeName="IList" key="ServiceIDs">
          <Item typeName="Int32">3</Item>
          <Item typeName="Int32">2</Item>
        </Item>
        <Item typeName="IList" key="AgentIDs">
          <Item typeName="Int32">0</Item>
        </Item>
        <Item typeName="IDictionary" key="AreaTypeIDDic">
          <Item typeName="IList" key="1">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
          </Item>
          <Item typeName="IList" key="2">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
          </Item>
        </Item>
        <Item typeName="Boolean" key="CheckFileName">False</Item>
        <Item typeName="String" key="FileNameFilter" />
        <Item typeName="Boolean" key="IsMergeData">True</Item>
        <Item typeName="Int32" key="QueryFunc">0</Item>
        <Item typeName="String" key="FileName">(区域名)_(报表名)</Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE业务</Item>
        <Item typeName="Boolean" key="IsCheck">False</Item>
        <Item typeName="IList" key="ReportNames">
          <Item typeName="String">LTE数据业务_上传and下载_按日_移动TDD联通电信FDD统计报表</Item>
          <Item typeName="String">LTE综合统计报表</Item>
        </Item>
        <Item typeName="IList" key="DistrictIDs">
          <Item typeName="Int32">1</Item>
        </Item>
        <Item typeName="IList" key="ProjectIDs">
          <Item typeName="Int32">5</Item>
          <Item typeName="Int32">4</Item>
          <Item typeName="Int32">3</Item>
          <Item typeName="Int32">7</Item>
          <Item typeName="Int32">2</Item>
          <Item typeName="Int32">1</Item>
          <Item typeName="Int32">8</Item>
          <Item typeName="Int32">6</Item>
        </Item>
        <Item typeName="IList" key="ServiceIDs">
          <Item typeName="Int32">34</Item>
          <Item typeName="Int32">33</Item>
          <Item typeName="Int32">43</Item>
        </Item>
        <Item typeName="IList" key="AgentIDs">
          <Item typeName="Int32">0</Item>
        </Item>
        <Item typeName="IDictionary" key="AreaTypeIDDic">
          <Item typeName="IList" key="1">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
          </Item>
          <Item typeName="IList" key="100">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
          </Item>
        </Item>
        <Item typeName="Boolean" key="CheckFileName">False</Item>
        <Item key="FileNameFilter" />
        <Item typeName="Boolean" key="IsMergeData">True</Item>
        <Item typeName="Int32" key="QueryFunc">0</Item>
        <Item typeName="String" key="FileName" />
      </Item>
    </Item>
  </Config>
</Configs>