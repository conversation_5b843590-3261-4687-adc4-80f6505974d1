﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRReasonOptionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpDefine = new DevExpress.XtraEditors.GroupControl();
            this.chkAnaLTE = new System.Windows.Forms.CheckBox();
            this.numMaxSINR = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.grpDetail = new DevExpress.XtraEditors.GroupControl();
            this.group = new DevExpress.XtraEditors.GroupControl();
            this.grpDetails = new System.Windows.Forms.Panel();
            this.reasonPnlSuddenWeak = new MasterCom.RAMS.ZTFunc.NRReasonPnlSuddenWeak();
            this.reasonPnlLeakOutCell = new DevExpress.XtraEditors.GroupControl();
            this.label3 = new System.Windows.Forms.Label();
            this.reasonPnlBackCover = new MasterCom.RAMS.ZTFunc.NRReasonPnlBackCover();
            this.reasonPnlOverCover = new MasterCom.RAMS.ZTFunc.NRReasonPnlOverCover();
            this.reasonPnlHandoverProblem = new MasterCom.RAMS.ZTFunc.NRReasonPnlHandoverProblem();
            this.reasonPnlHandOverUnTimely = new MasterCom.RAMS.ZTFunc.NRReasonPnlHandOverUnTimely();
            this.reasonPnlChangeFreq = new MasterCom.RAMS.ZTFunc.NRReasonPnlChangeFreq();
            this.reasonPnlMod3 = new MasterCom.RAMS.ZTFunc.NRReasonPnlMod3();
            this.pnlMultiCover = new MasterCom.RAMS.ZTFunc.NRReasonPnlMultiCover();
            this.pnlWeakCover = new MasterCom.RAMS.ZTFunc.NRReasonPnlWeakCover();
            this.grpReason = new DevExpress.XtraEditors.GroupControl();
            this.btnDown = new DevExpress.XtraEditors.SimpleButton();
            this.btnUp = new DevExpress.XtraEditors.SimpleButton();
            this.chkList = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.grpDefine)).BeginInit();
            this.grpDefine.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpDetail)).BeginInit();
            this.grpDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.group)).BeginInit();
            this.group.SuspendLayout();
            this.grpDetails.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.reasonPnlLeakOutCell)).BeginInit();
            this.reasonPnlLeakOutCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpReason)).BeginInit();
            this.grpReason.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkList)).BeginInit();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // grpDefine
            // 
            this.grpDefine.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grpDefine.Appearance.Options.UseFont = true;
            this.grpDefine.Controls.Add(this.chkAnaLTE);
            this.grpDefine.Controls.Add(this.numMaxSINR);
            this.grpDefine.Controls.Add(this.label1);
            this.grpDefine.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpDefine.Location = new System.Drawing.Point(0, 0);
            this.grpDefine.Name = "grpDefine";
            this.grpDefine.Size = new System.Drawing.Size(1022, 70);
            this.grpDefine.TabIndex = 0;
            this.grpDefine.Text = "SINR质差定义";
            // 
            // chkAnaLTE
            // 
            this.chkAnaLTE.AutoSize = true;
            this.chkAnaLTE.Location = new System.Drawing.Point(168, 41);
            this.chkAnaLTE.Name = "chkAnaLTE";
            this.chkAnaLTE.Size = new System.Drawing.Size(90, 16);
            this.chkAnaLTE.TabIndex = 29;
            this.chkAnaLTE.Text = "分析LTE切换";
            this.chkAnaLTE.UseVisualStyleBackColor = true;
            this.chkAnaLTE.CheckedChanged += new System.EventHandler(this.chkAnaLTE_CheckedChanged);
            // 
            // numMaxSINR
            // 
            this.numMaxSINR.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numMaxSINR.Location = new System.Drawing.Point(64, 36);
            this.numMaxSINR.Name = "numMaxSINR";
            this.numMaxSINR.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSINR.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSINR.Properties.MinValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMaxSINR.Size = new System.Drawing.Size(75, 21);
            this.numMaxSINR.TabIndex = 0;
            this.numMaxSINR.EditValueChanged += new System.EventHandler(this.numMaxSINR_EditValueChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(17, 41);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "SINR≤";
            // 
            // grpDetail
            // 
            this.grpDetail.Controls.Add(this.group);
            this.grpDetail.Controls.Add(this.grpReason);
            this.grpDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpDetail.Location = new System.Drawing.Point(0, 70);
            this.grpDetail.Name = "grpDetail";
            this.grpDetail.Size = new System.Drawing.Size(1022, 439);
            this.grpDetail.TabIndex = 1;
            this.grpDetail.Text = "分析规则";
            // 
            // group
            // 
            this.group.Controls.Add(this.grpDetails);
            this.group.Dock = System.Windows.Forms.DockStyle.Fill;
            this.group.Location = new System.Drawing.Point(269, 23);
            this.group.Name = "group";
            this.group.Size = new System.Drawing.Size(751, 414);
            this.group.TabIndex = 3;
            this.group.Text = "详细";
            this.group.MouseEnter += new System.EventHandler(this.group_MouseEnter);
            // 
            // grpDetails
            // 
            this.grpDetails.AutoScroll = true;
            this.grpDetails.Controls.Add(this.reasonPnlSuddenWeak);
            this.grpDetails.Controls.Add(this.reasonPnlLeakOutCell);
            this.grpDetails.Controls.Add(this.reasonPnlBackCover);
            this.grpDetails.Controls.Add(this.reasonPnlOverCover);
            this.grpDetails.Controls.Add(this.reasonPnlHandoverProblem);
            this.grpDetails.Controls.Add(this.reasonPnlHandOverUnTimely);
            this.grpDetails.Controls.Add(this.reasonPnlChangeFreq);
            this.grpDetails.Controls.Add(this.reasonPnlMod3);
            this.grpDetails.Controls.Add(this.pnlMultiCover);
            this.grpDetails.Controls.Add(this.pnlWeakCover);
            this.grpDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpDetails.Location = new System.Drawing.Point(2, 23);
            this.grpDetails.Name = "grpDetails";
            this.grpDetails.Size = new System.Drawing.Size(747, 389);
            this.grpDetails.TabIndex = 2;
            this.grpDetails.MouseEnter += new System.EventHandler(this.group_MouseEnter);
            // 
            // reasonPnlSuddenWeak
            // 
            this.reasonPnlSuddenWeak.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlSuddenWeak.Location = new System.Drawing.Point(0, 687);
            this.reasonPnlSuddenWeak.Name = "reasonPnlSuddenWeak";
            this.reasonPnlSuddenWeak.Size = new System.Drawing.Size(730, 71);
            this.reasonPnlSuddenWeak.TabIndex = 12;
            // 
            // reasonPnlLeakOutCell
            // 
            this.reasonPnlLeakOutCell.Controls.Add(this.label3);
            this.reasonPnlLeakOutCell.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlLeakOutCell.Location = new System.Drawing.Point(0, 624);
            this.reasonPnlLeakOutCell.Name = "reasonPnlLeakOutCell";
            this.reasonPnlLeakOutCell.Size = new System.Drawing.Size(730, 63);
            this.reasonPnlLeakOutCell.TabIndex = 11;
            this.reasonPnlLeakOutCell.Text = "室分外漏";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(36, 37);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 1;
            this.label3.Text = "占用到室分小区";
            // 
            // reasonPnlBackCover
            // 
            this.reasonPnlBackCover.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlBackCover.Location = new System.Drawing.Point(0, 556);
            this.reasonPnlBackCover.Name = "reasonPnlBackCover";
            this.reasonPnlBackCover.Size = new System.Drawing.Size(730, 68);
            this.reasonPnlBackCover.TabIndex = 10;
            // 
            // reasonPnlOverCover
            // 
            this.reasonPnlOverCover.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlOverCover.Location = new System.Drawing.Point(0, 485);
            this.reasonPnlOverCover.Name = "reasonPnlOverCover";
            this.reasonPnlOverCover.Size = new System.Drawing.Size(730, 71);
            this.reasonPnlOverCover.TabIndex = 9;
            // 
            // reasonPnlHandoverProblem
            // 
            this.reasonPnlHandoverProblem.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlHandoverProblem.Location = new System.Drawing.Point(0, 350);
            this.reasonPnlHandoverProblem.Name = "reasonPnlHandoverProblem";
            this.reasonPnlHandoverProblem.Size = new System.Drawing.Size(730, 135);
            this.reasonPnlHandoverProblem.TabIndex = 8;
            // 
            // reasonPnlHandOverUnTimely
            // 
            this.reasonPnlHandOverUnTimely.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlHandOverUnTimely.Location = new System.Drawing.Point(0, 280);
            this.reasonPnlHandOverUnTimely.Name = "reasonPnlHandOverUnTimely";
            this.reasonPnlHandOverUnTimely.Size = new System.Drawing.Size(730, 70);
            this.reasonPnlHandOverUnTimely.TabIndex = 7;
            // 
            // reasonPnlChangeFreq
            // 
            this.reasonPnlChangeFreq.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlChangeFreq.Location = new System.Drawing.Point(0, 210);
            this.reasonPnlChangeFreq.Name = "reasonPnlChangeFreq";
            this.reasonPnlChangeFreq.Size = new System.Drawing.Size(730, 70);
            this.reasonPnlChangeFreq.TabIndex = 6;
            // 
            // reasonPnlMod3
            // 
            this.reasonPnlMod3.Dock = System.Windows.Forms.DockStyle.Top;
            this.reasonPnlMod3.Location = new System.Drawing.Point(0, 140);
            this.reasonPnlMod3.Name = "reasonPnlMod3";
            this.reasonPnlMod3.Size = new System.Drawing.Size(730, 70);
            this.reasonPnlMod3.TabIndex = 4;
            // 
            // pnlMultiCover
            // 
            this.pnlMultiCover.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlMultiCover.Location = new System.Drawing.Point(0, 70);
            this.pnlMultiCover.Name = "pnlMultiCover";
            this.pnlMultiCover.Size = new System.Drawing.Size(730, 70);
            this.pnlMultiCover.TabIndex = 3;
            // 
            // pnlWeakCover
            // 
            this.pnlWeakCover.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlWeakCover.Location = new System.Drawing.Point(0, 0);
            this.pnlWeakCover.Name = "pnlWeakCover";
            this.pnlWeakCover.Size = new System.Drawing.Size(730, 70);
            this.pnlWeakCover.TabIndex = 2;
            // 
            // grpReason
            // 
            this.grpReason.Controls.Add(this.btnDown);
            this.grpReason.Controls.Add(this.btnUp);
            this.grpReason.Controls.Add(this.chkList);
            this.grpReason.Dock = System.Windows.Forms.DockStyle.Left;
            this.grpReason.Location = new System.Drawing.Point(2, 23);
            this.grpReason.Name = "grpReason";
            this.grpReason.Size = new System.Drawing.Size(267, 414);
            this.grpReason.TabIndex = 1;
            this.grpReason.Text = "原因分析次序";
            this.grpReason.MouseLeave += new System.EventHandler(this.group_MouseEnter);
            // 
            // btnDown
            // 
            this.btnDown.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDown.Appearance.Options.UseFont = true;
            this.btnDown.Enabled = false;
            this.btnDown.Location = new System.Drawing.Point(201, 177);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(55, 23);
            this.btnDown.TabIndex = 2;
            this.btnDown.Text = "↓";
            this.btnDown.Click += new System.EventHandler(this.btnDown_Click);
            // 
            // btnUp
            // 
            this.btnUp.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnUp.Appearance.Options.UseFont = true;
            this.btnUp.Enabled = false;
            this.btnUp.Location = new System.Drawing.Point(201, 124);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(55, 23);
            this.btnUp.TabIndex = 1;
            this.btnUp.Text = "↑";
            this.btnUp.Click += new System.EventHandler(this.btnUp_Click);
            // 
            // chkList
            // 
            this.chkList.Dock = System.Windows.Forms.DockStyle.Left;
            this.chkList.Location = new System.Drawing.Point(2, 23);
            this.chkList.Name = "chkList";
            this.chkList.Size = new System.Drawing.Size(193, 389);
            this.chkList.TabIndex = 0;
            this.chkList.ItemChecking += new DevExpress.XtraEditors.Controls.ItemCheckingEventHandler(this.chkList_ItemChecking);
            this.chkList.ItemCheck += new DevExpress.XtraEditors.Controls.ItemCheckEventHandler(this.chkList_ItemCheck);
            this.chkList.SelectedIndexChanged += new System.EventHandler(this.chkList_SelectedIndexChanged);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnCancel);
            this.panel1.Controls.Add(this.btnOK);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 509);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1022, 53);
            this.panel1.TabIndex = 7;
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(935, 18);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(854, 18);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            // 
            // NRReasonOptionDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1022, 562);
            this.Controls.Add(this.grpDetail);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.grpDefine);
            this.MinimumSize = new System.Drawing.Size(997, 519);
            this.Name = "NRReasonOptionDlg";
            this.Text = "质差原因设置";
            ((System.ComponentModel.ISupportInitialize)(this.grpDefine)).EndInit();
            this.grpDefine.ResumeLayout(false);
            this.grpDefine.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpDetail)).EndInit();
            this.grpDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.group)).EndInit();
            this.group.ResumeLayout(false);
            this.grpDetails.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.reasonPnlLeakOutCell)).EndInit();
            this.reasonPnlLeakOutCell.ResumeLayout(false);
            this.reasonPnlLeakOutCell.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpReason)).EndInit();
            this.grpReason.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkList)).EndInit();
            this.panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl grpDefine;
        private DevExpress.XtraEditors.SpinEdit numMaxSINR;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.GroupControl grpDetail;
        private DevExpress.XtraEditors.GroupControl grpReason;
        private DevExpress.XtraEditors.CheckedListBoxControl chkList;
        private DevExpress.XtraEditors.SimpleButton btnDown;
        private DevExpress.XtraEditors.SimpleButton btnUp;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private System.Windows.Forms.Panel grpDetails;
        private NRReasonPnlMod3 reasonPnlMod3;
        private NRReasonPnlMultiCover pnlMultiCover;
        private NRReasonPnlWeakCover pnlWeakCover;
        private DevExpress.XtraEditors.GroupControl group;
        private NRReasonPnlChangeFreq reasonPnlChangeFreq;
        private NRReasonPnlHandOverUnTimely reasonPnlHandOverUnTimely;
        private NRReasonPnlHandoverProblem reasonPnlHandoverProblem;
        private NRReasonPnlOverCover reasonPnlOverCover;
        private NRReasonPnlBackCover reasonPnlBackCover;
        private DevExpress.XtraEditors.GroupControl reasonPnlLeakOutCell;
        private System.Windows.Forms.Label label3;
        private NRReasonPnlSuddenWeak reasonPnlSuddenWeak;
        private System.Windows.Forms.CheckBox chkAnaLTE;
    }
}