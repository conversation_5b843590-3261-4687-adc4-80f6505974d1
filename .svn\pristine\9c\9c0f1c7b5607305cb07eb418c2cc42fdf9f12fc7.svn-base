﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTECSFBSignalAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLTECSFBSignalAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miCompareReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumn1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumn20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ListViewCallInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMO = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOCSFBRelease = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOEventLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOEventLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOCSFBReleaseLTECell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOLTEReleaseDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOSetupGSMCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOGSMReleaseDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOHOCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMOHODelay = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTCSFBRelease = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTEventLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTEventLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTCSFBReleaseLTECell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTLTEReleaseDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTSetupGSMCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTGSMReleaseDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTHOCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMTHODelay = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallInfo)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miCompareReplay,
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(152, 140);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(151, 26);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miCompareReplay
            // 
            this.miCompareReplay.Name = "miCompareReplay";
            this.miCompareReplay.Size = new System.Drawing.Size(151, 26);
            this.miCompareReplay.Text = "对比回放";
            this.miCompareReplay.Click += new System.EventHandler(this.miCompareReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(151, 26);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(148, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(151, 26);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(151, 26);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "序号";
            // 
            // olvColumn2
            // 
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "主叫";
            this.olvColumn2.Width = 100;
            // 
            // olvColumn3
            // 
            this.olvColumn3.HeaderFont = null;
            this.olvColumn3.Text = "被叫";
            this.olvColumn3.Width = 100;
            // 
            // olvColumn4
            // 
            this.olvColumn4.HeaderFont = null;
            this.olvColumn4.Text = "起呼时间";
            this.olvColumn4.Width = 200;
            // 
            // olvColumn5
            // 
            this.olvColumn5.HeaderFont = null;
            this.olvColumn5.Text = "振铃时间";
            this.olvColumn5.Width = 200;
            // 
            // olvColumn6
            // 
            this.olvColumn6.HeaderFont = null;
            this.olvColumn6.Text = "接续时长（毫秒）";
            this.olvColumn6.Width = 110;
            // 
            // olvColumn7
            // 
            this.olvColumn7.HeaderFont = null;
            this.olvColumn7.Text = "经度";
            this.olvColumn7.Width = 80;
            // 
            // olvColumn8
            // 
            this.olvColumn8.HeaderFont = null;
            this.olvColumn8.Text = "纬度";
            this.olvColumn8.Width = 80;
            // 
            // olvColumn9
            // 
            this.olvColumn9.HeaderFont = null;
            this.olvColumn9.Text = "信令名称";
            this.olvColumn9.Width = 200;
            // 
            // olvColumn10
            // 
            this.olvColumn10.HeaderFont = null;
            this.olvColumn10.Text = "信令时间";
            this.olvColumn10.Width = 200;
            // 
            // olvColumn11
            // 
            this.olvColumn11.AspectName = "";
            this.olvColumn11.HeaderFont = null;
            this.olvColumn11.Text = "序号";
            // 
            // olvColumn12
            // 
            this.olvColumn12.HeaderFont = null;
            this.olvColumn12.Text = "主叫";
            this.olvColumn12.Width = 100;
            // 
            // olvColumn13
            // 
            this.olvColumn13.HeaderFont = null;
            this.olvColumn13.Text = "被叫";
            this.olvColumn13.Width = 100;
            // 
            // olvColumn14
            // 
            this.olvColumn14.HeaderFont = null;
            this.olvColumn14.Text = "起呼时间";
            this.olvColumn14.Width = 200;
            // 
            // olvColumn15
            // 
            this.olvColumn15.HeaderFont = null;
            this.olvColumn15.Text = "振铃时间";
            this.olvColumn15.Width = 200;
            // 
            // olvColumn16
            // 
            this.olvColumn16.HeaderFont = null;
            this.olvColumn16.Text = "接续时长（毫秒）";
            this.olvColumn16.Width = 110;
            // 
            // olvColumn17
            // 
            this.olvColumn17.HeaderFont = null;
            this.olvColumn17.Text = "经度";
            this.olvColumn17.Width = 80;
            // 
            // olvColumn18
            // 
            this.olvColumn18.HeaderFont = null;
            this.olvColumn18.Text = "纬度";
            this.olvColumn18.Width = 80;
            // 
            // olvColumn19
            // 
            this.olvColumn19.HeaderFont = null;
            this.olvColumn19.Text = "信令名称";
            this.olvColumn19.Width = 200;
            // 
            // olvColumn20
            // 
            this.olvColumn20.HeaderFont = null;
            this.olvColumn20.Text = "信令时间";
            this.olvColumn20.Width = 200;
            // 
            // ListViewCallInfo
            // 
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnSN);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMO);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMT);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOCSFBRelease);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOEventLongitude);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOEventLatitude);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOCSFBReleaseLTECell);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOLTEReleaseDistance);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOSetupGSMCell);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOGSMReleaseDistance);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOHOCell);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMOHODelay);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTCSFBRelease);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTEventLongitude);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTEventLatitude);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTCSFBReleaseLTECell);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTLTEReleaseDistance);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTSetupGSMCell);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTGSMReleaseDistance);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTHOCell);
            this.ListViewCallInfo.AllColumns.Add(this.olvColumnMTHODelay);
            this.ListViewCallInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnMO,
            this.olvColumnMT,
            this.olvColumnMOCSFBRelease,
            this.olvColumnMOEventLongitude,
            this.olvColumnMOEventLatitude,
            this.olvColumnMOCSFBReleaseLTECell,
            this.olvColumnMOLTEReleaseDistance,
            this.olvColumnMOSetupGSMCell,
            this.olvColumnMOGSMReleaseDistance,
            this.olvColumnMOHOCell,
            this.olvColumnMOHODelay,
            this.olvColumnMTCSFBRelease,
            this.olvColumnMTEventLongitude,
            this.olvColumnMTEventLatitude,
            this.olvColumnMTCSFBReleaseLTECell,
            this.olvColumnMTLTEReleaseDistance,
            this.olvColumnMTSetupGSMCell,
            this.olvColumnMTGSMReleaseDistance,
            this.olvColumnMTHOCell,
            this.olvColumnMTHODelay});
            this.ListViewCallInfo.ContextMenuStrip = this.ctxMenu;
            this.ListViewCallInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCallInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCallInfo.FullRowSelect = true;
            this.ListViewCallInfo.GridLines = true;
            this.ListViewCallInfo.HeaderWordWrap = true;
            this.ListViewCallInfo.IsNeedShowOverlay = false;
            this.ListViewCallInfo.Location = new System.Drawing.Point(0, 0);
            this.ListViewCallInfo.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.ListViewCallInfo.Name = "ListViewCallInfo";
            this.ListViewCallInfo.OwnerDraw = true;
            this.ListViewCallInfo.ShowGroups = false;
            this.ListViewCallInfo.Size = new System.Drawing.Size(1522, 645);
            this.ListViewCallInfo.TabIndex = 6;
            this.ListViewCallInfo.UseCompatibleStateImageBehavior = false;
            this.ListViewCallInfo.View = System.Windows.Forms.View.Details;
            this.ListViewCallInfo.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnMO
            // 
            this.olvColumnMO.HeaderFont = null;
            this.olvColumnMO.Text = "主叫";
            this.olvColumnMO.Width = 40;
            // 
            // olvColumnMT
            // 
            this.olvColumnMT.HeaderFont = null;
            this.olvColumnMT.Text = "被叫";
            this.olvColumnMT.Width = 40;
            // 
            // olvColumnMOCSFBRelease
            // 
            this.olvColumnMOCSFBRelease.HeaderFont = null;
            this.olvColumnMOCSFBRelease.Text = "主叫CSFBRelease时间";
            this.olvColumnMOCSFBRelease.Width = 130;
            // 
            // olvColumnMOEventLongitude
            // 
            this.olvColumnMOEventLongitude.HeaderFont = null;
            this.olvColumnMOEventLongitude.Text = "主叫回落事件位置的经度";
            this.olvColumnMOEventLongitude.Width = 100;
            // 
            // olvColumnMOEventLatitude
            // 
            this.olvColumnMOEventLatitude.HeaderFont = null;
            this.olvColumnMOEventLatitude.Text = "主叫回落事件位置的纬度";
            this.olvColumnMOEventLatitude.Width = 100;
            // 
            // olvColumnMOCSFBReleaseLTECell
            // 
            this.olvColumnMOCSFBReleaseLTECell.HeaderFont = null;
            this.olvColumnMOCSFBReleaseLTECell.Text = "主叫回落前LTE小区";
            this.olvColumnMOCSFBReleaseLTECell.Width = 120;
            // 
            // olvColumnMOLTEReleaseDistance
            // 
            this.olvColumnMOLTEReleaseDistance.HeaderFont = null;
            this.olvColumnMOLTEReleaseDistance.Text = "主叫LTE小区与回落位置的距离";
            this.olvColumnMOLTEReleaseDistance.Width = 100;
            // 
            // olvColumnMOSetupGSMCell
            // 
            this.olvColumnMOSetupGSMCell.HeaderFont = null;
            this.olvColumnMOSetupGSMCell.Text = "主叫Setup小区";
            this.olvColumnMOSetupGSMCell.Width = 110;
            // 
            // olvColumnMOGSMReleaseDistance
            // 
            this.olvColumnMOGSMReleaseDistance.HeaderFont = null;
            this.olvColumnMOGSMReleaseDistance.Text = "主叫GSM小区与回落位置的距离";
            this.olvColumnMOGSMReleaseDistance.Width = 100;
            // 
            // olvColumnMOHOCell
            // 
            this.olvColumnMOHOCell.HeaderFont = null;
            this.olvColumnMOHOCell.Text = "主叫第一次切换后小区";
            this.olvColumnMOHOCell.Width = 130;
            // 
            // olvColumnMOHODelay
            // 
            this.olvColumnMOHODelay.HeaderFont = null;
            this.olvColumnMOHODelay.Text = "主叫切换距离AssignmentComplete时间（毫秒）";
            this.olvColumnMOHODelay.Width = 130;
            // 
            // olvColumnMTCSFBRelease
            // 
            this.olvColumnMTCSFBRelease.HeaderFont = null;
            this.olvColumnMTCSFBRelease.Text = "被叫CSFBRelease时间";
            this.olvColumnMTCSFBRelease.Width = 130;
            // 
            // olvColumnMTEventLongitude
            // 
            this.olvColumnMTEventLongitude.HeaderFont = null;
            this.olvColumnMTEventLongitude.Text = "被叫回落事件位置的经度";
            this.olvColumnMTEventLongitude.Width = 100;
            // 
            // olvColumnMTEventLatitude
            // 
            this.olvColumnMTEventLatitude.HeaderFont = null;
            this.olvColumnMTEventLatitude.Text = "被叫回落事件位置的纬度";
            this.olvColumnMTEventLatitude.Width = 100;
            // 
            // olvColumnMTCSFBReleaseLTECell
            // 
            this.olvColumnMTCSFBReleaseLTECell.HeaderFont = null;
            this.olvColumnMTCSFBReleaseLTECell.Text = "被叫回落前LTE小区";
            this.olvColumnMTCSFBReleaseLTECell.Width = 120;
            // 
            // olvColumnMTLTEReleaseDistance
            // 
            this.olvColumnMTLTEReleaseDistance.HeaderFont = null;
            this.olvColumnMTLTEReleaseDistance.Text = "被叫LTE小区与回落位置的距离";
            this.olvColumnMTLTEReleaseDistance.Width = 100;
            // 
            // olvColumnMTSetupGSMCell
            // 
            this.olvColumnMTSetupGSMCell.HeaderFont = null;
            this.olvColumnMTSetupGSMCell.Text = "被叫Setup小区";
            this.olvColumnMTSetupGSMCell.Width = 120;
            // 
            // olvColumnMTGSMReleaseDistance
            // 
            this.olvColumnMTGSMReleaseDistance.HeaderFont = null;
            this.olvColumnMTGSMReleaseDistance.Text = "被叫GSM小区与回落位置的距离";
            this.olvColumnMTGSMReleaseDistance.Width = 100;
            // 
            // olvColumnMTHOCell
            // 
            this.olvColumnMTHOCell.HeaderFont = null;
            this.olvColumnMTHOCell.Text = "被叫第一次切换后小区";
            this.olvColumnMTHOCell.Width = 130;
            // 
            // olvColumnMTHODelay
            // 
            this.olvColumnMTHODelay.HeaderFont = null;
            this.olvColumnMTHODelay.Text = "被叫切换距离AssignmentComplete时间（毫秒）";
            this.olvColumnMTHODelay.Width = 130;
            // 
            // ZTLTECSFBSignalAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1522, 645);
            this.Controls.Add(this.ListViewCallInfo);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.Name = "ZTLTECSFBSignalAnaListForm";
            this.Text = "CSFB信令分析";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallInfo)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private BrightIdeasSoftware.OLVColumn olvColumn3;
        private BrightIdeasSoftware.OLVColumn olvColumn4;
        private BrightIdeasSoftware.OLVColumn olvColumn5;
        private BrightIdeasSoftware.OLVColumn olvColumn6;
        private BrightIdeasSoftware.OLVColumn olvColumn7;
        private BrightIdeasSoftware.OLVColumn olvColumn8;
        private BrightIdeasSoftware.OLVColumn olvColumn9;
        private BrightIdeasSoftware.OLVColumn olvColumn10;
        private BrightIdeasSoftware.OLVColumn olvColumn11;
        private BrightIdeasSoftware.OLVColumn olvColumn12;
        private BrightIdeasSoftware.OLVColumn olvColumn13;
        private BrightIdeasSoftware.OLVColumn olvColumn14;
        private BrightIdeasSoftware.OLVColumn olvColumn15;
        private BrightIdeasSoftware.OLVColumn olvColumn16;
        private BrightIdeasSoftware.OLVColumn olvColumn17;
        private BrightIdeasSoftware.OLVColumn olvColumn18;
        private BrightIdeasSoftware.OLVColumn olvColumn19;
        private BrightIdeasSoftware.OLVColumn olvColumn20;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplay;
        private BrightIdeasSoftware.TreeListView ListViewCallInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMO;
        private BrightIdeasSoftware.OLVColumn olvColumnMT;
        private BrightIdeasSoftware.OLVColumn olvColumnMOCSFBRelease;
        private BrightIdeasSoftware.OLVColumn olvColumnMOCSFBReleaseLTECell;
        private BrightIdeasSoftware.OLVColumn olvColumnMOSetupGSMCell;
        private BrightIdeasSoftware.OLVColumn olvColumnMTSetupGSMCell;
        private BrightIdeasSoftware.OLVColumn olvColumnMOHOCell;
        private BrightIdeasSoftware.OLVColumn olvColumnMTHOCell;
        private BrightIdeasSoftware.OLVColumn olvColumnMOHODelay;
        private BrightIdeasSoftware.OLVColumn olvColumnMTHODelay;
        private BrightIdeasSoftware.OLVColumn olvColumnMTCSFBRelease;
        private BrightIdeasSoftware.OLVColumn olvColumnMTCSFBReleaseLTECell;
        private BrightIdeasSoftware.OLVColumn olvColumnMOEventLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnMOEventLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnMOLTEReleaseDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMOGSMReleaseDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMTEventLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnMTEventLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnMTLTEReleaseDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMTGSMReleaseDistance;

    }
}