﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRMobileServiceAnaForm : MinCloseForm
    {
        public NRMobileServiceAnaForm() : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        private List<NRMobileServiceAnaFileItem> resultList = new List<NRMobileServiceAnaFileItem>();

        public void FillData(List<NRMobileServiceAnaFileItem> resultList)
        {
            this.resultList = new List<NRMobileServiceAnaFileItem>();
            this.resultList = resultList;

            gcDetail.DataSource = resultList;
            gcDetail.RefreshDataSource();
            MainModel.ClearDTData();
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            NRMobileServiceAnaItem item = null;
            if (gvCell.IsFocusedView)
            {
                item = gvCell.GetFocusedRow() as NRMobileServiceAnaItem;
            }
            if (item != null)
            {
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(item.RequestEvt);
            }
        }

        private void ListViewCauseValueAna_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            NRMobileServiceAnaItem item = null;
            if (gvCell.IsFocusedView)
            {
                item = gvCell.GetFocusedRow() as NRMobileServiceAnaItem;
            }
            if (item != null)
            {
                this.MainModel.ClearDTData();
                this.MainModel.DTDataManager.Add(item.RequestEvt);
                item.RequestEvt.Selected = true;
                this.MainModel.FireDTDataChanged(this);
            }
        }

        #region 导出
        private void miExportExcel_Click(object sender, EventArgs e)
        {
            exportStatListToXls();
        }

        private void exportStatListToXls()
        {
            if (!exportListToExcel())
            {
                exportStatListToTxt();
            }
        }
  
        private bool exportListToExcel()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("文件序号");
            row.cellValues.Add("文件名称");
            row.cellValues.Add("序号");
            row.cellValues.Add("业务类别");
            row.cellValues.Add("URL");
            row.cellValues.Add("结果");

            row.cellValues.Add("信令1名称");
            row.cellValues.Add("信令1时间");
            row.cellValues.Add("信令2名称");
            row.cellValues.Add("信令2时间");
            row.cellValues.Add("信令1-2时间差(毫秒)");
            row.cellValues.Add("信令3名称");
            row.cellValues.Add("信令3时间");
            row.cellValues.Add("信令2-3时间差(毫秒)");
            row.cellValues.Add("失败原因");

            row.cellValues.Add("视频业务卡顿次数");
            row.cellValues.Add("视频业务卡顿时长(毫秒)");
            row.cellValues.Add("视频业务加载速率(Mbit/s)");

            row.cellValues.Add("传输字节(Byte)");
            row.cellValues.Add("传输时间(毫秒)");
            row.cellValues.Add("平均速率(Mbit/s)");
            row.cellValues.Add("峰值速率(Mbit/s)");
            row.cellValues.Add("采样点数");
            row.cellValues.Add("平均RSRP");
            row.cellValues.Add("平均SINR");
            row.cellValues.Add("小区信息");

            rows.Add(row);
            int rowCount = 1;

            foreach (var fileItem in resultList)
            {
                foreach (var anaItem in fileItem.AnaList)
                {
                    rowCount++;
                    if (rowCount > 65535)
                    {
                        return false;
                    }
                    row = new NPOIRow();

                    row.cellValues.Add(fileItem.SN);
                    row.cellValues.Add(fileItem.FileName);
                    row.cellValues.Add(anaItem.SN);
                    row.cellValues.Add(anaItem.TypeName);
                    row.cellValues.Add(anaItem.URL);
                    row.cellValues.Add(anaItem.Result);

                    row.cellValues.Add(anaItem.SignalFirstName);
                    row.cellValues.Add(anaItem.SignalFirstTime);
                    row.cellValues.Add(anaItem.SignalSecondName);
                    row.cellValues.Add(anaItem.SignalSecondTime);
                    row.cellValues.Add(anaItem.SignalSpanFirst2Second);
                    row.cellValues.Add(anaItem.SignalLastName);
                    row.cellValues.Add(anaItem.SignalLastTime);
                    row.cellValues.Add(anaItem.SignalSpanSecond2Last);
                    row.cellValues.Add(anaItem.FailReason);

                    row.cellValues.Add(anaItem.ReBufferCount);
                    row.cellValues.Add(anaItem.ReBufferTime);
                    row.cellValues.Add(anaItem.LoadSpeed);

                    row.cellValues.Add(anaItem.AppTotalByte);
                    row.cellValues.Add(anaItem.AppTotalTime);
                    row.cellValues.Add(anaItem.AppSpeedAvg);
                    row.cellValues.Add(anaItem.AppSpeedMax);
                    row.cellValues.Add(anaItem.SampleCount);
                    row.cellValues.Add(anaItem.RsrpAvg);
                    row.cellValues.Add(anaItem.SinrAvg);
                    row.cellValues.Add(anaItem.CellInfos);

                    rows.Add(row);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
            return true;
        }

        private void exportStatListToTxt()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Text file (*.txt)|*.txt";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    WaitBox.Show("正在导出到Txt...", doExportStatListToTxt, dlg.FileName);
                    MessageBox.Show("Txt导出完成！");
                }
                catch (System.Exception e)
                {
                    MessageBox.Show("导出到Txt出错：" + e.Message);
                }
            }
        }

        private void doExportStatListToTxt(object nameObj)
        {
            string fileName = nameObj.ToString();
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeStatListContent(streamWriter);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
                WaitBox.Close();
            }
        }

        private void writeStatListContent(System.IO.StreamWriter streamWriter)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("文件序号\t");
            sb.Append("文件名称\t");
            sb.Append("序号\t");
            sb.Append("业务类别\t");
            sb.Append("结果\t");

            sb.Append("信令1名称\t");
            sb.Append("信令1时间\t");
            sb.Append("信令2名称\t");
            sb.Append("信令2时间\t");
            sb.Append("信令1-2时间差(毫秒)\t");
            sb.Append("信令3名称\t");
            sb.Append("信令3时间\t");
            sb.Append("信令2-3时间差(毫秒)\t");
            sb.Append("失败原因\t");

            sb.Append("视频业务卡顿次数\t");
            sb.Append("视频业务卡顿次数\t");
            sb.Append("视频业务加载速率(Mbit/s)\t");

            sb.Append("传输字节(Byte)\t");
            sb.Append("传输时间(毫秒)\t");
            sb.Append("平均速率(Mbit/s)\t");
            sb.Append("峰值速率(Mbit/s)\t");
            sb.Append("采样点数\t");
            sb.Append("平均RSRP\t");
            sb.Append("平均SINR\t");
            sb.Append("小区信息\t");

            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;

            foreach (var fileItem in resultList)
            {
                foreach (var anaItem in fileItem.AnaList)
                {
                    sb.Append(fileItem.SN + "\t");
                    sb.Append(fileItem.FileName + "\t");
                    sb.Append(anaItem.SN + "\t");
                    sb.Append(anaItem.TypeName + "\t");
                    sb.Append(anaItem.URL + "\t");
                    sb.Append(anaItem.Result + "\t");

                    sb.Append(anaItem.SignalFirstName + "\t");
                    sb.Append(anaItem.SignalFirstTime + "\t");
                    sb.Append(anaItem.SignalSecondName + "\t");
                    sb.Append(anaItem.SignalSecondTime + "\t");
                    sb.Append(anaItem.SignalSpanFirst2Second + "\t");
                    sb.Append(anaItem.SignalLastName + "\t");
                    sb.Append(anaItem.SignalLastTime + "\t");
                    sb.Append(anaItem.SignalSpanSecond2Last + "\t");
                    sb.Append(anaItem.FailReason + "\t");

                    sb.Append(anaItem.ReBufferCount + "\t");
                    sb.Append(anaItem.ReBufferTime + "\t");
                    sb.Append(anaItem.LoadSpeed + "\t");

                    sb.Append(anaItem.AppTotalByte + "\t");
                    sb.Append(anaItem.AppTotalTime + "\t");
                    sb.Append(anaItem.AppSpeedAvg + "\t");
                    sb.Append(anaItem.AppSpeedMax + "\t");
                    sb.Append(anaItem.SampleCount + "\t");
                    sb.Append(anaItem.RsrpAvg + "\t");
                    sb.Append(anaItem.SinrAvg + "\t");
                    sb.Append(anaItem.CellInfos + "\t");

                    streamWriter.WriteLine(sb.ToString() + "\t");
                    sb.Remove(0, sb.Length);

                    WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / resultList.Count);
                }
            }
        }
        #endregion
    }
}
