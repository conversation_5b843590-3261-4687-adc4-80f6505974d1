﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using FileInfo = MasterCom.RAMS.Model.FileInfo;

namespace MasterCom.RAMS.ZTFunc
{
    public class NROptimizationCustersCreatePic
    {


        class NRAcpCoverPic : NRStationAcceptCoverPic
        {
            public NRAcpCoverPic(string picPath)
                : base(picPath)
            {
                reSetMapView("NR_SS_RSRP", new Func(getRsrpRanges), "");
                reSetMapView("NR_SS_SINR", new Func(getSinrRanges), "");
            }

            protected List<RangeInfo> getRsrpRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
                ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
                ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
                ranges.Add(new RangeInfo(true, false, -70, -30, Color.Blue));
                return ranges;
            }

            protected List<RangeInfo> getSinrRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                ranges.Add(new RangeInfo(false, false, -20, 3, Color.Red));
                ranges.Add(new RangeInfo(true, false, 3, 10, Color.Orange));
                ranges.Add(new RangeInfo(true, false, 10, 16, Color.Yellow));
                ranges.Add(new RangeInfo(true, false, 16, 25, Color.Green));
                ranges.Add(new RangeInfo(true, false, 25, 50, Color.Blue));
                return ranges;
            }

            protected override bool isValidFile(FileInfo fileInfo)
            {
                return fileInfo.Name.Contains("DT上传") || fileInfo.Name.Contains("DT下载");
            }

            protected List<RangeInfo> getDLRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (band)
                {
                    default:
                        ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 10, 100, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 100, 400, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 400, 700, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 700, 10000, Color.Blue));
                        break;
                }
                return ranges;
            }

            protected List<RangeInfo> getULRanges(string band)
            {
                List<RangeInfo> ranges = new List<RangeInfo>();
                switch (band)
                {
                    default:
                        ranges.Add(new RangeInfo(false, false, 0, 10, Color.Red));
                        ranges.Add(new RangeInfo(true, false, 10, 25, Color.Orange));
                        ranges.Add(new RangeInfo(true, false, 25, 75, Color.Yellow));
                        ranges.Add(new RangeInfo(true, false, 75, 100, Color.Green));
                        ranges.Add(new RangeInfo(true, false, 100, 10000, Color.Blue));
                        break;
                }
                return ranges;
            }
        }
    }


}
