﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public class Indicator
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 指标KPI数据
        /// </summary>
        public double KPI { get; set; }
        /// <summary>
        /// 达标阈值
        /// </summary>
        public double StandardThreshold { get; set; }
        /// <summary>
        /// 挑战阈值
        /// </summary>
        public double ChallengeThreshold { get; set; }
        /// <summary>
        /// 得分
        /// </summary>
        public double Score { get; set; }
        public double TotalScore { get; set; }

        public Indicator()
        {
            Name = "";
            KPI = -10000;
            StandardThreshold = -10000;
            ChallengeThreshold = -10000;
            Score = 0;
            TotalScore = 0;
        }

        public Indicator(string name, double kpi, double standardThreshold, double challengeThreshold)
        {
            this.Name = name;
            this.KPI = kpi;
            this.StandardThreshold = standardThreshold;
            this.ChallengeThreshold = challengeThreshold;
            this.Score = 0;
        }

        public Indicator(string name)
        {
            this.Name = name;
            this.KPI = -10000;
            this.StandardThreshold = -10000;
            this.ChallengeThreshold = -10000;
            this.Score = 0;
        }
        public void GetScore(IndicatorThresholdInf thresholdInf)
        {
            this.ChallengeThreshold = thresholdInf.FullData;
            this.StandardThreshold = thresholdInf.ThreadHoldData;
            ComputeScore(thresholdInf);
        }
        //根据门限和挑战值确定分数
        private void ComputeScore(IndicatorThresholdInf thresholdInf)
        {
            if (this.KPI < -9999)
            {
                return;
            }
            TotalScore = thresholdInf.FullScore;
            if (thresholdInf.ThreadHoldData < thresholdInf.FullData)
            {
                setLowerScore(thresholdInf);
            }
            else
            {
                setHeigherScore(thresholdInf);
            }
        }

        private void setLowerScore(IndicatorThresholdInf thresholdInf)
        {
            if (this.KPI <= thresholdInf.ZeroData)
            {
                this.Score = 0;
            }
            else
            {
                if (this.KPI >= thresholdInf.FullData)
                {
                    this.Score = thresholdInf.FullScore;
                }
                else
                {
                    if (this.KPI <= thresholdInf.ThreadHoldData)
                    {
                        this.Score = Math.Abs(((this.KPI - thresholdInf.ZeroData) / (thresholdInf.ThreadHoldData
                            - thresholdInf.ZeroData)) * (thresholdInf.ThreadHoldScore));
                    }
                    else
                    {
                        this.Score = Math.Abs(((thresholdInf.ThreadHoldData - this.KPI) / (thresholdInf.FullData - thresholdInf.ThreadHoldData)) *
                            (thresholdInf.FullScore - thresholdInf.ThreadHoldScore)) + thresholdInf.ThreadHoldScore;
                    }
                }
            }
        }

        private void setHeigherScore(IndicatorThresholdInf thresholdInf)
        {
            if (this.KPI >= thresholdInf.ZeroData)
            {
                this.Score = 0;
            }
            else
            {
                if (this.KPI <= thresholdInf.FullData)
                {
                    this.Score = thresholdInf.FullScore;
                }
                else
                {
                    if (this.KPI >= thresholdInf.ThreadHoldData)
                    {
                        this.Score = Math.Abs(((this.KPI - thresholdInf.ZeroData) / (thresholdInf.ThreadHoldData
                            - thresholdInf.ZeroData)) * (thresholdInf.ThreadHoldScore));
                    }
                    else
                    {
                        this.Score = Math.Abs(((thresholdInf.ThreadHoldData - this.KPI) / (thresholdInf.FullData - thresholdInf.ThreadHoldData)) *
                            (thresholdInf.FullScore - thresholdInf.ThreadHoldScore)) + thresholdInf.ThreadHoldScore;
                    }
                }
            }
        }
    }
}
