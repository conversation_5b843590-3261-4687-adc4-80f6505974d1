﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class GoodRsrpPoorSinrCell
    {
        public GoodRsrpPoorSinrCell(LTECell cell)
        {
            this.Cell = cell;
            TestPoints = new List<TestPoint>();
            RsrpMin = float.MaxValue;
            RsrpMax = float.MinValue;
            SinrMin = float.MaxValue;
            SinrMax = float.MinValue;
        }

        public int Sn
        {
            get;
            set;
        }

        public LTECell Cell
        {
            get;
            private set;
        }

        public string CellName
        {
            get { return this.Cell.Name; }
        }

        public int CellID
        {
            get { return this.Cell.SCellID; }
        }

        public List<TestPoint> TestPoints
        { get; private set; }

        public float RsrpMin
        {
            get;
            private set;
        }

        public float RsrpMax
        {
            get;
            private set;
        }

        public float RsrpAvg
        {
            get;
            private set;
        }

        public float SinrMin
        {
            get;
            private set;
        }

        public float SinrMax
        {
            get;
            private set;
        }

        public float SinrAvg
        {
            get;
            private set;
        }

        public int TestPointNum
        {
            get { return TestPoints.Count; }
        }

        public int MainCellPointNum
        {
            get;
            private set;
        }

        public int NbCellPointNum
        {
            get { return TestPoints.Count - MainCellPointNum; }
        }

        public string RoadDesc
        {
            get;
            private set;
        }

        private float rsrpTotal = 0;
        private float sinrTotal = 0;
        public void AddPoint(TestPoint tp, float rsrp, float sinr, bool isMainCell)
        {
            TestPoints.Add(tp);
            if (isMainCell)
            {
                MainCellPointNum++;
            }
            rsrpTotal += rsrp;
            RsrpMin = Math.Min(RsrpMin, rsrp);
            RsrpMax = Math.Max(RsrpMax, rsrp);
            sinrTotal += sinr;
            SinrMin = Math.Min(SinrMin, sinr);
            SinrMax = Math.Max(SinrMax, sinr);
        }


        internal void MakeSummary()
        {
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(TestPoints[0].Longitude);
            lats.Add(TestPoints[0].Latitude);
            lngs.Add(TestPoints[TestPoints.Count / 2].Longitude);
            lats.Add(TestPoints[TestPoints.Count / 2].Latitude);
            lngs.Add(TestPoints[TestPoints.Count - 1].Longitude);
            lats.Add(TestPoints[TestPoints.Count - 1].Latitude);
            this.RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
            RsrpAvg = (float)Math.Round(rsrpTotal / TestPointNum, 2);
            SinrAvg = (float)Math.Round(sinrTotal / TestPointNum, 2);
        }
    }
}
