﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Message = MasterCom.RAMS.Model.Message;
using MasterCom.RAMS.ZTFunc.ZTVoNRStatDelayAna;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoNRStatDelayAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> moCallAttemptEventIds = new List<int> { 
            (int)NREventManager.VoLTE_Audio_MO_Call_Attempt, (int)NREventManager.VoLTE_Video_MO_Call_Attempt, 
            (int)NREventManager.EPSFB_Audio_MO_Call_Attempt, (int)NREventManager.EPSFB_Video_MO_Call_Attempt,
            (int)NREventManager.VoNR_Audio_MO_Call_Attempt, (int)NREventManager.VoNR_Video_MO_Call_Attempt };
        readonly List<int> mtCallAttemptEventIds = new List<int> {
            (int)NREventManager.VoLTE_Audio_MT_Call_Attempt, (int)NREventManager.VoLTE_Video_MT_Call_Attempt,
            (int)NREventManager.EPSFB_Audio_MT_Call_Attempt, (int)NREventManager.EPSFB_Video_MT_Call_Attempt,
            (int)NREventManager.VoNR_Audio_MT_Call_Attempt, (int)NREventManager.VoNR_Video_MT_Call_Attempt };

        protected static readonly object lockObj = new object();
        private static VoNRStatDelayAnaByRegion instance = null;
        private List<VoNRItemsInfo> listVoNrItemsInfo = null;
        public static VoNRStatDelayAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoNRStatDelayAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected VoNRStatDelayAnaByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
            ServiceTypes.Add(ServiceType.SER_NR_DM_TDD_EPSFB);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VONR);
        }


        public override string Name
        {
            get
            {
                return "VoNR时延分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27004, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 180;
        StringBuilder strbErr = null;

        protected override bool getCondition()
        {
            CallConditionDlg dlg = new CallConditionDlg();
            dlg.SetCondition(checkDelay, maxDelaySec);
            strbErr = new StringBuilder();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out checkDelay, out maxDelaySec);
            callStatList = new List<VoNRPairsInfo>();
            listVoNrItemsInfo = new List<VoNRItemsInfo>();
            return true;
        }

        protected override void fireShowForm()
        {
            if (strbErr.Length > 0 )
            {
                XtraMessageBox.Show(strbErr.ToString());
            }

            if (listVoNrItemsInfo == null || listVoNrItemsInfo.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }

            VoNRStatDelayInfoForm frm = MainModel.CreateResultForm(typeof(VoNRStatDelayInfoForm)) as VoNRStatDelayInfoForm;
            frm.FillData(listVoNrItemsInfo);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
            listVoNrItemsInfo = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<FileInfo, bool> fileAdded = new Dictionary<FileInfo, bool>();
            relateMoMtFile(moMtPair, fileAdded);

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                    && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == (int)MoMtFile.MtFlag && !fileAdded.ContainsKey(fileInfo))
                {
                    moMtPair[fileInfo] = GetPairFile(fileInfo);
                }
            }

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }

                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private void relateMoMtFile(Dictionary<FileInfo, FileInfo> moMtPair, Dictionary<FileInfo, bool> fileAdded)
        {
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == (int)MoMtFile.MoFlag)
                {//主叫关联被叫
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    if (mtFile == null)//选中的文件中关联不到主被叫文件，再在服务段关联查询
                    {
                        mtFile = GetPairFile(fileInfo);
                    }

                    moMtPair[fileInfo] = mtFile;
                    if (mtFile != null)
                    {
                        fileAdded[mtFile] = true;
                    }
                }
            }
        }

        private FileInfo GetPairFile(FileInfo file)
        {
            FileInfo filePair = null;
            DIYQueryPeerFileInfo qryFileInfo = new DIYQueryPeerFileInfo(MainModel, file.LogTable, file.ID, file.Name,false);
            qryFileInfo.Query();

            if (qryFileInfo.PeerFileInfoDic != null)
            {
                foreach (FileInfo value in qryFileInfo.PeerFileInfoDic.Values)
                {
                    if (value != null)
                    {
                        return value;
                    }
                }
            }
            else
            {
                if (strbErr.Length == 0)
                {
                    strbErr.AppendLine("以下文件未能找到对应的主被叫文件：");
                }
                strbErr.AppendLine(file.Name);
            }

            return filePair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else
                {
                    mtFile = file;
                }
            }

            int lastMoMsgIdx = 0;
            int lastMtEventIdx = 0;
            int lastMtMsgIdx = 0;
            if (moFile != null)
            {
                try
                {
                    dealMoFile(moFile, mtFile, lastMoMsgIdx, lastMtEventIdx, lastMtMsgIdx);
                }
                catch (Exception e)
                {
                    MessageBox.Show("文件：" + moFile.FileName + Environment.NewLine + e.Message + Environment.NewLine + e.StackTrace);
                }
            }
            else if (mtFile != null)
            {
                try
                {
                    dealMtFile(mtFile, lastMtMsgIdx);
                }
                catch (Exception e)
                {
                    MessageBox.Show("文件：" + mtFile.FileName + Environment.NewLine + e.Message + Environment.NewLine + e.StackTrace);
                }
            }
        }

        private void dealMoFile(DTFileDataManager moFile, DTFileDataManager mtFile, int lastMoMsgIdx, int lastMtEventIdx, int lastMtMsgIdx)
        {
            VoNRCallInfo singleCall = null;
            for (int i = 0; i < moFile.Events.Count; i++)
            {
                Event evt = moFile.Events[i];
                if (moCallAttemptEventIds.Contains(evt.ID) || mtCallAttemptEventIds.Contains(evt.ID))
                {
                    singleCall = new VoNRCallInfo(moFile.FileName, moFile.MoMtFlag);
                    singleCall.AddEvent(evt);
                }
                else if (singleCall != null)
                {
                    //保存singlecall的事件
                    bool isAdded = singleCall.AddEvent(evt);
                    if (isAdded)
                    {
                        addTpMsgInfo2Call(singleCall, moFile, ref lastMoMsgIdx);
                        VoNRCallInfo mtCall = getMtCallInfo(singleCall, mtFile, ref lastMtEventIdx, ref lastMtMsgIdx);
                        saveCallInfo(singleCall, mtCall);
                        singleCall = null;
                    }
                }
            }
        }

        private void dealMtFile(DTFileDataManager mtFile, int lastMtMsgIdx)
        {
            VoNRCallInfo singleCall = null;
            for (int i = 0; i < mtFile.Events.Count; i++)
            {
                Event evt = mtFile.Events[i];
                if (mtCallAttemptEventIds.Contains(evt.ID))
                {
                    singleCall = new VoNRCallInfo(mtFile.FileName, mtFile.MoMtFlag);
                    singleCall.AddEvent(evt);
                }
                else if (singleCall != null)
                {
                    //保存singlecall的事件
                    bool isAdded = singleCall.AddEvent(evt);
                    if (isAdded)
                    {
                        addTpMsgInfo2Call(singleCall, mtFile, ref lastMtMsgIdx);
                        saveCallInfo(null, singleCall);
                        singleCall = null;
                    }
                }
            }
        }

        private List<VoNRPairsInfo> callStatList = null;
        private void saveCallInfo(VoNRCallInfo moCall, VoNRCallInfo mtCall)
        {
            VoNRPairsInfo info = new VoNRPairsInfo(moCall, mtCall);

            if (moCall != null)
            {
                if (moCall.MsgRinging180 != null)
                {
                    moCall.PhoneNum = getPhoneNum(moCall.MsgRinging180, "sip.from.user");
                    if (mtCall != null)
                    {
                        mtCall.PhoneNum = getPhoneNum(moCall.MsgRinging180, "sip.to.host");
                    }
                }
                else if (mtCall != null && mtCall.MsgRinging180 != null)
                {
                    mtCall.PhoneNum = getPhoneNum(mtCall.MsgRinging180, "sip.to.user");
                    moCall.PhoneNum = getPhoneNum(mtCall.MsgRinging180, "sip.from.host");
                }
            }
            else if (mtCall != null && mtCall.MsgRinging180 != null)
            {
                mtCall.PhoneNum = getPhoneNum(mtCall.MsgRinging180, "sip.to.host");
            }

            callStatList.Add(info);
        }
        private string getPhoneNum(Message msg, string strFieldName)
        {
            string phoneNumber = MessageDecodeHelper.GetMsgSingleString(msg, strFieldName);
            return phoneNumber.Replace("\0", "").Trim();
        }

        public static uint[] getLtePagingTmsiList(Message msg)
        {
            return MessageDecodeHelper.GetMsgMultiUInt(msg, "lte-rrc.pagingRecordList", "lte-rrc.m_TMSI_Uint");
        }

        public static ulong[] getNrPagingTmsiList(Message msg)
        {
            return MessageDecodeHelper.GetMsgMultiULong(msg, "nr-rrc.pagingRecordList", "nr-rrc.ng_5G_S_TMSI_UINT64");
        }

        private void addTpMsgInfo2Call(VoNRCallInfo call, DTFileDataManager file, ref int lastMsgIdx)
        {
            if(file.Messages.Count<=0)
            {
                return;
            }

            bool began = false;
            Event beginEvent = call.ListEvents[0];
            Event endEvent = call.ListEvents[call.ListEvents.Count - 1];
            int beginIdx = lastMsgIdx;
            for (; beginIdx >= 0; beginIdx++)
            {
                Message msg = file.Messages[beginIdx];
                if ((beginEvent.DateTime - msg.DateTime).TotalSeconds <= 10)
                {//取call attempt前10秒内的信令
                    break;
                }
            }

            if (beginIdx < 0)
            {
                beginIdx = 0;
            }

            for (int i = beginIdx; i < file.Messages.Count; i++)
            {
                Message msg = file.Messages[i];
                if (msg.SN > endEvent.SN)
                {
                    lastMsgIdx = i;
                    break;
                }

                if (msg.DateTime >= beginEvent.DateTime)
                {
                    began = true;
                }
                call.ListMessage.Add(msg);


                /* 5G S-TMSI长度为48bit
                 * 5G S-TMSI由两部分组成 = [ng_5G_S_TMSI_Part2](39bit) + [ng_5G_S_TMSI_Part1](9bit)
                 * 从[RRC Setup Request]信令中获取[ng_5G_S_TMSI_Part1]
                 * 从[RRC Setup Complete]信令中获取[ng_5G_S_TMSI_Part2]
                 */
                if (msg.ID == (int)MessageManager.NR_RRC_RRCSetupComplete && msg.MoMtFlag == (int)MoMtFile.MtFlag && !began)
                {
                    call.MsgRRCSetupComplete = msg;
                    if (call.MsgRRCSetupRequest == null)
                    {
                        continue;
                    }

                    ulong tmsi1 = MessageDecodeHelper.GetMsgSingleULong(call.MsgRRCSetupRequest, "nr-rrc.ng_5G_S_TMSI_Part1_UINT64");
                    ulong tmsi2 = MessageDecodeHelper.GetMsgSingleUInt(call.MsgRRCSetupComplete, "nr-rrc.ng_5G_S_TMSI_Part2_UINT");

                    if (tmsi1 == ulong.MaxValue || tmsi2 == uint.MaxValue)
                    {
                        continue;
                    }


                    ulong rrcTmsi = (tmsi2 << 39) | tmsi1;

                    for (int j = i - 1; j >= 0; j--)
                    {
                        Message msg1 = file.Messages[j];

                        if (msg1.ID == (int)MessageManager.NR_RRC_Paging)  //VoNR MT
                        {
                            ulong[] tmsiList = getNrPagingTmsiList(msg1);
                            foreach (ulong tmsi in tmsiList)
                            {
                                if (tmsi == rrcTmsi)
                                {
                                    call.MsgPaging = msg1;
                                    break;
                                }
                            }
                        }

                        if (call.MsgPaging != null || (msg.DateTime - msg1.DateTime).TotalSeconds > 10)
                        {
                            break;
                        }
                    }

                    continue;
                }
                else if (msg.ID == (int)MessageManager.NR_RRC_RRCSetupRequest && msg.MoMtFlag == (int)MoMtFile.MtFlag && !began)  // VoNR MT
                {
                    call.MsgRRCSetupRequest = msg;
                    
                    continue;
                }
                else if (msg.ID == (int)MessageManager.LTE_RRC_RRC_Connection_Request && msg.MoMtFlag == (int)MoMtFile.MtFlag && !began)  // VoLTE MT
                {
                    call.MsgRRCConnectionRequest = msg;
                    uint rrcTmsi = MessageDecodeHelper.GetMsgSingleUInt(msg, "lte-rrc.m_TMSI_Uint");

                    for (int j = i - 1; j >= 0; j--)
                    {
                        Message msg1 = file.Messages[j];

                        if (msg1.ID == (int)MessageManager.LTE_RRC_Paging)
                        {
                            uint[] tmsiList = getLtePagingTmsiList(msg1);

                            foreach (uint tmsi in tmsiList)
                            {
                                if (tmsi == rrcTmsi)
                                {
                                    call.MsgPaging = msg1;
                                    break;
                                }
                            }
                        }

                        if (call.MsgPaging != null || (msg.DateTime - msg1.DateTime).TotalSeconds > 10)
                        {
                            break;
                        }
                    }

                    continue;
                }
                else if (msg.ID == (int)MessageManager.Msg_IMS_SIP_INVITE && call.MsgIMS_SIP_INVITE == null)
                {
                    began = true;
                    call.MsgIMS_SIP_INVITE = msg;

                    continue;
                }
                else if (msg.ID == (int)MessageManager.LTE_NAS_Extended_service_request)
                {
                    began = true;
                    call.MsgExtendedSR = msg;

                    continue;
                }

                if (began)
                {
                    switch (msg.ID)
                    {
                        //case 1107689472:
                        //    if (call.MsgIMS_SIP_INVITE == null)
                        //    {
                        //        call.MsgIMS_SIP_INVITE = msg;
                        //    }
                        //    break;

                        
                        case 773:
                            //case 1899627269:
                            if (call.MsgSetup == null)
                            {
                                call.MsgSetup = msg;
                            }
                            break;
                        case 783:
                            if (call.MsgConnectAcknowledge == null)
                            {
                                call.MsgConnectAcknowledge = msg;
                            }
                            break;
                        case (int)MessageManager.Msg_IMS_SIP_ACK:
                            if (call.MsgIMS_SIP_ACK == null)
                            {
                                call.MsgIMS_SIP_ACK = msg;
                            }
                            break;
                        case (int)MessageManager.LTE_RRC_RRC_Connection_Reconfiguration_Complete:
                            if (call.MsgRRCCRComplete == null)
                            {
                                call.MsgRRCCRComplete = msg;
                            }
                            break;
                        case (int)MessageManager.NR_RRC_RRCReconfigurationComplete:
                            if (call.MsgRRCCRComplete == null)
                            {
                                call.MsgRRCCRComplete = msg;
                            }
                            break;
                        case (int)MessageManager.Msg_IMS_SIP_INVITE_Session_Progress:
                            if (call.MsgSessionProgress183 == null)
                            {
                                call.MsgSessionProgress183 = msg;
                            }
                            break;
                        case (int)MessageManager.LTE_NAS_Activate_dedicated_EPS_bearer_context_request:
                            if (call.MsgEPSBearerCR == null)
                            {
                                call.MsgEPSBearerCR = msg;
                            }
                            break;
                        case (int)MessageManager.NR_NAS_DL_NAS_transport:
                            if (call.MsgEPSBearerCR == null)
                            {
                                call.MsgEPSBearerCR = msg;
                            }
                            break;
                        case (int)MessageManager.Msg_IMS_SIP_INVITE_Ringing:
                            if (call.MsgRinging180 == null)
                            {
                                call.MsgRinging180 = msg;
                            }
                            break;
                        case (int)MessageManager.LTE_NAS_Extended_service_request:
                            if (call.MsgExtendedSR == null)
                            {
                                call.MsgExtendedSR = msg;
                            }
                            break;
                        case 1575:
                            if (call.MsgPagingResponse == null)
                            {
                                call.MsgPagingResponse = msg;
                            }
                            break;
                        case 1316:
                        case 1899627812:
                            if (call.MsgCMSR == null)
                            {
                                call.MsgCMSR = msg;
                            }
                            break;
                        case 769:
                        case 1899627265:
                            if (call.MsgAlerting == null)
                            {
                                call.MsgAlerting = msg;
                            }
                            break;
                        default:
                            break;
                    }
                }
            }

            int beginTpIdx = getChangeTestpointIndex(file.TestPoints, file.Messages[beginIdx]);
            int endTpIdx = getChangeTestpointIndex(file.TestPoints, file.Messages[lastMsgIdx]);

            beginTpIdx = beginTpIdx == -1 ? 0 : beginTpIdx;
            endTpIdx = endTpIdx == -1 ? 0 : endTpIdx;
            if (beginTpIdx <= endTpIdx)
            {
                for (int i = beginTpIdx; i <= endTpIdx; i++)
                {
                    call.ListPoints.Add(file.TestPoints[i]);
                }
            }
        }

        private static int getChangeTestpointIndex(List<TestPoint> tpList, Message msg)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > msg.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == msg.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        private VoNRCallInfo getMtCallInfo(VoNRCallInfo moCall, DTFileDataManager mtFile, ref int lastMtEentIdx, ref int lastMtMsgIdx)
        {
            if (mtFile == null)
            {
                return null;
            }

            VoNRCallInfo mtCall = null;
            if (moCall != null && moCall.CallAttemptResult == "Fail")
            {
                return null;
            }
            if (lastMtEentIdx < 0)
            {
                lastMtEentIdx = 0;
            }

            for (int i = lastMtEentIdx; i < mtFile.Events.Count; i++)
            {
                Event evt = mtFile.Events[i];
                bool isGet = judgeIsGetCallInfo(moCall, mtFile, ref lastMtEentIdx, ref lastMtMsgIdx, ref mtCall, i, evt);
                if (isGet)
                {
                    break;
                }

                if (moCall != null
                    && checkDelay
                    && (evt.DateTime - moCall.ListEvents[moCall.ListEvents.Count - 1].DateTime).TotalSeconds > maxDelaySec)
                {
                    if (mtCall != null)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtMsgIdx);
                    }
                    break;
                }
            }

            return mtCall;
        }

        private bool judgeIsGetCallInfo(VoNRCallInfo moCall, DTFileDataManager mtFile, ref int lastMtEentIdx, ref int lastMtMsgIdx, ref VoNRCallInfo mtCall, int i, Event evt)
        {
            if (moCall == null || evt.DateTime >= moCall.EvtCallAttempt.DateTime)
            {
                //mt call attempt事件只会在mo call attempt 后发生
                if (mtCallAttemptEventIds.Contains(evt.ID))
                {
                    if (mtCall != null)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtMsgIdx);
                        return true;
                    }
                    mtCall = new VoNRCallInfo(mtFile.FileName, mtFile.MoMtFlag);
                    mtCall.AddEvent(evt);
                }
                else if (mtCall != null)
                {
                    bool isAdded = mtCall.AddEvent(evt);
                    if (isAdded)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtMsgIdx);
                        return true;
                    }
                }
            }

            return false;
        }

        protected override void getResultsAfterQuery()  //以网格单位统计信息
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                return;
            }

            callStatList.Sort(comparer);
            string lastGridName = "";
            VoNRItemsInfo voItem = new VoNRItemsInfo();

            foreach (VoNRPairsInfo volInfo in callStatList)
            {
                if (volInfo.GridName != lastGridName)
                {
                    if (voItem.ListVoNrPairsInfo.Count != 0)
                    {
                        voItem.SN = listVoNrItemsInfo.Count + 1;
                        listVoNrItemsInfo.Add(voItem);
                    }
                    voItem = new VoNRItemsInfo();
                }
                volInfo.Sn = voItem.ListVoNrPairsInfo.Count + 1;
                voItem.ListVoNrPairsInfo.Add(volInfo);
                lastGridName = volInfo.GridName;
            }

            if (voItem.ListVoNrPairsInfo.Count != 0)
            {
                voItem.SN = listVoNrItemsInfo.Count + 1;
                listVoNrItemsInfo.Add(voItem);
            }
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<VoNRPairsInfo>
        {
            public int Compare(VoNRPairsInfo x, VoNRPairsInfo y)
            {
                int r = x.GridName.CompareTo(y.GridName);
                if (r != 0)
                {
                    return r;
                }
                else
                {
                    if (x.MoCall != null && y.MoCall != null && x.MoCall.ListEvents.Count > 0 && y.MoCall.ListEvents.Count > 0)
                    {
                        return x.MoCall.ListEvents[0].DateTime.CompareTo(y.MoCall.ListEvents[0].DateTime);
                    }
                    else if (x.MtCall != null && y.MtCall != null && x.MtCall.ListEvents.Count > 0 && y.MtCall.ListEvents.Count > 0)
                    {
                        return x.MtCall.ListEvents[0].DateTime.CompareTo(y.MtCall.ListEvents[0].DateTime);
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
        }
    }


    public class VoNRStatDelayAnaByRegion_FDD : VoNRStatDelayAnaByRegion
    {
        private static VoNRStatDelayAnaByRegion_FDD instance = null;
        public static new VoNRStatDelayAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoNRStatDelayAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        public VoNRStatDelayAnaByRegion_FDD()
            : base()
        {
            ServiceTypes.Clear();
        }
        public override string Name
        {
            get
            {
                return "VoNR时延分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30020, this.Name);
        }
    }
}
