<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">贵州单站验收VoLTE指标</Item>
          <Item typeName="Int32" key="KeyCount">1</Item>
          <Item typeName="Int32" key="TimeShowType">-1</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">无线接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]+evtIdCount[1069]+value9[1069] +evtIdCount[1369]+value9[1369])-(evtIdCount[1007]+value9[1007]+evtIdCount[1027]+value9[1027]+evtIdCount[1047]+value9[1047]+evtIdCount[1057]+value9[1057]+evtIdCount[1079]+value9[1079]+evtIdCount[1089]+value9[1089] +evtIdCount[1379]+value9[1379]+evtIdCount[1389]+value9[1389]))/(evtIdCount[1000]+value9[1000]+evtIdCount[1020]+value9[1020]+evtIdCount[1040]+value9[1040]+evtIdCount[1069]+value9[1069] +evtIdCount[1369]+value9[1369]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">无线掉线率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[1005]+value9[1005]+evtIdCount[1006]+value9[1006]+evtIdCount[1015]+value9[1015]+evtIdCount[1016]+value9[1016]+evtIdCount[1025]+value9[1025]+evtIdCount[1026]+value9[1026]+evtIdCount[1035]+value9[1035]+evtIdCount[1036]+value9[1036]+evtIdCount[1045]+value9[1045]+evtIdCount[1046]+value9[1046]+evtIdCount[1055]+value9[1055]+evtIdCount[1056]+value9[1056]+evtIdCount[1077]+value9[1077]+evtIdCount[1078]+value9[1078]+evtIdCount[1085]+value9[1085]+evtIdCount[1086]+value9[1086]+evtIdCount[1377]+value9[1377]+evtIdCount[1378]+value9[1378]+evtIdCount[1385]+value9[1385]+evtIdCount[1386]+value9[1386])/((evtIdCount[1000]+value9[1000]+evtIdCount[1001]+value9[1001]+evtIdCount[1020]+value9[1020]+evtIdCount[1021]+value9[1021]+evtIdCount[1040]+value9[1040]+evtIdCount[1041]+value9[1041]+evtIdCount[1069]+value9[1069]+evtIdCount[1070]+value9[1070]+evtIdCount[1369]+value9[1369]+evtIdCount[1370]+value9[1370])-(evtIdCount[1007]+value9[1007]+evtIdCount[1008]+value9[1008]+evtIdCount[1027]+value9[1027]+evtIdCount[1028]+value9[1028]+evtIdCount[1047]+value9[1047]+evtIdCount[1048]+value9[1048]+evtIdCount[1057]+value9[1057]+evtIdCount[1058]+value9[1058]+evtIdCount[1079]+value9[1079]+evtIdCount[1080]+value9[1080]+evtIdCount[1089]+value9[1089]+evtIdCount[1090]+value9[1090]+evtIdCount[1379]+value9[1379]+evtIdCount[1380]+value9[1380]+evtIdCount[1389]+value9[1389]+evtIdCount[1390]+value9[1390]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通时延(ms)</Item>
              <Item typeName="String" key="Exp">{value1[1071]/(evtIdCount[1071])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">视频功能验证成功率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[1369]+value9[1369])-(evtIdCount[1379]+value9[1379]+evtIdCount[1389]+value9[1389]))/(evtIdCount[1369]+value9[1369])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">视频/语音切换成功率</Item>
              <Item typeName="String" key="Exp">{evtIdCount[850]*100/evtIdCount[849]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RSRP</Item>
              <Item typeName="String" key="Exp">{Lte_61210309}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RS-SINR</Item>
              <Item typeName="String" key="Exp">{Lte_61210403}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均MOS分</Item>
              <Item typeName="String" key="Exp">{(Lte_6D2109*Lte_6D2108 +Lte_61216209*Lte_61216208)/(Lte_6D2108+Lte_61216208) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>