﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class CellSetByDateForm : MinCloseForm
    {
        public CellSetByDateForm()
            : base()
        {
            InitializeComponent();
            listView.ListViewItemSorter = new ListViewSorter(listView);
        }

        protected override string ShowImage
        {
            get
            {
                return "images\\dbmng.gif";
            }
        }

        public void ShowCellSet(List<CellGSMResult> CellSetOfDateResult)
        {
            try
            {
                showCells(CellSetOfDateResult);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.StackTrace);
            }
        }

        private void showCells(List<CellGSMResult> CellSetOfDateResult)
        {
            showCellSet(CellSetOfDateResult);
        }

        private void showCellSet(List<CellGSMResult> CellSetOfDateResult)
        {
            if (CellSetOfDateResult == null)
            {
                return;
            }
            listView.Items.Clear();

            addResultData(CellSetOfDateResult);
        }

        /// <summary>
        /// 添加结论数据到listview
        /// </summary>
        private void addResultData(List<CellGSMResult> CellSetOfDateResult)
        {
            foreach (CellGSMResult csRetData in CellSetOfDateResult)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Tag = csRetData.Cell;
                listViewItem.Text = csRetData.SN;
                listViewItem.SubItems.Add(csRetData.CellName);
                listViewItem.SubItems.Add(csRetData.CellType);
                listViewItem.SubItems.Add(csRetData.LAC);
                listViewItem.SubItems.Add(csRetData.CI);
                listViewItem.SubItems.Add(csRetData.StandardTestPointCount.ToString());//底层采样点总数：一个文件总数
                listViewItem.SubItems.Add(csRetData.StandardTestPointTotalCount.ToString());//底层占用采样点数：占用该小区的采样点数量
                listViewItem.SubItems.Add(csRetData.StandardTestPointRatio);//底层占用采样点占比
                listViewItem.SubItems.Add(csRetData.StandardDate);//底层时段
                listViewItem.SubItems.Add(csRetData.CompareDate);//对比时段
                listViewItem.SubItems.Add(csRetData.CompareTestPointCount.ToString());//对比时段占用采样点数
                listViewItem.SubItems.Add(csRetData.CompareTestPointTotalCount.ToString());//对比时段采样点总数
                listViewItem.SubItems.Add(csRetData.IsExist);//是否在该小区集
                listViewItem.SubItems.Add(csRetData.TestPointCount.ToString());//占用采样点数
                listViewItem.SubItems.Add(csRetData.TestPointTotalCount.ToString());//采样点总数
                listViewItem.SubItems.Add(csRetData.TestPointRatio);//采样点占比
                listViewItem.SubItems.Add(csRetData.ChangeRate);//变化趋势

                listView.Items.Add(listViewItem);
            }
        }      

        /// <summary>
        /// 最终结论显示用的数据类
        /// </summary>
        public class CellGSMResult
        {
            public object Cell { get; set; }
            public string SN { get; set; }
            public string CellName { get; set; }
            public string CellType { get; set; }
            public string LAC { get; set; }
            public string CI { get; set; }
            public int StandardTestPointCount { get; set; }
            public long StandardTestPointTotalCount { get; set; }
            public string StandardTestPointRatio { get; set; }
            public string StandardDate { get; set; }
            public string CompareDate { get; set; }
            public int CompareTestPointCount { get; set; }
            public long CompareTestPointTotalCount { get; set; }
            public string CompareTestPointRatio { get; set; }
            public string IsExist { get; set; }
            public int TestPointCount { get; set; }
            public long TestPointTotalCount { get; set; }
            public string TestPointRatio { get; set; }
            public string ChangeRate { get; set; }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(listView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

    }
}
