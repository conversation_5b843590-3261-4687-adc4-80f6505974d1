﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Message = MasterCom.RAMS.Model.Message;
using MasterCom.RAMS.ZTFunc.ZTVolteStatDelayAna;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;

namespace MasterCom.RAMS.ZTFunc
{
    class VolteStatDelayAnaByFile_Divided : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private List<ResultRow> listRow = null;
        private VolteStatDelayInfoForm_Divided resultForm = null;
        private static VolteStatDelayAnaByFile_Divided instance = null;
        public static VolteStatDelayAnaByFile_Divided GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteStatDelayAnaByFile_Divided();
                    }
                }
            }
            return instance;
        }
        protected VolteStatDelayAnaByFile_Divided()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get
            {
                return "VoLTE时延分析（分段统计）";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27010, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
        protected override bool getCondition()
        {
            return true;
        }
        protected override void getReadyBeforeQuery()
        {
            this.listRow = new List<ResultRow>();
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    Status status = Status.SIP_NONE;
                    ResultRow row = null;
                    for (int i = 0; i < file.Messages.Count; i++)
                    {
                        Message mesg = file.Messages[i];
                        if (this.dealWithEvent(ref status, mesg, ref row))
                        {
                            row.fileName = file.FileName;
                            this.listRow.Add(row);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }
        protected override void fireShowForm()
        {
            if (this.resultForm == null || this.resultForm.IsDisposed)
            {
                this.resultForm = new VolteStatDelayInfoForm_Divided();
            }
            this.resultForm.FillData(this.listRow);
            this.resultForm.Show();
            this.resultForm.Focus();
        }

        private DateTime getMsgTime(Message mesg, int status, DateTime defaultTime)
        {
            if (mesg.ID == status)
            { 
                return mesg.DateTime;
            }
            return defaultTime;
        }

        /// <summary>
        /// 根据状态和消息ID获取对应消息的时间。
        /// </summary>
        /// <param name="status"></param>
        /// <param name="mesg"></param>
        /// <param name="row"></param>
        /// <returns>true: 完成一个信息周期，可以保存信息。</returns>
        private bool dealWithEvent(ref Status status, Message mesg, ref ResultRow row)
        {
            switch (status)
            {
                case Status.SIP_NONE:
                    if (mesg.ID == (int)Status.SIP_INVITE)
                    {
                        row = new ResultRow();
                        row.time_invite = getMsgTime(mesg, (int)Status.SIP_INVITE, row.time_invite);
                        status = Status.SIP_INVITE;
                    }
                    break;
                case Status.SIP_INVITE:
                    row.time_100 = getMsgTime(mesg, (int)Status.SIP_100, row.time_100);
                    status = Status.SIP_100;
                    break;
                case Status.SIP_100:
                    row.time_183 = getMsgTime(mesg, (int)Status.SIP_183, row.time_183);
                    status = Status.SIP_183;
                    break;
                case Status.SIP_183:
                    row.time_prack = getMsgTime(mesg, (int)Status.SIP_PRACK, row.time_prack);
                    status = Status.SIP_PRACK;
                    break;
                case Status.SIP_PRACK:
                    row.time_prack200 = getMsgTime(mesg, (int)Status.SIP_PRACK_200, row.time_prack200);
                    status = Status.SIP_PRACK_200;
                    break;
                case Status.SIP_PRACK_200:
                    row.time_update = getMsgTime(mesg, (int)Status.SIP_UPDATE, row.time_update);
                    status = Status.SIP_UPDATE;
                    break;
                case Status.SIP_UPDATE:
                    row.time_update200 = getMsgTime(mesg, (int)Status.SIP_UPDATE_200, row.time_update200);
                    status = Status.SIP_UPDATE_200;
                    break;
                case Status.SIP_UPDATE_200:
                    row.time_180 = getMsgTime(mesg, (int)Status.SIP_180, row.time_180);
                    status = Status.SIP_180;
                    return true;
            }
            return false;
        }
    }

    class VolteStatDelayAnaByFile_Divided_FDD : VolteStatDelayAnaByFile_Divided
    {
        private static VolteStatDelayAnaByFile_Divided_FDD instance = null;
        public static new VolteStatDelayAnaByFile_Divided_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteStatDelayAnaByFile_Divided_FDD();
                    }
                }
            }
            return instance;
        }
        protected VolteStatDelayAnaByFile_Divided_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD时延分析（分段统计）";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30021, this.Name);
        }
    }

    enum Status
    {
        SIP_NONE = 0,               //无效
        SIP_INVITE = MessageManager.Msg_IMS_SIP_INVITE,   //0x42060000	Request	IMS_SIP_INVITE
        SIP_100 = 0x42064064,        //0x42064064 Status	IMS_SIP_INVITE->Trying
        SIP_183 = 0x420640b7,                //0x420640b7	Status	IMS_SIP_INVITE->SessionProgress183
        SIP_PRACK = 0x420A0000,              //0x420A0000	Request	IMS_SIP_PRACK
        SIP_PRACK_200 = 0x420A40c8,            //0x420640C8	Status	IMS_SIP_PRACK->OK
        SIP_UPDATE = 0x42100000,               //0x42100000	Request	IMS_SIP_UPDATE
        SIP_UPDATE_200 = 0x421040c8,          //0x420640C8	Status	IMS_SIP_UPDATE->OK
        SIP_180 = 0x420640B4,                //0x420640B4	Status	IMS_SIP_INVITE->Ringing
    }
}
