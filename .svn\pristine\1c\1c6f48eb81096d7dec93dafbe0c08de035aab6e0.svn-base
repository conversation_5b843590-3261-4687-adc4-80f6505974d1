﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTGSMScanHighCoverageRoadListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTGSMScanHighCoverageRoadListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.tabRoad = new System.Windows.Forms.TabControl();
            this.tabPage900 = new System.Windows.Forms.TabPage();
            this.LvRoad900 = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCH900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlev900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistance900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxlev900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxlev900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlev900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstTime900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTime900 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.tabPage1800 = new System.Windows.Forms.TabPage();
            this.LvRoad1800 = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCH1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlev1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistance1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxlev1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxlev1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlev1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstTime1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTime1800 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.tabPageAll = new System.Windows.Forms.TabPage();
            this.LvRoadAll = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSNAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadNameAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNameAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLACAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCIAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCHAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSICAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellDistanceAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxlevAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxlevAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlevAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMidAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMidAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileNameAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstTimeAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTimeAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            this.tabRoad.SuspendLayout();
            this.tabPage900.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.LvRoad900)).BeginInit();
            this.tabPage1800.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.LvRoad1800)).BeginInit();
            this.tabPageAll.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.LvRoadAll)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // tabRoad
            // 
            this.tabRoad.Controls.Add(this.tabPage900);
            this.tabRoad.Controls.Add(this.tabPage1800);
            this.tabRoad.Controls.Add(this.tabPageAll);
            this.tabRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabRoad.Location = new System.Drawing.Point(0, 0);
            this.tabRoad.Name = "tabRoad";
            this.tabRoad.SelectedIndex = 0;
            this.tabRoad.Size = new System.Drawing.Size(1097, 456);
            this.tabRoad.TabIndex = 6;
            this.tabRoad.SelectedIndexChanged += new System.EventHandler(this.tabRoad_SelectedIndexChanged);
            // 
            // tabPage900
            // 
            this.tabPage900.Controls.Add(this.LvRoad900);
            this.tabPage900.Location = new System.Drawing.Point(4, 23);
            this.tabPage900.Name = "tabPage900";
            this.tabPage900.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage900.Size = new System.Drawing.Size(1089, 429);
            this.tabPage900.TabIndex = 0;
            this.tabPage900.Text = "GSM900";
            this.tabPage900.UseVisualStyleBackColor = true;
            // 
            // LvRoad900
            // 
            this.LvRoad900.AllColumns.Add(this.olvColumnSN900);
            this.LvRoad900.AllColumns.Add(this.olvColumnRoadName900);
            this.LvRoad900.AllColumns.Add(this.olvColumnDistance900);
            this.LvRoad900.AllColumns.Add(this.olvColumnSample900);
            this.LvRoad900.AllColumns.Add(this.olvColumnCellName900);
            this.LvRoad900.AllColumns.Add(this.olvColumnLAC900);
            this.LvRoad900.AllColumns.Add(this.olvColumnCI900);
            this.LvRoad900.AllColumns.Add(this.olvColumnBCCH900);
            this.LvRoad900.AllColumns.Add(this.olvColumnBSIC900);
            this.LvRoad900.AllColumns.Add(this.olvColumnRxlev900);
            this.LvRoad900.AllColumns.Add(this.olvColumnCellDistance900);
            this.LvRoad900.AllColumns.Add(this.olvColumnMaxRxlev900);
            this.LvRoad900.AllColumns.Add(this.olvColumnMinRxlev900);
            this.LvRoad900.AllColumns.Add(this.olvColumnAvgRxlev900);
            this.LvRoad900.AllColumns.Add(this.olvColumnLongitudeMid900);
            this.LvRoad900.AllColumns.Add(this.olvColumnLatitudeMid900);
            this.LvRoad900.AllColumns.Add(this.olvColumnFileName900);
            this.LvRoad900.AllColumns.Add(this.olvColumnFirstTime900);
            this.LvRoad900.AllColumns.Add(this.olvColumnLastTime900);
            this.LvRoad900.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN900,
            this.olvColumnRoadName900,
            this.olvColumnDistance900,
            this.olvColumnSample900,
            this.olvColumnCellName900,
            this.olvColumnLAC900,
            this.olvColumnCI900,
            this.olvColumnBCCH900,
            this.olvColumnBSIC900,
            this.olvColumnRxlev900,
            this.olvColumnCellDistance900,
            this.olvColumnMaxRxlev900,
            this.olvColumnMinRxlev900,
            this.olvColumnAvgRxlev900,
            this.olvColumnLongitudeMid900,
            this.olvColumnLatitudeMid900,
            this.olvColumnFileName900,
            this.olvColumnFirstTime900,
            this.olvColumnLastTime900});
            this.LvRoad900.ContextMenuStrip = this.ctxMenu;
            this.LvRoad900.Cursor = System.Windows.Forms.Cursors.Default;
            this.LvRoad900.Dock = System.Windows.Forms.DockStyle.Fill;
            this.LvRoad900.FullRowSelect = true;
            this.LvRoad900.GridLines = true;
            this.LvRoad900.HeaderWordWrap = true;
            this.LvRoad900.IsNeedShowOverlay = false;
            this.LvRoad900.Location = new System.Drawing.Point(3, 3);
            this.LvRoad900.Name = "LvRoad900";
            this.LvRoad900.OwnerDraw = true;
            this.LvRoad900.ShowGroups = false;
            this.LvRoad900.Size = new System.Drawing.Size(1083, 423);
            this.LvRoad900.TabIndex = 5;
            this.LvRoad900.UseCompatibleStateImageBehavior = false;
            this.LvRoad900.View = System.Windows.Forms.View.Details;
            this.LvRoad900.VirtualMode = true;
            this.LvRoad900.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.LvRoad900_MouseDoubleClick);
            // 
            // olvColumnSN900
            // 
            this.olvColumnSN900.AspectName = "";
            this.olvColumnSN900.HeaderFont = null;
            this.olvColumnSN900.Text = "序号";
            this.olvColumnSN900.Width = 80;
            // 
            // olvColumnRoadName900
            // 
            this.olvColumnRoadName900.HeaderFont = null;
            this.olvColumnRoadName900.Text = "道路名称";
            this.olvColumnRoadName900.Width = 120;
            // 
            // olvColumnDistance900
            // 
            this.olvColumnDistance900.HeaderFont = null;
            this.olvColumnDistance900.Text = "距离(米)";
            // 
            // olvColumnSample900
            // 
            this.olvColumnSample900.HeaderFont = null;
            this.olvColumnSample900.Text = "采样点数";
            // 
            // olvColumnCellName900
            // 
            this.olvColumnCellName900.HeaderFont = null;
            this.olvColumnCellName900.Text = "小区名称";
            this.olvColumnCellName900.Width = 100;
            // 
            // olvColumnLAC900
            // 
            this.olvColumnLAC900.HeaderFont = null;
            this.olvColumnLAC900.Text = "LAC";
            // 
            // olvColumnCI900
            // 
            this.olvColumnCI900.HeaderFont = null;
            this.olvColumnCI900.Text = "CI";
            // 
            // olvColumnBCCH900
            // 
            this.olvColumnBCCH900.HeaderFont = null;
            this.olvColumnBCCH900.Text = "BCCH";
            // 
            // olvColumnBSIC900
            // 
            this.olvColumnBSIC900.HeaderFont = null;
            this.olvColumnBSIC900.Text = "BSIC";
            // 
            // olvColumnRxlev900
            // 
            this.olvColumnRxlev900.HeaderFont = null;
            this.olvColumnRxlev900.Text = "Rxlev";
            this.olvColumnRxlev900.Width = 100;
            // 
            // olvColumnCellDistance900
            // 
            this.olvColumnCellDistance900.HeaderFont = null;
            this.olvColumnCellDistance900.Text = "与采样点距离（米）";
            this.olvColumnCellDistance900.Width = 120;
            // 
            // olvColumnMaxRxlev900
            // 
            this.olvColumnMaxRxlev900.HeaderFont = null;
            this.olvColumnMaxRxlev900.Text = "第一强最大值";
            this.olvColumnMaxRxlev900.Width = 80;
            // 
            // olvColumnMinRxlev900
            // 
            this.olvColumnMinRxlev900.HeaderFont = null;
            this.olvColumnMinRxlev900.Text = "第一强最小值";
            this.olvColumnMinRxlev900.Width = 80;
            // 
            // olvColumnAvgRxlev900
            // 
            this.olvColumnAvgRxlev900.HeaderFont = null;
            this.olvColumnAvgRxlev900.Text = "第一强平均值";
            this.olvColumnAvgRxlev900.Width = 80;
            // 
            // olvColumnLongitudeMid900
            // 
            this.olvColumnLongitudeMid900.HeaderFont = null;
            this.olvColumnLongitudeMid900.Text = "经度";
            this.olvColumnLongitudeMid900.Width = 80;
            // 
            // olvColumnLatitudeMid900
            // 
            this.olvColumnLatitudeMid900.HeaderFont = null;
            this.olvColumnLatitudeMid900.Text = "纬度";
            this.olvColumnLatitudeMid900.Width = 80;
            // 
            // olvColumnFileName900
            // 
            this.olvColumnFileName900.HeaderFont = null;
            this.olvColumnFileName900.Text = "文件名";
            // 
            // olvColumnFirstTime900
            // 
            this.olvColumnFirstTime900.HeaderFont = null;
            this.olvColumnFirstTime900.Text = "开始时间";
            this.olvColumnFirstTime900.Width = 100;
            // 
            // olvColumnLastTime900
            // 
            this.olvColumnLastTime900.HeaderFont = null;
            this.olvColumnLastTime900.Text = "结束时间";
            this.olvColumnLastTime900.Width = 100;
            // 
            // tabPage1800
            // 
            this.tabPage1800.Controls.Add(this.LvRoad1800);
            this.tabPage1800.Location = new System.Drawing.Point(4, 23);
            this.tabPage1800.Name = "tabPage1800";
            this.tabPage1800.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1800.Size = new System.Drawing.Size(1089, 429);
            this.tabPage1800.TabIndex = 1;
            this.tabPage1800.Text = "DCS1800";
            this.tabPage1800.UseVisualStyleBackColor = true;
            // 
            // LvRoad1800
            // 
            this.LvRoad1800.AllColumns.Add(this.olvColumnSN1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnRoadName1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnDistance1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnSample1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnCellName1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnLAC1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnCI1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnBCCH1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnBSIC1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnRxlev1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnCellDistance1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnMaxRxlev1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnMinRxlev1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnAvgRxlev1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnLongitudeMid1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnLatitudeMid1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnFileName1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnFirstTime1800);
            this.LvRoad1800.AllColumns.Add(this.olvColumnLastTime1800);
            this.LvRoad1800.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN1800,
            this.olvColumnRoadName1800,
            this.olvColumnDistance1800,
            this.olvColumnSample1800,
            this.olvColumnCellName1800,
            this.olvColumnLAC1800,
            this.olvColumnCI1800,
            this.olvColumnBCCH1800,
            this.olvColumnBSIC1800,
            this.olvColumnRxlev1800,
            this.olvColumnCellDistance1800,
            this.olvColumnMaxRxlev1800,
            this.olvColumnMinRxlev1800,
            this.olvColumnAvgRxlev1800,
            this.olvColumnLongitudeMid1800,
            this.olvColumnLatitudeMid1800,
            this.olvColumnFileName1800,
            this.olvColumnFirstTime1800,
            this.olvColumnLastTime1800});
            this.LvRoad1800.ContextMenuStrip = this.ctxMenu;
            this.LvRoad1800.Cursor = System.Windows.Forms.Cursors.Default;
            this.LvRoad1800.Dock = System.Windows.Forms.DockStyle.Fill;
            this.LvRoad1800.FullRowSelect = true;
            this.LvRoad1800.GridLines = true;
            this.LvRoad1800.HeaderWordWrap = true;
            this.LvRoad1800.IsNeedShowOverlay = false;
            this.LvRoad1800.Location = new System.Drawing.Point(3, 3);
            this.LvRoad1800.Name = "LvRoad1800";
            this.LvRoad1800.OwnerDraw = true;
            this.LvRoad1800.ShowGroups = false;
            this.LvRoad1800.Size = new System.Drawing.Size(1083, 423);
            this.LvRoad1800.TabIndex = 6;
            this.LvRoad1800.UseCompatibleStateImageBehavior = false;
            this.LvRoad1800.View = System.Windows.Forms.View.Details;
            this.LvRoad1800.VirtualMode = true;
            this.LvRoad1800.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.LvRoad1800_MouseDoubleClick);
            // 
            // olvColumnSN1800
            // 
            this.olvColumnSN1800.AspectName = "";
            this.olvColumnSN1800.HeaderFont = null;
            this.olvColumnSN1800.Text = "序号";
            this.olvColumnSN1800.Width = 80;
            // 
            // olvColumnRoadName1800
            // 
            this.olvColumnRoadName1800.HeaderFont = null;
            this.olvColumnRoadName1800.Text = "道路名称";
            this.olvColumnRoadName1800.Width = 120;
            // 
            // olvColumnDistance1800
            // 
            this.olvColumnDistance1800.HeaderFont = null;
            this.olvColumnDistance1800.Text = "距离(米)";
            // 
            // olvColumnSample1800
            // 
            this.olvColumnSample1800.HeaderFont = null;
            this.olvColumnSample1800.Text = "采样点数";
            // 
            // olvColumnCellName1800
            // 
            this.olvColumnCellName1800.HeaderFont = null;
            this.olvColumnCellName1800.Text = "小区名称";
            this.olvColumnCellName1800.Width = 100;
            // 
            // olvColumnLAC1800
            // 
            this.olvColumnLAC1800.HeaderFont = null;
            this.olvColumnLAC1800.Text = "LAC";
            // 
            // olvColumnCI1800
            // 
            this.olvColumnCI1800.HeaderFont = null;
            this.olvColumnCI1800.Text = "CI";
            // 
            // olvColumnBCCH1800
            // 
            this.olvColumnBCCH1800.HeaderFont = null;
            this.olvColumnBCCH1800.Text = "BCCH";
            // 
            // olvColumnBSIC1800
            // 
            this.olvColumnBSIC1800.HeaderFont = null;
            this.olvColumnBSIC1800.Text = "BSIC";
            // 
            // olvColumnRxlev1800
            // 
            this.olvColumnRxlev1800.HeaderFont = null;
            this.olvColumnRxlev1800.Text = "Rxlev";
            this.olvColumnRxlev1800.Width = 100;
            // 
            // olvColumnCellDistance1800
            // 
            this.olvColumnCellDistance1800.HeaderFont = null;
            this.olvColumnCellDistance1800.Text = "与采样点距离（米）";
            this.olvColumnCellDistance1800.Width = 120;
            // 
            // olvColumnMaxRxlev1800
            // 
            this.olvColumnMaxRxlev1800.HeaderFont = null;
            this.olvColumnMaxRxlev1800.Text = "第一强最大值";
            this.olvColumnMaxRxlev1800.Width = 80;
            // 
            // olvColumnMinRxlev1800
            // 
            this.olvColumnMinRxlev1800.HeaderFont = null;
            this.olvColumnMinRxlev1800.Text = "第一强最小值";
            this.olvColumnMinRxlev1800.Width = 80;
            // 
            // olvColumnAvgRxlev1800
            // 
            this.olvColumnAvgRxlev1800.HeaderFont = null;
            this.olvColumnAvgRxlev1800.Text = "第一强平均值";
            this.olvColumnAvgRxlev1800.Width = 80;
            // 
            // olvColumnLongitudeMid1800
            // 
            this.olvColumnLongitudeMid1800.HeaderFont = null;
            this.olvColumnLongitudeMid1800.Text = "经度";
            this.olvColumnLongitudeMid1800.Width = 80;
            // 
            // olvColumnLatitudeMid1800
            // 
            this.olvColumnLatitudeMid1800.HeaderFont = null;
            this.olvColumnLatitudeMid1800.Text = "纬度";
            this.olvColumnLatitudeMid1800.Width = 80;
            // 
            // olvColumnFileName1800
            // 
            this.olvColumnFileName1800.HeaderFont = null;
            this.olvColumnFileName1800.Text = "文件名";
            // 
            // olvColumnFirstTime1800
            // 
            this.olvColumnFirstTime1800.HeaderFont = null;
            this.olvColumnFirstTime1800.Text = "开始时间";
            this.olvColumnFirstTime1800.Width = 100;
            // 
            // olvColumnLastTime1800
            // 
            this.olvColumnLastTime1800.HeaderFont = null;
            this.olvColumnLastTime1800.Text = "结束时间";
            this.olvColumnLastTime1800.Width = 100;
            // 
            // tabPageAll
            // 
            this.tabPageAll.Controls.Add(this.LvRoadAll);
            this.tabPageAll.Location = new System.Drawing.Point(4, 23);
            this.tabPageAll.Name = "tabPageAll";
            this.tabPageAll.Size = new System.Drawing.Size(1089, 429);
            this.tabPageAll.TabIndex = 2;
            this.tabPageAll.Text = "全部";
            this.tabPageAll.UseVisualStyleBackColor = true;
            // 
            // LvRoadAll
            // 
            this.LvRoadAll.AllColumns.Add(this.olvColumnSNAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnRoadNameAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnDistanceAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnSampleAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnCellNameAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnLACAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnCIAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnBCCHAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnBSICAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnRxlevAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnCellDistanceAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnMaxRxlevAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnMinRxlevAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnAvgRxlevAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnLongitudeMidAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnLatitudeMidAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnFileNameAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnFirstTimeAll);
            this.LvRoadAll.AllColumns.Add(this.olvColumnLastTimeAll);
            this.LvRoadAll.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSNAll,
            this.olvColumnRoadNameAll,
            this.olvColumnDistanceAll,
            this.olvColumnSampleAll,
            this.olvColumnCellNameAll,
            this.olvColumnLACAll,
            this.olvColumnCIAll,
            this.olvColumnBCCHAll,
            this.olvColumnBSICAll,
            this.olvColumnRxlevAll,
            this.olvColumnCellDistanceAll,
            this.olvColumnMaxRxlevAll,
            this.olvColumnMinRxlevAll,
            this.olvColumnAvgRxlevAll,
            this.olvColumnLongitudeMidAll,
            this.olvColumnLatitudeMidAll,
            this.olvColumnFileNameAll,
            this.olvColumnFirstTimeAll,
            this.olvColumnLastTimeAll});
            this.LvRoadAll.ContextMenuStrip = this.ctxMenu;
            this.LvRoadAll.Cursor = System.Windows.Forms.Cursors.Default;
            this.LvRoadAll.Dock = System.Windows.Forms.DockStyle.Fill;
            this.LvRoadAll.FullRowSelect = true;
            this.LvRoadAll.GridLines = true;
            this.LvRoadAll.HeaderWordWrap = true;
            this.LvRoadAll.IsNeedShowOverlay = false;
            this.LvRoadAll.Location = new System.Drawing.Point(0, 0);
            this.LvRoadAll.Name = "LvRoadAll";
            this.LvRoadAll.OwnerDraw = true;
            this.LvRoadAll.ShowGroups = false;
            this.LvRoadAll.Size = new System.Drawing.Size(1089, 429);
            this.LvRoadAll.TabIndex = 7;
            this.LvRoadAll.UseCompatibleStateImageBehavior = false;
            this.LvRoadAll.View = System.Windows.Forms.View.Details;
            this.LvRoadAll.VirtualMode = true;
            this.LvRoadAll.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.LvRoadAll_MouseDoubleClick);
            // 
            // olvColumnSNAll
            // 
            this.olvColumnSNAll.AspectName = "";
            this.olvColumnSNAll.HeaderFont = null;
            this.olvColumnSNAll.Text = "序号";
            this.olvColumnSNAll.Width = 80;
            // 
            // olvColumnRoadNameAll
            // 
            this.olvColumnRoadNameAll.HeaderFont = null;
            this.olvColumnRoadNameAll.Text = "道路名称";
            this.olvColumnRoadNameAll.Width = 120;
            // 
            // olvColumnDistanceAll
            // 
            this.olvColumnDistanceAll.HeaderFont = null;
            this.olvColumnDistanceAll.Text = "距离(米)";
            // 
            // olvColumnSampleAll
            // 
            this.olvColumnSampleAll.HeaderFont = null;
            this.olvColumnSampleAll.Text = "采样点数";
            // 
            // olvColumnCellNameAll
            // 
            this.olvColumnCellNameAll.HeaderFont = null;
            this.olvColumnCellNameAll.Text = "小区名称";
            this.olvColumnCellNameAll.Width = 100;
            // 
            // olvColumnLACAll
            // 
            this.olvColumnLACAll.HeaderFont = null;
            this.olvColumnLACAll.Text = "LAC";
            // 
            // olvColumnCIAll
            // 
            this.olvColumnCIAll.HeaderFont = null;
            this.olvColumnCIAll.Text = "CI";
            // 
            // olvColumnBCCHAll
            // 
            this.olvColumnBCCHAll.HeaderFont = null;
            this.olvColumnBCCHAll.Text = "BCCH";
            // 
            // olvColumnBSICAll
            // 
            this.olvColumnBSICAll.HeaderFont = null;
            this.olvColumnBSICAll.Text = "BSIC";
            // 
            // olvColumnRxlevAll
            // 
            this.olvColumnRxlevAll.HeaderFont = null;
            this.olvColumnRxlevAll.Text = "Rxlev";
            this.olvColumnRxlevAll.Width = 100;
            // 
            // olvColumnCellDistanceAll
            // 
            this.olvColumnCellDistanceAll.HeaderFont = null;
            this.olvColumnCellDistanceAll.Text = "与采样点距离（米）";
            this.olvColumnCellDistanceAll.Width = 120;
            // 
            // olvColumnMaxRxlevAll
            // 
            this.olvColumnMaxRxlevAll.HeaderFont = null;
            this.olvColumnMaxRxlevAll.Text = "第一强最大值";
            this.olvColumnMaxRxlevAll.Width = 80;
            // 
            // olvColumnMinRxlevAll
            // 
            this.olvColumnMinRxlevAll.HeaderFont = null;
            this.olvColumnMinRxlevAll.Text = "第一强最小值";
            this.olvColumnMinRxlevAll.Width = 80;
            // 
            // olvColumnAvgRxlevAll
            // 
            this.olvColumnAvgRxlevAll.HeaderFont = null;
            this.olvColumnAvgRxlevAll.Text = "第一强平均值";
            this.olvColumnAvgRxlevAll.Width = 80;
            // 
            // olvColumnLongitudeMidAll
            // 
            this.olvColumnLongitudeMidAll.HeaderFont = null;
            this.olvColumnLongitudeMidAll.Text = "经度";
            this.olvColumnLongitudeMidAll.Width = 80;
            // 
            // olvColumnLatitudeMidAll
            // 
            this.olvColumnLatitudeMidAll.HeaderFont = null;
            this.olvColumnLatitudeMidAll.Text = "纬度";
            this.olvColumnLatitudeMidAll.Width = 80;
            // 
            // olvColumnFileNameAll
            // 
            this.olvColumnFileNameAll.HeaderFont = null;
            this.olvColumnFileNameAll.Text = "文件名";
            // 
            // olvColumnFirstTimeAll
            // 
            this.olvColumnFirstTimeAll.HeaderFont = null;
            this.olvColumnFirstTimeAll.Text = "开始时间";
            this.olvColumnFirstTimeAll.Width = 100;
            // 
            // olvColumnLastTimeAll
            // 
            this.olvColumnLastTimeAll.HeaderFont = null;
            this.olvColumnLastTimeAll.Text = "结束时间";
            this.olvColumnLastTimeAll.Width = 100;
            // 
            // ZTGSMScanHighCoverageRoadListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1097, 456);
            this.Controls.Add(this.tabRoad);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTGSMScanHighCoverageRoadListForm";
            this.Text = "高重叠覆盖路段";
            this.ctxMenu.ResumeLayout(false);
            this.tabRoad.ResumeLayout(false);
            this.tabPage900.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.LvRoad900)).EndInit();
            this.tabPage1800.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.LvRoad1800)).EndInit();
            this.tabPageAll.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.LvRoadAll)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.TabControl tabRoad;
        private System.Windows.Forms.TabPage tabPage900;
        private System.Windows.Forms.TabPage tabPage1800;
        private System.Windows.Forms.TabPage tabPageAll;

        private BrightIdeasSoftware.TreeListView LvRoad900;
        private BrightIdeasSoftware.OLVColumn olvColumnSN900;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName900;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance900;
        private BrightIdeasSoftware.OLVColumn olvColumnSample900;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName900;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC900;
        private BrightIdeasSoftware.OLVColumn olvColumnCI900;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH900;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC900;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev900;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance900;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlev900;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlev900;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev900;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid900;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid900;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName900;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstTime900;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTime900;
        private BrightIdeasSoftware.TreeListView LvRoad1800;
        private BrightIdeasSoftware.OLVColumn olvColumnSN1800;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName1800;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance1800;
        private BrightIdeasSoftware.OLVColumn olvColumnSample1800;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName1800;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC1800;
        private BrightIdeasSoftware.OLVColumn olvColumnCI1800;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH1800;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC1800;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev1800;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance1800;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlev1800;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlev1800;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev1800;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid1800;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid1800;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName1800;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstTime1800;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTime1800;
        private BrightIdeasSoftware.TreeListView LvRoadAll;
        private BrightIdeasSoftware.OLVColumn olvColumnSNAll;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadNameAll;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceAll;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNameAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLACAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCIAll;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCHAll;
        private BrightIdeasSoftware.OLVColumn olvColumnBSICAll;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistanceAll;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlevAll;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlevAll;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlevAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMidAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMidAll;
        private BrightIdeasSoftware.OLVColumn olvColumnFileNameAll;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstTimeAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTimeAll;
    }
}