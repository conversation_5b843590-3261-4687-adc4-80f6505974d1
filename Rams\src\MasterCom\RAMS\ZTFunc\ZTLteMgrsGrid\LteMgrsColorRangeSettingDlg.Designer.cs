﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteMgrsColorRangeSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LteMgrsColorRangeSettingDlg));
            this.colorEditInvalidPoint = new DevExpress.XtraEditors.ColorEdit();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.colorEditInvalidPoint.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // colorEditInvalidPoint
            // 
            this.colorEditInvalidPoint.EditValue = System.Drawing.Color.Gray;
            this.colorEditInvalidPoint.Location = new System.Drawing.Point(110, 288);
            this.colorEditInvalidPoint.Name = "colorEditInvalidPoint";
            this.colorEditInvalidPoint.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEditInvalidPoint.Properties.ShowWebColors = false;
            this.colorEditInvalidPoint.Size = new System.Drawing.Size(58, 21);
            this.colorEditInvalidPoint.TabIndex = 10;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(20, 292);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71, 12);
            this.label1.TabIndex = 9;
            this.label1.Text = "无效点颜色:";
            // 
            // LteMgrsColorRangeSettingDlg
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("LteMgrsColorRangeSettingDlg.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(448, 328);
            this.Controls.Add(this.colorEditInvalidPoint);
            this.Controls.Add(this.label1);
            this.Name = "LteMgrsColorRangeSettingDlg";
            this.Text = "区间颜色设置";
            this.Controls.SetChildIndex(this.label1, 0);
            this.Controls.SetChildIndex(this.colorEditInvalidPoint, 0);
            ((System.ComponentModel.ISupportInitialize)(this.colorEditInvalidPoint.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.ColorEdit colorEditInvalidPoint;
        private System.Windows.Forms.Label label1;
    }
}