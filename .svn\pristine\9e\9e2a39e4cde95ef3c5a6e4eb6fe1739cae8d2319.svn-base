﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.VoLTEDropCallCause;
using MasterCom.Util;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLTEDropCallQuery : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> moCallDropEventIds = new List<int> { 1078, 3613 };
        readonly List<int> moCallAttemptEventIds = new List<int> { 1070, 1001, 3609, 3001 };
        readonly List<int> moCallOverEventIds = new List<int> { 1006, 1008, 1010, 1076, 1078, 1080, 1084, 1086, 1090,
            3006, 3008, 3010, 3608, 3610, 3613, 3622, 3623, 3625 };

        readonly List<int> mtCallDropEventIds = new List<int> { 1079, 3620 };
        readonly List<int> mtCallAttemptEventIds = new List<int> { 1002, 1071, 3002, 3616 };
        readonly List<int> mtCallOverEventIds = new List<int> { 1007, 1009, 1011, 1077, 1079, 1081, 1085, 1087
            , 1091, 3007, 3009, 3011, 3615, 3617, 3620, 3626, 3627, 3629 };

        protected static readonly object lockObj = new object();
        private static VoLTEDropCallQuery instance = null;
        public static VoLTEDropCallQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEDropCallQuery();
                    }
                }
            }
            return instance;
        }

        protected VoLTEDropCallQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.queryAllEvent = false;
            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_NCell_RSRP");
            Columns.Add("lte_NCell_SINR");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");
        }

        protected override void queryTimePeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byRound)
        {//只查询含掉话事件的文件
            condition.EventIDs = new List<int>();
            condition.EventIDs.AddRange(moCallDropEventIds);
            condition.EventIDs.AddRange(mtCallDropEventIds);
            condition.FilterOffValue9 = true;
            base.queryTimePeriodInfo(clientProxy, package, period, byRound);
        }

        public override string Name
        {
            get
            {
                return "VoLTE掉话原因分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27006, this.Name);
        }

        protected override bool getCondition()
        {
            CauseSettingForm dlg = new CauseSettingForm();
            dlg.Condition = this.dropCallCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dropCallCond = dlg.Condition;
            dropCalls = new List<DropCallInfo>();
            return true;
        }

        protected override void fireShowForm()
        {
            if (dropCalls == null || dropCalls.Count == 0)
            {
                MessageBox.Show("没有掉话。");
                return;
            }

            LTEDropCallCauseForm frm = MainModel.CreateResultForm(typeof(LTEDropCallCauseForm)) as LTEDropCallCauseForm;
            frm.FillData(this.dropCalls, this.dropCallCond);
            frm.Visible = true;
            frm.BringToFront();
            dropCalls = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<int, bool> fileAdded = new Dictionary<int, bool>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileAdded.ContainsKey(fileInfo.ID))
                {
                    continue;
                }
                fileAdded[fileInfo.ID] = true;
                if (fileInfo.EventCount == 0)
                {
                    moMtPair[fileInfo] = null;
                }
                else
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(
                        delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    if (mtFile == null)
                    {
                        mtFile = new FileInfo();
                        mtFile.ID = fileInfo.EventCount;
                        mtFile.LogTable = fileInfo.LogTable;
                    }
                    fileAdded[mtFile.ID] = true;
                    moMtPair[fileInfo] = mtFile;
                }
            }

            return moMtPair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    mtFile = file;
                }
            }

            List<CallInfo> moCalls = getCallInfos(moFile, moCallAttemptEventIds, moCallOverEventIds, moCallDropEventIds);
            List<CallInfo> mtCalls = getCallInfos(mtFile, mtCallAttemptEventIds, mtCallOverEventIds, mtCallDropEventIds);
            anaBothSideFiles(moCalls, mtCalls);
        }

        private List<CallInfo> getCallInfos(DTFileDataManager dtFile, List<int> callAttemptEventIds
            , List<int> callOverEventIds, List<int> callDropEventIds)
        {
            List<CallInfo> callInfoList = new List<CallInfo>();
            if (dtFile == null)
            {
                return callInfoList;
            }

            string strMoMtDesc = dtFile.GetMoMtDesc();
            CallInfo curCall = null;
            CallInfo lastCall = null;
            foreach (DTData data in dtFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    doWithTestPoint(curCall, data);
                }
                else if (data is Message)
                {
                    doWithMessage(curCall, data);
                }
                else if (data is Event)
                {
                    Event evt = data as Event;
                    if (callAttemptEventIds.Contains(evt.ID))
                    {
                        doWithCallAttemptEvent(callInfoList, strMoMtDesc, evt, ref curCall, ref lastCall);
                    }
                    else if (callOverEventIds.Contains(evt.ID))
                    {
                        doWithCallOverEvent(callDropEventIds, evt, ref curCall, lastCall);
                    }
                    else if (curCall != null)
                    {
                        curCall.AddEvent(evt);
                    }

                }
            }

            return callInfoList;
        }

        private void doWithCallOverEvent(List<int> callDropEventIds, Event evt
            , ref CallInfo curCall, CallInfo lastCall)
        {
            if (curCall != null)
            {
                bool needAdd = true;
                if (callDropEventIds.Contains(evt.ID))
                {
                    bool filter = int.Parse(evt["Value9"].ToString()) == -1;
                    if (lastCall != null && evt.DateTime == curCall.BeginTime)
                    {
                        //drop time与attempt time一致，则该drop call属于上一次通话结果
                        needAdd = false;
                        lastCall.IsDropCall = true;
                        lastCall.DropEvt = evt;
                        lastCall.DropTime = evt.DateTime;
                        lastCall.IsFilter = filter;
                    }
                    else
                    {
                        curCall.DropEvt = evt;
                        curCall.IsDropCall = true;
                        curCall.DropTime = evt.DateTime;
                        curCall.IsFilter = filter;
                    }
                }
                if (needAdd)
                {
                    curCall.AddEvent(evt);
                    curCall.EndTime = evt.DateTime;
                    curCall = null;
                }
            }
        }

        private void doWithCallAttemptEvent(List<CallInfo> callInfoList, string strMoMtDesc, Event evt
            , ref CallInfo curCall, ref CallInfo lastCall)
        {
            lastCall = curCall;
            if (curCall != null)
            {
                curCall.EndTime = evt.DateTime;
            }
            curCall = new CallInfo();
            curCall.FileName = evt.FileName;
            curCall.MoMtDesc = strMoMtDesc;
            curCall.BeginTime = evt.DateTime;
            curCall.AddEvent(evt);
            callInfoList.Add(curCall);
        }

        private void doWithTestPoint(CallInfo curCall, DTData data)
        {
            TestPoint tp = data as TestPoint;
            if (tp != null && curCall != null)
            {
                curCall.AddTestPoint(tp);
            }
        }

        private void doWithMessage(CallInfo curCall, DTData data)
        {
            Message msg = data as Message;
            if (msg != null && curCall != null)
            {
                curCall.AddMsg(msg);
            }
        }

        private List<DropCallInfo> dropCalls = new List<DropCallInfo>();
        private void anaBothSideFiles(List<CallInfo> moCalls
             , List<CallInfo> mtCalls)
        {
            int lastCallIdx = 0;
            for (int i = 0; i < moCalls.Count; i++)
            {
                CallInfo moCall = moCalls[i];
                if (moCall.IsDropCall && !moCall.IsFilter)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, moCall, mtCalls);
                    saveDropCall(moCall);
                }
            }

            lastCallIdx = 0;
            for (int i = 0; i < mtCalls.Count; i++)
            {
                CallInfo mtCall = mtCalls[i];
                if (mtCall.IsDropCall && !mtCall.IsFilter && mtCall.OtherSideCall == null)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, mtCall, moCalls);
                    saveDropCall(mtCall);
                }
            }

        }

        private int setOtherSideCall(int lastCallIdx, CallInfo call, List<CallInfo> otherSideCalls)
        {
            for (int j = lastCallIdx; j < otherSideCalls.Count; j++)
            {
                CallInfo otherSideCall = otherSideCalls[j];
                if (call.BeginTime > otherSideCall.BeginTime
                    && (call.BeginTime - otherSideCall.BeginTime).TotalSeconds < 15)
                {
                    call.OtherSideCall = otherSideCall;
                    lastCallIdx = j;
                    if (!otherSideCall.IsDropCall)
                    {
                        otherSideCall.DropTime = call.DropTime;
                    }
                    break;
                }
            }

            return lastCallIdx;
        }

        DropCallCondition dropCallCond;
        private void saveDropCall(CallInfo call)
        {
            call.Evaluate(dropCallCond, true);
            int sn = this.dropCalls.Count + 1;
            DropCallInfo dropCall = new DropCallInfo(sn, call);
            this.dropCalls.Add(dropCall);
        }

    }

    public class VoLTEDropCallQuery_FDD : VoLTEDropCallQuery
    {
        private static VoLTEDropCallQuery_FDD instance = null;
        public static new VoLTEDropCallQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEDropCallQuery_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoLTEDropCallQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSSI");
            Columns.Add("lte_fdd_SCell_LAC");
            Columns.Add("lte_fdd_SCell_CI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_NCell_RSRP");
            Columns.Add("lte_fdd_NCell_SINR");
            Columns.Add("lte_fdd_NCell_EARFCN");
            Columns.Add("lte_fdd_NCell_PCI");
        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD掉话原因分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30033, this.Name);
        }
    }
}
