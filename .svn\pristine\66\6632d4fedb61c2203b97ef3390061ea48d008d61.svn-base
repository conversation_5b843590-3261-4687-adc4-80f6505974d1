﻿using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTExportVillageTestShpMapInfo
    {
        protected ZTExportVillageTestShpMapInfo()
        {

        }

        public static List<ResvRegion> GetRegionList(string layerPath)
        {
            List<ResvRegion> regionList = new List<ResvRegion>();
            if (File.Exists(layerPath))
            {
                System.IO.FileInfo file = new System.IO.FileInfo(layerPath);
                string nameColumnName = "NAME";

                string typeName = file.Name;
                if (typeName.IndexOf('.') >= 0)
                {
                    typeName = typeName.Substring(0, typeName.IndexOf('.'));
                }

                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    if (!table.Open(file.FullName, null))
                    {
                        return new List<ResvRegion>();
                    }

                    getShapeFromFile(regionList, nameColumnName, typeName, table);
                    return regionList;
                }
                catch
                {
                    return new List<ResvRegion>();
                }
                finally
                {
                    table.Close();
                }
            }
            return new List<ResvRegion>();
        }

        private static void getShapeFromFile(List<ResvRegion> regionList, string nameColumnName, string typeName, MapWinGIS.Shapefile table)
        {
            int nmFldIndex = MapOperation.GetColumnFieldIndex(table, nameColumnName);
            for (int i = 0; i < table.NumShapes; i++)
            {
                MapWinGIS.Shape geome = table.get_Shape(i);
                if (geome != null)
                {
                    String namestr = table.get_CellValue(nmFldIndex, i).ToString();
                    if (namestr != null && namestr.Trim().Length > 0)
                    {
                        addValidRegion(regionList, typeName, geome, namestr);
                    }
                }
            }
        }

        protected static void setProgressPercent(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private static void addValidRegion(List<ResvRegion> regionList, string typeName, MapWinGIS.Shape geome, string namestr)
        {
            if (geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGONZ
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINE
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINEZ)
            {
                ResvRegion region = new ResvRegion();
                region.RootNodeName = typeName;
                region.RegionName = namestr;
                region.Shape = geome;

                regionList.Add(region);
            }
        }

        //支局图层各个地市在一起,特殊处理
        public static Dictionary<string, Dictionary<string, ResvRegion>> getBranchMapInfo(string layerPath)
        {
            Dictionary<string, Dictionary<string, ResvRegion>> regionDic = new Dictionary<string, Dictionary<string, ResvRegion>>();
            if (File.Exists(layerPath))
            {
                System.IO.FileInfo file = new System.IO.FileInfo(layerPath);

                string typeName = file.Name;
                if (typeName.IndexOf('.') >= 0)
                {
                    typeName = typeName.Substring(0, typeName.IndexOf('.'));
                }

                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    if (!table.Open(file.FullName, null))
                    {
                        return new Dictionary<string, Dictionary<string, ResvRegion>>();
                    }

                    getShapeFromFile(regionDic, typeName, table);
                    return regionDic;
                }
                catch
                {
                    return new Dictionary<string, Dictionary<string, ResvRegion>>();
                }
                finally
                {
                    table.Close();
                }
            }

            return regionDic;
        }

        private static void getShapeFromFile(Dictionary<string, Dictionary<string, ResvRegion>> districtBranchDic, string typeName, MapWinGIS.Shapefile table)
        {
            string district = "地市";
            string branch = "支局";

            int index = 0;
            int progress = 0;
            int districtIndex = MapOperation.GetColumnFieldIndex(table, district);
            int branchIndex = MapOperation.GetColumnFieldIndex(table, branch);
            for (int i = 0; i < table.NumShapes; i++)
            {
                MapWinGIS.Shape geome = table.get_Shape(i);
                if (geome != null)
                {
                    string curDistrictName = table.get_CellValue(districtIndex, i).ToString();
                    if (!string.IsNullOrEmpty(curDistrictName))
                    {
                        Dictionary<string, ResvRegion> branchDic;
                        if (!districtBranchDic.TryGetValue(curDistrictName, out branchDic))
                        {
                            branchDic = new Dictionary<string, ResvRegion>();
                            districtBranchDic.Add(curDistrictName, branchDic);
                        }

                        string curBranchName = table.get_CellValue(branchIndex, i).ToString();
                        if (!string.IsNullOrEmpty(curBranchName))
                        {
                            addValidRegion(branchDic, typeName, geome, curBranchName);
                        }
                    }
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private static void addValidRegion(Dictionary<string, ResvRegion> regionDic, string typeName, MapWinGIS.Shape geome, string curBranchName)
        {
            if (geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGONZ
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINE
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINEZ)
            {
                ResvRegion region;
                if (!regionDic.TryGetValue(curBranchName, out region))
                {
                    region = new ResvRegion();
                    region.RootNodeName = typeName;
                    region.RegionName = curBranchName;
                    region.Shape = geome;
                    regionDic.Add(curBranchName, region);
                }
                else
                {
                    MapWinGIS.Shape tempShp = region.Shape.Clip(geome, MapWinGIS.tkClipOperation.clUnion);
                    if (tempShp != null)
                    {
                        regionDic[curBranchName].Shape = tempShp;
                    }
                }
            }
        }

        public static bool MakeShpFile(string fileName, List<ZTVillageTestGrid> gridList)
        {
            try
            {
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    return false;
                }

                int idIdx = 0;
                int fiCenterLongitude = idIdx++;
                int fiCenterLatitude = idIdx++;
                int fiDistrictName = idIdx++;
                int fiCountyName = idIdx++;
                int fiTestSences = idIdx++;
                int fiTestDevice = idIdx++;
                int fiCarrier = idIdx++;
                int fiLogFile = idIdx++;
                int fiTestDateTime = idIdx++;
                int fiCGI = idIdx++;
                int fiPCI = idIdx++;
                int fiEARFCN = idIdx++;
                int fiRSRP = idIdx++;
                int fiSINR = idIdx;

                ShapeHelper.InsertNewField(shpFile, "CenterLongitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiCenterLongitude);
                ShapeHelper.InsertNewField(shpFile, "CenterLatitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiCenterLatitude);
                ShapeHelper.InsertNewField(shpFile, "DistrictName", FieldType.STRING_FIELD, 10, 30, ref fiDistrictName);
                ShapeHelper.InsertNewField(shpFile, "CountyName", FieldType.STRING_FIELD, 10, 30, ref fiCountyName);
                ShapeHelper.InsertNewField(shpFile, "TestSences", FieldType.STRING_FIELD, 10, 30, ref fiTestSences);
                ShapeHelper.InsertNewField(shpFile, "TestDevice", FieldType.STRING_FIELD, 10, 30, ref fiTestDevice);
                ShapeHelper.InsertNewField(shpFile, "Carrier", FieldType.STRING_FIELD, 10, 30, ref fiCarrier);
                ShapeHelper.InsertNewField(shpFile, "LogFile", FieldType.STRING_FIELD, 10, 30, ref fiLogFile);
                ShapeHelper.InsertNewField(shpFile, "TestDateTime", FieldType.STRING_FIELD, 10, 30, ref fiTestDateTime);
                ShapeHelper.InsertNewField(shpFile, "CGI", FieldType.STRING_FIELD, 10, 30, ref fiCGI);
                ShapeHelper.InsertNewField(shpFile, "PCI", FieldType.STRING_FIELD, 10, 30, ref fiPCI);
                ShapeHelper.InsertNewField(shpFile, "EARFCN", FieldType.STRING_FIELD, 10, 30, ref fiEARFCN);
                ShapeHelper.InsertNewField(shpFile, "RSRP", FieldType.DOUBLE_FIELD, 10, 30, ref fiRSRP);
                ShapeHelper.InsertNewField(shpFile, "SINR", FieldType.DOUBLE_FIELD, 10, 30, ref fiSINR);

                int numShp = 0;
                foreach (ZTVillageTestGrid grid in gridList)
                {
                    numShp++;
                    shpFile.EditInsertShape(ShapeHelper.CreateRectShape(grid.Grid.LTLng, grid.Grid.LTLat, grid.Grid.BRLng, grid.Grid.BRLat), ref numShp);
                    shpFile.EditCellValue(fiCenterLongitude, numShp, grid.CenterLongutidue);
                    shpFile.EditCellValue(fiCenterLatitude, numShp, grid.CenterLatitude);
                    shpFile.EditCellValue(fiDistrictName, numShp,grid.DistrictName);
                    shpFile.EditCellValue(fiCountyName, numShp, grid.CountyName);
                    shpFile.EditCellValue(fiTestSences, numShp, grid.TestScenesDesc);
                    shpFile.EditCellValue(fiTestDevice, numShp, grid.TestDeviceDesc);
                    shpFile.EditCellValue(fiCarrier, numShp, grid.CarrierDesc);
                    shpFile.EditCellValue(fiLogFile, numShp, grid.FileNameDEsc);
                    shpFile.EditCellValue(fiTestDateTime, numShp, grid.TestDateDesc);
                    shpFile.EditCellValue(fiCGI, numShp, grid.CGIDesc);
                    shpFile.EditCellValue(fiPCI, numShp, grid.PciDesc);
                    shpFile.EditCellValue(fiEARFCN, numShp, grid.EarfcnDesc);
                    shpFile.EditCellValue(fiRSRP, numShp, grid.RsrpAvg);
                    shpFile.EditCellValue(fiSINR, numShp, grid.SinrAvg);
                }

                ShapeHelper.DeleteShpFile(fileName);
                if (!shpFile.SaveAs(fileName, null))
                {
                    shpFile.Close();
                    return false;
                }
                shpFile.Close();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
