﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class EvalReqParam
    {
        public string cellname { get; set; }
        public int cellid { get; set; }
        public int lac { get; set; }
        public int ci { get; set; }
        public int freq { get; set; }
        public int cpi { get; set; }
        public int evalresult { get; set; }
        public string orig_sAssFreqList { get; set; }
        public string sAssRuledes { get; set; }//约束条件
        public string old_rule { get; set; }
        public int ibcch { get; set; }//GSM
        public int ibsic { get; set; }//GSM
        public string strtch { get; set; }//GSM
    }

    public class ArrangeResult
    {
        public ArrangeResult()
        {
            SinrList = new List<SINRToPCI>();
        }

        public int sectorid { get; set; }
        public int lac { get; set; }
        public int ci { get; set; }
        public string cellname { get; set; }
        public int orig_iUtranFreq { get; set; }
        public int orig_iScrambleCode { get; set; }
        public int iUtranFreq { get; set; }
        public int iHFreq { get; set; }
        public int iR4Freq { get; set; }
        public int iScrambleCode { get; set; }
        public int iDisterbEvalue { get; set; }
        public int ifreq { get; set; }
        public String sAssFreqList { get; set; }
        public String sAssFreqList24 { get; set; }
        public String sAssRule { get; set; }
        public int ibcch { get; set; }
        public int ibsic { get; set; }
        public String strtch { get; set; }
        public int orig_eval { get; set; }
        public string orig_sAssFreqList { get; set; }
        public string old_rule { get; set; }

        public LTECell mainCell { get; set; }

        public CityInfo curCity { get; set; }
        public List<SINRToPCI> SinrList { get; set; }

        public string BtsName
        {
            get
            {
                if (mainCell == null) 
                    return "未知";

                return mainCell.BTSName;
            }
        }

        public List<SINRToPCI> GetShowSinr(DateTime startTime, DateTime endTime)
        {
            List<SINRToPCI> rtLst = new List<SINRToPCI>();
            
            foreach (SINRToPCI sinr in SinrList)
            {
                DateTime dt = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(sinr.sampletime * 1000L);
                if (dt >= startTime && dt < endTime)
                {
                    rtLst.Add(sinr);
                }
            }
            return rtLst;
        }
    }

    public class FreqArrangeStart
    {
        public FreqArrangeStart()
        {
            Status = -1;
            Session = -1;
        }

        public int Status { get; set; }
        public int Session { get; set; }
    }

    public class FreqArrange
    {
        public int Status { get; set; }
        public int Session { get; set; }
        public int AveEvalue { get; set; }
        public int BestEvalue { get; set; }
        public double Rate { get; set; }
        public List<ArrangeResult> RetCellList { get; set; }
    }
}
