﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverFreqBandAnaBase : DIYAnalyseByFileBackgroundBase
    {
        LteHandOverFreqBandAnaHelper helper;
        LteHandOverFreqBandAnaCond condotion = null;
        List<LteHandOverFreqBandAnaResult> resList;

        protected static readonly object lockObj = new object();
        protected LteHandOverFreqBandAnaBase()
            : base(MainModel.GetInstance())
        {
            //FilterSampleByRegion = true;
            Columns = new List<string>();
            Columns.Add("lte_RSRP");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE);
        }

        public override string Name
        {
            get { return "按频段分析切换指标"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22127, this.Name);
        }

        protected override bool getCondition()
        {
            LteHandOverFreqBandAnaDlg setForm = new LteHandOverFreqBandAnaDlg();
            setForm.SetCondiotion(condotion);
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            condotion = setForm.GetCondition();
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            helper = new LteHandOverFreqBandAnaHelper();
            helper.Init(condotion);
            resList = new List<LteHandOverFreqBandAnaResult>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                helper.DealWithData(file);
            }
        }

        protected override void getResultsAfterQuery()
        {
            resList = helper.GetReult();
        }

        protected override void fireShowForm()
        {
            LteHandOverFreqBandAnaForm frm = MainModel.CreateResultForm(typeof(LteHandOverFreqBandAnaForm)) as LteHandOverFreqBandAnaForm;
            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class LteHandOverFreqBandAnaByFile : LteHandOverFreqBandAnaBase
    {
        private LteHandOverFreqBandAnaByFile()
            : base()
        {
        }

        private static LteHandOverFreqBandAnaByFile instance = null;
        public static LteHandOverFreqBandAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHandOverFreqBandAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "按频段分析切换指标(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class LteHandOverFreqBandAnaByRegion : LteHandOverFreqBandAnaBase
    {
        protected LteHandOverFreqBandAnaByRegion()
            : base()
        {
        }

        private static LteHandOverFreqBandAnaByRegion instance = null;
        public static LteHandOverFreqBandAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHandOverFreqBandAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "按频段分析切换指标(按区域)"; }
        }
    }
}
