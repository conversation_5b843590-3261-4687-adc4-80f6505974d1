﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRVoiceAnaByFreqBandHelper : VoiceAnaByFreqBandHelperBase
    {
        protected override VoiceAnaByFreqBandResult initRes(string str)
        {
            return new NRVoiceAnaByFreqBandResult(str);
        }

        protected override void intEvtList()
        {
            MoCallRequestEvtList.Add((int)NREventManager.VoLTE_Audio_MO_Call_Attempt);
            MoCallRequestEvtList.Add((int)NREventManager.EPSFB_Audio_MO_Call_Attempt);
            MoCallRequestEvtList.Add((int)NREventManager.VoNR_Audio_MO_Call_Attempt);

            MoCallRequestEvtList.Add((int)NREventManager.VoLTE_Video_MO_Call_Attempt);
            MoCallRequestEvtList.Add((int)NREventManager.EPSFB_Video_MO_Call_Attempt);
            MoCallRequestEvtList.Add((int)NREventManager.VoNR_Video_MO_Call_Attempt);

            MoCallEstablishedEvtList.Add((int)NREventManager.VoLTE_Audio_MO_Call_Established);
            MoCallEstablishedEvtList.Add((int)NREventManager.EPSFB_Audio_MO_Call_Established);
            MoCallEstablishedEvtList.Add((int)NREventManager.VoNR_Audio_MO_Call_Established);

            MoCallEstablishedEvtList.Add((int)NREventManager.VoLTE_Video_MO_Call_Established);
            MoCallEstablishedEvtList.Add((int)NREventManager.EPSFB_Video_MO_Call_Established);
            MoCallEstablishedEvtList.Add((int)NREventManager.VoNR_Video_MO_Call_Established);

            MoDropCallEvtList.Add((int)NREventManager.VoLTE_Audio_MO_Drop_Call);
            MoDropCallEvtList.Add((int)NREventManager.EPSFB_Audio_MO_Drop_Call);
            MoDropCallEvtList.Add((int)NREventManager.VoNR_Audio_MO_Drop_Call);

            MoDropCallEvtList.Add((int)NREventManager.VoLTE_Video_MO_Drop_Call);
            MoDropCallEvtList.Add((int)NREventManager.EPSFB_Video_MO_Drop_Call);
            MoDropCallEvtList.Add((int)NREventManager.VoNR_Video_MO_Drop_Call);

            MtCallRequestEvtList.Add((int)NREventManager.VoLTE_Audio_MT_Call_Attempt);
            MtCallRequestEvtList.Add((int)NREventManager.EPSFB_Audio_MT_Call_Attempt);
            MtCallRequestEvtList.Add((int)NREventManager.VoNR_Audio_MT_Call_Attempt);

            MtCallRequestEvtList.Add((int)NREventManager.VoLTE_Video_MT_Call_Attempt);
            MtCallRequestEvtList.Add((int)NREventManager.EPSFB_Video_MT_Call_Attempt);
            MtCallRequestEvtList.Add((int)NREventManager.VoNR_Video_MT_Call_Attempt);

            MtCallEstablishedEvtList.Add((int)NREventManager.VoLTE_Audio_MT_Call_Established);
            MtCallEstablishedEvtList.Add((int)NREventManager.EPSFB_Audio_MT_Call_Established);
            MtCallEstablishedEvtList.Add((int)NREventManager.VoNR_Audio_MT_Call_Established);

            MtCallEstablishedEvtList.Add((int)NREventManager.VoLTE_Video_MT_Call_Established);
            MtCallEstablishedEvtList.Add((int)NREventManager.EPSFB_Video_MT_Call_Established);
            MtCallEstablishedEvtList.Add((int)NREventManager.VoNR_Video_MT_Call_Established);

            MtDropCallEvtList.Add((int)NREventManager.VoLTE_Audio_MT_Drop_Call);
            MtDropCallEvtList.Add((int)NREventManager.EPSFB_Audio_MT_Drop_Call);
            MtDropCallEvtList.Add((int)NREventManager.VoNR_Audio_MT_Drop_Call);

            MtDropCallEvtList.Add((int)NREventManager.VoLTE_Video_MT_Drop_Call);
            MtDropCallEvtList.Add((int)NREventManager.EPSFB_Video_MT_Drop_Call);
            MtDropCallEvtList.Add((int)NREventManager.VoNR_Video_MT_Drop_Call);
        }

        protected override int getEarfcn(TestPoint tp)
        {
            int? nrArfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            if (nrArfcn != null)
            {
                return (int)nrArfcn;
            }

            int? lteEarfcn = (int?)NRTpHelper.NrLteTpManager.GetEARFCN(tp);
            if (lteEarfcn != null)
            {
                return (int)lteEarfcn;
            }

            return 0;
        }

        protected override float? getRsrp(TestPoint tp)
        {
            float? nrRsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (nrRsrp != null && nrRsrp > -150 && nrRsrp < 50)
            {
                return nrRsrp;
            }

            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            if (lteRsrp != null && lteRsrp > -150 && lteRsrp < 50)
            {
                return lteRsrp;
            }

            return null;
        }

        protected override float? getSinr(TestPoint tp)
        {
            float? nrSinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            if (nrSinr != null && nrSinr > -50 && nrSinr < 50)
            {
                return nrSinr;
            }

            float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            if (lteSinr != null && lteSinr > -50 && lteSinr < 50)
            {
                return lteSinr;
            }

            return null;
        }

        public NRVoiceAnaByFreqBandFileResult GetResult(string fileName)
        {
            if (resDic == null || resDic.Values.Count <= 1)
            {
                return null;
            }

            NRVoiceAnaByFreqBandFileResult fileRes = new NRVoiceAnaByFreqBandFileResult();
            fileRes.FileName = fileName;
            foreach (var res in resDic.Values)
            {
                if (res is NRVoiceAnaByFreqBandResult)
                {
                    fileRes.ResList.Add(res as NRVoiceAnaByFreqBandResult);
                }
            }
            return fileRes;
        }

        public NRVoiceAnaByFreqBandFileResult StatTotalResult(List<NRVoiceAnaByFreqBandFileResult> fileResList)
        {
            NRVoiceAnaByFreqBandFileResult totalFileRes = new NRVoiceAnaByFreqBandFileResult();
            totalFileRes.FileName = "总体";

            Dictionary<string, NRVoiceAnaByFreqBandResult> resDic = new Dictionary<string, NRVoiceAnaByFreqBandResult>
            {
                { "A", new NRVoiceAnaByFreqBandResult("A") },
                { "D", new NRVoiceAnaByFreqBandResult("D") },
                { "E", new NRVoiceAnaByFreqBandResult("E") },
                { "F", new NRVoiceAnaByFreqBandResult("F") },
                { "FDD900", new NRVoiceAnaByFreqBandResult("FDD900") },
                { "FDD1800", new NRVoiceAnaByFreqBandResult("FDD1800") },
                { "n28", new NRVoiceAnaByFreqBandResult("n28") },
                { "n41", new NRVoiceAnaByFreqBandResult("n41") },
                { "n77", new NRVoiceAnaByFreqBandResult("n77") },
                { "n78", new NRVoiceAnaByFreqBandResult("n78") },
                { "n79", new NRVoiceAnaByFreqBandResult("n79") },
                { "总体", new NRVoiceAnaByFreqBandResult("总体") }
            };

            foreach (var fileRes in fileResList)
            {
                foreach (var res in fileRes.ResList)
                {
                    NRVoiceAnaByFreqBandResult curRes;
                    if (resDic.TryGetValue(res.FreqBand, out curRes))
                    {
                        curRes.SampleRate.TotalCount += res.SampleRate.TotalCount;
                        curRes.Add(res);
                    }
                }
            }

            foreach (var res in resDic.Values)
            {
                res.Calculate();
            }

            totalFileRes.ResList = new List<NRVoiceAnaByFreqBandResult>(resDic.Values);
            return totalFileRes;
        }
    }
}
