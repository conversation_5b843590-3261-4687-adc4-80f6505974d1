﻿using MasterCom.RAMS.ZTFunc.KPISheet;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    /// <summary>
    /// 模板信息，包含网络类型、模板名、表名、列名
    /// </summary>
    public class TemplateInfo
    {
        public TemplateInfo()
        {

        }

        public TemplateInfo(string templateName)
        {
            this.TemplateName = templateName;
        }

        //模板名，唯一
        public string TemplateName { get; set; }
        //网络类型
        public string TestType { get; set; }
        //表名，与模板名对应，唯一
        public string TableName { get; set; }
        //当前模板的所有列（字符串）
        public string ColName { get; set; }

        public Dictionary<string, List<string>> subTemplate { get; set; }//子模板
        public List<string> colList { get; set; } = new List<string>();//所有列名

        public void init()
        {
            getColList();
            subTemplate = new Dictionary<string, List<string>>(); 
            subTemplate.Add(TemplateName + NetTypeName.getInstance().defaultTemplateName, colList);//默认自定义子模板名与父模板名+“默认模板”
        }

        /// <summary>
        /// 将数据库中colName字段分割成List
        /// </summary>
        private void getColList()
        {
            string[] paramArray = ColName.Split(new string[] { "^#@#^" }, StringSplitOptions.None);
            for (int i = 0; i < paramArray.Length; i++)
            {
                colList.Add(paramArray[i]);
            }
        }
    }
}
