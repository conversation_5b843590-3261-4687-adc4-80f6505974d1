﻿namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    partial class FocusSetListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colStatusDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colItemCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOrderDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAreaNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRoadNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colItemID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colEventName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colPreType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colReasonDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSolution = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colStartTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colEndTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCheckTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCheckFile = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCheckStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCheckCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvt = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.btnFilter = new DevExpress.XtraEditors.SimpleButton();
            this.txtOrderID = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.dtTo = new System.Windows.Forms.DateTimePicker();
            this.dtFrom = new System.Windows.Forms.DateTimePicker();
            this.miReplayFile = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lv.AllColumns.Add(this.colSN);
            this.lv.AllColumns.Add(this.colStatusDesc);
            this.lv.AllColumns.Add(this.colItemCount);
            this.lv.AllColumns.Add(this.colOrderDate);
            this.lv.AllColumns.Add(this.colAreaNames);
            this.lv.AllColumns.Add(this.colRoadNames);
            this.lv.AllColumns.Add(this.colLng);
            this.lv.AllColumns.Add(this.colLat);
            this.lv.AllColumns.Add(this.colItemID);
            this.lv.AllColumns.Add(this.colEventName);
            this.lv.AllColumns.Add(this.colPreType);
            this.lv.AllColumns.Add(this.colReasonDesc);
            this.lv.AllColumns.Add(this.colSolution);
            this.lv.AllColumns.Add(this.colStartTime);
            this.lv.AllColumns.Add(this.colEndTime);
            this.lv.AllColumns.Add(this.colFileName);
            this.lv.AllColumns.Add(this.colCheckTime);
            this.lv.AllColumns.Add(this.colCheckFile);
            this.lv.AllColumns.Add(this.colCheckStatus);
            this.lv.AllColumns.Add(this.colCheckCount);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN,
            this.colStatusDesc,
            this.colItemCount,
            this.colOrderDate,
            this.colAreaNames,
            this.colRoadNames,
            this.colLng,
            this.colLat,
            this.colItemID,
            this.colEventName,
            this.colPreType,
            this.colReasonDesc,
            this.colSolution,
            this.colStartTime,
            this.colEndTime,
            this.colFileName,
            this.colCheckTime,
            this.colCheckFile,
            this.colCheckStatus,
            this.colCheckCount});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.ShowItemToolTips = true;
            this.lv.Size = new System.Drawing.Size(973, 263);
            this.lv.TabIndex = 5;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            // 
            // colSN
            // 
            this.colSN.HeaderFont = null;
            this.colSN.MinimumWidth = 160;
            this.colSN.Text = "工单ID";
            this.colSN.Width = 160;
            // 
            // colStatusDesc
            // 
            this.colStatusDesc.HeaderFont = null;
            this.colStatusDesc.MinimumWidth = 60;
            this.colStatusDesc.Text = "状态";
            // 
            // colItemCount
            // 
            this.colItemCount.HeaderFont = null;
            this.colItemCount.Text = "汇聚事件个数";
            this.colItemCount.Width = 57;
            // 
            // colOrderDate
            // 
            this.colOrderDate.HeaderFont = null;
            this.colOrderDate.Text = "工单首事件时间";
            this.colOrderDate.Width = 101;
            // 
            // colAreaNames
            // 
            this.colAreaNames.HeaderFont = null;
            this.colAreaNames.MinimumWidth = 100;
            this.colAreaNames.Text = "区域";
            this.colAreaNames.Width = 200;
            // 
            // colRoadNames
            // 
            this.colRoadNames.HeaderFont = null;
            this.colRoadNames.MinimumWidth = 100;
            this.colRoadNames.Text = "道路";
            this.colRoadNames.Width = 200;
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.MinimumWidth = 60;
            this.colLng.Text = "经度";
            this.colLng.Width = 120;
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.MinimumWidth = 60;
            this.colLat.Text = "纬度";
            this.colLat.Width = 120;
            // 
            // colItemID
            // 
            this.colItemID.HeaderFont = null;
            this.colItemID.MinimumWidth = 50;
            this.colItemID.Text = "序号";
            // 
            // colEventName
            // 
            this.colEventName.HeaderFont = null;
            this.colEventName.MinimumWidth = 200;
            this.colEventName.Text = "事件名称";
            this.colEventName.Width = 200;
            // 
            // colPreType
            // 
            this.colPreType.HeaderFont = null;
            this.colPreType.MinimumWidth = 200;
            this.colPreType.Text = "问题类型";
            this.colPreType.Width = 200;
            // 
            // colReasonDesc
            // 
            this.colReasonDesc.HeaderFont = null;
            this.colReasonDesc.MinimumWidth = 200;
            this.colReasonDesc.Text = "预判详情";
            this.colReasonDesc.Width = 200;
            // 
            // colSolution
            // 
            this.colSolution.HeaderFont = null;
            this.colSolution.MinimumWidth = 200;
            this.colSolution.Text = "优化方案";
            this.colSolution.Width = 200;
            // 
            // colStartTime
            // 
            this.colStartTime.HeaderFont = null;
            this.colStartTime.MinimumWidth = 200;
            this.colStartTime.Text = "开始时间";
            this.colStartTime.Width = 200;
            // 
            // colEndTime
            // 
            this.colEndTime.HeaderFont = null;
            this.colEndTime.MinimumWidth = 200;
            this.colEndTime.Text = "结束时间";
            this.colEndTime.Width = 200;
            // 
            // colFileName
            // 
            this.colFileName.HeaderFont = null;
            this.colFileName.MinimumWidth = 200;
            this.colFileName.Text = "文件名";
            this.colFileName.Width = 200;
            // 
            // colCheckTime
            // 
            this.colCheckTime.HeaderFont = null;
            this.colCheckTime.MinimumWidth = 200;
            this.colCheckTime.Text = "验证时间";
            this.colCheckTime.Width = 200;
            // 
            // colCheckFile
            // 
            this.colCheckFile.HeaderFont = null;
            this.colCheckFile.MinimumWidth = 200;
            this.colCheckFile.Text = "验证文件";
            this.colCheckFile.Width = 200;
            // 
            // colCheckStatus
            // 
            this.colCheckStatus.HeaderFont = null;
            this.colCheckStatus.MinimumWidth = 50;
            this.colCheckStatus.Text = "验证结果";
            this.colCheckStatus.Width = 200;
            // 
            // colCheckCount
            // 
            this.colCheckCount.HeaderFont = null;
            this.colCheckCount.MinimumWidth = 50;
            this.colCheckCount.Text = "验证次数";
            this.colCheckCount.Width = 200;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvt,
            this.miReplayFile,
            this.toolStripSeparator1,
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator2,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 148);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplayEvt
            // 
            this.miReplayEvt.Name = "miReplayEvt";
            this.miReplayEvt.Size = new System.Drawing.Size(152, 22);
            this.miReplayEvt.Text = "回放事件";
            this.miReplayEvt.Click += new System.EventHandler(this.miReplayEvt_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(149, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(152, 22);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(149, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(152, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.btnQuery);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnFilter);
            this.splitContainerControl1.Panel1.Controls.Add(this.txtOrderID);
            this.splitContainerControl1.Panel1.Controls.Add(this.label4);
            this.splitContainerControl1.Panel1.Controls.Add(this.label2);
            this.splitContainerControl1.Panel1.Controls.Add(this.label3);
            this.splitContainerControl1.Panel1.Controls.Add(this.label1);
            this.splitContainerControl1.Panel1.Controls.Add(this.dtTo);
            this.splitContainerControl1.Panel1.Controls.Add(this.dtFrom);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.lv);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(973, 342);
            this.splitContainerControl1.SplitterPosition = 73;
            this.splitContainerControl1.TabIndex = 6;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(459, 39);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(75, 23);
            this.btnQuery.TabIndex = 3;
            this.btnQuery.Text = "查询";
            this.btnQuery.ToolTip = "重新查询工单";
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // btnFilter
            // 
            this.btnFilter.Location = new System.Drawing.Point(459, 11);
            this.btnFilter.Name = "btnFilter";
            this.btnFilter.Size = new System.Drawing.Size(75, 23);
            this.btnFilter.TabIndex = 3;
            this.btnFilter.Text = "筛选";
            this.btnFilter.ToolTip = "在查询结果中进行筛选";
            this.btnFilter.Click += new System.EventHandler(this.btnFilter_Click);
            // 
            // txtOrderID
            // 
            this.txtOrderID.Location = new System.Drawing.Point(162, 40);
            this.txtOrderID.Name = "txtOrderID";
            this.txtOrderID.Size = new System.Drawing.Size(271, 22);
            this.txtOrderID.TabIndex = 2;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(84, 43);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(47, 14);
            this.label4.TabIndex = 1;
            this.label4.Text = "工单ID:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(119, 14);
            this.label2.TabIndex = 1;
            this.label2.Text = "工单首事件时间范围:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(288, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(19, 14);
            this.label3.TabIndex = 1;
            this.label3.Text = "到";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(137, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(19, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "从";
            // 
            // dtTo
            // 
            this.dtTo.Location = new System.Drawing.Point(313, 12);
            this.dtTo.Name = "dtTo";
            this.dtTo.Size = new System.Drawing.Size(120, 22);
            this.dtTo.TabIndex = 0;
            // 
            // dtFrom
            // 
            this.dtFrom.Location = new System.Drawing.Point(162, 12);
            this.dtFrom.Name = "dtFrom";
            this.dtFrom.Size = new System.Drawing.Size(120, 22);
            this.dtFrom.TabIndex = 0;
            // 
            // miReplayFile
            // 
            this.miReplayFile.Name = "miReplayFile";
            this.miReplayFile.Size = new System.Drawing.Size(152, 22);
            this.miReplayFile.Text = "回放文件";
            this.miReplayFile.Click += new System.EventHandler(this.miReplayFile_Click);
            // 
            // FocusSetListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(973, 342);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "FocusSetListForm";
            this.Text = "工单列表";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colSN;
        private BrightIdeasSoftware.OLVColumn colItemCount;
        private BrightIdeasSoftware.OLVColumn colOrderDate;
        private BrightIdeasSoftware.OLVColumn colAreaNames;
        private BrightIdeasSoftware.OLVColumn colRoadNames;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private BrightIdeasSoftware.OLVColumn colItemID;
        private BrightIdeasSoftware.OLVColumn colEventName;
        private BrightIdeasSoftware.OLVColumn colPreType;
        private BrightIdeasSoftware.OLVColumn colReasonDesc;
        private BrightIdeasSoftware.OLVColumn colSolution;
        private BrightIdeasSoftware.OLVColumn colStartTime;
        private BrightIdeasSoftware.OLVColumn colEndTime;
        private BrightIdeasSoftware.OLVColumn colFileName;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvt;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.SimpleButton btnFilter;
        private System.Windows.Forms.TextBox txtOrderID;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DateTimePicker dtTo;
        private System.Windows.Forms.DateTimePicker dtFrom;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn colStatusDesc;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private BrightIdeasSoftware.OLVColumn colCheckTime;
        private BrightIdeasSoftware.OLVColumn colCheckFile;
        private BrightIdeasSoftware.OLVColumn colCheckStatus;
        private BrightIdeasSoftware.OLVColumn colCheckCount;
        private System.Windows.Forms.ToolStripMenuItem miReplayFile;
    }
}