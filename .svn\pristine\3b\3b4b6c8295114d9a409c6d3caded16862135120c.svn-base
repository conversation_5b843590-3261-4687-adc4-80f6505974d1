﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class ShowCsfbOrderForm : QueryBase
    {
        public ShowCsfbOrderForm()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "查看CSFB工单"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29011, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        TaskOrderForm orderForm = null;

        protected override void query()
        {
            if (orderForm == null || orderForm.IsDisposed)
            {
                orderForm = new TaskOrderForm(10);
            }
            orderForm.Visible = true;
            orderForm.Owner = MainModel.MainForm;
            orderForm.BringToFront();
        }
    }
}
