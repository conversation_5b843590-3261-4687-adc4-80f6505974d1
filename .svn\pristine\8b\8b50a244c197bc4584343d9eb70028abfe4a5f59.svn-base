﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class IndicatorsCompareShow : DevExpress.XtraEditors.XtraUserControl
    {
        private int luX;
        private int luY;
        private int xDistance;
        private int yDistance;
        private DataTable saveTable;
        public IndicatorsCompareShow()
        {
            InitializeComponent();
            labelControlDown.Hide();
            labelControlUp.Hide();
            IniSaveTable();
        }
        private void IniSaveTable()
        {
            saveTable = new DataTable();
            saveTable.Columns.Add("指标项");
            saveTable.Columns.Add("原时间KPI");
            saveTable.Columns.Add("对比时间KPI");
            saveTable.Columns.Add("原时间分数");
            saveTable.Columns.Add("对比时间分数");
        }
        public bool IniShow(List<Indicator> indicatorIni, List<Indicator> indicatorComplare)
        {
            if (indicatorIni == null||indicatorIni.Count == 0)
            {
                XtraMessageBox.Show("初始化的数据存在错误，请检查输入数据！");
                return false;
            }
            if (indicatorComplare == null || indicatorComplare.Count == 0)
            {
                indicatorComplare = new List<Indicator>();
                for (int i = 0; i < indicatorIni.Count; i++)
                {
                    indicatorComplare.Add(new Indicator(indicatorIni[i].Name));
                    indicatorComplare[i].KPI = -10000;
                }
            }
            panel1.Controls.Clear();
            saveTable.Rows.Clear();
            for (int i = 0; i < indicatorIni.Count; i++)
            {
                LabelControl labelNameIni = new LabelControl();
                LabelControl labelKPIIni = new LabelControl();
                LabelControl labelScoreIni = new LabelControl();

                LabelControl labelKPIComp = new LabelControl();
                LabelControl labelScoreComp = new LabelControl();
                labelNameIni.Text = indicatorIni[i].Name;

                setIniLableInfo(indicatorIni, indicatorComplare, i, labelKPIIni, labelScoreIni);
                setCompLableInfo(indicatorIni, indicatorComplare, i, labelKPIComp, labelScoreComp);
                labelNameIni.Location = new Point(luX - (int)(xDistance / 6.0 * 5.0), (int)(luY / 2.0) + i * yDistance);
                labelKPIIni.Location = new Point(luX * 2 - (int)(xDistance / 5.0), (int)(luY / 2.0) + i * yDistance);
                labelKPIComp.Location = new Point(luX * 3 - (int)(xDistance / 5.0), (int)(luY / 2.0) + i * yDistance);
                labelScoreIni.Location = new Point(luX * 4 - (int)(xDistance / 5.0), (int)(luY / 2.0) + i * yDistance);
                labelScoreComp.Location = new Point(luX * 5 - (int)(xDistance / 5.0), (int)(luY / 2.0) + i * yDistance);
                List<object> rowInf = new List<object>();
                rowInf.Add(labelNameIni.Text);
                rowInf.Add(labelKPIIni.Text);
                rowInf.Add(labelKPIComp.Text);
                rowInf.Add(labelScoreIni.Text);
                rowInf.Add(labelScoreComp.Text);
                saveTable.Rows.Add(rowInf.ToArray());

                panel1.Controls.Add(labelNameIni);
                panel1.Controls.Add(labelKPIIni);
                panel1.Controls.Add(labelScoreIni);

                panel1.Controls.Add(labelKPIComp);
                panel1.Controls.Add(labelScoreComp);
            }
            return false;
        }

        private void setIniLableInfo(List<Indicator> indicatorIni, List<Indicator> indicatorComplare, int i, LabelControl labelKPIIni, LabelControl labelScoreIni)
        {
            if (indicatorIni[i].KPI < -9999)
            {
                labelKPIIni.Text = "未知";
                labelScoreIni.Text = "未知";
            }
            else
            {
                if (noPencentageEleNames.Contains(indicatorComplare[i].Name))
                {
                    labelKPIIni.Text = indicatorIni[i].KPI.ToString("0.00");
                    labelScoreIni.Text = indicatorIni[i].Score.ToString("0.00");
                }
                else
                {
                    labelKPIIni.Text = (indicatorIni[i].KPI * 100).ToString("0.00") + "%";
                    labelScoreIni.Text = indicatorIni[i].Score.ToString("0.00");
                }
            }
        }

        private void setCompLableInfo(List<Indicator> indicatorIni, List<Indicator> indicatorComplare, int i, LabelControl labelKPIComp, LabelControl labelScoreComp)
        {
            if (indicatorComplare[i].KPI < -9999)
            {
                labelScoreComp.Text = "未知";
                labelKPIComp.Text = "未知";
            }
            else
            {
                if (noPencentageEleNames.Contains(indicatorComplare[i].Name))
                {
                    labelKPIComp.Text = indicatorComplare[i].KPI.ToString("0.00");
                    labelScoreComp.Text = indicatorComplare[i].Score.ToString("0.00");
                }
                else
                {
                    labelKPIComp.Text = (indicatorComplare[i].KPI * 100).ToString("0.00") + "%";
                    labelScoreComp.Text = indicatorComplare[i].Score.ToString("0.00");
                }
                if (indicatorIni[i].KPI >= -9999)
                {
                    setForeColor(indicatorIni, indicatorComplare, i, labelKPIComp, labelScoreComp);
                }
            }
        }

        private void setForeColor(List<Indicator> indicatorIni, List<Indicator> indicatorComplare, int i, LabelControl labelKPIComp, LabelControl labelScoreComp)
        {
            //如果指标不相同
            if (Math.Abs(indicatorComplare[i].KPI - indicatorIni[i].KPI) > 0)
            {
                if (indicatorComplare[i].KPI > indicatorIni[i].KPI)
                {
                    labelKPIComp.ForeColor = labelControlUp.ForeColor;
                    labelScoreComp.ForeColor = labelControlUp.ForeColor;
                }
                else
                {
                    labelKPIComp.ForeColor = labelControlDown.ForeColor;
                    labelScoreComp.ForeColor = labelControlDown.ForeColor;
                }
            }
        }

        private void IndicatorsCompareShow_Load(object sender, EventArgs e)
        {
            luX = (int)(this.Size.Width / 6.0 + 0.5);
            luY = 15;
            xDistance = (int)(this.Size.Width / 6.0 + 0.5);
            yDistance = 20;
            this.labelControl1.Location = new Point(luX - (int)(xDistance / 6.0 * 5.0), 30);
            this.labelControl2.Location = new Point(luX * 2 - (int)(xDistance / 5.0), 30);
            this.labelControl3.Location = new Point(luX * 3 - (int)(xDistance / 5.0), 30);
            this.labelControl4.Location = new Point(luX * 4 - (int)(xDistance / 5.0), 30);
            this.labelControl5.Location = new Point(luX * 5 - (int)(xDistance / 5.0), 30);
            this.labelControl7.Location = new Point((int)(luX * 2.6 - xDistance / 5.0), 10);
            this.labelControl8.Location = new Point((int)(luX * 4.6 - xDistance / 5.0), 10);
        }

        private List<string> noPencentageEleNames = new List<string>(new string[] { 
            "平均车速", "每呼切换次数", "FTP下载速率", "里程互操作比", "每呼切换数", "FTP平均速率" });

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (saveTable != null && saveTable.Rows.Count != 0)
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(saveTable);
            }
        }
    }
}
