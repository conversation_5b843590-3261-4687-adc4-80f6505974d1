﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanFartherCoverQueryByRegion : ScanFartherCoverQueryBase
    {
        private ScanFartherCoverQueryByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        public ScanFartherCoverQueryByRegion(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        private static ScanFartherCoverQueryByRegion instance = null;
        public static ScanFartherCoverQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ScanFartherCoverQueryByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "超远覆盖_NBIOT扫频(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33014, this.Name);
        }
    }

    public class ScanFartherCoverQueryByFile : ScanFartherCoverQueryBase
    {
        private ScanFartherCoverQueryByFile()
            : base()
        {
        }

        public ScanFartherCoverQueryByFile(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        private static ScanFartherCoverQueryByFile instance = null;
        public static ScanFartherCoverQueryByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ScanFartherCoverQueryByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "超远覆盖_NBIOT扫频(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33015, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class ScanFartherCoverQueryBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        public ScanFartherCoverCondition FartherCoverCond { get; set; }
        protected Dictionary<string, FartherCoverInfo> nameFartherMap;
        protected List<FartherCoverInfo> fartherVec;
        protected string themeName = "";//默认选中指标
        protected string indexName = "";//默认选中指标

        protected static readonly object lockObj = new object();
        protected ScanFartherCoverQueryBase()
            : base(MainModel.GetInstance())
        {
            IncludeEvent = false;
            FartherCoverCond = new ScanFartherCoverCondition();
            nameFartherMap = new Dictionary<string, FartherCoverInfo>();
            fartherVec = new List<FartherCoverInfo>();
            init();
        }

        protected void init()
        {
            Columns = new List<string>();
            Columns.Add("LTESCAN_TopN_EARFCN");
            Columns.Add("LTESCAN_TopN_PCI");
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSRP");
            
            themeName = "LTE_SCAN";
            indexName = "TopN_CELL_Specific_RSRP";
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        public override string Name
        {
            get { return "超远覆盖"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22021, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            ScanFartherCoverSettingDlg dlg = new ScanFartherCoverSettingDlg(FartherCoverCond);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                FartherCoverCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            nameFartherMap.Clear();
            fartherVec.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected virtual void doWithTestPoint(TestPoint tp)
        {
            //第一个小区就已经是第一强小区
            float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            if (rsrp == null) return;
            LTECell nbiotCell = tp.GetCell_LTEScan(0);
            if (nbiotCell == null) return;
            double distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nbiotCell.Longitude, nbiotCell.Latitude);
            addCoverInfo(nbiotCell, tp, (float)rsrp, distance);
            if (FartherCoverCond.ChkMaxRsrpCell) return;
            for (int i = 1; i < 6; i++)
            {
                float? nRsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                int? nEarfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
                short? nPci = (short?)tp["LTESCAN_TopN_PCI", i];

                if (nRsrp != null && nEarfcn != null && nPci != null && nRsrp >= FartherCoverCond.RsrpThreshold)
                {
                    LTECell nbCell = tp.GetCell_LTEScan(i);
                    if (nbCell != null)
                    {
                        distance = MasterCom.Util.MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nbCell.Longitude, nbCell.Latitude);
                        addCoverInfo(nbCell, tp, (float)nRsrp, distance);
                    }
                }
            }
        }
        protected void addCoverInfo(LTECell nbiotCell, TestPoint tp, float rsrp, double distance)
        {
            if (distance >= FartherCoverCond.DistanceMin && distance <= FartherCoverCond.DistanceMax && rsrp >= FartherCoverCond.RsrpThreshold)
            {
                FartherCoverInfo info;
                if (!nameFartherMap.TryGetValue(nbiotCell.Name, out info))
                {
                    info = new FartherCoverInfo(nbiotCell);
                    nameFartherMap.Add(nbiotCell.Name, info);
                }
                info.DealTestPoint(tp, rsrp, distance);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (KeyValuePair<string, FartherCoverInfo> pair in nameFartherMap)
            {
                if (pair.Value.SampleNum >= FartherCoverCond.SampleNum)
                {
                    fartherVec.Add(pair.Value);
                    pair.Value.SN = fartherVec.Count;
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName, indexName);
        }

        protected override void fireShowForm()
        {
            if (fartherVec.Count <= 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            FartherCoverInfoForm frm = MainModel.CreateResultForm(typeof(FartherCoverInfoForm)) as FartherCoverInfoForm;
            frm.FillData(fartherVec);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
