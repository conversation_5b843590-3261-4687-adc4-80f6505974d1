﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellSetResultDetail
    {
        public List<EarfcnCellResult> EarfcnCellResultList { get; set; } = new List<EarfcnCellResult>();
        public List<CellSetOfRegionResult> CellSetOfRegionResultList { get; set; } = new List<CellSetOfRegionResult>();
        public List<UnusedCellSetOfRegionResult> UnusedCellSetOfRegionResultList { get; set; } = new List<UnusedCellSetOfRegionResult>();
        public List<BtsSetOfRegionResult> BtsSetOfRegionResultList { get; set; } = new List<BtsSetOfRegionResult>();
        public List<NBCellSetOfRegionResult> NBCellSetOfRegionResultList { get; set; } = new List<NBCellSetOfRegionResult>();
        public List<SCellAndNCellResult> SCellAndNCellResultList { get; set; } = new List<SCellAndNCellResult>();

        public class EarfcnCellResult
        {
            public string RegionName { get; set; }
            public string CellType { get; set; }
            public int EARFCN { get; set; }
            public int CellCount { get; set; }
            public double CellProportion { get; set; }
            public int TPCount { get; set; }
            public double TPProportion { get; set; }

            public int CellTotalCount { get; set; }
            public int TpTotalCount { get; set; }

            public void Calculate()
            {
                CellProportion = Math.Round(1d * CellCount / CellTotalCount, 2);
                TPProportion = Math.Round(1d * TPCount / TpTotalCount, 2);
            }
        }

        public class CellSetOfRegionResult
        {
            public string RegionName { get; set; }
            public string CellType { get; set; }
            public CellStater Stater { get; set; } = new CellStater();
        }

        public class UnusedCellSetOfRegionResult
        {
            public string RegionName { get; set; }
            public string CellType { get; set; }
            public string CellName { get; set; }
            public int EARFCN { get; set; }
            public int PCI { get; set; }
            public int TAC { get; set; }
            public long NCI { get; set; }
        }

        public class BtsSetOfRegionResult
        {
            public string RegionName { get; set; }
            public string Type { get; set; }
            public BtsStater Stater { get; set; } = new BtsStater();
        }

        public class NBCellSetOfRegionResult
        {
            public string RegionName { get; set; }
            public string CellType { get; set; }
            public string CellName { get; set; }
            public int EARFCN { get; set; }
            public int PCI { get; set; }
            public double RsrpAvg { get; set; }
            public int Count { get; set; }
            public int TAC { get; set; }
            public long NCI { get; set; }
        }

        public class SCellAndNCellResult
        {
            public string RegionName { get; set; }
            public string CellType { get; set; }
            public string CellName { get; set; }
            public int EARFCN { get; set; }
            public int PCI { get; set; }
            public int SCellCount { get; set; }
            public int NCellCount { get; set; }
            public int TAC { get; set; }
            public long NCI { get; set; }
        }
    }
}
