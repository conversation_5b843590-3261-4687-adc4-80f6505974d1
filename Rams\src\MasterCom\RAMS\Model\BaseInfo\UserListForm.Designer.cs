﻿namespace MasterCom.RAMS.Model
{
    partial class UserListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpUser = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlUser = new DevExpress.XtraGrid.GridControl();
            this.gvUser = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLogonCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCity = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPhone = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcState = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcProp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLastLogin = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBelong = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnSubmit = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemove = new DevExpress.XtraEditors.SimpleButton();
            this.btnModify = new DevExpress.XtraEditors.SimpleButton();
            this.btnInputAdminPw = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).BeginInit();
            this.grpUser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlUser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUser)).BeginInit();
            this.SuspendLayout();
            // 
            // grpUser
            // 
            this.grpUser.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpUser.Controls.Add(this.gridCtrlUser);
            this.grpUser.Location = new System.Drawing.Point(3, 3);
            this.grpUser.Name = "grpUser";
            this.grpUser.Size = new System.Drawing.Size(822, 436);
            this.grpUser.TabIndex = 3;
            this.grpUser.Text = "账号列表";
            // 
            // gridCtrlUser
            // 
            this.gridCtrlUser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlUser.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlUser.MainView = this.gvUser;
            this.gridCtrlUser.Name = "gridCtrlUser";
            this.gridCtrlUser.Size = new System.Drawing.Size(818, 411);
            this.gridCtrlUser.TabIndex = 2;
            this.gridCtrlUser.UseEmbeddedNavigator = true;
            this.gridCtrlUser.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvUser});
            // 
            // gvUser
            // 
            this.gvUser.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gvUser.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.gvUser.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvUser.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcName,
            this.gcLogonCode,
            this.gcCity,
            this.gcPhone,
            this.gcDesc,
            this.gcState,
            this.gcProp,
            this.gcLastLogin,
            this.gcBelong});
            this.gvUser.GridControl = this.gridCtrlUser;
            this.gvUser.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvUser.Name = "gvUser";
            this.gvUser.OptionsBehavior.Editable = false;
            this.gvUser.OptionsDetail.EnableMasterViewMode = false;
            this.gvUser.OptionsDetail.ShowDetailTabs = false;
            this.gvUser.OptionsSelection.MultiSelect = true;
            this.gvUser.OptionsView.EnableAppearanceEvenRow = true;
            this.gvUser.OptionsView.EnableAppearanceOddRow = true;
            this.gvUser.PaintStyleName = "Skin";
            this.gvUser.DoubleClick += new System.EventHandler(this.gvUser_DoubleClick);
            // 
            // gcName
            // 
            this.gcName.Caption = "用户名";
            this.gcName.FieldName = "Name";
            this.gcName.Name = "gcName";
            this.gcName.Visible = true;
            this.gcName.VisibleIndex = 0;
            this.gcName.Width = 95;
            // 
            // gcLogonCode
            // 
            this.gcLogonCode.Caption = "登录名";
            this.gcLogonCode.FieldName = "LoginName";
            this.gcLogonCode.Name = "gcLogonCode";
            this.gcLogonCode.Visible = true;
            this.gcLogonCode.VisibleIndex = 1;
            this.gcLogonCode.Width = 108;
            // 
            // gcCity
            // 
            this.gcCity.Caption = "地市";
            this.gcCity.FieldName = "CityName";
            this.gcCity.Name = "gcCity";
            this.gcCity.Visible = true;
            this.gcCity.VisibleIndex = 2;
            this.gcCity.Width = 145;
            // 
            // gcPhone
            // 
            this.gcPhone.Caption = "电话号码";
            this.gcPhone.FieldName = "Phone";
            this.gcPhone.Name = "gcPhone";
            this.gcPhone.Visible = true;
            this.gcPhone.VisibleIndex = 3;
            this.gcPhone.Width = 203;
            // 
            // gcDesc
            // 
            this.gcDesc.Caption = "描述";
            this.gcDesc.FieldName = "Description";
            this.gcDesc.Name = "gcDesc";
            this.gcDesc.Visible = true;
            this.gcDesc.VisibleIndex = 4;
            this.gcDesc.Width = 156;
            // 
            // gcState
            // 
            this.gcState.Caption = "状态";
            this.gcState.FieldName = "UserStatusDes";
            this.gcState.Name = "gcState";
            this.gcState.Width = 100;
            // 
            // gcProp
            // 
            this.gcProp.Caption = "帐号属性";
            this.gcProp.FieldName = "UserProDes";
            this.gcProp.Name = "gcProp";
            this.gcProp.Width = 100;
            // 
            // gcLastLogin
            // 
            this.gcLastLogin.Caption = "最近登录时间";
            this.gcLastLogin.FieldName = "LastLoginTime";
            this.gcLastLogin.Name = "gcLastLogin";
            this.gcLastLogin.Width = 130;
            // 
            // gcBelong
            // 
            this.gcBelong.Caption = "所属责任人";
            this.gcBelong.FieldName = "BelongPerson";
            this.gcBelong.Name = "gcBelong";
            this.gcBelong.Width = 110;
            // 
            // btnAdd
            // 
            this.btnAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnAdd.Location = new System.Drawing.Point(230, 457);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(87, 27);
            this.btnAdd.TabIndex = 7;
            this.btnAdd.Text = "添加账号";
            this.btnAdd.Visible = false;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // btnSubmit
            // 
            this.btnSubmit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSubmit.Location = new System.Drawing.Point(727, 457);
            this.btnSubmit.Name = "btnSubmit";
            this.btnSubmit.Size = new System.Drawing.Size(87, 27);
            this.btnSubmit.TabIndex = 7;
            this.btnSubmit.Text = "提交修改";
            this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
            // 
            // btnRemove
            // 
            this.btnRemove.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRemove.Location = new System.Drawing.Point(121, 457);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(87, 27);
            this.btnRemove.TabIndex = 7;
            this.btnRemove.Text = "删除账号";
            this.btnRemove.Visible = false;
            this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
            // 
            // btnModify
            // 
            this.btnModify.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnModify.Location = new System.Drawing.Point(12, 457);
            this.btnModify.Name = "btnModify";
            this.btnModify.Size = new System.Drawing.Size(87, 27);
            this.btnModify.TabIndex = 7;
            this.btnModify.Text = "修改账号";
            this.btnModify.Click += new System.EventHandler(this.btnModify_Click);
            // 
            // btnInputAdminPw
            // 
            this.btnInputAdminPw.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnInputAdminPw.Location = new System.Drawing.Point(566, 457);
            this.btnInputAdminPw.Name = "btnInputAdminPw";
            this.btnInputAdminPw.Size = new System.Drawing.Size(145, 27);
            this.btnInputAdminPw.TabIndex = 7;
            this.btnInputAdminPw.Text = "进入系统管理员模式";
            this.btnInputAdminPw.Click += new System.EventHandler(this.btnInputAdminPw_Click);
            // 
            // UserListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(829, 498);
            this.Controls.Add(this.grpUser);
            this.Controls.Add(this.btnSubmit);
            this.Controls.Add(this.btnRemove);
            this.Controls.Add(this.btnModify);
            this.Controls.Add(this.btnInputAdminPw);
            this.Controls.Add(this.btnAdd);
            this.MinimumSize = new System.Drawing.Size(694, 536);
            this.Name = "UserListForm";
            this.Text = "账号管理";
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).EndInit();
            this.grpUser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlUser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvUser)).EndInit();
            this.ResumeLayout(false);

        }
        #endregion

        private DevExpress.XtraEditors.GroupControl grpUser;
        private DevExpress.XtraGrid.GridControl gridCtrlUser;
        private DevExpress.XtraGrid.Views.Grid.GridView gvUser;
        private DevExpress.XtraGrid.Columns.GridColumn gcName;
        private DevExpress.XtraGrid.Columns.GridColumn gcLogonCode;
        private DevExpress.XtraGrid.Columns.GridColumn gcCity;
        private DevExpress.XtraGrid.Columns.GridColumn gcDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gcPhone;
        private DevExpress.XtraEditors.SimpleButton btnAdd;
        private DevExpress.XtraEditors.SimpleButton btnSubmit;
        private DevExpress.XtraEditors.SimpleButton btnRemove;
        private DevExpress.XtraEditors.SimpleButton btnModify;
        private DevExpress.XtraEditors.SimpleButton btnInputAdminPw;
        private DevExpress.XtraGrid.Columns.GridColumn gcState;
        private DevExpress.XtraGrid.Columns.GridColumn gcProp;
        private DevExpress.XtraGrid.Columns.GridColumn gcLastLogin;
        private DevExpress.XtraGrid.Columns.GridColumn gcBelong;
    }
}