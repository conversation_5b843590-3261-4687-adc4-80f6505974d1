﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTRegionGridFilter
{
    public partial class RegionGridFilterListForm : MinCloseForm
    {
        public RegionGridFilterListForm()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
            initLv();
        }

        private void initLv()
        {
            colDistance.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.Distance;
                }
                return null;
            };

            colDuration.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.Duration;
                }
                return null;
            };

            colFilterGridNum.AspectGetter = delegate(object row)
            {
                if (row is RegionGridDataInfo)
                {
                    RegionGridDataInfo reg = row as RegionGridDataInfo;
                    return reg.FilteredGirds.Count;
                }
                return null;
            };

            colFtpNum.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.FtpSuccCount;
                }
                return null;
            };

            colGridRate.AspectGetter = delegate(object row)
            {
                if (row is RegionGridDataInfo)
                {
                    RegionGridDataInfo reg = row as RegionGridDataInfo;
                    return reg.FilteredRate;
                }
                return null;
            };

            colGridSumNum.AspectGetter = delegate(object row)
            {
                if (row is RegionGridDataInfo)
                {
                    RegionGridDataInfo reg = row as RegionGridDataInfo;
                    return reg.GridNum;
                }
                return null;
            };

            colLtLat.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.LTLat;
                }
                return null;
            };

            colLtLng.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.LTLng;
                }
                return null;
            };

            colRegName.AspectGetter = delegate(object row)
            {
                if (row is RegionGridDataInfo)
                {
                    RegionGridDataInfo reg = row as RegionGridDataInfo;
                    return reg.Name;
                }
                return null;
            };

            colSpeedM.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.AppSpeedM;
                }
                return null;
            };

            colTpNum.AspectGetter = delegate(object row)
            {
                if (row is GridItem)
                {
                    GridItem grid = row as GridItem;
                    return grid.TestPointCount;
                }
                return null;
            };

            lv.CanExpandGetter = delegate(object row)
            {
                return row is RegionGridDataInfo;
            };

            lv.ChildrenGetter = delegate(object row)
            {
                if (row is RegionGridDataInfo)
                {
                    return (row as RegionGridDataInfo).FilteredGirds;
                }
                return null;
            };

        }

        private List<RegionGridDataInfo> dataSet = null;
        public void FillData(List<RegionGridDataInfo> dataSet)
        {
            this.dataSet = dataSet;
            lv.ClearObjects();
            lv.SetObjects(dataSet);
            doFilter();
            makeSureLayerVisible();
        }

        private void doFilter()
        {
            foreach (RegionGridDataInfo item in dataSet)
            {
                item.FilteredGrid((int)numTpCount.Value, (int)numDuration.Value, (int)numDistance.Value);
            }
            lv.RefreshObjects(dataSet);
        }

        private void btnFilter_Click(object sender, EventArgs e)
        {
            doFilter();
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow header = new NPOIRow();
            header.AddCellValue("区域");
            header.AddCellValue("总测试栅格数");
            header.AddCellValue("条件栅格个数");
            header.AddCellValue("条件栅格占比");
            header.AddCellValue("采样点个数");
            header.AddCellValue("时长(4.5个采样点为1秒)(秒)");
            header.AddCellValue("里程(米)");
            header.AddCellValue("App层平均下载速率(Mbps)");
            header.AddCellValue("FTP下载成功次数");
            header.AddCellValue("左上经度");
            header.AddCellValue("左上纬度");
            rows.Add(header);
            foreach (RegionGridDataInfo item in dataSet)
            {
                NPOIRow row = new NPOIRow();
                row.AddCellValue(item.Name);
                row.AddCellValue(item.GridNum);
                row.AddCellValue(item.FilteredGirds.Count);
                row.AddCellValue(item.FilteredRate);
                foreach (GridItem grid in item.FilteredGirds)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(grid.TestPointCount);
                    subRow.AddCellValue(grid.Duration);
                    subRow.AddCellValue(grid.Distance);
                    subRow.AddCellValue(grid.AppSpeedM);
                    subRow.AddCellValue(grid.FtpSuccCount);
                    subRow.AddCellValue(grid.LTLng);
                    subRow.AddCellValue(grid.LTLat);
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void btnGis_Click(object sender, EventArgs e)
        {
            MasterCom.Grid.ColorRangeMngDlg mngDlg = new MasterCom.Grid.ColorRangeMngDlg();
            mngDlg.FixMinMax(-141, 25);
            mngDlg.FillColorRanges(layer.ColorRanges);
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                layer.ColorRanges = mngDlg.ColorRanges;
            }
        }

        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                layer = mf.GetCustomLayer(typeof(FilteredGridLayer)) as FilteredGridLayer;
                if (layer == null)
                {
                    layer = new FilteredGridLayer(mf.GetMapOperation(), "栅格测试深度");
                    mf.AddTempCustomLayer(layer);
                }
            }
            layer.RegionDataSet = this.dataSet;
        }

        FilteredGridLayer layer = null;

        private void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            layer.CurSelGrid = info.RowObject as GridItem;
            if (layer.CurSelGrid!=null)
            {
                MainModel.MainForm.GetMapForm().GoToView(layer.CurSelGrid.LTLng, layer.CurSelGrid.LTLat, 6000);
            }
        }

    }
}
