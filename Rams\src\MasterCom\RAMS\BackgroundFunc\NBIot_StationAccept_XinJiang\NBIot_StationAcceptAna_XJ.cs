﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class NbIotStationAcceptAnaXJ : NbIotStationAcceptAna
    {
        #region instance
        protected static new readonly object lockObj = new object();
        private static NbIotStationAcceptAnaXJ instance = null;
        public static new NbIotStationAcceptAnaXJ GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NbIotStationAcceptAnaXJ();
                    }
                }
            }
            return instance;
        }
        #endregion

        protected NbIotStationAcceptAnaXJ()
            : base()
        {
        }

        public override string Name
        {
            get { return "新疆NBIot单站验收"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22122, "新疆NBIot单站验收");
        }

        /// <summary>
        /// 文件回放后,将其数据按文件名进行不同的单站验收功能
        /// </summary>
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new NbIotStationAcceptManagerXJ();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            MainModel.DTDataManager.Clear();
        }

        /// <summary>
        /// 导出室外站
        /// </summary>
        /// <param name="bgResultList"></param>
        /// <param name="btsWorkParamInfo"></param>
        protected override void exportOutdoorBtsReport(List<BackgroundResult> bgResultList, NbIotBtsWorkParam btsWorkParamInfo)
        {
            NbIotOutDoorBtsAcceptInfoXJ curBtsAcceptInfo = NbIotStaionAcceptResultHelperXJ.GetOutDoorBtsResultByBgData(
                bgResultList, btsWorkParamInfo.ENodeBID);

            if (curBtsAcceptInfo != null)
            {
                bool hasExportReport = NbIotExportOutdoorBtsReportHelperXJ.ExportReports(curBtsAcceptInfo, btsWorkParamInfo, FuncSet.ReportSavePath);
                updateBtsAcceptDes(curBtsAcceptInfo.IsAccordAccept, hasExportReport, btsWorkParamInfo);
            }
            else
            {
                reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
            }
        }

        /// <summary>
        /// 获取站点覆盖截图保存文件夹地址
        /// </summary>
        /// <param name="btsName"></param>
        /// <returns></returns>
        public override string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(NbIotExportOutdoorBtsReportHelperXJ.WorkDir, btsName.Trim());
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("ExportReportSet");
                ignoreParamKeys.Add("Params_FTP");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["ExportReportSet"] = FuncSet.Params;
                param["Params_FTP"] = FuncSet.FtpSetInfo.Params;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    FuncSet.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
                if (param.ContainsKey("Params_FTP"))
                {
                    FuncSet.FtpSetInfo.Params = param["Params_FTP"] as Dictionary<string, object>;
                }
            }
        }
    }
}
