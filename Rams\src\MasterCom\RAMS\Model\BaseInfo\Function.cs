using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model
{
    public class MainFunction : IUserInfoObject
    {
        public MainFunction()
        {

        }

        public int ID { get; set; }

        public string LoginName { get; set; }

        public string Description { get; set; }

        public List<SubFunction> SubFuncList
        {
            get { return subFuncList; }
        }

        public override string ToString()
        {
            return LoginName;
        }

        public static MainFunction Fill(Content content)
        {
            MainFunction func = new MainFunction();
            func.ID = content.GetParamInt();
            func.LoginName = content.GetParamString();
            func.Description = content.GetParamString();
            return func;
        }

        readonly List<SubFunction> subFuncList = new List<SubFunction>();
    }
}
