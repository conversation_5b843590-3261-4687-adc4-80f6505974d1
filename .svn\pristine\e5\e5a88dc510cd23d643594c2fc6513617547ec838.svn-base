﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakMosReasonHelperBase
    {
        public StringBuilder StrbErr { get; set; }
        readonly List<ServiceType> serviceTypes;
        readonly MainModel MainModel;
        public WeakMosReasonHelperBase(MainModel mainModel, List<ServiceType> serviceTypes)
        {
            this.MainModel = mainModel;
            this.serviceTypes = serviceTypes;
        }

        #region 关联主被叫文件
        public Dictionary<FileInfo, FileInfo> GetMoMtPair()
        {
            StrbErr = new StringBuilder();
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<FileInfo, bool> fileAdded = new Dictionary<FileInfo, bool>();
            connectMoMtFile(moMtPair, fileAdded, connectMtFile);
            connectMoMtFile(moMtPair, fileAdded, connectMoFile);
            moMtPair = Sort.SortDic(moMtPair, sortByFileTime);
            return moMtPair;
        }

        delegate void Func(FileInfo fileInfo, Dictionary<FileInfo, FileInfo> moMtPair, Dictionary<FileInfo, bool> fileAdded);

        private void connectMoMtFile(Dictionary<FileInfo, FileInfo> moMtPair, Dictionary<FileInfo, bool> fileAdded
            , Func func)
        {
            //被叫关联主叫文件
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (serviceTypes.Count > 0 && fileInfo.ServiceType > 0
                    && !serviceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                func(fileInfo, moMtPair, fileAdded);
            }
        }

        private void connectMtFile(FileInfo fileInfo, Dictionary<FileInfo, FileInfo> moMtPair
            , Dictionary<FileInfo, bool> fileAdded)
        {
            if (fileInfo.Momt == 1)
            {
                //在选中的文件中主叫关联被叫
                FileInfo mtFile = MainModel.FileInfos.Find(x => x.ID == fileInfo.EventCount);
                if (mtFile == null)//选中的文件中关联不到主被叫文件，再在服务端关联查询
                {
                    mtFile = GetPairFile(fileInfo);
                }

                moMtPair[fileInfo] = mtFile;
                if (mtFile != null)
                {
                    //将对端文件插入
                    fileAdded[mtFile] = true;
                }
            }
        }

        private void connectMoFile(FileInfo fileInfo, Dictionary<FileInfo, FileInfo> moMtPair
            , Dictionary<FileInfo, bool> fileAdded)
        {
            if (fileInfo.Momt == 2 && !fileAdded.ContainsKey(fileInfo))
            {
                //从服务端查询其主叫文件
                moMtPair[fileInfo] = GetPairFile(fileInfo);
            }
        }

        /// <summary>
        /// 将主被叫文件配对
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        private FileInfo GetPairFile(FileInfo file)
        {
            FileInfo filePair = null;
            DIYQueryPeerFileInfo qryFileInfo = new DIYQueryPeerFileInfo(MainModel, file.LogTable, file.ID, file.Name, false);
            qryFileInfo.Query();
            if (qryFileInfo.PeerFileInfoDic != null)
            {
                List<FileInfo> fileList = new List<FileInfo>(qryFileInfo.PeerFileInfoDic.Values);
                if (fileList.Count > 0)
                {
                    return fileList[0];
                }
            }
            else
            {
                if (StrbErr.Length == 0)
                {
                    StrbErr.AppendLine("以下文件未能找到对应的主被叫文件：");
                }
                StrbErr.AppendLine(file.Name);
            }
            return filePair;
        }

        private int sortByFileTime(KeyValuePair<FileInfo, FileInfo> s1, KeyValuePair<FileInfo, FileInfo> s2)
        {
            return s1.Key.BeginTime.CompareTo(s2.Key.BeginTime);
        }
        #endregion


    }
}
