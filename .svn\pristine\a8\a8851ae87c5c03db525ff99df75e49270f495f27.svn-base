﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryScanAnalysisSetForm : BaseDialog
    {
        public ZTDIYQueryScanAnalysisSetForm()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
        public void GetSetCond(ref int iR0_RP, ref double dTime, ref int iCellCount
            , ref bool isGSM,ref bool isTD, ref bool isGSMTD)
        {
            if (!int.TryParse(txtR0_RP.Text.ToString(), out iR0_RP)
                || !double.TryParse(txtTime.Text.ToString(), out dTime) 
                || !int.TryParse(txtCellCount.Text.ToString(), out iCellCount))
            {
                MessageBox.Show("设置的参数有误，请检查！");
                return;
            }
            iR0_RP = int.Parse(txtR0_RP.Text.ToString());
            dTime = int.Parse(txtTime.Text.ToString());
            iCellCount = int.Parse(txtCellCount.Text.ToString());
            isGSM = chGSM.Checked;
            isTD = chTD.Checked;
            isGSMTD = chGT.Checked;
        }

    }
}
