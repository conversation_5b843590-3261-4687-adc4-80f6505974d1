﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MapWinGIS;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGSMCellEmulateCoverAnaByMR : QueryBase
    {
        public ZTGSMCellEmulateCoverAnaByMR(MainModel mainModel)
            : base(mainModel)
        {
        }
        public static int queryidGSM { get; set; } = -1;
        public static bool startGSM { get; set; }//判断是否进入"GSM仿真(MR)"

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "基于MR的GSM覆盖仿真"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19022, this.Name);
        }

        List<CellEmulateCovResult> ccResultList;
        CfgSettingItem cfgSet = null;
        CellEmulateShowItem showItem = null;
        string strCity = "";

        protected override void query()
        {
            initData();//初始化数据
            cfgSet = new CfgSettingItem();
            showItem = new CellEmulateShowItem();
            //用于查询条件传值，需要赋值
            if (!fillValue(cfgSet, showItem))
            {
                return;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";

                strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                MainModel.ClearDTData();
                SelectRegion();
                WaitBox.Show("开始绘制小区仿真点...", queryInThread, clientProxy);

                CalculateResult();
                fireShowResultForm();
                int queryid = (int)(JavaDate.GetMilliseconds(DateTime.Now) / 1000L);
                queryidGSM = queryid;
                MainModel.MainForm.FireCellEmulateMRQueried(queryid);

            }
            catch
            {
                clientProxy.Close();
            }
        }

        private bool fillValue(CfgSettingItem csItem,CellEmulateShowItem item)
        {
            CfgSettingDlg dlg = CfgSettingDlg.GetInstance();
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                if (dlg.BeginTime>dlg.EndTime)
                {
                    MessageBox.Show("开始时间不能大于结束时间！");
                    return false;
                }
                csItem.istime = dlg.BeginTime;
                csItem.ietime = dlg.EndTime;
                csItem.iLongitude = dlg.gridSizeLon;
                csItem.iLatitude = dlg.gridSizeLat;
                csItem.iRxlev = dlg.Rxlev;
                csItem.isTaDownWard = dlg.IsTaDownWard;
                csItem.iTestValue = dlg.TestValue;
                csItem.isAddAbisMr = dlg.IsAddAbisMr;
                csItem.iRepeaterStatus = dlg.RepeaterBts;

                item.visibleRxlevTopN = dlg.VisibleRxlevTopN;
                item.gridSpanLon = dlg.gridSizeLon * 0.0000001;
                item.gridSpanLat = dlg.gridSizeLat * 0.0000001;

                return true;
            }
            return false;
        }

        MapWinGIS.Shape gmt;
        Dictionary<string, MapOperation2> regionMopDic = null;
        Dictionary<string, List<GridLongLat>> gridLongLatDic = null;
        Dictionary<string, List<GridLongLat>> gridNonContainLongLatDic = null;
        Dictionary<string, RxlevGridNum> rxlevGridNumDic = null;
        RxlevGridLongLat rxlevGridLongLat = null;

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void initData()
        {
            gmt = null;
            regionMopDic = null;
            gridLongLatDic = new Dictionary<string, List<GridLongLat>>();
            gridNonContainLongLatDic = new Dictionary<string, List<GridLongLat>>();
            rxlevGridNumDic = new Dictionary<string, RxlevGridNum>();
            rxlevGridLongLat = new RxlevGridLongLat();
            startGSM = true;
        }

        private void SelectRegion()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            gmt = MainModel.SearchGeometrys.Region;
            regionMopDic = new Dictionary<string, MapOperation2>();

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        private void queryInThread(object o)
        {
            string strDesc = "";
            try
            {
                List<Cell> allGsmCell = CellManager.GetInstance().GetCurrentCells();
                Dictionary<CellLaiKey, RxlevCoverDist> abisMrDic = new Dictionary<CellLaiKey, RxlevCoverDist>();
                if (cfgSet.isAddAbisMr)
                {
                    abisMrDic = getAbisMrRxlev(cfgSet, strCity);
                }
                Dictionary<CellLaiKey, RxlevCoverDist> mrDic = anaCellRxlevTa(cfgSet, strCity);
                Dictionary<CellLaiKey, RxlevCoverDist> cellDistDic = mergeMrData(mrDic, abisMrDic);
                Dictionary<GridLongLat, int> gridRscpDic = getTestGridRscp(cfgSet, strCity);
                
                Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic = new Dictionary<string, Dictionary<GridLongLat, string>>();

                getRegionGrid(ref rectAllDic, ref strDesc);
                List<RxlevLongLat> cellEmulationPointsList = anaCoverAreaByCell(allGsmCell, cellDistDic, ref strDesc);
                allGsmCell.Clear();
                cellDistDic.Clear();
                Dictionary<GridLongLat, RxlevShape> winShapeDic = drawCoverAreaByCell(cellEmulationPointsList, ref strDesc);
                cellEmulationPointsList.Clear();
                rejectCoverGrid(winShapeDic, rectAllDic, gridRscpDic, ref strDesc,cfgSet);
            }
            catch(Exception exp)
            {
                log.Error("查询出错，步骤为：" + strDesc);
                MessageBox.Show("发生错误：" + exp.Message, "信息提示", MessageBoxButtons.OK);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 合并MR距离统计，优先取ABIS的MR距离
        /// </summary>
        private Dictionary<CellLaiKey, RxlevCoverDist> mergeMrData(Dictionary<CellLaiKey, RxlevCoverDist> mrDic, Dictionary<CellLaiKey, RxlevCoverDist> abisMrDic)
        {
            Dictionary<CellLaiKey, RxlevCoverDist> resultDic = new Dictionary<CellLaiKey, RxlevCoverDist>();
            foreach (CellLaiKey key in abisMrDic.Keys)
            {
                RxlevCoverDist tmpDist = abisMrDic[key];
                if (!resultDic.ContainsKey(key))
                {
                    resultDic.Add(key, tmpDist);
                }
            }
            foreach (CellLaiKey key in mrDic.Keys)
            {
                RxlevCoverDist tmpDist = mrDic[key];
                if (!resultDic.ContainsKey(key))
                {
                    resultDic.Add(key, tmpDist);
                }
            }
            return resultDic;
        }

        /// <summary>
        /// 获取区域内栅格
        /// </summary>
        private void getRegionGrid(ref Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic, ref string strDesc)
        {
            strDesc = "获取区域内栅格";
            WaitBox.Text = "[" + strCity + "] 获取区域内栅格...";

            int iMinLongitude = (int)(MainModel.SearchGeometrys.Region.Extents.xMin * 10000000) / cfgSet.iLongitude * cfgSet.iLongitude - cfgSet.iLongitude;
            int iMaxLongitude = (int)(MainModel.SearchGeometrys.Region.Extents.xMax * 10000000) / cfgSet.iLongitude * cfgSet.iLongitude + cfgSet.iLongitude;
            int iMinLatitude = (int)(MainModel.SearchGeometrys.Region.Extents.yMin * 10000000) / cfgSet.iLatitude * cfgSet.iLatitude - cfgSet.iLatitude;
            int iMaxLatitude = (int)(MainModel.SearchGeometrys.Region.Extents.yMax * 10000000) / cfgSet.iLatitude * cfgSet.iLatitude + cfgSet.iLatitude;

            while (iMinLongitude <= iMaxLongitude)
            {
                int iTmpLat = iMinLatitude;
                while (iTmpLat <= iMaxLatitude)
                {
                    DbRect rect = new DbRect();
                    rect.x1 = (float)(iMinLongitude) / 10000000;
                    rect.y1 = (float)(iTmpLat - cfgSet.iLatitude) / 10000000;
                    rect.x2 = (float)(iMinLongitude + cfgSet.iLongitude) / 10000000;
                    rect.y2 = (float)iTmpLat / 10000000;
                    GridLongLat gll = GridLongLat.cvtLongLat(rect);
                    foreach (string strRegionName in regionMopDic.Keys)
                    {
                        MapOperation2 mapOper = regionMopDic[strRegionName];
                        RegionExtern regExt = mapOper.GetRegion();//经纬度过滤，提高效率
                        if (rect.x2 < regExt.Bounds.x1 || rect.x1 > regExt.Bounds.x2 ||
                            rect.y2 < regExt.Bounds.y1 || rect.y1 > regExt.Bounds.y2)
                            continue;

                        if (mapOper.CheckRectIntersectWithRegion(rect))
                        {
                            addRectAllDic(rectAllDic, gll, strRegionName);
                            break;
                        }
                    }
                    iTmpLat += cfgSet.iLatitude;
                }
                iMinLongitude += cfgSet.iLongitude;
            }
            WaitBox.ProgressPercent = 50;
        }

        private void addRectAllDic(Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic, GridLongLat gll, string strRegionName)
        {
            if (gridLongLatDic.ContainsKey(strRegionName))
            {
                List<GridLongLat> rectList = gridLongLatDic[strRegionName];
                rectList.Add(gll);

                Dictionary<GridLongLat, string> tmpDic = rectAllDic[strRegionName];
                try
                {
                    tmpDic.Add(gll, strRegionName);
                }
                catch (Exception ex)
                {
                    log.Error(ex.StackTrace);
                }
            }
            else
            {
                List<GridLongLat> rectList = new List<GridLongLat>();
                rectList.Add(gll);
                gridLongLatDic.Add(strRegionName, rectList);

                Dictionary<GridLongLat, string> tmpDic = new Dictionary<GridLongLat, string>();
                tmpDic.Add(gll, strRegionName);
                rectAllDic.Add(strRegionName, tmpDic);
            }
        }

        /// <summary>
        /// 每个小区的覆盖区域
        /// </summary>
        private List<RxlevLongLat> anaCoverAreaByCell(List<Cell> allGsmCell, Dictionary<CellLaiKey, RxlevCoverDist> cellDistDic, ref string strDesc)
        {
            strDesc = "每个小区的覆盖区域";
            WaitBox.Text = "[" + strCity + "] 计算每个小区的覆盖区域...";

            double covAddDistance = 5500;   //最远覆盖距离：用于判断区域以外参与计算的小区
            covAddDistance = covAddDistance * 0.00001;
            double xMin = MainModel.SearchGeometrys.Region.Extents.xMin - covAddDistance;
            double xMax = MainModel.SearchGeometrys.Region.Extents.xMax + covAddDistance;
            double yMin = MainModel.SearchGeometrys.Region.Extents.yMin - covAddDistance;
            double yMax = MainModel.SearchGeometrys.Region.Extents.yMax + covAddDistance;

            int iTotalNum = allGsmCell.Count;
            int iCurNum = 0;
            List<RxlevLongLat> cellEmulationPointsList = new List<RxlevLongLat>();

            foreach (Cell cell in allGsmCell)
            {
                iCurNum++;
                WaitBox.ProgressPercent = (int)(100 * ((float)(iCurNum) / iTotalNum));
                if (cell.Type != BTSType.Outdoor)
                {
                    continue;
                }
                if (cell.Longitude > xMin && cell.Longitude < xMax && cell.Latitude > yMin && cell.Latitude < yMax)
                {
                    CellLaiKey clKey = new CellLaiKey();
                    clKey.ILac = cell.LAC;
                    clKey.ICi = cell.CI;
                    if (!cellDistDic.ContainsKey(clKey))
                        continue;
                    RxlevCoverDist rxlevCoverDist = cellDistDic[clKey];
                    RxlevLongLat rxlevLongLat = new RxlevLongLat();
                    rxlevLongLat.fillCellLongLat(cell, rxlevCoverDist);
                    cellEmulationPointsList.Add(rxlevLongLat);
                }
            }
            WaitBox.ProgressPercent = 70;
            return cellEmulationPointsList;
        }

        /// <summary>
        /// 绘制每个小区的覆盖区域
        /// </summary>
        private Dictionary<GridLongLat, RxlevShape> drawCoverAreaByCell(List<RxlevLongLat> cellEmulationPointsList, ref string strDesc)
        {
            strDesc = "绘制每个小区的覆盖区域";
            WaitBox.Text = "[" + strCity + "] 分析栅格覆盖区域...";

            //将小区覆盖区域绘制成图形
            Dictionary<GridLongLat, RxlevShape> winShapeDic = new Dictionary<GridLongLat, RxlevShape>();
            foreach (RxlevLongLat longLatList in cellEmulationPointsList)
            {
                if (longLatList.Rxlev70LongLatList.Count + longLatList.Rxlev80LongLatList.Count +
                    longLatList.Rxlev85LongLatList.Count + longLatList .Rxlev90LongLatList.Count+
                    longLatList.Rxlev94LongLatList.Count + longLatList .Rxlev95LongLatList.Count == 0)
                    continue;
                RxlevShape.drawShape(longLatList,ref winShapeDic);
            }
            return winShapeDic;
        }

        /// <summary>
        /// 剔除已覆盖栅格区域
        /// </summary>
        private void rejectCoverGrid(Dictionary<GridLongLat, RxlevShape> winShapeDic, Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic,
                                     Dictionary<GridLongLat,int> gridRscpDic, ref string strDesc,CfgSettingItem cfgSet)
        {
            strDesc = "剔除已覆盖栅格区域";
            WaitBox.Text = "[" + strCity + "] 剔除已覆盖栅格区域...";
            Dictionary<GridLongLat, string> gridStatDic = new Dictionary<GridLongLat, string>();

            if (cfgSet.iRxlev <= -70)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 70);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 70);
            }
            if (cfgSet.iRxlev <= -80)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 80);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 80);
            }
            if (cfgSet.iRxlev <= -85)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 85);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 85);
            }
            if (cfgSet.iRxlev <= -90)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 90);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 90);
            }
            if (cfgSet.iRxlev <= -94)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 94);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 94);
            }
            if (cfgSet.iRxlev <= -95)
            {
                rejectCoverGridByTestRxlev(gridRscpDic, ref rectAllDic, ref gridStatDic, 95);
                rejectCoverGridByRxlev(winShapeDic, ref rectAllDic, ref gridStatDic, 95);
            }
            winShapeDic.Clear();

            addGridLongLat(gridStatDic);

            MainModel.RxlevGridLongLat = rxlevGridLongLat;
            MainModel.cellEmulateShowItem = showItem;
            WaitBox.ProgressPercent = 90;
        }

        private void addGridLongLat(Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (string strRegion in gridLongLatDic.Keys)
            {
                foreach (GridLongLat gll in gridLongLatDic[strRegion])
                {
                    if (gridStatDic.ContainsKey(gll))
                        continue;

                    addRxlevGridNum(strRegion, -999);

                    if (gridNonContainLongLatDic.ContainsKey(strRegion))
                    {
                        gridNonContainLongLatDic[strRegion].Add(gll);
                    }
                    else
                    {
                        List<GridLongLat> rectList = new List<GridLongLat>();
                        rectList.Add(gll);
                        gridNonContainLongLatDic.Add(strRegion, rectList);
                    }
                    rxlevGridLongLat.RxlevNonCoverGllList.Add(gll);
                }
            }
        }

        private void rejectCoverGridByRxlev(Dictionary<GridLongLat, RxlevShape> winShapeDic,ref Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic,
                                            ref Dictionary<GridLongLat, string> gridStatDic,int iRxlev)
        {
            int wsCount = winShapeDic.Count;
            int iCurNum2 = 0;
            WaitBox.Text = string.Format("[{0}] 剔除场强-{1}dBm已覆盖栅格区域...",strCity,iRxlev);
            foreach (GridLongLat extGll in winShapeDic.Keys)
            {
                iCurNum2++;
                WaitBox.ProgressPercent = (int)(100 * ((float)(iCurNum2) / wsCount));
                Dictionary<GridLongLat, string> rectSubDic = getSubGridLongLat(rectAllDic, extGll);
                Dictionary<GridLongLat, string> gridStatSubDic = new Dictionary<GridLongLat, string>();
                RxlevShape rxlevWinShape = winShapeDic[extGll];

                if (iRxlev == 70)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev70ShapeList, 70, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 80)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev80ShapeList, 80, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 85)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev85ShapeList, 85, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 90)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev90ShapeList, 90, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 94)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev94ShapeList, 94, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }
                else if (iRxlev == 95)
                {
                    rejectCoverGridByRxlev(rxlevWinShape.Rxlev95ShapeList, 95, ref rectSubDic, ref gridStatSubDic, ref gridStatDic);
                }

                rectAllDic = removeGridLongLat(rectAllDic, gridStatSubDic);
            }
        }

        /// <summary>
        /// 按测试数据剔除删格
        /// </summary>
        private void rejectCoverGridByTestRxlev(Dictionary<GridLongLat, int> gridRscpDic,ref Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic,
                                                ref Dictionary<GridLongLat, string> gridStatDic, int iRxlev)
        {
            foreach (GridLongLat gll in gridRscpDic.Keys)
            {
                int iGirdRxlev = gridRscpDic[gll];
                if (iGirdRxlev < cfgSet.iRxlev)
                {
                    continue;
                }
                string strRegion = "";
                foreach (string tmpRegion in rectAllDic.Keys)
                {
                    if (rectAllDic[tmpRegion].ContainsKey(gll))
                    {
                        strRegion = tmpRegion;
                        break;
                    }
                }
                if (strRegion != "")
                {
                    dealRegionGrid(gridStatDic, iRxlev, gll, iGirdRxlev, strRegion);
                }
            }
            rectAllDic = removeGridLongLat(rectAllDic, gridStatDic);
        }

        private void dealRegionGrid(Dictionary<GridLongLat, string> gridStatDic, int iRxlev, GridLongLat gll, int iGirdRxlev, string strRegion)
        {
            if (judgeValidGrid(iRxlev, iGirdRxlev, 50, -70))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev70GllList.Add(gll);
                addRxlevGridNum(strRegion, 70);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -70, -80))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev80GllList.Add(gll);
                addRxlevGridNum(strRegion, 80);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -80, -85))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev85GllList.Add(gll);
                addRxlevGridNum(strRegion, 85);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -85, -90))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev90GllList.Add(gll);
                addRxlevGridNum(strRegion, 90);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -90, -94))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev94GllList.Add(gll);
                addRxlevGridNum(strRegion, 94);
            }
            if (judgeValidGrid(iRxlev, iGirdRxlev, -94, -95))
            {
                gridStatDic.Add(gll, strRegion);
                rxlevGridLongLat.Rxlev95GllList.Add(gll);
                addRxlevGridNum(strRegion, 95);
            }
        }

        private bool judgeValidGrid(int iRxlev, int iGirdRxlev, int iGirdRxlevMax, int iGirdRxlevMin)
        {
            return iRxlev == -iGirdRxlevMax && iGirdRxlev >= iGirdRxlevMin && iGirdRxlev < iGirdRxlevMax;
        }

        /// <summary>
        /// 累加每级电平栅格数
        /// </summary>
        private void addRxlevGridNum(string strRegion,int iRxlev)
        {
            if (!rxlevGridNumDic.ContainsKey(strRegion))
            {
                RxlevGridNum rxlGrid = new RxlevGridNum();
                rxlevGridNumDic.Add(strRegion, rxlGrid); 
            }
            if (iRxlev == 70)
            {
                rxlevGridNumDic[strRegion].iRxlev70Num += 1;
            }
            else if (iRxlev == 80)
            {
                rxlevGridNumDic[strRegion].iRxlev80Num += 1;
            }
            else if (iRxlev == 85)
            {
                rxlevGridNumDic[strRegion].iRxlev85Num += 1;
            }
            else if (iRxlev == 90)
            {
                rxlevGridNumDic[strRegion].iRxlev90Num += 1;
            }
            else if (iRxlev == 94)
            {
                rxlevGridNumDic[strRegion].iRxlev94Num += 1;
            }
            else if (iRxlev == 95)
            {
                rxlevGridNumDic[strRegion].iRxlev95Num += 1;
            }
            else if (iRxlev==-999)//无覆盖
            {
                rxlevGridNumDic[strRegion].iRxlevNonCoverNum += 1;
            }
        }

        /// <summary>
        /// 按覆盖强度剔除删格
        /// </summary>
        private void rejectCoverGridByRxlev(List<MapWinGIS.Shape> winShapeList,int iRxlev, ref Dictionary<GridLongLat, string> rectSubDic, 
                                            ref Dictionary<GridLongLat, string> gridStatSubDic,ref Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (MapWinGIS.Shape winShape in winShapeList)
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(winShape);
                DbRect tmpRect = MapOperation.GetShapeBounds(winShape);

                foreach (GridLongLat gll in rectSubDic.Keys)
                {
                    if (!gll.compareValue(tmpRect))
                        continue;

                    if (gridStatDic.ContainsKey(gll))
                        continue;

                    setGridStat(iRxlev, rectSubDic, gridStatSubDic, gridStatDic, mapOp2, gll);
                }
                rectSubDic = removeGridLongLat(rectSubDic, gridStatSubDic);
            }
        }

        private void setGridStat(int iRxlev, Dictionary<GridLongLat, string> rectSubDic, Dictionary<GridLongLat, string> gridStatSubDic, Dictionary<GridLongLat, string> gridStatDic, MapOperation2 mapOp2, GridLongLat gll)
        {
            string strRegion = rectSubDic[gll];
            DbRect rect = GridLongLat.cvtRect(gll);
            if (mapOp2.CheckRectIntersectWithRegion(rect)
                && !gridStatDic.ContainsKey(gll))
            {
                gridStatDic.Add(gll, strRegion);
                gridStatSubDic.Add(gll, strRegion);
                if (iRxlev == 70)
                {
                    rxlevGridLongLat.Rxlev70GllList.Add(gll);
                    addRxlevGridNum(strRegion, 70);
                }
                else if (iRxlev == 80)
                {
                    rxlevGridLongLat.Rxlev80GllList.Add(gll);
                    addRxlevGridNum(strRegion, 80);
                }
                else if (iRxlev == 85)
                {
                    rxlevGridLongLat.Rxlev85GllList.Add(gll);
                    addRxlevGridNum(strRegion, 85);
                }
                else if (iRxlev == 90)
                {
                    rxlevGridLongLat.Rxlev90GllList.Add(gll);
                    addRxlevGridNum(strRegion, 90);
                }
                else if (iRxlev == 94)
                {
                    rxlevGridLongLat.Rxlev94GllList.Add(gll);
                    addRxlevGridNum(strRegion, 94);
                }
                else if (iRxlev == 95)
                {
                    rxlevGridLongLat.Rxlev95GllList.Add(gll);
                    addRxlevGridNum(strRegion, 95);
                }
            }
        }

        /// <summary>
        /// 移除重复项
        /// </summary>
        private Dictionary<GridLongLat, string> removeGridLongLat(Dictionary<GridLongLat, string> rectAllDic, Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (GridLongLat gll in gridStatDic.Keys)
            {
                if (rectAllDic.ContainsKey(gll))
                    rectAllDic.Remove(gll);
            }
            return rectAllDic;
        }

        /// <summary>
        /// 移除重复项
        /// </summary>
        private Dictionary<string, Dictionary<GridLongLat, string>> removeGridLongLat(Dictionary<string, Dictionary<GridLongLat, string>> rectAllDic, Dictionary<GridLongLat, string> gridStatDic)
        {
            foreach (GridLongLat gll in gridStatDic.Keys)
            {
                foreach (string strRegion in rectAllDic.Keys)
                {
                    if (rectAllDic[strRegion].ContainsKey(gll))
                        rectAllDic[strRegion].Remove(gll);
                }
            }
            return rectAllDic;
        }

        /**
        /// <summary>
        /// 获取小区覆盖区域子集
        /// </summary>
        private Dictionary<GridLongLat, string> getSubGridLongLat(Dictionary<GridLongLat, string> rectAllDic,GridLongLat extGll)
        {
            Dictionary<GridLongLat, string> rectSubDic = new Dictionary<GridLongLat, string>();
            foreach (GridLongLat gll in rectAllDic.Keys)
            {
                if (gll.fbrlongitude < extGll.fltlongitude || gll.fltlongitude > extGll.fbrlongitude ||
                    gll.fltlatitude < extGll.fbrlatitude || gll.fbrlatitude > extGll.fltlatitude)
                    continue;

                rectSubDic.Add(gll, rectAllDic[gll]);
            }
            return rectSubDic;
        }
        */

        /// <summary>
        /// 获取小区覆盖区域子集
        /// </summary>
        private Dictionary<GridLongLat, string> getSubGridLongLat(Dictionary<string,Dictionary<GridLongLat, string>> rectAllDic, GridLongLat extGll)
        {
            Dictionary<GridLongLat, string> rectSubDic = new Dictionary<GridLongLat, string>();
            foreach (string strRegion in rectAllDic.Keys)
            {
                foreach (GridLongLat gll in rectAllDic[strRegion].Keys)
                {
                    if (gll.fbrlongitude < extGll.fltlongitude || gll.fltlongitude > extGll.fbrlongitude ||
                        gll.fltlatitude < extGll.fbrlatitude || gll.fbrlatitude > extGll.fltlatitude)
                        continue;

                    if (!rectSubDic.ContainsKey(gll))
                        rectSubDic.Add(gll, strRegion);
                }
            }
            return rectSubDic;
        }

        private void fireShowResultForm()
        {
            GSMCellEmulateCovMRForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(GSMCellEmulateCovMRForm).FullName) as GSMCellEmulateCovMRForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new GSMCellEmulateCovMRForm(MainModel);
            }
            frm.FillData(ccResultList);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
        }

        /// <summary>
        /// 计算最终统计结果
        /// </summary>
        private void CalculateResult()
        {
            ccResultList = new List<CellEmulateCovResult>();
            List<ResvRegion> resvRegions = MainModel.GetInstance().SearchGeometrys.SelectedResvRegions;//预存区域
            MapWinGIS.Shape curRegion = MainModel.SearchGeometrys.Region;

            //float regionSquare = ((float)((cfgSet.iLongitude / 100) * (cfgSet.iLongitude / 100))) / (1000 * 1000);
            
            foreach (string strRegion in rxlevGridNumDic.Keys)
            {
                int iTotalGrid = rxlevGridNumDic[strRegion].iRxlev70Num + rxlevGridNumDic[strRegion].iRxlev80Num + rxlevGridNumDic[strRegion].iRxlev85Num
                    + rxlevGridNumDic[strRegion].iRxlev90Num + rxlevGridNumDic[strRegion].iRxlev94Num + rxlevGridNumDic[strRegion].iRxlev95Num + rxlevGridNumDic[strRegion].iRxlevNonCoverNum;
                //float iTotalSquare = iTotalGrid * regionSquare;

                int iCoverGrid70 =rxlevGridNumDic[strRegion].iRxlev70Num;
                //float iCoverSquare70= rxlevGridNumDic[strRegion].iRxlev70Num*regionSquare;
                string fCoverRate70 = string.Format("{0}%", (100 * ((float)iCoverGrid70) / iTotalGrid).ToString("0.00"));
                
                int iCoverGrid80 =rxlevGridNumDic[strRegion].iRxlev80Num+iCoverGrid70;
                //float iCoverSquare80= rxlevGridNumDic[strRegion].iRxlev80Num*regionSquare+iCoverSquare70;
                string fCoverRate80 = string.Format("{0}%", (100 * ((float)iCoverGrid80) / iTotalGrid).ToString("0.00"));
                
                int iCoverGrid85 =rxlevGridNumDic[strRegion].iRxlev85Num+iCoverGrid80;
                //float iCoverSquare85= rxlevGridNumDic[strRegion].iRxlev85Num*regionSquare+iCoverSquare80;
                string fCoverRate85 = string.Format("{0}%", (100 * ((float)iCoverGrid85) / iTotalGrid).ToString("0.00"));
                
                int iCoverGrid90 =rxlevGridNumDic[strRegion].iRxlev90Num+iCoverGrid85;
                //float iCoverSquare90= rxlevGridNumDic[strRegion].iRxlev90Num*regionSquare+iCoverSquare85;
                string fCoverRate90 = string.Format("{0}%", (100 * ((float)iCoverGrid90) / iTotalGrid).ToString("0.00"));

                int iCoverGrid94 = rxlevGridNumDic[strRegion].iRxlev94Num + iCoverGrid90;
                //float iCoverSquare94= rxlevGridNumDic[strRegion].iRxlev94Num*regionSquare+iCoverSquare90;
                string fCoverRate94 = string.Format("{0}%", (100 * ((float)iCoverGrid94) / iTotalGrid).ToString("0.00"));

                int iCoverGrid95 = rxlevGridNumDic[strRegion].iRxlev95Num + iCoverGrid94;
                //float iCoverSquare95= rxlevGridNumDic[strRegion].iRxlev95Num*regionSquare+iCoverSquare94;
                string fCoverRate95 = string.Format("{0}%", (100 * ((float)iCoverGrid95) / iTotalGrid).ToString("0.00"));

                CellEmulateCovResult ret = new CellEmulateCovResult();
                ret.region = strRegion;
                ret.totalGrid = iTotalGrid;

                ret.coverGrid70 = iCoverGrid70;
                ret.coverGrid80 = iCoverGrid80;
                ret.coverGrid85 = iCoverGrid85;
                ret.coverGrid90 = iCoverGrid90;
                ret.coverGrid94 = iCoverGrid94;
                ret.coverGrid95 = iCoverGrid95;

                ret.coverRate70 = fCoverRate70;
                ret.coverRate80 = fCoverRate80;
                ret.coverRate85 = fCoverRate85;
                ret.coverRate90 = fCoverRate90;
                ret.coverRate94 = fCoverRate94;
                ret.coverRate95 = fCoverRate95;
                
                if (resvRegions != null && resvRegions.Count > 0)
                {
                    foreach (ResvRegion region in resvRegions)
                    {
                        double area = RegionAreaCalculator.CalculateArea(region.Shape);
                        if (region.RegionName == strRegion)
                        {
                            ret.totalSquare = area.ToString("0.00");
                            //ret.coverSquare = (area * ((float)iCoverGrid) / iTotalGrid).ToString("0.00");
                            ret.coverSquare70 = (area * ((float)iCoverGrid70) / iTotalGrid).ToString("0.00");
                            ret.coverSquare80 = (area * ((float)iCoverGrid80) / iTotalGrid).ToString("0.00");
                            ret.coverSquare85 = (area * ((float)iCoverGrid85) / iTotalGrid).ToString("0.00");
                            ret.coverSquare90 = (area * ((float)iCoverGrid90) / iTotalGrid).ToString("0.00");
                            ret.coverSquare94 = (area * ((float)iCoverGrid94) / iTotalGrid).ToString("0.00");
                            ret.coverSquare95 = (area * ((float)iCoverGrid95) / iTotalGrid).ToString("0.00");
                        }
                    }
                }
                else if (curRegion != null)
                {
                    double area = RegionAreaCalculator.CalculateArea(MainModel.GetInstance().SearchGeometrys.Region);
                    ret.totalSquare = area.ToString("0.00");
                    //ret.coverSquare = (area * ((float)iCoverGrid) / iTotalGrid).ToString("0.00");
                    ret.coverSquare70 = (area * ((float)iCoverGrid70) / iTotalGrid).ToString("0.00");
                    ret.coverSquare80 = (area * ((float)iCoverGrid80) / iTotalGrid).ToString("0.00");
                    ret.coverSquare85 = (area * ((float)iCoverGrid85) / iTotalGrid).ToString("0.00");
                    ret.coverSquare90 = (area * ((float)iCoverGrid90) / iTotalGrid).ToString("0.00");
                    ret.coverSquare94 = (area * ((float)iCoverGrid94) / iTotalGrid).ToString("0.00");
                    ret.coverSquare95 = (area * ((float)iCoverGrid95) / iTotalGrid).ToString("0.00");
                }

                ccResultList.Add(ret);
            }

            //foreach (string strRegion in gridLongLatDic.Keys)
            //{
            //    int iTotalGrid = gridLongLatDic[strRegion].Count;
            //    float iTotalSquare = iTotalGrid * regionSquare;

            //    int iCoverGrid = iTotalGrid;
            //    if (gridNonContainLongLatDic.ContainsKey(strRegion))
            //        iCoverGrid = iTotalGrid - gridNonContainLongLatDic[strRegion].Count;
            //    float iCoverSquare = iCoverGrid * regionSquare;
            //    string fCoverRate = string.Format("{0}%", (100 * ((float)iCoverGrid) / iTotalGrid).ToString("0.00"));

                

            //    CellEmulateCovResult ret = new CellEmulateCovResult();
            //    ret.region = strRegion;
            //    ret.totalGrid = iTotalGrid;
            //    ret.coverGrid = iCoverGrid;
            //    //ret.totalSquare = iTotalSquare.ToString("0.00");
            //    //ret.coverSquare = iCoverSquare.ToString("0.00");
            //    ret.coverRate = fCoverRate;

            //    if (resvRegions != null && resvRegions.Count > 0)
            //    {
            //        foreach (ResvRegion region in resvRegions)
            //        {
            //            double area = RegionAreaCalculator.CalculateArea(region.Shape);
            //            if (region.RegionName == strRegion)
            //            {
            //                ret.totalSquare = area.ToString("0.00");
            //                ret.coverSquare = (area * ((float)iCoverGrid) / iTotalGrid).ToString("0.00");
            //            }
            //        }
            //    }
            //    else if (gmt != null)
            //    {
            //        double area = RegionAreaCalculator.CalculateArea(MainModel.GetInstance().SearchGeometrys.Region);
            //        ret.totalSquare = area.ToString("0.00");
            //        ret.coverSquare = (area * ((float)iCoverGrid) / iTotalGrid).ToString("0.00");
            //    }

            //    ccResultList.Add(ret);
            //}
        }

        /**
        /// <summary>
        /// 导出EXCEL
        /// </summary>
        private void OutputExcel(CfgSettingItem cfgSet)
        {
            float regionSquare = ((float)((cfgSet.iLongitude / 100) * (cfgSet.iLongitude / 100))) / (1000 * 1000);
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            #region EXCEL-SHEET1列表构造
            cols.Add("区域名称");
            cols.Add("总栅格数");
            cols.Add("覆盖栅格数");
            cols.Add("总面积(平方公里)");
            cols.Add("覆盖面积(平方公里)");
            cols.Add("小区覆盖率(%)");
            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            foreach (string strRegion in gridLongLatDic.Keys)
            {
                int iTotalGrid = gridLongLatDic[strRegion].Count;
                float iTotalSquare = iTotalGrid * regionSquare;

                int iCoverGrid = iTotalGrid;
                if (gridNonContainLongLatDic.ContainsKey(strRegion))
                    iCoverGrid = iTotalGrid - gridNonContainLongLatDic[strRegion].Count;
                float iCoverSquare = iCoverGrid * regionSquare;
                string fCoverRate = string.Format("{0}%", (100 * ((float)iCoverGrid) / iTotalGrid).ToString("0.00"));

                NPOIRow nr2 = new NPOIRow();
                List<object> cols2 = new List<object>();
                cols2.Add(strRegion);
                cols2.Add(iTotalGrid);
                cols2.Add(iCoverGrid);
                cols2.Add(iTotalSquare.ToString("0.00"));
                cols2.Add(iCoverSquare.ToString("0.00"));
                cols2.Add(fCoverRate);
                nr2.cellValues = cols2;
                datas.Add(nr2);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("小区覆盖仿真统计");
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);

            WaitBox.Close();
        }
        */

        /// <summary>
        /// 通过数据库获取各类表信息
        /// </summary>
        private Dictionary<CellLaiKey, RxlevCoverDist> anaCellRxlevTa(CfgSettingItem cfgSet, string strCity)
        {
            WaitBox.Text = "[" + strCity + "] 获取小区MR数据...";
            WaitBox.ProgressPercent = 20;
            DiySqlGetCellRxlevTa sqlGetCellRxlevTa = new DiySqlGetCellRxlevTa(MainModel);
            sqlGetCellRxlevTa.SetQueryCondition(condition);
            sqlGetCellRxlevTa.setParam(cfgSet, strCity);
            sqlGetCellRxlevTa.Query();
            List<CellRxlevTaItem> cellRxlevTaList = sqlGetCellRxlevTa.cellRxlevTaList;
            WaitBox.ProgressPercent = 30;
            
            //计算每个小区的覆盖距离
            Dictionary<CellLaiKey, RxlevCoverDist> distDic = calcCellCoverDistance(cellRxlevTaList, cfgSet);
            return distDic;
        }

        /// <summary>
        /// 通过数据库获取各类表信息
        /// </summary>
        private Dictionary<GridLongLat, int> getTestGridRscp(CfgSettingItem cfgSet, string strCity)
        {
            WaitBox.Text = "[" + strCity + "] 获取测试栅格数据...";
            WaitBox.ProgressPercent = 30;
            DiySqlGetGsmGridRscp sqlGetGsmGridRscp = new DiySqlGetGsmGridRscp(MainModel);
            sqlGetGsmGridRscp.SetQueryCondition(condition);
            sqlGetGsmGridRscp.csItem = cfgSet;
            sqlGetGsmGridRscp.setParam(strCity);
            sqlGetGsmGridRscp.Query();
            Dictionary<GridLongLat, int> gridRscpDic = sqlGetGsmGridRscp.gridRscpDic;
            WaitBox.ProgressPercent = 35;
            return gridRscpDic;
        }

        /// <summary>
        /// 通过数据库获取Abis中的MR
        /// </summary>
        private Dictionary<CellLaiKey, RxlevCoverDist> getAbisMrRxlev(CfgSettingItem cfgSet, string strCity)
        {
            WaitBox.Text = "[" + strCity + "] 获取Abis中MR数据...";
            WaitBox.ProgressPercent = 10;
            DiySqlGetGsmAbisMrRxlev sqlGetGsmAbisMr = new DiySqlGetGsmAbisMrRxlev(MainModel);
            sqlGetGsmAbisMr.SetQueryCondition(condition);
            sqlGetGsmAbisMr.csItem = cfgSet;
            sqlGetGsmAbisMr.setParam(strCity);
            sqlGetGsmAbisMr.Query();
            Dictionary<CellLaiKey, AbisMrItem> abisMrDic = sqlGetGsmAbisMr.abisMrDic;
            Dictionary<CellLaiKey, RxlevCoverDist> rxlevDistDic = new Dictionary<CellLaiKey, RxlevCoverDist>();
            foreach (CellLaiKey cellKey in abisMrDic.Keys)
            {
                AbisMrItem abisMr = abisMrDic[cellKey];
                int[] abisMrArray = abisMr.getArray();
                RxlevCoverDist rxlevDist = new RxlevCoverDist();
                int iRxlev = 0;
                for (int i = 0; i < abisMrArray.Length; i++)
                {
                    if (iRxlev > abisMrArray[i])
                    {
                        iRxlev = abisMrArray[i];
                    }
                    if (abisMrArray[i] != 0)
                    {
                        calcDistByAbisMr(iRxlev, abisMrArray[i], i, ref rxlevDist,cfgSet);
                    }
                }
                rxlevDistDic.Add(cellKey, rxlevDist);
            }
            WaitBox.ProgressPercent = 20;
            return rxlevDistDic;
        }

        /// <summary>
        /// 通过ABIS的MR数据计算覆盖距离
        /// </summary>
        private void calcDistByAbisMr(int iRxlev, int iTaRxlev, int iTaValue, ref RxlevCoverDist rxlevDist, CfgSettingItem cfgSet)
        {
            if (iTaRxlev >= -70 && iRxlev >= -70 && cfgSet.iRxlev<= -70)
            {
                rxlevDist.Rxlev70CoverDist = (iTaValue + 1) * 550;
            }
            if (iTaRxlev >= -80 && iRxlev >= -80 && cfgSet.iRxlev <= -80)
            {
                rxlevDist.Rxlev80CoverDist = (iTaValue + 1) * 550;
            }
            if (iTaRxlev >= -85 && iRxlev >= -85 && cfgSet.iRxlev <= -85)
            {
                rxlevDist.Rxlev85CoverDist = (iTaValue + 1) * 550;
            }
            if (iTaRxlev >= -90 && iRxlev >= -90 && cfgSet.iRxlev <= -90)
            {
                rxlevDist.Rxlev90CoverDist = (iTaValue + 1) * 550;
            }
            if (iTaRxlev >= -94 && iRxlev >= -94 && cfgSet.iRxlev <= -94)
            {
                rxlevDist.Rxlev94CoverDist = (iTaValue + 1) * 550;
            }
            if (iTaRxlev >= -95 && iRxlev >= -95 && cfgSet.iRxlev <= -95)
            {
                rxlevDist.Rxlev95CoverDist = (iTaValue + 1) * 550;
            }
        }

        /// <summary>
        /// 生成一个矩形shape
        /// </summary>
        public static MapWinGIS.Shape CreateRectShape(List<LongLat> longLatList, ref GridLongLat gll)
        {
            //左上角开始，顺时针InsertPoint
            MapWinGIS.Shape shp = new MapWinGIS.Shape();
            shp.Create(ShpfileType.SHP_POLYGON);
            int idx = 0;
            foreach (LongLat ll in longLatList)
            {
                MapWinGIS.Point pnt = new MapWinGIS.Point();
                pnt.x = ll.fLongitude;//左上角
                pnt.y = ll.fLatitude;
                shp.InsertPoint(pnt, ref idx);
                idx++;

                gll.updateValue(ll);
            }
            return shp;
        }

        /// <summary>
        /// 小区覆盖仿真
        /// </summary>
        public static List<LongLat> getCellEmulateCover(LongLat btsLongLat, int iangle_dir, int coverDistance)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();

            int sDir = iangle_dir - 60;
            int eDir = iangle_dir + 60;
            for (int i = sDir; i <= eDir; i += 5)
            {
                LongLat tLongLat = calcPointX(i, coverDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }

        /// <summary>
        /// 计算每个小区的覆盖距离
        /// </summary>
        private Dictionary<CellLaiKey, RxlevCoverDist> calcCellCoverDistance(List<CellRxlevTaItem> cellRxlevTaList, CfgSettingItem cfgSet)
        {
            Dictionary<CellLaiKey, RxlevCoverDist> cellDistDic = new Dictionary<CellLaiKey, RxlevCoverDist>();
            foreach (CellRxlevTaItem crtItem in cellRxlevTaList)
            {
                int[] taArray = crtItem.convertArray();
                CellLaiKey clKey = new CellLaiKey();
                clKey.ILac = crtItem.lac;
                clKey.ICi = crtItem.ci;
                RxlevCoverDist rcDist = new RxlevCoverDist();
                float fCoverRate = 0;

                if (cfgSet.iRxlev <= -70)
                {
                    fCoverRate = ((float)crtItem.rxlev_ge_70) / crtItem.rxlev_total;
                    rcDist.Rxlev70CoverDist = calcTaDist(taArray, crtItem.ta_total, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -80)
                {
                    fCoverRate = ((float)crtItem.rxlev_ge_80) / crtItem.rxlev_total;
                    rcDist.Rxlev80CoverDist = calcTaDist(taArray, crtItem.ta_total, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -85)
                {
                    fCoverRate = ((float)crtItem.rxlev_ge_85) / crtItem.rxlev_total;
                    rcDist.Rxlev85CoverDist = calcTaDist(taArray, crtItem.ta_total, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -90)
                {
                    fCoverRate = ((float)crtItem.rxlev_ge_90) / crtItem.rxlev_total;
                    rcDist.Rxlev90CoverDist = calcTaDist(taArray, crtItem.ta_total, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -94)
                {
                    fCoverRate = ((float)crtItem.rxlev_ge_94) / crtItem.rxlev_total;
                    rcDist.Rxlev94CoverDist = calcTaDist(taArray, crtItem.ta_total, cfgSet, fCoverRate);
                }
                if (cfgSet.iRxlev <= -95)
                {
                    fCoverRate = ((float)crtItem.rxlev_ge_95) / crtItem.rxlev_total;
                    rcDist.Rxlev95CoverDist = calcTaDist(taArray, crtItem.ta_total, cfgSet, fCoverRate);
                }
                cellDistDic.Add(clKey, rcDist);
            }
            return cellDistDic;
        }

        /// <summary>
        /// 计算TA距离
        /// </summary>
        private int calcTaDist(int[] taArray, int ta_total, CfgSettingItem cfgSet, float fCoverRate)
        {
            if (ta_total == 0)
                return 0;
            int iArrayLength = taArray.Length;
            int iDist = 0;
            int idx = 0;
            int iMaxTa = judgeRepeaterBts(taArray, cfgSet.iRepeaterStatus);

            for (int i = 1; i <= iArrayLength - 1; i++)
            {
                float fTaRate1 = ((float)taArray[i - 1]) / ta_total;
                float fTaRate2 = ((float)taArray[i]) / ta_total;

                if ((i == 1 && fTaRate1 >= fCoverRate))
                {
                    idx = 0;
                    break;
                }
                else if ((fTaRate1 < fCoverRate && fTaRate2 >= fCoverRate) || (iMaxTa == i - 1))
                {
                    idx = i - 1;
                    break;
                }
            }

            if (idx < 0)
            {
                idx = 0;
            }

            if (cfgSet.isTaDownWard)
            {
                iDist = (idx + 1) * 550;
            }
            else
            {
                iDist = (idx + 2) * 550;
            }
            return iDist;
        }

        /// <summary>
        /// 过滤直放站算法
        /// </summary>
        /// <param name="iCfg">1.仅保存正常TA;2.不过滤直放站;3.截断空值TA</param>
        /// <returns>返回TA值</returns>
        private int judgeRepeaterBts(int[] taArray, int iCfg)
        {
            int iTaNormal = 0; //正常覆盖TA值
            int iTaNull = 0;   //真空TA值
            int iTaExtend = taArray.Length;//第二段TA值
            int iRepeaterStatus = 0;     //直放站状态

            for (int i = 0; i <= 9; i++)
            {
                int j = i + 3;
                if (j > taArray.Length - 1)
                {
                    j = taArray.Length - 1;
                }
                if ((taArray[j] - taArray[i] == 0 || taArray[2] == 0) && iRepeaterStatus == 0)
                {
                    iTaNormal = i;
                    iTaNull = j - iTaNormal;
                    iRepeaterStatus = 1;
                }
                else if (taArray[j] - taArray[i] != 0 && iRepeaterStatus == 1)
                {
                    iTaNull = j - iTaNormal;
                    iRepeaterStatus = 2;
                }
                else if (taArray[j] - taArray[i] == 0 && iRepeaterStatus == 2)
                {
                    iTaExtend = j - (iTaNull + iTaNormal);
                    iRepeaterStatus = 3;
                }
            }

            if (iRepeaterStatus == 0)//未发现直放站，且连续不为0
            {
                iTaNormal = 9;
            }
            else if (iRepeaterStatus == 1)//未发现直放站，存在连续为0情况
            {
                iTaExtend = 0;
            }
            else if (iRepeaterStatus == 2)//发现直放站情况
            {
                iTaExtend = taArray.Length - (iTaNull + iTaNormal);
            }

            int iResult = judgeRes(taArray, iCfg, iTaNormal, iTaExtend);
            return iResult;
        }

        private int judgeRes(int[] taArray, int iCfg, int iTaNormal, int iTaExtend)
        {
            int iResult = 0;
            if (iCfg == 1)
            {
                iResult = iTaNormal;
            }
            else if (iCfg == 2)
            {
                iResult = taArray.Length;//数组长度为11，会被置为9
            }
            else if (iCfg == 3)
            {
                iResult = iTaNormal + iTaExtend;
            }
            if (iResult > 9)
                iResult = 9;
            return iResult;
        }

        /**
        /// <summary>
        /// 计算TA距离(旧方法)
        /// </summary>
        private int calcTaDist(CellRxlevTaItem crtItem, CfgSettingItem cfgSet, float fCoverRate)
        {
            if (crtItem.ta_total == 0)
                return 0;

            int iMaxTa = judgeRepeaterBts(crtItem);
            float fTaRate1 = ((float)crtItem.ta_le_0) / crtItem.ta_total;
            float fTaRate2 = ((float)crtItem.ta_le_1) / crtItem.ta_total;
            int iDist = 550;

            //0
            if (fTaRate1 >= fCoverRate)
                return iDist;

            if (!cfgSet.isTaDownWard)
                iDist += 550;

            //0~1
            if (fTaRate2 >= fCoverRate || iMaxTa < 1)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_2) / crtItem.ta_total;
                iDist += 550;
            }
            //1~2
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 2)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_3) / crtItem.ta_total;
                iDist += 550;
            }
            //2~3
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 3)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_4) / crtItem.ta_total;
                iDist += 550;
            }
            //3~4
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 4)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_5) / crtItem.ta_total;
                iDist += 550;
            }
            //4~5
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 5)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_6) / crtItem.ta_total;
                iDist += 550;
            }
            //5~6
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 6)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_7) / crtItem.ta_total;
                iDist += 550;
            }
            //6~7
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 7)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_8) / crtItem.ta_total;
                iDist += 550;
            }
            //7~8
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 8)
                return iDist;
            else
            {
                fTaRate1 = fTaRate2;
                fTaRate2 = ((float)crtItem.ta_le_9) / crtItem.ta_total;
                iDist += 550;
            }
            //8~9
            if ((fTaRate1 <= fCoverRate && fTaRate2 >= fCoverRate) || iMaxTa < 9)
                return iDist;
            else
                iDist += 550;
            return iDist;
        }

        /// <summary>
        /// 过滤直放站算法(旧方法)
        /// </summary>
        /// <returns>返回实际TA值</returns>
        private int judgeRepeaterBts(CellRxlevTaItem crtItem)
        {
            int iTa = 9;
            if (crtItem.ta_le_2 == 0 || crtItem.ta_le_3 - crtItem.ta_le_0 == 0)
            {
                iTa = 0;
            }
            else if (crtItem.ta_le_4 - crtItem.ta_le_1 == 0)
            {
                iTa = 1;
            }
            else if (crtItem.ta_le_5 - crtItem.ta_le_2 == 0)
            {
                iTa = 2;
            }
            else if (crtItem.ta_le_6 - crtItem.ta_le_3 == 0)
            {
                iTa = 3;
            }
            else if (crtItem.ta_le_7 - crtItem.ta_le_4 == 0)
            {
                iTa = 4;
            }
            else if (crtItem.ta_le_8 - crtItem.ta_le_5 == 0)
            {
                iTa = 5;
            }
            else if (crtItem.ta_le_9 - crtItem.ta_le_6 == 0)
            {
                iTa = 6;
            }
            else if (crtItem.ta_le_10 - crtItem.ta_le_7 == 0)
            {
                iTa = 7;
            }
            else if (crtItem.ta_le_10 - crtItem.ta_le_8 == 0)
            {
                iTa = 8;
            }
            else if (crtItem.ta_le_10 - crtItem.ta_le_9 == 0)
            {
                iTa = 9;
            }
            else if (crtItem.ta_le_10 == 0)
            {
                iTa = 10;
            }
            return iTa;
        }
        */

        /// <summary>
        /// 粗略定位另一点信息
        /// </summary>
        /// <param name="iangle">夹角</param>
        public static LongLat calcPointX(int iangle, float idis, LongLat s)
        {
            LongLat e = new LongLat();
            double a = Math.Cos((90 - iangle) * 2 * Math.PI / 360);
            double b = Math.Sin((90 - iangle) * 2 * Math.PI / 360);
            e.fLongitude = s.fLongitude + (float)(a * (idis / 40075360) * 360);//32507969.15
            e.fLatitude = s.fLatitude + (float)(b * (idis / 39940670) * 360); //40172187.93
            return e;
        }

        /**
        /// <summary>
        /// 获取采样点与小区的夹角
        /// </summary>
        /// <param name="itype">0为相对夹角，1为相对夹角(绝对值)</param>
        private int calcSampleAngle(Cell cell, double longitude, double latitude, int itype)
        {
            //所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);

            double angle;
            double ygap = cell.GetDistance(cell.Longitude, latitude);
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            if (itype == 0)
            {
                angleDiff = angle - cell.Direction;
                if (angleDiff < 0)
                {
                    angleDiff = 360 + angleDiff;
                }
            }
            else
            {
                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
            }

            return (int)angleDiff;
        }
        */
    }

    public class CfgSettingItem
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public int istime{ get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public int ietime{ get; set; }
        /// <summary>
        /// 经度粒度
        /// </summary>
        public int iLongitude{ get; set; }
        /// <summary>
        /// 纬度粒度
        /// </summary>
        public int iLatitude{ get; set; }
        /// <summary>
        /// 电平值
        /// </summary>
        public int iRxlev{ get; set; }
        /// <summary>
        /// TA是否取小值
        /// </summary>
        public bool isTaDownWard{ get; set; }
        /// <summary>
        /// 测试电平：测试栅格电平值，1最大，2平均，3最小
        /// </summary>
        public int iTestValue{ get; set; }
        /// <summary>
        /// ABIS-MR数据：若需要添加ABIS中的MR则置为TRUE
        /// </summary>
        public bool isAddAbisMr{ get; set; }
        /// <summary>
        /// 1.仅保存正常TA{ get; set; }2.不过滤直放站{ get; set; }3.截断空值TA
        /// </summary>
        public int iRepeaterStatus{ get; set; }

        public CfgSettingItem()
        {
            iTestValue = 1;
            isAddAbisMr = false;
            iRepeaterStatus = 3;
        }
    }

    public class CellEmulateShowItem
    {
        public CellEmulateShowItem()
        {
            mapCellEmulateNeedFreshImg = false;
            exMapCellEmulateNeedFreshImg = false;

            rxlev70Color = Color.Lime;
            rxlev80Color = Color.Green;
            rxlev85Color = Color.Yellow;
            rxlev90Color = Color.Orange;
            rxlev94Color = Color.Purple;
            rxlev95Color = Color.Purple;
            rxlevNonCovColor = Color.Red;
        }

        public bool mapCellEmulateNeedFreshImg { get; set; }
        public bool exMapCellEmulateNeedFreshImg { get; set; }
        public double gridSpanLon{ get; set; }
        public double gridSpanLat { get; set; }
        /// <summary>
        /// 显示前N个电平的门限栅格数据结果，如等于2则显示70，80电平覆盖率
        /// </summary>
        public int visibleRxlevTopN { get; set; }

        public Color rxlev70Color { get; set; }
        public Color rxlev80Color { get; set; }
        public Color rxlev85Color { get; set; }
        public Color rxlev90Color { get; set; }
        public Color rxlev94Color { get; set; }
        public Color rxlev95Color { get; set; }
        public Color rxlevNonCovColor { get; set; }
    }

    public class CellRxlevTaItem
    {
        public int lac{ get; set; }
        public int ci{ get; set; }
        public int rxlev_total{ get; set; }
        public int rxlev_ge_70{ get; set; }
        public int rxlev_ge_80{ get; set; }
        public int rxlev_ge_85{ get; set; }
        public int rxlev_ge_90{ get; set; }
        public int rxlev_ge_94{ get; set; }
        public int rxlev_ge_95{ get; set; }
        public int ta_total{ get; set; }
        public int ta_le_0{ get; set; }
        public int ta_le_1{ get; set; }
        public int ta_le_2{ get; set; }
        public int ta_le_3{ get; set; }
        public int ta_le_4{ get; set; }
        public int ta_le_5{ get; set; }
        public int ta_le_6{ get; set; }
        public int ta_le_7{ get; set; }
        public int ta_le_8{ get; set; }
        public int ta_le_9{ get; set; }
        public int ta_le_10{ get; set; }

        public int[] convertArray()
        {
            int[] taArray = new int[11];
            taArray[0] = ta_le_0;
            taArray[1] = ta_le_1;
            taArray[2] = ta_le_2;
            taArray[3] = ta_le_3;
            taArray[4] = ta_le_4;
            taArray[5] = ta_le_5;
            taArray[6] = ta_le_6;
            taArray[7] = ta_le_7;
            taArray[8] = ta_le_8;
            taArray[9] = ta_le_9;
            taArray[10] = ta_le_10;
            return taArray;
        }
    }

    public class GridLongLat 
    {
        public float fltlongitude { get; set; }
        public float fltlatitude { get; set; }
        public float fbrlongitude { get; set; }
        public float fbrlatitude { get; set; }

        public static DbRect cvtRect(GridLongLat gll)
        {
            DbRect rect = new DbRect();
            rect.x1 = gll.fltlongitude;
            rect.x2 = gll.fbrlongitude;
            rect.y1 = gll.fbrlatitude;
            rect.y2 = gll.fltlatitude;
            return rect;
        }

        public static GridLongLat cvtLongLat(DbRect rect)
        {
            GridLongLat gll = new GridLongLat();
            gll.fltlongitude = (float)rect.x1;
            gll.fbrlongitude = (float)rect.x2;
            gll.fbrlatitude = (float)rect.y1;
            gll.fltlatitude = (float)rect.y2;
            return gll;
        }

        public static GridLongLat cvtLongLat(LongLat ll)
        {
            GridLongLat gll = new GridLongLat();
            gll.fltlongitude = ll.fLongitude;
            gll.fbrlongitude = ll.fLongitude;
            gll.fbrlatitude = ll.fLatitude;
            gll.fltlatitude = ll.fLatitude;
            return gll;
        }

        public static GridLongLat getGridByCenterLongLat(LongLat centerLongLat)
        {
            int iLong = ((int)(centerLongLat.fLongitude * 10000000))/4000*4000;
            int iLat = ((int)(centerLongLat.fLatitude * 10000000))/3600*3600;

            GridLongLat gll = new GridLongLat();
            gll.fltlongitude = (float)(iLong * 1.0 / 10000000);
            gll.fltlatitude = (float)((iLat + 3600) * 1.0 / 10000000);
            gll.fbrlongitude = (float)((iLong+4000) * 1.0 / 10000000);
            gll.fbrlatitude = (float)(iLat * 1.0 / 10000000);
            return gll;
        }

        public LongLat calcCenterLongLat()
        {
            LongLat cll = new LongLat();
            cll.fLongitude = fltlongitude / 2 + fbrlongitude / 2;
            cll.fLatitude = fltlatitude / 2 + fbrlatitude / 2;
            return cll;
        }

        public bool compareValue(DbRect rect)
        {
            if (fltlongitude > rect.x2 || fbrlongitude < rect.x1 ||
                fltlatitude < rect.y1 || fbrlatitude > rect.y2)
                return false;

            return true;
        }

        /// <summary>
        /// 获取List<LongLat>的左上右下经纬度
        /// </summary>
        public void updateValue(LongLat ll)
        {
            if (this.fltlongitude > ll.fLongitude)
                this.fltlongitude = ll.fLongitude;
            if (this.fbrlongitude < ll.fLongitude)
                this.fbrlongitude = ll.fLongitude;
            if (this.fltlatitude < ll.fLatitude)
                this.fltlatitude = ll.fLatitude;
            if (this.fbrlatitude > ll.fLatitude)
                this.fbrlatitude = ll.fLatitude;
        }

        /// <summary>
        /// 4KM栅格化
        /// </summary>
        public GridLongLat extendGrid()
        {
            GridLongLat gll = new GridLongLat();
            int minLong = (int)(this.fltlongitude * 10000000) / 400000 * 400000;
            int maxLong = (int)(this.fbrlongitude * 10000000) / 400000 * 400000 + 400000;
            int minLat = (int)(this.fbrlatitude * 10000000) / 360000 * 360000;
            int maxLat = (int)(this.fltlatitude * 10000000) / 360000 * 360000 + 360000;
            gll.fltlongitude = ((float)minLong) / 10000000;
            gll.fltlatitude = ((float)maxLat) / 10000000;
            gll.fbrlongitude = ((float)maxLong) / 10000000;
            gll.fbrlatitude = ((float)minLat) / 10000000;
            return gll;
        }

        public override bool Equals(object obj)
        {
            GridLongLat other = obj as GridLongLat;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.fltlongitude.Equals(other.fltlongitude) &&
                    this.fltlatitude.Equals(other.fltlatitude) &&
                    this.fbrlongitude.Equals(other.fbrlongitude) &&
                    this.fbrlatitude.Equals(other.fbrlatitude));
        }

        public override int GetHashCode()
        {
            int iKey = (int)(fltlongitude * 1000000 + fltlatitude * 1000000);
            return iKey.GetHashCode();
        }
    }

    public class CellEmulateCovResult
    {
        public string region{ get; set; }
        public int totalGrid{ get; set; }
        public string totalSquare{ get; set; }
        public int coverGrid{ get; set; }
        public string coverSquare{ get; set; }
        public string coverRate{ get; set; }

        public int coverGrid70{ get; set; }
        public int coverGrid80{ get; set; }
        public int coverGrid85{ get; set; }
        public int coverGrid90{ get; set; }
        public int coverGrid94{ get; set; }
        public int coverGrid95{ get; set; }

        public string coverSquare70{ get; set; }
        public string coverSquare80{ get; set; }
        public string coverSquare85{ get; set; }
        public string coverSquare90{ get; set; }
        public string coverSquare94{ get; set; }
        public string coverSquare95{ get; set; }

        public string coverRate70{ get; set; }
        public string coverRate80{ get; set; }
        public string coverRate85{ get; set; }
        public string coverRate90{ get; set; }
        public string coverRate94{ get; set; }
        public string coverRate95{ get; set; }
    }

    public class RxlevCoverDist
    {
        /// <summary>
        /// 70覆盖率距离
        /// </summary>
        public int Rxlev70CoverDist{ get; set; }
        /// <summary>
        /// 80覆盖率距离
        /// </summary>
        public int Rxlev80CoverDist{ get; set; }
        /// <summary>
        /// 85覆盖率距离
        /// </summary>
        public int Rxlev85CoverDist{ get; set; }
        /// <summary>
        /// 90覆盖率距离
        /// </summary>
        public int Rxlev90CoverDist{ get; set; }
        /// <summary>
        /// 94覆盖率距离
        /// </summary>
        public int Rxlev94CoverDist{ get; set; }
        /// <summary>
        /// 95覆盖率距离
        /// </summary>
        public int Rxlev95CoverDist{ get; set; }

        public RxlevCoverDist()
        {
            Rxlev70CoverDist = 0;
            Rxlev80CoverDist = 0;
            Rxlev85CoverDist = 0;
            Rxlev90CoverDist = 0;
            Rxlev94CoverDist = 0;
            Rxlev95CoverDist = 0;
        }
    }

    public class RxlevLongLat
    {
        public RxlevLongLat()
        {
            Rxlev70LongLatList = new List<LongLat>();
            Rxlev80LongLatList = new List<LongLat>();
            Rxlev85LongLatList = new List<LongLat>();
            Rxlev90LongLatList = new List<LongLat>();
            Rxlev94LongLatList = new List<LongLat>();
            Rxlev95LongLatList = new List<LongLat>();
        }

        public List<LongLat> Rxlev70LongLatList { get; set; }
        public List<LongLat> Rxlev80LongLatList { get; set; }
        public List<LongLat> Rxlev85LongLatList { get; set; }
        public List<LongLat> Rxlev90LongLatList { get; set; }
        public List<LongLat> Rxlev94LongLatList { get; set; }
        public List<LongLat> Rxlev95LongLatList { get; set; }

        /// <summary>
        /// 小区经纬度描绘
        /// </summary>
        public void fillCellLongLat(Cell cell, RxlevCoverDist rxlevCoverDist)
        {
            LongLat ll = new LongLat();
            ll.fLongitude = (float)(cell.Longitude);
            ll.fLatitude = (float)(cell.Latitude);

            if (rxlevCoverDist.Rxlev70CoverDist > 0)
            {
                Rxlev70LongLatList.Add(ll);
                Rxlev70LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev70CoverDist));
            }
            if (rxlevCoverDist.Rxlev80CoverDist > 0)
            {
                Rxlev80LongLatList.Add(ll);
                Rxlev80LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev80CoverDist));
            }
            if (rxlevCoverDist.Rxlev85CoverDist > 0)
            {
                Rxlev85LongLatList.Add(ll);
                Rxlev85LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev85CoverDist));
            }
            if (rxlevCoverDist.Rxlev90CoverDist > 0)
            {
                Rxlev90LongLatList.Add(ll);
                Rxlev90LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev90CoverDist));
            }
            if (rxlevCoverDist.Rxlev94CoverDist > 0)
            {
                Rxlev94LongLatList.Add(ll);
                Rxlev94LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev94CoverDist));
            }
            if (rxlevCoverDist.Rxlev95CoverDist > 0)
            {
                Rxlev95LongLatList.Add(ll);
                Rxlev95LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev95CoverDist));
            }
        }

        /// <summary>
        /// TD小区经纬度描绘
        /// </summary>
        public void fillCellLongLat(TDCell cell, RxlevCoverDist rxlevCoverDist)
        {
            LongLat ll = new LongLat();
            ll.fLongitude = (float)(cell.Longitude);
            ll.fLatitude = (float)(cell.Latitude);

            if (rxlevCoverDist.Rxlev70CoverDist > 0)
            {
                Rxlev70LongLatList.Add(ll);
                Rxlev70LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev70CoverDist));
            }
            if (rxlevCoverDist.Rxlev80CoverDist > 0)
            {
                Rxlev80LongLatList.Add(ll);
                Rxlev80LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev80CoverDist));
            }
            if (rxlevCoverDist.Rxlev85CoverDist > 0)
            {
                Rxlev85LongLatList.Add(ll);
                Rxlev85LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev85CoverDist));
            }
            if (rxlevCoverDist.Rxlev90CoverDist > 0)
            {
                Rxlev90LongLatList.Add(ll);
                Rxlev90LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev90CoverDist));
            }
            if (rxlevCoverDist.Rxlev94CoverDist > 0)
            {
                Rxlev94LongLatList.Add(ll);
                Rxlev94LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev94CoverDist));
            }
            if (rxlevCoverDist.Rxlev95CoverDist > 0)
            {
                Rxlev95LongLatList.Add(ll);
                Rxlev95LongLatList.AddRange(ZTGSMCellEmulateCoverAnaByMR.getCellEmulateCover(ll, (int)(cell.Direction), rxlevCoverDist.Rxlev95CoverDist));
            }
        }
    }

    public class RxlevShape
    {
        public RxlevShape()
        {
            Rxlev70ShapeList = new List<MapWinGIS.Shape>();
            Rxlev80ShapeList = new List<MapWinGIS.Shape>();
            Rxlev85ShapeList = new List<MapWinGIS.Shape>();
            Rxlev90ShapeList = new List<MapWinGIS.Shape>();
            Rxlev94ShapeList = new List<MapWinGIS.Shape>();
            Rxlev95ShapeList = new List<MapWinGIS.Shape>();
        }

        public List<MapWinGIS.Shape> Rxlev70ShapeList { get; set; }
        public List<MapWinGIS.Shape> Rxlev80ShapeList { get; set; }
        public List<MapWinGIS.Shape> Rxlev85ShapeList { get; set; }
        public List<MapWinGIS.Shape> Rxlev90ShapeList { get; set; }
        public List<MapWinGIS.Shape> Rxlev94ShapeList { get; set; }
        public List<MapWinGIS.Shape> Rxlev95ShapeList { get; set; }

        /// <summary>
        /// 绘制区域
        /// </summary>
        public static void drawShape(RxlevLongLat longLatList, ref Dictionary<GridLongLat, RxlevShape> winShapeDic)
        {
            GridLongLat gll = new GridLongLat();
            MapWinGIS.Shape win70Shape = null;
            MapWinGIS.Shape win80Shape = null;
            MapWinGIS.Shape win85Shape = null;
            MapWinGIS.Shape win90Shape = null;
            MapWinGIS.Shape win94Shape = null;
            MapWinGIS.Shape win95Shape = null;

            if (longLatList.Rxlev95LongLatList.Count != 0)
            {
                GridLongLat gll95 = GridLongLat.cvtLongLat(longLatList.Rxlev95LongLatList[0]);
                win95Shape = ZTGSMCellEmulateCoverAnaByMR.CreateRectShape(longLatList.Rxlev95LongLatList, ref gll95);
                getMaxValue(ref gll, gll95);
            }
            if (longLatList.Rxlev94LongLatList.Count != 0)
            {
                GridLongLat gll94 = GridLongLat.cvtLongLat(longLatList.Rxlev94LongLatList[0]);
                win94Shape = ZTGSMCellEmulateCoverAnaByMR.CreateRectShape(longLatList.Rxlev94LongLatList, ref gll94);
                getMaxValue(ref gll, gll94);
            }
            if (longLatList.Rxlev90LongLatList.Count != 0)
            {
                GridLongLat gll90 = GridLongLat.cvtLongLat(longLatList.Rxlev90LongLatList[0]);
                win90Shape = ZTGSMCellEmulateCoverAnaByMR.CreateRectShape(longLatList.Rxlev90LongLatList, ref gll90);
                getMaxValue(ref gll, gll90);
            }
            if (longLatList.Rxlev85LongLatList.Count != 0)
            {
                GridLongLat gll85 = GridLongLat.cvtLongLat(longLatList.Rxlev85LongLatList[0]);
                win85Shape = ZTGSMCellEmulateCoverAnaByMR.CreateRectShape(longLatList.Rxlev85LongLatList, ref gll85);
                getMaxValue(ref gll, gll85);
            }
            if (longLatList.Rxlev80LongLatList.Count != 0)
            {
                GridLongLat gll80 = GridLongLat.cvtLongLat(longLatList.Rxlev80LongLatList[0]);
                win80Shape = ZTGSMCellEmulateCoverAnaByMR.CreateRectShape(longLatList.Rxlev80LongLatList, ref gll80);
                getMaxValue(ref gll, gll80);
            }
            if (longLatList.Rxlev70LongLatList.Count != 0)
            {
                GridLongLat gll70 = GridLongLat.cvtLongLat(longLatList.Rxlev70LongLatList[0]);
                win70Shape = ZTGSMCellEmulateCoverAnaByMR.CreateRectShape(longLatList.Rxlev70LongLatList, ref gll70);
                getMaxValue(ref gll, gll70);
            }

            GridLongLat extGll = gll.extendGrid();
            if (winShapeDic.ContainsKey(extGll))
            {
                RxlevShape rxlevShape = winShapeDic[extGll];
                fillRxlevShape(win70Shape,win80Shape, win85Shape, win90Shape, win94Shape, win95Shape, ref rxlevShape);
            }
            else
            {
                RxlevShape rxlevShape = new RxlevShape();
                fillRxlevShape(win70Shape,win80Shape, win85Shape, win90Shape, win94Shape, win95Shape, ref rxlevShape);
                winShapeDic.Add(extGll, rxlevShape);
            }
        }

        /// <summary>
        /// RxlevShape赋值
        /// </summary>
        private static void fillRxlevShape(MapWinGIS.Shape win70Shape,MapWinGIS.Shape win80Shape,MapWinGIS.Shape win85Shape,
                                           MapWinGIS.Shape win90Shape,MapWinGIS.Shape win94Shape, MapWinGIS.Shape win95Shape,ref RxlevShape rxlevShape)
        {
            if (win70Shape != null)
                rxlevShape.Rxlev70ShapeList.Add(win70Shape);
            if (win80Shape != null)
                rxlevShape.Rxlev80ShapeList.Add(win80Shape);
            if (win85Shape != null)
                rxlevShape.Rxlev85ShapeList.Add(win85Shape);
            if (win90Shape != null)
                rxlevShape.Rxlev90ShapeList.Add(win90Shape);
            if (win94Shape != null)
                rxlevShape.Rxlev94ShapeList.Add(win94Shape);
            if (win95Shape != null)
                rxlevShape.Rxlev95ShapeList.Add(win95Shape);
        }

        /// <summary>
        /// 获取List的左上右下经纬度
        /// </summary>
        private static void getMaxValue(ref GridLongLat gll, GridLongLat tmpll)
        {
            if (gll == null || gll.fltlongitude == 0)
                gll = tmpll;

            if (gll.fltlongitude > tmpll.fltlongitude)
                gll.fltlongitude = tmpll.fltlongitude;
            if (gll.fbrlongitude < tmpll.fbrlongitude)
                gll.fbrlongitude = tmpll.fbrlongitude;
            if (gll.fltlatitude < tmpll.fltlatitude)
                gll.fltlatitude = tmpll.fltlatitude;
            if (gll.fbrlatitude > tmpll.fbrlatitude)
                gll.fbrlatitude = tmpll.fbrlatitude;
        }
    }

    public class RxlevGridLongLat
    {
        public RxlevGridLongLat()
        {
            Rxlev70GllList = new List<GridLongLat>();//天蓝色
            Rxlev80GllList = new List<GridLongLat>();//绿色
            Rxlev85GllList = new List<GridLongLat>();//黄色
            Rxlev90GllList = new List<GridLongLat>();//橙色
            Rxlev94GllList = new List<GridLongLat>();//紫红色
            Rxlev95GllList = new List<GridLongLat>();//紫红色
            RxlevNonCoverGllList = new List<GridLongLat>();//红色
        }

        public List<GridLongLat> Rxlev70GllList { get; set; }
        public List<GridLongLat> Rxlev80GllList { get; set; }
        public List<GridLongLat> Rxlev85GllList { get; set; }
        public List<GridLongLat> Rxlev90GllList { get; set; }
        public List<GridLongLat> Rxlev94GllList { get; set; }
        public List<GridLongLat> Rxlev95GllList { get; set; }
        public List<GridLongLat> RxlevNonCoverGllList { get; set; }
    }

    public class RxlevGridNum
    {
        public int iRxlev70Num{ get; set; }
        public int iRxlev80Num{ get; set; }
        public int iRxlev85Num{ get; set; }
        public int iRxlev90Num{ get; set; }
        public int iRxlev94Num{ get; set; }
        public int iRxlev95Num{ get; set; }
        public int iRxlevNonCoverNum{ get; set; }

        public RxlevGridNum()
        {
             iRxlev70Num = 0;
             iRxlev80Num = 0;
             iRxlev85Num = 0;
             iRxlev90Num = 0;
             iRxlev94Num = 0;
             iRxlev95Num = 0;
             iRxlevNonCoverNum = 0;
        }
    }

    public class AbisMrItem
    {
        public int iTaRxlev0 { get; set; }
        public int iTaRxlev1 { get; set; }
        public int iTaRxlev2 { get; set; }
        public int iTaRxlev3 { get; set; }
        public int iTaRxlev4 { get; set; }
        public int iTaRxlev5 { get; set; }
        public int iTaRxlev6 { get; set; }
        public int iTaRxlev7 { get; set; }
        public int iTaRxlev8 { get; set; }
        public int iTaRxlev9 { get; set; }
        public int iTaRxlev10 { get; set; }

        public AbisMrItem()
        {
            iTaRxlev0 = 0;
            iTaRxlev1 = 0;
            iTaRxlev2 = 0;
            iTaRxlev3 = 0;
            iTaRxlev4 = 0;
            iTaRxlev5 = 0;
            iTaRxlev6 = 0;
            iTaRxlev7 = 0;
            iTaRxlev8 = 0;
            iTaRxlev9 = 0;
            iTaRxlev10 = 0;
        }

        /// <summary>
        /// 转换为数组
        /// </summary>
        public int[] getArray()
        {
            int[] mrTaArray = new int[11];
            mrTaArray[0] = iTaRxlev0;
            mrTaArray[1] = iTaRxlev1;
            mrTaArray[2] = iTaRxlev2;
            mrTaArray[3] = iTaRxlev3;
            mrTaArray[4] = iTaRxlev4;
            mrTaArray[5] = iTaRxlev5;
            mrTaArray[6] = iTaRxlev6;
            mrTaArray[7] = iTaRxlev7;
            mrTaArray[8] = iTaRxlev8;
            mrTaArray[9] = iTaRxlev9;
            mrTaArray[10] = iTaRxlev10;
            return mrTaArray;
        }
    }

    public class CellLaiKey
    {
        public int ILac { get; set; }
        public int ICi { get; set; }

        public override bool Equals(object obj)
        {
            CellLaiKey other = obj as CellLaiKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.ILac.Equals(other.ILac) &&
                    this.ICi.Equals(other.ICi));
        }

        public override int GetHashCode()
        {
            return this.ICi.GetHashCode();
        }
    }

    public class DiySqlGetCellRxlevTa : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; }

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(CfgSettingItem csItem, string strCity)
        {
            DateTime dTime = JavaDate.GetDateTimeFromMilliseconds(csItem.istime * 1000L);
            DateTime tmpTime = dTime;
            DateTime eTime = JavaDate.GetDateTimeFromMilliseconds(csItem.ietime * 1000L);
            List<string> tbList = new List<string>();
            while (tmpTime <= eTime)
            {
                string strTableName = string.Format("DTASYSTEM.dbo.tb_stat_cell_rxlev_ta_dd_{0:yyMM}", tmpTime);
                if (!tbList.Contains(strTableName))
                    tbList.Add(strTableName);
                tmpTime = tmpTime.AddDays(1);
            }

            StringBuilder tmpSql = new StringBuilder();
            strSQL = "";
            foreach (string tbName in tbList)
            {
                if (tmpSql.Length > 0)
                {
                    tmpSql.Append(" union all ");
                }
                tmpSql.Append("select lac,ci,sum(rxlev_total) as rxlev_total,sum(rxlev_ge_80) as rxlev_ge_80,sum(rxlev_ge_85) as rxlev_ge_85,sum(rxlev_ge_90) as rxlev_ge_90,sum(rxlev_ge_94) as rxlev_ge_94,sum(rxlev_ge_95) as rxlev_ge_95," + "sum(ta_total) as ta_total,sum(ta_le_0) as ta_le_0,sum(ta_le_1) as ta_le_1,sum(ta_le_2) as ta_le_2,sum(ta_le_3) as ta_le_3,sum(ta_le_4) as ta_le_4,sum(ta_le_5) as ta_le_5," + "sum(ta_le_6) as ta_le_6,sum(ta_le_7) as ta_le_7,sum(ta_le_8) as ta_le_8,sum(ta_le_9) as ta_le_9,sum(ta_le_10) as ta_le_10 " + "from " + tbName + " where stime >= " + csItem.istime + " and stime <= " + csItem.ietime + " and region_name = '" + strCity + "' group by lac,ci ");
            }
            strSQL = "select * from (" + tmpSql.ToString() + ") a";

        }

        public DiySqlGetCellRxlevTa(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetCellRxlevTa"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[20];

            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;

            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_Int;
            rType[14] = E_VType.E_Int;
            rType[15] = E_VType.E_Int;
            rType[16] = E_VType.E_Int;
            rType[17] = E_VType.E_Int;
            rType[18] = E_VType.E_Int;
            rType[19] = E_VType.E_Int;

            return rType;
        }

        public List<CellRxlevTaItem> cellRxlevTaList { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            cellRxlevTaList = new List<CellRxlevTaItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellRxlevTaItem crItem = new CellRxlevTaItem();
                    crItem.lac = package.Content.GetParamInt();
                    crItem.ci = package.Content.GetParamInt();
                    crItem.rxlev_total = package.Content.GetParamInt();
                    crItem.rxlev_ge_80 = package.Content.GetParamInt();
                    crItem.rxlev_ge_85 = package.Content.GetParamInt();
                    crItem.rxlev_ge_90 = package.Content.GetParamInt();
                    crItem.rxlev_ge_94 = package.Content.GetParamInt();
                    crItem.rxlev_ge_70 = package.Content.GetParamInt();//将原来的95读至70
                    crItem.ta_total = package.Content.GetParamInt();
                    crItem.ta_le_0 = package.Content.GetParamInt();
                    crItem.ta_le_1 = package.Content.GetParamInt();
                    crItem.ta_le_2 = package.Content.GetParamInt();
                    crItem.ta_le_3 = package.Content.GetParamInt();
                    crItem.ta_le_4 = package.Content.GetParamInt();
                    crItem.ta_le_5 = package.Content.GetParamInt();
                    crItem.ta_le_6 = package.Content.GetParamInt();
                    crItem.ta_le_7 = package.Content.GetParamInt();
                    crItem.ta_le_8 = package.Content.GetParamInt();
                    crItem.ta_le_9 = package.Content.GetParamInt();
                    crItem.ta_le_10 = package.Content.GetParamInt();
                    cellRxlevTaList.Add(crItem);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlGetGsmGridRscp : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; }
        /// <summary>
        /// 界面配置
        /// </summary>
        public CfgSettingItem csItem { get; set; }

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(string strCity)
        {
            DateTime dTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-1 00:00:00", JavaDate.GetDateTimeFromMilliseconds(csItem.istime * 1000L)));
            DateTime tmpTime = dTime;
            DateTime eTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-1 00:00:00", JavaDate.GetDateTimeFromMilliseconds(csItem.ietime * 1000L).AddMonths(1))).AddSeconds(-1);
            List<string> tbList = new List<string>();
            while (tmpTime <= eTime)
            {
                string strTableName = string.Format("DTASYSTEM.dbo.tb_auto_para_competition_gsm_{0:yyMM}", tmpTime);
                if (!tbList.Contains(strTableName))
                    tbList.Add(strTableName);
                tmpTime = tmpTime.AddDays(1);
            }

            StringBuilder tmpSql = new StringBuilder();
            strSQL = "";
            foreach (string tbName in tbList)
            {
                if (tmpSql.Length > 0)
                {
                    tmpSql.Append(" union all ");
                }
                tmpSql.Append("select iltlongitude,iltlatitude,ibrlongitude,ibrlatitude,iPccpchRscp,ivalue1,ivalue2 " +
                         "from " + tbName + " where dstime >= '" + dTime + "' and detime <= '" + eTime + "' and strcity = '" + strCity + "'");
            }
            strSQL = "select iltlongitude,iltlatitude,ibrlongitude,ibrlatitude,avg(iPccpchRscp) as avgrscp,max(ivalue1) as maxrscp,min(ivalue2) as minrscp from (" + tmpSql.ToString() + 
                     ") a group by iltlongitude,iltlatitude,ibrlongitude,ibrlatitude";

        }

        public DiySqlGetGsmGridRscp(MainModel mainModel)
            : base(mainModel)
        {
            csItem = new CfgSettingItem();
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetGridRscp"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];

            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;

            return rType;
        }

        public Dictionary<GridLongLat, int> gridRscpDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            gridRscpDic = new Dictionary<GridLongLat, int>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            GridLongLat gllItem = new GridLongLat();
            int iltLongitude = package.Content.GetParamInt();
            int iltLatitude = package.Content.GetParamInt();
            package.Content.GetParamInt();//iBrLongitude
            package.Content.GetParamInt();//iBrLatiutde
            int iBrLongitude;
            int iBrLatiutde;

            //栅格化
            iltLongitude = iltLongitude / csItem.iLongitude * csItem.iLongitude;
            iltLatitude = iltLatitude / csItem.iLatitude * csItem.iLatitude;
            iBrLongitude = iltLongitude / csItem.iLongitude * csItem.iLongitude + csItem.iLongitude;
            iBrLatiutde = iltLatitude / csItem.iLatitude * csItem.iLatitude - csItem.iLatitude;

            gllItem.fltlongitude = ((float)iltLongitude) / 10000000;
            gllItem.fltlatitude = ((float)iltLatitude) / 10000000;
            gllItem.fbrlongitude = ((float)iBrLongitude) / 10000000;
            gllItem.fbrlatitude = ((float)iBrLatiutde) / 10000000;

            int iAvgRscp = package.Content.GetParamInt();
            int iMaxRscp = package.Content.GetParamInt();
            int iMinRscp = package.Content.GetParamInt();

            if (!gridRscpDic.ContainsKey(gllItem))
            {
                if (csItem.iTestValue == 1)
                {
                    gridRscpDic.Add(gllItem, iMaxRscp);
                }
                else if (csItem.iTestValue == 2)
                {
                    gridRscpDic.Add(gllItem, iAvgRscp);
                }
                else if (csItem.iTestValue == 3)
                {
                    gridRscpDic.Add(gllItem, iMinRscp);
                }
            }
        }
    }

    public class DiySqlGetGsmAbisMrRxlev : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; }
        /// <summary>
        /// 界面配置
        /// </summary>
        public CfgSettingItem csItem { get; set; }

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(string strCity)
        {
            DateTime dTime = Convert.ToDateTime(string.Format("{0:yyyy-MM-dd} 00:00:00", JavaDate.GetDateTimeFromMilliseconds(csItem.istime * 1000L)));
            DateTime tmpTime = dTime;
            DateTime eTime = Convert.ToDateTime(string.Format("{0:yyyy-MM-dd} 23:59:59", JavaDate.GetDateTimeFromMilliseconds(csItem.ietime * 1000L)));
            
            List<string> tbList = new List<string>();
            while (tmpTime <= eTime)
            {
                string strTableName = string.Format("DTASYSTEM.dbo.tb_stat_cell_rxlev_ta_abis_{0:yyMM}", tmpTime);
                if (!tbList.Contains(strTableName))
                    tbList.Add(strTableName);
                tmpTime = tmpTime.AddDays(1);
            }

            StringBuilder tmpSql = new StringBuilder();
            strSQL = "";
            foreach (string tbName in tbList)
            {
                if (tmpSql.Length > 0)
                {
                    tmpSql.Append(" union all ");
                }
                tmpSql.Append("select * from " + tbName + " where dstime >= '" + dTime + "' and detime <= '" + eTime + "' and strcity = '" + strCity + "'");
            }
            strSQL = "select ilac,ici,"+
                            "case when sum(convert(float,ita0num)) =0 then 0 else convert(int,sum(convert(float,ita0rxlev)*ita0num)/sum(convert(float,ita0num))) end,"+
                            "case when sum(convert(float,ita1num)) =0 then 0 else convert(int,sum(convert(float,ita1rxlev)*ita1num)/sum(convert(float,ita1num))) end,"+
                            "case when sum(convert(float,ita2num)) =0 then 0 else convert(int,sum(convert(float,ita2rxlev)*ita2num)/sum(convert(float,ita2num))) end,"+
                            "case when sum(convert(float,ita3num)) =0 then 0 else convert(int,sum(convert(float,ita3rxlev)*ita3num)/sum(convert(float,ita3num))) end,"+
                            "case when sum(convert(float,ita4num)) =0 then 0 else convert(int,sum(convert(float,ita4rxlev)*ita4num)/sum(convert(float,ita4num))) end,"+
                            "case when sum(convert(float,ita5num)) =0 then 0 else convert(int,sum(convert(float,ita5rxlev)*ita5num)/sum(convert(float,ita5num))) end,"+
                            "case when sum(convert(float,ita6num)) =0 then 0 else convert(int,sum(convert(float,ita6rxlev)*ita6num)/sum(convert(float,ita6num))) end,"+
                            "case when sum(convert(float,ita7num)) =0 then 0 else convert(int,sum(convert(float,ita7rxlev)*ita7num)/sum(convert(float,ita7num))) end,"+
                            "case when sum(convert(float,ita8num)) =0 then 0 else convert(int,sum(convert(float,ita8rxlev)*ita8num)/sum(convert(float,ita8num))) end,"+
                            "case when sum(convert(float,ita9num)) =0 then 0 else convert(int,sum(convert(float,ita9rxlev)*ita9num)/sum(convert(float,ita9num))) end,"+
                            "case when sum(convert(float,ita10num)) =0 then 0 else convert(int,sum(convert(float,ita10rxlev)*ita10num)/sum(convert(float,ita10num))) end " +
                            " from (" + tmpSql.ToString() + ") a group by ilac,ici";
        }

        public DiySqlGetGsmAbisMrRxlev(MainModel mainModel)
            : base(mainModel)
        {
            csItem = new CfgSettingItem();
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetGsmAbisMrRxlev"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[13];

            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;

            return rType;
        }

        public Dictionary<CellLaiKey, AbisMrItem> abisMrDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            abisMrDic = new Dictionary<CellLaiKey, AbisMrItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellLaiKey cellKey = new CellLaiKey();
                    cellKey.ILac = package.Content.GetParamInt();
                    cellKey.ICi = package.Content.GetParamInt();
                    AbisMrItem amItem = new AbisMrItem();
                    amItem.iTaRxlev0 = package.Content.GetParamInt();
                    amItem.iTaRxlev1 = package.Content.GetParamInt();
                    amItem.iTaRxlev2 = package.Content.GetParamInt();
                    amItem.iTaRxlev3 = package.Content.GetParamInt();
                    amItem.iTaRxlev4 = package.Content.GetParamInt();
                    amItem.iTaRxlev5 = package.Content.GetParamInt();
                    amItem.iTaRxlev6 = package.Content.GetParamInt();
                    amItem.iTaRxlev7 = package.Content.GetParamInt();
                    amItem.iTaRxlev8 = package.Content.GetParamInt();
                    amItem.iTaRxlev9 = package.Content.GetParamInt();
                    amItem.iTaRxlev10 = package.Content.GetParamInt();

                    if (!abisMrDic.ContainsKey(cellKey))
                    {
                        abisMrDic.Add(cellKey, amItem);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
