﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DLSpeedPitSet : BaseDialog
    {
        public DLSpeedPitSet()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public DLSpeedPitCond GetCond()
        {
            DLSpeedPitCond pitCond = new DLSpeedPitCond();
            pitCond.DPitAfterLastTime = (double)dPitAfterLastTime.Value;
            pitCond.DPitDropSpeed = (double)dPitDropSpeed.Value;
            pitCond.DPitLastSpeedAvg = (double)dPitLastSpeedAvg.Value;
            pitCond.DPitLastTime = (double)dPitLastTime.Value;
            pitCond.DPitPreLastTime = (double)dPitPreLastTime.Value;
            pitCond.DPitPreSpeedAvg = (double)dPitPreSpeedAvg.Value;
            return pitCond;
        }
    }
}
