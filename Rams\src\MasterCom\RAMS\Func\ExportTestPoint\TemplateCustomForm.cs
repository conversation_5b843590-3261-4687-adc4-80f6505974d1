﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.ExportTestPoint
{
    public partial class TemplateCustomForm : BaseForm
    {
        public TemplateCustomForm()
            : base()
        {
            InitializeComponent();
            numMaxValue.Properties.MinValue = numMinValue.Properties.MinValue = decimal.MinValue;
            numMaxValue.Properties.MaxValue = numMinValue.Properties.MaxValue = decimal.MaxValue;
            initCbx();
        }

        public TemplateCustomForm(ExportTemplate selTemplate)
            : this()
        {
            fillTemplateList(selTemplate);
        }

        private void fillTemplateList(ExportTemplate selRpt)
        {
            gridCtrlTmpl.DataSource = TemplateManager.Instance.Templates;
            gridCtrlTmpl.RefreshDataSource();
            if (selRpt != null)
            {
                int idx = TemplateManager.Instance.Templates.IndexOf(selRpt);
                if (idx != -1)
                {
                    gvTmpl.FocusedRowHandle = gvTmpl.GetRowHandle(idx);
                }
            }
        }

        ExportTemplate curTemplate = null;

        private void fillColumnView(ExportTemplate rpt, ColumnOptions selCol)
        {
            listDisplay.Items.Clear();
            btnRemoveDisCol.Enabled = false;
            btnModifyCol.Enabled = false;
            foreach (ColumnOptions cnd in rpt.Columns)
            {
                listDisplay.Items.Add(cnd);
            }
            if (listDisplay.Items.Count > 0)
            {
                if (selCol != null)
                {
                    listDisplay.SelectedItem = selCol;
                }
                else
                {
                    listDisplay.SelectedIndex = 0;
                }
            }
        }

        private void listDisplay_SelectedIndexChanged(object sender, EventArgs e)
        {
            ColumnOptions col = listDisplay.SelectedItem as ColumnOptions;
            if (col == null)
            {
                btnRemoveDisCol.Enabled = false;
                btnModifyCol.Enabled = false;
                btnDown.Enabled = false;
                btnUp.Enabled = false;
                return;
            }
            btnUp.Enabled = listDisplay.SelectedIndex != 0;
            btnDown.Enabled = listDisplay.SelectedIndex != listDisplay.Items.Count - 1;

            btnRemoveDisCol.Enabled = true;
            btnModifyCol.Enabled = true;
            cbxSysDisplay.SelectedItem = col.DisplayParam.System;
            cbxParamDisplay.SelectedItem = col.DisplayParam;
            cbxParamIdxDisplay.SelectedItem = col.ParamArrayIndex;
            txtCaption.Text = col.Caption;
            chkRange.Checked = col.CheckValue;
            chkIgnore.Checked = col.IsIgnoreWhenNotInRange;
            if (chkRange.Checked)
            {
                numMinValue.Value = (decimal)col.MinValue;
                numMaxValue.Value = (decimal)col.MaxValue;
            }
        }

        private void initCbx()
        {
            cbxSysDisplay.Properties.Items.Clear();
            cbxParamDisplay.Properties.Items.Clear();
            cbxParamIdxDisplay.Properties.Items.Clear();
            cbxSysDisplay.Properties.SelectedIndexChanged -= sysCbx_SelectedIndexChanged;
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                cbxSysDisplay.Properties.Items.Add(system);
            }
            cbxSysDisplay.Properties.SelectedIndexChanged += sysCbx_SelectedIndexChanged;
        }

        void sysCbx_SelectedIndexChanged(object sender, EventArgs e)
        {
            fillCbxParams(cbxParamDisplay, cbxSysDisplay.SelectedItem as DTDisplayParameterSystem);
        }

        private void fillCbxParams(ComboBoxEdit cbx, DTDisplayParameterSystem sys)
        {
            cbx.Properties.SelectedIndexChanged -= paramCbx_SelectedIndexChanged;
            cbx.Properties.Items.Clear();
            DTDisplayParameterSystem paramSys = sys;
            if (paramSys == null)
            {
                return;
            }
            foreach (DTDisplayParameterInfo param in paramSys.DisplayParamInfos)
            {
                cbx.Properties.Items.Add(param);
            }
            cbx.Properties.SelectedIndexChanged += paramCbx_SelectedIndexChanged;
            if (cbx.Properties.Items.Count > 0)
            {
                cbx.SelectedIndex = 0;
            }
        }

        private void paramCbx_SelectedIndexChanged(object sender, EventArgs e)
        {
            DTDisplayParameterInfo disParam = ((ComboBoxEdit)sender).SelectedItem as DTDisplayParameterInfo;
            if (disParam == null)
            {
                return;
            }
            btnAddDisCol.Enabled = true;
            numMinValue.Value = (decimal)disParam.ValueMin;
            numMaxValue.Value = (decimal)disParam.ValueMax;
            fillCbxParamIdx(cbxParamIdxDisplay, disParam);
        }
       
        private void fillCbxParamIdx(ComboBoxEdit cbx, DTDisplayParameterInfo param)
        {
            cbx.Properties.Items.Clear();
            if (param != null)
            {
                if (param.IsArray)
                {
                    cbx.Enabled = true;
                    for (int i = 0; i < param.ArrayBounds; i++)
                    {
                        cbx.Properties.Items.Add(i);
                    }
                    cbx.SelectedIndex = 0;
                }
                else
                {
                    cbx.Enabled = false;
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            TemplateManager.Instance.Save();
        }

        private void btnNewReport_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建模板", "模板名称", "未命名模板");
            if (box.ShowDialog() == DialogResult.OK)
            {
                ExportTemplate template = new ExportTemplate(box.TextInput);
                TemplateManager.Instance.Templates.Add(template);
                gridCtrlTmpl.RefreshDataSource();
                gvTmpl.FocusedRowHandle = TemplateManager.Instance.Templates.IndexOf(template);
            }
        }

        private void btnRemoveTemplate_Click(object sender, EventArgs e)
        {
            ExportTemplate t = gvTmpl.GetRow(gvTmpl.FocusedRowHandle) as ExportTemplate;
            if (t != null)
            {
                int idx = TemplateManager.Instance.Templates.IndexOf(t);
                if (idx != -1 
                    && MessageBox.Show(this, "确定删除该模板？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    TemplateManager.Instance.Templates.RemoveAt(idx);
                    gridCtrlTmpl.RefreshDataSource();
                    curTemplate = gvTmpl.GetRow(gvTmpl.FocusedRowHandle) as ExportTemplate;
                    visualizeCurTemplate();
                }
            }
        }


        public ExportTemplate SelectedTemplate { get { return curTemplate; } }

        private void btnAddDisCol_Click(object sender, EventArgs e)
        {
            ColumnOptions col = createColBySetting();
            if (col == null)
            {
                return;
            }
            if (curTemplate.AddColumn(col))
            {
                fillColumnView(curTemplate, col);
            }
            else
            {
                MessageBox.Show("已存在相同的参数列！");
            }
        }

        private ColumnOptions createColBySetting()
        {
            if (string.IsNullOrEmpty(txtCaption.Text))
            {
                MessageBox.Show("列标题不能为空！");
                return null;
            }

            DTDisplayParameterInfo param = cbxParamDisplay.SelectedItem as DTDisplayParameterInfo;
            int idx = -1;
            if (cbxParamIdxDisplay.Enabled)
            {
                int.TryParse(cbxParamIdxDisplay.SelectedItem.ToString(), out idx);
            }
            ColumnOptions col = new ColumnOptions();
            col.DisplayParam = param;
            col.Caption = txtCaption.Text;
            col.ParamArrayIndex = idx;
            col.CheckValue = chkRange.Checked;
            col.IsIgnoreWhenNotInRange = chkIgnore.Checked;
            if (chkRange.Checked)
            {
                col.MinValue = (double)numMinValue.Value;
                col.MaxValue = (double)numMaxValue.Value;
            }
            return col;
        }

        private void btnRemoveDisCol_Click(object sender, EventArgs e)
        {
            ColumnOptions col = listDisplay.SelectedItem as ColumnOptions;
            if (col == null)
            {
                MessageBox.Show("请在右边列表选择要删除的列！");
                return;
            }
            curTemplate.Columns.Remove(col);
            fillColumnView(curTemplate, null);
        }

        private void btnModifyCol_Click(object sender, EventArgs e)
        {
            ColumnOptions oldCol = listDisplay.SelectedItem as ColumnOptions;
            if (oldCol == null)
            {
                MessageBox.Show("请在右边列表选择要修改的列！");
                return;
            }
            ColumnOptions newCol = createColBySetting();
            if (newCol == null)
            {
                return;
            }
            oldCol.CfgParam = newCol.CfgParam;
            fillColumnView(curTemplate, oldCol);
        }

        private void gvTmpl_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            curTemplate = gvTmpl.GetRow(e.FocusedRowHandle) as ExportTemplate;
            visualizeCurTemplate();
        }

        private void visualizeCurTemplate()
        {
            btnRemoveTemplate.Enabled = groupControl3.Enabled = curTemplate != null;
            if (curTemplate != null)
            {
                fillColumnView(curTemplate, null);
            }
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            moveColPos(true);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            moveColPos(false);
        }

        private void moveColPos(bool isUp)
        {
            ColumnOptions curCol = listDisplay.SelectedItem as ColumnOptions;
            int curIdx = listDisplay.SelectedIndex;
            curTemplate.Columns.RemoveAt(listDisplay.SelectedIndex);
            if (isUp)
            {
                curTemplate.Columns.Insert(curIdx - 1, curCol);
            }
            else
            {
                curTemplate.Columns.Insert(curIdx + 1, curCol);
            }
            fillColumnView(curTemplate, curCol);
        }

        private void chkRange_CheckedChanged(object sender, EventArgs e)
        {
            chkIgnore.Enabled = numMinValue.Enabled = numMaxValue.Enabled = chkRange.Checked;
        }

    }
}
