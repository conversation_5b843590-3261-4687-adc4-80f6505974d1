﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateTPCellParamInfoForm : CreateChildForm
    {
        public CreateTPCellParamInfoForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建采样点主服/邻区参数列表窗口 TPCellParamInfoForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20033, Name);
        }
        public override string Name
        {
            get
            {
                return "创建采样点主服/邻区参数列表";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.TPCellParamInfoForm";
            actionParam["Text"] = "主服/邻区参数";
            actionParam["ImageFilePath"] = @"images\dataview.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
