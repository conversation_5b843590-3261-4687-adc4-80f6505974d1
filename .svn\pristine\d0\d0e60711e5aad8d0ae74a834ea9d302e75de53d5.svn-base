﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SatisfactionAnalysisReportForm : MinCloseForm
    {
        public SatisfactionAnalysisReportForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            init();
        }
        private Dictionary<Cell, List<Vertex[]>> gsmCellVertex = null;
        private Dictionary<TDCell, List<Vertex[]>> tdCellVertex = null;
        /// <summary>
        /// 窗口数据初始化
        /// </summary>
        private void init()
        {
            createDataTable();
            cellscoreBindingSource = new BindingSource();
            cellscoreBindingSource.DataSource = typeof(CellScore);
            gridscoreBindingSource = new BindingSource();
            gridscoreBindingSource.DataSource = typeof(GridScore);
            //初始化地市、月份、问卷类型
            ZTTerminalCBXItemSQLQuery cbxQuery = new ZTTerminalCBXItemSQLQuery(MainModel);
            cbxQuery.Query();
            cbxCity.Items.Clear();
            foreach (string city in cbxQuery.CityList)
            {
                cbxCity.Items.Add(city);
            }
            cbxCity.SelectedIndex = 0;
            cbxMonth.Items.Clear();
            foreach (string month in cbxQuery.MonthList)
            {
                cbxMonth.Items.Add(month);
            }
            cbxMonth.SelectedIndex = 0;
            cbxQuestionType.Items.Clear();
            foreach (string questiontype in cbxQuery.QuestionTypeList)
            {
                cbxQuestionType.Items.Add(questiontype);
            }
            cbxQuestionType.Items.Add("全部");
            cbxQuestionType.Text = "全部";
            //初始化网格GSM指标右键菜单
            ZTRegionInitMenuSQLQuery gridmenuQuery = new ZTRegionInitMenuSQLQuery(MainModel);
            gridmenuQuery.ReportName = "GSM区域评估报表";
            gridmenuQuery.Query();
            tmenuGSMGridKPI.DropDownItems.Clear();
            foreach (string dropdownitem in gridmenuQuery.MenuItems)
            {
                ToolStripMenuItem menuitem = new ToolStripMenuItem(dropdownitem);
                menuitem.Tag = "GSM区域评估报表";
                menuitem.Click += new EventHandler(toolStripGridMenuItem_Click);
                tmenuGSMGridKPI.DropDownItems.Add(menuitem);
            }
            //初始化网格TD指标右键菜单
            gridmenuQuery.ReportName = "TD区域评估报表";
            gridmenuQuery.Query();
            tmenuTDGridKPI.DropDownItems.Clear();
            foreach (string dropdownitem in gridmenuQuery.MenuItems)
            {
                ToolStripMenuItem menuitem = new ToolStripMenuItem(dropdownitem);
                menuitem.Tag = "TD区域评估报表";
                menuitem.Click += new EventHandler(toolStripGridMenuItem_Click);
                tmenuTDGridKPI.DropDownItems.Add(menuitem);
            }
            //初始化小区GSM指标右键菜单
            ZTCellInitMenuSQLQuery cellmenuQuery = new ZTCellInitMenuSQLQuery(MainModel);
            cellmenuQuery.ReportName = "GSM区域评估报表";
            cellmenuQuery.Query();
            tmenuGSMCellKPI.DropDownItems.Clear();
            foreach (string dropdownitem in cellmenuQuery.MenuItems)
            {
                ToolStripMenuItem menuitem = new ToolStripMenuItem(dropdownitem);
                menuitem.Tag = "GSM区域评估报表";
                menuitem.Click += new EventHandler(toolStripCellMenuItem_Click);
                tmenuGSMCellKPI.DropDownItems.Add(menuitem);
            }
            //初始化小区TD指标右键菜单
            cellmenuQuery.ReportName = "TD区域评估报表";
            cellmenuQuery.Query();
            tmenuTDCellKPI.DropDownItems.Clear();
            foreach (string dropdownitem in cellmenuQuery.MenuItems)
            {
                ToolStripMenuItem menuitem = new ToolStripMenuItem(dropdownitem);
                menuitem.Tag = "TD区域评估报表";
                menuitem.Click += new EventHandler(toolStripCellMenuItem_Click);
                tmenuTDCellKPI.DropDownItems.Add(menuitem);
            }
        }
        DataTable resultDataTable;
        BindingSource cellscoreBindingSource;
        BindingSource gridscoreBindingSource;
        private void createDataTable()
        {
            resultDataTable = new DataTable();
            resultDataTable.Columns.Add("userPhone", typeof(string));
            resultDataTable.Columns.Add("userSex", typeof(string));
            resultDataTable.Columns.Add("terminalType", typeof(string));
            resultDataTable.Columns.Add("networkCover", typeof(int));
            resultDataTable.Columns.Add("networkQuality", typeof(int));
            resultDataTable.Columns.Add("mobileInternet", typeof(int));
            resultDataTable.Columns.Add("voiceCall", typeof(int));
        }
        /// <summary>
        /// 菜单点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void toolStripCellMenuItem_Click(object sender, EventArgs e)
        {
            if (gvCellScore.SelectedRows.Count > 0)
            {
                string gridName = gvCellScore.SelectedRows[0].Cells["gvcCellName"].Value as string;
                string lac = gvCellScore.SelectedRows[0].Cells["gvcLAC"].Value as string;
                string ci = gvCellScore.SelectedRows[0].Cells["gvcCI"].Value as string;
                ToolStripMenuItem menuitem = sender as ToolStripMenuItem;
                string datepick = menuitem.Text;
                string[] splits = datepick.Split(new string[] { "至" }, StringSplitOptions.RemoveEmptyEntries);
                if (splits.Length == 2)
                {
                    ZTCellKPISQLQuery kpiquery = new ZTCellKPISQLQuery(this.MainModel);
                    kpiquery.ReportName = menuitem.Tag.ToString();
                    kpiquery.GridName = lac + "_" + ci;
                    kpiquery.BeginDate = splits[0];
                    kpiquery.EndDate = splits[1];
                    kpiquery.Query();
                    ZTCellKQISQLQuery kqiquery = new ZTCellKQISQLQuery(this.MainModel);
                    kqiquery.ReportName = menuitem.Tag.ToString();
                    kqiquery.GridName = lac + "_" + ci;
                    kqiquery.BeginDate = splits[0];
                    kqiquery.EndDate = splits[1];
                    kqiquery.Query();
                    KPIReportForm kpiForm = new KPIReportForm();
                    kpiForm.FillData(gridName, datepick, kpiquery.RegionKPIs, kqiquery.RegionKQIs);
                    kpiForm.ShowDialog(this);
                }
            }
        }
        private void toolStripGridMenuItem_Click(object sender, EventArgs e)
        {
            if (gvGridScore.SelectedRows.Count > 0)
            {
                string gridName = gvGridScore.SelectedRows[0].Cells["gvcGridName"].Value as string;
                ToolStripMenuItem menuitem = sender as ToolStripMenuItem;
                string datepick = menuitem.Text;
                string[] splits = datepick.Split(new string[] { "至" }, StringSplitOptions.RemoveEmptyEntries);
                if (splits.Length == 2)
                {
                    ZTRegionKPISQLQuery kpiquery = new ZTRegionKPISQLQuery(this.MainModel);
                    kpiquery.ReportName = menuitem.Tag.ToString();
                    kpiquery.GridName = gridName;
                    kpiquery.BeginDate = splits[0];
                    kpiquery.EndDate = splits[1];
                    kpiquery.Query();
                    ZTRegionKQISQLQuery kqiquery = new ZTRegionKQISQLQuery(this.MainModel);
                    kqiquery.ReportName = menuitem.Tag.ToString();
                    kqiquery.GridName = gridName;
                    kqiquery.BeginDate = splits[0];
                    kqiquery.EndDate = splits[1];
                    kqiquery.Query();
                    KPIReportForm kpiForm = new KPIReportForm();
                    kpiForm.FillData(gridName, datepick, kpiquery.RegionKPIs, kqiquery.RegionKQIs);
                    kpiForm.ShowDialog(this);
                }
            }
        }
        /// <summary>
        /// 添加条件按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (cbxParam.Text == null || cbxParam.Text.Trim() == "")
            {
                MessageBox.Show("指标项不允许为空");
                return;
            }
            if (cbxOperator.Text == null || cbxOperator.Text.Trim() == "")
            {
                MessageBox.Show("操作符不允许为空");
                return;
            }
            if (cbxConnection.Text == null || cbxConnection.Text.Trim() == "")
            {
                MessageBox.Show("连接符不允许为空");
                return;
            }
            if (!lstCondition.Items.Contains(cbxConnection.Text + " " + cbxParam.Text + " " + cbxOperator.Text + " " + cbxParamValue.Text))
            {
                lstCondition.Items.Add(cbxConnection.Text + " " + cbxParam.Text + " " + cbxOperator.Text + " " + cbxParamValue.Text);
            }
        }
        /// <summary>
        /// 移除条件按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRemove_Click(object sender, EventArgs e)
        {
            if (lstCondition.SelectedItems.Count > 0)
            {
                for (int i = lstCondition.SelectedItems.Count - 1; i > -1; i--)
                {
                    lstCondition.Items.Remove(lstCondition.SelectedItems[i]);
                }
            }
        }
        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            doQuery();  //执行数据查询
        }
        /// <summary>
        /// 清空条件列表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnClear_Click(object sender, EventArgs e)
        {
            lstCondition.Items.Clear();
        }
        /// <summary>
        /// 执行数据查询
        /// </summary>
        private void doQuery()
        {
            if (lstCondition.Items.Count < 1)
            {
                MessageBox.Show("统计条件不允许为空");
                return;
            }
            if (gsmCellVertex == null && tdCellVertex == null)
            {
                GSMCellVoiCover gsmVoiCover = new GSMCellVoiCover();
                gsmCellVertex = gsmVoiCover.Construct();
                TDCellVoiCover tdVoiCover = new TDCellVoiCover();
                tdCellVertex = tdVoiCover.Construct();
            }
            StringBuilder wherebuilder = new StringBuilder();
            bool first = true;
            foreach (object itemobj in lstCondition.Items)
            {
                if (first)
                {
                    wherebuilder.Append(itemobj.ToString().Replace("AND", "").Replace("OR", ""));
                    first = false;
                }
                else
                {
                    wherebuilder.Append(itemobj.ToString());
                }
            }
            ZTSatisfactionAnalysisDataQuery satisfactionDataQuery = new ZTSatisfactionAnalysisDataQuery(this.MainModel);
            satisfactionDataQuery.City = cbxCity.Text;
            satisfactionDataQuery.Month = cbxMonth.Text;
            satisfactionDataQuery.QuestionType = cbxQuestionType.Text;
            satisfactionDataQuery.WhereCondition = wherebuilder.ToString();
            satisfactionDataQuery.GSMCellVertex = gsmCellVertex;
            satisfactionDataQuery.TDCellVertex = tdCellVertex;
            satisfactionDataQuery.Query();
        }
        /// <summary>
        /// 窗体加载完毕后执行查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SatisfactionAnalysisReportForm_Load(object sender, EventArgs e)
        {
            doQuery();
        }
        /// <summary>
        /// 选择不同的行刷新其占用小区和网格列表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvResult_SelectionChanged(object sender, EventArgs e)
        {
            if (gvResult.SelectedRows.Count > 0)
            {
                string userphone = gvResult.SelectedRows[0].Cells["gvcUserPhone"].Value.ToString();
                if (userSatisfactionMap.ContainsKey(userphone))
                {
                    UserSatisfactionItem userSatisfaction = userSatisfactionMap[userphone];
                    cellscoreBindingSource.Clear();
                    foreach (CellScore cellscore in userSatisfaction.CellScoreList)
                    {
                        cellscoreBindingSource.Add(cellscore);
                    }
                    gvCellScore.DataSource = cellscoreBindingSource;
                    gridscoreBindingSource.Clear();
                    foreach (GridScore gridscore in userSatisfaction.GridScoreList)
                    {
                        gridscoreBindingSource.Add(gridscore);
                    }
                    gvGridScore.DataSource = gridscoreBindingSource;
                }
            }
        }

        private Dictionary<string, UserSatisfactionItem> userSatisfactionMap = new Dictionary<string, UserSatisfactionItem>();
        public void FillData(List<UserSatisfactionItem> userSatisfactionItems)
        {
            resultDataTable.Rows.Clear();
            userSatisfactionMap.Clear();
            foreach (UserSatisfactionItem userSatisfaction in userSatisfactionItems)
            {
                DataRow row = resultDataTable.NewRow();
                row["userPhone"] = userSatisfaction.UserPhone;
                row["userSex"] = userSatisfaction.UserSex;
                row["terminalType"] = userSatisfaction.TerminalType;
                row["networkCover"] = userSatisfaction.NetworkCover;
                row["networkQuality"] = userSatisfaction.NetworkQuality;
                row["mobileInternet"] = userSatisfaction.MobileInternet;
                row["voiceCall"] = userSatisfaction.VoiceCall;
                if (!userSatisfactionMap.ContainsKey(userSatisfaction.UserPhone))
                {
                    userSatisfactionMap[userSatisfaction.UserPhone] = userSatisfaction;
                }
                resultDataTable.Rows.Add(row);
            }
            gvResult.DataSource = resultDataTable;
        }

        private void 渲染该用户轨迹ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            List<List<Vertex[]>> vertexs = new List<List<Vertex[]>>();
            if (gvResult.SelectedRows.Count > 0)
            {
                string telphone = gvResult.SelectedRows[0].Cells["gvcUserPhone"].Value.ToString();
                UserSatisfactionItem satisfactionItem = userSatisfactionMap[telphone];
                if (satisfactionItem != null)
                {
                    foreach (CellScore cellScore in satisfactionItem.CellScoreList)
                    {
                        vertexs.Add(cellScore.Vertexs);
                    }
                    VoronoiLayer.GetInstance().Draw(vertexs);
                }
            }
        }

        private void 渲染全部用户ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            List<List<Vertex[]>> vertexs = new List<List<Vertex[]>>();
            if (gvResult.Rows.Count > 0)
            {
                foreach (string key in userSatisfactionMap.Keys)
                {
                    UserSatisfactionItem satisfactionItem = userSatisfactionMap[key];
                    if (satisfactionItem != null)
                    {
                        foreach (CellScore cellScore in satisfactionItem.CellScoreList)
                        {
                            vertexs.Add(cellScore.Vertexs);
                        }
                    }
                }
                VoronoiLayer.GetInstance().Draw(vertexs);
            }
        }
    }
}
