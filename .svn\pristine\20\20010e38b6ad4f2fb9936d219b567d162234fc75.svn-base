﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class CellParamDataManager
    {
        private static CellParamDataManager instance = null;
        private CellParamDataManager() { }
        public static CellParamDataManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CellParamDataManager();
            }
            return instance;
        }

        public static bool NeedShowParam { get; set; } = false;
    }
}
