﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.Func
{
    public partial class MapLTECellLayerBaseProperties : MTLayerPropUserControl
    {
        public MapLTECellLayerBaseProperties()
        {
            InitializeComponent();
        }

        private MapLTECellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapLTECellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "通用设置";
            numShapeLength.ValueChanged += new EventHandler(numShapeLength_ValueChanged);
            numShapeWidth.ValueChanged += new EventHandler(numShapeWidth_ValueChanged);
            this.numMaxScale.Value = (decimal)layer.FeatrueMaxZoomScale;
            this.checkBoxDrawServer.Checked=layer.DrawServer;
            this.checkBoxDrawIndoor.Checked = layer.DrawIndoor;
            this.checkBoxDrawOutdoor.Checked = layer.DrawOutdoor;

            this.colorSelected.Color = layer.ColorSelected;
            this.colorSvrCell.Color = layer.ColorServerCell;

            this.radioButtonDrawCurrent.Checked = MapLTECellLayer.DrawCurrent;
            this.radioButtonDrawSnapshotTime.Checked = !MapLTECellLayer.DrawCurrent;

            this.numMaxScale.ValueChanged += new EventHandler(numMaxScale_ValueChanged);
            this.checkBoxDrawServer.CheckedChanged += new EventHandler(checkBoxDrawServer_CheckedChanged);
            this.checkBoxDrawIndoor.CheckedChanged += new EventHandler(checkBoxDrawIndoor_CheckedChanged);
            this.checkBoxDrawOutdoor.CheckedChanged += new EventHandler(checkBoxDrawOutdoor_CheckedChanged);
            this.colorSelected.ColorChanged += new EventHandler(colorSelected_ColorChanged);
            this.colorSvrCell.ColorChanged += new EventHandler(colorSvrCell_ColorChanged);
            radioButtonDrawCurrent.CheckedChanged += new EventHandler(radioButtonDrawCurrent_CheckedChanged);
            radioButtonDrawSnapshotTime.CheckedChanged += new EventHandler(radioButtonDrawSnapshotTime_CheckedChanged);
            dateTimePickerTime.ValueChanged += new EventHandler(dateTimePickerTime_ValueChanged);
        }

        private void redrawPic()
        {
            try
            {
                Bitmap bmp = createBmp(layer.CellPaths[1], layer.ColorCell);
                pictureCell.Image = bmp;
                bmp = createBmp(layer.AntennaPaths[1], layer.ColorAntenna);
                pictureAntenna.Image = bmp;
            }
            catch
            {
                //忽略
            }
        }

        private Bitmap createBmp(GraphicsPath path,Color color)
        {
            double scale = layer.FeatrueMaxZoomScale > layer.MapScale ? layer.FeatrueMaxZoomScale : layer.MapScale;
            scale = 10000 / scale;
            RectangleF rect = path.GetBounds();
            Bitmap bmp = new Bitmap((int)(rect.Width * scale), (int)(rect.Height * scale));
            Graphics g = Graphics.FromImage(bmp);
            g.TranslateTransform(0, (float)bmp.Height / 2);
            g.ScaleTransform((float)scale, (float)scale);
            g.FillPath(new SolidBrush(color), path);
            g.Save();
            g.Dispose();
            return bmp;
        }

        void numShapeWidth_ValueChanged(object sender, EventArgs e)
        {
            valueChanged();
        }

        void numShapeLength_ValueChanged(object sender, EventArgs e)
        {
            valueChanged();
        }

        private void valueChanged()
        {
            layer.TransformDisplayShape((float)numShapeLength.Value, (float)numShapeWidth.Value);
            redrawPic();
        }

        void dateTimePickerTime_ValueChanged(object sender, EventArgs e)
        {
            MapLTECellLayer.DrawCurrent = !radioButtonDrawSnapshotTime.Checked;
            MapLTECellLayer.CurShowSnapshotTime = dateTimePickerTime.Value.Date;
        }

        void radioButtonDrawSnapshotTime_CheckedChanged(object sender, EventArgs e)
        {
            MapLTECellLayer.DrawCurrent = !radioButtonDrawSnapshotTime.Checked;
            dateTimePickerTime.Enabled = radioButtonDrawSnapshotTime.Checked;
            MapLTECellLayer.CurShowSnapshotTime = dateTimePickerTime.Value.Date;
        }

        void radioButtonDrawCurrent_CheckedChanged(object sender, EventArgs e)
        {
            MapLTECellLayer.DrawCurrent = radioButtonDrawCurrent.Checked;
        }

        void colorSvrCell_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorServerCell = colorSvrCell.Color;
        }

        void colorSelected_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorSelected = colorSelected.Color;
        }

        void checkBoxDrawOutdoor_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawOutdoor = checkBoxDrawOutdoor.Checked;
        }

        void checkBoxDrawIndoor_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawIndoor = checkBoxDrawIndoor.Checked;
        }

        void checkBoxDrawServer_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawServer = checkBoxDrawServer.Checked;
        }

        void numMaxScale_ValueChanged(object sender, EventArgs e)
        {
            layer.FeatrueMaxZoomScale = (double)numMaxScale.Value;
        }


    }
}
