﻿namespace MasterCom.RAMS.Func
{
    partial class CellGridDetailResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewCG = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnChecked = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTlLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTlLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBrLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBrLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCG)).BeginInit();
            this.SuspendLayout();
            // 
            // ListViewCG
            // 
            this.ListViewCG.AllColumns.Add(this.olvColumnSN);
            this.ListViewCG.AllColumns.Add(this.olvColumnChecked);
            this.ListViewCG.AllColumns.Add(this.olvColumnLAC);
            this.ListViewCG.AllColumns.Add(this.olvColumnCI);
            this.ListViewCG.AllColumns.Add(this.olvColumnServCell);
            this.ListViewCG.AllColumns.Add(this.olvColumnTlLng);
            this.ListViewCG.AllColumns.Add(this.olvColumnTlLat);
            this.ListViewCG.AllColumns.Add(this.olvColumnBrLng);
            this.ListViewCG.AllColumns.Add(this.olvColumnBrLat);
            this.ListViewCG.AllColumns.Add(this.olvColumnAvgRSRP);
            this.ListViewCG.CheckBoxes = true;
            this.ListViewCG.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnChecked,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnServCell,
            this.olvColumnTlLng,
            this.olvColumnTlLat,
            this.olvColumnBrLng,
            this.olvColumnBrLat,
            this.olvColumnAvgRSRP});
            this.ListViewCG.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCG.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCG.FullRowSelect = true;
            this.ListViewCG.GridLines = true;
            this.ListViewCG.HeaderWordWrap = true;
            this.ListViewCG.IsNeedShowOverlay = false;
            this.ListViewCG.Location = new System.Drawing.Point(0, 0);
            this.ListViewCG.Name = "ListViewCG";
            this.ListViewCG.OwnerDraw = true;
            this.ListViewCG.ShowGroups = false;
            this.ListViewCG.ShowImagesOnSubItems = true;
            this.ListViewCG.Size = new System.Drawing.Size(757, 539);
            this.ListViewCG.TabIndex = 9;
            this.ListViewCG.UseCompatibleStateImageBehavior = false;
            this.ListViewCG.View = System.Windows.Forms.View.Details;
            this.ListViewCG.VirtualMode = true;
            this.ListViewCG.ItemChecked += new System.Windows.Forms.ItemCheckedEventHandler(this.ListViewCG_ItemChecked);
            this.ListViewCG.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCG_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnChecked
            // 
            this.olvColumnChecked.CheckBoxes = true;
            this.olvColumnChecked.HeaderFont = null;
            this.olvColumnChecked.Text = "是否选择";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 80;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 80;
            // 
            // olvColumnServCell
            // 
            this.olvColumnServCell.HeaderFont = null;
            this.olvColumnServCell.Text = "小区";
            this.olvColumnServCell.Width = 150;
            // 
            // olvColumnTlLng
            // 
            this.olvColumnTlLng.HeaderFont = null;
            this.olvColumnTlLng.Text = "左上角经度";
            this.olvColumnTlLng.Width = 80;
            // 
            // olvColumnTlLat
            // 
            this.olvColumnTlLat.HeaderFont = null;
            this.olvColumnTlLat.Text = "左上角纬度";
            this.olvColumnTlLat.Width = 80;
            // 
            // olvColumnBrLng
            // 
            this.olvColumnBrLng.HeaderFont = null;
            this.olvColumnBrLng.Text = "右下角经度";
            this.olvColumnBrLng.Width = 80;
            // 
            // olvColumnBrLat
            // 
            this.olvColumnBrLat.HeaderFont = null;
            this.olvColumnBrLat.Text = "右下角纬度";
            this.olvColumnBrLat.Width = 80;
            // 
            // olvColumnAvgRSRP
            // 
            this.olvColumnAvgRSRP.HeaderFont = null;
            this.olvColumnAvgRSRP.Text = "平均RSRP";
            // 
            // CellGridDetailResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(757, 539);
            this.Controls.Add(this.ListViewCG);
            this.Name = "CellGridDetailResultForm";
            this.Text = "小区栅格数据";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCG)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewCG;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnServCell;
        private BrightIdeasSoftware.OLVColumn olvColumnTlLng;
        private BrightIdeasSoftware.OLVColumn olvColumnTlLat;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLng;
        private BrightIdeasSoftware.OLVColumn olvColumnBrLat;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnChecked;

    }
}