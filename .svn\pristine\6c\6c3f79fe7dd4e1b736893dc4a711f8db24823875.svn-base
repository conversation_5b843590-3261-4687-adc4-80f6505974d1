﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using System.Data;
using System.Data.OleDb;

using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace MasterCom.Util
{
    /// <summary>
    /// 获取工作表指定的列内容
    /// </summary>
    public class ExcelNPOIReader
    {
        public ExcelNPOIReader(string xlsFile)
        {
            using (FileStream fs = File.OpenRead(xlsFile))
            {
                book = WorkbookFactory.Create(fs);
            }
        }

        public IWorkbook WorkBook
        {
            get { return book; }
        }

        /// <summary>
        /// 获取所有可见的工作表名
        /// </summary>
        /// <returns></returns>
        public List<string> GetSheets()
        {
            List<string> retSheets = new List<string>();
            int sheetCount = book.NumberOfSheets;
            for (int i = 0; i < sheetCount; ++i)
            {
                if (book.IsSheetHidden(i))
                {
                    continue;
                }
                ISheet sheet = book.GetSheetAt(i);
                retSheets.Add(sheet.SheetName);
            }
            return retSheets;
        }

        /// <summary>
        /// 获取Sheet的首行列名
        /// </summary>
        /// <param name="sheetName">工作表名</param>
        /// <returns></returns>
        public List<string> GetColumns(string sheetName)
        {
            List<string> retColumns = new List<string>();
            ISheet sheet = book.GetSheet(sheetName);
            if (sheet == null)
            {
                return retColumns;
            }

            IRow row = sheet.GetRow(sheet.FirstRowNum);
            if (row == null)
            {
                return retColumns;
            }

            for (int i = row.FirstCellNum; i <= row.LastCellNum; ++i)
            {
                ICell cell = row.GetCell(i);
                if (cell == null)
                {
                    continue;
                }

                object cellValue = GetCellValue(cell);
                if (cellValue == null)
                {
                    continue;
                }
                retColumns.Add(cellValue.ToString());
            }
            return retColumns;
        }

        /// <summary>
        /// 获取第一个非空工作表的首行列名
        /// </summary>
        /// <returns></returns>
        public List<string> GetColumns()
        {
            for (int i = 0; i < book.NumberOfSheets; ++i)
            {
                if (book.IsSheetHidden(i))
                {
                    continue;
                }

                ISheet sheet = book.GetSheetAt(i);
                if (sheet == null)
                {
                    continue;
                }

                List<string> retColumns = GetColumns(sheet.SheetName);
                if (retColumns.Count != 0)
                {
                    return retColumns;
                }
            }
            return new List<string>();
        }

        /// <summary>
        /// 获取工作表的指定列内容
        /// </summary>
        /// <param name="sheetName"></param>
        /// <param name="colNames"></param>
        /// <returns></returns>
        public ExcelNPOITable GetTable(string sheetName, List<string> colNames)
        {
            ISheet sheet = book.GetSheet(sheetName);
            int[] idx = GetColumnsIndex(sheet, colNames);
            List<object[]> cellValues = new List<object[]>();

            // Set Cells Value
            for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; ++i)
            {
                object[] values = new object[colNames.Count];
                cellValues.Add(values);

                IRow row = sheet.GetRow(i);
                if (row == null)
                {
                    continue;
                }

                for (int j = 0; j < idx.Length; ++j)
                {
                    if (idx[j] == -1)
                    {
                        continue;
                    }
                    ICell cell = row.GetCell(idx[j]);
                    values[j] = cell == null ? null : GetCellValue(cell);
                }
            }

            // Set Columns Index
            for (int i = 0; i < idx.Length; ++i)
            {
                if (idx[i] != -1)
                {
                    idx[i] += 1;
                }
            }

            // return table
            ExcelNPOITable table = new ExcelNPOITable();
            table.CellValues = cellValues;
            table.FirstRow = sheet.FirstRowNum + 2; // 从0开始加1，第一行是列头再加1
            table.LastRow = sheet.LastRowNum + 1;
            table.ColumnsIndex = idx;

            return table;
        }

        /// <summary>
        /// 获取第一个匹配指定列名的列内容
        /// </summary>
        /// <param name="colNames"></param>
        /// <returns>如果所有工作表无法完全匹配指定列，返回null</returns>
        public ExcelNPOITable GetTable(List<string> colNames)
        {
            List<string> sheetNames = GetSheets();
            foreach (string shtName in sheetNames)
            {
                ISheet sheet = book.GetSheet(shtName);
                int sheetIndex = book.GetSheetIndex(sheet);
                if (book.IsSheetHidden(sheetIndex))
                {
                    continue;
                }

                bool isMatch = true;
                int[] idx = GetColumnsIndex(sheet, colNames);
                for (int i = 0; i < idx.Length; ++i)
                {
                    if (idx[i] == -1)
                    {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch)
                {
                    return GetTable(shtName, colNames);
                }
            }
            return null;
        }

        /// <summary>
        /// 获取指定工作表的所有列内容
        /// </summary>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public ExcelNPOITable GetTable(string sheetName)
        {
            List<string> colNames = GetColumns(sheetName);
            return GetTable(sheetName, colNames);
        }

        /// <summary>
        /// 获取第一个非空工作表的所有列内容
        /// </summary>
        /// <returns>如果所有工作表都为空，返回null</returns>
        public ExcelNPOITable GetTable()
        {
            List<string> colNames = GetColumns();
            return GetTable(colNames);
        }

        /// <summary>
        /// 从工作表中获取指定列名所在的列索引，索引从0开始
        /// </summary>
        /// <param name="sheet"></param>
        /// <param name="colNames"></param>
        /// <returns></returns>
        private int[] GetColumnsIndex(ISheet sheet, List<string> colNames)
        {
            int[] idx = new int[colNames.Count];
            for (int i = 0; i < idx.Length; ++i)
            {
                idx[i] = -1;
            }

            IRow firstRow = sheet.GetRow(sheet.FirstRowNum);
            if (firstRow == null)
            {
                return idx;
            }

            for (int i = 0; i < colNames.Count; ++i)
            {
                string colName = colNames[i];
                for (int j = firstRow.FirstCellNum; j <= firstRow.LastCellNum; ++j)
                {
                    ICell cell = firstRow.GetCell(j);
                    if (cell == null)
                    {
                        continue;
                    }

                    object value = GetCellValue(cell);
                    if (value == null || value.ToString().Trim() != colName)
                    {
                        continue;
                    }

                    idx[i] = j;
                    break;
                }
            }
            return idx;
        }

        private object GetCellValue(ICell cell)
        {
            switch (cell.CellType)
            {
                case CellType.Blank:
                    return "";
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Numeric:
                    return cell.NumericCellValue;
                case CellType.String:
                    return cell.StringCellValue;
                default:
                    return null;
            }
        }

        private IWorkbook book { get; set; }
    }

    /// <summary>
    /// 工作表内容
    /// </summary>
    public class ExcelNPOITable
    {
        /// <summary>
        /// 内容起始行，从1开始
        /// </summary>
        public int FirstRow { get; set; }
        
        /// <summary>
        /// 内容结束行，从1开始
        /// </summary>
        public int LastRow { get; set; }

        /// <summary>
        /// 列索引，从1开始
        /// </summary>
        public int[] ColumnsIndex { get; set; }

        /// <summary>
        /// 所有表格值，表格值可能为null
        /// 保证object[].Length == ColumnsIndex.Length
        /// 且List.Count == LastRow - FirstRow + 1
        /// </summary>
        public List<object[]> CellValues { get; set; }
    }

    public static class ExcelOleDbReader
    {
        public static List<DataTable> ReadTables(string filePath)
        {
            string connStr = GetConnectionString(filePath, 1);
            OleDbConnection conn = null;
            OleDbDataAdapter da = null;
            DataSet ds = new DataSet();
            List<DataTable> retList = new List<DataTable>();
            try
            {
                conn = new OleDbConnection(connStr);
                conn.Open();

                DataTable dtSheets = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
                da = new OleDbDataAdapter();
                for (int i = 0; i < dtSheets.Rows.Count; i++)
                {
                    string curSheet = (string)dtSheets.Rows[i]["TABLE_NAME"];
                    if (curSheet.Contains("$") && !curSheet.Replace("'", "").EndsWith("$"))
                    {
                        continue;
                    }

                    da.SelectCommand = new OleDbCommand(String.Format("select * from [{0}]", curSheet), conn);
                    try
                    {
                        da.Fill(ds, curSheet);
                    }
                    catch
                    {
                        //continue
                    }
                }

                foreach (DataTable dt in ds.Tables)
                {
                    retList.Add(dt.Copy());
                }
                ds.Clear();
            }
            finally
            {
                if (conn!= null && conn.State == ConnectionState.Open)
                {
                    conn.Close();
                    da.Dispose();
                    conn.Dispose();
                    ds.Dispose();
                }
            }
            return retList;
        }

        private static string GetConnectionString(string filePath, int imex)
        {
            string fileType = System.IO.Path.GetExtension(filePath);
            if (string.IsNullOrEmpty(fileType))
            {
                return null;
            }

            string connStr = "";
            if (fileType == ".xls")
            {
                connStr = string.Format("Provider=Microsoft.Jet.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;IMEX={1}'",
                    filePath, imex);
            }
            else
            {
                connStr = string.Format("Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0 XML;HDR=YES;IMEX={1}'",
                    filePath, imex);
            }
            return connStr;
        }
    }

    public static class CsvFileReader
    {
        public static DataTable ReadTable(string filePath)
        {
            char[] spliter = new char[] { ',' };
            DataTable table = null;
            using (StreamReader reader = new StreamReader(filePath, Encoding.Default))
            {
                int iLoop = 0;
                while (true)
                {
                    string curLine = reader.ReadLine();
                    if (curLine == null)
                    {
                        break;
                    }

                    string[] values = curLine.Split(spliter, StringSplitOptions.None);
                    if (iLoop++ == 0)
                    {
                        table = CreateDataTable(values);
                    }
                    else
                    {
                        InsertDataRow(table, values);
                    }
                }
            }
            return table;
        }

        private static DataTable CreateDataTable(string[] columns)
        {
            DataTable table = new DataTable();
            foreach (string colName in columns)
            {
                table.Columns.Add(colName, typeof(string));
            }
            return table;
        }

        private static void InsertDataRow(DataTable table, string[] values)
        {
            if (table.Columns.Count == values.Length)
            {
                table.Rows.Add(values);
                return;
            }

            List<string> valueList = new List<string>(values);
            for (int i = valueList.Count; i < table.Columns.Count; ++i) // 补全
            {
                valueList.Add(null);
            }
            while (valueList.Count > table.Columns.Count)               // 截短
            {
                valueList.RemoveAt(valueList.Count - 1);
            }
            table.Rows.Add(valueList.ToArray());
        }
    }

    public static class DataRowReader
    {
        public static bool GetInt(DataRow dr, string colName, out int value)
        {
            value = 0;
            return !Convert.IsDBNull(dr[colName]) && int.TryParse(dr[colName].ToString(), out value);
        }

        public static bool GetFloat(DataRow dr, string colName, out float value)
        {
            value = 0;
            return !Convert.IsDBNull(dr[colName]) && float.TryParse(dr[colName].ToString(), out value);
        }

        public static bool GetDouble(DataRow dr, string colName, out double value)
        {
            value = 0;
            return !Convert.IsDBNull(dr[colName]) && double.TryParse(dr[colName].ToString(), out value);
        }

        public static bool GetString(DataRow dr, string colName, out string value)
        {
            value = null;
            if (Convert.IsDBNull(dr[colName]))
            {
                return false;
            }
            value = dr[colName].ToString();
            return true;
        }

        public static bool GetDateTime(DataRow dr, string colName, out DateTime value)
        {
            value = DateTime.MaxValue;
            if (Convert.IsDBNull(dr[colName]))
            {
                return false;
            }
            return DateTime.TryParse(dr[colName].ToString(), out value);
        }
    }
}
