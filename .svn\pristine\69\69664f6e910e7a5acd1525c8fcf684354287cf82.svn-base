﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class MapNRCellLayerAntennaProperties : MTLayerPropUserControl
    {
        public MapNRCellLayerAntennaProperties()
        {
            InitializeComponent();
        }

        private MapNRCellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapNRCellLayer;
            if (layer==null)
            {
                return;
            }
            Text = "天线";
            this.checkBoxDisplay.Checked = layer.DrawAntenna;
            this.colorAntenna.Color = layer.ColorAntenna;
            this.TrackBarOpacity.Value = layer.ColorAntenna.A;
            this.cbxDrawAntennaLabel.Checked = layer.DrawAntennaLabel;
            this.cbxAntennaAltitude.Checked = layer.DrawAntennaAltitude;
            this.cbxAntennaDescription.Checked = layer.DrawAntennaDescription;
            this.cbxAntennaDirection.Checked = layer.DrawAntennaDirection;
            this.cbxAntennaDirectionType.Checked = layer.DrawAntennaDirectionType;
            this.cbxAntennaDownward.Checked = layer.DrawAntennaDownward;
            this.cbxAntennaLatitude.Checked = layer.DrawAntennaLatitude;
            this.cbxAntennaLongitude.Checked = layer.DrawAntennaLongitude;

            checkBoxDisplay.CheckedChanged += new EventHandler(checkBoxDisplay_CheckedChanged);
            colorAntenna.ColorChanged += new EventHandler(colorAntenna_ColorChanged);
            TrackBarOpacity.ValueChanged += new EventHandler(TrackBarOpacity_ValueChanged);
            cbxDrawAntennaLabel.CheckedChanged += new EventHandler(cbxDrawAntennaLabel_CheckedChanged);
            cbxAntennaAltitude.CheckedChanged += new EventHandler(cbxAntennaAltitude_CheckedChanged);
            cbxAntennaDescription.CheckedChanged += new EventHandler(cbxAntennaDescription_CheckedChanged);
            cbxAntennaDirection.CheckedChanged += new EventHandler(cbxAntennaDirection_CheckedChanged);
            cbxAntennaDirectionType.CheckedChanged += new EventHandler(cbxAntennaDirectionType_CheckedChanged);
            cbxAntennaDownward.CheckedChanged += new EventHandler(cbxAntennaDownward_CheckedChanged);
            cbxAntennaLatitude.CheckedChanged += new EventHandler(cbxAntennaLatitude_CheckedChanged);
            cbxAntennaLongitude.CheckedChanged += new EventHandler(cbxAntennaLongitude_CheckedChanged);
            btnFont.Click += new EventHandler(btnFont_Click);
        }

        FontDialog fontDialog = new FontDialog();
        void btnFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontCellLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontAntennaLabel = fontDialog.Font;
            }
        }
        void cbxAntennaLongitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaLongitude = cbxAntennaLongitude.Checked;
        }

        void cbxAntennaLatitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaLatitude = cbxAntennaLatitude.Checked;
        }

        void cbxAntennaDownward_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDownward = cbxAntennaDownward.Checked;
        }

        void cbxAntennaDirection_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDirection = cbxAntennaDirection.Checked;
        }

        void cbxAntennaDirectionType_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDirectionType = cbxAntennaDirectionType.Checked;
        }

        void cbxAntennaDescription_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDescription = cbxAntennaDescription.Checked;
        }

        void cbxAntennaAltitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaAltitude = cbxAntennaAltitude.Checked;
        }

        void cbxDrawAntennaLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaLabel = cbxDrawAntennaLabel.Checked;
        }

        void TrackBarOpacity_ValueChanged(object sender, EventArgs e)
        {
            layer.ColorAntenna = Color.FromArgb(TrackBarOpacity.Value, layer.ColorAntenna);
        }

        void colorAntenna_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorAntenna = colorAntenna.Color;
        }

        void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntenna = checkBoxDisplay.Checked;
        }
    }
}
