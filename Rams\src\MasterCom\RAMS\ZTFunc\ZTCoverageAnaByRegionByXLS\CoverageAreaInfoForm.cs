﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CoverageAreaInfoForm : BaseForm
    {
        int Radius = 0;
        public CoverageAreaInfoForm(MainModel mainModel, int radius)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.Radius = radius;
            InitializeComponent();
        }

        public void FillData(List<CoverageAreaInfo> coverageInfoList)
        {
            gridControl1.DataSource = coverageInfoList;
            gridControl1.RefreshDataSource();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] hRows = gridView1.GetSelectedRows();
            if (hRows.Length <= 0) return;
            CoverageAreaInfo cov = gridView1.GetRow(hRows[0]) as CoverageAreaInfo;
            if (cov == null) return;
            mainModel.DTDataManager.Clear();
            mainModel.SelectedTestPoints.Clear();
            foreach (TestPoint tp in cov.TpList)
            {
                mainModel.DTDataManager.Add(tp);
                mainModel.SelectedTestPoints.Add(tp);
            }
            mainModel.MainForm.GetMapForm().DrawCircle(
                new DbPoint(cov.Longitude, cov.Latitude),
                Radius * 0.00001);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(this.bandedGridView1);
            }
            catch
            {
                MessageBox.Show("导出xls失败");
            }
        }
    }
}
