﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using DevExpress.XtraEditors;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.CheckCellOccupation;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCheckCellOccupationBase : DIYAnalyseByFileBackgroundBase
    {
        protected bool isLteFdd = false;
        protected static readonly object lockObj = new object();

        public ZTCheckCellOccupationBase(MainModel model)
            : base(model)
        {
        }

        public ZTCheckCellOccupationBase(bool isVoLTE)
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }

        public override string Name
        {
            get { return "小区占用核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14021, this.Name);
        }

        Condition_CheckCellOccupation con = new Condition_CheckCellOccupation(-110, 1000, -90, 3, 1.6);//设置参数
        CheckCellOccupationConditionFrom condForm;
        protected override bool getCondition()
        {
            if (condForm == null)
            {
                condForm = new CheckCellOccupationConditionFrom();
            }
            condForm.SetCondition(con);
            if (condForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            condForm.GetCondition(ref this.con);

            return true;
        }

        protected override void fireShowForm()
        {
            CheckCellOccupationResultForm resultFrom = MainModel.CreateResultForm(typeof(CheckCellOccupationResultForm)) as CheckCellOccupationResultForm;
            this.cellTpList.GetResult(this.con);
            List<Result> listResult = this.cellTpList.listResult;
            resultFrom.FillData(listResult);
            resultFrom.Visible = true;
            resultFrom.BringToFront();
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            this.cellTpList = new CellTpList();
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        CellTpList cellTpList = null;
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> listTp = fileDataManager.TestPoints;
                foreach (TestPoint tp in listTp)
                {
                    this.cellTpList.AddTestPoint(tp, isLteFdd);
                }
            }
        }

            
        protected override void getResultsAfterQuery()
        {
            //
        }
    }

    public class ZTCheckCellOccupyByRegion : ZTCheckCellOccupationBase
    {
        private static ZTCheckCellOccupyByRegion instance = null;
        public static ZTCheckCellOccupyByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCheckCellOccupyByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get
            {
                return "占用核查(按区域)";
            }
        }
        protected ZTCheckCellOccupyByRegion()
            : base(false)
        {
            FilterSampleByRegion = true;
        }
        public ZTCheckCellOccupyByRegion(bool isVoLTE)
            : base(isVoLTE)
        {
            FilterSampleByRegion = true;
        }
    }

    public class ZTCheckCellOccupyByFile : ZTCheckCellOccupationBase
    {
        private static ZTCheckCellOccupyByFile intance = null;
        public static ZTCheckCellOccupyByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCheckCellOccupyByFile();
                    }
                }
            }
            return intance;

        }

        protected ZTCheckCellOccupyByFile()
            : base(false)
        {
        }
        public ZTCheckCellOccupyByFile(bool isVoLTE)
            : base(isVoLTE)
        {
        }

        public override string Name
        {
            get
            {
                return "占用核查(按文件)";
            }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;

        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }


        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }
}
