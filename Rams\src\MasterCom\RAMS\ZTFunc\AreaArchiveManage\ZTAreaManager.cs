﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class ZTAreaManager
    {
        private static ZTAreaManager instance { get; set; }
        public List<AreaRank> Ranks
        {
            get;
            set;
        }
        public AreaRank LowestRank
        {
            get
            {
                AreaRank rank = null;
                if (Ranks.Count > 0)
                {
                    rank = Ranks[Ranks.Count - 1];
                }
                return rank;
            }
        }
        public List<AreaBase> GetLowestAreas()
        {
            return GetArea(this.LowestRank);
        }

        private ZTAreaManager()
        {
            MainModel.GetInstance().DistrictChanged += ZTAreaManager_DistrictChanged;
            initRanks();
            WaitTextBox.Show("正在获取区域信息...", loadFromDb);
        }

        void ZTAreaManager_DistrictChanged(object sender, EventArgs e)
        {
            instance = null;
        }

        private void initRanks()
        {
            Ranks = new List<AreaRank>();
            int id = 0;
            Ranks.Add(new AreaRank(id++, "地市", 140, null));
            Ranks.Add(new AreaRank(id++, "县区", 130, Ranks[Ranks.Count - 1]));
            Ranks.Add(new AreaRank(id++, "乡镇", 120, Ranks[Ranks.Count - 1]));
            Ranks.Add(new AreaRank(id, "村庄", 110, Ranks[Ranks.Count - 1]));
            Ranks[Ranks.Count - 1].HasChildren = false;
        }

        public static ZTAreaManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ZTAreaManager();
                }
                return instance;
            }
        }

        private readonly Dictionary<AreaRank, List<AreaBase>> rankAreaDic = new Dictionary<AreaRank, List<AreaBase>>();
        public AreaBase GetArea(AreaRank rank,string areaName)
        {
            if (rank==null)
            {
                return null;
            }
            AreaBase area = null;
            List<AreaBase> areaSet = null;
            if (rankAreaDic.TryGetValue(rank, out areaSet))
            {
                area = areaSet.Find(delegate(AreaBase x) { return x.Name == areaName; });
            }
            return area;
        }

        public List<AreaBase> GetArea(AreaRank rank)
        {
            List<AreaBase> ret = null;
            rankAreaDic.TryGetValue(rank, out ret);
            return ret;
        }
        
        public List<AreaBase> AllAreas
        {
            get
            {
                List<AreaBase> areas = new List<AreaBase>(idAreaDic.Values);
                return areas;
            }
        }
        private readonly Dictionary<int, AreaBase> idAreaDic = new Dictionary<int, AreaBase>();
        public void AddArea(AreaBase areaItem)
        {
            idAreaDic[areaItem.ID] = areaItem;

            List<AreaBase> areaSet = null;
            if (rankAreaDic.TryGetValue(areaItem.Rank, out areaSet))
            {
                AreaBase area = areaSet.Find(delegate(AreaBase x) { return x.ID == areaItem.ID; });
                if (area == null)
                {
                    areaSet.Add(areaItem);
                }
            }
            else
            {
                areaSet = new List<AreaBase>();
                areaSet.Add(areaItem);
                rankAreaDic[areaItem.Rank] = areaSet;
            }
        }

        private readonly List<AreaBase> rootAreaList = new List<AreaBase>();
        public List<AreaBase> RootAreaList { get { return rootAreaList; } }
        public void AddRootArea(AreaBase rootItem)
        {
            rootAreaList.Add(rootItem);
        }

        public AreaBase GetRoot(AreaBase areaItem)
        {
            foreach (AreaBase area in RootAreaList)
            {
                if (areaItem.StrComment.StartsWith(area.StrComment))
                    return area;
            }
            return null;
        }

        public AreaBase GetArea(int id)
        {
            AreaBase area;
            idAreaDic.TryGetValue(id, out area);
            return area;
        }

        private void loadFromDb()
        {
            LoadAreaFromDb loading = new LoadAreaFromDb(this);
            loading.Query();
            WaitTextBox.Close();
        }

        internal AreaRank GetRank(int rankID)
        {
            AreaRank rank = Ranks.Find(delegate(AreaRank x) { return x.ID == rankID; });
            return rank;
        }

    }
}
