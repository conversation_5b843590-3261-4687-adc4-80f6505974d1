﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.Injection.TestPlanMultiLayer
{
    public partial class SettingDlg : BaseDialog
    {
        public SettingDlg()
        {
            InitializeComponent();
            dataGridView.AutoGenerateColumns = false;
            this.cmbAreaType.Properties.Items.Clear();
            foreach (CategoryEnumItem item in AreaManager.GetInstance().AreaTypes.Items)
            {
                this.cmbAreaType.Properties.Items.Add(item);
            }
            fillCmbItem(null);
        }

        private void btnRegionPath_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                Shapefile file = new Shapefile();
                if (!file.Open(dlg.FileName, null))
                {
                    MessageBox.Show("打开图层文件异常！");
                    return;
                }
                if (file.ShapefileType != ShpfileType.SHP_POLYGON)
                {
                    MessageBox.Show("该图层不是有效的多边形图层！");
                    return;
                }
                btnRegionPath.Text = dlg.FileName;
                cmbFieldIdx.Properties.Items.Clear();
                List<string> fields = new List<string>();
                for (int i = 0; i < file.NumFields; i++)
                {
                    string name = file.get_Field(i).Name;
                    fields.Add(name);
                    cmbFieldIdx.Properties.Items.Add(name);
                }
                file.Close();
                if (curCityOption != null)
                {
                    curCityOption.RegionLayerPath = dlg.FileName;
                    curCityOption.RegionFields = fields;
                }
            }
        }

        private void btnRemoveShpFile_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                foreach (DataGridViewRow row in dataGridView.SelectedRows)
                {
                    StreetInjectTableInfo layer = row.DataBoundItem as StreetInjectTableInfo;
                    this.curCityOption.RoadLayers.Remove(layer);
                }
                dataGridView.DataSource = null;
                if (this.curCityOption.RoadLayers.Count > 0)
                {
                    dataGridView.DataSource = this.curCityOption.RoadLayers;
                    dataGridView.CurrentCell = dataGridView.Rows[0].Cells[1];
                }
                dataGridView.Invalidate();
            }
            else
            {
                MessageBox.Show("请选择要移除的图层文件！");
            }
        }

        private void btnAddShpFile_Click(object sender, EventArgs e)
        {
            addShpFile(null);
        }

        private void addShpFile(string filePath)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Shp;
            dlg.Multiselect = filePath == null;
            dlg.FileName = filePath;
            List<StreetInjectTableInfo> layers = new List<StreetInjectTableInfo>();
            StringBuilder invalidFiles = new StringBuilder();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                foreach (string path in dlg.FileNames)
                {
                    removeOldLayer(path);

                    List<string> fields;
                    Shapefile shpFile = getShpFileProp(path, out fields);
                    if (shpFile != null && shpFile.ShapefileType != ShpfileType.SHP_POLYLINE)
                    {
                        StreetInjectTableInfo info = new StreetInjectTableInfo();
                        info.FilePath = path;
                        info.Fields = fields;
                        layers.Add(info);
                    }
                    else
                    {
                        invalidFiles.Append(path + Environment.NewLine);
                    }
                }
            }

            if (!string.IsNullOrEmpty(invalidFiles.ToString()))
            {
                MessageBox.Show("非道路图层文件：" + Environment.NewLine + invalidFiles.ToString());
            }
            if (layers.Count > 0)
            {
                foreach (StreetInjectTableInfo item in layers)
                {
                    this.curCityOption.RoadLayers.Add(item);
                }
            }
            if (this.curCityOption.RoadLayers.Count > 0)
            {
                dataGridView.DataSource = this.curCityOption.RoadLayers;
                if (dataGridView.Rows.Count > 0)
                {
                    dataGridView.CurrentCell = dataGridView.Rows[0].Cells[1];
                }
            }
            dataGridView.Invalidate();
        }

        private void removeOldLayer(string path)
        {
            StreetInjectTableInfo old = null;
            foreach (StreetInjectTableInfo i in this.curCityOption.RoadLayers)
            {
                if (i.FilePath == path)
                {
                    old = i;
                    break;
                }
            }
            if (old != null)
            {
                this.curCityOption.RoadLayers.Remove(old);
            }
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex != colShpFile.Index)
            {
                return;
            }

            string fileName = null;
            DataGridViewRow row = dataGridView.Rows[e.RowIndex];
            if (row != null)
            {
                StreetInjectTableInfo layer = row.DataBoundItem as StreetInjectTableInfo;
                if (layer != null)
                {
                    fileName = layer.FilePath;
                }
            }
            addShpFile(fileName);
        }

        private Shapefile getShpFileProp(string fileName, out List<string> fields)
        {
            fields = new List<string>();
            Shapefile shp = new Shapefile();
            if (!shp.Open(fileName, null))
            {
                return null;
            }

            for (int i = 0; i < shp.NumFields; ++i)
            {
                Field field = shp.get_Field(i);
                fields.Add(field.Name);
            }
            shp.Close();
            return shp;
        }

        private void dataGridView_DataSourceChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                StreetInjectTableInfo layer = row.DataBoundItem as StreetInjectTableInfo;
                if (layer != null)
                {
                    DataGridViewComboBoxCell cbxCell = ((DataGridViewComboBoxCell)row.Cells["colFieldName"]);
                    cbxCell.Items.Clear();
                    foreach (string fName in layer.Fields)
                    {
                        cbxCell.Items.Add(fName);
                    }
                    if (cbxCell.Items.Count > 0)
                    {
                        if (layer.ColumnName != null)
                        {
                            cbxCell.Value = layer.ColumnName;
                        }
                        else
                        {
                            cbxCell.Value = cbxCell.Items[0];
                        }
                    }
                }
            }
        }

        CityOption curCityOption = null;

        private void selectedOptionChanged(CityOption option)
        {
            curCityOption = null;
            this.btnRegionPath.Text = string.Empty;
            this.cmbFieldIdx.Properties.Items.Clear();
            this.dataGridView.DataSource = null;
            curCityOption = option;
            if (curCityOption != null)
            {
                this.btnRegionPath.Text = curCityOption.RegionLayerPath;
                this.cmbFieldIdx.Properties.Items.Clear();
                if (curCityOption.RegionFields != null)
                {
                    foreach (string name in curCityOption.RegionFields)
                    {
                        this.cmbFieldIdx.Properties.Items.Add(name);
                    }
                    this.cmbFieldIdx.SelectedItem = curCityOption.RegionNameField;
                }
                if (curCityOption.RoadLayers.Count > 0)
                {
                    this.dataGridView.DataSource = curCityOption.RoadLayers;
                }
            }
            this.dataGridView.Invalidate();
        }

        private void cmbFieldIdx_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curCityOption != null)
            {
                curCityOption.RegionNameField = cmbFieldIdx.SelectedItem as string;
                curCityOption.RegionNameFieldIdx = cmbFieldIdx.SelectedIndex;
            }
        }

        private void btnNewItem_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建项", "名称", "新建项");
            if (box.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            AreaTypeOption o = new AreaTypeOption();
            o.Name = box.TextInput;
            CfgManager.Instance.AreaTypeOptions.Add(o);
            fillCmbItem(o);
        }

        private void fillCmbItem(AreaTypeOption option)
        {
            this.cmbOption.Properties.Items.Clear();
            foreach (AreaTypeOption op in CfgManager.Instance.AreaTypeOptions)
            {
                this.cmbOption.Properties.Items.Add(op);
            }
            if (option != null)
            {
                this.cmbOption.SelectedItem = option;
                this.cmbAreaType.SelectedItem = option.AreaType;
            }
            else if (this.cmbOption.Properties.Items.Count > 0)
            {
                this.cmbOption.SelectedIndex = 0;
            }
        }

        private void btnRemoveItem_Click(object sender, EventArgs e)
        {
            AreaTypeOption op = cmbOption.SelectedItem as AreaTypeOption;
            if (op != null)
            {
                CfgManager.Instance.AreaTypeOptions.Remove(op);
                fillCmbItem(null);
            }
        }

        private AreaTypeOption curAreaOption = null;
        private void cmbOption_SelectedIndexChanged(object sender, EventArgs e)
        {
            visiualizeAreaOption(this.cmbOption.SelectedItem as AreaTypeOption);
        }

        private void visiualizeAreaOption(AreaTypeOption option)
        {
            this.treeList.DataSource = null;
            curAreaOption = option;
            if (option == null)
            {
                return;
            }
            this.cmbAreaType.SelectedItem = option.AreaType;
            this.treeList.Nodes.Clear();
            this.treeList.DataSource = option.CityOptions;
            this.treeList.RefreshDataSource();
        }

        private void treeList_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            selectedOptionChanged(treeList.GetDataRecordByNode(e.Node) as CityOption);
        }

        private void cmbAreaType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curAreaOption != null)
            {
                curAreaOption.AreaType = cmbAreaType.SelectedItem as CategoryEnumItem;
            }
        }

        private void dataGridView_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            e.ThrowException = false;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            CfgManager.Instance.Save();
            DialogResult = DialogResult.OK;
        }


        internal AreaTypeOption AreaOption
        {
            get
            {
                return cmbOption.SelectedItem as AreaTypeOption;
            }
            set
            {
                fillCmbItem(value);
            }
        }
    }
}
