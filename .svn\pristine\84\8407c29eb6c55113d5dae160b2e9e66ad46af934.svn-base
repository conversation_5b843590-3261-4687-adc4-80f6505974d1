﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.MTGis
{
    public abstract class LegendGroup<T> : ILegend where T : IGis
    {
        public override string ToString()
        {
            return this.Title;
        }
        public virtual string Title
        {
            get;
            set;
        }
        private object tag = null;
        public virtual object Tag
        {
            get { return tag; }
            set
            {
                tag = value;
                if (Items != null)
                {
                    foreach (LegendGroupSubItem<T> item in Items)
                    {
                        item.Tag = tag;
                    }
                }
            }
        }
        private bool visible = true;
        public bool Visible
        {
            get { return visible; }
            set
            {
                visible = value;
                foreach (LegendGroupSubItem<T> item in Items)
                {
                    item.Visible = value;
                }
            }
        }
        public List<LegendGroupSubItem<T>> Items { get; set; } = new List<LegendGroupSubItem<T>>();
        public abstract void DrawOnLayer(MapOperation map, Graphics graphics, PointF location, T entity2Draw);

        #region ILegend 成员
        public virtual void DrawOnListBox(ListBox listBox, DrawItemEventArgs e)
        {
            string text = string.Format("[{0}] {1}", visible ? "√" : "  ", Title);
            e.Graphics.DrawString(text, listBox.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y);
        }
        #endregion
    }

}
