﻿using MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_SX : StationAcceptBaseWithWorkParams<string,string>
    {
        #region 基础信息
        protected static readonly object lockObj = new object();
        private static StationAcceptAna_SX intance = null;
        public static StationAcceptAna_SX GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new StationAcceptAna_SX();
                    }
                }
            }
            return intance;
        }

        protected StationAcceptAna_SX()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            this.isIgnoreExport = true;
        }

        public override string Name
        {
            get
            {
                return "陕西单站验收";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18040, "查询");
        }

        protected override void init()
        {
            backgroundConfigManager = BackgroundFuncConfigManager.GetInstance();
            FilterSampleByRegion = false;
            IncludeEvent = false;

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);

            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");

            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_APP_type");
            Columns.Add("lte_APP_Status");
            Columns.Add("lte_PDCP_DL_Mb");
            Columns.Add("lte_PDCP_UL_Mb");

            workParamSumDic = new Dictionary<string, Dictionary<string, BtsAcceptWorkParamBase<string>>>();
        }
        #endregion

        #region 属性变量
        public StationAcceptCondition_SX StationCondition { get; set; } = new StationAcceptCondition_SX();
        private BackgroundFuncConfigManager backgroundConfigManager = null;
        private BtsAcceptRecordInfo_SX<string> recordInfo;
        private Dictionary<FileInfo, ErrorFileType> errorFileDic;

        protected StationAutoAcceptManager_SX_LTE manager;
        private List<CellAcceptFileInfo_SX_LTE> fileAnalysedResultList;
        #endregion

        #region 初始化
        protected override void loadWorkParams()
        {
            StationAcceptWorkParams_SX workParams = GetWorkParamsHelper_SX.GetInstance().GetWorkParams(StationCondition) as StationAcceptWorkParams_SX;
            if (workParams != null)
            {
                workParams.setWorkParam(workParamSumDic);
            }
        }

        protected override bool judgeWorkParams()
        {
            if (workParamSumDic == null || workParamSumDic.Count <= 0 || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待单验的基站信息");
                return false;
            }
            return true;
        }

        protected override void initCurBtsAcceptInfo()
        {
            errorFileDic = new Dictionary<FileInfo, ErrorFileType>();
            fileAnalysedResultList = new List<CellAcceptFileInfo_SX_LTE>();
        }

        protected override bool judgeValidBts(BtsAcceptWorkParamBase<string> btsInfo)
        {
            if (string.IsNullOrEmpty(StationCondition.FileNameFilters))
            {
                return true;
            }

            string[] split = new string[] { "or" };
            string[] filters = StationCondition.FileNameFilters.Split(split, StringSplitOptions.RemoveEmptyEntries);
            foreach (var filter in filters)
            {
                if (btsInfo.BtsNameFull.Contains(filter.Trim()))
                {
                    return true;
                }
                foreach (var cellInfo in btsInfo.CellWorkParams)
                {
                    if (cellInfo.CellNameFull.Contains(filter.Trim()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected override void setFileNameKeyStr(BtsAcceptWorkParamBase<string> btsInfo)
        {
            BtsAcceptWorkParam_SX sxBtsInfo = btsInfo as BtsAcceptWorkParam_SX;
            recordInfo = new BtsAcceptRecordInfo_SX<string>(btsInfo);
            StringBuilder strbFilter = new StringBuilder();
            strbFilter.Append(sxBtsInfo.BtsName);
            foreach (CellAcceptWorkParamBase info in btsInfo.CellWorkParamDic.Values)
            {
                CellAcceptWorkParam_SX sxInfo = info as CellAcceptWorkParam_SX;
                strbFilter.Append(" or ");
                strbFilter.Append(sxInfo.CellNameKey);
            }
            FileNameKeyStr = strbFilter.ToString();
        }
        #endregion

        #region 获取文件
        protected override bool filterFile(FileInfo fileInfo)
        {
            bool isValid = FileNameRuleHelper_SX.JudgeFileValidByName(fileInfo.Name);
            if (!isValid)
            {
                string strTip = string.Format("[{0}]是站[{1}]对应的环测文件或者并非单验文件", fileInfo.Name, curBtsInfo.BtsNameFull);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strTip);
                return true;
            }
            return false;
        }

        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(GetSubFuncID(), ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", FileNameKeyStr);

            if (mainModel.FileInfos.Count == 0)
            {
                recordInfo.BtsAcceptErrorInfo += "未找到基站对应的单验文件;";
                return;
            }

            StationAcceptHelper_SX.GetNewestFile(mainModel);
        }
        #endregion

        #region 回放后分析文件
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
			
            manager = getNewAcceptManager();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            saveFileAnalyzedResult();
            MainModel.DTDataManager.Clear();
        }

        protected virtual StationAutoAcceptManager_SX_LTE getNewAcceptManager()
        {
            return new StationAutoAcceptManager_SX_LTE();
        }

        private void saveFileAnalyzedResult()
        {
            ErrorFileType type = StationAcceptHelper_SX.JudgeError(manager);
            if (type == ErrorFileType.NULL)
            {
                fileAnalysedResultList.Add(manager.AcceptFileInfo as CellAcceptFileInfo_SX_LTE);

                var cellInfo = curBtsInfo.CellWorkParamDic[manager.AcceptFileInfo.CellNameKey];
                manager.AcceptFileInfo.BtsName = cellInfo.BtsNameFull;
                manager.AcceptFileInfo.CellName = cellInfo.CellNameFull;
            }
            else
            {
                errorFileDic.Add(curAnaFileInfo, type);
            }
        }
        #endregion

        #region 分析完文件后处理单验结果
        protected override void doSomethingAfterAnalyseFiles()
        {
            BtsAcceptInfo_SX<LTECell, string> btsAcceptInfo = getAcceptedResultInfo();
            if (btsAcceptInfo == null && mainModel.FileInfos.Count > 0)
            {
                //有文件进行过分析,但没有结果
                recordInfo.BtsAcceptErrorInfo += "当前站没有分析结果;";
            }
            else if (btsAcceptInfo != null && btsAcceptInfo.IsOutDoor)
            {
                QueryCondition queryCond = StationAcceptHelper_SX.GetQueryCondition(backgroundConfigManager);
                getOutCellCircleTestInfo(btsAcceptInfo, queryCond);
            }

            ExportReportHelper_SX_LTE export = new ExportReportHelper_SX_LTE();
            export.ExportReportToExcel(btsAcceptInfo, StationCondition.FilePath, recordInfo);

            StationAcceptHelper_SX.BackToBackgroundWorkSheet();
            string errorInfo = dealErrorInfo();
            recordInfo.Calculate();

            //导出无误后更新工参状态
            var update = new UpdateWorkParamDes_SX(curBtsInfo.BtsNameFull, errorInfo, recordInfo.IsBtsPassAccept, NetType.LTE);
            update.Query();

            var record = new UpLoadAcceptRecord_SX<string>(recordInfo, NetType.LTE);
            record.Query();
        }

        #region 获取单验结果
        protected BtsAcceptInfo_SX<LTECell, string> getAcceptedResultInfo()
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取已分析文件结果" + fileAnalysedResultList.Count + "个...");

            if (fileAnalysedResultList.Count == 0)
            {
                return null;
            }
            var fstFileRes = fileAnalysedResultList[0];
            bool isOutDoor = fstFileRes.Cell.Type == LTEBTSType.Outdoor;
            int btsId = fstFileRes.Cell.BelongBTS.BTSID;
            BtsAcceptInfo_SX<LTECell, string> btsAcceptInfo = getNewBtsAcceptInfo(isOutDoor, fstFileRes.BtsName, btsId);
         
            foreach (var item in fileAnalysedResultList)
            {
                AcceptKpiInfo_SX kpiInfo = new AcceptKpiInfo_SX();
                kpiInfo.Add(item);

                if (!btsAcceptInfo.CellAcceptInfoDic.TryGetValue(item.CellName, out var cellAcceptInfo))
                {
                    cellAcceptInfo = getNewCellAcceptInfo(isOutDoor);
                    cellAcceptInfo.CellId = item.CellId;
                    cellAcceptInfo.CellName = item.CellName;
                    cellAcceptInfo.BtsId = btsId;
                    cellAcceptInfo.BtsName = item.BtsName;
                    cellAcceptInfo.CellNameKey = item.CellNameKey;

                    btsAcceptInfo.CellAcceptInfoDic[item.CellName] = cellAcceptInfo;
                }
                cellAcceptInfo.AddFileAcceptInfo(item.FileNameKey, kpiInfo);
            }

            foreach (var item in btsAcceptInfo.CellAcceptInfoDic.Values)
            {
                item.Calculate();
            }

            dealCellRecord(btsAcceptInfo);
            return btsAcceptInfo;
        }

        protected virtual BtsAcceptInfo_SX<LTECell, string> getNewBtsAcceptInfo(bool isOutDoor, string btsName, int btsId)
        {
            BtsAcceptInfo_SX<LTECell, string> btsAcceptInfo = new BtsAcceptInfo_SX<LTECell, string>(isOutDoor)
            {
                BtsName = btsName,
                BtsId = btsId
            };
            return btsAcceptInfo;
        }

        protected virtual CellAcceptInfoBase_SX getNewCellAcceptInfo(bool isOutDoor)
        {
            CellAcceptInfoBase_SX cellAcceptInfo;
            if (isOutDoor)
            {
                cellAcceptInfo = new OutDoorCellAcceptInfo_SX();
            }
            else
            {
                cellAcceptInfo = new IndoorCellAcceptInfo_SX();
            }
            return cellAcceptInfo;
        }

        private void dealCellRecord(BtsAcceptInfo_SX<LTECell, string> btsInfo)
        {
            foreach (var cellInfo in btsInfo.CellAcceptInfoDic.Values)
            {
                foreach (var kpiDesc in cellInfo.KpiInfoDic)
                {
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.Rsrp.Count, kpiDesc.Key, "Rsrp");
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.Sinr.Count, kpiDesc.Key, "Sinr");
                    addCellErrorInfo(cellInfo.CellNameKey, kpiDesc.Value.Speed.Count, kpiDesc.Key, "Speed");
                }
            }
        }

        private void addCellErrorInfo(string cellNameKey, int tpCount, string kpiDesc, string type)
        {
            if (tpCount == 0)
            {
                recordInfo.CellRecordInfoDic[cellNameKey].CellAcceptErrorInfo += string.Format("没有获取到[{0}]{1}数据;", kpiDesc, type);
            }
        }
        #endregion

        private void getOutCellCircleTestInfo(BtsAcceptInfo_SX<LTECell, string> btsAcceptInfo, QueryCondition queryCond)
        {
            List<CellAcceptInfoBase_SX> cellInfoList = new List<CellAcceptInfoBase_SX>(btsAcceptInfo.CellAcceptInfoDic.Values);
            if (cellInfoList.Count > 0)
            {
                CellAcceptInfoBase_SX cellInfo = cellInfoList[0];
                LTECell cell = CellManager.GetInstance().GetLTECellLatest(cellInfo.CellNameKey);
                if (cell != null && cell.BelongBTS != null)
                {
                    btsAcceptInfo.CellList = cell.BelongBTS.Cells;
                }
            }

            if (queryCond != null)
            {
                StationAcceptHelper_SX.GetCellCircleTestTPs(Columns, btsAcceptInfo, queryCond, NetType.LTE);
            }
        }

        private string dealErrorInfo()
        {
            string curErrorinfo = "";
            if (!string.IsNullOrEmpty(recordInfo.BtsAcceptErrorInfo) || errorFileDic.Count > 0)
            {
                StringBuilder errorBulider = new StringBuilder();
                errorBulider.AppendLine(recordInfo.BtsAcceptErrorInfo);
                foreach (var errorFile in errorFileDic)
                {
                    string errorType = EnumDescriptionAttribute.GetText(errorFile.Value);
                    errorBulider.AppendLine("文件[" + errorFile.Key.Name + "]" + errorType + ";");
                }
                curErrorinfo = errorBulider.ToString();
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("单验问题信息 : " + curErrorinfo);
            }
            else
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("[{0}]单验正常\r\n", recordInfo.BtsName));
            }

            return curErrorinfo.Replace("\r\n", "");
        }
        #endregion

        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["BackgroundStat"] = BackgroundStat,
                    ["ExportReportSet"] = StationCondition.Params
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    StationCondition.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptPropertiesSX_LTE(this);
            }
        }
        #endregion
    }
}
