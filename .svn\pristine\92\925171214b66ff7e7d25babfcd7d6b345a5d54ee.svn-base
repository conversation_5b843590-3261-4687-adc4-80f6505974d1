﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.SiteCellInfo
{
    public class BTSSiteInfo
    {
        public LTEBTS BTS
        {
            get;
            set;
        }
        public BTSSiteInfo(LTEBTS bts)
        {
            this.BTS = bts;
        }

        private readonly Dictionary<SiteInfo, double> siteDisDic = new Dictionary<SiteInfo, double>();
        internal void AddSite(SiteInfo site, double dis)
        {
            siteDisDic[site] = dis;
        }

        private readonly Dictionary<BTSSiteInfo, double> otherDisDic = new Dictionary<BTSSiteInfo, double>();
        internal void AddOther(BTSSiteInfo other, double dis)
        {
            otherDisDic[other] = dis;
        }

        public int OtherCellCount
        {
            get;
            private set;
        }
        public double OtherCellAvgDis
        {
            get;
            private set;
        }
        public double OtherCellMinDis
        {
            get;
            private set;
        }
        public string CityName
        { get; private set; }
        public string GridName
        { get; private set; }
        public List<CellSiteInfo> MakeSummary()
        {
            CityName = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);
            GridName = GISManager.GetInstance().GetGridDesc(this.BTS.Longitude, this.BTS.Latitude);
            if (string.IsNullOrEmpty(GridName))
            {
                GridName = "非ATU网格";
            }
            List<CellSiteInfo> cells = new List<CellSiteInfo>();
            OtherCellMinDis = double.MaxValue;
            double sumDis = 0;
            foreach (BTSSiteInfo bts in this.otherDisDic.Keys)
            {
                OtherCellCount += bts.BTS.Cells.Count;
                double dis = otherDisDic[bts];
                sumDis += dis;
                OtherCellMinDis = Math.Min(OtherCellMinDis, dis);

                int sn = 1;
                foreach (LTECell cell in bts.BTS.Cells)
                {
                    if (siteDisDic.Count == 0)
                    {
                        CellSiteInfo info = new CellSiteInfo(cell, null, null, this, sn);
                        cells.Add(info);
                        sn++;
                    }
                    else
                    {
                        foreach (SiteInfo site in siteDisDic.Keys)
                        {
                            CellSiteInfo info = new CellSiteInfo(cell, site, siteDisDic[site], this, sn);
                            cells.Add(info);
                            sn++;
                        }
                    }
                   
                }
            }
            OtherCellAvgDis = Math.Round(sumDis / otherDisDic.Count, 2);
            return cells;
        }

    }
}
