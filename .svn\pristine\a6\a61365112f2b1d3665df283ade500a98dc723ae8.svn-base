﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanRoadMultiCoverageForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle2 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle3 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram4 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel10 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel11 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle4 = new DevExpress.XtraCharts.ChartTitle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScanRoadMultiCoverageForm));
            this.chartControlC_IAvg = new DevExpress.XtraCharts.ChartControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportMapInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToMapInfoGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToMapInfoWeakCoverGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSample = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.edtWeakCoverThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.chkFilterWeakCover = new DevExpress.XtraEditors.CheckEdit();
            this.btnConvertToGrid = new DevExpress.XtraEditors.SimpleButton();
            this.labelControlType = new DevExpress.XtraEditors.LabelControl();
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.btnColorRangeSetting = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupType = new DevExpress.XtraEditors.RadioGroup();
            this.radioGroupBand = new DevExpress.XtraEditors.RadioGroup();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlRelative = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlRelative = new DevExpress.XtraCharts.ChartControl();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlAbs = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlAbs = new DevExpress.XtraCharts.ChartControl();
            this.splitContainerControl6 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlRelORAbs = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlRelORAbs = new DevExpress.XtraCharts.ChartControl();
            this.miExportToExcelDataOnly = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IAvg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtWeakCoverThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterWeakCover.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupBand.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelative)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelative)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).BeginInit();
            this.splitContainerControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelORAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelORAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel11)).BeginInit();
            this.SuspendLayout();
            // 
            // chartControlC_IAvg
            // 
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlC_IAvg.Diagram = xyDiagram1;
            this.chartControlC_IAvg.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlC_IAvg.EmptyChartText.Text = "没有数据！";
            this.chartControlC_IAvg.Legend.Visible = false;
            this.chartControlC_IAvg.Location = new System.Drawing.Point(6, 0);
            this.chartControlC_IAvg.Name = "chartControlC_IAvg";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            this.chartControlC_IAvg.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControlC_IAvg.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControlC_IAvg.Size = new System.Drawing.Size(404, 476);
            this.chartControlC_IAvg.TabIndex = 33;
            chartTitle1.Text = "下载速率均值";
            this.chartControlC_IAvg.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiExport2Xls,
            this.miExportToExcelDataOnly,
            this.miExportMapInfo,
            this.miExportToMapInfoGrid,
            this.miExportToMapInfoWeakCoverGrid,
            this.miExportSample});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(227, 158);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // tsmiExport2Xls
            // 
            this.tsmiExport2Xls.Name = "tsmiExport2Xls";
            this.tsmiExport2Xls.Size = new System.Drawing.Size(226, 22);
            this.tsmiExport2Xls.Text = "导出Excel...";
            this.tsmiExport2Xls.Click += new System.EventHandler(this.tsmiExport2Xls_Click);
            // 
            // miExportMapInfo
            // 
            this.miExportMapInfo.Name = "miExportMapInfo";
            this.miExportMapInfo.Size = new System.Drawing.Size(226, 22);
            this.miExportMapInfo.Text = "导出shp图层...";
            this.miExportMapInfo.Click += new System.EventHandler(this.miExportMapInfo_Click);
            // 
            // miExportToMapInfoGrid
            // 
            this.miExportToMapInfoGrid.Name = "miExportToMapInfoGrid";
            this.miExportToMapInfoGrid.Size = new System.Drawing.Size(226, 22);
            this.miExportToMapInfoGrid.Text = "导出栅格到shp图层...";
            this.miExportToMapInfoGrid.Click += new System.EventHandler(this.miExportToMapInfoGrid_Click);
            // 
            // miExportToMapInfoWeakCoverGrid
            // 
            this.miExportToMapInfoWeakCoverGrid.Name = "miExportToMapInfoWeakCoverGrid";
            this.miExportToMapInfoWeakCoverGrid.Size = new System.Drawing.Size(226, 22);
            this.miExportToMapInfoWeakCoverGrid.Text = "导出弱覆盖栅格到shp图层...";
            this.miExportToMapInfoWeakCoverGrid.Click += new System.EventHandler(this.miExportToMapInfoWeakCoverGrid_Click);
            // 
            // miExportSample
            // 
            this.miExportSample.Name = "miExportSample";
            this.miExportSample.Size = new System.Drawing.Size(226, 22);
            this.miExportSample.Text = "导出采样点到Excel...";
            this.miExportSample.Click += new System.EventHandler(this.miExportSample_Click);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Controls.Add(this.edtWeakCoverThreshold);
            this.groupControl1.Controls.Add(this.chkFilterWeakCover);
            this.groupControl1.Controls.Add(this.btnConvertToGrid);
            this.groupControl1.Controls.Add(this.labelControlType);
            this.groupControl1.Controls.Add(this.labelControlBand);
            this.groupControl1.Controls.Add(this.btnColorRangeSetting);
            this.groupControl1.Controls.Add(this.radioGroupType);
            this.groupControl1.Controls.Add(this.radioGroupBand);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1122, 87);
            this.groupControl1.TabIndex = 20;
            this.groupControl1.Text = "GIS显示设置";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(1010, 34);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(24, 14);
            this.labelControl1.TabIndex = 10;
            this.labelControl1.Text = "dBm";
            // 
            // edtWeakCoverThreshold
            // 
            this.edtWeakCoverThreshold.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.edtWeakCoverThreshold.Location = new System.Drawing.Point(956, 31);
            this.edtWeakCoverThreshold.Name = "edtWeakCoverThreshold";
            this.edtWeakCoverThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtWeakCoverThreshold.Properties.IsFloatValue = false;
            this.edtWeakCoverThreshold.Properties.Mask.EditMask = "N00";
            this.edtWeakCoverThreshold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.edtWeakCoverThreshold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.edtWeakCoverThreshold.Size = new System.Drawing.Size(47, 21);
            this.edtWeakCoverThreshold.TabIndex = 9;
            // 
            // chkFilterWeakCover
            // 
            this.chkFilterWeakCover.Location = new System.Drawing.Point(814, 31);
            this.chkFilterWeakCover.Name = "chkFilterWeakCover";
            this.chkFilterWeakCover.Properties.Caption = "过滤弱覆盖栅格门限：";
            this.chkFilterWeakCover.Size = new System.Drawing.Size(136, 19);
            this.chkFilterWeakCover.TabIndex = 8;
            this.chkFilterWeakCover.CheckedChanged += new System.EventHandler(this.chkFilterWeakCover_CheckedChanged);
            // 
            // btnConvertToGrid
            // 
            this.btnConvertToGrid.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnConvertToGrid.Location = new System.Drawing.Point(1040, 28);
            this.btnConvertToGrid.Name = "btnConvertToGrid";
            this.btnConvertToGrid.Size = new System.Drawing.Size(77, 23);
            this.btnConvertToGrid.TabIndex = 3;
            this.btnConvertToGrid.Text = "栅格化处理";
            this.btnConvertToGrid.Click += new System.EventHandler(this.btnConvertToGrid_Click);
            // 
            // labelControlType
            // 
            this.labelControlType.Location = new System.Drawing.Point(296, 34);
            this.labelControlType.Name = "labelControlType";
            this.labelControlType.Size = new System.Drawing.Size(72, 14);
            this.labelControlType.TabIndex = 2;
            this.labelControlType.Text = "覆盖度类别：";
            // 
            // labelControlBand
            // 
            this.labelControlBand.Location = new System.Drawing.Point(10, 34);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(36, 14);
            this.labelControlBand.TabIndex = 2;
            this.labelControlBand.Text = "频段：";
            // 
            // btnColorRangeSetting
            // 
            this.btnColorRangeSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorRangeSetting.Location = new System.Drawing.Point(1040, 57);
            this.btnColorRangeSetting.Name = "btnColorRangeSetting";
            this.btnColorRangeSetting.Size = new System.Drawing.Size(77, 23);
            this.btnColorRangeSetting.TabIndex = 1;
            this.btnColorRangeSetting.Text = "着色设置...";
            this.btnColorRangeSetting.Click += new System.EventHandler(this.btnColorRangeSetting_Click);
            // 
            // radioGroupType
            // 
            this.radioGroupType.EditValue = true;
            this.radioGroupType.Location = new System.Drawing.Point(374, 28);
            this.radioGroupType.Name = "radioGroupType";
            this.radioGroupType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "相对覆盖度(-12dBm)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "绝对覆盖度(-80dBm)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "综合覆盖度")});
            this.radioGroupType.Size = new System.Drawing.Size(409, 28);
            this.radioGroupType.TabIndex = 0;
            this.radioGroupType.SelectedIndexChanged += new System.EventHandler(this.radioGroupType_SelectedIndexChanged);
            // 
            // radioGroupBand
            // 
            this.radioGroupBand.EditValue = true;
            this.radioGroupBand.Location = new System.Drawing.Point(52, 28);
            this.radioGroupBand.Name = "radioGroupBand";
            this.radioGroupBand.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "GSM900"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "DCS1800"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "总体")});
            this.radioGroupBand.Size = new System.Drawing.Size(226, 28);
            this.radioGroupBand.TabIndex = 0;
            this.radioGroupBand.ToolTip = "频段选择";
            this.radioGroupBand.SelectedIndexChanged += new System.EventHandler(this.radioGroupBand_SelectedIndexChanged);
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.Location = new System.Drawing.Point(0, 87);
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.splitContainerControl1);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.splitContainerControl6);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.splitContainerControl5.Size = new System.Drawing.Size(1122, 441);
            this.splitContainerControl5.SplitterPosition = 746;
            this.splitContainerControl5.TabIndex = 21;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitContainerControl3);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl4);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(746, 441);
            this.splitContainerControl1.SplitterPosition = 370;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControlRelative);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControlRelative);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(370, 441);
            this.splitContainerControl3.SplitterPosition = 223;
            this.splitContainerControl3.TabIndex = 0;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // gridControlRelative
            // 
            this.gridControlRelative.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRelative.Location = new System.Drawing.Point(0, 0);
            this.gridControlRelative.MainView = this.gridView1;
            this.gridControlRelative.Name = "gridControlRelative";
            this.gridControlRelative.Size = new System.Drawing.Size(370, 223);
            this.gridControlRelative.TabIndex = 0;
            this.gridControlRelative.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.gridControlRelative;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlRelative
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRelative.Diagram = xyDiagram2;
            this.chartControlRelative.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlRelative.Location = new System.Drawing.Point(0, 0);
            this.chartControlRelative.Name = "chartControlRelative";
            sideBySideBarSeriesLabel3.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel3;
            series2.Name = "Series 1";
            sideBySideBarSeriesLabel4.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel4;
            series3.Name = "Series 2";
            this.chartControlRelative.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2,
        series3};
            sideBySideBarSeriesLabel5.LineVisible = true;
            this.chartControlRelative.SeriesTemplate.Label = sideBySideBarSeriesLabel5;
            this.chartControlRelative.Size = new System.Drawing.Size(370, 212);
            this.chartControlRelative.TabIndex = 3;
            chartTitle2.Font = new System.Drawing.Font("Microsoft Sans Serif", 15F);
            chartTitle2.Text = "相对重叠覆盖度";
            this.chartControlRelative.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle2});
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl4.Horizontal = false;
            this.splitContainerControl4.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControlAbs);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControlAbs);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(370, 441);
            this.splitContainerControl4.SplitterPosition = 223;
            this.splitContainerControl4.TabIndex = 1;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // gridControlAbs
            // 
            this.gridControlAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlAbs.Location = new System.Drawing.Point(0, 0);
            this.gridControlAbs.MainView = this.gridView2;
            this.gridControlAbs.Name = "gridControlAbs";
            this.gridControlAbs.Size = new System.Drawing.Size(370, 223);
            this.gridControlAbs.TabIndex = 0;
            this.gridControlAbs.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControlAbs;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlAbs
            // 
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlAbs.Diagram = xyDiagram3;
            this.chartControlAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlAbs.Location = new System.Drawing.Point(0, 0);
            this.chartControlAbs.Name = "chartControlAbs";
            sideBySideBarSeriesLabel6.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel6;
            series4.Name = "Series 1";
            sideBySideBarSeriesLabel7.LineVisible = true;
            series5.Label = sideBySideBarSeriesLabel7;
            series5.Name = "Series 2";
            this.chartControlAbs.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series4,
        series5};
            sideBySideBarSeriesLabel8.LineVisible = true;
            this.chartControlAbs.SeriesTemplate.Label = sideBySideBarSeriesLabel8;
            this.chartControlAbs.Size = new System.Drawing.Size(370, 212);
            this.chartControlAbs.TabIndex = 3;
            chartTitle3.Font = new System.Drawing.Font("Tahoma", 15F);
            chartTitle3.Text = "绝对重叠覆盖度";
            this.chartControlAbs.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle3});
            // 
            // splitContainerControl6
            // 
            this.splitContainerControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl6.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl6.Horizontal = false;
            this.splitContainerControl6.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl6.Name = "splitContainerControl6";
            this.splitContainerControl6.Panel1.Controls.Add(this.gridControlRelORAbs);
            this.splitContainerControl6.Panel1.Text = "Panel1";
            this.splitContainerControl6.Panel2.Controls.Add(this.chartControlRelORAbs);
            this.splitContainerControl6.Panel2.Text = "Panel2";
            this.splitContainerControl6.Size = new System.Drawing.Size(370, 441);
            this.splitContainerControl6.SplitterPosition = 223;
            this.splitContainerControl6.TabIndex = 0;
            this.splitContainerControl6.Text = "splitContainerControl6";
            // 
            // gridControlRelORAbs
            // 
            this.gridControlRelORAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRelORAbs.Location = new System.Drawing.Point(0, 0);
            this.gridControlRelORAbs.MainView = this.gridView3;
            this.gridControlRelORAbs.Name = "gridControlRelORAbs";
            this.gridControlRelORAbs.Size = new System.Drawing.Size(370, 223);
            this.gridControlRelORAbs.TabIndex = 1;
            this.gridControlRelORAbs.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.GridControl = this.gridControlRelORAbs;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlRelORAbs
            // 
            xyDiagram4.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram4.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram4.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRelORAbs.Diagram = xyDiagram4;
            this.chartControlRelORAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlRelORAbs.Location = new System.Drawing.Point(0, 0);
            this.chartControlRelORAbs.Name = "chartControlRelORAbs";
            sideBySideBarSeriesLabel9.LineVisible = true;
            series6.Label = sideBySideBarSeriesLabel9;
            series6.Name = "Series 1";
            sideBySideBarSeriesLabel10.LineVisible = true;
            series7.Label = sideBySideBarSeriesLabel10;
            series7.Name = "Series 2";
            this.chartControlRelORAbs.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series6,
        series7};
            sideBySideBarSeriesLabel11.LineVisible = true;
            this.chartControlRelORAbs.SeriesTemplate.Label = sideBySideBarSeriesLabel11;
            this.chartControlRelORAbs.Size = new System.Drawing.Size(370, 212);
            this.chartControlRelORAbs.TabIndex = 4;
            chartTitle4.Font = new System.Drawing.Font("Tahoma", 15F);
            chartTitle4.Text = "综合重叠覆盖度";
            this.chartControlRelORAbs.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle4});
            // 
            // miExportToExcelDataOnly
            // 
            this.miExportToExcelDataOnly.Name = "miExportToExcelDataOnly";
            this.miExportToExcelDataOnly.Size = new System.Drawing.Size(226, 22);
            this.miExportToExcelDataOnly.Text = "导出数据到Excel...";
            this.miExportToExcelDataOnly.Click += new System.EventHandler(this.miExportToExcelDataOnly_Click);
            // 
            // ScanRoadMultiCoverageForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1122, 528);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.splitContainerControl5);
            this.Controls.Add(this.groupControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ScanRoadMultiCoverageForm";
            this.Text = "扫频数据道路重叠覆盖度";
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IAvg)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtWeakCoverThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterWeakCover.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupBand.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelative)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelative)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).EndInit();
            this.splitContainerControl6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRelORAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRelORAbs)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraCharts.ChartControl chartControlC_IAvg;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem tsmiExport2Xls;
        private System.Windows.Forms.ToolStripMenuItem miExportSample;
        private System.Windows.Forms.ToolStripMenuItem miExportMapInfo;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SimpleButton btnConvertToGrid;
        private DevExpress.XtraEditors.LabelControl labelControlType;
        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SimpleButton btnColorRangeSetting;
        private DevExpress.XtraEditors.RadioGroup radioGroupType;
        private DevExpress.XtraEditors.RadioGroup radioGroupBand;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraGrid.GridControl gridControlRelative;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraCharts.ChartControl chartControlRelative;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraGrid.GridControl gridControlAbs;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraCharts.ChartControl chartControlAbs;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl6;
        private DevExpress.XtraGrid.GridControl gridControlRelORAbs;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraCharts.ChartControl chartControlRelORAbs;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit edtWeakCoverThreshold;
        private DevExpress.XtraEditors.CheckEdit chkFilterWeakCover;
        private System.Windows.Forms.ToolStripMenuItem miExportToMapInfoGrid;
        private System.Windows.Forms.ToolStripMenuItem miExportToMapInfoWeakCoverGrid;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcelDataOnly;
    }
}