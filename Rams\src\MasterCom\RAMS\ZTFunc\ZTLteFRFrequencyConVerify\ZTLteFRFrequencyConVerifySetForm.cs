﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteFRFrequencyConVerifySetForm : BaseDialog
    {
        public ZTLteFRFrequencyConVerifySetForm()
        {
            InitializeComponent();
        }
         public FRFrequencyConVerifyByCondition GetCondition()
        {
            FRFrequencyConVerifyByCondition condition = new FRFrequencyConVerifyByCondition();
            condition.BeforeSecond = (int)numBeforeSecond.Value;
            condition.AfterSecond = (int)numAfterSecond.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
