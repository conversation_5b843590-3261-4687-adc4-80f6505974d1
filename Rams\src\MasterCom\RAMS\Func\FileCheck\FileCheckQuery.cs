﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.src.MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    public class FileCheckQuery : QueryBase
    {
        public DevExpress.XtraGrid.Views.Grid.GridView gv { get; set; }

        public override string Name
        {
            get
            {
                return "log文件分析";
            }
        }

        public FileCheckQuery(MainModel mainModel):base(mainModel)
        {
            
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            FileCheckForm frm = MainModel.CreateResultForm(typeof(FileCheckForm)) as FileCheckForm;
            frm.FillData(gv);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 400, 408, this.Name);
        }
    }
}
