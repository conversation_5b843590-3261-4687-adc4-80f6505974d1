﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PesudoBaseStationSearchByRegion : DIYSampleByRegion
    {
        private List<CRange> cRangeLacList;
        private int pesudoCI = 10;
        private bool chkPesudoCI = true;
        private bool chkPesudoLAC = true;
        private readonly Dictionary<string, PesudoBaseStationInfo> pesudoInfoDic;

        public PesudoBaseStationSearchByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            pesudoInfoDic = new Dictionary<string, PesudoBaseStationInfo>();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12087, this.Name);
        }

        protected override MasterCom.RAMS.Model.Interface.DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_RxLevSub");
            tmpDic.Add("themeName", (object)"GSM RxLevSub");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            PesudoBaseStationSettingDlg dlg = new PesudoBaseStationSettingDlg(pesudoCI, cRangeLacList, chkPesudoCI, chkPesudoLAC);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            chkPesudoCI = dlg.ChkPesudoCI;
            chkPesudoLAC = dlg.ChkPesudoLAC;
            cRangeLacList = dlg.CRangeList;
            pesudoCI = dlg.PesudoCI;
            return true;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            pesudoInfoDic.Clear();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (tp is TestPointDetail)
            {
                return base.isValidTestPoint(tp);
            }
            return false;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                int? lac = (int?)tp["LAC"];
                int? ci = (int?)tp["CI"];
                short? rxlev = (short?)tp["RxLevSub"];
                if (lac == null || ci == null || lac == 255 || lac == -255 || ci == 255 || ci == -255 || rxlev == null)
                {
                    return;
                }
                if (checkValidCI((int)ci) && checkValidLAC((int)lac))
                {
                    PesudoBaseStationInfo pesuInfo;
                    if (!pesudoInfoDic.TryGetValue(lac + "_" + ci, out pesuInfo))
                    {
                        pesuInfo = new PesudoBaseStationInfo();
                        pesudoInfoDic[lac + "_" + ci] = pesuInfo;
                    }
                    pesuInfo.Fill(tp);
                }
            }
            catch
            {
                //continue
            }
        }

        private bool checkValidCI(int ci)
        {
            if (chkPesudoCI)
            {
                return ci == pesudoCI;
            }
            return true;
        }

        private bool checkValidLAC(int lac)
        {
            if (chkPesudoLAC)
            {
                bool bValid = false;
                foreach (CRange range in cRangeLacList)
                {
                    if (lac >= range.IMin && lac <= range.IMax)
                    {
                        bValid = true;
                        break;
                    }
                }
                return bValid;
            }
            return true;
        }

        protected override void FireShowFormAfterQuery()
        {
            PesudoBaseStationInfoShowForm pesudoInfoForm =
                MainModel.CreateResultForm(typeof(PesudoBaseStationInfoShowForm))
                 as PesudoBaseStationInfoShowForm;
            pesudoInfoForm.FillData(new List<PesudoBaseStationInfo>(pesudoInfoDic.Values));
            pesudoInfoForm.Visible = true;
            pesudoInfoForm.BringToFront();
        }
    }

    public class PesudoBaseStationInfo
    {
        public string Name { get; set; }
        public int LAC { get; set; }
        public int CI { get; set; }
        public List<PesudoTestPoint> PsdTpList { get; set; }

        public PesudoBaseStationInfo()
        {
            PsdTpList = new List<PesudoTestPoint>();
        }

        public void Fill(TestPoint tp)
        {
            this.LAC = (int)(int?)tp["LAC"];
            this.CI = (int)(int?)tp["CI"];
            this.Name = LAC + "_" + CI;
            PsdTpList.Add(new PesudoTestPoint(tp));
        }
    }

    public class PesudoTestPoint
    {
        public short Rxlev { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string StrDateTime { get { return tp.DateTime.ToString(); } }
        public TestPoint tp { get; set; }
        public string RoadName { get; set; }

        public PesudoTestPoint(TestPoint tp)
        {
            this.tp = tp;
            this.Rxlev = (short)(short?)tp["RxLevSub"];
            this.Longitude = tp.Longitude;
            this.Latitude = tp.Latitude;
            this.RoadName = GISManager.GetInstance().GetRoadPlaceDesc(tp.Longitude, tp.Latitude);
        }
    }
}
