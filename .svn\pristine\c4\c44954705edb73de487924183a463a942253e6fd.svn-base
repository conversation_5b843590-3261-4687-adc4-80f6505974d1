﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.VoLTEDropCallCause;
using System.Windows.Forms;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLTEDropCallByFile : VoLTEDropCallQuery
    {
        private static VoLTEDropCallByFile instance = null;
        public static new VoLTEDropCallByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEDropCallByFile();
                    }
                }
            }
            return instance;
        }

        protected VoLTEDropCallByFile()
            : base()
        {
        }

        public override string Name
        {
            get
            {
                return "VoLTE掉话原因分析(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27006, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class VoLTEDropCallByFile_FDD : VoLTEDropCallQuery_FDD
    {
        private static VoLTEDropCallByFile_FDD instance = null;
        public static new VoLTEDropCallByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEDropCallByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoLTEDropCallByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD掉话原因分析(按文件)";
            }
        }


        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
