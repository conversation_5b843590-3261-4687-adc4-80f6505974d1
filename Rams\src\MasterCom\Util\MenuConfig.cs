using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Xml;

namespace MasterCom.Util
{
    public class MenuConfig : MenuItemConfig
    {
        public List<MenuItemConfig> SubMenuItems { get; set; } = new List<MenuItemConfig>();

        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(MenuConfig).Name))
            {
                MenuConfig menuConfig = new MenuConfig();
                menuConfig.FillItemValue(configFile, item);
                return menuConfig;
            }
            else if (typeName.Equals(typeof(MenuItemConfig).Name))
            {
                MenuItemConfig menuItemConfig = new MenuItemConfig();
                menuItemConfig.FillItemValue(configFile, item);
                return menuItemConfig;
            }
            return null;
        }

        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is MenuConfig)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                (value as MenuConfig).FillItem(configFile, item);
                return item;
            }
            else if (value is MenuItemConfig)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                (value as MenuItemConfig).FillItem(configFile, item);
                return item;
            }
            return null;
        }

        public new void FillItemValue(XmlConfigFile configFile, XmlElement item)
        {
            if (item != null)
            {
                base.FillItemValue(configFile, item);
                List<object> list = configFile.GetItemValue(item, "SubMenuItems", GetItemValue) as List<object>;
                SubMenuItems.Clear();
                foreach (object o in list)
                {
                    SubMenuItems.Add((MenuItemConfig)o);
                }
            }
        }

        public new void FillItem(XmlConfigFile configFile, XmlElement item)
        {
            base.FillItem(configFile, item);
            configFile.AddItem(item, "SubMenuItems", SubMenuItems, AddItem);
        }
    }

    public class MenuItemConfig
    {
        public string Text { get; set; }

        public char Mnemonic { get; set; }

        public string MenuText
        {
            get
            {
                return Text;
            }
        }

        public string ImageFilePath { get; set; }

        private Image image;
        public Image Image
        {
            get
            {
                if (image == null)
                {
                    try
                    {
                        if (System.IO.File.Exists(ImageFilePath))
                        {
                            image = Image.FromFile(ImageFilePath);
                        }
                    }
                    catch
                    {
                        //continue
                    }
                }
                return image;
            }
        }

        public string ActionAssemblyName { get; set; }

        public string ActionTypeName { get; set; }

        public Dictionary<string, object> ActionParam { get; set; } = new Dictionary<string, object>();

        public void FillItemValue(XmlConfigFile configFile, XmlElement item)
        {
            if (item != null)
            {
                Text = configFile.GetItemValue(item, "Text") as string;
                object value = null;
                value = configFile.GetItemValue(item, "Mnemonic");
                if (value != null)
                {
                    Mnemonic = (char)value;
                }
                ImageFilePath = configFile.GetItemValue(item, "ImageFilePath") as string;
                ActionAssemblyName = configFile.GetItemValue(item, "ActionAssemblyName") as string;
                ActionTypeName = configFile.GetItemValue(item, "ActionTypeName") as string;
                ActionParam = configFile.GetItemValue(item, "ActionParam") as Dictionary<string, object>;
            }
        }

        public void FillItem(XmlConfigFile configFile, XmlElement item)
        {
            configFile.AddItem(item, "Text", Text);
            configFile.AddItem(item, "Mnemonic", Mnemonic);
            configFile.AddItem(item, "ImageFilePath", ImageFilePath);
            configFile.AddItem(item, "ActionAssemblyName", ActionAssemblyName);
            configFile.AddItem(item, "ActionTypeName", ActionTypeName);
            configFile.AddItem(item, "ActionParam", ActionParam);
        }
    }
}
