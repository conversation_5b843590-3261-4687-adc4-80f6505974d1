﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYLastMainRoadCalculate : QueryBase
    {
        public ZTDIYLastMainRoadCalculate(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "干道平面里程渗透统计"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18042, this.Name);
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        #region 全局变量
        int iAreaType = 2;
        string strCity = "";
        List<LastDist> lastDist_list;
        List<RoadCalculateRoadpoint_all> lastroad_list;
        List<RoadCalculateRoadpoint_all> newroad_list;
        List<RoadCalculateRoadpoint_all> roadpoint_all_list;
        Dictionary<int, int> rejectRoadDic { get; set; }
        public List<MainRoadResult> resultList { get; set; } = new List<MainRoadResult>();
        public List<MainRoadResult> mainResultList { get; set; } = new List<MainRoadResult>();
        string strSRound { get; set; } = "";
        string strERound { get; set; } = "";
        List<RoundCfg> roundCfgList { get; set; } = new List<RoundCfg>();
        Dictionary<int, List<RoundCfg>> roundCfgDic = new Dictionary<int, List<RoundCfg>>();
        Dictionary<string, MapCfg> mapCfgDic = new Dictionary<string, MapCfg>();
        List<string> rejectMapList = new List<string>();
        MapCfg mapCfg = new MapCfg();
        Dictionary<MainRoadSummarryKey, MainRoadResult> mainRoadResultDic;
        Dictionary<MainRoadSummarryKey, MainRoadResult> mainRoadXQResultDic;
        MasterCom.RAMS.Stat.DIYQueryCQTPointInfo dqcpi { get; set; }
        Dictionary<MainRoadSummarryKey, RoadCalculateRoadpoint_all> lastRoadDic;
        Dictionary<MainRoadSummarryKey, RoadCalculateRoadpoint_all> mainLastRoadDic;
        Dictionary<MainRoadSummarryKey, List<long>> lastRoadSampleDic;
        Dictionary<MainRoadSummarryKey, List<long>> mainLastRoadSampleDic;
        List<string> mainRoadNameList { get; set; } = new List<string>();
        #endregion
        /// <summary>
        /// 初始化数据
        /// </summary>
        private void initData()
        {
            lastDist_list = new List<LastDist>();
            lastroad_list = new List<RoadCalculateRoadpoint_all>();
            newroad_list = new List<RoadCalculateRoadpoint_all>();
            roadpoint_all_list = new List<RoadCalculateRoadpoint_all>();
            rejectRoadDic = new Dictionary<int, int>();

            mainRoadResultDic = new Dictionary<MainRoadSummarryKey, MainRoadResult>();
            mainRoadXQResultDic = new Dictionary<MainRoadSummarryKey, MainRoadResult>();
            
        }
        /// <summary>
        /// 开始查询
        /// </summary>
        protected override void query()
        {
            mapCfgDic.Clear();
            mapCfg = new MapCfg();
            roundCfgDic.Clear();
            resultList.Clear();
            mainResultList.Clear();
            mainRoadNameList.Clear();
            resultList.Clear();
            roundCfgList.Clear();

            WaitBox.Show("正在查询图层及轮次表...", getMapSetCfg);
            ZTDIYLastRoadSetTimeForm timeForm = new ZTDIYLastRoadSetTimeForm(mapCfgDic, rejectMapList, roundCfgDic, true);
            if (timeForm.ShowDialog() != DialogResult.OK)
                return;
            string strMapCfg = timeForm.MapName;
            if (strMapCfg != "高速公路")
                iAreaType = 3;
            if (!mapCfgDic.ContainsKey(strMapCfg))
            {
                MessageBox.Show("所选择的底图有误，请重新选择！", "信息提示", MessageBoxButtons.OK);
                return;
            }
            mapCfg = mapCfgDic[strMapCfg];
            mapCfg.StrRejectMap = timeForm.rejMapName;
            strSRound = timeForm.StrSRound;
            strERound = timeForm.StrERound;
            foreach (RoundCfg rCfg in roundCfgDic[this.mapCfgDic[strMapCfg].IMapId])
            {
                if (int.Parse(rCfg.StrRound) >= int.Parse(strSRound)
                    && int.Parse(rCfg.StrRound) <= int.Parse(strERound))
                    roundCfgList.Add(rCfg);
            }
            try
            {                
                int iCurDistrictID = MainModel.DistrictID;
                if (MainModel.User.DBID == -1) //省用户执行
                {
                    foreach (int DistrictID in ZTDIYLastRoadCalculate.sortCity(condition.DistrictIDs))
                    {
                        MainModel.DistrictID = DistrictID;
                        initData();
                        strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        WaitBox.CanCancel = true;
                        WaitBox.Show("正在获取道路点...", obtainRoadPoint);
                        WaitBox.Show("[ " + strCity + " ]处理道路平面里程...", dealLastRoad);
                        backCalculate();
                    }
                    dealWihtSummarry();                 
                }
                else
                {
                    initData();
                    strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    WaitBox.CanCancel = true;
                    WaitBox.Show("正在获取道路点...", obtainRoadPoint);
                    WaitBox.Show("处理道路平面里程...", dealLastRoad);
                    backCalculate();
                    dealWihtSummarry();
                }
                MainModel.DistrictID = iCurDistrictID;
            }
            finally
            {
                initData();
            }
            showBackResultForm();
        }
        /// <summary>
        /// 查询地图配置
        /// </summary>
        private void getMapSetCfg()
        {
            WaitBox.Text = "正在查询轮次表...";
            if (roundCfgDic.Count == 0)
            {
                DiySqlRoundSetCfg sqlRoundSetCfg = new DiySqlRoundSetCfg(MainModel);
                sqlRoundSetCfg.SetQueryCondition(condition);
                sqlRoundSetCfg.Query();
                this.roundCfgDic = sqlRoundSetCfg.roundCfgDic;
            }
            WaitBox.ProgressPercent = 30;

            WaitBox.Text = "正在查询图层配置表...";
            if (mapCfgDic.Count == 0)
            {
                DiySqlMapSetCfg sqlMapSetCfg = new DiySqlMapSetCfg(MainModel, true);
                sqlMapSetCfg.SetQueryCondition(condition);
                sqlMapSetCfg.Query();
                this.mapCfgDic = sqlMapSetCfg.mapCfgDic;
            }
            WaitBox.ProgressPercent = 60;

            WaitBox.Text = "正在查询剔除图层表...";
            if (rejectMapList.Count == 0)
            {
                DiySqlRejectMap sqlMapRejCfg = new DiySqlRejectMap(MainModel);
                sqlMapRejCfg.SetQueryCondition(condition);
                sqlMapRejCfg.Query();
                rejectMapList = sqlMapRejCfg.rejectMapList;
            }
            WaitBox.ProgressPercent = 90;

            WaitBox.Close();
        }
        /// <summary>
        /// 查询各表数据
        /// </summary>
        private void obtainRoadPoint()
        {
            WaitBox.ProgressPercent = 20;
            lastDist_list = new List<LastDist>();
            lastroad_list = new List<RoadCalculateRoadpoint_all>();
            newroad_list = new List<RoadCalculateRoadpoint_all>();
            roadpoint_all_list = new List<RoadCalculateRoadpoint_all>();
            rejectRoadDic = new Dictionary<int, int>();

            obtainRoadPointByDataBase();
            WaitBox.Close();
        }
        /// <summary>
        /// 通过数据库获取各类表信息
        /// </summary>
        private void obtainRoadPointByDataBase()
        {
            dqcpi = new MasterCom.RAMS.Stat.DIYQueryCQTPointInfo(MainModel, iAreaType);
            dqcpi.Query();
            foreach (RoundCfg rCfg in roundCfgList)
            {
                //实际测试里程统计
                WaitBox.Text = "[ " + strCity + " ]第 " + rCfg.StrRound + " 轮实际测试里程统计";
                DiySqlGetTestStat sqlRoadpointRange_test = new DiySqlGetTestStat(MainModel);
                sqlRoadpointRange_test.SetQueryCondition(condition);
                sqlRoadpointRange_test.setParam(rCfg, mapCfg);
                sqlRoadpointRange_test.Query();
                List<LastDist> lastDistTmpList = sqlRoadpointRange_test.roadpoint_new_List;
                lastDist_list.AddRange(lastDistTmpList);
                WaitBox.ProgressPercent = 30;

                //平面里程统计
                WaitBox.Text = "[ " + strCity + " ]第 " + rCfg.StrRound + " 轮平面里程统计";
                DiySqlGetLastRoad sqlRoadpointRange_new_gather = new DiySqlGetLastRoad(MainModel, true);
                sqlRoadpointRange_new_gather.SetQueryCondition(condition);
                sqlRoadpointRange_new_gather.setParam(rCfg, mapCfg);
                sqlRoadpointRange_new_gather.Query();
                List<RoadCalculateRoadpoint_all> lastRoadTmpList = sqlRoadpointRange_new_gather.roadpoint_new_List;
                foreach (RoadCalculateRoadpoint_all rp in lastRoadTmpList)
                {
                    if (dqcpi.CQTPointDic.ContainsKey(rp.iareaid))
                    {
                        rp.strMainRoadName = dqcpi.CQTPointDic[rp.iareaid].Strareaname;
                        lastroad_list.Add(rp);
                    }
                }
                WaitBox.ProgressPercent = 40;

                //新增里程统计
                WaitBox.Text = "[ " + strCity + " ]第 " + rCfg.StrRound + " 轮新增里程统计";
                DiySqlGetNewRoad sqlRoadpointRange_new = new DiySqlGetNewRoad(MainModel, true);
                sqlRoadpointRange_new.SetQueryCondition(condition);
                sqlRoadpointRange_new.setParam(rCfg, mapCfg);
                sqlRoadpointRange_new.Query();
                List<RoadCalculateRoadpoint_all> newRoadTmpList = sqlRoadpointRange_new.roadpoint_new_List;
                foreach (RoadCalculateRoadpoint_all rp in newRoadTmpList)
                {
                    if (dqcpi.CQTPointDic.ContainsKey(rp.iareaid))
                    {
                        rp.strMainRoadName = dqcpi.CQTPointDic[rp.iareaid].Strareaname;
                        newroad_list.Add(rp);
                    }
                }
                WaitBox.ProgressPercent = 50;
            }

            //道路序列化采样点
            WaitBox.Text = "[ " + strCity + " ]道路序列化采样点";
            RoadCalculateDiySqlGetRoadpointAllRange sqlRoadpointRange_all 
                = new RoadCalculateDiySqlGetRoadpointAllRange(MainModel);
            sqlRoadpointRange_all.SetQueryCondition(condition);
            sqlRoadpointRange_all.setParam(mapCfg);
            sqlRoadpointRange_all.Query();
            List<RoadCalculateRoadpoint_all> roadPointAllList = sqlRoadpointRange_all.roadpoint_all_List;
            roadpoint_all_list.AddRange(roadPointAllList);
            WaitBox.ProgressPercent = 60;
        }
        /// <summary>
        /// 开始处理平面道路里程
        /// </summary>
        private void dealLastRoad()
        {
            statLastRoad();
            statNewRoad();

            foreach (LastDist ld in lastDist_list)
            {
                if (condition.ServiceTypes.Contains(12) && ld.StrFileName.Contains("1800)"))
                {
                    continue;
                }
                MainRoadSummarryKey rk = new MainRoadSummarryKey();
                rk.StrCityName = strCity;
                rk.StrMainRoadName = ld.StrFileName.Split('_')[2];
                if (!lastRoadDic.ContainsKey(rk) || ld.StrGridType != "全市")
                    continue;
                MainRoadSummarryKey hgdKey = new MainRoadSummarryKey();
                hgdKey.StrCityName = strCity;
                hgdKey.StrGridType = "全市";
                if (iAreaType == 2)
                    hgdKey.StrRoadType = "高速";
                else if (iAreaType == 3)
                    hgdKey.StrRoadType = "国道";

                CalcTestDist(hgdKey, ld,true);

                rk.IDir = 0;
                if (ld.StrFileName.Contains("反向") || ld.StrFileName.Contains("回程"))
                    rk.IDir = 1;
                CalcTestDist(rk, ld,false);

                rk.StrCityName = strCity;
                rk.IDir = 2;
                CalcTestDist(rk, ld,false);

            }
            WaitBox.Close();
        }
        /// <summary>
        /// 统计干道平面里程及渗透里程
        /// </summary>
        private void statLastRoad()
        {
            lastRoadDic = new Dictionary<MainRoadSummarryKey, RoadCalculateRoadpoint_all>();
            mainLastRoadDic = new Dictionary<MainRoadSummarryKey, RoadCalculateRoadpoint_all>();
            lastRoadSampleDic = new Dictionary<MainRoadSummarryKey, List<long>>();
            mainLastRoadSampleDic = new Dictionary<MainRoadSummarryKey, List<long>>();
            
            int iProcess = 1;
            foreach (RoadCalculateRoadpoint_all rp in lastroad_list)
            {
                if (iProcess > 95)
                    iProcess = 10;
                WaitBox.ProgressPercent = iProcess++;
                MainRoadSummarryKey rk = new MainRoadSummarryKey();//汇总
                rk.StrCityName = strCity;
                rk.StrMainRoadName = rp.strMainRoadName;
                if (!lastRoadDic.ContainsKey(rk))
                    lastRoadDic.Add(rk, rp);
                sampleAna(rk,rp.isampleid,true);

                MainRoadSummarryKey mrk = MakeKey(strCity, rp.strMainRoadName, rp.idir);
                if (!mainLastRoadDic.ContainsKey(mrk))
                    mainLastRoadDic.Add(mrk, rp);
                sampleAna(mrk, rp.isampleid, false);

                MainRoadSummarryKey rkk = MakeKey(strCity, rp.strMainRoadName, 2);
                if (!mainLastRoadDic.ContainsKey(rkk))
                    mainLastRoadDic.Add(rkk, rp);
                sampleAna(rkk, rp.isampleid, false);                
            }

            iProcess = 1;
            foreach (RoadCalculateRoadpoint_all rp in roadpoint_all_list)
            {
                if (iProcess > 95)
                    iProcess = 40;
                WaitBox.ProgressPercent = iProcess++;

                calcDistance(rp);
            }
        }

        private void calcDistance(RoadCalculateRoadpoint_all rp)
        {
            MainRoadSummarryKey rk = new MainRoadSummarryKey();
            rk.StrCityName = strCity;
            rk.StrMainRoadName = rp.strroad;
            if (rk.StrMainRoadName.Length == 4 && rk.StrMainRoadName[0].ToString().Equals("G"))
            {
                rk.StrMainRoadName = rk.StrMainRoadName.Substring(1, 3) + "国道";
            }

            RoadCalculateRoadpoint_all seepRp = new RoadCalculateRoadpoint_all();
            if (lastRoadDic.ContainsKey(rk)
                && lastRoadSampleDic[rk].Contains(rp.isampleid))
                seepRp = rp;
            MainRoadSummarryKey hgdKey = new MainRoadSummarryKey();
            hgdKey.StrCityName = strCity;
            hgdKey.StrGridType = "全市";
            if (iAreaType == 2)
                hgdKey.StrRoadType = "高速";
            else if (iAreaType == 3)
                hgdKey.StrRoadType = "国道";
            CalcRoadDist(hgdKey, rp, true);
            CalcSeepDist(hgdKey, seepRp, true);

            MCalcSubRoadDist(0, rp);
            MCalcSubRoadDist(1, rp);
            MCalcSubRoadDist(2, rp);
        }

        /// <summary>
        /// 对采样点进行按主键分类
        /// </summary>
        private void sampleAna(MainRoadSummarryKey key, long sampleId, bool isSummarry)
        {
            if (isSummarry)
            {
                if (!lastRoadSampleDic.ContainsKey(key))
                {
                    List<long> sample = new List<long>();
                    sample.Add(sampleId);
                    lastRoadSampleDic.Add(key, sample);
                }
                else
                {
                    if (!lastRoadSampleDic[key].Contains(sampleId))
                        lastRoadSampleDic[key].Add(sampleId);
                } 
            }
            else
            {
                if (!mainLastRoadSampleDic.ContainsKey(key))
                {
                    List<long> sample = new List<long>();
                    sample.Add(sampleId);
                    mainLastRoadSampleDic.Add(key, sample);
                }
                else
                {
                    if (!mainLastRoadSampleDic[key].Contains(sampleId))
                        mainLastRoadSampleDic[key].Add(sampleId);
                }
            }
        }
        /// <summary>
        /// 构造主键
        /// </summary>
        private MainRoadSummarryKey MakeKey(string cityName,string roadName,int iDIR)
        {
            MainRoadSummarryKey makeKey = new MainRoadSummarryKey();
            makeKey.StrCityName = cityName;
            makeKey.StrMainRoadName = roadName;
            if (makeKey.StrMainRoadName.Length == 4 && makeKey.StrMainRoadName[0].ToString().Equals("G"))
            {
                makeKey.StrMainRoadName = makeKey.StrMainRoadName.Substring(1, 3) + "国道";
            }
            makeKey.IDir = iDIR;
            return makeKey;
        }
        /// <summary>
        /// 新增干道里程统计
        /// </summary>
        private void statNewRoad()
        {
            #region 新增道路统计
            //新增道路主键转换
            Dictionary<MainRoadSummarryKey, List<RoadCalculateRoadpoint_all>> newroadDic =   //汇总
                new Dictionary<MainRoadSummarryKey, List<RoadCalculateRoadpoint_all>>();
            Dictionary<MainRoadSummarryKey, List<RoadCalculateRoadpoint_all>> newroadXQDic =   //详情
                new Dictionary<MainRoadSummarryKey, List<RoadCalculateRoadpoint_all>>();
            List<RoadCalculateRoadpoint_all> newroadList = new List<RoadCalculateRoadpoint_all>(); //汇总
            List<RoadCalculateRoadpoint_all> newroadXQList = new List<RoadCalculateRoadpoint_all>();  //详情
            foreach (RoadCalculateRoadpoint_all rp in newroad_list)
            {
                MainRoadSummarryKey rk = new MainRoadSummarryKey();
                rk.StrCityName = strCity;
                rk.StrMainRoadName = rp.strMainRoadName;
                if (newroadDic.ContainsKey(rk))
                    newroadDic[rk].Add(rp);
                else
                {
                    List<RoadCalculateRoadpoint_all> tmpList = new List<RoadCalculateRoadpoint_all>();
                    tmpList.Add(rp);
                    newroadDic.Add(rk, tmpList);
                }

                MainRoadSummarryKey Mrk = MakeKey(strCity, rp.strMainRoadName, rp.idir);
                if (newroadXQDic.ContainsKey(Mrk))
                    newroadXQDic[Mrk].Add(rp);
                else
                {
                    List<RoadCalculateRoadpoint_all> tmpList = new List<RoadCalculateRoadpoint_all>();
                    tmpList.Add(rp);
                    newroadXQDic.Add(Mrk, tmpList);
                }
            }

            addNewroadList(newroadDic, newroadList);

            calcNewDistance(newroadXQDic, newroadList, newroadXQList);
            #endregion
        }

        private void addNewroadList(Dictionary<MainRoadSummarryKey, List<RoadCalculateRoadpoint_all>> newroadDic, List<RoadCalculateRoadpoint_all> newroadList)
        {
            //新增道路距离计算
            foreach (MainRoadSummarryKey rk in newroadDic.Keys)
            {
                List<RoadCalculateRoadpoint_all> newRoadList = newroadDic[rk];
                newRoadList.Sort(RoadCalculateRoadpoint_all.GetCompareBySampleId());

                for (int i = 1; i < newRoadList.Count; i++)
                {
                    double longCurrent = (double)(newRoadList[i].ilongitude) / 10000000;
                    double latCurrent = (double)(newRoadList[i].ilatitude) / 10000000;
                    double longBefore = (double)(newRoadList[i - 1].ilongitude) / 10000000;
                    double latBefore = (double)(newRoadList[i - 1].ilatitude) / 10000000;
                    float fTmpDistance = ((float)MathFuncs.GetDistance(longCurrent, latCurrent, longBefore, latBefore)) / 1000;
                    if (fTmpDistance < 0.1)
                    {
                        newRoadList[i].fdistance = fTmpDistance;
                        newroadList.Add(newRoadList[i]);
                    }
                }
            }
        }

        private void calcNewDistance(Dictionary<MainRoadSummarryKey, List<RoadCalculateRoadpoint_all>> newroadXQDic, List<RoadCalculateRoadpoint_all> newroadList, List<RoadCalculateRoadpoint_all> newroadXQList)
        {
            foreach (RoadCalculateRoadpoint_all rp in newroadList)
            {
                MainRoadSummarryKey hgdKey = new MainRoadSummarryKey();
                hgdKey.StrCityName = strCity;
                hgdKey.StrGridType = "全市";
                if (iAreaType == 2)
                    hgdKey.StrRoadType = "高速";
                else if (iAreaType == 3)
                    hgdKey.StrRoadType = "国道";
                CalcNewDist(hgdKey, rp, true);
            }

            foreach (MainRoadSummarryKey rk in newroadXQDic.Keys)
            {
                List<RoadCalculateRoadpoint_all> newRoadList = newroadXQDic[rk];
                newRoadList.Sort(RoadCalculateRoadpoint_all.GetCompareBySampleId());

                for (int i = 1; i < newRoadList.Count; i++)
                {
                    double longCurrent = (double)(newRoadList[i].ilongitude) / 10000000;
                    double latCurrent = (double)(newRoadList[i].ilatitude) / 10000000;
                    double longBefore = (double)(newRoadList[i - 1].ilongitude) / 10000000;
                    double latBefore = (double)(newRoadList[i - 1].ilatitude) / 10000000;
                    float fTmpDistance = ((float)MathFuncs.GetDistance(longCurrent, latCurrent, longBefore, latBefore)) / 1000;
                    if (fTmpDistance < 0.1)
                    {
                        newRoadList[i].fdistance = fTmpDistance;
                        newroadXQList.Add(newRoadList[i]);
                    }
                }
            }
            foreach (RoadCalculateRoadpoint_all rp in newroadXQList)
            {
                MainRoadSummarryKey mhgdKey = MakeKey(strCity, rp.strMainRoadName, rp.idir);
                CalcNewDist(mhgdKey, rp, false);

                MainRoadSummarryKey rksingle = MakeKey(strCity, rp.strMainRoadName, 2);
                CalcNewDist(rksingle, rp, false);
            }
        }

        /// <summary>
        /// 计算干道道路里程
        /// </summary>
        private void CalcRoadDist(MainRoadSummarryKey hgdKey, RoadCalculateRoadpoint_all rp,bool isSummarry)
        {
            if (isSummarry)
            {
                if (mainRoadResultDic.ContainsKey(hgdKey))
                    mainRoadResultDic[hgdKey].FRoadDist += rp.fdistance;
                else
                    mainRoadResultDic.Add(hgdKey, createResultValue(hgdKey, rp.fdistance, isSummarry));
            }
            else
            {
                if (mainRoadXQResultDic.ContainsKey(hgdKey))
                    mainRoadXQResultDic[hgdKey].FRoadDist += rp.fdistance;
                else
                    mainRoadXQResultDic.Add(hgdKey, createResultValue(hgdKey, rp.fdistance, isSummarry));
            }
        }
        /// <summary>
        /// 计算干道渗透里程
        /// </summary>
        private void CalcSeepDist(MainRoadSummarryKey hgKey, RoadCalculateRoadpoint_all rp,bool isSummarry)
        {
            if (rp == null || rp.strdesc1.Split('|').Length < 3
                || !rp.strdesc1.Split('|')[2].Contains("规划区"))
                return;
            if (isSummarry)
            {
                if (mainRoadResultDic.ContainsKey(hgKey))
                    mainRoadResultDic[hgKey].FSeepDist += rp.fdistance;
            }
            else
            {
                if (mainRoadXQResultDic.ContainsKey(hgKey))
                    mainRoadXQResultDic[hgKey].FSeepDist += rp.fdistance;
            }
        }
        /// <summary>
        /// 计算干道新增里程
        /// </summary>
        private void CalcNewDist(MainRoadSummarryKey hgKey, RoadCalculateRoadpoint_all rp,bool isSummaryy)
        {
            if (rp == null)
                return;
            if (isSummaryy)
            {
                if (mainRoadResultDic.ContainsKey(hgKey))
                    mainRoadResultDic[hgKey].FNewDist += rp.fdistance / 2;
                else
                {
                    MainRoadResult rr = new MainRoadResult();
                    rr.StrGridType = hgKey.StrGridType;
                    rr.Strcity = hgKey.StrCityName;
                    rr.StrType = hgKey.StrRoadType;
                    rr.FNewDist = rp.fdistance / 2;
                    mainRoadResultDic.Add(hgKey, rr);
                }
            }
            else
            {
                if (mainRoadXQResultDic.ContainsKey(hgKey))
                    mainRoadXQResultDic[hgKey].FNewDist += rp.fdistance / 2;
                else
                {
                    MainRoadResult rr = new MainRoadResult();
                    rr.StrMainRoadName = hgKey.StrMainRoadName;
                    rr.Strcity = hgKey.StrCityName;
                    rr.StrDir = "正向";
                    if (hgKey.IDir == 1)
                        rr.StrDir = "反向";
                    if (hgKey.IDir == 2)
                        rr.StrDir = "全程";
                    rr.FNewDist = rp.fdistance / 2;
                    mainRoadXQResultDic.Add(hgKey, rr);
                }
            }
        }
        /// <summary>
        /// 计算干道实际测试里程
        /// </summary>
        private void CalcTestDist(MainRoadSummarryKey hgKey, LastDist ld,bool isSummarry)
        {
            if (ld == null)
                return;
            if (isSummarry)
            {
                if (mainRoadResultDic.ContainsKey(hgKey))
                {
                    mainRoadResultDic[hgKey].FTestDist += ld.FDist / 2;
                    mainRoadResultDic[hgKey].FTestTime += ld.FTime;
                    mainRoadResultDic[hgKey].FTestDistSpeed += ld.FDist;
                }
                else
                {
                    MainRoadResult rr = new MainRoadResult();
                    rr.StrGridType = hgKey.StrGridType;
                    rr.Strcity = strCity;
                    rr.StrType = hgKey.StrRoadType;

                    rr.FTestDist = ld.FDist / 2;
                    rr.FTestTime = ld.FTime;
                    rr.FTestDistSpeed = ld.FDist;
                    mainRoadResultDic.Add(hgKey, rr);
                }
            }
            else
            {
                if (mainRoadXQResultDic.ContainsKey(hgKey))
                {
                    mainRoadXQResultDic[hgKey].FTestDist += ld.FDist / 2;
                    mainRoadXQResultDic[hgKey].FTestTime += ld.FTime;
                    mainRoadXQResultDic[hgKey].FTestDistSpeed += ld.FDist;
                }
                else
                {
                    MainRoadResult rr = new MainRoadResult();
                    rr.StrMainRoadName = hgKey.StrMainRoadName;
                    rr.Strcity = hgKey.StrCityName;
                    rr.StrDir = "正向";
                    if (hgKey.IDir == 1)
                        rr.StrDir = "反向";
                    if (hgKey.IDir == 2)
                        rr.StrDir = "全程";
                    rr.FTestDist = ld.FDist / 2;
                    rr.FTestTime = ld.FTime;
                    rr.FTestDistSpeed = ld.FDist;
                    mainRoadXQResultDic.Add(hgKey, rr);
                }
            }
        }
        /// <summary>
        ///计算干道结果赋值
        /// </summary>
        private MainRoadResult createResultValue(MainRoadSummarryKey hgKey, float fDistance,bool isSummarry)
        {
            MainRoadResult rr = new MainRoadResult();
            if (isSummarry)
            {
                rr.StrGridType = hgKey.StrGridType;
                rr.Strcity = hgKey.StrCityName;
                rr.StrType = hgKey.StrRoadType;
                rr.FRoadDist += fDistance;
            }
            else
            {
                rr.StrMainRoadName = hgKey.StrMainRoadName;
                rr.Strcity = hgKey.StrCityName;
                rr.StrDir = "正向";
                if (hgKey.IDir == 1)
                    rr.StrDir = "反向";
                if (hgKey.IDir == 2)
                    rr.StrDir = "全程";
                rr.FRoadDist += fDistance;
            }
            return rr;
        }
        /// <summary>
        /// 计算子过程
        /// </summary>
        private void MCalcSubRoadDist(int idir, RoadCalculateRoadpoint_all rp)
        {
            MainRoadSummarryKey rk = MakeKey(strCity, rp.strroad, idir);
            RoadCalculateRoadpoint_all seepRp = new RoadCalculateRoadpoint_all();
            if (mainLastRoadDic.ContainsKey(rk) 
                && mainLastRoadSampleDic[rk].Contains(rp.isampleid))
                seepRp = rp;              
            CalcRoadDist(rk, rp,false);
            CalcSeepDist(rk, seepRp,false);
        }
        /// <summary>
        /// 最后统计结果处理
        /// </summary>
        public void backCalculate()
        {
            foreach (MainRoadSummarryKey gKey in mainRoadResultDic.Keys)
            {
                MainRoadResult mainRoad = new MainRoadResult();
                mainRoad.Strcity = gKey.StrCityName;
                mainRoad.StrGridType = gKey.StrGridType;
                mainRoad.StrType = gKey.StrRoadType;
                if (mainRoadResultDic.ContainsKey(gKey))
                    mainRoad = mainRoadResultDic[gKey];
                mainRoad.FRoadDist = (float)Math.Round(mainRoad.FRoadDist, 2);
                mainRoad.FSeepDist = (float)Math.Round(mainRoad.FSeepDist, 2);
                mainRoad.FNewDist = (float)Math.Round(mainRoad.FNewDist, 2);
                mainRoad.FTestDist = (float)Math.Round(mainRoad.FTestDist, 2);
                mainRoad.FTestDistSpeed = (float)Math.Round(mainRoad.FTestDistSpeed, 2);
                resultList.Add(mainRoad);
            }
            foreach (MainRoadSummarryKey gKey in mainRoadXQResultDic.Keys)
            {
                MainRoadResult mainRoad = new MainRoadResult();
                mainRoad.Strcity = gKey.StrCityName;
                mainRoad.StrMainRoadName = gKey.StrMainRoadName;
                if (mainRoad.StrMainRoadName.Length == 4 && mainRoad.StrMainRoadName[0].ToString().Equals("G"))
                {
                    mainRoad.StrMainRoadName = mainRoad.StrMainRoadName.Substring(1, 3) + "国道";
                }
                mainRoad.StrDir = "正向";
                if (gKey.IDir == 1)
                    mainRoad.StrDir = "反向";
                if (gKey.IDir == 2)
                    mainRoad.StrDir = "全程";
                if (mainRoadXQResultDic.ContainsKey(gKey))
                    mainRoad = mainRoadXQResultDic[gKey];
                mainRoad.FRoadDist = (float)Math.Round(mainRoad.FRoadDist, 2);
                mainRoad.FSeepDist = (float)Math.Round(mainRoad.FSeepDist, 2);
                mainRoad.FNewDist = (float)Math.Round(mainRoad.FNewDist, 2);
                mainRoad.FTestDist = (float)Math.Round(mainRoad.FTestDist, 2);
                mainRoad.FTestDistSpeed = (float)Math.Round(mainRoad.FTestDistSpeed, 2);
                mainResultList.Add(mainRoad);

                if (!mainRoadNameList.Contains(gKey.StrMainRoadName))
                    mainRoadNameList.Add(gKey.StrMainRoadName);
            }
        }
        /// <summary>
        /// 汇总处理
        /// </summary>
        private void dealWihtSummarry()
        {
            mainResultList.AddRange(mainRoadSummarryByALLCity("正向"));
            mainResultList.AddRange(mainRoadSummarryByALLCity("反向"));
            mainResultList.AddRange(mainRoadSummarryByALLCity("全程"));
        }
        /// <summary>
        /// 对干道进行所经过地市汇总
        /// </summary>       
        private List<MainRoadResult> mainRoadSummarryByALLCity(string strDir)
        {
            List<MainRoadResult> mainSubResultList = new List<MainRoadResult>();
            foreach (string roadName in mainRoadNameList)
            {
                MainRoadResult mainRoad = new MainRoadResult();
                mainRoad.Strcity = "汇总";
                mainRoad.StrMainRoadName = roadName;
                if (mainRoad.StrMainRoadName.Length == 4 && mainRoad.StrMainRoadName[0].ToString().Equals("G"))
                {
                    mainRoad.StrMainRoadName = mainRoad.StrMainRoadName.Substring(1, 3) + "国道";
                }

                mainRoad.StrDir = strDir;
                foreach (MainRoadResult mrk in mainResultList)
                {
                    if (mainRoad.StrDir == mrk.StrDir && mainRoad.StrMainRoadName == mrk.StrMainRoadName)
                    {
                        mainRoad.FRoadDist += mrk.FRoadDist;
                        mainRoad.FSeepDist += mrk.FSeepDist;
                        mainRoad.FNewDist += mrk.FNewDist;
                        mainRoad.FTestDist += mrk.FTestDist;
                        mainRoad.FTestTime += mrk.FTestTime;
                        mainRoad.FTestDistSpeed += mrk.FTestDistSpeed;
                    }
                }
                mainSubResultList.Add(mainRoad);
            }
            return mainSubResultList;
        }
        /// <summary>
        /// 显示结果集
        /// </summary>
        private void showBackResultForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LastMainRoadCalculate).FullName);
            LastMainRoadCalculate lastbackShowForm = obj == null ? null : obj as LastMainRoadCalculate;
            if (lastbackShowForm == null || lastbackShowForm.IsDisposed)
            {
                lastbackShowForm = new LastMainRoadCalculate();
            }
            lastbackShowForm.FillData(resultList, mainResultList);
            lastbackShowForm.Show(MainModel.MainForm);
        }

    }
    public class MainRoadSummarryKey
    {
        public MainRoadSummarryKey()
        {
            StrMainRoadName = "";
            StrCityName = "";
            IDir = 0;
            StrRoadType = "";
            StrGridType = "";
        }
        /// <summary>
        /// 干道名称
        /// </summary>
        public string StrMainRoadName { get; set; }
        /// <summary>
        /// 城市名称
        /// </summary>
        public string StrCityName { get; set; }
        /// <summary>
        /// 方向
        /// </summary>
        public int IDir { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        ///道路级别
        /// </summary>
        public string StrRoadType { get; set; }
        public override bool Equals(object obj)
        {
            MainRoadSummarryKey other = obj as MainRoadSummarryKey;
            if (other == null)
                return false;
            if (!base.GetType().Equals(obj.GetType()))
                return false;
            return (this.StrMainRoadName.Equals(other.StrMainRoadName)
                && this.StrCityName.Equals(other.StrCityName)
                && this.IDir.Equals(other.IDir)
                && this.StrGridType.Equals(other.StrGridType)
                && this.StrRoadType.Equals(other.StrRoadType));
        }
        public override int GetHashCode()
        {
            return (this.StrCityName
                 + this.StrMainRoadName
                 + this.StrGridType
                 + this.StrRoadType
                 + this.IDir).GetHashCode();
        }
    }
    public class MainRoadResult : RoadResult
    {
        /// <summary>
        /// 高速名称
        /// </summary>
        public string StrMainRoadName { get; set; }
        /// <summary>
        /// 方向
        /// </summary>
        public string StrDir { get; set; }
    }
    
}
