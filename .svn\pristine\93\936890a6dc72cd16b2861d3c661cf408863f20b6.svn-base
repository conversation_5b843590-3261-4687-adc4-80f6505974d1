﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFastFadingByRegion_TDSCAN : DIYFastFadingByRegion_GSCAN
    {
        public DIYFastFadingByRegion_TDSCAN(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TD_SCAN);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16019, this.Name);//////
        }

        readonly FastFadingDlg_TDSCAN conditionDlg = new FastFadingDlg_TDSCAN();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetFilterCondition(out rxLevDValue, out secondLast, out secondFading, out rxLevDValueFading);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        dealTestPoint(testPointList, i, testPoint);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTestPoint(List<TestPoint> testPointList, int i, TestPoint testPoint)
        {
            if (isValidTestPoint(testPoint))
            {
                Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
                float? rxLevMax = (float?)testPoint["TDS_PCCPCH_RSCP", 0];
                for (int j = 0; j < 50; j++)
                {
                    float? rxLev = (float?)testPoint["TDS_PCCPCH_RSCP", j];
                    if (rxLev == null || rxLev > -10 || rxLev < -120)
                    {
                        break;
                    }
                    int? bcch = (int?)testPoint["TDS_PCCPCH_Channel", j];
                    int? bsic = (int?)testPoint["TDS_PCCPCH_CPI", j];
                    TDCell cell = CellManager.GetInstance().GetNearestTDCell(testPoint.DateTime, (short)bcch, (byte)bsic, testPoint.Longitude, testPoint.Latitude);
                    if (cell != null)
                    {
                        cellRxLevDic[cell.Name] = (float)rxLev;
                        judgeCell(rxLevMax, rxLev, cell.Name, testPointList, i);
                    }
                }

                judgeTestPoint(testPointList, i, cellRxLevDic);
            }
            else
            {
                clearIndermediateVariable();
            }
        }
    }

    public class DIYFastFadingByRegion_WSCAN : DIYFastFadingByRegion_GSCAN
    {
        public DIYFastFadingByRegion_WSCAN(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_SCAN);
        }

        readonly FastFadingDlg_TDSCAN conditionDlg = new FastFadingDlg_TDSCAN();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetFilterCondition(out rxLevDValue, out secondLast, out secondFading, out rxLevDValueFading);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        dealTestPoint(testPointList, i, testPoint);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTestPoint(List<TestPoint> testPointList, int i, TestPoint testPoint)
        {
            if (isValidTestPoint(testPoint))
            {
                Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
                float? rxLevMax = (float?)testPoint["WS_CPICHTotalRSCP", 0];
                for (int j = 0; j < 50; j++)
                {
                    float? rxLev = (float?)testPoint["WS_CPICHTotalRSCP", j];
                    if (rxLev == null || rxLev > -10 || rxLev < -120)
                    {
                        break;
                    }
                    short? bcch = testPoint["WS_CPICHChannel", j] as short?;
                    short? bsic = testPoint["WS_CPICHPilot", j] as short?;
                    if (bcch == null || bsic == null)
                    {
                        break;
                    }
                    WCell cell = CellManager.GetInstance().GetNearestWCell(testPoint.DateTime, (short)bcch, (short)bsic, testPoint.Longitude, testPoint.Latitude);
                    if (cell != null)
                    {
                        cellRxLevDic[cell.Name] = (float)rxLev;
                        judgeCell(rxLevMax, rxLev, cell.Name, testPointList, i);
                    }
                }

                judgeTestPoint(testPointList, i, cellRxLevDic);
            }
            else
            {
                clearIndermediateVariable();
            }
        }

        protected override void fireShowForm()
        {
            MainModel.MainForm.FireShowFastFadingForm();
        }
    }
}
