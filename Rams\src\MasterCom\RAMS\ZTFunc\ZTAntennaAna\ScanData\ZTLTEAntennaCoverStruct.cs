﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntennaCoverStruct : ZTAntennaBase
    {
        public ZTLteAntennaCoverStruct()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        private static ZTLteAntennaCoverStruct instance = null;
        protected static readonly object lockObj = new object();

        public static ZTLteAntennaCoverStruct GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteAntennaCoverStruct();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "天线分析_覆盖结构优化"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28010, this.Name);
        }

        Dictionary<GridCellKey, CellAntData> cellDataDic = new Dictionary<GridCellKey, CellAntData>();
        Dictionary<string, List<SampleSubInfo>> fileSampleDic = new Dictionary<string, List<SampleSubInfo>>();
        Dictionary<int, RoadStat> roadStatDic = new Dictionary<int, RoadStat>();

        string strCityTypeName = "";
        LTEScanStructSetForm structSetForm;

        protected override void query()
        {
            structSetForm = new LTEScanStructSetForm();
            DialogResult dr = structSetForm.ShowDialog();
            if (dr != DialogResult.OK)
                return;

            setVarBeforQuery();
            InitRegionMop2();

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                cellDataDic = new Dictionary<GridCellKey, CellAntData>();
                fileSampleDic = new Dictionary<string, List<SampleSubInfo>>();
                roadStatDic = new Dictionary<int, RoadStat>();
                MainModel.ClearDTData();

                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                WaitBox.Show("读取数据分析...", queryInThread, clientProxy);
                WaitBox.Show("问题道路分析...", anaRoadStat);
                dealMainUtranCellSample();
            }
            catch (Exception exp)
            {
                clientProxy.Close();
                log.Error(exp.Message);
            }
            finally
            {
                MainModel.ClearDTData();
            }
        }

        protected override void statData(ClientProxy clientProxy)
        {
            InitRegionMop2();
            setVarBeforQuery();
            queryInThread(clientProxy);
        }

        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);
                    }
                }

                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,51,0,2,51,0,4,51,0,5,51,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude) && tp is ScanTestPoint_LTE)
                {
                    string strGridName = "";
                    isContainPoint(tp.Longitude, tp.Latitude, ref strGridName);
                    if (strGridName == "")
                        return;

                    LongLat ll = new LongLat((float)tp.Longitude, (float)tp.Latitude);
                    Dictionary<int, List<LteSampleSubInfo>> bandSampleInfoDic = doWithSCANData(tp);
                    List<int> bandSampleInfoList = new List<int>(bandSampleInfoDic.Keys);
                    if (bandSampleInfoList.Count > 0)
                    {
                        setCellDataDic(tp, strGridName, ll, bandSampleInfoDic, bandSampleInfoList);
                    }
                }
            }
            catch (Exception ee)
            {
                log.Error(ee.Message);
            }
        }

        private void setCellDataDic(TestPoint tp, string strGridName, LongLat ll, Dictionary<int, List<LteSampleSubInfo>> bandSampleInfoDic, List<int> bandSampleInfoList)
        {
            int iEarfcn = bandSampleInfoList[0];
            List<LteSampleSubInfo> sampleInfoList = bandSampleInfoDic[iEarfcn];
            int iNum = sampleInfoList.Count;
            if (iNum > 0)
            {
                int iSamePciTime = 0;
                LteSampleSubInfo mainCellInfo = sampleInfoList[0];
                //采样点统计
                GridCellKey gcKey = new GridCellKey();
                gcKey.strGridName = strGridName;
                gcKey.strCellName = mainCellInfo.CellName;
                CellAntData cellData;
                if (!cellDataDic.TryGetValue(gcKey, out cellData))
                    cellData = new CellAntData();

                cellData.strGrid = strGridName;
                cellData.iEarfcn = mainCellInfo.iEarfcn;
                cellData.iPci = mainCellInfo.iPCI;
                cellData.lteCell = mainCellInfo.servCell;

                int iCoverCell = 0;
                for (int i = 1; i < iNum; i++)
                {
                    LteSampleSubInfo curCellInfo = sampleInfoList[i];
                    if (mainCellInfo.fRSRP - curCellInfo.fRSRP < 6)
                    {
                        iCoverCell += 1;
                        if (curCellInfo.iPCI % 3 == mainCellInfo.iPCI % 3)
                            iSamePciTime += 1;
                    }
                    else
                        break;
                }
                //iCoverCell = iCoverCell - 1;//如果小区A为-80dBm，6dB以内没有其他小区，则重叠覆盖度是1
                cellData.fillAntData(mainCellInfo.fRSRP, mainCellInfo.fSINR, iCoverCell, iSamePciTime, ll, structSetForm.iRsrpSplit);
                cellDataDic[gcKey] = cellData;

                addSample(tp, strGridName, ll, iSamePciTime, mainCellInfo, iCoverCell);
            }
        }

        private void addSample(TestPoint tp, string strGridName, LongLat ll, int iSamePciTime, LteSampleSubInfo mainCellInfo, int iCoverCell)
        {
            if ((mainCellInfo.fRSRP >= structSetForm.iRsrpSplit && mainCellInfo.fRSRP <= 3 && iSamePciTime == 0) || iCoverCell < 0)
            {//采样点不添加
            }
            else
            {//采样点原始数据
                string strFileName = tp.FileName;
                SampleSubInfo sampleInfo = new SampleSubInfo();
                sampleInfo.iSampleid = tp.SN;
                sampleInfo.lTimeWithMillseconde = tp.lTimeWithMillsecond;
                sampleInfo.strGrid = strGridName;
                sampleInfo.iEarfcn = mainCellInfo.iEarfcn;
                sampleInfo.iPci = mainCellInfo.iPCI;
                sampleInfo.lteCell = mainCellInfo.servCell;
                sampleInfo.longLat = ll;
                sampleInfo.fRsrp = mainCellInfo.fRSRP;
                sampleInfo.fSinr = mainCellInfo.fSINR;
                sampleInfo.iCover = iCoverCell > 5 ? 5 : iCoverCell;
                sampleInfo.anaProbType(structSetForm.iRsrpSplit);

                List<SampleSubInfo> sampleList;
                if (!fileSampleDic.TryGetValue(strFileName, out sampleList))
                    sampleList = new List<SampleSubInfo>();

                sampleList.Add(sampleInfo);
                fileSampleDic[strFileName] = sampleList;
            }
        }

        /// <summary>
        /// 问题道路分析
        /// </summary>
        private void anaRoadStat()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = fileSampleDic.Count;
            int iNum = 0;

            roadStatDic.Clear();
            int iRoadNum = 0;
            foreach (string strFileName in fileSampleDic.Keys)
            {
                WaitBox.ProgressPercent = (int)(100 * ((iNum++ * 1.0) / iCount));

                List<SampleSubInfo> sampleList = fileSampleDic[strFileName];
                sampleList.Sort(LteSampleByTime.GetCompareByTime());
                Dictionary<string, List<SampleSubInfo>> probTypeDic = new Dictionary<string, List<SampleSubInfo>>();
                foreach (SampleSubInfo sample in sampleList)
                {
                    List<SampleSubInfo> tmpList;
                    if (!probTypeDic.TryGetValue(sample.strProbType, out tmpList))
                        tmpList = new List<SampleSubInfo>();
                    tmpList.Add(sample);
                    probTypeDic[sample.strProbType] = tmpList;
                }

                LongLat sLongLat;
                long lTime = 0;
                foreach (string strProb in probTypeDic.Keys)
                {
                    List<SampleSubInfo> sampleByTypeList = probTypeDic[strProb];
                    sLongLat = sampleByTypeList[0].longLat;
                    lTime = sampleByTypeList[0].lTimeWithMillseconde;

                    dealProbSample(ref iRoadNum, strFileName, ref sLongLat, ref lTime, strProb, sampleByTypeList);
                }
            }
            fileSampleDic.Clear();
            WaitBox.Close();
        }

        private void dealProbSample(ref int iRoadNum, string strFileName, ref LongLat sLongLat, ref long lTime, string strProb, List<SampleSubInfo> sampleByTypeList)
        {
            List<SampleSubInfo> tmpList = new List<SampleSubInfo>();
            double sumDist = 0;
            List<string> gridList = new List<string>();
            List<string> cellNameList = new List<string>();
            List<float> distanceList = new List<float>();
            foreach (SampleSubInfo s in sampleByTypeList)
            {
                if (WaitBox.CancelRequest)
                    break;

                double dDist = MathFuncs.GetDistance(sLongLat.fLongitude, sLongLat.fLatitude, s.longLat.fLongitude, s.longLat.fLatitude);
                double dCoverDist = 0;
                if (s.lteCell != null)
                    dCoverDist = MathFuncs.GetDistance(s.lteCell.Longitude, s.lteCell.Latitude, s.longLat.fLongitude, s.longLat.fLatitude);

                int dDura = (int)(s.lTimeWithMillseconde - lTime);
                lTime = s.lTimeWithMillseconde;
                sLongLat = s.longLat;

                if (dDist < 20 && dDura < 3000)//20米且3秒以内
                {
                    tmpList.Add(s);
                    sumDist += dDist;

                    addValidCellGrid(gridList, cellNameList, s);

                    distanceList.Add((float)dCoverDist);
                }
                else
                {
                    if (sumDist >= 50 && tmpList.Count > 1)
                    {
                        RoadStat roadStat = new RoadStat();
                        roadStat.strCity = strCityTypeName;
                        roadStat.strProbType = strProb;
                        roadStat.fDistance = (float)sumDist;
                        roadStat.strFileName = strFileName;

                        roadStat.cellNameList = cellNameList;
                        roadStat.gridList = gridList;
                        roadStat.coverDistList = distanceList;
                        roadStat.fillValue(tmpList);
                        roadStatDic.Add(++iRoadNum, roadStat);
                    }

                    tmpList = new List<SampleSubInfo>();
                    sumDist = 0;
                    gridList = new List<string>();
                    cellNameList = new List<string>();
                    distanceList = new List<float>();
                }
            }
        }

        private static void addValidCellGrid(List<string> gridList, List<string> cellNameList, SampleSubInfo s)
        {
            if (!cellNameList.Contains(s.strCellName))
                cellNameList.Add(s.strCellName);

            if (!gridList.Contains(s.strGrid))
                gridList.Add(s.strGrid);
        }

        /// <summary>
        /// 分析扫频采样点
        /// </summary>
        public Dictionary<int, List<LteSampleSubInfo>> doWithSCANData(TestPoint testPoint)
        {
            float maxRsrp = -10;    //最大有效电平 
            float minRsrp = -140;   //最小有效电平
            float maxSinr = 40;     //最大有效信噪比
            float minSinr = -25;    //最小有效信噪比
            Dictionary<int, List<LteSampleSubInfo>> bandSampleInfoDic = new Dictionary<int, List<LteSampleSubInfo>>();

            for (int index = 0; index < 50; index++)
            {
                int? earfcn = (int?)testPoint["LTESCAN_TopN_EARFCN", index];
                int? pci = (int?)(short?)testPoint["LTESCAN_TopN_PCI", index];
                float? rsrp = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", index];
                float? sinr = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSSINR", index];
                if (earfcn == null || pci == null || rsrp == null || sinr == null
                    || rsrp >= maxRsrp || rsrp <= minRsrp || sinr >= maxSinr || sinr <= minSinr)
                {
                    continue;
                }

                LTECell servCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(
                                    testPoint.DateTime, earfcn, pci, testPoint.Longitude, testPoint.Latitude);
                LteSampleSubInfo LTEInfo = new LteSampleSubInfo(index, (int)earfcn, (int)pci, (float)rsrp, (float)sinr, servCell);

                List<LteSampleSubInfo> tmpList;
                if (!bandSampleInfoDic.TryGetValue((int)earfcn, out tmpList))
                    tmpList = new List<LteSampleSubInfo>();
                tmpList.Add(LTEInfo);

                bandSampleInfoDic[(int)earfcn] = tmpList;
            }
            return bandSampleInfoDic;
        }

        /// <summary>
        /// 数据结果集封装
        /// </summary>
        private void dealMainUtranCellSample()
        {
            Dictionary<string, CellAntStat> gridAntStatDic = new Dictionary<string, CellAntStat>();
            Dictionary<string, SampleData> allSampleDataDic = new Dictionary<string, SampleData>();

            foreach (GridCellKey gKey in cellDataDic.Keys)
            {
                CellAntData cellData = cellDataDic[gKey];
                //网格统计
                CellAntStat cellAntStat;
                if (!gridAntStatDic.TryGetValue(gKey.strGridName, out cellAntStat))
                    cellAntStat = new CellAntStat();

                cellAntStat.addValue(cellData.cellAntStat);
                gridAntStatDic[gKey.strGridName] = cellAntStat;

                //二维四象
                SampleData sampleData;
                if (!allSampleDataDic.TryGetValue(gKey.strGridName, out sampleData))
                    sampleData = new SampleData();

                sampleData.addValue(cellData);
                allSampleDataDic[gKey.strGridName] = sampleData;
            }

            List<List<NPOIRow>> nrDatasList = null;
            List<string> sheetNames = null;
            nrDatasList = new List<List<NPOIRow>>();
            sheetNames = new List<string>();
            sheetNames.Add("网格指标统计");
            sheetNames.Add("二维四象优化分析-采样点占比");
            sheetNames.Add("二维四象优化分析-SINR均值");
            sheetNames.Add("小区级数据统计");
            sheetNames.Add("道路问题点详情");
            fillGridStatInfo(ref nrDatasList, gridAntStatDic);
            fillDetailInfo(ref nrDatasList, allSampleDataDic);
            fillCellStatInfo(ref nrDatasList);
            fillRoadStatInfo(ref nrDatasList);
            
            FireShowResultForm(nrDatasList, sheetNames, gridAntStatDic, allSampleDataDic);
        }

        /// <summary>
        /// 网格指标统计
        /// </summary>
        private void fillGridStatInfo(ref List<List<NPOIRow>> nrDatasList, Dictionary<string, CellAntStat> gridAntStatDic)
        {
            List<NPOIRow> datasCell = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("序号");
            cols.Add("地市");
            cols.Add("网格");
            cols.Add("采样点总数");
            cols.Add("问题采样点数");
            cols.Add("问题采样点占比");
            cols.Add("RSRP均值");
            cols.Add("SINR均值");
            cols.Add("重叠覆盖度");
            cols.Add("弱覆盖低重叠采样点数");
            cols.Add("弱覆盖低重叠采样点占比");
            cols.Add("弱覆盖低重叠RSRP均值");
            cols.Add("弱覆盖低重叠SINR均值");
            cols.Add("弱覆盖低重叠的重叠度");
            cols.Add("弱覆盖高重叠采样点数");
            cols.Add("弱覆盖高重叠采样点占比");
            cols.Add("弱覆盖高重叠RSRP均值");
            cols.Add("弱覆盖高重叠SINR均值");
            cols.Add("弱覆盖高重叠的重叠度");
            cols.Add("强覆盖高重叠采样点数");
            cols.Add("强覆盖高重叠采样点占比");
            cols.Add("强覆盖高重叠RSRP均值");
            cols.Add("强覆盖高重叠SINR均值");
            cols.Add("强覆盖高重叠的重叠度");
            cols.Add("强覆盖MOD3的采样点数");
            cols.Add("强覆盖MOD3的采样点占比");
            cols.Add("强覆盖MOD3的RSRP均值");
            cols.Add("强覆盖MOD3的SINR均值");
            cols.Add("强覆盖MOD3的重叠度");
            nrCell.cellValues = cols;
            datasCell.Add(nrCell);

            int idx = 1;
            foreach (string gridName in gridAntStatDic.Keys)
            {
                CellAntStat gridStat = gridAntStatDic[gridName];

                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(idx++);
                objs.Add(strCityTypeName);
                objs.Add(gridName);

                objs.Add(gridStat.iTotalSampleNum);
                objs.Add(gridStat.iSampleNum);
                objs.Add(gridStat.FProbPointRate);
                objs.Add(gridStat.FRsrp);
                objs.Add(gridStat.FSinr);
                objs.Add(gridStat.FCover);

                objs.Add(gridStat.iWeakLowNum);
                objs.Add(gridStat.FWeakLowRate);
                objs.Add(gridStat.FWeakLowRsrp);
                objs.Add(gridStat.FWeakLowSinr);
                objs.Add(gridStat.FWeakLowCover);

                objs.Add(gridStat.iWeakHighNum);
                objs.Add(gridStat.FWeakHighRate);
                objs.Add(gridStat.FWeakHighRsrp);
                objs.Add(gridStat.FWeakHighSinr);
                objs.Add(gridStat.FWeakHighCover);

                objs.Add(gridStat.iGoodHighNum);
                objs.Add(gridStat.FGoodHighRate);
                objs.Add(gridStat.FGoodHighRsrp);
                objs.Add(gridStat.FGoodHighSinr);
                objs.Add(gridStat.FGoodHighCover);

                objs.Add(gridStat.iGoodMod3Num);
                objs.Add(gridStat.FGoodMod3Rate);
                objs.Add(gridStat.FGoodMod3Rsrp);
                objs.Add(gridStat.FGoodMod3Sinr);
                objs.Add(gridStat.FGoodMod3Cover);

                nr.cellValues = objs;
                datasCell.Add(nr);
            }
            nrDatasList.Add(datasCell);
        }

        /// <summary>
        /// 网格统计详情
        /// </summary>
        private void fillDetailInfo(ref List<List<NPOIRow>> nrDatasList, Dictionary<string, SampleData> allSampleDataDic)
        {
            List<NPOIRow> datasSample = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            List<object> cols = InitDetailColumns("采样点占比");
            nrCell.cellValues = cols;
            datasSample.Add(nrCell);

            int idx = 1;
            foreach (string strGrid in allSampleDataDic.Keys)
            {
                SampleData sampleData = allSampleDataDic[strGrid];
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                objs.Add(idx++);
                objs.Add(strCityTypeName);
                objs.Add(strGrid);
                string[,] tmpRate = sampleData.DMultiSampleRate;
                for (int i = 0; i < 13; i++)
                {
                    for (int j = 0; j < 6; j++)
                    {
                        objs.Add(tmpRate[i, j]);
                    }
                }
                nr.cellValues = objs;
                datasSample.Add(nr);
            }
            nrDatasList.Add(datasSample);

            List<NPOIRow> datasSinr = new List<NPOIRow>();
            NPOIRow nrSinr = new NPOIRow();
            List<object> cols2 = InitDetailColumns("SINR均值");
            nrSinr.cellValues = cols2;
            datasSinr.Add(nrSinr);

            int idx2 = 1;
            foreach (string strGrid in allSampleDataDic.Keys)
            {
                SampleData sampleData = allSampleDataDic[strGrid];
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                objs.Add(idx2++);
                objs.Add(strCityTypeName);
                objs.Add(strGrid);
                string[,] tmpRate = sampleData.DMultiSinr;
                for (int i = 0; i < 13; i++)
                {
                    for (int j = 0; j < 6; j++)
                    {
                        objs.Add(tmpRate[i, j]);
                    }
                }
                nr.cellValues = objs;
                datasSinr.Add(nr);
            }
            nrDatasList.Add(datasSinr);
        }

        /// <summary>
        /// 小区数据统计
        /// </summary>
        private void fillCellStatInfo(ref List<List<NPOIRow>> nrDatasList)
        {
            List<NPOIRow> datasCell = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            nrCell.AddCellValue("序号");
            nrCell.AddCellValue("网格");
            nrCell.AddCellValue("小区名称");
            nrCell.AddCellValue("ECI");
            nrCell.AddCellValue("SectorId");
            nrCell.AddCellValue("Earfcn");
            nrCell.AddCellValue("PCI");
            nrCell.AddCellValue("采样点总数");
            nrCell.AddCellValue("问题采样点数");
            nrCell.AddCellValue("问题采样点占比");
            nrCell.AddCellValue("RSRP均值");
            nrCell.AddCellValue("SINR均值");
            nrCell.AddCellValue("重叠覆盖度");
            nrCell.AddCellValue("弱覆盖低重叠采样点数");
            nrCell.AddCellValue("弱覆盖低重叠采样点占比");
            nrCell.AddCellValue("弱覆盖低重叠RSRP均值");
            nrCell.AddCellValue("弱覆盖低重叠SINR均值");
            nrCell.AddCellValue("弱覆盖低重叠的重叠度");
            nrCell.AddCellValue("弱覆盖高重叠采样点数");
            nrCell.AddCellValue("弱覆盖高重叠采样点占比");
            nrCell.AddCellValue("弱覆盖高重叠RSRP均值");
            nrCell.AddCellValue("弱覆盖高重叠SINR均值");
            nrCell.AddCellValue("弱覆盖高重叠的重叠度");
            nrCell.AddCellValue("强覆盖高重叠采样点数");
            nrCell.AddCellValue("强覆盖高重叠采样点占比");
            nrCell.AddCellValue("强覆盖高重叠RSRP均值");
            nrCell.AddCellValue("强覆盖高重叠SINR均值");
            nrCell.AddCellValue("强覆盖高重叠的重叠度");
            nrCell.AddCellValue("强覆盖MOD3的采样点数");
            nrCell.AddCellValue("强覆盖MOD3的采样点占比");
            nrCell.AddCellValue("强覆盖MOD3的RSRP均值");
            nrCell.AddCellValue("强覆盖MOD3的SINR均值");
            nrCell.AddCellValue("强覆盖MOD3的重叠度");
            datasCell.Add(nrCell);

            int idx = 1;
            foreach (GridCellKey gKey in cellDataDic.Keys)
            {
                CellAntData cellData = cellDataDic[gKey];

                NPOIRow nr = new NPOIRow();

                nr.AddCellValue(idx++);
                nr.AddCellValue(gKey.strGridName);
                nr.AddCellValue(cellData.strCellName);
                if (cellData.lteCell != null)
                {
                    nr.AddCellValue(cellData.lteCell.ECI);
                    nr.AddCellValue(cellData.lteCell.SectorID);
                }
                else
                {
                    nr.AddCellValue("");
                    nr.AddCellValue("");
                }
                nr.AddCellValue(cellData.iEarfcn);
                nr.AddCellValue(cellData.iPci);

                nr.AddCellValue(cellData.sampleData.iSampleTotalNum);
                nr.AddCellValue(cellData.cellAntStat.iSampleNum);
                nr.AddCellValue(cellData.cellAntStat.FProbPointRate);
                nr.AddCellValue(cellData.cellAntStat.FRsrp);
                nr.AddCellValue(cellData.cellAntStat.FSinr);
                nr.AddCellValue(cellData.cellAntStat.FCover);

                nr.AddCellValue(cellData.cellAntStat.iWeakLowNum);
                nr.AddCellValue(cellData.cellAntStat.FWeakLowRate);
                nr.AddCellValue(cellData.cellAntStat.FWeakLowRsrp);
                nr.AddCellValue(cellData.cellAntStat.FWeakLowSinr);
                nr.AddCellValue(cellData.cellAntStat.FWeakLowCover);

                nr.AddCellValue(cellData.cellAntStat.iWeakHighNum);
                nr.AddCellValue(cellData.cellAntStat.FWeakHighRate);
                nr.AddCellValue(cellData.cellAntStat.FWeakHighRsrp);
                nr.AddCellValue(cellData.cellAntStat.FWeakHighSinr);
                nr.AddCellValue(cellData.cellAntStat.FWeakHighCover);

                nr.AddCellValue(cellData.cellAntStat.iGoodHighNum);
                nr.AddCellValue(cellData.cellAntStat.FGoodHighRate);
                nr.AddCellValue(cellData.cellAntStat.FGoodHighRsrp);
                nr.AddCellValue(cellData.cellAntStat.FGoodHighSinr);
                nr.AddCellValue(cellData.cellAntStat.FGoodHighCover);

                nr.AddCellValue(cellData.cellAntStat.iGoodMod3Num);
                nr.AddCellValue(cellData.cellAntStat.FGoodMod3Rate);
                nr.AddCellValue(cellData.cellAntStat.FGoodMod3Rsrp);
                nr.AddCellValue(cellData.cellAntStat.FGoodMod3Sinr);
                nr.AddCellValue(cellData.cellAntStat.FGoodMod3Cover);
                
                datasCell.Add(nr);
            }
            nrDatasList.Add(datasCell);
        }

        /// <summary>
        /// 道路数据统计
        /// </summary>
        private void fillRoadStatInfo(ref List<List<NPOIRow>> nrDatasList)
        {
            List<NPOIRow> datasCell = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("序号");
            cols.Add("地市");
            cols.Add("网格");
            cols.Add("道路名称");
            cols.Add("问题类别");
            cols.Add("持续距离(米)");
            cols.Add("持续时长(秒)");
            cols.Add("经度");
            cols.Add("纬度");
            cols.Add("平均覆盖距离(米)");
            cols.Add("第一强RSRP最大值");
            cols.Add("第一强RSRP最小值");
            cols.Add("第一强RSRP平均值");
            cols.Add("第一强SINR最大值");
            cols.Add("第一强SINR最小值");
            cols.Add("第一强SINR平均值");

            cols.Add("道路重叠覆盖度");
            cols.Add("高重叠覆盖度(>=4)采样点占比");
            cols.Add("文件名称");
            cols.Add("相关小区");
            nrCell.cellValues = cols;
            datasCell.Add(nrCell);

            foreach (int iRoad in roadStatDic.Keys)
            {
                RoadStat roadStat = roadStatDic[iRoad];
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(iRoad);
                objs.Add(roadStat.strCity);
                objs.Add(roadStat.StrGrid);
                objs.Add(roadStat.strRoadName);
                objs.Add(roadStat.strProbType);

                objs.Add(roadStat.fDistance);
                objs.Add(roadStat.fDuration);
                objs.Add(roadStat.firstLongLat.fLongitude);
                objs.Add(roadStat.firstLongLat.fLatitude);
                objs.Add(roadStat.fAvgCoverDist);

                objs.Add(roadStat.fMaxRsrp);
                objs.Add(roadStat.fMinRsrp);
                objs.Add(roadStat.fAvgRsrp);
                objs.Add(roadStat.fMaxSinr);
                objs.Add(roadStat.fMinSinr);
                objs.Add(roadStat.fAvgSinr);

                objs.Add(roadStat.iAvgCover);
                objs.Add(roadStat.str4CoverRate);
                objs.Add(roadStat.strFileName);
                objs.Add(roadStat.StrCellName);

                nr.cellValues = objs;
                datasCell.Add(nr);
            }
            nrDatasList.Add(datasCell);
        }

        /// <summary>
        /// 二维四象优化分析列头信息
        /// </summary>
        private List<object> InitDetailColumns(string strColName)
        {
            List<object> cols = new List<object>();
            cols.Add("序号");
            cols.Add("地市");
            cols.Add("网格");
            cols.Add(">=65且重叠度=1且MOD3" + strColName);
            cols.Add("[-70,-65)且重叠度=1且MOD3" + strColName);
            cols.Add("[-75,-70)且重叠度=1且MOD3" + strColName);
            cols.Add("[-80,-75)且重叠度=1且MOD3" + strColName);
            cols.Add("[-85,-80)且重叠度=1且MOD3" + strColName);
            cols.Add("[-90,-85)且重叠度=1且MOD3" + strColName);
            cols.Add("[-95,-90)且重叠度=1且MOD3" + strColName);
            cols.Add("[-100,-95)且重叠度=1" + strColName);
            cols.Add("[-105,-100)且重叠度=1" + strColName);
            cols.Add("[-110,-105)且重叠度=1" + strColName);
            cols.Add("[-115,-110)且重叠度=1" + strColName);
            cols.Add("[-120,-115)且重叠度=1" + strColName);
            cols.Add("<-120且重叠度=1" + strColName);
            cols.Add(">=65且重叠度=2且MOD3" + strColName);
            cols.Add("[-70,-65)且重叠度=2且MOD3" + strColName);
            cols.Add("[-75,-70)且重叠度=2且MOD3" + strColName);
            cols.Add("[-80,-75)且重叠度=2且MOD3" + strColName);
            cols.Add("[-85,-80)且重叠度=2且MOD3" + strColName);
            cols.Add("[-90,-85)且重叠度=2且MOD3" + strColName);
            cols.Add("[-95,-90)且重叠度=2且MOD3" + strColName);
            cols.Add("[-100,-95)且重叠度=2" + strColName);
            cols.Add("[-105,-100)且重叠度=2" + strColName);
            cols.Add("[-110,-105)且重叠度=2" + strColName);
            cols.Add("[-115,-110)且重叠度=2" + strColName);
            cols.Add("[-120,-115)且重叠度=2" + strColName);
            cols.Add("<-120且重叠度=2" + strColName);
            cols.Add(">=65且重叠度=3且MOD3" + strColName);
            cols.Add("[-70,-65)且重叠度=3且MOD3" + strColName);
            cols.Add("[-75,-70)且重叠度=3且MOD3" + strColName);
            cols.Add("[-80,-75)且重叠度=3且MOD3" + strColName);
            cols.Add("[-85,-80)且重叠度=3且MOD3" + strColName);
            cols.Add("[-90,-85)且重叠度=3且MOD3" + strColName);
            cols.Add("[-95,-90)且重叠度=3且MOD3" + strColName);
            cols.Add("[-100,-95)且重叠度=3" + strColName);
            cols.Add("[-105,-100)且重叠度=3" + strColName);
            cols.Add("[-110,-105)且重叠度=3" + strColName);
            cols.Add("[-115,-110)且重叠度=3" + strColName);
            cols.Add("[-120,-115)且重叠度=3" + strColName);
            cols.Add("<-120且重叠度=3" + strColName);
            cols.Add(">=65且重叠度=4" + strColName);
            cols.Add("[-70,-65)且重叠度=4" + strColName);
            cols.Add("[-75,-70)且重叠度=4" + strColName);
            cols.Add("[-80,-75)且重叠度=4" + strColName);
            cols.Add("[-85,-80)且重叠度=4" + strColName);
            cols.Add("[-90,-85)且重叠度=4" + strColName);
            cols.Add("[-95,-90)且重叠度=4" + strColName);
            cols.Add("[-100,-95)且重叠度=4" + strColName);
            cols.Add("[-105,-100)且重叠度=4" + strColName);
            cols.Add("[-110,-105)且重叠度=4" + strColName);
            cols.Add("[-115,-110)且重叠度=4" + strColName);
            cols.Add("[-120,-115)且重叠度=4" + strColName);
            cols.Add("<-120且重叠度=4" + strColName);
            cols.Add(">=65且重叠度=5" + strColName);
            cols.Add("[-70,-65)且重叠度=5" + strColName);
            cols.Add("[-75,-70)且重叠度=5" + strColName);
            cols.Add("[-80,-75)且重叠度=5" + strColName);
            cols.Add("[-85,-80)且重叠度=5" + strColName);
            cols.Add("[-90,-85)且重叠度=5" + strColName);
            cols.Add("[-95,-90)且重叠度=5" + strColName);
            cols.Add("[-100,-95)且重叠度=5" + strColName);
            cols.Add("[-105,-100)且重叠度=5" + strColName);
            cols.Add("[-110,-105)且重叠度=5" + strColName);
            cols.Add("[-115,-110)且重叠度=5" + strColName);
            cols.Add("[-120,-115)且重叠度=5" + strColName);
            cols.Add("<-120且重叠度=5" + strColName);
            cols.Add(">=65且重叠度>5" + strColName);
            cols.Add("[-70,-65)且重叠度>5" + strColName);
            cols.Add("[-75,-70)且重叠度>5" + strColName);
            cols.Add("[-80,-75)且重叠度>5" + strColName);
            cols.Add("[-85,-80)且重叠度>5" + strColName);
            cols.Add("[-90,-85)且重叠度>5" + strColName);
            cols.Add("[-95,-90)且重叠度>5" + strColName);
            cols.Add("[-100,-95)且重叠度>5" + strColName);
            cols.Add("[-105,-100)且重叠度>5" + strColName);
            cols.Add("[-110,-105)且重叠度>5" + strColName);
            cols.Add("[-115,-110)且重叠度>5" + strColName);
            cols.Add("[-120,-115)且重叠度>5" + strColName);
            cols.Add("<-120且重叠度>5" + strColName);
            return cols;
        }

        /// <summary>
        /// 数据结果窗赋值
        /// </summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames,
                                        Dictionary<string, CellAntStat> gridAntStatDic, Dictionary<string, SampleData> allSampleDataDic)
        {
            int iRsrpSplit = ((0 - structSetForm.iRsrpSplit) - 65) / 5;
            if (gridAntStatDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的结果集！！！");
                return;
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LTEScanStructForm).FullName);
            LTEScanStructForm form = obj == null ? null : obj as LTEScanStructForm;
            if (form == null || form.IsDisposed)
            {
                form = new LTEScanStructForm(MainModel);
            }
            form.RsrpSplit = iRsrpSplit;
            form.RowDatasList = nrDatasList;
            form.sheetNames = sheetNames;
            form.GridAntStatDic = gridAntStatDic;
            form.AllSampleDataDic = allSampleDataDic;
            form.CellInfoDic = cellDataDic;
            form.RoadStatDic = roadStatDic;
            form.FillData();
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        public class GridCellKey
        {
            public string strCellName { get; set; }
            public string strGridName { get; set; }

            public GridCellKey()
            {
                strCellName = "";
                strGridName = "";
            }

            public override bool Equals(object obj)
            {
                GridCellKey other = obj as GridCellKey;
                if (other == null)
                    return false;

                if (!base.GetType().Equals(obj.GetType()))
                    return false;

                return (this.strCellName.Equals(other.strCellName) &&
                        this.strGridName.Equals(other.strGridName));
            }

            public override int GetHashCode()
            {
                string strKey = string.Format("{0}_{1}", strGridName, strCellName);
                return strKey.GetHashCode();
            }
        }

        public class LteCellInfo
        {
            public LTECell lteCell { get; set; }
            public string strGrid { get; set; }
            public int iEarfcn { get; set; }
            public int iPci { get; set; }

            public LteCellInfo()
            {
                strGrid = "";
            }

            public string strCellName
            {
                get
                {
                    if (lteCell == null || lteCell.Name == "")
                    {
                        return string.Format("{0}-{1}", iEarfcn, iPci);
                    }
                    else
                    {
                        return lteCell.Name;
                    }
                }
            }
        }

        public class SampleSubInfo : LteCellInfo
        {
            public int iSampleid { get; set; }
            public long lTimeWithMillseconde { get; set; }
            public LongLat longLat { get; set; }
            public float fRsrp { get; set; }
            public float fSinr { get; set; }
            public int iCover { get; set; }
            public string strProbType { get; set; }

            public void anaProbType(int iRsrpSplit)
            {
                if (fRsrp >= iRsrpSplit && iCover <= 3)
                {
                    strProbType = "强覆盖MOD3";
                }
                else if (fRsrp >= iRsrpSplit && iCover > 3)
                {
                    strProbType = "强覆盖高重叠";
                }
                else if (fRsrp < iRsrpSplit && iCover <= 3)
                {
                    strProbType = "弱覆盖低重叠";
                }
                else if (fRsrp < iRsrpSplit && iCover > 3)
                {
                    strProbType = "弱覆盖高重叠";
                }
            }

            public SampleSubInfo()
            {
                iSampleid = 0;
                lTimeWithMillseconde = 0;
                longLat = new LongLat();
                fRsrp = 0;
                fSinr = 0;
                iCover = 0;
                strProbType = "";
            }
        }

        public class RoadStat
        {
            public string strCity { get; set; }
            public string strProbType { get; set; }
            public float fDistance { get; set; }
            public float fDuration { get; set; }
            public LongLat firstLongLat { get; set; }
            public string strFileName { get; set; }
            public string strRoadName { get; set; }

            public float fMaxRsrp { get; set; }
            public float fMinRsrp { get; set; }
            public float fAvgRsrp { get; set; }
            public float fMaxSinr { get; set; }
            public float fMinSinr { get; set; }
            public float fAvgSinr { get; set; }
            public int iAvgCover { get; set; }
            public string str4CoverRate { get; set; }

            public string StrGrid
            {
                get
                {
                    StringBuilder strTmp = new StringBuilder();
                    foreach (string strGrid in gridList)
                    {
                        strTmp.Append(strGrid);
                        strTmp.Append(",");
                    }
                    return strTmp.ToString().TrimEnd(',');
                }
            }
            public string StrCellName
            {
                get
                {
                    StringBuilder strTmp = new StringBuilder();
                    foreach (string strCell in cellNameList)
                    {
                        strTmp.Append(strCell);
                        strTmp.Append(",");
                    }
                    return strTmp.ToString().TrimEnd(',');
                }
            }
            public float fAvgCoverDist
            {
                get
                {
                    float sumDist = 0;
                    int iNum = coverDistList.Count;
                    if (iNum == 0)
                        return 0;

                    foreach (float fDist in coverDistList)
                    {
                        sumDist += fDist;
                    }

                    return sumDist / iNum;
                }
            }

            public Dictionary<int, List<LongLat>> testPointDic { get; set; }
            public List<string> gridList { get; set; }
            public List<string> cellNameList { get; set; }
            public List<float> coverDistList { get; set; }

            public RoadStat()
            {
                strCity = "";
                strProbType = "";
                strFileName = "";
                strRoadName = "";
                fDistance = 0;
                fDuration = 0;
                firstLongLat = new LongLat();

                fMaxRsrp = -999;
                fMinRsrp = 999;
                fAvgRsrp = 0;

                fMaxSinr = -999;
                fMinSinr = 999;
                fAvgSinr = 0;

                iAvgCover = 0;
                str4CoverRate = "0%";

                testPointDic = new Dictionary<int, List<LongLat>>();
                gridList = new List<string>();
                cellNameList = new List<string>();
                coverDistList = new List<float>();
            }

            public void fillValue(List<SampleSubInfo> sampleList)
            {
                int numSample = sampleList.Count;
                firstLongLat = sampleList[0].longLat;
                fDuration = Convert.ToSingle(sampleList[numSample - 1].lTimeWithMillseconde - sampleList[0].lTimeWithMillseconde) / 1000;

                float sumRsrp = 0;
                float sumSinr = 0;
                float sumCover = 0;
                float f4Cover = 0;

                foreach (SampleSubInfo sample in sampleList)
                {
                    if (strRoadName == "")
                        strRoadName = GISManager.GetInstance().GetRoadPlaceDesc(sample.longLat.fLongitude, sample.longLat.fLatitude);

                    float rsrp = sample.fRsrp;
                    sumRsrp += rsrp;
                    fMaxRsrp = getMaxData(rsrp, fMaxRsrp);
                    fMinRsrp = getMinData(rsrp, fMinRsrp);

                    float sinr = sample.fSinr;
                    sumSinr += sinr;
                    fMaxSinr = getMaxData(rsrp, fMaxSinr);
                    fMinSinr = getMinData(rsrp, fMinSinr);

                    int iCover = sample.iCover;
                    sumCover += iCover;
                    if (iCover >= 4)
                        f4Cover += 1;

                    int iMergeKey = getMergeKey(rsrp, iCover);

                    List<LongLat> tpList;
                    if (!testPointDic.TryGetValue(iMergeKey, out tpList))
                        tpList = new List<LongLat>();
                    tpList.Add(sample.longLat);
                    testPointDic[iMergeKey] = tpList;
                }

                if (numSample != 0)
                {
                    fAvgRsrp = sumRsrp / numSample;
                    fAvgSinr = sumSinr / numSample;
                    iAvgCover = (int)(sumCover / numSample);
                    str4CoverRate = string.Format("{0}%", Math.Round(100 * f4Cover / numSample, 2));
                }
            }

            private float getMaxData(float data, float maxData)
            {
                if (data > maxData)
                {
                    return data;
                }
                return maxData;
            }

            private float getMinData(float data, float minData)
            {
                if (data < minData)
                {
                    return data;
                }
                return minData;
            }


            private int getMergeKey(float rsrp, int iCover)
            {
                int iRsrpLevel = 0;
                if (rsrp >= -65)
                    iRsrpLevel = 0;
                else if (rsrp < -120)
                    iRsrpLevel = 12;
                else
                    iRsrpLevel = (int)Math.Ceiling(((0 - rsrp) - 65) / 5);

                int iMergeKey = iRsrpLevel * 10 + iCover;
                return iMergeKey;
            }
        }

        public class SampleData
        {
            public SampleData()
            {
                rsrpValue = new int[13];
                coverValue = new int[6];
                fMultiSinrSum = new float[13, 6];
                iMultiSampleNum = new int[13, 6];
                testPointDic = new Dictionary<int, List<LongLat>>();
            }

            public int iSampleTotalNum { get; set; }
            public int iSampleStatNum { get; set; }
            public int[] rsrpValue { get; set; }
            public int[] coverValue { get; set; }
            public float[,] fMultiSinrSum { get; set; }
            public int[,] iMultiSampleNum { get; set; }
            public Dictionary<int, List<LongLat>> testPointDic { get; set; }

            public string[,] DMultiSinr
            {
                get
                {
                    string[,] tmpRate = new string[13, 6];
                    for (int i = 0; i < 13; i++)
                    {
                        for (int j = 0; j < 6; j++)
                        {
                            if (iMultiSampleNum[i, j] == 0)
                                tmpRate[i, j] = "";
                            else
                                tmpRate[i, j] = Math.Round(fMultiSinrSum[i, j] / iMultiSampleNum[i, j], 2) + "";
                        }
                    }
                    return tmpRate;
                }
            }

            public string[,] DMultiSampleRate
            {
                get
                {
                    string[,] tmpRate = new string[13, 6];
                    for (int i = 0; i < 13; i++)
                    {
                        for (int j = 0; j < 6; j++)
                        {
                            if (iSampleTotalNum == 0)
                                tmpRate[i, j] = "0%";
                            else
                                tmpRate[i, j] = Math.Round((100.0 * iMultiSampleNum[i, j]) / iSampleTotalNum, 2) + "%";
                        }
                    }
                    return tmpRate;
                }
            }

            public void addValue(CellAntData cellData)
            {
                this.iSampleTotalNum += cellData.sampleData.iSampleTotalNum;
                this.iSampleStatNum += cellData.sampleData.iSampleStatNum;
                for (int i = 0; i < 13; i++)
                {
                    this.rsrpValue[i] += cellData.sampleData.rsrpValue[i];
                }
                for (int i = 0; i < 6; i++)
                {
                    this.coverValue[i] += cellData.sampleData.coverValue[i];
                }
                for (int i = 0; i < 13; i++)
                {
                    for (int j = 0; j < 6; j++)
                    {
                        this.fMultiSinrSum[i, j] += cellData.sampleData.fMultiSinrSum[i, j];
                        this.iMultiSampleNum[i, j] += cellData.sampleData.iMultiSampleNum[i, j];
                    }
                }
                foreach (int iMergeKey in cellData.sampleData.testPointDic.Keys)
                {
                    List<LongLat> tpList;
                    if (!testPointDic.TryGetValue(iMergeKey, out tpList))
                        tpList = new List<LongLat>();

                    tpList.AddRange(cellData.sampleData.testPointDic[iMergeKey]);
                    testPointDic[iMergeKey] = tpList;
                }
            }
        }

        public class CellAntData : LteCellInfo
        {
            public CellAntData()
            {
                cellAntStat = new CellAntStat();
                sampleData = new SampleData();
            }

            public CellAntStat cellAntStat { get; set; }
            public SampleData sampleData { get; set; }

            public void fillAntData(float fRsrp, float fSinr, int iCover, int iSamePciTime, LongLat ll, int iRsrpSplit)
            {
                iCover = iCover > 5 ? 5 : iCover;

                int iRsrpLevel = 0;
                if (fRsrp >= -65)
                    iRsrpLevel = 0;
                else if (fRsrp < -120)
                    iRsrpLevel = 12;
                else
                    iRsrpLevel = (int)Math.Ceiling(((0 - fRsrp) - 65) / 5);

                sampleData.iSampleTotalNum += 1;
                cellAntStat.iTotalSampleNum += 1;
                if ((fRsrp >= iRsrpSplit && iCover <= 3 && iSamePciTime == 0) || iCover < 0)
                    return;

                sampleData.iSampleStatNum += 1;
                sampleData.coverValue[iCover] += 1;
                sampleData.rsrpValue[iRsrpLevel] += 1;
                sampleData.iMultiSampleNum[iRsrpLevel, iCover] += 1;
                sampleData.fMultiSinrSum[iRsrpLevel, iCover] += fSinr;
                cellAntStat.fillValue(fRsrp, fSinr, iCover, iRsrpSplit);
                int iKey = iRsrpLevel * 10 + iCover;
                List<LongLat> tpList;
                if (!sampleData.testPointDic.TryGetValue(iKey, out tpList))
                {
                    tpList = new List<LongLat>();
                }
                tpList.Add(ll);
                sampleData.testPointDic[iKey] = tpList;
            }
        }

        public class CellAntStat
        {
            public int iTotalSampleNum { get; set; }
            public int iSampleNum { get; set; }
            private float fRsrpSum = 0;
            private float fSinrSum = 0;
            private float fCoverSum = 0;
            public string FProbPointRate
            {
                get
                {
                    if (iTotalSampleNum == 0)
                        return "0%";

                    return Math.Round(iSampleNum * 100.0 / iTotalSampleNum, 2) + "%";
                }
            }
            public double FRsrp
            {
                get
                {
                    if (iSampleNum == 0)
                        return 0;

                    return Math.Round(fRsrpSum / iSampleNum, 2);
                }
            }
            public double FSinr
            {
                get
                {
                    if (iSampleNum == 0)
                        return 0;

                    return Math.Round(fSinrSum / iSampleNum, 2);
                }
            }
            public double FCover
            {
                get
                {
                    if (iSampleNum == 0)
                        return 0;

                    return Math.Round(fCoverSum / iSampleNum, 2);
                }
            }

            //弱覆盖低重叠
            public int iWeakLowNum { get; set; }
            private float fWeakLowRsrpSum = 0;
            private float fWeakLowSinrSum = 0;
            private float fWeakLowCoverSum = 0;

            public string FWeakLowRate
            {
                get
                {
                    if (iTotalSampleNum == 0)
                        return "0%";

                    return Math.Round(iWeakLowNum * 100.0 / iTotalSampleNum, 2) + "%";
                }
            }
            public double FWeakLowRsrp
            {
                get
                {
                    if (iWeakLowNum == 0)
                        return 0;

                    return Math.Round(fWeakLowRsrpSum / iWeakLowNum, 2);
                }
            }
            public double FWeakLowSinr
            {
                get
                {
                    if (iWeakLowNum == 0)
                        return 0;

                    return Math.Round(fWeakLowSinrSum / iWeakLowNum, 2);
                }
            }
            public double FWeakLowCover
            {
                get
                {
                    if (iWeakLowNum == 0)
                        return 0;

                    return Math.Round(fWeakLowCoverSum / iWeakLowNum, 2);
                }
            }

            //弱覆盖高重叠
            public int iWeakHighNum { get; set; }
            private float fWeakHighRsrpSum = 0;
            private float fWeakHighSinrSum = 0;
            private float fWeakHighCoverSum = 0;

            public string FWeakHighRate
            {
                get
                {
                    if (iTotalSampleNum == 0)
                        return "0%";

                    return Math.Round(iWeakHighNum * 100.0 / iTotalSampleNum, 2) + "%";
                }
            }
            public double FWeakHighRsrp
            {
                get
                {
                    if (iWeakHighNum == 0)
                        return 0;

                    return Math.Round(fWeakHighRsrpSum / iWeakHighNum, 2);
                }
            }
            public double FWeakHighSinr
            {
                get
                {
                    if (iWeakHighNum == 0)
                        return 0;

                    return Math.Round(fWeakHighSinrSum / iWeakHighNum, 2);
                }
            }
            public double FWeakHighCover
            {
                get
                {
                    if (iWeakHighNum == 0)
                        return 0;

                    return Math.Round(fWeakHighCoverSum / iWeakHighNum, 2);
                }
            }

            //强覆盖高重叠
            public int iGoodHighNum { get; set; }
            private float fGoodHighRsrpSum = 0;
            private float fGoodHighSinrSum = 0;
            private float fGoodHighCoverSum = 0;

            public string FGoodHighRate
            {
                get
                {
                    if (iTotalSampleNum == 0)
                        return "0%";

                    return Math.Round(iGoodHighNum * 100.0 / iTotalSampleNum, 2) + "%";
                }
            }
            public double FGoodHighRsrp
            {
                get
                {
                    if (iGoodHighNum == 0)
                        return 0;

                    return Math.Round(fGoodHighRsrpSum / iGoodHighNum, 2);
                }
            }
            public double FGoodHighSinr
            {
                get
                {
                    if (iGoodHighNum == 0)
                        return 0;

                    return Math.Round(fGoodHighSinrSum / iGoodHighNum, 2);
                }
            }
            public double FGoodHighCover
            {
                get
                {
                    if (iGoodHighNum == 0)
                        return 0;

                    return Math.Round(fGoodHighCoverSum / iGoodHighNum, 2);
                }
            }

            //弱覆盖MOD3
            public int iGoodMod3Num { get; set; }
            private float fGoodMod3RsrpSum = 0;
            private float fGoodMod3SinrSum = 0;
            private float fGoodMod3CoverSum = 0;

            public string FGoodMod3Rate
            {
                get
                {
                    if (iTotalSampleNum == 0)
                        return "0%";

                    return Math.Round(iGoodMod3Num * 100.0 / iTotalSampleNum, 2) + "%";
                }
            }
            public double FGoodMod3Rsrp
            {
                get
                {
                    if (iGoodMod3Num == 0)
                        return 0;

                    return Math.Round(fGoodMod3RsrpSum / iGoodMod3Num, 2);
                }
            }
            public double FGoodMod3Sinr
            {
                get
                {
                    if (iGoodMod3Num == 0)
                        return 0;

                    return Math.Round(fGoodMod3SinrSum / iGoodMod3Num, 2);
                }
            }
            public double FGoodMod3Cover
            {
                get
                {
                    if (iGoodMod3Num == 0)
                        return 0;

                    return Math.Round(fGoodMod3CoverSum / iGoodMod3Num, 2);
                }
            }

            public void fillValue(float fRsrp, float fSinr, int iCover, int iRsrpSplit)
            {
                iSampleNum += 1;
                fRsrpSum += fRsrp;
                fSinrSum += fSinr;
                fCoverSum += iCover;

                if (fRsrp >= iRsrpSplit && iCover <= 3)
                {
                    iGoodMod3Num += 1;
                    fGoodMod3RsrpSum += fRsrp;
                    fGoodMod3SinrSum += fSinr;
                    fGoodMod3CoverSum += iCover;
                }
                else if (fRsrp >= iRsrpSplit && iCover > 3)
                {
                    iGoodHighNum += 1;
                    fGoodHighRsrpSum += fRsrp;
                    fGoodHighSinrSum += fSinr;
                    fGoodHighCoverSum += iCover;
                }
                else if (fRsrp < iRsrpSplit && iCover <= 3)
                {
                    iWeakLowNum += 1;
                    fWeakLowRsrpSum += fRsrp;
                    fWeakLowSinrSum += fSinr;
                    fWeakLowCoverSum += iCover;
                }
                else if (fRsrp < iRsrpSplit && iCover > 3)
                {
                    iWeakHighNum += 1;
                    fWeakHighRsrpSum += fRsrp;
                    fWeakHighSinrSum += fSinr;
                    fWeakHighCoverSum += iCover;
                }
            }

            public void addValue(CellAntStat cellStat)
            {
                iTotalSampleNum += cellStat.iTotalSampleNum;
                iSampleNum += cellStat.iSampleNum;
                fRsrpSum += cellStat.fRsrpSum;
                fSinrSum += cellStat.fSinrSum;
                fCoverSum += cellStat.fCoverSum;

                iGoodMod3Num += cellStat.iGoodMod3Num;
                fGoodMod3RsrpSum += cellStat.fGoodMod3RsrpSum;
                fGoodMod3SinrSum += cellStat.fGoodMod3SinrSum;
                fGoodMod3CoverSum += cellStat.fGoodMod3CoverSum;

                iGoodHighNum += cellStat.iGoodHighNum;
                fGoodHighRsrpSum += cellStat.fGoodHighRsrpSum;
                fGoodHighSinrSum += cellStat.fGoodHighSinrSum;
                fGoodHighCoverSum += cellStat.fGoodHighCoverSum;

                iWeakLowNum += cellStat.iWeakLowNum;
                fWeakLowRsrpSum += cellStat.fWeakLowRsrpSum;
                fWeakLowSinrSum += cellStat.fWeakLowSinrSum;
                fWeakLowCoverSum += cellStat.fWeakLowCoverSum;

                iWeakHighNum += cellStat.iWeakHighNum;
                fWeakHighRsrpSum += cellStat.fWeakHighRsrpSum;
                fWeakHighSinrSum += cellStat.fWeakHighSinrSum;
                fWeakHighCoverSum += cellStat.fWeakHighCoverSum;
            }
        }

        /// <summary>
        /// 实现对采样点时间的排序类
        /// </summary>
        public class LteSampleByTime
        {
            //实现排序的接口
            public static IComparer<SampleSubInfo> GetCompareByTime()
            {
                if (comparerByTime == null)
                {
                    comparerByTime = new CompareBySampleTime();
                }
                return comparerByTime;
            }
            public class CompareBySampleTime : IComparer<SampleSubInfo>
            {
                public int Compare(SampleSubInfo x, SampleSubInfo y)
                {
                    return x.lTimeWithMillseconde.CompareTo(y.lTimeWithMillseconde);
                }
            }
            private static IComparer<SampleSubInfo> comparerByTime;
        }
    }
}
