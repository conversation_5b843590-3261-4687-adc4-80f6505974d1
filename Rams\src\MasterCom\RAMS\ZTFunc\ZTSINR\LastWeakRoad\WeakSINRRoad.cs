﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSINRRoad
    {
        protected readonly List<string> cellNames = new List<string>();
        public string CellName
        {
            get
            {
                StringBuilder cellNameStr = new StringBuilder();
                foreach (string name in cellNames)
                {
                    if (cellNameStr.Length > 0)
                    {
                        cellNameStr.Append(" | ");
                    }
                    cellNameStr.Append(name);
                }
                return cellNameStr.ToString();
            }
        }

        protected readonly List<string> lacciList = new List<string>();
        public string LACCIs
        {
            get
            {
                StringBuilder laccis = new StringBuilder();
                foreach (string lacci in lacciList)
                {
                    if (laccis.Length > 0)
                    {
                        laccis.Append(" | ");
                    }
                    laccis.Append(lacci);
                }
                return laccis.ToString();
            }
        }
        public int SN
        {
            get;
            set;
        }
        public double Second
        {
            get
            {
                double sec = 0;
                if (testPoints.Count > 1)
                {
                    sec = (testPoints[testPoints.Count - 1].DateTime - testPoints[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }
        public string CityName { get; set; }
        public string StartTime
        {
            get
            {
                return testPoints[0].DateTime.ToString();
            }
        }
        public double WeakPercent
        {
            get;
            set;
        }

        /// <summary>
        /// FDD采样点占比
        /// </summary>
        public double FDDPointPercent
        {
            get;
            set;
        }
        public double Distance
        {
            get
            {
                return Math.Round(distance, 2);
            }
            set
            {
                distance = value;
            }
        }
        public double Duration
        {
            get;
            set;
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (testPoints.Count > 0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }

        protected double distance = 0;
        protected readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        public float totalSINR { get; set; } = 0;
        public float totalRSRP { get; set; } = 0;

        protected float minSINR = float.MaxValue;
        public float MinSINR
        {
            get { return minSINR; }
        }
        protected float minRSPR = float.MaxValue;
        public float MinRSRP
        {
            get { return minRSPR; }
        }
        public float MaxSINR
        {
            get { return maxSINR; }
        }
        protected float maxSINR = float.MinValue;
        public float MaxRSRP
        {
            get { return maxRSRP; }
        }
        protected float maxRSRP = float.MinValue;
        public float AvgSINR
        {
            get { return (float)Math.Round(totalSINR / testPoints.Count, 2); }
        }
        public int rsrpNum { get; set; } = 0;
        public float AvgRSRP
        {
            get { return (float)Math.Round(totalRSRP / rsrpNum, 2); }
        }
        
        protected double avgSpeed = 0;
        public double AvgSpeed
        {
            get 
            {
                return avgSpeed;
            }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }

        protected double dlCode0Qam64Rate = double.MinValue;
        public double DlCode0Qam64Rate
        {
            get 
            {
                return dlCode0Qam64Rate; 
            }
        }

        protected double dlCode1Qam64Rate = double.MinValue;
        public double DlCode1Qam64Rate
        {
            get 
            {
                return dlCode1Qam64Rate; 
            }
        }

        protected double rank_Indicator = double.MinValue;
        public double Rank_Indicator
        {
            get 
            {
                return rank_Indicator;
            }
        }
        protected double pdsch_Bler = double.MinValue;
        public double PDSCH_BLER
        {
            get { return pdsch_Bler; }
        }

        protected double tra_ModeCount = 0;
        protected double tra_Mode3Count = 0;
        protected double transmission_Mode = double.MinValue;
        public double Transmission_Mode
        {
            get { return transmission_Mode; }
        }
        protected double pdsch_PRb_Num_s = double.MinValue;
        public double PDSCH_PRb_Num_s
        {
            get { return pdsch_PRb_Num_s; }
        }
        
        public string GridName { get; set; }
        internal void Add(float sinr, float? rsrp, double distance, TestPoint testPoint)
        {
            totalSINR += sinr;
            minSINR = Math.Min(minSINR, sinr);
            maxSINR = Math.Max(maxSINR, sinr);
            if (rsrp != null && rsrp >= -141 && rsrp <= -10)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
                minRSPR = Math.Min(minRSPR, (float)rsrp);
                maxRSRP = Math.Max(maxRSRP, (float)rsrp);
            }
            this.distance += distance;
            testPoints.Add(testPoint);
        }

        internal void Add(float? sinr, float? rsrp, TestPoint testPoint)
        {
            addCellInfoList(testPoint);

            if (sinr != null)
            {
                totalSINR += (float)sinr;
                minSINR = Math.Min(minSINR, (float)sinr);
                maxSINR = Math.Max(maxSINR, (float)sinr);
            }

            if (rsrp != null && rsrp >= -141 && rsrp <= -10)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
                minRSPR = Math.Min(minRSPR, (float)rsrp);
                maxRSRP = Math.Max(maxRSRP, (float)rsrp);
            }
            testPoints.Add(testPoint);
        }

        protected virtual void addCellInfoList(TestPoint testPoint)
        {
            int? lac = getLac(testPoint);
            LTECell lteCell = testPoint.GetMainCell_LTE();

            if (lteCell != null)
            {
                string lacci = lac.ToString() + "_" + lteCell.SCellID.ToString();
                if (!lacciList.Contains(lacci))
                {
                    lacciList.Add(lacci);
                }
                if (!cellNames.Contains(lteCell.Name))
                {
                    cellNames.Add(lteCell.Name);
                }
            }
        }

        protected virtual int? getLac(TestPoint testPoint)
        {
            return (int?)(ushort?)testPoint["lte_TAC"];
        }

        public virtual void MakeSummary()
        {
            avgSpeed = this["lte_APP_Speed_Mb", SummaryType.Avg];

            double c0Qam64Sum = this["lte_Times_QAM64_DLCode0", SummaryType.Sum];
            double c0QpskSum = this["lte_Times_QPSK_DLCode0", SummaryType.Sum];
            double c0Qam16Sum = this["lte_Times_QAM16_DLCode0", SummaryType.Sum];
            dlCode0Qam64Rate = Math.Round(100.0 * c0Qam64Sum / (c0Qam64Sum + c0QpskSum + c0Qam16Sum), 2);

            double c1Qam64Sum = this["lte_Times_QAM64_DLCode1", SummaryType.Sum];
            double c1QpskSum = this["lte_Times_QPSK_DLCode1", SummaryType.Sum];
            double c1Qam16Sum = this["lte_Times_QAM16_DLCode1", SummaryType.Sum];
            dlCode1Qam64Rate = Math.Round(100.0 * c1Qam64Sum / (c1Qam64Sum + c1QpskSum + c1Qam16Sum), 2);

            pdsch_Bler = this["lte_PDSCH_BLER", SummaryType.Avg];//质差误块率

            pdsch_PRb_Num_s = this["lte_PDSCH_PRb_Num_s", SummaryType.Avg];//质差PRB调度数

            int time1Sum = 0;
            int time2Sum = 0;
            int lastRank = 0;
            int lastIdx = -1;
            for (int i = 0; i < TestPoints.Count; i++)
            {
                TestPoint tp = TestPoints[i];

                sumRankIndicator(ref time1Sum, ref time2Sum, ref lastRank, ref lastIdx, i, tp);
                int tra_Mode = 0;
                object obj1 = tp["lte_Transmission_Mode"];
                if (obj1 != null)
                {
                    int.TryParse(obj1.ToString(), out tra_Mode);

                    tra_ModeCount++;
                    if (tra_Mode == 3)
                    {
                        tra_Mode3Count++;
                    }
                }

            }
            rank_Indicator = Math.Round(time2Sum * 100.0 / (time1Sum + time2Sum), 2);
            transmission_Mode = Math.Round(tra_Mode3Count * 100.0 / tra_ModeCount, 2);
        }

        protected virtual void sumRankIndicator(ref int time1Sum, ref int time2Sum, ref int lastRank, ref int lastIdx, int i, TestPoint tp)
        {
            #region lte_Rank_Indicator质差双流比例
            bool added = false;
            int rank = 0;
            object obj = tp["lte_Rank_Indicator"];
            if (obj != null)
            {
                int.TryParse(obj.ToString(), out rank);
            }

            if (lastRank == 0 && (rank == 1 || rank == 2))
            {
                lastIdx = i;
                lastRank = rank;
            }

            if (rank != lastRank)
            {
                if (lastRank == 1)
                {
                    time1Sum += tp.Time - TestPoints[lastIdx].Time;
                    added = true;
                }
                else if (lastRank == 2)
                {
                    time2Sum += tp.Time - TestPoints[lastIdx].Time;
                    added = true;
                }
                lastIdx = i;
            }
            lastRank = rank;

            if (i == TestPoints.Count - 1 && !added)
            {//避免漏掉最后一段
                if (lastRank == 1)
                {
                    time1Sum += tp.Time - TestPoints[lastIdx].Time;
                }
                else if (lastRank == 2)
                {
                    time2Sum += tp.Time - TestPoints[lastIdx].Time;
                }
            }
            #endregion
        }

        public string RoadName { get; set; } = string.Empty;
        public string MotorWay { get; set; } = "";
        public void SetMotorWay(int areaID, int areaTypeID)
        {
            MotorWay = AreaManager.GetInstance().GetAreaDesc(areaTypeID, areaID);
        }    

        public double MidLng
        {
            get
            {
                double lng = double.NaN;
                if (testPoints.Count > 0)
                {
                    lng = testPoints[(testPoints.Count / 2)].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (testPoints.Count > 0)
                {
                    lat = testPoints[(testPoints.Count / 2)].Latitude;
                }
                return lat;
            }
        }
        public void FindRoadName()
        {
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }

        public void FindGridName()
        {
            string strGridName = GISManager.GetInstance().GetGridDesc(MidLng, MidLat);
            if (strGridName != null)
            {
                if (GridName == null || GridName == "")
                {
                    GridName = strGridName;
                }
                else
                {
                    if (!GridName.Contains(strGridName) && strGridName != "")
                    {
                        GridName += "," + strGridName;
                    }
                }
            }
        }

        public WeakSINRRoad()
        {
        }

        public WeakSINRRoad(WeakSINRRoad road)
        {
            testPoints.AddRange(road.TestPoints);
            SN = road.SN;
            distance = road.Distance;
            minSINR = road.MinSINR;
            minRSPR = road.MinRSRP;
            maxSINR = road.MaxSINR;
            maxRSRP = road.MaxRSRP;

            totalRSRP = road.totalRSRP;
            totalSINR = road.totalSINR;
            rsrpNum = road.rsrpNum;
            RoadName = road.RoadName;

            dlCode0Qam64Rate = road.dlCode0Qam64Rate;
            rank_Indicator = road.rank_Indicator;
            pdsch_Bler = road.pdsch_Bler;
            transmission_Mode = road.transmission_Mode;
            pdsch_PRb_Num_s = road.pdsch_PRb_Num_s;
        }

        public enum SummaryType
        {
            Num,
            Sum,
            Avg
        }

        public double this[string key, SummaryType type]
        {
            get
            {
                double sum = 0;
                int num = 0;
                foreach (TestPoint tp in TestPoints)
                {
                    object objV = tp[key];
                    if (objV == null)
                    {
                        continue;
                    }
                    double val;
                    if (double.TryParse(objV.ToString(), out val))
                    {
                        num++;
                        sum += val;
                    }
                }

                switch (type)
                {
                    case SummaryType.Num:
                        return num;
                    case SummaryType.Sum:
                        return Math.Round(sum, 2);
                    case SummaryType.Avg:
                        return Math.Round(sum / num, 2);
                    default:
                        break;
                }
                return double.NaN;
            }
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.LongitudeStart = 0;
            bgResult.LatitudeStart = 0;
            bgResult.LongitudeEnd = 0;
            bgResult.LatitudeEnd = 0;
            bgResult.ISTime = 0;
            bgResult.IETime = 0;
            if (testPoints.Count > 0)
            {
                bgResult.FileID = testPoints[0].FileID;
                bgResult.FileName = testPoints[0].FileName;
                bgResult.LongitudeStart = testPoints[0].Longitude;
                bgResult.LatitudeStart = testPoints[0].Latitude;
                bgResult.LongitudeEnd = testPoints[testPoints.Count - 1].Longitude;
                bgResult.LatitudeEnd = testPoints[testPoints.Count - 1].Latitude;
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(testPoints[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(testPoints[testPoints.Count - 1].DateTime) / 1000);
            }
            bgResult.LongitudeMid = MidLng;
            bgResult.LatitudeMid = MidLat;
            bgResult.DistanceLast = Distance;
            bgResult.SampleCount = TestPointCount;
            bgResult.RxLevMean = AvgRSRP;
            bgResult.RxLevMin = MinRSRP;
            bgResult.RxLevMax = MaxRSRP;
            bgResult.RxQualMean = AvgSINR;
            bgResult.RxQualMin = MinSINR;
            bgResult.RxQualMax = MaxSINR;
            bgResult.RoadDesc = RoadName;
            bgResult.GridDesc = GridName;
            bgResult.CellIDDesc = CellName;

            bgResult.AddImageValue((float)WeakPercent);
            bgResult.AddImageValue((float)AvgSpeed);
            bgResult.AddImageValue((float)DlCode0Qam64Rate);
            bgResult.AddImageValue((float)Rank_Indicator);
            bgResult.AddImageValue((float)PDSCH_BLER);
            bgResult.AddImageValue((float)Transmission_Mode);
            bgResult.AddImageValue((float)PDSCH_PRb_Num_s);
            bgResult.AddImageValue((float)DlCode1Qam64Rate);

            return bgResult;
        }
    }
}
