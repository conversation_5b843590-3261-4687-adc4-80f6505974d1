﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadData
{
    public partial class XtraFormStatusData : DevExpress.XtraEditors.XtraForm
    {
        private string typea;
        public XtraFormStatusData(string type)
        {
            InitializeComponent();
            typea = type;
            if (type == "GSM")
            {
                groupBoxTD.Visible = false;
            }
            else
            {
                groupBoxGSM.Visible = false;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (typea == "GSM")
            {
                if (!checkBox1.Checked && !checkBox2.Checked && !checkBox3.Checked 
                    && !checkBox4.Checked && !checkBox5.Checked && !checkBox10.Checked)
                {
                    this.DialogResult = DialogResult.Cancel;
                }
                else
                {
                    this.DialogResult = DialogResult.OK;
                }
            }
            else
            {
                if (!checkBox6.Checked && !checkBox7.Checked && !checkBox8.Checked 
                    && !checkBox9.Checked && !checkBox11.Checked)
                {
                    this.DialogResult = DialogResult.Cancel;
                }
                else
                {
                    this.DialogResult = DialogResult.OK;
                }
            }
            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        public void getStatusSelect(out List<bool> cb, out int dis, out int time)
        {
            if (typea == "GSM")
            {
                cb = new List<bool>();
                cb.Add(checkBox1.Checked);
                cb.Add(checkBox2.Checked);
                cb.Add(checkBox3.Checked);
                cb.Add(checkBox4.Checked);
                cb.Add(checkBox5.Checked);
                cb.Add(checkBox10.Checked);
            }
            else
            {
                cb = new List<bool>();
                cb.Add(checkBox6.Checked);
                cb.Add(checkBox7.Checked);
                cb.Add(checkBox8.Checked);
                cb.Add(checkBox9.Checked);
                cb.Add(checkBox11.Checked);
            }

            if (checkBox12.Checked)
            {
                dis = Convert.ToInt32(numericUpDown1.Value);
                time = Convert.ToInt32(numericUpDown2.Value);
            }
            else
            {
                dis = -1;
                time = -1;
            }
        }
    }
}