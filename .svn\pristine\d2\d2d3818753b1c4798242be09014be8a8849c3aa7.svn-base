﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class NewMarkFileForm : BaseDialog
    {
        public bool isSaveToXML { get; set; } = false;
        public NewMarkFileForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            int markIndex = 1;
            string str = string.Format("标记{0}", markIndex);
            if (mainModel.MarkedFiles != null)
            {
                while (true)
                {
                    str = string.Format("标记{0}", markIndex++);
                    MarkedFileGroup grp = getMarkedFileGroup(str);
                    if (grp == null)
                    {
                        break;
                    }
                }
            }
            this.textBox1.Text = str;
        }
        private MarkedFileGroup getMarkedFileGroup(string strName)
        {
            foreach (MarkedFileGroup item in mainModel.MarkedFiles)
            {
                if (item.Name.Equals(strName))
                {
                    return item;
                }
            }
            return null;
        }
        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            if (textBox1.Text.Length>0)
            {
                buttonOk.Enabled = true;
            }
            else
            {
                buttonCancel.Enabled = false;
            }
        }

        private void buttonOk_Click(object sender, EventArgs e)
        {
            string markName = textBox1.Text.Trim();
            if (mainModel.MarkedFiles!=null)
            {
                MarkedFileGroup grp = mainModel.MarkedFiles.Find(
                    delegate(MarkedFileGroup x) { return x.Name.Equals(markName); });
                if (grp != null
                    && MessageBox.Show("已存在相同的标记，是否覆盖原标记？", "注意", MessageBoxButtons.YesNo) != DialogResult.Yes)
                {
                    return;
                }
            }
            if (checkBox1.Checked)
            {
                isSaveToXML = true;
            }
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public string MarkName
        {
            get { return textBox1.Text.Trim(); }
        }

        public Color Color
        {
            get { return color.Color; }
        }

    }
}