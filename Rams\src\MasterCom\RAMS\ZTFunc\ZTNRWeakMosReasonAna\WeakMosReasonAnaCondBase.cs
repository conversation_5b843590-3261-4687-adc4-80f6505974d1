﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakMosReasonAnaCondBase
    {
        public virtual void Init()
        {
            ReasonList = new List<string>();
            ReasonList.Add("回落(CSFB)");
            ReasonList.Add("Esrvcc");
            ReasonList.Add("RRC重建");
            ReasonList.Add("频繁切换");
            ReasonList.Add("弱覆盖");
            ReasonList.Add("质差");
            ReasonList.Add("丢包");
            ReasonList.Add("其他");

            MosWeakGate = 2;
            HandoverCountGate = 4;
            WeakRsrpGate = -110;
            WeakRsrpTpCount = 3;
            WeakSinrGate = 0;
            WeakSinrTpCount = 3;
            RtpLostNumGate = 0;
        }

        public List<string> ReasonList { get; set; }
        public float MosWeakGate { get; set; }
        public int HandoverCountGate { get; set; }
        public float WeakRsrpGate { get; set; }
        public int WeakRsrpTpCount { get; set; }
        public float WeakSinrGate { get; set; }
        public int WeakSinrTpCount { get; set; }
        public int RtpLostNumGate { get; set; }
    }
}
