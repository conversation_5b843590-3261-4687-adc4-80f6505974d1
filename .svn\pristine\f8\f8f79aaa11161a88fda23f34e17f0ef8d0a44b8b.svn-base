﻿namespace MasterCom.RAMS.Func
{
    partial class CustomExpressionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.comboBoxSystem = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxInfomation = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxArgument = new DevExpress.XtraEditors.ComboBoxEdit();
            this.comboBoxMS = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbxInfomationControl = new DevExpress.XtraEditors.ComboBoxEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControlCustomObject = new DevExpress.XtraEditors.GroupControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.chkMS = new DevExpress.XtraEditors.CheckEdit();
            this.chkArgument = new DevExpress.XtraEditors.CheckEdit();
            this.groupControlValue = new DevExpress.XtraEditors.GroupControl();
            this.txtInformationThreshold = new DevExpress.XtraEditors.TextEdit();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.radioGroupLogic = new DevExpress.XtraEditors.RadioGroup();
            this.listBoxControlResult = new DevExpress.XtraEditors.ListBoxControl();
            this.btnClear = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.btnAddGroup = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnDeleteGroup = new DevExpress.XtraEditors.SimpleButton();
            this.listBoxControlOld = new DevExpress.XtraEditors.ListBoxControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.contextMenuStripResult = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miDelete = new System.Windows.Forms.ToolStripMenuItem();
            this.btnDeleteRecord = new DevExpress.XtraEditors.SimpleButton();
            this.toolTip = new System.Windows.Forms.ToolTip(this.components);
            this.btnAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnApply = new DevExpress.XtraEditors.SimpleButton();
            this.btnReset = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxSystem.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxInfomation.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxArgument.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxMS.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxInfomationControl.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlCustomObject)).BeginInit();
            this.groupControlCustomObject.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkMS.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkArgument.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlValue)).BeginInit();
            this.groupControlValue.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtInformationThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupLogic.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlResult)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlOld)).BeginInit();
            this.contextMenuStripResult.SuspendLayout();
            this.SuspendLayout();
            // 
            // comboBoxSystem
            // 
            this.comboBoxSystem.Location = new System.Drawing.Point(91, 40);
            this.comboBoxSystem.Name = "comboBoxSystem";
            this.comboBoxSystem.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxSystem.Size = new System.Drawing.Size(160, 21);
            this.comboBoxSystem.TabIndex = 0;
            this.comboBoxSystem.SelectedIndexChanged += new System.EventHandler(this.comboBoxSystem_SelectedIndexChanged);
            // 
            // comboBoxInfomation
            // 
            this.comboBoxInfomation.Location = new System.Drawing.Point(91, 72);
            this.comboBoxInfomation.Name = "comboBoxInfomation";
            this.comboBoxInfomation.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxInfomation.Size = new System.Drawing.Size(160, 21);
            this.comboBoxInfomation.TabIndex = 1;
            this.comboBoxInfomation.SelectedIndexChanged += new System.EventHandler(this.comboBoxInfomation_SelectedIndexChanged);
            // 
            // comboBoxArgument
            // 
            this.comboBoxArgument.Location = new System.Drawing.Point(91, 105);
            this.comboBoxArgument.Name = "comboBoxArgument";
            this.comboBoxArgument.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxArgument.Size = new System.Drawing.Size(160, 21);
            this.comboBoxArgument.TabIndex = 2;
            this.comboBoxArgument.EditValueChanged += new System.EventHandler(this.comboBoxArgument_EditValueChanged);
            // 
            // comboBoxMS
            // 
            this.comboBoxMS.Enabled = false;
            this.comboBoxMS.Location = new System.Drawing.Point(91, 137);
            this.comboBoxMS.Name = "comboBoxMS";
            this.comboBoxMS.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxMS.Size = new System.Drawing.Size(160, 21);
            this.comboBoxMS.TabIndex = 3;
            this.comboBoxMS.EditValueChanged += new System.EventHandler(this.comboBoxMS_EditValueChanged);
            // 
            // cbxInfomationControl
            // 
            this.cbxInfomationControl.EditValue = "=";
            this.cbxInfomationControl.Location = new System.Drawing.Point(4, 72);
            this.cbxInfomationControl.Name = "cbxInfomationControl";
            this.cbxInfomationControl.Properties.Appearance.Options.UseTextOptions = true;
            this.cbxInfomationControl.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.cbxInfomationControl.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cbxInfomationControl.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxInfomationControl.Size = new System.Drawing.Size(50, 19);
            this.cbxInfomationControl.TabIndex = 11;
            // 
            // groupControl1
            // 
            this.groupControl1.Appearance.BackColor = System.Drawing.Color.White;
            this.groupControl1.Appearance.Options.UseBackColor = true;
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl1.Controls.Add(this.cbxInfomationControl);
            this.groupControl1.Location = new System.Drawing.Point(269, 118);
            this.groupControl1.LookAndFeel.SkinName = "Office 2010 Silver";
            this.groupControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(58, 184);
            this.groupControl1.TabIndex = 13;
            this.groupControl1.Text = "表达式";
            // 
            // groupControlCustomObject
            // 
            this.groupControlCustomObject.Appearance.BackColor = System.Drawing.Color.White;
            this.groupControlCustomObject.Appearance.Options.UseBackColor = true;
            this.groupControlCustomObject.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControlCustomObject.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControlCustomObject.Controls.Add(this.labelControl2);
            this.groupControlCustomObject.Controls.Add(this.labelControl1);
            this.groupControlCustomObject.Controls.Add(this.chkMS);
            this.groupControlCustomObject.Controls.Add(this.chkArgument);
            this.groupControlCustomObject.Controls.Add(this.comboBoxSystem);
            this.groupControlCustomObject.Controls.Add(this.comboBoxMS);
            this.groupControlCustomObject.Controls.Add(this.comboBoxInfomation);
            this.groupControlCustomObject.Controls.Add(this.comboBoxArgument);
            this.groupControlCustomObject.Location = new System.Drawing.Point(7, 118);
            this.groupControlCustomObject.LookAndFeel.SkinName = "Office 2010 Silver";
            this.groupControlCustomObject.LookAndFeel.UseDefaultLookAndFeel = false;
            this.groupControlCustomObject.Name = "groupControlCustomObject";
            this.groupControlCustomObject.Size = new System.Drawing.Size(262, 184);
            this.groupControlCustomObject.TabIndex = 14;
            this.groupControlCustomObject.Text = "参数";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(33, 74);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(48, 14);
            this.labelControl2.TabIndex = 13;
            this.labelControl2.Text = "参数名称";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(33, 41);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(48, 14);
            this.labelControl1.TabIndex = 12;
            this.labelControl1.Text = "参数类别";
            // 
            // chkMS
            // 
            this.chkMS.Enabled = false;
            this.chkMS.Location = new System.Drawing.Point(3, 137);
            this.chkMS.Name = "chkMS";
            this.chkMS.Properties.Caption = "设备端口号";
            this.chkMS.Size = new System.Drawing.Size(80, 19);
            this.chkMS.TabIndex = 11;
            this.chkMS.CheckedChanged += new System.EventHandler(this.chkMS_CheckedChanged);
            // 
            // chkArgument
            // 
            this.chkArgument.Location = new System.Drawing.Point(15, 105);
            this.chkArgument.Name = "chkArgument";
            this.chkArgument.Properties.Caption = "参数序号";
            this.chkArgument.Size = new System.Drawing.Size(71, 19);
            this.chkArgument.TabIndex = 10;
            this.chkArgument.CheckedChanged += new System.EventHandler(this.chkArgument_CheckedChanged);
            // 
            // groupControlValue
            // 
            this.groupControlValue.Appearance.BackColor = System.Drawing.Color.White;
            this.groupControlValue.Appearance.Options.UseBackColor = true;
            this.groupControlValue.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControlValue.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControlValue.Controls.Add(this.txtInformationThreshold);
            this.groupControlValue.Location = new System.Drawing.Point(327, 118);
            this.groupControlValue.LookAndFeel.SkinName = "Office 2010 Silver";
            this.groupControlValue.LookAndFeel.UseDefaultLookAndFeel = false;
            this.groupControlValue.Name = "groupControlValue";
            this.groupControlValue.Size = new System.Drawing.Size(61, 184);
            this.groupControlValue.TabIndex = 15;
            this.groupControlValue.Text = "门限";
            // 
            // txtInformationThreshold
            // 
            this.txtInformationThreshold.Location = new System.Drawing.Point(5, 72);
            this.txtInformationThreshold.Name = "txtInformationThreshold";
            this.txtInformationThreshold.Size = new System.Drawing.Size(50, 21);
            this.txtInformationThreshold.TabIndex = 0;
            // 
            // groupControl2
            // 
            this.groupControl2.Appearance.BackColor = System.Drawing.Color.White;
            this.groupControl2.Appearance.Options.UseBackColor = true;
            this.groupControl2.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl2.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl2.Controls.Add(this.radioGroupLogic);
            this.groupControl2.Controls.Add(this.listBoxControlResult);
            this.groupControl2.Location = new System.Drawing.Point(469, 8);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(426, 284);
            this.groupControl2.TabIndex = 16;
            this.groupControl2.Text = "过滤公式合集";
            // 
            // radioGroupLogic
            // 
            this.radioGroupLogic.Location = new System.Drawing.Point(2, 253);
            this.radioGroupLogic.Name = "radioGroupLogic";
            this.radioGroupLogic.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "与"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "或")});
            this.radioGroupLogic.Size = new System.Drawing.Size(107, 29);
            this.radioGroupLogic.TabIndex = 23;
            this.radioGroupLogic.SelectedIndexChanged += new System.EventHandler(this.radioGroupLogic_SelectedIndexChanged);
            // 
            // listBoxControlResult
            // 
            this.listBoxControlResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxControlResult.HorizontalScrollbar = true;
            this.listBoxControlResult.Location = new System.Drawing.Point(2, 23);
            this.listBoxControlResult.Name = "listBoxControlResult";
            this.listBoxControlResult.Size = new System.Drawing.Size(422, 259);
            this.listBoxControlResult.TabIndex = 0;
            this.listBoxControlResult.MouseDown += new System.Windows.Forms.MouseEventHandler(this.listBoxControlResult_MouseDown);
            this.listBoxControlResult.SelectedIndexChanged += new System.EventHandler(this.listBoxControlResult_SelectedIndexChanged);
            // 
            // btnClear
            // 
            this.btnClear.Location = new System.Drawing.Point(392, 209);
            this.btnClear.LookAndFeel.SkinName = "Office 2010 Silver";
            this.btnClear.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(75, 23);
            this.btnClear.TabIndex = 17;
            this.btnClear.Text = "清空";
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // groupControl3
            // 
            this.groupControl3.Appearance.BackColor = System.Drawing.SystemColors.Window;
            this.groupControl3.Appearance.Options.UseBackColor = true;
            this.groupControl3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.groupControl3.Controls.Add(this.btnAddGroup);
            this.groupControl3.Controls.Add(this.btnSave);
            this.groupControl3.Controls.Add(this.btnDeleteGroup);
            this.groupControl3.Controls.Add(this.listBoxControlOld);
            this.groupControl3.Location = new System.Drawing.Point(7, 8);
            this.groupControl3.LookAndFeel.SkinName = "Office 2010 Silver";
            this.groupControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(460, 108);
            this.groupControl3.TabIndex = 18;
            this.groupControl3.Text = "预存公式";
            // 
            // btnAddGroup
            // 
            this.btnAddGroup.Location = new System.Drawing.Point(379, 29);
            this.btnAddGroup.Name = "btnAddGroup";
            this.btnAddGroup.Size = new System.Drawing.Size(75, 23);
            this.btnAddGroup.TabIndex = 3;
            this.btnAddGroup.Text = "<新增";
            this.btnAddGroup.Click += new System.EventHandler(this.btnAddGroup_Click);
            // 
            // btnSave
            // 
            this.btnSave.Location = new System.Drawing.Point(379, 52);
            this.btnSave.LookAndFeel.SkinName = "Office 2010 Silver";
            this.btnSave.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 2;
            this.btnSave.Text = "<保存";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnDeleteGroup
            // 
            this.btnDeleteGroup.Location = new System.Drawing.Point(379, 75);
            this.btnDeleteGroup.LookAndFeel.SkinName = "Office 2010 Silver";
            this.btnDeleteGroup.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnDeleteGroup.Name = "btnDeleteGroup";
            this.btnDeleteGroup.Size = new System.Drawing.Size(75, 23);
            this.btnDeleteGroup.TabIndex = 1;
            this.btnDeleteGroup.Text = "   删除";
            this.btnDeleteGroup.Click += new System.EventHandler(this.btnDeleteGroup_Click);
            // 
            // listBoxControlOld
            // 
            this.listBoxControlOld.HorizontalScrollbar = true;
            this.listBoxControlOld.Location = new System.Drawing.Point(6, 27);
            this.listBoxControlOld.Name = "listBoxControlOld";
            this.listBoxControlOld.Size = new System.Drawing.Size(367, 73);
            this.listBoxControlOld.TabIndex = 0;
            this.listBoxControlOld.SelectedIndexChanged += new System.EventHandler(this.listBoxControlOld_SelectedIndexChanged);
            // 
            // btnOK
            // 
            this.btnOK.Enabled = false;
            this.btnOK.Location = new System.Drawing.Point(658, 294);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 19;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(739, 294);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 20;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // contextMenuStripResult
            // 
            this.contextMenuStripResult.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miDelete});
            this.contextMenuStripResult.Name = "contextMenuStripResult";
            this.contextMenuStripResult.Size = new System.Drawing.Size(137, 26);
            // 
            // miDelete
            // 
            this.miDelete.Name = "miDelete";
            this.miDelete.Size = new System.Drawing.Size(136, 22);
            this.miDelete.Text = "删除选择项";
            this.miDelete.Click += new System.EventHandler(this.miDelete_Click);
            // 
            // btnDeleteRecord
            // 
            this.btnDeleteRecord.Enabled = false;
            this.btnDeleteRecord.Location = new System.Drawing.Point(895, 31);
            this.btnDeleteRecord.Name = "btnDeleteRecord";
            this.btnDeleteRecord.Size = new System.Drawing.Size(20, 20);
            this.btnDeleteRecord.TabIndex = 21;
            this.btnDeleteRecord.Text = "X";
            this.btnDeleteRecord.Click += new System.EventHandler(this.btnDeleteRecord_Click);
            // 
            // btnAdd
            // 
            this.btnAdd.Location = new System.Drawing.Point(392, 171);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(75, 23);
            this.btnAdd.TabIndex = 24;
            this.btnAdd.Text = "添加";
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // btnApply
            // 
            this.btnApply.Enabled = false;
            this.btnApply.Location = new System.Drawing.Point(820, 294);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(75, 23);
            this.btnApply.TabIndex = 25;
            this.btnApply.Text = "应用";
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // btnReset
            // 
            this.btnReset.Location = new System.Drawing.Point(471, 294);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(75, 23);
            this.btnReset.TabIndex = 26;
            this.btnReset.Text = "取消过滤";
            this.btnReset.Click += new System.EventHandler(this.btnReset_Click);
            // 
            // CustomExpressionForm
            // 
            this.Appearance.BackColor = System.Drawing.Color.White;
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(916, 323);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnApply);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.btnDeleteRecord);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControlCustomObject);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.groupControlValue);
            this.Controls.Add(this.groupControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.LookAndFeel.SkinName = "Office 2010 Silver";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.MaximizeBox = false;
            this.Name = "CustomExpressionForm";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "采样点过滤";
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxSystem.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxInfomation.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxArgument.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxMS.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxInfomationControl.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControlCustomObject)).EndInit();
            this.groupControlCustomObject.ResumeLayout(false);
            this.groupControlCustomObject.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkMS.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkArgument.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlValue)).EndInit();
            this.groupControlValue.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtInformationThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupLogic.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlResult)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlOld)).EndInit();
            this.contextMenuStripResult.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.ComboBoxEdit comboBoxSystem;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxInfomation;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxArgument;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxMS;
        private DevExpress.XtraEditors.ComboBoxEdit cbxInfomationControl;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControlCustomObject;
        private DevExpress.XtraEditors.GroupControl groupControlValue;
        private DevExpress.XtraEditors.TextEdit txtInformationThreshold;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnClear;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.SimpleButton btnDeleteGroup;
        private DevExpress.XtraEditors.ListBoxControl listBoxControlOld;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.ListBoxControl listBoxControlResult;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripResult;
        private System.Windows.Forms.ToolStripMenuItem miDelete;
        private DevExpress.XtraEditors.CheckEdit chkMS;
        private DevExpress.XtraEditors.CheckEdit chkArgument;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnDeleteRecord;
        private System.Windows.Forms.ToolTip toolTip;
        private DevExpress.XtraEditors.RadioGroup radioGroupLogic;
        private DevExpress.XtraEditors.SimpleButton btnAdd;
        public DevExpress.XtraEditors.SimpleButton btnApply;
        public DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnReset;
        private DevExpress.XtraEditors.SimpleButton btnAddGroup;
    }
}