﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class EditSampleParamGroupDlg : BaseDialog
    {
        private string curSelParam = "";
        private int curSelArg = -1;

        public EditSampleParamGroupDlg()
        {
            InitializeComponent();
            loadSystem();
        }

        public object CurSelGroupItem
        {
            get { return cbxSampleGroups.SelectedItem; }
        }

        private void saveCondSettings()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("DIYSampleGroups");
                List<object> styles = new List<object>();
                foreach (DIYSampleGroup rpt in cbxSampleGroups.Items)
                {
                    styles.Add(rpt.Param);
                }
                configFile.AddItem(cfg, "groups", styles);
                configFile.Save(string.Format(Application.StartupPath + "/config/diysamplegroup.xml"));
            }
            catch (Exception e)
            {
                MessageBox.Show("保存失败!" + e.Message);
            }
        }

        internal void FillGroups(List<DIYSampleGroup> groups, int curSel)
        {
            cbxSampleGroups.Items.Clear();
            foreach (DIYSampleGroup group in groups)
            {
                cbxSampleGroups.Items.Add(group);
            }
            if(curSel!=-1)
            {
                cbxSampleGroups.SelectedIndex = curSel;
            }
        }

        internal List<DIYSampleGroup> GetDIYSampleGroupResult()
        {
            List<DIYSampleGroup> newGroups = new List<DIYSampleGroup>();
            foreach (DIYSampleGroup group in cbxSampleGroups.Items)
            {
                newGroups.Add(group);
            }
            return newGroups;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            saveCondSettings();
        }

        private void cbxSampleGroups_SelectedIndexChanged(object sender, EventArgs e)
        {
            listBoxColumn.Items.Clear();
            DIYSampleGroup group = cbxSampleGroups.SelectedItem as DIYSampleGroup;
            btnAddColumn.Enabled = group != null;
            btnDelColumn.Enabled = group != null;
            if (group != null)
            {
                txtSerial.Text = group.ThemeName;
                foreach (DIYSampleParamDef col in group.ColumnsDefSet)
                {
                    listBoxColumn.Items.Add(col);
                }
            }
        }

        private void BtnSerial_Click(object sender, EventArgs e)
        {
            DIYSampleGroup group = cbxSampleGroups.SelectedItem as DIYSampleGroup;
            if (group == null)
            {
                return;
            }
            PopSelectSerialsForm form = new PopSelectSerialsForm();
            if (form.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtSerial.Text = "";
            if (form.SelectedSerials.Count > 0)
            {
                txtSerial.Text = DTLayerSerialManager.Instance.GetNameBySerial(form.SelectedSerials[0]);
            }
            group.ThemeName = txtSerial.Text;
        }

        private void cbxParaSystem_SelectedIndexChanged(object sender, EventArgs e)
        {
            loadInfomation(cbxParaSystem, cbxParaInfomation, curSelParam);
            checkColorState();
        }
        private void checkColorState()
        {
            cbxParaInfomation.Enabled = cbxParaInfomation.Items.Count > 0;
            cbxParaArg.Enabled = cbxParaArg.Items.Count > 0;
        }
        private void cbxParaInfomation_SelectedIndexChanged(object sender, EventArgs e)
        {
            loadArgument(cbxParaSystem, cbxParaInfomation, cbxParaArg, curSelArg);
            checkColorState();
        }
        private void loadSystem()
        {
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                cbxParaSystem.Items.Add(system.Name);
            }
        }
        private void loadArgument(ComboBox comboBoxSystem, ComboBox comboBoxInfomation, ComboBox comboBoxArgument, int arrayIndex)
        {
            comboBoxArgument.Items.Clear();
            DTDisplayParameterInfo displayInfo = DTDisplayParameterManager.GetInstance()[(string)comboBoxSystem.SelectedItem][(string)comboBoxInfomation.SelectedItem];
            if (displayInfo.ArrayBounds > 1)
            {
                for (int i = 0; i < displayInfo.ArrayBounds; i++)
                {
                    comboBoxArgument.Items.Add(i.ToString());
                }
            }
            if (comboBoxArgument.Items.Count > 0)
            {
                if (comboBoxArgument.Enabled)
                {
                    comboBoxArgument.SelectedIndex = arrayIndex;
                }
                else
                {
                    comboBoxArgument.SelectedIndex = 0;
                }
            }
        }
        private void loadInfomation(ComboBox comboBoxSystem, ComboBox comboBoxInfomation, string paramName)
        {
            try
            {
                comboBoxInfomation.Items.Clear();
                string systemName = (string)comboBoxSystem.SelectedItem;
                if (systemName == null)
                {
                    return;
                }
                int idx = -1;
                int selIdx = -1;
                foreach (DTDisplayParameterInfo displayInfo in DTDisplayParameterManager.GetInstance()[systemName].DisplayParamInfos)
                {
                    if ((displayInfo.Type & (int)DTDisplayParameterInfoType.Range) != 0)
                    {
                        idx++;
                        if (displayInfo.ParamInfo.Name == paramName)
                        {
                            selIdx = idx++;
                        }
                        comboBoxInfomation.Items.Add(displayInfo.Name);
                    }
                }
                comboBoxInfomation.SelectedIndex = selIdx;
            }
            catch
            {
                //continue
            }
        }

        private void listBoxColumn_SelectedIndexChanged(object sender, EventArgs e)
        {
            curSelParam = "";
            curSelArg = -1;
            if (listBoxColumn.SelectedItem is DIYSampleParamDef)
            {
                DIYSampleParamDef cs = listBoxColumn.SelectedItem as DIYSampleParamDef;
                if (cs.parameter != null)
                {
                    curSelParam = cs.parameter.Info.Name;
                    curSelArg = cs.parameter.ArrayIndex;
                    if (curSelParam.IndexOf("TD_") == 0)
                    {
                        cbxParaSystem.SelectedItem = "TD-SCDMA";
                    }
                    else if (curSelParam.IndexOf("W_") == 0)
                    {
                        cbxParaSystem.SelectedItem = "WCDMA";
                    }
                    else if (curSelParam.IndexOf("CD_") == 0)
                    {
                        cbxParaSystem.SelectedItem = "CDMA";
                    }
                    else if (curSelParam.IndexOf("SCAN_") == 0)
                    {
                        cbxParaSystem.SelectedItem = "SCAN";
                    }
                    else if (curSelParam.IndexOf("UL_") == 0)
                    {
                        cbxParaSystem.SelectedItem = "GSMUplink";
                    }
                    else if (curSelParam != "")
                    {
                        cbxParaSystem.SelectedItem = "GSM";
                    }
                    //
                    loadInfomation(cbxParaSystem, cbxParaInfomation, curSelParam);
                }
            }
        }

        private void btnAddColumn_Click(object sender, EventArgs e)
        {
            if(cbxSampleGroups.SelectedIndex==-1)
            {
                MessageBox.Show(this, "请先建立指标组！", "添加");
                return;
            }
            string sysname = (string)cbxParaSystem.SelectedItem;
            string paraname = (string)cbxParaInfomation.SelectedItem;
            if (sysname == null || paraname == null)
            {
                MessageBox.Show(this, "请先选择参数指标！");
                return;
            }
            int argAt = -1;
            //if (cbxParaArg.Enabled)
            //{
            //    argAt = cbxParaArg.SelectedIndex;
            //}
            argAt = 0;//强制显示第一个组
            DTDisplayParameter displayParam = DTDisplayParameterManager.GetInstance()[sysname, paraname, argAt];
            DTParameter dtParameter = displayParam.Info.ParamInfo[displayParam.ArrayIndex];
            string colShowName = dtParameter.Info.Name;
            List<ColumnDefItem> colDefItemList = InterfaceManager.GetInstance().GetColumnDefByShowName(colShowName);
            if (colDefItemList == null || colDefItemList.Count==0)
            {
                MessageBox.Show(this, "所选指标" + colShowName + "没有在配置中定制！", "错误");
                return;
            }
            DIYSampleParamDef diyNewDef = new DIYSampleParamDef();
            diyNewDef.parameter = dtParameter;
            foreach(DIYSampleParamDef def in listBoxColumn.Items)
            {
                if(def.parameter==dtParameter ||def.parameter.Info.Name==dtParameter.Info.Name)
                {
                    return;
                }
            }
            listBoxColumn.Items.Add(diyNewDef);
            DIYSampleGroup curGroup = cbxSampleGroups.SelectedItem as DIYSampleGroup;
            curGroup.ColumnsDefSet.Add(diyNewDef);

        }

        private void btnDelColumn_Click(object sender, EventArgs e)
        {
            if(listBoxColumn.SelectedIndex!=-1)
            {
                int index = listBoxColumn.SelectedIndex;
                listBoxColumn.Items.RemoveAt(index);
                if(cbxSampleGroups.SelectedItem!=null)
                {
                    DIYSampleGroup curGroup = cbxSampleGroups.SelectedItem as DIYSampleGroup;
                    curGroup.ColumnsDefSet.RemoveAt(index);
                }
                
            }
        }

        private void btnClearAll_Click(object sender, EventArgs e)
        {
            if(listBoxColumn.Items.Count>0)
            {
                DialogResult res = MessageBox.Show(this, "清空所有所选指标？", "清空", MessageBoxButtons.OKCancel);
                if (res == DialogResult.OK)
                {
                    listBoxColumn.Items.Clear();

                    if (cbxSampleGroups.SelectedItem != null)
                    {
                        DIYSampleGroup curGroup = cbxSampleGroups.SelectedItem as DIYSampleGroup;
                        curGroup.ColumnsDefSet.Clear();
                    }
                }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (cbxSampleGroups.SelectedItem != null)
            {
                cbxSampleGroups.Items.Remove(cbxSampleGroups.SelectedItem);
            }
        }

        private void btnNewGroup_Click(object sender, EventArgs e)
        {
            TextInputBox input = new TextInputBox("新增参数组", "输入参数组名称", "新参数组");
            if (DialogResult.OK == input.ShowDialog(this))
            {
                string textInput = input.TextInput;
                DIYSampleGroup asu = new DIYSampleGroup();
                asu.Name = textInput;
                cbxSampleGroups.Items.Add(asu);
                cbxSampleGroups.SelectedItem = asu;
            }
        }

        private void btnApplyThemeName_Click(object sender, EventArgs e)
        {
            if (cbxSampleGroups.SelectedIndex == -1)
            {
                MessageBox.Show(this, "请先建立指标组！", "应用主题");
                return;
            }
            DIYSampleGroup curGroup = cbxSampleGroups.SelectedItem as DIYSampleGroup;
            if (!string.IsNullOrEmpty(txtSerial.Text))
            {
                curGroup.ThemeName = txtSerial.Text;
            }
        }
    }
}