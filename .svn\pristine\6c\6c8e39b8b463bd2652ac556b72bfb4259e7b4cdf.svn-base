﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLowSpeedReasonSettingDlg : Form
    {
        public ZTLowSpeedReasonSettingDlg()
        {
            InitializeComponent();
        }

        public void InitData(LowSpeedCondi lowSpeedCondition, List<ReasonDealBase> rdbList)
        {
            if (lowSpeedCondition != null)
            {
                spinEditPccpch_Weak.Value = lowSpeedCondition.PoorCover_PccpchRscp;
                spinEditPccpch_Coverlap.Value = lowSpeedCondition.CoverLap_PccpchRscp;
                spinEditRatio_Coverlap.Value = (decimal)lowSpeedCondition.CoverLap_Ratio;
                spinEditPccpch_Pullotion.Value = lowSpeedCondition.Pollute_PccpchRscp;
                spinEditNum_Pullotion.Value = lowSpeedCondition.Pollute_CellNum;
                spinEditCornerBefore.Value = lowSpeedCondition.Corner_BeforeSecond;
                spinEditCornerAfter.Value = lowSpeedCondition.Corner_AfterSecond;

                spinEditPccpch_CI.Value = lowSpeedCondition.PoorC2I_PccpchRscp;
                spinEditPccpch_C_I_CI.Value = lowSpeedCondition.PoorC2I_PccpchC2i;
                spinEditDpch_C_I_CI.Value = lowSpeedCondition.PoorC2I_DpchC2i;
                spinEditHsScch_C_I_CI.Value = lowSpeedCondition.PoorC2I_HsScchC2i;
                spinEditHsPdsch_C_I_CI.Value = lowSpeedCondition.PoorC2I_HsPdschC2i;

                spinEditPccpch_UpInterrupt.Value = lowSpeedCondition.UpInter_PccpchRscp;
                spinEditPccpch_C_I_UpInterrupt.Value = lowSpeedCondition.UpInter_PccpchC2i;
                spinEditTxPower_UpInterrupt.Value = lowSpeedCondition.UpInter_UETxPower;
                spinEditPccpch_Sub_Dpch.Value = lowSpeedCondition.PowerControl_Diff;
                spinEditCorrelation.Value = (decimal)lowSpeedCondition.PscInter_Ratio;

                spinEditHOLBefore.Value = lowSpeedCondition.HODelay_BeforeSecond;
                spinEditHOLAfter.Value = lowSpeedCondition.HODelay_AfterSecond;
                spinEditPccpch_sSubn_Unreasonable.Value = lowSpeedCondition.HOAbnormal_Diff;

                spinEditDpch_Antenna.Value = lowSpeedCondition.Antenna_DpchRscp;

                spinEditScheduledRate.Value = (decimal)lowSpeedCondition.HSScchScheduledRate;
            }
            checkedListBoxControlReason.Items.Clear();
            if (rdbList != null && rdbList.Count > 0)
            {
                foreach (ReasonDealBase rdb in rdbList)
                {
                    DevExpress.XtraEditors.Controls.CheckedListBoxItem item = new DevExpress.XtraEditors.Controls.CheckedListBoxItem(rdb);
                    checkedListBoxControlReason.Items.Add(item);
                    item.CheckState = rdb.IsCheck ? CheckState.Checked : CheckState.Unchecked;
                }
            }
            radioGroupCoCPI.Properties.Items.Clear();
            radioGroupCoCPI.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(1, "码相关"));
            radioGroupCoCPI.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(2, "组相关"));
            radioGroupCoCPI.SelectedIndex = 0;
        }

        public void getCondition(out LowSpeedCondi lowSpeedCond, out List<ReasonDealBase> rdbList)
        {
            lowSpeedCond = new LowSpeedCondi();
            rdbList = new List<ReasonDealBase>();

            lowSpeedCond.PoorCover_PccpchRscp = (int)spinEditPccpch_Weak.Value;
            lowSpeedCond.CoverLap_PccpchRscp = (int)spinEditPccpch_Coverlap.Value;
            lowSpeedCond.CoverLap_Ratio = (float)spinEditRatio_Coverlap.Value;
            lowSpeedCond.Pollute_PccpchRscp = (int)spinEditPccpch_Pullotion.Value;
            lowSpeedCond.Pollute_CellNum = (int)spinEditNum_Pullotion.Value;
            lowSpeedCond.Corner_BeforeSecond = (int)spinEditCornerBefore.Value;
            lowSpeedCond.Corner_AfterSecond = (int)spinEditCornerAfter.Value;

            lowSpeedCond.PoorC2I_PccpchRscp = (int)spinEditPccpch_CI.Value;
            lowSpeedCond.PoorC2I_PccpchC2i = (int)spinEditPccpch_C_I_CI.Value;
            lowSpeedCond.PoorC2I_DpchC2i = (int)spinEditDpch_C_I_CI.Value;
            lowSpeedCond.PoorC2I_HsScchC2i = (int)spinEditHsScch_C_I_CI.Value;
            lowSpeedCond.PoorC2I_HsPdschC2i = (int)spinEditHsPdsch_C_I_CI.Value;

            lowSpeedCond.UpInter_PccpchRscp = (int)spinEditPccpch_UpInterrupt.Value;
            lowSpeedCond.UpInter_PccpchC2i = (int)spinEditPccpch_C_I_UpInterrupt.Value;
            lowSpeedCond.UpInter_UETxPower = (int)spinEditTxPower_UpInterrupt.Value;
            lowSpeedCond.PowerControl_Diff = (int)spinEditPccpch_Sub_Dpch.Value;
            lowSpeedCond.PscInter_Ratio = (float)spinEditCorrelation.Value;

            lowSpeedCond.HODelay_BeforeSecond = (int)spinEditHOLBefore.Value;
            lowSpeedCond.HODelay_AfterSecond = (int)spinEditHOLAfter.Value;
            lowSpeedCond.HOAbnormal_Diff = (int)spinEditPccpch_sSubn_Unreasonable.Value;

            lowSpeedCond.Antenna_DpchRscp = (int)spinEditDpch_Antenna.Value;
            lowSpeedCond.InterfereType = (EInterfere)(radioGroupCoCPI.SelectedIndex + 1);

            lowSpeedCond.HSScchScheduledRate = (float)spinEditScheduledRate.Value;

            foreach (DevExpress.XtraEditors.Controls.CheckedListBoxItem item in checkedListBoxControlReason.Items)
            {
                ReasonDealBase rdb = item.Value as ReasonDealBase;
                rdb.IsCheck = item.CheckState == CheckState.Checked;
                rdbList.Add(rdb);
            }
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleButtonUp_Click(object sender, EventArgs e)
        {
            if (checkedListBoxControlReason.Items.Count <= 1)
            {
                return;
            }
            int index = checkedListBoxControlReason.SelectedIndex;
            if (index == 0)
            {
                return;
            }
            object item = checkedListBoxControlReason.SelectedItem;
            checkedListBoxControlReason.Items.RemoveAt(index);
            checkedListBoxControlReason.Items.Insert(index - 1, item);
            checkedListBoxControlReason.SelectedIndex = index - 1;
        }

        private void simpleButtonDown_Click(object sender, EventArgs e)
        {
            if (checkedListBoxControlReason.Items.Count <= 1)
            {
                return;
            }
            int index = checkedListBoxControlReason.SelectedIndex;
            if (index == checkedListBoxControlReason.Items.Count - 1)
            {
                return;
            }
            object item = checkedListBoxControlReason.SelectedItem;
            checkedListBoxControlReason.Items.RemoveAt(index);
            checkedListBoxControlReason.Items.Insert(index + 1, item);
            checkedListBoxControlReason.SelectedIndex = index + 1;
        }

        private void checkedListBoxControlReason_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (checkedListBoxControlReason.SelectedItem == null)
            {
                return;
            }
            DevExpress.XtraEditors.Controls.ListBoxItem lbItem = (DevExpress.XtraEditors.Controls.ListBoxItem)checkedListBoxControlReason.SelectedItem;
            ReasonDealBase rdb = lbItem.Value as ReasonDealBase;

            tbxDescription.Text = rdb.GetDescription();
            if (rdb.ToString() == "同频同码干扰")
            {
                if (radioGroupCoCPI.SelectedIndex == 0)
                {
                    tbxDescription.Text += "【码相关】";
                }
                else
                {
                    tbxDescription.Text += "【组相关】";
                }
            }
            tbxSuggest.Text = rdb.GetSuggestion();
        }
    }

    public class LowSpeedCondi
    {
        public int PoorCover_PccpchRscp { get; set; } = -90;
        public int CoverLap_PccpchRscp { get; set; } = -85;
        public float CoverLap_Ratio { get; set; } = 1.6f;
        public int Pollute_CellNum { get; set; } = 4;
        public int Pollute_PccpchRscp { get; set; } = -85;
        public int BackCover_Angle { get; set; } = 90;
        public int BackCover_Distance { get; set; } = 300;
        public int Corner_BeforeSecond { get; set; } = 3;
        public int Corner_AfterSecond { get; set; } = 5;

        public int PoorC2I_PccpchRscp { get; set; } = -85;
        public int PoorC2I_PccpchC2i { get; set; } = -3;
        public int PoorC2I_DpchC2i { get; set; } = -3;
        public int PoorC2I_HsScchC2i { get; set; } = -3;
        public int PoorC2I_HsPdschC2i { get; set; } = -3;

        public int UpInter_PccpchRscp { get; set; } = -85;
        public int UpInter_PccpchC2i { get; set; } = -3;
        public int UpInter_UETxPower { get; set; } = 20;
        public int PowerControl_Diff { get; set; } = 25;
        public float PscInter_Ratio { get; set; } = 0.5f;

        public int HODelay_BeforeSecond { get; set; } = 5;
        public int HODelay_AfterSecond { get; set; } = 0;
        public int HOAbnormal_Diff { get; set; } = 3;

        public int Antenna_DpchRscp { get; set; } = 30;

        public float HSScchScheduledRate { get; set; } = 70;

        public EInterfere InterfereType { get; set; } = EInterfere.ByGroup;

        //限制HSPA下pccpch_rscp与pccpch_c2i的值
        public float HSPA_Pccpch_Rscp { get; set; } = -85;
        public int HSPA_Pccpch_C2I { get; set; } = -3;
    }

    public enum EInterfere
    {
        ByCoCPI = 1,
        ByGroup = 2,
    }
}
