<?xml version="1.0"?>
<Configs>
  <Config name="ColRenderCfg">
    <Item name="Options" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">工单总数</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">待派发工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">待接单工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">待回单工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">待验证工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">已归档工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">已解决工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item typeName="String" key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">未解决工单</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">10</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">10</Item>
            <Item typeName="Single" key="Max">20</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8586240</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">20</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">50</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-23296</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">50</Item>
            <Item typeName="Single" key="Max">9999</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item typeName="String" key="DescInfo" />
            <Item typeName="Int32" key="Value">-65536</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Caption">问题解决率</Item>
        <Item key="Expression" />
        <Item typeName="Single" key="ValueMin">0</Item>
        <Item typeName="Single" key="ValueMax">0</Item>
        <Item typeName="IList" key="ColorParam">
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">0</Item>
            <Item typeName="Single" key="Max">30</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-32640</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">30</Item>
            <Item typeName="Single" key="Max">60</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-20084</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">60</Item>
            <Item typeName="Single" key="Max">70</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-256</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">70</Item>
            <Item typeName="Single" key="Max">85</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">False</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-8323328</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="Single" key="Min">85</Item>
            <Item typeName="Single" key="Max">100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
            <Item key="DescInfo" />
            <Item typeName="Int32" key="Value">-10700708</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>