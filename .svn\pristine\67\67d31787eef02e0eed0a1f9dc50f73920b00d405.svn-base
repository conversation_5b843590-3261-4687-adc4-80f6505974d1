﻿using MasterCom.MTGis;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 小路段分析指标
    /// </summary>
    public class RoadPartAnaInfo
    {
        public RoadPartAnaInfo(RoadPartAreaBaseInfo roadPartBaseInfo)
        {
            this.RoadPartBaseInfo = roadPartBaseInfo;
            this.RoadPartPolygon = new MultiPolygon(roadPartBaseInfo.DbPointList);
        }

        public RoadPartAreaBaseInfo RoadPartBaseInfo { get; set; }
        public MultiPolygon RoadPartPolygon { get; set; }
        public int RoadPartAreaId { get { return RoadPartBaseInfo.RoadPartAreaId; } }
        public string RoadName { get { return RoadPartBaseInfo.RoadName; } }
        public string RoadLabel { get { return RoadPartBaseInfo.RoadLabel; } }
        public string IsAbnormalStr_2G { get { return IsWeakRxlev ? "是" : "否"; } }
        public string IsAbnormalStr_4G { get { return IsWeakRsrp ? "是" : "否"; } }
        public bool IsWeakRsrp { get; set; }
        public bool IsWeakRxlev { get; set; }
        public Color RoadPartCurColor { get; set; }
        public Color RoadAbnormalColor { get; set; }

        public RoadQualKpiDataHub KpiData { get; set; } = new RoadQualKpiDataHub();

        public int SampleNum_Total { get { return KpiData.SampleNum_Total; } }
        public double? RsrpAvg { get { return KpiData.RsrpInfo.KpiAvgValue; } }
        public double? SinrAvg { get { return KpiData.SinrInfo.KpiAvgValue; } }
        public double? LteCoverRate { get { return KpiData.LteCoverRateInfo.Rate; } }
        public double? RxlevAvg { get { return KpiData.RxlevInfo.KpiAvgValue; } }
        public int MainCellCount { get { return MainCellNameList.Count; } }
        public int MainCellCount_Exit { get { return MainCellNameList_Exit.Count; } }

        public List<string> MainCellNameList { get; set; } = new List<string>();//主覆盖小区数
        public List<string> MainCellNameList_Exit { get; set; } = new List<string>();//退服小区数

        private Dictionary<string, RoadPartCellKpiInfo> cellKpiDic { get; set; } = new Dictionary<string, RoadPartCellKpiInfo>();
        
        public List<RoadPartCellKpiInfo> RoadPartCellList { get; set; }

        private RoadPartCellKpiInfo roadPartCell_FirstNormal2G = null;
        private RoadPartCellKpiInfo roadPartCell_FirstNormal4G = null;
        public void AddRoadPartCellKpi(RoadPartCellKpiInfo roadPartCellInfo)
        {
            if (roadPartCellInfo != null && roadPartCellInfo.CellName != null)
            {
                RoadPartCellKpiInfo roadPartCellData;
                if (cellKpiDic.TryGetValue(roadPartCellInfo.CellName, out roadPartCellData))
                {
                    roadPartCellData.Merge(roadPartCellInfo);
                }
                else
                {
                    cellKpiDic.Add(roadPartCellInfo.CellName, roadPartCellInfo);
                }
            }
        }

        public void Calculate(RoadQualAnaCond roadAnaCond, Dictionary<string, CellExitInfo> cellExitInfoDic)
        {
            this.KpiData = new RoadQualKpiDataHub();
            this.IsWeakRxlev = this.IsWeakRsrp = false;
            roadPartCell_FirstNormal2G = null;
            roadPartCell_FirstNormal4G = null;
            MainCellNameList.Clear();
            MainCellNameList_Exit.Clear();

            RoadPartCellList = new List<RoadPartCellKpiInfo>(cellKpiDic.Values);
            RoadPartCellList.Sort();
            foreach (RoadPartCellKpiInfo roadPartCellData in RoadPartCellList)
            {
                CellExitInfo exitInfo;
                cellExitInfoDic.TryGetValue(roadPartCellData.CellToken, out exitInfo);//获取各小区的退服信息
                roadPartCellData.ExitInfo = exitInfo;
                roadPartCellData.StrDes = "";

                if (!MainCellNameList.Contains(roadPartCellData.CellName))
                {
                    MainCellNameList.Add(roadPartCellData.CellName);
                }

                if (roadPartCellData.IsExitCell)
                {
                    if (!MainCellNameList_Exit.Contains(roadPartCellData.CellName))
                    {
                        MainCellNameList_Exit.Add(roadPartCellData.CellName);
                    }
                }
                else
                {
                    if (roadAnaCond.IsCheck2G && roadPartCellData.NetType == ServiceName.GSM
                        && roadPartCell_FirstNormal2G == null)
                    {
                        //2G首强正常主小区指标信息
                        roadPartCellData.StrDes = "首强在网GSM小区";
                        roadPartCell_FirstNormal2G = roadPartCellData;
                        this.KpiData.Merge(roadPartCellData.KpiData);//栅格指标只取正常的（非退服）首强主小区指标均值
                        this.IsWeakRxlev = this.KpiData.RxlevInfo.KpiAvgValue < roadAnaCond.WeakRxlevGate;
                    }
                    else if (roadAnaCond.IsCheck4G && roadPartCellData.NetType == ServiceName.LTE
                        && roadPartCell_FirstNormal4G == null)
                    {
                        //4G首强正常主小区指标信息
                        roadPartCellData.StrDes = "首强在网LTE小区";
                        roadPartCell_FirstNormal4G = roadPartCellData;
                        this.KpiData.Merge(roadPartCellData.KpiData);//栅格指标只取正常的（非退服）首强主小区指标均值
                        this.IsWeakRsrp = this.KpiData.RsrpInfo.KpiAvgValue < roadAnaCond.WeakRsrpGate;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 路段分析指标
    /// </summary>
    public class RoadLabelQualAnaInfo
    {
        public RoadLabelQualAnaInfo(RoadLabelBaseInfo roadMapInfo)
        {
            RoadLabelBaseInfo = roadMapInfo;
            init(roadMapInfo);
            RoadPartStatSumInfo = new StreetStatByPartKpiInfo(RoadPartAnaInfoDic, roadMapInfo.Length_M);
        }
        
        public RoadLabelBaseInfo RoadLabelBaseInfo { get; set; }
        public MultiPolygon RoadLabelPolygon { get; set; }

        public string CountyName { get { return RoadLabelBaseInfo.CountyName; } }
        public string RoadName { get { return RoadLabelBaseInfo.RoadName; } }
        public string RoadLabel { get { return RoadLabelBaseInfo.RoadLabel; } }
        public float RoadLabelLength_M { get { return RoadLabelBaseInfo.Length_M; } }
        public double RoadLabelLength_KM { get { return Math.Round(RoadLabelBaseInfo.Length_KM, 2); } }

        /// <summary>
        /// 路段异常概率（模拟异常概率和测试异常概率两者最大值）
        /// </summary>
        public double? RoadAbnormalPer { get; set; }

        /// <summary>
        /// 影响因子（路段长度×异常概率 ）
        /// </summary>
        public double Affectoi_KM { get; set; }
        public string ProblemReason { get; set; }
        public bool IsProblemRoad { get; set; }
        public string IsProblemRoadStr { get { return IsProblemRoad ? "是" : "否"; } }
        public bool IsProblemRoad_2G { get; set; }
        public bool IsProblemRoad_4G { get; set; }
        public string NetTypeStr
        {
            get 
            {
                if (IsProblemRoad_2G && IsProblemRoad_4G)
                {
                    return "2G+4G";
                }
                else if (IsProblemRoad_2G)
                {
                    return "2G";
                }
                else if (IsProblemRoad_4G)
                {
                    return "4G";
                }
                return "";
            }
        }
        public Color RoadAbnormalColor { get; set; }

        //所有小路段信息
        public Dictionary<int, RoadPartAnaInfo> RoadPartAnaInfoDic { get; set; } = new Dictionary<int, RoadPartAnaInfo>();
        private void init(RoadLabelBaseInfo roadMapBaseInfo)
        {
            RoadPartAnaInfoDic.Clear();
            List<DbPoint> dbPoints = new List<DbPoint>();
            foreach (RoadPartAreaBaseInfo roadPartBaseInfo in roadMapBaseInfo.RoadPartAreaInfoList)
            {
                RoadPartAnaInfo partAnaInfo = new RoadPartAnaInfo(roadPartBaseInfo);
                RoadPartAnaInfoDic[roadPartBaseInfo.RoadPartAreaId] = partAnaInfo;

                dbPoints.AddRange(roadPartBaseInfo.DbPointList);
            }
            this.RoadLabelPolygon = new MultiPolygon(dbPoints);
        }

        #region 根据异常事件分析得到的测试异常概率信息

        /// <summary>
        /// 测试异常概率（根据异常事件获得）
        /// </summary>
        public double? TestAbnormalPer { get; set; }
        public RoadLabelEvtAnaInfo EvtAnaInfo { get; set; }
        #endregion

        #region 根据小路段KPI指标分析的模拟测试信息
        
        public StreetStatByPartKpiInfo RoadPartStatSumInfo { get; set; }
        public double ExitCellPer { get { return RoadPartStatSumInfo.ExitCellPer; } }//退服率
        public List<string> MainCellNameList { get { return RoadPartStatSumInfo.MainCellNameList; } }//主覆盖小区数
        public List<string> MainCellNameList_Exit { get { return RoadPartStatSumInfo.MainCellNameList_Exit; } }//退服小区数

        /// <summary>
        /// 模拟异常概率
        /// </summary>
        public double? WeakRxlevOrRsrpGridPer { get { return RoadPartStatSumInfo.WeakRxlevOrRsrpGridPer; } }

        public double? RsrpAvg { get { return RoadPartStatSumInfo.StreetKpiDataHub.RsrpInfo.KpiAvgValue; } }
        public double? SinrAvg { get { return RoadPartStatSumInfo.StreetKpiDataHub.SinrInfo.KpiAvgValue; } }
        public double? LteCoverRate { get { return RoadPartStatSumInfo.StreetKpiDataHub.LteCoverRateInfo.Rate; } }
        public double? RxlevAvg { get { return RoadPartStatSumInfo.StreetKpiDataHub.RxlevInfo.KpiAvgValue; } }

        public void AddRoadPartAnaInfo(RoadPartAnaInfo item)
        {
            RoadPartAnaInfoDic[item.RoadPartAreaId] = item;
            RoadPartStatSumInfo.AddRoadPartAnaInfo(item);
        }
        #endregion

        public void Calculate(RoadQualAnaCond roadAnaCond)
        {
            RoadPartStatSumInfo.Calculate(roadAnaCond);

            setAbnormalByEvtAnaInfo(roadAnaCond);

            //路段异常概率=模拟异常概率和测试异常概率两者最大值
            double roadAbnormalPer = double.MinValue;
            if (this.TestAbnormalPer != null)
            {
                roadAbnormalPer = (double)this.TestAbnormalPer;
            }
            if (this.WeakRxlevOrRsrpGridPer != null)
            {
                roadAbnormalPer = Math.Max(roadAbnormalPer, (double)this.WeakRxlevOrRsrpGridPer);

                if (roadAnaCond.IsCheck4G && roadAnaCond.IsAbnormal(RoadPartStatSumInfo.WeakRsrpRateInfo.Rate))
                {
                    IsProblemRoad_4G = true;
                }
                if (roadAnaCond.IsCheck2G && roadAnaCond.IsAbnormal(RoadPartStatSumInfo.WeakRxlevRateInfo.Rate))
                {
                    IsProblemRoad_2G = true;
                }
            }

            this.IsProblemRoad = IsProblemRoad_2G || IsProblemRoad_4G;
            if (roadAbnormalPer != double.MinValue)
            {
                this.RoadAbnormalPer = roadAbnormalPer;
                this.Affectoi_KM = Math.Round(roadAbnormalPer * RoadLabelLength_M / 100000, 2);
                if (WeakRxlevOrRsrpGridPer > TestAbnormalPer && ExitCellPer > 1)
                {
                    this.ProblemReason = "基站退服";
                }
            }
        }

        private void setAbnormalByEvtAnaInfo(RoadQualAnaCond roadAnaCond)
        {
            if (this.EvtAnaInfo != null)
            {
                this.ProblemReason = this.EvtAnaInfo.ReasonType;

                //计算测试异常概率
                double testAbnormalPer_2G = Math.Round(this.EvtAnaInfo.ProblemEvtPer_2G, 2);
                double testAbnormalPer_4G = Math.Round(this.EvtAnaInfo.ProblemEvtPer_4G, 2);
                if (roadAnaCond.IsCheck2G && roadAnaCond.IsAbnormal(testAbnormalPer_2G))
                {
                    IsProblemRoad_2G = true;
                }
                if (roadAnaCond.IsCheck4G && roadAnaCond.IsAbnormal(testAbnormalPer_4G))
                {
                    IsProblemRoad_4G = true;
                }

                if (roadAnaCond.IsCheck2G && roadAnaCond.IsCheck4G)
                {
                    this.TestAbnormalPer = Math.Max(testAbnormalPer_2G, testAbnormalPer_4G);
                }
                else if (roadAnaCond.IsCheck2G)
                {
                    this.TestAbnormalPer = testAbnormalPer_2G;
                }
                else if (roadAnaCond.IsCheck4G)
                {
                    this.TestAbnormalPer = testAbnormalPer_4G;
                }
            }
        }
    }

    /// <summary>
    /// 道路分析指标
    /// </summary>
    public class MainRoadQualAnaInfo
    {
        public MainRoadQualAnaInfo(string roadName)
        {
            this.Name = roadName;
        }
        public string Name { get; set; }
        public float Length_Total_M { get; set; }
        public double Length_Total_KM { get { return Math.Round(Length_Total_M / 1000, 2); } }

        /// <summary>
        /// 影响因子（路段长度×异常概率 ）
        /// </summary>
        public double Affectoi_KM { get; set; }

        public int RoadPartCount_Problem { get; set; }
        public int RoadPartCount_Total { get; set; }

        private float ProblemEvtPerSum_2G = 0;
        private float ProblemEvtPerSum_4G = 0;

        public double AbnormalPer { get; set; }//异常概率
        public double? TestAbnormalPer { get; set; }//测试异常概率
        public double? WeakRxlevOrRsrpGridPer { get; set; }//模拟异常概率

        public List<string> MainCellNameList { get; set; } = new List<string>();//主覆盖小区数
        public List<string> MainCellNameList_Exit { get; set; } = new List<string>();//退服小区数
        public double ExitCellPer { get; set; }
        
        public RoadQualKpiDataHub KpiInfoSum { get; set; }
        public double? RsrpAvg { get { return KpiInfoSum.RsrpInfo.KpiAvgValue; } }
        public double? RxlevAvg { get { return KpiInfoSum.RxlevInfo.KpiAvgValue; } }
        public double? LteCoverRate { get { return KpiInfoSum.LteCoverRateInfo.Rate; } }

        public List<string> RoadLabelList_Total { get; set; } = new List<string>();
        public List<string> RoadLabelList_Problem { get; set; } = new List<string>();
        public void AddRoadLabelAnaInfo(RoadLabelQualAnaInfo roadLabelAnaInfo)
        {
            if (this.RoadLabelList_Total.Contains(roadLabelAnaInfo.RoadLabel))
            {
                return;
            }
            this.RoadLabelList_Total.Add(roadLabelAnaInfo.RoadLabel);
            this.Length_Total_M += roadLabelAnaInfo.RoadLabelLength_M;
            this.RoadPartCount_Total += roadLabelAnaInfo.RoadPartAnaInfoDic.Count;
            this.RoadPartCount_Problem += roadLabelAnaInfo.RoadPartStatSumInfo.RoadPartCount_Problem;
            this.Affectoi_KM += roadLabelAnaInfo.Affectoi_KM;
            KpiInfoSum.Merge(roadLabelAnaInfo.RoadPartStatSumInfo.StreetKpiDataHub);

            if (roadLabelAnaInfo.EvtAnaInfo != null)
            {
                this.ProblemEvtPerSum_2G += roadLabelAnaInfo.EvtAnaInfo.ProblemEvtPer_2G;
                this.ProblemEvtPerSum_4G += roadLabelAnaInfo.EvtAnaInfo.ProblemEvtPer_4G;
            }

            if (roadLabelAnaInfo.IsProblemRoad && !this.RoadLabelList_Problem.Contains(roadLabelAnaInfo.RoadLabel))
            {
                this.RoadLabelList_Problem.Add(roadLabelAnaInfo.RoadLabel);
            }

            foreach (string cellName in roadLabelAnaInfo.MainCellNameList)
            {
                if (!MainCellNameList.Contains(cellName))
                {
                    MainCellNameList.Add(cellName);
                }
            }
            foreach (string cellName in roadLabelAnaInfo.MainCellNameList_Exit)
            {
                if (!MainCellNameList_Exit.Contains(cellName))
                {
                    MainCellNameList_Exit.Add(cellName);
                }
            }
        }
        public void Calculate()
        {
            RoadQualAnaCond roadAnaCond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;

            //计算测试异常概率
            double testAbnormalPer_2G = Math.Round(this.ProblemEvtPerSum_2G / RoadLabelList_Total.Count, 2);
            double testAbnormalPer_4G = Math.Round(this.ProblemEvtPerSum_4G / RoadLabelList_Total.Count, 2);
            if (roadAnaCond.IsCheck2G && roadAnaCond.IsCheck4G)
            {
                this.TestAbnormalPer = Math.Max(testAbnormalPer_2G, testAbnormalPer_4G);
            }
            else if (roadAnaCond.IsCheck2G)
            {
                this.TestAbnormalPer = testAbnormalPer_2G;
            }
            else if (roadAnaCond.IsCheck4G)
            {
                this.TestAbnormalPer = testAbnormalPer_4G;
            }

            //路段异常概率=模拟异常概率和测试异常概率两者最大值
            double roadAbnormalPer = double.MinValue;
            if (this.TestAbnormalPer != null)
            {
                roadAbnormalPer = (double)this.TestAbnormalPer;
            }

            if (RoadPartCount_Total > 0)
            {
                this.WeakRxlevOrRsrpGridPer = Math.Round(100 * (double)RoadPartCount_Problem / RoadPartCount_Total, 2);
                roadAbnormalPer = Math.Max(roadAbnormalPer, (double)this.WeakRxlevOrRsrpGridPer); 
            }

            if (roadAbnormalPer != double.MinValue)
            {
                this.AbnormalPer = roadAbnormalPer;
            }

            this.ExitCellPer = Math.Round(100 * (double)MainCellNameList_Exit.Count / MainCellNameList.Count, 2);
        }
    }

    /// <summary>
    /// 干线分析指标
    /// </summary>
    public class MainLineQualAnaInfo : MainRoadQualAnaInfo
    {
        public MainLineQualAnaInfo(string mainLineName)
            : base(mainLineName)
        {
        }
    }

    /// <summary>
    /// 区县问题信息
    /// </summary>
    public class CountyRoadQualInfo
    {
        public CountyRoadQualInfo(string county)
        {
            this.CountyName = county;
        }
        public string CountyName { get; set; }

        /// <summary>
        /// 只有2G问题的路段数
        /// </summary>
        public int ProblemRoadCount_2G { get; set; }

        /// <summary>
        /// 只有4G问题的路段数
        /// </summary>
        public int ProblemRoadCount_4G { get; set; }

        /// <summary>
        /// 同时有2G或4G问题的路段数
        /// </summary>
        public int ProblemRoadCount_24G { get; set; }
        public int ProblemRoadCount_Total { get; set; }

        public void AddProblemRoadInfo(RoadLabelQualAnaInfo roadAnaInfo)
        {
            this.ProblemRoadCount_Total++;
            if (roadAnaInfo.IsProblemRoad_2G && roadAnaInfo.IsProblemRoad_4G)
            {
                this.ProblemRoadCount_24G++;
            }
            else if (roadAnaInfo.IsProblemRoad_2G)
            {
                this.ProblemRoadCount_4G++;
            }
            else if (roadAnaInfo.IsProblemRoad_4G)
            {
                this.ProblemRoadCount_2G++;
            }
        }
    }

    /// <summary>
    /// 问题概览信息
    /// </summary>
    public class GeneralInfo
    {
        public GeneralInfo(int mainRoadCount, int mainRoadLabelCount, double lengthTotal_M)
        {
            this.MainRoadCount = mainRoadCount;
            this.MainRoadLabelCount = mainRoadLabelCount;
            this.LengthTotal_M = lengthTotal_M;
        }
        public int MainRoadCount { get; set; }
        public int MainRoadLabelCount { get; set; }
        public double LengthTotal_M { get; set; }
        public double LengthTotal_KM { get { return Math.Round(LengthTotal_M / 1000, 2); } }

        public int ProblemRoadLabelCount { get; set; }

        /// <summary>
        /// 影响因子（路段长度×异常概率 ）
        /// </summary>
        public double Affectoi_KM { get; set; }
        public double ProblemLengthRate { get; set; }

        public int MainCellCount { get; set; }
        public int ExitCellCount { get; set; }
        public double ExitCellPer { get; set; }

        public void Calculate()
        {
            this.ExitCellPer = Math.Round(100 * (double)ExitCellCount / MainCellCount, 2);
            this.ProblemLengthRate = Math.Round(100 * Affectoi_KM / LengthTotal_KM, 2);
        }
    }

    /// <summary>
    /// 汇总信息
    /// </summary>
    public class RoadQualAnaInfoSum
    {
        public RoadQualAnaInfoSum(Dictionary<int, RoadLabelBaseInfo> roadBaseInfoDic)
        {
            initRoadInfos(roadBaseInfoDic);
        }
        private double lengthTotal_M = 0;
        
        public GeneralInfo GeneralInfoItem { get; set; }

        public List<string> MainCellNameList { get; set; } = new List<string>();//主覆盖小区数
        public List<string> MainCellNameList_Exit { get; set; } = new List<string>();//退服小区数

        //栅格信息集
        public Dictionary<int, RoadPartAnaInfo> RoadPartAnaInfoDic { get; set; } = new Dictionary<int, RoadPartAnaInfo>();

        //区县问题路段信息汇总
        public Dictionary<string, CountyRoadQualInfo> CountyRoadQualInfoDic { get; set; } = new Dictionary<string, CountyRoadQualInfo>();
        public List<CountyRoadQualInfo> CountyRoadQualInfoList
        {
            get { return new List<CountyRoadQualInfo>(CountyRoadQualInfoDic.Values); }
        }

        //问题路段信息集
        public List<RoadLabelQualAnaInfo> ProblemRoadAnaInfoList { get; set; } = new List<RoadLabelQualAnaInfo>();

        //所有道路信息集
        public Dictionary<string, MainRoadQualAnaInfo> MainRoadAnaDic { get; set; } = new Dictionary<string, MainRoadQualAnaInfo>();

        //所有路段信息集
        public Dictionary<string, RoadLabelQualAnaInfo> RoadLabelAnaDic { get; set; } = new Dictionary<string, RoadLabelQualAnaInfo>();

        //所有干线信息集
        public Dictionary<string, MainLineQualAnaInfo> MainLineQualAnaDic { get; set; } = new Dictionary<string, MainLineQualAnaInfo>();

        private void initRoadInfos(Dictionary<int, RoadLabelBaseInfo> roadBaseInfoDic)
        {
            if (roadBaseInfoDic == null)
            {
                return;
            }
            foreach (RoadLabelBaseInfo roadBaseInfo in roadBaseInfoDic.Values)
            {
                MainRoadQualAnaInfo mainRoadInfo;//增加道路信息
                if (!MainRoadAnaDic.TryGetValue(roadBaseInfo.RoadName, out mainRoadInfo))
                {
                    mainRoadInfo = new MainRoadQualAnaInfo(roadBaseInfo.RoadName);
                    MainRoadAnaDic.Add(roadBaseInfo.RoadName, mainRoadInfo);
                }
                this.lengthTotal_M += roadBaseInfo.Length_M;

                RoadLabelQualAnaInfo roadAnaInfo;//增加路段信息
                if (!RoadLabelAnaDic.TryGetValue(roadBaseInfo.RoadLabel, out roadAnaInfo))
                {
                    roadAnaInfo = new RoadLabelQualAnaInfo(roadBaseInfo);
                    RoadLabelAnaDic.Add(roadBaseInfo.RoadLabel, roadAnaInfo);
                }

                foreach (string mainLineName in roadBaseInfo.MainLineNameList)//增加干线信息
                {
                    MainLineQualAnaInfo mainLineAnaInfo;
                    if (!MainLineQualAnaDic.TryGetValue(mainLineName, out mainLineAnaInfo))
                    {
                        mainLineAnaInfo = new MainLineQualAnaInfo(mainLineName);
                        MainLineQualAnaDic.Add(mainLineName, mainLineAnaInfo);
                    }
                }

                CountyRoadQualInfo countyInfo;//增加区县信息
                if (!this.CountyRoadQualInfoDic.TryGetValue(roadBaseInfo.CountyName, out countyInfo))
                {
                    countyInfo = new CountyRoadQualInfo(roadBaseInfo.CountyName);
                    this.CountyRoadQualInfoDic.Add(roadBaseInfo.CountyName, countyInfo);
                }
            }
        }
        public void AddEvtAnaInfos(List<RoadLabelEvtAnaInfo> roadEvtAnaInfoList)
        {
            if (roadEvtAnaInfoList == null)
            {
                return;
            }
            foreach (RoadLabelEvtAnaInfo evtAnaInfo in roadEvtAnaInfoList)
            {
                RoadLabelQualAnaInfo roadAnaInfo;
                if (RoadLabelAnaDic.TryGetValue(evtAnaInfo.RoadLabel, out roadAnaInfo))
                {
                    roadAnaInfo.EvtAnaInfo = evtAnaInfo;
                }
            }
        }
        public void AddRoadPartAnaInfo(RoadPartAnaInfo roadPartAnaItem)
        {
            RoadPartAnaInfoDic[roadPartAnaItem.RoadPartAreaId] = roadPartAnaItem;

            RoadLabelQualAnaInfo roadAnaInfo;
            if (RoadLabelAnaDic.TryGetValue(roadPartAnaItem.RoadPartBaseInfo.RoadLabel, out roadAnaInfo))
            {
                roadAnaInfo.AddRoadPartAnaInfo(roadPartAnaItem);//将栅格指标添加到对应路段信息中
            }
        }

        public void Calculate(RoadQualAnaCond roadAnaCond)
        {
            ProblemRoadAnaInfoList.Clear();
            double affectoi_KM = 0;

            foreach (RoadLabelQualAnaInfo roadLabelAnaInfo in RoadLabelAnaDic.Values)
            {
                roadLabelAnaInfo.Calculate(roadAnaCond);

                //统计干线信息
                foreach (string mainLineName in roadLabelAnaInfo.RoadLabelBaseInfo.MainLineNameList)
                {
                    MainLineQualAnaInfo mainLineAnaInfo;
                    if (MainLineQualAnaDic.TryGetValue(mainLineName, out mainLineAnaInfo))
                    {
                        mainLineAnaInfo.AddRoadLabelAnaInfo(roadLabelAnaInfo);
                    }
                }

                //统计道路信息
                MainRoadQualAnaInfo mainRoadInfo;
                if (MainRoadAnaDic.TryGetValue(roadLabelAnaInfo.RoadName, out mainRoadInfo))
                {
                    mainRoadInfo.AddRoadLabelAnaInfo(roadLabelAnaInfo);
                }

                affectoi_KM = addProblemRoad(affectoi_KM, roadLabelAnaInfo);

                addCellName(roadLabelAnaInfo);
            }

            foreach (MainLineQualAnaInfo mainLineInfo in MainLineQualAnaDic.Values)
            {
                mainLineInfo.Calculate();
            }
            foreach (MainRoadQualAnaInfo mainRoadInfo in MainRoadAnaDic.Values)
            {
                mainRoadInfo.Calculate();
            }

            GeneralInfoItem = new GeneralInfo(MainRoadAnaDic.Count, RoadLabelAnaDic.Count, lengthTotal_M);
            GeneralInfoItem.ProblemRoadLabelCount = ProblemRoadAnaInfoList.Count;
            GeneralInfoItem.Affectoi_KM = affectoi_KM;
            GeneralInfoItem.MainCellCount = MainCellNameList.Count;
            GeneralInfoItem.ExitCellCount = MainCellNameList_Exit.Count;
            this.GeneralInfoItem.Calculate();
        }

        private double addProblemRoad(double affectoi_KM, RoadLabelQualAnaInfo roadLabelAnaInfo)
        {
            //统计问题路段信息
            if (roadLabelAnaInfo.IsProblemRoad)
            {
                ProblemRoadAnaInfoList.Add(roadLabelAnaInfo);
                affectoi_KM += roadLabelAnaInfo.Affectoi_KM;

                CountyRoadQualInfo countyInfo;
                string countyName = roadLabelAnaInfo.RoadLabelBaseInfo.CountyName;
                if (CountyRoadQualInfoDic.TryGetValue(countyName, out countyInfo))
                {
                    countyInfo.AddProblemRoadInfo(roadLabelAnaInfo);
                }
            }

            return affectoi_KM;
        }

        private void addCellName(RoadLabelQualAnaInfo roadLabelAnaInfo)
        {
            //统计主覆盖小区信息
            foreach (string cellName in roadLabelAnaInfo.MainCellNameList)
            {
                if (!MainCellNameList.Contains(cellName))
                {
                    MainCellNameList.Add(cellName);
                }
            }
            foreach (string cellName in roadLabelAnaInfo.MainCellNameList_Exit)
            {
                if (!MainCellNameList_Exit.Contains(cellName))
                {
                    MainCellNameList_Exit.Add(cellName);
                }
            }
        }

        public void ClearData()
        {
            MainCellNameList.Clear();
            MainCellNameList_Exit.Clear();
            RoadPartAnaInfoDic.Clear();
            CountyRoadQualInfoDic.Clear();
            CountyRoadQualInfoList.Clear();
            ProblemRoadAnaInfoList.Clear();
            MainRoadAnaDic.Clear();
            RoadLabelAnaDic.Clear();
            MainLineQualAnaDic.Clear();
            GeneralInfoItem = null;
            lengthTotal_M = 0;
        }
    }

    /// <summary>
    /// 栅格小区信息
    /// </summary>
    public class RoadPartCellKpiInfo : IComparable<RoadPartCellKpiInfo>
    {
        public RoadPartCellKpiInfo(int areaId, ICell iCell)
        {
            this.AreaId = areaId;
            this.SrcCell = iCell;
            this.CellToken = iCell.Token;
            if (iCell is LTECell)
            {
                NetType = ServiceName.LTE;
                this.CellToken = (iCell as LTECell).ECI.ToString();
            }
            else if (iCell is Cell)
            {
                NetType = ServiceName.GSM;
            }
            else if (iCell is UnknowCell)
            {
                UnknowCell uCell = iCell as UnknowCell;
                if (uCell.CI > 65535)
                {
                    NetType = ServiceName.LTE;
                    this.CellToken = uCell.CI.ToString();
                }
            }
        }

        public ICell SrcCell { get; set; }
        public CellExitInfo ExitInfo { get; set; }
        public string ExitInfoDes
        {
            get
            {
                if (ExitInfo != null)
                {
                    return ExitInfo.ToString();
                }
                return "";
            }
        }
        public string CellState { get { return IsExitCell ? "退服" : "正常"; } }
        public bool IsExitCell { get { return ExitInfo != null; } }
        public ServiceName NetType { get; private set; }
        public string CellToken { get; private set; }
        public string CellName { get { return SrcCell.Name.Trim(); } }
        public int AreaId { get; set; }
        public string StrDes { get; set; }

        public RoadQualKpiDataHub KpiData { get; set; } = new RoadQualKpiDataHub();

        public int SampleNum_Total { get { return KpiData.SampleNum_Total; } }
        public double? RsrpAvg { get { return KpiData.RsrpInfo.KpiAvgValue; } }
        public double? SinrAvg { get { return KpiData.SinrInfo.KpiAvgValue; } }
        public double? LteCoverRate { get { return KpiData.LteCoverRateInfo.Rate; } }
        public double? RxlevAvg { get { return KpiData.RxlevInfo.KpiAvgValue; } }

        public void Merge(RoadPartCellKpiInfo gridCellData)
        {
            this.KpiData.Merge(gridCellData.KpiData);
        }
        public void AddGsmKpi(int sampleNum_Gsm, int sampleNum_Rxlev, float rxlevSum)
        {
            this.KpiData.AddGsmKpi(sampleNum_Gsm, sampleNum_Rxlev, rxlevSum);
        }
        public void AddLteKpi(int sampleNum_Lte, int sampleNum_Rsrp, float rsrpSum, int sampleNum_Sinr, float sinrSum
            , int sampleNum_LteCoverValid, int sampleNum_LteCoverTotal)
        {
            this.KpiData.AddLteKpi(sampleNum_Lte, sampleNum_Rsrp, rsrpSum, sampleNum_Sinr, sinrSum
            , sampleNum_LteCoverValid, sampleNum_LteCoverTotal);
        }
        public int CompareTo(RoadPartCellKpiInfo other)
        {
            if (this.NetType != other.NetType)
            {
                return other.NetType.CompareTo(this.NetType);
            }

            if (this.NetType == ServiceName.LTE)
            {
                if (other.RsrpAvg == this.RsrpAvg)
                {
                    return other.KpiData.SampleNum_Lte.CompareTo(this.KpiData.SampleNum_Lte);
                }
                return getCompareQualValue(other.RsrpAvg).CompareTo(getCompareQualValue(this.RsrpAvg));
            }
            else if (this.NetType == ServiceName.GSM)
            {
                if (other.RxlevAvg == this.RxlevAvg)
                {
                    return other.KpiData.SampleNum_Gsm.CompareTo(this.KpiData.SampleNum_Gsm);
                }
                return getCompareQualValue(other.RxlevAvg).CompareTo(getCompareQualValue(this.RxlevAvg));
            }
            return 0;
        }

        private double getCompareQualValue(double? dValue)
        {
            if (dValue == null)
            {
                return -142;
            }
            return (double)dValue;
        }
    }

    public class RoadQualKpiDataHub
    {
        public int SampleNum_Total { get{return SampleNum_Lte + SampleNum_Gsm;}}
        public int SampleNum_Lte { get; set; }
        public int SampleNum_Gsm { get; set; }

        public AvgKpiInfo RxlevInfo { get; set; } = new AvgKpiInfo();

        public AvgKpiInfo RsrpInfo { get; set; } = new AvgKpiInfo();

        public AvgKpiInfo SinrInfo { get; set; } = new AvgKpiInfo();

        public RateKpiInfo LteCoverRateInfo { get; set; } = new RateKpiInfo("LTE综合覆盖率", 0, 0);
        public void AddGsmKpi(int sampleNum_Gsm, int sampleNum_Rxlev, float rxlevSum)
        {
            this.SampleNum_Gsm += sampleNum_Gsm;
            this.RxlevInfo.AddSumKpi(sampleNum_Rxlev, rxlevSum);
        }
        public void AddLteKpi(int sampleNum_Lte, int sampleNum_Rsrp, float rsrpSum, int sampleNum_Sinr, float sinrSum
            , int sampleNum_LteCoverValid, int sampleNum_LteCoverTotal)
        {
            this.SampleNum_Lte += sampleNum_Lte;
            this.RsrpInfo.AddSumKpi(sampleNum_Rsrp, rsrpSum);
            this.SinrInfo.AddSumKpi(sampleNum_Sinr, sinrSum);
            this.LteCoverRateInfo.AddInfo(sampleNum_LteCoverValid, sampleNum_LteCoverTotal);
        }
        public void Merge(RoadQualKpiDataHub kpiData)
        {
            this.SampleNum_Lte += kpiData.SampleNum_Lte;
            this.SampleNum_Gsm += kpiData.SampleNum_Gsm;
            this.RxlevInfo.Merge(kpiData.RxlevInfo);
            this.RsrpInfo.Merge(kpiData.RsrpInfo);
            this.SinrInfo.Merge(kpiData.SinrInfo);
            this.LteCoverRateInfo.Merge(kpiData.LteCoverRateInfo);
        }
    }

    /// <summary>
    /// 道路、路段通过路测KPI指标分析到的信息
    /// </summary>
    public class StreetStatByPartKpiInfo
    {
        public StreetStatByPartKpiInfo(Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic
            , double streetLength_M)
        {
            this.RoadPartAnaInfoDic = roadPartAnaInfoDic;
            this.StreetLength_M = streetLength_M;
        }
        public double StreetLength_M { get; set; }
        public int RoadPartCount_Problem { get; set; }
        
        public Dictionary<int, RoadPartAnaInfo> RoadPartAnaInfoDic { get; set; }
        
        public RoadQualKpiDataHub StreetKpiDataHub { get; set; } = new RoadQualKpiDataHub();

        public List<string> MainCellNameList { get; set; } = new List<string>();//主覆盖小区数
        public List<string> MainCellNameList_Exit { get; set; } = new List<string>();//退服小区数
        public double ExitCellPer { get; set; }//退服率

        public RateKpiInfo WeakRsrpRateInfo { get; set; } = new RateKpiInfo("4G弱覆盖", 0, 0);
        public RateKpiInfo WeakRxlevRateInfo { get; set; } = new RateKpiInfo("2G弱覆盖", 0, 0);
        public double? WeakRxlevOrRsrpGridPer { get; set; }
        
        public string NetTypeStr
        {
            get
            {
                string netTypeStr = "";
                if (WeakRsrpRateInfo.IsAccord && WeakRxlevRateInfo.IsAccord)
                {
                    netTypeStr = "2G+4G";
                }
                else if (WeakRsrpRateInfo.IsAccord)
                {
                    netTypeStr = "4G";
                }
                else if (WeakRxlevRateInfo.IsAccord)
                {
                    netTypeStr = "2G";
                }
                return netTypeStr;
            }
        }

        public void AddRoadPartAnaInfo(RoadPartAnaInfo item)
        {
            RoadPartAnaInfoDic[item.RoadPartBaseInfo.RoadPartAreaId] = item;
        }
        public void Calculate(RoadQualAnaCond roadAnaCond)
        {
            int roadParTotalCount = RoadPartAnaInfoDic.Count;
            WeakRsrpRateInfo.TotalCount = roadParTotalCount;
            WeakRxlevRateInfo.TotalCount = roadParTotalCount;

            foreach (RoadPartAnaInfo item in RoadPartAnaInfoDic.Values)
            {
                StreetKpiDataHub.Merge(item.KpiData);
                addRoadPartAnaInfo(roadAnaCond, item);
            }
            this.ExitCellPer = Math.Round(100 * (double)MainCellNameList_Exit.Count / MainCellNameList.Count, 2);

            double roadAbnormalGate = roadAnaCond.RoadAbnormalGate;
            WeakRsrpRateInfo.CheckIsAccord(1, roadAbnormalGate);
            WeakRxlevRateInfo.CheckIsAccord(1, roadAbnormalGate);

            //路段问题信息取问题最多的一种网络类型的
            if (WeakRsrpRateInfo.Rate > WeakRxlevRateInfo.Rate)
            {
                this.WeakRxlevOrRsrpGridPer = WeakRsrpRateInfo.Rate;
                this.RoadPartCount_Problem = (int)WeakRsrpRateInfo.ValidCount;
            }
            else
            {
                this.WeakRxlevOrRsrpGridPer = WeakRxlevRateInfo.Rate;
                this.RoadPartCount_Problem = (int)WeakRxlevRateInfo.ValidCount;
            }
        }

        private void addRoadPartAnaInfo(RoadQualAnaCond roadAnaCond, RoadPartAnaInfo item)
        {
            foreach (string cellName in item.MainCellNameList)
            {
                if (!MainCellNameList.Contains(cellName))
                {
                    MainCellNameList.Add(cellName);
                }
            }
            foreach (string cellName in item.MainCellNameList_Exit)
            {
                if (!MainCellNameList_Exit.Contains(cellName))
                {
                    MainCellNameList_Exit.Add(cellName);
                }
            }

            if (roadAnaCond.IsCheck4G && item.IsWeakRsrp)
            {
                WeakRsrpRateInfo.ValidCount++;
            }
            if (roadAnaCond.IsCheck2G && item.IsWeakRxlev)
            {
                WeakRxlevRateInfo.ValidCount++;
            }
        }
    }
}
