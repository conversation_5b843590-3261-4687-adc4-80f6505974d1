﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTWeakSINRReason_Scan : ZTWeakSINRReason
    {
        public ZTWeakSINRReason_Scan(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "质差原因分析_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33018, this.Name);
        }

        protected override bool getCondition()
        {
            funcCond = new FuncCondition_Scan();

            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            ReasonOptionDlg dlg = new ReasonOptionDlg(funcCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
                FileInfo fi = MainModel.DTDataManager.FileDataManagers[0].GetFileInfo();
                for (int i = 0; i < testPointList.Count; i++)
                {
                    TestPoint tp = testPointList[i];
                    if (!filter(tp))
                    {
                        WeakSINRPoint pnt = null;
                        if (funcCond.IsValid(tp))
                        {
                            isSuddenWeak = isSuddenWeakFun(testPointList, i);
                            ReasonBase reason = funcCond.JudgeReason(tp);
                            pnt = new WeakSINRPoint_Scan(fi, tp, reason);
                        }
                        saveTestPointInfo(fi, tp, pnt);
                    }
                }
                condEvents.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + Environment.NewLine + ex.StackTrace);
            }
        }

        protected override float? GetSINR(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR"];
        }

    }
}
