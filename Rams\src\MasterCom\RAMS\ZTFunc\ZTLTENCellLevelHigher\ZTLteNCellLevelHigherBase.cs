﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 邻区强覆盖基类，由按区域和按文件查找采样点，但都是先得到查询文件
    /// </summary>
    public class ZTLteNCellLevelHigherBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        public ZTLteNCellLevelHigherBase(MainModel mainModel) : base(mainModel)
        {

        }
        //查询条件
        private int numRSRP = 0;
        //主服电平
        private float fRSRP = -1;
        //邻服电平
        private float fNRSRP = -1;
        //电平差
        private float fRSRPDiff = -1;
        //小区信息集合
        private List<LteRsrpInfo> listCelllteInfos = new List<LteRsrpInfo>();


        /// <summary>
        /// 添加功能点
        /// </summary>
        /// <returns></returns>
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22116, this.Name);
        }

        /// <summary>
        /// 采样点是否满足邻区强覆盖条件
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected bool setCondition(TestPoint tp)
        {
            float? fRsrp = getRSRP(tp);
            if (fRsrp == null)
            {
                fRSRP = -1;
                fRSRPDiff = -1;
                fNRSRP = -1;
                return false;
            }

            for (int i = 0; i < 10; i++)
            {
                float? fNRsrp = getNRSRP(tp, i);
                if (fNRsrp == null)
                {
                    break;
                }
                float diff = (float)fNRsrp - (float)fRsrp;
                if (diff >= numRSRP)
                {
                    fRSRP = (float)fRsrp;
                    fNRSRP = (float)fNRsrp;
                    fRSRPDiff = diff;
                    return true;
                }
            }
            fRSRP = -1;
            fRSRPDiff = -1;
            fNRSRP = -1;
            return false;
        }

        /// <summary>
        /// 查询前准备
        /// </summary>
        protected override void getReadyBeforeQuery()
        {
            listCelllteInfos = new List<LteRsrpInfo>();
        }

        /// <summary>
        /// 弹出邻区电平比主服务小区电平强多少的条件窗口
        /// </summary>
        /// <returns></returns>
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            ZTLTENCellLevelHigherDlg dlg = new ZTLTENCellLevelHigherDlg();
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                dlg.GetCondition(out numRSRP);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 查询完数据后调用展示窗口
        /// </summary>
        protected override void fireShowForm()
        {
            ZTLTENCellLevelHigherForm lteCellSetForm = MainModel.CreateResultForm(typeof(ZTLTENCellLevelHigherForm)) as ZTLTENCellLevelHigherForm;
            lteCellSetForm.FillData(listCelllteInfos);
            lteCellSetForm.Visible = true;
            lteCellSetForm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
            releaseData();
        }

        /// <summary>
        /// 释放部分变量
        /// </summary>
        private void releaseData()
        {
            listCelllteInfos = null;
        }

        /// <summary>
        /// 组织测试点数据，得到邻区强覆盖小区
        /// </summary>
        /// <param name="tp"></param>
        protected void doWithDTData(TestPoint tp)
        {
            LteRsrpInfo lteInfo = new LteRsrpInfo();
            LTECell cell = tp.GetMainCell_LTE();
            if (cell == null)
            {
                int? iTAC = tp.GetLAC();
                if (iTAC == null)
                {
                    iTAC = 0;
                }
                int? iEci = tp.GetCI();
                if (iEci == null)
                {
                    iEci = 0;
                }
                lteInfo.TAC = (int)iTAC;
                lteInfo.ECI = (int)iEci;
                lteInfo.CellName = "" + iTAC + "_" + iEci;
            }
            else
            {
                lteInfo.TAC = cell.TAC;
                lteInfo.ECI = cell.ECI;
                lteInfo.CellName = cell.Name;
            }
            lteInfo.Longitude = tp.Longitude;
            lteInfo.Latitude = tp.Latitude;
            lteInfo.RSRP = fRSRP;
            lteInfo.NRSRP = fNRSRP;
            lteInfo.RSRPDiff = fRSRPDiff;
            lteInfo.FileName = tp.FileName;
            lteInfo.TestPoint = tp;
            listCelllteInfos.Add(lteInfo);
        }

        /// <summary>
        /// 判断查询结果，加入数据到集合
        /// </summary>
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        //表示结束等待
                        if (WaitBox.CancelRequest)
                        {
                            return;
                        }
                        getEffectiveTestPoint(tp);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }

        /// <summary>
        /// 得到有效的采样点
        /// </summary>
        /// <param name="tp"></param>
        private void getEffectiveTestPoint(TestPoint tp)
        {
            if (tp == null)
            {
                return;
            }
            //判断采样点是否在区域内
            if (!isValidTestPoint(tp))
            {
                return;
            }
            DTDataHeader header = DTDataHeaderManager.GetInstance().GetHeaderByFileID(tp.FileID);
            if (header == null || !setCondition(tp))
            {
                return;
            }
            doForAttenuation(tp);
            if (doForLTEScanWorkMode(tp))
            {
                tp.ApplyHeader(header);
                doWithDTData(tp);
            }
        }


        /// <summary>
        /// 获取主服RSRP
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        private float? getRSRP(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        /// <summary>
        /// 获取邻服RSRP
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        private float? getNRSRP(TestPoint tp, int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }
    }

    /// <summary>
    /// 按文件查询
    /// </summary>
    public class ZTLteNCellLevelHigherByFile : ZTLteNCellLevelHigherBase
    {

        #region instance
        private static volatile ZTLteNCellLevelHigherByFile instance = null;
        public ZTLteNCellLevelHigherByFile(MainModel mainModel) : base(mainModel)
        {

        }

        /// <summary>
        /// 若为文件查询，则直接得到文件查询的条件
        /// </summary>
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        /// <summary>
        /// 重写查询类型
        /// </summary>
        /// <returns></returns>
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        /// <summary>
        /// 单例
        /// </summary>
        /// <returns></returns>
        public static ZTLteNCellLevelHigherByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTLteNCellLevelHigherByFile(MainModel.GetInstance());
            }
            return instance;
        }
        #endregion

        /// <summary>
        /// 界面显示按钮的注释
        /// </summary>
        public override string Name
        {
            get { return "邻区电平强于主服务小区电平小区集(按文件)"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
    }

    /// <summary>
    /// 按区域查询（实际先查询文件）
    /// </summary>
    public class ZTLteNCellLevelHigherByRegionNew : ZTLteNCellLevelHigherBase
    {

        #region instance
        private static volatile ZTLteNCellLevelHigherByRegionNew instance = null;

        protected ZTLteNCellLevelHigherByRegionNew(MainModel mainModel)
            : base(mainModel)
        {

        }

        public static ZTLteNCellLevelHigherByRegionNew GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTLteNCellLevelHigherByRegionNew(MainModel.GetInstance());
            }
            return instance;
        }
        #endregion


        /// <summary>
        /// 界面显示按钮的注释
        /// </summary>
        public override string Name
        {
            get { return "邻区电平强于主服务小区电平小区集(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        //是否把采样点信息放到DTDataManager，默认为放到DTDataManager
        protected bool isAddSampleToDTDataManager = true;
    }
}
