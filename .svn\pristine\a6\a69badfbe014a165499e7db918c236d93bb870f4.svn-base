﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class VoNRStatDelayInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tlvVoNrItemsInfo = new BrightIdeasSoftware.TreeListView();
            this.olvSn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoPhoneNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCallResault = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCallType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoNode1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoNode2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoNode3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoNode4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoNode5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCallDelayTimes1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCallDelayTimes2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMo1to2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMo2to3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMo3to4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMo4to5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCsfbNode1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCsfbNode2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCsfbNode3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCsfbDelayTimes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCsfb1to2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMoCsfb2to3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtPhoneNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCallResault = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCallType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtNode1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtNode2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtNode3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtNode4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtNode5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCallDelayTimes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMt1to2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMt2to3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMt3to4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMt4to5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCsfbNode1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCsfbNode2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCsfbNode3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCsfbDelayTimes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCsfb1to2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvMtCsfb2to3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.tlvVoNrItemsInfo)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // tlvVoNrItemsInfo
            // 
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvSn);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvGridName);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoFileName);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoPhoneNum);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCallResault);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCallType);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoNode1);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoNode2);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoNode3);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoNode4);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoNode5);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCallDelayTimes1);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCallDelayTimes2);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMo1to2);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMo2to3);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMo3to4);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMo4to5);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCsfbNode1);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCsfbNode2);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCsfbNode3);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCsfbDelayTimes);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCsfb1to2);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMoCsfb2to3);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtFileName);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtPhoneNum);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCallResault);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCallType);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtNode1);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtNode2);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtNode3);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtNode4);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtNode5);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCallDelayTimes);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMt1to2);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMt2to3);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMt3to4);
            this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMt4to5);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCsfbNode1);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCsfbNode2);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCsfbNode3);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCsfbDelayTimes);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCsfb1to2);
            //this.tlvVoNrItemsInfo.AllColumns.Add(this.olvMtCsfb2to3);
            this.tlvVoNrItemsInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvSn,
            this.olvGridName,
            this.olvMoFileName,
            this.olvMoPhoneNum,
            this.olvMoCallResault,
            this.olvMoCallType,
            this.olvMoNode1,
            this.olvMoNode2,
            this.olvMoNode3,
            this.olvMoNode4,
            this.olvMoNode5,
            this.olvMoCallDelayTimes1,
            this.olvMoCallDelayTimes2,
            this.olvMo1to2,
            this.olvMo2to3,
            this.olvMo3to4,
            this.olvMo4to5,
            //this.olvMoCsfbNode1,
            //this.olvMoCsfbNode2,
            //this.olvMoCsfbNode3,
            //this.olvMoCsfbDelayTimes,
            //this.olvMoCsfb1to2,
            //this.olvMoCsfb2to3,
            this.olvMtFileName,
            this.olvMtPhoneNum,
            this.olvMtCallResault,
            this.olvMtCallType,
            this.olvMtNode1,
            this.olvMtNode2,
            this.olvMtNode3,
            this.olvMtNode4,
            this.olvMtNode5,
            this.olvMtCallDelayTimes,
            this.olvMt1to2,
            this.olvMt2to3,
            this.olvMt3to4,
            this.olvMt4to5
            //this.olvMtCsfbNode1,
            //this.olvMtCsfbNode2,
            //this.olvMtCsfbNode3,
            //this.olvMtCsfbDelayTimes,
            //this.olvMtCsfb1to2,
            //this.olvMtCsfb2to3
            });
            this.tlvVoNrItemsInfo.ContextMenuStrip = this.contextMenuStrip;
            this.tlvVoNrItemsInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.tlvVoNrItemsInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlvVoNrItemsInfo.FullRowSelect = true;
            this.tlvVoNrItemsInfo.GridLines = true;
            this.tlvVoNrItemsInfo.HeaderWordWrap = true;
            this.tlvVoNrItemsInfo.IsNeedShowOverlay = false;
            this.tlvVoNrItemsInfo.Location = new System.Drawing.Point(0, 0);
            this.tlvVoNrItemsInfo.Name = "tlvVoNRItemsInfo";
            this.tlvVoNrItemsInfo.OwnerDraw = true;
            this.tlvVoNrItemsInfo.ShowGroups = false;
            this.tlvVoNrItemsInfo.Size = new System.Drawing.Size(1008, 562);
            this.tlvVoNrItemsInfo.TabIndex = 13;
            this.tlvVoNrItemsInfo.UseCompatibleStateImageBehavior = false;
            this.tlvVoNrItemsInfo.View = System.Windows.Forms.View.Details;
            this.tlvVoNrItemsInfo.VirtualMode = true;
            this.tlvVoNrItemsInfo.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.tlvCellsInfo_MouseDoubleClick);
            // 
            // olvSn
            // 
            this.olvSn.HeaderFont = null;
            this.olvSn.Text = "序号";
            // 
            // olvGridName
            // 
            this.olvGridName.AspectName = "";
            this.olvGridName.HeaderFont = null;
            this.olvGridName.Text = "网格";
            // 
            // olvMoFileName
            // 
            this.olvMoFileName.HeaderFont = null;
            this.olvMoFileName.Text = "主叫文件名";
            // 
            // olvMoPhoneNum
            // 
            this.olvMoPhoneNum.HeaderFont = null;
            this.olvMoPhoneNum.Text = "主叫号码";
            this.olvMoPhoneNum.Width = 120;
            // 
            // olvMoCallResault
            // 
            this.olvMoCallResault.HeaderFont = null;
            this.olvMoCallResault.Text = "是否接通";
            // 
            // olvMoCallType
            // 
            this.olvMoCallType.HeaderFont = null;
            this.olvMoCallType.Text = "呼叫建立类型";
            this.olvMoCallType.Width = 90;
            // 
            // olvMoNode1
            // 
            this.olvMoNode1.AspectName = "";
            this.olvMoNode1.HeaderFont = null;
            this.olvMoNode1.Text = "节点1：主叫发invite";
            this.olvMoNode1.Width = 120;
            // 
            // olvMoNode2
            // 
            this.olvMoNode2.AspectName = "";
            this.olvMoNode2.HeaderFont = null;
            this.olvMoNode2.Text = "节点2：主叫初始接入RRC重配完成";
            this.olvMoNode2.Width = 120;
            // 
            // olvMoNode3
            // 
            this.olvMoNode3.AspectName = "";
            this.olvMoNode3.HeaderFont = null;
            this.olvMoNode3.Text = "节点3：主叫收到 NR->DL NAS transport";
            this.olvMoNode3.Width = 120;
            // 
            // olvMoNode4
            // 
            this.olvMoNode4.HeaderFont = null;
            this.olvMoNode4.Text = "节点4：主叫收180 ringing";
            this.olvMoNode4.Width = 120;
            // 
            // olvMoNode5
            // 
            this.olvMoNode5.HeaderFont = null;
            this.olvMoNode5.Text = "节点5：主叫收ACK";
            this.olvMoNode5.Width = 120;
            // 
            // olvMoCallDelayTimes1
            // 
            this.olvMoCallDelayTimes1.HeaderFont = null;
            this.olvMoCallDelayTimes1.Text = "呼叫建立时延1(节点1-5；毫秒)";
            this.olvMoCallDelayTimes1.Width = 100;
            // 
            // olvMoCallDelayTimes2
            // 
            this.olvMoCallDelayTimes2.HeaderFont = null;
            this.olvMoCallDelayTimes2.Text = "呼叫建立时延2(节点1-4；毫秒)";
            this.olvMoCallDelayTimes2.Width = 100;
            // 
            // olvMo1to2
            // 
            this.olvMo1to2.HeaderFont = null;
            this.olvMo1to2.Text = "节点1-2时长";
            this.olvMo1to2.Width = 100;
            // 
            // olvMo2to3
            // 
            this.olvMo2to3.HeaderFont = null;
            this.olvMo2to3.Text = "节点2-3时长";
            this.olvMo2to3.Width = 100;
            // 
            // olvMo3to4
            // 
            this.olvMo3to4.HeaderFont = null;
            this.olvMo3to4.Text = "节点3-4时长";
            this.olvMo3to4.Width = 100;
            // 
            // olvMo4to5
            // 
            this.olvMo4to5.HeaderFont = null;
            this.olvMo4to5.Text = "节点4-5时长";
            this.olvMo4to5.Width = 100;
            // 
            // olvMoCsfbNode1
            // 
            this.olvMoCsfbNode1.HeaderFont = null;
            this.olvMoCsfbNode1.Text = "CSFB节点1：主叫发extended SR";
            this.olvMoCsfbNode1.Width = 120;
            // 
            // olvMoCsfbNode2
            // 
            this.olvMoCsfbNode2.HeaderFont = null;
            this.olvMoCsfbNode2.Text = "CSFB节点2：主叫发出CM service reques";
            this.olvMoCsfbNode2.Width = 120;
            // 
            // olvMoCsfbNode3
            // 
            this.olvMoCsfbNode3.HeaderFont = null;
            this.olvMoCsfbNode3.Text = "CSFB节点3：主叫收Alerting";
            this.olvMoCsfbNode3.Width = 120;
            // 
            // olvMoCsfbDelayTimes
            // 
            this.olvMoCsfbDelayTimes.HeaderFont = null;
            this.olvMoCsfbDelayTimes.Text = "CSFB总时长";
            this.olvMoCsfbDelayTimes.Width = 100;
            // 
            // olvMoCsfb1to2
            // 
            this.olvMoCsfb1to2.HeaderFont = null;
            this.olvMoCsfb1to2.Text = "CSFB节点1-2时长";
            this.olvMoCsfb1to2.Width = 120;
            // 
            // olvMoCsfb2to3
            // 
            this.olvMoCsfb2to3.HeaderFont = null;
            this.olvMoCsfb2to3.Text = "CSFB节点2-3时长";
            this.olvMoCsfb2to3.Width = 120;
            // 
            // olvMtFileName
            // 
            this.olvMtFileName.HeaderFont = null;
            this.olvMtFileName.Text = "被叫文件名";
            // 
            // olvMtPhoneNum
            // 
            this.olvMtPhoneNum.HeaderFont = null;
            this.olvMtPhoneNum.Text = "被叫号码";
            this.olvMtPhoneNum.Width = 100;
            // 
            // olvMtCallResault
            // 
            this.olvMtCallResault.HeaderFont = null;
            this.olvMtCallResault.Text = "被叫是否接通";
            // 
            // olvMtCallType
            // 
            this.olvMtCallType.HeaderFont = null;
            this.olvMtCallType.Text = "被叫呼叫建立类型";
            this.olvMtCallType.Width = 90;
            // 
            // olvMtNode1
            // 
            this.olvMtNode1.HeaderFont = null;
            this.olvMtNode1.Text = "节点1：被叫收到ps paging";
            this.olvMtNode1.Width = 120;
            // 
            // olvMtNode2
            // 
            this.olvMtNode2.HeaderFont = null;
            this.olvMtNode2.Text = "节点2：被叫收到invite";
            this.olvMtNode2.Width = 120;
            // 
            // olvMtNode3
            // 
            this.olvMtNode3.HeaderFont = null;
            this.olvMtNode3.Text = "节点3：被叫发出183";
            this.olvMtNode3.Width = 120;
            // 
            // olvMtNode4
            // 
            this.olvMtNode4.HeaderFont = null;
            this.olvMtNode4.Text = "节点4：被叫收到 NR->DL NAS transport";
            this.olvMtNode4.Width = 120;
            // 
            // olvMtNode5
            // 
            this.olvMtNode5.HeaderFont = null;
            this.olvMtNode5.Text = "节点5：被叫发出180ringing";
            this.olvMtNode5.Width = 120;
            // 
            // olvMtCallDelayTimes
            // 
            this.olvMtCallDelayTimes.HeaderFont = null;
            this.olvMtCallDelayTimes.Text = "被叫呼叫建立时延(毫秒)";
            this.olvMtCallDelayTimes.Width = 100;
            // 
            // olvMt1to2
            // 
            this.olvMt1to2.HeaderFont = null;
            this.olvMt1to2.Text = "被叫节点1-2时长";
            this.olvMt1to2.Width = 100;
            // 
            // olvMt2to3
            // 
            this.olvMt2to3.HeaderFont = null;
            this.olvMt2to3.Text = "被叫节点2-3时长";
            this.olvMt2to3.Width = 100;
            // 
            // olvMt3to4
            // 
            this.olvMt3to4.HeaderFont = null;
            this.olvMt3to4.Text = "被叫节点3-4时长";
            this.olvMt3to4.Width = 100;
            // 
            // olvMt4to5
            // 
            this.olvMt4to5.HeaderFont = null;
            this.olvMt4to5.Text = "被叫节点4-5时长";
            this.olvMt4to5.Width = 100;
            // 
            // olvMtCsfbNode1
            // 
            this.olvMtCsfbNode1.HeaderFont = null;
            this.olvMtCsfbNode1.Text = "CSFB节点1：被叫发extended SR";
            this.olvMtCsfbNode1.Width = 120;
            // 
            // olvMtCsfbNode2
            // 
            this.olvMtCsfbNode2.HeaderFont = null;
            this.olvMtCsfbNode2.Text = "CSFB节点2：被叫发RR paging response";
            this.olvMtCsfbNode2.Width = 120;
            // 
            // olvMtCsfbNode3
            // 
            this.olvMtCsfbNode3.HeaderFont = null;
            this.olvMtCsfbNode3.Text = "CSFB节点3：被叫发出Alerting";
            this.olvMtCsfbNode3.Width = 120;
            // 
            // olvMtCsfbDelayTimes
            // 
            this.olvMtCsfbDelayTimes.HeaderFont = null;
            this.olvMtCsfbDelayTimes.Text = "被叫CSFB总时长";
            this.olvMtCsfbDelayTimes.Width = 80;
            // 
            // olvMtCsfb1to2
            // 
            this.olvMtCsfb1to2.HeaderFont = null;
            this.olvMtCsfb1to2.Text = "被叫CSFB节点1-2时长";
            this.olvMtCsfb1to2.Width = 100;
            // 
            // olvMtCsfb2to3
            // 
            this.olvMtCsfb2to3.HeaderFont = null;
            this.olvMtCsfb2to3.Text = "被叫CSFB节点2-3时长";
            this.olvMtCsfb2to3.Width = 100;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCloseAll,
            this.toolStripMenuItem1,
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip1";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 76);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(129, 22);
            this.miCloseAll.Text = "全部合并";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(129, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // VoNRStatDelayInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 562);
            this.Controls.Add(this.tlvVoNrItemsInfo);
            this.Name = "VoNRStatDelayInfoForm";
            this.Text = "VoNR时延信息";
            ((System.ComponentModel.ISupportInitialize)(this.tlvVoNrItemsInfo)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView tlvVoNrItemsInfo;
        private BrightIdeasSoftware.OLVColumn olvSn;
        private BrightIdeasSoftware.OLVColumn olvGridName;
        private BrightIdeasSoftware.OLVColumn olvMoPhoneNum;
        private BrightIdeasSoftware.OLVColumn olvMoNode1;
        private BrightIdeasSoftware.OLVColumn olvMoNode2;
        private BrightIdeasSoftware.OLVColumn olvMoNode3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private BrightIdeasSoftware.OLVColumn olvMoCallResault;
        private BrightIdeasSoftware.OLVColumn olvMoCallType;
        private BrightIdeasSoftware.OLVColumn olvMoNode4;
        private BrightIdeasSoftware.OLVColumn olvMoNode5;
        private BrightIdeasSoftware.OLVColumn olvMoCallDelayTimes1;
        private BrightIdeasSoftware.OLVColumn olvMo1to2;
        private BrightIdeasSoftware.OLVColumn olvMo2to3;
        private BrightIdeasSoftware.OLVColumn olvMo3to4;
        private BrightIdeasSoftware.OLVColumn olvMo4to5;
        private BrightIdeasSoftware.OLVColumn olvMoCsfbNode1;
        private BrightIdeasSoftware.OLVColumn olvMoCsfbNode2;
        private BrightIdeasSoftware.OLVColumn olvMoCsfbNode3;
        private BrightIdeasSoftware.OLVColumn olvMoCsfbDelayTimes;
        private BrightIdeasSoftware.OLVColumn olvMoCsfb1to2;
        private BrightIdeasSoftware.OLVColumn olvMoCsfb2to3;
        private BrightIdeasSoftware.OLVColumn olvMtPhoneNum;
        private BrightIdeasSoftware.OLVColumn olvMtCallResault;
        private BrightIdeasSoftware.OLVColumn olvMtCallType;
        private BrightIdeasSoftware.OLVColumn olvMtNode1;
        private BrightIdeasSoftware.OLVColumn olvMtNode2;
        private BrightIdeasSoftware.OLVColumn olvMtNode3;
        private BrightIdeasSoftware.OLVColumn olvMtNode4;
        private BrightIdeasSoftware.OLVColumn olvMtNode5;
        private BrightIdeasSoftware.OLVColumn olvMtCallDelayTimes;
        private BrightIdeasSoftware.OLVColumn olvMt1to2;
        private BrightIdeasSoftware.OLVColumn olvMt2to3;
        private BrightIdeasSoftware.OLVColumn olvMt3to4;
        private BrightIdeasSoftware.OLVColumn olvMt4to5;
        private BrightIdeasSoftware.OLVColumn olvMtCsfbNode1;
        private BrightIdeasSoftware.OLVColumn olvMtCsfbNode2;
        private BrightIdeasSoftware.OLVColumn olvMtCsfbNode3;
        private BrightIdeasSoftware.OLVColumn olvMtCsfbDelayTimes;
        private BrightIdeasSoftware.OLVColumn olvMtCsfb1to2;
        private BrightIdeasSoftware.OLVColumn olvMtCsfb2to3;
        private BrightIdeasSoftware.OLVColumn olvMoFileName;
        private BrightIdeasSoftware.OLVColumn olvMtFileName;
        private BrightIdeasSoftware.OLVColumn olvMoCallDelayTimes2;

    }
}