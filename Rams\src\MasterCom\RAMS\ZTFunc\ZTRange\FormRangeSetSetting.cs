using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Chris.Util
{
    public partial class FormRangeSetSetting : Form
    {
        public FormRangeSetSetting()
        {
            InitializeComponent();
        }

        public RangeSetSetting RangeSetSetting
        {
            get { return rangeSetSetting; }
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Button buttonOK;
            System.Windows.Forms.Button buttonCancel;
            this.rangeSetSetting = new Chris.Util.RangeSetSetting();
            buttonOK = new System.Windows.Forms.Button();
            buttonCancel = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // buttonOK
            // 
            buttonOK.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right);
            buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            buttonOK.Location = new System.Drawing.Point(259, 130);
            buttonOK.Name = "buttonOK";
            buttonOK.Size = new System.Drawing.Size(75, 23);
            buttonOK.TabIndex = 1;
            buttonOK.Text = "OK";
            buttonOK.UseVisualStyleBackColor = true;
            // 
            // buttonCancel
            // 
            buttonCancel.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right);
            buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            buttonCancel.Location = new System.Drawing.Point(259, 159);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new System.Drawing.Size(75, 23);
            buttonCancel.TabIndex = 2;
            buttonCancel.Text = "Cancel";
            buttonCancel.UseVisualStyleBackColor = true;
            // 
            // rangeSetSetting
            // 
            this.rangeSetSetting.Anchor = (((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right);
            this.rangeSetSetting.Location = new System.Drawing.Point(12, 12);
            this.rangeSetSetting.Name = "rangeSetSetting";
            this.rangeSetSetting.Size = new System.Drawing.Size(330, 177);
            this.rangeSetSetting.TabIndex = 3;
            // 
            // FormRangeSetSetting
            // 
            this.AcceptButton = buttonOK;
            this.CancelButton = buttonCancel;
            this.ClientSize = new System.Drawing.Size(346, 194);
            this.Controls.Add(buttonCancel);
            this.Controls.Add(buttonOK);
            this.Controls.Add(this.rangeSetSetting);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "FormRangeSetSetting";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = " Range Set Setting";
            this.ResumeLayout(false);

        }

        private RangeSetSetting rangeSetSetting;
    }
}
