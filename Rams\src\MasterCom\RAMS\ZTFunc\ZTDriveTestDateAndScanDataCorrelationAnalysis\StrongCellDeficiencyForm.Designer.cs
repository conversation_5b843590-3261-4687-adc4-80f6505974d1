﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class StrongCellDeficiencyForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnMaincellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaincellLacCi = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMisscellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMisscellLacCi = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAllSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAbsampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAbsamplePct = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnMaincellName);
            this.treeListView.AllColumns.Add(this.olvColumnMaincellLacCi);
            this.treeListView.AllColumns.Add(this.olvColumnMisscellName);
            this.treeListView.AllColumns.Add(this.olvColumnMisscellLacCi);
            this.treeListView.AllColumns.Add(this.olvColumnAllSampleCount);
            this.treeListView.AllColumns.Add(this.olvColumnAbsampleCount);
            this.treeListView.AllColumns.Add(this.olvColumnAbsamplePct);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnMaincellName,
            this.olvColumnMaincellLacCi,
            this.olvColumnMisscellName,
            this.olvColumnMisscellLacCi,
            this.olvColumnAllSampleCount,
            this.olvColumnAbsampleCount,
            this.olvColumnAbsamplePct});
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1034, 518);
            this.treeListView.TabIndex = 0;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnMaincellName
            // 
            this.olvColumnMaincellName.HeaderFont = null;
            this.olvColumnMaincellName.Text = "主服小区名称";
            this.olvColumnMaincellName.Width = 120;
            // 
            // olvColumnMaincellLacCi
            // 
            this.olvColumnMaincellLacCi.HeaderFont = null;
            this.olvColumnMaincellLacCi.Text = "主服小区LAC_CI";
            this.olvColumnMaincellLacCi.Width = 120;
            // 
            // olvColumnMisscellName
            // 
            this.olvColumnMisscellName.HeaderFont = null;
            this.olvColumnMisscellName.Text = "缺失小区名称";
            this.olvColumnMisscellName.Width = 120;
            // 
            // olvColumnMisscellLacCi
            // 
            this.olvColumnMisscellLacCi.HeaderFont = null;
            this.olvColumnMisscellLacCi.Text = "缺失小区LAC_CI";
            this.olvColumnMisscellLacCi.Width = 120;
            // 
            // olvColumnAllSampleCount
            // 
            this.olvColumnAllSampleCount.HeaderFont = null;
            this.olvColumnAllSampleCount.Text = "总采样点数量";
            this.olvColumnAllSampleCount.Width = 120;
            // 
            // olvColumnAbsampleCount
            // 
            this.olvColumnAbsampleCount.HeaderFont = null;
            this.olvColumnAbsampleCount.Text = "异常采样点数量";
            this.olvColumnAbsampleCount.Width = 120;
            // 
            // olvColumnAbsamplePct
            // 
            this.olvColumnAbsamplePct.HeaderFont = null;
            this.olvColumnAbsamplePct.Text = "异常采样点比例";
            this.olvColumnAbsamplePct.Width = 120;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(145, 70);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(144, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(144, 22);
            this.miCollapseAll.Text = "全部叠合";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(144, 22);
            this.miExportExcel.Text = "导出Excel(&E)";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // StrongCellDeficiencyForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1034, 518);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.treeListView);
            this.Name = "StrongCellDeficiencyForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "强信号小区缺失度";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.StrongCellDeficiencyForm_FormClosed);
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnMaincellName;
        private BrightIdeasSoftware.OLVColumn olvColumnMaincellLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnMisscellName;
        private BrightIdeasSoftware.OLVColumn olvColumnMisscellLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnAllSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsamplePct;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}