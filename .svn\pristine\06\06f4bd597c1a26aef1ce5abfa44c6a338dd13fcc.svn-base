﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model
{
    public class QueryTableStructure
    {
        private readonly CellParamTable table = null;
        private readonly string dbConnStr;
        public QueryTableStructure(string dbConnStr,CellParamTable table)
        {
            this.dbConnStr = dbConnStr;
            this.table = table;
        }

        public CellParamTable Query()
        {
            string queryString = "SELECT TOP 1 * FROM " + table.FullName + ";";
            table.Columns.Clear();
            using (SqlConnection connection = new SqlConnection(dbConnStr))
            {
                try
                {
                    SqlCommand command = new SqlCommand(queryString, connection);
                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    DataTable tb = reader.GetSchemaTable();
                    foreach (DataRow row in tb.Rows)
                    {
                        string field = string.Empty;
                        bool isValueType = false;
                        foreach (DataColumn col in tb.Columns)
                        {
                            if (col.ColumnName == "ColumnName")
                            {
                                field = row[col].ToString();
                            }
                            else if (col.ColumnName == "DataType")
                            {
                                isValueType = ((Type)row[col]).IsValueType;
                            }
                        }
                        CellParamColumn colInfo = new CellParamColumn(table);
                        colInfo.Name = field;
                        colInfo.Alias = field;
                        colInfo.IsNumericColumn = isValueType;
                        table.Columns.Add(colInfo);
                    }
                    reader.Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
            return table;
        }
    }

}
