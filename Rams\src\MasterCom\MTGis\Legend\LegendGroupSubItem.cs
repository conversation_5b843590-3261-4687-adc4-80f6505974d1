﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.MTGis
{
    public abstract class LegendGroupSubItem<T> : ILegend where T : IGis
    {
        public GraphicsPath Path
        {
            get;
            set;
        }
        public bool Visible { get; set; } = true;
        public virtual string Desc { get; set; }
        public object Tag
        { get; set; }
        public Color Color
        { get; set; }
        public abstract bool IsMatch(T obj);

        #region ILegend 成员
        public abstract void DrawOnListBox(ListBox listBox, DrawItemEventArgs e);
        #endregion
    }

    public interface ILegendGroupSubItem<in T> : ILegend where T : IGis
    {
        GraphicsPath Path
        {
            get;
            set;
        }
        string Desc { get; set; }
        object Tag
        { get; set; }
        Color Color
        { get; set; }
        bool DrawOnLayer(MapOperation map, Graphics graphics, PointF location, T entity2Draw);
    }

}
