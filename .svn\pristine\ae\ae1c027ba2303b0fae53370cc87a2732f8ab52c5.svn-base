﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTDIYLeakOutCellSetByRegion_NR : ZTDIYLeakOutCellSetByRegion
    {
        protected LeakOutAsNCellDlg_NR conditionDlg = null;
        protected NRLeakOutCellSetCondition cond = null;
        protected Dictionary<NRCell, NRLeakOutIndoorCell> indoorCellDic = new Dictionary<NRCell, NRLeakOutIndoorCell>();
        private static ZTDIYLeakOutCellSetByRegion_NR instance;

        Dictionary<LTECell, LteLeakOutIndoorCell> nrLteIndoorCDic = new Dictionary<LTECell, LteLeakOutIndoorCell>();

        public new static ZTDIYLeakOutCellSetByRegion_NR GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTDIYLeakOutCellSetByRegion_NR();
            }
            return instance;
        }

        protected ZTDIYLeakOutCellSetByRegion_NR()
        {
            isAddSampleToDTDataManager = false;
        }

        public ZTDIYLeakOutCellSetByRegion_NR(ServiceName serviceName)
            : this()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "NR外泄分析"; }
        }

        /// <summary>
        /// 设置登入的功能权限设置
        /// </summary>
        /// <returns></returns>
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35046, this.Name);
        }

        /// <summary>
        /// 对应5G的参数
        /// </summary>
        /// <returns></returns>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup sampleGroup = new DIYSampleGroup();
            sampleGroup.ThemeName = "---";
            //5G NR参数
            addParameter(sampleGroup, "NR_SSB_ARFCN");
            addParameter(sampleGroup, "NR_PCI");
            addParameter(sampleGroup, "NR_SS_RSRP");
            addParameter(sampleGroup, "NR_SS_SINR");
            addParameter(sampleGroup, "NR_TAC");
            addParameter(sampleGroup, "NR_NCI");
            addParameter(sampleGroup, "NR_NCell_RSRP");
            addParameter(sampleGroup, "NR_NCell_SINR");
            addParameter(sampleGroup, "NR_NCell_ARFCN");
            addParameter(sampleGroup, "NR_NCell_PCI");

            //5G LTE参数
            addParameter(sampleGroup, "NR_lte_EARFCN");
            addParameter(sampleGroup, "NR_lte_PCI");
            addParameter(sampleGroup, "NR_lte_RSRP");
            addParameter(sampleGroup, "NR_lte_SINR");
            addParameter(sampleGroup, "NR_lte_TAC");
            addParameter(sampleGroup, "NR_lte_ECI");
            addParameter(sampleGroup, "NR_lte_NCell_RSRP");
            addParameter(sampleGroup, "NR_lte_NCell_SINR");
            addParameter(sampleGroup, "NR_lte_NCell_EARFCN");
            addParameter(sampleGroup, "NR_lte_NCell_PCI");

            return sampleGroup;
        }

        private void addParameter(DIYSampleGroup sampleGroup, string name)
        {
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter(name);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new LeakOutAsNCellDlg_NR(true);
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)//条件设置完成，确定
            {
                this.cond = new NRLeakOutCellSetCondition(conditionDlg);
                if (!this.cond.LeakOutAsMainCell && !this.cond.LeakOutAsNCell)//没有勾选主服和计算邻区
                {
                    return false;
                }
                this.rxLevThreshold = cond.MinNCellRxlev;//邻区电平
                this.rxLevDValue = cond.DiffRxlev;//邻区与主服电平差
                return true;
            }
            return false;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            //
        }

        protected override void getResultAfterQuery()
        {
            int nrIndex = 0;
            int lteIndex = 0;
            //NR数据
            foreach (NRLeakOutIndoorCell indoorCell in this.indoorCellDic.Values)
            {
                indoorCell.SN = ++nrIndex;
                indoorCell.GetResult(this.cond.IsGetRoadDesc);
                indoorCell.SetTop5FileName();
            }

            //lte数据
            foreach (LteLeakOutIndoorCell indoorCell in this.nrLteIndoorCDic.Values)
            {
                indoorCell.SN = ++lteIndex;
                indoorCell.GetNRLteResult(this.cond.IsGetRoadDesc);
                indoorCell.SetTop5FileName();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (indoorCellDic.Values.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            NRLeakOutCellSetResultForm resultForm = MainModel.CreateResultForm(typeof(NRLeakOutCellSetResultForm)) as NRLeakOutCellSetResultForm;
            resultForm.FillData(new List<NRLeakOutIndoorCell>(indoorCellDic.Values), new List<LteLeakOutIndoorCell>(nrLteIndoorCDic.Values));
            resultForm.Visible = true;
            //初始化
            indoorCellDic = new Dictionary<NRCell, NRLeakOutIndoorCell>();
            nrLteIndoorCDic = new Dictionary<LTECell, LteLeakOutIndoorCell>();
        }

        protected override void doWithDTData(TestPoint testPoint)
        {
            if (cond.IsGetLteCell)//分析锚点Lte小区
            {
                analysisNRLteDataSC(testPoint);
            }

            float? sRsrp = (float?)testPoint["NR_SS_RSRP"];
            float? sSinr = (float?)testPoint["NR_SS_SINR"];
            NRCell sNRCell = testPoint.GetMainCell_NR();
            if (sRsrp == null)
            {
                return;
            }

            bool isLeakOutBySCell = false;
            if (this.cond.LeakOutAsMainCell && sNRCell != null && sNRCell.Type == NRBTSType.Indoor)
            {
                NRLeakOutIndoorCell indoorCell = null;
                if (!indoorCellDic.TryGetValue(sNRCell, out indoorCell))
                {
                    indoorCell = new NRLeakOutIndoorCell(sNRCell);
                    indoorCellDic.Add(sNRCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)sRsrp, sSinr, true);
                isLeakOutBySCell = true;
            }

            if (this.cond.LeakOutAsNCell && !isLeakOutBySCell)//添加邻区小区数据
            {
                addNCell(testPoint, sRsrp);
            }
        }

        private void addNCell(TestPoint testPoint, float? sRsrp)
        {
            for (int i = 0; i < 12; ++i)
            {
                float? nRsrp = (float?)testPoint["NR_NCell_RSRP", i];
                if (nRsrp == null || nRsrp < this.cond.MinNCellRxlev || sRsrp - nRsrp > this.cond.DiffRxlev)
                {
                    break;
                }

                float? nSinr = (float?)testPoint["NR_NCell_SINR", i];
                NRCell nLteCell = testPoint.GetNBCell_NR(i);
                if (nLteCell == null || nLteCell.Type != NRBTSType.Indoor)
                {
                    continue;
                }

                NRLeakOutIndoorCell indoorCell = null;
                if (!indoorCellDic.TryGetValue(nLteCell, out indoorCell))
                {
                    indoorCell = new NRLeakOutIndoorCell(nLteCell);
                    indoorCellDic.Add(nLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)nRsrp, nSinr, false);
            }
        }

        /// <summary>
        /// 分析主服锚点lte数据
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        protected bool analysisNRLteDataSC(TestPoint testPoint)
        {
            float? rsrp = (float?)testPoint["NR_lte_RSRP"];
            float? sinr = (float?)testPoint["NR_lte_SINR"];
            if (rsrp == null || rsrp < -141 || rsrp > 25)//校验采样点的合法性
            {
                return false;
            }

            LTECell mainCell = testPoint.GetMainCell_LTE(true);
            if (mainCell != null && mainCell.Type == LTEBTSType.Indoor)//判断是否为室内小区
            {
                addLeakoutNRLteCell(mainCell, testPoint, rsrp, sinr);
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 添加室分外泄LTE数据
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="testPoint"></param>
        /// <param name="bNBCell"></param>
        protected void addLeakoutNRLteCell(LTECell cell, TestPoint testPoint, float? rsrp, float? sinr)
        {
            float? sRsrp = rsrp;
            float? sSinr = sinr;
            if (sRsrp == null)
            {
                return;
            }

            //室分lte
            if (this.cond.LeakOutAsMainCell && cell != null && cell.Type == LTEBTSType.Indoor)
            {
                LteLeakOutIndoorCell indoorCell = null;
                if (!nrLteIndoorCDic.TryGetValue(cell, out indoorCell))
                {
                    indoorCell = new LteLeakOutIndoorCell(cell);
                    nrLteIndoorCDic.Add(cell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)sRsrp, sSinr, true);
            }
            //增加邻区数据
            if (this.cond.LeakOutAsNCell)
            {
                addNLteCell(testPoint, sRsrp);
            }
        }

        private void addNLteCell(TestPoint testPoint, float? sRsrp)
        {
            for (int i = 0; i < 12; ++i)
            {
                float? nRsrp = (float?)testPoint["NR_lte_NCell_RSRP", i];
                if (nRsrp == null || nRsrp < this.cond.MinNCellRxlev || sRsrp - nRsrp > this.cond.DiffRxlev)
                {
                    break;
                }

                float? nSinr = (float?)testPoint["NR_lte_NCell_SINR", i];

                LTECell nLteCell = testPoint.GetNBCell_LTE(true, i);
                if (nLteCell == null || nLteCell.Type != LTEBTSType.Indoor)
                {
                    continue;
                }

                LteLeakOutIndoorCell indoorCell = null;
                if (!nrLteIndoorCDic.TryGetValue(nLteCell, out indoorCell))
                {
                    indoorCell = new LteLeakOutIndoorCell(nLteCell);
                    nrLteIndoorCDic.Add(nLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)nRsrp, nSinr, false);
            }
        }
    }

    /// <summary>
    /// 设置条件类
    /// </summary>
    public class NRLeakOutCellSetCondition
    {
        public bool LeakOutAsMainCell { get; set; }//是否只计算主服小区

        public bool LeakOutAsNCell { get; set; }//是否同时计算邻区

        public bool IsGetRoadDesc { get; set; }//是否覆盖道路信息

        public bool IsGetLteCell { get; set; }//是否分析锚点Lte小区

        public int MinNCellRxlev { get; set; } = -85;//邻区电平

        public int DiffRxlev { get; set; } = 10;//邻区与主服电平差
        public NRLeakOutCellSetCondition()
        {
        }
        public NRLeakOutCellSetCondition(LeakOutAsNCellDlg_NR condDlg)
        {
            LeakOutAsMainCell = condDlg.LeakOutAsMainCell;
            LeakOutAsNCell = condDlg.LeakOutAsNBCell;
            MinNCellRxlev = condDlg.RxLevThreshold;
            DiffRxlev = condDlg.RxLevDValue;
            IsGetRoadDesc = condDlg.getRoadDesc;
            IsGetLteCell = condDlg.IsGetLteCell;
        }
    }

    public class NRLeakOutCell
    {
        public NRCell NRCell
        {
            get;
            protected set;
        }

        public string CellName
        {
            get { return NRCell.Name; }
        }

        public int Tac
        {
            get;
            protected set;
        }

        public long Nci
        {
            get;
            protected set;
        }

        public int Earfcn
        {
            get;
            protected set;
        }

        public int Pci
        {
            get;
            protected set;
        }

        public double AvgRsrp
        {
            get;
            protected set;
        }

        public double MinRsrp
        {
            get;
            protected set;
        }

        public double MaxRsrp
        {
            get;
            protected set;
        }

        public string AvgSinrStr
        {
            get;
            protected set;
        }

        public string MinSinrStr
        {
            get;
            protected set;
        }

        public string MaxSinrStr
        {
            get;
            protected set;
        }

        public double AvgDistance
        {
            get;
            protected set;
        }

        public string RoadDesc
        {
            get;
            protected set;
        }

        public int SampleCount
        {
            get;
            protected set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            protected set;
        }

        public NRLeakOutCell(NRCell NRCell)
        {
            this.NRCell = NRCell;
            this.Tac = NRCell.TAC;
            this.Nci = NRCell.NCI;
            this.Earfcn = NRCell.SSBARFCN;
            this.Pci = NRCell.PCI;
            this.TestPoints = new List<TestPoint>();
            this.MinRsrp = double.MaxValue;
            this.MaxRsrp = double.MinValue;
        }

        public void AddTestPoint(TestPoint tp, float rsrp, float? sinr)
        {
            TestPoints.Add(tp);
            ++SampleCount;

            MinRsrp = Math.Min(MinRsrp, rsrp);
            MaxRsrp = Math.Max(MaxRsrp, rsrp);
            this.sumRsrp += rsrp;

            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                float sinrValue = (float)sinr;
                minSinr = Math.Min(minSinr, sinrValue);
                maxSinr = Math.Max(maxSinr, sinrValue);
                sumSinr += sinrValue;
                ++sinrCnt;
            }
        }

        public virtual void GetResult(bool isGetRoadInfo)
        {
            AvgRsrp = SampleCount == 0 ? 0 : sumRsrp / SampleCount;
            avgSinr = sinrCnt == 0 ? 0 : sumSinr / sinrCnt;
            AvgSinrStr = sinrCnt == 0 ? "" : Math.Round(avgSinr, 2).ToString();
            MinSinrStr = sinrCnt == 0 ? "" : Math.Round(minSinr, 2).ToString();
            MaxSinrStr = sinrCnt == 0 ? "" : Math.Round(maxSinr, 2).ToString();

            double distance = 0;
            foreach (TestPoint tp in TestPoints)
            {
                distance += MathFuncs.GetDistance(NRCell.Longitude, NRCell.Latitude, tp.Longitude, tp.Latitude);
            }
            AvgDistance = SampleCount == 0 ? 0 : distance / SampleCount;

            if (isGetRoadInfo && SampleCount != 0)
            {
                TestPoint midPoint = TestPoints[TestPoints.Count / 2];
                RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(midPoint.Longitude, midPoint.Latitude);
            }
        }

        protected double sumRsrp = 0;
        protected double sumSinr = 0;
        protected double minSinr = double.MaxValue;
        protected double maxSinr = double.MinValue;
        protected double avgSinr = 0;
        protected int sinrCnt = 0;
    }

    public class NRLeakOutOutdoorCell : NRLeakOutCell, IComparable<NRLeakOutOutdoorCell>
    {
        public NRLeakOutOutdoorCell(NRCell NrCell)
            : base(NrCell)
        {
        }

        public int SN
        {
            get;
            set;
        }

        public int CompareTo(NRLeakOutOutdoorCell other)
        {
            if (this == other || this.AvgRsrp == other.AvgRsrp) return 0;
            return this.AvgRsrp > other.AvgRsrp ? 1 : -1;
        }
    }

    public class NRLeakOutIndoorCell : NRLeakOutCell
    {
        public NRLeakOutIndoorCell(NRCell NrCell)
            : base(NrCell)
        {
            OutdoorCells = new List<NRLeakOutOutdoorCell>();
            TpFiles = new Dictionary<string, int>();
        }

        #region 属性字段
        public int SN
        {
            get;
            set;
        }

        public int SCellSampleCount
        {
            get;
            private set;
        }

        public int NCellSampleCount
        {
            get;
            private set;
        }

        public int SinrGT4SampleCount // sinr great than 4
        {
            get;
            private set;
        }

        public int SinrLT0SampleCount // sinr less than 0
        {
            get;
            private set;
        }

        public string Top5FileName
        {
            get;
            private set;
        }

        public List<NRLeakOutOutdoorCell> OutdoorCells
        {
            get;
            private set;
        }

        public Dictionary<string, int> TpFiles
        {
            get;
            private set;
        }
        #endregion

        public void SetTop5FileName()
        {
            Dictionary<string, int> nTpFiles = TpFiles;
            int fileCount = 0;

            if (TpFiles.Count <= 5)
                fileCount = TpFiles.Count;
            else
                fileCount = 5;

            for (int i = 0; i < fileCount; i++)
            {
                string fileName = findMax(nTpFiles);
                Top5FileName = string.Format(Top5FileName + fileName + "; ");
                nTpFiles.Remove(fileName);
            }
        }

        private string findMax(Dictionary<string, int> dic)
        {
            string MaxKey = string.Empty;
            foreach (KeyValuePair<string, int> item in dic)
            {
                if (MaxKey == string.Empty)
                    MaxKey = item.Key;
                else
                {
                    if (dic[MaxKey] < item.Value)
                        MaxKey = item.Key;
                }
            }
            return MaxKey;
        }

        public void AddTestPoint(TestPoint tp, float rsrp, float? sinr, bool isSCell)
        {
            base.AddTestPoint(tp, rsrp, sinr);

            if (TpFiles.ContainsKey(tp.FileName))
                TpFiles[tp.FileName]++;
            else
                TpFiles.Add(tp.FileName, 0);

            if (isSCell)
            {
                ++SCellSampleCount;
            }
            else
            {
                ++NCellSampleCount;
            }
            if (sinr != null)
            {
                if (sinr > 4)
                    ++SinrGT4SampleCount;
                if (sinr <= 0)
                    ++SinrLT0SampleCount;
            }
        }

        public override void GetResult(bool isGetRoadInfo)
        {
            base.GetResult(isGetRoadInfo);

            Dictionary<NRCell, NRLeakOutOutdoorCell> outCellDic = new Dictionary<NRCell, NRLeakOutOutdoorCell>();
            foreach (TestPoint tp in TestPoints)
            {
                NRCell sCell = tp.GetMainCell_NR();
                float? sRsrp = (float?)tp["NR_SS_RSRP"];
                float? sSinr = (float?)tp["NR_SS_SINR"];
                addCellInfo(outCellDic, tp, sCell, sRsrp, sSinr);

                for (int i = 0; i < 12; ++i)
                {
                    NRCell nCell = tp.GetNBCell_NR(i);
                    float? nRsrp = (float?)tp["NR_NCell_RSRP"];
                    float? nSinr = (float?)tp["NR_NCell_SINR"];
                    addCellInfo(outCellDic, tp, nCell, nRsrp, nSinr);
                }
            }

            OutdoorCells.AddRange(outCellDic.Values);
            OutdoorCells.Sort();
            int sn = 0;
            foreach (NRLeakOutOutdoorCell outCell in OutdoorCells)
            {
                outCell.GetResult(isGetRoadInfo);
                outCell.SN = ++sn;
            }
        }

        protected void addCellInfo(Dictionary<NRCell, NRLeakOutOutdoorCell> outCellDic, TestPoint tp, NRCell cell, float? rsrp, float? sinr)
        {
            if (rsrp != null && cell != null && cell.Type == NRBTSType.Outdoor)
            {
                NRLeakOutOutdoorCell outCell = null;
                if (!outCellDic.TryGetValue(cell, out outCell))
                {
                    outCell = new NRLeakOutOutdoorCell(cell);
                    outCellDic.Add(cell, outCell);
                }
                outCell.AddTestPoint(tp, (float)rsrp, sinr);
            }
        }
    }

}
