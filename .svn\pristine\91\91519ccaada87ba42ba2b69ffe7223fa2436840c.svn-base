﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using MasterCom;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class CityInfo
    {
        public int serverid { get; set; }
        public string servername { get; set; }
        public string serverip { get; set; }
        public string serverport { get; set; }
        public string maindbname { get; set; }
        public string username { get; set; }
        public string userpwd { get; set; }
        public string comment { get; set; }
        public string lwtdbstr { get; set; }
        public string CServerIP { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }

        public void Fill(SqlDataReader reader)
        {
            int idx = 0;
            serverid = reader.GetInt32(idx++);//地市ID
            servername = reader.GetString(idx++);
            serverip = reader.GetString(idx++);
            serverport = reader.GetString(idx++);
            maindbname = reader.GetString(idx++);//数据库名
            username = reader.GetString(idx++);
            userpwd = reader.GetString(idx++);
            comment = reader.GetString(idx++);
            lwtdbstr = reader.GetString(idx++);
            CServerIP = reader.GetString(idx);//后台算法地址
        }

        public string GetDBConn()
        {
            return string.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***",
                   this.serverip, this.maindbname, this.username, this.userpwd);
        }

        public string GetFiaDBConn()
        {
            return this.serverid.ToString() + "|" + GetDBConn();
        }

        public override string ToString()
        {
            return servername;
        }
    }

    public class DiyQueryCityInfo : DIYSQLBase
    {
        public List<CityInfo> CityInfos { get; set; }

        public DiyQueryCityInfo(MainModel mainModel)
            : base(mainModel)
        {
            CityInfos = new List<CityInfo>();
        }

        protected override string getSqlTextString()
        {
            return "SELECT distinct serverid,servername,serverip,serverport,maindbname,username,userpwd,comment,lwtdbstr,CServerIP " + "\r\n" +
                             " FROM tb_cfg_sys_server_fso WHERE comment='td'";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[11];
            vType[0] = E_VType.E_Int;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_String;
            vType[3] = E_VType.E_String;
            vType[4] = E_VType.E_String;
            vType[5] = E_VType.E_String;
            vType[6] = E_VType.E_String;
            vType[7] = E_VType.E_String;
            vType[8] = E_VType.E_String;
            vType[9] = E_VType.E_String;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CityInfo cityinfo = new CityInfo();
                    cityinfo.serverid = package.Content.GetParamInt();//地市ID
                    cityinfo.servername = package.Content.GetParamString();
                    cityinfo.serverip = package.Content.GetParamString();
                    cityinfo.serverport = package.Content.GetParamString();
                    cityinfo.maindbname = package.Content.GetParamString();//数据库名
                    cityinfo.username = package.Content.GetParamString();
                    cityinfo.userpwd = package.Content.GetParamString();
                    cityinfo.comment = package.Content.GetParamString();
                    cityinfo.lwtdbstr = package.Content.GetParamString();
                    cityinfo.CServerIP = package.Content.GetParamString();//后台算法地址
                    CityInfos.Add(cityinfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DIYQueryCityInfo"; }
        }
    }

    public class DiyQueryCityInfoFixDB
    {
        public DiyQueryCityInfoFixDB()
        {
            CityInfoList = new List<CityInfo>();
        }

        readonly string strConnDB = string.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***",
            DbConfig.GetInstance().CurDb.DBServer,
            DbConfig.GetInstance().CurDb.DBName,
            DbConfig.GetInstance().CurDb.DBUser,
            DbConfig.GetInstance().CurDb.DBPassword);
        public List<CityInfo> CityInfoList { get; set; }

        public void Query()
        {
            WaitBox.Show("正在查询地市信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "SELECT distinct serverid,servername,serverip,serverport,maindbname,username,userpwd,comment,lwtdbstr,CServerIP " + "\r\n" +
                             " FROM tb_cfg_sys_server_fso WHERE comment='lte'";

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        CityInfo cityinfo = new CityInfo();
                        cityinfo.Fill(reader);
                        CityInfoList.Add(cityinfo);
                    }
                }
            }
            finally
            {
                Thread.Sleep(100);
                WaitBox.Close();
            }
        }
    }

    public class DbConfig
    {
        private readonly string filePath = Application.StartupPath + "\\config\\PCIOptConfig.xml";

        public List<CDb> DbVec { get; set; }
        public CDb CurDb { get; set; }

        private DbConfig()
        {
            DbVec = new List<CDb>();
            load();
        }

        private static object obj = new object();
        private static DbConfig instance = null;
        public static DbConfig GetInstance()
        {
            if (instance == null)
            {
                lock (obj)
                {
                    if (instance == null)
                    {
                        instance = new DbConfig();
                    }
                }
            }
            return instance;
        }

        private void load()
        {
            XmlConfigFile file = new XmlConfigFile(filePath);
            XmlElement dbElement = file.GetConfig("DbConfig");
            CDb db = CDb.GetDb(file, dbElement, "");
            if (db != null)
            {
                db.DBPassword = DecryptDES(db.DBPassword, strKey);
                DbVec.Add(db);
            }

            for (int idx = 2; idx <= 10; idx++)
            {
                db = CDb.GetDb(file, dbElement, idx.ToString());
                if(db == null)
                    break;
                db.DBPassword = DecryptDES(db.DBPassword, strKey);
                DbVec.Add(db);
            }
        }

        //DES加密
        public static string EncryptDES(string encryptString, string encryptKey)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(encryptKey.Substring(0, 8));
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Encoding.UTF8.GetBytes(encryptString);
                DESCryptoServiceProvider dCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, dCSP.CreateEncryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Convert.ToBase64String(mStream.ToArray());
            }
            catch
            {
                return encryptString;
            }
        }

        //DES解密
        public static string DecryptDES(string decryptString, string decryptKey)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(decryptKey);
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Convert.FromBase64String(decryptString);
                DESCryptoServiceProvider DCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, DCSP.CreateDecryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Encoding.UTF8.GetString(mStream.ToArray());
            }
            catch
            {
                return decryptString;
            }
        }

        //默认密钥向量
        private static byte[] Keys = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
        private static string strKey = "mtsystem";
    }

    public class CDb
    {
        public string DBServer { get; set; }
        public string DBName { get; set; }
        public string DBUser { get; set; }
        public string DBPassword { get; set; }

        public override string ToString()
        {
            return DBServer;
        }

        public static CDb GetDb(XmlConfigFile file, XmlElement dbElement, string extra)
        {
            CDb db = new CDb();

            db.DBServer = file.GetItemValue(dbElement, Dbserver + extra) as string;
            if (db.DBServer == null) return null;

            db.DBName = file.GetItemValue(dbElement, Dbname + extra) as string;
            if (db.DBName == null) return null;

            db.DBUser = file.GetItemValue(dbElement, Dbuser + extra) as string;
            if (db.DBUser == null) return null;

            db.DBPassword = file.GetItemValue(dbElement, Dbpassword + extra) as string;
            if (db.DBPassword == null) return null;

            return db;
        }

        public const string Dbserver = "dbserver";
        public const string Dbname = "dbname";
        public const string Dbuser = "dbuser";
        public const string Dbpassword = "dbpassword";
    }
}
