﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HandoverCountStatResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn3 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn4 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn5 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn6 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn7 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn8 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn9 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn10 = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsMenuExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumn1);
            this.objectListView.AllColumns.Add(this.olvColumn2);
            this.objectListView.AllColumns.Add(this.olvColumn3);
            this.objectListView.AllColumns.Add(this.olvColumn4);
            this.objectListView.AllColumns.Add(this.olvColumn5);
            this.objectListView.AllColumns.Add(this.olvColumn6);
            this.objectListView.AllColumns.Add(this.olvColumn7);
            this.objectListView.AllColumns.Add(this.olvColumn8);
            this.objectListView.AllColumns.Add(this.olvColumn9);
            this.objectListView.AllColumns.Add(this.olvColumn10);
            this.objectListView.AllowColumnReorder = true;
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1,
            this.olvColumn2,
            this.olvColumn3,
            this.olvColumn4,
            this.olvColumn5,
            this.olvColumn6,
            this.olvColumn7,
            this.olvColumn8,
            this.olvColumn9,
            this.olvColumn10});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip1;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(848, 432);
            this.objectListView.TabIndex = 0;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "SrcCellName";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "源小区名";
            this.olvColumn1.Width = 100;
            // 
            // olvColumn2
            // 
            this.olvColumn2.AspectName = "SrcLacStr";
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "源LAC";
            // 
            // olvColumn3
            // 
            this.olvColumn3.AspectName = "SrcCiStr";
            this.olvColumn3.HeaderFont = null;
            this.olvColumn3.Text = "源CI";
            // 
            // olvColumn4
            // 
            this.olvColumn4.AspectName = "TarCellName";
            this.olvColumn4.HeaderFont = null;
            this.olvColumn4.Text = "目标小区名";
            this.olvColumn4.Width = 100;
            // 
            // olvColumn5
            // 
            this.olvColumn5.AspectName = "TarLacStr";
            this.olvColumn5.HeaderFont = null;
            this.olvColumn5.Text = "目标LAC";
            // 
            // olvColumn6
            // 
            this.olvColumn6.AspectName = "TarCiStr";
            this.olvColumn6.HeaderFont = null;
            this.olvColumn6.Text = "目标CI";
            // 
            // olvColumn7
            // 
            this.olvColumn7.AspectName = "HandoverRequestCount";
            this.olvColumn7.HeaderFont = null;
            this.olvColumn7.Text = "切换请求次数";
            this.olvColumn7.Width = 100;
            // 
            // olvColumn8
            // 
            this.olvColumn8.AspectName = "HandoverFailedCount";
            this.olvColumn8.HeaderFont = null;
            this.olvColumn8.Text = "切换失败次数";
            this.olvColumn8.Width = 100;
            // 
            // olvColumn9
            // 
            this.olvColumn9.AspectName = "HandoverSucceedCount";
            this.olvColumn9.HeaderFont = null;
            this.olvColumn9.Text = "切换成功次数";
            this.olvColumn9.Width = 100;
            // 
            // olvColumn10
            // 
            this.olvColumn10.AspectName = "EventDesc";
            this.olvColumn10.HeaderFont = null;
            this.olvColumn10.Text = "切换类型";
            this.olvColumn10.Width = 80;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsMenuExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(153, 48);
            // 
            // tsMenuExportExcel
            // 
            this.tsMenuExportExcel.Name = "tsMenuExportExcel";
            this.tsMenuExportExcel.Size = new System.Drawing.Size(152, 22);
            this.tsMenuExportExcel.Text = "导出Excel...";
            // 
            // HandoverCountStatResultForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(848, 432);
            this.Controls.Add(this.objectListView);
            this.Name = "HandoverCountStatResultForm";
            this.Text = "TD切换次数统计";
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private BrightIdeasSoftware.OLVColumn olvColumn3;
        private BrightIdeasSoftware.OLVColumn olvColumn4;
        private BrightIdeasSoftware.OLVColumn olvColumn5;
        private BrightIdeasSoftware.OLVColumn olvColumn6;
        private BrightIdeasSoftware.OLVColumn olvColumn7;
        private BrightIdeasSoftware.OLVColumn olvColumn8;
        private BrightIdeasSoftware.OLVColumn olvColumn9;
        private BrightIdeasSoftware.OLVColumn olvColumn10;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem tsMenuExportExcel;
    }
}