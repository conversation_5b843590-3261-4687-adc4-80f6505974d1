﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Compare;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_COMPBENCH_UNIT_INFO = 0x61;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_COMPBENCH_UNIT_INFO = 0X62;
        public const byte RESTYPE_COMPBENCH_DATE = 0X61;
    }
    /// <summary>
    /// 基于历史数据的竞争对比分析
    /// </summary>
    public abstract class CompHisGridQuery :QueryBase
    {
        protected CompHisGridQuery(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override bool isValidCondition()
        {
            int periodCount = Condition.Periods.Count;
            return periodCount == 1;
        }
        CompHisCondDlg hisCondDlg = null;
        ComHisCond curCond = null;
        protected override void query()
        {
            if(hisCondDlg==null)
            {
                hisCondDlg = new CompHisCondDlg();
            }
            curCond = null;
            if(hisCondDlg.ShowDialog()== DialogResult.OK)
            {
                curCond = hisCondDlg.GetHisCond();
            }
            if(curCond!=null)
            {
                Condition.Periods.Clear();
                Condition.Periods.Add(new TimePeriod(curCond.dateValueFrom, curCond.dateValueTo));
                ClientProxy clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                try
                {
                    WaitBox.Show("开始接收数据...", queryInThread, clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }

                MainModel.FireCompareHisGridQueried(this);  
            }
            
        }
        private void queryInThread(object o)
        {
            try{
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                if (periodCount != 1)
                {
                    return;
                }
                TimePeriod period = Condition.Periods[0];
                List<CompUnit> hisCompUnitList = new List<CompUnit>();
                WaitBox.Text = "开始获取竞争对比历史数据";
                prepareQueryPackage(package, period);//
                clientProxy.Send();
                recieveInfo(clientProxy, hisCompUnitList);
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                if(MainModel.CurGridCoverData!=null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
                if (MainModel.CompUnitHisData != null)
                {
                    MainModel.CompUnitHisData.Clear();
                }
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.CompUnitHisData = hisCompUnitList;
                WaitBox.Text = "数据获取完毕";
            }catch(Exception e){
                log.Error("Error:"+e.Message);
            }finally{
                WaitBox.Close();
            }
        }
        
        protected virtual void recieveInfo(ClientProxy clientProxy, List<CompUnit> compUnitList)
        {
            List<DateResult> dateResultList = new List<DateResult>();
            Dictionary<int, CompUnit> compUnitDic = new Dictionary<int, CompUnit>();
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                if (package.Content.Type == ResponseType.RESTYPE_COMPBENCH_UNIT_INFO)
                {
                    CompUnit cu = CompUnit.FillFrom(package.Content);
                    if (isValidPoint(cu))
                    {
                        compUnitDic[cu.id] = cu;
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_COMPBENCH_DATE)
                {
                    DateResult dr = DateResult.FillFrom(package.Content);
                    if (isValidPeriod(dr))
                    {
                        dateResultList.Add(dr);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    System.Console.Write("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref counter, ref curPercent);
            }
            addCompUnit(compUnitList, dateResultList, compUnitDic);
        }

        private void addCompUnit(List<CompUnit> compUnitList, List<DateResult> dateResultList, Dictionary<int, CompUnit> compUnitDic)
        {
            foreach (DateResult dr in dateResultList)
            {
                CompUnit cu = null;
                if (compUnitDic.TryGetValue(dr.unit_id, out cu))
                {
                    cu.testDates.Add(dr);
                }
            }
            foreach (CompUnit cu in compUnitDic.Values)
            {
                if (cu.testDates.Count > 0)
                {
                    compUnitList.Add(cu);
                }
            }
        }

        private bool isValidPeriod(DateResult dr)
        {
            if(dr.dateValue>=curCond.fromValue && dr.dateValue<=curCond.toValue)
            {
                return true;
            }
            return false;
            
        }

        protected virtual void prepareQueryPackage(Package package, TimePeriod period)
        {
        }
        protected virtual bool isValidPoint(CompUnit data)
        {
            return true;
        }

    }
}
