﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;
using DevExpress.XtraEditors;
using MasterCom.RAMS.KPI_Statistics;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTGDGridCompareStat : DIYAnalyseFilesOneByOneByRegion
    {
        public ZTGDGridCompareStat(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            FilterSampleByRegion = true;
            IncludeEvent = false;
        }
        public override string Name
        {
            get { return "LTE测试轨迹对比栅格化(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20045, this.Name);
        }

        #region 全局变量
        string strCityName = "";
        TestPoint preTestPoint = null;
        double dSpanTime = 3;
        int iSpanTimeNum = 8;
        bool isHost = true;
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        Dictionary<string, MapWinGIS.Shape> gridShapDic = null;
        Dictionary<string, MapWinGIS.Shape> gridTestShapDic = null;
        Dictionary<int, string> cellEciCellNameDic = null;
        Dictionary<GridCompareLongLat, int> gridRoadHostSampleDic = null;
        Dictionary<GridCompareLongLat, List<int>> gridRoadGuestSampleDic = null;
        Dictionary<GridCellKey, GridCellTestPointInfo> iPreECITestPointTakeUpDic = null;
        Dictionary<GridCellKey, GridCellTestPointInfo> iCurECITestPointTakeUpDic = null;
        Dictionary<TimeGridCellKey, TimeSpandGridCellInfo> timeSpanGridCellInfoDic = null;
        Dictionary<int, Dictionary<string, List<CellOutOfServiceAlarmInfo>>> cellEciAlarmInfoDic = null;
        Dictionary<GridTimePeriod, Dictionary<GridCompareLongLat, Dictionary<int, List<TestPointExtend>>>> timeGridCellTestPointDic = null;
        #endregion

        protected override void query()
        {
            ZTGDQueryKPIByCompareGrid compareGridKpiStat = new ZTGDQueryKPIByCompareGrid();
            compareGridKpiStat.isHost = false;
            if (!compareGridKpiStat.getSelectReport(condition) || !getCondition())
            {
                return;
            }
            dSpanTime = compareGridKpiStat.dTimeSpan;
            iSpanTimeNum = (int)(24 / dSpanTime);

            queryTestPointData();

            WaitBox.Show("处理小区占用集合情况 ...", doWithTakeUpDes);
            WaitBox.Show("正在查询小区退服告警信息，请稍后 ...", doQueryCellAlarmInfo);
            WaitBox.Show("处理小区关联分析情况 ...", doWithCellTakeUpDetails);

            fireShowForm();
            releaseSource();

            changeGridToShap();
            if (!compareGridKpiStat.isOnlyCellAna)
            {
                doWithKpiStatByCompareGrid(compareGridKpiStat);
            }
            else
            {
                changShapToResvRegion(StatMode.NotRoundStat);
                compareGridKpiStat.initReportName();
            }
        }

        private void queryTestPointData()
        {
            getReadyBeforeQuery();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.Columns = getColumns();
            initClar();

            queryFileToAnalyse();
            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);

            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
            MainModel.FireDTDataChanged(this);
        }

        private void initClar()
        {
            InitRegionMop2();
            gridShapDic = new Dictionary<string, MapWinGIS.Shape>();
            gridTestShapDic = new Dictionary<string, MapWinGIS.Shape>();
            cellEciCellNameDic = new Dictionary<int, string>();
            gridRoadHostSampleDic = new Dictionary<GridCompareLongLat, int>();
            gridRoadGuestSampleDic = new Dictionary<GridCompareLongLat, List<int>>();
            iPreECITestPointTakeUpDic = new Dictionary<GridCellKey, GridCellTestPointInfo>();
            iCurECITestPointTakeUpDic = new Dictionary<GridCellKey, GridCellTestPointInfo>();
            timeSpanGridCellInfoDic = new Dictionary<TimeGridCellKey, TimeSpandGridCellInfo>();
            cellEciAlarmInfoDic = new Dictionary<int,Dictionary<string,List<CellOutOfServiceAlarmInfo>>>();
            timeGridCellTestPointDic = new Dictionary<GridTimePeriod, Dictionary<GridCompareLongLat, Dictionary<int, List<TestPointExtend>>>>();
        }

        private List<string> getColumns()
        {
            List<string> columnsDef = new List<string>();
            columnsDef.Add("lte_TAC");
            columnsDef.Add("lte_ECI");
            columnsDef.Add("lte_EARFCN");
            columnsDef.Add("lte_PCI");
            return columnsDef;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                condition.Periods.Sort(delegate(TimePeriod a, TimePeriod b) { return a.BeginTime.CompareTo(b.BeginTime); });
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    if (testPointList.Count > 0 && condition.Periods[0].Contains(testPointList[0].DateTime))
                        isHost = false;
                    else
                        isHost = true;
                    if (!ZTGDQueryKPIByCompareGrid.IFileIdDic.ContainsKey(fileDataManager.FileID))
                    {
                        ZTGDQueryKPIByCompareGrid.IFileIdDic.Add(fileDataManager.FileID, 1);
                    }
                    if (testPointList.Count > 0)
                    {
                        preTestPoint = testPointList[0];
                    }
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        doWithDTData(testPointList[i]);
                        preTestPoint = testPointList[i];
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        protected virtual void doWithDTData(TestPoint tp)
        {
            int? iECI = (int?)tp["lte_ECI"];
            if (iECI == null)
                iECI = (int?)-99999;

            string strGrid = isContainPoint(tp.Longitude, tp.Latitude);
            double dis = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, preTestPoint.Longitude, preTestPoint.Latitude);
            string strDate = tp.DateTime.ToString("yyyyMMdd");           
            if (isHost)
            {
                addTPTakeUpDic(tp, iECI, strGrid, dis, strDate, iCurECITestPointTakeUpDic, "本轮");
            } 
            else
            {
                addTPTakeUpDic(tp, iECI, strGrid, dis, strDate, iPreECITestPointTakeUpDic, "上轮");
            }
            doWithGridLongLat(tp, (int)iECI, strGrid, dis);
        }

        private void addTPTakeUpDic(TestPoint tp, int? iECI, string strGrid, double dis, string strDate, Dictionary<GridCellKey, GridCellTestPointInfo> testPointTakeUpDic, string str)
        {
            if ((int)iECI > 0 && strGrid != "")
            {
                GridCellKey gridCellKey = new GridCellKey(strGrid, (int)iECI);
                if (!testPointTakeUpDic.ContainsKey(gridCellKey))
                {
                    testPointTakeUpDic[gridCellKey] = new GridCellTestPointInfo();
                    testPointTakeUpDic[gridCellKey].IECI = (int)iECI;
                    testPointTakeUpDic[gridCellKey].ISN = iCurECITestPointTakeUpDic.Count + testPointTakeUpDic.Count;
                    testPointTakeUpDic[gridCellKey].StrCityName = strCityName;
                    testPointTakeUpDic[gridCellKey].StrDataSource = str;
                    testPointTakeUpDic[gridCellKey].StrGridName = gridCellKey.StrGrid;
                }
                if (testPointTakeUpDic[gridCellKey].StrCellName == "")
                {
                    LTECell lteCell = tp.GetMainCell_LTE();
                    if (lteCell != null)
                        testPointTakeUpDic[gridCellKey].StrCellName = lteCell.Name;
                }
                testPointTakeUpDic[gridCellKey].AddTestPoint(dis, strDate);
            }
        }

        private GridTimePeriod getTimePeriodByTime(DateTime dDateTime)
        {
            DateTime dDateDate = Convert.ToDateTime(dDateTime.ToShortDateString());
            for (int i = 0; i < iSpanTimeNum; i++)
            {
                DateTime dStartTime = dDateDate.AddMinutes(i * dSpanTime * 60);
                DateTime dEndTime = dDateDate.AddMinutes((i + 1) * dSpanTime * 60).AddSeconds(-1).AddMilliseconds(999);
                GridTimePeriod timePeriod = new GridTimePeriod(dStartTime, dEndTime);
                if (timePeriod.Contains(dDateTime))
                {
                    return timePeriod;
                }
            }
            return null;
        }

        private List<GridCompareLongLat> getGridCompareLongLatListByECI(int iECI)
        {
            List<GridCompareLongLat> gridCompareLongLatList = new List<GridCompareLongLat>();
            foreach (GridCompareLongLat gridCompareLongLat in gridRoadGuestSampleDic.Keys)
            {
                if (gridRoadGuestSampleDic[gridCompareLongLat].Contains(iECI))
                {
                    gridCompareLongLatList.Add(gridCompareLongLat);
                }
            }
            return gridCompareLongLatList;
        }

        private void doWithGridLongLat(TestPoint tp, int iECI, string strGrid, double dis)
        {
            GridCompareLongLat gridLongLat = new GridCompareLongLat((int)(tp.Longitude * 10000000), (int)(tp.Latitude * 10000000), 40);      
            if (isHost)
            {
                if (!gridRoadHostSampleDic.ContainsKey(gridLongLat))
                {
                    gridRoadHostSampleDic[gridLongLat] = 1;
                }
                TestPointExtend tpe = new TestPointExtend(tp, strGrid, dis);
                GridTimePeriod timePeriod = getTimePeriodByTime(tp.DateTime);
                if (!timeGridCellTestPointDic.ContainsKey(timePeriod))
                {
                    Dictionary<GridCompareLongLat, Dictionary<int, List<TestPointExtend>>> gridListTestPointDic
                        = new Dictionary<GridCompareLongLat, Dictionary<int, List<TestPointExtend>>>();
                    Dictionary<int, List<TestPointExtend>> cellTestPointDic = new Dictionary<int, List<TestPointExtend>>();
                    cellTestPointDic[iECI] = new List<TestPointExtend>() { tpe };
                    gridListTestPointDic[gridLongLat] = cellTestPointDic;
                    timeGridCellTestPointDic[timePeriod] = gridListTestPointDic;
                }
                else
                {
                    setTimeGridCellTestPointDic(iECI, gridLongLat, tpe, timePeriod);
                }
            }
            else
            {
                setCellEciCellNameDic(tp, iECI);
                setGridRoadGuestSampleDic(iECI, gridLongLat);
            }
        }

        private void setTimeGridCellTestPointDic(int iECI, GridCompareLongLat gridLongLat, TestPointExtend tpe, GridTimePeriod timePeriod)
        {
            if (!timeGridCellTestPointDic[timePeriod].ContainsKey(gridLongLat))
            {
                Dictionary<int, List<TestPointExtend>> cellTestPointDic = new Dictionary<int, List<TestPointExtend>>();
                cellTestPointDic[iECI] = new List<TestPointExtend>() { tpe };
                timeGridCellTestPointDic[timePeriod][gridLongLat] = cellTestPointDic;
            }
            else
            {
                if (!timeGridCellTestPointDic[timePeriod][gridLongLat].ContainsKey(iECI))
                    timeGridCellTestPointDic[timePeriod][gridLongLat][iECI] = new List<TestPointExtend>() { tpe };
                else
                    timeGridCellTestPointDic[timePeriod][gridLongLat][iECI].Add(tpe);
            }
        }

        private void setCellEciCellNameDic(TestPoint tp, int iECI)
        {
            if (!cellEciCellNameDic.ContainsKey(iECI))
            {
                LTECell lteCell = tp.GetMainCell_LTE();
                if (lteCell != null)
                {
                    cellEciCellNameDic[iECI] = lteCell.Name;
                }
            }
        }

        private void setGridRoadGuestSampleDic(int iECI, GridCompareLongLat gridLongLat)
        {
            if (!gridRoadGuestSampleDic.ContainsKey(gridLongLat))
                gridRoadGuestSampleDic[gridLongLat] = new List<int>() { iECI };
            else
            {
                if (!gridRoadGuestSampleDic[gridLongLat].Contains(iECI))
                    gridRoadGuestSampleDic[gridLongLat].Add(iECI);
            }
        }

        private void InitRegionMop2()
        {
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        private string isContainPoint(double x, double y)
        {
            bool isFind = false;
            string gridTypeGrid = "";
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y))
                    {
                        gridTypeGrid = grid;
                        isFind = true;
                        break;
                    }
                }
                if (isFind) break;
            }
            return gridTypeGrid;
        }

        private void doWithTakeUpDes()
        {
            WaitBox.Text = "开始处理上轮小区占用情况...";
            WaitBox.ProgressPercent = 30;
            List<int> sameECIList = new List<int>();
            foreach (GridCellKey gridCellKey in iPreECITestPointTakeUpDic.Keys)
            {
                iPreECITestPointTakeUpDic[gridCellKey].StrTestDate = iPreECITestPointTakeUpDic[gridCellKey].StrTestDate.TrimEnd(',');
                bool isSame = false;
                foreach (GridCellKey gridCellKeyCur in iCurECITestPointTakeUpDic.Keys)
                {
                    if (gridCellKey.IECI == gridCellKeyCur.IECI)
                    {
                        isSame = true;
                        sameECIList.Add(gridCellKey.IECI);
                        break;
                    }
                }
                if (!isSame)
                    iPreECITestPointTakeUpDic[gridCellKey].StrTakeUp = "少跑占用小区";
                else
                    iPreECITestPointTakeUpDic[gridCellKey].StrTakeUp = "共同占用小区";
            }
            WaitBox.Text = "开始处理本轮小区占用情况...";
            WaitBox.ProgressPercent = 60;
            foreach (GridCellKey gridCellKey in iCurECITestPointTakeUpDic.Keys)
            {
                iCurECITestPointTakeUpDic[gridCellKey].StrTestDate = iCurECITestPointTakeUpDic[gridCellKey].StrTestDate.TrimEnd(',');
                if (!sameECIList.Contains(gridCellKey.IECI))
                    iCurECITestPointTakeUpDic[gridCellKey].StrTakeUp = "多跑占用小区";
                else
                    iCurECITestPointTakeUpDic[gridCellKey].StrTakeUp = "共同占用小区";
            }
            sameECIList.Clear();
            System.Threading.Thread.Sleep(100);
            WaitBox.Close();
        }

        private void doWithCellTakeUpDetails()
        {
            WaitBox.ProgressPercent = 65;
            Dictionary<string, List<GridCompareLongLat>> cellGridCompareLongLatDic = new Dictionary<string, List<GridCompareLongLat>>();
            foreach (GridTimePeriod gtp in timeGridCellTestPointDic.Keys)
            {
                string strTestDate = gtp.BeginTime.ToString("yyyy-MM-dd");
                List<int> iEciGuestList = new List<int>();
                List<GridCompareLongLat> nineGridListAll = new List<GridCompareLongLat>();
                foreach (GridCompareLongLat gcll in timeGridCellTestPointDic[gtp].Keys)
                {
                    if (!gridRoadGuestSampleDic.ContainsKey(gcll))
                        continue;
                    List<GridCompareLongLat> nineGridList = getNineGridByGrid(gcll);
                    nineGridListAll.AddRange(nineGridList);
                }
                foreach (GridCompareLongLat gcll in timeGridCellTestPointDic[gtp].Keys)
                {
                    if (!gridRoadGuestSampleDic.ContainsKey(gcll))
                        continue;

                    foreach (int iEci in gridRoadGuestSampleDic[gcll])
                    {
                        bool isContainCell = false;
                        foreach (GridCompareLongLat nineGrid in nineGridListAll)
                        {
                            if (!timeGridCellTestPointDic[gtp].ContainsKey(nineGrid)) 
                                continue;

                            if (timeGridCellTestPointDic[gtp][nineGrid].ContainsKey(iEci))
                            {
                                isContainCell = true;
                                break;
                            }
                        }
                        if (!isContainCell && !iEciGuestList.Contains(iEci))
                            iEciGuestList.Add(iEci);
                    }
                }
                foreach (int iEci in iEciGuestList)
                {
                    if (iEci < 0)
                    {
                        continue;
                    }
                    TimeGridCellKey timeGridCell = new TimeGridCellKey(gtp, iEci);
                    if (!timeSpanGridCellInfoDic.ContainsKey(timeGridCell))
                    {
                        timeSpanGridCellInfoDic[timeGridCell] = new TimeSpandGridCellInfo();
                        timeSpanGridCellInfoDic[timeGridCell].StrCityName = strCityName;
                        timeSpanGridCellInfoDic[timeGridCell].ISN = timeSpanGridCellInfoDic.Count;
                        timeSpanGridCellInfoDic[timeGridCell].IECI = iEci;
                        timeSpanGridCellInfoDic[timeGridCell].DStartTime = gtp.BeginTime;
                        timeSpanGridCellInfoDic[timeGridCell].DEndeTime = gtp.EndTime;
                        if (cellEciCellNameDic.ContainsKey(iEci))
                        {
                            timeSpanGridCellInfoDic[timeGridCell].StrCellName = cellEciCellNameDic[iEci];
                        }
                    }
                    List<GridCompareLongLat> gridCompareLongLatList = getGridCompareLongLatListByECI(iEci);
                    List<TestPointExtend> tpeList = new List<TestPointExtend>();
                    StringBuilder strTestGrid = new StringBuilder();
                    foreach (GridCompareLongLat gcllNew in gridCompareLongLatList)
                    {
                        if (!timeGridCellTestPointDic[gtp].ContainsKey(gcllNew))
                        {
                            continue;
                        }
                        strTestGrid.Append(gcllNew.LtLongitude + "_" + gcllNew.LtLatitude + ",");
                        foreach (int iEciNew in timeGridCellTestPointDic[gtp][gcllNew].Keys)
                        {
                            tpeList.AddRange(timeGridCellTestPointDic[gtp][gcllNew][iEciNew]);
                        }
                    }
                    timeSpanGridCellInfoDic[timeGridCell].AddTestPoint(tpeList, strTestGrid.ToString().TrimEnd(','));
                    if (cellEciAlarmInfoDic.ContainsKey(iEci))
                    {
                        foreach (string strDate in cellEciAlarmInfoDic[iEci].Keys)
                        {
                            if (strTestDate.Equals(strDate))
                            {
                                timeSpanGridCellInfoDic[timeGridCell].StrAlarmDate = "是"; 
                                bool isAlarm = false;
                                timeSpanGridCellInfoDic[timeGridCell].CellAlarmInfoNew = getCellAlarmInfoNearstByTime(gtp
                                                                       , cellEciAlarmInfoDic[iEci][strDate], ref isAlarm);
                                if (isAlarm)
                                {
                                    timeSpanGridCellInfoDic[timeGridCell].StrAlarm = "是";
                                    string strCellKey = iEci + "^" + timeSpanGridCellInfoDic[timeGridCell].StrCellName;
                                    if (!cellGridCompareLongLatDic.ContainsKey(strCellKey))
                                    {
                                        cellGridCompareLongLatDic[strCellKey] = gridCompareLongLatList;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            timeGridCellTestPointDic.Clear();
            cellEciAlarmInfoDic.Clear();
            doWithCellGridRegion(cellGridCompareLongLatDic);
            System.Threading.Thread.Sleep(100);
            WaitBox.Close();
        }

        private void doWithCellGridRegion(Dictionary<string, List<GridCompareLongLat>> cellGridCompareLongLatDic)
        {
            foreach (string strCellKey in cellGridCompareLongLatDic.Keys)
            {
                List<MapWinGIS.Shape> shpList = new List<MapWinGIS.Shape>();
                foreach (GridCompareLongLat compareGrid in cellGridCompareLongLatDic[strCellKey])
                {
                    MapWinGIS.Shape shap = ShapeHelper.CreateRectShape(compareGrid.DLtLongitude
                                  , compareGrid.DLtLatitude, compareGrid.DBrLongitude, compareGrid.DBrLatitude);
                    shpList.Add(shap);
                }
                gridTestShapDic[strCellKey] = ShapeHelper.CombineMultiRegions(shpList);
            }
        }

        private void doQueryCellAlarmInfo()
        {
            WaitBox.ProgressPercent = 30;
            cellEciAlarmInfoDic = new Dictionary<int, Dictionary<string, List<CellOutOfServiceAlarmInfo>>>();
            DIYQueryCellAlarmInfo cellAlarmInfo = new DIYQueryCellAlarmInfo(mainModel);
            TimePeriod timePeriod = condition.Periods[condition.Periods.Count - 1];
            cellAlarmInfo.SetCondition(timePeriod.BeginTime.ToString("yyyy-MM-dd"), timePeriod.EndTime.ToString("yyyy-MM-dd"), strCityName);
            cellAlarmInfo.Query();
            cellEciAlarmInfoDic = cellAlarmInfo.cellEciAlarmInfoDic;
            WaitBox.ProgressPercent = 60;
            WaitBox.Close();
        }

        private CellOutOfServiceAlarmInfo getCellAlarmInfoNearstByTime(GridTimePeriod gtp
                       , List<CellOutOfServiceAlarmInfo> cellAlarmInfoLsit,ref bool isAlarm)
        {
            double dTime = double.MaxValue;
            CellOutOfServiceAlarmInfo cellInfo = new CellOutOfServiceAlarmInfo();
            foreach (CellOutOfServiceAlarmInfo cellAlarmInfo in cellAlarmInfoLsit)
            {
                if (gtp.Contains(cellAlarmInfo.DStartTime) || gtp.Contains(cellAlarmInfo.DEndTime)
                    || (gtp.BeginTime >= cellAlarmInfo.DStartTime && gtp.BeginTime <= cellAlarmInfo.DEndTime)
                    || (gtp.EndTime >= cellAlarmInfo.DStartTime && gtp.EndTime <= cellAlarmInfo.DEndTime))
                {
                    isAlarm = true;
                    cellInfo = cellAlarmInfo;
                    break;
                }
                else
                {
                    double dTimeNewSpan = getMinSpanTime(gtp, cellAlarmInfo);
                    if (dTimeNewSpan < dTime)
                    {
                        cellInfo = cellAlarmInfo;
                        dTime = dTimeNewSpan;
                    }
                }
            }
            return cellInfo;
        }

        private double getMinSpanTime(GridTimePeriod gtp, CellOutOfServiceAlarmInfo cellAlarmInfo)
        {
            double dTime = double.MaxValue;
            TimeSpan tsPeriodStartTime = new TimeSpan(gtp.BeginTime.Ticks);
            TimeSpan tsPeriodEndTime = new TimeSpan(gtp.EndTime.Ticks);

            TimeSpan tsStartTime = new TimeSpan(cellAlarmInfo.DStartTime.Ticks);
            TimeSpan tsEndTime = new TimeSpan(cellAlarmInfo.DEndTime.Ticks);

            double startTime1 = tsPeriodStartTime.Subtract(tsStartTime).Duration().TotalMinutes;
            double startTime2 = tsPeriodStartTime.Subtract(tsEndTime).Duration().TotalMinutes;

            double endTime1 = tsPeriodEndTime.Subtract(tsStartTime).Duration().TotalMinutes;
            double endTime2 = tsPeriodEndTime.Subtract(tsEndTime).Duration().TotalMinutes;
            if (startTime1 < dTime)
                dTime = startTime1;
            if (startTime2 < dTime)
                dTime = startTime2;
            if (endTime1 < dTime)
                dTime = endTime1;
            if (endTime2 < dTime)
                dTime = endTime2;
            return dTime;
        }

        private List<GridCompareLongLat> getNineGridByGrid(GridCompareLongLat gridCompareLongLat)
        {
            List<GridCompareLongLat> nineGridList = new List<GridCompareLongLat>();
            for (int iRow = -1; iRow < 2; iRow++)
            {
                for (int iCol = -1; iCol < 2; iCol++)
                {
                    GridCompareLongLat gcll = new GridCompareLongLat();
                    gcll.LtLongitude = gridCompareLongLat.LtLongitude + 4000 * iRow;
                    gcll.LtLatitude = gridCompareLongLat.LtLatitude - 3600 * iCol;
                    gcll.BrLongitude = gridCompareLongLat.BrLongitude + 4000 * iRow;
                    gcll.BrLatitude = gridCompareLongLat.BrLatitude - 3600 * iCol;
                    nineGridList.Add(gcll);
                }
            }
            return nineGridList;
        }

        private void changeGridToShap()
        {
            List<GridCompareLongLat> sameGridLongLatList = new List<GridCompareLongLat>();
            List<GridCompareLongLat> onlyHostGridLongLatList = new List<GridCompareLongLat>();
            List<GridCompareLongLat> onlyGuestGridLongLatList = new List<GridCompareLongLat>();

            foreach (GridCompareLongLat gKey in gridRoadHostSampleDic.Keys)
            {
                List<GridCompareLongLat> nineGridList = getNineGridByGrid(gKey);
                bool isSameGrid = judgeGuestSameGrid(sameGridLongLatList, gKey, nineGridList);
                if (!isSameGrid)
                {
                    onlyHostGridLongLatList.Add(gKey);
                }
            }

            foreach (GridCompareLongLat gKey in gridRoadGuestSampleDic.Keys)
            {
                List<GridCompareLongLat> nineGridList = getNineGridByGrid(gKey);
                bool isSameGrid = judgeHostSameGrid(sameGridLongLatList, gKey, nineGridList);
                if (!isSameGrid)
                {
                    onlyGuestGridLongLatList.Add(gKey);
                }
            }

            List<MapWinGIS.Shape> shpList = new List<MapWinGIS.Shape>();
            foreach (GridCompareLongLat compareGrid in sameGridLongLatList)
            {
                MapWinGIS.Shape shap = ShapeHelper.CreateRectShape(compareGrid.DLtLongitude
                              , compareGrid.DLtLatitude, compareGrid.DBrLongitude, compareGrid.DBrLatitude);

                shpList.Add(shap);
            }
            gridShapDic["共同区域"] = ShapeHelper.CombineMultiRegions(shpList);

            shpList = new List<MapWinGIS.Shape>();
            foreach (GridCompareLongLat compareGrid in onlyGuestGridLongLatList)
            {
                MapWinGIS.Shape shap = ShapeHelper.CreateRectShape(compareGrid.DLtLongitude
                              , compareGrid.DLtLatitude, compareGrid.DBrLongitude, compareGrid.DBrLatitude);
                shpList.Add(shap);
            }
            gridShapDic["少跑区域"] = ShapeHelper.CombineMultiRegions(shpList);

            shpList = new List<MapWinGIS.Shape>();
            foreach (GridCompareLongLat compareGrid in onlyHostGridLongLatList)
            {
                MapWinGIS.Shape shap = ShapeHelper.CreateRectShape(compareGrid.DLtLongitude
                              , compareGrid.DLtLatitude, compareGrid.DBrLongitude, compareGrid.DBrLatitude);
                shpList.Add(shap);
            }
            gridShapDic["多跑区域"] = ShapeHelper.CombineMultiRegions(shpList);

            gridRoadHostSampleDic = new Dictionary<GridCompareLongLat, int>();
            gridRoadGuestSampleDic = new Dictionary<GridCompareLongLat, List<int>>();
        }

        private bool judgeGuestSameGrid(List<GridCompareLongLat> sameGridLongLatList, GridCompareLongLat gKey, List<GridCompareLongLat> nineGridList)
        {
            bool isSameGrid = false;
            foreach (GridCompareLongLat gcll in nineGridList)
            {
                if (gridRoadGuestSampleDic.ContainsKey(gcll))
                {
                    if (!sameGridLongLatList.Contains(gKey))
                    {
                        sameGridLongLatList.Add(gKey);
                    }
                    isSameGrid = true;
                    break;
                }
            }

            return isSameGrid;
        }

        private bool judgeHostSameGrid(List<GridCompareLongLat> sameGridLongLatList, GridCompareLongLat gKey, List<GridCompareLongLat> nineGridList)
        {
            bool isSameGrid = false;
            foreach (GridCompareLongLat gcll in nineGridList)
            {
                if (gridRoadHostSampleDic.ContainsKey(gcll))
                {
                    if (!sameGridLongLatList.Contains(gKey))
                    {
                        sameGridLongLatList.Add(gKey);
                    }
                    isSameGrid = true;
                    break;
                }
            }

            return isSameGrid;
        }

        private void changShapToResvRegion(StatMode statMode)
        {
            MapForm mapForm = MainModel.MainForm.GetMapForm();
            if (mapForm == null)
            {
                XtraMessageBox.Show("地图窗口不可见！");
                return;
            }
            mapForm.clearRegionLayer();
            mainModel.SearchGeometrys.SelectedResvRegions = new List<ResvRegion>();
            mainModel.SearchGeometrys.SelectedResvRegionDic = new Dictionary<string, List<ResvRegion>>();

            List<ResvRegion> subRegionList = null;
            Dictionary<string, MapWinGIS.Shape> gridShapStatDic = new Dictionary<string, MapWinGIS.Shape>();
            Dictionary<string,Color> gridShapColorDic=new Dictionary<string,Color> ();
            if (statMode == StatMode.PreRoundStat)
            {
                gridShapStatDic["少跑区域"] = gridShapDic["少跑区域"];
                gridShapStatDic["共同区域"] = gridShapDic["共同区域"];
                gridShapColorDic["少跑区域"] = Color.Blue;
                gridShapColorDic["共同区域"] = Color.Purple;
            }
            else if (statMode == StatMode.CurRoundStat)
            {
                gridShapStatDic["多跑区域"] = gridShapDic["多跑区域"];
                gridShapStatDic["共同区域"] = gridShapDic["共同区域"];
                gridShapColorDic["多跑区域"] = Color.Yellow;
                gridShapColorDic["共同区域"] = Color.Purple;
            }
            else
            {
                gridShapStatDic = gridShapDic;
                gridShapColorDic["少跑区域"] = Color.Blue;
                gridShapColorDic["多跑区域"] = Color.Yellow;
                gridShapColorDic["共同区域"] = Color.Purple;
            }
            foreach (string strReion in gridShapStatDic.Keys)
            {
                if (gridShapDic[strReion] == null) continue;
                ResvRegion region = new ResvRegion();
                region.RootNodeName = "栅格对比测试统计";
                region.RegionName = strReion;
                region.Shape = gridShapDic[strReion];
                mainModel.SearchGeometrys.SelectedResvRegions.Add(region);
                if (!mainModel.SearchGeometrys.SelectedResvRegionDic.TryGetValue("栅格对比测试统计", out subRegionList))
                {
                    subRegionList = new List<ResvRegion>();
                    mainModel.SearchGeometrys.SelectedResvRegionDic.Add("栅格对比测试统计", subRegionList);
                }
                subRegionList.Add(region);
            }
            mainModel.MultiGeometrys = true;
            mainModel.RootNodeGeometrys = false;

            mainModel.SelectedCell = null;
            mainModel.SelectedTDCell = null;
            mainModel.SelectedWCell = null;
            mainModel.SelectedLTECell = null;
            mapForm.RefreshResvLayer();
            mapForm.searchGeometrysChanged();
            mapForm.DrawRegionGrid(gridShapDic, gridShapColorDic, "", new Dictionary<string,string>());
            if (mainModel.SearchGeometrys.Region != null)
            {
                mapForm.GoToView(mainModel.SearchGeometrys.RegionBounds);
            }
        }

        private List<GridCellDateInfo> getCellDateInfoList()
        {
            List<GridCellDateInfo> cellDateInfoList = new List<GridCellDateInfo>();
            Dictionary<GridCellKey, GridCellDateInfo> gridCellDateInfoDic = new Dictionary<GridCellKey, GridCellDateInfo>();
            foreach (TimeGridCellKey gridCellKey in timeSpanGridCellInfoDic.Keys)
            {
                GridCellKey gridCell = new GridCellKey(timeSpanGridCellInfoDic[gridCellKey].StrGridName, timeSpanGridCellInfoDic[gridCellKey].IECI);
                if (!gridCellDateInfoDic.ContainsKey(gridCell))
                {
                    gridCellDateInfoDic[gridCell] = new GridCellDateInfo();
                    gridCellDateInfoDic[gridCell].ISN = gridCellDateInfoDic.Count;
                    gridCellDateInfoDic[gridCell].StrCityName = timeSpanGridCellInfoDic[gridCellKey].StrCityName;
                    gridCellDateInfoDic[gridCell].StrGridName = timeSpanGridCellInfoDic[gridCellKey].StrGridName;
                    gridCellDateInfoDic[gridCell].StrCellName = timeSpanGridCellInfoDic[gridCellKey].StrCellName;
                    gridCellDateInfoDic[gridCell].IECI = timeSpanGridCellInfoDic[gridCellKey].IECI;
                    gridCellDateInfoDic[gridCell].StrAlarm = timeSpanGridCellInfoDic[gridCellKey].StrAlarm;
                    gridCellDateInfoDic[gridCell].StrAlarmDate = timeSpanGridCellInfoDic[gridCellKey].StrAlarmDate;
                }
                else
                {
                    if (timeSpanGridCellInfoDic[gridCellKey].StrAlarm.Equals("是"))
                    {
                        gridCellDateInfoDic[gridCell].StrAlarm = "是";
                    }
                    if (timeSpanGridCellInfoDic[gridCellKey].StrAlarmDate.Equals("是"))
                    {
                        gridCellDateInfoDic[gridCell].StrAlarmDate = "是";
                    }
                }
            }
            cellDateInfoList.AddRange(gridCellDateInfoDic.Values);
            return cellDateInfoList;
        }

        protected override void fireShowForm()
        {
            List<GridCellDateInfo> cellDateInfoList = getCellDateInfoList();
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTGDGridCompareStatForm).FullName);
            ZTGDGridCompareStatForm resultForm = obj == null ? null : obj as ZTGDGridCompareStatForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ZTGDGridCompareStatForm(MainModel);
            }

            resultForm.FillData(iCurECITestPointTakeUpDic, iPreECITestPointTakeUpDic, timeSpanGridCellInfoDic
                                , gridShapDic, cellDateInfoList, gridTestShapDic);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }

        private void doWithKpiStatByCompareGrid(ZTGDQueryKPIByCompareGrid compareGridKpiStat)
        {
            changShapToResvRegion(StatMode.PreRoundStat);
            List<TimePeriod> timePeriod = new List<TimePeriod>();
            timePeriod.AddRange(condition.Periods);
            condition.Periods.Clear();
            condition.Periods.Add(timePeriod[0]);
            compareGridKpiStat.query();

            changShapToResvRegion(StatMode.CurRoundStat);
            condition.Periods.Clear();
            condition.Periods.Add(timePeriod[1]);
            compareGridKpiStat.isHost = true;
            compareGridKpiStat.getSelectReport(condition);
            compareGridKpiStat.query();

            condition.Periods.Clear();
            condition.Periods.AddRange(timePeriod);
            changShapToResvRegion(StatMode.NotRoundStat);
        }
    }
    public enum StatMode
    {
        [EnumDescriptionAttribute("上轮测试")]
        PreRoundStat = 0,

        [EnumDescriptionAttribute("本轮测试")]
        CurRoundStat,

        [EnumDescriptionAttribute("显示图层")]
        NotRoundStat,
    }
}
