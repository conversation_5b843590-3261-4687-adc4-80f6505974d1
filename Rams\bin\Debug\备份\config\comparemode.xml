<?xml version="1.0"?>
<Configs>
  <Config name="Configs">
    <Item name="CompareConfig" typeName="IDictionary">
      <Item typeName="IDictionary" key="compareParamConfigDic">
        <Item typeName="IDictionary" key="移动TD_PCCPCH_RSCP VS 联通W_TotalRSCP">
          <Item typeName="Int32" key="sn">1</Item>
          <Item typeName="String" key="name">移动TD_PCCPCH_RSCP VS 联通W_TotalRSCP</Item>
          <Item typeName="IList" key="serviceList_A">
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="Int32" key="carrier_A">1</Item>
          <Item typeName="String" key="formula_A">Tx_5C04030D</Item>
          <Item typeName="Boolean" key="isLimit_A">False</Item>
          <Item typeName="IDictionary" key="Range_A">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="Boolean" key="judgeByBndOr">True</Item>
          <Item typeName="IList" key="displayColumnList_A">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rxlev</Item>
              <Item typeName="String" key="ParamName">Tx_640402</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rxlev1</Item>
              <Item typeName="String" key="ParamName">Tx_640402</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="serviceList_B">
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="Int32" key="carrier_B">2</Item>
          <Item typeName="String" key="formula_B">Wx_710A3F</Item>
          <Item typeName="Boolean" key="isLimit_B">False</Item>
          <Item typeName="IDictionary" key="Range_B">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="IList" key="displayColumnList_B">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rxlevB12</Item>
              <Item typeName="String" key="ParamName">Tx_640402</Item>
              <Item typeName="String" key="Flag">B</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rxlevB1</Item>
              <Item typeName="String" key="ParamName">Tx_640402</Item>
              <Item typeName="String" key="Flag">B</Item>
            </Item>
          </Item>
          <Item key="algorithmName" />
          <Item typeName="IList" key="colorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">好</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">255</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">5</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">100</Item>
              <Item typeName="Boolean" key="maxValueInclude">True</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">相当</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-5</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">5</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">差</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-100</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">-5</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="bothStandards" />
          <Item typeName="IList" key="specials">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有主队</Item>
              <Item typeName="Int32" key="ColorR">211</Item>
              <Item typeName="Int32" key="ColorG">211</Item>
              <Item typeName="Int32" key="ColorB">211</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有客队</Item>
              <Item typeName="Int32" key="ColorR">169</Item>
              <Item typeName="Int32" key="ColorG">169</Item>
              <Item typeName="Int32" key="ColorB">169</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="移动TD_下载速率 VS 联通W_下载速率">
          <Item typeName="Int32" key="sn">4</Item>
          <Item typeName="String" key="name">移动TD_下载速率 VS 联通W_下载速率</Item>
          <Item typeName="IList" key="serviceList_A">
            <Item typeName="Int32">18</Item>
          </Item>
          <Item typeName="Int32" key="carrier_A">1</Item>
          <Item typeName="String" key="formula_A">(Tx_050564020101+Tx_051264020101)*(1000*8)/((Tx_050564020102+Tx_051264020102)*1024)</Item>
          <Item typeName="Boolean" key="isLimit_A">False</Item>
          <Item typeName="IDictionary" key="Range_A">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="Boolean" key="judgeByBndOr">True</Item>
          <Item typeName="IList" key="displayColumnList_A" />
          <Item typeName="IList" key="serviceList_B">
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">28</Item>
          </Item>
          <Item typeName="Int32" key="carrier_B">2</Item>
          <Item typeName="String" key="formula_B">((Wx_050B01640201+Wx_050F01640201)*(1000*8))/((Wx_050B01640202+Wx_050F01640202)*1024)</Item>
          <Item typeName="Boolean" key="isLimit_B">False</Item>
          <Item typeName="IDictionary" key="Range_B">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="IList" key="displayColumnList_B" />
          <Item key="algorithmName" />
          <Item typeName="IList" key="colorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">好</Item>
              <Item typeName="Int32" key="ColorR">128</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">255</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">50</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">999</Item>
              <Item typeName="Boolean" key="maxValueInclude">True</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">相当</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-50</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">50</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">差</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-999</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">-50</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="bothStandards" />
          <Item typeName="IList" key="specials">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有主队</Item>
              <Item typeName="Int32" key="ColorR">211</Item>
              <Item typeName="Int32" key="ColorG">211</Item>
              <Item typeName="Int32" key="ColorB">211</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有客队</Item>
              <Item typeName="Int32" key="ColorR">169</Item>
              <Item typeName="Int32" key="ColorG">169</Item>
              <Item typeName="Int32" key="ColorB">169</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="移动Rxlev VS 联通Rxlev">
          <Item typeName="Int32" key="sn">2</Item>
          <Item typeName="String" key="name">移动Rxlev VS 联通Rxlev</Item>
          <Item typeName="IList" key="serviceList_A">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="Int32" key="carrier_A">1</Item>
          <Item typeName="String" key="formula_A">Mx_640103</Item>
          <Item typeName="Boolean" key="isLimit_A">False</Item>
          <Item typeName="IDictionary" key="Range_A">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="Boolean" key="judgeByBndOr">True</Item>
          <Item typeName="IList" key="displayColumnList_A">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">RSCP</Item>
              <Item typeName="String" key="ParamName">Tx_5C04030D</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="serviceList_B">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="Int32" key="carrier_B">2</Item>
          <Item typeName="String" key="formula_B">Mx_640103</Item>
          <Item typeName="Boolean" key="isLimit_B">False</Item>
          <Item typeName="IDictionary" key="Range_B">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="IList" key="displayColumnList_B">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">RSCP</Item>
              <Item typeName="String" key="ParamName">Wx_710A3F</Item>
              <Item typeName="String" key="Flag">B</Item>
            </Item>
          </Item>
          <Item key="algorithmName" />
          <Item typeName="IList" key="colorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">好</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">255</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">10</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">100</Item>
              <Item typeName="Boolean" key="maxValueInclude">True</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">相当</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-10</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">10</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">差</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-100</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">-10</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="bothStandards" />
          <Item typeName="IList" key="specials">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有主队</Item>
              <Item typeName="Int32" key="ColorR">211</Item>
              <Item typeName="Int32" key="ColorG">211</Item>
              <Item typeName="Int32" key="ColorB">211</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有客队</Item>
              <Item typeName="Int32" key="ColorR">169</Item>
              <Item typeName="Int32" key="ColorG">169</Item>
              <Item typeName="Int32" key="ColorB">169</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="RSRP">
          <Item typeName="Int32" key="sn">3</Item>
          <Item typeName="String" key="name">RSRP</Item>
          <Item typeName="IList" key="serviceList_A">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="carrier_A">1</Item>
          <Item typeName="String" key="formula_A">Lte_61210309</Item>
          <Item typeName="Boolean" key="isLimit_A">False</Item>
          <Item typeName="IDictionary" key="Range_A">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="Boolean" key="judgeByBndOr">True</Item>
          <Item typeName="IList" key="displayColumnList_A">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">sinr1</Item>
              <Item typeName="String" key="ParamName">Lte_61210403</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">pdcp</Item>
              <Item typeName="String" key="ParamName">Lte_61210512</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="serviceList_B">
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="Int32" key="carrier_B">2</Item>
          <Item typeName="String" key="formula_B">Lte_61210309</Item>
          <Item typeName="Boolean" key="isLimit_B">True</Item>
          <Item typeName="IDictionary" key="Range_B">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="IList" key="displayColumnList_B">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">sinr</Item>
              <Item typeName="String" key="ParamName">Wx_710A3F</Item>
              <Item typeName="String" key="Flag">B</Item>
            </Item>
          </Item>
          <Item key="algorithmName" />
          <Item typeName="IList" key="colorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">好</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">128</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">10</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">100</Item>
              <Item typeName="Boolean" key="maxValueInclude">True</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">中</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">0</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">10</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">差</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-100</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">0</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="bothStandards" />
          <Item typeName="IList" key="specials">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有主队</Item>
              <Item typeName="Int32" key="ColorR">211</Item>
              <Item typeName="Int32" key="ColorG">211</Item>
              <Item typeName="Int32" key="ColorB">211</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有客队</Item>
              <Item typeName="Int32" key="ColorR">169</Item>
              <Item typeName="Int32" key="ColorG">169</Item>
              <Item typeName="Int32" key="ColorB">169</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="SINR">
          <Item typeName="Int32" key="sn">6</Item>
          <Item typeName="String" key="name">SINR</Item>
          <Item typeName="IList" key="serviceList_A">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="carrier_A">1</Item>
          <Item typeName="String" key="formula_A">Lte_61210403</Item>
          <Item typeName="Boolean" key="isLimit_A">False</Item>
          <Item typeName="IDictionary" key="Range_A">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="Boolean" key="judgeByBndOr">True</Item>
          <Item typeName="IList" key="displayColumnList_A">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rsrpA</Item>
              <Item typeName="String" key="ParamName">Lte_61210309</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rsrp</Item>
              <Item typeName="String" key="ParamName">Lte_61210309</Item>
              <Item typeName="String" key="Flag">A</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="serviceList_B">
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="Int32" key="carrier_B">2</Item>
          <Item typeName="String" key="formula_B">Wx_5D0A0309</Item>
          <Item typeName="Boolean" key="isLimit_B">False</Item>
          <Item typeName="IDictionary" key="Range_B">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="IList" key="displayColumnList_B">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rsrpB</Item>
              <Item typeName="String" key="ParamName">Lte_61210309</Item>
              <Item typeName="String" key="Flag">B</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Caption">rsrpA</Item>
              <Item typeName="String" key="ParamName">Lte_612103091</Item>
              <Item typeName="String" key="Flag">B</Item>
            </Item>
          </Item>
          <Item key="algorithmName" />
          <Item typeName="IList" key="colorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">好</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">128</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">10</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">100</Item>
              <Item typeName="Boolean" key="maxValueInclude">True</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">中</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">0</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">10</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">差</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-100</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">0</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="bothStandards" />
          <Item typeName="IList" key="specials">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有主队</Item>
              <Item typeName="Int32" key="ColorR">211</Item>
              <Item typeName="Int32" key="ColorG">211</Item>
              <Item typeName="Int32" key="ColorB">211</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有客队</Item>
              <Item typeName="Int32" key="ColorR">169</Item>
              <Item typeName="Int32" key="ColorG">169</Item>
              <Item typeName="Int32" key="ColorB">169</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="1">
          <Item typeName="Int32" key="sn">5</Item>
          <Item typeName="String" key="name">1</Item>
          <Item typeName="IList" key="serviceList_A">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="carrier_A">1</Item>
          <Item typeName="String" key="formula_A">Lte_61210403</Item>
          <Item typeName="Boolean" key="isLimit_A">False</Item>
          <Item typeName="IDictionary" key="Range_A">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="Boolean" key="judgeByBndOr">True</Item>
          <Item typeName="IList" key="displayColumnList_A" />
          <Item typeName="IList" key="serviceList_B">
            <Item typeName="Int32">46</Item>
          </Item>
          <Item typeName="Int32" key="carrier_B">2</Item>
          <Item typeName="String" key="formula_B">Lf_612D0309</Item>
          <Item typeName="Boolean" key="isLimit_B">False</Item>
          <Item typeName="IDictionary" key="Range_B">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-55</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
          <Item typeName="IList" key="displayColumnList_B" />
          <Item key="algorithmName" />
          <Item typeName="IList" key="colorItems">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">好</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">128</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">10</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">100</Item>
              <Item typeName="Boolean" key="maxValueInclude">True</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">中</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">255</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">0</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">10</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">差</Item>
              <Item typeName="Int32" key="ColorR">255</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
              <Item typeName="Double" key="minValue">-100</Item>
              <Item typeName="Boolean" key="minValueInclude">True</Item>
              <Item typeName="Double" key="maxValue">0</Item>
              <Item typeName="Boolean" key="maxValueInclude">False</Item>
              <Item typeName="Int32" key="CmpType">0</Item>
              <Item typeName="Single" key="fHostMin">0</Item>
              <Item typeName="Single" key="fGuestMin">0</Item>
              <Item typeName="Boolean" key="bHostMinInclde">True</Item>
              <Item typeName="Boolean" key="bGuestMinInclude">True</Item>
            </Item>
          </Item>
          <Item typeName="IList" key="bothStandards" />
          <Item typeName="IList" key="specials">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有主队</Item>
              <Item typeName="Int32" key="ColorR">211</Item>
              <Item typeName="Int32" key="ColorG">211</Item>
              <Item typeName="Int32" key="ColorB">211</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">只有客队</Item>
              <Item typeName="Int32" key="ColorR">169</Item>
              <Item typeName="Int32" key="ColorG">169</Item>
              <Item typeName="Int32" key="ColorB">169</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Name">其它</Item>
              <Item typeName="Int32" key="ColorR">0</Item>
              <Item typeName="Int32" key="ColorG">0</Item>
              <Item typeName="Int32" key="ColorB">0</Item>
              <Item typeName="Boolean" key="Visible">True</Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>