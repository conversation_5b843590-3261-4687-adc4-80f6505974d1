using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
//using EventDefineEngine;

namespace MasterCom.RAMS.Model
{
    public class CDMATestPointDetail : TestPoint
    {
        public override DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(Time * 1000L + Millisecond); }
        }

        public new short Millisecond { get; set; }
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            //TestType = content.GetParamInt();
            //DeviceType = content.GetParamInt();
            //FileType = content.GetParamInt();
            //ServiceType = content.GetParamInt();
            //CarrierType = content.GetParamInt();
            //ProjectType = content.GetParamInt();
            //Batch = content.GetParamInt();
            FileID = content.GetParamInt();
            //FileName = content.GetParamString();
            SN = content.GetParamInt();
            Time = content.GetParamInt();
            Millisecond = content.GetParamShort();
            MS = content.GetParamByte();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            this["CD_SID"] = content.GetParamInt();
            this["CD_NID"] = content.GetParamInt();
            this["CD_BID"] = content.GetParamInt();
            this["CD_SRCH_Win_A"] = content.GetParamInt();
            this["CD_SRCH_Win_N"] = content.GetParamInt();
            this["CD_SRCH_Win_R"] = content.GetParamInt();
            this["CD_Pilot_Inc"] = content.GetParamInt();
            this["CD_T_Add"] = content.GetParamFloat();
            this["CD_T_Comp"] = content.GetParamFloat();
            this["CD_T_Drop"] = content.GetParamFloat();
            this["CD_T_TDrop"] = content.GetParamInt();
            this["CD_Soft_Slope"] = content.GetParamInt();
            this["CD_Ec_THRESH"] = content.GetParamInt();
            this["CD_Ec_Io_THRESH"] = content.GetParamInt();
            this["CD_NGHBR_MAX_AGE"] = content.GetParamInt();
            this["CD_ESN"] = content.GetParamInt();

            this["CD_RxAGC"] = content.GetParamFloat();
            this["CD_TxAGC"] = content.GetParamFloat();

            this["CD_TX_Power"] = content.GetParamFloat();
            this["CD_TX_Adj"] = content.GetParamFloat();
            this["CD_FBER"] = content.GetParamFloat();
            this["CD_FFER"] = content.GetParamFloat();
            this["CD_MeasFER"] = content.GetParamFloat();
            this["CD_Locked"] = content.GetParamInt();
            this["CD_ActiveSet"] = content.GetParamInt();
            this["CD_ReferPN"] = content.GetParamInt();
            this["CD_MaxRSSIPN"] = content.GetParamInt();

            this["CD_TotalEcIo"] = content.GetParamFloat();
            this["CD_MaxEcIo"] = content.GetParamFloat();
            this["CD_ReferenceEcIo"] = content.GetParamFloat();
            this["CD_TotalEc"] = content.GetParamFloat();
            this["CD_MaxEc"] = content.GetParamFloat();
            this["CD_ReferenceEc"] = content.GetParamFloat();

            this["CD_Frequency"] = content.GetParamInt();
            this["CD_BandClass"] = content.GetParamInt();
            this["CD_Pollution"] = content.GetParamFloat();
            this["CD_HandoffState"] = content.GetParamInt();
            this["CD_InServiceState"] = content.GetParamInt();
            this["CD_RX_Power"] = content.GetParamFloat();
            this["CD_PESQScore"] = content.GetParamFloat();
            this["CD_PESQLQ"] = content.GetParamFloat();
            this["CD_PESQMos"] = content.GetParamFloat();

            for (int i = 0; i < 6; i++)
            {
                this["CD_NS_Frequency",i] = content.GetParamInt();
                this["CD_NS_PN", i] = content.GetParamInt();
                this["CD_NS_Ec_Io", i] = content.GetParamFloat();
                this["CD_NS_Ec", i] = content.GetParamFloat();
            }
            this["CD_RX_RLP_Thr"] = content.GetParamInt();
            this["CD_TX_RLP_Thr"] = content.GetParamInt();
            this["CD_RLP_Err_Rate"] = content.GetParamFloat();
            this["CD_RLP_RTX_Rate"] = content.GetParamFloat();
            this["CD_RX_PHYS_Rate"] = content.GetParamInt();
            this["CD_TX_PHYS_Rate"] = content.GetParamInt();

            this["CD_Data_Flag"] = content.GetParamInt();
            this["CD_FTP_Download"] = content.GetParamInt();

            this["CD_FCH_FER"] = content.GetParamFloat();
            this["CD_SCH0_FER"] = content.GetParamFloat();
            this["CD_SCH1_FER"] = content.GetParamFloat();
            this["CD_DCCH_FER"] = content.GetParamFloat();
        }
        public void Fill(string[] content)
        {
            int i = 0;
            SN = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            FileID = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            Time = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            Millisecond = content[i].Trim().Length == 0 ? (short)0 : parseShortStr(content[i].Trim()); i++;
            MS = content[i].Trim().Length == 0 ? (byte)0 : parseByteStr(content[i].Trim()); i++;
            Longitude = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()) / 10000000.0; i++;
            Latitude = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()) / 10000000.0; i++;
            //
            this["TD_Speed"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_SCell_CI] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_SCell_CPI] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_SCell_UARFCN] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellDRXCoefficient"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellBarred"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellSIntraSearch"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellSInterSearch"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellTreselections"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellQhysts"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_AssistantUARFCNSet"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_UpPchShift"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_AttachAllowed"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ServerHCS"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_T3212"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_MCC"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_MNC"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_SCell_LAC] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_RAC"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Midamble"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_RRC_State"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_MSMode"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_PS_Status"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_CarrierRSSI"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_PCCPCH_ISCP"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            for (int index = 0; index < 7; index++)
            {
                this["TD_TS_ISCP_VALUE", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            this["TD_DPCH_ISCP"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_PCCPCH_RSCP"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_PCCPCH_C2I"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            for (int index = 0; index < 7; index++)
            {
                this["TD_TS_RSCP_VALUE", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            this["TD_DPCH_RSCP"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_DPCH_C2I"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_TA] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;

            this["TD_BLER_VALUE"] = content[i].Trim().Length == 0 ? (float)0 : parseFloatStr(content[i].Trim()); i++;
            this["TD_BLER_TotalBlock"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_BLER_ErrBlock"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;

            this["TD_ScellPathloss"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellSrxlev"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellRs"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ScellRSSI"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_PCCPCH_SIR"] = content[i].Trim().Length == 0 ? (float)0 : parseFloatStr(content[i].Trim()); i++;
            this["TD_CCTRCHSIR"] = content[i].Trim().Length == 0 ? (float)0 : parseFloatStr(content[i].Trim()); i++;
            this["TD_DownlinkTargetSIR"] = content[i].Trim().Length == 0 ? (float)0 : parseFloatStr(content[i].Trim()); i++;

            this["TD_TxPower"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_MaxRxPower"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_MaxAllowedTxPower"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_SyncULTxPower"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_RachULTxPower"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_ChipWindow"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_CPI", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_UARFCN", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_PCCPCH_RSCP", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_Pathloss", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_ISCPNum", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_Rn", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_RSSI", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NCell_State", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this[MainModel.TD_GSM_NCell_ARFCN, index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this[MainModel.TD_GSM_NCell_BSIC, index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NGsmCell_RXLev", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            for (int index = 0; index < 6; index++)
            {
                this["TD_NGsmCell_c1", index] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            }
            this[MainModel.TD_GSM_SCell_ARFCN] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_GSM_SCell_CI] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_SCellgsm_c1"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Servgsm_bcchrxlev"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this[MainModel.TD_GSM_SCell_BSIC] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_rxqualfull"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_rxqualsub"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_rxlevfull"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_rxlevsub"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_speechcode"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_RTL_max"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_Scellgsm_RTL_cur"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;

            this["TD_MOSValue"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_RLC_DL_R4_Rate"] = content[i].Trim().Length == 0 ? (float)0 : ((float)parseIntStr(content[i].Trim()) / 1024); i++;
            this["TD_RLC_UL_R4_Rate"] = content[i].Trim().Length == 0 ? (float)0 : ((float)parseIntStr(content[i].Trim()) / 1024); i++;
            this["TD_RLC_DL_GPRS_Rate"] = content[i].Trim().Length == 0 ? (float)0 : ((float)parseIntStr(content[i].Trim()) / 1024); i++;
            this["TD_RLC_UL_GPRS_Rate"] = content[i].Trim().Length == 0 ? (float)0 : ((float)parseIntStr(content[i].Trim()) / 1024); i++;
            this["TD_APP_DL_R4_Rate"] = content[i].Trim().Length == 0 ? (float)0 : ((float)parseIntStr(content[i].Trim()) / 1024); i++;
            this["TD_APP_DL_GPRS_Rate"] = content[i].Trim().Length == 0 ? (float)0 : ((float)parseIntStr(content[i].Trim()) / 1024); i++;
            this["TD_FTP_DL_Bytes"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim()); i++;
            this["TD_FTPStatus"] = content[i].Trim().Length == 0 ? 0 : parseIntStr(content[i].Trim());
        }
        private int parseIntStr(string s)
        {
            int ret = 0;
            int.TryParse(s, out ret);
            return ret;
        }
        private short parseShortStr(string s)
        {
            short ret = 0;
            short.TryParse(s, out ret);
            return ret;
        }
        private byte parseByteStr(string s)
        {
            byte ret = 0;
            byte.TryParse(s, out ret);
            return ret;
        }
        private float parseFloatStr(string s)
        {
            int ret = 0;
            int.TryParse(s, out ret);
            return 0.001F*ret;
        }
    }
}
