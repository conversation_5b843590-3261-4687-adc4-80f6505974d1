﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverSequenceInfoForm : BaseFormStyle
    {
        private List<HandoverSequenceFileInfo> hosqFiles;

        public HandoverSequenceInfoForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            init();
        }

        private void init()
        {
            this.olvColumnStatSN.AspectGetter += delegate (object row)
            {
                if (row is HandoverSequenceFileInfo)
                {
                    HandoverSequenceFileInfo file = row as HandoverSequenceFileInfo;
                    return file.Sn;
                }
                else if (row is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = row as HandoverGroupPairInfo;
                    return gp.Sn;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter += delegate (object row)
            {
                if (row is HandoverSequenceFileInfo)
                {
                    HandoverSequenceFileInfo file = row as HandoverSequenceFileInfo;
                    return file.HOSQ.HOSFile.Name;
                }
                return "";
            };

            setNeedDrawData();

            this.treeViewHOSQFiles.CanExpandGetter = delegate (object row)
            {
                return row is HandoverSequenceFileInfo;
            };

            this.treeViewHOSQFiles.ChildrenGetter = delegate (object row)
            {
                if (row is HandoverSequenceFileInfo)
                {
                    HandoverSequenceFileInfo file = row as HandoverSequenceFileInfo;
                    return file.GroupPairLst;
                }
                else
                {
                    return new System.Collections.ArrayList();
                }
            };
        }

        private void setNeedDrawData()
        {
            setCellColor();

            this.olvColumnIdealHOSequence.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.IdealSequence == "") //如果没有值，进行渲染
                    {
                        g.FillRectangle(new SolidBrush(Color.LightGreen), r);
                    }

                    g.DrawString(gp.IdealSequence, this.treeViewHOSQFiles.Font, new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };

            this.olvColumnHOSequence.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.FileSequence == "") //如果没有值，进行渲染
                    {
                        g.FillRectangle(new SolidBrush(Color.LightPink), r);
                    }

                    g.DrawString(gp.FileSequence, this.treeViewHOSQFiles.Font, new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };



            this.olvColumnHOLastDuration.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.FileE == null)
                    {
                        g.FillRectangle(new SolidBrush(Color.LightPink), r);
                    }
                    else
                        g.DrawString(Math.Abs(gp.FileE.EndTime.Subtract(gp.FileE.StartTime).TotalSeconds).ToString(), this.treeViewHOSQFiles.Font,
                            new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };

            this.olvColumnIdealHOLastDuration.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.IdealSC == null)
                    {
                        g.FillRectangle(new SolidBrush(Color.LightGreen), r);
                    }
                    else
                        g.DrawString(gp.IdealSC.LastedSeconds.ToString(), this.treeViewHOSQFiles.Font,
                            new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };
        }

        private void setCellColor()
        {
            this.olvColumnIdealHOLAC.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.IdealLAC == "") //如果没有值，进行渲染
                    {
                        g.FillRectangle(new SolidBrush(Color.LightGreen), r);
                    }

                    g.DrawString(gp.IdealLAC, this.treeViewHOSQFiles.Font, new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };

            this.olvColumnIdealHOCI.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.IdealCI == "") //如果没有值，进行渲染
                    {
                        g.FillRectangle(new SolidBrush(Color.LightGreen), r);
                    }

                    g.DrawString(gp.IdealCI, this.treeViewHOSQFiles.Font, new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };

            this.olvColumnHOLAC.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.FileLAC == "") //如果没有值，进行渲染
                    {
                        g.FillRectangle(new SolidBrush(Color.LightPink), r);
                    }

                    g.DrawString(gp.FileLAC, this.treeViewHOSQFiles.Font, new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };

            this.olvColumnHOCI.RendererDelegate = delegate (EventArgs e, Graphics g, Rectangle r, Object rowObject)
            {
                if (rowObject is HandoverGroupPairInfo)
                {
                    HandoverGroupPairInfo gp = rowObject as HandoverGroupPairInfo;
                    if (gp.FileCI == "") //如果没有值，进行渲染
                    {
                        g.FillRectangle(new SolidBrush(Color.LightPink), r);
                    }

                    g.DrawString(gp.FileCI, this.treeViewHOSQFiles.Font, new SolidBrush(Color.Black), r.X, r.Y);
                }
                return true;
            };
        }

        public void FillData(List<HandoverSequenceFileInfo> hosqFiles)
        {
            this.hosqFiles = hosqFiles;
            this.treeViewHOSQFiles.ClearObjects();
            this.treeViewHOSQFiles.SetObjects(hosqFiles);
            this.treeViewHOSQFiles.Refresh();
        }

        private void ToolStripMenuItemExpandAll_Click(object sender, EventArgs e)
        {
            this.treeViewHOSQFiles.ExpandAll();
        }

        private void ToolStripMenuItemCollapsAll_Click(object sender, EventArgs e)
        {
            this.treeViewHOSQFiles.CollapseAll();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            exportStatListToXls();
        }

        private void exportStatListToXls()
        {
            if (!exportStatListToExcel())
            {
                exportStatListToTxt();
            }
        }

        #region ExportStatList
        private bool exportStatListToExcel()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("文件序号");
            row.cellValues.Add("文件名称");
            row.cellValues.Add("切换序列序号");
            row.cellValues.Add("理想序列小区名称");
            row.cellValues.Add("理想序列LAC");
            row.cellValues.Add("理想序列CI");
            row.cellValues.Add("实际序列小区名称");
            row.cellValues.Add("实际序列LAC");
            row.cellValues.Add("实际序列CI");

            rows.Add(row);
            int rowCount = 1;

            foreach (HandoverSequenceFileInfo fileItem in hosqFiles)
            {
                foreach (HandoverGroupPairInfo pairItem in fileItem.GroupPairLst)
                {
                    rowCount++;
                    if (rowCount > 65535)
                    {
                        return false;
                    }
                    row = new NPOIRow();

                    row.cellValues.Add(fileItem.Sn);
                    row.cellValues.Add(fileItem.HOSQ.HOSFile.Name);
                    row.cellValues.Add(pairItem.Sn);
                    row.cellValues.Add(pairItem.IdealSequence);
                    row.cellValues.Add(pairItem.IdealLAC);
                    row.cellValues.Add(pairItem.IdealCI);
                    row.cellValues.Add(pairItem.FileSequence);
                    row.cellValues.Add(pairItem.FileLAC);
                    row.cellValues.Add(pairItem.FileCI);

                    rows.Add(row);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
            return true;
        }

        private void exportStatListToTxt()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Text file (*.txt)|*.txt";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    WaitBox.Show("正在导出到Txt...", doExportStatListToTxt, dlg.FileName);
                    MessageBox.Show("Txt导出完成！");
                }
                catch (System.Exception e)
                {
                    MessageBox.Show("导出到Txt出错：" + e.Message);
                }
            }
        }

        private void doExportStatListToTxt(object nameObj)
        {
            string fileName = nameObj.ToString();
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeStatListContent(streamWriter);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
                WaitBox.Close();
            }
        }

        private void writeStatListContent(System.IO.StreamWriter streamWriter)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("文件序号\t");
            sb.Append("文件名称\t");
            sb.Append("切换序列序号\t");
            sb.Append("理想序列小区名称\t");
            sb.Append("理想序列LACt");
            sb.Append("理想序列CI\t");
            sb.Append("实际序列小区名称\t");
            sb.Append("实际序列LAC\t");
            sb.Append("实际序列CI\t");

            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;

            foreach (HandoverSequenceFileInfo fileItem in hosqFiles)
            {
                foreach (HandoverGroupPairInfo pairItem in fileItem.GroupPairLst)
                {
                    sb.Append(fileItem.Sn + "\t");
                    sb.Append(fileItem.HOSQ.HOSFile.Name + "\t");
                    sb.Append(pairItem.Sn + "\t");
                    sb.Append(pairItem.IdealSequence + "\t");
                    sb.Append(pairItem.IdealLAC + "\t");
                    sb.Append(pairItem.IdealCI + "\t");
                    sb.Append(pairItem.FileSequence + "\t");
                    sb.Append(pairItem.FileLAC + "\t");
                    sb.Append(pairItem.FileCI + "\t");

                    streamWriter.WriteLine(sb.ToString() + "\t");
                    sb.Remove(0, sb.Length);

                    WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / hosqFiles.Count);
                }
            }
        }
        #endregion

    }
}
