﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanUncoveredAndWeakCoveredSampleForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.dataGridViewPercentage = new System.Windows.Forms.DataGridView();
            this.ColumnTotalCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCountWeakCovered = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCountNoneCovered = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.WeakCoveredSamplePercentage = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.NoneCoveredSamplePercentage = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnAverageRxlev = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.dataGridViewRange = new System.Windows.Forms.DataGridView();
            this.n_100 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._95_100 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._95_90 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._90_85 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._85_80 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._80_75 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._75_70 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._70_65 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this._65_p = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.导出ExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.chartControlRexlv = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPercentage)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewRange)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRexlv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridViewPercentage
            // 
            this.dataGridViewPercentage.AllowUserToAddRows = false;
            this.dataGridViewPercentage.AllowUserToDeleteRows = false;
            this.dataGridViewPercentage.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewPercentage.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewPercentage.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnTotalCount,
            this.ColumnCountWeakCovered,
            this.ColumnCountNoneCovered,
            this.WeakCoveredSamplePercentage,
            this.NoneCoveredSamplePercentage,
            this.ColumnAverageRxlev});
            this.dataGridViewPercentage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewPercentage.Location = new System.Drawing.Point(3, 17);
            this.dataGridViewPercentage.MultiSelect = false;
            this.dataGridViewPercentage.Name = "dataGridViewPercentage";
            this.dataGridViewPercentage.ReadOnly = true;
            this.dataGridViewPercentage.RowHeadersVisible = false;
            this.dataGridViewPercentage.RowHeadersWidth = 100;
            this.dataGridViewPercentage.RowTemplate.Height = 35;
            this.dataGridViewPercentage.Size = new System.Drawing.Size(1092, 94);
            this.dataGridViewPercentage.TabIndex = 0;
            // 
            // ColumnTotalCount
            // 
            this.ColumnTotalCount.HeaderText = "总采样点数";
            this.ColumnTotalCount.Name = "ColumnTotalCount";
            this.ColumnTotalCount.ReadOnly = true;
            // 
            // ColumnCountWeakCovered
            // 
            this.ColumnCountWeakCovered.HeaderText = "弱覆盖采样点数目";
            this.ColumnCountWeakCovered.Name = "ColumnCountWeakCovered";
            this.ColumnCountWeakCovered.ReadOnly = true;
            // 
            // ColumnCountNoneCovered
            // 
            this.ColumnCountNoneCovered.HeaderText = "无覆盖采样点数目";
            this.ColumnCountNoneCovered.Name = "ColumnCountNoneCovered";
            this.ColumnCountNoneCovered.ReadOnly = true;
            // 
            // WeakCoveredSamplePercentage
            // 
            this.WeakCoveredSamplePercentage.HeaderText = "弱覆盖采样点占比";
            this.WeakCoveredSamplePercentage.Name = "WeakCoveredSamplePercentage";
            this.WeakCoveredSamplePercentage.ReadOnly = true;
            // 
            // NoneCoveredSamplePercentage
            // 
            this.NoneCoveredSamplePercentage.HeaderText = "无覆盖采样点占比";
            this.NoneCoveredSamplePercentage.Name = "NoneCoveredSamplePercentage";
            this.NoneCoveredSamplePercentage.ReadOnly = true;
            // 
            // ColumnAverageRxlev
            // 
            this.ColumnAverageRxlev.HeaderText = "采样点平均电平(dBm)";
            this.ColumnAverageRxlev.Name = "ColumnAverageRxlev";
            this.ColumnAverageRxlev.ReadOnly = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.dataGridViewRange);
            this.groupBox1.Location = new System.Drawing.Point(14, 148);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1098, 138);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "采样点按电平范围分布(单位：个)";
            // 
            // dataGridViewRange
            // 
            this.dataGridViewRange.AllowUserToAddRows = false;
            this.dataGridViewRange.AllowUserToDeleteRows = false;
            this.dataGridViewRange.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewRange.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewRange.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.n_100,
            this._95_100,
            this._95_90,
            this._90_85,
            this._85_80,
            this._80_75,
            this._75_70,
            this._70_65,
            this._65_p});
            this.dataGridViewRange.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewRange.Location = new System.Drawing.Point(3, 18);
            this.dataGridViewRange.Name = "dataGridViewRange";
            this.dataGridViewRange.ReadOnly = true;
            this.dataGridViewRange.RowHeadersVisible = false;
            this.dataGridViewRange.RowHeadersWidth = 100;
            this.dataGridViewRange.RowTemplate.Height = 23;
            this.dataGridViewRange.Size = new System.Drawing.Size(1092, 117);
            this.dataGridViewRange.TabIndex = 2;
            // 
            // n_100
            // 
            this.n_100.HeaderText = "(–∞,–100)";
            this.n_100.Name = "n_100";
            this.n_100.ReadOnly = true;
            // 
            // _95_100
            // 
            this._95_100.HeaderText = "[–100,–95)";
            this._95_100.Name = "_95_100";
            this._95_100.ReadOnly = true;
            // 
            // _95_90
            // 
            this._95_90.HeaderText = "[–95,–90)";
            this._95_90.Name = "_95_90";
            this._95_90.ReadOnly = true;
            // 
            // _90_85
            // 
            this._90_85.HeaderText = "[–90,–85)";
            this._90_85.Name = "_90_85";
            this._90_85.ReadOnly = true;
            // 
            // _85_80
            // 
            this._85_80.HeaderText = "[–85,–80)";
            this._85_80.Name = "_85_80";
            this._85_80.ReadOnly = true;
            // 
            // _80_75
            // 
            this._80_75.HeaderText = "[–80,-75)";
            this._80_75.Name = "_80_75";
            this._80_75.ReadOnly = true;
            // 
            // _75_70
            // 
            this._75_70.HeaderText = "[–75,-70)";
            this._75_70.Name = "_75_70";
            this._75_70.ReadOnly = true;
            // 
            // _70_65
            // 
            this._70_65.HeaderText = "[–70,-65)";
            this._70_65.Name = "_70_65";
            this._70_65.ReadOnly = true;
            // 
            // _65_p
            // 
            this._65_p.HeaderText = "[–65, +∞)";
            this._65_p.Name = "_65_p";
            this._65_p.ReadOnly = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.dataGridViewPercentage);
            this.groupBox2.Font = new System.Drawing.Font("宋体", 9F);
            this.groupBox2.Location = new System.Drawing.Point(14, 14);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1098, 114);
            this.groupBox2.TabIndex = 3;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "采样点占比";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.导出ExcelToolStripMenuItem});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // 导出ExcelToolStripMenuItem
            // 
            this.导出ExcelToolStripMenuItem.Name = "导出ExcelToolStripMenuItem";
            this.导出ExcelToolStripMenuItem.Size = new System.Drawing.Size(129, 22);
            this.导出ExcelToolStripMenuItem.Text = "导出Excel";
            this.导出ExcelToolStripMenuItem.Click += new System.EventHandler(this.tsmiExport2Xls_Click);
            // 
            // chartControlRexlv
            // 
            this.chartControlRexlv.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRexlv.Diagram = xyDiagram1;
            this.chartControlRexlv.Location = new System.Drawing.Point(17, 295);
            this.chartControlRexlv.Name = "chartControlRexlv";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            sideBySideBarSeriesLabel2.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel2;
            series2.Name = "Series 2";
            this.chartControlRexlv.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1,
        series2};
            sideBySideBarSeriesLabel3.LineVisible = true;
            this.chartControlRexlv.SeriesTemplate.Label = sideBySideBarSeriesLabel3;
            this.chartControlRexlv.Size = new System.Drawing.Size(1091, 233);
            this.chartControlRexlv.TabIndex = 4;
            // 
            // ScanUncoveredAndWeakCoveredSampleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1126, 542);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.chartControlRexlv);
            this.Controls.Add(this.groupBox1);
            this.Name = "ScanUncoveredAndWeakCoveredSampleForm";
            this.Text = "扫频覆盖采样点占比信息";
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPercentage)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewRange)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRexlv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridViewPercentage;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnTotalCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCountWeakCovered;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCountNoneCovered;
        private System.Windows.Forms.DataGridViewTextBoxColumn WeakCoveredSamplePercentage;
        private System.Windows.Forms.DataGridViewTextBoxColumn NoneCoveredSamplePercentage;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnAverageRxlev;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem 导出ExcelToolStripMenuItem;
        private System.Windows.Forms.DataGridView dataGridViewRange;
        private DevExpress.XtraCharts.ChartControl chartControlRexlv;
        private System.Windows.Forms.DataGridViewTextBoxColumn n_100;
        private System.Windows.Forms.DataGridViewTextBoxColumn _95_100;
        private System.Windows.Forms.DataGridViewTextBoxColumn _95_90;
        private System.Windows.Forms.DataGridViewTextBoxColumn _90_85;
        private System.Windows.Forms.DataGridViewTextBoxColumn _85_80;
        private System.Windows.Forms.DataGridViewTextBoxColumn _80_75;
        private System.Windows.Forms.DataGridViewTextBoxColumn _75_70;
        private System.Windows.Forms.DataGridViewTextBoxColumn _70_65;
        private System.Windows.Forms.DataGridViewTextBoxColumn _65_p;
    }
}