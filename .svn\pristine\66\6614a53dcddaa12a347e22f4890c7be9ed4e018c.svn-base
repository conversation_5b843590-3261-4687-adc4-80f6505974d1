﻿using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class LteFddStationAcceptQuery : LteStationAcceptQuery
    {
        public LteFddStationAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "LTEFDD宏站验收";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22125, Name);
        }

        protected override bool isValidCondition()
        {
            LteStationAcceptCondition cond = new LteStationAcceptCondition();
            System.Windows.Forms.FolderBrowserDialog fbd = new System.Windows.Forms.FolderBrowserDialog();
            if (fbd.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                cond.SaveFolder = fbd.SelectedPath;
                initManager(cond);
                return true;
            }
            return false;
        }

        protected override void initManager(LteStationAcceptCondition cond)
        {
            manager = new LteFddStationAcceptManager();
            manager.SetAcceptCond(cond);
        }

        protected override void query()
        {
            errMsg = null;
            base.query();
        }

        protected override bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0 || !fileInfo.Name.Contains("-FDD"))
            {
                //由于FDD文件也会入成TDD,所以不能通过业务类型来区分,仅单验文件名包含FDD的文件
                //不能区分FDD的室分和小站文件
                return false;
            }
            return true;
        }
    }
}
