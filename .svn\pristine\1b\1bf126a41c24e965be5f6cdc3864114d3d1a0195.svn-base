﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class PoorQualityRoadSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.numMaxDistance = new System.Windows.Forms.NumericUpDown();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.numRxQualityThreshold = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.chkPercent = new DevExpress.XtraEditors.CheckEdit();
            this.numPercent = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxQualityThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPercent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPercent)).BeginInit();
            this.SuspendLayout();
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(212, 110);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 21;
            this.label7.Text = "米";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(17, 109);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(107, 12);
            this.label6.TabIndex = 20;
            this.label6.Text = "相邻采样点距离 ≤";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(139, 137);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 16;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(49, 137);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 17;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.Location = new System.Drawing.Point(127, 105);
            this.numMaxDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Size = new System.Drawing.Size(81, 21);
            this.numMaxDistance.TabIndex = 15;
            this.numMaxDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // numDistance
            // 
            this.numDistance.Location = new System.Drawing.Point(127, 76);
            this.numDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(81, 21);
            this.numDistance.TabIndex = 13;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // numRxQualityThreshold
            // 
            this.numRxQualityThreshold.Location = new System.Drawing.Point(126, 13);
            this.numRxQualityThreshold.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numRxQualityThreshold.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.numRxQualityThreshold.Name = "numRxQualityThreshold";
            this.numRxQualityThreshold.Size = new System.Drawing.Size(82, 21);
            this.numRxQualityThreshold.TabIndex = 14;
            this.numRxQualityThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxQualityThreshold.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(212, 80);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "米";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(53, 80);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(71, 12);
            this.label2.TabIndex = 11;
            this.label2.Text = "持续距离 ≥";
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(12, 15);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(112, 12);
            this.label1.TabIndex = 10;
            this.label1.Text = "RxQuality ≥";
            this.label1.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // chkPercent
            // 
            this.chkPercent.Location = new System.Drawing.Point(36, 48);
            this.chkPercent.Name = "chkPercent";
            this.chkPercent.Properties.Caption = "质差占比 ≥";
            this.chkPercent.Size = new System.Drawing.Size(91, 19);
            this.chkPercent.TabIndex = 22;
            this.chkPercent.CheckedChanged += new System.EventHandler(this.chkPercent_CheckedChanged);
            // 
            // numPercent
            // 
            this.numPercent.Enabled = false;
            this.numPercent.Location = new System.Drawing.Point(126, 46);
            this.numPercent.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numPercent.Name = "numPercent";
            this.numPercent.Size = new System.Drawing.Size(81, 21);
            this.numPercent.TabIndex = 23;
            this.numPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPercent.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(213, 50);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(11, 12);
            this.label3.TabIndex = 24;
            this.label3.Text = "%";
            // 
            // PoorQualityRoadSettingForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(249, 208);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numPercent);
            this.Controls.Add(this.chkPercent);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numMaxDistance);
            this.Controls.Add(this.numDistance);
            this.Controls.Add(this.numRxQualityThreshold);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Name = "PoorQualityRoadSettingForm";
            this.Text = "质差路段分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxQualityThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPercent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPercent)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.NumericUpDown numMaxDistance;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.NumericUpDown numRxQualityThreshold;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.CheckEdit chkPercent;
        private System.Windows.Forms.NumericUpDown numPercent;
        private System.Windows.Forms.Label label3;

    }
}