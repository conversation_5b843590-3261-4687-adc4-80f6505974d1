﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTVoLteESRVCCAna
{
    public class ESRVCCAnaItem
    {
        public string FileName { get; set; }
        public int SN { get; set; }

        /// <summary>
        /// eSRVCC是否成功
        /// </summary>
        public string ESRVCCResult { get; set; }

        public Event EvtBegin { get; set; }       //开始事件-切换开始
        public Event EvtMid { get; set; }   //中间事件-切换成功
        public Event EvtEnd { get; set; }    //结束事件-切换失败或TAU成功或失败

        public TestPoint TpBegin { get; set; }

        public TestPoint TpMid { get; set; }

        public List<TestPoint> TestPoints { get; set; }
        public List<Event> Events { get; set; }
        public List<Message> Messages { get; set; } = new List<Message>();

        public Message MsgMeasurementReportLastFirst { get; set; } 
        public Message MsgHandoverCommandFirst { get; set; } 

        public Message MsgHandoverCommand { get; set; } 
        public Message MsgHandoverComplete { get; set; } 
        public Message MsgChannelRelease { get; set; } 
        public Message MsgTAUAccept { get; set; } 
        public Message MsgRRCConnectionRelease { get; set; } 
        public Message MsgRAUAccept { get; set; } 

        public Message MsgLtePacketDown { get; set; } 
        public Message MsgLtePacketUp { get; set; } 
        public Message MsgGsmPacketDown { get; set; } 
        public Message MsgGsmPacketUp { get; set; } 

        public bool IsGotBegin { get; set; }

        public string ESRVCCDelayTime { get; set; }

        /// <summary>
        /// LTE频点,切换前的LTE网络EARFCN
        /// </summary>
        public int? LteEARFCN { get; set; }
        public int? LtePCI { get; set; }
        public float? LteRSRP { get; set; }
        public float? LteSINR { get; set; }


        /// <summary>
        /// 目标GSM频点
        /// </summary>
        public int? GsmEARFCN { get; set; }
        public int? BSIC { get; set; }
        public float? RexLevel { get; set; }
        public float? C2I { get; set; }

        /// <summary>
        /// 失败现象及原因
        /// </summary>
        public string CallFailReason { get; set; }

        /// <summary>
        /// 挂机后自主FR是否成功返回
        /// </summary>
        public string FRIsBack { get; set; }

        /// <summary>
        /// LTE测控消息中包含的2G频点
        /// </summary>
        public int? LteContGsmEARFCN { get; set; }

        /// <summary>
        /// 切换准备时延
        /// </summary>
        public string HandoverPrepTimes { get; set; }

        /// <summary>
        /// 切换控制面中断时延
        /// </summary>
        public string HandoverCtrlSuspendTimes { get; set; }

        /// <summary>
        /// 切换用户面下行中断时延: 终端在4G收到的最后一个下行RTP包->终端在2G收到的第一个下行语音包
        /// </summary>
        public string HandoverDownDelayTimes { get; set; }
        public string HandoverUpDelayTimes { get; set; }
        public string HandoverDown2UpDelayTimes { get; set; }

        /// <summary>
        /// 返回控制面时延: 2G channel Release—> TAU Accept
        /// </summary>
        public string BackCtrlDelayTimes { get; set; }
        public string CtrlDelayTimes2To3 { get; set; }
        public string CtrlDelayTimes3To4 { get; set; }

        public ESRVCCAnaItem(string fileName)
        {
            this.FileName = fileName;
            IsGotBegin = false;
            TestPoints = new List<TestPoint>();
            Events = new List<Event>();
        }

        public void AddBeginEvtInfo(TestPoint tpBegin, Event evtESRVCCRequest)
        {
            TpBegin = tpBegin;
            EvtBegin = evtESRVCCRequest;
            IsGotBegin = true;
        }

        public void AddEndEvtInfo(Event evtESRVCC)
        {
            EvtEnd = evtESRVCC;
        }

        public void AddMidEvtInfo(Event evtESRVCC)
        {
            EvtMid = evtESRVCC;
        }
    }
}
