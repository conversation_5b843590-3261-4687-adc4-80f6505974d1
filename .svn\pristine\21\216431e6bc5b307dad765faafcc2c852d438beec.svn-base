﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class AnaByRulesOtherSetDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtExcelAutoPath = new System.Windows.Forms.TextBox();
            this.btnExcelAutoPath = new DevExpress.XtraEditors.SimpleButton();
            this.btnBeginUpLoad = new DevExpress.XtraEditors.SimpleButton();
            this.chkFileCountCheck = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numAutoDuration = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chkInAndOutSrc = new DevExpress.XtraEditors.CheckEdit();
            this.chkLeakOutRatioOrOtherSet = new DevExpress.XtraEditors.CheckEdit();
            this.chkHandOver = new DevExpress.XtraEditors.CheckEdit();
            this.btnApply = new DevExpress.XtraEditors.SimpleButton();
            this.cmbOutAndIndoor = new System.Windows.Forms.ComboBox();
            this.chkVolteVideoAllCall = new DevExpress.XtraEditors.CheckEdit();
            this.chkSrvcc = new DevExpress.XtraEditors.CheckEdit();
            this.chkVoLteVideoMoCall = new DevExpress.XtraEditors.CheckEdit();
            this.chkVoLteVoiceMoCall = new DevExpress.XtraEditors.CheckEdit();
            this.chkVolteVoiceAllCall = new DevExpress.XtraEditors.CheckEdit();
            this.chkCSFB = new DevExpress.XtraEditors.CheckEdit();
            this.groupBoxAutoUL = new System.Windows.Forms.GroupBox();
            this.panelAutoUL = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)(this.chkFileCountCheck.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAutoDuration.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkInAndOutSrc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLeakOutRatioOrOtherSet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHandOver.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVolteVideoAllCall.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSrvcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVoLteVideoMoCall.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVoLteVoiceMoCall.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVolteVoiceAllCall.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCSFB.Properties)).BeginInit();
            this.groupBoxAutoUL.SuspendLayout();
            this.panelAutoUL.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(269, 354);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 53;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(350, 354);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 54;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(15, 9);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(200, 14);
            this.labelControl4.TabIndex = 55;
            this.labelControl4.Text = "自动上传新站文件数量的Excel 路径：";
            // 
            // txtExcelAutoPath
            // 
            this.txtExcelAutoPath.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtExcelAutoPath.Location = new System.Drawing.Point(84, 40);
            this.txtExcelAutoPath.Name = "txtExcelAutoPath";
            this.txtExcelAutoPath.ReadOnly = true;
            this.txtExcelAutoPath.Size = new System.Drawing.Size(299, 21);
            this.txtExcelAutoPath.TabIndex = 57;
            // 
            // btnExcelAutoPath
            // 
            this.btnExcelAutoPath.Location = new System.Drawing.Point(15, 34);
            this.btnExcelAutoPath.Name = "btnExcelAutoPath";
            this.btnExcelAutoPath.Size = new System.Drawing.Size(52, 27);
            this.btnExcelAutoPath.TabIndex = 56;
            this.btnExcelAutoPath.Text = "选择";
            this.btnExcelAutoPath.Click += new System.EventHandler(this.btnExcelAutoPath_Click);
            // 
            // btnBeginUpLoad
            // 
            this.btnBeginUpLoad.Location = new System.Drawing.Point(221, 3);
            this.btnBeginUpLoad.Name = "btnBeginUpLoad";
            this.btnBeginUpLoad.Size = new System.Drawing.Size(67, 27);
            this.btnBeginUpLoad.TabIndex = 59;
            this.btnBeginUpLoad.Text = "立即上传";
            this.btnBeginUpLoad.Click += new System.EventHandler(this.btnBeginUpLoad_Click);
            // 
            // chkFileCountCheck
            // 
            this.chkFileCountCheck.EditValue = true;
            this.chkFileCountCheck.Location = new System.Drawing.Point(6, -2);
            this.chkFileCountCheck.Name = "chkFileCountCheck";
            this.chkFileCountCheck.Properties.Caption = "开启自动上传新站文件数量、分析时核对文件数量";
            this.chkFileCountCheck.Size = new System.Drawing.Size(290, 19);
            this.chkFileCountCheck.TabIndex = 60;
            this.chkFileCountCheck.CheckedChanged += new System.EventHandler(this.chkFileCountCheck_CheckedChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(12, 12);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(108, 14);
            this.labelControl1.TabIndex = 62;
            this.labelControl1.Text = "自动验收执行间隔：";
            // 
            // numAutoDuration
            // 
            this.numAutoDuration.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numAutoDuration.Location = new System.Drawing.Point(126, 9);
            this.numAutoDuration.Name = "numAutoDuration";
            this.numAutoDuration.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numAutoDuration.Properties.IsFloatValue = false;
            this.numAutoDuration.Properties.Mask.EditMask = "N00";
            this.numAutoDuration.Properties.MaxValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numAutoDuration.Size = new System.Drawing.Size(71, 21);
            this.numAutoDuration.TabIndex = 64;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(203, 12);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(24, 14);
            this.labelControl2.TabIndex = 65;
            this.labelControl2.Text = "分钟";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chkInAndOutSrc);
            this.groupBox1.Controls.Add(this.chkLeakOutRatioOrOtherSet);
            this.groupBox1.Controls.Add(this.chkHandOver);
            this.groupBox1.Controls.Add(this.btnApply);
            this.groupBox1.Controls.Add(this.cmbOutAndIndoor);
            this.groupBox1.Controls.Add(this.chkVolteVideoAllCall);
            this.groupBox1.Controls.Add(this.chkSrvcc);
            this.groupBox1.Controls.Add(this.chkVoLteVideoMoCall);
            this.groupBox1.Controls.Add(this.chkVoLteVoiceMoCall);
            this.groupBox1.Controls.Add(this.chkVolteVoiceAllCall);
            this.groupBox1.Controls.Add(this.chkCSFB);
            this.groupBox1.Location = new System.Drawing.Point(12, 40);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(413, 194);
            this.groupBox1.TabIndex = 66;
            this.groupBox1.TabStop = false;
            // 
            // chkInAndOutSrc
            // 
            this.chkInAndOutSrc.Location = new System.Drawing.Point(243, 133);
            this.chkInAndOutSrc.Name = "chkInAndOutSrc";
            this.chkInAndOutSrc.Properties.Caption = "室内外占用";
            this.chkInAndOutSrc.Size = new System.Drawing.Size(98, 19);
            this.chkInAndOutSrc.TabIndex = 60;
            this.chkInAndOutSrc.Visible = false;
            // 
            // chkLeakOutRatioOrOtherSet
            // 
            this.chkLeakOutRatioOrOtherSet.Location = new System.Drawing.Point(21, 166);
            this.chkLeakOutRatioOrOtherSet.Name = "chkLeakOutRatioOrOtherSet";
            this.chkLeakOutRatioOrOtherSet.Properties.Caption = "室外站RSRP弱覆盖、下载传低速率、SINR质差占比";
            this.chkLeakOutRatioOrOtherSet.Size = new System.Drawing.Size(349, 19);
            this.chkLeakOutRatioOrOtherSet.TabIndex = 59;
            // 
            // chkHandOver
            // 
            this.chkHandOver.Location = new System.Drawing.Point(21, 133);
            this.chkHandOver.Name = "chkHandOver";
            this.chkHandOver.Properties.Caption = "切换成功率";
            this.chkHandOver.Size = new System.Drawing.Size(87, 19);
            this.chkHandOver.TabIndex = 58;
            // 
            // btnApply
            // 
            this.btnApply.Location = new System.Drawing.Point(145, 0);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(52, 22);
            this.btnApply.TabIndex = 57;
            this.btnApply.Text = "应用";
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // cmbOutAndIndoor
            // 
            this.cmbOutAndIndoor.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbOutAndIndoor.FormattingEnabled = true;
            this.cmbOutAndIndoor.Items.AddRange(new object[] {
            "  室外站指标考核",
            "  室内站指标考核"});
            this.cmbOutAndIndoor.Location = new System.Drawing.Point(8, 0);
            this.cmbOutAndIndoor.Name = "cmbOutAndIndoor";
            this.cmbOutAndIndoor.Size = new System.Drawing.Size(136, 22);
            this.cmbOutAndIndoor.TabIndex = 36;
            this.cmbOutAndIndoor.SelectedIndexChanged += new System.EventHandler(this.cmbOutAndIndoor_SelectedIndexChanged);
            // 
            // chkVolteVideoAllCall
            // 
            this.chkVolteVideoAllCall.Location = new System.Drawing.Point(21, 100);
            this.chkVolteVideoAllCall.Name = "chkVolteVideoAllCall";
            this.chkVolteVideoAllCall.Properties.Caption = "VoLTE视频全程呼叫成功率";
            this.chkVolteVideoAllCall.Size = new System.Drawing.Size(178, 19);
            this.chkVolteVideoAllCall.TabIndex = 35;
            // 
            // chkSrvcc
            // 
            this.chkSrvcc.Location = new System.Drawing.Point(243, 35);
            this.chkSrvcc.Name = "chkSrvcc";
            this.chkSrvcc.Properties.Caption = "SRVCC切换成功率";
            this.chkSrvcc.Size = new System.Drawing.Size(127, 19);
            this.chkSrvcc.TabIndex = 34;
            // 
            // chkVoLteVideoMoCall
            // 
            this.chkVoLteVideoMoCall.Location = new System.Drawing.Point(243, 100);
            this.chkVoLteVideoMoCall.Name = "chkVoLteVideoMoCall";
            this.chkVoLteVideoMoCall.Properties.Caption = "VoLTE视频接通率";
            this.chkVoLteVideoMoCall.Size = new System.Drawing.Size(127, 19);
            this.chkVoLteVideoMoCall.TabIndex = 33;
            // 
            // chkVoLteVoiceMoCall
            // 
            this.chkVoLteVoiceMoCall.Location = new System.Drawing.Point(243, 68);
            this.chkVoLteVoiceMoCall.Name = "chkVoLteVoiceMoCall";
            this.chkVoLteVoiceMoCall.Properties.Caption = "VoLTE语音接通率";
            this.chkVoLteVoiceMoCall.Size = new System.Drawing.Size(127, 19);
            this.chkVoLteVoiceMoCall.TabIndex = 32;
            // 
            // chkVolteVoiceAllCall
            // 
            this.chkVolteVoiceAllCall.Location = new System.Drawing.Point(21, 68);
            this.chkVolteVoiceAllCall.Name = "chkVolteVoiceAllCall";
            this.chkVolteVoiceAllCall.Properties.Caption = "VoLTE语音全程呼叫成功率";
            this.chkVolteVoiceAllCall.Size = new System.Drawing.Size(178, 19);
            this.chkVolteVoiceAllCall.TabIndex = 31;
            // 
            // chkCSFB
            // 
            this.chkCSFB.Location = new System.Drawing.Point(21, 35);
            this.chkCSFB.Name = "chkCSFB";
            this.chkCSFB.Properties.Caption = "CSFB全程呼叫成功率";
            this.chkCSFB.Size = new System.Drawing.Size(163, 19);
            this.chkCSFB.TabIndex = 30;
            // 
            // groupBoxAutoUL
            // 
            this.groupBoxAutoUL.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxAutoUL.Controls.Add(this.panelAutoUL);
            this.groupBoxAutoUL.Controls.Add(this.chkFileCountCheck);
            this.groupBoxAutoUL.Location = new System.Drawing.Point(12, 242);
            this.groupBoxAutoUL.Name = "groupBoxAutoUL";
            this.groupBoxAutoUL.Size = new System.Drawing.Size(413, 106);
            this.groupBoxAutoUL.TabIndex = 67;
            this.groupBoxAutoUL.TabStop = false;
            this.groupBoxAutoUL.Text = "                                                ";
            // 
            // panelAutoUL
            // 
            this.panelAutoUL.Controls.Add(this.labelControl4);
            this.panelAutoUL.Controls.Add(this.btnExcelAutoPath);
            this.panelAutoUL.Controls.Add(this.btnBeginUpLoad);
            this.panelAutoUL.Controls.Add(this.txtExcelAutoPath);
            this.panelAutoUL.Location = new System.Drawing.Point(8, 21);
            this.panelAutoUL.Name = "panelAutoUL";
            this.panelAutoUL.Size = new System.Drawing.Size(394, 79);
            this.panelAutoUL.TabIndex = 68;
            // 
            // AnaByRulesOtherSetDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(439, 389);
            this.Controls.Add(this.groupBoxAutoUL);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.numAutoDuration);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "AnaByRulesOtherSetDlg";
            this.Text = "单站验收按规则分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.chkFileCountCheck.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAutoDuration.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkInAndOutSrc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLeakOutRatioOrOtherSet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHandOver.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVolteVideoAllCall.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSrvcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVoLteVideoMoCall.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVoLteVoiceMoCall.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkVolteVoiceAllCall.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCSFB.Properties)).EndInit();
            this.groupBoxAutoUL.ResumeLayout(false);
            this.panelAutoUL.ResumeLayout(false);
            this.panelAutoUL.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private System.Windows.Forms.TextBox txtExcelAutoPath;
        private DevExpress.XtraEditors.SimpleButton btnExcelAutoPath;
        private DevExpress.XtraEditors.SimpleButton btnBeginUpLoad;
        private DevExpress.XtraEditors.CheckEdit chkFileCountCheck;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numAutoDuration;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.CheckEdit chkVolteVoiceAllCall;
        private DevExpress.XtraEditors.CheckEdit chkCSFB;
        private DevExpress.XtraEditors.CheckEdit chkVoLteVideoMoCall;
        private DevExpress.XtraEditors.CheckEdit chkVoLteVoiceMoCall;
        private DevExpress.XtraEditors.CheckEdit chkSrvcc;
        private System.Windows.Forms.GroupBox groupBoxAutoUL;
        private System.Windows.Forms.Panel panelAutoUL;
        private DevExpress.XtraEditors.CheckEdit chkVolteVideoAllCall;
        private System.Windows.Forms.ComboBox cmbOutAndIndoor;
        private DevExpress.XtraEditors.SimpleButton btnApply;
        private DevExpress.XtraEditors.CheckEdit chkHandOver;
        private DevExpress.XtraEditors.CheckEdit chkLeakOutRatioOrOtherSet;
        private DevExpress.XtraEditors.CheckEdit chkInAndOutSrc;
    }
}