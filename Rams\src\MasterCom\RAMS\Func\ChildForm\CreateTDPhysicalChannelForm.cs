﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateTDPhysicalChannelForm : CreateChildForm
    {
        public CreateTDPhysicalChannelForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建TD Physical Channel图窗口  ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20035, this.Name);
        }
        public override string Name
        {
            get
            {
                return Description;
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.TDPhysicalChannelForm";
            actionParam["Text"] = "TD Physical Channel";
            actionParam["ImageFilePath"] = @"images\GSM频率核查\同邻频.png";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
