﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PingPangSettingForm : ZTHandOverAndCellReselSetForm
    {
        public PingPangSettingForm()
        {
            InitializeComponent();
        }

        public PingPangCondition GetCondition()
        {
            ZTHandOverAndCellReselCondition cond = null;
            base.GetCondition(out cond);
            PingPangCondition ppCond = new PingPangCondition();
            ppCond.IsLimitSpeed = cond.IsLimitSpeed;
            ppCond.SpeedLimitMax = cond.SpeedLimitMax;
            ppCond.SpeedLimitMin = cond.SpeedLimitMin;
            ppCond.TimeLimit = cond.TimeLimit;
            return ppCond;
        }
    }
    public class PingPangCondition : ZTHandOverAndCellReselCondition
    {
    }
}
