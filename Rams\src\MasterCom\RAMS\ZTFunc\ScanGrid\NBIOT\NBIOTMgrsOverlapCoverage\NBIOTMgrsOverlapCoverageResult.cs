﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsOverlapCoverageResult : NbIotMgrsResultControlBase
    {
        protected NbIotMgrsFuncItem funcItem = null;
        protected ZTScanGridCoverageLayer layer;
        protected List<CoverageRegion> scanGridInfoList = new List<CoverageRegion>();

        public NbIotMgrsOverlapCoverageResult()
        {
            InitializeComponent();
            InitCbxFreqType();
            cbxFreqType.SelectedIndexChanged += CbxFreqType_SelectedChanged;
            miExportExcel.Click += MiExportSimpleExcel_Click;
            miEditRange.Click += MiEditRange_Click;

            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportAllShp.Click += base.MiExportShpAll_Click;
        }

        public override string Desc
        {
            get { return "重叠覆盖度分布"; }
        }

        public void FillData(NbIotMgrsFuncItem curFuncItem)
        {
            this.funcItem = curFuncItem;
            RefreshResult();
        }

        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = Desc;
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            ExcelNPOIManager.ExportToExcel(gv, fileName, sheetName);
        }

        private void RefreshResult()
        {
            NbIotMgrsOverlapCoverageStater stater = this.funcItem.Stater as NbIotMgrsOverlapCoverageStater;
            string type = cbxFreqType.SelectedItem as string;

            DataSet ds = stater.GetDataSet(type);
            scanGridInfoList = stater.GetCoverageRegionList();
            gridControl1.DataSource = ds.Tables[0];
        }

        private void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void gridView1_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle > -1)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        protected virtual void GotoSelectedViewGV(CoverageRegion coverageRegion, int zoom)
        {
            double fLong = coverageRegion.CentLng;
            double fLat = coverageRegion.CentLat;
            if (zoom == 0)
            {
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat);
            }
            else
            {
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat, zoom);
            }
        }

        public override void DrawOnLayer()
        {
            mf = MainModel.MainForm.GetMapForm();
            LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridCoverageLayer));
            layer = clayer as ZTScanGridCoverageLayer;
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
            ZTScanGridCoverageLayer.IsActived = true;
            mf.updateMap();
            MainModel.RefreshLegend();
            if (scanGridInfoList.Count > 0)
            {
                GotoSelectedViewGV(scanGridInfoList[0], 0);
            }
        }

        public override void LayerDataClear()
        {
            layer.GridInfos = null;
            layer.SelectedGrid = null;
            ZTScanGridCoverageLayer.IsActived = false;
        }

        private void MiEditRange_Click(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(ZTScanGridCoverageLayer.Ranges.Minimum, ZTScanGridCoverageLayer.Ranges.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(ZTScanGridCoverageLayer.Ranges.ColorRanges);
            dlg.InvalidatePointColor = ZTScanGridCoverageLayer.Ranges.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }
            ZTScanGridCoverageLayer.Ranges.ColorRanges = new List<MControls.ColorRange>(dlg.ColorRanges);
            ZTScanGridCoverageLayer.Ranges.InvalidColor = dlg.InvalidatePointColor;
            ZTScanGridCoverageLayer.Ranges.SaveColorRange("Coverage");

            DrawOnLayer();
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();          
            foreach (var item in NbIotMgrsBaseSettingManager.Instance.BandType.BandType)
            {
                cbxFreqType.Items.Add(item.Value);
            }

            cbxFreqType.SelectedIndex = 0;
        }

        private void CbxFreqType_SelectedChanged(object sender, EventArgs e)
        {
            RefreshResult();
            DrawOnLayer();
        }
    }
}
