﻿using DevExpress.Accessibility;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverFreqBandAnaHelper : HandOverFreqBandAnaHelperBase
    {
        Dictionary<string, LteHandOverFreqBandAnaResult> ResultDic { get; set; }
            = new Dictionary<string, LteHandOverFreqBandAnaResult>();
        DTFileDataManager dtFileInfo;
        LteHandOverFreqBandAnaCond condition;

        public void Init(LteHandOverFreqBandAnaCond condition)
        {
            this.condition = condition;
            intEvtList();
        }

        protected override void intEvtList()
        {
            handOverEvtList.Add(851);
            handOverEvtList.Add(899);
        }

        public override void DealWithData(DTFileDataManager file)
        {
            dtFileInfo = file;
            int idx = 0;
            foreach (var evt in file.Events)
            {
                if (handOverEvtList.Contains(evt.ID))
                {
                    int srcEarfcn = getHandOverSrcArfcn(evt);
                    int tarEarfcn = getHandOverTargetArfcn(evt);
                    if (srcEarfcn != 0 && tarEarfcn != 0)
                    {
                        List<string> srcFreqBandList = new List<string>();
                        List<string> tarFreqBandList = new List<string>();
                        getHandOverFreqBand(srcEarfcn, tarEarfcn, srcFreqBandList, tarFreqBandList);
                        dealHandOverResult(evt, ref idx, srcEarfcn, tarEarfcn, srcFreqBandList, tarFreqBandList);
                    }
                }
            }
        }

        public List<LteHandOverFreqBandAnaResult> GetReult()
        {
            foreach (var item in ResultDic.Values)
            {
                item.Calculate();
            }
            return new List<LteHandOverFreqBandAnaResult>(ResultDic.Values);
        }

        /// <summary>
        /// 获取切换前后频段
        /// </summary>
        private void getHandOverFreqBand(int srcEarfcn, int tarEarfcn
            , List<string> srcFreqBandList, List<string> tarFreqBandList)
        {
            foreach (var freqBand in condition.FreqBands.FreqBandInfoList)
            {
                if (freqBand.Enable)
                {
                    string srcFreqBand = getFreqBandName(freqBand, srcEarfcn);
                    string tarFreqBand = getFreqBandName(freqBand, tarEarfcn);

                    if (!string.IsNullOrEmpty(srcFreqBand))
                    {
                        srcFreqBandList.Add(srcFreqBand);
                    }
                    if (!string.IsNullOrEmpty(tarFreqBand))
                    {
                        tarFreqBandList.Add(tarFreqBand);
                    }
                }
            }
        }

        private string getFreqBandName(FreqBandInfo freqBand, int earfcn)
        {
            foreach (var freq in freqBand.RangeList)
            {
                if (freq.Enable && freq.JudgeInRange(earfcn))
                {
                    return freq.FreqBandName;
                }
            }
            return "";
        }

        private void dealHandOverResult(Event evt, ref int idx, int srcEarfcn, int tarEarfcn
            , List<string> srcFreqBandList, List<string> tarFreqBandList)
        {
            LteHandOverFreqBandAnaResult.BandHandOverInfo handOverInfo = new LteHandOverFreqBandAnaResult.BandHandOverInfo(evt);
            handOverInfo.SrcHandOverInfo.Earfcn = srcEarfcn;
            handOverInfo.TarHandOverInfo.Earfcn = tarEarfcn;

            LteHandOverFreqBandAnaResult.HandOverInfo beforeHandOverTPInfo = getBeforeHandOverTPInfo(evt, ref idx, dtFileInfo.TestPoints);
            handOverInfo.SrcHandOverInfo.Fill(beforeHandOverTPInfo);
            LteHandOverFreqBandAnaResult.HandOverInfo afterHandOverTPInfo = getAfterHandOverTPInfo(evt, idx, dtFileInfo.TestPoints);
            handOverInfo.TarHandOverInfo.Fill(afterHandOverTPInfo);

            foreach (var src in srcFreqBandList)
            {
                LteHandOverFreqBandAnaResult result;
                if (!ResultDic.TryGetValue(src, out result))
                {
                    result = new LteHandOverFreqBandAnaResult(src);
                    ResultDic.Add(src, result);
                }
                handOverInfo.SrcHandOverInfo.FreqBand = src;

                dealHandOverType(tarFreqBandList, handOverInfo, src, result);
            }
        }

        private void dealHandOverType(List<string> tarFreqBandList, LteHandOverFreqBandAnaResult.BandHandOverInfo handOverInfo, string src, LteHandOverFreqBandAnaResult result)
        {
            bool isAdd = false;
            foreach (var tar in tarFreqBandList)
            {
                if (src == tar)
                {
                    LteHandOverFreqBandAnaResult.BandHandOverInfo res = (LteHandOverFreqBandAnaResult.BandHandOverInfo)handOverInfo.Clone();
                    res.HandOverType = "同频";
                    res.TarHandOverInfo.FreqBand = tar;
                    result.SameBandHandOver.Add(res);
                    isAdd = true;
                    break;
                }
                else if (condition.FreqBands.FreqBandInfoDic[src].ParentFreqBandName != tar)
                {
                    handOverInfo.TarHandOverInfo.FreqBand = tar;
                }
            }
            if (!isAdd)
            {
                LteHandOverFreqBandAnaResult.BandHandOverInfo res = (LteHandOverFreqBandAnaResult.BandHandOverInfo)handOverInfo.Clone();
                res.HandOverType = "异频";
                result.DiffBandHandOver.Add(res);
            }
        }

        private LteHandOverFreqBandAnaResult.HandOverInfo getBeforeHandOverTPInfo(Event evt, ref int idx, List<TestPoint> tps)
        {
            idx = idx - condition.BeforeHandOverSec * 10;
            if (idx < 0)
            {
                idx = 0;
            }

            LteHandOverFreqBandAnaResult.HandOverInfo info = new LteHandOverFreqBandAnaResult.HandOverInfo();
            for (; idx < tps.Count; idx++)
            {
                TestPoint tp = tps[idx];
                TimeSpan ts = evt.DateTime.Subtract(tp.DateTime);
                if (ts.TotalMilliseconds <= condition.BeforeHandOverSec * 1000 && ts.TotalMilliseconds >= 0)
                {
                    float rsrp = getRsrp(tp);
                    if (rsrp != 99)
                    {
                        info.RsrpInfo.Add(rsrp);
                    }
                }
                else if (ts.TotalMilliseconds < 0)
                {
                    break;
                }
            }
            info.RsrpInfo.Calculate();
            return info;
        }

        private LteHandOverFreqBandAnaResult.HandOverInfo getAfterHandOverTPInfo(Event evt, int idx, List<TestPoint> tps)
        {
            LteHandOverFreqBandAnaResult.HandOverInfo info = new LteHandOverFreqBandAnaResult.HandOverInfo();
            for (; idx < tps.Count; idx++)
            {
                TestPoint tp = tps[idx];
                TimeSpan ts = tp.DateTime.Subtract(evt.DateTime);
                if (ts.TotalMilliseconds <= condition.AfterHandOverSec * 1000 && ts.TotalMilliseconds >= 0)
                {
                    float rsrp = getRsrp(tp);
                    if (rsrp != 99)
                    {
                        info.RsrpInfo.Add(rsrp);
                    }
                }
                else if (ts.TotalMilliseconds < 0)
                {
                    break;
                }
            }
            info.RsrpInfo.Calculate();
            return info;
        }

        protected override int getHandOverSrcArfcn(Event e)
        {
            int earfcn = getValidData(NREventHelper.HandoverHelper.LTE.GetSrcArfcn(e));
            return earfcn;
        }

        protected override int getHandOverTargetArfcn(Event e)
        {
            int earfcn = getValidData(NREventHelper.HandoverHelper.LTE.GetTarArfcn(e));
            return earfcn;
        }

        protected float getRsrp(TestPoint tp)
        {
            float rsrp = getValidData((float?)tp["lte_RSRP"]);
            return rsrp;
        }
    }
}
