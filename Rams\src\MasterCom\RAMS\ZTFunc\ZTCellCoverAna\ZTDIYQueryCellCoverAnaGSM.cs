﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Grid;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYQueryCellCoverAnaGSM : DIYSampleByRegion
    {
        public ZTDIYQueryCellCoverAnaGSM(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        public override string Name
        {
            get { return "GSM小区栅格覆盖分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12099, this.Name);
        }
        readonly Dictionary<CellGridKey, Dictionary<Cell, Dictionary<int, List<SingSampleInfo>>>> gridCellDic
            = new Dictionary<CellGridKey, Dictionary<Cell, Dictionary<int, List<SingSampleInfo>>>>();
        public static List<CellCoverAnaInfo> resultList { get; set; }
        Dictionary<CellGridKey, int> cellGridKeyRxLev;
        ZTDIYQueryCellCoverAnaSetForm conditionDlg = null;
        int cellGridStart = 4000;
        int cellGridEnd = 3600;
        protected override void doSomethingBeforeQueryInThread()
        {
            MainModel.ClearDTData();
            if (conditionDlg == null)
            {
                conditionDlg = new ZTDIYQueryCellCoverAnaSetForm();
            }
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return ;
            }
            cellGridStart = conditionDlg.iGrid * 100;
            cellGridEnd = conditionDlg.iGrid * 90;
        }
        /// <summary>
        /// 接收并处理采样点信息
        /// </summary>
        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            short? rxlev = (short?)tp["RxLevSub"];
            byte? rxqual = (byte?)tp["RxQualSub"];
            if (rxqual == null || rxlev == null
                || rxlev < -120 || rxlev > -10
                || rxqual == 255)
            {
                return;
            }

            int iRxlevSub = (int)rxlev;
            int rxQual = (int)rxqual;
            Dictionary<Cell, Dictionary<int, List<SingSampleInfo>>> cellRxLevDic
                = new Dictionary<Cell, Dictionary<int, List<SingSampleInfo>>>();
            for (int i = -1; i < 6; i++)
            {
                Cell cell;
                if (i == -1)
                    cell = tp.GetMainCell_GSM();
                else
                    cell = tp.GetNBCell_GSM(false, i);
                if (cell != null)
                {
                    SingSampleInfo singSampleInfo = getSingSampleInfo(tp, iRxlevSub, rxQual, i);
                    List<SingSampleInfo> sampleInfo = new List<SingSampleInfo>();
                    sampleInfo.Add(singSampleInfo);
                    CellGridKey cellGridKey = GetGirdKey(tp.Longitude, tp.Latitude);
                    addGridCellDic(iRxlevSub, cellRxLevDic, cell, singSampleInfo, sampleInfo, cellGridKey);
                }
            }
        }

        private static SingSampleInfo getSingSampleInfo(TestPoint tp, int iRxlevSub, int rxQual, int i)
        {
            SingSampleInfo singSampleInfo = new SingSampleInfo();
            singSampleInfo.IRxLevSub = iRxlevSub;
            if (i == -1)
            {
                singSampleInfo.IRxqulity = rxQual;
                for (int ciID = 0; ciID < 50; ciID++)
                {
                    float? c_i = (float?)(short?)tp["C_I", ciID];
                    if (c_i == null || (float)c_i < -40 || (float)c_i > 40)
                        continue;
                    singSampleInfo.IC_I = (int)c_i;
                    break;
                }
            }

            return singSampleInfo;
        }

        private void addGridCellDic(int iRxlevSub, Dictionary<Cell, Dictionary<int, List<SingSampleInfo>>> cellRxLevDic, Cell cell, 
            SingSampleInfo singSampleInfo, List<SingSampleInfo> sampleInfo, CellGridKey cellGridKey)
        {
            if (!gridCellDic.ContainsKey(cellGridKey))
            {
                Dictionary<int, List<SingSampleInfo>> rxLevSampleDIc = new Dictionary<int, List<SingSampleInfo>>();
                rxLevSampleDIc.Add(iRxlevSub, sampleInfo);
                cellRxLevDic.Add(cell, rxLevSampleDIc);
                gridCellDic.Add(cellGridKey, cellRxLevDic);
            }
            else
            {
                if (!gridCellDic[cellGridKey].ContainsKey(cell))
                {
                    Dictionary<int, List<SingSampleInfo>> rxLevSampleDIc = new Dictionary<int, List<SingSampleInfo>>();
                    rxLevSampleDIc.Add(iRxlevSub, sampleInfo);
                    gridCellDic[cellGridKey].Add(cell, rxLevSampleDIc);
                }
                else
                {
                    if (!gridCellDic[cellGridKey][cell].ContainsKey(iRxlevSub))
                        gridCellDic[cellGridKey][cell][iRxlevSub] = sampleInfo;
                    else
                        gridCellDic[cellGridKey][cell][iRxlevSub].Add(singSampleInfo);
                }
            }
        }

        /// <summary>
        /// 重写查询的采样点信息
        /// </summary>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "PESQ";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "C_I";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "N_BCCH";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "N_BSIC";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "N_RxLev";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM NoMainCell");
            tmpDic.Add("themeName", (object)"GSM RxLevSub");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
        /// <summary>
        /// 处理查询之后的信息
        /// </summary>
        protected override void FireShowFormAfterQuery()
        {
            WaitBox.Text = "开始处理查询到的信息。。。";
            dealAfterQueryData();
            fireShowForm();
            FillLayer();
        }

        private void fireShowForm()
        {
            ZTDIYQueryCellCoverAnaForm cellCoverAnaShowForm = MainModel.GetInstance().CreateResultForm(typeof(ZTDIYQueryCellCoverAnaForm)) as ZTDIYQueryCellCoverAnaForm;
            cellCoverAnaShowForm.Owner = MainModel.MainForm;
            cellCoverAnaShowForm.FillData(ZTDIYQueryCellCoverAnaGSM.resultList);
            cellCoverAnaShowForm.Visible = true;
            cellCoverAnaShowForm.BringToFront();
        }

        /// <summary>
        /// 处理查询之后的数据
        /// </summary>
        private void dealAfterQueryData()
        {
            resultList = new List<CellCoverAnaInfo>();
            cellGridKeyRxLev = new Dictionary<CellGridKey, int>();
            int iGrid = 1;
            int iCount = gridCellDic.Count;
            if (iCount == 0)
                iCount = 2;
            foreach (CellGridKey cellKey in gridCellDic.Keys)
            {
                string strName = GISManager.GetInstance().GetRoadPlaceDesc(cellKey.ILongitude, cellKey.ILatitude);
                int sumRxLev = 0;
                foreach (Cell cell in gridCellDic[cellKey].Keys)
                {
                    CellCoverAnaInfo cellCoverAnaInfo = new CellCoverAnaInfo();
                    cellCoverAnaInfo.IGridNum = iGrid;
                    cellCoverAnaInfo.DLongitude = cellKey.ILongitude;
                    cellCoverAnaInfo.DLatitude = cellKey.ILatitude;
                    cellCoverAnaInfo.CellName = cell.Name;
                    cellCoverAnaInfo.ILac = cell.LAC;
                    cellCoverAnaInfo.ICi = cell.CI;
                    cellCoverAnaInfo.IBCCH = cell.BCCH;
                    cellCoverAnaInfo.IBSIC = cell.BSIC;
                    string strValue = GetMaxFiveSampleRxLevSub(gridCellDic[cellKey][cell]);
                    cellCoverAnaInfo.IRxLevSub = int.Parse(strValue.Split(',')[0]);
                    cellCoverAnaInfo.ISampleNum = int.Parse(strValue.Split(',')[1]);
                    int iMaxRxLevSub = GetMaxKey(gridCellDic[cellKey][cell]);
                    cellCoverAnaInfo.IC_I = GetMaxC_I(gridCellDic[cellKey][cell][iMaxRxLevSub]);
                    cellCoverAnaInfo.IRxqulity = GetMaxRxQuery(gridCellDic[cellKey][cell][iMaxRxLevSub]);
                    cellCoverAnaInfo.StrStreetName = strName;

                    resultList.Add(cellCoverAnaInfo);
                    sumRxLev += cellCoverAnaInfo.IRxLevSub;
                }
                iGrid++;
                cellGridKeyRxLev.Add(cellKey, sumRxLev / gridCellDic[cellKey].Count);
                
                WaitBox.ProgressPercent = iGrid / iCount;
            }
            gridCellDic.Clear();
            WaitBox.Close();
        }
        /// <summary>
        /// 获取采样点数前5的场强均值以及采样点数
        /// </summary>
        private string GetMaxFiveSampleRxLevSub(Dictionary<int, List<SingSampleInfo>> rxLevSampleDic)
        {
            string strValue = "";
            int iRxlevsub = 0;
            int iSampleCount = 0;
            if (rxLevSampleDic.Count < 5)
            {
                foreach (int id in rxLevSampleDic.Keys)
                {
                    iRxlevsub += id;
                    iSampleCount += rxLevSampleDic[id].Count;                   
                }
                strValue = (iRxlevsub / rxLevSampleDic.Count) + "," + iSampleCount;
            }
            else
            {
                strValue = getTop5RsrpTP(rxLevSampleDic, ref iRxlevsub, ref iSampleCount);
            }

            return strValue;
        }

        private static string getTop5RsrpTP(Dictionary<int, List<SingSampleInfo>> rxLevSampleDic, ref int iRxlevsub, ref int iSampleCount)
        {
            string strValue;
            List<int> iSample = new List<int>();
            foreach (int id in rxLevSampleDic.Keys)
            {
                if (!iSample.Contains(rxLevSampleDic[id].Count))
                    iSample.Add(rxLevSampleDic[id].Count);
            }
            iSample.Sort();
            int iCount = 0;
            int iJudeNum = 5;
            if (iSample.Count < 5)
                iJudeNum = iSample.Count;
            foreach (int id in rxLevSampleDic.Keys)
            {
                for (int i = 0; i < iJudeNum; i++)
                {
                    if (rxLevSampleDic[id].Count == iSample[iSample.Count - 1 - i])
                    {
                        iRxlevsub += id;
                        iSampleCount += rxLevSampleDic[id].Count;
                        iCount++;
                    }
                }
            }
            strValue = (iRxlevsub / iCount) + "," + iSampleCount;
            return strValue;
        }

        /// <summary>
        /// 获取采样点最多的场强值
        /// </summary>
        private int GetMaxKey(Dictionary<int, List<SingSampleInfo>> rxLevSampleDic)
        {
            int key = -300;
            int keyCount = 0;
            foreach (int id in rxLevSampleDic.Keys)
            {
                if (rxLevSampleDic[id].Count > keyCount)
                {
                    keyCount = rxLevSampleDic[id].Count;
                    key = id;
                }
            }
            return key;
        }
        /// <summary>
        /// 获取采样点最多的C/I值
        /// </summary>
        private int GetMaxC_I(List<SingSampleInfo> sampleList)
        {
            int sumCount = 0;
            int iMainCell = 0;
            foreach (SingSampleInfo sample in sampleList)
            {
                if (sample.IC_I != -200)
                {
                    sumCount += sample.IC_I;
                    iMainCell++;
                }
            }
            if (iMainCell != 0)
                return sumCount / sampleList.Count;
            else
                return -200;
        }
        /// <summary>
        /// 获取采样点最多的RxQuery值
        /// </summary>
        private int GetMaxRxQuery(List<SingSampleInfo> sampleList)
        {
            int sumCount = 0;
            int iMainCell = 0;
            foreach (SingSampleInfo sample in sampleList)
            {
                if (sample.IRxqulity != 200)
                {
                    sumCount += sample.IRxqulity;
                    iMainCell++;
                }
            }
            if (iMainCell != 0)
                return sumCount / sampleList.Count;
            else
                return 200;
        }
        /// <summary>
        /// 通过经纬度计算栅格
        /// </summary>
        public CellGridKey GetGirdKey(double longitude, double latitude)
        {
            CellGridKey grid = new CellGridKey();
            int tllongitude = (int)(longitude * 10000000) / cellGridStart * cellGridStart;
            grid.ILongitude =  tllongitude / 10000000.0D;
            int tllatitude = (int)(latitude * 10000000) / cellGridEnd * cellGridEnd;
            grid.ILatitude =  tllatitude / 10000000.0D;
            return grid;
        }
        /// <summary>
        /// GIS呈现栅格
        /// </summary>
        private void FillLayer()
        {
            MainModel.RefreshLegend();//更新图例
            MapForm mf = MainModel.MainForm.GetMapForm();
            
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CellCoverAnaLayer));
                if (cLayer == null)
                {
                    layer = new CellCoverAnaLayer(mf.GetMapOperation(), "小区栅格覆盖图层");
                    mf.AddTempCustomLayer(layer);
                }
                else
                {
                    layer = cLayer as CellCoverAnaLayer;
                }
            }
            layer.Invalidate();
            layer.FillData(cellGridKeyRxLev);

            MainModel.IsPrepareWithoutGridPartParam = true;
            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = null;

            MapGridLayer.NeedFreshFullImg = true;
            MainModel.FireGridCoverQueried(this);

            setLegendType();
            MainModel.RefreshLegend();

            MainModel.IsPrepareWithoutGridPartParam = false; 
        }
        private void setLegendType()
        {
            GridColorFixed gridColorFixed = new GridColorFixed();
            gridColorFixed.items = new List<GridColorFixedItem>();
            gridColorFixed.theme = "小区栅格覆盖场强均值";
            GridColorFixedItem item = new GridColorFixedItem();
            item.desc = "-120 <= 场强均值 < -100";
            item.color = Color.FromArgb(255, Color.FromArgb(255, 0, 0));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-100 <= 场强均值 < -90";
            item.color = Color.FromArgb(255, Color.FromArgb(255, 102, 0));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-90 <= 场强均值 < -80";
            item.color = Color.FromArgb(255, Color.FromArgb(255, 204, 0));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-80 <= 场强均值 < -70";
            item.color = Color.FromArgb(255, Color.FromArgb(0, 0, 255));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-70 <= 场强均值 < -60";
            item.color = Color.FromArgb(255, Color.FromArgb(101, 0, 255));
            gridColorFixed.items.Add(item);
            item = new GridColorFixedItem();
            item.desc = "-60 <= 场强均值 < -9";
            item.color = Color.FromArgb(255, Color.FromArgb(0, 255, 255));
            gridColorFixed.items.Add(item);

            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingGridColorFixed = gridColorFixed;
        }
        CellCoverAnaLayer layer = null;

    }
    public class CellGridKey
    {
        /// <summary>
        /// 栅格经度
        /// </summary>
        public double ILongitude { get; set; }
        /// <summary>
        /// 栅格维度
        /// </summary>
        public double ILatitude { get; set; }
        public override bool Equals(object obj)
        {
            CellGridKey other = obj as CellGridKey;
            if (other == null)
                return false;
            if (!base.GetType().Equals(obj.GetType()))
                return false;
            return (this.ILongitude.Equals(other.ILongitude)
                && this.ILatitude.Equals(other.ILatitude));
        }
        public override int GetHashCode()
        {
            return this.ILongitude.GetHashCode();
        }
        public CellGridKey()
        {
            ILongitude = 0;
            ILatitude = 0;
        }
    }
    public class CellCoverAnaInfo
    {
        /// <summary>
        /// 栅格序号
        /// </summary>
        public int IGridNum { get; set; }
        /// <summary>
        /// 栅格经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 栅格维度
        /// </summary>
        public double DLatitude { get; set; }
        /// <summary>
        /// 栅格主邻小区
        /// </summary>
        public List<Cell> CellList { get; set; }
        /// <summary>
        /// 小区名
        /// </summary>
        public string CellName { get; set; }
        /// <summary>
        /// Lac值
        /// </summary>
        public int ILac { get; set; }
        /// <summary>
        /// Ci值
        /// </summary>
        public int ICi { get; set; }
        /// <summary>
        /// BCCH值
        /// </summary>
        public int IBCCH { get; set; }
        /// <summary>
        /// BSIC值
        /// </summary>
        public int IBSIC { get; set; }
        /// <summary>
        /// 平均场强值
        /// </summary>
        public int IRxLevSub { get; set; }
        /// <summary>
        /// 采样点数(大)
        /// </summary>
        public int ISampleNum { get; set; }
        /// <summary>
        /// C/I值（主服）
        /// </summary>
        public int IC_I { get; set; }
        /// <summary>
        /// 字符的C/I值
        /// </summary>
        public string strC_I
        {
            get
            {
                if (IC_I != -200)
                    return IC_I.ToString();
                else
                    return "-";
            }
        }
        /// <summary>
        /// Rxqulity平均值(主服)
        /// </summary>
        public int IRxqulity { get; set; }
        /// <summary>
        /// 字符的Rxqulity值
        /// </summary>
        public string strRxqulity
        {
            get
            {
                if (IRxqulity != 200)
                    return IRxqulity.ToString();
                else
                    return "-";
            }
        }
        /// <summary>
        /// 路段名称
        /// </summary>
        public string StrStreetName { get; set; }
    }
    public class SingSampleInfo
    {
        /// <summary>
        /// 场强值
        /// </summary>
        public int IRxLevSub { get; set; }
        /// <summary>
        /// C/I值
        /// </summary>
        public int IC_I { get; set; }
        /// <summary>
        /// Rxqulity
        /// </summary>
        public int IRxqulity { get; set; }
        public SingSampleInfo()
        {
            IRxLevSub = 0;
            IC_I = -200;
            IRxqulity = 200;
        }
    }
}
