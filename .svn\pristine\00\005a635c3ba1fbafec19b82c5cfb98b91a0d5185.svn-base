﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections;
using System.Reflection;
using MasterCom.Util;
using Microsoft.Office.Interop.Excel;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public partial class XtraFormRoadProtectionOneCity : DevExpress.XtraEditors.XtraForm
    {
        private List<RoadWarningEntity> dts;
        MainModel MainModel;
        System.Data.DataTable ListtoTable;
        string network;  //网络类型
        string city;   //地市
        public XtraFormRoadProtectionOneCity(MainModel mainModel, string tmpNetwork)
        {
            InitializeComponent();
            MainModel = mainModel;
            network = tmpNetwork;
            city = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            this.Text = city + " " + network + " 道路预警详细情况";
        }

        private void XtraFormRoadProtectionOneCity_Load(object sender, EventArgs e)
        {
            BuildData();
        }

        public void setDataSource(List<RoadWarningEntity> dt)
        {
            this.dts = dt;


            List<RoadWarningManager> roadManagerList = new List<RoadWarningManager>();

            foreach (RoadWarningEntity item in dt)
            {
                if (roadManagerList.Exists(delegate(RoadWarningManager p) { return p.Istatnum == item.Istatnum; }))
                {
                    roadManagerList.Find(delegate(RoadWarningManager p) { return p.Istatnum == item.Istatnum; }).RoadWarningEntityList.Add(item);
                }
                else
                {
                    RoadWarningManager roadManager = new RoadWarningManager();
                    roadManager.Idistance = item.Idistance;
                    roadManager.Ieventid = item.Ieventid;
                    roadManager.Istatnum = item.Istatnum;
                    roadManager.Istatus = item.Istatus;
                    roadManager.发生时间 = item.发生时间;
                    roadManager.区域 = item.区域;
                    roadManager.权值 = item.权值;
                    roadManager.事件总数 = item.事件总数;
                    roadManager.首次发生时间 = item.首次发生时间;
                    roadManager.所属网格 = item.所属网格;
                    roadManager.位置信息 = item.位置信息;
                    roadManager.问题天数 = item.问题天数;
                    roadManager.严重程度 = item.严重程度;
                    roadManager.最后发生时间 = item.最后发生时间;

                    roadManager.RoadWarningEntityList.Add(item);
                    roadManagerList.Add(roadManager);
                }
            }
            gridControl1.DataSource = roadManagerList;
        }


        public void BuildCellManage()
        {
            /**
            //foreach (DevExpress.XtraGrid.Columns.GridColumn cn in gview_GSM.Columns)
            //{
            //    if (cn.FieldName != "区域")
            //    {
            //        cn.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            //    }
            //}
            *///
        }

        public System.Data.DataTable FillDataTable(List<RoadWarningEntity> modelList)
        {
            if (modelList == null || modelList.Count == 0)
            {
                return null;
            }
            System.Data.DataTable dt = CreateData();//创建表结构

            foreach (RoadWarningEntity model in modelList)
            {
                DataRow dataRow = dt.NewRow();
                foreach (PropertyInfo propertyInfo in typeof(RoadWarningEntity).GetProperties())
                {
                    dataRow[propertyInfo.Name] = propertyInfo.GetValue(model, null);
                }
                dt.Rows.Add(dataRow);
            }
            return dt;
        }
        /// <summary>
        /// 根据实体类得到表结构
        /// </summary>
        /// <param name="model">实体类</param>
        /// <returns></returns>
        private System.Data.DataTable CreateData()
        {
            System.Data.DataTable dataTable = new System.Data.DataTable(typeof(RoadWarningEntity).Name);
            foreach (PropertyInfo propertyInfo in typeof(RoadWarningEntity).GetProperties())
            {
                if (propertyInfo.Name != "CTimestamp")//些字段为oracle中的Timesstarmp类型
                {
                    dataTable.Columns.Add(new DataColumn(propertyInfo.Name, propertyInfo.PropertyType));
                }
                else
                {
                    dataTable.Columns.Add(new DataColumn(propertyInfo.Name, typeof(DateTime)));
                }
            }
            return dataTable;
        }

        public void BuildData()
        {
            ListtoTable = new System.Data.DataTable();
            ListtoTable = FillDataTable(dts);

            this.Grid_Export.DataSource = ListtoTable;
        }



        private void gview1_GSM_CellMerge(object sender, DevExpress.XtraGrid.Views.Grid.CellMergeEventArgs e)
        {
            DataRowView spot = gview_GSM.GetRow(e.RowHandle1) as DataRowView;
            DataRowView spot2 = gview_GSM.GetRow(e.RowHandle2) as DataRowView;
            if (spot.Row["Istatnum"].ToString() != spot2.Row["Istatnum"].ToString())
            {
                e.Handled = true;
            }
            else
            {
                e.Handled = false;
            }
        }

        string filenamedir = "";

        //到处EXCEL
        private void 导出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            filenamedir = "";
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Title = "导出Excel";
            saveFileDialog.Filter = "Excel文件(*.xls,*.xlsx)|*.xls,*.xlsx";
            DialogResult dialogResult = saveFileDialog.ShowDialog(this);
            if (dialogResult == DialogResult.OK)
            {
                filenamedir = saveFileDialog.FileName;
                WaitBox.Show(export2Xls);
                DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void makeItemRow(Microsoft.Office.Interop.Excel._Worksheet worksheet, int row, int column, object content)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = content;
        }

        private void makeTitle(Microsoft.Office.Interop.Excel._Worksheet worksheet, int col, string title, int width)
        {
            Range range = worksheet.Cells[1, col] as Range;
            range.Value2 = title;
            range.Font.Bold = true;
            range.ColumnWidth = width;
        }

        private void export2Xls()
        {
            try
            {
                WaitBox.Text = "正在创建Excel...";
                Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: ERROR: worksheet == null"));
                }
                WaitBox.Text = "准备导出数据...";
                worksheet.Name = "道路预警";
                //====列标题
                int idx = 1;
                makeTitle(worksheet, idx++, "严重程度", 30);
                makeTitle(worksheet, idx++, "权值", 40);
                makeTitle(worksheet, idx++, "距离", 40);
                makeTitle(worksheet, idx++, "状态", 40);
                makeTitle(worksheet, idx++, "首次发生时间", 40);
                makeTitle(worksheet, idx++, "最后发生时间", 40);
                makeTitle(worksheet, idx++, "事件总数", 30);
                makeTitle(worksheet, idx++, "问题天数", 30);
                makeTitle(worksheet, idx++, "所属网格", 30);
                makeTitle(worksheet, idx++, "位置信息", 30);
                makeTitle(worksheet, idx++, "事件类型", 30);
                makeTitle(worksheet, idx++, "发生时间", 30);
                makeTitle(worksheet, idx++, "占用小区", 30);
                makeTitle(worksheet, idx++, "LAC", 30);
                makeTitle(worksheet, idx++, "CI", 30);
                makeTitle(worksheet, idx++, "经度", 30);
                makeTitle(worksheet, idx++, "纬度", 30);
                makeTitle(worksheet, idx++, "问题类型", 30);
                makeTitle(worksheet, idx++, "原因描述", 30);
                makeTitle(worksheet, idx, "建议方案", 30);
                int index = 0;
                int progress = 5;
                int rowAt = 2;
                WaitBox.Text = "正在导出数据...";
                foreach (RoadWarningEntity roadwarning in MainModel.RoadWarningList)
                {
                    Range firstGroupRge_1 = worksheet.Cells[rowAt, 1] as Range;
                    firstGroupRge_1.Value2 = roadwarning.严重程度;
                    Range firstGroupRge_2 = worksheet.Cells[rowAt, 2] as Range;
                    firstGroupRge_2.Value2 = roadwarning.权值;
                    Range firstGroupRge_3 = worksheet.Cells[rowAt, 3] as Range;
                    firstGroupRge_3.Value2 = roadwarning.Idistance;
                    Range firstGroupRge_4 = worksheet.Cells[rowAt, 4] as Range;
                    firstGroupRge_4.Value2 = statusTransformString(roadwarning.Istatus);
                    Range firstGroupRge_5 = worksheet.Cells[rowAt, 5] as Range;
                    firstGroupRge_5.Value2 = roadwarning.首次发生时间.ToString("yyyy-MM-dd");
                    Range firstGroupRge_6 = worksheet.Cells[rowAt, 6] as Range;
                    firstGroupRge_6.Value2 = roadwarning.最后发生时间.ToString("yyyy-MM-dd");
                    Range firstGroupRge_7 = worksheet.Cells[rowAt, 7] as Range;
                    firstGroupRge_7.Value2 = roadwarning.事件总数;
                    Range firstGroupRge_8 = worksheet.Cells[rowAt, 8] as Range;
                    firstGroupRge_8.Value2 = roadwarning.问题天数;
                    Range firstGroupRge_9 = worksheet.Cells[rowAt, 9] as Range;
                    firstGroupRge_9.Value2 = roadwarning.所属网格;
                    Range firstGroupRge_10 = worksheet.Cells[rowAt, 10] as Range;
                    firstGroupRge_10.Value2 = roadwarning.位置信息;

                    WaitBox.ProgressPercent = 20;

                    setRowInfo(worksheet, ref index, ref progress, ref rowAt, roadwarning);
                }

                for (int i = 1; i < 20; i++)
                {
                    ((Range)worksheet.Cells[1, i]).EntireColumn.AutoFit();
                }
                WaitBox.ProgressPercent = 100;
                workbook.SaveAs(filenamedir, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                app.Quit();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出Excel出错：" + ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void setRowInfo(_Worksheet worksheet, ref int index, ref int progress, ref int rowAt, RoadWarningEntity roadwarning)
        {
            int rowOffset = 0;
            foreach (RoadWarningEntity road in roadwarning.getroadWarningEntityList()) //.RoadWarningEntityList)
            {
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }

                makeItemRow(worksheet, rowAt + rowOffset, 11, road.事件类型);
                makeItemRow(worksheet, rowAt + rowOffset, 12, road.发生时间.ToString("yyyy-MM-dd hh:mm:ss"));
                makeItemRow(worksheet, rowAt + rowOffset, 13, road.占用小区);
                makeItemRow(worksheet, rowAt + rowOffset, 14, road.LAC);
                makeItemRow(worksheet, rowAt + rowOffset, 15, road.CI);
                makeItemRow(worksheet, rowAt + rowOffset, 16, road.经度);
                makeItemRow(worksheet, rowAt + rowOffset, 17, road.纬度);
                makeItemRow(worksheet, rowAt + rowOffset, 18, road.问题类型);
                makeItemRow(worksheet, rowAt + rowOffset, 19, road.原因描述);
                makeItemRow(worksheet, rowAt + rowOffset, 20, road.建议方案);
                WaitBox.ProgressPercent = 95;
                rowOffset++;
            }
            WaitBox.ProgressPercent = 95;
            if (rowOffset > 1)
            {
                setColorIndex(worksheet, rowAt, rowOffset);
            }
            rowAt += rowOffset;
        }

        private void setColorIndex(_Worksheet worksheet, int rowAt, int rowOffset)
        {
            for (int i = 1; i < 11; i++)
            {
                Range rangeBegin = worksheet.Cells[rowAt, i] as Range;
                Range rangeEnd = worksheet.Cells[rowAt + rowOffset - 1, i] as Range;
                Range range = worksheet.get_Range(rangeBegin, rangeEnd);
                range.MergeCells = true;
                if (i == 4)
                {
                    switch (rangeBegin.Formula.ToString())
                    {
                        case "新增":
                            rangeBegin.Interior.ColorIndex = 3;// 新增 红色
                            break;
                        case "遗留":
                            rangeBegin.Interior.ColorIndex = 5;// 遗留 蓝色
                            break;
                        case "关闭":
                            rangeBegin.Interior.ColorIndex = 4;// 关闭 绿色
                            break;
                        case "复发":
                            rangeBegin.Interior.ColorIndex = 6;// 复发 黄色
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        //双击定位GIS 获取事件
        private void Grid_Export_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            string istatnum = gview_GSM.GetFocusedRowCellValue("Istatnum").ToString();

            List<RoadWarningEntity> dlyjList = new List<RoadWarningEntity>();
            foreach (RoadWarningEntity item in dts)
            {
                if (item.Istatnum == istatnum)
                {
                    dlyjList.Add(item);
                }
            }
            MainModel.MainForm.FireShowRoadProtecionEventForGIS(dlyjList);
        }

        private void 展开所有ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView1.RowCount - 1; i++)
            {
                gridView1.SetMasterRowExpanded(i, true);
            }
        }

        private void 合并所有ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView1.RowCount - 1; i++)
            {
                gridView1.SetMasterRowExpanded(i, false);
            }
        }


        //修改单元格值
        private void gridView1_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName=="权值")
            {
                double value=0;
                try
                {
                    value=Convert.ToDouble(e.CellValue.ToString());
                }
                catch (Exception)
                {
                    value=0;
                }
                e.DisplayText = Math.Round(value,2).ToString();
            }

            if (e.Column.FieldName=="Istatus")
            {
                int value = -1;
                try
                {
                    value = Convert.ToInt32(e.CellValue.ToString());
                }
                catch (Exception)
                {
                    value = -1;
                }
                
                e.DisplayText = statusTransformString(value);
                
            }
        }

        private string statusTransformString(int status)
        {
            if (status == 0)
            {
                return "新增";
            }
            if (status == 1)
            {
                return "遗留";
            }
            if (status == 2)
            {
                return "关闭";
            }
            if (status == 3)
            {
                return "复发";
            }
            return "未知";
        }

        //点击定位小区
        private void gridView2_Click(object sender, EventArgs e)
        {
            try
            {
                GridView gridview = sender as GridView;

                string imlongitude = gridview.GetFocusedRowCellValue("经度").ToString();
                string imlatitude = gridview.GetFocusedRowCellValue("纬度").ToString();
                if (imlongitude != "" && imlatitude != "")
                {

                    float fLong = float.Parse(imlongitude);
                    float fLat = float.Parse(imlatitude);
                    if (fLong > 100 && fLat > 20)
                    {
                        MainModel.MainForm.GetMapForm().GoToView(fLong, fLat);
                    }
                }

                //显示事件
                string istatnum = gridview.GetFocusedRowCellValue("Istatnum").ToString();

                List<RoadWarningEntity> dlyjList = new List<RoadWarningEntity>();
                foreach (RoadWarningEntity item in dts)
                {
                    if (item.Istatnum == istatnum)
                    {
                        dlyjList.Add(item);
                    }
                }
                MainModel.MainForm.FireShowRoadProtecionEventForGIS(dlyjList);
            }
            catch
            {
                 //continue
            }
        }
    }
}