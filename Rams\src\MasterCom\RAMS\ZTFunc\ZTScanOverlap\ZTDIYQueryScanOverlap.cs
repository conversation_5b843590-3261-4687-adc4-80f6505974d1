﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYQueryScanOverlap : DIYSampleByRegion
    {
        public ZTDIYQueryScanOverlap(MainModel mm)
            : base(mm)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "最强小区过覆盖分析"; }
        }

        public override string IconName
        {
            get { return "images/覆盖分析/过覆盖.png"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15029, this.Name);
        }

        public override string Description
        {
            get
            {
                return "最强，最近小区过覆盖分析";
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDic = new Dictionary<Cell, OverlapCellInfo>();
        }

        protected override bool getConditionBeforeQuery()
        {
            ScanOverlapSettingDlg dlg = new ScanOverlapSettingDlg(rxLevDiffMin, distanceDiffMin);
            dlg.ShowDialog();
            return dlg.DialogResult == System.Windows.Forms.DialogResult.OK;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (overlapPointList.Count==0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的采样点！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanOverlapCellForm).FullName);
            ScanOverlapCellForm resultFrm = obj == null ? null : obj as ScanOverlapCellForm;
            if (resultFrm == null||resultFrm.IsDisposed)
            {
                resultFrm = new ScanOverlapCellForm(MainModel);
            }
            resultFrm.FillData(overlapPointList);
            if (!resultFrm.Visible)
            {
                resultFrm.Show(MainModel.MainForm);
            }
            resultFrm.BringToFront();
            MainModel.FireDTDataChanged(this);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected int rxLevDiffMin = 10;
        protected int distanceDiffMin = 300;
        protected DTDisplayParameterInfo rxLevParam = DTDisplayParameterManager.GetInstance()["GSM_SCAN", "RxLev"];
        protected Dictionary<Cell, OverlapCellInfo> cellDic = new Dictionary<Cell, OverlapCellInfo>();
        protected override void doWithDTData(TestPoint tp)
        {
            float maxRxlev = rxLevParam.ValueMax;
            float minRxlev = rxLevParam.ValueMin;

            NearestMainCellInfo cellInfo = new NearestMainCellInfo();
            for (int index = 0; index < 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                float? rxlev = (float?)tp["GSCAN_RxLev", index];
                short? bcch = (short?)(int?)tp["GSCAN_BCCH", index];
                byte? bsic = (byte?)(int?)tp["GSCAN_BSIC", index];
                if (rxlev != null && bcch != null && bsic != null && rxlev <= maxRxlev && rxlev >= minRxlev)
                {
                    setNearestMainCellInfo(tp, cellInfo, rxlev, bcch, bsic);
                }
                else
                {
                    break;
                }
            }

            double disDiff = cellInfo.Dis2MainCell - cellInfo.DisMin;
            float rxLevDiff = cellInfo.MainRxLev - cellInfo.NearestCellRxLev;
            //最强小区rxlev-最近小区rxlev大于等于设定值 且 最强小区距离-最近小区距离大于等于等于设定值
            if (disDiff >= distanceDiffMin && rxLevDiff >= rxLevDiffMin)
            {
                ScanOverlapPoint ovlPnt = new ScanOverlapPoint(tp, cellInfo);
                overlapPointList.Add(ovlPnt);
                //if (cellDic.ContainsKey(mainCell))
                //{
                //    OverlapCellInfo overlapCell = cellDic[mainCell];
                //    overlapCell.AddTestPoint(testPoint, mainRxLev, dis2MainCell, nearestCell, nearestCellRxLev, disMin, rxLevDiff, disDiff);
                //}
                //else
                //{
                //    OverlapCellInfo overlapCell = new OverlapCellInfo(mainCell);
                //    overlapCell.AddTestPoint(testPoint, mainRxLev, dis2MainCell, nearestCell, nearestCellRxLev, disMin, rxLevDiff, disDiff);
                //    cellDic.Add(mainCell, overlapCell);
                //}
            }
        }

        private void setNearestMainCellInfo(TestPoint tp, NearestMainCellInfo cellInfo, float? rxlev, short? bcch, byte? bsic)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
            if (cell != null)
            {
                double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (cellInfo.MainCell == null)//主强小区信息
                {
                    cellInfo.MainRxLev = (float)rxlev;
                    cellInfo.MainCell = cell;
                    cellInfo.Dis2MainCell = distance;
                }
                else
                {
                    //最近小区为室内小区时，过滤掉；取非室内的最近小区
                    if (cell.Type != BTSType.Indoor && distance < cellInfo.DisMin && cell != cellInfo.MainCell)
                    {//最近小区
                        cellInfo.DisMin = distance;
                        cellInfo.NearestCell = cell;
                        cellInfo.NearestCellRxLev = (float)rxlev;
                    }
                }
            }
        }

        readonly List<ScanOverlapPoint> overlapPointList = new List<ScanOverlapPoint>();

        public class NearestMainCellInfo
        {
            public Cell MainCell { get; set; } = null;//主强小区
            public float MainRxLev { get; set; } = float.NaN;//主强小区rxlev
            public double Dis2MainCell { get; set; } = 0;//采样点与主强小区的距离

            public Cell NearestCell { get; set; } = null;//最近小区
            public double DisMin { get; set; } = double.MaxValue;//最近小区距离
            public float NearestCellRxLev { get; set; } = float.NaN;//最近小区rxlev
        }
    }
}
