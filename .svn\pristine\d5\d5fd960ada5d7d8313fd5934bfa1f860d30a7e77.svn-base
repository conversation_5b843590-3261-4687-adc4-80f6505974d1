﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedRoadDlg_NR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.radioThroughput = new System.Windows.Forms.RadioButton();
            this.radioApp = new System.Windows.Forms.RadioButton();
            this.grpThroughput = new System.Windows.Forms.GroupBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.cmbThroughput = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkThroughputUl = new System.Windows.Forms.CheckBox();
            this.chkThroughputDl = new System.Windows.Forms.CheckBox();
            this.label26 = new System.Windows.Forms.Label();
            this.numThroughputUlMin = new DevExpress.XtraEditors.SpinEdit();
            this.label27 = new System.Windows.Forms.Label();
            this.numThroughputUlDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numThroughputUlMax = new DevExpress.XtraEditors.SpinEdit();
            this.label28 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.numThroughputDlMin = new DevExpress.XtraEditors.SpinEdit();
            this.label25 = new System.Windows.Forms.Label();
            this.numThroughputDlDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numThroughputDlMax = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label29 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.numFTPULDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numFTPULMin = new DevExpress.XtraEditors.SpinEdit();
            this.label36 = new System.Windows.Forms.Label();
            this.numFTPULMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkFTPUL = new System.Windows.Forms.CheckBox();
            this.label33 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.numHTTPDistance = new DevExpress.XtraEditors.SpinEdit();
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.numEMailDistance = new DevExpress.XtraEditors.SpinEdit();
            this.label22 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.numVideoDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numVideoMin = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.numVideoMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkVideo = new System.Windows.Forms.CheckBox();
            this.numHTTPMin = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.numFTPDLDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numEmailMin = new DevExpress.XtraEditors.SpinEdit();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numFTPDLMin = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.numFTPDLMax = new DevExpress.XtraEditors.SpinEdit();
            this.numHTTPMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkHttp = new System.Windows.Forms.CheckBox();
            this.numEmailMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkEmail = new System.Windows.Forms.CheckBox();
            this.chkFTPDL = new System.Windows.Forms.CheckBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numLowPer = new DevExpress.XtraEditors.SpinEdit();
            this.num2TpDis = new DevExpress.XtraEditors.SpinEdit();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkSinr = new System.Windows.Forms.CheckBox();
            this.chkRsrp = new System.Windows.Forms.CheckBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numSinrMax = new DevExpress.XtraEditors.SpinEdit();
            this.numSinrMin = new DevExpress.XtraEditors.SpinEdit();
            this.numRsrpMin = new DevExpress.XtraEditors.SpinEdit();
            this.numRsrpMax = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.checkBoxLowSpeed_NR = new System.Windows.Forms.CheckBox();
            this.checkBoxLTELowSpeed = new System.Windows.Forms.CheckBox();
            this.checkBoxSynthesisLowSpeed = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            this.grpThroughput.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbThroughput.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputUlMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputUlDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputUlMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDlMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDlDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDlMax.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEMailDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numVideoDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numVideoMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numVideoMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLowPer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.num2TpDis.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMax.Properties)).BeginInit();
            this.groupBox5.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radioThroughput);
            this.groupBox1.Controls.Add(this.radioApp);
            this.groupBox1.Controls.Add(this.grpThroughput);
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(673, 321);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "NR速率(Mbps)";
            // 
            // radioThroughput
            // 
            this.radioThroughput.AutoSize = true;
            this.radioThroughput.Location = new System.Drawing.Point(6, 242);
            this.radioThroughput.Name = "radioThroughput";
            this.radioThroughput.Size = new System.Drawing.Size(83, 16);
            this.radioThroughput.TabIndex = 2;
            this.radioThroughput.Text = "Throughput";
            this.radioThroughput.UseVisualStyleBackColor = true;
            this.radioThroughput.CheckedChanged += new System.EventHandler(this.radioThroughput_CheckedChanged);
            // 
            // radioApp
            // 
            this.radioApp.AutoSize = true;
            this.radioApp.Checked = true;
            this.radioApp.Location = new System.Drawing.Point(6, 41);
            this.radioApp.Name = "radioApp";
            this.radioApp.Size = new System.Drawing.Size(41, 16);
            this.radioApp.TabIndex = 0;
            this.radioApp.TabStop = true;
            this.radioApp.Text = "App";
            this.radioApp.UseVisualStyleBackColor = true;
            this.radioApp.CheckedChanged += new System.EventHandler(this.radioApp_CheckedChanged);
            // 
            // grpThroughput
            // 
            this.grpThroughput.Controls.Add(this.label11);
            this.grpThroughput.Controls.Add(this.label10);
            this.grpThroughput.Controls.Add(this.cmbThroughput);
            this.grpThroughput.Controls.Add(this.chkThroughputUl);
            this.grpThroughput.Controls.Add(this.chkThroughputDl);
            this.grpThroughput.Controls.Add(this.label26);
            this.grpThroughput.Controls.Add(this.numThroughputUlMin);
            this.grpThroughput.Controls.Add(this.label27);
            this.grpThroughput.Controls.Add(this.numThroughputUlDistance);
            this.grpThroughput.Controls.Add(this.numThroughputUlMax);
            this.grpThroughput.Controls.Add(this.label28);
            this.grpThroughput.Controls.Add(this.label24);
            this.grpThroughput.Controls.Add(this.numThroughputDlMin);
            this.grpThroughput.Controls.Add(this.label25);
            this.grpThroughput.Controls.Add(this.numThroughputDlDistance);
            this.grpThroughput.Controls.Add(this.numThroughputDlMax);
            this.grpThroughput.Controls.Add(this.label3);
            this.grpThroughput.Enabled = false;
            this.grpThroughput.Location = new System.Drawing.Point(90, 215);
            this.grpThroughput.Name = "grpThroughput";
            this.grpThroughput.Size = new System.Drawing.Size(567, 100);
            this.grpThroughput.TabIndex = 3;
            this.grpThroughput.TabStop = false;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(321, 70);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(29, 12);
            this.label11.TabIndex = 45;
            this.label11.Text = "Mbps";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(321, 39);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(29, 12);
            this.label10.TabIndex = 44;
            this.label10.Text = "Mbps";
            // 
            // cmbThroughput
            // 
            this.cmbThroughput.EditValue = "MAC";
            this.cmbThroughput.Location = new System.Drawing.Point(25, 0);
            this.cmbThroughput.Name = "cmbThroughput";
            this.cmbThroughput.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbThroughput.Properties.Items.AddRange(new object[] {
            "MAC",
            "PDCP"});
            this.cmbThroughput.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbThroughput.Size = new System.Drawing.Size(72, 21);
            this.cmbThroughput.TabIndex = 43;
            // 
            // chkThroughputUl
            // 
            this.chkThroughputUl.AutoSize = true;
            this.chkThroughputUl.Checked = true;
            this.chkThroughputUl.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkThroughputUl.Location = new System.Drawing.Point(27, 70);
            this.chkThroughputUl.Name = "chkThroughputUl";
            this.chkThroughputUl.Size = new System.Drawing.Size(15, 14);
            this.chkThroughputUl.TabIndex = 39;
            this.chkThroughputUl.UseVisualStyleBackColor = true;
            // 
            // chkThroughputDl
            // 
            this.chkThroughputDl.AutoSize = true;
            this.chkThroughputDl.Checked = true;
            this.chkThroughputDl.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkThroughputDl.Location = new System.Drawing.Point(27, 39);
            this.chkThroughputDl.Name = "chkThroughputDl";
            this.chkThroughputDl.Size = new System.Drawing.Size(15, 14);
            this.chkThroughputDl.TabIndex = 38;
            this.chkThroughputDl.UseVisualStyleBackColor = true;
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(534, 71);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(17, 12);
            this.label26.TabIndex = 37;
            this.label26.Text = "米";
            // 
            // numThroughputUlMin
            // 
            this.numThroughputUlMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numThroughputUlMin.Location = new System.Drawing.Point(48, 65);
            this.numThroughputUlMin.Name = "numThroughputUlMin";
            this.numThroughputUlMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputUlMin.Size = new System.Drawing.Size(60, 21);
            this.numThroughputUlMin.TabIndex = 32;
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(398, 71);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(65, 12);
            this.label27.TabIndex = 35;
            this.label27.Text = "持续距离≥";
            // 
            // numThroughputUlDistance
            // 
            this.numThroughputUlDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numThroughputUlDistance.Location = new System.Drawing.Point(468, 65);
            this.numThroughputUlDistance.Name = "numThroughputUlDistance";
            this.numThroughputUlDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputUlDistance.Size = new System.Drawing.Size(60, 21);
            this.numThroughputUlDistance.TabIndex = 36;
            // 
            // numThroughputUlMax
            // 
            this.numThroughputUlMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numThroughputUlMax.Location = new System.Drawing.Point(255, 65);
            this.numThroughputUlMax.Name = "numThroughputUlMax";
            this.numThroughputUlMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputUlMax.Size = new System.Drawing.Size(60, 21);
            this.numThroughputUlMax.TabIndex = 34;
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(136, 71);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(95, 12);
            this.label28.TabIndex = 33;
            this.label28.Text = "≤Upload Rate≤";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(534, 40);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(17, 12);
            this.label24.TabIndex = 31;
            this.label24.Text = "米";
            // 
            // numThroughputDlMin
            // 
            this.numThroughputDlMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numThroughputDlMin.Location = new System.Drawing.Point(48, 34);
            this.numThroughputDlMin.Name = "numThroughputDlMin";
            this.numThroughputDlMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputDlMin.Size = new System.Drawing.Size(60, 21);
            this.numThroughputDlMin.TabIndex = 0;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(398, 40);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(65, 12);
            this.label25.TabIndex = 29;
            this.label25.Text = "持续距离≥";
            // 
            // numThroughputDlDistance
            // 
            this.numThroughputDlDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numThroughputDlDistance.Location = new System.Drawing.Point(468, 34);
            this.numThroughputDlDistance.Name = "numThroughputDlDistance";
            this.numThroughputDlDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputDlDistance.Size = new System.Drawing.Size(60, 21);
            this.numThroughputDlDistance.TabIndex = 30;
            // 
            // numThroughputDlMax
            // 
            this.numThroughputDlMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numThroughputDlMax.Location = new System.Drawing.Point(255, 34);
            this.numThroughputDlMax.Name = "numThroughputDlMax";
            this.numThroughputDlMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numThroughputDlMax.Size = new System.Drawing.Size(60, 21);
            this.numThroughputDlMax.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(130, 40);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(107, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "≤Download Rate≤";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label29);
            this.groupBox2.Controls.Add(this.label34);
            this.groupBox2.Controls.Add(this.label35);
            this.groupBox2.Controls.Add(this.numFTPULDistance);
            this.groupBox2.Controls.Add(this.numFTPULMin);
            this.groupBox2.Controls.Add(this.label36);
            this.groupBox2.Controls.Add(this.numFTPULMax);
            this.groupBox2.Controls.Add(this.chkFTPUL);
            this.groupBox2.Controls.Add(this.label33);
            this.groupBox2.Controls.Add(this.label32);
            this.groupBox2.Controls.Add(this.label31);
            this.groupBox2.Controls.Add(this.label30);
            this.groupBox2.Controls.Add(this.label20);
            this.groupBox2.Controls.Add(this.label21);
            this.groupBox2.Controls.Add(this.numHTTPDistance);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.label19);
            this.groupBox2.Controls.Add(this.numEMailDistance);
            this.groupBox2.Controls.Add(this.label22);
            this.groupBox2.Controls.Add(this.label23);
            this.groupBox2.Controls.Add(this.numVideoDistance);
            this.groupBox2.Controls.Add(this.numVideoMin);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.label17);
            this.groupBox2.Controls.Add(this.numVideoMax);
            this.groupBox2.Controls.Add(this.chkVideo);
            this.groupBox2.Controls.Add(this.numHTTPMin);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.numFTPDLDistance);
            this.groupBox2.Controls.Add(this.numEmailMin);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.numFTPDLMin);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.numFTPDLMax);
            this.groupBox2.Controls.Add(this.numHTTPMax);
            this.groupBox2.Controls.Add(this.chkHttp);
            this.groupBox2.Controls.Add(this.numEmailMax);
            this.groupBox2.Controls.Add(this.chkEmail);
            this.groupBox2.Controls.Add(this.chkFTPDL);
            this.groupBox2.Location = new System.Drawing.Point(90, 20);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(567, 177);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "App";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(321, 55);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(29, 12);
            this.label29.TabIndex = 52;
            this.label29.Text = "Mbps";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(534, 55);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(17, 12);
            this.label34.TabIndex = 50;
            this.label34.Text = "米";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(398, 55);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(65, 12);
            this.label35.TabIndex = 45;
            this.label35.Text = "持续距离≥";
            // 
            // numFTPULDistance
            // 
            this.numFTPULDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numFTPULDistance.Location = new System.Drawing.Point(468, 49);
            this.numFTPULDistance.Name = "numFTPULDistance";
            this.numFTPULDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPULDistance.Size = new System.Drawing.Size(60, 21);
            this.numFTPULDistance.TabIndex = 46;
            // 
            // numFTPULMin
            // 
            this.numFTPULMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPULMin.Location = new System.Drawing.Point(48, 49);
            this.numFTPULMin.Name = "numFTPULMin";
            this.numFTPULMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPULMin.Size = new System.Drawing.Size(60, 21);
            this.numFTPULMin.TabIndex = 47;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(120, 55);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(125, 12);
            this.label36.TabIndex = 51;
            this.label36.Text = "≤FTP Upload  Rate≤";
            // 
            // numFTPULMax
            // 
            this.numFTPULMax.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numFTPULMax.Location = new System.Drawing.Point(255, 49);
            this.numFTPULMax.Name = "numFTPULMax";
            this.numFTPULMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPULMax.Size = new System.Drawing.Size(60, 21);
            this.numFTPULMax.TabIndex = 48;
            // 
            // chkFTPUL
            // 
            this.chkFTPUL.AutoSize = true;
            this.chkFTPUL.Checked = true;
            this.chkFTPUL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPUL.Location = new System.Drawing.Point(27, 54);
            this.chkFTPUL.Name = "chkFTPUL";
            this.chkFTPUL.Size = new System.Drawing.Size(15, 14);
            this.chkFTPUL.TabIndex = 49;
            this.chkFTPUL.UseVisualStyleBackColor = true;
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(321, 117);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(29, 12);
            this.label33.TabIndex = 44;
            this.label33.Text = "Mbps";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(321, 86);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(29, 12);
            this.label32.TabIndex = 43;
            this.label32.Text = "Mbps";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(321, 149);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(29, 12);
            this.label31.TabIndex = 42;
            this.label31.Text = "Mbps";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(321, 24);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(29, 12);
            this.label30.TabIndex = 41;
            this.label30.Text = "Mbps";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(534, 86);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 12);
            this.label20.TabIndex = 28;
            this.label20.Text = "米";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(398, 86);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(65, 12);
            this.label21.TabIndex = 26;
            this.label21.Text = "持续距离≥";
            // 
            // numHTTPDistance
            // 
            this.numHTTPDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numHTTPDistance.Location = new System.Drawing.Point(468, 80);
            this.numHTTPDistance.Name = "numHTTPDistance";
            this.numHTTPDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPDistance.Size = new System.Drawing.Size(60, 21);
            this.numHTTPDistance.TabIndex = 27;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(534, 149);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 25;
            this.label18.Text = "米";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(398, 149);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(65, 12);
            this.label19.TabIndex = 23;
            this.label19.Text = "持续距离≥";
            // 
            // numEMailDistance
            // 
            this.numEMailDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numEMailDistance.Location = new System.Drawing.Point(468, 143);
            this.numEMailDistance.Name = "numEMailDistance";
            this.numEMailDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEMailDistance.Size = new System.Drawing.Size(60, 21);
            this.numEMailDistance.TabIndex = 24;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(534, 117);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(17, 12);
            this.label22.TabIndex = 22;
            this.label22.Text = "米";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(398, 117);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(65, 12);
            this.label23.TabIndex = 20;
            this.label23.Text = "持续距离≥";
            // 
            // numVideoDistance
            // 
            this.numVideoDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numVideoDistance.Location = new System.Drawing.Point(468, 111);
            this.numVideoDistance.Name = "numVideoDistance";
            this.numVideoDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numVideoDistance.Size = new System.Drawing.Size(60, 21);
            this.numVideoDistance.TabIndex = 21;
            // 
            // numVideoMin
            // 
            this.numVideoMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numVideoMin.Location = new System.Drawing.Point(48, 111);
            this.numVideoMin.Name = "numVideoMin";
            this.numVideoMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numVideoMin.Size = new System.Drawing.Size(60, 21);
            this.numVideoMin.TabIndex = 10;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(534, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 6;
            this.label2.Text = "米";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(136, 117);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(89, 12);
            this.label17.TabIndex = 13;
            this.label17.Text = "≤HTTP Video≤";
            // 
            // numVideoMax
            // 
            this.numVideoMax.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numVideoMax.Location = new System.Drawing.Point(255, 111);
            this.numVideoMax.Name = "numVideoMax";
            this.numVideoMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numVideoMax.Size = new System.Drawing.Size(60, 21);
            this.numVideoMax.TabIndex = 12;
            // 
            // chkVideo
            // 
            this.chkVideo.AutoSize = true;
            this.chkVideo.Checked = true;
            this.chkVideo.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkVideo.Location = new System.Drawing.Point(27, 116);
            this.chkVideo.Name = "chkVideo";
            this.chkVideo.Size = new System.Drawing.Size(15, 14);
            this.chkVideo.TabIndex = 11;
            this.chkVideo.UseVisualStyleBackColor = true;
            // 
            // numHTTPMin
            // 
            this.numHTTPMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numHTTPMin.Location = new System.Drawing.Point(48, 80);
            this.numHTTPMin.Name = "numHTTPMin";
            this.numHTTPMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMin.Size = new System.Drawing.Size(60, 21);
            this.numHTTPMin.TabIndex = 4;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(398, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "持续距离≥";
            // 
            // numFTPDLDistance
            // 
            this.numFTPDLDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numFTPDLDistance.Location = new System.Drawing.Point(468, 18);
            this.numFTPDLDistance.Name = "numFTPDLDistance";
            this.numFTPDLDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDLDistance.Size = new System.Drawing.Size(60, 21);
            this.numFTPDLDistance.TabIndex = 0;
            // 
            // numEmailMin
            // 
            this.numEmailMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numEmailMin.Location = new System.Drawing.Point(48, 143);
            this.numEmailMin.Name = "numEmailMin";
            this.numEmailMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMin.Size = new System.Drawing.Size(60, 21);
            this.numEmailMin.TabIndex = 2;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(112, 86);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(137, 12);
            this.label14.TabIndex = 9;
            this.label14.Text = "≤HTTP Download Rate≤";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(130, 149);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(119, 12);
            this.label13.TabIndex = 8;
            this.label13.Text = "≤EMail SMTP Rate≤";
            // 
            // numFTPDLMin
            // 
            this.numFTPDLMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPDLMin.Location = new System.Drawing.Point(48, 18);
            this.numFTPDLMin.Name = "numFTPDLMin";
            this.numFTPDLMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDLMin.Size = new System.Drawing.Size(60, 21);
            this.numFTPDLMin.TabIndex = 0;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(118, 24);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(131, 12);
            this.label12.TabIndex = 6;
            this.label12.Text = "≤FTP DownLoad Rate≤";
            // 
            // numFTPDLMax
            // 
            this.numFTPDLMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numFTPDLMax.Location = new System.Drawing.Point(255, 18);
            this.numFTPDLMax.Name = "numFTPDLMax";
            this.numFTPDLMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDLMax.Size = new System.Drawing.Size(60, 21);
            this.numFTPDLMax.TabIndex = 1;
            // 
            // numHTTPMax
            // 
            this.numHTTPMax.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numHTTPMax.Location = new System.Drawing.Point(255, 80);
            this.numHTTPMax.Name = "numHTTPMax";
            this.numHTTPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMax.Size = new System.Drawing.Size(60, 21);
            this.numHTTPMax.TabIndex = 5;
            // 
            // chkHttp
            // 
            this.chkHttp.AutoSize = true;
            this.chkHttp.Checked = true;
            this.chkHttp.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkHttp.Location = new System.Drawing.Point(27, 85);
            this.chkHttp.Name = "chkHttp";
            this.chkHttp.Size = new System.Drawing.Size(15, 14);
            this.chkHttp.TabIndex = 4;
            this.chkHttp.UseVisualStyleBackColor = true;
            // 
            // numEmailMax
            // 
            this.numEmailMax.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numEmailMax.Location = new System.Drawing.Point(255, 143);
            this.numEmailMax.Name = "numEmailMax";
            this.numEmailMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMax.Size = new System.Drawing.Size(60, 21);
            this.numEmailMax.TabIndex = 3;
            // 
            // chkEmail
            // 
            this.chkEmail.AutoSize = true;
            this.chkEmail.Checked = true;
            this.chkEmail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkEmail.Location = new System.Drawing.Point(27, 148);
            this.chkEmail.Name = "chkEmail";
            this.chkEmail.Size = new System.Drawing.Size(15, 14);
            this.chkEmail.TabIndex = 0;
            this.chkEmail.UseVisualStyleBackColor = true;
            // 
            // chkFTPDL
            // 
            this.chkFTPDL.AutoSize = true;
            this.chkFTPDL.Checked = true;
            this.chkFTPDL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPDL.Location = new System.Drawing.Point(27, 23);
            this.chkFTPDL.Name = "chkFTPDL";
            this.chkFTPDL.Size = new System.Drawing.Size(15, 14);
            this.chkFTPDL.TabIndex = 2;
            this.chkFTPDL.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label5);
            this.groupBox4.Controls.Add(this.label4);
            this.groupBox4.Controls.Add(this.label16);
            this.groupBox4.Controls.Add(this.label15);
            this.groupBox4.Controls.Add(this.numLowPer);
            this.groupBox4.Controls.Add(this.num2TpDis);
            this.groupBox4.Location = new System.Drawing.Point(12, 424);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(673, 63);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "持续性";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(222, 28);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 6;
            this.label5.Text = "米";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(49, 28);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "两采样点间距离≤";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(465, 28);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(11, 12);
            this.label16.TabIndex = 0;
            this.label16.Text = "%";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(304, 28);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(89, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "低速率点占比≥";
            // 
            // numLowPer
            // 
            this.numLowPer.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            0});
            this.numLowPer.Location = new System.Drawing.Point(399, 23);
            this.numLowPer.Name = "numLowPer";
            this.numLowPer.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLowPer.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numLowPer.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numLowPer.Size = new System.Drawing.Size(60, 21);
            this.numLowPer.TabIndex = 2;
            // 
            // num2TpDis
            // 
            this.num2TpDis.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.num2TpDis.Location = new System.Drawing.Point(156, 23);
            this.num2TpDis.Name = "num2TpDis";
            this.num2TpDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num2TpDis.Size = new System.Drawing.Size(60, 21);
            this.num2TpDis.TabIndex = 1;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(518, 562);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(607, 562);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkSinr);
            this.groupBox3.Controls.Add(this.chkRsrp);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.numSinrMax);
            this.groupBox3.Controls.Add(this.numSinrMin);
            this.groupBox3.Controls.Add(this.numRsrpMin);
            this.groupBox3.Controls.Add(this.numRsrpMax);
            this.groupBox3.Location = new System.Drawing.Point(12, 345);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(673, 69);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "指标限定（且关系）";
            // 
            // chkSinr
            // 
            this.chkSinr.AutoSize = true;
            this.chkSinr.Location = new System.Drawing.Point(317, 34);
            this.chkSinr.Name = "chkSinr";
            this.chkSinr.Size = new System.Drawing.Size(15, 14);
            this.chkSinr.TabIndex = 3;
            this.chkSinr.UseVisualStyleBackColor = true;
            this.chkSinr.CheckedChanged += new System.EventHandler(this.chkSinr_CheckedChanged);
            // 
            // chkRsrp
            // 
            this.chkRsrp.AutoSize = true;
            this.chkRsrp.Location = new System.Drawing.Point(30, 34);
            this.chkRsrp.Name = "chkRsrp";
            this.chkRsrp.Size = new System.Drawing.Size(15, 14);
            this.chkRsrp.TabIndex = 0;
            this.chkRsrp.UseVisualStyleBackColor = true;
            this.chkRsrp.CheckedChanged += new System.EventHandler(this.chkRsrp_CheckedChanged);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(547, 34);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 6;
            this.label9.Text = "dB";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(260, 34);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(23, 12);
            this.label6.TabIndex = 6;
            this.label6.Text = "dBm";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(404, 34);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "≤SS-SINR≤";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(117, 34);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(71, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "≤SS-RSRP≤";
            // 
            // numSinrMax
            // 
            this.numSinrMax.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSinrMax.Enabled = false;
            this.numSinrMax.Location = new System.Drawing.Point(481, 29);
            this.numSinrMax.Name = "numSinrMax";
            this.numSinrMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSinrMax.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSinrMax.Properties.MinValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numSinrMax.Size = new System.Drawing.Size(60, 21);
            this.numSinrMax.TabIndex = 5;
            // 
            // numSinrMin
            // 
            this.numSinrMin.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numSinrMin.Enabled = false;
            this.numSinrMin.Location = new System.Drawing.Point(338, 29);
            this.numSinrMin.Name = "numSinrMin";
            this.numSinrMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSinrMin.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numSinrMin.Properties.MinValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numSinrMin.Size = new System.Drawing.Size(60, 21);
            this.numSinrMin.TabIndex = 4;
            // 
            // numRsrpMin
            // 
            this.numRsrpMin.EditValue = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            this.numRsrpMin.Enabled = false;
            this.numRsrpMin.Location = new System.Drawing.Point(51, 29);
            this.numRsrpMin.Name = "numRsrpMin";
            this.numRsrpMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRsrpMin.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRsrpMin.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRsrpMin.Size = new System.Drawing.Size(60, 21);
            this.numRsrpMin.TabIndex = 1;
            // 
            // numRsrpMax
            // 
            this.numRsrpMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRsrpMax.Enabled = false;
            this.numRsrpMax.Location = new System.Drawing.Point(194, 29);
            this.numRsrpMax.Name = "numRsrpMax";
            this.numRsrpMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRsrpMax.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRsrpMax.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRsrpMax.Size = new System.Drawing.Size(60, 21);
            this.numRsrpMax.TabIndex = 2;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.checkBoxLowSpeed_NR);
            this.groupBox5.Controls.Add(this.checkBoxLTELowSpeed);
            this.groupBox5.Controls.Add(this.checkBoxSynthesisLowSpeed);
            this.groupBox5.Location = new System.Drawing.Point(12, 495);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(673, 55);
            this.groupBox5.TabIndex = 4;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "网络";
            // 
            // checkBoxLowSpeed_NR
            // 
            this.checkBoxLowSpeed_NR.AutoSize = true;
            this.checkBoxLowSpeed_NR.Location = new System.Drawing.Point(167, 20);
            this.checkBoxLowSpeed_NR.Name = "checkBoxLowSpeed_NR";
            this.checkBoxLowSpeed_NR.Size = new System.Drawing.Size(96, 16);
            this.checkBoxLowSpeed_NR.TabIndex = 2;
            this.checkBoxLowSpeed_NR.Text = "NR低速率路段";
            this.checkBoxLowSpeed_NR.UseVisualStyleBackColor = true;
            // 
            // checkBoxLTELowSpeed
            // 
            this.checkBoxLTELowSpeed.AutoSize = true;
            this.checkBoxLTELowSpeed.Location = new System.Drawing.Point(296, 20);
            this.checkBoxLTELowSpeed.Name = "checkBoxLTELowSpeed";
            this.checkBoxLTELowSpeed.Size = new System.Drawing.Size(102, 16);
            this.checkBoxLTELowSpeed.TabIndex = 1;
            this.checkBoxLTELowSpeed.Text = "LTE低速率路段";
            this.checkBoxLTELowSpeed.UseVisualStyleBackColor = true;
            // 
            // checkBoxSynthesisLowSpeed
            // 
            this.checkBoxSynthesisLowSpeed.AutoSize = true;
            this.checkBoxSynthesisLowSpeed.Location = new System.Drawing.Point(30, 20);
            this.checkBoxSynthesisLowSpeed.Name = "checkBoxSynthesisLowSpeed";
            this.checkBoxSynthesisLowSpeed.Size = new System.Drawing.Size(108, 16);
            this.checkBoxSynthesisLowSpeed.TabIndex = 0;
            this.checkBoxSynthesisLowSpeed.Text = "综合低速率路段";
            this.checkBoxSynthesisLowSpeed.UseVisualStyleBackColor = true;
            // 
            // LowSpeedRoadDlg_NR
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(697, 596);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox1);
            this.Name = "LowSpeedRoadDlg_NR";
            this.Text = "低速率路段设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.grpThroughput.ResumeLayout(false);
            this.grpThroughput.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbThroughput.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputUlMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputUlDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputUlMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDlMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDlDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThroughputDlMax.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPULMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEMailDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numVideoDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numVideoMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numVideoMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDLMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLowPer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.num2TpDis.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSinrMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMax.Properties)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton radioThroughput;
        private System.Windows.Forms.RadioButton radioApp;
        private System.Windows.Forms.GroupBox grpThroughput;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.SpinEdit numThroughputDlMax;
        private DevExpress.XtraEditors.SpinEdit numFTPDLMax;
        private DevExpress.XtraEditors.SpinEdit numHTTPMax;
        private System.Windows.Forms.CheckBox chkHttp;
        private DevExpress.XtraEditors.SpinEdit numEmailMax;
        private System.Windows.Forms.CheckBox chkEmail;
        private System.Windows.Forms.CheckBox chkFTPDL;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numFTPDLDistance;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit num2TpDis;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label8;
        private DevExpress.XtraEditors.SpinEdit numRsrpMin;
        private DevExpress.XtraEditors.SpinEdit numRsrpMax;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label7;
        private DevExpress.XtraEditors.SpinEdit numSinrMax;
        private DevExpress.XtraEditors.SpinEdit numSinrMin;
        private System.Windows.Forms.CheckBox chkSinr;
        private System.Windows.Forms.CheckBox chkRsrp;
        private DevExpress.XtraEditors.SpinEdit numHTTPMin;
        private DevExpress.XtraEditors.SpinEdit numEmailMin;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private DevExpress.XtraEditors.SpinEdit numFTPDLMin;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.SpinEdit numThroughputDlMin;
        private System.Windows.Forms.Label label15;
        private DevExpress.XtraEditors.SpinEdit numLowPer;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.CheckBox checkBoxLTELowSpeed;
        private System.Windows.Forms.CheckBox checkBoxSynthesisLowSpeed;
        private DevExpress.XtraEditors.SpinEdit numVideoMin;
        private System.Windows.Forms.Label label17;
        private DevExpress.XtraEditors.SpinEdit numVideoMax;
        private System.Windows.Forms.CheckBox chkVideo;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label23;
        private DevExpress.XtraEditors.SpinEdit numVideoDistance;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private DevExpress.XtraEditors.SpinEdit numHTTPDistance;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private DevExpress.XtraEditors.SpinEdit numEMailDistance;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
        private DevExpress.XtraEditors.SpinEdit numThroughputDlDistance;
        private System.Windows.Forms.Label label26;
        private DevExpress.XtraEditors.SpinEdit numThroughputUlMin;
        private System.Windows.Forms.Label label27;
        private DevExpress.XtraEditors.SpinEdit numThroughputUlDistance;
        private DevExpress.XtraEditors.SpinEdit numThroughputUlMax;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.CheckBox chkThroughputUl;
        private System.Windows.Forms.CheckBox chkThroughputDl;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.Label label35;
        private DevExpress.XtraEditors.SpinEdit numFTPULDistance;
        private DevExpress.XtraEditors.SpinEdit numFTPULMin;
        private System.Windows.Forms.Label label36;
        private DevExpress.XtraEditors.SpinEdit numFTPULMax;
        private System.Windows.Forms.CheckBox chkFTPUL;
        private System.Windows.Forms.CheckBox checkBoxLowSpeed_NR;
        private DevExpress.XtraEditors.ComboBoxEdit cmbThroughput;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
    }
}