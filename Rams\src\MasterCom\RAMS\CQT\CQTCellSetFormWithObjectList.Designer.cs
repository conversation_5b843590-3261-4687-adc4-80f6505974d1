﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTCellSetFormWithObjectList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelTop = new System.Windows.Forms.Panel();
            this.comboBoxInOutDoor = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.pagCoverCell = new System.Windows.Forms.TabPage();
            this.objectListViewCoverCell = new BrightIdeasSoftware.ObjectListView();
            this.ColumnHeaderSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ColumnHeaderPositionName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ColumnHeaderCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ColumnHeaderLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ColumnHeaderCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ColumnHeaderCellType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ColumnHeaderProperty = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTestPointCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileTestPointRatio = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnC_IMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnC_IMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnC_IAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaPlaceDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.virtualObjectListView1 = new BrightIdeasSoftware.VirtualObjectListView();
            this.pagBTS = new System.Windows.Forms.TabPage();
            this.objectListViewCoverBTS = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnBTSSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTsCqtName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSSampleRatio = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSRxlevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSRxlevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSRxlevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSRxQualMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSRxQualMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSRxQualAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSDistanceMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSDistanceMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSC2IMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSC2IMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSC2IAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBTSAreaPlaceDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pagNBCell = new System.Windows.Forms.TabPage();
            this.objectListViewNBCell = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnNBSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCqtName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBRxlevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pagCoverFrequencyPoint = new System.Windows.Forms.TabPage();
            this.objectListViewCoverFrequencyPoint = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnFreqSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreqCqtName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreqType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreq = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreqCellCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreqCellRatio = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreqSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFreqSampleRatio = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.panelTop.SuspendLayout();
            this.tabControl.SuspendLayout();
            this.pagCoverCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewCoverCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.virtualObjectListView1)).BeginInit();
            this.pagBTS.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewCoverBTS)).BeginInit();
            this.pagNBCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewNBCell)).BeginInit();
            this.pagCoverFrequencyPoint.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewCoverFrequencyPoint)).BeginInit();
            this.SuspendLayout();
            // 
            // panelTop
            // 
            this.panelTop.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.panelTop.Controls.Add(this.comboBoxInOutDoor);
            this.panelTop.Controls.Add(this.label1);
            this.panelTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelTop.Location = new System.Drawing.Point(0, 0);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new System.Drawing.Size(936, 38);
            this.panelTop.TabIndex = 2;
            // 
            // comboBoxInOutDoor
            // 
            this.comboBoxInOutDoor.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxInOutDoor.FormattingEnabled = true;
            this.comboBoxInOutDoor.Items.AddRange(new object[] {
            "全部",
            "室外",
            "室内"});
            this.comboBoxInOutDoor.Location = new System.Drawing.Point(107, 9);
            this.comboBoxInOutDoor.Name = "comboBoxInOutDoor";
            this.comboBoxInOutDoor.Size = new System.Drawing.Size(91, 20);
            this.comboBoxInOutDoor.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(24, 13);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "室内外类型：";
            // 
            // tabControl
            // 
            this.tabControl.Controls.Add(this.pagCoverCell);
            this.tabControl.Controls.Add(this.pagBTS);
            this.tabControl.Controls.Add(this.pagNBCell);
            this.tabControl.Controls.Add(this.pagCoverFrequencyPoint);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 38);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(936, 370);
            this.tabControl.TabIndex = 3;
            // 
            // pagCoverCell
            // 
            this.pagCoverCell.Controls.Add(this.objectListViewCoverCell);
            this.pagCoverCell.Controls.Add(this.virtualObjectListView1);
            this.pagCoverCell.Location = new System.Drawing.Point(4, 22);
            this.pagCoverCell.Name = "pagCoverCell";
            this.pagCoverCell.Padding = new System.Windows.Forms.Padding(3);
            this.pagCoverCell.Size = new System.Drawing.Size(928, 344);
            this.pagCoverCell.TabIndex = 0;
            this.pagCoverCell.Text = "覆盖小区";
            this.pagCoverCell.UseVisualStyleBackColor = true;
            // 
            // objectListViewCoverCell
            // 
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderSN);
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderPositionName);
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderCellName);
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderLAC);
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderCI);
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderCellType);
            this.objectListViewCoverCell.AllColumns.Add(this.ColumnHeaderProperty);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnTestPointCount);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnFileTestPointRatio);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnRxLevMin);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnRxLevMax);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnRxLevAvg);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnRxQualMin);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnRxQualMax);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnRxQualAvg);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnDistanceMin);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnDistanceMax);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnDistanceAvg);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnLongitude);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnLatitude);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnC_IMin);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnC_IMax);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnC_IAvg);
            this.objectListViewCoverCell.AllColumns.Add(this.olvColumnAreaPlaceDesc);
            this.objectListViewCoverCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.ColumnHeaderSN,
            this.ColumnHeaderPositionName,
            this.ColumnHeaderCellName,
            this.ColumnHeaderLAC,
            this.ColumnHeaderCI,
            this.ColumnHeaderCellType,
            this.ColumnHeaderProperty,
            this.olvColumnTestPointCount,
            this.olvColumnFileTestPointRatio,
            this.olvColumnRxLevMin,
            this.olvColumnRxLevMax,
            this.olvColumnRxLevAvg,
            this.olvColumnRxQualMin,
            this.olvColumnRxQualMax,
            this.olvColumnRxQualAvg,
            this.olvColumnDistanceMin,
            this.olvColumnDistanceMax,
            this.olvColumnDistanceAvg,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnC_IMin,
            this.olvColumnC_IMax,
            this.olvColumnC_IAvg,
            this.olvColumnAreaPlaceDesc});
            this.objectListViewCoverCell.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewCoverCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListViewCoverCell.Location = new System.Drawing.Point(3, 3);
            this.objectListViewCoverCell.Name = "objectListViewCoverCell";
            this.objectListViewCoverCell.Size = new System.Drawing.Size(922, 338);
            this.objectListViewCoverCell.TabIndex = 1;
            this.objectListViewCoverCell.UseCompatibleStateImageBehavior = false;
            this.objectListViewCoverCell.View = System.Windows.Forms.View.Details;
            // 
            // ColumnHeaderSN
            // 
            this.ColumnHeaderSN.AspectName = "SN";
            this.ColumnHeaderSN.HeaderFont = null;
            this.ColumnHeaderSN.Text = "序号";
            // 
            // ColumnHeaderPositionName
            // 
            this.ColumnHeaderPositionName.AspectName = "CqtName";
            this.ColumnHeaderPositionName.HeaderFont = null;
            this.ColumnHeaderPositionName.Text = "地点名称";
            // 
            // ColumnHeaderCellName
            // 
            this.ColumnHeaderCellName.AspectName = "Name";
            this.ColumnHeaderCellName.AspectToStringFormat = "";
            this.ColumnHeaderCellName.HeaderFont = null;
            this.ColumnHeaderCellName.Text = "小区名称";
            // 
            // ColumnHeaderLAC
            // 
            this.ColumnHeaderLAC.AspectName = "LAC";
            this.ColumnHeaderLAC.HeaderFont = null;
            this.ColumnHeaderLAC.Text = "LAC";
            // 
            // ColumnHeaderCI
            // 
            this.ColumnHeaderCI.AspectName = "CI";
            this.ColumnHeaderCI.HeaderFont = null;
            this.ColumnHeaderCI.Text = "CI";
            // 
            // ColumnHeaderCellType
            // 
            this.ColumnHeaderCellType.AspectName = "Type";
            this.ColumnHeaderCellType.HeaderFont = null;
            this.ColumnHeaderCellType.Text = "小区类型";
            // 
            // ColumnHeaderProperty
            // 
            this.ColumnHeaderProperty.AspectName = "Property";
            this.ColumnHeaderProperty.HeaderFont = null;
            this.ColumnHeaderProperty.Text = "小区覆盖属性";
            // 
            // olvColumnTestPointCount
            // 
            this.olvColumnTestPointCount.AspectName = "TestPointCount";
            this.olvColumnTestPointCount.HeaderFont = null;
            this.olvColumnTestPointCount.Text = "采样点数";
            // 
            // olvColumnFileTestPointRatio
            // 
            this.olvColumnFileTestPointRatio.AspectName = "FileTestPointRatio";
            this.olvColumnFileTestPointRatio.HeaderFont = null;
            this.olvColumnFileTestPointRatio.Text = "采样点比(%)";
            // 
            // olvColumnRxLevMin
            // 
            this.olvColumnRxLevMin.AspectName = "RxLevMin";
            this.olvColumnRxLevMin.HeaderFont = null;
            this.olvColumnRxLevMin.Text = "最小场强";
            // 
            // olvColumnRxLevMax
            // 
            this.olvColumnRxLevMax.AspectName = "RxLevMax";
            this.olvColumnRxLevMax.HeaderFont = null;
            this.olvColumnRxLevMax.Text = "最大场强";
            // 
            // olvColumnRxLevAvg
            // 
            this.olvColumnRxLevAvg.AspectName = "RxLevAvg";
            this.olvColumnRxLevAvg.HeaderFont = null;
            this.olvColumnRxLevAvg.Text = "平均场强";
            // 
            // olvColumnRxQualMin
            // 
            this.olvColumnRxQualMin.AspectName = "RxQualMin";
            this.olvColumnRxQualMin.HeaderFont = null;
            this.olvColumnRxQualMin.Text = "最小质量";
            // 
            // olvColumnRxQualMax
            // 
            this.olvColumnRxQualMax.AspectName = "RxQualMax";
            this.olvColumnRxQualMax.HeaderFont = null;
            this.olvColumnRxQualMax.Text = "最大质量";
            // 
            // olvColumnRxQualAvg
            // 
            this.olvColumnRxQualAvg.AspectName = "RxQualAvg";
            this.olvColumnRxQualAvg.HeaderFont = null;
            this.olvColumnRxQualAvg.Text = "平均质量";
            // 
            // olvColumnDistanceMin
            // 
            this.olvColumnDistanceMin.AspectName = "DistanceMin";
            this.olvColumnDistanceMin.HeaderFont = null;
            this.olvColumnDistanceMin.Text = "最小距离";
            // 
            // olvColumnDistanceMax
            // 
            this.olvColumnDistanceMax.AspectName = "DistanceMax";
            this.olvColumnDistanceMax.HeaderFont = null;
            this.olvColumnDistanceMax.Text = "最大距离";
            // 
            // olvColumnDistanceAvg
            // 
            this.olvColumnDistanceAvg.AspectName = "DistanceAvg";
            this.olvColumnDistanceAvg.HeaderFont = null;
            this.olvColumnDistanceAvg.Text = "平均距离(m)";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.AspectName = "Longitude";
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.AspectName = "Latitude";
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnC_IMin
            // 
            this.olvColumnC_IMin.AspectName = "C_IMin";
            this.olvColumnC_IMin.HeaderFont = null;
            this.olvColumnC_IMin.Text = "最小C/I";
            // 
            // olvColumnC_IMax
            // 
            this.olvColumnC_IMax.AspectName = "C_IMax";
            this.olvColumnC_IMax.HeaderFont = null;
            this.olvColumnC_IMax.Text = "最大C/I";
            // 
            // olvColumnC_IAvg
            // 
            this.olvColumnC_IAvg.AspectName = "C_IAvg";
            this.olvColumnC_IAvg.HeaderFont = null;
            this.olvColumnC_IAvg.Text = "平均C/I";
            // 
            // olvColumnAreaPlaceDesc
            // 
            this.olvColumnAreaPlaceDesc.AspectName = "AreaPlaceDesc";
            this.olvColumnAreaPlaceDesc.HeaderFont = null;
            this.olvColumnAreaPlaceDesc.Text = "网格";
            // 
            // virtualObjectListView1
            // 
            this.virtualObjectListView1.BackColor = System.Drawing.SystemColors.Window;
            this.virtualObjectListView1.Location = new System.Drawing.Point(751, 313);
            this.virtualObjectListView1.Name = "virtualObjectListView1";
            this.virtualObjectListView1.ShowGroups = false;
            this.virtualObjectListView1.Size = new System.Drawing.Size(8, 8);
            this.virtualObjectListView1.TabIndex = 0;
            this.virtualObjectListView1.UseCompatibleStateImageBehavior = false;
            this.virtualObjectListView1.View = System.Windows.Forms.View.Details;
            this.virtualObjectListView1.VirtualMode = true;
            // 
            // pagBTS
            // 
            this.pagBTS.Controls.Add(this.objectListViewCoverBTS);
            this.pagBTS.Location = new System.Drawing.Point(4, 22);
            this.pagBTS.Name = "pagBTS";
            this.pagBTS.Size = new System.Drawing.Size(928, 344);
            this.pagBTS.TabIndex = 4;
            this.pagBTS.Text = "覆盖基站";
            this.pagBTS.UseVisualStyleBackColor = true;
            // 
            // objectListViewCoverBTS
            // 
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSSN);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTsCqtName);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSName);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSType);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSSampleCount);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSSampleRatio);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSRxlevMin);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSRxlevMax);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSRxlevAvg);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSRxQualMin);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSRxQualMax);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSRxQualAvg);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSDistanceMin);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSDistanceMax);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSDistanceAvg);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSLongitude);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSLatitude);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSC2IMin);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSC2IMax);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSC2IAvg);
            this.objectListViewCoverBTS.AllColumns.Add(this.olvColumnBTSAreaPlaceDesc);
            this.objectListViewCoverBTS.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnBTSSN,
            this.olvColumnBTsCqtName,
            this.olvColumnBTSName,
            this.olvColumnBTSType,
            this.olvColumnBTSSampleCount,
            this.olvColumnBTSSampleRatio,
            this.olvColumnBTSRxlevMin,
            this.olvColumnBTSRxlevMax,
            this.olvColumnBTSRxlevAvg,
            this.olvColumnBTSRxQualMin,
            this.olvColumnBTSRxQualMax,
            this.olvColumnBTSRxQualAvg,
            this.olvColumnBTSDistanceMin,
            this.olvColumnBTSDistanceMax,
            this.olvColumnBTSDistanceAvg,
            this.olvColumnBTSLongitude,
            this.olvColumnBTSLatitude,
            this.olvColumnBTSC2IMin,
            this.olvColumnBTSC2IMax,
            this.olvColumnBTSC2IAvg,
            this.olvColumnBTSAreaPlaceDesc});
            this.objectListViewCoverBTS.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewCoverBTS.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListViewCoverBTS.Location = new System.Drawing.Point(0, 0);
            this.objectListViewCoverBTS.Name = "objectListViewCoverBTS";
            this.objectListViewCoverBTS.Size = new System.Drawing.Size(928, 344);
            this.objectListViewCoverBTS.TabIndex = 0;
            this.objectListViewCoverBTS.UseCompatibleStateImageBehavior = false;
            this.objectListViewCoverBTS.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnBTSSN
            // 
            this.olvColumnBTSSN.AspectName = "SN";
            this.olvColumnBTSSN.HeaderFont = null;
            this.olvColumnBTSSN.Text = "序号";
            // 
            // olvColumnBTsCqtName
            // 
            this.olvColumnBTsCqtName.AspectName = "BTsCqtName";
            this.olvColumnBTsCqtName.HeaderFont = null;
            this.olvColumnBTsCqtName.Text = "地点名称";
            // 
            // olvColumnBTSName
            // 
            this.olvColumnBTSName.AspectName = "BTSName";
            this.olvColumnBTSName.HeaderFont = null;
            this.olvColumnBTSName.Text = "基站名称";
            // 
            // olvColumnBTSType
            // 
            this.olvColumnBTSType.AspectName = "BTSType";
            this.olvColumnBTSType.HeaderFont = null;
            this.olvColumnBTSType.Text = "基站类型";
            // 
            // olvColumnBTSSampleCount
            // 
            this.olvColumnBTSSampleCount.AspectName = "BTSSampleCount";
            this.olvColumnBTSSampleCount.HeaderFont = null;
            this.olvColumnBTSSampleCount.Text = "采样点数";
            // 
            // olvColumnBTSSampleRatio
            // 
            this.olvColumnBTSSampleRatio.AspectName = "BTSSampleRatio";
            this.olvColumnBTSSampleRatio.HeaderFont = null;
            this.olvColumnBTSSampleRatio.Text = "采样点比(%)";
            // 
            // olvColumnBTSRxlevMin
            // 
            this.olvColumnBTSRxlevMin.AspectName = "BTSRxlevMin";
            this.olvColumnBTSRxlevMin.HeaderFont = null;
            this.olvColumnBTSRxlevMin.Text = "最小场强";
            this.olvColumnBTSRxlevMin.Width = 68;
            // 
            // olvColumnBTSRxlevMax
            // 
            this.olvColumnBTSRxlevMax.AspectName = "BTSRxlevMax";
            this.olvColumnBTSRxlevMax.HeaderFont = null;
            this.olvColumnBTSRxlevMax.Text = "最大场强";
            // 
            // olvColumnBTSRxlevAvg
            // 
            this.olvColumnBTSRxlevAvg.AspectName = "BTSRxlevAvg";
            this.olvColumnBTSRxlevAvg.HeaderFont = null;
            this.olvColumnBTSRxlevAvg.Text = "平均场强";
            // 
            // olvColumnBTSRxQualMin
            // 
            this.olvColumnBTSRxQualMin.AspectName = "BTSRxQualMin";
            this.olvColumnBTSRxQualMin.HeaderFont = null;
            this.olvColumnBTSRxQualMin.Text = "最小质量";
            // 
            // olvColumnBTSRxQualMax
            // 
            this.olvColumnBTSRxQualMax.AspectName = "BTSRxQualMax";
            this.olvColumnBTSRxQualMax.HeaderFont = null;
            this.olvColumnBTSRxQualMax.Text = "最大质量";
            // 
            // olvColumnBTSRxQualAvg
            // 
            this.olvColumnBTSRxQualAvg.AspectName = "BTSRxQualAvg";
            this.olvColumnBTSRxQualAvg.HeaderFont = null;
            this.olvColumnBTSRxQualAvg.Text = "平均质量";
            // 
            // olvColumnBTSDistanceMin
            // 
            this.olvColumnBTSDistanceMin.AspectName = "BTSDistanceMin";
            this.olvColumnBTSDistanceMin.HeaderFont = null;
            this.olvColumnBTSDistanceMin.Text = "最小距离";
            // 
            // olvColumnBTSDistanceMax
            // 
            this.olvColumnBTSDistanceMax.AspectName = "BTSDistanceMax";
            this.olvColumnBTSDistanceMax.HeaderFont = null;
            this.olvColumnBTSDistanceMax.Text = "最大距离";
            // 
            // olvColumnBTSDistanceAvg
            // 
            this.olvColumnBTSDistanceAvg.AspectName = "BTSDistanceAvg";
            this.olvColumnBTSDistanceAvg.HeaderFont = null;
            this.olvColumnBTSDistanceAvg.Text = "平均距离(m)";
            // 
            // olvColumnBTSLongitude
            // 
            this.olvColumnBTSLongitude.AspectName = "BTSLongitude";
            this.olvColumnBTSLongitude.HeaderFont = null;
            this.olvColumnBTSLongitude.Text = "经度";
            // 
            // olvColumnBTSLatitude
            // 
            this.olvColumnBTSLatitude.AspectName = "BTSLatitude";
            this.olvColumnBTSLatitude.HeaderFont = null;
            this.olvColumnBTSLatitude.Text = "纬度";
            // 
            // olvColumnBTSC2IMin
            // 
            this.olvColumnBTSC2IMin.AspectName = "BTSC2IMin";
            this.olvColumnBTSC2IMin.HeaderFont = null;
            this.olvColumnBTSC2IMin.Text = "最小C/I";
            // 
            // olvColumnBTSC2IMax
            // 
            this.olvColumnBTSC2IMax.AspectName = "BTSC2IMax";
            this.olvColumnBTSC2IMax.HeaderFont = null;
            this.olvColumnBTSC2IMax.Text = "最大C/I";
            // 
            // olvColumnBTSC2IAvg
            // 
            this.olvColumnBTSC2IAvg.AspectName = "BTSC2IAvg";
            this.olvColumnBTSC2IAvg.HeaderFont = null;
            this.olvColumnBTSC2IAvg.Text = "平均C/I";
            // 
            // olvColumnBTSAreaPlaceDesc
            // 
            this.olvColumnBTSAreaPlaceDesc.AspectName = "BTSAreaPlaceDesc";
            this.olvColumnBTSAreaPlaceDesc.HeaderFont = null;
            this.olvColumnBTSAreaPlaceDesc.Text = "网格";
            // 
            // pagNBCell
            // 
            this.pagNBCell.Controls.Add(this.objectListViewNBCell);
            this.pagNBCell.Location = new System.Drawing.Point(4, 22);
            this.pagNBCell.Name = "pagNBCell";
            this.pagNBCell.Size = new System.Drawing.Size(928, 344);
            this.pagNBCell.TabIndex = 2;
            this.pagNBCell.Text = "邻区中小区";
            this.pagNBCell.UseVisualStyleBackColor = true;
            // 
            // objectListViewNBCell
            // 
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBSN);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBCqtName);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBName);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBLAC);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBCI);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBType);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnCount);
            this.objectListViewNBCell.AllColumns.Add(this.olvColumnNBRxlevAvg);
            this.objectListViewNBCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnNBSN,
            this.olvColumnNBCqtName,
            this.olvColumnNBName,
            this.olvColumnNBLAC,
            this.olvColumnNBCI,
            this.olvColumnNBType,
            this.olvColumnCount,
            this.olvColumnNBRxlevAvg});
            this.objectListViewNBCell.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewNBCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListViewNBCell.Location = new System.Drawing.Point(0, 0);
            this.objectListViewNBCell.Name = "objectListViewNBCell";
            this.objectListViewNBCell.Size = new System.Drawing.Size(928, 344);
            this.objectListViewNBCell.TabIndex = 0;
            this.objectListViewNBCell.UseCompatibleStateImageBehavior = false;
            this.objectListViewNBCell.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnNBSN
            // 
            this.olvColumnNBSN.AspectName = "SN";
            this.olvColumnNBSN.HeaderFont = null;
            this.olvColumnNBSN.Text = "序号";
            // 
            // olvColumnNBCqtName
            // 
            this.olvColumnNBCqtName.AspectName = "NBCqtName";
            this.olvColumnNBCqtName.HeaderFont = null;
            this.olvColumnNBCqtName.Text = "地点名称";
            // 
            // olvColumnNBName
            // 
            this.olvColumnNBName.AspectName = "NBName";
            this.olvColumnNBName.HeaderFont = null;
            this.olvColumnNBName.Text = "小区名称";
            // 
            // olvColumnNBLAC
            // 
            this.olvColumnNBLAC.AspectName = "NBLAC";
            this.olvColumnNBLAC.HeaderFont = null;
            this.olvColumnNBLAC.Text = "LAC";
            // 
            // olvColumnNBCI
            // 
            this.olvColumnNBCI.AspectName = "NBCI";
            this.olvColumnNBCI.HeaderFont = null;
            this.olvColumnNBCI.Text = "CI";
            // 
            // olvColumnNBType
            // 
            this.olvColumnNBType.AspectName = "NBType";
            this.olvColumnNBType.HeaderFont = null;
            this.olvColumnNBType.Text = "小区类型";
            // 
            // olvColumnCount
            // 
            this.olvColumnCount.AspectName = "Count";
            this.olvColumnCount.HeaderFont = null;
            this.olvColumnCount.Text = "出现次数";
            // 
            // olvColumnNBRxlevAvg
            // 
            this.olvColumnNBRxlevAvg.AspectName = "NBRxlevAvg";
            this.olvColumnNBRxlevAvg.HeaderFont = null;
            this.olvColumnNBRxlevAvg.Text = "平均场强";
            // 
            // pagCoverFrequencyPoint
            // 
            this.pagCoverFrequencyPoint.Controls.Add(this.objectListViewCoverFrequencyPoint);
            this.pagCoverFrequencyPoint.Location = new System.Drawing.Point(4, 22);
            this.pagCoverFrequencyPoint.Name = "pagCoverFrequencyPoint";
            this.pagCoverFrequencyPoint.Padding = new System.Windows.Forms.Padding(3);
            this.pagCoverFrequencyPoint.Size = new System.Drawing.Size(928, 344);
            this.pagCoverFrequencyPoint.TabIndex = 3;
            this.pagCoverFrequencyPoint.Text = "覆盖频点";
            this.pagCoverFrequencyPoint.UseVisualStyleBackColor = true;
            // 
            // objectListViewCoverFrequencyPoint
            // 
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqSN);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqCqtName);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqType);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreq);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqCellCount);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqCellRatio);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqSampleCount);
            this.objectListViewCoverFrequencyPoint.AllColumns.Add(this.olvColumnFreqSampleRatio);
            this.objectListViewCoverFrequencyPoint.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnFreqSN,
            this.olvColumnFreqCqtName,
            this.olvColumnFreqType,
            this.olvColumnFreq,
            this.olvColumnFreqCellCount,
            this.olvColumnFreqCellRatio,
            this.olvColumnFreqSampleCount,
            this.olvColumnFreqSampleRatio});
            this.objectListViewCoverFrequencyPoint.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewCoverFrequencyPoint.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListViewCoverFrequencyPoint.Location = new System.Drawing.Point(3, 3);
            this.objectListViewCoverFrequencyPoint.Name = "objectListViewCoverFrequencyPoint";
            this.objectListViewCoverFrequencyPoint.Size = new System.Drawing.Size(922, 338);
            this.objectListViewCoverFrequencyPoint.TabIndex = 0;
            this.objectListViewCoverFrequencyPoint.UseCompatibleStateImageBehavior = false;
            this.objectListViewCoverFrequencyPoint.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnFreqSN
            // 
            this.olvColumnFreqSN.AspectName = "SN";
            this.olvColumnFreqSN.HeaderFont = null;
            this.olvColumnFreqSN.Text = "序号";
            // 
            // olvColumnFreqCqtName
            // 
            this.olvColumnFreqCqtName.AspectName = "FreqCqtName";
            this.olvColumnFreqCqtName.HeaderFont = null;
            this.olvColumnFreqCqtName.Text = "地点名称";
            // 
            // olvColumnFreqType
            // 
            this.olvColumnFreqType.AspectName = "FreqType";
            this.olvColumnFreqType.HeaderFont = null;
            this.olvColumnFreqType.Text = "小区类型";
            // 
            // olvColumnFreq
            // 
            this.olvColumnFreq.AspectName = "Freq";
            this.olvColumnFreq.HeaderFont = null;
            this.olvColumnFreq.Text = "频点";
            // 
            // olvColumnFreqCellCount
            // 
            this.olvColumnFreqCellCount.AspectName = "FreqCellCount";
            this.olvColumnFreqCellCount.HeaderFont = null;
            this.olvColumnFreqCellCount.Text = "小区数";
            // 
            // olvColumnFreqCellRatio
            // 
            this.olvColumnFreqCellRatio.AspectName = "FreqCellRatio";
            this.olvColumnFreqCellRatio.HeaderFont = null;
            this.olvColumnFreqCellRatio.Text = "小区占比";
            // 
            // olvColumnFreqSampleCount
            // 
            this.olvColumnFreqSampleCount.AspectName = "FreqSampleCount";
            this.olvColumnFreqSampleCount.HeaderFont = null;
            this.olvColumnFreqSampleCount.Text = "采样点数";
            // 
            // olvColumnFreqSampleRatio
            // 
            this.olvColumnFreqSampleRatio.AspectName = "FreqSampleRatio";
            this.olvColumnFreqSampleRatio.HeaderFont = null;
            this.olvColumnFreqSampleRatio.Text = "采样点占比";
            // 
            // CQTCellSetFormWithObjectList
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(936, 408);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.panelTop);
            this.Name = "CQTCellSetFormWithObjectList";
            this.Text = "CQTCellSetFormWithObjectList";
            this.panelTop.ResumeLayout(false);
            this.panelTop.PerformLayout();
            this.tabControl.ResumeLayout(false);
            this.pagCoverCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewCoverCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.virtualObjectListView1)).EndInit();
            this.pagBTS.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewCoverBTS)).EndInit();
            this.pagNBCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewNBCell)).EndInit();
            this.pagCoverFrequencyPoint.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewCoverFrequencyPoint)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panelTop;
        private System.Windows.Forms.ComboBox comboBoxInOutDoor;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage pagCoverCell;
        private BrightIdeasSoftware.ObjectListView objectListViewCoverCell;
        private BrightIdeasSoftware.VirtualObjectListView virtualObjectListView1;
        private System.Windows.Forms.TabPage pagBTS;
        private BrightIdeasSoftware.ObjectListView objectListViewCoverBTS;
        private System.Windows.Forms.TabPage pagNBCell;
        private BrightIdeasSoftware.ObjectListView objectListViewNBCell;
        private System.Windows.Forms.TabPage pagCoverFrequencyPoint;
        private BrightIdeasSoftware.ObjectListView objectListViewCoverFrequencyPoint;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderSN;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderPositionName;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderCellName;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderLAC;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderCI;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderCellType;
        private BrightIdeasSoftware.OLVColumn ColumnHeaderProperty;
        private BrightIdeasSoftware.OLVColumn olvColumnTestPointCount;
        private BrightIdeasSoftware.OLVColumn olvColumnFileTestPointRatio;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualMax;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceMin;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceMax;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnC_IMin;
        private BrightIdeasSoftware.OLVColumn olvColumnC_IMax;
        private BrightIdeasSoftware.OLVColumn olvColumnC_IAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaPlaceDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSSN;
        private BrightIdeasSoftware.OLVColumn olvColumnBTsCqtName;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSName;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSType;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSSampleRatio;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSRxlevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSRxlevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSRxlevAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSRxQualMin;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSRxQualMax;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSRxQualAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSDistanceMin;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSDistanceMax;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSC2IMin;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSC2IMax;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSC2IAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnBTSAreaPlaceDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnNBSN;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCqtName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBType;
        private BrightIdeasSoftware.OLVColumn olvColumnCount;
        private BrightIdeasSoftware.OLVColumn olvColumnNBRxlevAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqCqtName;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqType;
        private BrightIdeasSoftware.OLVColumn olvColumnFreq;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqCellCount;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqCellRatio;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnFreqSampleRatio;
    }
}