﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    class UnDoneGroupStatAnalyzer
    {
        public List<GroupStatResult> Results = null;
        public List<TaskStatResult> TaskResults = null;

        public UnDoneGroupStatAnalyzer()
        {
            TaskResults = new List<TaskStatResult>();
            init();
        }

        public void ClearResult()
        {
            TaskResults = new List<TaskStatResult>();
            Results = new List<GroupStatResult>();
        }

        public void Analyze(List<GroupStatModel> models)
        {
            if (models == null)
            {
                return;
            }
           
            Results = new List<GroupStatResult>()
            {
                new GroupStatResult("T1"),
                new GroupStatResult("T2_1"),
                new GroupStatResult("T2_2"),
                new GroupStatResult("T2_3"),
                new GroupStatResult("T2_4"),
                new GroupStatResult("T2_5"),
                new GroupStatResult("T3"),
                new GroupStatResult("地市处理"),
                new GroupStatResult("申诉审核"),
                new GroupStatResult("挂起处理"),
                new GroupStatResult("归档工单")
            };

            foreach (var item in models)
            {
                int countIndex = getCountIndex(item);
                TaskStatResult taskResult = new TaskStatResult(item.TaskID, item.District, item.TaskName);
                int groupIndex = getGroupIndex(item.GroupName);
                if (item.Description.Contains("已完成"))
                {
                    Results[10].Counts[countIndex]++;
                    taskResult.Counts[10]++;
                    TaskResults.Add(taskResult);
                    continue;
                }

                //申诉审核和挂起处理工单
                if (item.Description.Contains("申诉审核"))
                {
                    Results[8].Counts[countIndex]++;
                    taskResult.Counts[8]++;
                    TaskResults.Add(taskResult);
                    continue;
                }
                if (item.Description.Contains("挂起处理"))
                {
                    Results[9].Counts[countIndex]++;
                    taskResult.Counts[9]++;
                    TaskResults.Add(taskResult);
                    continue;
                }

                if (groupIndex >= 0 && groupIndex != 7 && validState(item.Description))
                {
                    Results[groupIndex].Counts[countIndex]++;
                    taskResult.Counts[groupIndex]++;
                }
                if (groupIndex == 7 && validState2(item.Description))
                {
                    Results[groupIndex].Counts[countIndex]++;
                    taskResult.Counts[groupIndex]++;
                }
                TaskResults.Add(taskResult);
            }
        }

        private int getCountIndex(GroupStatModel item)
        {
            int countIndex = validDistrict(item.District);
            if (countIndex == 6)
            {
                if (item.AreaName.Contains("天门市"))
                {
                    countIndex = 7;
                }
                else if (item.AreaName.Contains("潜江市"))
                {
                    countIndex = 8;
                }
            }

            return countIndex;
        }

        private int getGroupIndex(string str)
        {
            int index = -1;
            if (groupDic.ContainsKey(str))
            {
                index = groupDic[str];
            }
            else if (validDistrict(str) >= 0)
            {
                index = 7;
            }
            return index;
        }

        private int validDistrict(string str)
        {
            int index = -1;
            if (districtDic.ContainsKey(str))
            {
                index = districtDic[str];
            }
            return index;
        }

        private void init()
        {
            initGroup();
            initDistrict();
        }

        Dictionary<string, int> groupDic;
        private void initGroup()
        {
            groupDic = new Dictionary<string, int>();
            groupDic.Add("T1", 0);
            groupDic.Add("测试数据管理", 0);
            groupDic.Add("T2_1", 1);
            groupDic.Add("片区优化组", 1);
            groupDic.Add("T2_2", 2);
            groupDic.Add("参数维护组", 2);
            groupDic.Add("T2_3", 3);
            groupDic.Add("室分维护组", 3);
            groupDic.Add("T2_4", 4);
            groupDic.Add("无线规划组", 4);
            groupDic.Add("T2_5", 5);
            groupDic.Add("干线优化组", 5);
            groupDic.Add("T2_6", 6);
            groupDic.Add("疑难问题处理组", 6);
        }

        Dictionary<string, int> districtDic;
        private void initDistrict()
        {
            districtDic = new Dictionary<string, int>();
            districtDic.Add("ezhou", 0);
            districtDic.Add("鄂州", 0);
            districtDic.Add("enshi", 1);
            districtDic.Add("恩施", 1);
            districtDic.Add("huangshi", 2);
            districtDic.Add("黄石", 2);
            districtDic.Add("huanggang", 3);
            districtDic.Add("黄冈", 3);
            districtDic.Add("jingmen", 4);
            districtDic.Add("荆门", 4);
            districtDic.Add("jingzhou", 5);
            districtDic.Add("荆州", 5);
            districtDic.Add("jianghan", 6);
            districtDic.Add("江汉", 6);
            districtDic.Add("tianmen", 7);
            districtDic.Add("天门", 7);
            districtDic.Add("qianjiang", 8);
            districtDic.Add("潜江", 8);
            districtDic.Add("shiyan", 9);
            districtDic.Add("十堰", 9);
            districtDic.Add("suizhou", 10);
            districtDic.Add("随州", 10);
            districtDic.Add("wuhan", 11);
            districtDic.Add("武汉", 11);
            districtDic.Add("xiangyang", 12);
            districtDic.Add("襄阳", 12);
            districtDic.Add("xianning", 13);
            districtDic.Add("咸宁", 13);
            districtDic.Add("xiaogan", 14);
            districtDic.Add("孝感", 14);
            districtDic.Add("yichang", 15);
            districtDic.Add("宜昌", 15);
        }

        private bool validState(string state)
        {
            bool reson = false;
            if (state.Contains("已接单"))
            {
                reson = true;
            }
            else if (state.Contains("已回单"))
            {
                reson = true;
            }
            else if (state.Contains("验证失败"))
            {
                reson = true;
            }
            return reson;
        }

        private bool validState2(string state)
        {
            bool reson = false;
            if (state.Contains("已派单"))
            {
                reson = true;
            }
            else if (state.Contains("已回单"))
            {
                reson = true;
            }
            else if (state.Contains("验证失败"))
            {
                reson = true;
            }
            return reson;
        }
    }
}
