﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.Interface
{
    //获取系统时间
    class QueryServerDate : DIYSQLBase
    {
        DateTime time;
        public DateTime Time
        {
            get { return time;}
        }
        public QueryServerDate(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override string getSqlTextString()
        {
            return "select getdate()";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            while(true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();

                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    time =Convert.ToDateTime( package.Content.GetParamString());
                 
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
    }
}
