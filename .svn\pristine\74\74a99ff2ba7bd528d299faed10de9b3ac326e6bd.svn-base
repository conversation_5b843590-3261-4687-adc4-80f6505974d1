﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTFilePathInfoForm : Form
    {
        public ZTFilePathInfoForm(MainModel mainModel)
        {
            InitializeComponent();

            this.mainModel = mainModel;
         listView.ListViewItemSorter = new ListViewSorter(listView);
        }

        MainModel mainModel;
        public void LoadFiilePathInfo()
        {
            listView.ListViewItemSorter = null;
            listView.Items.Clear();
            int i = 1;
            foreach (PathInfo info in mainModel.PathInfoList)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Tag = info;
                listViewItem.ToolTipText = info.roadDesc;
                listViewItem.Text = i.ToString();
                listViewItem.SubItems[0].Tag = i;
                listViewItem.SubItems.Add(info.fileName);
                listViewItem.SubItems.Add(info.district);
                listViewItem.SubItems.Add(info.roadDesc);
                
                listView.Items.Add(listViewItem);
                i++;
            }


        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(listView);
        }
    }
}
