﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEReSelectBase : DIYAnalyseFilesOneByOneByRegion
    {
        protected static readonly object lockObj = new object();
        public List<ZTLTEReSelectItem> resultList { get; set; } = new List<ZTLTEReSelectItem>();    //保存结果
        public ZTLTEReSelectCondition hoCondition { get; set; } = new ZTLTEReSelectCondition();   //查询条件
        protected List<int> valuedEvtId;
        public ZTLTEReSelectBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
            valuedEvtId = new List<int> { 1300, 1302, 1304, 1306, 1308, 1310, 1312, 1314, 1316 };
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTLTEReSelectItem>();
        }

        ZTLTEReSelectSetForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTLTEReSelectSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileMng.TestPoints;
                List<Event> eventList = fileMng.Events;

                for (int eLoop = 0; eLoop < eventList.Count; ++eLoop)
                {
                    Event e = eventList[eLoop];
                    if (!valuedEvtId.Contains(e.ID))
                    {
                        continue;
                    }

                    int index = -1;
                    if ((index = GetNearestTestPointIndex(e.SN, testPointList)) == -1)
                    {
                        continue;
                    }

                    addResultList(testPointList, e, index);
                }
            }
        }

        private void addResultList(List<TestPoint> testPointList, Event e, int index)
        {
            ZTLTEReSelectItem item = new ZTLTEReSelectItem(e.FileName, e);

            long tpTimeHead = e.Time * 1000L + e.Millisecond - hoCondition.BeforeSecond * 1000L;
            long tpTimeTail = e.Time * 1000L + e.Millisecond + hoCondition.AfterSecond * 1000L;

            for (int i = index; i >= 0; --i)
            {
                TestPoint tp = testPointList[i];
                if ((tp.Time + hoCondition.BeforeSecond) * 1000L + tp.Millisecond < tpTimeHead)
                {
                    break;
                }
                item.AddBeforeTp(tp);
            }
            for (int i = index + 1; i < testPointList.Count; ++i)
            {
                TestPoint tp = testPointList[i];
                if (tpTimeTail < tp.Time * 1000L + tp.Millisecond)
                {
                    break;
                }
                item.AddAfterTp(tp);
            }

            item.SetSameSiteFlag(hoCondition.SiteDistance);

            item.SN = resultList.Count + 1;
            resultList.Add(item);
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLTEReSelectListForm).FullName);
            ZTLTEReSelectListForm cellReselectAnaListForm = obj == null ? null : obj as ZTLTEReSelectListForm;
            if (cellReselectAnaListForm == null || cellReselectAnaListForm.IsDisposed)
            {
                cellReselectAnaListForm = new ZTLTEReSelectListForm(MainModel);
            }

            cellReselectAnaListForm.FillData(resultList);
            if (!cellReselectAnaListForm.Visible)
            {
                cellReselectAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        /// <summary>
        /// tpList升序，寻找最大的index使得tpList[index] <= eventSN
        /// </summary>
        /// <param name="eventSN"></param>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected int GetNearestTestPointIndex(int eventSN, List<TestPoint> tpList)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > eventSN)
                {
                    index = i - 1;
                    break;
                }
            }
            return index;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTLTEReSelectBase_FDD : ZTLTEReSelectBase
    {
        public ZTLTEReSelectBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
            valuedEvtId = new List<int> { 3300, 3304, 3306, 3308, 3310, 3312, 3314, 3316 };
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26050, this.Name);//////
        }
    }

    public class ZTLTEReSelectItem
    {
        private readonly List<int> valueEvtID = new List<int> { 1300, 1302, 1304, 1306, 1308, 1310, 1312, 1314, 1316, 3300, 3304, 3306, 3308, 3310, 3312, 3314, 3316 };
        public int SN { get; set; }
        public string FileName { get; set; }

        public string DateTime
        {
            get
            {
                if (HOEvt != null)
                {
                    return HOEvt.DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                }
                return "";
            }
        }

        public double Longitude
        {
            get
            {
                if (HOEvt != null)
                {
                    return HOEvt.Longitude;
                }
                return 0;
            }
        }
        public double Latitude
        {
            get
            {
                if (HOEvt != null)
                {
                    return HOEvt.Latitude;
                }
                return 0;
            }
        }

        public string HandOverResult
        {
            get
            {
                if (HOEvt != null && valueEvtID.Contains(HOEvt.ID))
                {
                    return "成功";
                }
                return "";
            }
        }
        public string HnadOverDirection
        {
            get
            {
                return LTECell.GetBandTypeByEarfcn(SrcCellItem.EARFCN) + "->" + LTECell.GetBandTypeByEarfcn(DestCellItem.EARFCN);
            }
        }

        public ZTLTEReSelectCellItem SrcCellItem { get; set; }
        public ZTLTEReSelectCellItem DestCellItem { get; set; }

        public ZTLTEReSelectTpItem BeforeTpItem { get; set; }
        public ZTLTEReSelectTpItem AfterTpItem { get; set; }

        public Event HOEvt { get; set; }
        public List<TestPoint> TpList { get; set; }
        public string IsSameSite { get; set; }

        public ZTLTEReSelectItem(string fileName, Event hoEvt)
        {
            FileName = fileName;
            HOEvt = hoEvt;
            SrcCellItem = new ZTLTEReSelectCellItem();
            DestCellItem = new ZTLTEReSelectCellItem();
            BeforeTpItem = new ZTLTEReSelectTpItem();
            AfterTpItem = new ZTLTEReSelectTpItem();
            TpList = new List<TestPoint>();

            SetCellItem(SrcCellItem, (int)hoEvt["LAC"], (int)hoEvt["CI"], Convert.ToInt32(hoEvt["Value2"]), Convert.ToInt32(hoEvt["Value1"]), hoEvt);
            SetCellItem(DestCellItem, (int)hoEvt["TargetLAC"], (int)hoEvt["TargetCI"], Convert.ToInt32(hoEvt["Value4"]), Convert.ToInt32(hoEvt["Value3"]), hoEvt);
        }

        public void AddBeforeTp(TestPoint tp)
        {
            AddTpInfo(this.BeforeTpItem, tp);
        }

        public void AddAfterTp(TestPoint tp)
        {
            AddTpInfo(this.AfterTpItem, tp);
        }

        public void AddTpInfo(ZTLTEReSelectTpItem tpItem, TestPoint tp)
        {
            TpList.Add(tp);
            float? rsrp = GetRSRP(tp);
            if (rsrp != null && rsrp >= -140 && rsrp <= -10)
            {
                tpItem.Rsrp += (float)rsrp;
                tpItem.RsrpCount++;
            }

            float? sinr = GetSINR(tp);
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                tpItem.Sinr += (float)sinr;
                tpItem.SinrCount++;
            }

            float? appSpeed = GetAppSpeed(tp);
            if (appSpeed != null && appSpeed >= 0)
            {
                tpItem.AppSpeed += (float)appSpeed / 1000 / 1000;
                tpItem.AppSpeedCount++;
            }

            int? status = GetStatus(tp);
            float? pdcpSpeed = GetPDCP(tp);
            if (pdcpSpeed != null && pdcpSpeed >= 0 && status != null && status == 12)
            {
                tpItem.PdcpSpeed += (float)pdcpSpeed / 1000 / 1000;
                tpItem.PdcpSpeedCount++;
            }

            float? rsrq = GetRSRQ(tp);
            if (rsrq != null && rsrq >= -40 && rsrq <= 40)
            {
                tpItem.Rsrq += (float)rsrq;
                tpItem.RsrqCount++;
            }

            float? rssi = GetRSSI(tp);
            if (rssi != null && rssi >= -125 && rssi <= -25)
            {
                tpItem.Rssi += (float)rssi;
                tpItem.RssiCount++;
            }
        }

        protected float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
        protected float? GetAppSpeed(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)(int?)tp["lte_fdd_APP_ThroughputDL"];
            }
            return (float?)(int?)tp["lte_APP_ThroughputDL"];
        }
        protected int? GetStatus(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)(short?)tp["lte_fdd_APP_Status"]; 
            }
            return (int?)(short?)tp["lte_APP_Status"];
        }
        protected float? GetPDCP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)(int?)tp["lte_fdd_PDCP_DL"];
            }
            return (float?)(int?)tp["lte_PDCP_DL"];
        }
        protected float? GetRSRQ(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRQ"];
            }
            return (float?)tp["lte_RSRQ"];
        }
        protected float? GetRSSI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSSI"];
            }
            return (float?)tp["lte_RSSI"];
        }

        public void SetCellItem(ZTLTEReSelectCellItem cellItem, int tac, int eci, int earfrn, int pci, Event reselEvt)
        {
            LTECell cell = CellManager.GetInstance().GetLTECell(reselEvt.DateTime, tac, eci);

            if (cell != null)
            {
                cellItem.LteCell = cell;
                cellItem.CellName = cell.Name;
                cellItem.CellID = cell.CellID.ToString();
                cellItem.Distance = Math.Round(cell.GetDistance(reselEvt.Longitude, reselEvt.Latitude), 2).ToString();
            }

            cellItem.TAC = tac.ToString();
            cellItem.ECI = eci.ToString();
            cellItem.EARFCN = earfrn;
            cellItem.PCI = pci;
        }

        public void SetSameSiteFlag(int siteDistance)
        {
            if (SrcCellItem.LteCell != null && DestCellItem.LteCell != null)
            {
                if (SrcCellItem.LteCell.GetDistance(DestCellItem.LteCell.Longitude, DestCellItem.LteCell.Latitude) <= siteDistance)
                {
                    IsSameSite = "是";
                }
                else
                {
                    IsSameSite = "否";
                }
            }
        }

        #region 预处理
        public int BeforeEarfcn
        {
            get
            {
                return SrcCellItem.EARFCN;
            }
        }
        public int BeforePCI
        {
            get
            {
                return SrcCellItem.PCI;
            }
        }
        public string BeforeCellName
        {
            get
            {
                return SrcCellItem.CellName;
            }
        }
        public string BeforeCellDistance
        {
            get
            {
                return SrcCellItem.Distance;
            }
        }
        public string BeforeRsrpAvg
        {
            get
            {
                if (BeforeTpItem.RsrpCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rsrp / BeforeTpItem.RsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeSinrAvg
        {
            get
            {
                if (BeforeTpItem.SinrCount > 0)
                {
                    return Math.Round(BeforeTpItem.Sinr / BeforeTpItem.SinrCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeAppSpeedAvg
        {
            get
            {
                if (BeforeTpItem.AppSpeedCount > 0)
                {
                    return Math.Round(BeforeTpItem.AppSpeed / BeforeTpItem.AppSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforePdcpSpeedAvg
        {
            get
            {
                if (BeforeTpItem.PdcpSpeedCount > 0)
                {
                    return Math.Round(BeforeTpItem.PdcpSpeed / BeforeTpItem.PdcpSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRsrqAvg
        {
            get
            {
                if (BeforeTpItem.RsrqCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rsrq / BeforeTpItem.RsrqCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRssiAvg
        {
            get
            {
                if (BeforeTpItem.RssiCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rssi / BeforeTpItem.RssiCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public int AfterEarfcn
        {
            get
            {
                return DestCellItem.EARFCN;
            }
        }
        public int AfterPCI
        {
            get
            {
                return DestCellItem.PCI;
            }
        }
        public string AfterCellName
        {
            get
            {
                return DestCellItem.CellName;
            }
        }
        public string AfterCellDistance
        {
            get
            {
                return DestCellItem.Distance;
            }
        }
        public string AfterRsrpAvg
        {
            get
            {
                if (AfterTpItem.RsrpCount > 0)
                {
                    return Math.Round(AfterTpItem.Rsrp / AfterTpItem.RsrpCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterSinrAvg
        {
            get
            {
                if (AfterTpItem.SinrCount > 0)
                {
                    return Math.Round(AfterTpItem.Sinr / AfterTpItem.SinrCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterAppSpeedAvg
        {
            get
            {
                if (AfterTpItem.AppSpeedCount > 0)
                {
                    return Math.Round(AfterTpItem.AppSpeed / AfterTpItem.AppSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterPdcpSpeedAvg
        {
            get
            {
                if (AfterTpItem.PdcpSpeedCount > 0)
                {
                    return Math.Round(AfterTpItem.PdcpSpeed / AfterTpItem.PdcpSpeedCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRsrqAvg
        {
            get
            {
                if (AfterTpItem.RsrqCount > 0)
                {
                    return Math.Round(AfterTpItem.Rsrq / AfterTpItem.RsrqCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRssiAvg
        {
            get
            {
                if (AfterTpItem.RssiCount > 0)
                {
                    return Math.Round(AfterTpItem.Rssi / AfterTpItem.RssiCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string GridName
        {
            get
            {
                return GISManager.GetInstance().GetGridDesc(HOEvt.Longitude, HOEvt.Latitude);
            }
        }
        #endregion
    }

    public class ZTLTEReSelectCellItem
    {
        public LTECell LteCell { get; set; }
        public string CellName { get; set; }
        public string TAC { get; set; }
        public string ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public string CellID { get; set; }
        public string Distance { get; set; }
    }

    public class ZTLTEReSelectTpItem
    {
        public float Rsrp { get; set; }
        public float RsrpCount { get; set; }
        public float Sinr { get; set; }
        public float SinrCount { get; set; }
        public float AppSpeed { get; set; }
        public float AppSpeedCount { get; set; }
        public float PdcpSpeed { get; set; }
        public float PdcpSpeedCount { get; set; }
        public float Rsrq { get; set; }
        public float RsrqCount { get; set; }
        public float Rssi { get; set; }
        public float RssiCount { get; set; }
    }

    public class ZTLTEReSelectCondition
    {
        public int BeforeSecond { get; set; }           //切换前时长
        public int AfterSecond { get; set; }            //切换后时长
        public int SiteDistance { get; set; }            //同站距离

        public ZTLTEReSelectCondition()
        {
            BeforeSecond = 5;
            AfterSecond = 5;
            SiteDistance = 50;
        }
    }
}
