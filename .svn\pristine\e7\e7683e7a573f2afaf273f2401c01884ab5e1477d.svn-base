﻿namespace MasterCom.RAMS.Func
{
    partial class FindTDInterferenceForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelBTS;
            this.buttonFind = new System.Windows.Forms.Button();
            this.numericUpDownFreq = new System.Windows.Forms.NumericUpDown();
            this.buttonClose = new System.Windows.Forms.Button();
            labelBTS = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFreq)).BeginInit();
            this.SuspendLayout();
            // 
            // labelBTS
            // 
            labelBTS.AutoSize = true;
            labelBTS.Location = new System.Drawing.Point(32, 15);
            labelBTS.Name = "labelBTS";
            labelBTS.Size = new System.Drawing.Size(35, 12);
            labelBTS.TabIndex = 4;
            labelBTS.Text = "FREQ:";
            // 
            // buttonFind
            // 
            this.buttonFind.Location = new System.Drawing.Point(19, 48);
            this.buttonFind.Name = "buttonFind";
            this.buttonFind.Size = new System.Drawing.Size(75, 23);
            this.buttonFind.TabIndex = 6;
            this.buttonFind.Text = "&Find";
            this.buttonFind.UseVisualStyleBackColor = true;
            this.buttonFind.Click += new System.EventHandler(this.buttonFind_Click);
            // 
            // numericUpDownFreq
            // 
            this.numericUpDownFreq.Location = new System.Drawing.Point(71, 11);
            this.numericUpDownFreq.Maximum = new decimal(new int[] {
            99999,
            0,
            0,
            0});
            this.numericUpDownFreq.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownFreq.Name = "numericUpDownFreq";
            this.numericUpDownFreq.Size = new System.Drawing.Size(104, 21);
            this.numericUpDownFreq.TabIndex = 5;
            this.numericUpDownFreq.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numericUpDownFreq.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // buttonClose
            // 
            this.buttonClose.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonClose.Location = new System.Drawing.Point(100, 48);
            this.buttonClose.Name = "buttonClose";
            this.buttonClose.Size = new System.Drawing.Size(75, 23);
            this.buttonClose.TabIndex = 7;
            this.buttonClose.Text = "Close";
            this.buttonClose.UseVisualStyleBackColor = true;
            this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
            // 
            // FindTDInterferenceForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(192, 83);
            this.Controls.Add(this.buttonFind);
            this.Controls.Add(this.numericUpDownFreq);
            this.Controls.Add(this.buttonClose);
            this.Controls.Add(labelBTS);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FindTDInterferenceForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TDInterferenceForm";
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFreq)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button buttonFind;
        private System.Windows.Forms.NumericUpDown numericUpDownFreq;
        private System.Windows.Forms.Button buttonClose;
    }
}