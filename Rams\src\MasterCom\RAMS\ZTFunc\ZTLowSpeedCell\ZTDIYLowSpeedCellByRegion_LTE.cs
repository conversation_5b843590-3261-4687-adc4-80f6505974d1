﻿
using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYLowSpeedCellByRegion_LTE : ZTDIYLowSpeedCellByRegion
    {
        public new static ZTDIYLowSpeedCellByRegion_LTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYLowSpeedCellByRegion_LTE();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYLowSpeedCellByRegion_LTE()
            : base()
        {
            isAddSampleToDTDataManager = false;
            init();
        }

        public override string Name
        {
            get { return "低速率小区_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22019, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_type";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_Speed";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PDCP_DL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PDCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRQ";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSSI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_ThroughputDL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Transmission_Mode";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Rank_Indicator";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PDSCH_PRb_Num_s";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PDSCH_RB_Number";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PDSCH_BLER";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PDCCH_DL_Grant_Count";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Ratio_DL_Code0_HARQ_ACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Ratio_DL_Code0_HARQ_NACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Ratio_DL_Code1_HARQ_ACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_Ratio_DL_Code1_HARQ_NACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LowSpeed");
            tmpDic.Add("themeName", (object)"lte_APP_Speed_kb");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            LowSpeedCellDlg_LTE settingForm = new LowSpeedCellDlg_LTE();
            settingForm.SetCondition(settingCond);
            if(settingForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            settingCond = settingForm.GetCondition();
            return true;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellItemDict=new Dictionary<ICell , LowSpeedCell_LTE>();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return tp is LTETestPointDetail && Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
        }

        protected override void doWithDTData(TestPoint tp)
        {
            double? rsrp = GetRSRP(tp);
            double? sinr = GetSINR(tp);
             if (rsrp == null || sinr == null)
            {
                return;
            }

            ICell cell = tp.GetMainCell();
            if (cell == null)
            {
                return;
            }
            if (!cellItemDict.ContainsKey(cell))
            {
                cellItemDict.Add(cell, new LowSpeedCell_LTE(cell));
            }
            if (settingCond.IsSavePoint)
            {
                cellItemDict[cell].TestPoints.Add(tp);
            }


            double? speed = null;
            if((sinr < settingCond.MinSinr || sinr > settingCond.MaxSinr
               || rsrp < settingCond.MinRsrp || rsrp > settingCond.MaxRsrp
               || !settingCond.IsValidSpeed(tp , out speed))
               && speed != null)
            {
                cellItemDict[cell].AddPoint((double)rsrp , (double)sinr , (double)speed , false);
                cellItemDict[cell].AddPoint(tp, false);
            }
            else if(speed != null)
            {
                cellItemDict[cell].AddPoint((double)rsrp , (double)sinr , (double)speed , true);
                cellItemDict[cell].AddPoint(tp, true);
            }
        }

        protected float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }

        protected override void getResultAfterQuery()
        {
            Dictionary<ICell, LowSpeedCell_LTE> tmpDic = new Dictionary<ICell,LowSpeedCell_LTE>();
            foreach (KeyValuePair<ICell, LowSpeedCell_LTE> kvp in cellItemDict)
            {
                LowSpeedCell_LTE cell = kvp.Value;
                cell.CalcResult();
                cell.CalcResult2();
                if (cell.ProblemCount < settingCond.MinProblemCount || cell.ProblemRate < settingCond.MinProblemRate)
                {
                    continue;
                }
                tmpDic.Add(kvp.Key, cell);
            }
            cellItemDict = tmpDic;
        }

        protected override void FireShowFormAfterQuery()
        {
            MainModel.FireSetDefaultMapSerialTheme(settingCond.IsAppSpeed
                ? "LTE:APP_Speed_Mb"
                : "LTE:PDCP_DL_Mb",
                settingCond.IsAppSpeed
                ? "LTE_FDD:APP_Speed_Mb"
                : "LTE_FDD:PDCP_DL_Mb");
            LowSpeedCellForm_LTE cellForm =
                MainModel.GetInstance().GetObjectFromBlackboard(typeof(LowSpeedCellForm_LTE)) as LowSpeedCellForm_LTE;
            if(cellForm == null || cellForm.IsDisposed)
            {
                cellForm = new LowSpeedCellForm_LTE(MainModel);
            }
            cellForm.FillData(new List<LowSpeedCell_LTE>(cellItemDict.Values));
            if(!cellForm.Visible)
            {
                cellForm.Show(MainModel.MainForm);
            }
            cellItemDict = null;
        }

        protected void init()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            carrierID = CarrierType.ChinaMobile;
        }

        private LowSpeedCellCond_LTE settingCond;
        private Dictionary<ICell, LowSpeedCell_LTE> cellItemDict = new Dictionary<ICell, LowSpeedCell_LTE>();
        private static ZTDIYLowSpeedCellByRegion_LTE intance = null;
    }

    public class ZTDIYLowSpeedCellByRegion_LTE_FDD : ZTDIYLowSpeedCellByRegion_LTE
    {
        private static ZTDIYLowSpeedCellByRegion_LTE_FDD instance = null;
        public static new ZTDIYLowSpeedCellByRegion_LTE_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYLowSpeedCellByRegion_LTE_FDD();
                    }
                }
            }
            return instance;
        }
        public ZTDIYLowSpeedCellByRegion_LTE_FDD()
            : base()
        {
            isAddSampleToDTDataManager = false;
            init();
        }
        public override string Name
        {
            get { return "低速率小区_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26056, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_type";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_Speed";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PDCP_DL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PDCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRQ";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSSI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_APP_ThroughputDL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_Transmission_Mode";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_Rank_Indicator";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PDSCH_PRb_Num_s";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PDSCH_RB_Number";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PDSCH_BLER";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PDCCH_DL_Grant_Count";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_Ratio_DL_Code0_HARQ_ACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_Ratio_DL_Code0_HARQ_NACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_Ratio_DL_Code1_HARQ_ACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_Ratio_DL_Code1_HARQ_NACK";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LowSpeed");
            tmpDic.Add("themeName", (object)"lte_fdd_APP_Speed_kb");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected new void init()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            carrierID = CarrierType.ChinaUnicom;
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            return tp is LTEFddTestPoint && Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
        }
    }

    public class LowSpeedCell_LTE
    {
        public LowSpeedCell_LTE(ICell cell)
        {
            this.Cell = cell;
            this.TestPoints = new List<TestPoint>();
            this.SetCellInfo();
        }

        #region base cell info
        public ICell Cell
        {
            get;
            protected set;
        }

        public string CellName
        {
            get;
            protected set;
        }

        public int Tac
        {
            get;
            protected set;
        }

        public int Eci
        {
            get;
            protected set;
        }

        public int Earfcn
        {
            get;
            protected set;
        }

        public int Pci
        {
            get;
            protected set;
        }

        public int CellID
        {
            get;
            private set;
        }

        public double Longitude
        {
            get;
            protected set;
        }

        public double Latitude
        {
            get;
            protected set;
        }
        #endregion

        #region testpoint info
        public int ProblemCount
        {
            get;
            protected set;
        }

        public int SampleCount
        {
            get;
            protected set;
        }

        public double ProblemRate
        {
            get;
            protected set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            protected set;
        }
        #endregion

        #region 指标项
        public double ProbAvgSpeed
        {
            get;
            protected set;
        }

        public double AllAvgSpeed
        {
            get;
            protected set;
        }

        public double ProbAvgRsrp
        {
            get;
            protected set;
        }

        public double AllAvgRsrp
        {
            get;
            protected set;
        }

        public double ProbAvgSinr
        {
            get;
            protected set;
        }

        public double AllAvgSinr
        {
            get;
            protected set;
        }

        private double probSumSpeed;
        private double allSumSpeed;
        private double probSumRsrp;
        private double allSumRsrp;
        private double probSumSinr;
        private double allSumSinr;

        #endregion

        public virtual void AddPoint(double rsrp, double sinr, double speed, bool isProblemPoint)
        {
            ++SampleCount;
            ProblemCount += isProblemPoint ? 1 : 0;

            allSumRsrp += rsrp;
            allSumSinr += sinr;
            allSumSpeed += speed;

            if (isProblemPoint)
            {
                probSumRsrp += rsrp;
                probSumSinr += sinr;
                probSumSpeed += speed;
            }
        }

        public virtual void CalcResult()
        {
            ProblemRate = 1d * ProblemCount / SampleCount;

            AllAvgRsrp = allSumRsrp / SampleCount;
            AllAvgSinr = allSumSinr / SampleCount;
            AllAvgSpeed = allSumSpeed / SampleCount;

            if (ProblemCount != 0)
            {
                ProbAvgRsrp = probSumRsrp / ProblemCount;
                ProbAvgSinr = probSumSinr / ProblemCount;
                ProbAvgSpeed = probSumSpeed / ProblemCount;
            }
        }

        protected void SetCellInfo()
        {
            if (Cell is LTECell)
            {
                LTECell cell = this.Cell as LTECell;
                CellName = cell.Name;
                Tac = cell.TAC;
                Eci = cell.ECI;
                Earfcn = cell.EARFCN;
                Pci = cell.PCI;
                CellID = cell.SCellID;
                Longitude = cell.Longitude;
                Latitude = cell.Latitude;
            }
            else
            {
                CellName = this.Cell.Name;
            }
           
        }

        public virtual void AddPoint(TestPoint tp, bool isProblemPoint)
        {
            short? transMode = null;
            short? rank_indicator = null;
            int? pdsch_rb_num = null;
            float? pdsch_bler = null;
            short? pdcch_dl_grant_count = null;
            float? ratio_dl_code0_harq_ack = null;
            float? ratio_dl_code0_harq_nack = null;
            float? ratio_dl_code1_harq_ack = null;
            float? ratio_dl_code1_harq_nack = null;
            if (tp is LTEFddTestPoint)
            {
                transMode = (short?)tp["lte_fdd_Transmission_Mode"];
                rank_indicator = (short?)tp["lte_fdd_Rank_Indicator"];
                pdsch_rb_num = (int?)tp["lte_fdd_PDSCH_RB_Number"];
                pdsch_bler = (float?)tp["lte_fdd_PDSCH_BLER"];
                pdcch_dl_grant_count = (short?)tp["lte_fdd_PDCCH_DL_Grant_Count"];
                ratio_dl_code0_harq_ack = (float?)tp["lte_fdd_Ratio_DL_Code0_HARQ_ACK"];
                ratio_dl_code0_harq_nack = (float?)tp["lte_fdd_Ratio_DL_Code0_HARQ_NACK"];
                ratio_dl_code1_harq_ack = (float?)tp["lte_fdd_Ratio_DL_Code1_HARQ_ACK"];
                ratio_dl_code1_harq_nack = (float?)tp["lte_fdd_Ratio_DL_Code1_HARQ_NACK"];
            }
            else
            {
                transMode = (short?)tp["lte_Transmission_Mode"];
                rank_indicator = (short?)tp["lte_Rank_Indicator"];
                pdsch_rb_num = (int?)tp["lte_PDSCH_RB_Number"];
                pdsch_bler = (float?)tp["lte_PDSCH_BLER"];
                pdcch_dl_grant_count = (short?)tp["lte_PDCCH_DL_Grant_Count"];
                ratio_dl_code0_harq_ack = (float?)tp["lte_Ratio_DL_Code0_HARQ_ACK"];
                ratio_dl_code0_harq_nack = (float?)tp["lte_Ratio_DL_Code0_HARQ_NACK"];
                ratio_dl_code1_harq_ack = (float?)tp["lte_Ratio_DL_Code1_HARQ_ACK"];
                ratio_dl_code1_harq_nack = (float?)tp["lte_Ratio_DL_Code1_HARQ_NACK"];
            }
            this.allTransmission_mode += getValidData(transMode);

            
            this.allRank_indicator += getValidData(rank_indicator);

            
            this.allPdsch_rb_number += getValidData(pdsch_rb_num);

            
            this.allPdsch_bler += getValidData(pdsch_bler);

            
            this.allPdcch_dl_grant_count += getValidData(pdcch_dl_grant_count);

            
            this.allRatio_dl_code0_harq_ack += getValidData(ratio_dl_code0_harq_ack);

            
            this.allRatio_dl_code0_harq_nack += getValidData(ratio_dl_code0_harq_nack);

            
            this.allRatio_dl_code1_harq_ack += getValidData(ratio_dl_code1_harq_ack);

            
            this.allRatio_dl_code1_harq_nack += getValidData(ratio_dl_code1_harq_nack);

            if (isProblemPoint)
            {
                this.proTransmission_mode += getValidData(transMode);

                this.proRank_indicator += getValidData(rank_indicator);

                this.proPdsch_rb_number += getValidData(pdsch_rb_num);

                this.proPdsch_bler += getValidData(pdsch_bler);

                this.proPdcch_dl_grant_count += getValidData(pdcch_dl_grant_count);

                this.proRatio_dl_code0_harq_ack += getValidData(ratio_dl_code0_harq_ack);

                this.proRatio_dl_code0_harq_nack += getValidData(ratio_dl_code0_harq_nack);

                this.proRatio_dl_code1_harq_ack += getValidData(ratio_dl_code1_harq_ack);

                this.proRatio_dl_code1_harq_nack += getValidData(ratio_dl_code1_harq_nack);
            }
        }

        private short getValidData(short? data)
        {
            if (data == null)
            {
                return 0;
            }
            return (short)data;
        }

        private float getValidData(float? data)
        {
            if (data == null)
            {
                return 0;
            }
            return (float)data;
        }

        public void CalcResult2()
        {
            allTransmission_mode = allTransmission_mode / SampleCount;
            allRank_indicator = allRank_indicator / SampleCount;
            allPdsch_rb_number = allPdsch_rb_number / SampleCount;
            allPdsch_bler = allPdsch_bler / SampleCount;
            allPdcch_dl_grant_count = allPdcch_dl_grant_count / SampleCount;
            allRatio_dl_code0_harq_ack = allRatio_dl_code0_harq_ack / SampleCount;
            allRatio_dl_code0_harq_nack = allRatio_dl_code0_harq_nack / SampleCount;
            allRatio_dl_code1_harq_ack = allRatio_dl_code1_harq_ack / SampleCount;
            allRatio_dl_code1_harq_nack = allRatio_dl_code1_harq_nack / SampleCount;

            if (ProblemCount != 0)
            {
                proTransmission_mode = proTransmission_mode / ProblemCount;
                proRank_indicator = proRank_indicator / ProblemCount;
                proPdsch_rb_number = proPdsch_rb_number / ProblemCount;
                proPdsch_bler = proPdsch_bler / ProblemCount;
                proPdcch_dl_grant_count = proPdcch_dl_grant_count / ProblemCount;
                proRatio_dl_code0_harq_ack = proRatio_dl_code0_harq_ack / ProblemCount;
                proRatio_dl_code0_harq_nack = proRatio_dl_code0_harq_nack / ProblemCount;
                proRatio_dl_code1_harq_ack = proRatio_dl_code1_harq_ack / ProblemCount;
                proRatio_dl_code1_harq_nack = proRatio_dl_code1_harq_nack / ProblemCount;
            }
        }

        private double allTransmission_mode = 0;

        public double AllTransmission_Mode
        {
            get
            {
                return allTransmission_mode;
            }
        }

        private double allRank_indicator = 0;
        public double AllRank_Indicator
        {
            get { return allRank_indicator; }
        }

        private double allPdsch_rb_number = 0;
        public double AllPDSCH_RB_Number
        {
            get { return allPdsch_rb_number; }
        }

        private double allPdsch_bler = 0;
        public double AllPDSCH_BLER
        {
            get { return allPdsch_bler; }
        }

        private double allPdcch_dl_grant_count = 0;
        public double AllPDCCH_DL_Grant_Count
        {
            get { return allPdcch_dl_grant_count; }
        }

        private double allRatio_dl_code0_harq_ack = 0;
        public double AllRatio_DL_Code0_HARQ_ACK
        {
            get
            {
                return allRatio_dl_code0_harq_ack;
            }
        }

        private double allRatio_dl_code0_harq_nack = 0;
        public double AllRatio_DL_Code0_HARQ_NACK
        {
            get
            {
                return allRatio_dl_code0_harq_nack;
            }
        }

        private double allRatio_dl_code1_harq_ack = 0;
        public double AllRatio_DL_Code1_HARQ_ACK
        {
            get
            {
                return allRatio_dl_code1_harq_ack;
            }
        }

        private double allRatio_dl_code1_harq_nack = 0;
        public double AllRatio_DL_Code1_HARQ_NACK
        {
            get
            {
                return allRatio_dl_code1_harq_nack;
            }
        }

        private double proTransmission_mode = 0;

        public double ProTransmission_Mode
        {
            get
            {
                return proTransmission_mode;
            }
        }

        private double proRank_indicator = 0;
        public double ProRank_Indicator
        {
            get { return proRank_indicator; }
        }

        private double proPdsch_rb_number = 0;
        public double ProPDSCH_RB_Number
        {
            get { return proPdsch_rb_number; }
        }

        private double proPdsch_bler = 0;
        public double ProPDSCH_BLER
        {
            get { return proPdsch_bler; }
        }

        private double proPdcch_dl_grant_count = 0;
        public double ProPDCCH_DL_Grant_Count
        {
            get { return proPdcch_dl_grant_count; }
        }

        private double proRatio_dl_code0_harq_ack = 0;
        public double ProRatio_DL_Code0_HARQ_ACK
        {
            get
            {
                return proRatio_dl_code0_harq_ack;
            }
        }

        private double proRatio_dl_code0_harq_nack = 0;
        public double ProRatio_DL_Code0_HARQ_NACK
        {
            get
            {
                return proRatio_dl_code0_harq_nack;
            }
        }

        private double proRatio_dl_code1_harq_ack = 0;
        public double ProRatio_DL_Code1_HARQ_ACK
        {
            get
            {
                return proRatio_dl_code1_harq_ack;
            }
        }

        private double proRatio_dl_code1_harq_nack = 0;
        public double ProRatio_DL_Code1_HARQ_NACK
        {
            get
            {
                return proRatio_dl_code1_harq_nack;
            }
        }
    }
}
