﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.KPI_Statistics
{
    public static class LoadFilterCellFile
    {
        //通过文件名获取文件内容
        public static Dictionary<string, string> GetInfosByFileName(string[] filesPathxlsx)
        {
            Dictionary<string, string> dicExcel = new Dictionary<string, string>();
            try
            {
                if (filesPathxlsx != null && filesPathxlsx.Length > 0)
                {
                    foreach (var ExcelfileName in filesPathxlsx)
                    {
                        ReadFileByFileName(ExcelfileName, dicExcel);
                    }
                }
                return dicExcel;
            }
            catch { return dicExcel; }
        }

        //获取excel中的 "TAC", "ECI", "CellName"存入字典
        public static void ReadFileByFileName(string fileName, Dictionary<string, string> dicExcel)
        {
            try
            {
                ExcelNPOIReader reader = new ExcelNPOIReader(fileName);
                ExcelNPOITable tb = reader.GetTable(new List<string>() { "TAC", "ECI", "CellName" });
                if (tb == null)
                { return; }
                if (tb.CellValues == null)
                {
                    return;
                }

                foreach (object[] r in tb.CellValues)
                {
                    string cellkey = r[0].ToString() + "_" + r[1].ToString();
                    string cellname = r[2].ToString();
                    if (!dicExcel.ContainsKey(cellkey))
                    {
                        dicExcel.Add(cellkey, cellname);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

    }
}
