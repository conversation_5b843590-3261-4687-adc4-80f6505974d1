﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LowSpeedCell_NR
    {
        public LowSpeedCell_NR(ICell cell)
        {
            this.Cell = cell;
            this.SetCellInfo();
        }

        public Dictionary<SpeedType, LowSpeedInfo> SpeedInfoDic { get; set; } = new Dictionary<SpeedType, LowSpeedInfo>();
        public List<LowSpeedInfo> SpeedInfoList { get; set; }

        #region base cell info
        public ICell Cell { get; protected set; }
        public string CellName { get; protected set; }
        public int Tac { get; protected set; }
        public long Nci { get; protected set; }
        public int Arfcn { get; protected set; }
        public int Pci { get; protected set; }
        public int CellID { get; protected set; }
        public double Longitude { get; protected set; }
        public double Latitude { get; protected set; }
        public string AreaName { get; set; }
        #endregion

        #region testpoint info
        public int ProblemCount { get; protected set; }
        public int SampleCount { get; protected set; }
        public double ProblemRate { get; protected set; }
        public List<TestPoint> TestPoints { get; } = new List<TestPoint>();
        #endregion

        #region 指标项
        public LowSpeedIndex ProbRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex ProbSinr { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllSinr { get; set; } = new LowSpeedIndex();

        #region lte
        public LowSpeedIndex ProbLteRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex ProbLteSinr { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllLteRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllLteSinr { get; set; } = new LowSpeedIndex();
        #endregion

        public double? Rate_16QAM_DL { get; set; }
        public double? Rate_64QAM_DL { get; set; }
        public double? Rate_256QAM_DL { get; set; }
        public double? Rate_BPSK_DL { get; set; }
        public double? Rate_QPSK_DL { get; set; }
        #endregion

        public void AddPoint(double rsrp, double sinr, List<LowSpeedCellCond_NR.SpeedTypeInfo> speedInfo, TestPoint tp)
        {
            foreach (var info in speedInfo)
            {
                LowSpeedInfo res;
                if (!SpeedInfoDic.TryGetValue(info.Type, out res))
                {
                    res = new LowSpeedInfo(info.Type);
                    SpeedInfoDic.Add(info.Type, res);
                }

                res.AddPoint(rsrp, sinr, info, tp);
            }
        }

        public void Calculate()
        {
            foreach (var info in SpeedInfoDic)
            {
                addPoint(info.Value);
                info.Value.Caculate();
            }
            SpeedInfoList = new List<LowSpeedInfo>(SpeedInfoDic.Values);
            ProblemRate = Math.Round(1d * ProblemCount / SampleCount, 2);

            ProbRsrp.Caculate();
            ProbSinr.Caculate();
            AllRsrp.Caculate();
            AllSinr.Caculate();

            ProbLteRsrp.Caculate();
            ProbLteSinr.Caculate();
            AllLteRsrp.Caculate();
            AllLteSinr.Caculate();
            computeModulation(TestPoints);
        }

        protected void addPoint(LowSpeedInfo speedInfo)
        {
            SampleCount += speedInfo.SampleCount;
            ProblemCount += speedInfo.ProblemCount;
            ProbRsrp.Add(speedInfo.AllRsrp);
            ProbSinr.Add(speedInfo.AllSinr);
            AllRsrp.Add(speedInfo.ProbRsrp);
            AllSinr.Add(speedInfo.ProbSinr);

            ProbLteRsrp.Add(speedInfo.AllLteRsrp);
            ProbLteSinr.Add(speedInfo.AllLteSinr);
            AllLteRsrp.Add(speedInfo.ProbLteRsrp);
            AllLteSinr.Add(speedInfo.ProbLteSinr);
        }

        protected void SetCellInfo()
        {
            if (Cell is NRCell)
            {
                NRCell cell = this.Cell as NRCell;
                CellName = cell.Name;
                Tac = cell.TAC;
                Nci = cell.NCI;
                Arfcn = cell.SSBARFCN;
                Pci = cell.PCI;
                CellID = cell.CellID;
                Longitude = cell.Longitude;
                Latitude = cell.Latitude;
            }
            else
            {
                CellName = this.Cell.Name;
            }
        }

        private void computeModulation(List<TestPoint> testPoints)
        {
            int sum_16QAM_Count_DL = 0;
            int sum_64QAM_Count_DL = 0;
            int sum_256QAM_Count_DL = 0;
            int sum_BPSK_Count_DL = 0;
            int sum_QPSK_Count_DL = 0;
            foreach (TestPoint tp in testPoints)
            {
                addIntValueToSum(tp, "NR_16QAM_Count_DL", ref sum_16QAM_Count_DL, 0);
                addIntValueToSum(tp, "NR_64QAM_Count_DL", ref sum_64QAM_Count_DL, 0);
                addIntValueToSum(tp, "NR_256QAM_Count_DL", ref sum_256QAM_Count_DL, 0);
                addIntValueToSum(tp, "NR_BPSK_Count_DL", ref sum_BPSK_Count_DL, 0);
                addIntValueToSum(tp, "NR_QPSK_Count_DL", ref sum_QPSK_Count_DL, 0);
            }

            int sumDlCount = sum_16QAM_Count_DL + sum_64QAM_Count_DL + sum_256QAM_Count_DL + sum_BPSK_Count_DL + sum_QPSK_Count_DL;

            Rate_16QAM_DL = Math.Round(100.0 * sum_16QAM_Count_DL / sumDlCount, 2);
            Rate_64QAM_DL = Math.Round(100.0 * sum_64QAM_Count_DL / sumDlCount, 2);
            Rate_256QAM_DL = Math.Round(100.0 * sum_256QAM_Count_DL / sumDlCount, 2);
            Rate_BPSK_DL = Math.Round(100.0 * sum_BPSK_Count_DL / sumDlCount, 2);
            Rate_QPSK_DL = Math.Round(100.0 * sum_QPSK_Count_DL / sumDlCount, 2);
        }

        private void addIntValueToSum(TestPoint tp, string paramName, ref int sumValue
            , int minValue)
        {
            int? curValue = (int?)tp[paramName];
            if (curValue != null && curValue >= minValue)
            {
                sumValue += (int)curValue;
            }
        }
    }

    public class LowSpeedInfo
    {
        public string SpeedTypeName { get; set; }
        public SpeedType Type { get; set; }

        public int ProblemCount { get; set; }
        public int SampleCount { get; set; }
        public double? ProblemRate { get; set; }

        public LowSpeedIndex ProbSpeed { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex ProbRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex ProbSinr { get; set; } = new LowSpeedIndex();

        public LowSpeedIndex AllSpeed { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllSinr { get; set; } = new LowSpeedIndex();

        #region lte
        public LowSpeedIndex ProbLteRsrp { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex ProbLteSinr { get; set; } = new LowSpeedIndex();
        public LowSpeedIndex AllLteRsrp  { get; set; }= new LowSpeedIndex();
        public LowSpeedIndex AllLteSinr  { get; set; }= new LowSpeedIndex();
        #endregion

        public LowSpeedInfo(SpeedType type)
        {
            Type = type;
        }

        public void AddPoint(double rsrp, double sinr, LowSpeedCellCond_NR.SpeedTypeInfo speedInfo, TestPoint tp)
        {
            ++SampleCount;
            AllRsrp.Count++;
            AllRsrp.Sum += rsrp;
            AllSinr.Count++;
            AllSinr.Sum += sinr;
            AllSpeed.Count++;
            AllSpeed.Sum += speedInfo.Speed;

            if (speedInfo.IsValid)
            {
                ++ProblemCount;
                ProbRsrp.Count++;
                ProbRsrp.Sum += rsrp;
                ProbSinr.Count++;
                ProbSinr.Sum += sinr;
                ProbSpeed.Count++;
                ProbSpeed.Sum += speedInfo.Speed;
            }

            dealLteInfo(speedInfo, tp);
        }

        private void dealLteInfo(LowSpeedCellCond_NR.SpeedTypeInfo speedInfo, TestPoint tp)
        {
            float? lteRsrp = getValidData(tp, "NR_lte_RSRP", -200, 100);
            if (lteRsrp != null)
            {
                AllLteRsrp.Count++;
                AllLteRsrp.Sum += (double)lteRsrp;

                if (speedInfo.IsValid)
                {
                    ProbLteRsrp.Count++;
                    ProbLteRsrp.Sum += (double)lteRsrp;
                }
            }

            float? lteSinr = getValidData(tp, "NR_lte_SINR", -50, 50);
            if (lteSinr != null)
            {
                AllLteSinr.Count++;
                AllLteSinr.Sum += (double)lteSinr;

                if (speedInfo.IsValid)
                {
                    ProbLteSinr.Count++;
                    ProbLteSinr.Sum += (double)lteSinr;
                }
            }
        }

        protected float? getValidData(TestPoint tp, string name, float min, float max)
        {
            float? data = (float?)tp[name];
            if (data != null && data >= min && data <= max)
            {
                return data;
            }
            return null;
        }

        public void Caculate()
        {
            SpeedTypeName = EnumDescriptionAttribute.GetText(Type);
            ProblemRate = Math.Round(1d * ProblemCount / SampleCount, 2);

            ProbSpeed.Caculate();
            ProbRsrp.Caculate();
            ProbSinr.Caculate();
            AllSpeed.Caculate();
            AllRsrp.Caculate();
            AllSinr.Caculate();

            ProbLteRsrp.Caculate();
            ProbLteSinr.Caculate();
            AllLteRsrp.Caculate();
            AllLteSinr.Caculate();
        }
    }

    public class LowSpeedIndex
    { 
        public int Count { get; set; }
        public double Sum { get; set; }
        public double? Avg { get; set; }

        public void Caculate()
        {
            if (Count > 0)
            {
                Avg = Math.Round(Sum / Count, 2);
            }
            else
            {
                Avg = null;
            }
        }

        public void Add(LowSpeedIndex index)
        {
            Count += index.Count;
            Sum += index.Sum;
        }
    }
}
