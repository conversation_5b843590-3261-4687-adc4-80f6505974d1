﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PointStatusColoringForm : MinCloseForm
    {
        private PointStatusColoringReader reader;
        private List<StatusPointStyle> statusStyles;

        public PointStatusColoringForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();

            reader = new PointStatusColoringReader();
            statusStyles = reader.StatusStyles;
            InitCbxSymbol();
            cbxStatus.SelectedIndexChanged += CbxStatus_SelectedChanged;
            InitCbxStatus();

            btnClose.Click += BtnClose_Click;
            btnOpen.Click += BtnOpen_Click;
            panelColor.Click += PanelColor_Click;
            btnRefresh.Click += BtnRefresh_Click;
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            reader.Clear();
            base.MinCloseForm_FormClosing(sender, e);
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnOpen_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = MasterCom.Util.FilterHelper.Excel;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFile.Text = dlg.FileName;
            reader.ColorFile(txtFile.Text);
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            RefreshStyle();
        }

        private void PanelColor_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.AllowFullOpen = false;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            panelColor.BackColor = dlg.Color;
        }

        private void CbxStatus_SelectedChanged(object sender, EventArgs e)
        {
            StatusPointStyle style = GetCurStyle();
            if (style == null)
            {
                return;
            }

            chkVisible.Checked = style.Visible;
            cbxSymbol.SelectedIndex = style.Symbol >= 0 && style.Symbol < cbxSymbol.Items.Count ? style.Symbol : 0;
            panelColor.BackColor = style.FillColor;
            numSize.Value = style.PointSize;
        }

        private void InitCbxStatus()
        {
            cbxStatus.Items.Clear();
            foreach (StatusPointStyle style in statusStyles)
            {
                cbxStatus.Items.Add(style.Name);
            }
            cbxStatus.SelectedIndex = 0;
        }

        private void InitCbxSymbol()
        {
            cbxSymbol.Items.Clear();
            List<string> symbolDescs = new List<string>() { "圆形", "方形", "三角形" };
            foreach (string desc in symbolDescs)
            {
                cbxSymbol.Items.Add(desc);
            }
            cbxSymbol.SelectedIndex = 0;
        }

        private void RefreshStyle()
        {
            StatusPointStyle style = GetCurStyle();
            if (style == null)
            {
                return;
            }

            style.FillColor = panelColor.BackColor;
            style.Symbol = cbxSymbol.SelectedIndex;
            style.PointSize = (int)numSize.Value;
            style.Visible = chkVisible.Checked;

            reader.Refresh();
        }

        private StatusPointStyle GetCurStyle()
        {
            string status = cbxStatus.SelectedItem as string;
            StatusPointStyle style = null;
            foreach (StatusPointStyle s in statusStyles)
            {
                if (s.Name == status)
                {
                    style = s;
                }
            }
            return style;
        }
    }
}
