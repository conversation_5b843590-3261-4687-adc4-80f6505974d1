using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class WlanMacApInfoForm : ChildForm
    {
        public Dictionary<string, WlanApMacInfo> apMacDic { get; set; }
        public WlanMacApInfoForm()
        {
            apMacDic = new Dictionary<string, WlanApMacInfo>();
            InitializeComponent();
            initColumn();
        }

        public void initColumn()
        {
            this.columnHeaderSSID_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.ssid;
                }
                else if (row is WlanApMacInfo)
                {
                    WlanApMacInfo apMac = row as WlanApMacInfo;
                    return apMac.ssid;
                }
                return "";
            };
            addPart1Data();
            addPart2Data();
            this.listViewMac.CanExpandGetter = delegate (object x)
            {
                return x is WlanApMacInfo;
            };
            this.listViewMac.ChildrenGetter = delegate (object x)
            {
                WlanApMacInfo macInfo = (WlanApMacInfo)x;
                return macInfo.apInfoList;
            };
        }

        private void addPart1Data()
        {
            this.columnHeaderBSSID_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.bssid;
                }
                return "";
            };
            this.columnHeaderChannel_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.channel;
                }
                return "";
            };
            this.columnHeaderFreq_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.freqecncy;
                }
                return "";
            };
            this.columnHeaderRSSI_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.rssi;
                }
                return "";
            };
            this.columnHeaderCI_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.ci;
                }
                return "";
            };
            this.columnHeaderSFI_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.sfi;
                }
                return "";
            };
            this.columnHeaderNFI_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.nfi;
                }
                return "";
            };
            this.columnHeaderFirstSeen_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.firstSeen;
                }
                return "";
            };
            this.columnHeaderLastSeen_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.lastSeen;
                }
                return "";
            };
            this.columnHeaderActiveTimes_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.activeTime;
                }
                return "";
            };
            this.columnHeaderRx_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.rx;
                }
                return "";
            };
            this.columnHeaderTx_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.tx;
                }
                return "";
            };
            this.columnHeaderBand_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.band;
                }
                return "";
            };
            this.columnHeaderABGN_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.abgn;
                }
                return "";
            };
        }

        private void addPart2Data()
        {
            this.columnHeaderType_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.type;
                }
                return "";
            };
            this.columnHeaderEncrytion_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.encrytion;
                }
                return "";
            };
            this.columnHeaderSecurity_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.security;
                }
                return "";
            };
            this.columnHeaderBridge_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.bridge;
                }
                return "";
            };
            this.columnHeaderDCFPCF_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.dcf_pcf;
                }
                return "";
            };
            this.columnHeaderBeaconInterval_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.beaconInterval;
                }
                return "";
            };
            this.columnHeaderSupportRate_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.supportRate;
                }
                return "";
            };
            this.columnHeaderMaxRate_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.maxRate;
                }
                return "";
            };
            this.columnHeaderPreamble_Max.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.preamble;
                }
                return "";
            };
            this.columnHeaderNoise_Mac.AspectGetter = delegate (object row)
            {
                if (row is WlanApInfo)
                {
                    WlanApInfo apInfo = row as WlanApInfo;
                    return apInfo.noise;
                }
                return "";
            };
        }

        public override void Init()
        {
            base.Init();
            //MainModel.DistrictChanged += districtChanged
            //MainModel.DTDataChanged += dtDataChanged
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            //MainModel.SelectedEventsChanged += selectedEventsChanged
            //MainModel.SelectedMessageChanged += selectedMessageChanged
            Disposed += disposed;
            initializeComponent();
            //dtDataChanged(null, null)
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                return param;
            }
            set
            {
                Console.Write(value);
            }
        }

        private void initializeComponent()
        {
            //columnCountChanged()
            //rowCountChanged()
            //cellDefineChanged()
        }

        private void disposed(object sender, EventArgs e)
        {
            //MainModel.DistrictChanged -= districtChanged
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            //MainModel.SelectedEventsChanged -= selectedEventsChanged
            //MainModel.SelectedMessageChanged -= selectedMessageChanged
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            apMacDic.Clear();
            if (MainModel.SelectedTestPoints.Count > 0
                && MainModel.SelectedTestPoints[0] is WLANTestPoint)
            {
                addApMacDic();
            }
            List<WlanApMacInfo> macInfoList = new List<WlanApMacInfo>(apMacDic.Values);
            FillForm(macInfoList);
            listViewMac.ExpandAll();
        }

        private void addApMacDic()
        {
            WLANTestPoint testPoint = MainModel.SelectedTestPoints[0] as WLANTestPoint;
            for (int i = 0; i < 50; i++)
            {
                int? channel = (int?)testPoint["WLAN_ARR_AP_Channel", i];
                int? rssi = (int?)testPoint["WLAN_ARR_AP_RSSI", i];
                object bssidObj = testPoint["WLAN_ARR_AP_BSSID_String", i];
                object ssidObj = testPoint["WLAN_ARR_AP_SSID", i];
                string ssid = ssidObj == null ? "" : ssidObj.ToString();
                if (channel == null || rssi == null || bssidObj == null || ssidObj == null)
                {
                    //continue
                }
                else
                {
                    WlanApInfo apInfo = new WlanApInfo();
                    apInfo.FillInfo(testPoint, i);
                    if (!apMacDic.ContainsKey(ssid))
                    {
                        apMacDic[ssid] = new WlanApMacInfo();
                        apMacDic[ssid].ssid = ssid;
                    }
                    apMacDic[ssid].apInfoList.Add(apInfo);
                }
            }
        }

        public void FillForm(List<WlanApMacInfo> macInfoList)
        {
            listViewMac.Items.Clear();
            listViewMac.SetObjects(macInfoList);
        }

        private void listViewMac_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewMac.SelectedObject is WlanApInfo)
            {
                WlanApInfo apInfo = listViewMac.SelectedObject as WlanApInfo;
                MainModel.GetInstance().FireWlanApInfoDetailShow(apInfo);
            }
        }
    }
}