﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePlanningCellResultForm : MinCloseForm
    {
        public LtePlanningCellResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            miExportCurXls.Click += MiExportCurXls_Click;
            miExportAllXls.Click += MiExportAllXls_Click;
        }

        public void FillData(List<object> results)
        {
            gcSummary.DataSource = null;
            gcNotOpened.DataSource = null;
            gcSampleLess.DataSource = null;
            gcWeakCoverage.DataSource = null;
            gcOverCoverage.DataSource = null;
            gcWeakQuality.DataSource = null;
            foreach (object o in results)
            {
                if (o is List<LtePlanningBtsView>)
                {
                    gcNotOpened.DataSource = o;
                }
                else if (o is List<SampleLessCellView>)
                {
                    gcSampleLess.DataSource = o;
                }
                else if (o is List<LtePlanningSummaryView>)
                {
                    gcSummary.DataSource = o;
                }
                else if (o is List<WeakCoverageCellView>)
                {
                    gcWeakCoverage.DataSource = o;
                }
                else if (o is List<OverCoverageCellView>)
                {
                    gcOverCoverage.DataSource = o;
                }
                else if (o is List<WeakQualityCellView>)
                {
                    gcWeakQuality.DataSource = o;
                }
            }
        }

        public void MiExportCurXls_Click(object sender, EventArgs e)
        {
            TabPage tp = tabControl.SelectedTab;
            GridControl gc = tp.Controls[0] as GridControl;
            GridView gv = gc.MainView as GridView;

            List<GridView> gvs = new List<GridView>();
            gvs.Add(gv);
            List<string> sheetNames = new List<string>();
            sheetNames.Add(tp.Text);
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }

        public void MiExportAllXls_Click(object sender, EventArgs e)
        {
            List<GridView> gvs = new List<GridView>();
            List<string> sheetNames = new List<string>();
            foreach (TabPage tp in tabControl.TabPages)
            {
                GridControl gc = tp.Controls[0] as GridControl;
                GridView gv = gc.MainView as GridView;

                gvs.Add(gv);
                sheetNames.Add(tp.Text);
            }
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }
    }
}
