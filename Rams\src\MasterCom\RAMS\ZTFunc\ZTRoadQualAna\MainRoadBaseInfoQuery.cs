﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MainRoadBaseInfoQuery : DIYSQLBase
    {
        private readonly string dbName;
        public Dictionary<int, RoadLabelBaseInfo> RoadBaseInfoDic { get; set; } 

        public MainRoadBaseInfoQuery(string dbName)
            : base(MainModel.GetInstance())
        {
            this.dbName = dbName;
            RoadBaseInfoDic = new Dictionary<int, RoadLabelBaseInfo>();
        }
        public override string Name
        {
            get { return "MainRoadBaseInfoQuery"; }
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select a.roadLabelId,a.[roadLabel],[wayName],[county],[length_M],b.mainLineName
from [{0}].dbo.[tb_cfg_roadAna_mainRoad] a left join [{0}].dbo.[tb_cfg_roadana_mainline_road] b 
on a.roadLabel = b.roadLabel", dbName);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[6];
            vType[0] = E_VType.E_Int;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_String;
            vType[3] = E_VType.E_String;
            vType[4] = E_VType.E_Float;
            vType[5] = E_VType.E_String;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            RoadLabelBaseInfo road = new RoadLabelBaseInfo();
            road.RoadLabelId = package.Content.GetParamInt();
            road.RoadLabel = package.Content.GetParamString();
            road.RoadName = package.Content.GetParamString();
            road.CountyName = package.Content.GetParamString();
            road.Length_M = package.Content.GetParamFloat();
            string mainLine = package.Content.GetParamString();

            RoadLabelBaseInfo roadExist;
            if (!RoadBaseInfoDic.TryGetValue(road.RoadLabelId, out roadExist))
            {
                roadExist = road;
                RoadBaseInfoDic[road.RoadLabelId] = roadExist;
            }
            if (!string.IsNullOrEmpty(mainLine))
            {
                roadExist.MainLineNameList.Add(mainLine);
            }
        }
    }
    public class RoadPartAreaInfoQuery : DIYSQLBase
    {
        private readonly string dbName;
        private readonly Dictionary<int, RoadLabelBaseInfo> roadBaseInfoDic;
        public Dictionary<int, RoadPartAreaBaseInfo> RoadPartAreaBaseInfoDic { get; set; }

        /// <summary>
        /// 经纬度倍率(10000000.0)
        /// </summary>
        public const double DRatio = 10000000;

        public RoadPartAreaInfoQuery(string dbName, Dictionary<int, RoadLabelBaseInfo> roadBaseInfoDic)
            : base(MainModel.GetInstance())
        {
            this.dbName = dbName;
            this.roadBaseInfoDic = roadBaseInfoDic;
            this.RoadPartAreaBaseInfoDic = new Dictionary<int, RoadPartAreaBaseInfo>();
        }
        public override string Name
        {
            get { return "RoadPartAreaInfoQuery"; }
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select b.wayName,b.roadLabel,RoadId,AreaID,BrLongitude,BrLatitude,TlLongitude,TlLatitude
,CenterLongitude,CenterLatitude,Length,PointShape 
from {0}.dbo.tb_cfg_arealist_point_road a left join {0}.dbo.tb_cfg_roadana_mainroad b on a.RoadID = b.roadLabelId
order by AreaID", dbName);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[12];
            vType[0] = E_VType.E_String;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_Int;
            vType[3] = E_VType.E_Int;
            vType[4] = E_VType.E_Int;
            vType[5] = E_VType.E_Int;
            vType[6] = E_VType.E_Int;
            vType[7] = E_VType.E_Int;
            vType[8] = E_VType.E_Int;
            vType[9] = E_VType.E_Int;
            vType[10] = E_VType.E_Float;
            vType[11] = E_VType.E_VARYBIN;
            return vType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            RoadPartAreaBaseInfo roadPart = new RoadPartAreaBaseInfo();
            roadPart.RoadName = package.Content.GetParamString();
            roadPart.RoadLabel = package.Content.GetParamString();
            roadPart.RoadLabelId = package.Content.GetParamInt();
            roadPart.RoadPartAreaId = package.Content.GetParamInt();
            roadPart.BrLongitude = package.Content.GetParamDouble();
            roadPart.BrLatitude = package.Content.GetParamDouble();
            roadPart.TlLongitude = package.Content.GetParamDouble();
            roadPart.TlLatitude = package.Content.GetParamDouble();
            roadPart.CenterLongitude = package.Content.GetParamDouble();
            roadPart.CenterLatitude = package.Content.GetParamDouble();
            roadPart.Length_M = package.Content.GetParamFloat();
            byte[] pointBytes = package.Content.GetParamBytes();
            roadPart.DbPointList = bytesToPoints(pointBytes);

            RoadPartAreaBaseInfoDic[roadPart.RoadPartAreaId] = roadPart;

            RoadLabelBaseInfo item;
            if (roadBaseInfoDic.TryGetValue(roadPart.RoadLabelId, out item))
            {
                item.RoadPartAreaInfoList.Add(roadPart);
            }
        }

        protected List<DbPoint> bytesToPoints(byte[] buffer)
        {
            int length = buffer.Length / 8;
            List<DbPoint> pts = new List<DbPoint>();

            int offset = 0;
            for (int i = 0; i < length; i++)
            {
                int x = BitConverter.ToInt32(buffer, offset);
                offset += 4;
                int y = BitConverter.ToInt32(buffer, offset);
                offset += 4;

                pts.Add(new DbPoint(x / DRatio, y / DRatio));
            }
            return pts;
        }
    }
    public class RoadEvtAnaInfoQuery : DIYSQLBase
    {
        private readonly string dbName;
        
        public List<RoadLabelEvtAnaInfo> RoadEvtAnaInfoList { get; set; }

        public RoadEvtAnaInfoQuery(string dbName)
            : base(MainModel.GetInstance())
        {
            this.dbName = dbName;
            RoadEvtAnaInfoList = new List<RoadLabelEvtAnaInfo>();
        }
        public override string Name
        {
            get { return "RoadEvtAnaInfoQuery"; }
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select [roadLabel],[countyName],[netType],[reasonType]
,[problemPer],[roadLength_M],[problemPer_2G],[problemPer_4G] 
from [{0}].dbo.[tb_roadana_evtana]", dbName);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[8];
            vType[0] = E_VType.E_String;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_String;
            vType[3] = E_VType.E_String;
            vType[4] = E_VType.E_Float;
            vType[5] = E_VType.E_Float;
            vType[6] = E_VType.E_Float;
            vType[7] = E_VType.E_Float;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoadLabelEvtAnaInfo road = new RoadLabelEvtAnaInfo();
                    road.RoadLabel = package.Content.GetParamString();
                    road.CountyName = package.Content.GetParamString();
                    road.NetTypeStr = package.Content.GetParamString();
                    road.ReasonType = package.Content.GetParamString();
                    road.ProblemPer = package.Content.GetParamFloat();
                    road.RoadLength_M = package.Content.GetParamFloat();
                    road.ProblemEvtPer_2G = package.Content.GetParamFloat();
                    road.ProblemEvtPer_4G = package.Content.GetParamFloat();
                    RoadEvtAnaInfoList.Add(road);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
    }
    public class CellExitInfoQuery : DIYSQLBase
    {
        private readonly string dbName;
        private readonly TimePeriod timePeriod_ExitCell;
        public Dictionary<string, CellExitInfo> CellExitInfoDic { get; set; }

        public CellExitInfoQuery(string dbName, TimePeriod timePeriod)
            : base(MainModel.GetInstance())
        {
            this.dbName = dbName;
            timePeriod_ExitCell = timePeriod;
            CellExitInfoDic = new Dictionary<string, CellExitInfo>();
        }
        public override string Name
        {
            get { return "CellExitInfoQuery"; }
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select CONVERT(varchar(10),ECI),'LTE',告警时间,告警名称,告警描述 
from {0}.dbo.tb_cfg_roadana_exitcell_lte where 告警时间 between '{1}' and '{2}' 
union 
select CONVERT(varchar(10),LAC) + '_' + CONVERT(varchar(10),CI),'GSM',告警时间,告警名称,告警描述 
from {0}.dbo.tb_cfg_roadana_exitcell_gsm where 告警时间 between '{1}' and '{2}'"
                , dbName, timePeriod_ExitCell.BeginTime, timePeriod_ExitCell.EndTime);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vType = new E_VType[5];
            vType[0] = E_VType.E_String;
            vType[1] = E_VType.E_String;
            vType[2] = E_VType.E_String;
            vType[3] = E_VType.E_String;
            vType[4] = E_VType.E_String;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            CellExitInfo exitInfo = new CellExitInfo();
            exitInfo.CellToken = package.Content.GetParamString();
            exitInfo.NetType = package.Content.GetParamString();

            string strDate = package.Content.GetParamString();
            DateTime time;
            if (DateTime.TryParse(strDate, out time))
            {
                exitInfo.ExitTime = time;
                exitInfo.ProblemType = package.Content.GetParamString();
                exitInfo.ProblemReason = package.Content.GetParamString();
                CellExitInfoDic[exitInfo.CellToken] = exitInfo;
            }
        }
    }
}
