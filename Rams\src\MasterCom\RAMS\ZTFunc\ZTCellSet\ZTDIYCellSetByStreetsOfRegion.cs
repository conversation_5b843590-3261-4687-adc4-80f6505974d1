﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
namespace MasterCom.RAMS.Net
{
    /*Need2BePerfect_<PERSON>
     * 待优化
     */ 
    public class ZTDIYCellSetByStreetsOfRegion : ZTDIYCellSetByRegion
    {
        public ZTDIYCellSetByStreetsOfRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "区域道路小区集分析"; }
        }
        public override string IconName
        {
            get { return "Images/streetq.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12010, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            WaitBox.Show("正在计算区域中的道路...", getStreetsInRegion);
            return true;
        }

        protected override void initDataMap()
        {
            cellStaterMapDic.Clear();
            tdcellStaterMapDic.Clear();
            wcellStaterMapDic.Clear();
            cdcellStaterMapDic.Clear();
            unknownCellStaterMapDic.Clear();
            unknownTDCellStaterMapDic.Clear();
            unknownWCellStaterMapDic.Clear();
            unknownCDCellStaterMapDic.Clear();
            nbCellStaterMapDic.Clear();
            tdNBCellStaterMapDic.Clear();
            wNBCellStaterMapDic.Clear();
            cdNBCellStaterMapDic.Clear();
            totalCountDic.Clear();
            tdtotalCountDic.Clear();
            wtotalCountDic.Clear();
            cdtotalCountDic.Clear();
        }

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CellSetOfStreetsForm).FullName);
            CellSetOfStreetsForm cellSetForm = obj == null ? null : obj as CellSetOfStreetsForm;
            if (cellSetForm == null || cellSetForm.IsDisposed)
            {
                cellSetForm = new CellSetOfStreetsForm(MainModel);
            }
            cellSetForm.FillData();
            if (!cellSetForm.Visible)
            {
                cellSetForm.Show(MainModel.MainForm);
            }
        }

        // 将区域中的道路名称跟MopOperation2对应，用于判断测试点落在哪条道路
        private Dictionary<string, MapOperation2> streetMop2Dic;
        private void getStreetsInRegion()
        {
            Condition.Geometorys.SelectedStreetFeatures.Clear();
            MapOperation2 mop2 = new MapOperation2();
            streetMop2Dic = new Dictionary<string, MapOperation2>();
            mop2.FillPolygon(Condition.Geometorys.Region);

            MapWinGIS.Shapefile shpFile = GISManager.GetInstance().StreetTable;
            int nmAreaFieldIdx = GISManager.GetInstance().NmFieldIdx;
            if (shpFile == null)
            {
                WaitBox.Close();
                return;
            }
            for (int i = 0; i < shpFile.NumShapes; i++)
            {
                WaitBox.ProgressPercent = (i + 1) * 100 / shpFile.NumShapes;
                MapWinGIS.Shape street = shpFile.get_Shape(i);
                if (street.ShapeType != MapWinGIS.ShpfileType.SHP_POLYGON && street.ShapeType != MapWinGIS.ShpfileType.SHP_POLYLINE)
                {
                    continue;
                }
                string streetName = shpFile.get_CellValue(nmAreaFieldIdx, i) as string;
                if (streetName == null || streetName.Trim() == "" || streetName.Contains("未命名"))
                {
                    continue;
                }

                // 判断道路是否在区域中
                if (mop2.CheckStreetInRegion(street))
                {
                    ObjInfoStreet st = new ObjInfoStreet();
                    st.name = streetName;
                    st.geoShape = street;
                    Condition.Geometorys.SelectedStreetFeatures.Add(st);
                    MapOperation2 tmpMop2 = new MapOperation2();
                    List<MapWinGIS.Shape> tmpList = new List<MapWinGIS.Shape>();
                    tmpList.Add(street);
                    tmpMop2.FillStreets(tmpList);
                    streetMop2Dic[st.name] = tmpMop2;
                }
            }
            WaitBox.Close();
        }

        // 获取测试点所位于的道路名称
         private string getStreetOfTestPoint(TestPoint tp)
        {
             if (streetMop2Dic == null)
             {
                 return "";
             }
             foreach (string key in streetMop2Dic.Keys)
             {
                 if (streetMop2Dic[key].CheckPointInStreets(tp.Longitude, tp.Latitude))
                 {
                     return key;
                 }
             }
             return "";
        }
        
        protected override void statData()
        {
            addCell<Cell>(cellStaterMapDic, (cell) => { return cell.Name; });
            addCell<TDCell>(tdcellStaterMapDic, (cell) => { return cell.Name; });
            addCell<WCell>(wcellStaterMapDic, (cell) => { return cell.Name; });
            addCell<CDCell>(cdcellStaterMapDic, (cell) => { return cell.Name; });

            Dictionary<string, CellSetForm.CellSetResultData> cellSetRetDataDic = new Dictionary<string, CellSetForm.CellSetResultData>();
            addData(cellSetRetDataDic, cellStaterMapDic, (a, b) => { a.cellStaterMap = b; });
            addData(cellSetRetDataDic, tdcellStaterMapDic, (a, b) => { a.tdcellStaterMap = b; });
            addData(cellSetRetDataDic, wcellStaterMapDic, (a, b) => { a.wcellStaterMap = b; });
            addData(cellSetRetDataDic, cdcellStaterMapDic, (a, b) => { a.cdcellStaterMap = b; });
            addData(cellSetRetDataDic, unknownCellStaterMapDic, (a, b) => { a.unknownCellStaterMap = b; });
            addData(cellSetRetDataDic, unknownTDCellStaterMapDic, (a, b) => { a.unknownTDCellStaterMap = b; });
            addData(cellSetRetDataDic, unknownWCellStaterMapDic, (a, b) => { a.unknownWCellStaterMap = b; });
            addData(cellSetRetDataDic, unknownCDCellStaterMapDic, (a, b) => { a.unknownCDCellStaterMap = b; });

            addData(cellSetRetDataDic, totalCountDic, (a, b) => { a.totalCount = b; });
            addData(cellSetRetDataDic, tdtotalCountDic, (a, b) => { a.tdtotalCount = b; });
            addData(cellSetRetDataDic, wtotalCountDic, (a, b) => { a.wtotalCount = b; });
            addData(cellSetRetDataDic, cdtotalCountDic, (a, b) => { a.cdtotalCount = b; });

            MainModel.CellSetRetDataDic = cellSetRetDataDic;
        }

        delegate string FuncName<in T>(T cell);

        private void addCell<T>(Dictionary<string, Dictionary<object, CellSetForm.CellStater>> cellStaterMapDic,
            FuncName<T> func)
        {
            List<string> cellNameList = new List<string>();
            List<T> cellList = new List<T>();
            List<string> streetList = new List<string>(cellStaterMapDic.Keys);
            foreach (string streetName in streetList)
            {
                foreach (T cell in cellStaterMapDic[streetName].Keys)
                {
                    string name = func(cell);
                    if (cellNameList.Contains(name))
                    {
                        cellList.Add(cell);
                        continue;
                    }
                    cellNameList.Add(name);
                }
                for (int i = 0; i < cellList.Count; i++)
                {
                    cellStaterMapDic[streetName].Remove(cellList[i]);
                }
            }
        }

        delegate void Func<in T>(CellSetForm.CellSetResultData res, T data);

        private void addData<T>(Dictionary<string, CellSetForm.CellSetResultData> cellSetRetDataDic, Dictionary<string, T> dic, Func<T> func)
        {
            foreach (string streetName in dic.Keys)
            {
                if (!cellSetRetDataDic.ContainsKey(streetName))
                {
                    CellSetForm.CellSetResultData resultData = new CellSetForm.CellSetResultData();
                    cellSetRetDataDic[streetName] = resultData;
                }
                func(cellSetRetDataDic[streetName], dic[streetName]);
            }
        }
        
        protected override void doWithDTData(TestPoint tpPoint)
        {
            string streetName = getStreetOfTestPoint(tpPoint);
            if (streetName == "")
            {
                return;
            }
            if (tpPoint is TestPointDetail)
            {
                for (int i = 0; i < 6; i++)
                {
                    short? nbcch = (short?)tpPoint["N_BCCH", i];
                    byte? nbsic = (byte?)tpPoint["N_BSIC", i];
                    int? nRxLev = (int?)(short?)tpPoint["N_RxLev", i];
                    if (nbcch == null || nbsic == null)
                    {
                        break;
                    }
                    Cell nbCell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (short)nbcch, (byte)nbsic, tpPoint.Longitude, tpPoint.Latitude);
                    if (nbCell != null)
                    {
                        if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                        {
                            continue;
                        }
                        if (MainModel.GetInstance().SystemConfigInfo.distLimit)//距离限制设置
                        {
                            if (nbCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                            {
                                setNBCellStaterMapDic(streetName, nbCell, (int)nRxLev);
                            }
                        }
                        else
                        {
                            setNBCellStaterMapDic(streetName, nbCell, (int)nRxLev);
                        }
                    }
                }
                Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["LAC"], (ushort?)(int?)tpPoint["CI"], (short?)tpPoint["BCCH"], (byte?)tpPoint["BSIC"], tpPoint.Longitude, tpPoint.Latitude);
                if (cell != null)
                {
                    if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                    {
                        return;
                    }
                    if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                    {
                        if (cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                        {
                            setCellStaterMapDic(streetName, cell, tpPoint);
                        }
                    }
                    else
                    {
                        setCellStaterMapDic(streetName, cell, tpPoint);
                    }
                    return;
                }
                cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["LAC"], (ushort?)(int?)tpPoint["CI"], (short?)(int?)tpPoint[MainModel.TD_GSM_SCell_ARFCN], (byte?)tpPoint[MainModel.TD_GSM_SCell_BSIC], tpPoint.Longitude, tpPoint.Latitude);
                if (cell != null)
                {
                    if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                    {
                        return;
                    }
                    if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                    {
                        if (cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                        {
                            setCellStaterMapDic(streetName, cell, tpPoint);
                        }
                    }
                    else
                    {
                        setCellStaterMapDic(streetName, cell, tpPoint);
                    }
                    return;
                }

                if (tpPoint["LAC"] != null && tpPoint["CI"] != null && (int)tpPoint["LAC"] != 0 && (int)tpPoint["CI"] != 0)
                {
                    setUnknownCellStaterMapDic(streetName, tpPoint);
                }
            }//gsm
            else if (tpPoint is TDTestPointDetail)
            {
                for (int i = 0; i < 6; i++)
                {
                    int? arfcn = (int?)tpPoint["TD_NCell_UARFCN", i];
                    int? cpi = (int?)tpPoint["TD_NCell_CPI", i];
                    int? nRSCP = (int?)tpPoint["TD_NCell_PCCPCH_RSCP", i];
                    if (arfcn == null || cpi == null)
                    {
                        break;
                    }
                    TDCell nbCell = CellManager.GetInstance().GetNearestTDCell(tpPoint.DateTime, (short)arfcn, (short)cpi, tpPoint.Longitude, tpPoint.Latitude);
                    if (nbCell != null)
                    {
                        if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                        {
                            continue;
                        }
                        if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                        {
                            if (nbCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_TD)
                            {
                                setNBCellStaterMapDic(streetName, nbCell, (int)nRSCP);
                            }
                        }
                        else
                        {
                            setNBCellStaterMapDic(streetName, nbCell, (int)nRSCP);
                        }
                    }
                }
                TDCell tdcell = tpPoint.GetMainCell_TD_TDCell();
                if (tdcell != null)
                {
                    if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                    {
                        return;
                    }
                    if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                    {
                        if (tdcell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_TD)
                        {
                            setCellStaterMapDic(streetName, tdcell, tpPoint);
                        }
                    }
                    else
                    {
                        setCellStaterMapDic(streetName, tdcell, tpPoint);
                    }
                    return;
                }
                Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint[MainModel.TD_GSM_SCell_LAC], (ushort?)(int?)tpPoint[MainModel.TD_GSM_SCell_CI], (short?)(int?)tpPoint[MainModel.TD_GSM_SCell_ARFCN], (byte?)tpPoint[MainModel.TD_GSM_SCell_BSIC], tpPoint.Longitude, tpPoint.Latitude);
                if (cell != null)
                {
                    if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                    {
                        return;
                    }
                    if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                    {
                        if (cell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                        {
                            setCellStaterMapDic(streetName, cell, tpPoint);
                        }
                    }
                    else
                    {
                        setCellStaterMapDic(streetName, cell, tpPoint);
                    }
                    return;
                }

                if (tpPoint[MainModel.TD_SCell_LAC] != null && tpPoint[MainModel.TD_SCell_CI] != null)
                {
                    setUnknownCellStaterMapDic(streetName, tpPoint);
                }
            }
            else if (tpPoint is WCDMATestPointDetail)
            {
                WCell wcell = CellManager.GetInstance().GetNearestWCell(tpPoint.DateTime, (int?)tpPoint["W_SysLAI"], (int?)tpPoint["W_SysCellID"], (int?)tpPoint["W_frequency"], (int?)tpPoint["W_Reference_PSC"], tpPoint.Longitude, tpPoint.Latitude);
                if (wcell != null)
                {
                    if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                    {
                        return;
                    }
                    if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                    {
                        if (wcell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_W)
                        {
                            setCellStaterMapDic(streetName, wcell, tpPoint);
                        }
                    }
                    else
                    {
                        setCellStaterMapDic(streetName, wcell, tpPoint);
                    }
                    return;
                }

                if (tpPoint["W_SysLAI"] != null && tpPoint["W_SysCellID"] != null)
                {
                    setUnknownCellStaterMapDic(streetName, tpPoint);
                }
            }
            else if (tpPoint is CDMATestPointDetail)
            {
                CDCell cdcell = CellManager.GetInstance().GetNearestCDCell(tpPoint.DateTime, (int?)tpPoint["CD_SID"], (int?)tpPoint["CD_BID"], (int?)tpPoint["CD_Frequency"], (int?)tpPoint["CD_ReferPN"], tpPoint.Longitude, tpPoint.Latitude);
                if (cdcell != null)
                {
                    if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                    {
                        return;
                    }
                    if (MainModel.SystemConfigInfo.distLimit)//距离限制设置
                    {
                        if (cdcell.GetDistance(tpPoint.Longitude, tpPoint.Latitude) < CD.MAX_COV_DISTANCE_CD)
                        {
                            setCellStaterMapDic(streetName, cdcell, tpPoint);
                        }
                    }
                    else
                    {
                        setCellStaterMapDic(streetName, cdcell, tpPoint);
                    }
                    return;
                }

                if (tpPoint["CD_SID"] != null && tpPoint["CD_BID"] != null && (int)tpPoint["CD_SID"] != -10000000 && (int)tpPoint["CD_BID"] != -10000000)
                {
                    setUnknownCellStaterMapDic(streetName, tpPoint);
                }
            }
        }

        private void setNBCellStaterMapDic(string streetName, object nbCell, int nRxLev)
        {
            Dictionary<string, Dictionary<object, List<int>>> tmpDic;
            if (nbCell is Cell)
            {
                tmpDic = nbCellStaterMapDic;
            }
            else if (nbCell is TDCell)
            {
                tmpDic = tdNBCellStaterMapDic;
            }
            else if (nbCell is WCell)
            {
                tmpDic = wNBCellStaterMapDic;
            }
            else if (nbCell is CDCell)
            {
                tmpDic = cdNBCellStaterMapDic;
            }
            else
            {
                return;
            }
            if (tmpDic.ContainsKey(streetName))
            {
                if (tmpDic[streetName].ContainsKey(nbCell))
                {
                    tmpDic[streetName][nbCell].Add(nRxLev);
                }
                else
                {
                    List<int> rxLevList = new List<int>();
                    rxLevList.Add(nRxLev);
                    tmpDic[streetName][nbCell] = rxLevList;
                }
            }
            else
            {
                List<int> rxLevList = new List<int>();
                rxLevList.Add(nRxLev);
                Dictionary<object, List<int>> nbCellStaterMap = new Dictionary<object, List<int>>();
                nbCellStaterMap[nbCell] = rxLevList;
                tmpDic[streetName] = nbCellStaterMap;
            }
        }

        private void setCellStaterMapDic(string streetName, object cell, TestPoint tpPoint)
        {
            Dictionary<string, Dictionary<object, CellSetForm.CellStater>> tmpDic = new Dictionary<string, Dictionary<object, CellSetForm.CellStater>>();
            int lac = 0;
            int ci = 0;
            float? rxLevSub = 0;
            float? rxQual = 0;
            byte? rxQualSub = null;
            float? c2i = null;
            double distance = 0;
            if (cell is Cell)
            {
                Cell gCell = cell as Cell;
                lac = gCell.LAC;
                ci = gCell.CI;
                if (tpPoint is TestPointDetail)
                {
                    rxLevSub = (float?)(short?)tpPoint["RxLevSub"];
                }
                else
                {
                    rxLevSub = (float?)(int?)tpPoint["TD_GSM_RxlevSub"];
                }
                rxQualSub = (byte?)tpPoint["RxQualSub"];
                c2i = (float?)(short?)tpPoint["C_I", 0];
                distance = gCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude);
                tmpDic = cellStaterMapDic;
            }
            else if (cell is TDCell)
            {
                TDCell tdCell = cell as TDCell;
                lac = tdCell.LAC;
                ci = tdCell.CI;
                if (tpPoint["TD_PCCPCH_RSCP"] is float)
                {
                    rxLevSub = (float)tpPoint["TD_PCCPCH_RSCP"];
                }
                else
                {
                    rxLevSub = (float?)(int?)tpPoint["TD_PCCPCH_RSCP"];
                }
                distance = tdCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude);
                tmpDic = tdcellStaterMapDic;
            }
            else if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                lac = wCell.LAC;
                ci = wCell.CI;
                rxLevSub = (float?)tpPoint["W_Reference_RSCP"];
                rxQual = (float?)tpPoint["W_Reference_Ec_Io"];
                distance = wCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude);
                tmpDic = wcellStaterMapDic;
            }
            else if (cell is CDCell)
            {
                CDCell cdCell = cell as CDCell;
                lac = cdCell.LAC;
                ci = cdCell.CI;
                rxLevSub = (float?)tpPoint["CD_RX_Power"];
                distance = cdCell.GetDistance(tpPoint.Longitude, tpPoint.Latitude);
                tmpDic = cdcellStaterMapDic;
            }

            CellSetForm.CellStater stater = getStater(streetName, cell, tmpDic, lac, ci);

            stater.AddRxLevSub(rxLevSub);
            stater.AddDistance(distance);
            if (cell is Cell)
            {
                stater.AddRxQualSub(rxQualSub);
                stater.AddC_I(c2i);
            }
            if (cell is WCell)
            {
                rxQualMin = -40;
                rxQualMax = -12;
                stater.AddRSRQ(rxQual, rxQualMin, rxQualMax);
                rxQualMax = 7;
                rxQualMin = 5;
            }
            if (totalCountDic.ContainsKey(streetName))
            {
                totalCountDic[streetName]++;
            }
            else
            {
                totalCountDic[streetName] = 1;
            }
        }

        private static CellSetForm.CellStater getStater(string streetName, object cell, 
            Dictionary<string, Dictionary<object, CellSetForm.CellStater>> tmpDic, int lac, int ci)
        {
            CellSetForm.CellStater stater = null;
            if (tmpDic.ContainsKey(streetName))
            {
                if (tmpDic[streetName].ContainsKey(cell))
                {
                    stater = tmpDic[streetName][cell];
                }
                else
                {
                    stater = new CellSetForm.CellStater();
                    stater.Cell = cell;
                    stater.LAC = lac;
                    stater.CI = ci;
                    stater.XH = tmpDic[streetName].Count + 1;
                    tmpDic[streetName][cell] = stater;
                }
            }
            else
            {
                Dictionary<object, CellSetForm.CellStater> cellStaterMap = new Dictionary<object, CellSetForm.CellStater>();
                stater = new CellSetForm.CellStater();
                stater.Cell = cell;
                stater.LAC = lac;
                stater.CI = ci;
                stater.XH = 1;
                cellStaterMap[cell] = stater;
                tmpDic[streetName] = cellStaterMap;
            }

            return stater;
        }

        class ResInfo
        {
            public float? rxLevSub = 0;
            public byte? rxQualSub = null;
            public float? rxQual = null;
            public float? c2i = null;
            public string key = "";
            public int? lac = null;
            public int? ci = null;
        }

        private void setUnknownCellStaterMapDic(string streetName, TestPoint tpPoint)
        {
            Dictionary<string, Dictionary<string, CellSetForm.CellStater>> tmpDic = new Dictionary<string, Dictionary<string, CellSetForm.CellStater>>();
            ResInfo info;
            setResInfo(tpPoint, ref tmpDic, out info);
            if (info.lac == null || info.ci == null)
            {
                return;
            }

            CellSetForm.CellStater stater = null;
            if (tmpDic.ContainsKey(streetName))
            {
                if (tmpDic[streetName].ContainsKey(info.key))
                {
                    stater = tmpDic[streetName][info.key];
                }
                else
                {
                    stater = new CellSetForm.CellStater();
                    stater.LAC = (int)info.lac;
                    stater.CI = (int)info.ci;
                    stater.XH = tmpDic[streetName].Count + 1;
                    tmpDic[streetName][info.key] = stater;
                }
            }
            else
            {
                Dictionary<string, CellSetForm.CellStater> cellStaterMap = new Dictionary<string, CellSetForm.CellStater>();
                stater = new CellSetForm.CellStater();
                stater.LAC = (int)info.lac;
                stater.CI = (int)info.ci;
                stater.XH = 1;
                cellStaterMap[info.key] = stater;
                tmpDic[streetName] = cellStaterMap;
            }

            stater.AddRxLevSub(info.rxLevSub);
            if (tpPoint is TestPointDetail)
            {
                stater.AddRxQualSub(info.rxQualSub);
                stater.AddC_I(info.c2i);
            }
            if (tpPoint is WCDMATestPointDetail)
            {
                rxQualMin = -40;
                rxQualMax = -12;
                stater.AddRSRQ(info.rxQual, rxQualMin, rxQualMax);
                rxQualMin = 5;
                rxQualMax = 7;
            }
            if (totalCountDic.ContainsKey(streetName))
            {
                totalCountDic[streetName]++;
            }
            else
            {
                totalCountDic[streetName] = 1;
            }
        }

        private void setResInfo(TestPoint tpPoint, ref Dictionary<string, Dictionary<string, CellSetForm.CellStater>> tmpDic, out ResInfo info)
        {
            info = new ResInfo();
            if (tpPoint is TestPointDetail)
            {
                setGsm(tpPoint, ref tmpDic, info);
            }
            else if (tpPoint is TDTestPointDetail)
            {
                setTD(tpPoint, ref tmpDic, info);
            }
            else if (tpPoint is WCDMATestPointDetail)
            {
                setWCDMA(tpPoint, ref tmpDic, info);
            }
            else if (tpPoint is CDMATestPointDetail)
            {
                setCDMA(tpPoint, ref tmpDic, info);
            }
        }

        private void setGsm(TestPoint tpPoint, ref Dictionary<string, Dictionary<string, CellSetForm.CellStater>> tmpDic, ResInfo info)
        {
            info.rxLevSub = (float?)(short?)tpPoint["RxLevSub"];
            info.rxQualSub = (byte?)tpPoint["RxQualSub"];
            info.c2i = (float?)(short?)tpPoint["C_I", 0];
            info.lac = (int?)tpPoint["LAC"];
            info.ci = (int?)tpPoint["CI"];
            if (info.lac == null || info.ci == null)
            {
                return;
            }
            info.key = info.lac + "_" + info.ci;
            tmpDic = unknownCellStaterMapDic;
        }

        private void setTD(TestPoint tpPoint, ref Dictionary<string, Dictionary<string, CellSetForm.CellStater>> tmpDic, ResInfo info)
        {
            if (tpPoint["TD_PCCPCH_RSCP"] is float)
            {
                info.rxLevSub = (float)tpPoint["TD_PCCPCH_RSCP"];
            }
            else
            {
                info.rxLevSub = (float?)(int?)tpPoint["TD_PCCPCH_RSCP"];
            }
            info.lac = (int?)tpPoint[MainModel.TD_SCell_LAC];
            info.ci = (int?)tpPoint[MainModel.TD_SCell_CI];
            if (info.lac == null || info.ci == null)
            {
                return;
            }
            info.key = info.lac + "_" + info.ci;
            tmpDic = unknownTDCellStaterMapDic;
        }

        private void setWCDMA(TestPoint tpPoint, ref Dictionary<string, Dictionary<string, CellSetForm.CellStater>> tmpDic, ResInfo info)
        {
            info.lac = (int?)tpPoint["W_SysLAI"];
            info.ci = (int?)tpPoint["W_SysCellID"];
            if (info.lac == null || info.ci == null)
            {
                return;
            }
            info.key = info.lac + "_" + info.ci;
            float? rxLev = (float?)tpPoint["W_Reference_RSCP"];
            info.rxQual = (float?)tpPoint["W_Reference_Ec_Io"];
            if (rxLev != null && info.rxQual != null)
            {
                info.rxLevSub = (float)rxLev;
                tmpDic = unknownWCellStaterMapDic;
            }
        }

        private void setCDMA(TestPoint tpPoint, ref Dictionary<string, Dictionary<string, CellSetForm.CellStater>> tmpDic, ResInfo info)
        {
            info.rxLevSub = (float?)tpPoint["CD_RX_Power"];
            info.lac = (int?)tpPoint["CD_SID"];
            info.ci = (int?)tpPoint["CD_BID"];
            if (info.lac == null || info.ci == null)
            {
                return;
            }
            info.key = info.lac + "_" + info.ci;
            tmpDic = unknownCDCellStaterMapDic;
        }

        readonly Dictionary<string, Dictionary<object, CellSetForm.CellStater>> cellStaterMapDic 
            = new Dictionary<string, Dictionary<object, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<object, CellSetForm.CellStater>> tdcellStaterMapDic 
            = new Dictionary<string, Dictionary<object, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<object, CellSetForm.CellStater>> wcellStaterMapDic 
            = new Dictionary<string, Dictionary<object, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<object, CellSetForm.CellStater>> cdcellStaterMapDic 
            = new Dictionary<string, Dictionary<object, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<string, CellSetForm.CellStater>> unknownCellStaterMapDic 
            = new Dictionary<string, Dictionary<string, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<string, CellSetForm.CellStater>> unknownTDCellStaterMapDic
            = new Dictionary<string, Dictionary<string, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<string, CellSetForm.CellStater>> unknownWCellStaterMapDic 
            = new Dictionary<string, Dictionary<string, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<string, CellSetForm.CellStater>> unknownCDCellStaterMapDic 
            = new Dictionary<string, Dictionary<string, CellSetForm.CellStater>>();
        readonly Dictionary<string, Dictionary<object, List<int>>> nbCellStaterMapDic = new Dictionary<string, Dictionary<object, List<int>>>();
        readonly Dictionary<string, Dictionary<object, List<int>>> tdNBCellStaterMapDic = new Dictionary<string, Dictionary<object, List<int>>>();
        readonly Dictionary<string, Dictionary<object, List<int>>> wNBCellStaterMapDic = new Dictionary<string, Dictionary<object, List<int>>>();
        readonly Dictionary<string, Dictionary<object, List<int>>> cdNBCellStaterMapDic = new Dictionary<string, Dictionary<object, List<int>>>();
        readonly Dictionary<string, long> totalCountDic = new Dictionary<string,long>();
        readonly Dictionary<string, long> tdtotalCountDic = new Dictionary<string, long>();
        readonly Dictionary<string, long> wtotalCountDic = new Dictionary<string, long>();
        readonly Dictionary<string, long> cdtotalCountDic = new Dictionary<string, long>();
    }
}
