﻿using System;
using System.Collections.Generic;
using System.Text;

using DevExpress.XtraGrid.Columns;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class PoorQualityRoadQueryByRegion_W : DIYSimpleRoadQueryByRegionBase
    {
        protected double maxQuality = -10;
        protected PoorQualityRoadSettingForm setForm = null;

        public PoorQualityRoadQueryByRegion_W(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "WCDMA质差路段"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14035, this.Name);
        }

        protected override bool getCondition()
        {
            if (setForm == null)
            {
                setForm = new PoorQualityRoadSettingForm("TotalEc_Io ≤", (int)maxQuality, (int)minRoadLength, (int)maxSampleGap);
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            setForm.GetSettingFilterRet(out maxQuality, out minRoadLength, out maxSampleGap);
            return true;
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("WCDMA", "TotalEc_Io");
        }

        protected override void fireRoadCompleted(List<TestPoint> roadPoints, double roadLength, DTFileDataManager fileManager)
        {
            if (roadLength < minRoadLength)
            {
                return;
            }

            PoorQualityRoadView_W road = new PoorQualityRoadView_W(roadPoints, roadLength, fileManager.FileName);
            road.GetResult();
            base.resultRoadList.Add(road);
        }

        protected override SimpleRoadPointStatus GetPointStatus(TestPoint tp)
        {
            float? ecio = (float?)tp["W_TotalEc_Io"];
            if (ecio == null || ecio == -10000000)
            {
                return SimpleRoadPointStatus.Ignore;
            }
            return ecio <= maxQuality ? SimpleRoadPointStatus.Valid : SimpleRoadPointStatus.Invalid;
        }

        protected override object GetResultBindingObject(List<RoadInfoViewBase> resultList)
        {
            if (resultList.Count == 0)
            {
                return null;
            }
            List<PoorQualityRoadView_W> lst = new List<PoorQualityRoadView_W>();
            foreach (RoadInfoViewBase view in resultList)
            {
                lst.Add(view as PoorQualityRoadView_W);
            }
            return lst;
        }
    }

    public class PoorQualityRoadView_W : RoadInfoViewBase
    {
        public string FileName
        {
            get;
            private set;
        }

        public String MinEcio
        {
            get;
            private set;
        }

        public String MaxEcio
        {
            get;
            private set;
        }

        public String AvgEcio
        {
            get;
            private set;
        }

        public String MinRscp
        {
            get;
            private set;
        }

        public String MaxRscp
        {
            get;
            private set;
        }

        public String AvgRscp
        {
            get;
            private set;
        }

        public PoorQualityRoadView_W(List<TestPoint> roadPoints, double roadLength, string fileName) : base(roadPoints, roadLength)
        {
            FileName = fileName;
        }

        public void GetResult()
        {
            double minEcio = double.MaxValue;
            double maxEcio = double.MinValue;
            double sumEcio = 0;
            double ecioCnt = 0;
            double minRscp = double.MaxValue;
            double maxRscp = double.MinValue;
            double sumRscp = 0;
            double rscpCnt = 0;

            foreach (TestPoint tp in base.RoadPoints)
            {
                float? rscp = (float?)tp["W_TotalRSCP"];
                if (rscp != null)
                {
                    minRscp = Math.Min(minRscp, (double)rscp);
                    maxRscp = Math.Max(maxRscp, (double)rscp);
                    sumRscp += (double)rscp;
                    ++rscpCnt;
                }

                float? ecio = (float?)tp["W_TotalEc_Io"];
                if (ecio != null)
                {
                    minEcio = Math.Min(minEcio, (double)ecio);
                    maxEcio = Math.Max(maxEcio, (double)ecio);
                    sumEcio += (double)ecio;
                    ++ecioCnt;
                }
            }

            MinRscp = rscpCnt == 0 ? "" : Math.Round(minRscp, 2).ToString();
            MaxRscp = rscpCnt == 0 ? "" : Math.Round(maxRscp, 2).ToString();
            AvgRscp = rscpCnt == 0 ? "" : Math.Round(sumRscp / rscpCnt, 2).ToString();

            MinEcio = ecioCnt == 0 ? "" : Math.Round(minEcio, 2).ToString();
            MaxEcio = ecioCnt == 0 ? "" : Math.Round(maxEcio, 2).ToString();
            AvgEcio = ecioCnt == 0 ? "" : Math.Round(sumEcio / ecioCnt, 2).ToString();
        }

        public override GridColumn[] GridColumns
        {
            get
            {
                List<GridColumn> columns = new List<GridColumn>(base.GridColumns);
                GridColumn gridColumn = new GridColumn();
                gridColumn.Caption = "最小Rscp";
                gridColumn.FieldName = "MinRscp";
                columns.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "最大Rscp";
                gridColumn.FieldName = "MaxRscp";
                columns.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "平局Rscp";
                gridColumn.FieldName = "AvgRscp";
                columns.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "最小Ecio";
                gridColumn.FieldName = "MinEcio";
                columns.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "最大Ecio";
                gridColumn.FieldName = "MaxEcio";
                columns.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "平均Rscp";
                gridColumn.FieldName = "AvgEcio";
                columns.Add(gridColumn);

                gridColumn = new GridColumn();
                gridColumn.Caption = "文件名";
                gridColumn.FieldName = "FileName";
                columns.Add(gridColumn);

                return columns.ToArray();
            }
        }
    }
}
