﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTInterferenceDataNew : DIYStatQuery
    {
        public DIYQueryCQTInterferenceDataNew(MainModel mainModel, string netWorker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            netType = netWorker;
        }
        readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            string str = "查询" + netType + "业务网络干扰";
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21023, string.Format("Name[{0}], {1}", this.Name,str));
        }

        #region  全局变量
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        readonly List<CQTInvadeCoverItem> cqtInvadeCoverItemList = new List<CQTInvadeCoverItem>();
        readonly Dictionary<Cell, List<TestPoint>> cellTestPointDic = new Dictionary<Cell, List<TestPoint>>();
        readonly Dictionary<TDCell, List<TestPoint>> tdCellTestPointDic = new Dictionary<TDCell, List<TestPoint>>();
        readonly List<CQTC2IItemNew> cqtC2IItemNewList = new List<CQTC2IItemNew>();
        int numci = 0;
        int numall = 0;
        double numerror = 0;
        #endregion
        /// <summary>
        /// 准备查询数据
        /// </summary>
        protected override void query()
        {
            fileList.Clear();
            fileValueNameList.Clear();
            fileValueList.Clear();
            cqtInvadeCoverItemList.Clear();
            cellTestPointDic.Clear();
            tdCellTestPointDic.Clear();
            cqtC2IItemNewList.Clear();

            numci = -1;
            numall = -1;
            numerror = -1;
            CQTGSMInterferenceQueryForm cqtQueryForm = new CQTGSMInterferenceQueryForm(netType);
            if (cqtQueryForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            cqtQueryForm.getSelect(out numci, out numall, out numerror);

            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //每个地点所涉及的文件
            addFileList();
            WaitBox.Show("开始分析各个测试地点的干扰情况", cqtCoverIntrusion);
            if (netType.Equals("GSM"))
            {
                CQTGSMInterferenceXtraForm cqtFrm = new CQTGSMInterferenceXtraForm(MainModel);
                cqtFrm.Show();
                cqtFrm.setData(cqtC2IItemNewList);
            }
            else if (netType.Equals("TD"))
            {
                CQTTDInterferenceXtraForm cqtFrm = new CQTTDInterferenceXtraForm(MainModel);
                cqtFrm.Show();
                cqtFrm.setData(cqtC2IItemNewList);
            }
        }

        private void addFileList()
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        /// <summary>
        /// 按测试地点逐个分析
        /// </summary>
        private void cqtCoverIntrusion()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                    break;
                
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的网络干扰情况...";
                WaitBox.ProgressPercent = 30;
                if (netType.Equals("GSM"))
                {
                    GetCellByTestPointGSM(replayCQTFileTest(fileValueList[cpn]), cpn);
                }
                else if (netType.Equals("TD"))
                {
                    GetCellByTestPointTD(replayCQTFileTest(fileValueList[cpn]), cpn);
                }
            }
            WaitBox.Close();
        }
        /// <summary>
        /// 回放文件获取每个CQT地点涉及文件所有有效的采样点
        /// </summary>
        private List<TestPoint> replayCQTFileTest(List<FileInfo> filelist)
        {
            List<TestPoint> fileTestPointList = new List<TestPoint>();
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.AddRange(filelist);
            ReplayFileCQT query = new ReplayFileCQT(mainModel);
            query.SetQueryCondition(condition);
            query.Query();
            foreach (TestPoint tp in query.testPointList)
            {
                if (WaitBox.CancelRequest)
                    break;
                if (netType.Equals("GSM"))
                {
                    int? iCi = (int?)tp["CI"];
                    if (iCi == null || iCi <= 0)
                        continue;
                }
                else if (netType.Equals("TD"))
                {
                    int? iCi = (int?)tp["TD_SCell_CI"];
                    if (iCi == null || iCi <= 0)
                        continue;
                }
                fileTestPointList.Add(tp);
            }
            return fileTestPointList;
        }
        /// <summary>
        /// 通过采样点获取小区,并且按小区处理采样点(GSM)
        /// </summary>
        private void GetCellByTestPointGSM(List<TestPoint> tpList, string cqtName)
        {
            float maxCI;
            float minCI;
            int iAbnormal = 0;
            int sumNumber = 0;
            float sumCI = 0;
            cellTestPointDic.Clear();
            setCellTestPointDic(tpList);

            foreach (Cell cell in cellTestPointDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                maxCI = -40;
                minCI = 40;
                iAbnormal = 0;
                sumNumber = 0;
                sumCI = 0;
                CQTC2IItemNew cqtC2IItemNew = new CQTC2IItemNew();
                cqtC2IItemNew.Strcqtname = cqtName;
                cqtC2IItemNew.Strcellname = cell.Name;
                cqtC2IItemNew.Ilaic = cell.LAC;
                cqtC2IItemNew.Ici = cell.CI;
                cqtC2IItemNew.Ibcch = (int)cell.BCCH;
                cqtC2IItemNew.Ibsic = (int)cell.BSIC;
                cqtC2IItemNew.INumC2I = cellTestPointDic[cell].Count;
                if (cqtC2IItemNew.INumC2I < numall)
                    continue;
                Dictionary<string, List<TestPoint>> cellNameTestPointDic 
                    = getCellNameTestPointDicGsm(ref maxCI, ref minCI, ref iAbnormal, ref sumNumber, ref sumCI, cell);
                cqtC2IItemNew.IMaxC2I = (int)maxCI;
                cqtC2IItemNew.IMinC2I = (int)minCI;
                cqtC2IItemNew.IMeanC2I = (int)(sumCI / sumNumber);
                cqtC2IItemNew.FAbnormalRate = 100.0 * iAbnormal / cellTestPointDic[cell].Count;
                if (cqtC2IItemNew.FAbnormalRate < numerror)
                    continue;
                cqtC2IItemNew.StrAbnormalRate = (100.0 * iAbnormal / cellTestPointDic[cell].Count).ToString("0.00") + "%";
                List<underCqtC2IItem> underCqtC2IItemList = new List<underCqtC2IItem>();
                foreach (string fileName in cellNameTestPointDic.Keys)
                {
                    if (WaitBox.CancelRequest)
                        break;
                    setUnderCqtC2IItemGsm(cqtName, cellNameTestPointDic, underCqtC2IItemList, fileName);
                }
                if (underCqtC2IItemList.Count == 0)
                    continue;
                cqtC2IItemNew.cqtCellInfo = underCqtC2IItemList;
                cqtC2IItemNewList.Add(cqtC2IItemNew);
            }
        }

        private Dictionary<string, List<TestPoint>> getCellNameTestPointDicGsm(ref float maxCI, ref float minCI, 
            ref int iAbnormal, ref int sumNumber, ref float sumCI, Cell cell)
        {
            Dictionary<string, List<TestPoint>> cellNameTestPointDic = new Dictionary<string, List<TestPoint>>();
            foreach (TestPoint tpp in cellTestPointDic[cell])
            {
                if (WaitBox.CancelRequest)
                    break;
                addSumCIGsm(ref maxCI, ref minCI, ref iAbnormal, ref sumNumber, ref sumCI, tpp);
                if (!cellNameTestPointDic.ContainsKey(tpp.FileName))
                {
                    List<TestPoint> tpTem = new List<TestPoint>();
                    tpTem.Add(tpp);
                    cellNameTestPointDic.Add(tpp.FileName, tpTem);
                }
                else
                {
                    cellNameTestPointDic[tpp.FileName].Add(tpp);
                }
            }

            return cellNameTestPointDic;
        }

        private void addSumCIGsm(ref float maxCI, ref float minCI, ref int iAbnormal, ref int sumNumber, ref float sumCI, TestPoint tpp)
        {
            for (int ciID = 0; ciID < 50; ciID++)
            {
                float? c2i = (float?)(short?)tpp["C_I", ciID];
                if (c2i == null || (float)c2i < -40 || (float)c2i > 40)
                    continue;
                if ((float)c2i > maxCI)
                    maxCI = (float)c2i;
                if ((float)c2i < minCI)
                    minCI = (float)c2i;
                if ((int)c2i <= numci)
                {
                    iAbnormal++;
                    break;
                }
                sumNumber++;
                sumCI += (float)c2i;
            }
        }

        private void setUnderCqtC2IItemGsm(string cqtName, Dictionary<string, List<TestPoint>> cellNameTestPointDic, List<underCqtC2IItem> underCqtC2IItemList, string fileName)
        {
            float underMax = -40;
            float underMin = 40;
            int underSumNumber = 0;
            float underSumCI = 0;
            underCqtC2IItem underCqtC2I = new underCqtC2IItem();
            underCqtC2I.Strufilename = fileName;
            foreach (FileInfo file in fileValueList[cqtName])
            {
                if (WaitBox.CancelRequest)
                    break;
                if (file.Name.Equals(fileName))
                {
                    underCqtC2I.ReplayFile = file;
                    break;
                }
            }
            underCqtC2I.IuNumC2I = cellNameTestPointDic[fileName].Count;
            int dtId = 0;
            foreach (TestPoint tpl in cellNameTestPointDic[fileName])
            {
                if (WaitBox.CancelRequest)
                    break;
                if (dtId == 0)
                {
                    underCqtC2I.Dtime = tpl.DateTime;
                    dtId++;
                }
                addUnderSumCIGsm(ref underMax, ref underMin, ref underSumNumber, ref underSumCI, tpl);
            }
            underCqtC2I.IuMaxC2I = (int)underMax;
            underCqtC2I.IuMinC2I = (int)underMin;
            underCqtC2I.IuMeanC2I = (int)(underSumCI / underSumNumber);
            if (underCqtC2I.IuMaxC2I != -40 && underCqtC2I.IuMinC2I != 40)
            {
                underCqtC2IItemList.Add(underCqtC2I);
            }
        }

        private static void addUnderSumCIGsm(ref float underMax, ref float underMin, ref int underSumNumber, ref float underSumCI, TestPoint tpl)
        {
            for (int ciID = 0; ciID < 50; ciID++)
            {
                float? c2i = (float?)(short?)tpl["C_I", ciID];
                if (c2i == null || (float)c2i < -40 || (float)c2i > 40)
                    continue;
                if ((float)c2i > underMax)
                    underMax = (float)c2i;
                if ((float)c2i < underMin)
                    underMin = (float)c2i;
                underSumNumber++;
                underSumCI += (float)c2i;
            }
        }

        private void setCellTestPointDic(List<TestPoint> tpList)
        {
            foreach (TestPoint tp in tpList)
            {
                if (WaitBox.CancelRequest)
                    break;
                Cell mainCell = CellManager.GetInstance().GetCell(tp.DateTime, (ushort)(int?)tp["LAC"], (ushort)(int?)tp["CI"]);
                if (mainCell != null)
                {
                    List<Cell> cellTem = new List<Cell>();
                    if (cellTestPointDic.Keys.Count > 0)
                        cellTem.AddRange(cellTestPointDic.Keys);
                    if (!cellTem.Contains(mainCell))
                    {
                        List<TestPoint> testTem = new List<TestPoint>();
                        testTem.Add(tp);
                        cellTestPointDic.Add(mainCell, testTem);
                    }
                    else
                    {
                        cellTestPointDic[mainCell].Add(tp);
                    }
                }
            }
        }

        /// <summary>
        /// 通过采样点获取小区,并且按小区处理采样点(TD)
        /// </summary>
        private void GetCellByTestPointTD(List<TestPoint> tpList, string cqtName)
        {
            float maxCI;
            float minCI;
            int iAbnormal = 0;
            int sumNumber = 0;
            float sumCI = 0;
            tdCellTestPointDic.Clear();
            setTdCellTestPointDic(tpList);

            foreach (TDCell cell in tdCellTestPointDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                maxCI = -40;
                minCI = 40;
                iAbnormal = 0;
                sumNumber = 0;
                sumCI = 0;
                CQTC2IItemNew cqtC2IItemNew = new CQTC2IItemNew();
                cqtC2IItemNew.Strcqtname = cqtName;
                cqtC2IItemNew.Strcellname = cell.Name;
                cqtC2IItemNew.Ilaic = cell.LAC;
                cqtC2IItemNew.Ici = cell.CI;
                cqtC2IItemNew.Ibcch = cell.FREQ;
                cqtC2IItemNew.Ibsic = cell.CPI;
                cqtC2IItemNew.INumC2I = tdCellTestPointDic[cell].Count;
                if (cqtC2IItemNew.INumC2I < numall)
                    continue;
                Dictionary<string, List<TestPoint>> cellNameTestPointDic = 
                    getCellNameTestPointDic(ref maxCI, ref minCI, ref iAbnormal, ref sumNumber, ref sumCI, cell);
                cqtC2IItemNew.IMaxC2I = (int)maxCI;
                cqtC2IItemNew.IMinC2I = (int)minCI;
                cqtC2IItemNew.IMeanC2I = (int)(sumCI / sumNumber);
                cqtC2IItemNew.FAbnormalRate = 100.0 * iAbnormal / tdCellTestPointDic[cell].Count;
                if (cqtC2IItemNew.FAbnormalRate < numerror)
                    continue;
                cqtC2IItemNew.StrAbnormalRate = (100.0 * iAbnormal / tdCellTestPointDic[cell].Count).ToString("0.00") + "%";
                List<underCqtC2IItem> underCqtC2IItemList = new List<underCqtC2IItem>();
                foreach (string fileName in cellNameTestPointDic.Keys)
                {
                    if (WaitBox.CancelRequest)
                        break;
                    setUnderCqtC2IItem(cqtName, cellNameTestPointDic, underCqtC2IItemList, fileName);
                }
                if (underCqtC2IItemList.Count == 0)
                    continue;
                cqtC2IItemNew.cqtCellInfo = underCqtC2IItemList;
                cqtC2IItemNewList.Add(cqtC2IItemNew);
            }
        }

        private Dictionary<string, List<TestPoint>> getCellNameTestPointDic(ref float maxCI, ref float minCI, 
            ref int iAbnormal, ref int sumNumber, ref float sumCI, TDCell cell)
        {
            Dictionary<string, List<TestPoint>> cellNameTestPointDic = new Dictionary<string, List<TestPoint>>();
            foreach (TestPoint tpp in tdCellTestPointDic[cell])
            {
                if (WaitBox.CancelRequest)
                    break;
                float? c2i = (float?)(int?)tpp["TD_PCCPCH_C2I"];
                if (c2i == null || (float)c2i < -40 || (float)c2i > 40)
                    continue;
                if ((float)c2i > maxCI)
                    maxCI = (float)c2i;
                if ((float)c2i < minCI)
                    minCI = (float)c2i;
                if ((int)c2i <= numci)
                    iAbnormal++;
                sumNumber++;
                sumCI += (float)c2i;
                if (!cellNameTestPointDic.ContainsKey(tpp.FileName))
                {
                    List<TestPoint> tpTem = new List<TestPoint>();
                    tpTem.Add(tpp);
                    cellNameTestPointDic.Add(tpp.FileName, tpTem);
                }
                else
                {
                    cellNameTestPointDic[tpp.FileName].Add(tpp);
                }
            }

            return cellNameTestPointDic;
        }

        private void setUnderCqtC2IItem(string cqtName, Dictionary<string, List<TestPoint>> cellNameTestPointDic, 
            List<underCqtC2IItem> underCqtC2IItemList, string fileName)
        {
            float underMax = -40;
            float underMin = 40;
            int underSumNumber = 0;
            float underSumCI = 0;
            underCqtC2IItem underCqtC2I = new underCqtC2IItem();
            underCqtC2I.Strufilename = fileName;
            foreach (FileInfo file in fileValueList[cqtName])
            {
                if (file.Name.Equals(fileName))
                {
                    underCqtC2I.ReplayFile = file;
                    break;
                }
            }
            underCqtC2I.IuNumC2I = cellNameTestPointDic[fileName].Count;
            int dId = 0;
            foreach (TestPoint tpl in cellNameTestPointDic[fileName])
            {
                if (dId == 0)
                {
                    underCqtC2I.Dtime = tpl.DateTime;
                }
                float? c2i = (float?)(int?)tpl["TD_PCCPCH_C2I"];
                if (c2i == null || (float)c2i < -40 || (float)c2i > 40)
                    continue;
                if ((float)c2i > underMax)
                    underMax = (float)c2i;
                if ((float)c2i < underMin)
                    underMin = (float)c2i;
                underSumNumber++;
                underSumCI += (float)c2i;
            }
            underCqtC2I.IuMaxC2I = (int)underMax;
            underCqtC2I.IuMinC2I = (int)underMin;
            underCqtC2I.IuMeanC2I = (int)(underSumCI / underSumNumber);
            if (underCqtC2I.IuMaxC2I != -40 && underCqtC2I.IuMinC2I != 40)
            {
                underCqtC2IItemList.Add(underCqtC2I);
            }
        }

        private void setTdCellTestPointDic(List<TestPoint> tpList)
        {
            foreach (TestPoint tp in tpList)
            {
                if (WaitBox.CancelRequest)
                    break;
                TDCell mainCell;
                Cell tmp;
                tp.GetMainCell_TD(out mainCell, out tmp);
                if (mainCell != null)
                {
                    List<TDCell> cellTem = new List<TDCell>();
                    if (tdCellTestPointDic.Keys.Count > 0)
                        cellTem.AddRange(tdCellTestPointDic.Keys);
                    if (!cellTem.Contains(mainCell))
                    {
                        List<TestPoint> testTem = new List<TestPoint>();
                        testTem.Add(tp);
                        tdCellTestPointDic.Add(mainCell, testTem);
                    }
                    else
                    {
                        tdCellTestPointDic[mainCell].Add(tp);
                    }
                }
            }
        }
    }
    public class CQTC2IItemNew
    {
        public double FAbnormalRate { get; set; }
        public float FAvgC2I { get; set; }
        public int IAbnormal { get; set; }
        public int Ibcch { get; set; }
        public int Ibsic { get; set; }
        public int Ici { get; set; }
        public int Icpi { get; set; }
        public int Ilaic { get; set; }
        public int IMaxC2I { get; set; }
        public int IMeanC2I { get; set; }
        public int IMinC2I { get; set; }
        public int INumC2I { get; set; }
        public string StrAbnormalRate { get; set; }
        public string Strcellname { get; set; }
        public string Strcqtname { get; set; }
        public List<underCqtC2IItem> cqtCellInfo { get; set; } = new List<underCqtC2IItem>();
    }
    public class underCqtC2IItem
    {
        public string Strufilename { get; set; }
        public int IuMaxC2I { get; set; }
        public int IuMeanC2I { get; set; }
        public int IuMinC2I { get; set; }
        public int IuNumC2I { get; set; }
        public FileInfo ReplayFile { get; set; }
        public DateTime Dtime { get; set; }
    }
}
