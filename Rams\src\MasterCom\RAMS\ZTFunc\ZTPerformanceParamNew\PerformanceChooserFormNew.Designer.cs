﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class PerformanceChooserFormNew
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PerformanceChooserFormNew));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            this.treeListParam = new DevExpress.XtraTreeList.TreeList();
            this.colKey = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.listBoxSelected = new DevExpress.XtraEditors.ListBoxControl();
            this.labelEndDate = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.dateEditBeginTime = new DevExpress.XtraEditors.DateEdit();
            this.dateEditEndTime = new DevExpress.XtraEditors.DateEdit();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.listBoxControl1 = new DevExpress.XtraEditors.ListBoxControl();
            this.buttonOK = new DevExpress.XtraEditors.SimpleButton();
            this.buttonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.buttonDiy = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton3 = new DevExpress.XtraEditors.SimpleButton();
            this.directoryEntry1 = new System.DirectoryServices.DirectoryEntry();
            this.saveKPIButton = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.treeListParam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxSelected)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListParam
            // 
            this.treeListParam.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListParam.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListParam.Appearance.Empty.Options.UseBackColor = true;
            this.treeListParam.Appearance.Empty.Options.UseForeColor = true;
            this.treeListParam.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListParam.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListParam.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListParam.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListParam.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListParam.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListParam.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListParam.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListParam.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListParam.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListParam.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListParam.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListParam.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListParam.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListParam.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListParam.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListParam.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListParam.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListParam.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListParam.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListParam.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListParam.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListParam.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListParam.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListParam.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListParam.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListParam.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(208)))), ((int)(((byte)(200)))));
            this.treeListParam.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListParam.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListParam.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListParam.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListParam.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListParam.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListParam.Appearance.Preview.Options.UseBackColor = true;
            this.treeListParam.Appearance.Preview.Options.UseForeColor = true;
            this.treeListParam.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListParam.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListParam.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListParam.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListParam.Appearance.Row.Options.UseBackColor = true;
            this.treeListParam.Appearance.Row.Options.UseForeColor = true;
            this.treeListParam.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListParam.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListParam.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListParam.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListParam.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListParam.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListParam.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListParam.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListParam.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListParam.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListParam.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListParam.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListParam.BackgroundImage")));
            this.treeListParam.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colKey});
            this.treeListParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListParam.Location = new System.Drawing.Point(2, 23);
            this.treeListParam.Name = "treeListParam";
            this.treeListParam.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListParam.OptionsBehavior.AutoChangeParent = false;
            this.treeListParam.OptionsBehavior.AutoNodeHeight = false;
            this.treeListParam.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListParam.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListParam.OptionsBehavior.Editable = false;
            this.treeListParam.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListParam.OptionsBehavior.ResizeNodes = false;
            this.treeListParam.OptionsBehavior.SmartMouseHover = false;
            this.treeListParam.OptionsMenu.EnableFooterMenu = false;
            this.treeListParam.OptionsPrint.PrintHorzLines = false;
            this.treeListParam.OptionsPrint.PrintVertLines = false;
            this.treeListParam.OptionsPrint.UsePrintStyles = true;
            this.treeListParam.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListParam.OptionsView.ShowCheckBoxes = true;
            this.treeListParam.OptionsView.ShowColumns = false;
            this.treeListParam.OptionsView.ShowFocusedFrame = false;
            this.treeListParam.OptionsView.ShowHorzLines = false;
            this.treeListParam.OptionsView.ShowIndicator = false;
            this.treeListParam.OptionsView.ShowVertLines = false;
            this.treeListParam.Size = new System.Drawing.Size(360, 345);
            this.treeListParam.TabIndex = 17;
            this.treeListParam.BeforeCheckNode += new DevExpress.XtraTreeList.CheckNodeEventHandler(this.treeListParam_BeforeCheckNode);
            this.treeListParam.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeListParam_AfterCheckNode);
            // 
            // colKey
            // 
            this.colKey.AllNodesSummary = true;
            this.colKey.Caption = "Registry Keys";
            this.colKey.FieldName = "Key";
            this.colKey.MinWidth = 27;
            this.colKey.Name = "colKey";
            this.colKey.SummaryFooter = DevExpress.XtraTreeList.SummaryItemType.Count;
            this.colKey.SummaryFooterStrFormat = "Count keys = {0}";
            this.colKey.Visible = true;
            this.colKey.VisibleIndex = 0;
            // 
            // listBoxSelected
            // 
            this.listBoxSelected.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxSelected.Location = new System.Drawing.Point(2, 23);
            this.listBoxSelected.Name = "listBoxSelected";
            this.listBoxSelected.Size = new System.Drawing.Size(251, 345);
            this.listBoxSelected.TabIndex = 18;
            // 
            // labelEndDate
            // 
            this.labelEndDate.AutoSize = true;
            this.labelEndDate.Location = new System.Drawing.Point(274, 15);
            this.labelEndDate.Name = "labelEndDate";
            this.labelEndDate.Size = new System.Drawing.Size(59, 14);
            this.labelEndDate.TabIndex = 21;
            this.labelEndDate.Text = "结束时间:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(10, 15);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 14);
            this.label1.TabIndex = 21;
            this.label1.Text = "开始时间:";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.treeListParam);
            this.groupControl1.Location = new System.Drawing.Point(13, 56);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(364, 370);
            this.groupControl1.TabIndex = 22;
            this.groupControl1.Text = "可选性能指标组";
            // 
            // dateEditBeginTime
            // 
            this.dateEditBeginTime.EditValue = new System.DateTime(2012, 3, 30, 0, 0, 0, 0);
            this.dateEditBeginTime.Location = new System.Drawing.Point(75, 12);
            this.dateEditBeginTime.Name = "dateEditBeginTime";
            this.dateEditBeginTime.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Office2003;
            this.dateEditBeginTime.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dateEditBeginTime.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject2, "", null, null, true)});
            this.dateEditBeginTime.Properties.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.dateEditBeginTime.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dateEditBeginTime.Properties.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.dateEditBeginTime.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dateEditBeginTime.Properties.Mask.EditMask = "F";
            this.dateEditBeginTime.Properties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Style3D;
            this.dateEditBeginTime.Properties.PopupResizeMode = DevExpress.XtraEditors.Controls.ResizeMode.FrameResize;
            this.dateEditBeginTime.Properties.ShowWeekNumbers = true;
            this.dateEditBeginTime.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.dateEditBeginTime.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dateEditBeginTime.Size = new System.Drawing.Size(177, 22);
            this.dateEditBeginTime.TabIndex = 23;
            // 
            // dateEditEndTime
            // 
            this.dateEditEndTime.EditValue = new System.DateTime(2012, 3, 30, 0, 0, 0, 0);
            this.dateEditEndTime.Location = new System.Drawing.Point(339, 12);
            this.dateEditEndTime.Name = "dateEditEndTime";
            this.dateEditEndTime.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Office2003;
            this.dateEditEndTime.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dateEditEndTime.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject3, "", null, null, true)});
            this.dateEditEndTime.Properties.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.dateEditEndTime.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dateEditEndTime.Properties.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.dateEditEndTime.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dateEditEndTime.Properties.Mask.EditMask = "F";
            this.dateEditEndTime.Properties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Style3D;
            this.dateEditEndTime.Properties.PopupResizeMode = DevExpress.XtraEditors.Controls.ResizeMode.FrameResize;
            this.dateEditEndTime.Properties.ShowWeekNumbers = true;
            this.dateEditEndTime.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.dateEditEndTime.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dateEditEndTime.Size = new System.Drawing.Size(177, 22);
            this.dateEditEndTime.TabIndex = 23;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.listBoxControl1);
            this.groupControl2.Location = new System.Drawing.Point(705, 55);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(194, 371);
            this.groupControl2.TabIndex = 22;
            this.groupControl2.Text = "指标查询模板";
            // 
            // listBoxControl1
            // 
            this.listBoxControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxControl1.Location = new System.Drawing.Point(2, 23);
            this.listBoxControl1.Name = "listBoxControl1";
            this.listBoxControl1.Size = new System.Drawing.Size(190, 346);
            this.listBoxControl1.TabIndex = 19;
            this.listBoxControl1.SelectedValueChanged += new System.EventHandler(this.listBoxControl1_SelectedValueChanged);
            // 
            // buttonOK
            // 
            this.buttonOK.Location = new System.Drawing.Point(741, 433);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(75, 23);
            this.buttonOK.TabIndex = 24;
            this.buttonOK.Text = "确定";
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.Location = new System.Drawing.Point(822, 433);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(75, 23);
            this.buttonCancel.TabIndex = 24;
            this.buttonCancel.Text = "取消";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonDiy
            // 
            this.buttonDiy.Location = new System.Drawing.Point(444, 433);
            this.buttonDiy.Name = "buttonDiy";
            this.buttonDiy.Size = new System.Drawing.Size(80, 23);
            this.buttonDiy.TabIndex = 25;
            this.buttonDiy.Text = "保存指标模板";
            this.buttonDiy.Click += new System.EventHandler(this.buttonDiy_Click);
            // 
            // simpleButton1
            // 
            this.simpleButton1.Location = new System.Drawing.Point(383, 137);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(55, 23);
            this.simpleButton1.TabIndex = 26;
            this.simpleButton1.Text = "添加>";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.listBoxSelected);
            this.groupControl3.Location = new System.Drawing.Point(444, 55);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(255, 370);
            this.groupControl3.TabIndex = 27;
            this.groupControl3.Text = "查询指标";
            // 
            // simpleButton2
            // 
            this.simpleButton2.Location = new System.Drawing.Point(383, 175);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(55, 23);
            this.simpleButton2.TabIndex = 28;
            this.simpleButton2.Text = "<删除";
            this.simpleButton2.Click += new System.EventHandler(this.simpleButton2_Click);
            // 
            // simpleButton3
            // 
            this.simpleButton3.Location = new System.Drawing.Point(383, 213);
            this.simpleButton3.Name = "simpleButton3";
            this.simpleButton3.Size = new System.Drawing.Size(55, 23);
            this.simpleButton3.TabIndex = 29;
            this.simpleButton3.Text = "<清空";
            this.simpleButton3.Click += new System.EventHandler(this.simpleButton3_Click);
            // 
            // saveKPIButton
            // 
            this.saveKPIButton.Location = new System.Drawing.Point(624, 433);
            this.saveKPIButton.Name = "saveKPIButton";
            this.saveKPIButton.Size = new System.Drawing.Size(80, 23);
            this.saveKPIButton.TabIndex = 30;
            this.saveKPIButton.Text = "删除指标模板";
            this.saveKPIButton.Click += new System.EventHandler(this.saveKPIButton_Click);
            // 
            // PerformanceChooserFormNew
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(911, 468);
            this.Controls.Add(this.saveKPIButton);
            this.Controls.Add(this.simpleButton3);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.simpleButton1);
            this.Controls.Add(this.buttonDiy);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.dateEditEndTime);
            this.Controls.Add(this.dateEditBeginTime);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.labelEndDate);
            this.MaximizeBox = false;
            this.Name = "PerformanceChooserFormNew";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "性能指标选择";
            ((System.ComponentModel.ISupportInitialize)(this.treeListParam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxSelected)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditBeginTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditEndTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraTreeList.TreeList treeListParam;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colKey;
        private DevExpress.XtraEditors.ListBoxControl listBoxSelected;
        private System.Windows.Forms.Label labelEndDate;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.DateEdit dateEditBeginTime;
        private DevExpress.XtraEditors.DateEdit dateEditEndTime;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton buttonOK;
        private DevExpress.XtraEditors.SimpleButton buttonCancel;
        private DevExpress.XtraEditors.SimpleButton buttonDiy;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.ListBoxControl listBoxControl1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.SimpleButton simpleButton3;
        private System.DirectoryServices.DirectoryEntry directoryEntry1;
        private DevExpress.XtraEditors.SimpleButton saveKPIButton;
    }
}