﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTEpsfbDelayAna
{
    public class SingleEpsfbCallStatInfo
    {
        public SingleEpsfbCallStatInfo(EpsfbCallInfo moCall, EpsfbCallInfo mtCall)
        {
            if (moCall != null)
            {
                this.MoFileName = moCall.FileName;
                this.OneEvent = moCall.Events[0];

                if (moCall.FBSuccTime != null && moCall.CallAttemptTime != null)
                {
                    this.FBDelayTime = (int)(moCall.FBSuccTime - moCall.CallAttemptTime).TotalMilliseconds;
                }

                if (moCall.CallEndTime != null && moCall.FRSuccTime != null)
                {
                    this.FRDelayTime = (int)(moCall.FRSuccTime - moCall.CallEndTime).TotalMilliseconds;
                }

                this.CallAttemptTime = moCall.CallAttemptTime;
                this.BeforeLng = moCall.BeforeLng;
                this.BeforeLat = moCall.BeforeLat;
                this.BeforeNetType = moCall.BeforeNetType;
                this.BeforeNrTac = moCall.BeforeNrTac;
                this.BeforeNrNci = moCall.BeforeNrNci;
                this.BeforeGNodeBId = moCall.BeforeGNodeBId;
                this.BeforeCellId = moCall.BeforeCellId;
                this.BeforeSSRsrp = moCall.BeforeSSRsrp;

                this.FBSuccTime = moCall.FBSuccTime;
                this.NowLng = moCall.NowLng;
                this.NowLat = moCall.NowLat;
                this.NowNetType = moCall.NowNetType;
                this.NowLteTac = moCall.NowLteTac;
                this.NowLteEci = moCall.NowLteEci;
                this.NowENodeBId = moCall.NowENodeBId;
                this.NowCellId = moCall.NowCellId;
                this.NowLteRsrp = moCall.NowLteRsrp;
                this.CallEndTime = moCall.CallEndTime;

                this.FRSuccTime = moCall.FRSuccTime;
                this.AfterLng = moCall.AfterLng;
                this.AfterLat = moCall.AfterLat;
                this.AfterNetType = moCall.AfterNetType;
                this.AfterNrTac = moCall.AfterNrTac;
                this.AfterNrNci = moCall.AfterNrNci;
                this.AfterGNodeBId = moCall.AfterGNodeBId;
                this.AfterCellId = moCall.AfterCellId;
                this.AfterSSRsrp = moCall.AfterSSRsrp;

            }

            if (mtCall != null)
            {
                this.MtFileName = mtCall.FileName;
            }
        }

        public int Sn { get; set; }
        public string MoFileName { get; private set; }
        public string MtFileName { get; private set; }

        public Model.Event OneEvent { get; private set; }

        public int? FBDelayTime { get; private set; }    // 回落时延(ms)
        public int? FRDelayTime { get; private set; }    // 返回时延(ms)

        // EPSFB 回落前 NR 小区
        public DateTime CallAttemptTime { get; private set; }    // 请求时间
        public double? BeforeLng { get; private set; }
        public double? BeforeLat { get; private set; }
        public string BeforeNetType { get; private set; }
        public int? BeforeNrTac { get; private set; }
        public long? BeforeNrNci { get; private set; }
        public int? BeforeGNodeBId { get; private set; }
        public int? BeforeCellId { get; private set; }
        public float? BeforeSSRsrp { get; private set; }


        // EPSFB 回落后 LTE 小区
        public DateTime FBSuccTime { get; private set; }    // 回落成功时间
        public double? NowLng { get; private set; }
        public double? NowLat { get; private set; }
        public string NowNetType { get; private set; }
        public int? NowLteTac { get; private set; }
        public long? NowLteEci { get; private set; }
        public int? NowENodeBId { get; private set; }
        public int? NowCellId { get; private set; }
        public int? NowLteRsrp { get; private set; }
        public DateTime CallEndTime { get; private set; }    // 通话结束时间


        // EPSFB 返回后 NR 小区
        public DateTime FRSuccTime { get; private set; }    // 返回成功时间
        public double? AfterLng { get; private set; }
        public double? AfterLat { get; private set; }
        public string AfterNetType { get; private set; }
        public int? AfterNrTac { get; private set; }
        public long? AfterNrNci { get; private set; }
        public int? AfterGNodeBId { get; private set; }
        public int? AfterCellId { get; private set; }
        public float? AfterSSRsrp { get; private set; }
    }
}
