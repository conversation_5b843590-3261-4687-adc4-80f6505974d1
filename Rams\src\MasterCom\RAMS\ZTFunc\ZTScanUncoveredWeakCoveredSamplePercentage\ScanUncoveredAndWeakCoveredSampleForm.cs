﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.Util;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanUncoveredAndWeakCoveredSampleForm : MinCloseForm
    {
        SampleRangeInfo sampleRangeInfo;
        DataTable tbSamplePect;
        DataTable tbSampleRexlv;

        public ScanUncoveredAndWeakCoveredSampleForm()
            :base()
        {
            InitializeComponent();
            sampleRangeInfo = MainModel.SampleRangeInfo;
        }

        public void FillData()
        {
            if (MainModel.SampleRangeInfo.totalCount == 0)
            {
                return;
            }
            init();

            Series series = null;
            if (sampleRangeInfo.SampleRangeInfoType == SampleRangeInfo.SampleRangeType.TD)
            {
                series = new Series("TDScan", ViewType.Bar);
            }
            else if (sampleRangeInfo.SampleRangeInfoType == SampleRangeInfo.SampleRangeType.WCDMA)
            {
                series = new Series("WCDMAScan", ViewType.Bar);
            }
            if (series != null)
            {
                series.Points.Add(new SeriesPoint("(–∞,-100)", sampleRangeInfo.n_100));
                series.Points.Add(new SeriesPoint("[–100,-95)", sampleRangeInfo._100_95));
                series.Points.Add(new SeriesPoint("[–95,-90)", sampleRangeInfo._95_90));
                series.Points.Add(new SeriesPoint("[–90,-85)", sampleRangeInfo._90_85));
                series.Points.Add(new SeriesPoint("[–85,-80)", sampleRangeInfo._85_80));
                series.Points.Add(new SeriesPoint("[–80,-75)", sampleRangeInfo._80_75));
                series.Points.Add(new SeriesPoint("[–75,-70)", sampleRangeInfo._75_70));
                series.Points.Add(new SeriesPoint(" [–70, -65)", sampleRangeInfo._70_65));
                series.Points.Add(new SeriesPoint("[–65, +∞)", sampleRangeInfo._65_p));

                chartControlRexlv.Series.Clear();
                chartControlRexlv.Series.Add(series);
            }
        }

        private void init()
        {
            dataGridViewPercentage.RowCount = 1;
            sampleRangeInfo = MainModel.SampleRangeInfo;
            dataGridViewPercentage.Rows[0].Cells[0].Value = sampleRangeInfo.totalCount;
            dataGridViewPercentage.Rows[0].Cells[1].Value = sampleRangeInfo.weakCoveredCount;
            dataGridViewPercentage.Rows[0].Cells[2].Value = sampleRangeInfo.noneCoveredCount;
            dataGridViewPercentage.Rows[0].Cells[3].Value = sampleRangeInfo.GetWeakCoveredSamplePercentage();
            dataGridViewPercentage.Rows[0].Cells[4].Value = sampleRangeInfo.GetNoneCoveredSamplePercentage();
            dataGridViewPercentage.Rows[0].Cells[5].Value = sampleRangeInfo.GetAverageRxlev();

            dataGridViewRange.RowCount = 1;
            sampleRangeInfo = MainModel.SampleRangeInfo;
            dataGridViewRange.Rows[0].Cells[0].Value = sampleRangeInfo.n_100.ToString();
            dataGridViewRange.Rows[0].Cells[1].Value = sampleRangeInfo._100_95.ToString();
            dataGridViewRange.Rows[0].Cells[2].Value = sampleRangeInfo._95_90.ToString();
            dataGridViewRange.Rows[0].Cells[3].Value = sampleRangeInfo._90_85.ToString();
            dataGridViewRange.Rows[0].Cells[4].Value = sampleRangeInfo._85_80.ToString();
            dataGridViewRange.Rows[0].Cells[5].Value = sampleRangeInfo._80_75.ToString();
            dataGridViewRange.Rows[0].Cells[6].Value = sampleRangeInfo._75_70.ToString();
            dataGridViewRange.Rows[0].Cells[7].Value = sampleRangeInfo._70_65.ToString();
            dataGridViewRange.Rows[0].Cells[8].Value = sampleRangeInfo._65_p.ToString();

            tbSamplePect = new DataTable();
            tbSamplePect.Columns.Add("总采样点数", typeof(int));
            tbSamplePect.Columns.Add("弱覆盖采样点数目", typeof(int));
            tbSamplePect.Columns.Add("无覆盖采样点数目", typeof(int));
            tbSamplePect.Columns.Add("弱覆盖采样点占比", typeof(string));
            tbSamplePect.Columns.Add("无覆盖采样点占比", typeof(string));
            tbSamplePect.Columns.Add("采样点平均电平(dBm)", typeof(int));

            tbSampleRexlv = new DataTable();
            tbSampleRexlv.Columns.Add("(–∞,-100)", typeof(string));
            tbSampleRexlv.Columns.Add("[–100,-95)", typeof(string));
            tbSampleRexlv.Columns.Add("[–95,-90)", typeof(string));
            tbSampleRexlv.Columns.Add("[–90,-85)", typeof(string));
            tbSampleRexlv.Columns.Add("[–85,-80)", typeof(string));
            tbSampleRexlv.Columns.Add("[–80,-75)", typeof(string));
            tbSampleRexlv.Columns.Add("[–75,-70)", typeof(string));
            tbSampleRexlv.Columns.Add("[–70,-65)", typeof(string));
            tbSampleRexlv.Columns.Add("[–65, +∞)", typeof(string));

            tbSamplePect.Rows.Add(new object[]{sampleRangeInfo.totalCount,sampleRangeInfo.weakCoveredCount,sampleRangeInfo.noneCoveredCount
            ,sampleRangeInfo.GetWeakCoveredSamplePercentage(),sampleRangeInfo.GetNoneCoveredSamplePercentage(),sampleRangeInfo.GetAverageRxlev()});

            tbSampleRexlv.Rows.Add(new object[]{sampleRangeInfo.n_100,sampleRangeInfo._100_95,sampleRangeInfo._95_90,sampleRangeInfo._90_85
            ,sampleRangeInfo._85_80,sampleRangeInfo._80_75,sampleRangeInfo._75_70,sampleRangeInfo._70_65,sampleRangeInfo._65_p});
        }

        private void tsmiExport2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Excel file (*.xls)|*.xls";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = dlg.FileName;
                WaitBox.Show("正在导出到Excel...", export2Xls, fileName);
                if (DialogResult.Yes == MessageBox.Show("Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))  //添加了自动打开文件的提示
                {
                    try
                    {
                        System.Diagnostics.Process.Start(fileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + fileName);
                    }
                }
            }
        }

        private void export2Xls(object nameObj)
        {
            Excel.Application excel = null;
            try
            {
                string name = nameObj.ToString();
                excel = new Excel.Application();
                Excel.Workbook workbook = excel.Workbooks.Add(true);
                Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;
                excel.Visible = false;
                worksheet.Name = "无覆盖弱覆盖采样点占比";
                int rowIndex = 1;
                WaitBox.ProgressPercent = 10;
                exportATable(tbSamplePect, workbook, worksheet, "", ref rowIndex);
                WaitBox.ProgressPercent = 40;
                rowIndex += 7;
                exportATable(tbSampleRexlv, workbook, worksheet, "采样点按电平范围分布", ref rowIndex);
                WaitBox.ProgressPercent = 70;
                worksheet._SaveAs(name, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                WaitBox.ProgressPercent = 100;
            }
            finally
            {
                if (excel != null)
                {
                    excel.Quit();
                }
                WaitBox.Close();
            }
        }

        private void exportATable(DataTable tb, Excel.Workbook workbook, Excel.Worksheet worksheet, string typeName, ref int beginRow)
        {
            if (tb == null)
            {
                return;
            }
            int nextBeginRow = 0;
            if (tb == tbSamplePect)
            {
                fillCellsDataSamplePect(tb, worksheet, beginRow);
            }
            if (tb == tbSampleRexlv)
            {
                nextBeginRow = fillCellsDataSampleRexlv(tb, worksheet, beginRow);

                Excel.Chart chart = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);

                Excel.Range range = worksheet.get_Range("B" + beginRow, "B" + (beginRow + tbSampleRexlv.Columns.Count));

                chart.ChartWizard(range, Excel.XlChartType.xl3DColumn, Missing.Value,
                    Excel.XlRowCol.xlColumns, 0, 1, true, typeName, Missing.Value, Missing.Value, Missing.Value);

                Excel.Series series900 = (Excel.Series)chart.SeriesCollection(1);
                series900.BarShape = Excel.XlBarShape.xlCylinder;
                series900.XValues = worksheet.get_Range("A" + (beginRow + 1), "A" + (beginRow + tbSampleRexlv.Columns.Count));
                series900.HasDataLabels = true;

                Excel.Axis yAxis = (Excel.Axis)chart.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
                yAxis.MinimumScale = 0.0;
                yAxis.HasMajorGridlines = true;
                yAxis.MajorGridlines.Border.ColorIndex = 15;
                yAxis.TickLabels.NumberFormat = "0";

                chart.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
                Excel.Range rangeLT = worksheet.get_Range("H" + beginRow, "H" + beginRow);
                worksheet.Shapes.Item(worksheet.Shapes.Count).Top = (float)(double)rangeLT.Top;
                worksheet.Shapes.Item(worksheet.Shapes.Count).Left = (float)(double)rangeLT.Left;
                beginRow = beginRow + 16;
                if (nextBeginRow > beginRow)
                {
                    beginRow = nextBeginRow;
                }
            }
        }


        private void fillCellsDataSamplePect(DataTable tb, Excel.Worksheet worksheet, int rowIndex)
        {
            int colCount = tb.Columns.Count;
            int rowCount = tb.Rows.Count;
            worksheet.Cells[rowIndex, 1] = "无覆盖，弱覆盖采样点占比情况";
            rowIndex++;
            for (int c = 0; c < tbSamplePect.Columns.Count; c++)//表格标题
            {
                worksheet.Cells[rowIndex, c + 1] = tbSamplePect.Columns[c].ColumnName;
            }
            rowIndex++;
            for (int r = 0; r < rowCount; r++)//表格内容
            {
                for (int c = 0; c < colCount; c++)
                {
                    worksheet.Cells[rowIndex, c + 1] = tb.Rows[r][c];
                }
                rowIndex++;
            }
        }

        private int fillCellsDataSampleRexlv(DataTable tb,Excel.Worksheet worksheet,int rowIndex)
        {
            int colCount=tb.Columns.Count;
            worksheet.Cells[rowIndex,1]="采样点按电平范围分布(单位：个)";
            rowIndex++;
            for (int r = 0; r < colCount; r++)
			{
                worksheet.Cells[rowIndex+r, 1] = tbSampleRexlv.Columns[r].ColumnName;
                worksheet.Cells[rowIndex + r, 2] = tb.Rows[0][r];
			}
            rowIndex += 2;
            return rowIndex;
        }
    }
}
