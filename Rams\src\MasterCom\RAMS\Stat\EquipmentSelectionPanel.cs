﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Stat
{
    public partial class EquipmentSelectionPanel : UserControl
    {
        ToolStripDropDown parentDropDown;
        CheckedListBox effectCheckedListBox;
        public EquipmentSelectionPanel(ToolStripDropDown dropDown, CheckedListBox clbx)
        {
            InitializeComponent();
            this.effectCheckedListBox = clbx;
            this.parentDropDown = dropDown;
        }

        internal void FreshItems(List<CommonNoGisStatForm.EquipmentInfo> equipmentInfoList)
        {
            treeView.Nodes.Clear();
            foreach (CommonNoGisStatForm.EquipmentInfo eInfo in equipmentInfoList)
            {
                TreeNode node = new TreeNode();
                node.Text = eInfo.ToString();
                node.Tag = eInfo;
                node.Checked = true;
                treeView.Nodes.Add(node);
            }
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            effectCheckedListBox.Items.Clear();
            Dictionary<string, CommonNoGisStatForm.EquipmentInfo> dict = new Dictionary<string, CommonNoGisStatForm.EquipmentInfo>();
            foreach (TreeNode node in treeView.Nodes)
            {
                if (node.Checked)
                {
                    string key = ((CommonNoGisStatForm.EquipmentInfo)node.Tag).ToString();
                    if (!dict.ContainsKey(key))
                    {
                        dict.Add(key, (CommonNoGisStatForm.EquipmentInfo)node.Tag);
                    }
                }
            }
            foreach (string key in dict.Keys)
            {
                CommonNoGisStatForm.EquipmentInfo einfo = dict[key];
                effectCheckedListBox.Items.Add(einfo);
            }
            parentDropDown.Close();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            foreach (TreeNode node in treeView.Nodes)
            {
                node.Checked = false;
            }
        }
    }
}
