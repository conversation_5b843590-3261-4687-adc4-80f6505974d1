﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningCellQueryByFile : DIYReplayFileQuery
    {
        public LtePlanningCellQueryByFile(MainModel mainModel) : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            queryer = LtePlanningCellQueryer.Instance;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.IsValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;
            option.EventInclude = false;

            List<ColumnDefItem> cols = null;
            foreach (string item in queryer.Columns)
            {
                cols = InterfaceManager.GetInstance().GetColumnDefByShowName(item);
                option.SampleColumns.AddRange(cols);
            }

            return option;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            queryer.DoWithTestPoint(tp);
        }

        protected override void doPostReplayAction()
        {
            queryer.GetResultAfterQuery();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowForm();
        }

        private readonly LtePlanningCellQueryer queryer;
    }

    public class LtePlanningCellQueryByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public LtePlanningCellQueryByRegion(MainModel mainModel) : base(mainModel)
        {
            IncludeEvent = false;
            FilterEventByRegion = false;
            FilterSampleByRegion = false;
            queryer = LtePlanningCellQueryer.Instance;
            Columns = queryer.Columns;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool getCondition()
        {
            return queryer.IsValidCondition();
        }

        protected override void fireShowForm()
        {
            queryer.FireShowForm();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                foreach (TestPoint tp in testPointList)
                {
                    queryer.DoWithTestPoint(tp);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            queryer.GetResultAfterQuery();
        }

        private readonly LtePlanningCellQueryer queryer;
    }

    public class LtePlanningCellQueryer
    {
        public static LtePlanningCellQueryer Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LtePlanningCellQueryer();
                }
                return instance;
            }
        }

        public string Name
        {
            get { return "LTE规划站小区评估"; }
        }

        public List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "lte_TAC",
                    "lte_ECI",
                    "lte_SCell_CI",
                    "lte_EARFCN",
                    "lte_PCI",
                    "lte_RSRQ",
                    "lte_RSRP",
                    "lte_RSSI",
                    "lte_SINR",
                    "lte_NCell_EARFCN",
                    "lte_NCell_PCI",
                    "lte_NCell_RSRP",
                    "lte_NCell_RSRQ",
                    "lte_NCell_RSSI",
                    "lte_NCell_SINR",
                    "lte_PDCP_DL",
                };
            }
        }

        public MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22028, "LTE规划小区评估");
        }

        public bool IsValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new LtePlanningCellSettingForm();
            }
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = setForm.GetCondition();

            // load Excel
            try
            {
                if (!LtePlanningInfoManager.Instance.LoadFromXls(cond.XlsFileName))
                {
                    return false;
                }
                LtePlanningInfoManager.Instance.CalculateRadius();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            InitEvaluators();
            return true;
        }

        public void DoWithTestPoint(TestPoint tp)
        {
            LtePlanningTestPoint ptp = new LtePlanningTestPoint(tp);
            foreach (ILtePlanningCellEvaluator eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.DoWithTestPoint(ptp);
                }
            }
        }

        public void GetResultAfterQuery()
        {
            foreach (ILtePlanningCellEvaluator eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.CalcResult();
                }
            }
        }

        public void FireShowForm()
        {
            List<object> result = new List<object>();
            foreach (ILtePlanningCellEvaluator eva in evaluators)
            {
                if (eva.Enable)
                {
                    result.Add(eva.GetResult());
                }
            }

            foreach (ILtePlanningCellEvaluator eva in evaluators)
            {
                if (eva.Enable)
                {
                    eva.Clear();
                }
            }

            LtePlanningCellResultForm resultForm = mainModel.GetObjectFromBlackboard(typeof(LtePlanningCellResultForm).FullName) as LtePlanningCellResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LtePlanningCellResultForm(mainModel);
            }

            resultForm.FillData(result);
            if (!resultForm.Visible)
            {
                resultForm.Show(mainModel.MainForm);
            }
        }

        private void InitEvaluators()
        {
            evaluators = new List<ILtePlanningCellEvaluator>();
            evaluators.Add(new NotOpenedEvaluator(this.cond));
            evaluators.Add(new SampleLessCellEvaluator(this.cond));
            evaluators.Add(new WeakCoverageCellEvaluator(this.cond));
            evaluators.Add(new OverCoverageCellEvaluator(this.cond));
            evaluators.Add(new WeakQualityCellEvaluator(this.cond));
            evaluators.Add(new SummaryEvaluator(evaluators));
        }

        private LtePlanningCellQueryer()
        {
            this.mainModel = MainModel.GetInstance();
        }

        private readonly MainModel mainModel;
        private LtePlanningCellSettingForm setForm;
        private LtePlanningCellCondition cond;
        private List<ILtePlanningCellEvaluator> evaluators;
        private static LtePlanningCellQueryer instance;
    }
}
