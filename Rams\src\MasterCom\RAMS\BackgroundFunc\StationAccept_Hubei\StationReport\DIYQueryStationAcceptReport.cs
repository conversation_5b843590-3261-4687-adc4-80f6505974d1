﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyQueryStationAcceptReport : DIYSQLBase
    {
        public DiyQueryStationAcceptReport(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
        }

        public override string Name
        {
            get { return ""; }
        }

        readonly List<StationAcceptReportInfo> stationAcceptReportInfoDic = new List<StationAcceptReportInfo>();
        public List<StationAcceptReportInfo> StationAcceptReportInfoDic
        {
            get { return stationAcceptReportInfoDic; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22119, "单站验收报告");
        }

        protected override void query()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询单站验收报告...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询单站验收报告...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            string sql = string.Format(@"Select eNodeBID, distictName, btsName, coverType, cast(isBtsPassAccept as int), updateTime, errorInfo, strdesc from tb_lte_cell_acceptInfo Where id IN (Select Min(id) From tb_lte_cell_acceptInfo  Group by eNodeBID) and updateTime between '{0}' and '{1}'",
            condition.Periods[0].BeginTime, condition.Periods[0].EndTime);
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[8];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    StationAcceptReportInfo reportInfo = StationAcceptReportInfo.Fill(package.Content);
                    stationAcceptReportInfoDic.Add(reportInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected override void queryInThread(object o)
        {
            stationAcceptReportInfoDic.Clear();
            base.queryInThread(o);
            WaitBox.Close();
        }

        private void fireShowForm()
        {
            StationAcceptReportForm frm = MainModel.GetInstance().CreateResultForm(typeof(StationAcceptReportForm)) as StationAcceptReportForm;
            frm.FillData(stationAcceptReportInfoDic);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class StationAcceptReportInfo
    {
        /// <summary>
        /// 地市名
        /// </summary>
        public string DistictName { get; set; }
        /// <summary>
        /// 基站名
        /// </summary>
        public string BtsName { get; set; }
        /// <summary>
        /// 基站号
        /// </summary>
        public int ENodeBID { get; set; }
        /// <summary>
        /// 基站类型
        /// </summary>
        public string CoverType { get; set; }
        /// <summary>
        /// 基站是否通过验收
        /// </summary>
        public string IsBtsPassAccept { get; set; }
        /// <summary>
        /// 验收时间
        /// </summary>
        public string UpdateTime { get; set; }
        /// <summary>
        /// 不合格原因
        /// </summary>
        public string ErrorInfo { get; set; }
        /// <summary>
        /// 报表路径
        /// </summary>
        public string Strdesc { get; set; }

        public static StationAcceptReportInfo Fill(Content content)
        {
            StationAcceptReportInfo subFunc = new StationAcceptReportInfo();
            subFunc.ENodeBID = content.GetParamInt();
            subFunc.DistictName = content.GetParamString();
            subFunc.BtsName = content.GetParamString();
            subFunc.CoverType = content.GetParamString();
            subFunc.IsBtsPassAccept = content.GetParamInt() == 1 ? "通过" : "未通过";
            subFunc.UpdateTime = content.GetParamString();
            subFunc.ErrorInfo = content.GetParamString();
            subFunc.Strdesc = content.GetParamString();
            return subFunc;
        }
    }
}
