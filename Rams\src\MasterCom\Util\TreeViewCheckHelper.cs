﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.Util
{
    static class TreeViewCheckHelper
    {
        public static void AutoUpdateCheckState(TreeView treeView)
        {
            treeView.AfterCheck -= treeView_AfterCheck;
            treeView.AfterCheck += treeView_AfterCheck;
        }

        static void treeView_AfterCheck(object sender, TreeViewEventArgs e)
        {
            //通过鼠标或者键盘触发事件，防止修改节点的Checked状态时候再次进入
            if (e.Action == TreeViewAction.ByMouse || e.Action == TreeViewAction.ByKeyboard)
            {
                setCheckedChildNodes(e.Node);
                setCheckedParentNodes(e.Node);
            }
        }

        private static void setCheckedChildNodes(TreeNode node)
        {
            for (int i = 0; i < node.Nodes.Count; i++)
            {
                node.Nodes[i].Checked = node.Checked;
                setCheckedChildNodes(node.Nodes[i]);
            }
        }

        private static void setCheckedParentNodes(TreeNode node)
        {
            if (node.Parent == null)
            {
                return; //没有父节点返回
            } 
            if (node.Checked) //如果当前节点被选中，则设置所有父节点都被选中
            {
                node.Parent.ForeColor = System.Drawing.Color.Black;
                node.Parent.Checked = node.Checked;
                setCheckedParentNodes(node.Parent);
            }
            else //如果当前节点没有被选中，则当其父节点的子节点有一个被选中时，父节点被选中，否则父节点不被选中
            {
                bool checkedFlag = false;
                foreach (TreeNode tmpNode in node.Parent.Nodes)
                {
                    if (tmpNode.Checked)
                    {
                        checkedFlag = true;
                        break;
                    }
                }
                node.Parent.Checked = checkedFlag;
                setCheckedParentNodes(node.Parent);
            }
        }

    }
}
