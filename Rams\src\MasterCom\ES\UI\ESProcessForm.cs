﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Core;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.ES.UI
{
    public partial class ESProcessForm : BaseFormStyle
    {
        public ESProcessForm(MainModel model)
        {
            InitializeComponent();
            this.mainModel = model;
            this.tabControlMain.TabPages.RemoveAt(1);
            this.panelReason.Height = 218;
            InitLabelEvent();
            InitGridViewRows();

#if !ES_Performance
            this.tabControlMain.TabPages.RemoveAt(1);

            this.tableLayoutPanel1.Controls.Remove(panel1);
#endif
            miOpenConfig.Visible = false;
            miContinue.Visible = false;
        }
        public ESProcessForm()
        {
            InitializeComponent();

            miOpenConfig.Visible = true;
            miContinue.Visible = true;

            InitLabelEvent();
            InitGridViewRows();
        }

        // 内容区域标签事件响应
        private void InitLabelEvent()
        {
            this.label1.MouseDown += this.Label_MouseDown;
            this.label1.MouseUp += this.Label_MouseUp;
            this.label2.MouseDown += this.Label_MouseDown;
            this.label2.MouseUp += this.Label_MouseUp;
            this.label3.MouseDown += this.Label_MouseDown;
            this.label3.MouseUp += this.Label_MouseUp;
            this.label4.MouseDown += this.Label_MouseDown;
            this.label4.MouseUp += this.Label_MouseUp;

            this.label1.MouseLeave += this.Label_MouseLeave;
            this.label2.MouseLeave += this.Label_MouseLeave;
            this.label3.MouseLeave += this.Label_MouseLeave;
            this.label4.MouseLeave += this.Label_MouseLeave;
        }

        private void InitGridViewRows()
        {
            this.dataGridPreType.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridReason.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridPerformance.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridSolution.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
        }

        internal MainModel GetModal()
        {
            return mainModel;
        }

        public void appendReportInfo(string s, Color color, Color bgColor)
        {
            rptRichBox.SelectionColor = color;
            rptRichBox.SelectionBackColor = bgColor;
            rptRichBox.AppendText(s);
        }

        private void bgWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                Event evt = e.Argument as Event;
                DTData dtData = null;
                dtData = evt;

                DiySqlQueryType diysql = new DiySqlQueryType(mainModel, Convert.ToInt32(evt["LAC"].ToString()), Convert.ToInt32(evt["CI"].ToString()), evt.DateTime, evt.ID, "gsm", dtData.FileID, dtData.Time);
                diysql.Query();
                foreach (ESResult item in diysql.ResultList)
                {
                    doBgWorker(item.Type, 1);
                    doBgWorker(item.Analysis, 2);
                    doBgWorker(item.Solution, 3);
                }

            }
            catch (Exception ex)
            {
                bgWorker.ReportProgress(1, new OutputUnit("发生错误：" + ex.Message, Color.Blue, Color.Red));
            }

        }

        private void doBgWorker(string str, int type)
        {
            string[] items = str.Split('|');
            foreach (string item2 in items)
            {
                if (item2.Trim() != "")
                {
                    bgWorker.ReportProgress(1, new OutputUnit(item2, Color.Red, Color.Yellow, type));
                }
            }
        }

        private void bgWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is OutputUnit)
            {
                OutputUnit ou = e.UserState as OutputUnit;

                if(ou.showType>0)
                {
                    report2List(ou);
                }
            }
        }
        private void clearReportList()
        {
            dataGridPreType.Rows.Clear();
            dataGridReason.Rows.Clear();
            dataGridSolution.Rows.Clear();
            dataGridPerformance.Rows.Clear();
        }
        private void report2List(OutputUnit ou)
        {
            if(ou.showType==1)
            {
                int r = dataGridPreType.Rows.Add(1);
                dataGridPreType.Rows[r].Cells[0].Value = ou.text;
            }
            else if(ou.showType==2)
            {
                int r = dataGridReason.Rows.Add(1);
                dataGridReason.Rows[r].Cells[0].Value = ou.text;
            }
            else if(ou.showType==3)
            {
                int r = dataGridSolution.Rows.Add(1);
                dataGridSolution.Rows[r].Cells[0].Value = ou.text;
            }
            else if (ou.showType == 4)
            {
                int r = dataGridPerformance.Rows.Add(1);
                dataGridPerformance.Rows[r].Cells[0].Value = ou.text;
            }
        }


        public void FillReportList(ListView list)
        {
            clearReportList();
            foreach (string result in list.SelectedItems[0].SubItems[2].Text.ToString().Split('|'))
            {
                if (result.Trim() != string.Empty)
                {
                    int r = dataGridPreType.Rows.Add(1);
                    dataGridPreType.Rows[r].Cells[0].Value = result;
                }
            }
            foreach (string result in list.SelectedItems[0].SubItems[3].Text.ToString().Split('|'))
            {
                if (result.Trim() != string.Empty)
                {
                    int r = dataGridReason.Rows.Add(1);
                    dataGridReason.Rows[r].Cells[0].Value = result;
                }
            }
            foreach (string result in list.SelectedItems[0].SubItems[4].Text.ToString().Split('|'))
            {
                if (result.Trim() != string.Empty)
                {
                    int r = dataGridSolution.Rows.Add(1);
                    dataGridSolution.Rows[r].Cells[0].Value = result;
                }
            }
        }

        internal void OutputInfo(string p, Color color)
        {
            bgWorker.ReportProgress(1, new OutputUnit(p, color));
        }
        public void analyseEvent(Event evt)
        {
            if (!bgWorker.IsBusy)
            {
                DataGatherProxy.GetInstance().fireSetData(evt);
                rptRichBox.Text = "开始分析...\r\n";
                clearReportList();
                bgWorker.RunWorkerAsync(evt);
#if ES_Performance
                bgPerformanceWorker.RunWorkerAsync(evt);
#endif
            }
            else
            {
                XtraMessageBox.Show(this, "running!");
            }
        }

        private void ESProcessForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Visible = false;
            }
        }

        private void bgWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            //
        }

        private void miOpenConfig_Click(object sender, EventArgs e)
        {
            //
        }

        private void miContinue_Click(object sender, EventArgs e)
        {
            if (!bgContinueWorker.IsBusy)
            {
                bgContinueWorker.RunWorkerAsync();
            }

        }

        //类型??
        private void bgContinueWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            ESEngine engine = ESEngine.GetInstance(this);
            try
            {
                engine.ContinueRunOnNode(bgContinueWorker);                
            }
            catch (Exception ex)
            {
                bgContinueWorker.ReportProgress(1, new OutputUnit("发生错误：" + ex.Message, Color.Blue, Color.Red));
            }
        }

        private void bgContinueWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is OutputUnit)
            {
                OutputUnit ou = e.UserState as OutputUnit;
                if(ou.showType>0)
                {
                    report2List(ou);
                }
            }
        }

        private void bgContinueWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            //
        }

        internal void fireContinueGo()
        {
            miContinue.PerformClick();
        }

        internal void fireStepOver()
        {
            if (!bgStepOverWorker.IsBusy)
            {
                bgStepOverWorker.RunWorkerAsync();
            }
        }

        internal void fireStepIn()
        {
            if (!bgStepInWorker.IsBusy)
            {
                bgStepInWorker.RunWorkerAsync();
            }
        }

        //原因?
        private void bgStepOverWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            ESEngine engine = ESEngine.GetInstance(this);
            try
            {
                engine.StepOverRunOnNode(bgStepOverWorker);

                
                
            }
            catch (Exception ex)
            {
                bgStepOverWorker.ReportProgress(1, new OutputUnit("发生错误：" + ex.Message, Color.Blue, Color.Red));
            }
        }

        private void bgStepOverWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is OutputUnit)
            {
                OutputUnit ou = e.UserState as OutputUnit;
                appendReportInfo(ou.text, ou.color, ou.bgColor);
                if (ou.showType > 0)
                {
                    report2List(ou);
                }
            }
        }

        private void bgStepOverWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            //
        }

        //解决?
        private void bgStepInWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            ESEngine engine = ESEngine.GetInstance(this);
            try
            {
                engine.StepInRunOnNode(bgStepInWorker);
            }
            catch (Exception ex)
            {
                bgStepInWorker.ReportProgress(1, new OutputUnit("发生错误：" + ex.Message, Color.Blue, Color.Red));
            }
        }

        private void bgStepInWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is OutputUnit)
            {
                OutputUnit ou = e.UserState as OutputUnit;
                appendReportInfo(ou.text, ou.color, ou.bgColor);
                if (ou.showType > 0)
                {
                    report2List(ou);
                }
            }
        }

        private void bgStepInWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            //
        }

        //性能关联分析
        private void bgPerformanceWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                dataGridView1.DataSource = null;


                Event evt = e.Argument as Event;

                DTData dtData = null;

                dtData = evt;

                if(dtData.SampleTbName.Contains("gsm"))
                {
                    DiySqlQueryPerformance diysql = new DiySqlQueryPerformance(mainModel, Convert.ToInt32(evt["LAC"].ToString()), Convert.ToInt32(evt["CI"].ToString()), evt.DateTime, evt.ID,"gsm");
                    diysql.Query();
                    foreach (string item in diysql.ResultList)
                    {
                         bgPerformanceWorker.ReportProgress(1,new OutputUnit(item,Color.Red,Color.Yellow,4));
                    }


                    DiySqlQueryPerformanceDetail diysqldet = new DiySqlQueryPerformanceDetail(mainModel, Convert.ToInt32(evt["LAC"].ToString()), Convert.ToInt32(evt["CI"].ToString()), evt.DateTime, evt.ID, "gsm");
                    diysqldet.Query();
                    dataGridView1.DataSource = diysqldet.datatable;
                }
                else if (dtData.SampleTbName.Contains("td"))
                {
                    DiySqlQueryPerformance diysql = new DiySqlQueryPerformance(mainModel, Convert.ToInt32(evt["LAC"].ToString()), Convert.ToInt32(evt["CI"].ToString()), evt.DateTime, evt.ID, "td");
                    diysql.Query();
                    foreach (string item in diysql.ResultList)
                    {
                        bgPerformanceWorker.ReportProgress(1, new OutputUnit(item, Color.Red, Color.Yellow, 4));
                    }


                    DiySqlQueryPerformanceDetail diysqldet = new DiySqlQueryPerformanceDetail(mainModel, Convert.ToInt32(evt["LAC"].ToString()), Convert.ToInt32(evt["CI"].ToString()), evt.DateTime, evt.ID, "td");
                    diysqldet.Query();
                    dataGridView1.DataSource = diysqldet.datatable;
                }
                else
                {
                    //非gsm或td  ,或者.. 没获取到采样点表
                }
                
            }
            catch (Exception ex)
            {
                bgPerformanceWorker.ReportProgress(1, new OutputUnit("发生错误：" + ex.Message, Color.Blue, Color.Red));
            }
            dataGridView1.Dock = DockStyle.None;
            dataGridView1.Dock = DockStyle.Fill;
        }


        private void bgPerformanceWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is OutputUnit)
            {
                OutputUnit ou = e.UserState as OutputUnit;
                if (ou.showType > 0)
                {
                    report2List(ou);
                }
            }
        }

        private void bgPerformanceWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            //
        }

        // 记录鼠标按下的位置
        private int preMousePixY;
        private void Label_MouseDown(object sender, MouseEventArgs e)
        {
            Label label = sender as Label;
            label.MouseMove += this.Label_MouseMove;
            label.Cursor = Cursors.SizeNS;
            preMousePixY = e.Y;
        }

        private void Label_MouseUp(object sender, MouseEventArgs e)
        {
            Label label = sender as Label;
            label.MouseMove -= this.Label_MouseMove;
            label.Cursor = Cursors.Default;
            preMousePixY = 0;
        }

        // 鼠标上下移动即为调整该内容区域的高度
        private void Label_MouseMove(object sender, MouseEventArgs e)
        {
            int height = e.Y - preMousePixY;
            Label label = sender as Label;
            int count = this.tableLayoutPanel1.Controls.Count;
            // 最后一个区域高度不可调整
            if (label.Parent == this.tableLayoutPanel1.Controls[count - 1])
            {
                this.tableLayoutPanel1.Controls[count - 2].Height += height;
            }
            else
            {
                label.Parent.Height += height;
            }
            preMousePixY = e.Y;
        }

        // 修正鼠标按下后进行截屏无法恢复指针状态
        private void Label_MouseLeave(Object sender, EventArgs e)
        {
            this.Label_MouseUp(sender, null);
        }

    }
    public class OutputUnit
    {
        public string text { get; set; }
        public Color color { get; set; }
        public Color bgColor { get; set; }
        public int showType { get; set; } = 0;
        public OutputUnit(string s, Color color, Color bgColor)
        {
            this.text = s;
            this.color = color;
            this.bgColor = bgColor;
            showType = 0;
        }
        public OutputUnit(string s, Color color)
        {
            this.text = s;
            this.color = color;
            this.bgColor = Color.White;
            showType = 0;
        }
        public OutputUnit(string s, Color color, int showtype)
        {
            this.text = s;
            this.color = color;
            this.bgColor = Color.White;
            showType = showtype;
        }
        public OutputUnit(string s, Color color, Color bgColor, int showtype)
        {
            this.text = s;
            this.color = color;
            this.bgColor = bgColor;
            showType = showtype;
        }
    };
}