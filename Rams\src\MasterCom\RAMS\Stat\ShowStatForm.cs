﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Stat
{
    public class ShowStatForm : ShowFuncForm
    {
        public ShowStatForm(MainModel mm)
            : base(mm)
        { }
        StatHolderForm holderForm = null;
        protected override void showForm()
        {
            if (holderForm==null||holderForm.IsDisposed)
            {
                CommonNoGisStatForm.AllAreaNameDic.Clear();
                holderForm = new StatHolderForm(MainModel.MainForm);
            }
            if (!holderForm.Visible)
            {
                holderForm.Show(MainModel.MainForm);
            }
            holderForm.BringToFront();
            holderForm.WindowState = FormWindowState.Maximized;
            holderForm.FireShowFirstPage();
        }

        public override string Name
        {
            get { return "呈现报表窗口"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 11000, 11045, this.Name);
        }
    }
}
