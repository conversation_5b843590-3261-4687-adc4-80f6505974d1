using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.Util
{
    public interface IID
    {
        int ID
        {
            get;
            set;
        }
    }
    public class Snapshot<T> : IID
    {
        public static T Empty { get; }

        public Snapshot()
        {
            ValidPeriod = new TimePeriod();
            LastSnapshot = this;
        }

        public Snapshot(int id, TimePeriod ValidPeriod, T self)
        {
            ID = id;
            this.ValidPeriod = ValidPeriod;
            Value = self;
            LastSnapshot = this;
        }

        public int ID { get; set; }
        public TimePeriod ValidPeriod { get; set; }
        public T Value { get; set; }
        public T Previous
        {
            get { return PreviousSnapshot != null ? PreviousSnapshot.Value : Empty; }
        }

        public T Next
        {
            get { return NextSnapshot != null ? NextSnapshot.Value : Empty; }
        }

        public T Last
        {
            get { return LastSnapshot.Value; }
        }

        public T Current
        {
            get { return CurrentSnapshot != null ? CurrentSnapshot.Value : Empty; }
        }

        public Snapshot<T> PreviousSnapshot { get; set; }

        public Snapshot<T> NextSnapshot { get; set; }

        public Snapshot<T> LastSnapshot { get; set; }

        public Snapshot<T> CurrentSnapshot
        {
            get 
            { 
                if (LastSnapshot.ValidPeriod.EndTime == DateTime.MaxValue) 
                { 
                    return LastSnapshot; 
                } 
                return null; 
            }
        }

        public void Add(Snapshot<T> snapshot, bool isLast)
        {
            if (isLast)
            {
                TimePeriod tp = new TimePeriod(snapshot.ValidPeriod.BeginTime, snapshot.ValidPeriod.BeginTime.AddMinutes(1));
                for (Snapshot<T> temp = LastSnapshot; temp != null; temp = temp.PreviousSnapshot)
                {
                    if (temp.ValidPeriod.IsIntersect(tp))
                    {
                        temp.ValidPeriod =new TimePeriod(temp.ValidPeriod.BeginTime, snapshot.ValidPeriod.BeginTime);
                    }
                    else if (temp.ValidPeriod.EndTime <= tp.BeginTime)
                    {
                        break;
                    }
                }
                LastSnapshot.NextSnapshot = snapshot;
                snapshot.PreviousSnapshot = LastSnapshot;
                LastSnapshot = snapshot;
            }
            else
            {
                Add(snapshot);
            }
        }

        public void Add(Snapshot<T> snapshot)
        {
            if (snapshot.ID == ID && snapshot.ValidPeriod.BeginTime == ValidPeriod.BeginTime)
            {
                return;
            }
            Snapshot<T> temp = LastSnapshot;
            for (; temp != null; temp = temp.PreviousSnapshot)
            {
                if (temp.ValidPeriod.BeginTime <= snapshot.ValidPeriod.BeginTime)
                {
                    break;
                }
            }
            Snapshot<T> tempNext = null;
            if (temp != null)
            {
                tempNext = temp.NextSnapshot;
            }
            else
            {
                temp = LastSnapshot;
                while (temp.PreviousSnapshot != null)
                {
                    temp = temp.PreviousSnapshot;
                }
            }
            snapshot.NextSnapshot = tempNext;
            snapshot.PreviousSnapshot = temp;
            temp.NextSnapshot = snapshot;
            if (tempNext != null)
            {
                tempNext.PreviousSnapshot = snapshot;
                snapshot.LastSnapshot = tempNext.LastSnapshot;
            }
            else
            {
                LastSnapshot = snapshot;
            }
        }

        public T Get(DateTime time)
        {
            for (Snapshot<T> temp = LastSnapshot; temp != null; temp = temp.PreviousSnapshot)
            {
                if (temp.ValidPeriod.Contains(time))
                {
                    return temp.Value;
                }
            }
            return Empty;
        }

        public List<T> GetAll(TimePeriod period)
        {
            List<T> list = new List<T>();
            for (Snapshot<T> temp = LastSnapshot; temp != null; temp = temp.PreviousSnapshot)
            {
                if (temp.ValidPeriod.IsIntersect(period))
                {
                    list.Insert(0, temp.Value);
                }
                else if (temp.ValidPeriod.EndTime <= period.BeginTime)
                {
                    break;
                }
            }
            return list;
        }

        public void Fill(int id, int beginTime, int endTime)
        {
            ID = id;
            ValidPeriod = new TimePeriod(beginTime == 0 ? DateTime.MinValue : JavaDate.GetDateTimeFromMilliseconds(beginTime * 1000L), endTime == 0x7fffffff ? DateTime.MaxValue : JavaDate.GetDateTimeFromMilliseconds(endTime * 1000L));
        }
    }
}
