﻿namespace MasterCom.RAMS.ZTFunc.ZTCellSet
{
    partial class CellServiceConditionForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tlvCellsInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnRegion = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCode = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServiceSeconds = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServiceTimes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCategory = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumnCellBSC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.tlvCellsInfo)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // tlvCellsInfo
            // 
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnRegion);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellName);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellCode);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellBSC);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellLAC);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCellCI);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnServiceSeconds);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnServiceTimes);
            this.tlvCellsInfo.AllColumns.Add(this.olvColumnCategory);
            this.tlvCellsInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRegion,
            this.olvColumnCellName,
            this.olvColumnCellCode,
            this.olvColumnCellBSC,
            this.olvColumnCellLAC,
            this.olvColumnCellCI,
            this.olvColumnServiceSeconds,
            this.olvColumnServiceTimes,
            this.olvColumnCategory});
            this.tlvCellsInfo.ContextMenuStrip = this.contextMenuStrip;
            this.tlvCellsInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.tlvCellsInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlvCellsInfo.FullRowSelect = true;
            this.tlvCellsInfo.GridLines = true;
            this.tlvCellsInfo.HeaderWordWrap = true;
            this.tlvCellsInfo.IsNeedShowOverlay = false;
            this.tlvCellsInfo.Location = new System.Drawing.Point(0, 0);
            this.tlvCellsInfo.Name = "tlvCellsInfo";
            this.tlvCellsInfo.OwnerDraw = true;
            this.tlvCellsInfo.ShowGroups = false;
            this.tlvCellsInfo.Size = new System.Drawing.Size(885, 477);
            this.tlvCellsInfo.TabIndex = 13;
            this.tlvCellsInfo.UseCompatibleStateImageBehavior = false;
            this.tlvCellsInfo.View = System.Windows.Forms.View.Details;
            this.tlvCellsInfo.VirtualMode = true;
            this.tlvCellsInfo.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.tlvCellsInfo_MouseDoubleClick);
            // 
            // olvColumnRegion
            // 
            this.olvColumnRegion.HeaderFont = null;
            this.olvColumnRegion.Text = "区域";
            this.olvColumnRegion.Width = 100;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.AspectName = "";
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnCellCode
            // 
            this.olvColumnCellCode.HeaderFont = null;
            this.olvColumnCellCode.Text = "小区ID";
            this.olvColumnCellCode.Width = 80;
            // 
            // olvColumnCellLAC
            // 
            this.olvColumnCellLAC.HeaderFont = null;
            this.olvColumnCellLAC.Text = "LAC";
            // 
            // olvColumnCellCI
            // 
            this.olvColumnCellCI.HeaderFont = null;
            this.olvColumnCellCI.Text = "CI";
            // 
            // olvColumnServiceSeconds
            // 
            this.olvColumnServiceSeconds.AspectName = "";
            this.olvColumnServiceSeconds.HeaderFont = null;
            this.olvColumnServiceSeconds.Text = "占用时长(秒)";
            this.olvColumnServiceSeconds.Width = 90;
            // 
            // olvColumnServiceTimes
            // 
            this.olvColumnServiceTimes.AspectName = "";
            this.olvColumnServiceTimes.HeaderFont = null;
            this.olvColumnServiceTimes.Text = "占用次数";
            this.olvColumnServiceTimes.Width = 70;
            // 
            // olvColumnCategory
            // 
            this.olvColumnCategory.AspectName = "";
            this.olvColumnCategory.HeaderFont = null;
            this.olvColumnCategory.Text = "小区类别";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCloseAll,
            this.toolStripMenuItem1,
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip1";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 76);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(129, 22);
            this.miCloseAll.Text = "全部合并";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(129, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // olvColumnCellBSC
            // 
            this.olvColumnCellBSC.HeaderFont = null;
            this.olvColumnCellBSC.Text = "BSC";
            // 
            // CellServiceConditionForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(885, 477);
            this.Controls.Add(this.tlvCellsInfo);
            this.Name = "CellServiceConditionForm";
            this.Text = "小区占用情况";
            ((System.ComponentModel.ISupportInitialize)(this.tlvCellsInfo)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView tlvCellsInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnRegion;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCode;
        private BrightIdeasSoftware.OLVColumn olvColumnServiceSeconds;
        private BrightIdeasSoftware.OLVColumn olvColumnServiceTimes;
        private BrightIdeasSoftware.OLVColumn olvColumnCategory;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellBSC;


    }
}