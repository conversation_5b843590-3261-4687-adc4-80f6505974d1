using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.Model
{
    [Serializable]
    public class ChildFormConfig
    {
        public string AssemblyName { get; set; }
        public string TypeName { get; set; }
        public Dictionary<string, object> Param { get; set; }
        public string Text { get; set; }
        public string ImageFilePath { get; set; }
        public Image Image
        {
            get
            {
                if (image == null)
                {
                    if (ImageFilePath != null)
                    {
                        if (System.IO.File.Exists(ImageFilePath))
                        {
                            image = Image.FromFile(ImageFilePath);
                        }
                        else
                        {
                            image = defaultImage;

                        }
                    }
                    else
                    {
                        image = defaultImage;
                    }
                }
                return image;
            }
            set { image = value; }
        }

        public FormWindowState WindowState { get; set; }
        public Point Location { get; set; } = new Point(-1, -1);
        public Size Size { get; set; }
        public Form ChildForm
        {
            get { return instance; }
            set { instance = value; }
        }
        public bool IsTopFront { get; set; } = false;
        public Point BeforeLocation { get; set; } = new Point(-1, -1);

        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(ChildFormConfig).Name))
            {
                AssemblyName = configFile.GetItemValue(item, "AssemblyName") as string;
                this.TypeName = configFile.GetItemValue(item, "TypeName") as string;
                Text = configFile.GetItemValue(item, "Text") as string;
                ImageFilePath = configFile.GetItemValue(item, "ImageFilePath") as string;
                AssemblyName = configFile.GetItemValue(item, "AssemblyName") as string;
                object value = null;
                value = configFile.GetItemValue(item, "WindowState");
                if (value != null)
                {
                    WindowState = (FormWindowState)(int)value;
                }
                Point location = new Point();
                value = configFile.GetItemValue(item, "LocationX");
                if (value != null)
                {
                    location.X = (int)value;
                }
                value = configFile.GetItemValue(item, "LocationY");
                if (value != null)
                {
                    location.Y = (int)value;
                }
                Location = location;

                Size size = new Size();
                value = configFile.GetItemValue(item, "SizeWidth");
                if (value != null)
                {
                    size.Width = (int)value;
                }
                value = configFile.GetItemValue(item, "SizeHeight");
                if (value != null)
                {
                    size.Height = (int)value;
                }
                Size = size;

                value = configFile.GetItemValue(item, "IsTopFront");
                if (value != null)
                {
                    IsTopFront = (bool)value;
                }

                getBeforeLocation(configFile, item);
                Param = configFile.GetItemValue(item, "Param") as Dictionary<string, object>;
                return this;
            }
            return null;
        }

        private void getBeforeLocation(XmlConfigFile configFile, XmlElement item)
        {
            Point beforeLocation = new Point();
            object value = configFile.GetItemValue(item, "BeforeLocationX");
            if (value != null)
            {
                beforeLocation.X = (int)value;
            }
            value = configFile.GetItemValue(item, "BeforeLocationY");
            if (value != null)
            {
                beforeLocation.Y = (int)value;
            }
            BeforeLocation = beforeLocation;
        }

        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is ChildFormConfig)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddTypeAttribute(item, typeof(ChildFormConfig));
                configFile.AddItem(item, "AssemblyName", AssemblyName);
                configFile.AddItem(item, "TypeName", TypeName);
                configFile.AddItem(item, "ImageFilePath", ImageFilePath);
                if (instance != null)
                {
                    addInstanceItem(configFile, item);
                }
                else if (Text != null && Param != null)
                {
                    configFile.AddItem(item, "Text", Text.Trim().Contains(ChildFormConfig.FormTitleFileNameMark) ? Text.Trim().Substring(0, Text.IndexOf(ChildFormConfig.FormTitleFileNameMark)) : Text.Trim());
                    configFile.AddItem(item, "WindowState", (int)WindowState);
                    configFile.AddItem(item, "LocationX", Location.X);
                    configFile.AddItem(item, "LocationY", Location.Y);
                    configFile.AddItem(item, "SizeWidth", Size.Width);
                    configFile.AddItem(item, "SizeHeight", Size.Height);
                    configFile.AddItem(item, "IsTopFront", IsTopFront);

                    configFile.AddItem(item, "BeforeLocationX", BeforeLocation.X);
                    configFile.AddItem(item, "BeforeLocationY", BeforeLocation.Y);
                    configFile.AddItem(item, "Param", Param);
                }
                return item;
            }
            return null;
        }

        private void addInstanceItem(XmlConfigFile configFile, XmlElement item)
        {
            configFile.AddItem(item, "Text", instance.Text.Trim().Contains(ChildFormConfig.FormTitleFileNameMark) ? instance.Text.Trim().Substring(0, instance.Text.IndexOf(ChildFormConfig.FormTitleFileNameMark)) : instance.Text.Trim());
            if (instance.Visible)
            {
                configFile.AddItem(item, "WindowState", (int)instance.WindowState);
            }
            else
            {
                configFile.AddItem(item, "WindowState", (int)WindowState);
            }
            configFile.AddItem(item, "LocationX", instance.Location.X);
            configFile.AddItem(item, "LocationY", instance.Location.Y);
            configFile.AddItem(item, "SizeWidth", instance.Size.Width);
            configFile.AddItem(item, "SizeHeight", instance.Size.Height);
            configFile.AddItem(item, "IsTopFront", instance.MdiParent == null);
            if (instance is ChildForm)
            {
                Point point = (instance as ChildForm).BeforePopPos;
                configFile.AddItem(item, "BeforeLocationX", point.X);
                configFile.AddItem(item, "BeforeLocationY", point.Y);
                configFile.AddItem(item, "Param", (instance as ChildForm).Param);
            }
        }

        private static Image defaultImage = Image.FromFile(Application.StartupPath + @"\images\DefaultApp.png");

        private Image image;

        [NonSerialized]
        private Form instance;

        public static string FormTitleFileNameMark { get; set; } = " - ";
    }
}
