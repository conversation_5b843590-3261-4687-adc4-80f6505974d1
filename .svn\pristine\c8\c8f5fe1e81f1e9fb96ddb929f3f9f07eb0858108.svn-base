﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace MasterCom.RAMS.KPI_Statistics
{
    public static class FileOpHelper
    {
        private static double curMark = 1;

        public static string CreateVirtualFile(string FileFullPath)
        {
            string souceFileNameExpend = FileFullPath.Substring(FileFullPath.LastIndexOf(".") + 1, FileFullPath.Length - FileFullPath.LastIndexOf(".") - 1);
            string souceFilePath = FileFullPath.Substring(0, FileFullPath.LastIndexOf("\\"));

            string FileTempPath = souceFilePath + "\\FileTemp" + DateTime.Now.Ticks + "." + souceFileNameExpend;

            while (File.Exists(FileTempPath))
            {
                FileTempPath = souceFilePath + "\\FileTemp" + DateTime.Now.Ticks + curMark + "." + souceFileNameExpend;
                curMark++;
            }

            File.Copy(FileFullPath, FileTempPath);

            return FileTempPath;
        }

        public static bool Delete(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return true;

                File.Delete(filePath);

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
