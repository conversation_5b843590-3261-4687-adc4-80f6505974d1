﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRSinrCondition
    {
        public NRSinrCondition()
        {
        }

        public NRSinrCondition(int oper, float sinr)
        {
            SINRType = oper;
            SINR = sinr;
        }

        public int SINRType { get; set; }
        public float SINR { get; set; }
        public string KPIName { get; set; }

        public List<NRSinrConditionItem> Conditions
        {
            get;
            private set;
        } = new List<NRSinrConditionItem>();

        public string ConName
        {
            get
            {
                return "SINR " + getType(SINRType) + SINR;
            }
        }

        private string getType(int type)
        {
            switch (type)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                default:
                    return "";
            }
        }

        public bool IsEligibility(float sinrValue)
        {
            switch (SINRType)
            {
                case 0:
                    if (sinrValue < SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 1:
                    if (sinrValue > SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 2:
                    if (sinrValue <= SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 3:
                    if (sinrValue >= SINR)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                default:
                    return false;
            }
        }
    }

    public class NRSinrConditionItem
    {
        public NRSinrConditionItem(int type, float first, float second, string kpiName)
        {
            OperType = type;
            ExtremumFirst = first;
            ExtremumSecond = second;
            KPIName = kpiName;
        }

        public int OperType { get; set; }
        public float ExtremumFirst { get; set; }
        public float ExtremumSecond { get; set; }
        public string KPIName { get; set; }

        public string Name
        {
            get
            {
                if (OperType == (int)NRSinrOperatorsType.GreaterThanOrEqual)
                {
                    return ExtremumFirst + " < " + KPIName + " ≤ " + ExtremumSecond;
                }
                else
                {
                    return KPIName + getType() + ExtremumFirst;
                }
            }
        }

        private string getType()
        {
            switch (OperType)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                case 3:
                    return "≤";
                default:
                    return "";
            }
        }
    }

    enum NRSinrOperatorsType
    {
        LessThan = 0,
        GreaterThan = 1,
        GreaterThanOrEqual = 2,
        LessThanOrEqual = 3,
    }
}
