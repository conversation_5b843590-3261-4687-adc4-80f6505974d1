﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    public class ZTCQTRendering : DIYSQLBase
    {
        public ZTCQTRendering(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
        }

        public override string Name
        {
            get { return "自定义"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        readonly List<ZTCQTRenderingInfo> ztCQTTempInfoDic = new List<ZTCQTRenderingInfo>();
        public List<ZTCQTRenderingInfo> ZTCQTTempInfoDic
        {
            get { return ztCQTTempInfoDic; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询自定义...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询自定义...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            string sql = string.Format(@"Select * From tb_bt_sample_rt Where Time between '{0}' and '{1}'",
            condition.Periods[0].BeginTime, condition.Periods[0].EndTime);
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ZTCQTRenderingInfo info = ZTCQTRenderingInfo.Fill(package.Content);
                    ztCQTTempInfoDic.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        protected override void queryInThread(object o)
        {
            ztCQTTempInfoDic.Clear();
            base.queryInThread(o);
            WaitBox.Close();
        }

        private void fireShowForm()
        {
            ZTCQTRenderingForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTCQTRenderingForm)) as ZTCQTRenderingForm;
            frm.FillData(ztCQTTempInfoDic);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class ZTCQTRenderingInfo
    {
        public string Time { get; set; } = "";
        public string MAC { get; set; } = "";
        public float RSSI { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public bool Selected { get; set; } = false;

        public static ZTCQTRenderingInfo Fill(Content content)
        {
            ZTCQTRenderingInfo info = new ZTCQTRenderingInfo();
            info.Time = content.GetParamString();
            info.MAC = content.GetParamString();
            info.RSSI = content.GetParamFloat();
            info.Longitude = double.Parse(content.GetParamInt().ToString()) / 10000000;
            info.Latitude = double.Parse(content.GetParamInt().ToString()) / 10000000;
            return info;
        }
    }
}
