﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaKpiSearchByCountry : AreaKpiQueryDirectly
    {
        protected Dictionary<AreaBase, Dictionary<AreaBase, bool>> countryAreaDic = new Dictionary<AreaBase, Dictionary<AreaBase, bool>>();

        protected Dictionary<int, Dictionary<int, AreaBase>> curTypePairMap = new Dictionary<int, Dictionary<int, AreaBase>>();

        public AreaKpiSearchByCountry()
        {
        }

        private MasterCom.MTGis.DbRect getRect()
        {
            if (curTypePairMap.Count == 0)
                return null;

            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect();
            rect.x1 = rect.y1 = 900;
            rect.x2 = rect.y2 = 0;

            foreach (Dictionary<int, AreaBase> areaDic in curTypePairMap.Values)
            {
                foreach (AreaBase area in areaDic.Values)
                {
                    rect.x1 = Math.Min(area.Bounds.x1, rect.x1);
                    rect.x2 = Math.Max(area.Bounds.x2, rect.x2);
                    rect.y1 = Math.Min(area.Bounds.y1, rect.y1);
                    rect.y2 = Math.Max(area.Bounds.y2, rect.y2);
                }
            }
            return rect;
        }

        public new void SetTypes(Dictionary<int, Dictionary<int, AreaBase>> typeIdMap)
        {
            typePairMap = typeIdMap;
            makeGroup();
        }

        private void makeGroup()
        {
            ZTAreaManager mng = ZTAreaManager.Instance;
            AreaRank countryRank = mng.GetRank(1);
            foreach (Dictionary<int, AreaBase> idAreaDic in typePairMap.Values)
            {
                foreach (AreaBase area in idAreaDic.Values)
                {
                    AreaBase country = area.GetSectionArea(countryRank);
                    if (country == null)
                    {
                        continue;
                    }

                    Dictionary<AreaBase, bool> areaDic;
                    if (!countryAreaDic.TryGetValue(country, out areaDic))
                    {
                        areaDic = new Dictionary<AreaBase, bool>();
                        countryAreaDic[country] = areaDic;
                    }
                    areaDic[area] = true;
                }
            }
        }

        protected new AreaBase getArea(int areaType, int areaID)
        {
            AreaBase rtArea;
            Dictionary<int, AreaBase> idArea;
            if (!curTypePairMap.TryGetValue(areaType, out idArea))
            {
                return null;
            }
            if (!idArea.TryGetValue(areaID, out rtArea))
            {
                return null;
            }
            return rtArea;
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            addRegion(package);
            AddDIYEndOpFlag(package);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam(strType);


            Dictionary<int, bool> areaIdDic = new Dictionary<int, bool>();
            foreach (Dictionary<int, AreaBase> pairSet in curTypePairMap.Values)
            {
                foreach (int id in pairSet.Keys)
                {
                    areaIdDic[id] = true;
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (int id in areaIdDic.Keys)
            {
                sb.Append(id.ToString() + ",");
            }
            if (sb.Length > 0 && sb.Length < 5000)
            {
                package.Content.AddParam((byte)OpOptionDef.InSelect);
                package.Content.AddParam("0,26,1");
                package.Content.AddParam(sb.Remove(sb.Length - 1, 1).ToString());
            }
        }

        private void setCurTypes(Dictionary<AreaBase, bool> areaDic)
        {
            curTypePairMap.Clear();
            foreach (AreaBase area in areaDic.Keys)
            {
                Dictionary<int, AreaBase> idDic;
                if (!curTypePairMap.TryGetValue(area.AreaTypeID, out idDic))
                {
                    idDic = new Dictionary<int, AreaBase>();
                    curTypePairMap[area.AreaTypeID] = idDic;
                }
                idDic[area.AreaID] = area;
            }


            StringBuilder sb = new StringBuilder();
            foreach (int typeId in curTypePairMap.Keys)
            {
                sb.Append(typeId);
                sb.Append(',');
            }
            strType = sb.ToString().TrimEnd(',');
        }

        protected override void queryPeriodInfo(MasterCom.Util.TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            foreach (Dictionary<AreaBase, bool> areaDic in countryAreaDic.Values)
            {
                setCurTypes(areaDic);
                rect = getRect();

                //stat
                isQueringEvent = false;
                preparePackageBasicContent(clientProxy.Package, period, reservedParams);
                preparePackageNeededInfo_StatImg(clientProxy.Package, reservedParams);
                clientProxy.Send();
                recieveInfo_ImgGrid(clientProxy);

                if (this.evtIDSvrIDDic.Count > 0)
                {
                    //event
                    isQueringEvent = true;
                    preparePackageBasicContent(clientProxy.Package, period, reservedParams);
                    preparePackageNeededInfo_Event(clientProxy.Package);
                    clientProxy.Send();
                    recieveInfo_Event(clientProxy);
                }
                afterRecieveOnePeriodData(period);
            }
        }

        protected override void afterRecieveOnePeriodData(params object[] reservedParams)
        {
            foreach (AreaKPIDataGroup<AreaBase> group in AreaKpiMap.Values)
            {
                group.FinalMtMoGroup();
            }

            foreach (Dictionary<int, AreaBase> idAreaDic in curTypePairMap.Values)
            {
                foreach (AreaBase area in idAreaDic.Values)
                {
                    CKpiValue kpi;
                    if (!KpiValueDic.TryGetValue(area, out kpi))
                    {
                        kpi = new CKpiValue(area);
                        KpiValueDic[area] = kpi;
                    }
                    AreaKPIDataGroup<AreaBase> group;
                    if (!AreaKpiMap.TryGetValue(area, out group))
                    {
                        group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                        AreaKpiMap[area] = group;
                    }
                    kpi.CalcValue(group, tmplaColumns);
                }
            }

            AreaKpiMap.Clear();
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //
        }
    }
}
