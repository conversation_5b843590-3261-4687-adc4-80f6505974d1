﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints
{
    public class PointInfo
    {
        public override string ToString()
        {
            if (string.IsNullOrEmpty(Name))
            {
                return string.Empty;
            }
            return Name;
        }
        public string Name
        { get; set; }

        public double Longitude
        {
            get;
            set;
        }

        public double Latitude
        {
            get;
            set;
        }
    }
}
