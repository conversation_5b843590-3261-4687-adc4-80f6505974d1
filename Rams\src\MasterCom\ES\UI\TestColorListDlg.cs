﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class TestColorListDlg : BaseFormStyle
    {
        public TestColorListDlg()
        {
            InitializeComponent();
            this.TopMost = true;
        }

        private void TestColorListDlg_Load(object sender, EventArgs e)
        {
            for(int i=0;i<137;i++)
            {
                Color color = ColorSequenceSupplier.getColor(i);
                ListViewItem lvi = new ListViewItem();
                lvi.Text = color.Name;
                lvi.BackColor = color;
                lvi.UseItemStyleForSubItems = false;
                listView1.Items.Add(lvi);
            }
            
        }
    }
}