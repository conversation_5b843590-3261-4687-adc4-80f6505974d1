﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntMRAna : ZTLteAntMR
    {
        public ZTLteAntMRAna(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "LTE MR覆盖分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28008, this.Name);
        }

        string strCityName = "";
      

        readonly Dictionary<int, CellMRData> cellParaDataDic = new Dictionary<int, CellMRData>();

        protected override void query()
        {
            setVarBeforQuery();
            InitRegionMop2();

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                if (condition.IsByRound)
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", condition.ByRoundYear, condition.ByRoundRound))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 23:59:59", condition.ByRoundYear, condition.ByRoundRound)).AddMonths(1).AddDays(-1)) / (1000L));
                }
                else
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 00:00:00", condition.Periods[0].BeginTime))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 23:59:59", condition.Periods[0].EndTime.AddDays(-1)))) / (1000L));
                }

                cellParaDataDic.Clear();
                MainModel.ClearDTData();
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                //WaitBox.Show("读取MR数据处理...", doWithCellMRData)
                //WaitBox.Show("读取小区性能数据...", doWithCellParaData)
                WaitBox.Show("读取状态库天线信息...", doWithAntCfgData);
                WaitBox.Show("分类获取MR数据...", doWithCellMRByTypeData);
                //WaitBox.Show("读取天线权值数据...", doWithParaData)
                WaitBox.Show("联合数据处理...", AnaCellAngleData);
                dealMainUtranCellSample();
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                clearData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            // Method intentionally left empty.
        }

        /// <summary>
        /// 分析小区的角度数组，进行平滑化处理
        /// </summary>
        private void AnaCellAngleData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = antCfgParaSDic.Count;
            int iNum = 0;//用于进度计数
            int idx = 0; //用于序号编排

            foreach (int ieci in antCfgParaSDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                WaitBox.ProgressPercent = (int)(100 * ((++iNum * 1.0) / iCount));

                AntCfgSub antCfgSub = antCfgParaSDic[ieci];
                if (mapOp2 != null && mapOp2.Parts.Count > 0 && !mapOp2.CheckPointInRegion(antCfgSub.fLongitude, antCfgSub.fLatitude))
                    continue;

                idx = addCellParaDataDic(idx, ieci, antCfgSub);
            }
            WaitBox.Close();
        }

        private int addCellParaDataDic(int idx, int ieci, AntCfgSub antCfgSub)
        {
            CellMRData mrData = new CellMRData();
            mrData.iEci = ieci;
            mrData.index = ++idx;
            mrData.strcityname = strCityName;
            mrData.cellname = antCfgSub.strCellName;
            mrData.cellNameEn = antCfgSub.strCellNameEn;
            mrData.iTac = antCfgSub.iTAC;
            mrData.iangle_dir = (int)antCfgSub.方向角;

            LTECell lteMainCell = CellManager.GetInstance().GetNearestLTECellByTACCI(DateTime.Now, antCfgSub.iTAC, ieci, antCfgSub.fLongitude, antCfgSub.fLatitude);
            if (lteMainCell != null)
            {
                mrData.dLongitude = lteMainCell.Longitude;
                mrData.dLatitude = lteMainCell.Latitude;
            }
            else
            {
                mrData.dLongitude = antCfgSub.fLongitude;
                mrData.dLatitude = antCfgSub.fLatitude;
            }

            int iNewEci = (mrData.iEci / 256) * 256 + ((mrData.iEci % 256) % 10);
            ZTLteAntMR.CellParaData tmpData = new CellParaData();
            AnaCellMrData(ref tmpData, iNewEci);
            mrData.fillValue(tmpData);

            int[,] taAoa90 = mrData.AnaRttdAoa90;
            AnaRttdAoaArray ary = new AnaRttdAoaArray();
            ary.AnaRttdAoa90 = taAoa90;
            int[] dirArray = getDirArray(ary);
            mrData.maxTaArray = getDirMaxTa(ary);
            int[] subValue = calcMainRegion(dirArray);
            mrData.iMainDir = subValue[1] == -1 ? -1 : subValue[1] * 5 + 2; //步长为5，除以2

            if (mrData.iMainDir != -1)
            {
                mrData.iTotalNum = subValue[2];
                mrData.iDirNum = calcDirNum(dirArray);
                List<AntMRInfo> sideList = calcMRInfo(dirArray, mrData.maxTaArray, mrData.iTotalNum);
                mrData.iSideNum = sideList.Count;
                FillAngleDataByRegionAbs(ref mrData, dirArray);//各区间数据填值
                mrData.strIsMrCell = "是";
                mrData.iDirDiff = ZTAntFuncHelper.CalcAntDir(mrData.iMainDir, mrData.iangle_dir);
            }

            LongLat ll = new LongLat();
            ll.fLongitude = (float)(mrData.dLongitude);
            ll.fLatitude = (float)(mrData.dLatitude);
            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(mrData.maxTaArray, ref iMaxValue, ref iMinValue);
            mrData.mrAoaList = ZTAntFuncHelper.getCellMrCover(ll, mrData.maxTaArray, iMaxValue, iMinValue, 0);

            if (mrData.strIsMrCell == "是" && !cellParaDataDic.ContainsKey(ieci))//鉴于巡检，仅取能匹配小区
            {
                cellParaDataDic.Add(ieci, mrData);
            }

            return idx;
        }

        /// <summary>
        /// 绝对方位角（华为、大唐、诺基亚）
        /// </summary>
        private void FillAngleDataByRegionAbs(ref CellMRData mrData, int[] dirArray)
        {
            //主覆盖方向
            mrData.iRel30Num = calcRegionSample(dirArray, mrData.iMainDir, 30, 1, 0)
                             + calcRegionSample(dirArray, mrData.iMainDir, 30, 2, 0);
            mrData.iRel60Num = calcRegionSample(dirArray, mrData.iMainDir, 60, 1, 0)
                             + calcRegionSample(dirArray, mrData.iMainDir, 60, 2, 0);
            mrData.iRel90Num = calcRegionSample(dirArray, mrData.iMainDir, 90, 1, 0)
                             + calcRegionSample(dirArray, mrData.iMainDir, 90, 2, 0);
            mrData.iRelMinus60To120Num = calcRegionSample(dirArray, mrData.iMainDir, 60, 1, 60);
            mrData.iRelPlus60To120Num = calcRegionSample(dirArray, mrData.iMainDir, 60, 2, 60);
            mrData.iRelBack60Num = calcRegionSample(dirArray, (mrData.iMainDir + 180) % 360, 60, 1, 0)
                                 + calcRegionSample(dirArray, (mrData.iMainDir + 180) % 360, 60, 2, 0);
            mrData.iRelBack15Num = calcRegionSample(dirArray, (mrData.iMainDir + 180) % 360, 15, 1, 0)
                                 + calcRegionSample(dirArray, (mrData.iMainDir + 180) % 360, 15, 2, 0);

            mrData.dAbs30MaxDist = getRegionMaxTa(mrData.maxTaArray, mrData.iMainDir, 30, 3, 0);
            mrData.dAbs60MaxDist = getRegionMaxTa(mrData.maxTaArray, mrData.iMainDir, 60, 3, 0);
            mrData.dAbs90MaxDist = getRegionMaxTa(mrData.maxTaArray, mrData.iMainDir, 90, 3, 0);
            mrData.dAbsPlus60To120MaxDist = getRegionMaxTa(mrData.maxTaArray, mrData.iMainDir, 60, 1, 60);
            mrData.dAbsMinus60To120MaxDist = getRegionMaxTa(mrData.maxTaArray, mrData.iMainDir, 60, 2, 60);
            mrData.dAbsBack60MaxDist = getRegionMaxTa(mrData.maxTaArray, (mrData.iMainDir + 180) % 360, 60, 3, 0);
            mrData.dAbsBack15MaxDist = getRegionMaxTa(mrData.maxTaArray, (mrData.iMainDir + 180) % 360, 15, 3, 0);

            AnaRttdAoaArray ary = new AnaRttdAoaArray();
            ary.AnaRttdAoa90 = mrData.AnaRttdAoa90;
            mrData.dAbs30AvgDist = getRegionAvgTa(ary, mrData.iMainDir, 30, 3, 0,44);
            mrData.dAbs60AvgDist = getRegionAvgTa(ary, mrData.iMainDir, 60, 3, 0, 44);
            mrData.dAbs90AvgDist = getRegionAvgTa(ary, mrData.iMainDir, 90, 3, 0, 44);
            mrData.dAbsPlus60To120AvgDist = getRegionAvgTa(ary, mrData.iMainDir, 60, 1, 60, 44);
            mrData.dAbsMinus60To120AvgDist = getRegionAvgTa(ary, mrData.iMainDir, 60, 2, 60, 44);
            mrData.dAbsBack60AvgDist = getRegionAvgTa(ary, (mrData.iMainDir + 180) % 360, 60, 3, 0, 44);
            mrData.dAbsBack15AvgDist = getRegionAvgTa(ary, (mrData.iMainDir + 180) % 360, 15, 3, 0, 44);

            //工参方向
            mrData.iAbs30Num = calcRegionSample(dirArray, mrData.iangle_dir, 30, 1, 0)
                             + calcRegionSample(dirArray, mrData.iangle_dir, 30, 2, 0);
            mrData.iAbs60Num = calcRegionSample(dirArray, mrData.iangle_dir, 60, 1, 0)
                             + calcRegionSample(dirArray, mrData.iangle_dir, 60, 2, 0);
            mrData.iAbs90Num = calcRegionSample(dirArray, mrData.iangle_dir, 90, 1, 0)
                             + calcRegionSample(dirArray, mrData.iangle_dir, 90, 2, 0);
            mrData.iAbsMinus60To120Num = calcRegionSample(dirArray, mrData.iangle_dir, 60, 1, 60);
            mrData.iAbsPlus60To120Num = calcRegionSample(dirArray, mrData.iangle_dir, 60, 2, 60);
            mrData.iAbsBack60Num = calcRegionSample(dirArray, (mrData.iangle_dir + 180) % 360, 60, 1, 0)
                                 + calcRegionSample(dirArray, (mrData.iangle_dir + 180) % 360, 60, 2, 0);
            mrData.iAbsBack15Num = calcRegionSample(dirArray, (mrData.iangle_dir + 180) % 360, 15, 1, 0)
                                 + calcRegionSample(dirArray, (mrData.iangle_dir + 180) % 360, 15, 2, 0);
        }

        #region MR覆盖分析判断算法

        /// <summary>
        /// 获取60度、10度的主方位角
        /// </summary>
        public static int[] calcMainRegion(int[] dirArray)
        {
            int[] maxRegion = new int[3];

            int iAoa = -1;
            int iMaxNum = 0;
            int iTotalNum = 0;
            for (int i = 0; i < 72; i++)
            {
                iTotalNum += dirArray[i];
                int iNum = dirArray[i] + dirArray[(i + 1) % 72] + dirArray[(i + 2) % 72] + dirArray[(i + 3) % 72] + dirArray[(i + 4) % 72] + dirArray[(i + 5) % 72]
                         + dirArray[(i + 6) % 72] + dirArray[(i + 7) % 72] + dirArray[(i + 8) % 72] + dirArray[(i + 9) % 72] + dirArray[(i + 10) % 72] + dirArray[(i + 11) % 72];
                if (iNum > iMaxNum)
                {
                    iAoa = i;
                    iMaxNum = iNum;
                }
            }
            maxRegion[0] = iAoa;

            int iSecAoA = -1;
            int iSecNum = 0;
            if (iAoa != -1)
            {
                for (int i = iAoa; i < iAoa + 12; i++)
                {
                    int iNum = dirArray[i % 72] + dirArray[(i + 1) % 72];
                    if (iNum > iSecNum)
                    {
                        iSecAoA = i % 72;
                        iSecNum = iNum;
                    }
                }
            }
            maxRegion[1] = iSecAoA;
            maxRegion[2] = iTotalNum;

            return maxRegion;
        }

        /// <summary>
        /// 统计各分段采样点数
        /// </summary>
        /// <param name="iSort">1为反向,2为正向</param>
        /// <param name="ioffset">偏移量</param>
        public static int calcRegionSample(int[] dirArray, int iMainDir, int iLevel, int iSort, int ioffset)
        {
            int iNum = 0;
            if (iSort == 1)
            {
                for (int i = (iMainDir - iLevel - ioffset) / 5; i <= (iMainDir - ioffset) / 5; i++)
                {
                    int j = (i + 72) % 72;
                    iNum += dirArray[j];
                }
            }
            else if (iSort == 2)
            {
                for (int i = (iMainDir + ioffset) / 5 + 1; i <= (iMainDir + iLevel + ioffset) / 5 + 1; i++)
                {
                    int j = (i + 72) % 72;
                    iNum += dirArray[j];
                }
            }
            return iNum;
        }

        /// <summary>
        /// 有数的角度
        /// </summary>
        public static int calcDirNum(int[] dirArray)
        {
            int iDirNum = 0;
            for (int i = 0; i < 72; i++)
            {
                if (dirArray[i] > 0)
                {
                    iDirNum++;
                }
            }
            return iDirNum;
        }

        /// <summary>
        /// 获取每个角度的采样点数
        /// </summary>
        public static int[] getDirArray(AnaRttdAoaArray ary)
        {
            int[] dirArray = new int[72];
            for (int j = 0; j < 72; j++)
            {
                int iNum = 0;
                for (int i = 0; i < 44; i++)
                {
                    iNum += ary.AnaRttdAoa90[i, j];
                }
                dirArray[j] = iNum;
            }
            return dirArray;
        }

        /**
        /// <summary>
        /// 获取每个角度的TA计数
        /// </summary>
        public static int[] getDirCount(int[,] AnaRttdAoa90)
        {
            int[] dirArray = new int[72];
            for (int j = 0; j < 72; j++)
            {
                int iNum = 0;
                for (int i = 0; i < 44; i++)
                {
                    if (AnaRttdAoa90[i, j] > 0)
                        iNum += 1;
                }
                dirArray[j] = iNum;
            }
            return dirArray;
        }
        */

        /// <summary>
        /// 获取每个角度的最大TA值
        /// </summary>
        public static double[] getDirMaxTa(AnaRttdAoaArray ary)
        {
            int[] dirArray = new int[72];
            for (int j = 0; j < 72; j++)
            {
                int iMaxTa = -1;
                for (int i = 0; i < 44; i++)
                {
                    if (ary.AnaRttdAoa90[i, j] > 0)
                        iMaxTa = i;
                }
                dirArray[j] = iMaxTa;
            }

            double[] dirNewArray = new double[72];
            for (int i = 0; i < 72; i++)
            {
                int iDistF2 = ZTAntFuncHelper.calcDistByLteMrTa(dirArray[(i - 2 + 72) % 72]);
                int iDistF1 = ZTAntFuncHelper.calcDistByLteMrTa(dirArray[(i - 1 + 72) % 72]);
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(dirArray[i]);
                int iDist1 = ZTAntFuncHelper.calcDistByLteMrTa(dirArray[(i + 1 + 72) % 72]);
                int iDist2 = ZTAntFuncHelper.calcDistByLteMrTa(dirArray[(i + 2 + 72) % 72]);
                dirNewArray[i] = (iDistF2 + iDistF1 + iDist + iDist1 + iDist2)*1.0 / 5;
            }
            return dirNewArray;
        }

        /// <summary>
        /// 计算区间最远TA距离
        /// </summary>
        /// <param name="iSort">1为反向,2为正向,3为全部</param>
        public static double getRegionMaxTa(double[] dirNewArray, int iMainDir, int iLevel, int iSort, int ioffset)
        {
            double dAvgTa = 0;
            if (iSort == 1 || iSort == 3)
            {
                for (int i = (iMainDir - iLevel - ioffset) / 5; i <= (iMainDir - ioffset) / 5; i++)
                {
                    int j = (i + 72) % 72;
                    dAvgTa = dirNewArray[j] > dAvgTa ? dirNewArray[j] : dAvgTa;
                }
            }
            if (iSort == 2 || iSort == 3)
            {
                for (int i = (iMainDir + ioffset) / 5 + 1; i <= (iMainDir + iLevel + ioffset) / 5 + 1; i++)
                {
                    int j = (i + 72) % 72;
                    dAvgTa = dirNewArray[j] > dAvgTa ? dirNewArray[j] : dAvgTa;
                }
            }
            return dAvgTa;
        }

        /// <summary>
        /// 计算区间平均A距离
        /// </summary>
        /// <param name="iSort">1为反向,2为正向,3为全部</param>
        public static double getRegionAvgTa(AnaRttdAoaArray ary, int iMainDir, int iLevel, int iSort, int ioffset,int iTaNum)
        {
            double dAvgDist = 0;
            double dSumDist = 0;
            double dNum = 0;
            if (iSort == 1 || iSort == 3)
            {
                for (int i = (iMainDir - iLevel - ioffset) / 5; i <= (iMainDir - ioffset) / 5; i++)
                {
                    addDistance(ary, iTaNum, ref dSumDist, ref dNum, i);
                }
            }
            if (iSort == 2 || iSort == 3)
            {
                for (int i = (iMainDir + ioffset) / 5 + 1; i <= (iMainDir + iLevel + ioffset) / 5 + 1; i++)
                {
                    addDistance(ary, iTaNum, ref dSumDist, ref dNum, i);
                }
            }
            if (dNum == 0)
                return 0;

            dAvgDist = dSumDist / dNum;
            return dAvgDist;
        }

        private static void addDistance(AnaRttdAoaArray ary, int iTaNum, ref double dSumDist, ref double dNum, int i)
        {
            int j = (i + 72) % 72;
            for (int k = 0; k < iTaNum; k++)
            {
                if (ary.AnaRttdAoa90[k, j] > 0)
                {
                    dNum += ary.AnaRttdAoa90[k, j];
                    dSumDist += ary.AnaRttdAoa90[k, j] * ZTAntFuncHelper.calcDistByLteMrTa(k);
                }
            }
        }

        /// <summary>
        /// 计算MR天线辐射旁瓣
        /// </summary>
        public static List<AntMRInfo> calcMRInfo(int[] dirArray, double[] taArray, int iTotalNum)
        {
            List<AntMRInfo> infoList = new List<AntMRInfo>();
            AntMRInfo mi = new AntMRInfo();

            int iStartId = 0;//开始位置
            for (int i = 72; i > 0; i--)
            {
                int iTwoD = dirArray[i % 72] + dirArray[(i - 1) % 72];
                if (iTwoD == 0)
                {
                    iStartId = (72 - (i + 1)) % 72;
                    break;
                }
            }

            for (int i = 0 - iStartId; i < 72 - iStartId; i++)
            {
                mi = getAntMRInfo(dirArray, taArray, iTotalNum, infoList, mi, i);
            }

            return infoList;
        }

        private static AntMRInfo getAntMRInfo(int[] dirArray, double[] taArray, int iTotalNum, List<AntMRInfo> infoList, AntMRInfo mi, int i)
        {
            try
            {
                int k = (i + 72) % 72;

                if (dirArray[k] != 0 && dirArray[k] + dirArray[(k + 1) % 72] > 0)
                {
                    if (mi.iMinDir == -1)
                    {
                        mi.iMinDir = k;
                        mi.sampNum = dirArray[k];
                        mi.iTotalNum = iTotalNum;
                        mi.iTaDist = (int)taArray[k];
                    }
                    else
                    {
                        mi.iMaxDir = k;
                        mi.sampNum += dirArray[k];
                        mi.iTaDist = (int)(mi.iTaDist < taArray[k] ? taArray[k] : mi.iTaDist);
                    }
                }
                else
                {
                    if (mi.iMaxDir != -1 && mi.iMinDir != -1 && mi.iTaDist >= 500 && mi.fRate >= 0.1)
                    {
                        infoList.Add(mi);
                        mi = new AntMRInfo();
                    }
                }
            }
            catch
            {
                //continue
            }

            return mi;
        }

        #endregion

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            List<CellMRData> cellMRList = new List<CellMRData>();
            foreach (int iEci in cellParaDataDic.Keys)
            {
                CellMRData mrData = cellParaDataDic[iEci];
                cellMRList.Add(mrData);
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteAntMRForm).FullName);
            LteAntMRAnaForm form = obj == null ? null : obj as LteAntMRAnaForm;
            if (form == null || form.IsDisposed)
            {
                form = new LteAntMRAnaForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(cellMRList);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        #region 数据整理及导出
        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<NPOIRow> data8s = new List<NPOIRow>();
            NPOIRow nr8 = new NPOIRow();
            nr8.cellValues = GetExcelColName();
            data8s.Add(nr8);

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<CellParaData> listCellParaData = new List<CellParaData>();
            foreach (int ieci in cellParaDataDic.Keys)
            {
                try
                {
                    NPOIRow nr9 = new NPOIRow();
                    nr9.cellValues = FillCellValue(cellParaDataDic[ieci]);
                    data8s.Add(nr9);
                    listCellParaData.Add(cellParaDataDic[ieci]);
                }
                catch
                {
                    //continue
                }
            }
            nrDatasList.Add(data8s);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("MR覆盖分析");
            MainModel.ListCellLteMRData = listCellParaData;
            FireShowResultForm(nrDatasList, sheetNames);
        }

        /// <summary>
        /// 数据表头
        /// </summary>
        private List<object> GetExcelColName()
        {
            List<object> col8s = new List<object>();
            #region 字段定义
            col8s.Add("序号");
            col8s.Add("地市");
            col8s.Add("小区英文名");
            col8s.Add("小区名");
            col8s.Add("主设备厂家");
            col8s.Add("是否区配工参");
            col8s.Add("CGI");
            col8s.Add("覆盖类型");
            col8s.Add("场景类型");
            col8s.Add("小区频段");

            col8s.Add("方位角");
            col8s.Add("预置下倾角");
            col8s.Add("机械下倾角");
            col8s.Add("电调下倾角");
            col8s.Add("挂高");
#if jtcheck //集团巡检，演示功能，先屏蔽
            col8s.Add("分析结果");
            col8s.Add("告警状态");

            col8s.Add("上行吞吐量(统计)");
            col8s.Add("下行吞吐量(统计)");
            col8s.Add("无线接通率(统计)");
            col8s.Add("无线掉线率(统计)");
            col8s.Add("切换成功率(统计)");
            col8s.Add("ERAB建立成功率(统计)");
            col8s.Add("ERAB掉线率(统计)");
            col8s.Add("RSRP均值(统计)");
            col8s.Add("SINR均值(统计)");
            col8s.Add("95覆盖率(统计)");
            col8s.Add("110覆盖率(统计)");
#endif
            col8s.Add("方向角度数(MR数据)");
            col8s.Add("方向角正负30°范围打点比例(MR数据)");
            col8s.Add("方向角正负30°覆盖距离峰值(MR数据)");
            col8s.Add("方向角正负30°覆盖距离均值(MR数据)");
            col8s.Add("方向角正负60°范围打点比例(MR数据)");
            col8s.Add("方向角正负60°覆盖距离峰值(MR数据)");
            col8s.Add("方向角正负60°覆盖距离均值(MR数据)");
            col8s.Add("方向角正负90°范围打点比例(MR数据)");
            col8s.Add("方向角正负90°覆盖距离峰值(MR数据)");
            col8s.Add("方向角正负90°覆盖距离均值(MR数据)");
            col8s.Add("方向角正60-120°范围打点比例(MR数据)");
            col8s.Add("方向角正60-120°覆盖距离峰值(MR数据)");
            col8s.Add("方向角正60-120°覆盖距离均值(MR数据)");
            col8s.Add("方向角负60-120°范围打点比例(MR数据)");
            col8s.Add("方向角负60-120°覆盖距离峰值(MR数据)");
            col8s.Add("方向角负60-120°覆盖距离均值(MR数据)");
            col8s.Add("背向正负60°范围打点占比(MR数据)");
            col8s.Add("背向正负60°覆盖距离峰值(MR数据)");
            col8s.Add("背向正负60°覆盖距离均值(MR数据)");
            col8s.Add("背向正负15°范围打点占比(MR数据)");
            col8s.Add("背向正负15°覆盖距离峰值(MR数据)");
            col8s.Add("背向正负15°覆盖距离均值(MR数据)");

            col8s.Add("旁瓣数量(MR数据)");
            col8s.Add("1000米外的打点占比(MR数据)");
            col8s.Add("打点角度数量(MR数据)");
            col8s.Add("方向角偏差度数");

            col8s.Add("方向角正负30°范围打点比例(工参方向角,MR数据)");
            col8s.Add("方向角正负60°范围打点比例(工参方向角,MR数据)");
            col8s.Add("方向角正负90°范围打点比例(工参方向角,MR数据)");
            col8s.Add("方向角正60-120°范围打点比例(工参方向角,MR数据)");
            col8s.Add("方向角负60-120°范围打点比例(工参方向角,MR数据)");
            col8s.Add("背向正负60°范围打点占比(工参方向角,MR数据)");
            col8s.Add("背向正负15°范围打点占比(工参方向角,MR数据)");
            col8s.Add("旁瓣数量(工参方向角,MR数据)");
#if jtcheck //集团巡检，演示功能，先屏蔽
            col8s.Add("Gmax");
            col8s.Add("3dB功率角");
            col8s.Add("6dB功率角");
            col8s.Add("端口1幅度");
            col8s.Add("端口2幅度");
            col8s.Add("端口3幅度");
            col8s.Add("端口4幅度");
            col8s.Add("端口5幅度");
            col8s.Add("端口6幅度");
            col8s.Add("端口7幅度");
            col8s.Add("端口8幅度");
            col8s.Add("端口1相位");
            col8s.Add("端口2相位");
            col8s.Add("端口3相位");
            col8s.Add("端口4相位");
            col8s.Add("端口5相位");
            col8s.Add("端口6相位");
            col8s.Add("端口7相位");
            col8s.Add("端口8相位");
            col8s.Add("是否(8通道)智能天线");
#endif
            col8s.Add("是否可MR波形重构");
            #endregion
            return col8s;
        }

        /// <summary>
        /// 小区级赋值
        /// </summary>
        private List<object> FillCellValue(CellMRData data)
        {
            List<object> objs8 = new List<object>();
            objs8.Add(data.index);//序号
            objs8.Add(data.strcityname);//地市
            objs8.Add(data.cellNameEn);//小区英文名
            objs8.Add(data.cellname);//小区名
            objs8.Add(data.antCfg.strVender);//主设备厂家
            objs8.Add(data.strMatch);//是否区配工参
            objs8.Add("460-00-" + data.antCfg.iEnodebID.ToString() + "-" + data.antCfg.iSectorID.ToString());//CGI
            objs8.Add(data.antCfg.strType);//覆盖类型
            objs8.Add(data.antCfg.场景类型);//场景类型
            objs8.Add(data.antCfg.strBtsType);//小区频段

            objs8.Add(Math.Round(data.antCfg.方向角, 2));//预置下倾角
            objs8.Add(Math.Round(data.antCfg.预置下倾角, 2));//预置下倾角
            objs8.Add(Math.Round(data.antCfg.机械下倾角, 2));//机械下倾角
            objs8.Add(Math.Round(data.antCfg.电调下倾角, 2));//电调下倾角
            objs8.Add(Math.Round(data.antCfg.挂高, 2));//挂高
#if jtcheck //集团巡检，演示功能，先屏蔽 
            objs8.Add(data.strAntResult);
            objs8.Add(data.strProbType);
     
            objs8.Add(Math.Round(data.cellPara.F上行吞吐量, 2));
            objs8.Add(Math.Round(data.cellPara.F下行吞吐量, 2));
            objs8.Add(Math.Round(data.cellPara.F无线接通率, 2));
            objs8.Add(Math.Round(data.cellPara.F无线掉线率, 2));
            objs8.Add(Math.Round(data.cellPara.F切换成功率, 2));
            objs8.Add(Math.Round(data.cellPara.fERAB建立成功率, 2));
            objs8.Add(Math.Round(data.cellPara.fERAB掉线率, 2));
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dAvgRsrp, 2));//RSRP均值
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dAvgSinr, 2));//SINR均值
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dRate95Rsrp, 2));//95覆盖率
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dRate110Rsrp, 2));//110覆盖率
#endif
            objs8.Add(data.strMainDir);
            objs8.Add(data.strRel30Rate);
            objs8.Add(Math.Round(data.dAbs30MaxDist, 2));
            objs8.Add(Math.Round(data.dAbs30AvgDist, 2));
            objs8.Add(data.strRel60Rate);
            objs8.Add(Math.Round(data.dAbs60MaxDist, 2));
            objs8.Add(Math.Round(data.dAbs60AvgDist, 2));
            objs8.Add(data.strRel90Rate);
            objs8.Add(Math.Round(data.dAbs90MaxDist, 2));
            objs8.Add(Math.Round(data.dAbs90AvgDist, 2));
            objs8.Add(data.strRelPlus60To120Rate);
            objs8.Add(Math.Round(data.dAbsPlus60To120MaxDist, 2));
            objs8.Add(Math.Round(data.dAbsPlus60To120AvgDist, 2));
            objs8.Add(data.strRelMinus60To120Rate);
            objs8.Add(Math.Round(data.dAbsMinus60To120MaxDist, 2));
            objs8.Add(Math.Round(data.dAbsMinus60To120AvgDist, 2));
            objs8.Add(data.strRelBack60Rate);
            objs8.Add(Math.Round(data.dAbsBack60MaxDist, 2));
            objs8.Add(Math.Round(data.dAbsBack60AvgDist, 2));
            objs8.Add(data.strRelBack15Rate);
            objs8.Add(Math.Round(data.dAbsBack15MaxDist, 2));
            objs8.Add(Math.Round(data.dAbsBack15AvgDist, 2));

            objs8.Add(data.iSideNum);
            objs8.Add(data.strOverCoverRate);
            objs8.Add(data.iDirNum);
            objs8.Add(data.iDirDiff);

            objs8.Add(data.strAbs30Rate);
            objs8.Add(data.strAbs60Rate);
            objs8.Add(data.strAbs90Rate);
            objs8.Add(data.strAbsPlus60To120Rate);
            objs8.Add(data.strAbsMinus60To120Rate);
            objs8.Add(data.strAbsBack60Rate);
            objs8.Add(data.strAbsBack15Rate);
            objs8.Add(data.iSideNum);
#if jtcheck //集团巡检，演示功能，先屏蔽
            objs8.Add(Math.Round(data.antPara.GMax, 2).ToString());//Gmax
            objs8.Add(Math.Round(data.antPara._3dbValue, 0).ToString());//3dB功率角
            objs8.Add(Math.Round(data.antPara._6dbValue, 0).ToString());//6dB功率角
            objs8.Add(data.antPara.drangeport1 == 9999 ? "-" : Math.Round(data.antPara.drangeport1, 2).ToString());//端口1幅度
            objs8.Add(data.antPara.drangeport2 == 9999 ? "-" : Math.Round(data.antPara.drangeport2, 2).ToString());//端口2幅度
            objs8.Add(data.antPara.drangeport3 == 9999 ? "-" : Math.Round(data.antPara.drangeport3, 2).ToString());//端口3幅度
            objs8.Add(data.antPara.drangeport4 == 9999 ? "-" : Math.Round(data.antPara.drangeport4, 2).ToString());//端口4幅度
            objs8.Add(data.antPara.drangeport5 == 9999 ? "-" : Math.Round(data.antPara.drangeport5, 2).ToString());//端口5幅度
            objs8.Add(data.antPara.drangeport6 == 9999 ? "-" : Math.Round(data.antPara.drangeport6, 2).ToString());//端口6幅度
            objs8.Add(data.antPara.drangeport7 == 9999 ? "-" : Math.Round(data.antPara.drangeport7, 2).ToString());//端口7幅度
            objs8.Add(data.antPara.drangeport8 == 9999 ? "-" : Math.Round(data.antPara.drangeport8, 2).ToString());//端口8幅度
            objs8.Add(data.antPara.dphaseport1 == 9999 ? "-" : Math.Round(data.antPara.dphaseport1, 2).ToString());//端口1相位
            objs8.Add(data.antPara.dphaseport2 == 9999 ? "-" : Math.Round(data.antPara.dphaseport2, 2).ToString());//端口2相位
            objs8.Add(data.antPara.dphaseport3 == 9999 ? "-" : Math.Round(data.antPara.dphaseport3, 2).ToString());//端口3相位
            objs8.Add(data.antPara.dphaseport4 == 9999 ? "-" : Math.Round(data.antPara.dphaseport4, 2).ToString());//端口4相位
            objs8.Add(data.antPara.dphaseport5 == 9999 ? "-" : Math.Round(data.antPara.dphaseport5, 2).ToString());//端口5相位
            objs8.Add(data.antPara.dphaseport6 == 9999 ? "-" : Math.Round(data.antPara.dphaseport6, 2).ToString());//端口6相位
            objs8.Add(data.antPara.dphaseport7 == 9999 ? "-" : Math.Round(data.antPara.dphaseport7, 2).ToString());//端口7相位
            objs8.Add(data.antPara.dphaseport8 == 9999 ? "-" : Math.Round(data.antPara.dphaseport8, 2).ToString());//端口8相位
            objs8.Add(data.antPara.isSmartAnt);//是否(8通道)智能天线
#endif
            objs8.Add(data.strIsMrCell);

            return objs8;
        }

        #endregion

        public class CellMRData : ZTLteAntMR.CellParaData
        {
            /// <summary>
            /// 打点的角度数据
            /// </summary>
            public int iDirNum { get; set; }
            /// <summary>
            /// 主覆盖方向
            /// </summary>
            public int iMainDir { get; set; }
            /// <summary>
            /// 总采样点数
            /// </summary>
            public int iTotalNum { get; set; }
            /// <summary>
            /// 旁瓣数量
            /// </summary>
            public int iSideNum { get; set; }

            //主覆盖采样点
            public int iRel30Num { get; set; }
            public int iRel60Num { get; set; }
            public int iRel90Num { get; set; }
            public int iRelPlus60To120Num { get; set; }
            public int iRelMinus60To120Num { get; set; }
            public int iRelBack60Num { get; set; }
            public int iRelBack15Num { get; set; }

            public string strRel30Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRel30Num) / iTotalNum, 2) + "%";
                }
            }
            public string strRel60Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRel60Num) / iTotalNum, 2) + "%";
                }
            }
            public string strRel90Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRel90Num) / iTotalNum, 2) + "%";
                }
            }
            public string strRelPlus60To120Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRelPlus60To120Num) / iTotalNum, 2) + "%";
                }
            }
            public string strRelMinus60To120Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRelMinus60To120Num) / iTotalNum, 2) + "%";
                }
            }
            public string strRelBack60Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRelBack60Num) / iTotalNum, 2) + "%";
                }
            }
            public string strRelBack15Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iRelBack15Num) / iTotalNum, 2) + "%";
                }
            }

            //工参采样点
            public int iAbs30Num { get; set; }
            public int iAbs60Num { get; set; }
            public int iAbs90Num { get; set; }
            public int iAbsPlus60To120Num { get; set; }
            public int iAbsMinus60To120Num { get; set; }
            public int iAbsBack60Num { get; set; }
            public int iAbsBack15Num { get; set; }

            public double dAbs30MaxDist { get; set; }
            public double dAbs60MaxDist { get; set; }
            public double dAbs90MaxDist { get; set; }
            public double dAbsPlus60To120MaxDist { get; set; }
            public double dAbsMinus60To120MaxDist { get; set; }
            public double dAbsBack60MaxDist { get; set; }
            public double dAbsBack15MaxDist { get; set; }

            public double dAbs30AvgDist { get; set; }
            public double dAbs60AvgDist { get; set; }
            public double dAbs90AvgDist { get; set; }
            public double dAbsPlus60To120AvgDist { get; set; }
            public double dAbsMinus60To120AvgDist { get; set; }
            public double dAbsBack60AvgDist { get; set; }
            public double dAbsBack15AvgDist { get; set; }

            public string strAbs30Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbs30Num) / iTotalNum, 2) + "%";
                }
            }
            public string strAbs60Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbs60Num) / iTotalNum, 2) + "%";
                }
            }
            public string strAbs90Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbs90Num) / iTotalNum, 2) + "%";
                }
            }
            public string strAbsPlus60To120Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbsPlus60To120Num) / iTotalNum, 2) + "%";
                }
            }
            public string strAbsMinus60To120Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbsMinus60To120Num) / iTotalNum, 2) + "%";
                }
            }
            public string strAbsBack60Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbsBack60Num) / iTotalNum, 2) + "%";
                }
            }
            public string strAbsBack15Rate
            {
                get
                {
                    if (iTotalNum == 0)
                        return "";

                    return Math.Round(100 * ((float)iAbsBack15Num) / iTotalNum, 2) + "%";
                }
            }

            public double[] maxTaArray { get; set; } = new double[72];//每个角度最大TA值

            public string strMainDir
            {
                get
                {
                    if (iMainDir == -1)
                        return "";
                    else
                        return iMainDir + "";
                }
            }

            public CellMRData()
            {
                iDirNum = 0;
                iMainDir = -1;
                iTotalNum = 0;
                iSideNum = 0;

                iRel30Num = 0;
                iRel60Num = 0;
                iRel90Num = 0;
                iRelPlus60To120Num = 0;
                iRelMinus60To120Num = 0;
                iRelBack60Num = 0;
                iRelBack15Num = 0;

                iAbs30Num = 0;
                iAbs60Num = 0;
                iAbs90Num = 0;
                iAbsPlus60To120Num = 0;
                iAbsMinus60To120Num = 0;
                iAbsBack60Num = 0;
                iAbsBack15Num = 0;

                dAbs30MaxDist = 0;
                dAbs60MaxDist = 0;
                dAbs90MaxDist = 0;
                dAbsPlus60To120MaxDist = 0;
                dAbsMinus60To120MaxDist = 0;
                dAbsBack60MaxDist = 0;
                dAbsBack15MaxDist = 0;

                dAbs30AvgDist = 0;
                dAbs60AvgDist = 0;
                dAbs90AvgDist = 0;
                dAbsPlus60To120AvgDist = 0;
                dAbsMinus60To120AvgDist = 0;
                dAbsBack60AvgDist = 0;
                dAbsBack15AvgDist = 0;
            }

            public void fillValue(ZTLteAntMR.CellParaData cellPara)
            {
                this.cellPara = cellPara.cellPara;
                this.antCfg = cellPara.antCfg;
                this.cellMrData.lteMRItem = cellPara.cellMrData.lteMRItem;
                this.cellMrData.lteMRCoverItem = cellPara.cellMrData.lteMRCoverItem;

                this.cellMrData.lteMRAoaItem = cellPara.cellMrData.lteMRAoaItem;
                this.cellMrData.lteMRRttdAoaItem = cellPara.cellMrData.lteMRRttdAoaItem;
                this.cellMrData.lteMRTaItem = cellPara.cellMrData.lteMRTaItem;
                this.cellMrData.lteMRSinrUlItem = cellPara.cellMrData.lteMRSinrUlItem;
                this.cellMrData.lteMRRsrpItem = cellPara.cellMrData.lteMRRsrpItem;
                this.cellMrData.lteMRPowerHeadRoomItem = cellPara.cellMrData.lteMRPowerHeadRoomItem;

                this.antPara = cellPara.antPara;
            }

            /// <summary>
            /// 打点的90%
            /// </summary>
            public int[,] AnaRttdAoa90
            {
                get
                {
                    int[,] rttdAoaItem = new int[44, 72];
                    
                    List<AntMRAoaTa> atList = getAoaTaList();

                    int iNum = (int)(0.9 * atList.Count);
                    for (int i = 0; i < iNum; i++)
                    {
                        AntMRAoaTa at = atList[i];
                        rttdAoaItem[at.iTaId, at.iAoaId] = at.iCount;
                    }
                    return rttdAoaItem;
                }
            }

            /// <summary>
            /// 1000米以外打点占比
            /// </summary>
            public string strOverCoverRate
            {
                get
                {
                    int[,] taAoa = AnaRttdAoa90;
                    int iNum = 0;
                    int iAllNum = 0;
                    for (int i = 0; i < 44; i++)
                    {
                        for (int j = 0; j < 72; j++)
                        {
                            if (taAoa[i, j] > 0)
                            {
                                iAllNum++;
                                if (i >= 12)
                                    iNum += 1;
                            }
                        }
                    }

                    if (iAllNum == 0)
                        return "0%";

                    return Math.Round(100 * ((float)iNum) / iAllNum, 2) + "%";
                }
            }

            /// <summary>
            /// 打点的90%-分段
            /// </summary>
            public Dictionary<int,List<AntMRAoaTa>> aoaTaDic
            {
                get
                {
                    List<AntMRAoaTa> atList = getAoaTaList();

                    int iNum = atList.Count;
                    Dictionary<int, List<AntMRAoaTa>> aoaTa = new Dictionary<int, List<AntMRAoaTa>>();
                    for (int i = 0; i < iNum; i++)
                    {
                        int iLevel = getMrLevel(iNum, i);
                        if (iLevel == 0)
                            break;

                        List<AntMRAoaTa> tmpList;
                        if (!aoaTa.TryGetValue(iLevel, out tmpList))
                            tmpList = new List<AntMRAoaTa>();

                        tmpList.Add(atList[i]);
                        aoaTa[iLevel] = tmpList;
                    }

                    return aoaTa;
                }
            }

            /// <summary>
            /// 获取MR采样点
            /// </summary>
            private List<AntMRAoaTa> getAoaTaList()
            {
                List<AntMRAoaTa> atList = new List<AntMRAoaTa>();
                int[,] taAoa = cellMrData.AnaRttdAoa;
                for (int i = 0; i < 44; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        AntMRAoaTa at = new AntMRAoaTa();
                        at.iAoaId = j;
                        at.iTaId = i;
                        if (taAoa[i, j] > 0)
                        {
                            at.iCount = taAoa[i, j];
                            atList.Add(at);
                        }
                    }
                }
                atList.Sort(MRSampleByNum.GetCompareByNum());
                return atList;
            }

            /// <summary>
            /// 获取采样点归属等级
            /// </summary>
            public int getMrLevel(int iTotalNum,int iNum)
            {
                int iLevel = 0;
                float fRate = (float)(iNum * 1.0 / iTotalNum);
                if (fRate >= 0 && fRate <= 0.5)
                {
                    iLevel = 1;
                }
                else if (fRate > 0.5 && fRate <= 0.7)
                {
                    iLevel = 2;
                }
                else if (fRate > 0.7 && fRate <= 0.8)
                {
                    iLevel = 3;
                }
                else if (fRate > 0.8 && fRate <= 0.9)
                {
                    iLevel = 4; 
                }
                else
                {
                    iLevel = 0;
                }
                return iLevel;
            }
        }

        public class AntMRAoaTa
        {
            public int iAoaId { get; set; }
            public int iTaId { get; set; }
            public int iCount { get; set; }

            public AntMRAoaTa()
            {
                iAoaId = 0;
                iTaId = 0;
                iCount = 0;
            }
        }

        public class AntMRInfo
        {
            public int iMinDir { get; set; } //辐射叶左边角度
            public int iMaxDir { get; set; } //辐射叶右边角度
            public int sampNum { get; set; } //区间采样点数
            public int iTaDist { get; set; } //TA值计数
            public int iTotalNum { get; set; } //采样点总数

            public float fRate
            {
                get
                {
                    return (float)(sampNum * 1.0 / iTotalNum);
                }
            }

            public AntMRInfo()
            {
                iMinDir = -1;
                iMaxDir = -1;
                sampNum = 0;
                iTaDist = 0;
                iTotalNum = 0;
            }
        }

        /// <summary>
        /// 实现对采样点时间的排序类
        /// </summary>
        public class MRSampleByNum
        {
            //实现排序的接口
            public static IComparer<AntMRAoaTa> GetCompareByNum()
            {
                if (comparerByDist == null)
                {
                    comparerByDist = new CompareByNum();
                }
                return comparerByDist;
            }
            public class CompareByNum : IComparer<AntMRAoaTa>
            {
                public int Compare(AntMRAoaTa x, AntMRAoaTa y)
                {
                    return y.iCount.CompareTo(x.iCount);
                }
            }
            private static IComparer<AntMRAoaTa> comparerByDist;
        }

        public class AnaRttdAoaArray
        {
            public int[,] AnaRttdAoa90 { get; set; }
        }
    }
}
