﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellWrongDirSettingDlg_TD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.spinEditAngle = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDis = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditRSCP = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditPer = new DevExpress.XtraEditors.SpinEdit();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSCP.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(87, 43);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "场强≥";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(27, 37);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(96, 12);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "与主服小区距离≥";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(27, 66);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(96, 12);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "与主服小区夹角≥";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.spinEditAngle);
            this.groupControl1.Controls.Add(this.spinEditDis);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(267, 117);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "覆盖不符条件";
            // 
            // spinEditAngle
            // 
            this.spinEditAngle.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditAngle.Location = new System.Drawing.Point(129, 63);
            this.spinEditAngle.Name = "spinEditAngle";
            this.spinEditAngle.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditAngle.Properties.Appearance.Options.UseFont = true;
            this.spinEditAngle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditAngle.Properties.IsFloatValue = false;
            this.spinEditAngle.Properties.Mask.EditMask = "N00";
            this.spinEditAngle.Properties.MaxValue = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.spinEditAngle.Size = new System.Drawing.Size(90, 20);
            this.spinEditAngle.TabIndex = 1;
            // 
            // spinEditDis
            // 
            this.spinEditDis.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditDis.Location = new System.Drawing.Point(129, 31);
            this.spinEditDis.Name = "spinEditDis";
            this.spinEditDis.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDis.Properties.Appearance.Options.UseFont = true;
            this.spinEditDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDis.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditDis.Properties.IsFloatValue = false;
            this.spinEditDis.Properties.Mask.EditMask = "N00";
            this.spinEditDis.Size = new System.Drawing.Size(90, 20);
            this.spinEditDis.TabIndex = 0;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(225, 68);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "度";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(225, 36);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "米";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(73, 252);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(168, 252);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // spinEditRSCP
            // 
            this.spinEditRSCP.EditValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Location = new System.Drawing.Point(129, 40);
            this.spinEditRSCP.Name = "spinEditRSCP";
            this.spinEditRSCP.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRSCP.Properties.Appearance.Options.UseFont = true;
            this.spinEditRSCP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRSCP.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditRSCP.Properties.IsFloatValue = false;
            this.spinEditRSCP.Properties.Mask.EditMask = "N00";
            this.spinEditRSCP.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRSCP.Size = new System.Drawing.Size(90, 20);
            this.spinEditRSCP.TabIndex = 0;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(51, 79);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(72, 12);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "不符合比例≥";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(225, 79);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(6, 12);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "%";
            // 
            // spinEditPer
            // 
            this.spinEditPer.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditPer.Location = new System.Drawing.Point(129, 76);
            this.spinEditPer.Name = "spinEditPer";
            this.spinEditPer.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPer.Properties.Appearance.Options.UseFont = true;
            this.spinEditPer.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPer.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditPer.Properties.IsFloatValue = false;
            this.spinEditPer.Properties.Mask.EditMask = "N00";
            this.spinEditPer.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditPer.Size = new System.Drawing.Size(90, 20);
            this.spinEditPer.TabIndex = 1;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.spinEditRSCP);
            this.groupControl2.Controls.Add(this.labelControl1);
            this.groupControl2.Controls.Add(this.spinEditPer);
            this.groupControl2.Controls.Add(this.labelControl4);
            this.groupControl2.Controls.Add(this.labelControl6);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(0, 117);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(267, 116);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "过滤设置";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(225, 43);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(18, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "dBm";
            // 
            // CellWrongDirSettingDlg_TD
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(267, 298);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl1);
            this.Name = "CellWrongDirSettingDlg_TD";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAngle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRSCP.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SpinEdit spinEditAngle;
        private DevExpress.XtraEditors.SpinEdit spinEditDis;
        private DevExpress.XtraEditors.SpinEdit spinEditRSCP;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit spinEditPer;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl8;
    }
}