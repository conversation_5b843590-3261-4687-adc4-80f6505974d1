﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class CellWeakCoverByGridInfoBase
    {
        public ICell Cell { get; protected set; }
        public double LongitudeCell { get; protected set; }
        public double LatitudeCell { get; protected set; }
        public string CellName { get; protected set; }
        public string LAC { get; protected set; }
        public string CI { get; protected set; }
        public string BCCH { get; protected set; }
        public string BSIC { get; protected set; }
        public List<GridForCellWeakCover> GridList { get; set; } = new List<GridForCellWeakCover>();
        public int GridCount { get; protected set; }
        public double RxLevMean { get; protected set; }

        public virtual void FillCellData(ICell cell)
        {
            Cell = cell;
            LongitudeCell = cell.Longitude;
            LatitudeCell = cell.Latitude;
            CellName = cell.Name;
        }

        public void Calculate()
        {
            GridCount= GridList.Count;

            AvgInfo rsrp = new AvgInfo();
            foreach (GridForCellWeakCover grid in GridList)
            {
                rsrp.Add(grid.rxLevSum, grid.SampleCount);
            }

            rsrp.Calculate();
            RxLevMean = rsrp.Avg;
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            setCellInfo(bgResult);
            bgResult.LongitudeMid = LongitudeCell;
            bgResult.LatitudeMid = LatitudeCell;
            bgResult.RxLevMean = (float)RxLevMean;
            bgResult.AddImageValue(GridCount);
            return bgResult;
        }

        protected virtual void setCellInfo(BackgroundResult bgResult)
        {
            if (Cell is Cell)
            {
                bgResult.CellType = BackgroundCellType.GSM;
            }
        }
    }

    public class CellWeakCoverByGridInfoGsm : CellWeakCoverByGridInfoBase
    {
        public override void FillCellData(ICell cell)
        {
            base.FillCellData(cell);
            Cell gsmCell = cell as Cell;
            LAC = gsmCell.LAC.ToString();
            CI = gsmCell.CI.ToString();
            BCCH = gsmCell.BCCH.ToString();
            BSIC = gsmCell.BSIC.ToString();
        }

        protected override void setCellInfo(BackgroundResult bgResult)
        {
            if (Cell is Cell)
            {
                bgResult.CellType = BackgroundCellType.GSM;
                bgResult.LAC = Convert.ToInt32(LAC);
                bgResult.CI = Convert.ToInt32(CI);
                bgResult.BCCH = Convert.ToInt32(BCCH);
                bgResult.BSIC = Convert.ToInt32(BSIC);
            }
        }
    }

    public class CellWeakCoverByGridInfoTd : CellWeakCoverByGridInfoBase
    {
        public override void FillCellData(ICell cell)
        {
            base.FillCellData(cell);
            TDCell gsmCell = cell as TDCell;
            LAC = gsmCell.LAC.ToString();
            CI = gsmCell.CI.ToString();
            BCCH = gsmCell.FREQ.ToString();
            BSIC = gsmCell.CPI.ToString();
        }

        protected override void setCellInfo(BackgroundResult bgResult)
        {
            if (Cell is Cell)
            {
                bgResult.CellType = BackgroundCellType.GSM;
                bgResult.LAC = Convert.ToInt32(LAC);
                bgResult.CI = Convert.ToInt32(CI);
                bgResult.BCCH = Convert.ToInt32(BCCH);
                bgResult.BSIC = Convert.ToInt32(BSIC);
            }
        }
    }

    public class CellWeakCoverByGridInfoWcdma : CellWeakCoverByGridInfoBase
    {
        public override void FillCellData(ICell cell)
        {
            base.FillCellData(cell);
            WCell gsmCell = cell as WCell;
            LAC = gsmCell.LAC.ToString();
            CI = gsmCell.CI.ToString();
            BCCH = gsmCell.UARFCN.ToString();
            BSIC = gsmCell.PSC.ToString();
        }
    }

    public class CellWeakCoverByGridInfoLte : CellWeakCoverByGridInfoBase
    {
        public override void FillCellData(ICell cell)
        {
            base.FillCellData(cell);
            LTECell gsmCell = cell as LTECell;
            LAC = gsmCell.TAC.ToString();
            CI = gsmCell.ECI.ToString();
            BCCH = gsmCell.EARFCN.ToString();
            BSIC = gsmCell.PCI.ToString();
        }
    }

    public class CellWeakCoverByGridInfoNr : CellWeakCoverByGridInfoBase
    {
        public override void FillCellData(ICell cell)
        {
            base.FillCellData(cell);
            NRCell nrCell = cell as NRCell;
            LAC = nrCell.TAC.ToString();
            CI = nrCell.NCI.ToString();
            BCCH = nrCell.SSBARFCN.ToString();
            BSIC = nrCell.PCI.ToString();
        }
    }
}
