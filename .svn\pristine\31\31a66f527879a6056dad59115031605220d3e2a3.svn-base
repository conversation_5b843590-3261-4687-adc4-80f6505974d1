﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckAnaBase_GSM : DIYAnalyseFilesOneByOneByRegion
    {
        protected bool isLteFdd = false;
        public ZTLteNBCellCheckCondition nbCellCheckCondition { get; set; } = new ZTLteNBCellCheckCondition();   //查询条件
        public Dictionary<string,ZTLteNBCellCheckCellItem> resultDic { get; set; } = new Dictionary<string,ZTLteNBCellCheckCellItem>() ;    //保存结果

        protected LTECell curLTECell = null; //记录最近的LTE小区
        protected int curLTETime = 0;        //记录取得有效的LTE小区时的时间，用于GSM邻区漏配，只看最近的X秒，避免过远

        public ZTLteNBCellCheckAnaBase_GSM(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = false;
            this.IncludeMessage = true;
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultDic = new Dictionary<string, ZTLteNBCellCheckCellItem>();
        }

        ZTLteNBCellCheckAnaSetForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTLteNBCellCheckAnaSetForm(isLteFdd);
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                nbCellCheckCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TimePeriodNBFreq> tnfLst = new List<TimePeriodNBFreq>();
                List<Message> messageList = fileMng.Messages;
                foreach (Message msg in messageList)
                {
                    dealMsg(tnfLst, msg);
                }

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    doStatWithTestPoint(tp, tnfLst);
                }
            }
        }

        private void dealMsg(List<TimePeriodNBFreq> tnfLst, Message msg)
        {
            if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease)
            {
                uint carrier = 0, group = 0;
                MasterCom.RAMS.Model.MessageWithSource msgWithSoure = msg as MasterCom.RAMS.Model.MessageWithSource;
                MessageDecodeHelper.StartDissect(msgWithSoure.Direction, msgWithSoure.Source, msgWithSoure.Length, msgWithSoure.ID);
                bool isGetCarrier = MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier);
                bool isGetGroup = MessageDecodeHelper.GetSingleUInt("lte-rrc.explicitListOfARFCNs", ref group);
                if (isGetCarrier && isGetGroup && carrier == (int)ECarrierInfo.geran && group > 0)
                {
                    uint[] freqAry = new uint[group];
                    MessageDecodeHelper.GetMultiUInt("lte-rrc.ARFCN_ValueGERAN", ref freqAry, (int)group);
                    TimePeriodNBFreq tnf = new TimePeriodNBFreq(msg.DateTime, msg.DateTime.AddSeconds(nbCellCheckCondition.TimeSpan), freqAry);
                    tnfLst.Add(tnf);
                }
            }
        }

        /// <summary>
        /// 对采样点进行处理
        /// </summary>
        private void doStatWithTestPoint(TestPoint tp, List<TimePeriodNBFreq> tnfLst)
        {
            float? rsrp = (float?)tp["lte_RSRP"];

            if (rsrp != null && rsrp >= -141)   //有LTE下的信号强度，则认为是LTE的采样点
            {
                doTestPoint_LTE(tp, (float)rsrp);
            }
            else
            {
                doTestPoint_GSM(tp, tnfLst);
            }
        }

        protected void doTestPoint_LTE(TestPoint tp, float rsrp)
        {
            LTECell servCell = tp.GetMainLTECell_TdOrFdd();

            if (servCell == null)    //无法匹配到工参，则无经纬度，剔除
            {
                return;  
            }

            ZTLteNBCellCheckCellItem cellItem;
            if (resultDic.ContainsKey(servCell.Name))
            {
                cellItem = resultDic[servCell.Name];
                cellItem.MergeData(rsrp);
            }
            else
            {
                cellItem = new ZTLteNBCellCheckCellItem(servCell, rsrp);
                resultDic.Add(servCell.Name, cellItem);
            }

            if (!nbCellCheckCondition.IsShowGSMNbCellOnly)  //如果不是只呈现GSM邻区，则需要分析LTE邻区
            {
                for (int i = 0; i < 6; i++)
                {
                    addValidNbCell_LTE(tp, servCell, cellItem, i);
                }
            }

            curLTECell = servCell; //记录为当前小区，用于GSM邻区的匹配
            curLTETime = tp.Time;
        }

        private void addValidNbCell_LTE(TestPoint tp, LTECell servCell, ZTLteNBCellCheckCellItem cellItem, int i)
        {
            float? nRsrp = getNRsrp(tp, i);
            if (nRsrp != null && nRsrp >= nbCellCheckCondition.RSRP)
            {
                LTECell nbCell = tp.GetNBLTECell_TdOrFdd(i);
                if (nbCell != null)
                {
                    if (cellItem.NBDic.ContainsKey(nbCell.Name))
                    {
                        cellItem.NBDic[nbCell.Name].MergeData((float)nRsrp, i);
                    }
                    else
                    {
                        ZTLteNBCellCheckNBItem nbItem = new ZTLteNBCellCheckNBItem(nbCell, (float)nRsrp, i, servCell);
                        cellItem.NBDic.Add(nbCell.Name, nbItem);
                    }
                }
            }
        }

        private void doTestPoint_GSM(TestPoint tp, List<TimePeriodNBFreq> tnfLst)
        {
            if (curLTECell == null)    //找不到当前LTE小区，不进行处理
            {
                return;
            }

            if (!resultDic.ContainsKey(curLTECell.Name))    //该LTE小区必然曾经出现过
            {
                return;
            }

            if ((tp.Time - curLTETime) > nbCellCheckCondition.TimeSpan)  //超出时间范围
            {
                return;
            }

            TimePeriodNBFreq tnf;
            getTimePeriodNBFreq(tnfLst, tp, out tnf);

            ZTLteNBCellCheckCellItem cellItem;
            cellItem = resultDic[curLTECell.Name];

            #region GSM主服小区
            Cell servCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["lte_gsm_SC_LAC"], (ushort?)(int?)tp["lte_gsm_SC_CI"],
                                                                 (short?)tp["lte_gsm_SC_BCCH"], (byte?)tp["lte_gsm_SC_BSIC"], tp.Longitude, tp.Latitude);
            if (servCell != null)
            {
                short? rxlev = (short?)tp["lte_gsm_DM_RxLevSub"];
                short? rxqual = getRxqual(tp);

                if (rxlev != null && rxlev > -110 && rxlev >= nbCellCheckCondition.RSRP)  //是合法的rxlev值
                {
                    addNBDic(cellItem, servCell, rxlev, rxqual);
                    nbCellAddMsgFreq(cellItem.NBDic[servCell.Name], tnf);
                }
            }
            #endregion

            #region GSM邻区
            for (int i = 0; i < 6; i++)
            {
                addValidNbCell_GSM(tp, tnf, cellItem, i);
            }
            #endregion
        }

        private void addNBDic(ZTLteNBCellCheckCellItem cellItem, Cell servCell, short? rxlev, short? rxqual)
        {
            if (cellItem.NBDic.ContainsKey(servCell.Name))
            {
                cellItem.NBDic[servCell.Name].MergeData((float)rxlev, (float?)rxqual, 0);
            }
            else
            {
                //主服的index默认为1，排位第一
                ZTLteNBCellCheckNBItem nbItem = new ZTLteNBCellCheckNBItem(servCell, (float)rxlev, (float?)rxqual, 0, curLTECell);
                cellItem.NBDic.Add(servCell.Name, nbItem);
            }
        }

        private static short? getRxqual(TestPoint tp)
        {
            short? rxqual = null;
            object value = tp["lte_gsm_DM_RxQualSub"];
            if (value != null)
            {
                int qual = int.Parse(value.ToString());
                if (0 <= qual && qual <= 7)
                {
                    rxqual = (short)qual;
                }
            }

            return rxqual;
        }

        private void addValidNbCell_GSM(TestPoint tp, TimePeriodNBFreq tnf, ZTLteNBCellCheckCellItem cellItem, int i)
        {
            short? nRxlev = (short?)tp["lte_gsm_NC_RxLev", i];
            short? nBcch = (short?)tp["lte_gsm_NC_BCCH", i];
            byte? nBsic = (byte?)tp["lte_gsm_NC_BSIC", i];

            if (nRxlev != null && nBcch != null && nBsic != null && nRxlev >= nbCellCheckCondition.RSRP)
            {
                Cell nbCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)nBcch, (byte)nBsic, tp.Longitude, tp.Latitude);
                if (nbCell != null)
                {
                    if (cellItem.NBDic.ContainsKey(nbCell.Name))
                    {
                        cellItem.NBDic[nbCell.Name].MergeData((float)nRxlev, -255, i + 1);     //i+1:邻区的index默认从1开始
                    }
                    else
                    {
                        ZTLteNBCellCheckNBItem nbItem = new ZTLteNBCellCheckNBItem(nbCell, (float)nRxlev, -255, i + 1, curLTECell);
                        cellItem.NBDic.Add(nbCell.Name, nbItem);
                    }
                    nbCellAddMsgFreq(cellItem.NBDic[nbCell.Name], tnf);
                }
            }
        }

        protected void nbCellAddMsgFreq(ZTLteNBCellCheckNBItem nbItem, TimePeriodNBFreq tnf)
        {
            if (tnf != null)
            {
                nbItem.AddRangeFreq(tnf.FreqLst);
            }
        }

        protected void getTimePeriodNBFreq(List<TimePeriodNBFreq> tnfLst, TestPoint tp, out TimePeriodNBFreq tnf)
        {
            tnf = null;
            while (tnfLst.Count > 0)
            {
                if (tnfLst[0].IsInPeriod(tp.DateTime))
                {
                    tnf = tnfLst[0];
                    break;
                }
                else if (tnfLst[0].IsPreviously(tp.DateTime))
                {
                    break;
                }
                tnfLst.RemoveAt(0);
            }
        }

        /// <summary>
        /// 对最终结果按照条件进行过滤
        /// </summary>
        /// <returns></returns>
        protected virtual List<ZTLteNBCellCheckCellItem> filterResultByCondition()
        {
            if (resultDic.Count > 0)
            {
                MainModel.CellManager.GetLTENBCellInfo();
            }

            List<ZTLteNBCellCheckCellItem> resultList = new List<ZTLteNBCellCheckCellItem>();
            int seqNo = 1;  //序号

            foreach (string cellName in resultDic.Keys)
            {
                LTECell servCell = resultDic[cellName].ServCell;

                //按照条件进行过滤
                List<ZTLteNBCellCheckNBItem> nbList = getNbList(cellName, servCell);

                if (nbList.Count > 0)
                {
                    nbList.Sort(ZTLteNBCellCheckNBItem.GetCompareByScore());    //按照score进行排序
                    resultDic[cellName].NBDic = new Dictionary<string, ZTLteNBCellCheckNBItem>();

                    foreach (ZTLteNBCellCheckNBItem nbItem in nbList)
                    {
                        resultDic[cellName].NBDic.Add(nbItem.CellName, nbItem);
                    }

                    resultDic[cellName].SN = seqNo++;
                    resultList.Add(resultDic[cellName]);
                }
            }

            return resultList;
        }

        private List<ZTLteNBCellCheckNBItem> getNbList(string cellName, LTECell servCell)
        {
            List<ZTLteNBCellCheckNBItem> nbList = new List<ZTLteNBCellCheckNBItem>();
            foreach (ZTLteNBCellCheckNBItem nbItem in resultDic[cellName].NBDic.Values)
            {
                if (nbItem.RSRPCount >= nbCellCheckCondition.SampleCount && nbItem.Distance < nbCellCheckCondition.Distance)
                {
                    nbItem.Status = "漏配";     //初始化为漏配

                    if (judgeNbStatus(servCell, nbItem))
                    {
                        nbList.Add(nbItem);
                    }
                }
            }

            return nbList;
        }

        private bool judgeNbStatus(LTECell servCell, ZTLteNBCellCheckNBItem nbItem)
        {
            if (nbItem.NBCell_GSM != null)      //GSM邻区
            {
                if (nbItem.TAC == servCell.TAC) //当LTE的TAC和GSM的LAC相同时，才会配置邻区，否则先进行位置更新
                {
                    nbItem.SetMsgStatus();
#if NBCellCheckByBcch
                    if (servCell.IsInGsmFreq((int)nbItem.NBCell_GSM.BCCH))
                    {
                        nbItem.Status = "在配置中";
                    }
#else
                    dealGsmStatus(servCell, nbItem);
#endif
                }
                else
                {
                    return false;   //不相同，不进行判断
                }
            }
            else                             //TDD-LTE邻区
            {
                dealLteStatus(servCell, nbItem);
            }
            return true;
        }

        private void dealGsmStatus(LTECell servCell, ZTLteNBCellCheckNBItem nbItem)
        {
            foreach (Cell nbCell in servCell.NeighbourGSMCells)
            {
                if (nbCell.Name == nbItem.NBCell_GSM.Name)   //名称相同
                {
                    nbItem.Status = "在配置中";
                    break;
                }
            }
        }

        private void dealLteStatus(LTECell servCell, ZTLteNBCellCheckNBItem nbItem)
        {
            foreach (LTECell nbCell in servCell.NeighbourCells)
            {
                if (nbCell.Name == nbItem.NBCell_LTE.Name)   //名称相同
                {
                    nbItem.Status = "在配置中";
                    break;
                }
            }
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            List<ZTLteNBCellCheckCellItem> resultList = filterResultByCondition();

            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLteNBCellCheckAnaListForm).FullName);
            ZTLteNBCellCheckAnaListForm lteNBCellCheckListForm = obj == null ? null : obj as ZTLteNBCellCheckAnaListForm;
            if (lteNBCellCheckListForm == null || lteNBCellCheckListForm.IsDisposed)
            {
                lteNBCellCheckListForm = new ZTLteNBCellCheckAnaListForm(MainModel);
            }

            lteNBCellCheckListForm.FillData(resultList);
            if (!lteNBCellCheckListForm.Visible)
            {
                lteNBCellCheckListForm.Show(MainModel.MainForm);
            }
        }

        protected virtual float? getNRsrp(TestPoint tp, int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }
    }

    public enum ECarrierInfo
    {
        eutra,
        geran,
        utra_FDD,
        utra_TDD,
        cdma2000_HRPD,
        cdma2000_1xRTT,
        utra_TDD_r10,
        nr_r15
    }

    public class TimePeriodNBFreq
    {
        public DateTime DtStart { get; set; }
        public DateTime DtEnd { get; set; }
        public List<int> FreqLst { get; set; }
        public TimePeriodNBFreq()
        { 
        }
        public TimePeriodNBFreq(DateTime dtStart, DateTime dtEnd, uint[] freqAry)
        {
            this.DtStart = dtStart;
            this.DtEnd = dtEnd;
            FreqLst = new List<int>();
            for (int i = 0; i < freqAry.Length; i++ )
            {
                FreqLst.Add((int)freqAry[i]);
            }
        }

        public bool IsInPeriod(DateTime dt)
        {
            return DtStart <= dt && dt <= DtEnd;
        }

        public bool IsPreviously(DateTime dt)
        {
            return DtStart > dt;
        }
    }
}
