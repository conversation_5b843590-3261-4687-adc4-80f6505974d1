using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GSMPoorRxQualityRoadListForm : MinCloseForm
    {
        public GSMPoorRxQualityRoadListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        private void init()
        {
            addPart1Data();
            addPart2Data();
        }

        private void addPart1Data()
        {
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnRoadName.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.roadName;
                }
                return "";
            };

            this.olvColumnDitance.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.Distance_Show;
                }
                return "";
            };

            this.olvColumnSample.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.SampleCount;
                }
                return "";
            };

            this.olvColumnMaxRxQuality.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.RxQualMaxString;
                }
                return "";
            };

            this.olvColumnMinRxQuality.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.RxQualMinString;
                }
                return "";
            };

            this.olvColumnAvgRxQuality.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.RxQualAvgString;
                }
                return "";
            };

            this.olvColumnMaxRxlev.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.RxLevMaxString;
                }
                return "";
            };

            this.olvColumnMinRxlev.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.RxLevMinString;
                }
                return "";
            };

            this.olvColumnAvgRxlev.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.RxLevAvgString;
                }
                return "";
            };
            this.olvColumnCells.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.CellsStr;
                }
                return "";
            };

            this.olvColumnCellNames.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.CellNames;
                }
                return "";
            };
        }

        private void addPart2Data()
        {
            this.olvColumnMaxC2I.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.C2IMaxString;
                }
                return "";
            };

            this.olvColumnMinC2I.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.C2IMinString;
                }
                return "";
            };

            this.olvColumnAvgC2I.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.C2IAvgString;
                }
                return "";
            };

            this.olvColumnLongitudeMid.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.LongitudeMid;
                }
                return "";
            };

            this.olvColumnLatitudeMid.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.LatitudeMid;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.fileName;
                }
                return "";
            };

            this.olvColumnFirstTime.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.getFirstTime();
                }
                return "";
            };

            this.olvColumnLastTime.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.getLasttime();
                }
                return "";
            };
            this.olvColumnGridName.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.GridName;
                }
                return "";
            };
            this.olvColumnAreaAgentName.AspectGetter = delegate (object row)
            {
                if (row is GSMPoorRxQualityRoadInfo)
                {
                    GSMPoorRxQualityRoadInfo item = row as GSMPoorRxQualityRoadInfo;
                    return item.AreaAgentName;
                }
                return "";
            };
        }

        public void FillData(List<GSMPoorRxQualityRoadInfo> roadRxQualityList)
        {
            ListViewRoad.RebuildColumns();
            ListViewRoad.ClearObjects();
            ListViewRoad.SetObjects(roadRxQualityList);//(MainModel.TdPoorBlerRoadCovLst);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewRoad.SelectedObject is GSMPoorRxQualityRoadInfo)
            {
                List<Cell> clList = new List<Cell>();
                List<WCell> wlList = new List<WCell>();
                GSMPoorRxQualityRoadInfo info = ListViewRoad.SelectedObject as GSMPoorRxQualityRoadInfo;
                foreach (MasterCom.RAMS.Model.ICell ic in info.Cells)
                {
                    if (ic is Cell)
                    { 
                        clList.Add(ic as Cell);
                    }
                    else if (ic is WCell)
                    {
                        wlList.Add(ic as WCell);
                    }
                }
                if (clList.Count > 0)
                {
                    mModel.SelectedCells = clList;
                }
                else if (wlList.Count > 0)
                {
                    mModel.SelectedWCells = wlList;
                }
                //mModel.SelectedCells = info.Cells;
                mModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.sampleLst)
                {
                    mModel.DTDataManager.Add(tp);
                }
                mModel.FireDTDataChanged(this);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewRoad.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewRoad.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewRoad);
        }
    }
}