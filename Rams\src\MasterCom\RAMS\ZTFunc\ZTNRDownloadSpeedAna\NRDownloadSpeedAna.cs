﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRDownloadSpeedAna : DIYAnalyseByFileBackgroundBase
    {
        List<NRDownloadSpeedAnaRes> resList;
        NRDownloadSpeedAnaCondition curCondition;

        protected static readonly object lockObj = new object();
        protected NRDownloadSpeedAna()
            : base(MainModel.GetInstance())
        {
            IncludeMessage = true;

            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_APP_type");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_SA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_DATA);
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35050, this.Name);
        }

        protected override bool getCondition()
        {
            NRDownloadSpeedAnaDlg dlg = new NRDownloadSpeedAnaDlg();
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            resList = new List<NRDownloadSpeedAnaRes>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                List<NRDownloadSpeedAnaInfo> infoList = dealMsgs(file);

                //按小区,分钟汇聚结果
                dealRes(file, infoList);
            }
        }

        private List<NRDownloadSpeedAnaInfo> dealMsgs(DTFileDataManager file)
        {
            List<NRDownloadSpeedAnaInfo> infoList = new List<NRDownloadSpeedAnaInfo>();
            int tpIdx = 0;
            for (int i = 0; i < file.Messages.Count; i++)
            {
                Message msg = file.Messages[i];
                //FTP_Download_Qos || FTP_Download_Continue
                if (msg.ID == 2147418660 || msg.ID == 2147418636)
                {
                    NRDownloadSpeedAnaInfo info = new NRDownloadSpeedAnaInfo();
                    //最近的一个采样点
                    TestPoint tp = getNearestTP(file, ref tpIdx, msg, info);

                    //判断采样点是否有效
                    bool isValid = judgeValidTP(info, tp);
                    if (!isValid)
                    {
                        continue;
                    }

                    //获取信令数据
                    MessageWithSource msgSource = msg as MessageWithSource;
                    MessageDecodeHelper.StartDissect(msgSource.Direction, msgSource.Source, msgSource.Length, msg.ID);
                    string[] strs = new string[5];
                    if (MessageDecodeHelper.GetMultiString("mt.message", ref strs, 5) && strs.Length >= 4)
                    {
                        info.Size += Convert.ToInt32(strs[2].Split(':')[1]);
                        info.Time += Convert.ToInt32(strs[3].Split(':')[1]);
                        infoList.Add(info);
                    }
                }
            }

            return infoList;
        }


        private TestPoint getNearestTP(DTFileDataManager file, ref int tpIdx, Message msg, NRDownloadSpeedAnaInfo info)
        {
            TestPoint tp = null;
            for (; tpIdx < file.TestPoints.Count; tpIdx++)
            {
                tp = file.TestPoints[tpIdx];
                if (tp.lTimeWithMillsecond > msg.lTimeWithMillsecond)
                {
                    info.TP = tp;
                    tpIdx++;
                    break;
                }
            }

            return tp;
        }

        private bool judgeValidTP(NRDownloadSpeedAnaInfo info, TestPoint tp)
        {
            if (tp != null && isValidTestPoint(tp))
            {
                int? earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                if (earfcn != null && pci != null)
                {
                    info.EARFCN = (int)earfcn;
                    info.PCI = (int)pci;
                    NRCell cell = tp.GetMainCell_NR();
                    if (cell != null)
                    {
                        info.CellName = cell.Name;
                    }
                    else
                    {
                        info.CellName = info.EARFCN + "_" + info.PCI;
                    }
                    return true;
                }
            }
            return false;
        }

        private void dealRes(DTFileDataManager file, List<NRDownloadSpeedAnaInfo> infoList)
        {
            Dictionary<string, NRDownloadSpeedAnaRes> dic = new Dictionary<string, NRDownloadSpeedAnaRes>();
            foreach (var info in infoList)
            {
                if (info.Time < curCondition.TransferedTimeLimit)
                {
                    continue;
                }

                string minute = info.TP.DateTime.ToString("yy-MM-dd HH:mm");
                StringBuilder sb = new StringBuilder();
                sb.Append(info.CellName);
                sb.Append(minute);
                NRDownloadSpeedAnaRes res;
                if (!dic.TryGetValue(sb.ToString(), out res))
                {
                    res = new NRDownloadSpeedAnaRes();
                    dic.Add(sb.ToString(), res);
                    res.FileName = file.FileName;
                    res.Time = minute;
                    res.EARFCN = info.EARFCN;
                    res.PCI = info.PCI;
                    res.CellName = info.CellName;
                }
                res.Count++;
                res.TransferSize += info.Size;
                res.TransferTime += info.Time;
            }
            resList.AddRange(dic.Values);
        }

        protected override void fireShowForm()
        {
            NRDownloadSpeedAnaForm frm = MainModel.CreateResultForm(typeof(NRDownloadSpeedAnaForm)) as NRDownloadSpeedAnaForm;
            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NRDownloadSpeedAnaByFile : NRDownloadSpeedAna
    {
        private NRDownloadSpeedAnaByFile()
            : base()
        {
        }

        private static NRDownloadSpeedAnaByFile instance = null;
        public static NRDownloadSpeedAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRDownloadSpeedAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR单位时间速率统计分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class NRDownloadSpeedAnaByRegion : NRDownloadSpeedAna
    {
        protected NRDownloadSpeedAnaByRegion()
            : base()
        {
        }

        private static NRDownloadSpeedAnaByRegion instance = null;
        public static NRDownloadSpeedAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRDownloadSpeedAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR单位时间速率统计分析(按区域)"; }
        }
    }
}
