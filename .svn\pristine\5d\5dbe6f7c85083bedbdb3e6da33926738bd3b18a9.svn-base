﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDMOSParam
    {
        public TDMOSParam(DateTime startTime)
        {
            OwnParam = new TDParam(startTime);
            OtherParam = new TDParam(startTime);
        }

        public int SN { get; set; }
        public string FileName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public float MOS { get; set; }
        public int MSMode { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public TDParam OwnParam { get; set; }
        public TDParam OtherParam { get; set; }

        public override string ToString()
        {
            List<object> objs = GetContent();
            StringBuilder sb = new StringBuilder();
            foreach (object obj in objs)
            {
                sb.Append(obj.ToString() + "\t");
            }
            return sb.ToString();
        }
    }

    public partial class TDParam
    {
        private float td_PCCPCH_RSCP_Sum;
        public float TD_PCCPCH_RSCP_Sum { get { return td_PCCPCH_RSCP_Sum; } }
        private int td_PCCPCH_RSCP_Count;
        public int TD_PCCPCH_RSCP_Count { get { return td_PCCPCH_RSCP_Count; } }
        public float TD_PCCPCH_RSCP_Mean { get; set; }

        private float td_PCCPCH_ISCP_Sum;
        public float TD_PCCPCH_ISCP_Sum { get { return td_PCCPCH_ISCP_Sum; } }
        private int td_PCCPCH_ISCP_Count;
        public int TD_PCCPCH_ISCP_Count { get { return td_PCCPCH_ISCP_Count; } }
        public float TD_PCCPCH_ISCP_Mean { get; set; }

        private int td_PCCPCH_C2I_Sum;
        public int TD_PCCPCH_C2I_Sum { get { return td_PCCPCH_C2I_Sum; } }
        private int td_PCCPCH_C2I_Count;
        public int TD_PCCPCH_C2I_Count { get { return td_PCCPCH_C2I_Count; } }
        public float TD_PCCPCH_C2I_Mean { get; set; }

        private float td_PCCPCH_SIR_Sum;
        public float TD_PCCPCH_SIR_Sum { get { return td_PCCPCH_SIR_Sum; } }
        public float TD_PCCPCH_SIR_Mean { get; set; }
        private int td_PCCPCH_SIR_Count;
        public int TD_PCCPCH_SIR_Count { get { return td_PCCPCH_SIR_Count; } }

        private int td_PCCPCH_Pathloss_Sum;
        public int TD_PCCPCH_Pathloss_Sum { get { return td_PCCPCH_Pathloss_Sum; } }
        public float TD_PCCPCH_Pathloss_Mean { get; set; }
        private int td_PCCPCH_Pathloss_Count;
        public int TD_PCCPCH_Pathloss_Count { get { return td_PCCPCH_Pathloss_Count; } }

        private int td_TxPower_Sum;
        public int TD_TxPower_Sum { get { return td_TxPower_Sum; } }
        public float TD_TxPower_Mean { get; set; }
        private int td_TxPower_Count;
        public int TD_TxPower_Count { get { return td_TxPower_Count; } }

        private float td_DPCH_RSCP_Sum;
        public float TD_DPCH_RSCP_Sum { get { return td_DPCH_RSCP_Sum; } }
        public float TD_DPCH_RSCP_Mean { get; set; }
        private int td_DPCH_RSCP_Count;
        public int TD_DPCH_RSCP_Count { get { return td_DPCH_RSCP_Count; } }

        private float td_DPCH_ISCP_Sum;
        public float TD_DPCH_ISCP_Sum { get { return td_DPCH_ISCP_Sum; } }
        public float TD_DPCH_ISCP_Mean { get; set; }
        private int td_DPCH_ISCP_Count;
        public int TD_DPCH_ISCP_Count { get { return td_DPCH_ISCP_Count; } }

        private int td_DPCH_C2I_Sum;
        public int TD_DPCH_C2I_Sum { get { return td_DPCH_C2I_Sum; } }
        public float TD_DPCH_C2I_Mean { get; set; }
        private int td_DPCH_C2I_Count;
        public int TD_DPCH_C2I_Count { get { return td_DPCH_C2I_Count; } }

        private int td_DPCH_PathLoss_Sum;
        public int TD_DPCH_PathLoss_Sum { get { return td_DPCH_PathLoss_Sum; } }
        public float TD_DPCH_PathLoss_Mean { get; set; }
        private int td_DPCH_PathLoss_Count;
        public int TD_DPCH_PathLoss_Count { get { return td_DPCH_PathLoss_Count; } }

        private int td_BLER_Sum;
        public int TD_BLER_Sum { get { return td_BLER_Sum; } }
        public float TD_BLER_Mean { get; set; }
        private int td_BLER_Count;
        public int TD_BLER_Count { get { return td_BLER_Count; } }

        private int td_TA_Sum;
        public int TD_TA_Sum { get { return td_TA_Sum; } }
        public float TD_TA_Mean { get; set; }
        private int td_TA_Count;
        public int TD_TA_Count { get { return td_TA_Count; } }

        private int td_CarrierRSSI_Sum;
        public int TD_CarrierRSSI_Sum { get { return td_CarrierRSSI_Sum; } }
        public float TD_CarrierRSSI_Mean { get; set; }
        private int td_CarrierRSSI_Count;
        public int TD_CarrierRSSI_Count { get { return td_CarrierRSSI_Count; } }

        public int HandoverCount { get; set; }
        public int CellReselectionCount { get; set; }
        public int CellUpdateCount { get; set; }

        public List<int> LacList { get; set; }
        public List<int> CiList { get; set; }

        public List<int> LacListForHo { get; set; }
        public List<int> CiListForHo { get; set; }

        public TDParam(DateTime startTime)
        {
            LacList = new List<int>();
            CiList = new List<int>();
            LacListForHo = new List<int>();
            CiListForHo = new List<int>();
        }

        public void Fill(List<TestPoint> tps, List<Event> evts, DateTime endTime)
        {
            FillTestPoint(tps);
            FillEvent(evts, endTime);
            CalcMeanValue();
        }

        private void FillTestPoint(List<TestPoint> tps)
        {
            foreach (TestPoint tp in tps)
            {
                RFTP(tp, "TD_PCCPCH_RSCP", ref td_PCCPCH_RSCP_Sum, ref td_PCCPCH_RSCP_Count, -140, -10);
                RFTP(tp, "TD_PCCPCH_ISCP", ref td_PCCPCH_ISCP_Sum, ref td_PCCPCH_ISCP_Count, -140, -10);
                RFTP(tp, "TD_PCCPCH_C2I", ref td_PCCPCH_C2I_Sum, ref td_PCCPCH_C2I_Count, -20, 25);
                RFTP(tp, "TD_PCCPCH_SIR", ref td_PCCPCH_SIR_Sum, ref td_PCCPCH_SIR_Count, -11, 20);
                RFTP(tp, "TD_PCCPCH_Pathloss", ref td_PCCPCH_Pathloss_Sum, ref td_PCCPCH_Pathloss_Count, 1, 100);
                RFTP(tp, "TD_TxPower", ref td_TxPower_Sum, ref td_TxPower_Count, -50, 34);
                RFTP(tp, "TD_DPCH_RSCP", ref td_DPCH_RSCP_Sum, ref td_DPCH_RSCP_Count, -140, 10);
                RFTP(tp, "TD_DPCH_ISCP", ref td_DPCH_ISCP_Sum, ref td_DPCH_ISCP_Count, -140, 10);
                RFTP(tp, "TD_DPCH_C2I", ref td_DPCH_C2I_Sum, ref td_DPCH_C2I_Count, -20, 25);
                RFTP(tp, "TD_DPCH_PathLoss", ref td_DPCH_PathLoss_Sum, ref td_DPCH_PathLoss_Count, 30, 165);
                RFTP(tp, MainModel.TD_TA, ref td_TA_Sum, ref td_TA_Count, 0, 100000);
                RFTP(tp, "TD_CarrierRSSI", ref td_CarrierRSSI_Sum, ref td_CarrierRSSI_Count, -100, -25);

                int sum = 0, cnt = 0;
                for (int i = 0; i < 8; ++i)
                {
                    int? val = (int?)tp["TD_BLER", i];
                    if (val != null && val >= 0 && val <= 100)
                    {
                        sum += (int)val;
                        ++cnt;
                        continue;
                    }
                    break;
                }
                if (cnt != 0)
                {
                    td_BLER_Sum += sum / cnt;
                    ++td_BLER_Count;
                }

                LacList.Add((int?)tp["TD_SCell_LAC"] ?? -255);
                CiList.Add((int?)tp["TD_SCell_CI"] ?? -255);
            }
        }

        private void FillEvent(List<Event> evts, DateTime endTime)
        {
            foreach (Event e in evts)
            {
                if (e.DateTime > endTime && !handOverSuccess.Contains(e.ID))
                {
                    continue;
                }
                if (handOverSuccess.Contains(e.ID))
                {
                    ++HandoverCount;
                    LacListForHo.Add((int?)e["LAC"] ?? -255);
                    CiListForHo.Add((int?)e["CI"] ?? -255);
                    LacListForHo.Add((int?)e["TargetLAC"] ?? -255);
                    CiListForHo.Add((int?)e["TargetCI"] ?? -255);
                }
                if (cellReselection.Contains(e.ID))
                {
                    ++CellReselectionCount;
                }
                if (cellUpdate.Contains(e.ID))
                {
                    ++CellUpdateCount;
                }
            }
        }

        private void CalcMeanValue()
        {
            TD_PCCPCH_RSCP_Mean = RFMV(TD_PCCPCH_RSCP_Sum, TD_PCCPCH_RSCP_Count);
            TD_PCCPCH_ISCP_Mean = RFMV(TD_PCCPCH_ISCP_Sum, TD_PCCPCH_ISCP_Count);
            TD_PCCPCH_C2I_Mean = RFMV(TD_PCCPCH_C2I_Sum, TD_PCCPCH_C2I_Count);
            TD_PCCPCH_SIR_Mean = RFMV(TD_PCCPCH_SIR_Sum, TD_PCCPCH_SIR_Count);
            TD_PCCPCH_Pathloss_Mean = RFMV(TD_PCCPCH_Pathloss_Sum, TD_PCCPCH_Pathloss_Count);
            TD_TxPower_Mean = RFMV(TD_TxPower_Sum, TD_TxPower_Count);
            TD_DPCH_RSCP_Mean = RFMV(TD_DPCH_RSCP_Sum, TD_DPCH_RSCP_Count);
            TD_DPCH_ISCP_Mean = RFMV(TD_DPCH_ISCP_Sum, TD_DPCH_ISCP_Count);
            TD_DPCH_C2I_Mean = RFMV(TD_DPCH_C2I_Sum, TD_DPCH_C2I_Count);
            TD_DPCH_PathLoss_Mean = RFMV(TD_DPCH_PathLoss_Sum, TD_DPCH_PathLoss_Count);
            TD_BLER_Mean = RFMV(TD_BLER_Sum, TD_BLER_Count);
            TD_TA_Mean = RFMV(TD_TA_Sum, TD_TA_Count);
            TD_CarrierRSSI_Mean = RFMV(TD_CarrierRSSI_Sum, TD_CarrierRSSI_Count);
        }

        private float RFMV(float sum, int cnt)
        {
            float mean;
            if (cnt == 0)
            {
                mean = 0;
            }
            else
            {
                mean = sum / cnt;
            }
            return mean;
        }

        private void RFTP (TestPoint tp, string name, ref int sum, ref int count, int minVal, int maxVal)
        {
            int? value = (int?)tp[name];
            if (value != null && value >= minVal && value <= maxVal)
            {
                sum += (int)value;
                ++count;
            }
        }

        private void RFTP(TestPoint tp, string name, ref float sum, ref int count, float minVal, float maxVal)
        {
            float? value = (float?)tp[name];
            if (value != null && value >= minVal && value <= maxVal)
            {
                sum += (float)value;
                ++count;
            }
        }

        public override string ToString()
        {
            object[] objs = GetContent();
            StringBuilder sb = new StringBuilder();
            foreach (object obj in objs)
            {
                sb.Append(obj.ToString() + "\t");
            }
            return sb.ToString();
        }

        private static List<int> handOverSuccess = InitHandOverSuccess();
        private static List<int> cellReselection = InitCellReselection();
        private static List<int> cellUpdate = InitCellUpdate();

        static List<int> InitHandOverSuccess()
        {
            List<int> evtList = new List<int>();
            evtList.Add((int)TDEventID.TD_HandoverSuccess_T2G);
            evtList.Add((int)TDEventID.TD_HandoverSuccess_IntraT);
            evtList.Add((int)TDEventID.TD_HandoverSuccess_Baton);
            evtList.Add((int)TDEventID.TD_HandvoerSuccess_IntraG);
            evtList.Add((int)TDEventID.TD_HandoverSuccess_RBReConfigT);
            return evtList;
        }

        static List<int> InitCellReselection()
        {
            List<int> evtList = new List<int>();
            evtList.Add((int)TDEventID.TD_CellReselection_T2G);
            evtList.Add((int)TDEventID.TD_CellReselection_G2T);
            evtList.Add((int)TDEventID.TD_CellReselection_T2T);
            evtList.Add((int)TDEventID.TD_CellReselection_G2G);
            return evtList;
        }

        static List<int> InitCellUpdate()
        {
            List<int> evtList = new List<int>();
            evtList.Add((int)TDEventID.TD_CellUpdate);
            return evtList;
        }
    }

    public partial class TDMOSParam
    {
        public static List<object> GetTitle()
        {
            List<object> retList = new List<object>(
                new object[] {
                    "文件名",
                    "序号",
                    "起始时间",
                    "结束时间",
                    "MSMode",
                    "MOS值",
                    "经度",
                    "纬度",
                }
            );
            retList.AddRange(TDParam.GetTitle(""));
            retList.AddRange(TDParam.GetTitle("对端"));
            return retList;
        }

        public List<object> GetContent()
        {
            List<object> retList = new List<object>(
                new object[] {
                    FileName,
                    SN,
                    StartTime,
                    EndTime,
                    MSMode,
                    MOS,
                    Longitude,
                    Latitude,
                }
            );
            retList.AddRange(OwnParam.GetContent());
            retList.AddRange(OtherParam.GetContent());
            return retList;
        }
    }

    public partial class TDParam
    {
        public static object[] GetTitle(string prefix)
        {
            return new object[] {
                prefix + "TD_PCCPCH_RSCP",
                prefix + "TD_PCCPCH_ISCP",
                prefix + "TD_PCCPCH_C2I", 
                prefix + "TD_PCCPCH_SIR", //5
                prefix + "TD_PCCPCH_Pathloss",
                prefix + "TD_TxPower",
                prefix + "TD_DPCH_RSCP",
                prefix + "TD_DPCH_ISCP", 
                prefix + "TD_DPCH_C2I", //10
                prefix + "TD_DPCH_PathLoss",
                prefix + "TD_TA",
                prefix + "TD_CarrierRSSI",
                prefix + "TD_BLER", 
                prefix + "切换次数", //15
                prefix + "重选次数",
                prefix + "更新次数",
                prefix + "LAC-CI", // 18
            };
        }

        public object[] GetContent()
        {
            return new object[] {
                TD_PCCPCH_RSCP_Mean,
                TD_PCCPCH_ISCP_Mean,
                TD_PCCPCH_C2I_Mean, 
                TD_PCCPCH_SIR_Mean, // 5
                TD_PCCPCH_Pathloss_Mean,
                TD_TxPower_Mean,
                TD_DPCH_RSCP_Mean,
                TD_DPCH_ISCP_Mean, 
                TD_DPCH_C2I_Mean, // 10
                TD_DPCH_PathLoss_Mean,
                TD_TA_Mean,
                TD_CarrierRSSI_Mean,
                TD_BLER_Mean, 
                HandoverCount, //15
                CellReselectionCount,
                CellUpdateCount,
                GetLacCiStr(), // 18
            };
        }

        private string GetLacCiStr()
        {
            List<int> existed = new List<int>();
            StringBuilder sb = new StringBuilder();

            List<int> lacList = null;
            List<int> ciList = null;

            if (HandoverCount < 1)
            {
                lacList = LacList;
                ciList = CiList;
            }
            else
            {
                lacList = LacListForHo;
                ciList = CiListForHo;
            }

            for (int i = 0; i < lacList.Count && i < ciList.Count; ++i)
            {
                int key = CellManager.MakeLACCIKey((ushort)lacList[i], (ushort)ciList[i]);
                if (existed.Contains(key))
                {
                    continue;
                }
                existed.Add(key);
                sb.Append(lacList[i]);
                sb.Append("-");
                sb.Append(ciList[i]);
                sb.Append("; ");
            }
            
            return sb.ToString();
        }
    }
}
