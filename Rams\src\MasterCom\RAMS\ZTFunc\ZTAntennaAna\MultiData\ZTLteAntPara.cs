﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntPara : ZTAntennaBase
    {
        public ZTLteAntPara(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            this.mainModel = mainModel;

            antCfgParaDic = new Dictionary<int, AntCfgSub>();
            antCfgParaSDic = new Dictionary<int, AntCfgSub>();
            antScanInfoDic = new Dictionary<int, ZTLteScanAntenna.AntInfoItem>();
            antScanSubEciInfoDic = new Dictionary<int, ZTLteScanAntenna.AntInfoItem>();
            antInfoDic = new Dictionary<int, ZTLteAntenna.CellInfoOutPutItem>();
            antInfoSDic = new Dictionary<int, ZTLteAntenna.CellInfoOutPutItem>();
            scanR0R1ItemEciDic = new Dictionary<int, SCANR0R1Item>();
            scanR0R1ItemEciSDic = new Dictionary<int, SCANR0R1Item>();
            mimoCellAngleDataDic = new Dictionary<int, ZTLteMiMoAntenna.CellAngleData>();
        }

        public override string Name
        {
            get { return "LTE天线综合分析"; }
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28001, this.Name);
        }

        protected override void query()
        {
            ZTDataCfgForm dataCfg = new ZTDataCfgForm(true, "LTE");
            if (dataCfg.ShowDialog() != DialogResult.OK)
                return;
            iFunc = dataCfg.iFunc;
            if (dataCfg.isCompareStat && (dataCfg.timeCfgList.Count < 2
                || dataCfg.timeCfgList[0].DStime == dataCfg.timeCfgList[1].DStime))
            {
                MessageBox.Show("对比月份时间选择有误，或错误，或与当前月相同");
                return;
            }
            GetDt_ScanProject(dataCfg.timeCfgList[0], "LTE");
            if (strProject[0].Length == 0 || strProject[1].Length == 0)
            {
                MessageBox.Show("天线角度表获取扫频或路测项目ID异常！！！");
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                Dictionary<string, CellParaData> cellParaDataDicTmp = new Dictionary<string, CellParaData>();
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                dealData(dataCfg, cellParaDataDicTmp);
                cellParaDataDic = cellParaDataDicTmp;
                dealMainUtranCellSample();
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                clearData();
                MainModel.ClearDTData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
            }
        }

        private void dealData(ZTDataCfgForm dataCfg, Dictionary<string, CellParaData> cellParaDataDicTmp)
        {
            for (int i = 0; i < 2; i++)
            {
                if (!dataCfg.isCompareStat && i == 1)
                {
                    break;
                }
                MainModel.ClearDTData();
                cellParaDataDic.Clear();
                timeCfg = dataCfg.timeCfgList[i];
                WaitBox.Show("1.读取MR数据处理...", doWithCellMRData);
                WaitBox.Show("2.读取扫频数据处理...", doWithCellScanData);
                WaitBox.Show("3.读取小区性能数据...", doWithCellParaData);
                WaitBox.Show("4.读取状态库天线信息...", doWithAntCfgData);
                WaitBox.Show("5.读取小区权值数据...", doWithParaData);
                WaitBox.Show("6.天线分析数据处理...", AnaCellTestData);
                WaitBox.Show("7.联合数据处理...", AnaCellAngleData);
                if (iFunc != 2)
                    WaitBox.Show("8.获取天线角度赋形数据...", AnaAngleLevelData);
                if (cellParaDataDicTmp.Keys.Count == 0)
                {
                    foreach (string strCell in cellParaDataDic.Keys)
                    {
                        cellParaDataDicTmp.Add(strCell, cellParaDataDic[strCell]);
                    }
                }
                else
                {
                    setCellParaDataCompareStatus(cellParaDataDicTmp);
                }
            }
        }

        private void setCellParaDataCompareStatus(Dictionary<string, CellParaData> cellParaDataDicTmp)
        {
            foreach (string strCell in cellParaDataDic.Keys)
            {
                if (cellParaDataDicTmp.ContainsKey(strCell))
                {
                    if (cellParaDataDic[strCell].strProbType != ""
                        && cellParaDataDicTmp[strCell].strProbType != "")
                    {
                        cellParaDataDicTmp[strCell].strCompareStatus = "遗留";
                    }
                    else if (cellParaDataDic[strCell].strProbType != ""
                        && cellParaDataDicTmp[strCell].strProbType == "")
                    {
                        cellParaDataDicTmp[strCell].strCompareStatus = "解决";
                    }
                    else if (cellParaDataDic[strCell].strProbType == ""
                        && cellParaDataDicTmp[strCell].strProbType != "")
                    {
                        cellParaDataDicTmp[strCell].strCompareStatus = "新增";
                    }
                }
            }
            foreach (string strCell in cellParaDataDicTmp.Keys)
            {
                if (!cellParaDataDic.ContainsKey(strCell) && cellParaDataDicTmp[strCell].strProbType != "")
                {
                    cellParaDataDicTmp[strCell].strCompareStatus = "新增";
                }
            }
        }

        #region 公共变量
        int iFunc = 1;
        AntTimeCfg timeCfg = new AntTimeCfg();
        string strCityName = "";
        //天线权值数据
        Dictionary<int, AntennaPara> antParaEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<int, AntennaPara> antParaEciSDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antParaCellNameDic = new Dictionary<string, AntennaPara>();
        //LTE MR指标
        Dictionary<int, LteMrItem> lteMREciDic = new Dictionary<int, LteMrItem>();
        Dictionary<int, LteMrItem> lteMREciSDic = new Dictionary<int, LteMrItem>();
        //小区性能数据
        Dictionary<int, CellPara> cellParaEciDic = new Dictionary<int, CellPara>();
        Dictionary<int, CellPara> cellParaEciSDic = new Dictionary<int, CellPara>();//Sector取一位数
        //状态库天线数据
        public Dictionary<int, AntCfgSub> antCfgParaDic { get; set; }
        public Dictionary<int, AntCfgSub> antCfgParaSDic { get; set; }
        //天线扫频分析数据
        public Dictionary<int, ZTLteScanAntenna.AntInfoItem> antScanInfoDic { get; set; }
        public Dictionary<int, ZTLteScanAntenna.AntInfoItem> antScanSubEciInfoDic { get; set; }
        //LTE MR覆盖指数
        Dictionary<int, LteCoverItem> lteMRCoverEciDic = new Dictionary<int, LteCoverItem>();
        Dictionary<int, LteCoverItem> lteMRCoverEciSDic = new Dictionary<int, LteCoverItem>();

        //天线分析数据
        Dictionary<string, CellParaData> cellParaDataDic = new Dictionary<string, CellParaData>();
        //路测天线分析数据
        public Dictionary<int, ZTLteAntenna.CellInfoOutPutItem> antInfoDic { get; set; }
        public Dictionary<int, ZTLteAntenna.CellInfoOutPutItem> antInfoSDic { get; set; }
        //小区性能数据
        Dictionary<int, CellParaRank> cellParaRankEciDic = new Dictionary<int, CellParaRank>();
        Dictionary<int, CellParaRank> cellParaRankEciSDic = new Dictionary<int, CellParaRank>();//Sector取一位数
        //扫频R0R1统计
        public Dictionary<int, SCANR0R1Item> scanR0R1ItemEciDic { get; set; }
        public Dictionary<int, SCANR0R1Item> scanR0R1ItemEciSDic { get; set; }
        //MR分类统计
        Dictionary<int, ZTAntMRBaseItem> lteMRAoaEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRAoaEciSDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRTAEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRTAEciSDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRTAAoaEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRTAAoaEciSDic = new Dictionary<int, ZTAntMRBaseItem>();
        public Dictionary<int, ZTLteMiMoAntenna.CellAngleData> mimoCellAngleDataDic { get; set; }

        readonly Dictionary<int, int> cellEciDic = new Dictionary<int, int>();//分析问题点小区清单
        readonly Dictionary<string, ProblemType> cellProNum = new Dictionary<string, ProblemType>();//小区问题点分析数

        private void clearData()
        {
            antParaEciDic.Clear();
            antParaEciSDic.Clear();
            antParaCellNameDic.Clear();

            lteMREciDic.Clear();
            lteMREciSDic.Clear();

            cellParaEciDic.Clear();
            cellParaEciSDic.Clear();

            antCfgParaDic.Clear();
            antCfgParaSDic.Clear();

            antScanInfoDic.Clear();
            antScanSubEciInfoDic.Clear();

            lteMRCoverEciDic.Clear();
            lteMRCoverEciSDic.Clear();

            antInfoDic.Clear();
            antInfoSDic.Clear();

            lteMRAoaEciDic.Clear();
            lteMRAoaEciSDic.Clear();
            lteMRTAEciDic.Clear();
            lteMRTAEciSDic.Clear();
            lteMRTAAoaEciDic.Clear();
            lteMRTAAoaEciSDic.Clear();

            cellEciDic.Clear();
        }
        #endregion

        #region 接口数据查询

        /// <summary>
        /// 设置需要的字段
        /// </summary>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LteAntenna");
            tmpDic.Add("themeName", (object)"lte_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        /// <summary>
        /// 分析小区级辐射统计数据
        /// </summary>
        private void AnaCellTestData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            antScanInfoDic = ZTLteScanAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[0]);
            foreach (int ieci in antScanInfoDic.Keys)
            {
                int iNewEci = (ieci / 256) * 256 + ((ieci % 256) % 10);
                if (!antScanSubEciInfoDic.ContainsKey(iNewEci))
                {
                    antScanSubEciInfoDic.Add(iNewEci, antScanInfoDic[ieci]);
                }
            }
            WaitBox.ProgressPercent = 60;
            antInfoDic = ZTLteAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[1]);
            foreach (int ieci in antInfoDic.Keys)
            {
                int iNewEci = (ieci / 256) * 256 + ((ieci % 256) % 10);
                if (!antInfoSDic.ContainsKey(iNewEci))
                {
                    antInfoSDic.Add(iNewEci, antInfoDic[ieci]);
                }
            }

            WaitBox.Close();
        }

        /// <summary>
        /// 分析小区角度级赋形数据
        /// </summary>
        private void AnaAngleLevelData()
        {
            if (iFunc == 3)
                cellEciDic.Clear();

            //扫频
            WaitBox.ProgressPercent = 20;
            WaitBox.Text = "8.获取天线角度赋形扫频数据...";
            Dictionary<int, ZTLteScanAntenna.CellInfoItem> cellScanInfoDic;
            DiyQueryCellAngleResult scanQuery = new DiyQueryCellAngleResult(MainModel, cellEciDic);
            scanQuery.SetCondition(timeCfg.ISitme, timeCfg.IEitme);
            scanQuery.Query();
            cellScanInfoDic = scanQuery.cellInfoDic;

            //路测
            WaitBox.ProgressPercent = 50;
            WaitBox.Text = "8.获取天线角度赋形路测数据...";
            Dictionary<int, ZTLteAntenna.CellInfoItem> cellDTInfoDic;
            DIYQueryCellDTAngleResult dtQuery = new DIYQueryCellDTAngleResult(MainModel, cellEciDic);
            dtQuery.SetCondition(timeCfg.ISitme, timeCfg.IEitme);
            dtQuery.Query();
            cellDTInfoDic = dtQuery.cellInfoDic;

            WaitBox.ProgressPercent = 80;
            WaitBox.Text = "8.获取天线角度赋形数据...";
            foreach (string cellName in cellParaDataDic.Keys)
            {
                CellParaData cellPara = cellParaDataDic[cellName];
                int iNewEci = (cellPara.iEci / 256) * 256 + ((cellPara.iEci % 256) % 10);

                if (cellScanInfoDic.ContainsKey(iNewEci))
                {
                    cellPara.ciItem = cellScanInfoDic[iNewEci];
                }

                if (cellDTInfoDic.ContainsKey(iNewEci))
                {
                    cellPara.ciopItem.citem = cellDTInfoDic[iNewEci];
                }
            }
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区性能参数数据
        /// </summary>
        private void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyCellPara cellPara = new DiyCellPara(MainModel, timeCfg);
            cellPara.SetQueryCondition(condition);
            cellPara.Query();
            cellParaEciDic = cellPara.cellParaEciDic;
            cellParaEciSDic = cellPara.cellParaEciSDic;

            WaitBox.ProgressPercent = 50;
            DiyCellParaRank cellParaRank = new DiyCellParaRank(MainModel, timeCfg);
            cellParaRank.SetQueryCondition(condition);
            cellParaRank.Query();
            cellParaRankEciDic = cellParaRank.cellParaRankEciDic;
            cellParaRankEciSDic = cellParaRank.cellParaRankEciSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 小区MR数据
        /// </summary>
        private void doWithCellMRData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyLTECellMR lteCelMR = new DiyLTECellMR(MainModel, timeCfg);
            lteCelMR.SetQueryCondition(condition);
            lteCelMR.Query();
            lteMREciDic = lteCelMR.lteMREciDic;
            lteMREciSDic = lteCelMR.lteMREciSDic;

            WaitBox.ProgressPercent = 40;
            DiyLTEMRCover lteCover = new DiyLTEMRCover(MainModel, timeCfg);
            lteCover.SetQueryCondition(condition);
            lteCover.Query();
            lteMRCoverEciDic = lteCover.lteMREciDic;
            lteMRCoverEciSDic = lteCover.lteMREciSDic;

            WaitBox.ProgressPercent = 60;
            DiyLTEMRData lteMRAoaData = new DiyLTEMRData(MainModel, 4, 72, timeCfg);
            lteMRAoaData.SetQueryCondition(condition);
            lteMRAoaData.Query();
            lteMRAoaEciDic = lteMRAoaData.lteMREciDic;
            lteMRAoaEciSDic = lteMRAoaData.lteMREciSDic;

            WaitBox.ProgressPercent = 80;
            DiyLTEMRData lteMRTAData = new DiyLTEMRData(MainModel, 6, 44, timeCfg);
            lteMRTAData.SetQueryCondition(condition);
            lteMRTAData.Query();
            lteMRTAEciDic = lteMRTAData.lteMREciDic;
            lteMRTAEciSDic = lteMRTAData.lteMREciSDic;

            WaitBox.ProgressPercent = 90;
            DiyLTEMRData lteMRTAAoaData = new DiyLTEMRData(MainModel, 7, 132, timeCfg);
            lteMRTAAoaData.SetQueryCondition(condition);
            lteMRTAAoaData.Query();
            lteMRTAAoaEciDic = lteMRTAAoaData.lteMREciDic;
            lteMRTAAoaEciSDic = lteMRTAAoaData.lteMREciSDic;

            WaitBox.Close();
        }

        /// <summary>
        /// 查询扫频稳定度统计
        /// </summary>
        private void doWithCellScanData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            mimoCellAngleDataDic = ZTLteMiMoAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[0]);
            foreach (int ieci in mimoCellAngleDataDic.Keys)
            {
                SCANR0R1Item scan = new SCANR0R1Item();
                scan.地市 = mimoCellAngleDataDic[ieci].strcityname;
                scan.小区CGI = mimoCellAngleDataDic[ieci].strcgi;
                scan.小区TAC = mimoCellAngleDataDic[ieci].iTac;
                scan.小区ECI = mimoCellAngleDataDic[ieci].iEci;
                scan.RSRP最大差值 = mimoCellAngleDataDic[ieci].bgExt.DRSRPDiffMax;
                scan.RSRP最大差值角度 = mimoCellAngleDataDic[ieci].bgExt.DRSRPDiffMax;
                scan.主瓣RSRP最大差值 = mimoCellAngleDataDic[ieci].bgExt.DRSRPDiffMax;
                scan.主瓣RSRP最大差值角度 = mimoCellAngleDataDic[ieci].bgExt.DRSRPDiffMax;
                scan.FR0覆盖率 = mimoCellAngleDataDic[ieci].bgExt.fRSRP0CoverRate;
                scan.FR1覆盖率 = mimoCellAngleDataDic[ieci].bgExt.fRSRP1CoverRate;
                scan.FRSRP0均值 = mimoCellAngleDataDic[ieci].bgExt.fDRSRP0AVG;
                scan.FRSRP1均值 = mimoCellAngleDataDic[ieci].bgExt.fDRSRP1AVG;
                scan.FRSRP稳定度 = mimoCellAngleDataDic[ieci].bgExt.fDRSRP;
                scan.FDRSRP = mimoCellAngleDataDic[ieci].bgExt.fDRSRPDIFF;
                scan.FDSINR = mimoCellAngleDataDic[ieci].bgExt.fDSINRDIFF;
                scan.FSINR稳定度 = mimoCellAngleDataDic[ieci].bgExt.fDSINR;
                scan.SINR0均值 = mimoCellAngleDataDic[ieci].bgExt.fDSINR0AVG;
                scan.SINR1均值 = mimoCellAngleDataDic[ieci].bgExt.fDSINR1AVG;

                scan.cell360gt9dB扫描角 = mimoCellAngleDataDic[ieci].bgExt.cellDiff9Angle;
                scan.cell360gt5dB扫描角 = mimoCellAngleDataDic[ieci].bgExt.cellDiff5Angle;
                scan.cell360gt3dB扫描角 = mimoCellAngleDataDic[ieci].bgExt.cellDiff3Angle;
                scan.主瓣双流功率差gt9dB扫描角 = mimoCellAngleDataDic[ieci].bgExt.maincellDiff9Angle;
                scan.主瓣双流功率差gt5dB扫描角 = mimoCellAngleDataDic[ieci].bgExt.maincellDiff5Angle;
                scan.主瓣双流功率差gt3dB扫描角 = mimoCellAngleDataDic[ieci].bgExt.maincellDiff3Angle;
                float mainDRSRP0AVG;
                float.TryParse(mimoCellAngleDataDic[ieci].bgExt.mainDRSRP0AVG, out mainDRSRP0AVG);
                scan.F主瓣RSRP0均值 = mainDRSRP0AVG;
                float mainDRSRP1AVG;
                float.TryParse(mimoCellAngleDataDic[ieci].bgExt.mainDRSRP1AVG, out mainDRSRP1AVG);
                scan.F主瓣RSRP1均值 = mainDRSRP1AVG;
                float mainDRSRPDIFF;
                float.TryParse(mimoCellAngleDataDic[ieci].bgExt.mainDRSRPDIFF, out mainDRSRPDIFF);
                scan.F主瓣DRSRP = mainDRSRPDIFF;

                if (!scanR0R1ItemEciDic.ContainsKey(ieci))
                {
                    scanR0R1ItemEciDic.Add(ieci, scan);
                }
                int iNewEci = (ieci / 256) * 256 + ((ieci % 256) % 10);
                if (!scanR0R1ItemEciSDic.ContainsKey(iNewEci))
                {
                    scanR0R1ItemEciSDic.Add(iNewEci, scan);
                }
            }
            WaitBox.ProgressPercent = 80;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询状态库天线数据
        /// </summary>
        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyCfgPara cellPara = new DiyCfgPara(MainModel);
            cellPara.Query();
            antCfgParaDic = cellPara.antCfgParaDic;
            antCfgParaSDic = cellPara.antCfgParaSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区参数数据
        /// </summary>
        private void doWithParaData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyAntennaPara antPara = new DiyAntennaPara(MainModel, timeCfg);
            antPara.Query();
            antParaEciDic = antPara.antParaEciDic;
            antParaEciSDic = antPara.antParaEciSDic;
            antParaCellNameDic = antPara.antParaCellNameDic;
            WaitBox.Close();
        }

        #endregion

        #region 数据分析处理
        /// <summary>
        /// 分析小区的角度数组，进行平滑化处理
        /// </summary>
        private void AnaCellAngleData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = antCfgParaDic.Count;
            int iNum = 0;

            ProblemType probType = new ProblemType();
            cellParaDataDic.Clear();
            foreach (int ieci in antCfgParaDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;

                if (iNum % 200 == 0)
                {
                    WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                    WaitBox.Text = "7.联合数据处理(" + iNum + "/" + iCount + ")";
                }
                try
                {
                    AntCfgSub antCfgSub = antCfgParaDic[ieci];
                    CellParaData cellPara = new CellParaData();
                    cellPara.iEci = ieci;
                    cellPara.index = iNum + 1;
                    cellPara.strcityname = strCityName;
                    cellPara.cellname = antCfgSub.strCellName;
                    cellPara.iTac = antCfgSub.iTAC;
                    cellPara.strMatch = "是";
                    cellPara.iangle_dir = (int)antCfgSub.方向角;

                    setLongitudeLatitute(ieci, antCfgSub, cellPara);

                    int iNewEci = (cellPara.iEci / 256) * 256 + ((cellPara.iEci % 256) % 10);
                    //小区性能数据
                    AntennaPara antPara;
                    if (!antParaEciDic.TryGetValue(iNewEci, out antPara)
                        && !antParaEciSDic.TryGetValue(iNewEci, out antPara))
                    {
                        antPara = new AntennaPara();
                    }
                    cellPara.antPara = antPara;
                    antPara.strbandtype = antCfgSub.strBtsType;
                    AnaCellData(ref cellPara, iNewEci);
                    AnaCellMrData(ref cellPara);
                    ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
                    ary.AnaRttdAoa90 = cellPara.mrCellParaData.cellMrData.AnaRttdAoa;
                    Dictionary<int, int> tmpDirSampleDic = ZTAntFuncHelper.GetDirSample(ary);
                    cellPara.mrCellParaData.dirSampleDic = ZTAntFuncHelper.GetNewDirSample(tmpDirSampleDic);
                    cellPara.mrCellParaData.iAntMaxDir = ZTAntFuncHelper.GetAntMaxDir(tmpDirSampleDic);
                    if (cellPara.mrCellParaData.iAntMaxDir != -1)
                    {
                        cellPara.mrCellParaData.strIsMrCell = "是";
                    }

                    AnaCellProblem(ref cellPara, ref probType);
                    cellPara.iStime = timeCfg.ISitme;
                    cellPara.iEtime = timeCfg.IEitme;
                    if (!cellParaDataDic.ContainsKey(cellPara.cellname))
                        cellParaDataDic.Add(cellPara.cellname, cellPara);
                    iNum++;
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                }
            }
            cellProNum[strCityName] = probType;
            WaitBox.Close();
        }

        private void setLongitudeLatitute(int ieci, AntCfgSub antCfgSub, CellParaData cellPara)
        {
            LTECell lteMainCell = CellManager.GetInstance().GetNearestLTECellByTACCI(DateTime.Now, antCfgSub.iTAC, ieci, antCfgSub.fLongitude, antCfgSub.fLatitude);
            if (lteMainCell != null)
            {
                cellPara.cellLongitude = lteMainCell.Longitude;
                cellPara.cellLatitude = lteMainCell.Latitude;
            }
            else
            {
                cellPara.cellLongitude = antCfgSub.fLongitude;
                cellPara.cellLatitude = antCfgSub.fLatitude;
            }
        }

        /// <summary>
        /// 分析小区数据
        /// </summary>
        private void AnaCellData(ref CellParaData cellPara, int iNewEci)
        {
            //小区性能数据
            CellPara cPara;
            if (!cellParaEciDic.TryGetValue(iNewEci, out cPara)
                && !cellParaEciSDic.TryGetValue(iNewEci, out cPara))
            {
                cPara = new CellPara();
            }
            cellPara.cellPara = cPara;

            //小区RANK
            CellParaRank cellParaRank;
            if (!cellParaRankEciDic.TryGetValue(iNewEci, out cellParaRank)
                && !cellParaRankEciSDic.TryGetValue(iNewEci, out cellParaRank))
            {
                cellParaRank = new CellParaRank();
            }
            cellPara.cellParaRank = cellParaRank;

            //状态库天线数据
            AntCfgSub antCfg;
            if (!antCfgParaDic.TryGetValue(iNewEci, out antCfg)
                && !antCfgParaSDic.TryGetValue(iNewEci, out antCfg))
            {
                antCfg = new AntCfgSub();
            }
            cellPara.antCfg = antCfg;

            ZTLteScanAntenna.AntInfoItem antInfoItem;
            if (!antScanInfoDic.TryGetValue(iNewEci, out antInfoItem)
                && !antScanSubEciInfoDic.TryGetValue(iNewEci, out antInfoItem))
            {
                antInfoItem = new ZTLteScanAntenna.AntInfoItem();
            }
            cellPara.antInfoItem = antInfoItem;
            cellPara.ciItem = antInfoItem.ciItem;
            if (antInfoDic.ContainsKey(iNewEci))
                cellPara.ciopItem = antInfoDic[iNewEci];
            else if (antInfoSDic.ContainsKey(iNewEci))
                cellPara.ciopItem = antInfoSDic[iNewEci];
            else
                cellPara.ciopItem = new ZTLteAntenna.CellInfoOutPutItem();

            setMRItem(cellPara, iNewEci);

            //小区MR覆盖指数
            setMRCoverItem(cellPara, iNewEci);

            setScanItem(cellPara, iNewEci);
        }

        private void setMRItem(CellParaData cellPara, int iNewEci)
        {
            LteMrItem lteMRItem;
            if (!lteMREciDic.TryGetValue(iNewEci, out lteMRItem)
                && !lteMREciSDic.TryGetValue(iNewEci, out lteMRItem))
            {
                lteMRItem = new LteMrItem();
            }
            cellPara.lteMRItem = lteMRItem;
        }

        private void setMRCoverItem(CellParaData cellPara, int iNewEci)
        {
            LteCoverItem lteMRCoverItem;
            if (!lteMRCoverEciDic.TryGetValue(iNewEci, out lteMRCoverItem)
                && !lteMRCoverEciSDic.TryGetValue(iNewEci, out lteMRCoverItem))
            {
                lteMRCoverItem = new LteCoverItem();
            }
            cellPara.lteMRCoverItem = lteMRCoverItem;
        }

        private void setScanItem(CellParaData cellPara, int iNewEci)
        {
            SCANR0R1Item scanR0R1Item;
            if (!scanR0R1ItemEciDic.TryGetValue(iNewEci, out scanR0R1Item)
                && !scanR0R1ItemEciSDic.TryGetValue(iNewEci, out scanR0R1Item))
            {
                scanR0R1Item = new SCANR0R1Item();
            }
            cellPara.scanR0R1Item = scanR0R1Item;
        }

        /// <summary>
        /// 分析小区MR数据
        /// </summary>
        private void AnaCellMrData(ref CellParaData cellPara)
        {
            ZTAntMRBaseItem lteMRAoaItem;
            if (!lteMRAoaEciDic.TryGetValue(cellPara.iEci, out lteMRAoaItem)
                && !lteMRAoaEciSDic.TryGetValue(cellPara.iEci, out lteMRAoaItem))
            {
                lteMRAoaItem = new ZTAntMRBaseItem();
            }
            cellPara.mrCellParaData.cellMrData.lteMRAoaItem = lteMRAoaItem;

            ZTAntMRBaseItem lteMRRttdAoaItem;
            if (!lteMRTAAoaEciDic.TryGetValue(cellPara.iEci, out lteMRRttdAoaItem)
                && !lteMRTAAoaEciSDic.TryGetValue(cellPara.iEci, out lteMRRttdAoaItem))
            {
                lteMRRttdAoaItem = new ZTAntMRBaseItem();
            }
            cellPara.mrCellParaData.cellMrData.lteMRRttdAoaItem = lteMRRttdAoaItem;

            ZTAntMRBaseItem lteMRTaItem;
            if (!lteMRTAEciDic.TryGetValue(cellPara.iEci, out lteMRTaItem)
                && !lteMRTAEciSDic.TryGetValue(cellPara.iEci, out lteMRTaItem))
            {
                lteMRTaItem = new ZTAntMRBaseItem();
            }
            cellPara.mrCellParaData.cellMrData.lteMRTaItem = lteMRTaItem;
        }

        /// <summary>
        /// 小区问题类型判定
        /// </summary>
        private void AnaCellProblem(ref CellParaData cellPara, ref ProblemType probType)
        {
            #region 问题类型算法判定
            int iIndoorType = 0;
            if (cellPara.antCfg.strBtsType.Contains("E") || cellPara.antCfg.strType.Contains("室内"))
                iIndoorType = 1;

            if (iIndoorType == 0)
            {
                if (cellPara.ciopItem.IsampleTotal > 300 && (cellPara.ciopItem.F旁瓣采样点比例 + cellPara.ciopItem.F背瓣采样点比例) >= 60)
                {
                    cellPara.strProbType += "天线工参核查;";
                    cellPara.strProbStatus += "一级告警;";
                    probType.天线工参核查Level1 += 1;
                    return;
                }

                主瓣弱覆盖问题分析(ref cellPara, ref probType);
                旁瓣泄露问题分析(ref cellPara, ref probType);
                背瓣覆盖问题分析(ref cellPara, ref probType);
                MR覆盖指标问题分析(ref cellPara, ref probType);
                信号失衡问题分析(ref cellPara, ref probType);
                天线权值异常(ref cellPara, ref probType);
            }
            else
            {
                室分泄露问题分析(ref cellPara, ref probType);
            }
            #endregion

            if (cellPara.strProbType.Contains("主瓣") || cellPara.strProbType.Contains("旁瓣") ||
                cellPara.strProbType.Contains("背瓣") || cellPara.strProbType.Contains("失衡"))
            {
                int iNewEci = (cellPara.iEci / 256) * 256 + ((cellPara.iEci % 256) % 10);
                if (!cellEciDic.ContainsKey(iNewEci))
                    cellEciDic.Add(iNewEci, iNewEci);
            }
        }

        #endregion

        #region 问题判断分析

        private void 主瓣弱覆盖问题分析(ref CellParaData cellPara, ref ProblemType probType)
        {
            int iSampleNumDef = 0;
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (strCityLevel == "二类")
                iSampleNumDef = 105;
            else if (strCityLevel == "三类")
                iSampleNumDef = 95;
            else
                iSampleNumDef = 120;

            int iMainSampleNum = (int)(cellPara.ciopItem.IsampleTotal * cellPara.ciopItem.F主瓣采样点比例 / 100);
            if (iMainSampleNum >= iSampleNumDef && cellPara.ciopItem.F主瓣小区平均RSRP <= -95)
            {
                cellPara.strProbType += "主瓣弱覆盖;";
                if (cellPara.ciopItem.FLteCoverRate <= 85)
                {
                    cellPara.strProbStatus += "一级告警;";
                    probType.主瓣弱覆盖Level1 += 1;
                }
                else
                {
                    cellPara.strProbStatus += "二级告警;";
                    probType.主瓣弱覆盖Level2 += 1;
                }
            }
            if (iMainSampleNum >= iSampleNumDef)
            {
                probType.主瓣弱覆盖 += 1;
            }
        }

        private void 旁瓣泄露问题分析(ref CellParaData cellPara, ref ProblemType probType)
        {
            int iSampleNumDef = 0;
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (strCityLevel == "二类")
                iSampleNumDef = 270;
            else if (strCityLevel == "三类")
                iSampleNumDef = 240;
            else
                iSampleNumDef = 300;

            if (cellPara.antInfoItem.SampNum > iSampleNumDef 
                && (cellPara.antInfoItem.ISideNum >= 2 
                    || (cellPara.ciopItem.F旁瓣采样点比例 > 40 && cellPara.ciopItem.F旁瓣采样点比例 < 60)))
            {
                cellPara.strProbType += "旁瓣泄露;";
                if ((cellPara.lteMRCoverItem.S过覆盖小区 == "是" && cellPara.lteMRCoverItem.S高重叠覆盖小区 == "是")
                || cellPara.ciopItem.FSinr <= 5 || cellPara.ciopItem.FLteCoverRate <= 85)
                {
                    cellPara.strProbStatus += "一级告警;";
                    probType.旁瓣泄露Level1 += 1;
                }
                else
                {
                    cellPara.strProbStatus += "二级告警;";
                    probType.旁瓣泄露Level2 += 1;
                }
            }

            if (cellPara.antInfoItem.SampNum > iSampleNumDef || cellPara.ciopItem.IsampleTotal > iSampleNumDef)
            {
                probType.旁瓣泄露 += 1;
            }
        }

        private void 背瓣覆盖问题分析(ref CellParaData cellPara, ref ProblemType probType)
        {
            int iSampleNumDef = 0;
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (strCityLevel == "二类")
                iSampleNumDef = 270;
            else if (strCityLevel == "三类")
                iSampleNumDef = 240;
            else
                iSampleNumDef = 300;

            setBacklopeError(cellPara, probType, iSampleNumDef);

            if (cellPara.antInfoItem.SampNum > iSampleNumDef && cellPara.ciopItem.IsampleTotal > iSampleNumDef)
            {
                probType.背瓣异常 += 1;
            }
        }

        private void setBacklopeError(CellParaData cellPara, ProblemType probType, int iSampleNumDef)
        {
            if (cellPara.antInfoItem.SampNum > iSampleNumDef && cellPara.antInfoItem.fScanBackSampleRate >= 8 && cellPara.antInfoItem.fScanMainSampleRate > 0
                && cellPara.antInfoItem.fScanDiscrepancy <= 7 && cellPara.antInfoItem.fSacnMainMaxRsrp > -20)
            {
                cellPara.strProbType += "背瓣覆盖异常;";
                int iScanBackNum = (int)(cellPara.ciopItem.IsampleTotal * cellPara.ciopItem.F背瓣采样点比例 / 100);
                if (iScanBackNum >= 12 && (cellPara.ciopItem.FSinr <= 5 || cellPara.ciopItem.FLteCoverRate <= 90))
                {
                    cellPara.strProbStatus += "一级告警;";
                    probType.背瓣覆盖异常Level1 += 1;
                }
                else
                {
                    cellPara.strProbStatus += "二级告警;";
                    probType.背瓣覆盖异常Level2 += 1;
                }
            }
            else if (cellPara.ciopItem.IsampleTotal > iSampleNumDef && (cellPara.ciopItem.F背瓣采样点比例 >= 5 || cellPara.ciopItem.I前后比 <= 5))
            {
                cellPara.strProbType += "背瓣覆盖异常;";
                if (cellPara.ciopItem.FSinr <= 5 || cellPara.ciopItem.FLteCoverRate <= 90)
                {
                    cellPara.strProbStatus += "一级告警;";
                    probType.背瓣覆盖异常Level1 += 1;
                }
                else
                {
                    cellPara.strProbStatus += "二级告警;";
                    probType.背瓣覆盖异常Level2 += 1;
                }
            }
        }

        private void MR覆盖指标问题分析(ref CellParaData cellPara, ref ProblemType probType)
        {
            if (cellPara.lteMRCoverItem.S过覆盖小区 == "是" && cellPara.lteMRCoverItem.I过覆盖影响小区数 >= 23)
            {
                cellPara.strProbType += "严重过覆盖小区;";
                cellPara.strProbStatus += "二级告警;";
                probType.严重过覆盖Level2 += 1;
            }

            if (cellPara.lteMRCoverItem.S高重叠覆盖小区 == "是" && cellPara.lteMRCoverItem.F重叠覆盖指数 >= 35)
            {
                cellPara.strProbType += "严重重叠覆盖小区;";
                cellPara.strProbStatus += "二级告警;";
                probType.严重重叠覆盖Level2 += 1;
            }
        }

        private void 信号失衡问题分析(ref CellParaData cellPara, ref ProblemType probType)
        {
            int iSampleNumDef = 0;
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (strCityLevel == "二类")
                iSampleNumDef = 180;
            else if (strCityLevel == "三类")
                iSampleNumDef = 150;
            else
                iSampleNumDef = 210;

            if (cellPara.antInfoItem.SampNum > iSampleNumDef && cellPara.scanR0R1Item.F主瓣DRSRP >= 5)
            {
                cellPara.strProbType += "RS0/1信号失衡;";
                if (cellPara.scanR0R1Item.F主瓣双流功率差gt9dB扫描角 > 20
                    && cellPara.cellParaRank.FRank2的下行传输TB数比例 <= 30 && cellPara.cellParaRank.FRank1的下行传输TB数比例 >= 70)
                {
                    cellPara.strProbStatus += "一级告警";
                    probType.RS信号失衡Level1 += 1;
                }
                else
                {
                    cellPara.strProbStatus += "二级告警";
                    probType.RS信号失衡Level2 += 1;
                }
            }

            if (cellPara.antInfoItem.SampNum > iSampleNumDef)
            {
                probType.RS失衡 += 1;
            }
        }

        private void 天线权值异常(ref CellParaData cellPara, ref ProblemType probType)
        {
            if (!cellPara.antCfg.场景类型.Contains("道")
                && !cellPara.antCfg.场景类型.Contains("路")
                && !cellPara.antCfg.场景类型.Contains("速")
                && cellPara.antPara.isSmartAnt == "是"
                && cellPara.antPara._3dbValue <= 33
                && cellPara.antPara._3dbValue > 0)
            {
                cellPara.strProbType += "天线权值异常;";
                cellPara.strProbStatus += "二级告警";
                probType.天线权值异常Level2 += 1;
            }
        }

        private void 室分泄露问题分析(ref CellParaData cellPara, ref ProblemType probType)
        {
            int iSampleNumDef = 0;
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (strCityLevel == "二类")
                iSampleNumDef = 30;
            else if (strCityLevel == "三类")
                iSampleNumDef = 30;
            else
                iSampleNumDef = 50;

            if (cellPara.ciopItem.IsampleTotal >= iSampleNumDef && cellPara.ciopItem.IsampleTotal <= 1000)
            {
                cellPara.strProbType = "室分泄露;";
                if (cellPara.ciopItem.FLteCoverRate <= 85)
                {
                    cellPara.strProbStatus = "一级告警";
                    probType.室分泄露Level1 += 1;
                }
                else
                {
                    cellPara.strProbStatus = "二级告警";
                    probType.室分泄露Level2 += 1;
                }
                probType.室分泄露 += 1;
            }
        }

        #endregion

        #region 数据整理及导出

        /// <summary>
        /// 按角度拆分导出数据
        /// </summary>
        private List<NPOIRow> FillAngleValue(CellParaData data)
        {
            List<NPOIRow> dataNpoi = new List<NPOIRow>();
            NPOIRow nr;
            int iStart = 0;
            int iEnd = 360;
            #region 小区角度数据
            List<object> objsL;
            setScanData(data, dataNpoi, out nr, iStart, iEnd, out objsL);

            //路测数据
            setDTData(data, dataNpoi, out nr, iStart, iEnd, out objsL);
            #endregion

            return dataNpoi;
        }

        private static void setScanData(CellParaData data, List<NPOIRow> dataNpoi, out NPOIRow nr, int iStart, int iEnd, out List<object> objsL)
        {
            int[] rsrpArray = data.ciItem.rsrpArray;
            double[] newRsrpArray = data.ciItem.newRsrpArray;
            int[] sinrArray = data.ciItem.sinrArray;
            double[] sampleDistArray = data.ciItem.sampArray;
            int[] sampleNumArray = data.ciItem.sampNumArray;

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("RSRP(Scan)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(sampleNumArray[i] == 0 ? -140 : rsrpArray[i] / sampleNumArray[i]);
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("平滑RSRP(Scan)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(Math.Round(newRsrpArray[i], 2));
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("SINR(Scan)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(sampleNumArray[i] == 0 ? -25 : sinrArray[i] / sampleNumArray[i]);
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("通信距离(Scan)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(Math.Round(sampleNumArray[i] == 0 ? 0 : sampleDistArray[i] / sampleNumArray[i], 2));
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);
        }

        private static void setDTData(CellParaData data, List<NPOIRow> dataNpoi, out NPOIRow nr, int iStart, int iEnd, out List<object> objsL)
        {
            int[] dtrsrpArray = data.ciopItem.citem.rsrpArray;
            double[] dtnewRsrpArray = data.ciopItem.citem.newRsrpArray;
            int[] dtsinrArray = data.ciopItem.citem.sinrArray;
            double[] dtsampleDistArray = data.ciopItem.citem.sampArray;
            int[] dtsampleNumArray = data.ciopItem.citem.sampNumArray;

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("RSRP(DT)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(dtsampleNumArray[i] == 0 ? -140 : dtrsrpArray[i] / dtsampleNumArray[i]);
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("平滑RSRP(DT)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(Math.Round(dtnewRsrpArray[i], 2));
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("SINR(DT)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(dtsampleNumArray[i] == 0 ? -25 : dtsinrArray[i] / dtsampleNumArray[i]);
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("通信距离(DT)");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(Math.Round(dtsampleNumArray[i] == 0 ? 0 : dtsampleDistArray[i] / dtsampleNumArray[i], 2));
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);
        }

        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<NPOIRow> data8s = new List<NPOIRow>();
            NPOIRow nr8 = new NPOIRow();
            List<object> col8s = 天线综合分析总表字段配置();
            nr8.cellValues = col8s;
            data8s.Add(nr8);

            List<NPOIRow> data2s = new List<NPOIRow>();
            NPOIRow nr2 = new NPOIRow();
            List<object> col2s = new List<object>();
            col2s.Add("小区名称");
            col2s.Add("指标项");
            for (int i = 0; i < 360; i++)
            {
                col2s.Add(i.ToString() + "°");
            }
            nr2.cellValues = col2s;
            data2s.Add(nr2);

            List<NPOIRow> data3s = new List<NPOIRow>();
            NPOIRow nr3 = new NPOIRow();
            List<object> col3s = 天线问题分析字段配置();
            nr3.cellValues = col3s;
            data3s.Add(nr3);

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            foreach (string cellName in cellParaDataDic.Keys)
            {
                try
                {
                    NPOIRow nr9 = new NPOIRow();
                    nr9.cellValues = FillCellValue(cellParaDataDic[cellName]);
                    data8s.Add(nr9);
                    if (cellParaDataDic[cellName].ciItem.antAngleDic.Count > 0
                        || cellParaDataDic[cellName].ciopItem.citem.antAngleDic.Count > 0)
                    {
                        data2s.AddRange(FillAngleValue(cellParaDataDic[cellName]));
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                }
            }

            foreach (string cityName in cellProNum.Keys)
            {
                NPOIRow nr5 = new NPOIRow();
                List<object> tmpList = 天线问题分析字段赋值(cityName, cellProNum[cityName]);
                nr5.cellValues = tmpList;
                data3s.Add(nr5);
            }
            nrDatasList.Add(data8s);
            nrDatasList.Add(data2s);
            nrDatasList.Add(data3s);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("天线综合分析总表");
            sheetNames.Add("小区角度数据");
            sheetNames.Add("天线问题分析汇总");
            FireShowResultForm(nrDatasList, sheetNames);
        }

        private List<object> 天线综合分析总表字段配置()
        {
            List<object> col8s = new List<object>();
            col8s.Add("序号");
            col8s.Add("地市");
            col8s.Add("小区英文名");
            col8s.Add("小区名");
            col8s.Add("主设备厂家");
            col8s.Add("是否区配工参");
            col8s.Add("天线权值");
            col8s.Add("CGI");
            col8s.Add("覆盖类型");
            col8s.Add("场景类型");

            col8s.Add("小区频段");
            col8s.Add("波束宽度");

            col8s.Add("方位角");
            col8s.Add("预置下倾角");
            col8s.Add("机械下倾角");
            col8s.Add("电调下倾角");
            col8s.Add("挂高");

            col8s.Add("分析结果");
            col8s.Add("告警状态");
            col8s.Add("评估结果");

            col8s.Add("Gmax(天线权值计算)");
            col8s.Add("3dB功率角(天线权值计算)");
            col8s.Add("6dB功率角(天线权值计算)");

            col8s.Add("上行吞吐量(统计)");
            col8s.Add("下行吞吐量(统计)");
            col8s.Add("无线接通率(统计)");
            col8s.Add("无线掉线率(统计)");
            col8s.Add("切换成功率(统计)");
            col8s.Add("ERAB建立成功率(统计)");
            col8s.Add("ERAB掉线率(统计)");

            col8s.Add("Rank1的下行传输TB数(统计)");
            col8s.Add("Rank2的下行传输TB数(统计)");
            col8s.Add("Rank1的下行传输TB数比例(统计)");
            col8s.Add("Rank2的下行传输TB数比例(统计)");

            col8s.Add("RSRP均值(统计)");
            col8s.Add("SINR均值(统计)");
            col8s.Add("95覆盖率(统计)");
            col8s.Add("110覆盖率(统计)");

            col8s.Add("MRO总采样点数(统计)");
            col8s.Add("重叠覆盖条件采样点数(统计)");
            col8s.Add("重叠覆盖指数(统计)");
            col8s.Add("过覆盖影响小区数(统计)");
            col8s.Add("高重叠覆盖小区(统计)");
            col8s.Add("过覆盖小区(统计)");

            col8s.Add("采样点总数(扫频)");
            col8s.Add("主瓣采样点比例(扫频)");
            col8s.Add("主瓣最强信号强度(扫频)");
            col8s.Add("疑似旁瓣数量(扫频)");
            col8s.Add("旁瓣1辐射方向(扫频)");
            col8s.Add("旁瓣1最强信号强度(扫频)");
            col8s.Add("旁瓣1平均信号强度(扫频)");
            col8s.Add("旁瓣1采样点比例(扫频)");
            col8s.Add("旁瓣2辐射方向(扫频)");
            col8s.Add("旁瓣2最强信号强度(扫频)");
            col8s.Add("旁瓣2平均信号强度(扫频)");
            col8s.Add("旁瓣2采样点比例(扫频)");
            col8s.Add("旁瓣3辐射方向(扫频)");
            col8s.Add("旁瓣3最强信号强度(扫频)");
            col8s.Add("旁瓣3平均信号强度(扫频)");
            col8s.Add("旁瓣3采样点比例(扫频)");
            col8s.Add("背瓣采样点比例(扫频)");
            col8s.Add("前后比(扫频)");

            col8s.Add("R0覆盖率(扫频)");
            col8s.Add("R1覆盖率(扫频)");
            col8s.Add("RSRP0均值(扫频)");
            col8s.Add("RSRP1均值(扫频)");
            col8s.Add("DRSRP(扫频)");
            col8s.Add("RSRP稳定度(扫频)");
            col8s.Add("SINR0均值(扫频)");
            col8s.Add("SINR1均值(扫频)");
            col8s.Add("DSINR(扫频)");
            col8s.Add("SINR稳定度(扫频)");
            col8s.Add("主瓣RSRP0均值(扫频)");
            col8s.Add("主瓣RSRP1均值(扫频)");
            col8s.Add("主瓣DRSRP(扫频)");
            col8s.Add("主瓣双流功率差>9dB扫描角(扫频)");

            col8s.Add("采样点数(路测)");
            col8s.Add("覆盖率(RSRP≥-110&SINR>=-3)(路测)");
            col8s.Add("LTE覆盖率(RSRP≥-110)(路测)");
            col8s.Add("LTE覆盖率(SINR>=-3)(路测)");

            col8s.Add("小区平均RSRP(路测)");
            col8s.Add("小区平均SINR(路测)");
            col8s.Add("小区平均RSRP0(路测)");
            col8s.Add("小区平均RSRP1(路测)");

            col8s.Add("±(0,60°)范围内采样点比例(路测)");
            col8s.Add("±(0,60°)范围内小区平均RSRP(路测)");
            col8s.Add("±(0,60°)范围内覆盖率(RSRP≥-110&SINR>=-3)(路测)");
            col8s.Add("±(0,60°)范围内LTE覆盖率(RSRP≥-110)(路测)");
            col8s.Add("±(0,60°)范围内LTE覆盖率(SINR>=-3)(路测)");
            col8s.Add("±(60,150°)范围内采样点比例(路测)");
            col8s.Add("±(150,180°)范围内采样点比例(路测)");
            col8s.Add("前后比(路测)");

            col8s.Add("端口1幅度(天线权值)");
            col8s.Add("端口2幅度(天线权值)");
            col8s.Add("端口3幅度(天线权值)");
            col8s.Add("端口4幅度(天线权值)");
            col8s.Add("端口5幅度(天线权值)");
            col8s.Add("端口6幅度(天线权值)");
            col8s.Add("端口7幅度(天线权值)");
            col8s.Add("端口8幅度(天线权值)");

            col8s.Add("端口1相位(天线权值)");
            col8s.Add("端口2相位(天线权值)");
            col8s.Add("端口3相位(天线权值)");
            col8s.Add("端口4相位(天线权值)");
            col8s.Add("端口5相位(天线权值)");
            col8s.Add("端口6相位(天线权值)");
            col8s.Add("端口7相位(天线权值)");
            col8s.Add("端口8相位(天线权值)");
            col8s.Add("是否(8通道)智能天线");

            col8s.Add("是否可MR波形重构");
            return col8s;
        }

        private List<object> 天线问题分析字段配置()
        {
            List<object> col3s = new List<object>();
            col3s.Add("地市");
            col3s.Add("天线工参核查(一级告警)");
            col3s.Add("主瓣弱覆盖(一级告警)");
            col3s.Add("主瓣弱覆盖(二级告警)");
            col3s.Add("主瓣弱覆盖总数");//主瓣弱覆盖总数
            col3s.Add("旁瓣泄露(一级告警)");
            col3s.Add("旁瓣泄露(二级告警)");
            col3s.Add("旁瓣泄露总数");//旁瓣泄露总数
            col3s.Add("背瓣覆盖异常(一级告警)");
            col3s.Add("背瓣覆盖异常(二级告警)");
            col3s.Add("背瓣覆盖异常总数");//背瓣覆盖异常总数
            col3s.Add("严重过覆盖(二级告警)");
            col3s.Add("严重重叠覆盖(二级告警)");
            col3s.Add("RS0/1信号失衡(一级告警)");
            col3s.Add("RS0/1信号失衡(二级告警)");
            col3s.Add("RS0/1信号失衡总数");//RS0/1信号失衡总数
            col3s.Add("天线权值异常(二级告警)");
            col3s.Add("室分泄露(一级告警)");
            col3s.Add("室分泄露(二级告警)");
            col3s.Add("室分泄露总数");//室分泄露总数
            col3s.Add("主瓣弱覆盖(正负0-60°区间内，采样点数>=120/105/95)");
            col3s.AddRange(getDefaultValue(1, "一级告警占比"));
            col3s.Add("旁瓣泄露(扫频采样点300/270/240以上或者路测数据300/270/240以上)");
            col3s.AddRange(getDefaultValue(1, "一级告警占比"));
            col3s.Add("背瓣异常(扫频采样点300/270/240以上或者路测采样点300/270/240以上)");
            col3s.AddRange(getDefaultValue(1, "一级告警占比"));
            col3s.Add("RS失衡(扫频采样点210/180/150以上)");
            col3s.AddRange(getDefaultValue(1, "一级告警占比"));
            col3s.Add("室分泄露(路测采样点50-1000/30-1000/30-1000)");
            col3s.AddRange(getDefaultValue(1, "一级告警占比"));
            return col3s;
        }

        private List<object> getDefaultValue(int index, string value)
        {
            List<object> tmpCols = new List<object>();
            for (int i = 0; i < index; i++)
            {
                tmpCols.Add(value);
            }
            return tmpCols;
        }

        private List<object> 天线问题分析字段赋值(string cityName, ProblemType iProbStat)
        {
            List<object> tmpList = new List<object>();
            tmpList.Add(cityName);
            tmpList.Add(iProbStat.天线工参核查Level1);
            tmpList.Add(iProbStat.主瓣弱覆盖Level1);
            tmpList.Add(iProbStat.主瓣弱覆盖Level2);
            tmpList.Add(iProbStat.主瓣弱覆盖总数);
            tmpList.Add(iProbStat.旁瓣泄露Level1);
            tmpList.Add(iProbStat.旁瓣泄露Level2);
            tmpList.Add(iProbStat.旁瓣泄露总数);
            tmpList.Add(iProbStat.背瓣覆盖异常Level1);
            tmpList.Add(iProbStat.背瓣覆盖异常Level2);
            tmpList.Add(iProbStat.背瓣覆盖异常总数);
            tmpList.Add(iProbStat.严重过覆盖Level2);
            tmpList.Add(iProbStat.严重重叠覆盖Level2);
            tmpList.Add(iProbStat.RS信号失衡Level1);
            tmpList.Add(iProbStat.RS信号失衡Level2);
            tmpList.Add(iProbStat.RS信号失衡总数);
            tmpList.Add(iProbStat.天线权值异常Level2);
            tmpList.Add(iProbStat.室分泄露Level1);
            tmpList.Add(iProbStat.室分泄露Level2);
            tmpList.Add(iProbStat.室分泄露总数);
            tmpList.Add(iProbStat.主瓣弱覆盖);
            tmpList.Add(iProbStat.主瓣弱覆盖Level1占比);
            tmpList.Add(iProbStat.旁瓣泄露);
            tmpList.Add(iProbStat.旁瓣泄露Level1占比);
            tmpList.Add(iProbStat.背瓣异常);
            tmpList.Add(iProbStat.背瓣异常Level1占比);
            tmpList.Add(iProbStat.RS失衡);
            tmpList.Add(iProbStat.RS失衡Level1占比);
            tmpList.Add(iProbStat.室分泄露);
            tmpList.Add(iProbStat.室分泄露占比);
            return tmpList;
        }

        /// <summary>
        /// 小区级赋值
        /// </summary>
        private List<object> FillCellValue(CellParaData data)
        {
            List<object> objs8 = new List<object>();
            objs8.Add(data.index);//序号
            objs8.Add(data.strcityname);//地市
            objs8.Add(data.antCfg.strCellNameEn);//小区英文名
            objs8.Add(data.antCfg.strCellName);//小区名
            objs8.Add(data.antCfg.strVender);//主设备厂家

            objs8.Add(data.strMatch);//是否区配工参
            objs8.Add(data.antPara.strWeightValue);//天线权值
            objs8.Add("460-00-" + data.antCfg.iEnodebID.ToString() + "-" + data.antCfg.iSectorID.ToString());//CGI
            objs8.Add(data.antCfg.strType);//覆盖类型
            objs8.Add(data.antCfg.场景类型);//场景类型
            objs8.Add(data.antCfg.strBtsType);//小区频段
            objs8.Add(data.antPara.strbeamwidth);//波束宽度

            objs8.Add(Math.Round(data.antCfg.方向角, 2));
            objs8.Add(Math.Round(data.antCfg.预置下倾角, 2));
            objs8.Add(Math.Round(data.antCfg.机械下倾角, 2));
            objs8.Add(Math.Round(data.antCfg.电调下倾角, 2));
            objs8.Add(Math.Round(data.antCfg.挂高, 2));

            objs8.Add(data.strProbType);
            objs8.Add(data.strProbStatus);
            objs8.Add(data.strCompareStatus);

            int iInfoShow = 1;
            if (data.antPara.iStatus == 1)
            {
                if (data.antCfg.strBtsType.Contains("E") || data.antCfg.strBtsType.Contains("A"))
                    iInfoShow = 0;
            }
            else
                iInfoShow = 0;
            
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.GMax, 2).ToString()));//Gmax
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara._3dbValue, 0).ToString()));//3dB功率角
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara._6dbValue, 0).ToString()));//6dB功率角

            objs8.Add(Math.Round(data.cellPara.F上行吞吐量, 2));
            objs8.Add(Math.Round(data.cellPara.F下行吞吐量, 2));
            objs8.Add(GetDataRate(data.cellPara.F无线接通率));
            objs8.Add(GetDataRate(data.cellPara.F无线掉线率));
            objs8.Add(GetDataRate(data.cellPara.F切换成功率));
            objs8.Add(GetDataRate(data.cellPara.fERAB建立成功率));
            objs8.Add(GetDataRate(data.cellPara.fERAB掉线率));

            objs8.Add(Math.Round(data.cellParaRank.FRank1的下行传输TB数, 2));
            objs8.Add(Math.Round(data.cellParaRank.FRank2的下行传输TB数, 2));
            objs8.Add(GetDataRate(data.cellParaRank.FRank1的下行传输TB数比例));
            objs8.Add(GetDataRate(data.cellParaRank.FRank2的下行传输TB数比例));

            objs8.Add(Math.Round(data.lteMRItem.dAvgRsrp, 2));//RSRP均值
            objs8.Add(Math.Round(data.lteMRItem.dAvgSinr, 2));//SINR均值
            objs8.Add(GetDataRate(data.lteMRItem.dRate95Rsrp));//95覆盖率
            objs8.Add(GetDataRate(data.lteMRItem.dRate110Rsrp));//110覆盖率

            objs8.Add(data.lteMRCoverItem.IMRO总采样点数);//MRO总采样点数
            objs8.Add(data.lteMRCoverItem.I重叠覆盖条件采样点数);//重叠覆盖条件采样点数
            objs8.Add(GetDataRate(data.lteMRCoverItem.F重叠覆盖指数));//重叠覆盖指数
            objs8.Add(data.lteMRCoverItem.I过覆盖影响小区数);//过覆盖影响小区数
            objs8.Add(data.lteMRCoverItem.S高重叠覆盖小区);//高重叠覆盖小区
            objs8.Add(data.lteMRCoverItem.S过覆盖小区);//过覆盖小区

            objs8.Add(data.antInfoItem.SampNum);
            objs8.Add(data.antInfoItem.mSampleRate);
            objs8.Add(data.antInfoItem.mMaxRsrp);
            objs8.Add(data.antInfoItem.SuspectedFlap);

            objs8.Add(data.antInfoItem.p1MaxRsrpDir);
            objs8.Add(data.antInfoItem.p1MaxRsrp);
            objs8.Add(data.antInfoItem.p1MeanRsrp);
            objs8.Add(data.antInfoItem.p1SampleRate);

            objs8.Add(data.antInfoItem.p2MaxRsrpDir);
            objs8.Add(data.antInfoItem.p2MaxRsrp);
            objs8.Add(data.antInfoItem.p2MeanRsrp);
            objs8.Add(data.antInfoItem.p2SampleRate);

            objs8.Add(data.antInfoItem.p3MaxRsrpDir);
            objs8.Add(data.antInfoItem.p3MaxRsrp);
            objs8.Add(data.antInfoItem.p3MeanRsrp);
            objs8.Add(data.antInfoItem.p3SampleRate);
            
            objs8.Add(data.antInfoItem.bSampleRate);
            objs8.Add(data.antInfoItem.bDiscrepancy);
            objs8.Add(GetDataRate(data.scanR0R1Item.FR0覆盖率));
            objs8.Add(GetDataRate(data.scanR0R1Item.FR1覆盖率));
            objs8.Add(data.scanR0R1Item.FRSRP0均值);
            objs8.Add(data.scanR0R1Item.FRSRP1均值);
            objs8.Add(data.scanR0R1Item.FDRSRP);
            objs8.Add(data.scanR0R1Item.FRSRP稳定度);
            objs8.Add(data.scanR0R1Item.SINR0均值);
            objs8.Add(data.scanR0R1Item.SINR1均值);
            objs8.Add(data.scanR0R1Item.FDSINR);
            objs8.Add(data.scanR0R1Item.FSINR稳定度);
            objs8.Add(data.scanR0R1Item.F主瓣RSRP0均值);
            objs8.Add(data.scanR0R1Item.F主瓣RSRP1均值);
            objs8.Add(data.scanR0R1Item.F主瓣DRSRP);
            objs8.Add(data.scanR0R1Item.主瓣双流功率差gt9dB扫描角);

            objs8.Add(data.ciopItem.IsampleTotal);
            if (data.ciopItem.IsampleTotal == 0)
            {
                objs8.AddRange(getDefaultValue(2, "-"));
            }
            else
            {
                objs8.Add(GetDataRate(data.ciopItem.FLteCoverRate));
                objs8.Add(GetDataRate(data.ciopItem.FLteRsrpRate));
            }
            objs8.Add(GetDataRate(data.ciopItem.FLteSinrRate));

            objs8.Add(data.ciopItem.FRsrp);
            objs8.Add(data.ciopItem.FSinr);
            //不显示过覆盖指数
            objs8.Add(data.ciopItem.FRSRP0电平均值);
            objs8.Add(data.ciopItem.FRSRP1电平均值);
            objs8.Add(GetDataRate(data.ciopItem.F主瓣采样点比例));
            objs8.Add(data.ciopItem.F主瓣小区平均RSRP);
            objs8.Add(GetDataRate(data.ciopItem.F主瓣覆盖率RSRPf110SINRf3));
            objs8.Add(GetDataRate(data.ciopItem.F主瓣覆盖率RSRPf110));
            objs8.Add(GetDataRate(data.ciopItem.F主瓣覆盖率SINRf3));
            objs8.Add(GetDataRate(data.ciopItem.F旁瓣采样点比例));
            objs8.Add(GetDataRate(data.ciopItem.F背瓣采样点比例));
            if (data.ciopItem.I前后比 == 10)
                objs8.Add("-");
            else
                objs8.Add(data.ciopItem.I前后比);

            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport1, 2).ToString()));//端口1幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport2, 2).ToString()));//端口2幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport3, 2).ToString()));//端口3幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport4, 2).ToString()));//端口4幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport5, 2).ToString()));//端口5幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport6, 2).ToString()));//端口6幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport7, 2).ToString()));//端口7幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.drangeport8, 2).ToString()));//端口8幅度
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport1, 2).ToString()));//端口1相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport2, 2).ToString()));//端口2相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport3, 2).ToString()));//端口3相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport4, 2).ToString()));//端口4相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport5, 2).ToString()));//端口5相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport6, 2).ToString()));//端口6相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport7, 2).ToString()));//端口7相位
            objs8.Add(getValidData(iInfoShow, Math.Round(data.antPara.dphaseport8, 2).ToString()));//端口8相位
            objs8.Add(getValidData(iInfoShow, data.antPara.isSmartAnt));//是否(8通道)智能天线
            objs8.Add(data.mrCellParaData.strIsMrCell);

            return objs8;
        }


        private string getValidData(int iInfoShow, string str)
        {
            if(iInfoShow == 0)
            {
                return "-";
            }

            return str;
        }

        private object GetDataRate(float p)
        {
            if (p > 100)
                return "-";
            else
                return p.ToString("0.00") + "%";
        }

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteAntParaForm).FullName);
            LteAntParaForm form = obj == null ? null : obj as LteAntParaForm;
            if (form == null || form.IsDisposed)
            {
                form = new LteAntParaForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(cellParaDataDic);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
        #endregion

        public class CellParaData
        {
            //查询时间
            public int iStime{ get; set; }
            public int iEtime{ get; set; }

            //小区
            public int index{ get; set; }
            public string strcityname{ get; set; }
            public string cellname{ get; set; }
            public int iTac{ get; set; }
            public int iEci{ get; set; }
            public string strMatch{ get; set; }
            public int iangle_dir{ get; set; }

            public string strProbType{ get; set; }
            public string strProbStatus{ get; set; }
            public string strCompareStatus{ get; set; }

            public ZTLteScanAntenna.CellInfoItem ciItem{ get; set; }//扫频集合
            public ZTLteAntenna.CellInfoOutPutItem ciopItem{ get; set; }//路测集合
            public AntennaPara antPara{ get; set; }//权值数据
            public CellPara cellPara{ get; set; }//小区性能数据
            public CellParaRank cellParaRank{ get; set; }//小区性能数据（RANK）
            public AntCfgSub antCfg{ get; set; }//状态库天线配置信息
            public ZTLteScanAntenna.AntInfoItem antInfoItem{ get; set; }
            public LteMrItem lteMRItem{ get; set; }
            public LteCoverItem lteMRCoverItem{ get; set; }
            public SCANR0R1Item scanR0R1Item{ get; set; }
            public ZTLteAntMR.CellParaData mrCellParaData{ get; set; }//MR分项呈现

            public double cellLongitude{ get; set; }
            public double cellLatitude{ get; set; }
            public List<LongLat> longLatModelList { get; set; }//小区理想模型
            public List<LongLat> longLatScanList { get; set; }//扫频覆盖
            public List<LongLat> longLatDTlList { get; set; }//路测覆盖

            public CellParaData()
            {
                iStime = 0;
                iEtime = 0;
                index = 0;
                strcityname = "";
                cellname = "";
                strMatch = "是";
                iTac = 0;
                iEci = 0;
                cellLongitude = 0;
                cellLatitude = 0;
                iangle_dir = 0;

                strProbType = "";
                strProbStatus = "";
                strCompareStatus = "";

                ciItem = new ZTLteScanAntenna.CellInfoItem(false);
                antPara = new AntennaPara();
                cellPara = new CellPara();
                antCfg = new AntCfgSub();
                mrCellParaData = new ZTLteAntMR.CellParaData();

                longLatModelList = new List<LongLat>();
                longLatScanList = new List<LongLat>();
                longLatDTlList = new List<LongLat>();
            }
        }

        public class ProblemType
        {
            public int 天线工参核查Level1 { get; set; }
            public int 主瓣弱覆盖Level1 { get; set; }
            public int 主瓣弱覆盖Level2 { get; set; }
            public int 主瓣弱覆盖总数
            {
                get
                {
                    return 主瓣弱覆盖Level1 + 主瓣弱覆盖Level2;
                }
            }
            public int 旁瓣泄露Level1 { get; set; }
            public int 旁瓣泄露Level2 { get; set; }
            public int 旁瓣泄露总数
            {
                get
                {
                    return 旁瓣泄露Level1 + 旁瓣泄露Level2;
                }
            }
            public int 背瓣覆盖异常Level1 { get; set; }
            public int 背瓣覆盖异常Level2 { get; set; }
            public int 背瓣覆盖异常总数
            {
                get
                {
                    return 背瓣覆盖异常Level1 + 背瓣覆盖异常Level2;
                }
            }
            public int 严重过覆盖Level2 { get; set; }
            public int 严重重叠覆盖Level2 { get; set; }
            public int RS信号失衡Level1 { get; set; }
            public int RS信号失衡Level2 { get; set; }
            public int RS信号失衡总数
            {
                get
                {
                    return RS信号失衡Level1 + RS信号失衡Level2;
                }
            }
            public int 天线权值异常Level2 { get; set; }
            public int 室分泄露Level1 { get; set; }
            public int 室分泄露Level2 { get; set; }
            public int 室分泄露总数
            {
                get
                {
                    return 室分泄露Level1 + 室分泄露Level2;
                }
            }
            public int 主瓣弱覆盖 { get; set; }
            public string 主瓣弱覆盖Level1占比
            {
                get
                {
                    if (主瓣弱覆盖 == 0)
                    {
                        return "0%";
                    }
                    else
                    {
                        return Math.Round(100 * (double)主瓣弱覆盖Level1 / 主瓣弱覆盖, 2) + "%";
                    }
                }
            }
            public int 旁瓣泄露 { get; set; }
            public string 旁瓣泄露Level1占比
            {
                get
                {
                    if (旁瓣泄露 == 0)
                    {
                        return "0%";
                    }
                    else
                    {
                        return Math.Round(100 * (double)旁瓣泄露Level1 / 旁瓣泄露, 2) + "%";
                    }
                }
            }
            public int 背瓣异常 { get; set; }
            public string 背瓣异常Level1占比
            {
                get
                {
                    if (背瓣异常 == 0)
                    {
                        return "0%";
                    }
                    else
                    {
                        return Math.Round(100 * (double)背瓣覆盖异常Level1 / 背瓣异常, 2) + "%";
                    }
                }
            }
            public int RS失衡 { get; set; }
            public string RS失衡Level1占比
            {
                get
                {
                    if (RS失衡 == 0)
                    {
                        return "0%";
                    }
                    else
                    {
                        return Math.Round(100 * (double)RS信号失衡Level1 / RS失衡, 2) + "%";
                    }
                }
            }
            public int 室分泄露 { get; set; }
            public string 室分泄露占比
            {
                get
                {
                    if (室分泄露 == 0)
                    {
                        return "0%";
                    }
                    else
                    {
                        return Math.Round(100 * (double)室分泄露Level1 / 室分泄露, 2) + "%";
                    }
                }
            }

            public ProblemType()
            {
                天线工参核查Level1 = 0;
                主瓣弱覆盖Level1 = 0;
                主瓣弱覆盖Level2 = 0;
                旁瓣泄露Level1 = 0;
                旁瓣泄露Level2 = 0;
                背瓣覆盖异常Level1 = 0;
                背瓣覆盖异常Level2 = 0;
                严重过覆盖Level2 = 0;
                严重重叠覆盖Level2 = 0;
                RS信号失衡Level1 = 0;
                RS信号失衡Level2 = 0;
                天线权值异常Level2 = 0;
                室分泄露Level1 = 0;
                室分泄露Level2 = 0;
                主瓣弱覆盖 = 0;
                旁瓣泄露 = 0;
                背瓣异常 = 0;
                RS失衡 = 0;
                室分泄露 = 0;
            }
        }
    }
}
