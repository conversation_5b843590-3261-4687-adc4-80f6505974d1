<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">联通_GSM语音采样点（新）</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010201}</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010207}</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010208}</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub[-94,-90)采样点数</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub[-90,-85)采样点数</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub[-80,-75)采样点数</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub[-85,-80)采样点数</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">43</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">71</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[0,1)采样点数</Item>
          <Item typeName="Int32" key="RowAt">72</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[1,2)采样点数</Item>
          <Item typeName="Int32" key="RowAt">73</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2,3)采样点数</Item>
          <Item typeName="Int32" key="RowAt">74</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[3,4)采样点数</Item>
          <Item typeName="Int32" key="RowAt">75</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">83</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[0,2.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">84</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">91</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">99</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01050C}</Item>
          <Item typeName="Int32" key="RowAt">43</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_67010C}</Item>
          <Item typeName="Int32" key="RowAt">71</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_670101}</Item>
          <Item typeName="Int32" key="RowAt">72</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_670102}</Item>
          <Item typeName="Int32" key="RowAt">74</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_67010E}</Item>
          <Item typeName="Int32" key="RowAt">75</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B5C}</Item>
          <Item typeName="Int32" key="RowAt">83</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C5C}</Item>
          <Item typeName="Int32" key="RowAt">91</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_6C0145}</Item>
          <Item typeName="Int32" key="RowAt">99</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010209}</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01020A}</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower</Item>
          <Item typeName="Int32" key="RowAt">99</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">PesqScore</Item>
          <Item typeName="Int32" key="RowAt">91</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B55}</Item>
          <Item typeName="Int32" key="RowAt">84</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">84</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_67010D}</Item>
          <Item typeName="Int32" key="RowAt">73</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">PesqLQ</Item>
          <Item typeName="Int32" key="RowAt">83</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">75</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">74</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">73</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">72</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA</Item>
          <Item typeName="Int32" key="RowAt">71</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQualSub</Item>
          <Item typeName="Int32" key="RowAt">43</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[0,5)</Item>
          <Item typeName="Int32" key="RowAt">100</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[5,10)</Item>
          <Item typeName="Int32" key="RowAt">101</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[10,20)</Item>
          <Item typeName="Int32" key="RowAt">102</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[20,30)</Item>
          <Item typeName="Int32" key="RowAt">103</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[30,30]</Item>
          <Item typeName="Int32" key="RowAt">104</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">105</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTCur[16,32)</Item>
          <Item typeName="Int32" key="RowAt">107</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTCur[32,48)</Item>
          <Item typeName="Int32" key="RowAt">108</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTCur[48,64)</Item>
          <Item typeName="Int32" key="RowAt">109</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTCur[64,64]</Item>
          <Item typeName="Int32" key="RowAt">110</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">111</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTMax[0,16)</Item>
          <Item typeName="Int32" key="RowAt">112</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTMax[16,32)</Item>
          <Item typeName="Int32" key="RowAt">113</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTMax[48,64)</Item>
          <Item typeName="Int32" key="RowAt">115</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTMax[64,64]</Item>
          <Item typeName="Int32" key="RowAt">116</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">100</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">101</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">102</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">103</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">104</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTCur</Item>
          <Item typeName="Int32" key="RowAt">105</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">107</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">108</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">109</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">110</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTMax</Item>
          <Item typeName="Int32" key="RowAt">111</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">112</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">113</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">115</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">116</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_6C013D}</Item>
          <Item typeName="Int32" key="RowAt">100</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_6C013E}</Item>
          <Item typeName="Int32" key="RowAt">101</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_6C013F}</Item>
          <Item typeName="Int32" key="RowAt">102</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_6C0140}</Item>
          <Item typeName="Int32" key="RowAt">103</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_6C0141}</Item>
          <Item typeName="Int32" key="RowAt">104</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010904}</Item>
          <Item typeName="Int32" key="RowAt">105</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010906}</Item>
          <Item typeName="Int32" key="RowAt">107</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010907}</Item>
          <Item typeName="Int32" key="RowAt">108</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010908}</Item>
          <Item typeName="Int32" key="RowAt">109</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010909}</Item>
          <Item typeName="Int32" key="RowAt">110</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010A04}</Item>
          <Item typeName="Int32" key="RowAt">111</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010A05}</Item>
          <Item typeName="Int32" key="RowAt">112</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010A06}</Item>
          <Item typeName="Int32" key="RowAt">113</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010A08}</Item>
          <Item typeName="Int32" key="RowAt">115</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010A09}</Item>
          <Item typeName="Int32" key="RowAt">116</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub[-120,-94)采样点数</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub&lt;-120采样点数</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull&gt;=-75采样点数</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010205}</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010101}</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010117}</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010206}</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[4,5)采样点数</Item>
          <Item typeName="Int32" key="RowAt">76</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[5,6)采样点数</Item>
          <Item typeName="Int32" key="RowAt">77</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">76</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">77</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_67010F}</Item>
          <Item typeName="Int32" key="RowAt">76</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_670103}</Item>
          <Item typeName="Int32" key="RowAt">77</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">112</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub&gt;=-75采样点数</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010217}</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">28</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull[-80,-75)采样点数</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull[-94,-90)采样点数</Item>
          <Item typeName="Int32" key="RowAt">28</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01010A}</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010107}</Item>
          <Item typeName="Int32" key="RowAt">28</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull[-85,-80)采样点数</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010109}</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull[-90,-85)采样点数</Item>
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010108}</Item>
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull[-120,-94)采样点数</Item>
          <Item typeName="Int32" key="RowAt">29</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull&lt;-120</Item>
          <Item typeName="Int32" key="RowAt">30</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010106}</Item>
          <Item typeName="Int32" key="RowAt">29</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010105}</Item>
          <Item typeName="Int32" key="RowAt">30</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">29</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">30</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual0</Item>
          <Item typeName="Int32" key="RowAt">44</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual1</Item>
          <Item typeName="Int32" key="RowAt">45</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual2</Item>
          <Item typeName="Int32" key="RowAt">46</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">44</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">45</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">46</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010501}</Item>
          <Item typeName="Int32" key="RowAt">44</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010502}</Item>
          <Item typeName="Int32" key="RowAt">45</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010503}</Item>
          <Item typeName="Int32" key="RowAt">46</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.0,2.5)采样点数</Item>
          <Item typeName="Int32" key="RowAt">85</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.5,2.8)采样点数</Item>
          <Item typeName="Int32" key="RowAt">86</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.8,3.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">87</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[3.0,3.5)采样点数</Item>
          <Item typeName="Int32" key="RowAt">88</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[3.5,3.8)采样点数</Item>
          <Item typeName="Int32" key="RowAt">89</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[&gt;=3.8]采样点数</Item>
          <Item typeName="Int32" key="RowAt">90</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">85</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">86</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">87</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">88</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">89</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">90</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B56}</Item>
          <Item typeName="Int32" key="RowAt">85</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B57}</Item>
          <Item typeName="Int32" key="RowAt">86</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B58}</Item>
          <Item typeName="Int32" key="RowAt">87</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B59}</Item>
          <Item typeName="Int32" key="RowAt">88</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B5A}</Item>
          <Item typeName="Int32" key="RowAt">89</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B5B}</Item>
          <Item typeName="Int32" key="RowAt">90</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTCur[0,16)</Item>
          <Item typeName="Int32" key="RowAt">106</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">106</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010905}</Item>
          <Item typeName="Int32" key="RowAt">106</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RLTMax[32,48)</Item>
          <Item typeName="Int32" key="RowAt">114</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010A07}</Item>
          <Item typeName="Int32" key="RowAt">114</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">114</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标分类</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标名称</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">BCCH</Item>
          <Item typeName="Int32" key="RowAt">117</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">117</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">119</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">120</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">121</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">122</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">124</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI &lt; 0</Item>
          <Item typeName="Int32" key="RowAt">124</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[0,6)</Item>
          <Item typeName="Int32" key="RowAt">125</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[6,9)</Item>
          <Item typeName="Int32" key="RowAt">126</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[9,12)</Item>
          <Item typeName="Int32" key="RowAt">127</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[12,18)</Item>
          <Item typeName="Int32" key="RowAt">128</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">142</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">125</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">126</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">127</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">128</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">142</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010601}</Item>
          <Item typeName="Int32" key="RowAt">124</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010602}</Item>
          <Item typeName="Int32" key="RowAt">125</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010603}</Item>
          <Item typeName="Int32" key="RowAt">126</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010604}</Item>
          <Item typeName="Int32" key="RowAt">127</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010605}</Item>
          <Item typeName="Int32" key="RowAt">128</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">142</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">129</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI &gt;= 18</Item>
          <Item typeName="Int32" key="RowAt">129</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01060A}</Item>
          <Item typeName="Int32" key="RowAt">129</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">汇总</Item>
          <Item typeName="Int32" key="RowAt">138</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总里程</Item>
          <Item typeName="Int32" key="RowAt">138</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_0806/1000}公里</Item>
          <Item typeName="Int32" key="RowAt">138</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总时长</Item>
          <Item typeName="Int32" key="RowAt">139</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_0805/(3600*1000)}小时</Item>
          <Item typeName="Int32" key="RowAt">139</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">78</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[6,8)采样点数</Item>
          <Item typeName="Int32" key="RowAt">78</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_670104}</Item>
          <Item typeName="Int32" key="RowAt">78</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">139</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch</Item>
          <Item typeName="Int32" key="RowAt">33</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">34</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">35</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">36</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">37</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">39</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">40</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">33</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch&gt;=-75采样点数</Item>
          <Item typeName="Int32" key="RowAt">34</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch[-80,-75)采样点数</Item>
          <Item typeName="Int32" key="RowAt">35</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch[-85,-80)采样点数</Item>
          <Item typeName="Int32" key="RowAt">36</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch[-90,-85)采样点数</Item>
          <Item typeName="Int32" key="RowAt">37</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch[-94,-90)采样点数</Item>
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch[-120,-94)采样点数</Item>
          <Item typeName="Int32" key="RowAt">39</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch&lt;-120</Item>
          <Item typeName="Int32" key="RowAt">40</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010301}</Item>
          <Item typeName="Int32" key="RowAt">33</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010317}</Item>
          <Item typeName="Int32" key="RowAt">34</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01030A}</Item>
          <Item typeName="Int32" key="RowAt">35</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010309}</Item>
          <Item typeName="Int32" key="RowAt">36</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010308}</Item>
          <Item typeName="Int32" key="RowAt">37</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010307}</Item>
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010306}</Item>
          <Item typeName="Int32" key="RowAt">39</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010305}</Item>
          <Item typeName="Int32" key="RowAt">40</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">47</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">48</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">49</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual3</Item>
          <Item typeName="Int32" key="RowAt">47</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual4</Item>
          <Item typeName="Int32" key="RowAt">48</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual5</Item>
          <Item typeName="Int32" key="RowAt">49</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual6</Item>
          <Item typeName="Int32" key="RowAt">50</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual7</Item>
          <Item typeName="Int32" key="RowAt">51</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">50</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">51</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010504}</Item>
          <Item typeName="Int32" key="RowAt">47</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010505}</Item>
          <Item typeName="Int32" key="RowAt">48</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010506}</Item>
          <Item typeName="Int32" key="RowAt">49</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010507}</Item>
          <Item typeName="Int32" key="RowAt">50</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010508}</Item>
          <Item typeName="Int32" key="RowAt">51</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQualFull</Item>
          <Item typeName="Int32" key="RowAt">53</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">54</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">55</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">56</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">57</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">58</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">59</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">60</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">61</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">53</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual0</Item>
          <Item typeName="Int32" key="RowAt">54</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual1</Item>
          <Item typeName="Int32" key="RowAt">55</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual2</Item>
          <Item typeName="Int32" key="RowAt">56</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual3</Item>
          <Item typeName="Int32" key="RowAt">57</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual4</Item>
          <Item typeName="Int32" key="RowAt">58</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual5</Item>
          <Item typeName="Int32" key="RowAt">59</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual6</Item>
          <Item typeName="Int32" key="RowAt">60</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQual7</Item>
          <Item typeName="Int32" key="RowAt">61</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01040C}</Item>
          <Item typeName="Int32" key="RowAt">53</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010401}</Item>
          <Item typeName="Int32" key="RowAt">54</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010402}</Item>
          <Item typeName="Int32" key="RowAt">55</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010403}</Item>
          <Item typeName="Int32" key="RowAt">56</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010404}</Item>
          <Item typeName="Int32" key="RowAt">57</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010405}</Item>
          <Item typeName="Int32" key="RowAt">58</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010406}</Item>
          <Item typeName="Int32" key="RowAt">59</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010407}</Item>
          <Item typeName="Int32" key="RowAt">60</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010408}</Item>
          <Item typeName="Int32" key="RowAt">61</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[0,2.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">92</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">92</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">93</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">94</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">95</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">98</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">96</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">97</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.0,2.5)采样点数</Item>
          <Item typeName="Int32" key="RowAt">93</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.5,2.8)采样点数</Item>
          <Item typeName="Int32" key="RowAt">94</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.8,3.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">95</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[3.0,3.5)采样点数</Item>
          <Item typeName="Int32" key="RowAt">96</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[3.5,3.8)采样点数</Item>
          <Item typeName="Int32" key="RowAt">97</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[&gt;=3.8]采样点数</Item>
          <Item typeName="Int32" key="RowAt">98</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C55}</Item>
          <Item typeName="Int32" key="RowAt">92</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C56}</Item>
          <Item typeName="Int32" key="RowAt">93</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C57}</Item>
          <Item typeName="Int32" key="RowAt">94</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C58}</Item>
          <Item typeName="Int32" key="RowAt">95</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C59}</Item>
          <Item typeName="Int32" key="RowAt">96</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C5A}</Item>
          <Item typeName="Int32" key="RowAt">97</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010C5B}</Item>
          <Item typeName="Int32" key="RowAt">98</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">79</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[8,10]采样点数</Item>
          <Item typeName="Int32" key="RowAt">79</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_670105}</Item>
          <Item typeName="Int32" key="RowAt">79</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub 90覆盖率</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010217+Mx_5A01020A+Mx_5A010209+Mx_5A010208)/Mx_5A010201}%</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevSub 94覆盖率</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010217+Mx_5A01020A+Mx_5A010209+Mx_5A010208+Mx_5A010207)/Mx_5A010201}%</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">31</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull 90覆盖率</Item>
          <Item typeName="Int32" key="RowAt">31</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010117+Mx_5A01010A+Mx_5A010109+Mx_5A010108)/Mx_5A010101 }%</Item>
          <Item typeName="Int32" key="RowAt">31</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">32</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevFull 94覆盖率</Item>
          <Item typeName="Int32" key="RowAt">32</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010117+Mx_5A01010A+Mx_5A010109+Mx_5A010108+Mx_5A010107)/Mx_5A010101 }%</Item>
          <Item typeName="Int32" key="RowAt">32</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">41</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch 90覆盖率</Item>
          <Item typeName="Int32" key="RowAt">41</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010317+Mx_5A01030A+Mx_5A010309+Mx_5A010308)/Mx_5A010301 }%</Item>
          <Item typeName="Int32" key="RowAt">41</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">42</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxlevBcch 94覆盖率</Item>
          <Item typeName="Int32" key="RowAt">42</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010317+Mx_5A01030A+Mx_5A010309+Mx_5A010308+Mx_5A010307)/Mx_5A010301 }%</Item>
          <Item typeName="Int32" key="RowAt">42</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">52</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQualSub公式</Item>
          <Item typeName="Int32" key="RowAt">52</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((Mx_5A010501+Mx_5A010502+Mx_5A010503)+0.7*(Mx_5A010504+Mx_5A010505+Mx_5A010506))/Mx_5A01050C }%</Item>
          <Item typeName="Int32" key="RowAt">52</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">62</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxQualFull公式</Item>
          <Item typeName="Int32" key="RowAt">62</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*((Mx_5A010401+Mx_5A010402+Mx_5A010403)+0.7*(Mx_5A010404+Mx_5A010405+Mx_5A010406))/Mx_5A01040C }%</Item>
          <Item typeName="Int32" key="RowAt">62</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">118</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">BCCH_CI</Item>
          <Item typeName="Int32" key="RowAt">123</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">123</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010609}</Item>
          <Item typeName="Int32" key="RowAt">123</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TCH_CI</Item>
          <Item typeName="Int32" key="RowAt">131</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">131</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010709}</Item>
          <Item typeName="Int32" key="RowAt">131</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">132</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">133</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">134</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">135</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">136</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">137</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI &lt; 0</Item>
          <Item typeName="Int32" key="RowAt">132</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[0,6)</Item>
          <Item typeName="Int32" key="RowAt">133</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[6,9)</Item>
          <Item typeName="Int32" key="RowAt">134</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[9,12)</Item>
          <Item typeName="Int32" key="RowAt">135</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI[12,18)</Item>
          <Item typeName="Int32" key="RowAt">136</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI &gt;= 18</Item>
          <Item typeName="Int32" key="RowAt">137</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010701}</Item>
          <Item typeName="Int32" key="RowAt">132</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010702}</Item>
          <Item typeName="Int32" key="RowAt">133</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010703}</Item>
          <Item typeName="Int32" key="RowAt">134</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010704}</Item>
          <Item typeName="Int32" key="RowAt">135</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010705}</Item>
          <Item typeName="Int32" key="RowAt">136</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A01070A}</Item>
          <Item typeName="Int32" key="RowAt">137</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">140</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均时速(公里/小时)</Item>
          <Item typeName="Int32" key="RowAt">140</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_0806/1000)/(Mx_0805/3600000) }公里/小时</Item>
          <Item typeName="Int32" key="RowAt">140</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">138</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SpeechCodec</Item>
          <Item typeName="Int32" key="RowAt">63</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">63</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010810}</Item>
          <Item typeName="Int32" key="RowAt">63</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">64</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">65</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">HR采样点数</Item>
          <Item typeName="Int32" key="RowAt">64</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FR采样点数</Item>
          <Item typeName="Int32" key="RowAt">65</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">EFR采样点数</Item>
          <Item typeName="Int32" key="RowAt">66</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">AMR-HR采样点数</Item>
          <Item typeName="Int32" key="RowAt">67</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">AMR-FR采样点数</Item>
          <Item typeName="Int32" key="RowAt">68</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010802}</Item>
          <Item typeName="Int32" key="RowAt">64</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010801}</Item>
          <Item typeName="Int32" key="RowAt">65</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010803}</Item>
          <Item typeName="Int32" key="RowAt">66</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010804}</Item>
          <Item typeName="Int32" key="RowAt">67</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010805}</Item>
          <Item typeName="Int32" key="RowAt">68</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SignalOnly</Item>
          <Item typeName="Int32" key="RowAt">69</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010810-(Mx_5A010802+Mx_5A010801+Mx_5A010803+Mx_5A010804+Mx_5A010805) }</Item>
          <Item typeName="Int32" key="RowAt">69</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">66</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">67</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">68</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">69</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">70</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">半速率半分比</Item>
          <Item typeName="Int32" key="RowAt">70</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010802+Mx_5A010804)/Mx_5A010810 }%</Item>
          <Item typeName="Int32" key="RowAt">70</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">81</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA[0,1]百分比</Item>
          <Item typeName="Int32" key="RowAt">81</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_670101+Mx_67010D)/Mx_67010C }%</Item>
          <Item typeName="Int32" key="RowAt">81</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">81</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">81</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">82</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA[2,7]百分比</Item>
          <Item typeName="Int32" key="RowAt">82</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_670102+Mx_67010E+Mx_67010F+Mx_670103+Mx_670104 )/Mx_67010C }%</Item>
          <Item typeName="Int32" key="RowAt">82</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev&gt;=-75采样点数</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev[-80,-75)采样点数</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev[-85,-80)采样点数</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev[-90,-85)采样点数</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev[-94,-90)采样点数</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev[-120,-94)采样点数</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev&lt;-120采样点数</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev 90覆盖率</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev 94覆盖率</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev 94覆盖里程</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640101}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640117}</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_64010A}</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640109}</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640108}</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640107}</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640106}</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_640105}</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}%</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108+Mx_640107)/Mx_640101}%</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_640119+Mx_640116+Mx_640115+Mx_640114+Mx_640113)/1000}公里</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">130</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CI &lt; 6百分比</Item>
          <Item typeName="Int32" key="RowAt">130</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010601+Mx_5A010602)/Mx_5A010609}%</Item>
          <Item typeName="Int32" key="RowAt">130</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlev 90覆盖里程</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_640119+Mx_640116+Mx_640115+Mx_640114)/1000}公里</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">80</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[10,63]采样点数</Item>
          <Item typeName="Int32" key="RowAt">80</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_670106+Mx_670107+Mx_670108 }</Item>
          <Item typeName="Int32" key="RowAt">80</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">联通-GSM900频段[96,124]</Item>
          <Item typeName="Int32" key="RowAt">118</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">联通-DCS1800频段[637,736]</Item>
          <Item typeName="Int32" key="RowAt">119</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">移动-GSM900频段[1,94]</Item>
          <Item typeName="Int32" key="RowAt">120</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">移动-DCS1800频段[512,636]</Item>
          <Item typeName="Int32" key="RowAt">121</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">移动-EGSM900频段[1000,1023]</Item>
          <Item typeName="Int32" key="RowAt">122</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010D01+Mx_5A010D02+Mx_5A010D03+Mx_5A010D04+Mx_5A010D05 }</Item>
          <Item typeName="Int32" key="RowAt">117</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010D02}</Item>
          <Item typeName="Int32" key="RowAt">118</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010D04}</Item>
          <Item typeName="Int32" key="RowAt">119</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010D01}</Item>
          <Item typeName="Int32" key="RowAt">120</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010D03}</Item>
          <Item typeName="Int32" key="RowAt">121</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010D05}</Item>
          <Item typeName="Int32" key="RowAt">122</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">141</Item>
        <Item typeName="Int32">318</Item>
        <Item typeName="Int32">136</Item>
        <Item typeName="Int32">123</Item>
        <Item typeName="Int32">114</Item>
        <Item typeName="Int32">108</Item>
        <Item typeName="Int32">104</Item>
        <Item typeName="Int32">102</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>