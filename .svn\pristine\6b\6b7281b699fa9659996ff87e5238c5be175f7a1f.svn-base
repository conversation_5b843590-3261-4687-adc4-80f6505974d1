﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;

namespace EvtEngineLib
{
    public static class OwnMsgDecode
    {
        private static Dictionary<int, object> retObjDic = new Dictionary<int, object>();
        public static bool StartDissect(byte[] byteArray, int msgId)
        {
            retObjDic.Clear();
            if (byteArray.Length < 2)
            {
                return false;
            }
            int offset = 0;

            //short count = BitConverter.ToInt16(byteArray, offset);
            //count = IPAddress.NetworkToHostOrder(count);
            offset += 2;
            while (offset < (byteArray.Length - 2))
            {
                short type = BitConverter.ToInt16(byteArray, offset);

                type = IPAddress.NetworkToHostOrder(type);
                offset += 2;
                if (type == (short)eSam_Field_Type.eSam_StrComment)
                {
                    short strLen = BitConverter.ToInt16(byteArray, offset);
                    strLen = IPAddress.NetworkToHostOrder(strLen);
                    offset += 2;
                    string valueStr = Encoding.Default.GetString(byteArray, offset, strLen);
                    offset += strLen;
                    retObjDic[type] = valueStr;
                }
                else if (type == (short)eSam_Field_Type.eSam_4G_AppLTE_TotalSize_bits64
                    || type == (short)eSam_Field_Type.eSam_APP_TotalSize_64bits
                    || type == (short)eSam_Field_Type.eSam_Qualcomm_TimeStamp_64bits)
                {
                    Int64 valueInt64 = BitConverter.ToInt64(byteArray, offset);
                    Int64 vv = IPAddress.NetworkToHostOrder(valueInt64);
                    offset += 8;
                    retObjDic[type] = vv;
                }
                else
                {
                    int valueInt = BitConverter.ToInt32(byteArray, offset);
                    int vv = IPAddress.NetworkToHostOrder(valueInt);
                    offset += 4;
                    retObjDic[type] = vv;
                    if (type == (short)eSam_Field_Type.eSam_LTE_PRBINFO_DL
                        || type == (short)eSam_Field_Type.eSam_LTE_PRBINFO_UL)
                    {
                        break;
                    }
                }
            }
            return true;
        }
        public static bool GetIntValue(string typeDescStr, ref Int64 value)
        {
            int type = parseTypeString(typeDescStr);
            if (type == -1)
            {
                return false;
            }
            return GetIntValue(type, ref value);
        }

        public static bool GetStringValue(int msgid, string indexStr, ref Int64 val)
        {
            // 只用于判断baidu、jd、qq、taobao网站访问时间、次数
            if (msgid == 2147424830)
            {
                Dictionary<string, int> DicHttpString = new Dictionary<string, int>();
                DicHttpString.Add("www.baidu.com", 1);
                DicHttpString.Add("www.jd.com", 2);
                DicHttpString.Add("www.qq.com", 3);
                DicHttpString.Add("www.taobao.com", 4);

                object objV;
                if (retObjDic.TryGetValue((int)(eSam_Field_Type)Enum.Parse(typeof(eSam_Field_Type), indexStr), out objV))
                {
                    return judgeValidValue(objV, DicHttpString, ref val);
                }
            }

            return false;
        }

        private static bool judgeValidValue(object objV, Dictionary<string, int> DicHttpString, ref Int64 val)
        {
            if (objV is string)
            {
                string value = objV.ToString();
                if (value.IndexOf("Browse") >= 0)
                {
                    value = value.Replace("Browse ", "");
                }

                if (DicHttpString.ContainsKey(value))
                {
                    val = DicHttpString[value];

                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        private static Dictionary<string, int> prenameDic = null;
        private static int parseTypeString(string typeDescStr)
        {
            if (prenameDic == null)
            {
                #region Dic Init Set
                prenameDic = new Dictionary<string, int>();
                prenameDic["eSam_MtMsg_Version"] = 0;
                prenameDic["eSam_APP_type"] = 1;
                prenameDic["eSam_APP_Status"] = 2;
                prenameDic["eSam_APP_DataStatus_DL"] = 3;
                prenameDic["eSam_APP_DataStatus_UL"] = 4;
                prenameDic["eSam_APP_TransferedSize"] = 5;
                prenameDic["eSam_APP_TransferedTime"] = 6;
                prenameDic["eSam_APP_Speed"] = 7;
                prenameDic["eSam_APP_TotalSize"] = 8;
                prenameDic["eSam_APP_Average_Speed"] = 9;
                prenameDic["eSam_APP_TotalTime"] = 10;
                prenameDic["eSam_APP_FRAMES"] = 11;
                prenameDic["eSam_StrComment"] = 12;
                prenameDic["eSam_PESQ_LQ"] = 13;
                prenameDic["eSam_PESQ_Score"] = 14;
                prenameDic["eSam_PESQ_MOS"] = 15;
                prenameDic["eSam_Mode"] = 16;
                prenameDic["eSam_LAC"] = 17;
                prenameDic["eSam_CI"] = 18;
                prenameDic["eSam_RAC"] = 19;
                prenameDic["eSam_VoiceHangup_Reason"] = 20;

                prenameDic["eSam_3G_APPHSDPA_TransferedSize"] = 21;
                prenameDic["eSam_3G_APPR4_TransferedSize"] = 22;
                prenameDic["eSam_3G_APPGPRSEDGE_TransferedSize"] = 23;
                prenameDic["eSam_3G_AppHSDPA_TransferedTime"] = 24;
                prenameDic["eSam_3G_AppR4_TransferedTime"] = 25;
                prenameDic["eSam_3G_AppGPRSEDGE_TransferedTime"] = 26;
                prenameDic["eSam_3G_AppHSDPA_TotalSize"] = 27;
                prenameDic["eSam_3G_AppR4_TotalSize"] = 28;
                prenameDic["eSam_3G_AppGPRSEDGE_TotalSize"] = 29;
                prenameDic["eSam_3G_AppHSDPA_TotalTime"] = 30;
                prenameDic["eSam_3G_AppR4_TotalTime"] = 31;
                prenameDic["eSam_3G_AppGPRSEDGE_TotalTime"] = 32;
 
                prenameDic["eSam_4G_AppLTE_TransferedTime"] = 44;
                prenameDic["eSam_4G_APPLTE_TransferedSize"] = 45;
                prenameDic["eSam_4G_AppLTE_TotalTime"] = 46;
                prenameDic["eSam_4G_AppLTE_TotalSize_bits64"] = 47;
                prenameDic["eSam_APP_TotalSize_64bits"] = 48;
                prenameDic["eSam_Current_Network_Type"] = 49;
                prenameDic["eSam_APP_type_Per"] = 50;
                prenameDic["eSam_APP_Status_Per"] = 51;
                prenameDic["eSam_Network_Carrier"] = 52;
                prenameDic["eSam_ATU_Device_AlarmType"] = 53;
                prenameDic["eSam_Layer3_MessageId"] = 54;
                prenameDic["eSam_APP_HSPA_ADV_TransferedTime"] = 55;
                prenameDic["eSam_APP_HSPA_ADV_TransferedSize"] = 56;
                prenameDic["eSam_APP_HSPA_ADV_TotalTime"] = 57;
                prenameDic["eSam_APP_HSPA_ADV_TotalSize"] = 58;
                prenameDic["eSam_APP_EVDO_TransferedTime"] = 59;
                prenameDic["eSam_APP_EVDO_TransferedSize"] = 60;
                prenameDic["eSam_APP_EVDO_TotalTime"] = 61;
                prenameDic["eSam_APP_EVDO_TotalSize"] = 62;
                prenameDic["eSam_APP_1x_TransferedTime"] = 63;
                prenameDic["eSam_APP_1x_TransferedSize"] = 64;
                prenameDic["eSam_APP_1x_TotalTime"] = 65;
                prenameDic["eSam_APP_1x_TotalSize"] = 66;
                prenameDic["eSam_APP_Continue_Type_DL"] = 67;
                prenameDic["eSam_APP_Continue_Type_UL"] = 68;
                prenameDic["eSam_Qualcomm_TimeStamp_64bits"] = 69;

                prenameDic["eSam_Ping_Ip"] = 33;    //原始值, int表示************(4247718666)
                prenameDic["eSam_Ping_Size"] = 34;  //原始值, 单位:Byte
                prenameDic["eSam_Ping_TTL"] = 35;   //原始值, 单位:次 Time to Live生存周期(经过一个网络设备减一)
                prenameDic["eSam_Ping_Delay"] = 36; //原始值, 单位:ms 
                prenameDic["eSam_Ping_ULRate"] = 37;//原始值*1000
                prenameDic["eSam_Ping_DLRate"] = 38;//原始值*1000
                prenameDic["eSam_Ping_Jitter"] = 39;//原始值, 单位:ms invalid：0xFFFFD8F1(-9999)
                prenameDic["eSam_HTTPPage_Delay"] = 40;       //原始值, 单位:ms 
                prenameDic["eSam_HTTPPage_ConnectTime"] = 41; //原始值, 单位:ms 
                prenameDic["eSam_PPPDial_TestKind"] = 42; //测试类型, ping ftp wap http
                prenameDic["eSam_PPPDial_NetFlag"] = 43;  //网络类型 

                prenameDic["eSam_GPRS_BLER"] = 1000;
                prenameDic["eSam_GPRS_RLC_UL_Thr"] = 1001;
                prenameDic["eSam_GPRS_RLC_DL_Thr"] = 1002;
                prenameDic["eSam_GPRS_RLC_UL_RTX_Rate"] = 1003;
                prenameDic["eSam_GPRS_RLC_DL_RTX_Rate"] = 1004;
                prenameDic["eSam_EDGE_UL_MCS"] = 1005;
                prenameDic["eSam_EDGE_DL_MCS"] = 1006;
                prenameDic["eSam_Gsm_RxlevSub"] = 1007;
                prenameDic["eSam_Gsm_RxlevFull"] = 1008;
                prenameDic["eSam_Gsm_RxqualSub"] = 1009;
                prenameDic["eSam_Gsm_RxqualFull"] = 1010;
                prenameDic["eSam_Gsm_RxLevBCCH"] = 1011;
                prenameDic["eSam_Gsm_TA"] = 1012;
                prenameDic["eSam_Gsm_TXPOWER"] = 1013;
                prenameDic["eSam_Gsm_BCCH"] = 1014;
                prenameDic["eSam_Gsm_BSIC"] = 1015;
                prenameDic["eSam_Gsm_SQI"] = 1016;
                prenameDic["eSam_TD_PCCPCH_RSCP"] = 3000;
                prenameDic["eSam_TD_PCCPCH_C_I"] = 3001;
                prenameDic["eSam_TD_UL_RLC_Thr"] = 3002;
                prenameDic["eSam_TD_DL_RLC_Thr"] = 3003;
                prenameDic["eSam_TD_BLER"] = 3004;
                prenameDic["eSam_TD_RLC_UL_RTX_Rate"] = 3005;
                prenameDic["eSam_TD_CPI"] = 3006;
                prenameDic["eSam_TD_UARFCN"] = 3007;
                prenameDic["eSam_TD_UL_PDCP_Thr"] = 3008;
                prenameDic["eSam_TD_DL_PDCP_Thr"] = 3009;
                prenameDic["eSam_WCDMA_PSC"] = 5000;
                prenameDic["eSam_WCDMA_FREQ"] = 5001;

                prenameDic["eSam_WLAN_Attempt_Time"] = 13000;	 // 一次事件里的第几次细分事件                                                
                prenameDic["eSam_WLAN_TTL"] = 13001;	 // (Time To Live ) 生存时间                                                  
                prenameDic["eSam_WLAN_AP_Channel"] = 13002;	 // 信道号(原始值)                                                            
                prenameDic["eSam_WLAN_AP_Freq"] = 13003;	 // 频点(原始值)                                                              
                prenameDic["eSam_WLAN_AP_RSSI"] = 13004;	 // RSSI信号强度(dBm)(原始值 * 1000)                                          
                prenameDic["eSam_WLAN_AP_SFI"] = 13005;	 // 同频AP干扰场强(dBm)(原始值 * 1000)                                        
                prenameDic["eSam_WLAN_AP_NFI"] = 13006;	 // 邻频AP干扰场强(dBm)(原始值 * 1000)                                        
                prenameDic["eSam_WLAN_AP_CI"] = 13007;	 // 载干比(db)(原始值 * 1000)                                                 
                prenameDic["eSam_WLAN_BSS_Active_Users"] = 13008;	 // 关联AP下并发用户数(原始值)                                                
                prenameDic["eSam_WLAN_BSS_Total_Users"] = 13009;	 // 关联AP下发现用户数(原始值)                                                
                prenameDic["eSam_WLAN_Channel_Active_Users"] = 13010;	 // 信道并发用户数(原始值)                                                    
                prenameDic["eSam_WLAN_Channel_Total_Users"] = 13011;	 // 信道发现用户数(原始值)                                                    
                prenameDic["eSam_WLAN_NFI_Channel"] = 13012;	 // 邻频干扰信道号(原始值)                                                    
                prenameDic["eSam_WLAN_RxThrput"] = 13013;	 // 用户接收数据帧吞吐率(bps)(原始值)(除1000即改为kb/s，即和CDS对上)          
                prenameDic["eSam_WLAN_RxRetrans"] = 13014;	 // 用户接收数据帧重传率(%)(原始值 * 1000)                                    
                prenameDic["eSam_WLAN_RxPhysicalRate"] = 13015;	 // 用户接收数据平均调制速率(bps)(原始值)(除1000000即改为Mb/s，即和CDS对上)   
                prenameDic["eSam_WLAN_TxThrput"] = 13016;	 // 用户发送数据帧吞吐率(bps)(原始值)(除1000即改为kb/s，即和CDS对上)          
                prenameDic["eSam_WLAN_TxRetrans"] = 13017;	 // 用户数据帧重传率(%)(原始值 * 1000)                                        
                prenameDic["eSam_WLAN_TxPhysicalRate"] = 13018;	 // 用户发送数据平均调制速率(bps)(原始值)(除1000000即改为Mb/s，即和CDS对上)   

                prenameDic["eSam_LTE_EARFCN "] = 15000;
                prenameDic["eSam_LTE_PCI"] = 15001;
                prenameDic["eSam_LTE_RSRP"] = 15002;
                prenameDic["eSam_LTE_RSRQ"] = 15003;
                prenameDic["eSam_LTE_RSSI"] = 15004;
                prenameDic["eSam_LTE_SINR"] = 15005;
                prenameDic["eSam_LTE_SERNEIINFO"] = 15006;
                prenameDic["eSam_LTE_PUSCH_Power"] = 15007;
                prenameDic["eSam_LTE_ULTBF_ErrBlock"] = 15008;
                prenameDic["eSam_LTE_ULTBF_TotalBlock"] = 15009;
                prenameDic["eSam_LTE_PDCCH_DL_Grant_Count"] = 15010;
                prenameDic["eSam_LTE_PDCCH_UL_Grant_Count"] = 15011;
                prenameDic["eSam_LTE_PRBINFO_DL"] = 15012;
                prenameDic["eSam_LTE_PRBINFO_UL"] = 15013;

                prenameDic["eSam_LTE_UL_MCS_INFO"] = 15014;
                prenameDic["eSam_LTE_DL_Code0_MCS_INFO"] = 15015;
                prenameDic["eSam_LTE_DL_Code1_MCS_INFO"] = 15016;
                prenameDic["eSam_LTE_PDSCH_BLER"] = 15017;
                prenameDic["eSam_LTE_PDSCH_Code0_BLER"] = 15018;
                prenameDic["eSam_LTE_PDSCH_Code1_BLER"] = 15019;
                prenameDic["eSam_LTE_PDCCH_BLER"] = 15020;
                prenameDic["eSam_LTE_PDCCH_Estimated_BLER"] = 15021;
                prenameDic["eSam_LTE_PUSCH_Initial_BLER"] = 15022;
                prenameDic["eSam_LTE_PUSCH_Residual_BLER"] = 15023;
                prenameDic["eSam_LTE_UL_HARQ_ACK"] = 15024;
                prenameDic["eSam_LTE_UL_HARQ_NACK"] = 15025;
                prenameDic["eSam_LTE_DL_Code0_HARQ_ACK"] = 15026;
                prenameDic["eSam_LTE_DL_Code0_HARQ_NACK"] = 15027;
                prenameDic["eSam_LTE_DL_Code1_HARQ_ACK"] = 15028;
                prenameDic["eSam_LTE_DL_Code1_HARQ_NACK"] = 15029;
                prenameDic["eSam_LTE_Modulation_UL_QPSK"] = 15030;
                prenameDic["eSam_LTE_Modulation_UL_16QAM"] = 15031;
                prenameDic["eSam_LTE_Modulation_UL_64QAM"] = 15032;
                prenameDic["eSam_LTE_Modulation_DL_Code0_QPSK"] = 15033;
                prenameDic["eSam_LTE_Modulation_DL_Code0_16QAM"] = 15034;
                prenameDic["eSam_LTE_Modulation_DL_Code0_64QAM"] = 15035;
                prenameDic["eSam_LTE_Modulation_DL_Code1_QPSK"] = 15036;
                prenameDic["eSam_LTE_Modulation_DL_Code1_16QAM"] = 15037;
                prenameDic["eSam_LTE_Modulation_DL_Code1_64QAM"] = 15038;
                prenameDic["eSam_LTE_Wideband_CQI_for_CW0"] = 15039;
                prenameDic["eSam_LTE_Wideband_CQI_for_CW1"] = 15040;
                prenameDic["eSam_LTE_Rank_Indicator"] = 15041;
                prenameDic["eSam_LTE_Transmission_Mode"] = 15042;
                prenameDic["eSam_LTE_PHY_DL_Throughput"] = 15043;
                prenameDic["eSam_LTE_PHY_UL_Throughput"] = 15044;
                prenameDic["eSam_LTE_MAC_DL_Throughput"] = 15045;
                prenameDic["eSam_LTE_MAC_UL_Throughput"] = 15046;
                prenameDic["eSam_LTE_RLC_DL_Throughput"] = 15047;
                prenameDic["eSam_LTE_RLC_UL_Throughput"] = 15048;
                prenameDic["eSam_LTE_PHY_Code0_DL_Throughput"] = 15049;
                prenameDic["eSam_LTE_PHY_Code1_DL_Throughput"] = 15050;
                prenameDic["eSam_LTE_PHY_DL_Single_Stream"] = 15051;
                prenameDic["eSam_LTE_PHY_DL_Dual_Stream"] = 15052;
                prenameDic["eSam_LTE_PLMN"] = 15053;
                prenameDic["eSam_LTE_Power_Headroom"] = 15054;
                prenameDic["eSam_LTE_RSRP_Rx0"] = 15055;
                prenameDic["eSam_LTE_RSRP_Rx1"] = 15056;
                prenameDic["eSam_LTE_SINR_Rx0"] = 15057;
                prenameDic["eSam_LTE_SINR_Rx1"] = 15058;
                prenameDic["eSam_LTE_PRACH_Power"] = 15059;
                prenameDic["eSam_LTE_PUCCH_Power"] = 15060;
                prenameDic["eSam_LTE_SRS_Power"] = 15061;
                prenameDic["eSam_LTE_PDCCH_CFI1"] = 15062;
                prenameDic["eSam_LTE_PDCCH_CFI2"] = 15063;
                prenameDic["eSam_LTE_PDCCH_CFI3"] = 15064;
                prenameDic["eSam_LTE_PDCCH_CFI4"] = 15065;
                prenameDic["eSam_LTE_PDCCH_DCI_Format"] = 15066;
                prenameDic["eSam_LTE_PDSCH_Init_BLER"] = 15067;
                prenameDic["eSam_LTE_PDSCH_Init_BLER_Code0"] = 15068;
                prenameDic["eSam_LTE_PDSCH_Init_BLER_Code1"] = 15069;
                prenameDic["eSam_LTE_PDSCH_Remain_BLER"] = 15070;
                prenameDic["eSam_LTE_PDSCH_Remain_BLER_Code0"] = 15071;
                prenameDic["eSam_LTE_PDSCH_Remain_BLER_Code1"] = 15072;
                prenameDic["eSam_LTE_PUSCH_BLER"] = 15073;
                prenameDic["eSam_LTE_DL_Bandwidth"] = 15074;
                prenameDic["eSam_LTE_UL_Bandwidth"] = 15075;
                prenameDic["eSam_LTE_SINR_ATU"] = 15076;
                prenameDic["eSam_LTE_Work_Mode"] = 15077;
                prenameDic["eSam_VOLTE_RTP_Direction"] = 15078;
                prenameDic["eSam_VOLTE_RTP_Source_SSRC"] = 15079;
                prenameDic["eSam_VOLTE_RTP_Sequence_Number"] = 15080;
                prenameDic["eSam_VOLTE_RTP_Timestamp"] = 15081;
                prenameDic["eSam_VOLTE_POLQA_Score_SWB"] = 15082;
                prenameDic["eSam_VOLTE_RAT_Type"] = 15083;
                prenameDic["eSam_VOLTE_RTP_Codec_Type"] = 15084;
                prenameDic["eSam_VOLTE_RTP_Media_Type"] = 15085;
                prenameDic["eSam_VOLTE_RTP_Payload_size"] = 15086;
                prenameDic["eSam_VOLTE_RTP_Packets_Lost_Num"] = 15087;
                prenameDic["eSam_VOLTE_RTP_Packets_Num"] = 15088;
                prenameDic["eSam_LTE_EPS_QCI"] = 15089;
                #endregion
            }
            int ret;
            if (prenameDic.TryGetValue(typeDescStr.Trim(), out ret))
            {
                return ret;
            }
            return -1;
        }
        public static bool GetIntValue(int type, ref Int64 val)
        {
            object objV;
            if (retObjDic.TryGetValue(type, out objV))
            {
                if (objV is int)
                {
                    val = (Int32)objV;
                    return true;
                }
                else if (objV is Int64)
                {
                    val = (Int64)objV;
                    return true;
                }
            }
            return false;
        }
        public static bool GetStringValue(int type, ref string val)
        {
            object objV;
            if (retObjDic.TryGetValue(type, out objV) && objV is string)
            {
                val = objV as string;
                return true;
            }
            return false;
        }
    }
    public enum eSam_Field_Type
    {
        eSam_MtMsg_Version = 0,
        eSam_APP_type = 1,         // app层业务类型，包括FTPDownload,FTPUPLOAD,emailPOP3/IMAP,email SMTP,HTTP Download，WAP_Page，WAP_Download，MMS_Send，MMS_Recv等
        eSam_APP_Status,         // 包括FTPDownload状态，FTPUPLOAD状态，emailPOP3/IMAP状态，email SMTP状态，HTTP Download状态等
        eSam_APP_DataStatus_DL,      // 用于标识当前处于HSPA,R4384,R4128,R464,EDGE/GPRS状态
        eSam_APP_DataStatus_UL,      // 用于标识当前处于HSPA,R4384,R4128,R464,EDGE/GPRS状态
        eSam_APP_TransferedSize,   // BYTES
        eSam_APP_TransferedTime,   // ms
        eSam_APP_Speed,            // bps
        eSam_APP_TotalSize,         // BYTES
        eSam_APP_Average_Speed,      // bps
        eSam_APP_TotalTime,         // ms
        eSam_APP_FRAMES,         // 原始值
        eSam_StrComment,         // 字符串
        eSam_PESQ_LQ,            // 原始值*1000
        eSam_PESQ_Score,         // 原始值*1000
        eSam_PESQ_MOS,            // 原始值*1000
        eSam_Mode,
        eSam_LAC,               // 原始值
        eSam_CI,               // 原始值
        eSam_RAC,               // 原始值
        eSam_VoiceHangup_Reason,//原始值
        eSam_3G_APPHSDPA_TransferedSize, // ATU 应用层当前下载(GPRSEDGE)字节数,单位(BYTE)
        eSam_3G_APPR4_TransferedSize, // ATU 应用层当前下载(R4)字节数,单位(BYTE)
        eSam_3G_APPGPRSEDGE_TransferedSize,// ATU 应用层当前下载(HSDPA)字节数,单位(BYTE)
        eSam_3G_AppHSDPA_TransferedTime, // ATU 应用层当前下载(HSDPA)时长,单位(ms)
        eSam_3G_AppR4_TransferedTime,    // ATU 应用层当前下载(R4)时长,单位(ms)
        eSam_3G_AppGPRSEDGE_TransferedTime,// ATU 应用层当前下载(GPRSEDGE)时长,单位(ms)
        eSam_3G_AppHSDPA_TotalSize, // ATU 应用层总下载(HSDPA)字节数,单位(BYTE)
        eSam_3G_AppR4_TotalSize,    // ATU 应用层总下载(R4)字节数,单位(BYTE)
        eSam_3G_AppGPRSEDGE_TotalSize,// ATU 应用层总下载(GPRSEDGE)字节数,单位(BYTE)
        eSam_3G_AppHSDPA_TotalTime, // ATU 应用层总下载(HSDPA)时长,单位(ms)
        eSam_3G_AppR4_TotalTime,    // ATU 应用层总下载(R4)时长,单位(ms)
        eSam_3G_AppGPRSEDGE_TotalTime,// ATU 应用层总下载(GPRSEDGE)时长,单位(ms)

        eSam_Ping_Ip,    //原始值, int表示************(4247718666)
        eSam_Ping_Size,  //原始值, 单位:Byte
        eSam_Ping_TTL,   //原始值, 单位:次 Time to Live生存周期(经过一个网络设备减一)
        eSam_Ping_Delay, //原始值, 单位:ms 
        eSam_Ping_ULRate,//原始值*1000
        eSam_Ping_DLRate,//原始值*1000
        eSam_Ping_Jitter,//原始值, 单位:ms invalid：0xFFFFD8F1(-9999)

        eSam_HTTPPage_Delay,       //原始值, 单位:ms 
        eSam_HTTPPage_ConnectTime, //原始值, 单位:ms 

        eSam_PPPDial_TestKind,//原始值
        eSam_PPPDial_NetFlag,//原始值

        //////////////////////////////////////////////////////////////////////////
        eSam_4G_AppLTE_TransferedTime, // ATU 应用层当前下载(LTE)时长,单位(ms)
        eSam_4G_APPLTE_TransferedSize,	// ATU 应用层当前下载(LTE)字节数,单位(BYTE)
        eSam_4G_AppLTE_TotalTime, // ATU 应用层总下载(LTE)时长,单位(ms)
        eSam_4G_AppLTE_TotalSize_bits64, // ATU 应用层总下载(LTE)字节数,单位(BYTE)
        eSam_APP_TotalSize_64bits, //UINT64(64bits, BYTES, 替代原来的eSam_APP_TotalSize)
        //////////////////////////////////////////////////////////////////////////

        eSam_Current_Network_Type,   // 原始值（统计是否脱网，脱网值为128）
        eSam_APP_type_Per,           // app层业务类型，每个采样点都写（eSam_APP_type则不是）
        eSam_APP_Status_Per,         // 用于标记测试业务状态，每个采样点都写（eSam_APP_Status则不是）

        eSam_Network_Carrier,       // 原始值，运营商
        eSam_ATU_Device_AlarmType,  // 原始值，设备告警类型
        eSam_Layer3_MessageId,      // 原始值，消息ID

        //////////////////////////////////////////////////////////////////////////
        eSam_APP_HSPA_ADV_TransferedTime, // ATU 应用层当前下载(HSPA+)时长,单位(ms)
        eSam_APP_HSPA_ADV_TransferedSize,	// ATU 应用层当前下载(HSPA+)字节数,单位(BYTE)
        eSam_APP_HSPA_ADV_TotalTime, // ATU 应用层总下载(HSPA+)时长,单位(ms)
        eSam_APP_HSPA_ADV_TotalSize, // ATU 应用层总下载(HSPA+)字节数,单位(BYTE)
        eSam_APP_EVDO_TransferedTime, // ATU 应用层当前下载(EVDO)时长,单位(ms)
        eSam_APP_EVDO_TransferedSize,	// ATU 应用层当前下载(EVDO)字节数,单位(BYTE)
        eSam_APP_EVDO_TotalTime, // ATU 应用层总下载(EVDO)时长,单位(ms)
        eSam_APP_EVDO_TotalSize, // ATU 应用层总下载(EVDO)字节数,单位(BYTE)
        eSam_APP_1x_TransferedTime, // ATU 应用层当前下载(1x)时长,单位(ms)
        eSam_APP_1x_TransferedSize,	// ATU 应用层当前下载(1x)字节数,单位(BYTE)
        eSam_APP_1x_TotalTime, // ATU 应用层总下载(1x)时长,单位(ms)
        eSam_APP_1x_TotalSize, // ATU 应用层总下载(1x)字节数,单位(BYTE)
        //////////////////////////////////////////////////////////////////////////

        eSam_APP_Continue_Type_DL, // 标识App的Continue，用于给栅格进行统计
        eSam_APP_Continue_Type_UL, // 标识App的Continue，用于给栅格进行统计

        eSam_Qualcomm_TimeStamp_64bits, // 高通芯片的手机时间戳（毫秒），目前只是插到消息的blockcode字段

        eSam_GPRS_BLER = 1000,      // %原始值*1000
        eSam_GPRS_RLC_UL_Thr,      // bps
        eSam_GPRS_RLC_DL_Thr,      // bps
        eSam_GPRS_RLC_UL_RTX_Rate,   // 原始值*1000
        eSam_GPRS_RLC_DL_RTX_Rate,   // 原始值*1000
        eSam_EDGE_UL_MCS,         // 原始值
        eSam_EDGE_DL_MCS,         // 原始值
        eSam_Gsm_RxlevSub,         // 原始值*1000
        eSam_Gsm_RxlevFull,         // 原始值*1000
        eSam_Gsm_RxqualSub,         // 原始值
        eSam_Gsm_RxqualFull,      // 原始值
        eSam_Gsm_RxLevBCCH,         // 原始值*1000
        eSam_Gsm_TA,
        eSam_Gsm_TXPOWER,         // 原始值*1000
        eSam_Gsm_BCCH,            // 原始值
        eSam_Gsm_BSIC,            // 原始值
        eSam_Gsm_SQI,            // 原始值
        eSam_TD_PCCPCH_RSCP = 3000,   // 原始值*1000
        eSam_TD_PCCPCH_C_I,         // 原始值*1000
        eSam_TD_UL_RLC_Thr,         // bps
        eSam_TD_DL_RLC_Thr,         // bps
        eSam_TD_BLER,            // 原始值*1000
        eSam_TD_RLC_UL_RTX_Rate,   // 原始值*1000
        eSam_TD_CPI,            // 原始值
        eSam_TD_UARFCN,            // 原始值
        eSam_TD_UL_PDCP_Thr,
        eSam_TD_DL_PDCP_Thr,
        eSam_WCDMA_PSC,
        eSam_WCDMA_FREQ,
        eSam_LTE_EARFCN = 15000,		// 原始值
        eSam_LTE_PCI,				    // 原始值
        eSam_LTE_RSRP,				    // 原始值 * 1000
        eSam_LTE_RSRQ,				    // 原始值 * 1000
        eSam_LTE_RSSI,				    // 原始值 * 1000
        eSam_LTE_SINR,				    // 原始值 * 1000
        eSam_LTE_SERNEIINFO,			// 邻区信息[组数(WORD)+值(int)]
        eSam_LTE_PUSCH_Power,           // 原始值
        eSam_LTE_ULTBF_ErrBlock,
        eSam_LTE_ULTBF_TotalBlock,
        eSam_LTE_PDCCH_DL_Grant_Count,  // 原始值
        eSam_LTE_PDCCH_UL_Grant_Count,  // 原始值
        eSam_LTE_PRBINFO_DL,             // [汇总(int)+组数(WORD)+Slot0(WORD)+Slot1(WORD)]
        eSam_LTE_PRBINFO_UL,             // [汇总(int)+组数(WORD)+Slot0(WORD)+Slot1(WORD)]

        eSam_LTE_UL_MCS_INFO,           // [组数(WORD)+MCS0 ~ MCS31(WORD*32)]
        eSam_LTE_DL_Code0_MCS_INFO,     // [组数(WORD)+MCS0 ~ MCS31(WORD*32)]
        eSam_LTE_DL_Code1_MCS_INFO,     // [组数(WORD)+MCS0 ~ MCS31(WORD*32)]

        eSam_LTE_PDSCH_BLER,            // 原始值 * 1000
        eSam_LTE_PDSCH_Code0_BLER,      // 原始值 * 1000
        eSam_LTE_PDSCH_Code1_BLER,      // 原始值 * 1000
        eSam_LTE_PDCCH_BLER,            // 原始值 * 1000
        eSam_LTE_PDCCH_Estimated_BLER,  // 原始值 * 1000
        eSam_LTE_PUSCH_Initial_BLER,    // 原始值 * 1000
        eSam_LTE_PUSCH_Residual_BLER,   // 原始值 * 1000

        eSam_LTE_UL_HARQ_ACK,           // 原始值 
        eSam_LTE_UL_HARQ_NACK,          // 原始值
        eSam_LTE_DL_Code0_HARQ_ACK,     // 原始值
        eSam_LTE_DL_Code0_HARQ_NACK,    // 原始值
        eSam_LTE_DL_Code1_HARQ_ACK,     // 原始值
        eSam_LTE_DL_Code1_HARQ_NACK,    // 原始值

        eSam_LTE_Modulation_UL_QPSK,    // 原始值
        eSam_LTE_Modulation_UL_16QAM,   // 原始值
        eSam_LTE_Modulation_UL_64QAM,   // 原始值

        eSam_LTE_Modulation_DL_Code0_QPSK,    // 原始值
        eSam_LTE_Modulation_DL_Code0_16QAM,   // 原始值
        eSam_LTE_Modulation_DL_Code0_64QAM,   // 原始值

        eSam_LTE_Modulation_DL_Code1_QPSK,    // 原始值
        eSam_LTE_Modulation_DL_Code1_16QAM,   // 原始值
        eSam_LTE_Modulation_DL_Code1_64QAM,   // 原始值

        eSam_LTE_Wideband_CQI_for_CW0,  // 原始值
        eSam_LTE_Wideband_CQI_for_CW1,  // 原始值

        eSam_LTE_Rank_Indicator,        // 原始值
        eSam_LTE_Transmission_Mode,     // 原始值

        eSam_LTE_PHY_DL_Throughput,    // 原始值(bps)
        eSam_LTE_PHY_UL_Throughput,    // 原始值(bps)
        eSam_LTE_MAC_DL_Throughput,    // 原始值(bps)
        eSam_LTE_MAC_UL_Throughput,    // 原始值(bps)
        eSam_LTE_RLC_DL_Throughput,    // 原始值(bps)
        eSam_LTE_RLC_UL_Throughput,    // 原始值(bps)
        eSam_LTE_PHY_Code0_DL_Throughput,    // 原始值(bps)
        eSam_LTE_PHY_Code1_DL_Throughput,    // 原始值(bps)
        eSam_LTE_PHY_DL_Single_Stream,       // 原始值(bps)
        eSam_LTE_PHY_DL_Dual_Stream,         // 原始值(bps)

        eSam_LTE_PLMN,                 // 原始值
        eSam_LTE_Power_Headroom,       // 原始值 * 1000

        eSam_LTE_RSRP_Rx0,             // 原始值 * 1000
        eSam_LTE_RSRP_Rx1,             // 原始值 * 1000
        eSam_LTE_SINR_Rx0,             // 原始值 * 1000
        eSam_LTE_SINR_Rx1,             // 原始值 * 1000

        eSam_LTE_PRACH_Power,          // 原始值
        eSam_LTE_PUCCH_Power,          // 原始值
        eSam_LTE_SRS_Power,            // 原始值
        eSam_LTE_PDCCH_CFI1,           // 原始值
        eSam_LTE_PDCCH_CFI2,           // 原始值
        eSam_LTE_PDCCH_CFI3,           // 原始值
        eSam_LTE_PDCCH_CFI4,           // 原始值

        eSam_LTE_PDCCH_DCI_Format,     // 原始值

        eSam_LTE_PDSCH_Init_BLER,			 // 原始值 * 1000
        eSam_LTE_PDSCH_Init_BLER_Code0,      // 原始值 * 1000
        eSam_LTE_PDSCH_Init_BLER_Code1,      // 原始值 * 1000
        eSam_LTE_PDSCH_Remain_BLER,			 // 原始值 * 1000
        eSam_LTE_PDSCH_Remain_BLER_Code0,    // 原始值 * 1000
        eSam_LTE_PDSCH_Remain_BLER_Code1,    // 原始值 * 1000
        eSam_LTE_PUSCH_BLER,    // 原始值 * 1000

        eSam_LTE_DL_Bandwidth,           // 原始值
        eSam_LTE_UL_Bandwidth,           // 原始值

        eSam_LTE_SINR_ATU,               // 原始值 * 1000（我们的算法。集团取的是Rx0（对应我们:eSam_LTE_SINR），我们这个取的是Max(Rx0, Rx1)）
        eSam_LTE_Work_Mode,

        eSam_VOLTE_RTP_Direction,       // 原始值(1下2上)
        eSam_VOLTE_RTP_Source_SSRC,     // 原始值
        eSam_VOLTE_RTP_Sequence_Number, // 原始值
        eSam_VOLTE_RTP_Timestamp,       // 原始值
        eSam_VOLTE_POLQA_Score_SWB,     // 原始值*1000 (不再使用，赋值于eSam_PESQ_LQ)
        eSam_VOLTE_RAT_Type,            // 原始值
        eSam_VOLTE_RTP_Codec_Type,      // 原始值
        eSam_VOLTE_RTP_Media_Type,      // 原始值
        eSam_VOLTE_RTP_Payload_size,    // 原始值
        eSam_VOLTE_RTP_Packets_Lost_Num,// 原始值（RTP丢包数量）
        eSam_VOLTE_RTP_Packets_Num,     // 原始值（RTP包总数量）

        eSam_LTE_EPS_QCI,               // 原始值
    };

}
