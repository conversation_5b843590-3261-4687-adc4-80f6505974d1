﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class ReappearCondDlg : BaseForm
    {
        public ReappearCondDlg()
        {
            InitializeComponent();

            // time
            pickerStartTime.Value = DateTime.Now.AddMonths(-3);
            pickerEndTime.Value = DateTime.Now;

            // cbxType
            tokenDic = QueryNewBlackBlock.GetTokenDic();
            foreach (string token in tokenDic.Keys)
            {
                cbxType.Items.Add(tokenDic[token].Name);
            }
            if (cbxType.Items.Count != 0)
            {
                cbxType.SelectedIndex = 0;
            }

            // event
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public void GetCondition(ref ReappearCondition cond)
        {
            // time
            cond.StartDate = (int)(JavaDate.GetMilliseconds(pickerStartTime.Value.Date) / 1000);
            cond.EndDate = (int)(JavaDate.GetMilliseconds(pickerEndTime.Value.Date) / 1000);
            cond.QueryCond.dateValue = DateTime.Now.Date;
            
            // status
            for (int i = 0; i <= 5; ++i)
            {
                cond.QueryCond.statusMap[i] = true;
            }

            // token
            string blackType = cbxType.SelectedItem.ToString();
            foreach (string token in tokenDic.Keys)
            {
                if (blackType == tokenDic[token].Name)
                {
                    cond.QueryCond.TokenName = token;
                    break;
                }
            }

            // 考核属性, 全部
            cond.QueryCond.examBlockType = 2;

            // string
            cond.QueryCond.name = string.Empty;
            cond.QueryCond.reasonDesc = string.Empty;
            cond.QueryCond.placeDesc = string.Empty;
            cond.QueryCond.cellName = string.Empty;

            // radius
            cond.Radius = (double)numRadius.Value;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (pickerStartTime.Value > pickerEndTime.Value)
            {
                MessageBox.Show("关闭时间段设置不正确!", "条件错误");
                DialogResult = DialogResult.None;
            }
            else if (cbxType.SelectedItem == null)
            {
                MessageBox.Show("未选择黑点类型!", "条件错误");
                DialogResult = DialogResult.None;
            }
            else
            {
                DialogResult = DialogResult.OK;
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private Dictionary<string, BlackBlockToken> tokenDic;
    }
}
