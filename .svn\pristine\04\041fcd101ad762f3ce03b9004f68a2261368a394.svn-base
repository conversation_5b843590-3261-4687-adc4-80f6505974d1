﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using DevExpress.XtraTab;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;

using MasterCom.MControls;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaWidget
    {
        readonly XtraTabControl tabControl;
        ScanGridAnaResult anaResult { get; set; }
        ScanGridAnaResult cmpResult { get; set; }
        readonly ScanGridAnaStater stater;

        public ScanGridAnaWidget(XtraTabControl tabControl, ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult)
        {
            this.tabControl = tabControl;
            this.anaResult = anaResult;
            this.cmpResult = cmpResult;
            this.stater = new ScanGridAnaStater(anaResult, cmpResult);
            this.stater.Stat();
        }

        public void ColorChanged(ScanGridAnaRangeType rangeType, ScanGridAnaGridType netType, int covType)
        {
            stater.Stat(rangeType);
            switch (rangeType)
            {
                case ScanGridAnaRangeType.Coverage:
                    FillCoverage(netType, covType);
                    break;
                case ScanGridAnaRangeType.Rxlev:
                    FillRxlev(netType);
                    break;
                case ScanGridAnaRangeType.Compare:
                    FillCompare(netType);
                    break;
            }
        }

        public void CovTypeChanged(ScanGridAnaGridType netType, int covType)
        {
            FillCoverage(netType, covType);
        }

        public void NetTypeChanged(ScanGridAnaGridType netType, int covType)
        {
            FillAll(netType, covType);
        }

        private void FillAll(ScanGridAnaGridType netType, int covType)
        {
            FillCoverage(netType, covType);
            FillRxlev(netType);
            FillCompare(netType);
            FillWeakRxlev(netType);
            FillHighCoverage(netType);
        }

        private void FillCoverage(ScanGridAnaGridType netType, int covType)
        {
            tabControl.TabPages[0].Controls.Clear();
            tabControl.TabPages[1].Controls.Clear();
            List<DataTable> dsList = stater.GetResult(ScanGridAnaStatType.Coverage, netType, covType);

            if (dsList.Count == 4)
            {
                GridView cntGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl cntChart = ScanGridAnaWidgetMethod.CreateChartControl();
                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[0], cntGrid, cntChart);
                ScanGridAnaWidgetMethod.FillCountGrid(cntGrid, dsList[0]);
                ScanGridAnaWidgetMethod.FillCountChart(cntChart, dsList[1], ScanGridAnaRangeType.Coverage);

                GridView rateGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl rateChart = ScanGridAnaWidgetMethod.CreateChartControl();
                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[1], rateGrid, rateChart);
                ScanGridAnaWidgetMethod.FillRateGrid(rateGrid, dsList[2]);
                ScanGridAnaWidgetMethod.FillRateChart(rateChart, dsList[3], ScanGridAnaRangeType.Coverage);
            }
            else if (dsList.Count == 8)
            {
                GridView srcCntGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl srcCntChart = ScanGridAnaWidgetMethod.CreateChartControl();
                GridView srcRateGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl srcRateChart = ScanGridAnaWidgetMethod.CreateChartControl();

                GridView tarCntGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl tarCntChart = ScanGridAnaWidgetMethod.CreateChartControl();
                GridView tarRateGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl tarRateChart = ScanGridAnaWidgetMethod.CreateChartControl();

                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[0], srcCntGrid, srcCntChart, tarCntGrid, tarCntChart);
                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[1], srcRateGrid, srcRateChart, tarRateGrid, tarRateChart);

                ScanGridAnaWidgetMethod.FillCountGrid(srcCntGrid, dsList[0]);
                ScanGridAnaWidgetMethod.FillCountChart(srcCntChart, dsList[1], ScanGridAnaRangeType.Coverage);
                ScanGridAnaWidgetMethod.FillRateGrid(srcRateGrid, dsList[2]);
                ScanGridAnaWidgetMethod.FillRateChart(srcRateChart, dsList[3], ScanGridAnaRangeType.Coverage);
                ScanGridAnaWidgetMethod.FillCountGrid(tarCntGrid, dsList[4]);
                ScanGridAnaWidgetMethod.FillCountChart(tarCntChart, dsList[5], ScanGridAnaRangeType.Coverage);
                ScanGridAnaWidgetMethod.FillRateGrid(tarRateGrid, dsList[6]);
                ScanGridAnaWidgetMethod.FillRateChart(tarRateChart, dsList[7], ScanGridAnaRangeType.Coverage);
            }
        }

        private void FillRxlev(ScanGridAnaGridType netType)
        {
            tabControl.TabPages[2].Controls.Clear();
            tabControl.TabPages[3].Controls.Clear();
            List<DataTable> dtList = stater.GetResult(ScanGridAnaStatType.Rxlev, netType, 0);

            if (dtList.Count == 4)
            {
                GridView cntGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl cntChart = ScanGridAnaWidgetMethod.CreateChartControl();
                GridView rateGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl rateChart = ScanGridAnaWidgetMethod.CreateChartControl();

                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[2], cntGrid, cntChart);
                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[3], rateGrid, rateChart);

                ScanGridAnaWidgetMethod.FillCountGrid(cntGrid, dtList[0]);
                ScanGridAnaWidgetMethod.FillCountChart(cntChart, dtList[1], ScanGridAnaRangeType.Rxlev);
                ScanGridAnaWidgetMethod.FillRateGrid(rateGrid, dtList[2]);
                ScanGridAnaWidgetMethod.FillRateChart(rateChart, dtList[3], ScanGridAnaRangeType.Rxlev);

            }
            else if (dtList.Count == 8)
            {
                GridView srcCntGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl srcCntChart = ScanGridAnaWidgetMethod.CreateChartControl();
                GridView srcRateGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl srcRateChart = ScanGridAnaWidgetMethod.CreateChartControl();
                GridView tarCntGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl tarCntChart = ScanGridAnaWidgetMethod.CreateChartControl();
                GridView tarRateGrid = ScanGridAnaWidgetMethod.CreateGridView();
                ChartControl tarRateChart = ScanGridAnaWidgetMethod.CreateChartControl();

                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[2], srcCntGrid, srcCntChart, tarCntGrid, tarCntChart);
                ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[3], srcRateGrid, srcRateChart, tarRateGrid, tarRateChart);

                ScanGridAnaWidgetMethod.FillCountGrid(srcCntGrid, dtList[0]);
                ScanGridAnaWidgetMethod.FillCountChart(srcCntChart, dtList[1], ScanGridAnaRangeType.Rxlev);
                ScanGridAnaWidgetMethod.FillRateGrid(srcRateGrid, dtList[2]);
                ScanGridAnaWidgetMethod.FillRateChart(srcRateChart, dtList[3], ScanGridAnaRangeType.Rxlev);
                ScanGridAnaWidgetMethod.FillCountGrid(tarCntGrid, dtList[4]);
                ScanGridAnaWidgetMethod.FillCountChart(tarCntChart, dtList[5], ScanGridAnaRangeType.Rxlev);
                ScanGridAnaWidgetMethod.FillRateGrid(tarRateGrid, dtList[6]);
                ScanGridAnaWidgetMethod.FillRateChart(tarRateChart, dtList[7], ScanGridAnaRangeType.Rxlev);
            }
        }

        private void FillCompare(ScanGridAnaGridType netType)
        {
            if (!tabControl.TabPages[4].PageVisible)
            {
                return;
            }

            tabControl.TabPages[4].Controls.Clear();
            List<DataTable> dtList = stater.GetResult(ScanGridAnaStatType.Compare, netType, 0);
            GridView gv = ScanGridAnaWidgetMethod.CreateGridView();
            ScanGridAnaWidgetMethod.AddToTabPage(tabControl.TabPages[4], gv);
            ScanGridAnaWidgetMethod.FillCountGrid(gv, dtList[0]);
            gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatString = "p";
        }

        private void FillWeakRxlev(ScanGridAnaGridType netType)
        {
            GridControl gc = tabControl.TabPages[5].Controls[0] as GridControl;
            List<ScanGridAnaGridBlockLevelOne> levelOneList = stater.GetShowResult(netType, ScanGridAnaRangeType.WeakRxlev);
            gc.DataSource = levelOneList;
            gc.RefreshDataSource();

            tabControl.TabPages[5].Tag = levelOneList;
        }

        private void FillHighCoverage(ScanGridAnaGridType netType)
        {
            GridControl gc = tabControl.TabPages[6].Controls[0] as GridControl;
            List<ScanGridAnaGridBlockLevelOne> levelOneList = stater.GetShowResult(netType, ScanGridAnaRangeType.HighCoverage);
            gc.DataSource = levelOneList;
            gc.RefreshDataSource();

            tabControl.TabPages[6].Tag = levelOneList;
        }
    }

    public static class ScanGridAnaWidgetMethod
    {
        public static GridView CreateGridView()
        {
            GridControl gc = new GridControl();
            GridView gv = new GridView();
            gv.OptionsView.ShowGroupPanel = false;
            gv.OptionsView.ShowIndicator = false;
            gv.OptionsBehavior.Editable = false;
            gc.MainView = gv;
            return gv;
        }

        public static ChartControl CreateChartControl()
        {
            ChartControl chart = new ChartControl();
            return chart;
        }

        public static void AddToTabPage(XtraTabPage tp, GridView gv)
        {
            gv.GridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            tp.Controls.Add(gv.GridControl);

            tp.Tag = new GridView[] { gv };
        }

        public static void AddToTabPage(XtraTabPage tp, GridView gv, ChartControl chart)
        {
            SplitContainerControl scc = new SplitContainerControl();
            scc.Dock = System.Windows.Forms.DockStyle.Fill;
            scc.Horizontal = false;
            scc.SplitterPosition = tp.ClientSize.Height / 2;
            tp.Controls.Add(scc);

            gv.GridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            chart.Dock = System.Windows.Forms.DockStyle.Fill;
            scc.Panel1.Controls.Add(gv.GridControl);
            scc.Panel2.Controls.Add(chart);

            tp.Tag = new GridView[] { gv };
        }

        public static void AddToTabPage(XtraTabPage tp, GridView anaGv, ChartControl anaCt, GridView cmpGv, ChartControl cmpCt)
        {
            SplitContainerControl sccLow = new SplitContainerControl();
            sccLow.Dock = System.Windows.Forms.DockStyle.Fill;
            sccLow.Horizontal = true;
            sccLow.SplitterPosition = tp.ClientSize.Width / 2;
            tp.Controls.Add(sccLow);

            SplitContainerControl sccTop1 = new SplitContainerControl();
            sccTop1.Dock = System.Windows.Forms.DockStyle.Fill;
            sccTop1.Horizontal = false;
            sccTop1.SplitterPosition = tp.ClientSize.Height / 2;
            sccLow.Panel1.Controls.Add(sccTop1);

            SplitContainerControl sccTop2 = new SplitContainerControl();
            sccTop2.Dock = System.Windows.Forms.DockStyle.Fill;
            sccTop2.Horizontal = false;
            sccTop2.SplitterPosition = tp.ClientSize.Height / 2;
            sccLow.Panel2.Controls.Add(sccTop2);

            anaGv.GridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            sccTop1.Panel1.Controls.Add(anaGv.GridControl);
            anaCt.Dock = System.Windows.Forms.DockStyle.Fill;
            sccTop1.Panel2.Controls.Add(anaCt);

            cmpGv.GridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            sccTop2.Panel1.Controls.Add(cmpGv.GridControl);
            cmpCt.Dock = System.Windows.Forms.DockStyle.Fill;
            sccTop2.Panel2.Controls.Add(cmpCt);

            tp.Tag = new GridView[] { anaGv, cmpGv };
        }

        public static void FillCountGrid(GridView gv, DataTable dt)
        {
            gv.Columns.Clear();
            gv.GridControl.DataSource = dt;
            gv.GridControl.RefreshDataSource();
        }

        public static void FillRateGrid(GridView gv, DataTable dt)
        {
            gv.Columns.Clear();
            gv.GridControl.DataSource = dt;
            gv.GridControl.RefreshDataSource();
            for (int i = 1; i < gv.Columns.Count; ++i)
            {
                gv.Columns[i].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                gv.Columns[i].DisplayFormat.FormatString = "p";
            }
        }

        public static void FillCountChart(ChartControl chart, DataTable dt, ScanGridAnaRangeType rangeType)
        {
            List<ColorRange> colorRanges = ScanGridAnaColorRanger.Instance.GetColorRanges(rangeType);
            Series[] series = new Series[dt.Columns.Count - 1];
            for (int i = 1; i < dt.Columns.Count; ++i)
            {
                Series sis = new Series(dt.Columns[i].Caption, ViewType.StackedBar);
                ((StackedBarSeriesView)sis.View).FillStyle.FillMode = FillMode.Solid;
                ((StackedBarSeriesLabel)sis.Label).Visible = false;
                ((StackedBarSeriesView)sis.View).BarWidth = 0.4;
                if (i - 1 < colorRanges.Count)
                {
                    ((StackedBarSeriesView)sis.View).Color = colorRanges[i - 1].color;
                }
                else if (rangeType == ScanGridAnaRangeType.Coverage)
                {
                    ((StackedBarSeriesView)sis.View).Color = ScanGridAnaColorRanger.Instance.InvalidateColor;
                }

                series[i - 1] = sis;
            }

            for (int i = 0; i < dt.Rows.Count; ++i)
            {
                string argument = dt.Rows[i].ItemArray[0] as string;
                for (int j = 1; j < dt.Columns.Count; ++j)
                {
                    series[j - 1].Points.Add(new SeriesPoint(argument, dt.Rows[i].ItemArray[j]));
                }
            }

            chart.Series.Clear();
            chart.Series.AddRange(series);
            ((XYDiagram)chart.Diagram).Rotated = true;
            ((XYDiagram)chart.Diagram).AxisX.Reverse = true;
        }

        public static void FillRateChart(ChartControl chart, DataTable dt, ScanGridAnaRangeType rangeType)
        {
            List<ColorRange> colorRanges = ScanGridAnaColorRanger.Instance.GetColorRanges(rangeType);
            Series[] series = new Series[dt.Columns.Count - 1];
            for (int i = 1; i < dt.Columns.Count; ++i)
            {
                Series sis = new Series(dt.Columns[i].Caption, ViewType.FullStackedBar);
                ((FullStackedBarSeriesView)sis.View).FillStyle.FillMode = FillMode.Solid;
                ((FullStackedBarSeriesLabel)sis.Label).Visible = false;
                ((StackedBarSeriesView)sis.View).BarWidth = 0.4;
                if (i - 1 < colorRanges.Count)
                {
                    ((FullStackedBarSeriesView)sis.View).Color = colorRanges[i - 1].color;
                }
                else if (rangeType == ScanGridAnaRangeType.Coverage)
                {
                    ((FullStackedBarSeriesView)sis.View).Color = ScanGridAnaColorRanger.Instance.InvalidateColor;
                }

                series[i - 1] = sis;
            }

            for (int i = 0; i < dt.Rows.Count; ++i)
            {
                string argument = dt.Rows[i].ItemArray[0] as string;
                for (int j = 1; j < dt.Columns.Count; ++j)
                {
                    series[j - 1].Points.Add(new SeriesPoint(argument, dt.Rows[i].ItemArray[j]));
                }
            }

            chart.Series.Clear();
            chart.Series.AddRange(series);
            ((XYDiagram)chart.Diagram).Rotated = true;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Format = NumericFormat.Percent;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Precision = 0;
            ((XYDiagram)chart.Diagram).AxisX.Reverse = true;
        }
    }
}
