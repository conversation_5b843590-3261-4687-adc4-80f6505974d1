﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellReverseShowForm : MinCloseForm
    {
        public CellReverseShowForm()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<ReverseCellInfo> listCells)
        {
            gridControl.DataSource = listCells;
            gridControl.RefreshDataSource();
        }
      
        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            //
        }
    }
}
